{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Country, State } from 'country-state-city';\nimport { finalize } from 'rxjs/operators';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../prospects.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dialog\";\nimport * as i14 from \"../employee-select/employee-select.component\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => ({\n  width: \"42rem\"\n});\nfunction AddProspectComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_15_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"bp_full_name\"].errors && ctx_r1.f[\"bp_full_name\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_25_div_1_Template, 2, 0, \"div\", 22)(2, AddProspectComponent_div_25_div_2_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction AddProspectComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid website URL. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_33_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"website_url\"].errors[\"pattern\"]);\n  }\n}\nfunction AddProspectComponent_div_64_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Country is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_64_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"country\"].errors && ctx_r1.f[\"country\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_74_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" State is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_74_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"region\"].errors && ctx_r1.f[\"region\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_82_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" ZIP code must be exactly 5 letters or digits. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddProspectComponent_div_82_div_1_Template, 2, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.ProspectForm.get(\"postal_code\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"pattern\"]);\n  }\n}\nfunction AddProspectComponent_div_97_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please enter a valid mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddProspectComponent_div_97_div_1_Template, 2, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.ProspectForm.get(\"mobile\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"pattern\"]);\n  }\n}\nfunction AddProspectComponent_div_107_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Phone is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_107_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"phone_number\"].errors && ctx_r1.f[\"phone_number\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_108_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddProspectComponent_div_108_div_1_Template, 2, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.ProspectForm.get(\"phone_number\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"pattern\"]);\n  }\n}\nfunction AddProspectComponent_ng_template_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 52)(2, \"span\", 53)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" First Name\");\n    i0.ɵɵelementStart(6, \"span\", 10);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"th\", 52)(9, \"span\", 53)(10, \"span\", 13);\n    i0.ɵɵtext(11, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Last Name\");\n    i0.ɵɵelementStart(13, \"span\", 10);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"th\", 52)(16, \"span\", 53)(17, \"span\", 13);\n    i0.ɵɵtext(18, \"inbox_text_person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" Department\");\n    i0.ɵɵelementStart(20, \"span\", 10);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"th\", 52)(23, \"span\", 53)(24, \"span\", 13);\n    i0.ɵɵtext(25, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Email Address\");\n    i0.ɵɵelementStart(27, \"span\", 10);\n    i0.ɵɵtext(28, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"th\", 52)(30, \"span\", 53)(31, \"span\", 13);\n    i0.ɵɵtext(32, \"phone_iphone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Mobile\");\n    i0.ɵɵelementStart(34, \"span\", 10);\n    i0.ɵɵtext(35, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"th\", 54)(37, \"span\", 53)(38, \"span\", 13);\n    i0.ɵɵtext(39, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddProspectComponent_ng_template_120_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 51);\n    i0.ɵɵtext(1, \"This is Required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 51);\n    i0.ɵɵtext(1, \"This is Required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_small_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 51);\n    i0.ɵɵtext(1, \" Select a valid Department.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_small_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 51);\n    i0.ɵɵtext(1, \"Enter a valid email.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 51);\n    i0.ɵɵtext(1, \"Enter a valid phone.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AddProspectComponent_ng_template_120_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r6 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_120_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 2)(1, \"td\");\n    i0.ɵɵelement(2, \"input\", 55);\n    i0.ɵɵtemplate(3, AddProspectComponent_ng_template_120_small_3_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵelement(5, \"input\", 56);\n    i0.ɵɵtemplate(6, AddProspectComponent_ng_template_120_small_6_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"p-dropdown\", 57);\n    i0.ɵɵlistener(\"onChange\", function AddProspectComponent_ng_template_120_Template_p_dropdown_onChange_8_listener($event) {\n      const contact_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeDepartment($event, contact_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AddProspectComponent_ng_template_120_small_9_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵelement(11, \"input\", 58);\n    i0.ɵɵtemplate(12, AddProspectComponent_ng_template_120_small_12_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵelement(14, \"input\", 59);\n    i0.ɵɵtemplate(15, AddProspectComponent_ng_template_120_small_15_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 60);\n    i0.ɵɵtemplate(17, AddProspectComponent_ng_template_120_button_17_Template, 1, 0, \"button\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r4 = ctx.$implicit;\n    const i_r6 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"first_name\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"last_name\", \"contacts\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"contact_person_department\", \"contacts\") || ctx_r1.isFieldInvalid(i_r6, \"contact_person_department_name\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"email_address\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"mobile\", \"contacts\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contacts.length > 1);\n  }\n}\nfunction AddProspectComponent_ng_template_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 63)(2, \"span\", 53)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"supervisor_account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Role \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"th\", 63)(7, \"span\", 53)(8, \"span\", 13);\n    i0.ɵɵtext(9, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Employee \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 52)(12, \"span\", 53)(13, \"span\", 13);\n    i0.ɵɵtext(14, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddProspectComponent_ng_template_130_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AddProspectComponent_ng_template_130_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r8 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteEmployee(i_r8));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2)(1, \"td\");\n    i0.ɵɵelement(2, \"p-dropdown\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"app-employee-select\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 60);\n    i0.ɵɵtemplate(6, AddProspectComponent_ng_template_130_button_6_Template, 1, 0, \"button\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const employee_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", employee_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.partnerfunction);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.employees.length > 1);\n  }\n}\nfunction AddProspectComponent_ng_template_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_145_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.bp_full_name, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_145_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.email, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_145_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.mobile, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddProspectComponent_ng_template_145_span_2_Template, 2, 1, \"span\", 22)(3, AddProspectComponent_ng_template_145_span_3_Template, 2, 1, \"span\", 22)(4, AddProspectComponent_ng_template_145_span_4_Template, 2, 1, \"span\", 22);\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.mobile);\n  }\n}\nexport let AddProspectComponent = /*#__PURE__*/(() => {\n  class AddProspectComponent {\n    constructor(formBuilder, prospectsservice, messageservice, router) {\n      this.formBuilder = formBuilder;\n      this.prospectsservice = prospectsservice;\n      this.messageservice = messageservice;\n      this.router = router;\n      this.ngUnsubscribe = new Subject();\n      this.ProspectForm = this.formBuilder.group({\n        bp_full_name: ['', [Validators.required]],\n        email_address: ['', [Validators.required, Validators.email]],\n        website_url: ['', [Validators.pattern(/^(https?:\\/\\/)?([\\w\\-]+\\.)+[\\w\\-]+(\\/[\\w\\-./?%&=]*)?$/)]],\n        owner: [''],\n        additional_street_prefix_name: [''],\n        additional_street_suffix_name: [''],\n        house_number: [''],\n        street_name: [''],\n        city_name: [''],\n        region: ['', [Validators.required]],\n        country: ['', [Validators.required]],\n        postal_code: ['', [Validators.pattern(/^[A-Za-z0-9]{5}$/)]],\n        fax_number: [''],\n        phone_number: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        mobile: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        contactexisting: [''],\n        contacts: this.formBuilder.array([this.createContactFormGroup()]),\n        employees: this.formBuilder.array([this.createEmployeeFormGroup()])\n      });\n      this.submitted = false;\n      this.saving = false;\n      this.existingMessage = '';\n      this.existingDialogVisible = false;\n      this.position = 'right';\n      this.partnerfunction = [];\n      this.partnerLoading = false;\n      this.countries = [];\n      this.states = [];\n      this.selectedCountry = '';\n      this.selectedState = '';\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.cpDepartments = [];\n    }\n    ngOnInit() {\n      this.loadContacts();\n      this.loadPartners();\n      this.loadDepartment();\n      this.loadCountries();\n    }\n    loadDepartment() {\n      this.prospectsservice.getCPDepartment().pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n        if (response && response.data) {\n          this.cpDepartments = [{\n            name: 'Select Department',\n            value: null\n          }, ...response.data.map(item => ({\n            name: item.description,\n            value: item.code\n          }))];\n        }\n      });\n    }\n    loadCountries() {\n      const allCountries = Country.getAllCountries().map(country => ({\n        name: country.name,\n        isoCode: country.isoCode\n      })).filter(country => State.getStatesOfCountry(country.isoCode).length > 0);\n      const unitedStates = allCountries.find(c => c.isoCode === 'US');\n      const canada = allCountries.find(c => c.isoCode === 'CA');\n      const others = allCountries.filter(c => c.isoCode !== 'US' && c.isoCode !== 'CA').sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically\n      this.countries = [unitedStates, canada, ...others].filter(Boolean);\n    }\n    onCountryChange() {\n      this.states = State.getStatesOfCountry(this.selectedCountry).map(state => ({\n        name: state.name,\n        isoCode: state.isoCode\n      }));\n      this.selectedState = ''; // Reset state\n    }\n    loadPartners() {\n      this.partnerLoading = true;\n      this.prospectsservice.getPartnerfunction().pipe(finalize(() => this.partnerLoading = false)).subscribe({\n        next: data => {\n          // Replace `any` with the correct type if known\n          this.partnerfunction = data;\n        },\n        error: error => {\n          console.error('Error fetching partner data:', error);\n        }\n      });\n    }\n    loadContacts() {\n      this.contacts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP001',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n          return this.prospectsservice.getContacts(params).pipe(map(data => {\n            return data || []; // Make sure to return correct data structure\n          }), tap(() => this.contactLoading = false), catchError(error => {\n            this.contactLoading = false;\n            return of([]);\n          }));\n        }\n        return of([]).pipe(tap(() => this.contactLoading = false));\n      })));\n    }\n    onChangeDepartment(event, contact) {\n      contact.get('contact_person_department')?.patchValue(event.value.value);\n      contact.get('contact_person_department_name')?.patchValue(event.value.name);\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.ProspectForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.ProspectForm.value\n        };\n        const selectedcodewisecountry = _this.countries.find(c => c.isoCode === _this.selectedCountry);\n        const selectedState = _this.states.find(state => state.isoCode === value?.region);\n        const data = {\n          bp_full_name: value?.bp_full_name,\n          email_address: value?.email_address,\n          fax_number: value?.fax_number,\n          website_url: value?.website_url,\n          phone_number: value?.phone_number,\n          mobile: value?.mobile,\n          house_number: value?.house_number,\n          additional_street_prefix_name: value?.additional_street_prefix_name,\n          additional_street_suffix_name: value?.additional_street_suffix_name,\n          street_name: value?.street_name,\n          city_name: value?.city_name,\n          country: selectedcodewisecountry?.name,\n          county_code: selectedcodewisecountry?.isoCode,\n          postal_code: value?.postal_code,\n          region: selectedState?.isoCode,\n          contacts: Array.isArray(value.contacts) ? value.contacts : [],\n          // Ensures contacts is an array\n          employees: value.employees\n        };\n        _this.prospectsservice.createProspect(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          next: response => {\n            if (response?.data?.documentId) {\n              sessionStorage.setItem('prospectMessage', 'Prospect created successfully!');\n              window.location.href = `${window.location.origin}#/store/prospects/${response?.data?.bp_id}/overview`;\n            } else {\n              console.error('Missing documentId in response:', response);\n            }\n          },\n          error: res => {\n            _this.saving = false;\n            const msg = res?.error?.message || null;\n            if (msg) {\n              if (msg && msg.includes('unique constraint violated') && msg.includes(\"constraint='EMAIL'\")) {\n                _this.messageservice.add({\n                  severity: 'error',\n                  detail: 'Given email address already in use.'\n                });\n              } else {\n                _this.messageservice.add({\n                  severity: 'error',\n                  detail: res?.error?.message\n                });\n              }\n            } else {\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          }\n        });\n      })();\n    }\n    selectExistingContact() {\n      this.addExistingContact(this.ProspectForm.value);\n      this.existingDialogVisible = false; // Close dialog\n    }\n    addExistingContact(existing) {\n      const name = existing.contactexisting.bp_full_name.split(' ');\n      const contactForm = this.formBuilder.group({\n        first_name: [name[0] || '', [Validators.required]],\n        last_name: [name[1] || '', [Validators.required]],\n        contact_person_department: [existing.contact_person_department || null, [Validators.required]],\n        contact_person_department_name: [existing.contact_person_department_name || '', [Validators.required]],\n        email_address: [existing.contactexisting.email || '', [Validators.required, Validators.email]],\n        mobile: [existing.contactexisting.mobile || '', [Validators.required]],\n        bp_person_id: existing.contactexisting.bp_id\n      });\n      this.contacts.push(contactForm);\n      if (this.ProspectForm.value.contacts[0]?.first_name == '') {\n        this.deleteContact(0);\n      }\n    }\n    addNewContact() {\n      this.contacts.push(this.createContactFormGroup());\n    }\n    addNewEmployee() {\n      this.employees.push(this.createEmployeeFormGroup());\n    }\n    createContactFormGroup() {\n      return this.formBuilder.group({\n        first_name: ['', [Validators.required]],\n        last_name: ['', [Validators.required]],\n        contact_person_department_name: ['', [Validators.required]],\n        contact_person_department: ['', [Validators.required]],\n        email_address: ['', [Validators.required, Validators.email]],\n        mobile: ['', [Validators.required]]\n      });\n    }\n    createEmployeeFormGroup() {\n      return this.formBuilder.group({\n        partner_function: [null],\n        bp_customer_number: [null]\n      });\n    }\n    deleteContact(index) {\n      if (this.contacts.length > 1) {\n        this.contacts.removeAt(index);\n      }\n    }\n    deleteEmployee(index) {\n      if (this.employees.length > 1) {\n        this.employees.removeAt(index);\n      }\n    }\n    isFieldInvalid(index, field, arrayName) {\n      const control = this.ProspectForm.get(arrayName).at(index).get(field);\n      return control?.invalid && (control?.touched || this.submitted);\n    }\n    get f() {\n      return this.ProspectForm.controls;\n    }\n    get contacts() {\n      return this.ProspectForm.get('contacts');\n    }\n    get employees() {\n      return this.ProspectForm.get('employees');\n    }\n    showExistingDialog(position) {\n      this.position = position;\n      this.existingDialogVisible = true;\n    }\n    onCancel() {\n      this.router.navigate(['/store/prospects']);\n    }\n    onReset() {\n      this.submitted = false;\n      this.ProspectForm.reset();\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function AddProspectComponent_Factory(t) {\n        return new (t || AddProspectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProspectsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddProspectComponent,\n        selectors: [[\"app-add-prospect\"]],\n        decls: 151,\n        vars: 63,\n        consts: [[\"dt\", \"\"], [\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"bp_full_name\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"pInputText\", \"\", \"id\", \"email_address\", \"type\", \"text\", \"formControlName\", \"email_address\", \"placeholder\", \"Email Address\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"website_url\", \"type\", \"text\", \"formControlName\", \"website_url\", \"placeholder\", \"Website\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"house_number\", \"type\", \"text\", \"formControlName\", \"house_number\", \"placeholder\", \"House Number\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"street_name\", \"type\", \"text\", \"formControlName\", \"street_name\", \"placeholder\", \"Street\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"city_name\", \"type\", \"text\", \"formControlName\", \"city_name\", \"placeholder\", \"City\", 1, \"h-3rem\", \"w-full\"], [\"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"formControlName\", \"country\", \"placeholder\", \"Select Country\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"filter\", \"styleClass\", \"ngClass\"], [\"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"formControlName\", \"region\", \"placeholder\", \"Select State\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"postal_code\", \"type\", \"text\", \"formControlName\", \"postal_code\", \"placeholder\", \"Zip Code\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"fax_number\", \"type\", \"text\", \"formControlName\", \"fax_number\", \"placeholder\", \"Fax Number\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"mobile\", \"type\", \"text\", \"formControlName\", \"mobile\", \"placeholder\", \"Mobile\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"phone_number\", \"type\", \"text\", \"formControlName\", \"phone_number\", \"placeholder\", \"Phone\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"border-top-2\", \"border-gray-400\", \"my-5\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"flex\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [\"pButton\", \"\", \"type\", \"button\", \"pTooltip\", \"New Contact\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", \"iconPos\", \"right\", \"label\", \"New Contact\", 1, \"p-button-rounded\", \"p-button-primary\", 3, \"click\"], [\"responsiveLayout\", \"scroll\", 1, \"prospect-add-table\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pButton\", \"\", \"type\", \"button\", \"pTooltip\", \"New Employee\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", \"iconPos\", \"right\", \"label\", \"Add Employee\", 1, \"p-button-rounded\", \"p-button-primary\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Create\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"font-medium\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [1, \"text-left\", \"w-2\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"text-color\", \"font-medium\"], [1, \"text-left\", 2, \"width\", \"60px\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"first_name\", \"placeholder\", \"Enter a First Name\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"last_name\", \"placeholder\", \"Enter a Last Name\", 1, \"h-3rem\", \"w-full\"], [\"optionLabel\", \"name\", \"appendTo\", \"body\", \"placeholder\", \"Select Department\", 3, \"onChange\", \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"mobile\", \"placeholder\", \"Enter Mobile\", 1, \"h-3rem\", \"w-full\"], [1, \"pl-5\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"], [1, \"text-left\", \"w-4\"], [\"optionLabel\", \"label\", \"optionValue\", \"value\", \"appendTo\", \"body\", \"formControlName\", \"partner_function\", \"loading\", \"partnerLoading\", \"placeholder\", \"Select Partner Function\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"bp_customer_number\"]],\n        template: function AddProspectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"p-toast\", 1);\n            i0.ɵɵelementStart(1, \"form\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n            i0.ɵɵtext(4, \"Create Prospect\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"label\", 8)(9, \"span\", 9);\n            i0.ɵɵtext(10, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Name \");\n            i0.ɵɵelementStart(12, \"span\", 10);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(14, \"input\", 11);\n            i0.ɵɵtemplate(15, AddProspectComponent_div_15_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 6)(17, \"div\", 7)(18, \"label\", 8)(19, \"span\", 13);\n            i0.ɵɵtext(20, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" Email Address \");\n            i0.ɵɵelementStart(22, \"span\", 10);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(24, \"input\", 14);\n            i0.ɵɵtemplate(25, AddProspectComponent_div_25_Template, 3, 2, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 6)(27, \"div\", 7)(28, \"label\", 8)(29, \"span\", 13);\n            i0.ɵɵtext(30, \"globe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31, \" Wesbite \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(32, \"input\", 15);\n            i0.ɵɵtemplate(33, AddProspectComponent_div_33_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"div\", 6)(35, \"div\", 7)(36, \"label\", 8)(37, \"span\", 13);\n            i0.ɵɵtext(38, \"pin\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(39, \" House Number \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 6)(42, \"div\", 7)(43, \"label\", 8)(44, \"span\", 13);\n            i0.ɵɵtext(45, \"near_me\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(46, \" Street \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(47, \"input\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 6)(49, \"div\", 7)(50, \"label\", 8)(51, \"span\", 13);\n            i0.ɵɵtext(52, \"home_pin\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(53, \" City \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(54, \"input\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 6)(56, \"div\", 7)(57, \"label\", 8)(58, \"span\", 13);\n            i0.ɵɵtext(59, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(60, \" Country \");\n            i0.ɵɵelementStart(61, \"span\", 10);\n            i0.ɵɵtext(62, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"p-dropdown\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProspectComponent_Template_p_dropdown_ngModelChange_63_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCountry, $event) || (ctx.selectedCountry = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"onChange\", function AddProspectComponent_Template_p_dropdown_onChange_63_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCountryChange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(64, AddProspectComponent_div_64_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"div\", 6)(66, \"div\", 7)(67, \"label\", 8)(68, \"span\", 13);\n            i0.ɵɵtext(69, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(70, \" State \");\n            i0.ɵɵelementStart(71, \"span\", 10);\n            i0.ɵɵtext(72, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"p-dropdown\", 20);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProspectComponent_Template_p_dropdown_ngModelChange_73_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedState, $event) || (ctx.selectedState = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(74, AddProspectComponent_div_74_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(75, \"div\", 6)(76, \"div\", 7)(77, \"label\", 8)(78, \"span\", 13);\n            i0.ɵɵtext(79, \"code_blocks\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(80, \" Zip Code \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(81, \"input\", 21);\n            i0.ɵɵtemplate(82, AddProspectComponent_div_82_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(83, \"div\", 6)(84, \"div\", 7)(85, \"label\", 8)(86, \"span\", 13);\n            i0.ɵɵtext(87, \"fax\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(88, \" Fax Number \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(89, \"input\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(90, \"div\", 6)(91, \"div\", 7)(92, \"label\", 8)(93, \"span\", 13);\n            i0.ɵɵtext(94, \"phone_iphone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(95, \" Mobile \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(96, \"input\", 24);\n            i0.ɵɵtemplate(97, AddProspectComponent_div_97_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(98, \"div\", 6)(99, \"div\", 7)(100, \"label\", 8)(101, \"span\", 13);\n            i0.ɵɵtext(102, \"phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(103, \" Phone \");\n            i0.ɵɵelementStart(104, \"span\", 10);\n            i0.ɵɵtext(105, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(106, \"input\", 25);\n            i0.ɵɵtemplate(107, AddProspectComponent_div_107_Template, 2, 1, \"div\", 12)(108, AddProspectComponent_div_108_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelement(109, \"div\", 26);\n            i0.ɵɵelementStart(110, \"div\", 27)(111, \"div\", 28)(112, \"h3\", 29);\n            i0.ɵɵtext(113, \"Contacts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"div\", 30)(115, \"p-button\", 31);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_p_button_click_115_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(116, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_116_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addNewContact());\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(117, \"p-table\", 33, 0);\n            i0.ɵɵtemplate(119, AddProspectComponent_ng_template_119_Template, 41, 0, \"ng-template\", 34)(120, AddProspectComponent_ng_template_120_Template, 18, 9, \"ng-template\", 35);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(121, \"div\", 26);\n            i0.ɵɵelementStart(122, \"div\", 27)(123, \"div\", 28)(124, \"h3\", 29);\n            i0.ɵɵtext(125, \"Employees\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(126, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_126_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addNewEmployee());\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(127, \"p-table\", 33, 0);\n            i0.ɵɵtemplate(129, AddProspectComponent_ng_template_129_Template, 16, 0, \"ng-template\", 34)(130, AddProspectComponent_ng_template_130_Template, 7, 3, \"ng-template\", 35);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(131, \"div\", 37)(132, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_132_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_133_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(134, \"p-dialog\", 40);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AddProspectComponent_Template_p_dialog_visibleChange_134_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(135, AddProspectComponent_ng_template_135_Template, 2, 0, \"ng-template\", 34);\n            i0.ɵɵelementStart(136, \"form\", 41)(137, \"div\", 42)(138, \"label\", 43)(139, \"span\", 44);\n            i0.ɵɵtext(140, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(141, \"Contacts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(142, \"div\", 45)(143, \"ng-select\", 46);\n            i0.ɵɵpipe(144, \"async\");\n            i0.ɵɵtemplate(145, AddProspectComponent_ng_template_145_Template, 5, 4, \"ng-template\", 47);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(146, \"div\", 48)(147, \"button\", 49);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_147_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n            });\n            i0.ɵɵtext(148, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(149, \"button\", 50);\n            i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_149_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectExistingContact());\n            });\n            i0.ɵɵtext(150, \" Save \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_22_0;\n            let tmp_23_0;\n            let tmp_26_0;\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.ProspectForm);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c0, ctx.submitted && ctx.f[\"bp_full_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"bp_full_name\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c0, ctx.submitted && ctx.f[\"email_address\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c0, ctx.submitted && ctx.f[\"website_url\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"website_url\"].errors);\n            i0.ɵɵadvance(30);\n            i0.ɵɵproperty(\"options\", ctx.countries);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCountry);\n            i0.ɵɵproperty(\"filter\", true)(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(56, _c0, ctx.submitted && ctx.f[\"country\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"country\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.states);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedState);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedCountry)(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(58, _c0, ctx.submitted && ctx.f[\"region\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"region\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx.ProspectForm.get(\"postal_code\")) == null ? null : tmp_22_0.touched) && ((tmp_22_0 = ctx.ProspectForm.get(\"postal_code\")) == null ? null : tmp_22_0.invalid));\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.ProspectForm.get(\"mobile\")) == null ? null : tmp_23_0.touched) && ((tmp_23_0 = ctx.ProspectForm.get(\"mobile\")) == null ? null : tmp_23_0.invalid));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(60, _c0, ctx.submitted && ctx.f[\"phone_number\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"phone_number\"].errors);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx.ProspectForm.get(\"phone_number\")) == null ? null : tmp_26_0.touched) && ((tmp_26_0 = ctx.ProspectForm.get(\"phone_number\")) == null ? null : tmp_26_0.invalid));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.contacts == null ? null : ctx.contacts.controls)(\"paginator\", false)(\"rows\", 10);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"value\", ctx.employees == null ? null : ctx.employees.controls)(\"paginator\", false)(\"rows\", 10);\n            i0.ɵɵadvance(7);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(62, _c1));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.ProspectForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(144, 48, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i7.Table, i3.PrimeTemplate, i1.FormGroupDirective, i1.FormControlName, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Tooltip, i11.Toast, i12.InputText, i13.Dialog, i14.EmployeeSelectComponent, i5.AsyncPipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:#dc3545}  .prospect-add-table tbody td{vertical-align:top;padding:8px 6px}  .prospect-popup .p-dialog{margin-right:50px}  .prospect-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .prospect-popup .p-dialog .p-dialog-header h4{margin:0}  .prospect-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return AddProspectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}