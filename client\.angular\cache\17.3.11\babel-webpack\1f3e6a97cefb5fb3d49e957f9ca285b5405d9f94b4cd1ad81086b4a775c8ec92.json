{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, Input, Output, signal, computed, effect, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i7 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { DomHandler } from 'primeng/dom';\nimport * as i4 from 'primeng/overlay';\nimport { OverlayModule } from 'primeng/overlay';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i6 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport * as i5 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { SearchIcon } from 'primeng/icons/search';\nconst _c0 = a0 => ({\n  height: a0\n});\nconst _c1 = (a0, a1, a2) => ({\n  \"p-dropdown-item\": true,\n  \"p-highlight\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction DropdownItem_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r0.label) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"empty\");\n  }\n}\nfunction DropdownItem_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c3 = [\"container\"];\nconst _c4 = [\"filter\"];\nconst _c5 = [\"focusInput\"];\nconst _c6 = [\"editableInput\"];\nconst _c7 = [\"items\"];\nconst _c8 = [\"scroller\"];\nconst _c9 = [\"overlay\"];\nconst _c10 = [\"firstHiddenFocusableEl\"];\nconst _c11 = [\"lastHiddenFocusableEl\"];\nconst _c12 = a0 => ({\n  options: a0\n});\nconst _c13 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c14 = () => ({});\nfunction Dropdown_span_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.label() === \"p-emptylabel\" ? \"\\xA0\" : ctx_r2.label());\n  }\n}\nfunction Dropdown_span_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_span_2_ng_template_4_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.label() === \"p-emptylabel\" ? \"\\xA0\" : ctx_r2.placeholder);\n  }\n}\nfunction Dropdown_span_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_span_2_ng_template_4_span_0_Template, 2, 1, \"span\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.modelValue() && (ctx_r2.label() === ctx_r2.placeholder || ctx_r2.label() && !ctx_r2.placeholder));\n  }\n}\nfunction Dropdown_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 22, 2);\n    i0.ɵɵlistener(\"focus\", function Dropdown_span_2_Template_span_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputFocus($event));\n    })(\"blur\", function Dropdown_span_2_Template_span_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputBlur($event));\n    })(\"keydown\", function Dropdown_span_2_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, Dropdown_span_2_ng_container_2_Template, 2, 1, \"ng-container\", 23)(3, Dropdown_span_2_ng_container_3_Template, 1, 0, \"ng-container\", 24)(4, Dropdown_span_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultPlaceholder_r4 = i0.ɵɵreference(5);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.inputClass)(\"pTooltip\", ctx_r2.tooltip)(\"tooltipPosition\", ctx_r2.tooltipPosition)(\"positionStyle\", ctx_r2.tooltipPositionStyle)(\"tooltipStyleClass\", ctx_r2.tooltipStyleClass)(\"autofocus\", ctx_r2.autofocus);\n    i0.ɵɵattribute(\"aria-disabled\", ctx_r2.disabled)(\"id\", ctx_r2.inputId)(\"aria-label\", ctx_r2.ariaLabel || (ctx_r2.label() === \"p-emptylabel\" ? undefined : ctx_r2.label()))(\"aria-labelledby\", ctx_r2.ariaLabelledBy)(\"aria-haspopup\", \"listbox\")(\"aria-expanded\", ctx_r2.overlayVisible)(\"aria-controls\", ctx_r2.id + \"_list\")(\"tabindex\", !ctx_r2.disabled ? ctx_r2.tabindex : -1)(\"aria-activedescendant\", ctx_r2.focused ? ctx_r2.focusedOptionId : undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedItemTemplate)(\"ngIfElse\", defaultPlaceholder_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.selectedItemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(19, _c2, ctx_r2.selectedOption));\n  }\n}\nfunction Dropdown_input_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 25, 4);\n    i0.ɵɵlistener(\"input\", function Dropdown_input_3_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onEditableInput($event));\n    })(\"keydown\", function Dropdown_input_3_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    })(\"focus\", function Dropdown_input_3_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputFocus($event));\n    })(\"blur\", function Dropdown_input_3_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputBlur($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.inputClass)(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"maxlength\", ctx_r2.maxlength)(\"placeholder\", ctx_r2.placeholder)(\"aria-expanded\", ctx_r2.overlayVisible);\n  }\n}\nfunction Dropdown_ng_container_4_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 28);\n    i0.ɵɵlistener(\"click\", function Dropdown_ng_container_4_TimesIcon_1_Template_TimesIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dropdown-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\");\n  }\n}\nfunction Dropdown_ng_container_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Dropdown_ng_container_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_ng_container_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dropdown_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵlistener(\"click\", function Dropdown_ng_container_4_span_2_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear($event));\n    });\n    i0.ɵɵtemplate(1, Dropdown_ng_container_4_span_2_1_Template, 1, 0, null, 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction Dropdown_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_ng_container_4_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 26)(2, Dropdown_ng_container_4_span_2_Template, 2, 2, \"span\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction Dropdown_ng_container_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.dropdownIcon);\n  }\n}\nfunction Dropdown_ng_container_6_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 34);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dropdown-trigger-icon\");\n  }\n}\nfunction Dropdown_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_ng_container_6_span_1_Template, 1, 1, \"span\", 31)(2, Dropdown_ng_container_6_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dropdownIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.dropdownIcon);\n  }\n}\nfunction Dropdown_span_7_1_ng_template_0_Template(rf, ctx) {}\nfunction Dropdown_span_7_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_span_7_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dropdown_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtemplate(1, Dropdown_span_7_1_Template, 1, 0, null, 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.dropdownIconTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_div_4_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c12, ctx_r2.filterOptions));\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_template_2_SearchIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SearchIcon\", 34);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dropdown-filter-icon\");\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_template_2_span_4_1_ng_template_0_Template(rf, ctx) {}\nfunction Dropdown_ng_template_10_div_4_ng_template_2_span_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_ng_template_10_div_4_ng_template_2_span_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_template_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_div_4_ng_template_2_span_4_1_Template, 1, 0, null, 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.filterIconTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_div_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"input\", 43, 9);\n    i0.ɵɵlistener(\"input\", function Dropdown_ng_template_10_div_4_ng_template_2_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFilterInputChange($event));\n    })(\"keydown\", function Dropdown_ng_template_10_div_4_ng_template_2_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFilterKeyDown($event));\n    })(\"blur\", function Dropdown_ng_template_10_div_4_ng_template_2_Template_input_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFilterBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Dropdown_ng_template_10_div_4_ng_template_2_SearchIcon_3_Template, 1, 1, \"SearchIcon\", 32)(4, Dropdown_ng_template_10_div_4_ng_template_2_span_4_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2._filterValue() || \"\");\n    i0.ɵɵattribute(\"placeholder\", ctx_r2.filterPlaceholder)(\"aria-owns\", ctx_r2.id + \"_list\")(\"aria-label\", ctx_r2.ariaFilterLabel)(\"aria-activedescendant\", ctx_r2.focusedOptionId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.filterIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filterIconTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function Dropdown_ng_template_10_div_4_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_div_4_ng_container_1_Template, 2, 4, \"ng-container\", 23)(2, Dropdown_ng_template_10_div_4_ng_template_2_Template, 5, 7, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const builtInFilterElement_r11 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filterTemplate)(\"ngIfElse\", builtInFilterElement_r11);\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_ng_template_10_p_scroller_6_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 24);\n  }\n  if (rf & 2) {\n    const items_r13 = ctx.$implicit;\n    const scrollerOptions_r14 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const buildInItems_r15 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c13, items_r13, scrollerOptions_r14));\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_ng_template_10_p_scroller_6_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 24);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r16 = ctx.options;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c12, scrollerOptions_r16));\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_p_scroller_6_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 47);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction Dropdown_ng_template_10_p_scroller_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 46, 10);\n    i0.ɵɵlistener(\"onLazyLoad\", function Dropdown_ng_template_10_p_scroller_6_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, Dropdown_ng_template_10_p_scroller_6_ng_template_2_Template, 1, 5, \"ng-template\", 21)(3, Dropdown_ng_template_10_p_scroller_6_ng_container_3_Template, 2, 0, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c0, ctx_r2.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r2.visibleOptions())(\"itemSize\", ctx_r2.virtualScrollItemSize || ctx_r2._itemSize)(\"autoSize\", true)(\"lazy\", ctx_r2.lazy)(\"options\", ctx_r2.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loaderTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_ng_container_7_ng_container_1_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const buildInItems_r15 = i0.ɵɵreference(9);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c13, ctx_r2.visibleOptions(), i0.ɵɵpureFunction0(2, _c14)));\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getOptionGroupLabel(option_r17.optionGroup));\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 51);\n    i0.ɵɵtemplate(2, Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_span_2_Template, 2, 1, \"span\", 17)(3, Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_ng_container_3_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    const option_r17 = ctx_r17.$implicit;\n    const i_r19 = ctx_r17.index;\n    const scrollerOptions_r20 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c0, scrollerOptions_r20.itemSize + \"px\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_\" + ctx_r2.getOptionIndex(i_r19, scrollerOptions_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.groupTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c2, option_r17.optionGroup));\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdownItem\", 52);\n    i0.ɵɵlistener(\"onClick\", function Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_1_Template_p_dropdownItem_onClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const option_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onOptionSelect($event, option_r17));\n    })(\"onMouseEnter\", function Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_1_Template_p_dropdownItem_onMouseEnter_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const i_r19 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r20 = i0.ɵɵnextContext().options;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onOptionMouseEnter($event, ctx_r2.getOptionIndex(i_r19, scrollerOptions_r20)));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    const option_r17 = ctx_r17.$implicit;\n    const i_r19 = ctx_r17.index;\n    const scrollerOptions_r20 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r2.id + \"_\" + ctx_r2.getOptionIndex(i_r19, scrollerOptions_r20))(\"option\", option_r17)(\"selected\", ctx_r2.isSelected(option_r17))(\"label\", ctx_r2.getOptionLabel(option_r17))(\"disabled\", ctx_r2.isOptionDisabled(option_r17))(\"template\", ctx_r2.itemTemplate)(\"focused\", ctx_r2.focusedOptionIndex() === ctx_r2.getOptionIndex(i_r19, scrollerOptions_r20))(\"ariaPosInset\", ctx_r2.getAriaPosInset(ctx_r2.getOptionIndex(i_r19, scrollerOptions_r20)))(\"ariaSetSize\", ctx_r2.ariaSetSize);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_0_Template, 4, 9, \"ng-container\", 17)(1, Dropdown_ng_template_10_ng_template_8_ng_template_2_ng_container_1_Template, 2, 9, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const option_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", option_r17.group);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !option_r17.group);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.emptyFilterMessageLabel, \" \");\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 12);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 53);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_ng_template_8_li_3_ng_container_1_Template, 2, 1, \"ng-container\", 23)(2, Dropdown_ng_template_10_ng_template_8_li_3_ng_container_2_Template, 2, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r20 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r20.itemSize + \"px\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.emptyFilterTemplate && !ctx_r2.emptyTemplate)(\"ngIfElse\", ctx_r2.emptyFilter);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.emptyFilterTemplate || ctx_r2.emptyTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.emptyMessageLabel, \" \");\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 13);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 53);\n    i0.ɵɵtemplate(1, Dropdown_ng_template_10_ng_template_8_li_4_ng_container_1_Template, 2, 1, \"ng-container\", 23)(2, Dropdown_ng_template_10_ng_template_8_li_4_ng_container_2_Template, 2, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r20 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r20.itemSize + \"px\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.emptyTemplate)(\"ngIfElse\", ctx_r2.empty);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.emptyTemplate);\n  }\n}\nfunction Dropdown_ng_template_10_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 48, 11);\n    i0.ɵɵtemplate(2, Dropdown_ng_template_10_ng_template_8_ng_template_2_Template, 2, 2, \"ng-template\", 49)(3, Dropdown_ng_template_10_ng_template_8_li_3_Template, 3, 6, \"li\", 50)(4, Dropdown_ng_template_10_ng_template_8_li_4_Template, 3, 6, \"li\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const items_r22 = ctx.$implicit;\n    const scrollerOptions_r20 = ctx.options;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(scrollerOptions_r20.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r20.contentStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_list\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", items_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filterValue && ctx_r2.isEmpty());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.filterValue && ctx_r2.isEmpty());\n  }\n}\nfunction Dropdown_ng_template_10_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dropdown_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"span\", 37, 5);\n    i0.ɵɵlistener(\"focus\", function Dropdown_ng_template_10_Template_span_focus_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFirstHiddenFocus($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Dropdown_ng_template_10_ng_container_3_Template, 1, 0, \"ng-container\", 30)(4, Dropdown_ng_template_10_div_4_Template, 4, 2, \"div\", 38);\n    i0.ɵɵelementStart(5, \"div\", 39);\n    i0.ɵɵtemplate(6, Dropdown_ng_template_10_p_scroller_6_Template, 4, 10, \"p-scroller\", 40)(7, Dropdown_ng_template_10_ng_container_7_Template, 2, 6, \"ng-container\", 17)(8, Dropdown_ng_template_10_ng_template_8_Template, 5, 7, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, Dropdown_ng_template_10_ng_container_10_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementStart(11, \"span\", 37, 7);\n    i0.ɵɵlistener(\"focus\", function Dropdown_ng_template_10_Template_span_focus_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLastHiddenFocus($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-dropdown-panel p-component\")(\"ngStyle\", ctx_r2.panelStyle);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"tabindex\", 0)(\"data-p-hidden-accessible\", true)(\"data-p-hidden-focusable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filter);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"max-height\", ctx_r2.virtualScroll ? \"auto\" : ctx_r2.scrollHeight || \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.virtualScroll);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.virtualScroll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.footerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"tabindex\", 0)(\"data-p-hidden-accessible\", true)(\"data-p-hidden-focusable\", true);\n  }\n}\nconst DROPDOWN_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Dropdown),\n  multi: true\n};\nlet DropdownItem = /*#__PURE__*/(() => {\n  class DropdownItem {\n    id;\n    option;\n    selected;\n    focused;\n    label;\n    disabled;\n    visible;\n    itemSize;\n    ariaPosInset;\n    ariaSetSize;\n    template;\n    onClick = new EventEmitter();\n    onMouseEnter = new EventEmitter();\n    ngOnInit() {}\n    onOptionClick(event) {\n      this.onClick.emit(event);\n    }\n    onOptionMouseEnter(event) {\n      this.onMouseEnter.emit(event);\n    }\n    static ɵfac = function DropdownItem_Factory(t) {\n      return new (t || DropdownItem)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DropdownItem,\n      selectors: [[\"p-dropdownItem\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        id: \"id\",\n        option: \"option\",\n        selected: \"selected\",\n        focused: \"focused\",\n        label: \"label\",\n        disabled: \"disabled\",\n        visible: \"visible\",\n        itemSize: \"itemSize\",\n        ariaPosInset: \"ariaPosInset\",\n        ariaSetSize: \"ariaSetSize\",\n        template: \"template\"\n      },\n      outputs: {\n        onClick: \"onClick\",\n        onMouseEnter: \"onMouseEnter\"\n      },\n      decls: 3,\n      vars: 21,\n      consts: [[\"role\", \"option\", \"pRipple\", \"\", 3, \"click\", \"mouseenter\", \"id\", \"ngStyle\", \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function DropdownItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"li\", 0);\n          i0.ɵɵlistener(\"click\", function DropdownItem_Template_li_click_0_listener($event) {\n            return ctx.onOptionClick($event);\n          })(\"mouseenter\", function DropdownItem_Template_li_mouseenter_0_listener($event) {\n            return ctx.onOptionMouseEnter($event);\n          });\n          i0.ɵɵtemplate(1, DropdownItem_span_1_Template, 2, 1, \"span\", 1)(2, DropdownItem_ng_container_2_Template, 1, 0, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"id\", ctx.id)(\"ngStyle\", i0.ɵɵpureFunction1(13, _c0, ctx.itemSize + \"px\"))(\"ngClass\", i0.ɵɵpureFunction3(15, _c1, ctx.selected, ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"aria-label\", ctx.label)(\"aria-setsize\", ctx.ariaSetSize)(\"aria-posinset\", ctx.ariaPosInset)(\"aria-selected\", ctx.selected)(\"data-p-focused\", ctx.focused)(\"data-p-highlight\", ctx.selected)(\"data-p-disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.template);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(19, _c2, ctx.option));\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n      encapsulation: 2\n    });\n  }\n  return DropdownItem;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Dropdown also known as Select, is used to choose an item from a collection of options.\n * @group Components\n */\nlet Dropdown = /*#__PURE__*/(() => {\n  class Dropdown {\n    el;\n    renderer;\n    cd;\n    zone;\n    filterService;\n    config;\n    /**\n     * Unique identifier of the component\n     * @group Props\n     */\n    id;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    scrollHeight = '200px';\n    /**\n     * When specified, displays an input field to filter the items on keyup.\n     * @group Props\n     */\n    filter;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Inline style of the overlay panel element.\n     * @group Props\n     */\n    panelStyle;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    panelStyleClass;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    required;\n    /**\n     * When present, custom value instead of predefined options can be entered using the editable input field.\n     * @group Props\n     */\n    editable;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Default text to display when no option is selected.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Placeholder text to show when filter input is empty.\n     * @group Props\n     */\n    filterPlaceholder;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    filterLocale;\n    /**\n     * Identifier of the accessible input element.\n     * @group Props\n     */\n    inputId;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    filterBy;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    filterFields;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Clears the filter value when hiding the dropdown.\n     * @group Props\n     */\n    resetFilterOnHide = false;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    dropdownIcon;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    optionLabel;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    optionValue;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    optionDisabled;\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    optionGroupLabel = 'label';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    optionGroupChildren = 'items';\n    /**\n     * Whether to display the first item as the label if no placeholder is defined and value is null.\n     * @group Props\n     */\n    autoDisplayFirst = true;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    group;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    emptyFilterMessage = '';\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    emptyMessage = '';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.\n     * @group Props\n     */\n    overlayOptions;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    ariaFilterLabel;\n    /**\n     * Used to define a aria label attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    filterMatchMode = 'contains';\n    /**\n     * Maximum number of character allows in the editable input field.\n     * @group Props\n     */\n    maxlength;\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    tooltip = '';\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition = 'right';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    tooltipPositionStyle = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    focusOnHover = false;\n    /**\n     * Determines if the option will be selected on focus.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    autoOptionFocus = true;\n    /**\n     * Applies focus to the filter element when the overlay is shown.\n     * @group Props\n     */\n    autofocusFilter = true;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(_disabled) {\n      if (_disabled) {\n        this.focused = false;\n        if (this.overlayVisible) this.hide();\n      }\n      this._disabled = _disabled;\n      if (!this.cd.destroyed) {\n        this.cd.detectChanges();\n      }\n    }\n    /**\n     * Item size of item to be virtual scrolled.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(val) {\n      this._itemSize = val;\n      console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    _itemSize;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get autoZIndex() {\n      return this._autoZIndex;\n    }\n    set autoZIndex(val) {\n      this._autoZIndex = val;\n      console.warn('The autoZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _autoZIndex;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get baseZIndex() {\n      return this._baseZIndex;\n    }\n    set baseZIndex(val) {\n      this._baseZIndex = val;\n      console.warn('The baseZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _baseZIndex;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get showTransitionOptions() {\n      return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val) {\n      this._showTransitionOptions = val;\n      console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _showTransitionOptions;\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get hideTransitionOptions() {\n      return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val) {\n      this._hideTransitionOptions = val;\n      console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _hideTransitionOptions;\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    get filterValue() {\n      return this._filterValue();\n    }\n    set filterValue(val) {\n      this._filterValue.set(val);\n    }\n    /**\n     * An array of objects to display as the available options.\n     * @group Props\n     */\n    get options() {\n      const options = this._options();\n      return options;\n    }\n    set options(val) {\n      this._options.set(val);\n    }\n    /**\n     * Callback to invoke when value of dropdown changes.\n     * @param {DropdownChangeEvent} event - custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {DropdownFilterEvent} event - custom filter event.\n     * @group Emits\n     */\n    onFilter = new EventEmitter();\n    /**\n     * Callback to invoke when dropdown gets focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when dropdown loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when component is clicked.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to invoke when dropdown overlay gets visible.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dropdown overlay gets hidden.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when dropdown clears the value.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {DropdownLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    containerViewChild;\n    filterViewChild;\n    focusInputViewChild;\n    editableInputViewChild;\n    itemsViewChild;\n    scroller;\n    overlayViewChild;\n    firstHiddenFocusableElementOnOverlay;\n    lastHiddenFocusableElementOnOverlay;\n    templates;\n    _disabled;\n    itemsWrapper;\n    itemTemplate;\n    groupTemplate;\n    loaderTemplate;\n    selectedItemTemplate;\n    headerTemplate;\n    filterTemplate;\n    footerTemplate;\n    emptyFilterTemplate;\n    emptyTemplate;\n    dropdownIconTemplate;\n    clearIconTemplate;\n    filterIconTemplate;\n    filterOptions;\n    _options = signal(null);\n    modelValue = signal(null);\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    hover;\n    focused;\n    overlayVisible;\n    optionsChanged;\n    panel;\n    dimensionsUpdated;\n    hoveredItem;\n    selectedOptionUpdated;\n    _filterValue = signal(null);\n    searchValue;\n    searchIndex;\n    searchTimeout;\n    previousSearchChar;\n    currentSearchChar;\n    preventModelTouched;\n    focusedOptionIndex = signal(-1);\n    labelId;\n    listId;\n    get emptyMessageLabel() {\n      return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n    get emptyFilterMessageLabel() {\n      return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n    }\n    get filled() {\n      if (typeof this.modelValue() === 'string') return !!this.modelValue();\n      return this.modelValue() || this.modelValue() != null || this.modelValue() != undefined;\n    }\n    get isVisibleClearIcon() {\n      return this.modelValue() != null && this.hasSelectedOption() && this.showClear && !this.disabled;\n    }\n    get containerClass() {\n      return {\n        'p-dropdown p-component p-inputwrapper': true,\n        'p-disabled': this.disabled,\n        'p-dropdown-clearable': this.showClear && !this.disabled,\n        'p-focus': this.focused,\n        'p-inputwrapper-filled': this.modelValue(),\n        'p-inputwrapper-focus': this.focused || this.overlayVisible\n      };\n    }\n    get inputClass() {\n      const label = this.label();\n      return {\n        'p-dropdown-label p-inputtext': true,\n        'p-placeholder': this.placeholder && label === this.placeholder,\n        'p-dropdown-label-empty': !this.editable && !this.selectedItemTemplate && (!label || label === 'p-emptylabel' || label.length === 0)\n      };\n    }\n    get panelClass() {\n      return {\n        'p-dropdown-panel p-component': true,\n        'p-input-filled': this.config.inputStyle === 'filled',\n        'p-ripple-disabled': this.config.ripple === false\n      };\n    }\n    visibleOptions = computed(() => {\n      const options = this.group ? this.flatOptions(this.options) : this.options || [];\n      if (this._filterValue()) {\n        const filteredOptions = !this.filterBy && !this.filterFields && !this.optionValue ? this.options.filter(option => option.toLowerCase().indexOf(this._filterValue().toLowerCase()) !== -1) : this.filterService.filter(options, this.searchFields(), this._filterValue(), this.filterMatchMode, this.filterLocale);\n        if (this.group) {\n          const optionGroups = this.options || [];\n          const filtered = [];\n          optionGroups.forEach(group => {\n            const groupChildren = this.getOptionGroupChildren(group);\n            const filteredItems = groupChildren.filter(item => filteredOptions.includes(item));\n            if (filteredItems.length > 0) filtered.push({\n              ...group,\n              [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems]\n            });\n          });\n          return this.flatOptions(filtered);\n        }\n        return filteredOptions;\n      }\n      return options;\n    });\n    label = computed(() => {\n      const selectedOptionIndex = this.findSelectedOptionIndex();\n      return selectedOptionIndex !== -1 ? this.getOptionLabel(this.visibleOptions()[selectedOptionIndex]) : this.placeholder || 'p-emptylabel';\n    });\n    selectedOption;\n    constructor(el, renderer, cd, zone, filterService, config) {\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.zone = zone;\n      this.filterService = filterService;\n      this.config = config;\n      effect(() => {\n        const modelValue = this.modelValue();\n        const visibleOptions = this.visibleOptions();\n        if (modelValue && this.editable) {\n          this.updateEditableLabel();\n        }\n        if (visibleOptions && ObjectUtils.isNotEmpty(visibleOptions)) {\n          this.selectedOption = visibleOptions[this.findSelectedOptionIndex()];\n          this.cd.markForCheck();\n        }\n      });\n    }\n    ngOnInit() {\n      this.id = this.id || UniqueComponentId();\n      this.autoUpdateModel();\n      if (this.filterBy) {\n        this.filterOptions = {\n          filter: value => this.onFilterInputChange(value),\n          reset: () => this.resetFilter()\n        };\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.optionsChanged && this.overlayVisible) {\n        this.optionsChanged = false;\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => {\n            if (this.overlayViewChild) {\n              this.overlayViewChild.alignOverlay();\n            }\n          }, 1);\n        });\n      }\n      if (this.selectedOptionUpdated && this.itemsWrapper) {\n        let selectedItem = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, 'li.p-highlight');\n        if (selectedItem) {\n          DomHandler.scrollInView(this.itemsWrapper, selectedItem);\n        }\n        this.selectedOptionUpdated = false;\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'selectedItem':\n            this.selectedItemTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'filter':\n            this.filterTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'emptyfilter':\n            this.emptyFilterTemplate = item.template;\n            break;\n          case 'empty':\n            this.emptyTemplate = item.template;\n            break;\n          case 'group':\n            this.groupTemplate = item.template;\n            break;\n          case 'loader':\n            this.loaderTemplate = item.template;\n            break;\n          case 'dropdownicon':\n            this.dropdownIconTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'filtericon':\n            this.filterIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    flatOptions(options) {\n      return (options || []).reduce((result, option, index) => {\n        result.push({\n          optionGroup: option,\n          group: true,\n          index\n        });\n        const optionGroupChildren = this.getOptionGroupChildren(option);\n        optionGroupChildren && optionGroupChildren.forEach(o => result.push(o));\n        return result;\n      }, []);\n    }\n    autoUpdateModel() {\n      if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption()) {\n        this.focusedOptionIndex.set(this.findFirstFocusedOptionIndex());\n        this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()], false);\n      }\n      if (this.autoDisplayFirst && !this.modelValue()) {\n        const ind = this.findFirstOptionIndex();\n        this.onOptionSelect(null, this.visibleOptions()[ind], false, true);\n      }\n    }\n    onOptionSelect(event, option, isHide = true, preventChange = false) {\n      const value = this.getOptionValue(option);\n      this.updateModel(value, event);\n      this.focusedOptionIndex.set(this.findSelectedOptionIndex());\n      isHide && this.hide(true);\n      preventChange === false && this.onChange.emit({\n        originalEvent: event,\n        value: value\n      });\n    }\n    onOptionMouseEnter(event, index) {\n      if (this.focusOnHover) {\n        this.changeFocusedOptionIndex(event, index);\n      }\n    }\n    updateModel(value, event) {\n      this.value = value;\n      this.onModelChange(value);\n      this.modelValue.set(value);\n      this.selectedOptionUpdated = true;\n    }\n    writeValue(value) {\n      if (this.filter) {\n        this.resetFilter();\n      }\n      this.value = value;\n      this.allowModelChange() && this.onModelChange(value);\n      this.modelValue.set(this.value);\n      this.updateEditableLabel();\n      this.cd.markForCheck();\n    }\n    allowModelChange() {\n      return this.autoDisplayFirst && !this.placeholder && !this.modelValue() && !this.editable && this.options && this.options.length;\n    }\n    isSelected(option) {\n      return this.isValidOption(option) && ObjectUtils.equals(this.modelValue(), this.getOptionValue(option), this.equalityKey());\n    }\n    ngAfterViewInit() {\n      if (this.editable) {\n        this.updateEditableLabel();\n      }\n    }\n    updateEditableLabel() {\n      if (this.editableInputViewChild) {\n        this.editableInputViewChild.nativeElement.value = this.getOptionLabel(this.modelValue()) === undefined ? this.editableInputViewChild.nativeElement.value : this.getOptionLabel(this.modelValue());\n      }\n    }\n    getOptionIndex(index, scrollerOptions) {\n      return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n    getOptionLabel(option) {\n      return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option && option.label !== undefined ? option.label : option;\n    }\n    getOptionValue(option) {\n      return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n    isOptionDisabled(option) {\n      return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false;\n    }\n    getOptionGroupLabel(optionGroup) {\n      return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup;\n    }\n    getOptionGroupChildren(optionGroup) {\n      return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    getAriaPosInset(index) {\n      return (this.optionGroupLabel ? index - this.visibleOptions().slice(0, index).filter(option => this.isOptionGroup(option)).length : index) + 1;\n    }\n    get ariaSetSize() {\n      return this.visibleOptions().filter(option => !this.isOptionGroup(option)).length;\n    }\n    /**\n     * Callback to invoke on filter reset.\n     * @group Method\n     */\n    resetFilter() {\n      this._filterValue.set(null);\n      if (this.filterViewChild && this.filterViewChild.nativeElement) {\n        this.filterViewChild.nativeElement.value = '';\n      }\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    onContainerClick(event) {\n      if (this.disabled || this.readonly) {\n        return;\n      }\n      this.focusInputViewChild?.nativeElement.focus({\n        preventScroll: true\n      });\n      if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n        return;\n      } else if (!this.overlayViewChild || !this.overlayViewChild.el.nativeElement.contains(event.target)) {\n        this.overlayVisible ? this.hide(true) : this.show(true);\n      }\n      this.onClick.emit(event);\n      this.cd.detectChanges();\n    }\n    isEmpty() {\n      return !this._options() || this.visibleOptions() && this.visibleOptions().length === 0;\n    }\n    onEditableInput(event) {\n      const value = event.target.value;\n      this.searchValue = '';\n      const matched = this.searchOptions(event, value);\n      !matched && this.focusedOptionIndex.set(-1);\n      this.onModelChange(value);\n      this.updateModel(value, event);\n      this.onChange.emit({\n        originalEvent: event,\n        value: value\n      });\n    }\n    /**\n     * Displays the panel.\n     * @group Method\n     */\n    show(isFocus) {\n      this.overlayVisible = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      if (isFocus) {\n        DomHandler.focus(this.focusInputViewChild?.nativeElement);\n      }\n      this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n      if (event.toState === 'visible') {\n        this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-dropdown-items-wrapper');\n        this.virtualScroll && this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n        if (this.options && this.options.length) {\n          if (this.virtualScroll) {\n            const selectedIndex = this.modelValue() ? this.focusedOptionIndex() : -1;\n            if (selectedIndex !== -1) {\n              this.scroller?.scrollToIndex(selectedIndex);\n            }\n          } else {\n            let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-dropdown-item.p-highlight');\n            if (selectedListItem) {\n              selectedListItem.scrollIntoView({\n                block: 'nearest',\n                inline: 'nearest'\n              });\n            }\n          }\n        }\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n          this.preventModelTouched = true;\n          if (this.autofocusFilter) {\n            this.filterViewChild.nativeElement.focus();\n          }\n        }\n        this.onShow.emit(event);\n      }\n      if (event.toState === 'void') {\n        this.itemsWrapper = null;\n        this.onModelTouched();\n        this.onHide.emit(event);\n      }\n    }\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    hide(isFocus) {\n      this.overlayVisible = false;\n      this.focusedOptionIndex.set(-1);\n      if (this.filter && this.resetFilterOnHide) {\n        this.resetFilter();\n      }\n      isFocus && DomHandler.focus(this.focusInputViewChild?.nativeElement);\n      this.cd.markForCheck();\n    }\n    onInputFocus(event) {\n      if (this.disabled) {\n        // For ScreenReaders\n        return;\n      }\n      this.focused = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n      this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      this.overlayVisible === false && this.onBlur.emit(event);\n      if (!this.preventModelTouched) {\n        this.onModelTouched();\n      }\n      this.preventModelTouched = false;\n    }\n    onKeyDown(event, search) {\n      if (this.disabled || this.readonly) {\n        return;\n      }\n      switch (event.code) {\n        //down\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        //up\n        case 'ArrowUp':\n          this.onArrowUpKey(event, this.editable);\n          break;\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          this.onArrowLeftKey(event, this.editable);\n          break;\n        case 'Delete':\n          this.onDeleteKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event, this.editable);\n          break;\n        case 'End':\n          this.onEndKey(event, this.editable);\n          break;\n        case 'PageDown':\n          this.onPageDownKey(event);\n          break;\n        case 'PageUp':\n          this.onPageUpKey(event);\n          break;\n        //space\n        case 'Space':\n          this.onSpaceKey(event, search);\n          break;\n        //enter\n        case 'Enter':\n        case 'NumpadEnter':\n          this.onEnterKey(event);\n          break;\n        //escape and tab\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event);\n          break;\n        case 'Backspace':\n          this.onBackspaceKey(event, this.editable);\n          break;\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          //NOOP\n          break;\n        default:\n          if (!event.metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n            !this.overlayVisible && this.show();\n            !this.editable && this.searchOptions(event, event.key);\n          }\n          break;\n      }\n    }\n    onFilterKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event, true);\n          break;\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          this.onArrowLeftKey(event, true);\n          break;\n        case 'Home':\n          this.onHomeKey(event, true);\n          break;\n        case 'End':\n          this.onEndKey(event, true);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event, true);\n          break;\n        default:\n          break;\n      }\n    }\n    onFilterBlur(event) {\n      this.focusedOptionIndex.set(-1);\n    }\n    onArrowDownKey(event) {\n      const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n      this.changeFocusedOptionIndex(event, optionIndex);\n      !this.overlayVisible && this.show();\n      event.preventDefault();\n    }\n    changeFocusedOptionIndex(event, index) {\n      if (this.focusedOptionIndex() !== index) {\n        this.focusedOptionIndex.set(index);\n        this.scrollInView();\n        if (this.selectOnFocus) {\n          const option = this.visibleOptions()[index];\n          this.onOptionSelect(event, option, false);\n        }\n      }\n    }\n    get virtualScrollerDisabled() {\n      return !this.virtualScroll;\n    }\n    scrollInView(index = -1) {\n      const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n      if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n        const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n          element.scrollIntoView && element.scrollIntoView({\n            block: 'nearest',\n            inline: 'nearest'\n          });\n        } else if (!this.virtualScrollerDisabled) {\n          setTimeout(() => {\n            this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n          }, 0);\n        }\n      }\n    }\n    get focusedOptionId() {\n      return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n    hasSelectedOption() {\n      return this.modelValue() !== undefined;\n    }\n    isValidSelectedOption(option) {\n      return this.isValidOption(option) && this.isSelected(option);\n    }\n    equalityKey() {\n      return this.optionValue ? null : this.dataKey;\n    }\n    findFirstFocusedOptionIndex() {\n      const selectedIndex = this.findSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n    findFirstOptionIndex() {\n      return this.visibleOptions().findIndex(option => this.isValidOption(option));\n    }\n    findSelectedOptionIndex() {\n      return this.hasSelectedOption() ? this.visibleOptions().findIndex(option => this.isValidSelectedOption(option)) : -1;\n    }\n    findNextOptionIndex(index) {\n      const matchedOptionIndex = index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n    findPrevOptionIndex(index) {\n      const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    findLastOptionIndex() {\n      return ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidOption(option));\n    }\n    findLastFocusedOptionIndex() {\n      const selectedIndex = this.findSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n    isValidOption(option) {\n      return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n    isOptionGroup(option) {\n      return this.optionGroupLabel && option.optionGroup && option.group;\n    }\n    onArrowUpKey(event, pressedInInputText = false) {\n      if (event.altKey && !pressedInInputText) {\n        if (this.focusedOptionIndex() !== -1) {\n          const option = this.visibleOptions()[this.focusedOptionIndex()];\n          this.onOptionSelect(event, option);\n        }\n        this.overlayVisible && this.hide();\n        event.preventDefault();\n      } else {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n        this.changeFocusedOptionIndex(event, optionIndex);\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n      }\n    }\n    onArrowLeftKey(event, pressedInInputText = false) {\n      pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n    onDeleteKey(event) {\n      if (this.showClear) {\n        this.clear(event);\n        event.preventDefault();\n      }\n    }\n    onHomeKey(event, pressedInInputText = false) {\n      if (pressedInInputText) {\n        event.currentTarget.setSelectionRange(0, 0);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        this.changeFocusedOptionIndex(event, this.findFirstOptionIndex());\n        !this.overlayVisible && this.show();\n      }\n      event.preventDefault();\n    }\n    onEndKey(event, pressedInInputText = false) {\n      if (pressedInInputText) {\n        const target = event.currentTarget;\n        const len = target.value.length;\n        target.setSelectionRange(len, len);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        this.changeFocusedOptionIndex(event, this.findLastOptionIndex());\n        !this.overlayVisible && this.show();\n      }\n      event.preventDefault();\n    }\n    onPageDownKey(event) {\n      this.scrollInView(this.visibleOptions().length - 1);\n      event.preventDefault();\n    }\n    onPageUpKey(event) {\n      this.scrollInView(0);\n      event.preventDefault();\n    }\n    onSpaceKey(event, pressedInInputText = false) {\n      !this.editable && !pressedInInputText && this.onEnterKey(event);\n    }\n    onEnterKey(event) {\n      if (!this.overlayVisible) {\n        this.onArrowDownKey(event);\n      } else {\n        if (this.focusedOptionIndex() !== -1) {\n          const option = this.visibleOptions()[this.focusedOptionIndex()];\n          this.onOptionSelect(event, option);\n        }\n        this.hide();\n      }\n      event.preventDefault();\n    }\n    onEscapeKey(event) {\n      this.overlayVisible && this.hide(true);\n      event.preventDefault();\n    }\n    onTabKey(event, pressedInInputText = false) {\n      if (!pressedInInputText) {\n        if (this.overlayVisible && this.hasFocusableElements()) {\n          DomHandler.focus(event.shiftKey ? this.lastHiddenFocusableElementOnOverlay.nativeElement : this.firstHiddenFocusableElementOnOverlay.nativeElement);\n          event.preventDefault();\n        } else {\n          if (this.focusedOptionIndex() !== -1) {\n            const option = this.visibleOptions()[this.focusedOptionIndex()];\n            this.onOptionSelect(event, option);\n          }\n          this.overlayVisible && this.hide(this.filter);\n        }\n      }\n    }\n    onFirstHiddenFocus(event) {\n      const focusableEl = event.relatedTarget === this.focusInputViewChild?.nativeElement ? DomHandler.getFirstFocusableElement(this.overlayViewChild.el.nativeElement, ':not(.p-hidden-focusable)') : this.focusInputViewChild.nativeElement;\n      DomHandler.focus(focusableEl);\n    }\n    onLastHiddenFocus(event) {\n      const focusableEl = event.relatedTarget === this.focusInputViewChild?.nativeElement ? DomHandler.getLastFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])') : this.focusInputViewChild?.nativeElement;\n      DomHandler.focus(focusableEl);\n    }\n    hasFocusableElements() {\n      return DomHandler.getFocusableElements(this.overlayViewChild.overlayViewChild.nativeElement, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n    }\n    onBackspaceKey(event, pressedInInputText = false) {\n      if (pressedInInputText) {\n        !this.overlayVisible && this.show();\n      }\n    }\n    searchFields() {\n      return this.filterFields || [this.optionLabel];\n    }\n    searchOptions(event, char) {\n      this.searchValue = (this.searchValue || '') + char;\n      let optionIndex = -1;\n      let matched = false;\n      if (this.focusedOptionIndex() !== -1) {\n        optionIndex = this.visibleOptions().slice(this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option));\n        optionIndex = optionIndex === -1 ? this.visibleOptions().slice(0, this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option)) : optionIndex + this.focusedOptionIndex();\n      } else {\n        optionIndex = this.visibleOptions().findIndex(option => this.isOptionMatched(option));\n      }\n      if (optionIndex !== -1) {\n        matched = true;\n      }\n      if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n        optionIndex = this.findFirstFocusedOptionIndex();\n      }\n      if (optionIndex !== -1) {\n        this.changeFocusedOptionIndex(event, optionIndex);\n      }\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.searchValue = '';\n        this.searchTimeout = null;\n      }, 500);\n      return matched;\n    }\n    isOptionMatched(option) {\n      return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n    onFilterInputChange(event) {\n      let value = event.target.value?.trim();\n      this._filterValue.set(value);\n      this.focusedOptionIndex.set(-1);\n      this.onFilter.emit({\n        originalEvent: event,\n        filter: this._filterValue()\n      });\n      !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n      this.cd.markForCheck();\n    }\n    applyFocus() {\n      if (this.editable) DomHandler.findSingle(this.el.nativeElement, '.p-dropdown-label.p-inputtext').focus();else DomHandler.focus(this.focusInputViewChild?.nativeElement);\n    }\n    /**\n     * Applies focus.\n     * @group Method\n     */\n    focus() {\n      this.applyFocus();\n    }\n    clear(event) {\n      this.updateModel(null, event);\n      this.updateEditableLabel();\n      this.onChange.emit({\n        originalEvent: event,\n        value: this.value\n      });\n      this.onClear.emit(event);\n    }\n    static ɵfac = function Dropdown_Factory(t) {\n      return new (t || Dropdown)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.FilterService), i0.ɵɵdirectiveInject(i3.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Dropdown,\n      selectors: [[\"p-dropdown\"]],\n      contentQueries: function Dropdown_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Dropdown_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n          i0.ɵɵviewQuery(_c8, 5);\n          i0.ɵɵviewQuery(_c9, 5);\n          i0.ɵɵviewQuery(_c10, 5);\n          i0.ɵɵviewQuery(_c11, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.focusInputViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editableInputViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.firstHiddenFocusableElementOnOverlay = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lastHiddenFocusableElementOnOverlay = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 4,\n      hostBindings: function Dropdown_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused || ctx.overlayVisible);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        scrollHeight: \"scrollHeight\",\n        filter: \"filter\",\n        name: \"name\",\n        style: \"style\",\n        panelStyle: \"panelStyle\",\n        styleClass: \"styleClass\",\n        panelStyleClass: \"panelStyleClass\",\n        readonly: \"readonly\",\n        required: \"required\",\n        editable: \"editable\",\n        appendTo: \"appendTo\",\n        tabindex: \"tabindex\",\n        placeholder: \"placeholder\",\n        filterPlaceholder: \"filterPlaceholder\",\n        filterLocale: \"filterLocale\",\n        inputId: \"inputId\",\n        dataKey: \"dataKey\",\n        filterBy: \"filterBy\",\n        filterFields: \"filterFields\",\n        autofocus: \"autofocus\",\n        resetFilterOnHide: \"resetFilterOnHide\",\n        dropdownIcon: \"dropdownIcon\",\n        optionLabel: \"optionLabel\",\n        optionValue: \"optionValue\",\n        optionDisabled: \"optionDisabled\",\n        optionGroupLabel: \"optionGroupLabel\",\n        optionGroupChildren: \"optionGroupChildren\",\n        autoDisplayFirst: \"autoDisplayFirst\",\n        group: \"group\",\n        showClear: \"showClear\",\n        emptyFilterMessage: \"emptyFilterMessage\",\n        emptyMessage: \"emptyMessage\",\n        lazy: \"lazy\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        overlayOptions: \"overlayOptions\",\n        ariaFilterLabel: \"ariaFilterLabel\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        filterMatchMode: \"filterMatchMode\",\n        maxlength: \"maxlength\",\n        tooltip: \"tooltip\",\n        tooltipPosition: \"tooltipPosition\",\n        tooltipPositionStyle: \"tooltipPositionStyle\",\n        tooltipStyleClass: \"tooltipStyleClass\",\n        focusOnHover: \"focusOnHover\",\n        selectOnFocus: \"selectOnFocus\",\n        autoOptionFocus: \"autoOptionFocus\",\n        autofocusFilter: \"autofocusFilter\",\n        disabled: \"disabled\",\n        itemSize: \"itemSize\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        filterValue: \"filterValue\",\n        options: \"options\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onFilter: \"onFilter\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onClick: \"onClick\",\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        onClear: \"onClear\",\n        onLazyLoad: \"onLazyLoad\"\n      },\n      features: [i0.ɵɵProvidersFeature([DROPDOWN_VALUE_ACCESSOR])],\n      decls: 11,\n      vars: 20,\n      consts: [[\"container\", \"\"], [\"overlay\", \"\"], [\"focusInput\", \"\"], [\"defaultPlaceholder\", \"\"], [\"editableInput\", \"\"], [\"firstHiddenFocusableEl\", \"\"], [\"buildInItems\", \"\"], [\"lastHiddenFocusableEl\", \"\"], [\"builtInFilterElement\", \"\"], [\"filter\", \"\"], [\"scroller\", \"\"], [\"items\", \"\"], [\"emptyFilter\", \"\"], [\"empty\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [\"role\", \"combobox\", \"pAutoFocus\", \"\", 3, \"ngClass\", \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\", \"autofocus\", \"focus\", \"blur\", \"keydown\", 4, \"ngIf\"], [\"type\", \"text\", \"aria-haspopup\", \"listbox\", 3, \"ngClass\", \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\", 4, \"ngIf\"], [4, \"ngIf\"], [\"role\", \"button\", \"aria-label\", \"dropdown trigger\", \"aria-haspopup\", \"listbox\", 1, \"p-dropdown-trigger\"], [\"class\", \"p-dropdown-trigger-icon\", 4, \"ngIf\"], [3, \"visibleChange\", \"onAnimationStart\", \"onHide\", \"visible\", \"options\", \"target\", \"appendTo\", \"autoZIndex\", \"baseZIndex\", \"showTransitionOptions\", \"hideTransitionOptions\"], [\"pTemplate\", \"content\"], [\"role\", \"combobox\", \"pAutoFocus\", \"\", 3, \"focus\", \"blur\", \"keydown\", \"ngClass\", \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\", \"autofocus\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"text\", \"aria-haspopup\", \"listbox\", 3, \"input\", \"keydown\", \"focus\", \"blur\", \"ngClass\", \"disabled\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-dropdown-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"styleClass\"], [1, \"p-dropdown-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-dropdown-trigger-icon\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [1, \"p-dropdown-trigger-icon\", 3, \"ngClass\"], [3, \"styleClass\"], [1, \"p-dropdown-trigger-icon\"], [3, \"ngClass\", \"ngStyle\"], [\"role\", \"presentation\", 1, \"p-hidden-accessible\", \"p-hidden-focusable\", 3, \"focus\"], [\"class\", \"p-dropdown-header\", 3, \"click\", 4, \"ngIf\"], [1, \"p-dropdown-items-wrapper\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [1, \"p-dropdown-header\", 3, \"click\"], [1, \"p-dropdown-filter-container\"], [\"type\", \"text\", \"autocomplete\", \"off\", 1, \"p-dropdown-filter\", \"p-inputtext\", \"p-component\", 3, \"input\", \"keydown\", \"blur\", \"value\"], [\"class\", \"p-dropdown-filter-icon\", 4, \"ngIf\"], [1, \"p-dropdown-filter-icon\"], [3, \"onLazyLoad\", \"items\", \"itemSize\", \"autoSize\", \"lazy\", \"options\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", 1, \"p-dropdown-items\", 3, \"ngClass\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-dropdown-empty-message\", 3, \"ngStyle\", 4, \"ngIf\"], [\"role\", \"option\", 1, \"p-dropdown-item-group\", 3, \"ngStyle\"], [3, \"onClick\", \"onMouseEnter\", \"id\", \"option\", \"selected\", \"label\", \"disabled\", \"template\", \"focused\", \"ariaPosInset\", \"ariaSetSize\"], [1, \"p-dropdown-empty-message\", 3, \"ngStyle\"]],\n      template: function Dropdown_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 14, 0);\n          i0.ɵɵlistener(\"click\", function Dropdown_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onContainerClick($event));\n          });\n          i0.ɵɵtemplate(2, Dropdown_span_2_Template, 6, 21, \"span\", 15)(3, Dropdown_input_3_Template, 2, 5, \"input\", 16)(4, Dropdown_ng_container_4_Template, 3, 2, \"ng-container\", 17);\n          i0.ɵɵelementStart(5, \"div\", 18);\n          i0.ɵɵtemplate(6, Dropdown_ng_container_6_Template, 3, 2, \"ng-container\", 17)(7, Dropdown_span_7_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p-overlay\", 20, 1);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function Dropdown_Template_p_overlay_visibleChange_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.overlayVisible, $event) || (ctx.overlayVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onAnimationStart\", function Dropdown_Template_p_overlay_onAnimationStart_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOverlayAnimationStart($event));\n          })(\"onHide\", function Dropdown_Template_p_overlay_onHide_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hide());\n          });\n          i0.ɵɵtemplate(10, Dropdown_ng_template_10_Template, 13, 19, \"ng-template\", 21);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.editable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisibleClearIcon);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-expanded\", ctx.overlayVisible)(\"data-pc-section\", \"trigger\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.dropdownIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dropdownIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.overlayVisible);\n          i0.ɵɵproperty(\"options\", ctx.overlayOptions)(\"target\", \"@parent\")(\"appendTo\", ctx.appendTo)(\"autoZIndex\", ctx.autoZIndex)(\"baseZIndex\", ctx.baseZIndex)(\"showTransitionOptions\", ctx.showTransitionOptions)(\"hideTransitionOptions\", ctx.hideTransitionOptions);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i4.Overlay, i3.PrimeTemplate, i5.Tooltip, i6.Scroller, i7.AutoFocus, TimesIcon, ChevronDownIcon, SearchIcon, DropdownItem],\n      styles: [\"@layer primeng{.p-dropdown{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-dropdown-clear-icon{position:absolute;top:50%;margin-top:-.5rem}.p-dropdown-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-dropdown-label{display:block;white-space:nowrap;overflow:hidden;flex:1 1 auto;width:1%;text-overflow:ellipsis;cursor:pointer}.p-dropdown-label-empty{overflow:hidden;opacity:0}input.p-dropdown-label{cursor:default}.p-dropdown .p-dropdown-panel{min-width:100%}.p-dropdown-items-wrapper{overflow:auto}.p-dropdown-item{cursor:pointer;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-dropdown-item-group{cursor:auto}.p-dropdown-items{margin:0;padding:0;list-style-type:none}.p-dropdown-filter{width:100%}.p-dropdown-filter-container{position:relative}.p-dropdown-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-fluid .p-dropdown{display:flex}.p-fluid .p-dropdown .p-dropdown-label{width:1%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Dropdown;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet DropdownModule = /*#__PURE__*/(() => {\n  class DropdownModule {\n    static ɵfac = function DropdownModule_Factory(t) {\n      return new (t || DropdownModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DropdownModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, OverlayModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, AutoFocusModule, TimesIcon, ChevronDownIcon, SearchIcon, OverlayModule, SharedModule, ScrollerModule]\n    });\n  }\n  return DropdownModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DROPDOWN_VALUE_ACCESSOR, Dropdown, DropdownItem, DropdownModule };\n//# sourceMappingURL=primeng-dropdown.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}