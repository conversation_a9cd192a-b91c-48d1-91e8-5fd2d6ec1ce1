{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class OpportunitiesDocumentFlowComponent {\n  constructor() {\n    this.tableData = [];\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function OpportunitiesDocumentFlowComponent_Factory(t) {\n      return new (t || OpportunitiesDocumentFlowComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesDocumentFlowComponent,\n      selectors: [[\"app-opportunities-document-flow\"]],\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"]],\n      template: function OpportunitiesDocumentFlowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Document Flow\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["OpportunitiesDocumentFlowComponent", "constructor", "tableData", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "OpportunitiesDocumentFlowComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-document-flow\\opportunities-document-flow.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-document-flow\\opportunities-document-flow.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\ninterface AccountTableData {\r\n  FileIcon?: string;\r\n  Title?: string;\r\n  Type?: string;\r\n  ChangedOn?: string;\r\n  ChangedBy?: string;\r\n  Action?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-opportunities-document-flow',\r\n  templateUrl: './opportunities-document-flow.component.html',\r\n  styleUrl: './opportunities-document-flow.component.scss',\r\n})\r\nexport class OpportunitiesDocumentFlowComponent implements OnInit {\r\n  tableData: AccountTableData[] = [];\r\n\r\n  ngOnInit() {}\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Document Flow</h4>\r\n    </div>\r\n\r\n</div>"], "mappings": ";AAgBA,OAAM,MAAOA,kCAAkC;EAL/CC,YAAA;IAME,KAAAC,SAAS,GAAuB,EAAE;;EAElCC,QAAQA,CAAA,GAAI;;;uBAHDH,kCAAkC;IAAA;EAAA;;;YAAlCA,kCAAkC;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdvCE,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAGpEF,EAHoE,CAAAG,YAAA,EAAK,EAC/D,EAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}