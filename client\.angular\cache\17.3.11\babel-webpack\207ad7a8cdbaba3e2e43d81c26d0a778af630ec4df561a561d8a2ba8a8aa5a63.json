{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CompetitorsComponent } from './competitors.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CompetitorsComponent\n}];\nexport let CompetitorsRoutingModule = /*#__PURE__*/(() => {\n  class CompetitorsRoutingModule {\n    static {\n      this.ɵfac = function CompetitorsRoutingModule_Factory(t) {\n        return new (t || CompetitorsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CompetitorsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return CompetitorsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}