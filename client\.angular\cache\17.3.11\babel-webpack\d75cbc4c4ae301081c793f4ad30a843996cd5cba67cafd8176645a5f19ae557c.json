{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountRoutingModule } from './account-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AccountComponent } from './account.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ToastModule } from 'primeng/toast';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { DialogModule } from 'primeng/dialog';\nimport { EditorModule } from 'primeng/editor';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { AccountSharedModule } from './account-shared.module';\nlet AccountModule = class AccountModule {};\nAccountModule = __decorate([NgModule({\n  declarations: [AccountComponent],\n  imports: [CommonModule, AccountSharedModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, NgSelectModule, ButtonModule, TabViewModule, ToastModule, CheckboxModule, ConfirmDialogModule, AutoCompleteModule, InputTextModule, ProgressSpinnerModule, SidebarModule, DialogModule, EditorModule, SharedModule],\n  providers: [MessageService, ConfirmationService]\n})], AccountModule);\nexport { AccountModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "NgSelectModule", "AccountRoutingModule", "FormsModule", "ReactiveFormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "AccountComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "ToastModule", "CheckboxModule", "SidebarModule", "DialogModule", "EditorModule", "MessageService", "ConfirmationService", "ProgressSpinnerModule", "ConfirmDialogModule", "SharedModule", "AccountSharedModule", "AccountModule", "__decorate", "declarations", "imports", "providers"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { AccountRoutingModule } from './account-routing.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { AccountComponent } from './account.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { AccountSharedModule } from './account-shared.module';\r\n@NgModule({\r\n  declarations: [\r\n    AccountComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AccountSharedModule,\r\n    AccountRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    ToastModule,\r\n    CheckboxModule,\r\n    ConfirmDialogModule,\r\n    AutoCompleteModule,\r\n    InputTextModule,\r\n    ProgressSpinnerModule,\r\n    SidebarModule,\r\n    DialogModule,\r\n    EditorModule,\r\n    SharedModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class AccountModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,mBAAmB,QAAQ,yBAAyB;AA+BtD,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAI;AAAjBA,aAAa,GAAAC,UAAA,EA9BzB3B,QAAQ,CAAC;EACR4B,YAAY,EAAE,CACZlB,gBAAgB,CACjB;EACDmB,OAAO,EAAE,CACP5B,YAAY,EACZwB,mBAAmB,EACnBtB,oBAAoB,EACpBG,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXL,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdL,cAAc,EACdU,YAAY,EACZE,aAAa,EACbC,WAAW,EACXC,cAAc,EACdO,mBAAmB,EACnBZ,kBAAkB,EAClBE,eAAe,EACfS,qBAAqB,EACrBL,aAAa,EACbC,YAAY,EACZC,YAAY,EACZK,YAAY,CACb;EACDM,SAAS,EAAE,CAACV,cAAc,EAAEC,mBAAmB;CAChD,CAAC,C,EACWK,aAAa,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}