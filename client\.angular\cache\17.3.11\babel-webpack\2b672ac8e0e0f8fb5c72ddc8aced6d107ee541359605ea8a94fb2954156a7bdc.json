{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsListingComponent } from './service-tickets-listing/service-tickets-listing.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ServiceTicketsListingComponent\n}];\nexport class ServiceTicketsListingRoutingModule {\n  static {\n    this.ɵfac = function ServiceTicketsListingRoutingModule_Factory(t) {\n      return new (t || ServiceTicketsListingRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsListingRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsListingRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceTicketsListingComponent", "routes", "path", "component", "ServiceTicketsListingRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets-listing\\service-tickets-listing-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceTicketsListingComponent } from './service-tickets-listing/service-tickets-listing.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ServiceTicketsListingComponent,\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceTicketsListingRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,8BAA8B,QAAQ,6DAA6D;;;AAE5G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,kCAAkC;;;uBAAlCA,kCAAkC;IAAA;EAAA;;;YAAlCA;IAAkC;EAAA;;;gBAHnCL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,kCAAkC;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFnCT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}