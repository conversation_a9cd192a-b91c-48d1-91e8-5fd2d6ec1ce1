{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.menu.service\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵproperty(\"item\", item_r2)(\"index\", i_r3)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2)(2, AppMenuComponent_ng_container_1_li_2_Template, 1, 0, \"li\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.separator);\n  }\n}\nexport class AppMenuComponent {\n  ngOnInit() {\n    this.model = [{\n      // label: 'Apps',\n      icon: 'pi pi-th-large',\n      items: [{\n        label: 'Dashboard',\n        icon: 'space_dashboard',\n        routerLink: ['/store/dashboard'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Home',\n        icon: 'home',\n        routerLink: ['/store'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Prospects',\n        icon: 'person_search',\n        routerLink: ['/store/prospects'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Account',\n        icon: 'person',\n        routerLink: ['/store/account'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Contacts',\n        icon: 'contact_phone',\n        routerLink: ['/store/contacts'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Activities',\n        icon: 'diversity_2',\n        routerLink: ['/store/activities'],\n        command: event => this.setActiveMenu(event.item.label),\n        items: [{\n          label: 'Appointments',\n          icon: 'event',\n          routerLink: ['/store/activities/appointments']\n        }, {\n          label: 'E-Mails',\n          icon: 'email',\n          routerLink: ['/store/activities/emails']\n        }, {\n          label: 'Sales Call',\n          icon: 'support_agent',\n          routerLink: ['/store/activities/calls']\n        }, {\n          label: 'Tasks',\n          icon: 'task',\n          routerLink: ['/store/activities/tasks']\n        }]\n      }, {\n        label: 'Opportunities',\n        icon: 'wb_incandescent',\n        routerLink: ['/store/opportunities'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Ai Insights',\n        icon: 'analytics',\n        routerLink: ['/store/ai-insights'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Sales Quotes',\n        icon: 'request_quote',\n        routerLink: ['/store/sales-quotes'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Sales Orders',\n        icon: 'list_alt',\n        routerLink: ['/store/sales-orders'],\n        command: event => this.setActiveMenu(event.item.label)\n      }]\n    }, {\n      label: 'Services Tickets Menu List',\n      items: [{\n        label: 'Identify Account',\n        icon: 'person_search',\n        routerLink: ['/store/identify-account'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Interaction Details',\n        icon: 'phone_in_talk',\n        routerLink: ['/store/service-tickets/overview'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Account',\n        icon: 'manage_accounts',\n        routerLink: ['/store'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Opportunities',\n        icon: 'request_quote',\n        routerLink: ['/store'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Sales Quotes',\n        icon: 'person',\n        routerLink: ['/store'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Services Ticket',\n        icon: 'phone_in_talk',\n        routerLink: ['/store/service-tickets/list'],\n        command: event => this.setActiveMenu(event.item.label)\n      }, {\n        label: 'Sales Order',\n        icon: 'confirmation_number',\n        routerLink: ['/store'],\n        command: event => this.setActiveMenu(event.item.label)\n      }]\n    }];\n  }\n  constructor(menuService) {\n    this.menuService = menuService;\n    this.model = [];\n  }\n  setActiveMenu(menuName) {\n    this.menuService.setActiveMenu(menuName);\n  }\n  static {\n    this.ɵfac = function AppMenuComponent_Factory(t) {\n      return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.MenuService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppMenuComponent,\n      selectors: [[\"app-menu\"]],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"]],\n      template: function AppMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0);\n          i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "item_r2", "i_r3", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_ng_container_1_li_1_Template", "AppMenuComponent_ng_container_1_li_2_Template", "ɵɵadvance", "separator", "AppMenuComponent", "ngOnInit", "model", "icon", "items", "label", "routerLink", "command", "event", "setActiveMenu", "item", "constructor", "menuService", "menuName", "ɵɵdirectiveInject", "i1", "MenuService", "selectors", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "AppMenuComponent_ng_container_1_Template", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\app.menu.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\app.menu.component.html"], "sourcesContent": ["import { OnInit } from '@angular/core';\r\nimport { Component } from '@angular/core';\r\nimport { MenuService } from './app.menu.service';\r\n\r\n@Component({\r\n  selector: 'app-menu',\r\n  templateUrl: './app.menu.component.html',\r\n})\r\nexport class AppMenuComponent implements OnInit {\r\n  model: any[] = [];\r\n\r\n  ngOnInit() {\r\n    this.model = [\r\n      {\r\n        // label: 'Apps',\r\n        icon: 'pi pi-th-large',\r\n        items: [\r\n          {\r\n            label: 'Dashboard',\r\n            icon: 'space_dashboard',\r\n            routerLink: ['/store/dashboard'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Home',\r\n            icon: 'home',\r\n            routerLink: ['/store'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Prospects',\r\n            icon: 'person_search',\r\n            routerLink: ['/store/prospects'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Account',\r\n            icon: 'person',\r\n            routerLink: ['/store/account'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Contacts',\r\n            icon: 'contact_phone',\r\n            routerLink: ['/store/contacts'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Activities',\r\n            icon: 'diversity_2',\r\n            routerLink: ['/store/activities'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n            items: [\r\n              {\r\n                label: 'Appointments',\r\n                icon: 'event',\r\n                routerLink: ['/store/activities/appointments'],\r\n              },\r\n              {\r\n                label: 'E-Mails',\r\n                icon: 'email',\r\n                routerLink: ['/store/activities/emails'],\r\n              },\r\n              {\r\n                label: 'Sales Call',\r\n                icon: 'support_agent',\r\n                routerLink: ['/store/activities/calls'],\r\n              },\r\n              {\r\n                label: 'Tasks',\r\n                icon: 'task',\r\n                routerLink: ['/store/activities/tasks'],\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            label: 'Opportunities',\r\n            icon: 'wb_incandescent',\r\n            routerLink: ['/store/opportunities'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Ai Insights',\r\n            icon: 'analytics',\r\n            routerLink: ['/store/ai-insights'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Sales Quotes',\r\n            icon: 'request_quote',\r\n            routerLink: ['/store/sales-quotes'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Sales Orders',\r\n            icon: 'list_alt',\r\n            routerLink: ['/store/sales-orders'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        label: 'Services Tickets Menu List',\r\n        items: [\r\n          {\r\n            label: 'Identify Account',\r\n            icon: 'person_search',\r\n            routerLink: ['/store/identify-account'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Interaction Details',\r\n            icon: 'phone_in_talk',\r\n            routerLink: ['/store/service-tickets/overview'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Account',\r\n            icon: 'manage_accounts',\r\n            routerLink: ['/store'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Opportunities',\r\n            icon: 'request_quote',\r\n            routerLink: ['/store'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Sales Quotes',\r\n            icon: 'person',\r\n            routerLink: ['/store'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Services Ticket',\r\n            icon: 'phone_in_talk',\r\n            routerLink: ['/store/service-tickets/list'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n          {\r\n            label: 'Sales Order',\r\n            icon: 'confirmation_number',\r\n            routerLink: ['/store'],\r\n            command: (event: any) => this.setActiveMenu(event.item.label),\r\n          },\r\n        ],\r\n      },\r\n    ];\r\n  }\r\n\r\n  constructor(private menuService: MenuService) {}\r\n\r\n  setActiveMenu(menuName: string): void {\r\n    this.menuService.setActiveMenu(menuName);\r\n  }\r\n}\r\n", "<ul class=\"layout-menu\">\r\n    <ng-container *ngFor=\"let item of model; let i = index;\">\r\n        <li app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n        <li *ngIf=\"item.separator\" class=\"menu-separator\"></li>\r\n    </ng-container>\r\n</ul>"], "mappings": ";;;;ICEQA,EAAA,CAAAC,SAAA,YAAsF;;;;;;IAAnBD,EAA1B,CAAAE,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA,CAAY,cAAc;;;;;IAChFJ,EAAA,CAAAC,SAAA,YAAuD;;;;;IAF3DD,EAAA,CAAAK,uBAAA,GAAyD;IAErDL,EADA,CAAAM,UAAA,IAAAC,6CAAA,gBAAiF,IAAAC,6CAAA,gBAC/B;;;;;IADhCR,EAAA,CAAAS,SAAA,EAAqB;IAArBT,EAAA,CAAAE,UAAA,UAAAC,OAAA,CAAAO,SAAA,CAAqB;IAClCV,EAAA,CAAAS,SAAA,EAAoB;IAApBT,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAO,SAAA,CAAoB;;;ADKjC,OAAM,MAAOC,gBAAgB;EAG3BC,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAG,CACX;MACE;MACAC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,WAAW;QAClBF,IAAI,EAAE,iBAAiB;QACvBG,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,MAAM;QACbF,IAAI,EAAE,MAAM;QACZG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtBC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,WAAW;QAClBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,SAAS;QAChBF,IAAI,EAAE,QAAQ;QACdG,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,UAAU;QACjBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,iBAAiB,CAAC;QAC/BC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,YAAY;QACnBF,IAAI,EAAE,aAAa;QACnBG,UAAU,EAAE,CAAC,mBAAmB,CAAC;QACjCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK,CAAC;QAC7DD,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,cAAc;UACrBF,IAAI,EAAE,OAAO;UACbG,UAAU,EAAE,CAAC,gCAAgC;SAC9C,EACD;UACED,KAAK,EAAE,SAAS;UAChBF,IAAI,EAAE,OAAO;UACbG,UAAU,EAAE,CAAC,0BAA0B;SACxC,EACD;UACED,KAAK,EAAE,YAAY;UACnBF,IAAI,EAAE,eAAe;UACrBG,UAAU,EAAE,CAAC,yBAAyB;SACvC,EACD;UACED,KAAK,EAAE,OAAO;UACdF,IAAI,EAAE,MAAM;UACZG,UAAU,EAAE,CAAC,yBAAyB;SACvC;OAEJ,EACD;QACED,KAAK,EAAE,eAAe;QACtBF,IAAI,EAAE,iBAAiB;QACvBG,UAAU,EAAE,CAAC,sBAAsB,CAAC;QACpCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,aAAa;QACpBF,IAAI,EAAE,WAAW;QACjBG,UAAU,EAAE,CAAC,oBAAoB,CAAC;QAClCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,cAAc;QACrBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,qBAAqB,CAAC;QACnCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,cAAc;QACrBF,IAAI,EAAE,UAAU;QAChBG,UAAU,EAAE,CAAC,qBAAqB,CAAC;QACnCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D;KAEJ,EACD;MACEA,KAAK,EAAE,4BAA4B;MACnCD,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,kBAAkB;QACzBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,yBAAyB,CAAC;QACvCC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,qBAAqB;QAC5BF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,iCAAiC,CAAC;QAC/CC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,SAAS;QAChBF,IAAI,EAAE,iBAAiB;QACvBG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtBC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,eAAe;QACtBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtBC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,cAAc;QACrBF,IAAI,EAAE,QAAQ;QACdG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtBC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,iBAAiB;QACxBF,IAAI,EAAE,eAAe;QACrBG,UAAU,EAAE,CAAC,6BAA6B,CAAC;QAC3CC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D,EACD;QACEA,KAAK,EAAE,aAAa;QACpBF,IAAI,EAAE,qBAAqB;QAC3BG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtBC,OAAO,EAAGC,KAAU,IAAK,IAAI,CAACC,aAAa,CAACD,KAAK,CAACE,IAAI,CAACL,KAAK;OAC7D;KAEJ,CACF;EACH;EAEAM,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA9I/B,KAAAV,KAAK,GAAU,EAAE;EA8I8B;EAE/CO,aAAaA,CAACI,QAAgB;IAC5B,IAAI,CAACD,WAAW,CAACH,aAAa,CAACI,QAAQ,CAAC;EAC1C;;;uBAnJWb,gBAAgB,EAAAX,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBhB,gBAAgB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR7BlC,EAAA,CAAAoC,cAAA,YAAwB;UACpBpC,EAAA,CAAAM,UAAA,IAAA+B,wCAAA,0BAAyD;UAI7DrC,EAAA,CAAAsC,YAAA,EAAK;;;UAJ8BtC,EAAA,CAAAS,SAAA,EAAU;UAAVT,EAAA,CAAAE,UAAA,YAAAiC,GAAA,CAAAtB,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}