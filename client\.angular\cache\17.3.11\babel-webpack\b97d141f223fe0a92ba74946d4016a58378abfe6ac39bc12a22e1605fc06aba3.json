{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        decls: 2,\n        vars: 0,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\", \"surface-card\"], [\"src\", \"assets/layout/images/home-page-dashboard.png\", \"alt\", \"\", 1, \"w-full\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"img\", 1);\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}