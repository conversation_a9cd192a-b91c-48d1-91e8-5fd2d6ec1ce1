import { Component, OnInit } from '@angular/core';
import { OrganizationalService } from '../../organizational.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil, Observable } from 'rxjs';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ActivatedRoute } from '@angular/router';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-functions',
  templateUrl: './functions.component.html',
  styleUrl: './functions.component.scss',
})
export class FunctionsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public functionDetails: any[] = [];
  public organisational_unit_id: string = '';
  public addDialogVisible: boolean = false;
  public visible: boolean = false;
  public position: string = 'right';
  public submitted = false;
  public editid: string = '';
  public saving = false;
  public selectedFunctions = [];

  public FunctionForm: FormGroup = this.formBuilder.group({
    start_date: ['', [Validators.required]],
    end_date: ['', [Validators.required]],
    company_indicator: [''],
    sales_indicator: [''],
    sales_organisation_indicator: [''],
    sales_office_indicator: [''],
    sales_group_indicator: [''],
    service_indicator: [''],
    service_organisation_indicator: [''],
    marketing_indicator: [''],
    reporting_line_indicator: [''],
    currency_code: ['USD'],
  });

  constructor(
    private route: ActivatedRoute,
    private organizationalservice: OrganizationalService,
    private formBuilder: FormBuilder,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'end_date', header: 'Valid To' },
    { field: 'company_indicator', header: 'Company' },
    { field: 'sales_indicator', header: 'Sales' },
    { field: 'sales_organisation_indicator', header: 'Sales Organization' },
    { field: 'service_indicator', header: 'Service' },
    { field: 'service_organisation_indicator', header: 'Service Organization' },
    { field: 'marketing_indicator', header: 'Marketing' },
    { field: 'reporting_line_indicator', header: 'Reporting Line' },
    { field: 'currency_code', header: 'Currency' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.functionDetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit(): void {
    this.organisational_unit_id =
      this.route.parent?.snapshot.paramMap.get('id') || '';
    this.organizationalservice.organizational
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.functionDetails = response?.crm_org_unit_functions || [];
        }
      });

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }

  editFunction(functions: any) {
    this.addDialogVisible = true;
    this.editid = functions?.documentId;

    this.FunctionForm.patchValue({
      start_date: functions?.start_date
        ? new Date(functions?.start_date)
        : null,
      end_date: functions?.end_date ? new Date(functions?.end_date) : null,
      company_indicator: functions?.company_indicator,
      sales_indicator: functions?.sales_indicator,
      sales_organisation_indicator: functions?.sales_organisation_indicator,
      sales_office_indicator: functions?.sales_office_indicator,
      sales_group_indicator: functions?.sales_group_indicator,
      service_indicator: functions?.service_indicator,
      service_organisation_indicator: functions?.service_organisation_indicator,
      marketing_indicator: functions?.marketing_indicator,
      reporting_line_indicator: functions?.reporting_line_indicator,
      currency_code: functions?.currency_code,
    });
  }

  async onSubmit() {
    this.submitted = true;
    this.visible = true;

    if (this.FunctionForm.invalid) {
      console.log('Form is invalid:', this.FunctionForm.errors);
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.FunctionForm.value };

    const data = {
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      company_indicator: value?.company_indicator,
      sales_indicator: value?.sales_indicator,
      sales_organisation_indicator: value?.sales_organisation_indicator,
      sales_office_indicator: value?.sales_office_indicator,
      sales_group_indicator: value?.sales_group_indicator,
      service_indicator: value?.service_indicator,
      service_organisation_indicator: value?.service_organisation_indicator,
      marketing_indicator: value?.marketing_indicator,
      reporting_line_indicator: value?.reporting_line_indicator,
      currency_code: value?.currency_code,
      organisational_unit_id: this.organisational_unit_id,
    };

    let functionRequest$: Observable<any>;

    if (this.editid) {
      functionRequest$ = this.organizationalservice.updateFunction(
        this.editid,
        data
      );
    } else {
      functionRequest$ = this.organizationalservice.createFunction(data);
    }

    functionRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({
      complete: () => {
        this.saving = false;
        this.addDialogVisible = false;
        this.FunctionForm.reset();
        this.messageservice.add({
          severity: 'success',
          detail: this.editid
            ? 'Function updated successfully!'
            : 'Function created successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.saving = false;
        this.addDialogVisible = false;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  confirmRemove(item: any) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item);
      },
    });
  }

  remove(item: any) {
    this.organizationalservice
      .deleteFunction(item.documentId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Record Deleted Successfully!',
          });
          this.organizationalservice
            .getOrganizationByID(this.organisational_unit_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  showNewDialog(position: string) {
    this.position = position;
    this.addDialogVisible = true;
    this.submitted = false;
    this.FunctionForm.reset();
  }

  get f(): any {
    return this.FunctionForm.controls;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
