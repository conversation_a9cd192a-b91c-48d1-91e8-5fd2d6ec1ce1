{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DashboardComponent {\n  constructor() {\n    this.tableData = [];\n  }\n  ngOnInit() {\n    this.tableData = [{\n      SalesOrganization: '1',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'In Progress',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '2',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'Completed',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '3',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'In Progress',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '4',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'Completed',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '5',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'In Progress',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '6',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'Completed',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '7',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'In Progress',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '8',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'Completed',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '9',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'In Progress',\n      Action: '09/06/2025'\n    }, {\n      SalesOrganization: '10',\n      DistributionChannel: '00830VGB',\n      Division: '199911',\n      SalesOffice: '**********',\n      SalesGroup: 'Completed',\n      Action: '09/06/2025'\n    }];\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\", \"surface-card\"], [\"src\", \"assets/layout/images/home-page-dashboard.png\", \"alt\", \"\", 1, \"w-full\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "tableData", "ngOnInit", "SalesOrganization", "DistributionChannel", "Division", "SalesOffice", "SalesGroup", "Action", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\dashboard\\dashboard.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\ninterface AccountTableData {\r\n  SalesOrganization?: string;\r\n  DistributionChannel?: string;\r\n  Division?: string;\r\n  SalesOffice?: string;\r\n  SalesGroup?: string;\r\n  Action?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrl: './dashboard.component.scss'\r\n})\r\nexport class DashboardComponent {\r\n\r\n  tableData: AccountTableData[] = [];\r\n\r\n  ngOnInit() {\r\n    this.tableData = [\r\n      {\r\n        SalesOrganization: '1',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'In Progress',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '2',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'Completed',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '3',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'In Progress',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '4',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'Completed',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '5',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'In Progress',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '6',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'Completed',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '7',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'In Progress',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '8',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'Completed',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '9',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'In Progress',\r\n        Action: '09/06/2025',\r\n      },\r\n      {\r\n        SalesOrganization: '10',\r\n        DistributionChannel: '00830VGB',\r\n        Division: '199911',\r\n        SalesOffice: '**********',\r\n        SalesGroup: 'Completed',\r\n        Action: '09/06/2025',\r\n      },\r\n      \r\n    ];\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg surface-card\">\r\n    <img src=\"assets/layout/images/home-page-dashboard.png\" class=\"w-full\" alt=\"\" />\r\n</div>"], "mappings": ";AAgBA,OAAM,MAAOA,kBAAkB;EAL/BC,YAAA;IAOE,KAAAC,SAAS,GAAuB,EAAE;;EAElCC,QAAQA,CAAA;IACN,IAAI,CAACD,SAAS,GAAG,CACf;MACEE,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,WAAW;MACvBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,WAAW;MACvBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,WAAW;MACvBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,WAAW;MACvBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;KACT,EACD;MACEL,iBAAiB,EAAE,IAAI;MACvBC,mBAAmB,EAAE,UAAU;MAC/BC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,WAAW;MACvBC,MAAM,EAAE;KACT,CAEF;EACH;;;uBAxFWT,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB/BE,EAAA,CAAAC,cAAA,aAA2E;UACvED,EAAA,CAAAE,SAAA,aAAgF;UACpFF,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}