{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Output, ViewChild, InjectionToken, forwardRef, TemplateRef, Attribute, HostBinding, ContentChild, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil, auditTime, startWith, tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { animationFrameScheduler, asapScheduler, Subject, fromEvent, merge } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nconst _c0 = [\"content\"];\nconst _c1 = [\"scroll\"];\nconst _c2 = [\"padding\"];\nconst _c3 = [\"*\"];\nconst _c4 = a0 => ({\n  searchTerm: a0\n});\nfunction NgDropdownPanelComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementContainer(1, 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.filterValue));\n  }\n}\nfunction NgDropdownPanelComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelementContainer(1, 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.filterValue));\n  }\n}\nconst _c5 = [\"searchInput\"];\nconst _c6 = [\"clearButton\"];\nconst _c7 = (a0, a1, a2) => ({\n  item: a0,\n  clear: a1,\n  label: a2\n});\nconst _c8 = (a0, a1) => ({\n  items: a0,\n  clear: a1\n});\nconst _c9 = (a0, a1, a2, a3) => ({\n  item: a0,\n  item$: a1,\n  index: a2,\n  searchTerm: a3\n});\nfunction NgSelectComponent_ng_container_4_div_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_ng_container_4_div_1_ng_template_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.unselect(item_r3));\n    });\n    i0.ɵɵtext(1, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 23);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r3.label)(\"escape\", ctx_r3.escapeHTML);\n  }\n}\nfunction NgSelectComponent_ng_container_4_div_1_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_container_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_4_div_1_ng_template_1_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_container_4_div_1_ng_template_3_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const defaultLabelTemplate_r5 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-value-disabled\", item_r3.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.labelTemplate || defaultLabelTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(4, _c7, item_r3.value, ctx_r3.clearItem, item_r3.label));\n  }\n}\nfunction NgSelectComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_4_div_1_Template, 4, 8, \"div\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedItems)(\"ngForTrackBy\", ctx_r3.trackByOption);\n  }\n}\nfunction NgSelectComponent_5_ng_template_0_Template(rf, ctx) {}\nfunction NgSelectComponent_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_5_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.multiLabelTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c8, ctx_r3.selectedValues, ctx_r3.clearItem));\n  }\n}\nfunction NgSelectComponent_ng_container_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n}\nfunction NgSelectComponent_ng_container_9_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_9_ng_template_1_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_container_9_ng_template_3_Template, 0, 0, \"ng-template\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const defaultLoadingSpinnerTemplate_r7 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.loadingSpinnerTemplate || defaultLoadingSpinnerTemplate_r7);\n  }\n}\nfunction NgSelectComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26, 3)(2, \"span\", 27);\n    i0.ɵɵtext(3, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r3.clearAllText);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 32);\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r10.label)(\"escape\", ctx_r3.escapeHTML);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_click_0_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleItem(item_r10));\n    })(\"mouseover\", function NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_mouseover_0_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onItemHover(item_r10));\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_3_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const defaultOptionTemplate_r11 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-disabled\", item_r10.disabled)(\"ng-option-selected\", item_r10.selected)(\"ng-optgroup\", item_r10.children)(\"ng-option\", !item_r10.children)(\"ng-option-child\", !!item_r10.parent)(\"ng-option-marked\", item_r10 === ctx_r3.itemsList.markedItem);\n    i0.ɵɵattribute(\"role\", item_r10.children ? \"group\" : \"option\")(\"aria-selected\", item_r10.selected)(\"id\", item_r10 == null ? null : item_r10.htmlId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r10.children ? ctx_r3.optgroupTemplate || defaultOptionTemplate_r11 : ctx_r3.optionTemplate || defaultOptionTemplate_r11)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(17, _c9, item_r10.value, item_r10, item_r10.index, ctx_r3.searchTerm));\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"span\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.addTagText);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\\"\", ctx_r3.searchTerm, \"\\\"\");\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"mouseover\", function NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_mouseover_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.itemsList.unmarkItem());\n    })(\"click\", function NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.selectTag());\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_1_Template, 4, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_3_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultTagTemplate_r13 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-marked\", !ctx_r3.itemsList.markedItem);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.tagTemplate || defaultTagTemplate_r13)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c4, ctx_r3.searchTerm));\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.notFoundText);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_1_Template, 2, 1, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_3_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const defaultNotFoundTemplate_r14 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.notFoundTemplate || defaultNotFoundTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r3.searchTerm));\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.typeToSearchText);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_1_Template, 2, 1, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_3_Template, 0, 0, \"ng-template\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const defaultTypeToSearchTemplate_r15 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.typeToSearchTemplate || defaultTypeToSearchTemplate_r15);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.loadingText);\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_1_Template, 2, 1, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_3_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const defaultLoadingTextTemplate_r16 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.loadingTextTemplate || defaultLoadingTextTemplate_r16)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r3.searchTerm));\n  }\n}\nfunction NgSelectComponent_ng_dropdown_panel_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ng-dropdown-panel\", 28);\n    i0.ɵɵlistener(\"update\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_update_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.viewPortItems = $event);\n    })(\"scroll\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.scroll.emit($event));\n    })(\"scrollToEnd\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scrollToEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.scrollToEnd.emit($event));\n    })(\"outsideClick\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_outsideClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.close());\n    });\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, NgSelectComponent_ng_dropdown_panel_13_div_2_Template, 4, 22, \"div\", 29)(3, NgSelectComponent_ng_dropdown_panel_13_div_3_Template, 4, 6, \"div\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(4, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_Template, 4, 4, \"ng-container\", 12)(5, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_Template, 4, 1, \"ng-container\", 12)(6, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_Template, 4, 4, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ng-select-multiple\", ctx_r3.multiple);\n    i0.ɵɵproperty(\"virtualScroll\", ctx_r3.virtualScroll)(\"bufferAmount\", ctx_r3.bufferAmount)(\"appendTo\", ctx_r3.appendTo)(\"position\", ctx_r3.dropdownPosition)(\"headerTemplate\", ctx_r3.headerTemplate)(\"footerTemplate\", ctx_r3.footerTemplate)(\"filterValue\", ctx_r3.searchTerm)(\"items\", ctx_r3.itemsList.filteredItems)(\"markedItem\", ctx_r3.itemsList.markedItem)(\"ngClass\", ctx_r3.appendTo ? ctx_r3.classes : null)(\"id\", ctx_r3.dropdownId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.viewPortItems)(\"ngForTrackBy\", ctx_r3.trackByOption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showAddTag);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showNoItemsFound());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showTypeToSearch());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading && ctx_r3.itemsList.filteredItems.length === 0);\n  }\n}\nconst unescapedHTMLExp = /[&<>\"']/g;\nconst hasUnescapedHTMLExp = RegExp(unescapedHTMLExp.source);\nconst htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\nfunction escapeHTML(value) {\n  return value && hasUnescapedHTMLExp.test(value) ? value.replace(unescapedHTMLExp, chr => htmlEscapes[chr]) : value;\n}\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\nfunction isObject(value) {\n  return typeof value === 'object' && isDefined(value);\n}\nfunction isPromise(value) {\n  return value instanceof Promise;\n}\nfunction isFunction(value) {\n  return value instanceof Function;\n}\nclass NgItemLabelDirective {\n  constructor(element) {\n    this.element = element;\n    this.escape = true;\n  }\n  ngOnChanges(changes) {\n    this.element.nativeElement.innerHTML = this.escape ? escapeHTML(this.ngItemLabel) : this.ngItemLabel;\n  }\n  static {\n    this.ɵfac = function NgItemLabelDirective_Factory(t) {\n      return new (t || NgItemLabelDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgItemLabelDirective,\n      selectors: [[\"\", \"ngItemLabel\", \"\"]],\n      inputs: {\n        ngItemLabel: \"ngItemLabel\",\n        escape: \"escape\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgItemLabelDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngItemLabel]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    ngItemLabel: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }]\n  });\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptionTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgOptionTemplateDirective_Factory(t) {\n      return new (t || NgOptionTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgOptionTemplateDirective,\n      selectors: [[\"\", \"ng-option-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-option-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptgroupTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgOptgroupTemplateDirective_Factory(t) {\n      return new (t || NgOptgroupTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgOptgroupTemplateDirective,\n      selectors: [[\"\", \"ng-optgroup-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptgroupTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-optgroup-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLabelTemplateDirective_Factory(t) {\n      return new (t || NgLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLabelTemplateDirective,\n      selectors: [[\"\", \"ng-label-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-label-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgMultiLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgMultiLabelTemplateDirective_Factory(t) {\n      return new (t || NgMultiLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgMultiLabelTemplateDirective,\n      selectors: [[\"\", \"ng-multi-label-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgMultiLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-multi-label-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgHeaderTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgHeaderTemplateDirective_Factory(t) {\n      return new (t || NgHeaderTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgHeaderTemplateDirective,\n      selectors: [[\"\", \"ng-header-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgHeaderTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-header-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgFooterTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgFooterTemplateDirective_Factory(t) {\n      return new (t || NgFooterTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgFooterTemplateDirective,\n      selectors: [[\"\", \"ng-footer-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgFooterTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-footer-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgNotFoundTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgNotFoundTemplateDirective_Factory(t) {\n      return new (t || NgNotFoundTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgNotFoundTemplateDirective,\n      selectors: [[\"\", \"ng-notfound-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgNotFoundTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-notfound-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTypeToSearchTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgTypeToSearchTemplateDirective_Factory(t) {\n      return new (t || NgTypeToSearchTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgTypeToSearchTemplateDirective,\n      selectors: [[\"\", \"ng-typetosearch-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTypeToSearchTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-typetosearch-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingTextTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLoadingTextTemplateDirective_Factory(t) {\n      return new (t || NgLoadingTextTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLoadingTextTemplateDirective,\n      selectors: [[\"\", \"ng-loadingtext-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingTextTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-loadingtext-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTagTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgTagTemplateDirective_Factory(t) {\n      return new (t || NgTagTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgTagTemplateDirective,\n      selectors: [[\"\", \"ng-tag-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTagTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-tag-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingSpinnerTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLoadingSpinnerTemplateDirective_Factory(t) {\n      return new (t || NgLoadingSpinnerTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLoadingSpinnerTemplateDirective,\n      selectors: [[\"\", \"ng-loadingspinner-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingSpinnerTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-loadingspinner-tmp]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nfunction newId() {\n  // First character is an 'a', it's good practice to tag id to begin with a letter\n  return 'axxxxxxxxxxx'.replace(/[x]/g, () => {\n    // eslint-disable-next-line no-bitwise\n    const val = Math.random() * 16 | 0;\n    return val.toString(16);\n  });\n}\nconst diacritics = {\n  '\\u24B6': 'A',\n  '\\uFF21': 'A',\n  '\\u00C0': 'A',\n  '\\u00C1': 'A',\n  '\\u00C2': 'A',\n  '\\u1EA6': 'A',\n  '\\u1EA4': 'A',\n  '\\u1EAA': 'A',\n  '\\u1EA8': 'A',\n  '\\u00C3': 'A',\n  '\\u0100': 'A',\n  '\\u0102': 'A',\n  '\\u1EB0': 'A',\n  '\\u1EAE': 'A',\n  '\\u1EB4': 'A',\n  '\\u1EB2': 'A',\n  '\\u0226': 'A',\n  '\\u01E0': 'A',\n  '\\u00C4': 'A',\n  '\\u01DE': 'A',\n  '\\u1EA2': 'A',\n  '\\u00C5': 'A',\n  '\\u01FA': 'A',\n  '\\u01CD': 'A',\n  '\\u0200': 'A',\n  '\\u0202': 'A',\n  '\\u1EA0': 'A',\n  '\\u1EAC': 'A',\n  '\\u1EB6': 'A',\n  '\\u1E00': 'A',\n  '\\u0104': 'A',\n  '\\u023A': 'A',\n  '\\u2C6F': 'A',\n  '\\uA732': 'AA',\n  '\\u00C6': 'AE',\n  '\\u01FC': 'AE',\n  '\\u01E2': 'AE',\n  '\\uA734': 'AO',\n  '\\uA736': 'AU',\n  '\\uA738': 'AV',\n  '\\uA73A': 'AV',\n  '\\uA73C': 'AY',\n  '\\u24B7': 'B',\n  '\\uFF22': 'B',\n  '\\u1E02': 'B',\n  '\\u1E04': 'B',\n  '\\u1E06': 'B',\n  '\\u0243': 'B',\n  '\\u0182': 'B',\n  '\\u0181': 'B',\n  '\\u24B8': 'C',\n  '\\uFF23': 'C',\n  '\\u0106': 'C',\n  '\\u0108': 'C',\n  '\\u010A': 'C',\n  '\\u010C': 'C',\n  '\\u00C7': 'C',\n  '\\u1E08': 'C',\n  '\\u0187': 'C',\n  '\\u023B': 'C',\n  '\\uA73E': 'C',\n  '\\u24B9': 'D',\n  '\\uFF24': 'D',\n  '\\u1E0A': 'D',\n  '\\u010E': 'D',\n  '\\u1E0C': 'D',\n  '\\u1E10': 'D',\n  '\\u1E12': 'D',\n  '\\u1E0E': 'D',\n  '\\u0110': 'D',\n  '\\u018B': 'D',\n  '\\u018A': 'D',\n  '\\u0189': 'D',\n  '\\uA779': 'D',\n  '\\u01F1': 'DZ',\n  '\\u01C4': 'DZ',\n  '\\u01F2': 'Dz',\n  '\\u01C5': 'Dz',\n  '\\u24BA': 'E',\n  '\\uFF25': 'E',\n  '\\u00C8': 'E',\n  '\\u00C9': 'E',\n  '\\u00CA': 'E',\n  '\\u1EC0': 'E',\n  '\\u1EBE': 'E',\n  '\\u1EC4': 'E',\n  '\\u1EC2': 'E',\n  '\\u1EBC': 'E',\n  '\\u0112': 'E',\n  '\\u1E14': 'E',\n  '\\u1E16': 'E',\n  '\\u0114': 'E',\n  '\\u0116': 'E',\n  '\\u00CB': 'E',\n  '\\u1EBA': 'E',\n  '\\u011A': 'E',\n  '\\u0204': 'E',\n  '\\u0206': 'E',\n  '\\u1EB8': 'E',\n  '\\u1EC6': 'E',\n  '\\u0228': 'E',\n  '\\u1E1C': 'E',\n  '\\u0118': 'E',\n  '\\u1E18': 'E',\n  '\\u1E1A': 'E',\n  '\\u0190': 'E',\n  '\\u018E': 'E',\n  '\\u24BB': 'F',\n  '\\uFF26': 'F',\n  '\\u1E1E': 'F',\n  '\\u0191': 'F',\n  '\\uA77B': 'F',\n  '\\u24BC': 'G',\n  '\\uFF27': 'G',\n  '\\u01F4': 'G',\n  '\\u011C': 'G',\n  '\\u1E20': 'G',\n  '\\u011E': 'G',\n  '\\u0120': 'G',\n  '\\u01E6': 'G',\n  '\\u0122': 'G',\n  '\\u01E4': 'G',\n  '\\u0193': 'G',\n  '\\uA7A0': 'G',\n  '\\uA77D': 'G',\n  '\\uA77E': 'G',\n  '\\u24BD': 'H',\n  '\\uFF28': 'H',\n  '\\u0124': 'H',\n  '\\u1E22': 'H',\n  '\\u1E26': 'H',\n  '\\u021E': 'H',\n  '\\u1E24': 'H',\n  '\\u1E28': 'H',\n  '\\u1E2A': 'H',\n  '\\u0126': 'H',\n  '\\u2C67': 'H',\n  '\\u2C75': 'H',\n  '\\uA78D': 'H',\n  '\\u24BE': 'I',\n  '\\uFF29': 'I',\n  '\\u00CC': 'I',\n  '\\u00CD': 'I',\n  '\\u00CE': 'I',\n  '\\u0128': 'I',\n  '\\u012A': 'I',\n  '\\u012C': 'I',\n  '\\u0130': 'I',\n  '\\u00CF': 'I',\n  '\\u1E2E': 'I',\n  '\\u1EC8': 'I',\n  '\\u01CF': 'I',\n  '\\u0208': 'I',\n  '\\u020A': 'I',\n  '\\u1ECA': 'I',\n  '\\u012E': 'I',\n  '\\u1E2C': 'I',\n  '\\u0197': 'I',\n  '\\u24BF': 'J',\n  '\\uFF2A': 'J',\n  '\\u0134': 'J',\n  '\\u0248': 'J',\n  '\\u24C0': 'K',\n  '\\uFF2B': 'K',\n  '\\u1E30': 'K',\n  '\\u01E8': 'K',\n  '\\u1E32': 'K',\n  '\\u0136': 'K',\n  '\\u1E34': 'K',\n  '\\u0198': 'K',\n  '\\u2C69': 'K',\n  '\\uA740': 'K',\n  '\\uA742': 'K',\n  '\\uA744': 'K',\n  '\\uA7A2': 'K',\n  '\\u24C1': 'L',\n  '\\uFF2C': 'L',\n  '\\u013F': 'L',\n  '\\u0139': 'L',\n  '\\u013D': 'L',\n  '\\u1E36': 'L',\n  '\\u1E38': 'L',\n  '\\u013B': 'L',\n  '\\u1E3C': 'L',\n  '\\u1E3A': 'L',\n  '\\u0141': 'L',\n  '\\u023D': 'L',\n  '\\u2C62': 'L',\n  '\\u2C60': 'L',\n  '\\uA748': 'L',\n  '\\uA746': 'L',\n  '\\uA780': 'L',\n  '\\u01C7': 'LJ',\n  '\\u01C8': 'Lj',\n  '\\u24C2': 'M',\n  '\\uFF2D': 'M',\n  '\\u1E3E': 'M',\n  '\\u1E40': 'M',\n  '\\u1E42': 'M',\n  '\\u2C6E': 'M',\n  '\\u019C': 'M',\n  '\\u24C3': 'N',\n  '\\uFF2E': 'N',\n  '\\u01F8': 'N',\n  '\\u0143': 'N',\n  '\\u00D1': 'N',\n  '\\u1E44': 'N',\n  '\\u0147': 'N',\n  '\\u1E46': 'N',\n  '\\u0145': 'N',\n  '\\u1E4A': 'N',\n  '\\u1E48': 'N',\n  '\\u0220': 'N',\n  '\\u019D': 'N',\n  '\\uA790': 'N',\n  '\\uA7A4': 'N',\n  '\\u01CA': 'NJ',\n  '\\u01CB': 'Nj',\n  '\\u24C4': 'O',\n  '\\uFF2F': 'O',\n  '\\u00D2': 'O',\n  '\\u00D3': 'O',\n  '\\u00D4': 'O',\n  '\\u1ED2': 'O',\n  '\\u1ED0': 'O',\n  '\\u1ED6': 'O',\n  '\\u1ED4': 'O',\n  '\\u00D5': 'O',\n  '\\u1E4C': 'O',\n  '\\u022C': 'O',\n  '\\u1E4E': 'O',\n  '\\u014C': 'O',\n  '\\u1E50': 'O',\n  '\\u1E52': 'O',\n  '\\u014E': 'O',\n  '\\u022E': 'O',\n  '\\u0230': 'O',\n  '\\u00D6': 'O',\n  '\\u022A': 'O',\n  '\\u1ECE': 'O',\n  '\\u0150': 'O',\n  '\\u01D1': 'O',\n  '\\u020C': 'O',\n  '\\u020E': 'O',\n  '\\u01A0': 'O',\n  '\\u1EDC': 'O',\n  '\\u1EDA': 'O',\n  '\\u1EE0': 'O',\n  '\\u1EDE': 'O',\n  '\\u1EE2': 'O',\n  '\\u1ECC': 'O',\n  '\\u1ED8': 'O',\n  '\\u01EA': 'O',\n  '\\u01EC': 'O',\n  '\\u00D8': 'O',\n  '\\u01FE': 'O',\n  '\\u0186': 'O',\n  '\\u019F': 'O',\n  '\\uA74A': 'O',\n  '\\uA74C': 'O',\n  '\\u01A2': 'OI',\n  '\\uA74E': 'OO',\n  '\\u0222': 'OU',\n  '\\u24C5': 'P',\n  '\\uFF30': 'P',\n  '\\u1E54': 'P',\n  '\\u1E56': 'P',\n  '\\u01A4': 'P',\n  '\\u2C63': 'P',\n  '\\uA750': 'P',\n  '\\uA752': 'P',\n  '\\uA754': 'P',\n  '\\u24C6': 'Q',\n  '\\uFF31': 'Q',\n  '\\uA756': 'Q',\n  '\\uA758': 'Q',\n  '\\u024A': 'Q',\n  '\\u24C7': 'R',\n  '\\uFF32': 'R',\n  '\\u0154': 'R',\n  '\\u1E58': 'R',\n  '\\u0158': 'R',\n  '\\u0210': 'R',\n  '\\u0212': 'R',\n  '\\u1E5A': 'R',\n  '\\u1E5C': 'R',\n  '\\u0156': 'R',\n  '\\u1E5E': 'R',\n  '\\u024C': 'R',\n  '\\u2C64': 'R',\n  '\\uA75A': 'R',\n  '\\uA7A6': 'R',\n  '\\uA782': 'R',\n  '\\u24C8': 'S',\n  '\\uFF33': 'S',\n  '\\u1E9E': 'S',\n  '\\u015A': 'S',\n  '\\u1E64': 'S',\n  '\\u015C': 'S',\n  '\\u1E60': 'S',\n  '\\u0160': 'S',\n  '\\u1E66': 'S',\n  '\\u1E62': 'S',\n  '\\u1E68': 'S',\n  '\\u0218': 'S',\n  '\\u015E': 'S',\n  '\\u2C7E': 'S',\n  '\\uA7A8': 'S',\n  '\\uA784': 'S',\n  '\\u24C9': 'T',\n  '\\uFF34': 'T',\n  '\\u1E6A': 'T',\n  '\\u0164': 'T',\n  '\\u1E6C': 'T',\n  '\\u021A': 'T',\n  '\\u0162': 'T',\n  '\\u1E70': 'T',\n  '\\u1E6E': 'T',\n  '\\u0166': 'T',\n  '\\u01AC': 'T',\n  '\\u01AE': 'T',\n  '\\u023E': 'T',\n  '\\uA786': 'T',\n  '\\uA728': 'TZ',\n  '\\u24CA': 'U',\n  '\\uFF35': 'U',\n  '\\u00D9': 'U',\n  '\\u00DA': 'U',\n  '\\u00DB': 'U',\n  '\\u0168': 'U',\n  '\\u1E78': 'U',\n  '\\u016A': 'U',\n  '\\u1E7A': 'U',\n  '\\u016C': 'U',\n  '\\u00DC': 'U',\n  '\\u01DB': 'U',\n  '\\u01D7': 'U',\n  '\\u01D5': 'U',\n  '\\u01D9': 'U',\n  '\\u1EE6': 'U',\n  '\\u016E': 'U',\n  '\\u0170': 'U',\n  '\\u01D3': 'U',\n  '\\u0214': 'U',\n  '\\u0216': 'U',\n  '\\u01AF': 'U',\n  '\\u1EEA': 'U',\n  '\\u1EE8': 'U',\n  '\\u1EEE': 'U',\n  '\\u1EEC': 'U',\n  '\\u1EF0': 'U',\n  '\\u1EE4': 'U',\n  '\\u1E72': 'U',\n  '\\u0172': 'U',\n  '\\u1E76': 'U',\n  '\\u1E74': 'U',\n  '\\u0244': 'U',\n  '\\u24CB': 'V',\n  '\\uFF36': 'V',\n  '\\u1E7C': 'V',\n  '\\u1E7E': 'V',\n  '\\u01B2': 'V',\n  '\\uA75E': 'V',\n  '\\u0245': 'V',\n  '\\uA760': 'VY',\n  '\\u24CC': 'W',\n  '\\uFF37': 'W',\n  '\\u1E80': 'W',\n  '\\u1E82': 'W',\n  '\\u0174': 'W',\n  '\\u1E86': 'W',\n  '\\u1E84': 'W',\n  '\\u1E88': 'W',\n  '\\u2C72': 'W',\n  '\\u24CD': 'X',\n  '\\uFF38': 'X',\n  '\\u1E8A': 'X',\n  '\\u1E8C': 'X',\n  '\\u24CE': 'Y',\n  '\\uFF39': 'Y',\n  '\\u1EF2': 'Y',\n  '\\u00DD': 'Y',\n  '\\u0176': 'Y',\n  '\\u1EF8': 'Y',\n  '\\u0232': 'Y',\n  '\\u1E8E': 'Y',\n  '\\u0178': 'Y',\n  '\\u1EF6': 'Y',\n  '\\u1EF4': 'Y',\n  '\\u01B3': 'Y',\n  '\\u024E': 'Y',\n  '\\u1EFE': 'Y',\n  '\\u24CF': 'Z',\n  '\\uFF3A': 'Z',\n  '\\u0179': 'Z',\n  '\\u1E90': 'Z',\n  '\\u017B': 'Z',\n  '\\u017D': 'Z',\n  '\\u1E92': 'Z',\n  '\\u1E94': 'Z',\n  '\\u01B5': 'Z',\n  '\\u0224': 'Z',\n  '\\u2C7F': 'Z',\n  '\\u2C6B': 'Z',\n  '\\uA762': 'Z',\n  '\\u24D0': 'a',\n  '\\uFF41': 'a',\n  '\\u1E9A': 'a',\n  '\\u00E0': 'a',\n  '\\u00E1': 'a',\n  '\\u00E2': 'a',\n  '\\u1EA7': 'a',\n  '\\u1EA5': 'a',\n  '\\u1EAB': 'a',\n  '\\u1EA9': 'a',\n  '\\u00E3': 'a',\n  '\\u0101': 'a',\n  '\\u0103': 'a',\n  '\\u1EB1': 'a',\n  '\\u1EAF': 'a',\n  '\\u1EB5': 'a',\n  '\\u1EB3': 'a',\n  '\\u0227': 'a',\n  '\\u01E1': 'a',\n  '\\u00E4': 'a',\n  '\\u01DF': 'a',\n  '\\u1EA3': 'a',\n  '\\u00E5': 'a',\n  '\\u01FB': 'a',\n  '\\u01CE': 'a',\n  '\\u0201': 'a',\n  '\\u0203': 'a',\n  '\\u1EA1': 'a',\n  '\\u1EAD': 'a',\n  '\\u1EB7': 'a',\n  '\\u1E01': 'a',\n  '\\u0105': 'a',\n  '\\u2C65': 'a',\n  '\\u0250': 'a',\n  '\\uA733': 'aa',\n  '\\u00E6': 'ae',\n  '\\u01FD': 'ae',\n  '\\u01E3': 'ae',\n  '\\uA735': 'ao',\n  '\\uA737': 'au',\n  '\\uA739': 'av',\n  '\\uA73B': 'av',\n  '\\uA73D': 'ay',\n  '\\u24D1': 'b',\n  '\\uFF42': 'b',\n  '\\u1E03': 'b',\n  '\\u1E05': 'b',\n  '\\u1E07': 'b',\n  '\\u0180': 'b',\n  '\\u0183': 'b',\n  '\\u0253': 'b',\n  '\\u24D2': 'c',\n  '\\uFF43': 'c',\n  '\\u0107': 'c',\n  '\\u0109': 'c',\n  '\\u010B': 'c',\n  '\\u010D': 'c',\n  '\\u00E7': 'c',\n  '\\u1E09': 'c',\n  '\\u0188': 'c',\n  '\\u023C': 'c',\n  '\\uA73F': 'c',\n  '\\u2184': 'c',\n  '\\u24D3': 'd',\n  '\\uFF44': 'd',\n  '\\u1E0B': 'd',\n  '\\u010F': 'd',\n  '\\u1E0D': 'd',\n  '\\u1E11': 'd',\n  '\\u1E13': 'd',\n  '\\u1E0F': 'd',\n  '\\u0111': 'd',\n  '\\u018C': 'd',\n  '\\u0256': 'd',\n  '\\u0257': 'd',\n  '\\uA77A': 'd',\n  '\\u01F3': 'dz',\n  '\\u01C6': 'dz',\n  '\\u24D4': 'e',\n  '\\uFF45': 'e',\n  '\\u00E8': 'e',\n  '\\u00E9': 'e',\n  '\\u00EA': 'e',\n  '\\u1EC1': 'e',\n  '\\u1EBF': 'e',\n  '\\u1EC5': 'e',\n  '\\u1EC3': 'e',\n  '\\u1EBD': 'e',\n  '\\u0113': 'e',\n  '\\u1E15': 'e',\n  '\\u1E17': 'e',\n  '\\u0115': 'e',\n  '\\u0117': 'e',\n  '\\u00EB': 'e',\n  '\\u1EBB': 'e',\n  '\\u011B': 'e',\n  '\\u0205': 'e',\n  '\\u0207': 'e',\n  '\\u1EB9': 'e',\n  '\\u1EC7': 'e',\n  '\\u0229': 'e',\n  '\\u1E1D': 'e',\n  '\\u0119': 'e',\n  '\\u1E19': 'e',\n  '\\u1E1B': 'e',\n  '\\u0247': 'e',\n  '\\u025B': 'e',\n  '\\u01DD': 'e',\n  '\\u24D5': 'f',\n  '\\uFF46': 'f',\n  '\\u1E1F': 'f',\n  '\\u0192': 'f',\n  '\\uA77C': 'f',\n  '\\u24D6': 'g',\n  '\\uFF47': 'g',\n  '\\u01F5': 'g',\n  '\\u011D': 'g',\n  '\\u1E21': 'g',\n  '\\u011F': 'g',\n  '\\u0121': 'g',\n  '\\u01E7': 'g',\n  '\\u0123': 'g',\n  '\\u01E5': 'g',\n  '\\u0260': 'g',\n  '\\uA7A1': 'g',\n  '\\u1D79': 'g',\n  '\\uA77F': 'g',\n  '\\u24D7': 'h',\n  '\\uFF48': 'h',\n  '\\u0125': 'h',\n  '\\u1E23': 'h',\n  '\\u1E27': 'h',\n  '\\u021F': 'h',\n  '\\u1E25': 'h',\n  '\\u1E29': 'h',\n  '\\u1E2B': 'h',\n  '\\u1E96': 'h',\n  '\\u0127': 'h',\n  '\\u2C68': 'h',\n  '\\u2C76': 'h',\n  '\\u0265': 'h',\n  '\\u0195': 'hv',\n  '\\u24D8': 'i',\n  '\\uFF49': 'i',\n  '\\u00EC': 'i',\n  '\\u00ED': 'i',\n  '\\u00EE': 'i',\n  '\\u0129': 'i',\n  '\\u012B': 'i',\n  '\\u012D': 'i',\n  '\\u00EF': 'i',\n  '\\u1E2F': 'i',\n  '\\u1EC9': 'i',\n  '\\u01D0': 'i',\n  '\\u0209': 'i',\n  '\\u020B': 'i',\n  '\\u1ECB': 'i',\n  '\\u012F': 'i',\n  '\\u1E2D': 'i',\n  '\\u0268': 'i',\n  '\\u0131': 'i',\n  '\\u24D9': 'j',\n  '\\uFF4A': 'j',\n  '\\u0135': 'j',\n  '\\u01F0': 'j',\n  '\\u0249': 'j',\n  '\\u24DA': 'k',\n  '\\uFF4B': 'k',\n  '\\u1E31': 'k',\n  '\\u01E9': 'k',\n  '\\u1E33': 'k',\n  '\\u0137': 'k',\n  '\\u1E35': 'k',\n  '\\u0199': 'k',\n  '\\u2C6A': 'k',\n  '\\uA741': 'k',\n  '\\uA743': 'k',\n  '\\uA745': 'k',\n  '\\uA7A3': 'k',\n  '\\u24DB': 'l',\n  '\\uFF4C': 'l',\n  '\\u0140': 'l',\n  '\\u013A': 'l',\n  '\\u013E': 'l',\n  '\\u1E37': 'l',\n  '\\u1E39': 'l',\n  '\\u013C': 'l',\n  '\\u1E3D': 'l',\n  '\\u1E3B': 'l',\n  '\\u017F': 'l',\n  '\\u0142': 'l',\n  '\\u019A': 'l',\n  '\\u026B': 'l',\n  '\\u2C61': 'l',\n  '\\uA749': 'l',\n  '\\uA781': 'l',\n  '\\uA747': 'l',\n  '\\u01C9': 'lj',\n  '\\u24DC': 'm',\n  '\\uFF4D': 'm',\n  '\\u1E3F': 'm',\n  '\\u1E41': 'm',\n  '\\u1E43': 'm',\n  '\\u0271': 'm',\n  '\\u026F': 'm',\n  '\\u24DD': 'n',\n  '\\uFF4E': 'n',\n  '\\u01F9': 'n',\n  '\\u0144': 'n',\n  '\\u00F1': 'n',\n  '\\u1E45': 'n',\n  '\\u0148': 'n',\n  '\\u1E47': 'n',\n  '\\u0146': 'n',\n  '\\u1E4B': 'n',\n  '\\u1E49': 'n',\n  '\\u019E': 'n',\n  '\\u0272': 'n',\n  '\\u0149': 'n',\n  '\\uA791': 'n',\n  '\\uA7A5': 'n',\n  '\\u01CC': 'nj',\n  '\\u24DE': 'o',\n  '\\uFF4F': 'o',\n  '\\u00F2': 'o',\n  '\\u00F3': 'o',\n  '\\u00F4': 'o',\n  '\\u1ED3': 'o',\n  '\\u1ED1': 'o',\n  '\\u1ED7': 'o',\n  '\\u1ED5': 'o',\n  '\\u00F5': 'o',\n  '\\u1E4D': 'o',\n  '\\u022D': 'o',\n  '\\u1E4F': 'o',\n  '\\u014D': 'o',\n  '\\u1E51': 'o',\n  '\\u1E53': 'o',\n  '\\u014F': 'o',\n  '\\u022F': 'o',\n  '\\u0231': 'o',\n  '\\u00F6': 'o',\n  '\\u022B': 'o',\n  '\\u1ECF': 'o',\n  '\\u0151': 'o',\n  '\\u01D2': 'o',\n  '\\u020D': 'o',\n  '\\u020F': 'o',\n  '\\u01A1': 'o',\n  '\\u1EDD': 'o',\n  '\\u1EDB': 'o',\n  '\\u1EE1': 'o',\n  '\\u1EDF': 'o',\n  '\\u1EE3': 'o',\n  '\\u1ECD': 'o',\n  '\\u1ED9': 'o',\n  '\\u01EB': 'o',\n  '\\u01ED': 'o',\n  '\\u00F8': 'o',\n  '\\u01FF': 'o',\n  '\\u0254': 'o',\n  '\\uA74B': 'o',\n  '\\uA74D': 'o',\n  '\\u0275': 'o',\n  '\\u01A3': 'oi',\n  '\\u0223': 'ou',\n  '\\uA74F': 'oo',\n  '\\u24DF': 'p',\n  '\\uFF50': 'p',\n  '\\u1E55': 'p',\n  '\\u1E57': 'p',\n  '\\u01A5': 'p',\n  '\\u1D7D': 'p',\n  '\\uA751': 'p',\n  '\\uA753': 'p',\n  '\\uA755': 'p',\n  '\\u24E0': 'q',\n  '\\uFF51': 'q',\n  '\\u024B': 'q',\n  '\\uA757': 'q',\n  '\\uA759': 'q',\n  '\\u24E1': 'r',\n  '\\uFF52': 'r',\n  '\\u0155': 'r',\n  '\\u1E59': 'r',\n  '\\u0159': 'r',\n  '\\u0211': 'r',\n  '\\u0213': 'r',\n  '\\u1E5B': 'r',\n  '\\u1E5D': 'r',\n  '\\u0157': 'r',\n  '\\u1E5F': 'r',\n  '\\u024D': 'r',\n  '\\u027D': 'r',\n  '\\uA75B': 'r',\n  '\\uA7A7': 'r',\n  '\\uA783': 'r',\n  '\\u24E2': 's',\n  '\\uFF53': 's',\n  '\\u00DF': 's',\n  '\\u015B': 's',\n  '\\u1E65': 's',\n  '\\u015D': 's',\n  '\\u1E61': 's',\n  '\\u0161': 's',\n  '\\u1E67': 's',\n  '\\u1E63': 's',\n  '\\u1E69': 's',\n  '\\u0219': 's',\n  '\\u015F': 's',\n  '\\u023F': 's',\n  '\\uA7A9': 's',\n  '\\uA785': 's',\n  '\\u1E9B': 's',\n  '\\u24E3': 't',\n  '\\uFF54': 't',\n  '\\u1E6B': 't',\n  '\\u1E97': 't',\n  '\\u0165': 't',\n  '\\u1E6D': 't',\n  '\\u021B': 't',\n  '\\u0163': 't',\n  '\\u1E71': 't',\n  '\\u1E6F': 't',\n  '\\u0167': 't',\n  '\\u01AD': 't',\n  '\\u0288': 't',\n  '\\u2C66': 't',\n  '\\uA787': 't',\n  '\\uA729': 'tz',\n  '\\u24E4': 'u',\n  '\\uFF55': 'u',\n  '\\u00F9': 'u',\n  '\\u00FA': 'u',\n  '\\u00FB': 'u',\n  '\\u0169': 'u',\n  '\\u1E79': 'u',\n  '\\u016B': 'u',\n  '\\u1E7B': 'u',\n  '\\u016D': 'u',\n  '\\u00FC': 'u',\n  '\\u01DC': 'u',\n  '\\u01D8': 'u',\n  '\\u01D6': 'u',\n  '\\u01DA': 'u',\n  '\\u1EE7': 'u',\n  '\\u016F': 'u',\n  '\\u0171': 'u',\n  '\\u01D4': 'u',\n  '\\u0215': 'u',\n  '\\u0217': 'u',\n  '\\u01B0': 'u',\n  '\\u1EEB': 'u',\n  '\\u1EE9': 'u',\n  '\\u1EEF': 'u',\n  '\\u1EED': 'u',\n  '\\u1EF1': 'u',\n  '\\u1EE5': 'u',\n  '\\u1E73': 'u',\n  '\\u0173': 'u',\n  '\\u1E77': 'u',\n  '\\u1E75': 'u',\n  '\\u0289': 'u',\n  '\\u24E5': 'v',\n  '\\uFF56': 'v',\n  '\\u1E7D': 'v',\n  '\\u1E7F': 'v',\n  '\\u028B': 'v',\n  '\\uA75F': 'v',\n  '\\u028C': 'v',\n  '\\uA761': 'vy',\n  '\\u24E6': 'w',\n  '\\uFF57': 'w',\n  '\\u1E81': 'w',\n  '\\u1E83': 'w',\n  '\\u0175': 'w',\n  '\\u1E87': 'w',\n  '\\u1E85': 'w',\n  '\\u1E98': 'w',\n  '\\u1E89': 'w',\n  '\\u2C73': 'w',\n  '\\u24E7': 'x',\n  '\\uFF58': 'x',\n  '\\u1E8B': 'x',\n  '\\u1E8D': 'x',\n  '\\u24E8': 'y',\n  '\\uFF59': 'y',\n  '\\u1EF3': 'y',\n  '\\u00FD': 'y',\n  '\\u0177': 'y',\n  '\\u1EF9': 'y',\n  '\\u0233': 'y',\n  '\\u1E8F': 'y',\n  '\\u00FF': 'y',\n  '\\u1EF7': 'y',\n  '\\u1E99': 'y',\n  '\\u1EF5': 'y',\n  '\\u01B4': 'y',\n  '\\u024F': 'y',\n  '\\u1EFF': 'y',\n  '\\u24E9': 'z',\n  '\\uFF5A': 'z',\n  '\\u017A': 'z',\n  '\\u1E91': 'z',\n  '\\u017C': 'z',\n  '\\u017E': 'z',\n  '\\u1E93': 'z',\n  '\\u1E95': 'z',\n  '\\u01B6': 'z',\n  '\\u0225': 'z',\n  '\\u0240': 'z',\n  '\\u2C6C': 'z',\n  '\\uA763': 'z',\n  '\\u0386': '\\u0391',\n  '\\u0388': '\\u0395',\n  '\\u0389': '\\u0397',\n  '\\u038A': '\\u0399',\n  '\\u03AA': '\\u0399',\n  '\\u038C': '\\u039F',\n  '\\u038E': '\\u03A5',\n  '\\u03AB': '\\u03A5',\n  '\\u038F': '\\u03A9',\n  '\\u03AC': '\\u03B1',\n  '\\u03AD': '\\u03B5',\n  '\\u03AE': '\\u03B7',\n  '\\u03AF': '\\u03B9',\n  '\\u03CA': '\\u03B9',\n  '\\u0390': '\\u03B9',\n  '\\u03CC': '\\u03BF',\n  '\\u03CD': '\\u03C5',\n  '\\u03CB': '\\u03C5',\n  '\\u03B0': '\\u03C5',\n  '\\u03C9': '\\u03C9',\n  '\\u03C2': '\\u03C3'\n};\nfunction stripSpecialChars(text) {\n  const match = a => diacritics[a] || a;\n  return text.replace(/[^\\u0000-\\u007E]/g, match);\n}\nclass ItemsList {\n  constructor(_ngSelect, _selectionModel) {\n    this._ngSelect = _ngSelect;\n    this._selectionModel = _selectionModel;\n    this._items = [];\n    this._filteredItems = [];\n    this._markedIndex = -1;\n  }\n  get items() {\n    return this._items;\n  }\n  get filteredItems() {\n    return this._filteredItems;\n  }\n  get markedIndex() {\n    return this._markedIndex;\n  }\n  get selectedItems() {\n    return this._selectionModel.value;\n  }\n  get markedItem() {\n    return this._filteredItems[this._markedIndex];\n  }\n  get noItemsToSelect() {\n    return this._ngSelect.hideSelected && this._items.length === this.selectedItems.length;\n  }\n  get maxItemsSelected() {\n    return this._ngSelect.multiple && this._ngSelect.maxSelectedItems <= this.selectedItems.length;\n  }\n  get lastSelectedItem() {\n    let i = this.selectedItems.length - 1;\n    for (; i >= 0; i--) {\n      const item = this.selectedItems[i];\n      if (!item.disabled) {\n        return item;\n      }\n    }\n    return null;\n  }\n  setItems(items) {\n    this._items = items.map((item, index) => this.mapItem(item, index));\n    if (this._ngSelect.groupBy) {\n      this._groups = this._groupBy(this._items, this._ngSelect.groupBy);\n      this._items = this._flatten(this._groups);\n    } else {\n      this._groups = new Map();\n      this._groups.set(undefined, this._items);\n    }\n    this._filteredItems = [...this._items];\n  }\n  select(item) {\n    if (item.selected || this.maxItemsSelected) {\n      return;\n    }\n    const multiple = this._ngSelect.multiple;\n    if (!multiple) {\n      this.clearSelected();\n    }\n    this._selectionModel.select(item, multiple, this._ngSelect.selectableGroupAsModel);\n    if (this._ngSelect.hideSelected) {\n      this._hideSelected(item);\n    }\n  }\n  unselect(item) {\n    if (!item.selected) {\n      return;\n    }\n    this._selectionModel.unselect(item, this._ngSelect.multiple);\n    if (this._ngSelect.hideSelected && isDefined(item.index) && this._ngSelect.multiple) {\n      this._showSelected(item);\n    }\n  }\n  findItem(value) {\n    let findBy;\n    if (this._ngSelect.compareWith) {\n      findBy = item => this._ngSelect.compareWith(item.value, value);\n    } else if (this._ngSelect.bindValue) {\n      findBy = item => !item.children && this.resolveNested(item.value, this._ngSelect.bindValue) === value;\n    } else {\n      findBy = item => item.value === value || !item.children && item.label && item.label === this.resolveNested(value, this._ngSelect.bindLabel);\n    }\n    return this._items.find(item => findBy(item));\n  }\n  addItem(item) {\n    const option = this.mapItem(item, this._items.length);\n    this._items.push(option);\n    this._filteredItems.push(option);\n    return option;\n  }\n  clearSelected(keepDisabled = false) {\n    this._selectionModel.clear(keepDisabled);\n    this._items.forEach(item => {\n      item.selected = keepDisabled && item.selected && item.disabled;\n      item.marked = false;\n    });\n    if (this._ngSelect.hideSelected) {\n      this.resetFilteredItems();\n    }\n  }\n  findByLabel(term) {\n    term = stripSpecialChars(term).toLocaleLowerCase();\n    return this.filteredItems.find(item => {\n      const label = stripSpecialChars(item.label).toLocaleLowerCase();\n      return label.substr(0, term.length) === term;\n    });\n  }\n  filter(term) {\n    if (!term) {\n      this.resetFilteredItems();\n      return;\n    }\n    this._filteredItems = [];\n    term = this._ngSelect.searchFn ? term : stripSpecialChars(term).toLocaleLowerCase();\n    const match = this._ngSelect.searchFn || this._defaultSearchFn;\n    const hideSelected = this._ngSelect.hideSelected;\n    for (const key of Array.from(this._groups.keys())) {\n      const matchedItems = [];\n      for (const item of this._groups.get(key)) {\n        if (hideSelected && (item.parent && item.parent.selected || item.selected)) {\n          continue;\n        }\n        const searchItem = this._ngSelect.searchFn ? item.value : item;\n        if (match(term, searchItem)) {\n          matchedItems.push(item);\n        }\n      }\n      if (matchedItems.length > 0) {\n        const [last] = matchedItems.slice(-1);\n        if (last.parent) {\n          const head = this._items.find(x => x === last.parent);\n          this._filteredItems.push(head);\n        }\n        this._filteredItems.push(...matchedItems);\n      }\n    }\n  }\n  resetFilteredItems() {\n    if (this._filteredItems.length === this._items.length) {\n      return;\n    }\n    if (this._ngSelect.hideSelected && this.selectedItems.length > 0) {\n      this._filteredItems = this._items.filter(x => !x.selected);\n    } else {\n      this._filteredItems = this._items;\n    }\n  }\n  unmarkItem() {\n    this._markedIndex = -1;\n  }\n  markNextItem() {\n    this._stepToItem(+1);\n  }\n  markPreviousItem() {\n    this._stepToItem(-1);\n  }\n  markItem(item) {\n    this._markedIndex = this._filteredItems.indexOf(item);\n  }\n  markSelectedOrDefault(markDefault) {\n    if (this._filteredItems.length === 0) {\n      return;\n    }\n    const lastMarkedIndex = this._getLastMarkedIndex();\n    if (lastMarkedIndex > -1) {\n      this._markedIndex = lastMarkedIndex;\n    } else {\n      this._markedIndex = markDefault ? this.filteredItems.findIndex(x => !x.disabled) : -1;\n    }\n  }\n  resolveNested(option, key) {\n    if (!isObject(option)) {\n      return option;\n    }\n    if (key.indexOf('.') === -1) {\n      return option[key];\n    } else {\n      const keys = key.split('.');\n      let value = option;\n      for (let i = 0, len = keys.length; i < len; ++i) {\n        if (value == null) {\n          return null;\n        }\n        value = value[keys[i]];\n      }\n      return value;\n    }\n  }\n  mapItem(item, index) {\n    const label = isDefined(item.$ngOptionLabel) ? item.$ngOptionLabel : this.resolveNested(item, this._ngSelect.bindLabel);\n    const value = isDefined(item.$ngOptionValue) ? item.$ngOptionValue : item;\n    return {\n      index,\n      label: isDefined(label) ? label.toString() : '',\n      value,\n      disabled: item.disabled,\n      htmlId: `${this._ngSelect.dropdownId}-${index}`\n    };\n  }\n  mapSelectedItems() {\n    const multiple = this._ngSelect.multiple;\n    for (const selected of this.selectedItems) {\n      const value = this._ngSelect.bindValue ? this.resolveNested(selected.value, this._ngSelect.bindValue) : selected.value;\n      const item = isDefined(value) ? this.findItem(value) : null;\n      this._selectionModel.unselect(selected, multiple);\n      this._selectionModel.select(item || selected, multiple, this._ngSelect.selectableGroupAsModel);\n    }\n    if (this._ngSelect.hideSelected) {\n      this._filteredItems = this.filteredItems.filter(x => this.selectedItems.indexOf(x) === -1);\n    }\n  }\n  _showSelected(item) {\n    this._filteredItems.push(item);\n    if (item.parent) {\n      const parent = item.parent;\n      const parentExists = this._filteredItems.find(x => x === parent);\n      if (!parentExists) {\n        this._filteredItems.push(parent);\n      }\n    } else if (item.children) {\n      for (const child of item.children) {\n        child.selected = false;\n        this._filteredItems.push(child);\n      }\n    }\n    this._filteredItems = [...this._filteredItems.sort((a, b) => a.index - b.index)];\n  }\n  _hideSelected(item) {\n    this._filteredItems = this._filteredItems.filter(x => x !== item);\n    if (item.parent) {\n      const children = item.parent.children;\n      if (children.every(x => x.selected)) {\n        this._filteredItems = this._filteredItems.filter(x => x !== item.parent);\n      }\n    } else if (item.children) {\n      this._filteredItems = this.filteredItems.filter(x => x.parent !== item);\n    }\n  }\n  _defaultSearchFn(search, opt) {\n    const label = stripSpecialChars(opt.label).toLocaleLowerCase();\n    return label.indexOf(search) > -1;\n  }\n  _getNextItemIndex(steps) {\n    if (steps > 0) {\n      return this._markedIndex >= this._filteredItems.length - 1 ? 0 : this._markedIndex + 1;\n    }\n    return this._markedIndex <= 0 ? this._filteredItems.length - 1 : this._markedIndex - 1;\n  }\n  _stepToItem(steps) {\n    if (this._filteredItems.length === 0 || this._filteredItems.every(x => x.disabled)) {\n      return;\n    }\n    this._markedIndex = this._getNextItemIndex(steps);\n    if (this.markedItem.disabled) {\n      this._stepToItem(steps);\n    }\n  }\n  _getLastMarkedIndex() {\n    if (this._ngSelect.hideSelected) {\n      return -1;\n    }\n    if (this._markedIndex > -1 && this.markedItem === undefined) {\n      return -1;\n    }\n    const selectedIndex = this._filteredItems.indexOf(this.lastSelectedItem);\n    if (this.lastSelectedItem && selectedIndex < 0) {\n      return -1;\n    }\n    return Math.max(this.markedIndex, selectedIndex);\n  }\n  _groupBy(items, prop) {\n    const groups = new Map();\n    if (items.length === 0) {\n      return groups;\n    }\n    // Check if items are already grouped by given key.\n    if (Array.isArray(items[0].value[prop])) {\n      for (const item of items) {\n        const children = (item.value[prop] || []).map((x, index) => this.mapItem(x, index));\n        groups.set(item, children);\n      }\n      return groups;\n    }\n    const isFnKey = isFunction(this._ngSelect.groupBy);\n    const keyFn = item => {\n      const key = isFnKey ? prop(item.value) : item.value[prop];\n      return isDefined(key) ? key : undefined;\n    };\n    // Group items by key.\n    for (const item of items) {\n      const key = keyFn(item);\n      const group = groups.get(key);\n      if (group) {\n        group.push(item);\n      } else {\n        groups.set(key, [item]);\n      }\n    }\n    return groups;\n  }\n  _flatten(groups) {\n    const isGroupByFn = isFunction(this._ngSelect.groupBy);\n    const items = [];\n    for (const key of Array.from(groups.keys())) {\n      let i = items.length;\n      if (key === undefined) {\n        const withoutGroup = groups.get(undefined) || [];\n        items.push(...withoutGroup.map(x => {\n          x.index = i++;\n          return x;\n        }));\n        continue;\n      }\n      const isObjectKey = isObject(key);\n      const parent = {\n        label: isObjectKey ? '' : String(key),\n        children: undefined,\n        parent: null,\n        index: i++,\n        disabled: !this._ngSelect.selectableGroup,\n        htmlId: newId()\n      };\n      const groupKey = isGroupByFn ? this._ngSelect.bindLabel : this._ngSelect.groupBy;\n      const groupValue = this._ngSelect.groupValue || (() => {\n        if (isObjectKey) {\n          return key.value;\n        }\n        return {\n          [groupKey]: key\n        };\n      });\n      const children = groups.get(key).map(x => {\n        x.parent = parent;\n        x.children = undefined;\n        x.index = i++;\n        return x;\n      });\n      parent.children = children;\n      parent.value = groupValue(key, children.map(x => x.value));\n      items.push(parent);\n      items.push(...children);\n    }\n    return items;\n  }\n}\nvar KeyCode;\n(function (KeyCode) {\n  KeyCode[KeyCode[\"Tab\"] = 9] = \"Tab\";\n  KeyCode[KeyCode[\"Enter\"] = 13] = \"Enter\";\n  KeyCode[KeyCode[\"Esc\"] = 27] = \"Esc\";\n  KeyCode[KeyCode[\"Space\"] = 32] = \"Space\";\n  KeyCode[KeyCode[\"ArrowUp\"] = 38] = \"ArrowUp\";\n  KeyCode[KeyCode[\"ArrowDown\"] = 40] = \"ArrowDown\";\n  KeyCode[KeyCode[\"Backspace\"] = 8] = \"Backspace\";\n})(KeyCode || (KeyCode = {}));\nclass NgDropdownPanelService {\n  constructor() {\n    this._dimensions = {\n      itemHeight: 0,\n      panelHeight: 0,\n      itemsPerViewport: 0\n    };\n  }\n  get dimensions() {\n    return this._dimensions;\n  }\n  calculateItems(scrollPos, itemsLength, buffer) {\n    const d = this._dimensions;\n    const scrollHeight = d.itemHeight * itemsLength;\n    const scrollTop = Math.max(0, scrollPos);\n    const indexByScrollTop = scrollTop / scrollHeight * itemsLength;\n    let end = Math.min(itemsLength, Math.ceil(indexByScrollTop) + (d.itemsPerViewport + 1));\n    const maxStartEnd = end;\n    const maxStart = Math.max(0, maxStartEnd - d.itemsPerViewport);\n    let start = Math.min(maxStart, Math.floor(indexByScrollTop));\n    let topPadding = d.itemHeight * Math.ceil(start) - d.itemHeight * Math.min(start, buffer);\n    topPadding = !isNaN(topPadding) ? topPadding : 0;\n    start = !isNaN(start) ? start : -1;\n    end = !isNaN(end) ? end : -1;\n    start -= buffer;\n    start = Math.max(0, start);\n    end += buffer;\n    end = Math.min(itemsLength, end);\n    return {\n      topPadding,\n      scrollHeight,\n      start,\n      end\n    };\n  }\n  setDimensions(itemHeight, panelHeight) {\n    const itemsPerViewport = Math.max(1, Math.floor(panelHeight / itemHeight));\n    this._dimensions = {\n      itemHeight,\n      panelHeight,\n      itemsPerViewport\n    };\n  }\n  getScrollTo(itemTop, itemHeight, lastScroll) {\n    const {\n      panelHeight\n    } = this.dimensions;\n    const itemBottom = itemTop + itemHeight;\n    const top = lastScroll;\n    const bottom = top + panelHeight;\n    if (panelHeight >= itemBottom && lastScroll === itemTop) {\n      return null;\n    }\n    if (itemBottom > bottom) {\n      return top + itemBottom - bottom;\n    } else if (itemTop <= top) {\n      return itemTop;\n    }\n    return null;\n  }\n  static {\n    this.ɵfac = function NgDropdownPanelService_Factory(t) {\n      return new (t || NgDropdownPanelService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgDropdownPanelService,\n      factory: NgDropdownPanelService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelService, [{\n    type: Injectable\n  }], null, null);\n})();\nconst CSS_POSITIONS = ['top', 'right', 'bottom', 'left'];\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nclass NgDropdownPanelComponent {\n  constructor(_renderer, _zone, _panelService, _elementRef, _document) {\n    this._renderer = _renderer;\n    this._zone = _zone;\n    this._panelService = _panelService;\n    this._document = _document;\n    this.items = [];\n    this.position = 'auto';\n    this.virtualScroll = false;\n    this.filterValue = null;\n    this.update = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.outsideClick = new EventEmitter();\n    this._destroy$ = new Subject();\n    this._scrollToEndFired = false;\n    this._updateScrollHeight = false;\n    this._lastScrollPosition = 0;\n    this._dropdown = _elementRef.nativeElement;\n  }\n  get currentPosition() {\n    return this._currentPosition;\n  }\n  get itemsLength() {\n    return this._itemsLength;\n  }\n  set itemsLength(value) {\n    if (value !== this._itemsLength) {\n      this._itemsLength = value;\n      this._onItemsLengthChanged();\n    }\n  }\n  get _startOffset() {\n    if (this.markedItem) {\n      const {\n        itemHeight,\n        panelHeight\n      } = this._panelService.dimensions;\n      const offset = this.markedItem.index * itemHeight;\n      return panelHeight > offset ? 0 : offset;\n    }\n    return 0;\n  }\n  ngOnInit() {\n    this._select = this._dropdown.parentElement;\n    this._virtualPadding = this.paddingElementRef.nativeElement;\n    this._scrollablePanel = this.scrollElementRef.nativeElement;\n    this._contentPanel = this.contentElementRef.nativeElement;\n    this._handleScroll();\n    this._handleOutsideClick();\n    this._appendDropdown();\n    this._setupMousedownListener();\n  }\n  ngOnChanges(changes) {\n    if (changes.items) {\n      const change = changes.items;\n      this._onItemsChange(change.currentValue, change.firstChange);\n    }\n  }\n  ngOnDestroy() {\n    this._destroy$.next();\n    this._destroy$.complete();\n    this._destroy$.unsubscribe();\n    if (this.appendTo) {\n      this._renderer.removeChild(this._dropdown.parentNode, this._dropdown);\n    }\n  }\n  scrollTo(option, startFromOption = false) {\n    if (!option) {\n      return;\n    }\n    const index = this.items.indexOf(option);\n    if (index < 0 || index >= this.itemsLength) {\n      return;\n    }\n    let scrollTo;\n    if (this.virtualScroll) {\n      const itemHeight = this._panelService.dimensions.itemHeight;\n      scrollTo = this._panelService.getScrollTo(index * itemHeight, itemHeight, this._lastScrollPosition);\n    } else {\n      const item = this._dropdown.querySelector(`#${option.htmlId}`);\n      const lastScroll = startFromOption ? item.offsetTop : this._lastScrollPosition;\n      scrollTo = this._panelService.getScrollTo(item.offsetTop, item.clientHeight, lastScroll);\n    }\n    if (isDefined(scrollTo)) {\n      this._scrollablePanel.scrollTop = scrollTo;\n    }\n  }\n  scrollToTag() {\n    const panel = this._scrollablePanel;\n    panel.scrollTop = panel.scrollHeight - panel.clientHeight;\n  }\n  adjustPosition() {\n    this._updateYPosition();\n  }\n  _handleDropdownPosition() {\n    this._currentPosition = this._calculateCurrentPosition(this._dropdown);\n    if (CSS_POSITIONS.includes(this._currentPosition)) {\n      this._updateDropdownClass(this._currentPosition);\n    } else {\n      this._updateDropdownClass('bottom');\n    }\n    if (this.appendTo) {\n      this._updateYPosition();\n    }\n    this._dropdown.style.opacity = '1';\n  }\n  _updateDropdownClass(currentPosition) {\n    CSS_POSITIONS.forEach(position => {\n      const REMOVE_CSS_CLASS = `ng-select-${position}`;\n      this._renderer.removeClass(this._dropdown, REMOVE_CSS_CLASS);\n      this._renderer.removeClass(this._select, REMOVE_CSS_CLASS);\n    });\n    const ADD_CSS_CLASS = `ng-select-${currentPosition}`;\n    this._renderer.addClass(this._dropdown, ADD_CSS_CLASS);\n    this._renderer.addClass(this._select, ADD_CSS_CLASS);\n  }\n  _handleScroll() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this.scrollElementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroy$), auditTime(0, SCROLL_SCHEDULER)).subscribe(e => {\n        const path = e.path || e.composedPath && e.composedPath();\n        const scrollTop = !path || path.length === 0 ? e.target.scrollTop : path[0].scrollTop;\n        this._onContentScrolled(scrollTop);\n      });\n    });\n  }\n  _handleOutsideClick() {\n    if (!this._document) {\n      return;\n    }\n    this._zone.runOutsideAngular(() => {\n      merge(fromEvent(this._document, 'touchstart', {\n        capture: true\n      }), fromEvent(this._document, 'mousedown', {\n        capture: true\n      })).pipe(takeUntil(this._destroy$)).subscribe($event => this._checkToClose($event));\n    });\n  }\n  _checkToClose($event) {\n    if (this._select.contains($event.target) || this._dropdown.contains($event.target)) {\n      return;\n    }\n    const path = $event.path || $event.composedPath && $event.composedPath();\n    if ($event.target && $event.target.shadowRoot && path && path[0] && this._select.contains(path[0])) {\n      return;\n    }\n    this._zone.run(() => this.outsideClick.emit());\n  }\n  _onItemsChange(items, firstChange) {\n    this.items = items || [];\n    this._scrollToEndFired = false;\n    this.itemsLength = items.length;\n    if (this.virtualScroll) {\n      this._updateItemsRange(firstChange);\n    } else {\n      this._setVirtualHeight();\n      this._updateItems(firstChange);\n    }\n  }\n  _updateItems(firstChange) {\n    this.update.emit(this.items);\n    if (firstChange === false) {\n      return;\n    }\n    this._zone.runOutsideAngular(() => {\n      Promise.resolve().then(() => {\n        const panelHeight = this._scrollablePanel.clientHeight;\n        this._panelService.setDimensions(0, panelHeight);\n        this._handleDropdownPosition();\n        this.scrollTo(this.markedItem, firstChange);\n      });\n    });\n  }\n  _updateItemsRange(firstChange) {\n    this._zone.runOutsideAngular(() => {\n      this._measureDimensions().then(() => {\n        if (firstChange) {\n          this._renderItemsRange(this._startOffset);\n          this._handleDropdownPosition();\n        } else {\n          this._renderItemsRange();\n        }\n      });\n    });\n  }\n  _onContentScrolled(scrollTop) {\n    if (this.virtualScroll) {\n      this._renderItemsRange(scrollTop);\n    }\n    this._lastScrollPosition = scrollTop;\n    this._fireScrollToEnd(scrollTop);\n  }\n  _updateVirtualHeight(height) {\n    if (this._updateScrollHeight) {\n      this._virtualPadding.style.height = `${height}px`;\n      this._updateScrollHeight = false;\n    }\n  }\n  _setVirtualHeight() {\n    if (!this._virtualPadding) {\n      return;\n    }\n    this._virtualPadding.style.height = `0px`;\n  }\n  _onItemsLengthChanged() {\n    this._updateScrollHeight = true;\n  }\n  _renderItemsRange(scrollTop = null) {\n    if (scrollTop && this._lastScrollPosition === scrollTop) {\n      return;\n    }\n    scrollTop = scrollTop || this._scrollablePanel.scrollTop;\n    const range = this._panelService.calculateItems(scrollTop, this.itemsLength, this.bufferAmount);\n    this._updateVirtualHeight(range.scrollHeight);\n    this._contentPanel.style.transform = `translateY(${range.topPadding}px)`;\n    this._zone.run(() => {\n      this.update.emit(this.items.slice(range.start, range.end));\n      this.scroll.emit({\n        start: range.start,\n        end: range.end\n      });\n    });\n    if (isDefined(scrollTop) && this._lastScrollPosition === 0) {\n      this._scrollablePanel.scrollTop = scrollTop;\n      this._lastScrollPosition = scrollTop;\n    }\n  }\n  _measureDimensions() {\n    if (this._panelService.dimensions.itemHeight > 0 || this.itemsLength === 0) {\n      return Promise.resolve(this._panelService.dimensions);\n    }\n    const [first] = this.items;\n    this.update.emit([first]);\n    return Promise.resolve().then(() => {\n      const option = this._dropdown.querySelector(`#${first.htmlId}`);\n      const optionHeight = option.clientHeight;\n      this._virtualPadding.style.height = `${optionHeight * this.itemsLength}px`;\n      const panelHeight = this._scrollablePanel.clientHeight;\n      this._panelService.setDimensions(optionHeight, panelHeight);\n      return this._panelService.dimensions;\n    });\n  }\n  _fireScrollToEnd(scrollTop) {\n    if (this._scrollToEndFired || scrollTop === 0) {\n      return;\n    }\n    const padding = this.virtualScroll ? this._virtualPadding : this._contentPanel;\n    if (scrollTop + this._dropdown.clientHeight >= padding.clientHeight - 1) {\n      this._zone.run(() => this.scrollToEnd.emit());\n      this._scrollToEndFired = true;\n    }\n  }\n  _calculateCurrentPosition(dropdownEl) {\n    if (this.position !== 'auto') {\n      return this.position;\n    }\n    const selectRect = this._select.getBoundingClientRect();\n    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n    const offsetTop = selectRect.top + window.pageYOffset;\n    const height = selectRect.height;\n    const dropdownHeight = dropdownEl.getBoundingClientRect().height;\n    if (offsetTop + height + dropdownHeight > scrollTop + document.documentElement.clientHeight) {\n      return 'top';\n    } else {\n      return 'bottom';\n    }\n  }\n  _appendDropdown() {\n    if (!this.appendTo) {\n      return;\n    }\n    this._parent = document.querySelector(this.appendTo);\n    if (!this._parent) {\n      throw new Error(`appendTo selector ${this.appendTo} did not found any parent element`);\n    }\n    this._updateXPosition();\n    this._parent.appendChild(this._dropdown);\n  }\n  _updateXPosition() {\n    const select = this._select.getBoundingClientRect();\n    const parent = this._parent.getBoundingClientRect();\n    const offsetLeft = select.left - parent.left;\n    this._dropdown.style.left = offsetLeft + 'px';\n    this._dropdown.style.width = select.width + 'px';\n    this._dropdown.style.minWidth = select.width + 'px';\n  }\n  _updateYPosition() {\n    const select = this._select.getBoundingClientRect();\n    const parent = this._parent.getBoundingClientRect();\n    const delta = select.height;\n    if (this._currentPosition === 'top') {\n      const offsetBottom = parent.bottom - select.bottom;\n      this._dropdown.style.bottom = offsetBottom + delta + 'px';\n      this._dropdown.style.top = 'auto';\n    } else if (this._currentPosition === 'bottom') {\n      const offsetTop = select.top - parent.top;\n      this._dropdown.style.top = offsetTop + delta + 'px';\n      this._dropdown.style.bottom = 'auto';\n    }\n  }\n  _setupMousedownListener() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this._dropdown, 'mousedown').pipe(takeUntil(this._destroy$)).subscribe(event => {\n        const target = event.target;\n        if (target.tagName === 'INPUT') {\n          return;\n        }\n        event.preventDefault();\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NgDropdownPanelComponent_Factory(t) {\n      return new (t || NgDropdownPanelComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(NgDropdownPanelService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgDropdownPanelComponent,\n      selectors: [[\"ng-dropdown-panel\"]],\n      viewQuery: function NgDropdownPanelComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7, ElementRef);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n          i0.ɵɵviewQuery(_c2, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentElementRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollElementRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paddingElementRef = _t.first);\n        }\n      },\n      inputs: {\n        items: \"items\",\n        markedItem: \"markedItem\",\n        position: \"position\",\n        appendTo: \"appendTo\",\n        bufferAmount: \"bufferAmount\",\n        virtualScroll: \"virtualScroll\",\n        headerTemplate: \"headerTemplate\",\n        footerTemplate: \"footerTemplate\",\n        filterValue: \"filterValue\"\n      },\n      outputs: {\n        update: \"update\",\n        scroll: \"scroll\",\n        scrollToEnd: \"scrollToEnd\",\n        outsideClick: \"outsideClick\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 6,\n      consts: [[\"scroll\", \"\"], [\"padding\", \"\"], [\"content\", \"\"], [\"class\", \"ng-dropdown-header\", 4, \"ngIf\"], [\"role\", \"listbox\", 1, \"ng-dropdown-panel-items\", \"scroll-host\"], [\"class\", \"ng-dropdown-footer\", 4, \"ngIf\"], [1, \"ng-dropdown-header\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ng-dropdown-footer\"]],\n      template: function NgDropdownPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NgDropdownPanelComponent_div_0_Template, 2, 4, \"div\", 3);\n          i0.ɵɵelementStart(1, \"div\", 4, 0);\n          i0.ɵɵelement(3, \"div\", null, 1);\n          i0.ɵɵelementStart(5, \"div\", null, 2);\n          i0.ɵɵprojection(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, NgDropdownPanelComponent_div_8_Template, 2, 4, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.headerTemplate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"total-padding\", ctx.virtualScroll);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"scrollable-content\", ctx.virtualScroll && ctx.items.length);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.footerTemplate);\n        }\n      },\n      dependencies: [i3.NgIf, i3.NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'ng-dropdown-panel',\n      template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll role=\"listbox\" class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: NgDropdownPanelService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    items: [{\n      type: Input\n    }],\n    markedItem: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    headerTemplate: [{\n      type: Input\n    }],\n    footerTemplate: [{\n      type: Input\n    }],\n    filterValue: [{\n      type: Input\n    }],\n    update: [{\n      type: Output\n    }],\n    scroll: [{\n      type: Output\n    }],\n    scrollToEnd: [{\n      type: Output\n    }],\n    outsideClick: [{\n      type: Output\n    }],\n    contentElementRef: [{\n      type: ViewChild,\n      args: ['content', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    scrollElementRef: [{\n      type: ViewChild,\n      args: ['scroll', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    paddingElementRef: [{\n      type: ViewChild,\n      args: ['padding', {\n        read: ElementRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass NgOptionComponent {\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = this._isDisabled(value);\n  }\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.stateChange$ = new Subject();\n    this._disabled = false;\n  }\n  get label() {\n    return (this.elementRef.nativeElement.textContent || '').trim();\n  }\n  ngOnChanges(changes) {\n    if (changes.disabled) {\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this._disabled\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.label !== this._previousLabel) {\n      this._previousLabel = this.label;\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this._disabled,\n        label: this.elementRef.nativeElement.innerHTML\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.stateChange$.complete();\n  }\n  _isDisabled(value) {\n    return value != null && `${value}` !== 'false';\n  }\n  static {\n    this.ɵfac = function NgOptionComponent_Factory(t) {\n      return new (t || NgOptionComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgOptionComponent,\n      selectors: [[\"ng-option\"]],\n      inputs: {\n        value: \"value\",\n        disabled: \"disabled\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      template: function NgOptionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-option',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `<ng-content></ng-content>`\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\nclass NgSelectConfig {\n  constructor() {\n    this.notFoundText = 'No items found';\n    this.typeToSearchText = 'Type to search';\n    this.addTagText = 'Add item';\n    this.loadingText = 'Loading...';\n    this.clearAllText = 'Clear all';\n    this.disableVirtualScroll = true;\n    this.openOnEnter = true;\n    this.appearance = 'underline';\n  }\n  static {\n    this.ɵfac = function NgSelectConfig_Factory(t) {\n      return new (t || NgSelectConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgSelectConfig,\n      factory: NgSelectConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ConsoleService {\n  warn(message) {\n    console.warn(message);\n  }\n  static {\n    this.ɵfac = function ConsoleService_Factory(t) {\n      return new (t || ConsoleService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConsoleService,\n      factory: ConsoleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConsoleService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst SELECTION_MODEL_FACTORY = new InjectionToken('ng-select-selection-model');\nclass NgSelectComponent {\n  get items() {\n    return this._items;\n  }\n  set items(value) {\n    if (value === null) {\n      value = [];\n    }\n    this._itemsAreUsed = true;\n    this._items = value;\n  }\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (fn !== undefined && fn !== null && !isFunction(fn)) {\n      throw Error('`compareWith` must be a function.');\n    }\n    this._compareWith = fn;\n  }\n  get clearSearchOnAdd() {\n    if (isDefined(this._clearSearchOnAdd)) {\n      return this._clearSearchOnAdd;\n    } else if (isDefined(this.config.clearSearchOnAdd)) {\n      return this.config.clearSearchOnAdd;\n    }\n    return this.closeOnSelect;\n  }\n  set clearSearchOnAdd(value) {\n    this._clearSearchOnAdd = value;\n  }\n  get deselectOnClick() {\n    if (isDefined(this._deselectOnClick)) {\n      return this._deselectOnClick;\n    } else if (isDefined(this.config.deselectOnClick)) {\n      return this.config.deselectOnClick;\n    }\n    return this.multiple;\n  }\n  set deselectOnClick(value) {\n    this._deselectOnClick = value;\n  }\n  get disabled() {\n    return this.readonly || this._disabled;\n  }\n  get filtered() {\n    return !!this.searchTerm && this.searchable || this._isComposing;\n  }\n  get single() {\n    return !this.multiple;\n  }\n  get _editableSearchTerm() {\n    return this.editableSearchTerm && !this.multiple;\n  }\n  constructor(classes, autoFocus, config, newSelectionModel, _elementRef, _cd, _console) {\n    this.classes = classes;\n    this.autoFocus = autoFocus;\n    this.config = config;\n    this._cd = _cd;\n    this._console = _console;\n    this.markFirst = true;\n    this.dropdownPosition = 'auto';\n    this.loading = false;\n    this.closeOnSelect = true;\n    this.hideSelected = false;\n    this.selectOnTab = false;\n    this.bufferAmount = 4;\n    this.selectableGroup = false;\n    this.selectableGroupAsModel = true;\n    this.searchFn = null;\n    this.trackByFn = null;\n    this.clearOnBackspace = true;\n    this.labelForId = null;\n    this.inputAttrs = {};\n    this.readonly = false;\n    this.searchWhileComposing = true;\n    this.minTermLength = 0;\n    this.editableSearchTerm = false;\n    this.keyDownFn = _ => true;\n    this.multiple = false;\n    this.addTag = false;\n    this.searchable = true;\n    this.clearable = true;\n    this.isOpen = false;\n    // output events\n    this.blurEvent = new EventEmitter();\n    this.focusEvent = new EventEmitter();\n    this.changeEvent = new EventEmitter();\n    this.openEvent = new EventEmitter();\n    this.closeEvent = new EventEmitter();\n    this.searchEvent = new EventEmitter();\n    this.clearEvent = new EventEmitter();\n    this.addEvent = new EventEmitter();\n    this.removeEvent = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.useDefaultClass = true;\n    this.viewPortItems = [];\n    this.searchTerm = null;\n    this.dropdownId = newId();\n    this.escapeHTML = true;\n    this._items = [];\n    this._defaultLabel = 'label';\n    this._pressedKeys = [];\n    this._isComposing = false;\n    this._destroy$ = new Subject();\n    this._keyPress$ = new Subject();\n    this._onChange = _ => {};\n    this._onTouched = () => {};\n    this.clearItem = item => {\n      const option = this.selectedItems.find(x => x.value === item);\n      this.unselect(option);\n    };\n    this.trackByOption = (_, item) => {\n      if (this.trackByFn) {\n        return this.trackByFn(item.value);\n      }\n      return item;\n    };\n    this._mergeGlobalConfig(config);\n    this.itemsList = new ItemsList(this, newSelectionModel());\n    this.element = _elementRef.nativeElement;\n  }\n  get selectedItems() {\n    return this.itemsList.selectedItems;\n  }\n  get selectedValues() {\n    return this.selectedItems.map(x => x.value);\n  }\n  get hasValue() {\n    return this.selectedItems.length > 0;\n  }\n  get currentPanelPosition() {\n    if (this.dropdownPanel) {\n      return this.dropdownPanel.currentPosition;\n    }\n    return undefined;\n  }\n  ngOnInit() {\n    this._handleKeyPresses();\n    this._setInputAttributes();\n  }\n  ngOnChanges(changes) {\n    if (changes.multiple) {\n      this.itemsList.clearSelected();\n    }\n    if (changes.items) {\n      this._setItems(changes.items.currentValue || []);\n    }\n    if (changes.isOpen) {\n      this._manualOpen = isDefined(changes.isOpen.currentValue);\n    }\n  }\n  ngAfterViewInit() {\n    if (!this._itemsAreUsed) {\n      this.escapeHTML = false;\n      this._setItemsFromNgOptions();\n    }\n    if (isDefined(this.autoFocus)) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this._destroy$.next();\n    this._destroy$.complete();\n  }\n  handleKeyDown($event) {\n    const keyCode = KeyCode[$event.which];\n    if (keyCode) {\n      if (this.keyDownFn($event) === false) {\n        return;\n      }\n      this.handleKeyCode($event);\n    } else if ($event.key && $event.key.length === 1) {\n      this._keyPress$.next($event.key.toLocaleLowerCase());\n    }\n  }\n  handleKeyCode($event) {\n    const target = $event.target;\n    if (this.clearButton && this.clearButton.nativeElement === target) {\n      this.handleKeyCodeClear($event);\n    } else {\n      this.handleKeyCodeInput($event);\n    }\n  }\n  handleKeyCodeInput($event) {\n    switch ($event.which) {\n      case KeyCode.ArrowDown:\n        this._handleArrowDown($event);\n        break;\n      case KeyCode.ArrowUp:\n        this._handleArrowUp($event);\n        break;\n      case KeyCode.Space:\n        this._handleSpace($event);\n        break;\n      case KeyCode.Enter:\n        this._handleEnter($event);\n        break;\n      case KeyCode.Tab:\n        this._handleTab($event);\n        break;\n      case KeyCode.Esc:\n        this.close();\n        $event.preventDefault();\n        break;\n      case KeyCode.Backspace:\n        this._handleBackspace();\n        break;\n    }\n  }\n  handleKeyCodeClear($event) {\n    switch ($event.which) {\n      case KeyCode.Enter:\n        this.handleClearClick();\n        $event.preventDefault();\n        break;\n    }\n  }\n  handleMousedown($event) {\n    const target = $event.target;\n    if (target.tagName !== 'INPUT') {\n      $event.preventDefault();\n    }\n    if (target.classList.contains('ng-clear-wrapper')) {\n      this.handleClearClick();\n      return;\n    }\n    if (target.classList.contains('ng-arrow-wrapper')) {\n      this.handleArrowClick();\n      return;\n    }\n    if (target.classList.contains('ng-value-icon')) {\n      return;\n    }\n    if (!this.focused) {\n      this.focus();\n    }\n    if (this.searchable) {\n      this.open();\n    } else {\n      this.toggle();\n    }\n  }\n  handleArrowClick() {\n    if (this.isOpen) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n  handleClearClick() {\n    if (this.hasValue) {\n      this.itemsList.clearSelected(true);\n      this._updateNgModel();\n    }\n    this._clearSearch();\n    this.focus();\n    this.clearEvent.emit();\n    this._onSelectionChanged();\n  }\n  clearModel() {\n    if (!this.clearable) {\n      return;\n    }\n    this.itemsList.clearSelected();\n    this._updateNgModel();\n  }\n  writeValue(value) {\n    this.itemsList.clearSelected();\n    this._handleWriteValue(value);\n    this._cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  setDisabledState(state) {\n    this._disabled = state;\n    this._cd.markForCheck();\n  }\n  toggle() {\n    if (!this.isOpen) {\n      this.open();\n    } else {\n      this.close();\n    }\n  }\n  open() {\n    if (this.disabled || this.isOpen || this._manualOpen) {\n      return;\n    }\n    if (!this._isTypeahead && !this.addTag && this.itemsList.noItemsToSelect) {\n      return;\n    }\n    this.isOpen = true;\n    this.itemsList.markSelectedOrDefault(this.markFirst);\n    this.openEvent.emit();\n    if (!this.searchTerm) {\n      this.focus();\n    }\n    this.detectChanges();\n  }\n  close() {\n    if (!this.isOpen || this._manualOpen) {\n      return;\n    }\n    this.isOpen = false;\n    this._isComposing = false;\n    if (!this._editableSearchTerm) {\n      this._clearSearch();\n    } else {\n      this.itemsList.resetFilteredItems();\n    }\n    this.itemsList.unmarkItem();\n    this._onTouched();\n    this.closeEvent.emit();\n    this._cd.markForCheck();\n  }\n  toggleItem(item) {\n    if (!item || item.disabled || this.disabled) {\n      return;\n    }\n    if (this.deselectOnClick && item.selected) {\n      this.unselect(item);\n    } else {\n      this.select(item);\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n    this._onSelectionChanged();\n  }\n  select(item) {\n    if (!item.selected) {\n      this.itemsList.select(item);\n      if (this.clearSearchOnAdd && !this._editableSearchTerm) {\n        this._clearSearch();\n      }\n      this._updateNgModel();\n      if (this.multiple) {\n        this.addEvent.emit(item.value);\n      }\n    }\n    if (this.closeOnSelect || this.itemsList.noItemsToSelect) {\n      this.close();\n    }\n  }\n  focus() {\n    this.searchInput.nativeElement.focus();\n  }\n  blur() {\n    this.searchInput.nativeElement.blur();\n  }\n  unselect(item) {\n    if (!item) {\n      return;\n    }\n    this.itemsList.unselect(item);\n    this.focus();\n    this._updateNgModel();\n    this.removeEvent.emit(item.value);\n  }\n  selectTag() {\n    let tag;\n    if (isFunction(this.addTag)) {\n      tag = this.addTag(this.searchTerm);\n    } else {\n      tag = this._primitive ? this.searchTerm : {\n        [this.bindLabel]: this.searchTerm\n      };\n    }\n    const handleTag = item => this._isTypeahead || !this.isOpen ? this.itemsList.mapItem(item, null) : this.itemsList.addItem(item);\n    if (isPromise(tag)) {\n      tag.then(item => this.select(handleTag(item))).catch(() => {});\n    } else if (tag) {\n      this.select(handleTag(tag));\n    }\n  }\n  showClear() {\n    return this.clearable && (this.hasValue || this.searchTerm) && !this.disabled;\n  }\n  focusOnClear() {\n    this.blur();\n    if (this.clearButton) {\n      this.clearButton.nativeElement.focus();\n    }\n  }\n  get showAddTag() {\n    if (!this._validTerm) {\n      return false;\n    }\n    const term = this.searchTerm.toLowerCase().trim();\n    return this.addTag && !this.itemsList.filteredItems.some(x => x.label.toLowerCase() === term) && (!this.hideSelected && this.isOpen || !this.selectedItems.some(x => x.label.toLowerCase() === term)) && !this.loading;\n  }\n  showNoItemsFound() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return (empty && !this._isTypeahead && !this.loading || empty && this._isTypeahead && this._validTerm && !this.loading) && !this.showAddTag;\n  }\n  showTypeToSearch() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return empty && this._isTypeahead && !this._validTerm && !this.loading;\n  }\n  onCompositionStart() {\n    this._isComposing = true;\n  }\n  onCompositionEnd(term) {\n    this._isComposing = false;\n    if (this.searchWhileComposing) {\n      return;\n    }\n    this.filter(term);\n  }\n  filter(term) {\n    if (this._isComposing && !this.searchWhileComposing) {\n      return;\n    }\n    this.searchTerm = term;\n    if (this._isTypeahead && (this._validTerm || this.minTermLength === 0)) {\n      this.typeahead.next(term);\n    }\n    if (!this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n      if (this.isOpen) {\n        this.itemsList.markSelectedOrDefault(this.markFirst);\n      }\n    }\n    this.searchEvent.emit({\n      term,\n      items: this.itemsList.filteredItems.map(x => x.value)\n    });\n    this.open();\n  }\n  onInputFocus($event) {\n    if (this.focused) {\n      return;\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n    this.element.classList.add('ng-select-focused');\n    this.focusEvent.emit($event);\n    this.focused = true;\n  }\n  onInputBlur($event) {\n    this.element.classList.remove('ng-select-focused');\n    this.blurEvent.emit($event);\n    if (!this.isOpen && !this.disabled) {\n      this._onTouched();\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n    this.focused = false;\n  }\n  onItemHover(item) {\n    if (item.disabled) {\n      return;\n    }\n    this.itemsList.markItem(item);\n  }\n  detectChanges() {\n    if (!this._cd.destroyed) {\n      this._cd.detectChanges();\n    }\n  }\n  _setSearchTermFromItems() {\n    const selected = this.selectedItems && this.selectedItems[0];\n    this.searchTerm = selected && selected.label || null;\n  }\n  _setItems(items) {\n    const firstItem = items[0];\n    this.bindLabel = this.bindLabel || this._defaultLabel;\n    this._primitive = isDefined(firstItem) ? !isObject(firstItem) : this._primitive || this.bindLabel === this._defaultLabel;\n    this.itemsList.setItems(items);\n    if (items.length > 0 && this.hasValue) {\n      this.itemsList.mapSelectedItems();\n    }\n    if (this.isOpen && isDefined(this.searchTerm) && !this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n    }\n    if (this._isTypeahead || this.isOpen) {\n      this.itemsList.markSelectedOrDefault(this.markFirst);\n    }\n  }\n  _setItemsFromNgOptions() {\n    const mapNgOptions = options => {\n      this.items = options.map(option => ({\n        $ngOptionValue: option.value,\n        $ngOptionLabel: option.elementRef.nativeElement.innerHTML,\n        disabled: option.disabled\n      }));\n      this.itemsList.setItems(this.items);\n      if (this.hasValue) {\n        this.itemsList.mapSelectedItems();\n      }\n      this.detectChanges();\n    };\n    const handleOptionChange = () => {\n      const changedOrDestroyed = merge(this.ngOptions.changes, this._destroy$);\n      merge(...this.ngOptions.map(option => option.stateChange$)).pipe(takeUntil(changedOrDestroyed)).subscribe(option => {\n        const item = this.itemsList.findItem(option.value);\n        item.disabled = option.disabled;\n        item.label = option.label || item.label;\n        this._cd.detectChanges();\n      });\n    };\n    this.ngOptions.changes.pipe(startWith(this.ngOptions), takeUntil(this._destroy$)).subscribe(options => {\n      this.bindLabel = this._defaultLabel;\n      mapNgOptions(options);\n      handleOptionChange();\n    });\n  }\n  _isValidWriteValue(value) {\n    if (!isDefined(value) || this.multiple && value === '' || Array.isArray(value) && value.length === 0) {\n      return false;\n    }\n    const validateBinding = item => {\n      if (!isDefined(this.compareWith) && isObject(item) && this.bindValue) {\n        this._console.warn(`Setting object(${JSON.stringify(item)}) as your model with bindValue is not allowed unless [compareWith] is used.`);\n        return false;\n      }\n      return true;\n    };\n    if (this.multiple) {\n      if (!Array.isArray(value)) {\n        this._console.warn('Multiple select ngModel should be array.');\n        return false;\n      }\n      return value.every(item => validateBinding(item));\n    } else {\n      return validateBinding(value);\n    }\n  }\n  _handleWriteValue(ngModel) {\n    if (!this._isValidWriteValue(ngModel)) {\n      return;\n    }\n    const select = val => {\n      let item = this.itemsList.findItem(val);\n      if (item) {\n        this.itemsList.select(item);\n      } else {\n        const isValObject = isObject(val);\n        const isPrimitive = !isValObject && !this.bindValue;\n        if (isValObject || isPrimitive) {\n          this.itemsList.select(this.itemsList.mapItem(val, null));\n        } else if (this.bindValue) {\n          item = {\n            [this.bindLabel]: null,\n            [this.bindValue]: val\n          };\n          this.itemsList.select(this.itemsList.mapItem(item, null));\n        }\n      }\n    };\n    if (this.multiple) {\n      ngModel.forEach(item => select(item));\n    } else {\n      select(ngModel);\n    }\n  }\n  _handleKeyPresses() {\n    if (this.searchable) {\n      return;\n    }\n    this._keyPress$.pipe(takeUntil(this._destroy$), tap(letter => this._pressedKeys.push(letter)), debounceTime(200), filter(() => this._pressedKeys.length > 0), map(() => this._pressedKeys.join(''))).subscribe(term => {\n      const item = this.itemsList.findByLabel(term);\n      if (item) {\n        if (this.isOpen) {\n          this.itemsList.markItem(item);\n          this._scrollToMarked();\n          this._cd.markForCheck();\n        } else {\n          this.select(item);\n        }\n      }\n      this._pressedKeys = [];\n    });\n  }\n  _setInputAttributes() {\n    const input = this.searchInput.nativeElement;\n    const attributes = {\n      type: 'text',\n      autocorrect: 'off',\n      autocapitalize: 'off',\n      autocomplete: this.labelForId ? 'off' : this.dropdownId,\n      ...this.inputAttrs\n    };\n    for (const key of Object.keys(attributes)) {\n      input.setAttribute(key, attributes[key]);\n    }\n  }\n  _updateNgModel() {\n    const model = [];\n    for (const item of this.selectedItems) {\n      if (this.bindValue) {\n        let value = null;\n        if (item.children) {\n          const groupKey = this.groupValue ? this.bindValue : this.groupBy;\n          value = item.value[groupKey || this.groupBy];\n        } else {\n          value = this.itemsList.resolveNested(item.value, this.bindValue);\n        }\n        model.push(value);\n      } else {\n        model.push(item.value);\n      }\n    }\n    const selected = this.selectedItems.map(x => x.value);\n    if (this.multiple) {\n      this._onChange(model);\n      this.changeEvent.emit(selected);\n    } else {\n      this._onChange(isDefined(model[0]) ? model[0] : null);\n      this.changeEvent.emit(selected[0]);\n    }\n    this._cd.markForCheck();\n  }\n  _clearSearch() {\n    if (!this.searchTerm) {\n      return;\n    }\n    this._changeSearch(null);\n    this.itemsList.resetFilteredItems();\n  }\n  _changeSearch(searchTerm) {\n    this.searchTerm = searchTerm;\n    if (this._isTypeahead) {\n      this.typeahead.next(searchTerm);\n    }\n  }\n  _scrollToMarked() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n    this.dropdownPanel.scrollTo(this.itemsList.markedItem);\n  }\n  _scrollToTag() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n    this.dropdownPanel.scrollToTag();\n  }\n  _onSelectionChanged() {\n    if (this.isOpen && this.deselectOnClick && this.appendTo) {\n      // Make sure items are rendered.\n      this._cd.detectChanges();\n      this.dropdownPanel.adjustPosition();\n    }\n  }\n  _handleTab($event) {\n    if (this.isOpen === false) {\n      if (this.showClear()) {\n        this.focusOnClear();\n        $event.preventDefault();\n      } else if (!this.addTag) {\n        return;\n      }\n    }\n    if (this.selectOnTab) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n        $event.preventDefault();\n      } else if (this.showAddTag) {\n        this.selectTag();\n        $event.preventDefault();\n      } else {\n        this.close();\n      }\n    } else {\n      this.close();\n    }\n  }\n  _handleEnter($event) {\n    if (this.isOpen || this._manualOpen) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n      } else if (this.showAddTag) {\n        this.selectTag();\n      }\n    } else if (this.openOnEnter) {\n      this.open();\n    } else {\n      return;\n    }\n    $event.preventDefault();\n  }\n  _handleSpace($event) {\n    if (this.isOpen || this._manualOpen) {\n      return;\n    }\n    this.open();\n    $event.preventDefault();\n  }\n  _handleArrowDown($event) {\n    if (this._nextItemIsTag(+1)) {\n      this.itemsList.unmarkItem();\n      this._scrollToTag();\n    } else {\n      this.itemsList.markNextItem();\n      this._scrollToMarked();\n    }\n    this.open();\n    $event.preventDefault();\n  }\n  _handleArrowUp($event) {\n    if (!this.isOpen) {\n      return;\n    }\n    if (this._nextItemIsTag(-1)) {\n      this.itemsList.unmarkItem();\n      this._scrollToTag();\n    } else {\n      this.itemsList.markPreviousItem();\n      this._scrollToMarked();\n    }\n    $event.preventDefault();\n  }\n  _nextItemIsTag(nextStep) {\n    const nextIndex = this.itemsList.markedIndex + nextStep;\n    return this.addTag && this.searchTerm && this.itemsList.markedItem && (nextIndex < 0 || nextIndex === this.itemsList.filteredItems.length);\n  }\n  _handleBackspace() {\n    if (this.searchTerm || !this.clearable || !this.clearOnBackspace || !this.hasValue) {\n      return;\n    }\n    if (this.multiple) {\n      this.unselect(this.itemsList.lastSelectedItem);\n    } else {\n      this.clearModel();\n    }\n  }\n  get _isTypeahead() {\n    return this.typeahead && this.typeahead.observers.length > 0;\n  }\n  get _validTerm() {\n    const term = this.searchTerm && this.searchTerm.trim();\n    return term && term.length >= this.minTermLength;\n  }\n  _mergeGlobalConfig(config) {\n    this.placeholder = this.placeholder || config.placeholder;\n    this.notFoundText = this.notFoundText || config.notFoundText;\n    this.typeToSearchText = this.typeToSearchText || config.typeToSearchText;\n    this.addTagText = this.addTagText || config.addTagText;\n    this.loadingText = this.loadingText || config.loadingText;\n    this.clearAllText = this.clearAllText || config.clearAllText;\n    this.virtualScroll = isDefined(this.virtualScroll) ? this.virtualScroll : isDefined(config.disableVirtualScroll) ? !config.disableVirtualScroll : false;\n    this.openOnEnter = isDefined(this.openOnEnter) ? this.openOnEnter : config.openOnEnter;\n    this.appendTo = this.appendTo || config.appendTo;\n    this.bindValue = this.bindValue || config.bindValue;\n    this.bindLabel = this.bindLabel || config.bindLabel;\n    this.appearance = this.appearance || config.appearance;\n  }\n  static {\n    this.ɵfac = function NgSelectComponent_Factory(t) {\n      return new (t || NgSelectComponent)(i0.ɵɵinjectAttribute('class'), i0.ɵɵinjectAttribute('autofocus'), i0.ɵɵdirectiveInject(NgSelectConfig), i0.ɵɵdirectiveInject(SELECTION_MODEL_FACTORY), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ConsoleService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgSelectComponent,\n      selectors: [[\"ng-select\"]],\n      contentQueries: function NgSelectComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgOptionTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgOptgroupTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLabelTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgMultiLabelTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgHeaderTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgFooterTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgNotFoundTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgTypeToSearchTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLoadingTextTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgTagTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLoadingSpinnerTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgOptionComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optgroupTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.labelTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiLabelTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notFoundTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.typeToSearchTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingTextTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tagTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingSpinnerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ngOptions = _t);\n        }\n      },\n      viewQuery: function NgSelectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NgDropdownPanelComponent, 5);\n          i0.ɵɵviewQuery(_c5, 7);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownPanel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearButton = _t.first);\n        }\n      },\n      hostVars: 20,\n      hostBindings: function NgSelectComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function NgSelectComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-select-typeahead\", ctx.typeahead)(\"ng-select-multiple\", ctx.multiple)(\"ng-select-taggable\", ctx.addTag)(\"ng-select-searchable\", ctx.searchable)(\"ng-select-clearable\", ctx.clearable)(\"ng-select-opened\", ctx.isOpen)(\"ng-select\", ctx.useDefaultClass)(\"ng-select-disabled\", ctx.disabled)(\"ng-select-filtered\", ctx.filtered)(\"ng-select-single\", ctx.single);\n        }\n      },\n      inputs: {\n        bindLabel: \"bindLabel\",\n        bindValue: \"bindValue\",\n        markFirst: \"markFirst\",\n        placeholder: \"placeholder\",\n        notFoundText: \"notFoundText\",\n        typeToSearchText: \"typeToSearchText\",\n        addTagText: \"addTagText\",\n        loadingText: \"loadingText\",\n        clearAllText: \"clearAllText\",\n        appearance: \"appearance\",\n        dropdownPosition: \"dropdownPosition\",\n        appendTo: \"appendTo\",\n        loading: \"loading\",\n        closeOnSelect: \"closeOnSelect\",\n        hideSelected: \"hideSelected\",\n        selectOnTab: \"selectOnTab\",\n        openOnEnter: \"openOnEnter\",\n        maxSelectedItems: \"maxSelectedItems\",\n        groupBy: \"groupBy\",\n        groupValue: \"groupValue\",\n        bufferAmount: \"bufferAmount\",\n        virtualScroll: \"virtualScroll\",\n        selectableGroup: \"selectableGroup\",\n        selectableGroupAsModel: \"selectableGroupAsModel\",\n        searchFn: \"searchFn\",\n        trackByFn: \"trackByFn\",\n        clearOnBackspace: \"clearOnBackspace\",\n        labelForId: \"labelForId\",\n        inputAttrs: \"inputAttrs\",\n        tabIndex: \"tabIndex\",\n        readonly: \"readonly\",\n        searchWhileComposing: \"searchWhileComposing\",\n        minTermLength: \"minTermLength\",\n        editableSearchTerm: \"editableSearchTerm\",\n        keyDownFn: \"keyDownFn\",\n        typeahead: \"typeahead\",\n        multiple: \"multiple\",\n        addTag: \"addTag\",\n        searchable: \"searchable\",\n        clearable: \"clearable\",\n        isOpen: \"isOpen\",\n        items: \"items\",\n        compareWith: \"compareWith\",\n        clearSearchOnAdd: \"clearSearchOnAdd\",\n        deselectOnClick: \"deselectOnClick\"\n      },\n      outputs: {\n        blurEvent: \"blur\",\n        focusEvent: \"focus\",\n        changeEvent: \"change\",\n        openEvent: \"open\",\n        closeEvent: \"close\",\n        searchEvent: \"search\",\n        clearEvent: \"clear\",\n        addEvent: \"add\",\n        removeEvent: \"remove\",\n        scroll: \"scroll\",\n        scrollToEnd: \"scrollToEnd\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgSelectComponent),\n        multi: true\n      }, NgDropdownPanelService]), i0.ɵɵNgOnChangesFeature],\n      decls: 14,\n      vars: 19,\n      consts: [[\"searchInput\", \"\"], [\"defaultLabelTemplate\", \"\"], [\"defaultLoadingSpinnerTemplate\", \"\"], [\"clearButton\", \"\"], [\"defaultOptionTemplate\", \"\"], [\"defaultTagTemplate\", \"\"], [\"defaultNotFoundTemplate\", \"\"], [\"defaultTypeToSearchTemplate\", \"\"], [\"defaultLoadingTextTemplate\", \"\"], [1, \"ng-select-container\", 3, \"mousedown\"], [1, \"ng-value-container\"], [1, \"ng-placeholder\"], [4, \"ngIf\"], [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"ng-input\"], [\"aria-autocomplete\", \"list\", 3, \"input\", \"compositionstart\", \"compositionend\", \"focus\", \"blur\", \"change\", \"readOnly\", \"disabled\", \"value\"], [\"class\", \"ng-clear-wrapper\", \"tabindex\", \"0\", 3, \"title\", 4, \"ngIf\"], [1, \"ng-arrow-wrapper\"], [1, \"ng-arrow\"], [\"class\", \"ng-dropdown-panel\", \"role\", \"listbox\", \"aria-label\", \"Options list\", 3, \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ng-select-multiple\", \"ngClass\", \"id\", \"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\", 4, \"ngIf\"], [\"class\", \"ng-value\", 3, \"ng-value-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ng-value\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"aria-hidden\", \"true\", 1, \"ng-value-icon\", \"left\", 3, \"click\"], [1, \"ng-value-label\", 3, \"ngItemLabel\", \"escape\"], [3, \"ngTemplateOutlet\"], [1, \"ng-spinner-loader\"], [\"tabindex\", \"0\", 1, \"ng-clear-wrapper\", 3, \"title\"], [\"aria-hidden\", \"true\", 1, \"ng-clear\"], [\"role\", \"listbox\", \"aria-label\", \"Options list\", 1, \"ng-dropdown-panel\", 3, \"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\", \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ngClass\", \"id\"], [\"class\", \"ng-option\", 3, \"ng-option-disabled\", \"ng-option-selected\", \"ng-optgroup\", \"ng-option\", \"ng-option-child\", \"ng-option-marked\", \"click\", \"mouseover\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"ng-option\", \"role\", \"option\", 3, \"ng-option-marked\", \"mouseover\", \"click\", 4, \"ngIf\"], [1, \"ng-option\", 3, \"click\", \"mouseover\"], [1, \"ng-option-label\", 3, \"ngItemLabel\", \"escape\"], [\"role\", \"option\", 1, \"ng-option\", 3, \"mouseover\", \"click\"], [1, \"ng-tag-label\"], [1, \"ng-option\", \"ng-option-disabled\"]],\n      template: function NgSelectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 9);\n          i0.ɵɵlistener(\"mousedown\", function NgSelectComponent_Template_div_mousedown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleMousedown($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 10)(2, \"div\", 11);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NgSelectComponent_ng_container_4_Template, 2, 2, \"ng-container\", 12)(5, NgSelectComponent_5_Template, 1, 5, null, 12);\n          i0.ɵɵelementStart(6, \"div\", 13)(7, \"input\", 14, 0);\n          i0.ɵɵlistener(\"input\", function NgSelectComponent_Template_input_input_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const searchInput_r6 = i0.ɵɵreference(8);\n            return i0.ɵɵresetView(ctx.filter(searchInput_r6.value));\n          })(\"compositionstart\", function NgSelectComponent_Template_input_compositionstart_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCompositionStart());\n          })(\"compositionend\", function NgSelectComponent_Template_input_compositionend_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const searchInput_r6 = i0.ɵɵreference(8);\n            return i0.ɵɵresetView(ctx.onCompositionEnd(searchInput_r6.value));\n          })(\"focus\", function NgSelectComponent_Template_input_focus_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"blur\", function NgSelectComponent_Template_input_blur_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          })(\"change\", function NgSelectComponent_Template_input_change_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.stopPropagation());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, NgSelectComponent_ng_container_9_Template, 4, 1, \"ng-container\", 12)(10, NgSelectComponent_span_10_Template, 4, 1, \"span\", 15);\n          i0.ɵɵelementStart(11, \"span\", 16);\n          i0.ɵɵelement(12, \"span\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, NgSelectComponent_ng_dropdown_panel_13_Template, 7, 19, \"ng-dropdown-panel\", 18);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-appearance-outline\", ctx.appearance === \"outline\")(\"ng-has-value\", ctx.hasValue);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.placeholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (!ctx.multiLabelTemplate || !ctx.multiple) && ctx.selectedItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple && ctx.multiLabelTemplate && ctx.selectedValues.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-expanded\", ctx.isOpen)(\"aria-owns\", ctx.isOpen ? ctx.dropdownId : null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"readOnly\", !ctx.searchable || ctx.itemsList.maxItemsSelected)(\"disabled\", ctx.disabled)(\"value\", ctx.searchTerm ? ctx.searchTerm : \"\");\n          i0.ɵɵattribute(\"id\", ctx.labelForId)(\"tabindex\", ctx.tabIndex)(\"aria-activedescendant\", ctx.isOpen ? ctx.itemsList == null ? null : ctx.itemsList.markedItem == null ? null : ctx.itemsList.markedItem.htmlId : null)(\"aria-controls\", ctx.isOpen ? ctx.dropdownId : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showClear());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, NgDropdownPanelComponent, NgItemLabelDirective],\n      styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-select',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgSelectComponent),\n        multi: true\n      }, NgDropdownPanelService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" tabindex=\\\"0\\\" title=\\\"{{clearAllText}}\\\" #clearButton>\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\",\n      styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['class']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['autofocus']\n    }]\n  }, {\n    type: NgSelectConfig\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [SELECTION_MODEL_FACTORY]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: ConsoleService\n  }], {\n    bindLabel: [{\n      type: Input\n    }],\n    bindValue: [{\n      type: Input\n    }],\n    markFirst: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    notFoundText: [{\n      type: Input\n    }],\n    typeToSearchText: [{\n      type: Input\n    }],\n    addTagText: [{\n      type: Input\n    }],\n    loadingText: [{\n      type: Input\n    }],\n    clearAllText: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    dropdownPosition: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    closeOnSelect: [{\n      type: Input\n    }],\n    hideSelected: [{\n      type: Input\n    }],\n    selectOnTab: [{\n      type: Input\n    }],\n    openOnEnter: [{\n      type: Input\n    }],\n    maxSelectedItems: [{\n      type: Input\n    }],\n    groupBy: [{\n      type: Input\n    }],\n    groupValue: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    selectableGroup: [{\n      type: Input\n    }],\n    selectableGroupAsModel: [{\n      type: Input\n    }],\n    searchFn: [{\n      type: Input\n    }],\n    trackByFn: [{\n      type: Input\n    }],\n    clearOnBackspace: [{\n      type: Input\n    }],\n    labelForId: [{\n      type: Input\n    }],\n    inputAttrs: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    searchWhileComposing: [{\n      type: Input\n    }],\n    minTermLength: [{\n      type: Input\n    }],\n    editableSearchTerm: [{\n      type: Input\n    }],\n    keyDownFn: [{\n      type: Input\n    }],\n    typeahead: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-typeahead']\n    }],\n    multiple: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-multiple']\n    }],\n    addTag: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-taggable']\n    }],\n    searchable: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-searchable']\n    }],\n    clearable: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-clearable']\n    }],\n    isOpen: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-opened']\n    }],\n    items: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    clearSearchOnAdd: [{\n      type: Input\n    }],\n    deselectOnClick: [{\n      type: Input\n    }],\n    blurEvent: [{\n      type: Output,\n      args: ['blur']\n    }],\n    focusEvent: [{\n      type: Output,\n      args: ['focus']\n    }],\n    changeEvent: [{\n      type: Output,\n      args: ['change']\n    }],\n    openEvent: [{\n      type: Output,\n      args: ['open']\n    }],\n    closeEvent: [{\n      type: Output,\n      args: ['close']\n    }],\n    searchEvent: [{\n      type: Output,\n      args: ['search']\n    }],\n    clearEvent: [{\n      type: Output,\n      args: ['clear']\n    }],\n    addEvent: [{\n      type: Output,\n      args: ['add']\n    }],\n    removeEvent: [{\n      type: Output,\n      args: ['remove']\n    }],\n    scroll: [{\n      type: Output,\n      args: ['scroll']\n    }],\n    scrollToEnd: [{\n      type: Output,\n      args: ['scrollToEnd']\n    }],\n    optionTemplate: [{\n      type: ContentChild,\n      args: [NgOptionTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    optgroupTemplate: [{\n      type: ContentChild,\n      args: [NgOptgroupTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    labelTemplate: [{\n      type: ContentChild,\n      args: [NgLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    multiLabelTemplate: [{\n      type: ContentChild,\n      args: [NgMultiLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: [NgHeaderTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: [NgFooterTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    notFoundTemplate: [{\n      type: ContentChild,\n      args: [NgNotFoundTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    typeToSearchTemplate: [{\n      type: ContentChild,\n      args: [NgTypeToSearchTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingTextTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingTextTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    tagTemplate: [{\n      type: ContentChild,\n      args: [NgTagTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingSpinnerTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingSpinnerTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    dropdownPanel: [{\n      type: ViewChild,\n      args: [forwardRef(() => NgDropdownPanelComponent)]\n    }],\n    searchInput: [{\n      type: ViewChild,\n      args: ['searchInput', {\n        static: true\n      }]\n    }],\n    clearButton: [{\n      type: ViewChild,\n      args: ['clearButton']\n    }],\n    ngOptions: [{\n      type: ContentChildren,\n      args: [NgOptionComponent, {\n        descendants: true\n      }]\n    }],\n    useDefaultClass: [{\n      type: HostBinding,\n      args: ['class.ng-select']\n    }],\n    disabled: [{\n      type: HostBinding,\n      args: ['class.ng-select-disabled']\n    }],\n    filtered: [{\n      type: HostBinding,\n      args: ['class.ng-select-filtered']\n    }],\n    single: [{\n      type: HostBinding,\n      args: ['class.ng-select-single']\n    }],\n    handleKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nfunction DefaultSelectionModelFactory() {\n  return new DefaultSelectionModel();\n}\nclass DefaultSelectionModel {\n  constructor() {\n    this._selected = [];\n  }\n  get value() {\n    return this._selected;\n  }\n  select(item, multiple, groupAsModel) {\n    item.selected = true;\n    if (!item.children || !multiple && groupAsModel) {\n      this._selected.push(item);\n    }\n    if (multiple) {\n      if (item.parent) {\n        const childrenCount = item.parent.children.length;\n        const selectedCount = item.parent.children.filter(x => x.selected).length;\n        item.parent.selected = childrenCount === selectedCount;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, true);\n        this._removeChildren(item);\n        if (groupAsModel && this._activeChildren(item)) {\n          this._selected = [...this._selected.filter(x => x.parent !== item), item];\n        } else {\n          this._selected = [...this._selected, ...item.children.filter(x => !x.disabled)];\n        }\n      }\n    }\n  }\n  unselect(item, multiple) {\n    this._selected = this._selected.filter(x => x !== item);\n    item.selected = false;\n    if (multiple) {\n      if (item.parent && item.parent.selected) {\n        const children = item.parent.children;\n        this._removeParent(item.parent);\n        this._removeChildren(item.parent);\n        this._selected.push(...children.filter(x => x !== item && !x.disabled));\n        item.parent.selected = false;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, false);\n        this._removeChildren(item);\n      }\n    }\n  }\n  clear(keepDisabled) {\n    this._selected = keepDisabled ? this._selected.filter(x => x.disabled) : [];\n  }\n  _setChildrenSelectedState(children, selected) {\n    for (const child of children) {\n      if (child.disabled) {\n        continue;\n      }\n      child.selected = selected;\n    }\n  }\n  _removeChildren(parent) {\n    this._selected = [...this._selected.filter(x => x.parent !== parent), ...parent.children.filter(x => x.parent === parent && x.disabled && x.selected)];\n  }\n  _removeParent(parent) {\n    this._selected = this._selected.filter(x => x !== parent);\n  }\n  _activeChildren(item) {\n    return item.children.every(x => !x.disabled || x.selected);\n  }\n}\nclass NgSelectModule {\n  static {\n    this.ɵfac = function NgSelectModule_Factory(t) {\n      return new (t || NgSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgSelectModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: SELECTION_MODEL_FACTORY,\n        useValue: DefaultSelectionModelFactory\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgDropdownPanelComponent, NgOptionComponent, NgSelectComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgItemLabelDirective],\n      imports: [CommonModule],\n      exports: [NgSelectComponent, NgOptionComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective],\n      providers: [{\n        provide: SELECTION_MODEL_FACTORY,\n        useValue: DefaultSelectionModelFactory\n      }]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ng-select\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgFooterTemplateDirective, NgHeaderTemplateDirective, NgItemLabelDirective, NgLabelTemplateDirective, NgLoadingSpinnerTemplateDirective, NgLoadingTextTemplateDirective, NgMultiLabelTemplateDirective, NgNotFoundTemplateDirective, NgOptgroupTemplateDirective, NgOptionComponent, NgOptionTemplateDirective, NgSelectComponent, NgSelectConfig, NgSelectModule, NgTagTemplateDirective, NgTypeToSearchTemplateDirective, SELECTION_MODEL_FACTORY };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "Injectable", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Inject", "Output", "ViewChild", "InjectionToken", "forwardRef", "TemplateRef", "Attribute", "HostBinding", "ContentChild", "ContentChildren", "HostListener", "NgModule", "NG_VALUE_ACCESSOR", "takeUntil", "auditTime", "startWith", "tap", "debounceTime", "filter", "map", "animationFrameScheduler", "asapScheduler", "Subject", "fromEvent", "merge", "i3", "DOCUMENT", "CommonModule", "_c0", "_c1", "_c2", "_c3", "_c4", "a0", "searchTerm", "NgDropdownPanelComponent_div_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "headerTemplate", "ɵɵpureFunction1", "filterValue", "NgDropdownPanelComponent_div_8_Template", "footerTemplate", "_c5", "_c6", "_c7", "a1", "a2", "item", "clear", "label", "_c8", "items", "_c9", "a3", "item$", "index", "NgSelectComponent_ng_container_4_div_1_ng_template_1_Template", "_r2", "ɵɵgetCurrentView", "ɵɵlistener", "NgSelectComponent_ng_container_4_div_1_ng_template_1_Template_span_click_0_listener", "ɵɵrestoreView", "item_r3", "$implicit", "ctx_r3", "ɵɵresetView", "unselect", "ɵɵtext", "ɵɵelement", "escapeHTML", "NgSelectComponent_ng_container_4_div_1_ng_template_3_Template", "NgSelectComponent_ng_container_4_div_1_Template", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "defaultLabelTemplate_r5", "ɵɵreference", "ɵɵclassProp", "disabled", "labelTemplate", "ɵɵpureFunction3", "value", "clearItem", "NgSelectComponent_ng_container_4_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "selectedItems", "trackByOption", "NgSelectComponent_5_ng_template_0_Template", "NgSelectComponent_5_Template", "multiLabelTemplate", "ɵɵpureFunction2", "<PERSON><PERSON><PERSON><PERSON>", "NgSelectComponent_ng_container_9_ng_template_1_Template", "NgSelectComponent_ng_container_9_ng_template_3_Template", "NgSelectComponent_ng_container_9_Template", "defaultLoadingSpinnerTemplate_r7", "loadingSpinnerTemplate", "NgSelectComponent_span_10_Template", "ɵɵpropertyInterpolate", "clearAllText", "NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_1_Template", "item_r10", "NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_3_Template", "NgSelectComponent_ng_dropdown_panel_13_div_2_Template", "_r9", "NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_click_0_listener", "toggleItem", "NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_mouseover_0_listener", "onItemHover", "defaultOptionTemplate_r11", "selected", "children", "parent", "itemsList", "markedItem", "ɵɵattribute", "htmlId", "optgroupTemplate", "optionTemplate", "ɵɵpureFunction4", "NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_1_Template", "ɵɵtextInterpolate", "addTagText", "ɵɵtextInterpolate1", "NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_3_Template", "NgSelectComponent_ng_dropdown_panel_13_div_3_Template", "_r12", "NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_mouseover_0_listener", "unmarkItem", "NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_click_0_listener", "selectTag", "defaultTagTemplate_r13", "tagTemplate", "NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_1_Template", "notFoundText", "NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_3_Template", "NgSelectComponent_ng_dropdown_panel_13_ng_container_4_Template", "defaultNotFoundTemplate_r14", "notFoundTemplate", "NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_1_Template", "typeToSearchText", "NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_3_Template", "NgSelectComponent_ng_dropdown_panel_13_ng_container_5_Template", "defaultTypeToSearchTemplate_r15", "typeToSearchTemplate", "NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_1_Template", "loadingText", "NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_3_Template", "NgSelectComponent_ng_dropdown_panel_13_ng_container_6_Template", "defaultLoadingTextTemplate_r16", "loadingTextTemplate", "NgSelectComponent_ng_dropdown_panel_13_Template", "_r8", "NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_update_0_listener", "$event", "viewPortItems", "NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scroll_0_listener", "scroll", "emit", "NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scrollToEnd_0_listener", "scrollToEnd", "NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_outsideClick_0_listener", "close", "multiple", "virtualScroll", "bufferAmount", "appendTo", "dropdownPosition", "filteredItems", "classes", "dropdownId", "showAddTag", "showNoItemsFound", "showTypeToSearch", "loading", "length", "unescapedHTMLExp", "hasUnescapedHTMLExp", "RegExp", "source", "htmlEscapes", "test", "replace", "chr", "isDefined", "undefined", "isObject", "isPromise", "Promise", "isFunction", "Function", "NgItemLabelDirective", "constructor", "element", "escape", "ngOnChanges", "changes", "nativeElement", "innerHTML", "ngItemLabel", "ɵfac", "NgItemLabelDirective_Factory", "t", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "NgOptionTemplateDirective", "template", "NgOptionTemplateDirective_Factory", "NgOptgroupTemplateDirective", "NgOptgroupTemplateDirective_Factory", "NgLabelTemplateDirective", "NgLabelTemplateDirective_Factory", "NgMultiLabelTemplateDirective", "NgMultiLabelTemplateDirective_Factory", "NgHeaderTemplateDirective", "NgHeaderTemplateDirective_Factory", "NgFooterTemplateDirective", "NgFooterTemplateDirective_Factory", "NgNotFoundTemplateDirective", "NgNotFoundTemplateDirective_Factory", "NgTypeToSearchTemplateDirective", "NgTypeToSearchTemplateDirective_Factory", "NgLoadingTextTemplateDirective", "NgLoadingTextTemplateDirective_Factory", "NgTagTemplateDirective", "NgTagTemplateDirective_Factory", "NgLoadingSpinnerTemplateDirective", "NgLoadingSpinnerTemplateDirective_Factory", "newId", "val", "Math", "random", "toString", "diacritics", "stripSpecialChars", "text", "match", "a", "ItemsList", "_ngSelect", "_selectionModel", "_items", "_filteredItems", "_markedIndex", "markedIndex", "noItemsToSelect", "hideSelected", "maxItemsSelected", "maxSelectedItems", "lastSelectedItem", "i", "setItems", "mapItem", "groupBy", "_groups", "_groupBy", "_flatten", "Map", "set", "select", "clearSelected", "selectableGroupAsModel", "_hideSelected", "_showSelected", "findItem", "find<PERSON><PERSON>", "compareWith", "bindValue", "resolveNested", "bind<PERSON>abel", "find", "addItem", "option", "push", "keepDisabled", "for<PERSON>ach", "marked", "resetFilteredItems", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "term", "toLocaleLowerCase", "substr", "searchFn", "_defaultSearchFn", "key", "Array", "from", "keys", "matchedItems", "get", "searchItem", "last", "slice", "head", "x", "markNextItem", "_stepToItem", "markPreviousItem", "markItem", "indexOf", "markSele<PERSON><PERSON>rDefault", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkedIndex", "_getLastMarkedIndex", "findIndex", "split", "len", "$ngOptionLabel", "$ngOptionValue", "mapSelectedItems", "parentExists", "child", "sort", "b", "every", "search", "opt", "_getNextItemIndex", "steps", "selectedIndex", "max", "prop", "groups", "isArray", "isFnKey", "keyFn", "group", "isGroupByFn", "withoutGroup", "isObjectKey", "String", "selectableGroup", "groupKey", "groupValue", "KeyCode", "NgDropdownPanelService", "_dimensions", "itemHeight", "panelHeight", "itemsPerViewport", "dimensions", "calculateItems", "scrollPos", "itemsLength", "buffer", "d", "scrollHeight", "scrollTop", "indexByScrollTop", "end", "min", "ceil", "maxStartEnd", "maxStart", "start", "floor", "topPadding", "isNaN", "setDimensions", "getScrollTo", "itemTop", "lastScroll", "itemBottom", "top", "bottom", "NgDropdownPanelService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "CSS_POSITIONS", "SCROLL_SCHEDULER", "requestAnimationFrame", "NgDropdownPanelComponent", "_renderer", "_zone", "_panelService", "_elementRef", "_document", "position", "update", "outsideClick", "_destroy$", "_scrollToEndFired", "_updateScrollHeight", "_lastScrollPosition", "_dropdown", "currentPosition", "_currentPosition", "_itemsLength", "_onItemsLengthChanged", "_startOffset", "offset", "ngOnInit", "_select", "parentElement", "_virtualPadding", "paddingElementRef", "_scrollablePanel", "scrollElementRef", "_contentPanel", "contentElementRef", "_handleScroll", "_handleOutsideClick", "_appendDropdown", "_setupMousedownListener", "change", "_onItemsChange", "currentValue", "firstChange", "ngOnDestroy", "next", "complete", "unsubscribe", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "scrollTo", "startFromOption", "querySelector", "offsetTop", "clientHeight", "scrollToTag", "panel", "adjustPosition", "_updateYPosition", "_handleDropdownPosition", "_calculateCurrentPosition", "includes", "_updateDropdownClass", "style", "opacity", "REMOVE_CSS_CLASS", "removeClass", "ADD_CSS_CLASS", "addClass", "runOutsideAngular", "pipe", "subscribe", "e", "path", "<PERSON><PERSON><PERSON>", "target", "_onContentScrolled", "capture", "_checkToClose", "contains", "shadowRoot", "run", "_updateItemsRange", "_setVirtualHeight", "_updateItems", "resolve", "then", "_measureDimensions", "_renderItemsRange", "_fireScrollToEnd", "_updateVirtualHeight", "height", "range", "transform", "first", "optionHeight", "padding", "dropdownEl", "selectRect", "getBoundingClientRect", "document", "documentElement", "body", "window", "pageYOffset", "dropdownHeight", "_parent", "Error", "_updateXPosition", "append<PERSON><PERSON><PERSON>", "offsetLeft", "left", "width", "min<PERSON><PERSON><PERSON>", "delta", "offsetBottom", "event", "tagName", "preventDefault", "NgDropdownPanelComponent_Factory", "Renderer2", "NgZone", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "NgDropdownPanelComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outputs", "ngContentSelectors", "decls", "vars", "consts", "NgDropdownPanelComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "dependencies", "NgIf", "NgTemplateOutlet", "encapsulation", "changeDetection", "OnPush", "None", "decorators", "read", "static", "NgOptionComponent", "_disabled", "_isDisabled", "elementRef", "stateChange$", "textContent", "trim", "ngAfterViewChecked", "_<PERSON><PERSON><PERSON><PERSON>", "NgOptionComponent_Factory", "NgOptionComponent_Template", "NgSelectConfig", "disableVirtualScroll", "openOnEnter", "appearance", "NgSelectConfig_Factory", "providedIn", "ConsoleService", "warn", "message", "console", "ConsoleService_Factory", "SELECTION_MODEL_FACTORY", "NgSelectComponent", "_itemsAreUsed", "_compareWith", "fn", "clearSearchOnAdd", "_clearSearchOnAdd", "config", "closeOnSelect", "deselectOnClick", "_deselectOnClick", "readonly", "filtered", "searchable", "_isComposing", "single", "_editableSearchTerm", "editableSearchTerm", "autoFocus", "newSelectionModel", "_cd", "_console", "<PERSON><PERSON><PERSON><PERSON>", "selectOnTab", "trackByFn", "clearOnBackspace", "labelForId", "inputAttrs", "searchWhileComposing", "minT<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyDownFn", "_", "addTag", "clearable", "isOpen", "blurEvent", "focusEvent", "changeEvent", "openEvent", "closeEvent", "searchEvent", "clearEvent", "addEvent", "removeEvent", "useDefaultClass", "_defaultLabel", "_pressed<PERSON><PERSON>s", "_keyPress$", "_onChange", "_onTouched", "_mergeGlobalConfig", "hasValue", "currentPanelPosition", "dropdownPanel", "_handleKeyPresses", "_setInputAttributes", "_setItems", "_manualOpen", "ngAfterViewInit", "_setItemsFromNgOptions", "focus", "handleKeyDown", "keyCode", "which", "handleKeyCode", "clearButton", "handleKeyCodeClear", "handleKeyCodeInput", "ArrowDown", "_handleArrowDown", "ArrowUp", "_handleArrowUp", "Space", "_handleSpace", "Enter", "_handleEnter", "Tab", "_handleTab", "Esc", "Backspace", "_handleBackspace", "handleClearClick", "handleMousedown", "classList", "handleArrowClick", "focused", "open", "toggle", "_updateNgModel", "_clearSearch", "_onSelectionChanged", "clearModel", "writeValue", "_handleWriteValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "registerOnTouched", "setDisabledState", "state", "_isTypeahead", "detectChanges", "_setSearchTermFromItems", "searchInput", "blur", "tag", "_primitive", "handleTag", "catch", "showClear", "focusOnClear", "_validTerm", "toLowerCase", "some", "empty", "onCompositionStart", "onCompositionEnd", "typeahead", "onInputFocus", "add", "onInputBlur", "remove", "destroyed", "firstItem", "mapNgOptions", "options", "handleOptionChange", "changedOrDestroyed", "ngOptions", "_isValidWriteValue", "validateBinding", "JSON", "stringify", "ngModel", "isValObject", "isPrimitive", "letter", "join", "_scrollToMarked", "input", "attributes", "autocorrect", "autocapitalize", "autocomplete", "Object", "setAttribute", "model", "_changeSearch", "_scrollToTag", "_nextItemIsTag", "nextStep", "nextIndex", "observers", "placeholder", "NgSelectComponent_Factory", "ɵɵinjectAttribute", "ChangeDetectorRef", "contentQueries", "NgSelectComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "NgSelectComponent_Query", "hostVars", "hostBindings", "NgSelectComponent_HostBindings", "NgSelectComponent_keydown_HostBindingHandler", "tabIndex", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "NgSelectComponent_Template", "_r1", "NgSelectComponent_Template_div_mousedown_0_listener", "NgSelectComponent_Template_input_input_7_listener", "searchInput_r6", "NgSelectComponent_Template_input_compositionstart_7_listener", "NgSelectComponent_Template_input_compositionend_7_listener", "NgSelectComponent_Template_input_focus_7_listener", "NgSelectComponent_Template_input_blur_7_listener", "NgSelectComponent_Template_input_change_7_listener", "stopPropagation", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles", "providers", "descendants", "DefaultSelectionModelFactory", "DefaultSelectionModel", "_selected", "groupAsModel", "childrenCount", "selectedCount", "_setChildrenSelectedState", "_remove<PERSON><PERSON><PERSON>n", "_active<PERSON><PERSON><PERSON><PERSON>", "_removeParent", "NgSelectModule", "NgSelectModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "useValue", "imports", "declarations", "exports"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@ng-select/ng-select/fesm2022/ng-select-ng-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Output, ViewChild, InjectionToken, forwardRef, TemplateRef, Attribute, HostBinding, ContentChild, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil, auditTime, startWith, tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { animationFrameScheduler, asapScheduler, Subject, fromEvent, merge } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\n\nconst unescapedHTMLExp = /[&<>\"']/g;\nconst hasUnescapedHTMLExp = RegExp(unescapedHTMLExp.source);\nconst htmlEscapes = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '\\'': '&#39;'\n};\nfunction escapeHTML(value) {\n    return (value && hasUnescapedHTMLExp.test(value)) ?\n        value.replace(unescapedHTMLExp, chr => htmlEscapes[chr]) :\n        value;\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\nfunction isObject(value) {\n    return typeof value === 'object' && isDefined(value);\n}\nfunction isPromise(value) {\n    return value instanceof Promise;\n}\nfunction isFunction(value) {\n    return value instanceof Function;\n}\n\nclass NgItemLabelDirective {\n    constructor(element) {\n        this.element = element;\n        this.escape = true;\n    }\n    ngOnChanges(changes) {\n        this.element.nativeElement.innerHTML = this.escape ?\n            escapeHTML(this.ngItemLabel) :\n            this.ngItemLabel;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgItemLabelDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgItemLabelDirective, selector: \"[ngItemLabel]\", inputs: { ngItemLabel: \"ngItemLabel\", escape: \"escape\" }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgItemLabelDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ngItemLabel]' }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { ngItemLabel: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }] } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptionTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptionTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgOptionTemplateDirective, selector: \"[ng-option-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptionTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-option-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptgroupTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptgroupTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgOptgroupTemplateDirective, selector: \"[ng-optgroup-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptgroupTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-optgroup-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLabelTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLabelTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgLabelTemplateDirective, selector: \"[ng-label-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLabelTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-label-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgMultiLabelTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgMultiLabelTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgMultiLabelTemplateDirective, selector: \"[ng-multi-label-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgMultiLabelTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-multi-label-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgHeaderTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgHeaderTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgHeaderTemplateDirective, selector: \"[ng-header-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgHeaderTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-header-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgFooterTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgFooterTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgFooterTemplateDirective, selector: \"[ng-footer-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgFooterTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-footer-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgNotFoundTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgNotFoundTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgNotFoundTemplateDirective, selector: \"[ng-notfound-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgNotFoundTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-notfound-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTypeToSearchTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgTypeToSearchTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgTypeToSearchTemplateDirective, selector: \"[ng-typetosearch-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgTypeToSearchTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-typetosearch-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingTextTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLoadingTextTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgLoadingTextTemplateDirective, selector: \"[ng-loadingtext-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLoadingTextTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-loadingtext-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTagTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgTagTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgTagTemplateDirective, selector: \"[ng-tag-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgTagTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-tag-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingSpinnerTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLoadingSpinnerTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgLoadingSpinnerTemplateDirective, selector: \"[ng-loadingspinner-tmp]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgLoadingSpinnerTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-loadingspinner-tmp]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nfunction newId() {\n    // First character is an 'a', it's good practice to tag id to begin with a letter\n    return 'axxxxxxxxxxx'.replace(/[x]/g, () => {\n        // eslint-disable-next-line no-bitwise\n        const val = Math.random() * 16 | 0;\n        return val.toString(16);\n    });\n}\n\nconst diacritics = {\n    '\\u24B6': 'A',\n    '\\uFF21': 'A',\n    '\\u00C0': 'A',\n    '\\u00C1': 'A',\n    '\\u00C2': 'A',\n    '\\u1EA6': 'A',\n    '\\u1EA4': 'A',\n    '\\u1EAA': 'A',\n    '\\u1EA8': 'A',\n    '\\u00C3': 'A',\n    '\\u0100': 'A',\n    '\\u0102': 'A',\n    '\\u1EB0': 'A',\n    '\\u1EAE': 'A',\n    '\\u1EB4': 'A',\n    '\\u1EB2': 'A',\n    '\\u0226': 'A',\n    '\\u01E0': 'A',\n    '\\u00C4': 'A',\n    '\\u01DE': 'A',\n    '\\u1EA2': 'A',\n    '\\u00C5': 'A',\n    '\\u01FA': 'A',\n    '\\u01CD': 'A',\n    '\\u0200': 'A',\n    '\\u0202': 'A',\n    '\\u1EA0': 'A',\n    '\\u1EAC': 'A',\n    '\\u1EB6': 'A',\n    '\\u1E00': 'A',\n    '\\u0104': 'A',\n    '\\u023A': 'A',\n    '\\u2C6F': 'A',\n    '\\uA732': 'AA',\n    '\\u00C6': 'AE',\n    '\\u01FC': 'AE',\n    '\\u01E2': 'AE',\n    '\\uA734': 'AO',\n    '\\uA736': 'AU',\n    '\\uA738': 'AV',\n    '\\uA73A': 'AV',\n    '\\uA73C': 'AY',\n    '\\u24B7': 'B',\n    '\\uFF22': 'B',\n    '\\u1E02': 'B',\n    '\\u1E04': 'B',\n    '\\u1E06': 'B',\n    '\\u0243': 'B',\n    '\\u0182': 'B',\n    '\\u0181': 'B',\n    '\\u24B8': 'C',\n    '\\uFF23': 'C',\n    '\\u0106': 'C',\n    '\\u0108': 'C',\n    '\\u010A': 'C',\n    '\\u010C': 'C',\n    '\\u00C7': 'C',\n    '\\u1E08': 'C',\n    '\\u0187': 'C',\n    '\\u023B': 'C',\n    '\\uA73E': 'C',\n    '\\u24B9': 'D',\n    '\\uFF24': 'D',\n    '\\u1E0A': 'D',\n    '\\u010E': 'D',\n    '\\u1E0C': 'D',\n    '\\u1E10': 'D',\n    '\\u1E12': 'D',\n    '\\u1E0E': 'D',\n    '\\u0110': 'D',\n    '\\u018B': 'D',\n    '\\u018A': 'D',\n    '\\u0189': 'D',\n    '\\uA779': 'D',\n    '\\u01F1': 'DZ',\n    '\\u01C4': 'DZ',\n    '\\u01F2': 'Dz',\n    '\\u01C5': 'Dz',\n    '\\u24BA': 'E',\n    '\\uFF25': 'E',\n    '\\u00C8': 'E',\n    '\\u00C9': 'E',\n    '\\u00CA': 'E',\n    '\\u1EC0': 'E',\n    '\\u1EBE': 'E',\n    '\\u1EC4': 'E',\n    '\\u1EC2': 'E',\n    '\\u1EBC': 'E',\n    '\\u0112': 'E',\n    '\\u1E14': 'E',\n    '\\u1E16': 'E',\n    '\\u0114': 'E',\n    '\\u0116': 'E',\n    '\\u00CB': 'E',\n    '\\u1EBA': 'E',\n    '\\u011A': 'E',\n    '\\u0204': 'E',\n    '\\u0206': 'E',\n    '\\u1EB8': 'E',\n    '\\u1EC6': 'E',\n    '\\u0228': 'E',\n    '\\u1E1C': 'E',\n    '\\u0118': 'E',\n    '\\u1E18': 'E',\n    '\\u1E1A': 'E',\n    '\\u0190': 'E',\n    '\\u018E': 'E',\n    '\\u24BB': 'F',\n    '\\uFF26': 'F',\n    '\\u1E1E': 'F',\n    '\\u0191': 'F',\n    '\\uA77B': 'F',\n    '\\u24BC': 'G',\n    '\\uFF27': 'G',\n    '\\u01F4': 'G',\n    '\\u011C': 'G',\n    '\\u1E20': 'G',\n    '\\u011E': 'G',\n    '\\u0120': 'G',\n    '\\u01E6': 'G',\n    '\\u0122': 'G',\n    '\\u01E4': 'G',\n    '\\u0193': 'G',\n    '\\uA7A0': 'G',\n    '\\uA77D': 'G',\n    '\\uA77E': 'G',\n    '\\u24BD': 'H',\n    '\\uFF28': 'H',\n    '\\u0124': 'H',\n    '\\u1E22': 'H',\n    '\\u1E26': 'H',\n    '\\u021E': 'H',\n    '\\u1E24': 'H',\n    '\\u1E28': 'H',\n    '\\u1E2A': 'H',\n    '\\u0126': 'H',\n    '\\u2C67': 'H',\n    '\\u2C75': 'H',\n    '\\uA78D': 'H',\n    '\\u24BE': 'I',\n    '\\uFF29': 'I',\n    '\\u00CC': 'I',\n    '\\u00CD': 'I',\n    '\\u00CE': 'I',\n    '\\u0128': 'I',\n    '\\u012A': 'I',\n    '\\u012C': 'I',\n    '\\u0130': 'I',\n    '\\u00CF': 'I',\n    '\\u1E2E': 'I',\n    '\\u1EC8': 'I',\n    '\\u01CF': 'I',\n    '\\u0208': 'I',\n    '\\u020A': 'I',\n    '\\u1ECA': 'I',\n    '\\u012E': 'I',\n    '\\u1E2C': 'I',\n    '\\u0197': 'I',\n    '\\u24BF': 'J',\n    '\\uFF2A': 'J',\n    '\\u0134': 'J',\n    '\\u0248': 'J',\n    '\\u24C0': 'K',\n    '\\uFF2B': 'K',\n    '\\u1E30': 'K',\n    '\\u01E8': 'K',\n    '\\u1E32': 'K',\n    '\\u0136': 'K',\n    '\\u1E34': 'K',\n    '\\u0198': 'K',\n    '\\u2C69': 'K',\n    '\\uA740': 'K',\n    '\\uA742': 'K',\n    '\\uA744': 'K',\n    '\\uA7A2': 'K',\n    '\\u24C1': 'L',\n    '\\uFF2C': 'L',\n    '\\u013F': 'L',\n    '\\u0139': 'L',\n    '\\u013D': 'L',\n    '\\u1E36': 'L',\n    '\\u1E38': 'L',\n    '\\u013B': 'L',\n    '\\u1E3C': 'L',\n    '\\u1E3A': 'L',\n    '\\u0141': 'L',\n    '\\u023D': 'L',\n    '\\u2C62': 'L',\n    '\\u2C60': 'L',\n    '\\uA748': 'L',\n    '\\uA746': 'L',\n    '\\uA780': 'L',\n    '\\u01C7': 'LJ',\n    '\\u01C8': 'Lj',\n    '\\u24C2': 'M',\n    '\\uFF2D': 'M',\n    '\\u1E3E': 'M',\n    '\\u1E40': 'M',\n    '\\u1E42': 'M',\n    '\\u2C6E': 'M',\n    '\\u019C': 'M',\n    '\\u24C3': 'N',\n    '\\uFF2E': 'N',\n    '\\u01F8': 'N',\n    '\\u0143': 'N',\n    '\\u00D1': 'N',\n    '\\u1E44': 'N',\n    '\\u0147': 'N',\n    '\\u1E46': 'N',\n    '\\u0145': 'N',\n    '\\u1E4A': 'N',\n    '\\u1E48': 'N',\n    '\\u0220': 'N',\n    '\\u019D': 'N',\n    '\\uA790': 'N',\n    '\\uA7A4': 'N',\n    '\\u01CA': 'NJ',\n    '\\u01CB': 'Nj',\n    '\\u24C4': 'O',\n    '\\uFF2F': 'O',\n    '\\u00D2': 'O',\n    '\\u00D3': 'O',\n    '\\u00D4': 'O',\n    '\\u1ED2': 'O',\n    '\\u1ED0': 'O',\n    '\\u1ED6': 'O',\n    '\\u1ED4': 'O',\n    '\\u00D5': 'O',\n    '\\u1E4C': 'O',\n    '\\u022C': 'O',\n    '\\u1E4E': 'O',\n    '\\u014C': 'O',\n    '\\u1E50': 'O',\n    '\\u1E52': 'O',\n    '\\u014E': 'O',\n    '\\u022E': 'O',\n    '\\u0230': 'O',\n    '\\u00D6': 'O',\n    '\\u022A': 'O',\n    '\\u1ECE': 'O',\n    '\\u0150': 'O',\n    '\\u01D1': 'O',\n    '\\u020C': 'O',\n    '\\u020E': 'O',\n    '\\u01A0': 'O',\n    '\\u1EDC': 'O',\n    '\\u1EDA': 'O',\n    '\\u1EE0': 'O',\n    '\\u1EDE': 'O',\n    '\\u1EE2': 'O',\n    '\\u1ECC': 'O',\n    '\\u1ED8': 'O',\n    '\\u01EA': 'O',\n    '\\u01EC': 'O',\n    '\\u00D8': 'O',\n    '\\u01FE': 'O',\n    '\\u0186': 'O',\n    '\\u019F': 'O',\n    '\\uA74A': 'O',\n    '\\uA74C': 'O',\n    '\\u01A2': 'OI',\n    '\\uA74E': 'OO',\n    '\\u0222': 'OU',\n    '\\u24C5': 'P',\n    '\\uFF30': 'P',\n    '\\u1E54': 'P',\n    '\\u1E56': 'P',\n    '\\u01A4': 'P',\n    '\\u2C63': 'P',\n    '\\uA750': 'P',\n    '\\uA752': 'P',\n    '\\uA754': 'P',\n    '\\u24C6': 'Q',\n    '\\uFF31': 'Q',\n    '\\uA756': 'Q',\n    '\\uA758': 'Q',\n    '\\u024A': 'Q',\n    '\\u24C7': 'R',\n    '\\uFF32': 'R',\n    '\\u0154': 'R',\n    '\\u1E58': 'R',\n    '\\u0158': 'R',\n    '\\u0210': 'R',\n    '\\u0212': 'R',\n    '\\u1E5A': 'R',\n    '\\u1E5C': 'R',\n    '\\u0156': 'R',\n    '\\u1E5E': 'R',\n    '\\u024C': 'R',\n    '\\u2C64': 'R',\n    '\\uA75A': 'R',\n    '\\uA7A6': 'R',\n    '\\uA782': 'R',\n    '\\u24C8': 'S',\n    '\\uFF33': 'S',\n    '\\u1E9E': 'S',\n    '\\u015A': 'S',\n    '\\u1E64': 'S',\n    '\\u015C': 'S',\n    '\\u1E60': 'S',\n    '\\u0160': 'S',\n    '\\u1E66': 'S',\n    '\\u1E62': 'S',\n    '\\u1E68': 'S',\n    '\\u0218': 'S',\n    '\\u015E': 'S',\n    '\\u2C7E': 'S',\n    '\\uA7A8': 'S',\n    '\\uA784': 'S',\n    '\\u24C9': 'T',\n    '\\uFF34': 'T',\n    '\\u1E6A': 'T',\n    '\\u0164': 'T',\n    '\\u1E6C': 'T',\n    '\\u021A': 'T',\n    '\\u0162': 'T',\n    '\\u1E70': 'T',\n    '\\u1E6E': 'T',\n    '\\u0166': 'T',\n    '\\u01AC': 'T',\n    '\\u01AE': 'T',\n    '\\u023E': 'T',\n    '\\uA786': 'T',\n    '\\uA728': 'TZ',\n    '\\u24CA': 'U',\n    '\\uFF35': 'U',\n    '\\u00D9': 'U',\n    '\\u00DA': 'U',\n    '\\u00DB': 'U',\n    '\\u0168': 'U',\n    '\\u1E78': 'U',\n    '\\u016A': 'U',\n    '\\u1E7A': 'U',\n    '\\u016C': 'U',\n    '\\u00DC': 'U',\n    '\\u01DB': 'U',\n    '\\u01D7': 'U',\n    '\\u01D5': 'U',\n    '\\u01D9': 'U',\n    '\\u1EE6': 'U',\n    '\\u016E': 'U',\n    '\\u0170': 'U',\n    '\\u01D3': 'U',\n    '\\u0214': 'U',\n    '\\u0216': 'U',\n    '\\u01AF': 'U',\n    '\\u1EEA': 'U',\n    '\\u1EE8': 'U',\n    '\\u1EEE': 'U',\n    '\\u1EEC': 'U',\n    '\\u1EF0': 'U',\n    '\\u1EE4': 'U',\n    '\\u1E72': 'U',\n    '\\u0172': 'U',\n    '\\u1E76': 'U',\n    '\\u1E74': 'U',\n    '\\u0244': 'U',\n    '\\u24CB': 'V',\n    '\\uFF36': 'V',\n    '\\u1E7C': 'V',\n    '\\u1E7E': 'V',\n    '\\u01B2': 'V',\n    '\\uA75E': 'V',\n    '\\u0245': 'V',\n    '\\uA760': 'VY',\n    '\\u24CC': 'W',\n    '\\uFF37': 'W',\n    '\\u1E80': 'W',\n    '\\u1E82': 'W',\n    '\\u0174': 'W',\n    '\\u1E86': 'W',\n    '\\u1E84': 'W',\n    '\\u1E88': 'W',\n    '\\u2C72': 'W',\n    '\\u24CD': 'X',\n    '\\uFF38': 'X',\n    '\\u1E8A': 'X',\n    '\\u1E8C': 'X',\n    '\\u24CE': 'Y',\n    '\\uFF39': 'Y',\n    '\\u1EF2': 'Y',\n    '\\u00DD': 'Y',\n    '\\u0176': 'Y',\n    '\\u1EF8': 'Y',\n    '\\u0232': 'Y',\n    '\\u1E8E': 'Y',\n    '\\u0178': 'Y',\n    '\\u1EF6': 'Y',\n    '\\u1EF4': 'Y',\n    '\\u01B3': 'Y',\n    '\\u024E': 'Y',\n    '\\u1EFE': 'Y',\n    '\\u24CF': 'Z',\n    '\\uFF3A': 'Z',\n    '\\u0179': 'Z',\n    '\\u1E90': 'Z',\n    '\\u017B': 'Z',\n    '\\u017D': 'Z',\n    '\\u1E92': 'Z',\n    '\\u1E94': 'Z',\n    '\\u01B5': 'Z',\n    '\\u0224': 'Z',\n    '\\u2C7F': 'Z',\n    '\\u2C6B': 'Z',\n    '\\uA762': 'Z',\n    '\\u24D0': 'a',\n    '\\uFF41': 'a',\n    '\\u1E9A': 'a',\n    '\\u00E0': 'a',\n    '\\u00E1': 'a',\n    '\\u00E2': 'a',\n    '\\u1EA7': 'a',\n    '\\u1EA5': 'a',\n    '\\u1EAB': 'a',\n    '\\u1EA9': 'a',\n    '\\u00E3': 'a',\n    '\\u0101': 'a',\n    '\\u0103': 'a',\n    '\\u1EB1': 'a',\n    '\\u1EAF': 'a',\n    '\\u1EB5': 'a',\n    '\\u1EB3': 'a',\n    '\\u0227': 'a',\n    '\\u01E1': 'a',\n    '\\u00E4': 'a',\n    '\\u01DF': 'a',\n    '\\u1EA3': 'a',\n    '\\u00E5': 'a',\n    '\\u01FB': 'a',\n    '\\u01CE': 'a',\n    '\\u0201': 'a',\n    '\\u0203': 'a',\n    '\\u1EA1': 'a',\n    '\\u1EAD': 'a',\n    '\\u1EB7': 'a',\n    '\\u1E01': 'a',\n    '\\u0105': 'a',\n    '\\u2C65': 'a',\n    '\\u0250': 'a',\n    '\\uA733': 'aa',\n    '\\u00E6': 'ae',\n    '\\u01FD': 'ae',\n    '\\u01E3': 'ae',\n    '\\uA735': 'ao',\n    '\\uA737': 'au',\n    '\\uA739': 'av',\n    '\\uA73B': 'av',\n    '\\uA73D': 'ay',\n    '\\u24D1': 'b',\n    '\\uFF42': 'b',\n    '\\u1E03': 'b',\n    '\\u1E05': 'b',\n    '\\u1E07': 'b',\n    '\\u0180': 'b',\n    '\\u0183': 'b',\n    '\\u0253': 'b',\n    '\\u24D2': 'c',\n    '\\uFF43': 'c',\n    '\\u0107': 'c',\n    '\\u0109': 'c',\n    '\\u010B': 'c',\n    '\\u010D': 'c',\n    '\\u00E7': 'c',\n    '\\u1E09': 'c',\n    '\\u0188': 'c',\n    '\\u023C': 'c',\n    '\\uA73F': 'c',\n    '\\u2184': 'c',\n    '\\u24D3': 'd',\n    '\\uFF44': 'd',\n    '\\u1E0B': 'd',\n    '\\u010F': 'd',\n    '\\u1E0D': 'd',\n    '\\u1E11': 'd',\n    '\\u1E13': 'd',\n    '\\u1E0F': 'd',\n    '\\u0111': 'd',\n    '\\u018C': 'd',\n    '\\u0256': 'd',\n    '\\u0257': 'd',\n    '\\uA77A': 'd',\n    '\\u01F3': 'dz',\n    '\\u01C6': 'dz',\n    '\\u24D4': 'e',\n    '\\uFF45': 'e',\n    '\\u00E8': 'e',\n    '\\u00E9': 'e',\n    '\\u00EA': 'e',\n    '\\u1EC1': 'e',\n    '\\u1EBF': 'e',\n    '\\u1EC5': 'e',\n    '\\u1EC3': 'e',\n    '\\u1EBD': 'e',\n    '\\u0113': 'e',\n    '\\u1E15': 'e',\n    '\\u1E17': 'e',\n    '\\u0115': 'e',\n    '\\u0117': 'e',\n    '\\u00EB': 'e',\n    '\\u1EBB': 'e',\n    '\\u011B': 'e',\n    '\\u0205': 'e',\n    '\\u0207': 'e',\n    '\\u1EB9': 'e',\n    '\\u1EC7': 'e',\n    '\\u0229': 'e',\n    '\\u1E1D': 'e',\n    '\\u0119': 'e',\n    '\\u1E19': 'e',\n    '\\u1E1B': 'e',\n    '\\u0247': 'e',\n    '\\u025B': 'e',\n    '\\u01DD': 'e',\n    '\\u24D5': 'f',\n    '\\uFF46': 'f',\n    '\\u1E1F': 'f',\n    '\\u0192': 'f',\n    '\\uA77C': 'f',\n    '\\u24D6': 'g',\n    '\\uFF47': 'g',\n    '\\u01F5': 'g',\n    '\\u011D': 'g',\n    '\\u1E21': 'g',\n    '\\u011F': 'g',\n    '\\u0121': 'g',\n    '\\u01E7': 'g',\n    '\\u0123': 'g',\n    '\\u01E5': 'g',\n    '\\u0260': 'g',\n    '\\uA7A1': 'g',\n    '\\u1D79': 'g',\n    '\\uA77F': 'g',\n    '\\u24D7': 'h',\n    '\\uFF48': 'h',\n    '\\u0125': 'h',\n    '\\u1E23': 'h',\n    '\\u1E27': 'h',\n    '\\u021F': 'h',\n    '\\u1E25': 'h',\n    '\\u1E29': 'h',\n    '\\u1E2B': 'h',\n    '\\u1E96': 'h',\n    '\\u0127': 'h',\n    '\\u2C68': 'h',\n    '\\u2C76': 'h',\n    '\\u0265': 'h',\n    '\\u0195': 'hv',\n    '\\u24D8': 'i',\n    '\\uFF49': 'i',\n    '\\u00EC': 'i',\n    '\\u00ED': 'i',\n    '\\u00EE': 'i',\n    '\\u0129': 'i',\n    '\\u012B': 'i',\n    '\\u012D': 'i',\n    '\\u00EF': 'i',\n    '\\u1E2F': 'i',\n    '\\u1EC9': 'i',\n    '\\u01D0': 'i',\n    '\\u0209': 'i',\n    '\\u020B': 'i',\n    '\\u1ECB': 'i',\n    '\\u012F': 'i',\n    '\\u1E2D': 'i',\n    '\\u0268': 'i',\n    '\\u0131': 'i',\n    '\\u24D9': 'j',\n    '\\uFF4A': 'j',\n    '\\u0135': 'j',\n    '\\u01F0': 'j',\n    '\\u0249': 'j',\n    '\\u24DA': 'k',\n    '\\uFF4B': 'k',\n    '\\u1E31': 'k',\n    '\\u01E9': 'k',\n    '\\u1E33': 'k',\n    '\\u0137': 'k',\n    '\\u1E35': 'k',\n    '\\u0199': 'k',\n    '\\u2C6A': 'k',\n    '\\uA741': 'k',\n    '\\uA743': 'k',\n    '\\uA745': 'k',\n    '\\uA7A3': 'k',\n    '\\u24DB': 'l',\n    '\\uFF4C': 'l',\n    '\\u0140': 'l',\n    '\\u013A': 'l',\n    '\\u013E': 'l',\n    '\\u1E37': 'l',\n    '\\u1E39': 'l',\n    '\\u013C': 'l',\n    '\\u1E3D': 'l',\n    '\\u1E3B': 'l',\n    '\\u017F': 'l',\n    '\\u0142': 'l',\n    '\\u019A': 'l',\n    '\\u026B': 'l',\n    '\\u2C61': 'l',\n    '\\uA749': 'l',\n    '\\uA781': 'l',\n    '\\uA747': 'l',\n    '\\u01C9': 'lj',\n    '\\u24DC': 'm',\n    '\\uFF4D': 'm',\n    '\\u1E3F': 'm',\n    '\\u1E41': 'm',\n    '\\u1E43': 'm',\n    '\\u0271': 'm',\n    '\\u026F': 'm',\n    '\\u24DD': 'n',\n    '\\uFF4E': 'n',\n    '\\u01F9': 'n',\n    '\\u0144': 'n',\n    '\\u00F1': 'n',\n    '\\u1E45': 'n',\n    '\\u0148': 'n',\n    '\\u1E47': 'n',\n    '\\u0146': 'n',\n    '\\u1E4B': 'n',\n    '\\u1E49': 'n',\n    '\\u019E': 'n',\n    '\\u0272': 'n',\n    '\\u0149': 'n',\n    '\\uA791': 'n',\n    '\\uA7A5': 'n',\n    '\\u01CC': 'nj',\n    '\\u24DE': 'o',\n    '\\uFF4F': 'o',\n    '\\u00F2': 'o',\n    '\\u00F3': 'o',\n    '\\u00F4': 'o',\n    '\\u1ED3': 'o',\n    '\\u1ED1': 'o',\n    '\\u1ED7': 'o',\n    '\\u1ED5': 'o',\n    '\\u00F5': 'o',\n    '\\u1E4D': 'o',\n    '\\u022D': 'o',\n    '\\u1E4F': 'o',\n    '\\u014D': 'o',\n    '\\u1E51': 'o',\n    '\\u1E53': 'o',\n    '\\u014F': 'o',\n    '\\u022F': 'o',\n    '\\u0231': 'o',\n    '\\u00F6': 'o',\n    '\\u022B': 'o',\n    '\\u1ECF': 'o',\n    '\\u0151': 'o',\n    '\\u01D2': 'o',\n    '\\u020D': 'o',\n    '\\u020F': 'o',\n    '\\u01A1': 'o',\n    '\\u1EDD': 'o',\n    '\\u1EDB': 'o',\n    '\\u1EE1': 'o',\n    '\\u1EDF': 'o',\n    '\\u1EE3': 'o',\n    '\\u1ECD': 'o',\n    '\\u1ED9': 'o',\n    '\\u01EB': 'o',\n    '\\u01ED': 'o',\n    '\\u00F8': 'o',\n    '\\u01FF': 'o',\n    '\\u0254': 'o',\n    '\\uA74B': 'o',\n    '\\uA74D': 'o',\n    '\\u0275': 'o',\n    '\\u01A3': 'oi',\n    '\\u0223': 'ou',\n    '\\uA74F': 'oo',\n    '\\u24DF': 'p',\n    '\\uFF50': 'p',\n    '\\u1E55': 'p',\n    '\\u1E57': 'p',\n    '\\u01A5': 'p',\n    '\\u1D7D': 'p',\n    '\\uA751': 'p',\n    '\\uA753': 'p',\n    '\\uA755': 'p',\n    '\\u24E0': 'q',\n    '\\uFF51': 'q',\n    '\\u024B': 'q',\n    '\\uA757': 'q',\n    '\\uA759': 'q',\n    '\\u24E1': 'r',\n    '\\uFF52': 'r',\n    '\\u0155': 'r',\n    '\\u1E59': 'r',\n    '\\u0159': 'r',\n    '\\u0211': 'r',\n    '\\u0213': 'r',\n    '\\u1E5B': 'r',\n    '\\u1E5D': 'r',\n    '\\u0157': 'r',\n    '\\u1E5F': 'r',\n    '\\u024D': 'r',\n    '\\u027D': 'r',\n    '\\uA75B': 'r',\n    '\\uA7A7': 'r',\n    '\\uA783': 'r',\n    '\\u24E2': 's',\n    '\\uFF53': 's',\n    '\\u00DF': 's',\n    '\\u015B': 's',\n    '\\u1E65': 's',\n    '\\u015D': 's',\n    '\\u1E61': 's',\n    '\\u0161': 's',\n    '\\u1E67': 's',\n    '\\u1E63': 's',\n    '\\u1E69': 's',\n    '\\u0219': 's',\n    '\\u015F': 's',\n    '\\u023F': 's',\n    '\\uA7A9': 's',\n    '\\uA785': 's',\n    '\\u1E9B': 's',\n    '\\u24E3': 't',\n    '\\uFF54': 't',\n    '\\u1E6B': 't',\n    '\\u1E97': 't',\n    '\\u0165': 't',\n    '\\u1E6D': 't',\n    '\\u021B': 't',\n    '\\u0163': 't',\n    '\\u1E71': 't',\n    '\\u1E6F': 't',\n    '\\u0167': 't',\n    '\\u01AD': 't',\n    '\\u0288': 't',\n    '\\u2C66': 't',\n    '\\uA787': 't',\n    '\\uA729': 'tz',\n    '\\u24E4': 'u',\n    '\\uFF55': 'u',\n    '\\u00F9': 'u',\n    '\\u00FA': 'u',\n    '\\u00FB': 'u',\n    '\\u0169': 'u',\n    '\\u1E79': 'u',\n    '\\u016B': 'u',\n    '\\u1E7B': 'u',\n    '\\u016D': 'u',\n    '\\u00FC': 'u',\n    '\\u01DC': 'u',\n    '\\u01D8': 'u',\n    '\\u01D6': 'u',\n    '\\u01DA': 'u',\n    '\\u1EE7': 'u',\n    '\\u016F': 'u',\n    '\\u0171': 'u',\n    '\\u01D4': 'u',\n    '\\u0215': 'u',\n    '\\u0217': 'u',\n    '\\u01B0': 'u',\n    '\\u1EEB': 'u',\n    '\\u1EE9': 'u',\n    '\\u1EEF': 'u',\n    '\\u1EED': 'u',\n    '\\u1EF1': 'u',\n    '\\u1EE5': 'u',\n    '\\u1E73': 'u',\n    '\\u0173': 'u',\n    '\\u1E77': 'u',\n    '\\u1E75': 'u',\n    '\\u0289': 'u',\n    '\\u24E5': 'v',\n    '\\uFF56': 'v',\n    '\\u1E7D': 'v',\n    '\\u1E7F': 'v',\n    '\\u028B': 'v',\n    '\\uA75F': 'v',\n    '\\u028C': 'v',\n    '\\uA761': 'vy',\n    '\\u24E6': 'w',\n    '\\uFF57': 'w',\n    '\\u1E81': 'w',\n    '\\u1E83': 'w',\n    '\\u0175': 'w',\n    '\\u1E87': 'w',\n    '\\u1E85': 'w',\n    '\\u1E98': 'w',\n    '\\u1E89': 'w',\n    '\\u2C73': 'w',\n    '\\u24E7': 'x',\n    '\\uFF58': 'x',\n    '\\u1E8B': 'x',\n    '\\u1E8D': 'x',\n    '\\u24E8': 'y',\n    '\\uFF59': 'y',\n    '\\u1EF3': 'y',\n    '\\u00FD': 'y',\n    '\\u0177': 'y',\n    '\\u1EF9': 'y',\n    '\\u0233': 'y',\n    '\\u1E8F': 'y',\n    '\\u00FF': 'y',\n    '\\u1EF7': 'y',\n    '\\u1E99': 'y',\n    '\\u1EF5': 'y',\n    '\\u01B4': 'y',\n    '\\u024F': 'y',\n    '\\u1EFF': 'y',\n    '\\u24E9': 'z',\n    '\\uFF5A': 'z',\n    '\\u017A': 'z',\n    '\\u1E91': 'z',\n    '\\u017C': 'z',\n    '\\u017E': 'z',\n    '\\u1E93': 'z',\n    '\\u1E95': 'z',\n    '\\u01B6': 'z',\n    '\\u0225': 'z',\n    '\\u0240': 'z',\n    '\\u2C6C': 'z',\n    '\\uA763': 'z',\n    '\\u0386': '\\u0391',\n    '\\u0388': '\\u0395',\n    '\\u0389': '\\u0397',\n    '\\u038A': '\\u0399',\n    '\\u03AA': '\\u0399',\n    '\\u038C': '\\u039F',\n    '\\u038E': '\\u03A5',\n    '\\u03AB': '\\u03A5',\n    '\\u038F': '\\u03A9',\n    '\\u03AC': '\\u03B1',\n    '\\u03AD': '\\u03B5',\n    '\\u03AE': '\\u03B7',\n    '\\u03AF': '\\u03B9',\n    '\\u03CA': '\\u03B9',\n    '\\u0390': '\\u03B9',\n    '\\u03CC': '\\u03BF',\n    '\\u03CD': '\\u03C5',\n    '\\u03CB': '\\u03C5',\n    '\\u03B0': '\\u03C5',\n    '\\u03C9': '\\u03C9',\n    '\\u03C2': '\\u03C3'\n};\nfunction stripSpecialChars(text) {\n    const match = (a) => diacritics[a] || a;\n    return text.replace(/[^\\u0000-\\u007E]/g, match);\n}\n\nclass ItemsList {\n    constructor(_ngSelect, _selectionModel) {\n        this._ngSelect = _ngSelect;\n        this._selectionModel = _selectionModel;\n        this._items = [];\n        this._filteredItems = [];\n        this._markedIndex = -1;\n    }\n    get items() {\n        return this._items;\n    }\n    get filteredItems() {\n        return this._filteredItems;\n    }\n    get markedIndex() {\n        return this._markedIndex;\n    }\n    get selectedItems() {\n        return this._selectionModel.value;\n    }\n    get markedItem() {\n        return this._filteredItems[this._markedIndex];\n    }\n    get noItemsToSelect() {\n        return this._ngSelect.hideSelected && this._items.length === this.selectedItems.length;\n    }\n    get maxItemsSelected() {\n        return this._ngSelect.multiple && this._ngSelect.maxSelectedItems <= this.selectedItems.length;\n    }\n    get lastSelectedItem() {\n        let i = this.selectedItems.length - 1;\n        for (; i >= 0; i--) {\n            const item = this.selectedItems[i];\n            if (!item.disabled) {\n                return item;\n            }\n        }\n        return null;\n    }\n    setItems(items) {\n        this._items = items.map((item, index) => this.mapItem(item, index));\n        if (this._ngSelect.groupBy) {\n            this._groups = this._groupBy(this._items, this._ngSelect.groupBy);\n            this._items = this._flatten(this._groups);\n        }\n        else {\n            this._groups = new Map();\n            this._groups.set(undefined, this._items);\n        }\n        this._filteredItems = [...this._items];\n    }\n    select(item) {\n        if (item.selected || this.maxItemsSelected) {\n            return;\n        }\n        const multiple = this._ngSelect.multiple;\n        if (!multiple) {\n            this.clearSelected();\n        }\n        this._selectionModel.select(item, multiple, this._ngSelect.selectableGroupAsModel);\n        if (this._ngSelect.hideSelected) {\n            this._hideSelected(item);\n        }\n    }\n    unselect(item) {\n        if (!item.selected) {\n            return;\n        }\n        this._selectionModel.unselect(item, this._ngSelect.multiple);\n        if (this._ngSelect.hideSelected && isDefined(item.index) && this._ngSelect.multiple) {\n            this._showSelected(item);\n        }\n    }\n    findItem(value) {\n        let findBy;\n        if (this._ngSelect.compareWith) {\n            findBy = item => this._ngSelect.compareWith(item.value, value);\n        }\n        else if (this._ngSelect.bindValue) {\n            findBy = item => !item.children && this.resolveNested(item.value, this._ngSelect.bindValue) === value;\n        }\n        else {\n            findBy = item => item.value === value ||\n                !item.children && item.label && item.label === this.resolveNested(value, this._ngSelect.bindLabel);\n        }\n        return this._items.find(item => findBy(item));\n    }\n    addItem(item) {\n        const option = this.mapItem(item, this._items.length);\n        this._items.push(option);\n        this._filteredItems.push(option);\n        return option;\n    }\n    clearSelected(keepDisabled = false) {\n        this._selectionModel.clear(keepDisabled);\n        this._items.forEach(item => {\n            item.selected = keepDisabled && item.selected && item.disabled;\n            item.marked = false;\n        });\n        if (this._ngSelect.hideSelected) {\n            this.resetFilteredItems();\n        }\n    }\n    findByLabel(term) {\n        term = stripSpecialChars(term).toLocaleLowerCase();\n        return this.filteredItems.find(item => {\n            const label = stripSpecialChars(item.label).toLocaleLowerCase();\n            return label.substr(0, term.length) === term;\n        });\n    }\n    filter(term) {\n        if (!term) {\n            this.resetFilteredItems();\n            return;\n        }\n        this._filteredItems = [];\n        term = this._ngSelect.searchFn ? term : stripSpecialChars(term).toLocaleLowerCase();\n        const match = this._ngSelect.searchFn || this._defaultSearchFn;\n        const hideSelected = this._ngSelect.hideSelected;\n        for (const key of Array.from(this._groups.keys())) {\n            const matchedItems = [];\n            for (const item of this._groups.get(key)) {\n                if (hideSelected && (item.parent && item.parent.selected || item.selected)) {\n                    continue;\n                }\n                const searchItem = this._ngSelect.searchFn ? item.value : item;\n                if (match(term, searchItem)) {\n                    matchedItems.push(item);\n                }\n            }\n            if (matchedItems.length > 0) {\n                const [last] = matchedItems.slice(-1);\n                if (last.parent) {\n                    const head = this._items.find(x => x === last.parent);\n                    this._filteredItems.push(head);\n                }\n                this._filteredItems.push(...matchedItems);\n            }\n        }\n    }\n    resetFilteredItems() {\n        if (this._filteredItems.length === this._items.length) {\n            return;\n        }\n        if (this._ngSelect.hideSelected && this.selectedItems.length > 0) {\n            this._filteredItems = this._items.filter(x => !x.selected);\n        }\n        else {\n            this._filteredItems = this._items;\n        }\n    }\n    unmarkItem() {\n        this._markedIndex = -1;\n    }\n    markNextItem() {\n        this._stepToItem(+1);\n    }\n    markPreviousItem() {\n        this._stepToItem(-1);\n    }\n    markItem(item) {\n        this._markedIndex = this._filteredItems.indexOf(item);\n    }\n    markSelectedOrDefault(markDefault) {\n        if (this._filteredItems.length === 0) {\n            return;\n        }\n        const lastMarkedIndex = this._getLastMarkedIndex();\n        if (lastMarkedIndex > -1) {\n            this._markedIndex = lastMarkedIndex;\n        }\n        else {\n            this._markedIndex = markDefault ? this.filteredItems.findIndex(x => !x.disabled) : -1;\n        }\n    }\n    resolveNested(option, key) {\n        if (!isObject(option)) {\n            return option;\n        }\n        if (key.indexOf('.') === -1) {\n            return option[key];\n        }\n        else {\n            const keys = key.split('.');\n            let value = option;\n            for (let i = 0, len = keys.length; i < len; ++i) {\n                if (value == null) {\n                    return null;\n                }\n                value = value[keys[i]];\n            }\n            return value;\n        }\n    }\n    mapItem(item, index) {\n        const label = isDefined(item.$ngOptionLabel) ? item.$ngOptionLabel : this.resolveNested(item, this._ngSelect.bindLabel);\n        const value = isDefined(item.$ngOptionValue) ? item.$ngOptionValue : item;\n        return {\n            index,\n            label: isDefined(label) ? label.toString() : '',\n            value,\n            disabled: item.disabled,\n            htmlId: `${this._ngSelect.dropdownId}-${index}`,\n        };\n    }\n    mapSelectedItems() {\n        const multiple = this._ngSelect.multiple;\n        for (const selected of this.selectedItems) {\n            const value = this._ngSelect.bindValue ? this.resolveNested(selected.value, this._ngSelect.bindValue) : selected.value;\n            const item = isDefined(value) ? this.findItem(value) : null;\n            this._selectionModel.unselect(selected, multiple);\n            this._selectionModel.select(item || selected, multiple, this._ngSelect.selectableGroupAsModel);\n        }\n        if (this._ngSelect.hideSelected) {\n            this._filteredItems = this.filteredItems.filter(x => this.selectedItems.indexOf(x) === -1);\n        }\n    }\n    _showSelected(item) {\n        this._filteredItems.push(item);\n        if (item.parent) {\n            const parent = item.parent;\n            const parentExists = this._filteredItems.find(x => x === parent);\n            if (!parentExists) {\n                this._filteredItems.push(parent);\n            }\n        }\n        else if (item.children) {\n            for (const child of item.children) {\n                child.selected = false;\n                this._filteredItems.push(child);\n            }\n        }\n        this._filteredItems = [...this._filteredItems.sort((a, b) => (a.index - b.index))];\n    }\n    _hideSelected(item) {\n        this._filteredItems = this._filteredItems.filter(x => x !== item);\n        if (item.parent) {\n            const children = item.parent.children;\n            if (children.every(x => x.selected)) {\n                this._filteredItems = this._filteredItems.filter(x => x !== item.parent);\n            }\n        }\n        else if (item.children) {\n            this._filteredItems = this.filteredItems.filter(x => x.parent !== item);\n        }\n    }\n    _defaultSearchFn(search, opt) {\n        const label = stripSpecialChars(opt.label).toLocaleLowerCase();\n        return label.indexOf(search) > -1;\n    }\n    _getNextItemIndex(steps) {\n        if (steps > 0) {\n            return (this._markedIndex >= this._filteredItems.length - 1) ? 0 : (this._markedIndex + 1);\n        }\n        return (this._markedIndex <= 0) ? (this._filteredItems.length - 1) : (this._markedIndex - 1);\n    }\n    _stepToItem(steps) {\n        if (this._filteredItems.length === 0 || this._filteredItems.every(x => x.disabled)) {\n            return;\n        }\n        this._markedIndex = this._getNextItemIndex(steps);\n        if (this.markedItem.disabled) {\n            this._stepToItem(steps);\n        }\n    }\n    _getLastMarkedIndex() {\n        if (this._ngSelect.hideSelected) {\n            return -1;\n        }\n        if (this._markedIndex > -1 && this.markedItem === undefined) {\n            return -1;\n        }\n        const selectedIndex = this._filteredItems.indexOf(this.lastSelectedItem);\n        if (this.lastSelectedItem && selectedIndex < 0) {\n            return -1;\n        }\n        return Math.max(this.markedIndex, selectedIndex);\n    }\n    _groupBy(items, prop) {\n        const groups = new Map();\n        if (items.length === 0) {\n            return groups;\n        }\n        // Check if items are already grouped by given key.\n        if (Array.isArray(items[0].value[prop])) {\n            for (const item of items) {\n                const children = (item.value[prop] || []).map((x, index) => this.mapItem(x, index));\n                groups.set(item, children);\n            }\n            return groups;\n        }\n        const isFnKey = isFunction(this._ngSelect.groupBy);\n        const keyFn = (item) => {\n            const key = isFnKey ? prop(item.value) : item.value[prop];\n            return isDefined(key) ? key : undefined;\n        };\n        // Group items by key.\n        for (const item of items) {\n            const key = keyFn(item);\n            const group = groups.get(key);\n            if (group) {\n                group.push(item);\n            }\n            else {\n                groups.set(key, [item]);\n            }\n        }\n        return groups;\n    }\n    _flatten(groups) {\n        const isGroupByFn = isFunction(this._ngSelect.groupBy);\n        const items = [];\n        for (const key of Array.from(groups.keys())) {\n            let i = items.length;\n            if (key === undefined) {\n                const withoutGroup = groups.get(undefined) || [];\n                items.push(...withoutGroup.map(x => {\n                    x.index = i++;\n                    return x;\n                }));\n                continue;\n            }\n            const isObjectKey = isObject(key);\n            const parent = {\n                label: isObjectKey ? '' : String(key),\n                children: undefined,\n                parent: null,\n                index: i++,\n                disabled: !this._ngSelect.selectableGroup,\n                htmlId: newId(),\n            };\n            const groupKey = isGroupByFn ? this._ngSelect.bindLabel : this._ngSelect.groupBy;\n            const groupValue = this._ngSelect.groupValue || (() => {\n                if (isObjectKey) {\n                    return key.value;\n                }\n                return { [groupKey]: key };\n            });\n            const children = groups.get(key).map(x => {\n                x.parent = parent;\n                x.children = undefined;\n                x.index = i++;\n                return x;\n            });\n            parent.children = children;\n            parent.value = groupValue(key, children.map(x => x.value));\n            items.push(parent);\n            items.push(...children);\n        }\n        return items;\n    }\n}\n\nvar KeyCode;\n(function (KeyCode) {\n    KeyCode[KeyCode[\"Tab\"] = 9] = \"Tab\";\n    KeyCode[KeyCode[\"Enter\"] = 13] = \"Enter\";\n    KeyCode[KeyCode[\"Esc\"] = 27] = \"Esc\";\n    KeyCode[KeyCode[\"Space\"] = 32] = \"Space\";\n    KeyCode[KeyCode[\"ArrowUp\"] = 38] = \"ArrowUp\";\n    KeyCode[KeyCode[\"ArrowDown\"] = 40] = \"ArrowDown\";\n    KeyCode[KeyCode[\"Backspace\"] = 8] = \"Backspace\";\n})(KeyCode || (KeyCode = {}));\n\nclass NgDropdownPanelService {\n    constructor() {\n        this._dimensions = {\n            itemHeight: 0,\n            panelHeight: 0,\n            itemsPerViewport: 0\n        };\n    }\n    get dimensions() {\n        return this._dimensions;\n    }\n    calculateItems(scrollPos, itemsLength, buffer) {\n        const d = this._dimensions;\n        const scrollHeight = d.itemHeight * itemsLength;\n        const scrollTop = Math.max(0, scrollPos);\n        const indexByScrollTop = scrollTop / scrollHeight * itemsLength;\n        let end = Math.min(itemsLength, Math.ceil(indexByScrollTop) + (d.itemsPerViewport + 1));\n        const maxStartEnd = end;\n        const maxStart = Math.max(0, maxStartEnd - d.itemsPerViewport);\n        let start = Math.min(maxStart, Math.floor(indexByScrollTop));\n        let topPadding = d.itemHeight * Math.ceil(start) - (d.itemHeight * Math.min(start, buffer));\n        topPadding = !isNaN(topPadding) ? topPadding : 0;\n        start = !isNaN(start) ? start : -1;\n        end = !isNaN(end) ? end : -1;\n        start -= buffer;\n        start = Math.max(0, start);\n        end += buffer;\n        end = Math.min(itemsLength, end);\n        return {\n            topPadding,\n            scrollHeight,\n            start,\n            end\n        };\n    }\n    setDimensions(itemHeight, panelHeight) {\n        const itemsPerViewport = Math.max(1, Math.floor(panelHeight / itemHeight));\n        this._dimensions = {\n            itemHeight,\n            panelHeight,\n            itemsPerViewport\n        };\n    }\n    getScrollTo(itemTop, itemHeight, lastScroll) {\n        const { panelHeight } = this.dimensions;\n        const itemBottom = itemTop + itemHeight;\n        const top = lastScroll;\n        const bottom = top + panelHeight;\n        if (panelHeight >= itemBottom && lastScroll === itemTop) {\n            return null;\n        }\n        if (itemBottom > bottom) {\n            return top + itemBottom - bottom;\n        }\n        else if (itemTop <= top) {\n            return itemTop;\n        }\n        return null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgDropdownPanelService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgDropdownPanelService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgDropdownPanelService, decorators: [{\n            type: Injectable\n        }] });\n\nconst CSS_POSITIONS = ['top', 'right', 'bottom', 'left'];\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nclass NgDropdownPanelComponent {\n    constructor(_renderer, _zone, _panelService, _elementRef, _document) {\n        this._renderer = _renderer;\n        this._zone = _zone;\n        this._panelService = _panelService;\n        this._document = _document;\n        this.items = [];\n        this.position = 'auto';\n        this.virtualScroll = false;\n        this.filterValue = null;\n        this.update = new EventEmitter();\n        this.scroll = new EventEmitter();\n        this.scrollToEnd = new EventEmitter();\n        this.outsideClick = new EventEmitter();\n        this._destroy$ = new Subject();\n        this._scrollToEndFired = false;\n        this._updateScrollHeight = false;\n        this._lastScrollPosition = 0;\n        this._dropdown = _elementRef.nativeElement;\n    }\n    get currentPosition() {\n        return this._currentPosition;\n    }\n    get itemsLength() {\n        return this._itemsLength;\n    }\n    set itemsLength(value) {\n        if (value !== this._itemsLength) {\n            this._itemsLength = value;\n            this._onItemsLengthChanged();\n        }\n    }\n    get _startOffset() {\n        if (this.markedItem) {\n            const { itemHeight, panelHeight } = this._panelService.dimensions;\n            const offset = this.markedItem.index * itemHeight;\n            return panelHeight > offset ? 0 : offset;\n        }\n        return 0;\n    }\n    ngOnInit() {\n        this._select = this._dropdown.parentElement;\n        this._virtualPadding = this.paddingElementRef.nativeElement;\n        this._scrollablePanel = this.scrollElementRef.nativeElement;\n        this._contentPanel = this.contentElementRef.nativeElement;\n        this._handleScroll();\n        this._handleOutsideClick();\n        this._appendDropdown();\n        this._setupMousedownListener();\n    }\n    ngOnChanges(changes) {\n        if (changes.items) {\n            const change = changes.items;\n            this._onItemsChange(change.currentValue, change.firstChange);\n        }\n    }\n    ngOnDestroy() {\n        this._destroy$.next();\n        this._destroy$.complete();\n        this._destroy$.unsubscribe();\n        if (this.appendTo) {\n            this._renderer.removeChild(this._dropdown.parentNode, this._dropdown);\n        }\n    }\n    scrollTo(option, startFromOption = false) {\n        if (!option) {\n            return;\n        }\n        const index = this.items.indexOf(option);\n        if (index < 0 || index >= this.itemsLength) {\n            return;\n        }\n        let scrollTo;\n        if (this.virtualScroll) {\n            const itemHeight = this._panelService.dimensions.itemHeight;\n            scrollTo = this._panelService.getScrollTo(index * itemHeight, itemHeight, this._lastScrollPosition);\n        }\n        else {\n            const item = this._dropdown.querySelector(`#${option.htmlId}`);\n            const lastScroll = startFromOption ? item.offsetTop : this._lastScrollPosition;\n            scrollTo = this._panelService.getScrollTo(item.offsetTop, item.clientHeight, lastScroll);\n        }\n        if (isDefined(scrollTo)) {\n            this._scrollablePanel.scrollTop = scrollTo;\n        }\n    }\n    scrollToTag() {\n        const panel = this._scrollablePanel;\n        panel.scrollTop = panel.scrollHeight - panel.clientHeight;\n    }\n    adjustPosition() {\n        this._updateYPosition();\n    }\n    _handleDropdownPosition() {\n        this._currentPosition = this._calculateCurrentPosition(this._dropdown);\n        if (CSS_POSITIONS.includes(this._currentPosition)) {\n            this._updateDropdownClass(this._currentPosition);\n        }\n        else {\n            this._updateDropdownClass('bottom');\n        }\n        if (this.appendTo) {\n            this._updateYPosition();\n        }\n        this._dropdown.style.opacity = '1';\n    }\n    _updateDropdownClass(currentPosition) {\n        CSS_POSITIONS.forEach((position) => {\n            const REMOVE_CSS_CLASS = `ng-select-${position}`;\n            this._renderer.removeClass(this._dropdown, REMOVE_CSS_CLASS);\n            this._renderer.removeClass(this._select, REMOVE_CSS_CLASS);\n        });\n        const ADD_CSS_CLASS = `ng-select-${currentPosition}`;\n        this._renderer.addClass(this._dropdown, ADD_CSS_CLASS);\n        this._renderer.addClass(this._select, ADD_CSS_CLASS);\n    }\n    _handleScroll() {\n        this._zone.runOutsideAngular(() => {\n            fromEvent(this.scrollElementRef.nativeElement, 'scroll')\n                .pipe(takeUntil(this._destroy$), auditTime(0, SCROLL_SCHEDULER))\n                .subscribe((e) => {\n                const path = e.path || (e.composedPath && e.composedPath());\n                const scrollTop = !path || path.length === 0 ? e.target.scrollTop : path[0].scrollTop;\n                this._onContentScrolled(scrollTop);\n            });\n        });\n    }\n    _handleOutsideClick() {\n        if (!this._document) {\n            return;\n        }\n        this._zone.runOutsideAngular(() => {\n            merge(fromEvent(this._document, 'touchstart', { capture: true }), fromEvent(this._document, 'mousedown', { capture: true })).pipe(takeUntil(this._destroy$))\n                .subscribe($event => this._checkToClose($event));\n        });\n    }\n    _checkToClose($event) {\n        if (this._select.contains($event.target) || this._dropdown.contains($event.target)) {\n            return;\n        }\n        const path = $event.path || ($event.composedPath && $event.composedPath());\n        if ($event.target && $event.target.shadowRoot && path && path[0] && this._select.contains(path[0])) {\n            return;\n        }\n        this._zone.run(() => this.outsideClick.emit());\n    }\n    _onItemsChange(items, firstChange) {\n        this.items = items || [];\n        this._scrollToEndFired = false;\n        this.itemsLength = items.length;\n        if (this.virtualScroll) {\n            this._updateItemsRange(firstChange);\n        }\n        else {\n            this._setVirtualHeight();\n            this._updateItems(firstChange);\n        }\n    }\n    _updateItems(firstChange) {\n        this.update.emit(this.items);\n        if (firstChange === false) {\n            return;\n        }\n        this._zone.runOutsideAngular(() => {\n            Promise.resolve().then(() => {\n                const panelHeight = this._scrollablePanel.clientHeight;\n                this._panelService.setDimensions(0, panelHeight);\n                this._handleDropdownPosition();\n                this.scrollTo(this.markedItem, firstChange);\n            });\n        });\n    }\n    _updateItemsRange(firstChange) {\n        this._zone.runOutsideAngular(() => {\n            this._measureDimensions().then(() => {\n                if (firstChange) {\n                    this._renderItemsRange(this._startOffset);\n                    this._handleDropdownPosition();\n                }\n                else {\n                    this._renderItemsRange();\n                }\n            });\n        });\n    }\n    _onContentScrolled(scrollTop) {\n        if (this.virtualScroll) {\n            this._renderItemsRange(scrollTop);\n        }\n        this._lastScrollPosition = scrollTop;\n        this._fireScrollToEnd(scrollTop);\n    }\n    _updateVirtualHeight(height) {\n        if (this._updateScrollHeight) {\n            this._virtualPadding.style.height = `${height}px`;\n            this._updateScrollHeight = false;\n        }\n    }\n    _setVirtualHeight() {\n        if (!this._virtualPadding) {\n            return;\n        }\n        this._virtualPadding.style.height = `0px`;\n    }\n    _onItemsLengthChanged() {\n        this._updateScrollHeight = true;\n    }\n    _renderItemsRange(scrollTop = null) {\n        if (scrollTop && this._lastScrollPosition === scrollTop) {\n            return;\n        }\n        scrollTop = scrollTop || this._scrollablePanel.scrollTop;\n        const range = this._panelService.calculateItems(scrollTop, this.itemsLength, this.bufferAmount);\n        this._updateVirtualHeight(range.scrollHeight);\n        this._contentPanel.style.transform = `translateY(${range.topPadding}px)`;\n        this._zone.run(() => {\n            this.update.emit(this.items.slice(range.start, range.end));\n            this.scroll.emit({ start: range.start, end: range.end });\n        });\n        if (isDefined(scrollTop) && this._lastScrollPosition === 0) {\n            this._scrollablePanel.scrollTop = scrollTop;\n            this._lastScrollPosition = scrollTop;\n        }\n    }\n    _measureDimensions() {\n        if (this._panelService.dimensions.itemHeight > 0 || this.itemsLength === 0) {\n            return Promise.resolve(this._panelService.dimensions);\n        }\n        const [first] = this.items;\n        this.update.emit([first]);\n        return Promise.resolve().then(() => {\n            const option = this._dropdown.querySelector(`#${first.htmlId}`);\n            const optionHeight = option.clientHeight;\n            this._virtualPadding.style.height = `${optionHeight * this.itemsLength}px`;\n            const panelHeight = this._scrollablePanel.clientHeight;\n            this._panelService.setDimensions(optionHeight, panelHeight);\n            return this._panelService.dimensions;\n        });\n    }\n    _fireScrollToEnd(scrollTop) {\n        if (this._scrollToEndFired || scrollTop === 0) {\n            return;\n        }\n        const padding = this.virtualScroll ?\n            this._virtualPadding :\n            this._contentPanel;\n        if (scrollTop + this._dropdown.clientHeight >= padding.clientHeight - 1) {\n            this._zone.run(() => this.scrollToEnd.emit());\n            this._scrollToEndFired = true;\n        }\n    }\n    _calculateCurrentPosition(dropdownEl) {\n        if (this.position !== 'auto') {\n            return this.position;\n        }\n        const selectRect = this._select.getBoundingClientRect();\n        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n        const offsetTop = selectRect.top + window.pageYOffset;\n        const height = selectRect.height;\n        const dropdownHeight = dropdownEl.getBoundingClientRect().height;\n        if (offsetTop + height + dropdownHeight > scrollTop + document.documentElement.clientHeight) {\n            return 'top';\n        }\n        else {\n            return 'bottom';\n        }\n    }\n    _appendDropdown() {\n        if (!this.appendTo) {\n            return;\n        }\n        this._parent = document.querySelector(this.appendTo);\n        if (!this._parent) {\n            throw new Error(`appendTo selector ${this.appendTo} did not found any parent element`);\n        }\n        this._updateXPosition();\n        this._parent.appendChild(this._dropdown);\n    }\n    _updateXPosition() {\n        const select = this._select.getBoundingClientRect();\n        const parent = this._parent.getBoundingClientRect();\n        const offsetLeft = select.left - parent.left;\n        this._dropdown.style.left = offsetLeft + 'px';\n        this._dropdown.style.width = select.width + 'px';\n        this._dropdown.style.minWidth = select.width + 'px';\n    }\n    _updateYPosition() {\n        const select = this._select.getBoundingClientRect();\n        const parent = this._parent.getBoundingClientRect();\n        const delta = select.height;\n        if (this._currentPosition === 'top') {\n            const offsetBottom = parent.bottom - select.bottom;\n            this._dropdown.style.bottom = offsetBottom + delta + 'px';\n            this._dropdown.style.top = 'auto';\n        }\n        else if (this._currentPosition === 'bottom') {\n            const offsetTop = select.top - parent.top;\n            this._dropdown.style.top = offsetTop + delta + 'px';\n            this._dropdown.style.bottom = 'auto';\n        }\n    }\n    _setupMousedownListener() {\n        this._zone.runOutsideAngular(() => {\n            fromEvent(this._dropdown, 'mousedown')\n                .pipe(takeUntil(this._destroy$))\n                .subscribe((event) => {\n                const target = event.target;\n                if (target.tagName === 'INPUT') {\n                    return;\n                }\n                event.preventDefault();\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgDropdownPanelComponent, deps: [{ token: i0.Renderer2 }, { token: i0.NgZone }, { token: NgDropdownPanelService }, { token: i0.ElementRef }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgDropdownPanelComponent, selector: \"ng-dropdown-panel\", inputs: { items: \"items\", markedItem: \"markedItem\", position: \"position\", appendTo: \"appendTo\", bufferAmount: \"bufferAmount\", virtualScroll: \"virtualScroll\", headerTemplate: \"headerTemplate\", footerTemplate: \"footerTemplate\", filterValue: \"filterValue\" }, outputs: { update: \"update\", scroll: \"scroll\", scrollToEnd: \"scrollToEnd\", outsideClick: \"outsideClick\" }, viewQueries: [{ propertyName: \"contentElementRef\", first: true, predicate: [\"content\"], descendants: true, read: ElementRef, static: true }, { propertyName: \"scrollElementRef\", first: true, predicate: [\"scroll\"], descendants: true, read: ElementRef, static: true }, { propertyName: \"paddingElementRef\", first: true, predicate: [\"padding\"], descendants: true, read: ElementRef, static: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll role=\"listbox\" class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgDropdownPanelComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'ng-dropdown-panel',\n                    template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll role=\"listbox\" class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i0.NgZone }, { type: NgDropdownPanelService }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { items: [{\n                type: Input\n            }], markedItem: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], bufferAmount: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], headerTemplate: [{\n                type: Input\n            }], footerTemplate: [{\n                type: Input\n            }], filterValue: [{\n                type: Input\n            }], update: [{\n                type: Output\n            }], scroll: [{\n                type: Output\n            }], scrollToEnd: [{\n                type: Output\n            }], outsideClick: [{\n                type: Output\n            }], contentElementRef: [{\n                type: ViewChild,\n                args: ['content', { read: ElementRef, static: true }]\n            }], scrollElementRef: [{\n                type: ViewChild,\n                args: ['scroll', { read: ElementRef, static: true }]\n            }], paddingElementRef: [{\n                type: ViewChild,\n                args: ['padding', { read: ElementRef, static: true }]\n            }] } });\n\nclass NgOptionComponent {\n    get disabled() { return this._disabled; }\n    set disabled(value) { this._disabled = this._isDisabled(value); }\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n        this.stateChange$ = new Subject();\n        this._disabled = false;\n    }\n    get label() {\n        return (this.elementRef.nativeElement.textContent || '').trim();\n    }\n    ngOnChanges(changes) {\n        if (changes.disabled) {\n            this.stateChange$.next({\n                value: this.value,\n                disabled: this._disabled\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        if (this.label !== this._previousLabel) {\n            this._previousLabel = this.label;\n            this.stateChange$.next({\n                value: this.value,\n                disabled: this._disabled,\n                label: this.elementRef.nativeElement.innerHTML\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.stateChange$.complete();\n    }\n    _isDisabled(value) {\n        return value != null && `${value}` !== 'false';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptionComponent, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgOptionComponent, selector: \"ng-option\", inputs: { value: \"value\", disabled: \"disabled\" }, usesOnChanges: true, ngImport: i0, template: `<ng-content></ng-content>`, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgOptionComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ng-option',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `<ng-content></ng-content>`\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { value: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\nclass NgSelectConfig {\n    constructor() {\n        this.notFoundText = 'No items found';\n        this.typeToSearchText = 'Type to search';\n        this.addTagText = 'Add item';\n        this.loadingText = 'Loading...';\n        this.clearAllText = 'Clear all';\n        this.disableVirtualScroll = true;\n        this.openOnEnter = true;\n        this.appearance = 'underline';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectConfig, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectConfig, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass ConsoleService {\n    warn(message) {\n        console.warn(message);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: ConsoleService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: ConsoleService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: ConsoleService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nconst SELECTION_MODEL_FACTORY = new InjectionToken('ng-select-selection-model');\nclass NgSelectComponent {\n    get items() { return this._items; }\n    ;\n    set items(value) {\n        if (value === null) {\n            value = [];\n        }\n        this._itemsAreUsed = true;\n        this._items = value;\n    }\n    ;\n    get compareWith() { return this._compareWith; }\n    set compareWith(fn) {\n        if (fn !== undefined && fn !== null && !isFunction(fn)) {\n            throw Error('`compareWith` must be a function.');\n        }\n        this._compareWith = fn;\n    }\n    get clearSearchOnAdd() {\n        if (isDefined(this._clearSearchOnAdd)) {\n            return this._clearSearchOnAdd;\n        }\n        else if (isDefined(this.config.clearSearchOnAdd)) {\n            return this.config.clearSearchOnAdd;\n        }\n        return this.closeOnSelect;\n    }\n    ;\n    set clearSearchOnAdd(value) {\n        this._clearSearchOnAdd = value;\n    }\n    ;\n    get deselectOnClick() {\n        if (isDefined(this._deselectOnClick)) {\n            return this._deselectOnClick;\n        }\n        else if (isDefined(this.config.deselectOnClick)) {\n            return this.config.deselectOnClick;\n        }\n        return this.multiple;\n    }\n    ;\n    set deselectOnClick(value) {\n        this._deselectOnClick = value;\n    }\n    ;\n    get disabled() { return this.readonly || this._disabled; }\n    ;\n    get filtered() { return (!!this.searchTerm && this.searchable || this._isComposing); }\n    ;\n    get single() { return !this.multiple; }\n    ;\n    get _editableSearchTerm() {\n        return this.editableSearchTerm && !this.multiple;\n    }\n    constructor(classes, autoFocus, config, newSelectionModel, _elementRef, _cd, _console) {\n        this.classes = classes;\n        this.autoFocus = autoFocus;\n        this.config = config;\n        this._cd = _cd;\n        this._console = _console;\n        this.markFirst = true;\n        this.dropdownPosition = 'auto';\n        this.loading = false;\n        this.closeOnSelect = true;\n        this.hideSelected = false;\n        this.selectOnTab = false;\n        this.bufferAmount = 4;\n        this.selectableGroup = false;\n        this.selectableGroupAsModel = true;\n        this.searchFn = null;\n        this.trackByFn = null;\n        this.clearOnBackspace = true;\n        this.labelForId = null;\n        this.inputAttrs = {};\n        this.readonly = false;\n        this.searchWhileComposing = true;\n        this.minTermLength = 0;\n        this.editableSearchTerm = false;\n        this.keyDownFn = (_) => true;\n        this.multiple = false;\n        this.addTag = false;\n        this.searchable = true;\n        this.clearable = true;\n        this.isOpen = false;\n        // output events\n        this.blurEvent = new EventEmitter();\n        this.focusEvent = new EventEmitter();\n        this.changeEvent = new EventEmitter();\n        this.openEvent = new EventEmitter();\n        this.closeEvent = new EventEmitter();\n        this.searchEvent = new EventEmitter();\n        this.clearEvent = new EventEmitter();\n        this.addEvent = new EventEmitter();\n        this.removeEvent = new EventEmitter();\n        this.scroll = new EventEmitter();\n        this.scrollToEnd = new EventEmitter();\n        this.useDefaultClass = true;\n        this.viewPortItems = [];\n        this.searchTerm = null;\n        this.dropdownId = newId();\n        this.escapeHTML = true;\n        this._items = [];\n        this._defaultLabel = 'label';\n        this._pressedKeys = [];\n        this._isComposing = false;\n        this._destroy$ = new Subject();\n        this._keyPress$ = new Subject();\n        this._onChange = (_) => { };\n        this._onTouched = () => { };\n        this.clearItem = (item) => {\n            const option = this.selectedItems.find(x => x.value === item);\n            this.unselect(option);\n        };\n        this.trackByOption = (_, item) => {\n            if (this.trackByFn) {\n                return this.trackByFn(item.value);\n            }\n            return item;\n        };\n        this._mergeGlobalConfig(config);\n        this.itemsList = new ItemsList(this, newSelectionModel());\n        this.element = _elementRef.nativeElement;\n    }\n    get selectedItems() {\n        return this.itemsList.selectedItems;\n    }\n    get selectedValues() {\n        return this.selectedItems.map(x => x.value);\n    }\n    get hasValue() {\n        return this.selectedItems.length > 0;\n    }\n    get currentPanelPosition() {\n        if (this.dropdownPanel) {\n            return this.dropdownPanel.currentPosition;\n        }\n        return undefined;\n    }\n    ngOnInit() {\n        this._handleKeyPresses();\n        this._setInputAttributes();\n    }\n    ngOnChanges(changes) {\n        if (changes.multiple) {\n            this.itemsList.clearSelected();\n        }\n        if (changes.items) {\n            this._setItems(changes.items.currentValue || []);\n        }\n        if (changes.isOpen) {\n            this._manualOpen = isDefined(changes.isOpen.currentValue);\n        }\n    }\n    ngAfterViewInit() {\n        if (!this._itemsAreUsed) {\n            this.escapeHTML = false;\n            this._setItemsFromNgOptions();\n        }\n        if (isDefined(this.autoFocus)) {\n            this.focus();\n        }\n    }\n    ngOnDestroy() {\n        this._destroy$.next();\n        this._destroy$.complete();\n    }\n    handleKeyDown($event) {\n        const keyCode = KeyCode[$event.which];\n        if (keyCode) {\n            if (this.keyDownFn($event) === false) {\n                return;\n            }\n            this.handleKeyCode($event);\n        }\n        else if ($event.key && $event.key.length === 1) {\n            this._keyPress$.next($event.key.toLocaleLowerCase());\n        }\n    }\n    handleKeyCode($event) {\n        const target = $event.target;\n        if (this.clearButton && this.clearButton.nativeElement === target) {\n            this.handleKeyCodeClear($event);\n        }\n        else {\n            this.handleKeyCodeInput($event);\n        }\n    }\n    handleKeyCodeInput($event) {\n        switch ($event.which) {\n            case KeyCode.ArrowDown:\n                this._handleArrowDown($event);\n                break;\n            case KeyCode.ArrowUp:\n                this._handleArrowUp($event);\n                break;\n            case KeyCode.Space:\n                this._handleSpace($event);\n                break;\n            case KeyCode.Enter:\n                this._handleEnter($event);\n                break;\n            case KeyCode.Tab:\n                this._handleTab($event);\n                break;\n            case KeyCode.Esc:\n                this.close();\n                $event.preventDefault();\n                break;\n            case KeyCode.Backspace:\n                this._handleBackspace();\n                break;\n        }\n    }\n    handleKeyCodeClear($event) {\n        switch ($event.which) {\n            case KeyCode.Enter:\n                this.handleClearClick();\n                $event.preventDefault();\n                break;\n        }\n    }\n    handleMousedown($event) {\n        const target = $event.target;\n        if (target.tagName !== 'INPUT') {\n            $event.preventDefault();\n        }\n        if (target.classList.contains('ng-clear-wrapper')) {\n            this.handleClearClick();\n            return;\n        }\n        if (target.classList.contains('ng-arrow-wrapper')) {\n            this.handleArrowClick();\n            return;\n        }\n        if (target.classList.contains('ng-value-icon')) {\n            return;\n        }\n        if (!this.focused) {\n            this.focus();\n        }\n        if (this.searchable) {\n            this.open();\n        }\n        else {\n            this.toggle();\n        }\n    }\n    handleArrowClick() {\n        if (this.isOpen) {\n            this.close();\n        }\n        else {\n            this.open();\n        }\n    }\n    handleClearClick() {\n        if (this.hasValue) {\n            this.itemsList.clearSelected(true);\n            this._updateNgModel();\n        }\n        this._clearSearch();\n        this.focus();\n        this.clearEvent.emit();\n        this._onSelectionChanged();\n    }\n    clearModel() {\n        if (!this.clearable) {\n            return;\n        }\n        this.itemsList.clearSelected();\n        this._updateNgModel();\n    }\n    writeValue(value) {\n        this.itemsList.clearSelected();\n        this._handleWriteValue(value);\n        this._cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    setDisabledState(state) {\n        this._disabled = state;\n        this._cd.markForCheck();\n    }\n    toggle() {\n        if (!this.isOpen) {\n            this.open();\n        }\n        else {\n            this.close();\n        }\n    }\n    open() {\n        if (this.disabled || this.isOpen || this._manualOpen) {\n            return;\n        }\n        if (!this._isTypeahead && !this.addTag && this.itemsList.noItemsToSelect) {\n            return;\n        }\n        this.isOpen = true;\n        this.itemsList.markSelectedOrDefault(this.markFirst);\n        this.openEvent.emit();\n        if (!this.searchTerm) {\n            this.focus();\n        }\n        this.detectChanges();\n    }\n    close() {\n        if (!this.isOpen || this._manualOpen) {\n            return;\n        }\n        this.isOpen = false;\n        this._isComposing = false;\n        if (!this._editableSearchTerm) {\n            this._clearSearch();\n        }\n        else {\n            this.itemsList.resetFilteredItems();\n        }\n        this.itemsList.unmarkItem();\n        this._onTouched();\n        this.closeEvent.emit();\n        this._cd.markForCheck();\n    }\n    toggleItem(item) {\n        if (!item || item.disabled || this.disabled) {\n            return;\n        }\n        if (this.deselectOnClick && item.selected) {\n            this.unselect(item);\n        }\n        else {\n            this.select(item);\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this._onSelectionChanged();\n    }\n    select(item) {\n        if (!item.selected) {\n            this.itemsList.select(item);\n            if (this.clearSearchOnAdd && !this._editableSearchTerm) {\n                this._clearSearch();\n            }\n            this._updateNgModel();\n            if (this.multiple) {\n                this.addEvent.emit(item.value);\n            }\n        }\n        if (this.closeOnSelect || this.itemsList.noItemsToSelect) {\n            this.close();\n        }\n    }\n    focus() {\n        this.searchInput.nativeElement.focus();\n    }\n    blur() {\n        this.searchInput.nativeElement.blur();\n    }\n    unselect(item) {\n        if (!item) {\n            return;\n        }\n        this.itemsList.unselect(item);\n        this.focus();\n        this._updateNgModel();\n        this.removeEvent.emit(item.value);\n    }\n    selectTag() {\n        let tag;\n        if (isFunction(this.addTag)) {\n            tag = this.addTag(this.searchTerm);\n        }\n        else {\n            tag = this._primitive ? this.searchTerm : { [this.bindLabel]: this.searchTerm };\n        }\n        const handleTag = (item) => this._isTypeahead || !this.isOpen ? this.itemsList.mapItem(item, null) : this.itemsList.addItem(item);\n        if (isPromise(tag)) {\n            tag.then(item => this.select(handleTag(item))).catch(() => { });\n        }\n        else if (tag) {\n            this.select(handleTag(tag));\n        }\n    }\n    showClear() {\n        return this.clearable && (this.hasValue || this.searchTerm) && !this.disabled;\n    }\n    focusOnClear() {\n        this.blur();\n        if (this.clearButton) {\n            this.clearButton.nativeElement.focus();\n        }\n    }\n    get showAddTag() {\n        if (!this._validTerm) {\n            return false;\n        }\n        const term = this.searchTerm.toLowerCase().trim();\n        return this.addTag &&\n            (!this.itemsList.filteredItems.some(x => x.label.toLowerCase() === term) &&\n                (!this.hideSelected && this.isOpen || !this.selectedItems.some(x => x.label.toLowerCase() === term))) &&\n            !this.loading;\n    }\n    showNoItemsFound() {\n        const empty = this.itemsList.filteredItems.length === 0;\n        return ((empty && !this._isTypeahead && !this.loading) ||\n            (empty && this._isTypeahead && this._validTerm && !this.loading)) &&\n            !this.showAddTag;\n    }\n    showTypeToSearch() {\n        const empty = this.itemsList.filteredItems.length === 0;\n        return empty && this._isTypeahead && !this._validTerm && !this.loading;\n    }\n    onCompositionStart() {\n        this._isComposing = true;\n    }\n    onCompositionEnd(term) {\n        this._isComposing = false;\n        if (this.searchWhileComposing) {\n            return;\n        }\n        this.filter(term);\n    }\n    filter(term) {\n        if (this._isComposing && !this.searchWhileComposing) {\n            return;\n        }\n        this.searchTerm = term;\n        if (this._isTypeahead && (this._validTerm || this.minTermLength === 0)) {\n            this.typeahead.next(term);\n        }\n        if (!this._isTypeahead) {\n            this.itemsList.filter(this.searchTerm);\n            if (this.isOpen) {\n                this.itemsList.markSelectedOrDefault(this.markFirst);\n            }\n        }\n        this.searchEvent.emit({ term, items: this.itemsList.filteredItems.map(x => x.value) });\n        this.open();\n    }\n    onInputFocus($event) {\n        if (this.focused) {\n            return;\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this.element.classList.add('ng-select-focused');\n        this.focusEvent.emit($event);\n        this.focused = true;\n    }\n    onInputBlur($event) {\n        this.element.classList.remove('ng-select-focused');\n        this.blurEvent.emit($event);\n        if (!this.isOpen && !this.disabled) {\n            this._onTouched();\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this.focused = false;\n    }\n    onItemHover(item) {\n        if (item.disabled) {\n            return;\n        }\n        this.itemsList.markItem(item);\n    }\n    detectChanges() {\n        if (!this._cd.destroyed) {\n            this._cd.detectChanges();\n        }\n    }\n    _setSearchTermFromItems() {\n        const selected = this.selectedItems && this.selectedItems[0];\n        this.searchTerm = (selected && selected.label) || null;\n    }\n    _setItems(items) {\n        const firstItem = items[0];\n        this.bindLabel = this.bindLabel || this._defaultLabel;\n        this._primitive = isDefined(firstItem) ? !isObject(firstItem) : this._primitive || this.bindLabel === this._defaultLabel;\n        this.itemsList.setItems(items);\n        if (items.length > 0 && this.hasValue) {\n            this.itemsList.mapSelectedItems();\n        }\n        if (this.isOpen && isDefined(this.searchTerm) && !this._isTypeahead) {\n            this.itemsList.filter(this.searchTerm);\n        }\n        if (this._isTypeahead || this.isOpen) {\n            this.itemsList.markSelectedOrDefault(this.markFirst);\n        }\n    }\n    _setItemsFromNgOptions() {\n        const mapNgOptions = (options) => {\n            this.items = options.map(option => ({\n                $ngOptionValue: option.value,\n                $ngOptionLabel: option.elementRef.nativeElement.innerHTML,\n                disabled: option.disabled\n            }));\n            this.itemsList.setItems(this.items);\n            if (this.hasValue) {\n                this.itemsList.mapSelectedItems();\n            }\n            this.detectChanges();\n        };\n        const handleOptionChange = () => {\n            const changedOrDestroyed = merge(this.ngOptions.changes, this._destroy$);\n            merge(...this.ngOptions.map(option => option.stateChange$))\n                .pipe(takeUntil(changedOrDestroyed))\n                .subscribe(option => {\n                const item = this.itemsList.findItem(option.value);\n                item.disabled = option.disabled;\n                item.label = option.label || item.label;\n                this._cd.detectChanges();\n            });\n        };\n        this.ngOptions.changes\n            .pipe(startWith(this.ngOptions), takeUntil(this._destroy$))\n            .subscribe(options => {\n            this.bindLabel = this._defaultLabel;\n            mapNgOptions(options);\n            handleOptionChange();\n        });\n    }\n    _isValidWriteValue(value) {\n        if (!isDefined(value) || (this.multiple && value === '') || Array.isArray(value) && value.length === 0) {\n            return false;\n        }\n        const validateBinding = (item) => {\n            if (!isDefined(this.compareWith) && isObject(item) && this.bindValue) {\n                this._console.warn(`Setting object(${JSON.stringify(item)}) as your model with bindValue is not allowed unless [compareWith] is used.`);\n                return false;\n            }\n            return true;\n        };\n        if (this.multiple) {\n            if (!Array.isArray(value)) {\n                this._console.warn('Multiple select ngModel should be array.');\n                return false;\n            }\n            return value.every(item => validateBinding(item));\n        }\n        else {\n            return validateBinding(value);\n        }\n    }\n    _handleWriteValue(ngModel) {\n        if (!this._isValidWriteValue(ngModel)) {\n            return;\n        }\n        const select = (val) => {\n            let item = this.itemsList.findItem(val);\n            if (item) {\n                this.itemsList.select(item);\n            }\n            else {\n                const isValObject = isObject(val);\n                const isPrimitive = !isValObject && !this.bindValue;\n                if ((isValObject || isPrimitive)) {\n                    this.itemsList.select(this.itemsList.mapItem(val, null));\n                }\n                else if (this.bindValue) {\n                    item = {\n                        [this.bindLabel]: null,\n                        [this.bindValue]: val\n                    };\n                    this.itemsList.select(this.itemsList.mapItem(item, null));\n                }\n            }\n        };\n        if (this.multiple) {\n            ngModel.forEach(item => select(item));\n        }\n        else {\n            select(ngModel);\n        }\n    }\n    _handleKeyPresses() {\n        if (this.searchable) {\n            return;\n        }\n        this._keyPress$\n            .pipe(takeUntil(this._destroy$), tap(letter => this._pressedKeys.push(letter)), debounceTime(200), filter(() => this._pressedKeys.length > 0), map(() => this._pressedKeys.join('')))\n            .subscribe(term => {\n            const item = this.itemsList.findByLabel(term);\n            if (item) {\n                if (this.isOpen) {\n                    this.itemsList.markItem(item);\n                    this._scrollToMarked();\n                    this._cd.markForCheck();\n                }\n                else {\n                    this.select(item);\n                }\n            }\n            this._pressedKeys = [];\n        });\n    }\n    _setInputAttributes() {\n        const input = this.searchInput.nativeElement;\n        const attributes = {\n            type: 'text',\n            autocorrect: 'off',\n            autocapitalize: 'off',\n            autocomplete: this.labelForId ? 'off' : this.dropdownId,\n            ...this.inputAttrs\n        };\n        for (const key of Object.keys(attributes)) {\n            input.setAttribute(key, attributes[key]);\n        }\n    }\n    _updateNgModel() {\n        const model = [];\n        for (const item of this.selectedItems) {\n            if (this.bindValue) {\n                let value = null;\n                if (item.children) {\n                    const groupKey = this.groupValue ? this.bindValue : this.groupBy;\n                    value = item.value[groupKey || this.groupBy];\n                }\n                else {\n                    value = this.itemsList.resolveNested(item.value, this.bindValue);\n                }\n                model.push(value);\n            }\n            else {\n                model.push(item.value);\n            }\n        }\n        const selected = this.selectedItems.map(x => x.value);\n        if (this.multiple) {\n            this._onChange(model);\n            this.changeEvent.emit(selected);\n        }\n        else {\n            this._onChange(isDefined(model[0]) ? model[0] : null);\n            this.changeEvent.emit(selected[0]);\n        }\n        this._cd.markForCheck();\n    }\n    _clearSearch() {\n        if (!this.searchTerm) {\n            return;\n        }\n        this._changeSearch(null);\n        this.itemsList.resetFilteredItems();\n    }\n    _changeSearch(searchTerm) {\n        this.searchTerm = searchTerm;\n        if (this._isTypeahead) {\n            this.typeahead.next(searchTerm);\n        }\n    }\n    _scrollToMarked() {\n        if (!this.isOpen || !this.dropdownPanel) {\n            return;\n        }\n        this.dropdownPanel.scrollTo(this.itemsList.markedItem);\n    }\n    _scrollToTag() {\n        if (!this.isOpen || !this.dropdownPanel) {\n            return;\n        }\n        this.dropdownPanel.scrollToTag();\n    }\n    _onSelectionChanged() {\n        if (this.isOpen && this.deselectOnClick && this.appendTo) {\n            // Make sure items are rendered.\n            this._cd.detectChanges();\n            this.dropdownPanel.adjustPosition();\n        }\n    }\n    _handleTab($event) {\n        if (this.isOpen === false) {\n            if (this.showClear()) {\n                this.focusOnClear();\n                $event.preventDefault();\n            }\n            else if (!this.addTag) {\n                return;\n            }\n        }\n        if (this.selectOnTab) {\n            if (this.itemsList.markedItem) {\n                this.toggleItem(this.itemsList.markedItem);\n                $event.preventDefault();\n            }\n            else if (this.showAddTag) {\n                this.selectTag();\n                $event.preventDefault();\n            }\n            else {\n                this.close();\n            }\n        }\n        else {\n            this.close();\n        }\n    }\n    _handleEnter($event) {\n        if (this.isOpen || this._manualOpen) {\n            if (this.itemsList.markedItem) {\n                this.toggleItem(this.itemsList.markedItem);\n            }\n            else if (this.showAddTag) {\n                this.selectTag();\n            }\n        }\n        else if (this.openOnEnter) {\n            this.open();\n        }\n        else {\n            return;\n        }\n        $event.preventDefault();\n    }\n    _handleSpace($event) {\n        if (this.isOpen || this._manualOpen) {\n            return;\n        }\n        this.open();\n        $event.preventDefault();\n    }\n    _handleArrowDown($event) {\n        if (this._nextItemIsTag(+1)) {\n            this.itemsList.unmarkItem();\n            this._scrollToTag();\n        }\n        else {\n            this.itemsList.markNextItem();\n            this._scrollToMarked();\n        }\n        this.open();\n        $event.preventDefault();\n    }\n    _handleArrowUp($event) {\n        if (!this.isOpen) {\n            return;\n        }\n        if (this._nextItemIsTag(-1)) {\n            this.itemsList.unmarkItem();\n            this._scrollToTag();\n        }\n        else {\n            this.itemsList.markPreviousItem();\n            this._scrollToMarked();\n        }\n        $event.preventDefault();\n    }\n    _nextItemIsTag(nextStep) {\n        const nextIndex = this.itemsList.markedIndex + nextStep;\n        return this.addTag && this.searchTerm\n            && this.itemsList.markedItem\n            && (nextIndex < 0 || nextIndex === this.itemsList.filteredItems.length);\n    }\n    _handleBackspace() {\n        if (this.searchTerm || !this.clearable || !this.clearOnBackspace || !this.hasValue) {\n            return;\n        }\n        if (this.multiple) {\n            this.unselect(this.itemsList.lastSelectedItem);\n        }\n        else {\n            this.clearModel();\n        }\n    }\n    get _isTypeahead() {\n        return this.typeahead && this.typeahead.observers.length > 0;\n    }\n    get _validTerm() {\n        const term = this.searchTerm && this.searchTerm.trim();\n        return term && term.length >= this.minTermLength;\n    }\n    _mergeGlobalConfig(config) {\n        this.placeholder = this.placeholder || config.placeholder;\n        this.notFoundText = this.notFoundText || config.notFoundText;\n        this.typeToSearchText = this.typeToSearchText || config.typeToSearchText;\n        this.addTagText = this.addTagText || config.addTagText;\n        this.loadingText = this.loadingText || config.loadingText;\n        this.clearAllText = this.clearAllText || config.clearAllText;\n        this.virtualScroll = isDefined(this.virtualScroll)\n            ? this.virtualScroll\n            : isDefined(config.disableVirtualScroll) ? !config.disableVirtualScroll : false;\n        this.openOnEnter = isDefined(this.openOnEnter) ? this.openOnEnter : config.openOnEnter;\n        this.appendTo = this.appendTo || config.appendTo;\n        this.bindValue = this.bindValue || config.bindValue;\n        this.bindLabel = this.bindLabel || config.bindLabel;\n        this.appearance = this.appearance || config.appearance;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectComponent, deps: [{ token: 'class', attribute: true }, { token: 'autofocus', attribute: true }, { token: NgSelectConfig }, { token: SELECTION_MODEL_FACTORY }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: ConsoleService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.0\", type: NgSelectComponent, selector: \"ng-select\", inputs: { bindLabel: \"bindLabel\", bindValue: \"bindValue\", markFirst: \"markFirst\", placeholder: \"placeholder\", notFoundText: \"notFoundText\", typeToSearchText: \"typeToSearchText\", addTagText: \"addTagText\", loadingText: \"loadingText\", clearAllText: \"clearAllText\", appearance: \"appearance\", dropdownPosition: \"dropdownPosition\", appendTo: \"appendTo\", loading: \"loading\", closeOnSelect: \"closeOnSelect\", hideSelected: \"hideSelected\", selectOnTab: \"selectOnTab\", openOnEnter: \"openOnEnter\", maxSelectedItems: \"maxSelectedItems\", groupBy: \"groupBy\", groupValue: \"groupValue\", bufferAmount: \"bufferAmount\", virtualScroll: \"virtualScroll\", selectableGroup: \"selectableGroup\", selectableGroupAsModel: \"selectableGroupAsModel\", searchFn: \"searchFn\", trackByFn: \"trackByFn\", clearOnBackspace: \"clearOnBackspace\", labelForId: \"labelForId\", inputAttrs: \"inputAttrs\", tabIndex: \"tabIndex\", readonly: \"readonly\", searchWhileComposing: \"searchWhileComposing\", minTermLength: \"minTermLength\", editableSearchTerm: \"editableSearchTerm\", keyDownFn: \"keyDownFn\", typeahead: \"typeahead\", multiple: \"multiple\", addTag: \"addTag\", searchable: \"searchable\", clearable: \"clearable\", isOpen: \"isOpen\", items: \"items\", compareWith: \"compareWith\", clearSearchOnAdd: \"clearSearchOnAdd\", deselectOnClick: \"deselectOnClick\" }, outputs: { blurEvent: \"blur\", focusEvent: \"focus\", changeEvent: \"change\", openEvent: \"open\", closeEvent: \"close\", searchEvent: \"search\", clearEvent: \"clear\", addEvent: \"add\", removeEvent: \"remove\", scroll: \"scroll\", scrollToEnd: \"scrollToEnd\" }, host: { listeners: { \"keydown\": \"handleKeyDown($event)\" }, properties: { \"class.ng-select-typeahead\": \"this.typeahead\", \"class.ng-select-multiple\": \"this.multiple\", \"class.ng-select-taggable\": \"this.addTag\", \"class.ng-select-searchable\": \"this.searchable\", \"class.ng-select-clearable\": \"this.clearable\", \"class.ng-select-opened\": \"this.isOpen\", \"class.ng-select\": \"this.useDefaultClass\", \"class.ng-select-disabled\": \"this.disabled\", \"class.ng-select-filtered\": \"this.filtered\", \"class.ng-select-single\": \"this.single\" } }, providers: [{\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => NgSelectComponent),\n                multi: true\n            }, NgDropdownPanelService], queries: [{ propertyName: \"optionTemplate\", first: true, predicate: NgOptionTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"optgroupTemplate\", first: true, predicate: NgOptgroupTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"labelTemplate\", first: true, predicate: NgLabelTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"multiLabelTemplate\", first: true, predicate: NgMultiLabelTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"headerTemplate\", first: true, predicate: NgHeaderTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"footerTemplate\", first: true, predicate: NgFooterTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"notFoundTemplate\", first: true, predicate: NgNotFoundTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"typeToSearchTemplate\", first: true, predicate: NgTypeToSearchTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"loadingTextTemplate\", first: true, predicate: NgLoadingTextTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"tagTemplate\", first: true, predicate: NgTagTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"loadingSpinnerTemplate\", first: true, predicate: NgLoadingSpinnerTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"ngOptions\", predicate: NgOptionComponent, descendants: true }], viewQueries: [{ propertyName: \"dropdownPanel\", first: true, predicate: i0.forwardRef(() => NgDropdownPanelComponent), descendants: true }, { propertyName: \"searchInput\", first: true, predicate: [\"searchInput\"], descendants: true, static: true }, { propertyName: \"clearButton\", first: true, predicate: [\"clearButton\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" tabindex=\\\"0\\\" title=\\\"{{clearAllText}}\\\" #clearButton>\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\", styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NgDropdownPanelComponent, selector: \"ng-dropdown-panel\", inputs: [\"items\", \"markedItem\", \"position\", \"appendTo\", \"bufferAmount\", \"virtualScroll\", \"headerTemplate\", \"footerTemplate\", \"filterValue\"], outputs: [\"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\"] }, { kind: \"directive\", type: NgItemLabelDirective, selector: \"[ngItemLabel]\", inputs: [\"ngItemLabel\", \"escape\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ng-select', providers: [{\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NgSelectComponent),\n                            multi: true\n                        }, NgDropdownPanelService], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" tabindex=\\\"0\\\" title=\\\"{{clearAllText}}\\\" #clearButton>\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\", styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['class']\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['autofocus']\n                }] }, { type: NgSelectConfig }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [SELECTION_MODEL_FACTORY]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: ConsoleService }], propDecorators: { bindLabel: [{\n                type: Input\n            }], bindValue: [{\n                type: Input\n            }], markFirst: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], notFoundText: [{\n                type: Input\n            }], typeToSearchText: [{\n                type: Input\n            }], addTagText: [{\n                type: Input\n            }], loadingText: [{\n                type: Input\n            }], clearAllText: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }], dropdownPosition: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], closeOnSelect: [{\n                type: Input\n            }], hideSelected: [{\n                type: Input\n            }], selectOnTab: [{\n                type: Input\n            }], openOnEnter: [{\n                type: Input\n            }], maxSelectedItems: [{\n                type: Input\n            }], groupBy: [{\n                type: Input\n            }], groupValue: [{\n                type: Input\n            }], bufferAmount: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], selectableGroup: [{\n                type: Input\n            }], selectableGroupAsModel: [{\n                type: Input\n            }], searchFn: [{\n                type: Input\n            }], trackByFn: [{\n                type: Input\n            }], clearOnBackspace: [{\n                type: Input\n            }], labelForId: [{\n                type: Input\n            }], inputAttrs: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], searchWhileComposing: [{\n                type: Input\n            }], minTermLength: [{\n                type: Input\n            }], editableSearchTerm: [{\n                type: Input\n            }], keyDownFn: [{\n                type: Input\n            }], typeahead: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-typeahead']\n            }], multiple: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-multiple']\n            }], addTag: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-taggable']\n            }], searchable: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-searchable']\n            }], clearable: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-clearable']\n            }], isOpen: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-opened']\n            }], items: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], clearSearchOnAdd: [{\n                type: Input\n            }], deselectOnClick: [{\n                type: Input\n            }], blurEvent: [{\n                type: Output,\n                args: ['blur']\n            }], focusEvent: [{\n                type: Output,\n                args: ['focus']\n            }], changeEvent: [{\n                type: Output,\n                args: ['change']\n            }], openEvent: [{\n                type: Output,\n                args: ['open']\n            }], closeEvent: [{\n                type: Output,\n                args: ['close']\n            }], searchEvent: [{\n                type: Output,\n                args: ['search']\n            }], clearEvent: [{\n                type: Output,\n                args: ['clear']\n            }], addEvent: [{\n                type: Output,\n                args: ['add']\n            }], removeEvent: [{\n                type: Output,\n                args: ['remove']\n            }], scroll: [{\n                type: Output,\n                args: ['scroll']\n            }], scrollToEnd: [{\n                type: Output,\n                args: ['scrollToEnd']\n            }], optionTemplate: [{\n                type: ContentChild,\n                args: [NgOptionTemplateDirective, { read: TemplateRef }]\n            }], optgroupTemplate: [{\n                type: ContentChild,\n                args: [NgOptgroupTemplateDirective, { read: TemplateRef }]\n            }], labelTemplate: [{\n                type: ContentChild,\n                args: [NgLabelTemplateDirective, { read: TemplateRef }]\n            }], multiLabelTemplate: [{\n                type: ContentChild,\n                args: [NgMultiLabelTemplateDirective, { read: TemplateRef }]\n            }], headerTemplate: [{\n                type: ContentChild,\n                args: [NgHeaderTemplateDirective, { read: TemplateRef }]\n            }], footerTemplate: [{\n                type: ContentChild,\n                args: [NgFooterTemplateDirective, { read: TemplateRef }]\n            }], notFoundTemplate: [{\n                type: ContentChild,\n                args: [NgNotFoundTemplateDirective, { read: TemplateRef }]\n            }], typeToSearchTemplate: [{\n                type: ContentChild,\n                args: [NgTypeToSearchTemplateDirective, { read: TemplateRef }]\n            }], loadingTextTemplate: [{\n                type: ContentChild,\n                args: [NgLoadingTextTemplateDirective, { read: TemplateRef }]\n            }], tagTemplate: [{\n                type: ContentChild,\n                args: [NgTagTemplateDirective, { read: TemplateRef }]\n            }], loadingSpinnerTemplate: [{\n                type: ContentChild,\n                args: [NgLoadingSpinnerTemplateDirective, { read: TemplateRef }]\n            }], dropdownPanel: [{\n                type: ViewChild,\n                args: [forwardRef(() => NgDropdownPanelComponent)]\n            }], searchInput: [{\n                type: ViewChild,\n                args: ['searchInput', { static: true }]\n            }], clearButton: [{\n                type: ViewChild,\n                args: ['clearButton']\n            }], ngOptions: [{\n                type: ContentChildren,\n                args: [NgOptionComponent, { descendants: true }]\n            }], useDefaultClass: [{\n                type: HostBinding,\n                args: ['class.ng-select']\n            }], disabled: [{\n                type: HostBinding,\n                args: ['class.ng-select-disabled']\n            }], filtered: [{\n                type: HostBinding,\n                args: ['class.ng-select-filtered']\n            }], single: [{\n                type: HostBinding,\n                args: ['class.ng-select-single']\n            }], handleKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nfunction DefaultSelectionModelFactory() {\n    return new DefaultSelectionModel();\n}\nclass DefaultSelectionModel {\n    constructor() {\n        this._selected = [];\n    }\n    get value() {\n        return this._selected;\n    }\n    select(item, multiple, groupAsModel) {\n        item.selected = true;\n        if (!item.children || (!multiple && groupAsModel)) {\n            this._selected.push(item);\n        }\n        if (multiple) {\n            if (item.parent) {\n                const childrenCount = item.parent.children.length;\n                const selectedCount = item.parent.children.filter(x => x.selected).length;\n                item.parent.selected = childrenCount === selectedCount;\n            }\n            else if (item.children) {\n                this._setChildrenSelectedState(item.children, true);\n                this._removeChildren(item);\n                if (groupAsModel && this._activeChildren(item)) {\n                    this._selected = [...this._selected.filter(x => x.parent !== item), item];\n                }\n                else {\n                    this._selected = [...this._selected, ...item.children.filter(x => !x.disabled)];\n                }\n            }\n        }\n    }\n    unselect(item, multiple) {\n        this._selected = this._selected.filter(x => x !== item);\n        item.selected = false;\n        if (multiple) {\n            if (item.parent && item.parent.selected) {\n                const children = item.parent.children;\n                this._removeParent(item.parent);\n                this._removeChildren(item.parent);\n                this._selected.push(...children.filter(x => x !== item && !x.disabled));\n                item.parent.selected = false;\n            }\n            else if (item.children) {\n                this._setChildrenSelectedState(item.children, false);\n                this._removeChildren(item);\n            }\n        }\n    }\n    clear(keepDisabled) {\n        this._selected = keepDisabled ? this._selected.filter(x => x.disabled) : [];\n    }\n    _setChildrenSelectedState(children, selected) {\n        for (const child of children) {\n            if (child.disabled) {\n                continue;\n            }\n            child.selected = selected;\n        }\n    }\n    _removeChildren(parent) {\n        this._selected = [\n            ...this._selected.filter(x => x.parent !== parent),\n            ...parent.children.filter(x => x.parent === parent && x.disabled && x.selected)\n        ];\n    }\n    _removeParent(parent) {\n        this._selected = this._selected.filter(x => x !== parent);\n    }\n    _activeChildren(item) {\n        return item.children.every(x => !x.disabled || x.selected);\n    }\n}\n\nclass NgSelectModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectModule, declarations: [NgDropdownPanelComponent,\n            NgOptionComponent,\n            NgSelectComponent,\n            NgOptgroupTemplateDirective,\n            NgOptionTemplateDirective,\n            NgLabelTemplateDirective,\n            NgMultiLabelTemplateDirective,\n            NgHeaderTemplateDirective,\n            NgFooterTemplateDirective,\n            NgNotFoundTemplateDirective,\n            NgTypeToSearchTemplateDirective,\n            NgLoadingTextTemplateDirective,\n            NgTagTemplateDirective,\n            NgLoadingSpinnerTemplateDirective,\n            NgItemLabelDirective], imports: [CommonModule], exports: [NgSelectComponent,\n            NgOptionComponent,\n            NgOptgroupTemplateDirective,\n            NgOptionTemplateDirective,\n            NgLabelTemplateDirective,\n            NgMultiLabelTemplateDirective,\n            NgHeaderTemplateDirective,\n            NgFooterTemplateDirective,\n            NgNotFoundTemplateDirective,\n            NgTypeToSearchTemplateDirective,\n            NgLoadingTextTemplateDirective,\n            NgTagTemplateDirective,\n            NgLoadingSpinnerTemplateDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectModule, providers: [\n            { provide: SELECTION_MODEL_FACTORY, useValue: DefaultSelectionModelFactory }\n        ], imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.0\", ngImport: i0, type: NgSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        NgDropdownPanelComponent,\n                        NgOptionComponent,\n                        NgSelectComponent,\n                        NgOptgroupTemplateDirective,\n                        NgOptionTemplateDirective,\n                        NgLabelTemplateDirective,\n                        NgMultiLabelTemplateDirective,\n                        NgHeaderTemplateDirective,\n                        NgFooterTemplateDirective,\n                        NgNotFoundTemplateDirective,\n                        NgTypeToSearchTemplateDirective,\n                        NgLoadingTextTemplateDirective,\n                        NgTagTemplateDirective,\n                        NgLoadingSpinnerTemplateDirective,\n                        NgItemLabelDirective\n                    ],\n                    imports: [\n                        CommonModule\n                    ],\n                    exports: [\n                        NgSelectComponent,\n                        NgOptionComponent,\n                        NgOptgroupTemplateDirective,\n                        NgOptionTemplateDirective,\n                        NgLabelTemplateDirective,\n                        NgMultiLabelTemplateDirective,\n                        NgHeaderTemplateDirective,\n                        NgFooterTemplateDirective,\n                        NgNotFoundTemplateDirective,\n                        NgTypeToSearchTemplateDirective,\n                        NgLoadingTextTemplateDirective,\n                        NgTagTemplateDirective,\n                        NgLoadingSpinnerTemplateDirective\n                    ],\n                    providers: [\n                        { provide: SELECTION_MODEL_FACTORY, useValue: DefaultSelectionModelFactory }\n                    ]\n                }]\n        }] });\n\n/*\n * Public API Surface of ng-select\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgFooterTemplateDirective, NgHeaderTemplateDirective, NgItemLabelDirective, NgLabelTemplateDirective, NgLoadingSpinnerTemplateDirective, NgLoadingTextTemplateDirective, NgMultiLabelTemplateDirective, NgNotFoundTemplateDirective, NgOptgroupTemplateDirective, NgOptionComponent, NgOptionTemplateDirective, NgSelectComponent, NgSelectConfig, NgSelectModule, NgTagTemplateDirective, NgTypeToSearchTemplateDirective, SELECTION_MODEL_FACTORY };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC1S,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAChG,SAASC,uBAAuB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AACxF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,UAAA,EAAAD;AAAA;AAAA,SAAAE,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAuC2C9C,EAAE,CAAAgD,cAAA,YAmtDxC,CAAC;IAntDqChD,EAAE,CAAAiD,kBAAA,KAotD8B,CAAC;IAptDjCjD,EAAE,CAAAkD,YAAA,CAqtDzF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArtDsFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAotD1C,CAAC;IAptDuCrD,EAAE,CAAAsD,UAAA,qBAAAH,MAAA,CAAAI,cAotD1C,CAAC,4BAptDuCvD,EAAE,CAAAwD,eAAA,IAAAd,GAAA,EAAAS,MAAA,CAAAM,WAAA,CAotDc,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAptDjB9C,EAAE,CAAAgD,cAAA,YA4tDxC,CAAC;IA5tDqChD,EAAE,CAAAiD,kBAAA,KA6tD8B,CAAC;IA7tDjCjD,EAAE,CAAAkD,YAAA,CA8tDzF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GA9tDsFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CA6tD1C,CAAC;IA7tDuCrD,EAAE,CAAAsD,UAAA,qBAAAH,MAAA,CAAAQ,cA6tD1C,CAAC,4BA7tDuC3D,EAAE,CAAAwD,eAAA,IAAAd,GAAA,EAAAS,MAAA,CAAAM,WAAA,CA6tDc,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAnB,EAAA,EAAAoB,EAAA,EAAAC,EAAA;EAAAC,IAAA,EAAAtB,EAAA;EAAAuB,KAAA,EAAAH,EAAA;EAAAI,KAAA,EAAAH;AAAA;AAAA,MAAAI,GAAA,GAAAA,CAAAzB,EAAA,EAAAoB,EAAA;EAAAM,KAAA,EAAA1B,EAAA;EAAAuB,KAAA,EAAAH;AAAA;AAAA,MAAAO,GAAA,GAAAA,CAAA3B,EAAA,EAAAoB,EAAA,EAAAC,EAAA,EAAAO,EAAA;EAAAN,IAAA,EAAAtB,EAAA;EAAA6B,KAAA,EAAAT,EAAA;EAAAU,KAAA,EAAAT,EAAA;EAAApB,UAAA,EAAA2B;AAAA;AAAA,SAAAG,8DAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6B,GAAA,GA7tDjB3E,EAAE,CAAA4E,gBAAA;IAAF5E,EAAE,CAAAgD,cAAA,cAkpFi+E,CAAC;IAlpFp+EhD,EAAE,CAAA6E,UAAA,mBAAAC,oFAAA;MAAF9E,EAAE,CAAA+E,aAAA,CAAAJ,GAAA;MAAA,MAAAK,OAAA,GAAFhF,EAAE,CAAAoD,aAAA,GAAA6B,SAAA;MAAA,MAAAC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpF27ED,MAAA,CAAAE,QAAA,CAAAJ,OAAa,CAAC;IAAA,CAAE,CAAC;IAlpF98EhF,EAAE,CAAAqF,MAAA,UAkpFu+E,CAAC;IAlpF1+ErF,EAAE,CAAAkD,YAAA,CAkpF8+E,CAAC;IAlpFj/ElD,EAAE,CAAAsF,SAAA,cAkpF+lF,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAAkC,OAAA,GAlpFlmFhF,EAAE,CAAAoD,aAAA,GAAA6B,SAAA;IAAA,MAAAC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpF+jF,CAAC;IAlpFlkFrD,EAAE,CAAAsD,UAAA,gBAAA0B,OAAA,CAAAb,KAkpF+jF,CAAC,WAAAe,MAAA,CAAAK,UAAuB,CAAC;EAAA;AAAA;AAAA,SAAAC,8DAAA1C,EAAA,EAAAC,GAAA;AAAA,SAAA0C,gDAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpF1lF9C,EAAE,CAAAgD,cAAA,aAkpFk0E,CAAC;IAlpFr0EhD,EAAE,CAAA0F,UAAA,IAAAhB,6DAAA,gCAAF1E,EAAE,CAAA2F,sBAkpFu3E,CAAC,IAAAH,6DAAA,yBAAue,CAAC;IAlpFl2FxF,EAAE,CAAAkD,YAAA,CAkpFm5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAkC,OAAA,GAAAjC,GAAA,CAAAkC,SAAA;IAAA,MAAAW,uBAAA,GAlpFt5F5F,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA8F,WAAA,sBAAAd,OAAA,CAAAe,QAkpFivE,CAAC;IAlpFpvE/F,EAAE,CAAAqD,SAAA,EAkpFivF,CAAC;IAlpFpvFrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAc,aAAA,IAAAJ,uBAkpFivF,CAAC,4BAlpFpvF5F,EAAE,CAAAiG,eAAA,IAAAnC,GAAA,EAAAkB,OAAA,CAAAkB,KAAA,EAAAhB,MAAA,CAAAiB,SAAA,EAAAnB,OAAA,CAAAb,KAAA,CAkpF81F,CAAC;EAAA;AAAA;AAAA,SAAAiC,0CAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFj2F9C,EAAE,CAAAqG,uBAAA,EAkpFmrE,CAAC;IAlpFtrErG,EAAE,CAAA0F,UAAA,IAAAD,+CAAA,iBAkpFk0E,CAAC;IAlpFr0EzF,EAAE,CAAAsG,qBAAA;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAAoC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAkpFyyE,CAAC;IAlpF5yErD,EAAE,CAAAsD,UAAA,YAAA4B,MAAA,CAAAqB,aAkpFyyE,CAAC,iBAAArB,MAAA,CAAAsB,aAAqB,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAA3D,EAAA,EAAAC,GAAA;AAAA,SAAA2D,6BAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFl0E9C,EAAE,CAAA0F,UAAA,IAAAe,0CAAA,yBAkpFiqG,CAAC;EAAA;EAAA,IAAA3D,EAAA;IAAA,MAAAoC,MAAA,GAlpFpqGlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAyB,kBAkpFqkG,CAAC,4BAlpFxkG3G,EAAE,CAAA4G,eAAA,IAAAxC,GAAA,EAAAc,MAAA,CAAA2B,cAAA,EAAA3B,MAAA,CAAAiB,SAAA,CAkpFgqG,CAAC;EAAA;AAAA;AAAA,SAAAW,wDAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFnqG9C,EAAE,CAAAsF,SAAA,aAkpF07I,CAAC;EAAA;AAAA;AAAA,SAAAyB,wDAAAjE,EAAA,EAAAC,GAAA;AAAA,SAAAiE,0CAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpF77I9C,EAAE,CAAAqG,uBAAA,EAkpF+0I,CAAC;IAlpFl1IrG,EAAE,CAAA0F,UAAA,IAAAoB,uDAAA,gCAAF9G,EAAE,CAAA2F,sBAkpFq4I,CAAC,IAAAoB,uDAAA,yBAAiM,CAAC;IAlpF1kJ/G,EAAE,CAAAsG,qBAAA;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAAmE,gCAAA,GAAFjH,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpFskJ,CAAC;IAlpFzkJrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAgC,sBAAA,IAAAD,gCAkpFskJ,CAAC;EAAA;AAAA;AAAA,SAAAE,mCAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFzkJ9C,EAAE,CAAAgD,cAAA,iBAkpF0uJ,CAAC,cAAuD,CAAC;IAlpFryJhD,EAAE,CAAAqF,MAAA,UAkpFwyJ,CAAC;IAlpF3yJrF,EAAE,CAAAkD,YAAA,CAkpF+yJ,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpF/zJlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAoH,qBAAA,UAAAlC,MAAA,CAAAmC,YAkpF4tJ,CAAC;EAAA;AAAA;AAAA,SAAAC,oEAAAxE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpF/tJ9C,EAAE,CAAAsF,SAAA,cAkpFwxN,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAAyE,QAAA,GAlpF3xNvH,EAAE,CAAAoD,aAAA,GAAA6B,SAAA;IAAA,MAAAC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAsD,UAAA,gBAAAiE,QAAA,CAAApD,KAkpFwvN,CAAC,WAAAe,MAAA,CAAAK,UAAuB,CAAC;EAAA;AAAA;AAAA,SAAAiC,oEAAA1E,EAAA,EAAAC,GAAA;AAAA,SAAA0E,sDAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4E,GAAA,GAlpFnxN1H,EAAE,CAAA4E,gBAAA;IAAF5E,EAAE,CAAAgD,cAAA,aAkpFsnN,CAAC;IAlpFznNhD,EAAE,CAAA6E,UAAA,mBAAA8C,2EAAA;MAAA,MAAAJ,QAAA,GAAFvH,EAAE,CAAA+E,aAAA,CAAA2C,GAAA,EAAAzC,SAAA;MAAA,MAAAC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpFgiMD,MAAA,CAAA0C,UAAA,CAAAL,QAAe,CAAC;IAAA,CAAC,CAAC,uBAAAM,+EAAA;MAAA,MAAAN,QAAA,GAlpFpjMvH,EAAE,CAAA+E,aAAA,CAAA2C,GAAA,EAAAzC,SAAA;MAAA,MAAAC,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpFikMD,MAAA,CAAA4C,WAAA,CAAAP,QAAgB,CAAC;IAAA,CAAC,CAAC;IAlpFtlMvH,EAAE,CAAA0F,UAAA,IAAA4B,mEAAA,gCAAFtH,EAAE,CAAA2F,sBAkpF0qN,CAAC,IAAA6B,mEAAA,yBAAib,CAAC;IAlpF/lOxH,EAAE,CAAAkD,YAAA,CAkpFwoO,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAyE,QAAA,GAAAxE,GAAA,CAAAkC,SAAA;IAAA,MAAA8C,yBAAA,GAlpF3oO/H,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA8F,WAAA,uBAAAyB,QAAA,CAAAxB,QAkpF+tM,CAAC,uBAAAwB,QAAA,CAAAS,QAA6D,CAAC,gBAAAT,QAAA,CAAAU,QAAsD,CAAC,eAAAV,QAAA,CAAAU,QAAqD,CAAC,sBAAAV,QAAA,CAAAW,MAA0D,CAAC,qBAAAX,QAAA,KAAArC,MAAA,CAAAiD,SAAA,CAAAC,UAA2E,CAAC;IAlpFphNpI,EAAE,CAAAqI,WAAA,SAAAd,QAAA,CAAAU,QAAA,wCAAAV,QAAA,CAAAS,QAAA,QAAAT,QAAA,kBAAAA,QAAA,CAAAe,MAAA;IAAFtI,EAAE,CAAAqD,SAAA,EAkpFg+N,CAAC;IAlpFn+NrD,EAAE,CAAAsD,UAAA,qBAAAiE,QAAA,CAAAU,QAAA,GAAA/C,MAAA,CAAAqD,gBAAA,IAAAR,yBAAA,GAAA7C,MAAA,CAAAsD,cAAA,IAAAT,yBAkpFg+N,CAAC,4BAlpFn+N/H,EAAE,CAAAyI,eAAA,KAAAnE,GAAA,EAAAiD,QAAA,CAAArB,KAAA,EAAAqB,QAAA,EAAAA,QAAA,CAAA9C,KAAA,EAAAS,MAAA,CAAAtC,UAAA,CAkpF2lO,CAAC;EAAA;AAAA;AAAA,SAAA8F,oEAAA5F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpF9lO9C,EAAE,CAAAgD,cAAA,UAkpF24O,CAAC,cAA4B,CAAC;IAlpF36OhD,EAAE,CAAAqF,MAAA,EAkpFs7O,CAAC;IAlpFz7OrF,EAAE,CAAAkD,YAAA,CAkpF67O,CAAC;IAlpFh8OlD,EAAE,CAAAqF,MAAA,EAkpF+8O,CAAC;IAlpFl9OrF,EAAE,CAAAkD,YAAA,CAkpFs9O,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpFz9OlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpFs7O,CAAC;IAlpFz7OrD,EAAE,CAAA2I,iBAAA,CAAAzD,MAAA,CAAA0D,UAkpFs7O,CAAC;IAlpFz7O5I,EAAE,CAAAqD,SAAA,CAkpF+8O,CAAC;IAlpFl9OrD,EAAE,CAAA6I,kBAAA,OAAA3D,MAAA,CAAAtC,UAAA,MAkpF+8O,CAAC;EAAA;AAAA;AAAA,SAAAkG,oEAAAhG,EAAA,EAAAC,GAAA;AAAA,SAAAgG,sDAAAjG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkG,IAAA,GAlpFl9OhJ,EAAE,CAAA4E,gBAAA;IAAF5E,EAAE,CAAAgD,cAAA,aAkpFo0O,CAAC;IAlpFv0OhD,EAAE,CAAA6E,UAAA,uBAAAoE,+EAAA;MAAFjJ,EAAE,CAAA+E,aAAA,CAAAiE,IAAA;MAAA,MAAA9D,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpF+uOD,MAAA,CAAAiD,SAAA,CAAAe,UAAA,CAAqB,CAAC;IAAA,CAAC,CAAC,mBAAAC,2EAAA;MAlpFzwOnJ,EAAE,CAAA+E,aAAA,CAAAiE,IAAA;MAAA,MAAA9D,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpFkyOD,MAAA,CAAAkE,SAAA,CAAU,CAAC;IAAA,CAAC,CAAC;IAlpFjzOpJ,EAAE,CAAA0F,UAAA,IAAAgD,mEAAA,gCAAF1I,EAAE,CAAA2F,sBAkpFm3O,CAAC,IAAAmD,mEAAA,yBAA+S,CAAC;IAlpFtqP9I,EAAE,CAAAkD,YAAA,CAkpF+sP,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAuG,sBAAA,GAlpFltPrJ,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA8F,WAAA,sBAAAZ,MAAA,CAAAiD,SAAA,CAAAC,UAkpF+tO,CAAC;IAlpFluOpI,EAAE,CAAAqD,SAAA,EAkpFwlP,CAAC;IAlpF3lPrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAoE,WAAA,IAAAD,sBAkpFwlP,CAAC,4BAlpF3lPrJ,EAAE,CAAAwD,eAAA,IAAAd,GAAA,EAAAwC,MAAA,CAAAtC,UAAA,CAkpFkqP,CAAC;EAAA;AAAA;AAAA,SAAA2G,6EAAAzG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFrqP9C,EAAE,CAAAgD,cAAA,aAkpFi4P,CAAC;IAlpFp4PhD,EAAE,CAAAqF,MAAA,EAkpFi5P,CAAC;IAlpFp5PrF,EAAE,CAAAkD,YAAA,CAkpFu5P,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpF15PlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAkpFi5P,CAAC;IAlpFp5PrD,EAAE,CAAA2I,iBAAA,CAAAzD,MAAA,CAAAsE,YAkpFi5P,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAA3G,EAAA,EAAAC,GAAA;AAAA,SAAA2G,+DAAA5G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFp5P9C,EAAE,CAAAqG,uBAAA,EAkpFuxP,CAAC;IAlpF1xPrG,EAAE,CAAA0F,UAAA,IAAA6D,4EAAA,gCAAFvJ,EAAE,CAAA2F,sBAkpFu0P,CAAC,IAAA8D,4EAAA,yBAAsR,CAAC;IAlpFjmQzJ,EAAE,CAAAsG,qBAAA;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAA6G,2BAAA,GAAF3J,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpFuhQ,CAAC;IAlpF1hQrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAA0E,gBAAA,IAAAD,2BAkpFuhQ,CAAC,4BAlpF1hQ3J,EAAE,CAAAwD,eAAA,IAAAd,GAAA,EAAAwC,MAAA,CAAAtC,UAAA,CAkpF6lQ,CAAC;EAAA;AAAA;AAAA,SAAAiH,6EAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFhmQ9C,EAAE,CAAAgD,cAAA,aAkpF4yQ,CAAC;IAlpF/yQhD,EAAE,CAAAqF,MAAA,EAkpFg0Q,CAAC;IAlpFn0QrF,EAAE,CAAAkD,YAAA,CAkpFs0Q,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpFz0QlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAkpFg0Q,CAAC;IAlpFn0QrD,EAAE,CAAA2I,iBAAA,CAAAzD,MAAA,CAAA4E,gBAkpFg0Q,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAAjH,EAAA,EAAAC,GAAA;AAAA,SAAAiH,+DAAAlH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFn0Q9C,EAAE,CAAAqG,uBAAA,EAkpF8rQ,CAAC;IAlpFjsQrG,EAAE,CAAA0F,UAAA,IAAAmE,4EAAA,gCAAF7J,EAAE,CAAA2F,sBAkpFkvQ,CAAC,IAAAoE,4EAAA,yBAA4N,CAAC;IAlpFl9Q/J,EAAE,CAAAsG,qBAAA;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAAmH,+BAAA,GAAFjK,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpF88Q,CAAC;IAlpFj9QrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAgF,oBAAA,IAAAD,+BAkpF88Q,CAAC;EAAA;AAAA;AAAA,SAAAE,6EAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpFj9Q9C,EAAE,CAAAgD,cAAA,aAkpFyrR,CAAC;IAlpF5rRhD,EAAE,CAAAqF,MAAA,EAkpFwsR,CAAC;IAlpF3sRrF,EAAE,CAAAkD,YAAA,CAkpF8sR,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpFjtRlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAkpFwsR,CAAC;IAlpF3sRrD,EAAE,CAAA2I,iBAAA,CAAAzD,MAAA,CAAAkF,WAkpFwsR,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAAvH,EAAA,EAAAC,GAAA;AAAA,SAAAuH,+DAAAxH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlpF3sR9C,EAAE,CAAAqG,uBAAA,EAkpF4kR,CAAC;IAlpF/kRrG,EAAE,CAAA0F,UAAA,IAAAyE,4EAAA,gCAAFnK,EAAE,CAAA2F,sBAkpF+nR,CAAC,IAAA0E,4EAAA,yBAA2R,CAAC;IAlpF95RrK,EAAE,CAAAsG,qBAAA;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAAyH,8BAAA,GAAFvK,EAAE,CAAA6F,WAAA;IAAA,MAAAX,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,EAkpFo1R,CAAC;IAlpFv1RrD,EAAE,CAAAsD,UAAA,qBAAA4B,MAAA,CAAAsF,mBAAA,IAAAD,8BAkpFo1R,CAAC,4BAlpFv1RvK,EAAE,CAAAwD,eAAA,IAAAd,GAAA,EAAAwC,MAAA,CAAAtC,UAAA,CAkpF05R,CAAC;EAAA;AAAA;AAAA,SAAA6H,gDAAA3H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4H,GAAA,GAlpF75R1K,EAAE,CAAA4E,gBAAA;IAAF5E,EAAE,CAAAgD,cAAA,2BAkpFy6L,CAAC;IAlpF56LhD,EAAE,CAAA6E,UAAA,oBAAA8F,oFAAAC,MAAA;MAAF5K,EAAE,CAAA+E,aAAA,CAAA2F,GAAA;MAAA,MAAAxF,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAAAD,MAAA,CAAA2F,aAAA,GAAAD,MAAA;IAAA,CAkpFghL,CAAC,oBAAAE,oFAAAF,MAAA;MAlpFnhL5K,EAAE,CAAA+E,aAAA,CAAA2F,GAAA;MAAA,MAAAxF,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpFijLD,MAAA,CAAA6F,MAAA,CAAAC,IAAA,CAAAJ,MAAkB,CAAC;IAAA,CAAC,CAAC,yBAAAK,yFAAAL,MAAA;MAlpFxkL5K,EAAE,CAAA+E,aAAA,CAAA2F,GAAA;MAAA,MAAAxF,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpF2mLD,MAAA,CAAAgG,WAAA,CAAAF,IAAA,CAAAJ,MAAuB,CAAC;IAAA,CAAC,CAAC,0BAAAO,0FAAA;MAlpFvoLnL,EAAE,CAAA+E,aAAA,CAAA2F,GAAA;MAAA,MAAAxF,MAAA,GAAFlF,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAmF,WAAA,CAkpF2qLD,MAAA,CAAAkG,KAAA,CAAM,CAAC;IAAA,CAAC,CAAC;IAlpFtrLpL,EAAE,CAAAqG,uBAAA,EAkpF+7L,CAAC;IAlpFl8LrG,EAAE,CAAA0F,UAAA,IAAA+B,qDAAA,kBAkpFsnN,CAAC,IAAAsB,qDAAA,iBAA6sB,CAAC;IAlpFv0O/I,EAAE,CAAAsG,qBAAA;IAAFtG,EAAE,CAAA0F,UAAA,IAAAgE,8DAAA,0BAkpFuxP,CAAC,IAAAM,8DAAA,0BAAsa,CAAC,IAAAM,8DAAA,0BAA6Y,CAAC;IAlpF/kRtK,EAAE,CAAAkD,YAAA,CAkpFg+R,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAoC,MAAA,GAlpFn+RlF,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA8F,WAAA,uBAAAZ,MAAA,CAAAmG,QAkpF+uL,CAAC;IAlpFlvLrL,EAAE,CAAAsD,UAAA,kBAAA4B,MAAA,CAAAoG,aAkpFijK,CAAC,iBAAApG,MAAA,CAAAqG,YAAmD,CAAC,aAAArG,MAAA,CAAAsG,QAA2C,CAAC,aAAAtG,MAAA,CAAAuG,gBAAmD,CAAC,mBAAAvG,MAAA,CAAA3B,cAAuD,CAAC,mBAAA2B,MAAA,CAAAvB,cAAuD,CAAC,gBAAAuB,MAAA,CAAAtC,UAAgD,CAAC,UAAAsC,MAAA,CAAAiD,SAAA,CAAAuD,aAAuD,CAAC,eAAAxG,MAAA,CAAAiD,SAAA,CAAAC,UAAyD,CAAC,YAAAlD,MAAA,CAAAsG,QAAA,GAAAtG,MAAA,CAAAyG,OAAA,OAAkV,CAAC,OAAAzG,MAAA,CAAA0G,UAAuC,CAAC;IAlpFt1L5L,EAAE,CAAAqD,SAAA,EAkpFyoM,CAAC;IAlpF5oMrD,EAAE,CAAAsD,UAAA,YAAA4B,MAAA,CAAA2F,aAkpFyoM,CAAC,iBAAA3F,MAAA,CAAAsB,aAAqB,CAAC;IAlpFlqMxG,EAAE,CAAAqD,SAAA,CAkpFi0O,CAAC;IAlpFp0OrD,EAAE,CAAAsD,UAAA,SAAA4B,MAAA,CAAA2G,UAkpFi0O,CAAC;IAlpFp0O7L,EAAE,CAAAqD,SAAA,CAkpFoxP,CAAC;IAlpFvxPrD,EAAE,CAAAsD,UAAA,SAAA4B,MAAA,CAAA4G,gBAAA,EAkpFoxP,CAAC;IAlpFvxP9L,EAAE,CAAAqD,SAAA,CAkpF2rQ,CAAC;IAlpF9rQrD,EAAE,CAAAsD,UAAA,SAAA4B,MAAA,CAAA6G,gBAAA,EAkpF2rQ,CAAC;IAlpF9rQ/L,EAAE,CAAAqD,SAAA,CAkpFykR,CAAC;IAlpF5kRrD,EAAE,CAAAsD,UAAA,SAAA4B,MAAA,CAAA8G,OAAA,IAAA9G,MAAA,CAAAiD,SAAA,CAAAuD,aAAA,CAAAO,MAAA,MAkpFykR,CAAC;EAAA;AAAA;AAvrFhrR,MAAMC,gBAAgB,GAAG,UAAU;AACnC,MAAMC,mBAAmB,GAAGC,MAAM,CAACF,gBAAgB,CAACG,MAAM,CAAC;AAC3D,MAAMC,WAAW,GAAG;EAChB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,IAAI,EAAE;AACV,CAAC;AACD,SAAS/G,UAAUA,CAACW,KAAK,EAAE;EACvB,OAAQA,KAAK,IAAIiG,mBAAmB,CAACI,IAAI,CAACrG,KAAK,CAAC,GAC5CA,KAAK,CAACsG,OAAO,CAACN,gBAAgB,EAAEO,GAAG,IAAIH,WAAW,CAACG,GAAG,CAAC,CAAC,GACxDvG,KAAK;AACb;AACA,SAASwG,SAASA,CAACxG,KAAK,EAAE;EACtB,OAAOA,KAAK,KAAKyG,SAAS,IAAIzG,KAAK,KAAK,IAAI;AAChD;AACA,SAAS0G,QAAQA,CAAC1G,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIwG,SAAS,CAACxG,KAAK,CAAC;AACxD;AACA,SAAS2G,SAASA,CAAC3G,KAAK,EAAE;EACtB,OAAOA,KAAK,YAAY4G,OAAO;AACnC;AACA,SAASC,UAAUA,CAAC7G,KAAK,EAAE;EACvB,OAAOA,KAAK,YAAY8G,QAAQ;AACpC;AAEA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACH,OAAO,CAACI,aAAa,CAACC,SAAS,GAAG,IAAI,CAACJ,MAAM,GAC9C7H,UAAU,CAAC,IAAI,CAACkI,WAAW,CAAC,GAC5B,IAAI,CAACA,WAAW;EACxB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFX,oBAAoB,EAA9BjN,EAAE,CAAA6N,iBAAA,CAA8C7N,EAAE,CAACK,UAAU;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACyN,IAAI,kBAD8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EACJf,oBAAoB;MAAAgB,SAAA;MAAAC,MAAA;QAAAT,WAAA;QAAAL,MAAA;MAAA;MAAAe,QAAA,GADlBnO,EAAE,CAAAoO,oBAAA;IAAA,EAC2I;EAAE;AACnP;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrO,EAAE,CAAAsO,iBAAA,CAGXrB,oBAAoB,EAAc,CAAC;IAClHe,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAgB,CAAC;EACxC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACK;EAAW,CAAC,CAAC,EAAkB;IAAEoN,WAAW,EAAE,CAAC;MAC7EO,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEkN,MAAM,EAAE,CAAC;MACTY,IAAI,EAAE9N;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMuO,yBAAyB,CAAC;EAC5BvB,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAiB,kCAAAf,CAAA;MAAA,YAAAA,CAAA,IAAwFa,yBAAyB,EAhBnCzO,EAAE,CAAA6N,iBAAA,CAgBmD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAAC+M,IAAI,kBAjB8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAiBJS,yBAAyB;MAAAR,SAAA;IAAA,EAA8C;EAAE;AAC3K;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAnBoGrO,EAAE,CAAAsO,iBAAA,CAmBXG,yBAAyB,EAAc,CAAC;IACvHT,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAkB,CAAC;EAC1C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAM6N,2BAA2B,CAAC;EAC9B1B,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAmB,oCAAAjB,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,2BAA2B,EA5BrC5O,EAAE,CAAA6N,iBAAA,CA4BqD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACnN;IAAS,IAAI,CAAC+M,IAAI,kBA7B8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EA6BJY,2BAA2B;MAAAX,SAAA;IAAA,EAAgD;EAAE;AAC/K;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA/BoGrO,EAAE,CAAAsO,iBAAA,CA+BXM,2BAA2B,EAAc,CAAC;IACzHZ,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAoB,CAAC;EAC5C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAM+N,wBAAwB,CAAC;EAC3B5B,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAqB,iCAAAnB,CAAA;MAAA,YAAAA,CAAA,IAAwFkB,wBAAwB,EAxClC9O,EAAE,CAAA6N,iBAAA,CAwCkD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EAChN;IAAS,IAAI,CAAC+M,IAAI,kBAzC8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAyCJc,wBAAwB;MAAAb,SAAA;IAAA,EAA6C;EAAE;AACzK;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA3CoGrO,EAAE,CAAAsO,iBAAA,CA2CXQ,wBAAwB,EAAc,CAAC;IACtHd,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAiB,CAAC;EACzC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAMiO,6BAA6B,CAAC;EAChC9B,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAuB,sCAAArB,CAAA;MAAA,YAAAA,CAAA,IAAwFoB,6BAA6B,EApDvChP,EAAE,CAAA6N,iBAAA,CAoDuD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACrN;IAAS,IAAI,CAAC+M,IAAI,kBArD8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAqDJgB,6BAA6B;MAAAf,SAAA;IAAA,EAAmD;EAAE;AACpL;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAvDoGrO,EAAE,CAAAsO,iBAAA,CAuDXU,6BAA6B,EAAc,CAAC;IAC3HhB,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAuB,CAAC;EAC/C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAMmO,yBAAyB,CAAC;EAC5BhC,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAyB,kCAAAvB,CAAA;MAAA,YAAAA,CAAA,IAAwFsB,yBAAyB,EAhEnClP,EAAE,CAAA6N,iBAAA,CAgEmD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAAC+M,IAAI,kBAjE8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAiEJkB,yBAAyB;MAAAjB,SAAA;IAAA,EAA8C;EAAE;AAC3K;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAnEoGrO,EAAE,CAAAsO,iBAAA,CAmEXY,yBAAyB,EAAc,CAAC;IACvHlB,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAkB,CAAC;EAC1C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAMqO,yBAAyB,CAAC;EAC5BlC,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAA2B,kCAAAzB,CAAA;MAAA,YAAAA,CAAA,IAAwFwB,yBAAyB,EA5EnCpP,EAAE,CAAA6N,iBAAA,CA4EmD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAAC+M,IAAI,kBA7E8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EA6EJoB,yBAAyB;MAAAnB,SAAA;IAAA,EAA8C;EAAE;AAC3K;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA/EoGrO,EAAE,CAAAsO,iBAAA,CA+EXc,yBAAyB,EAAc,CAAC;IACvHpB,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAkB,CAAC;EAC1C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAMuO,2BAA2B,CAAC;EAC9BpC,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAA6B,oCAAA3B,CAAA;MAAA,YAAAA,CAAA,IAAwF0B,2BAA2B,EAxFrCtP,EAAE,CAAA6N,iBAAA,CAwFqD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACnN;IAAS,IAAI,CAAC+M,IAAI,kBAzF8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAyFJsB,2BAA2B;MAAArB,SAAA;IAAA,EAAgD;EAAE;AAC/K;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA3FoGrO,EAAE,CAAAsO,iBAAA,CA2FXgB,2BAA2B,EAAc,CAAC;IACzHtB,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAoB,CAAC;EAC5C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAMyO,+BAA+B,CAAC;EAClCtC,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAA+B,wCAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAwF4B,+BAA+B,EApGzCxP,EAAE,CAAA6N,iBAAA,CAoGyD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACvN;IAAS,IAAI,CAAC+M,IAAI,kBArG8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAqGJwB,+BAA+B;MAAAvB,SAAA;IAAA,EAAoD;EAAE;AACvL;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAvGoGrO,EAAE,CAAAsO,iBAAA,CAuGXkB,+BAA+B,EAAc,CAAC;IAC7HxB,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAwB,CAAC;EAChD,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAM2O,8BAA8B,CAAC;EACjCxC,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAiC,uCAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAwF8B,8BAA8B,EAhHxC1P,EAAE,CAAA6N,iBAAA,CAgHwD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACtN;IAAS,IAAI,CAAC+M,IAAI,kBAjH8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAiHJ0B,8BAA8B;MAAAzB,SAAA;IAAA,EAAmD;EAAE;AACrL;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAnHoGrO,EAAE,CAAAsO,iBAAA,CAmHXoB,8BAA8B,EAAc,CAAC;IAC5H1B,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAuB,CAAC;EAC/C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAM6O,sBAAsB,CAAC;EACzB1C,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAmC,+BAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAwFgC,sBAAsB,EA5HhC5P,EAAE,CAAA6N,iBAAA,CA4HgD7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAAC+M,IAAI,kBA7H8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EA6HJ4B,sBAAsB;MAAA3B,SAAA;IAAA,EAA2C;EAAE;AACrK;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA/HoGrO,EAAE,CAAAsO,iBAAA,CA+HXsB,sBAAsB,EAAc,CAAC;IACpH5B,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAe,CAAC;EACvC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAC5D;AACA,MAAM+O,iCAAiC,CAAC;EACpC5C,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAqC,0CAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,iCAAiC,EAxI3C9P,EAAE,CAAA6N,iBAAA,CAwI2D7N,EAAE,CAACe,WAAW;IAAA,CAA4C;EAAE;EACzN;IAAS,IAAI,CAAC+M,IAAI,kBAzI8E9N,EAAE,CAAA+N,iBAAA;MAAAC,IAAA,EAyIJ8B,iCAAiC;MAAA7B,SAAA;IAAA,EAAsD;EAAE;AAC3L;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KA3IoGrO,EAAE,CAAAsO,iBAAA,CA2IXwB,iCAAiC,EAAc,CAAC;IAC/H9B,IAAI,EAAE/N,SAAS;IACfsO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAA0B,CAAC;EAClD,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEhO,EAAE,CAACe;EAAY,CAAC,CAAC;AAAA;AAE5D,SAASiP,KAAKA,CAAA,EAAG;EACb;EACA,OAAO,cAAc,CAACxD,OAAO,CAAC,MAAM,EAAE,MAAM;IACxC;IACA,MAAMyD,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClC,OAAOF,GAAG,CAACG,QAAQ,CAAC,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;AAEA,MAAMC,UAAU,GAAG;EACf,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE;AACd,CAAC;AACD,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC7B,MAAMC,KAAK,GAAIC,CAAC,IAAKJ,UAAU,CAACI,CAAC,CAAC,IAAIA,CAAC;EACvC,OAAOF,IAAI,CAAC/D,OAAO,CAAC,mBAAmB,EAAEgE,KAAK,CAAC;AACnD;AAEA,MAAME,SAAS,CAAC;EACZxD,WAAWA,CAACyD,SAAS,EAAEC,eAAe,EAAE;IACpC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;EAC1B;EACA,IAAI1M,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwM,MAAM;EACtB;EACA,IAAInF,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACoF,cAAc;EAC9B;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,YAAY;EAC5B;EACA,IAAIxK,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACqK,eAAe,CAAC1K,KAAK;EACrC;EACA,IAAIkC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC0I,cAAc,CAAC,IAAI,CAACC,YAAY,CAAC;EACjD;EACA,IAAIE,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACN,SAAS,CAACO,YAAY,IAAI,IAAI,CAACL,MAAM,CAAC5E,MAAM,KAAK,IAAI,CAAC1F,aAAa,CAAC0F,MAAM;EAC1F;EACA,IAAIkF,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACR,SAAS,CAACtF,QAAQ,IAAI,IAAI,CAACsF,SAAS,CAACS,gBAAgB,IAAI,IAAI,CAAC7K,aAAa,CAAC0F,MAAM;EAClG;EACA,IAAIoF,gBAAgBA,CAAA,EAAG;IACnB,IAAIC,CAAC,GAAG,IAAI,CAAC/K,aAAa,CAAC0F,MAAM,GAAG,CAAC;IACrC,OAAOqF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChB,MAAMrN,IAAI,GAAG,IAAI,CAACsC,aAAa,CAAC+K,CAAC,CAAC;MAClC,IAAI,CAACrN,IAAI,CAAC8B,QAAQ,EAAE;QAChB,OAAO9B,IAAI;MACf;IACJ;IACA,OAAO,IAAI;EACf;EACAsN,QAAQA,CAAClN,KAAK,EAAE;IACZ,IAAI,CAACwM,MAAM,GAAGxM,KAAK,CAACxC,GAAG,CAAC,CAACoC,IAAI,EAAEQ,KAAK,KAAK,IAAI,CAAC+M,OAAO,CAACvN,IAAI,EAAEQ,KAAK,CAAC,CAAC;IACnE,IAAI,IAAI,CAACkM,SAAS,CAACc,OAAO,EAAE;MACxB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACd,MAAM,EAAE,IAAI,CAACF,SAAS,CAACc,OAAO,CAAC;MACjE,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACe,QAAQ,CAAC,IAAI,CAACF,OAAO,CAAC;IAC7C,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAG,IAAIG,GAAG,CAAC,CAAC;MACxB,IAAI,CAACH,OAAO,CAACI,GAAG,CAACnF,SAAS,EAAE,IAAI,CAACkE,MAAM,CAAC;IAC5C;IACA,IAAI,CAACC,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAAC;EAC1C;EACAkB,MAAMA,CAAC9N,IAAI,EAAE;IACT,IAAIA,IAAI,CAAC+D,QAAQ,IAAI,IAAI,CAACmJ,gBAAgB,EAAE;MACxC;IACJ;IACA,MAAM9F,QAAQ,GAAG,IAAI,CAACsF,SAAS,CAACtF,QAAQ;IACxC,IAAI,CAACA,QAAQ,EAAE;MACX,IAAI,CAAC2G,aAAa,CAAC,CAAC;IACxB;IACA,IAAI,CAACpB,eAAe,CAACmB,MAAM,CAAC9N,IAAI,EAAEoH,QAAQ,EAAE,IAAI,CAACsF,SAAS,CAACsB,sBAAsB,CAAC;IAClF,IAAI,IAAI,CAACtB,SAAS,CAACO,YAAY,EAAE;MAC7B,IAAI,CAACgB,aAAa,CAACjO,IAAI,CAAC;IAC5B;EACJ;EACAmB,QAAQA,CAACnB,IAAI,EAAE;IACX,IAAI,CAACA,IAAI,CAAC+D,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAAC4I,eAAe,CAACxL,QAAQ,CAACnB,IAAI,EAAE,IAAI,CAAC0M,SAAS,CAACtF,QAAQ,CAAC;IAC5D,IAAI,IAAI,CAACsF,SAAS,CAACO,YAAY,IAAIxE,SAAS,CAACzI,IAAI,CAACQ,KAAK,CAAC,IAAI,IAAI,CAACkM,SAAS,CAACtF,QAAQ,EAAE;MACjF,IAAI,CAAC8G,aAAa,CAAClO,IAAI,CAAC;IAC5B;EACJ;EACAmO,QAAQA,CAAClM,KAAK,EAAE;IACZ,IAAImM,MAAM;IACV,IAAI,IAAI,CAAC1B,SAAS,CAAC2B,WAAW,EAAE;MAC5BD,MAAM,GAAGpO,IAAI,IAAI,IAAI,CAAC0M,SAAS,CAAC2B,WAAW,CAACrO,IAAI,CAACiC,KAAK,EAAEA,KAAK,CAAC;IAClE,CAAC,MACI,IAAI,IAAI,CAACyK,SAAS,CAAC4B,SAAS,EAAE;MAC/BF,MAAM,GAAGpO,IAAI,IAAI,CAACA,IAAI,CAACgE,QAAQ,IAAI,IAAI,CAACuK,aAAa,CAACvO,IAAI,CAACiC,KAAK,EAAE,IAAI,CAACyK,SAAS,CAAC4B,SAAS,CAAC,KAAKrM,KAAK;IACzG,CAAC,MACI;MACDmM,MAAM,GAAGpO,IAAI,IAAIA,IAAI,CAACiC,KAAK,KAAKA,KAAK,IACjC,CAACjC,IAAI,CAACgE,QAAQ,IAAIhE,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,KAAK,IAAI,CAACqO,aAAa,CAACtM,KAAK,EAAE,IAAI,CAACyK,SAAS,CAAC8B,SAAS,CAAC;IAC1G;IACA,OAAO,IAAI,CAAC5B,MAAM,CAAC6B,IAAI,CAACzO,IAAI,IAAIoO,MAAM,CAACpO,IAAI,CAAC,CAAC;EACjD;EACA0O,OAAOA,CAAC1O,IAAI,EAAE;IACV,MAAM2O,MAAM,GAAG,IAAI,CAACpB,OAAO,CAACvN,IAAI,EAAE,IAAI,CAAC4M,MAAM,CAAC5E,MAAM,CAAC;IACrD,IAAI,CAAC4E,MAAM,CAACgC,IAAI,CAACD,MAAM,CAAC;IACxB,IAAI,CAAC9B,cAAc,CAAC+B,IAAI,CAACD,MAAM,CAAC;IAChC,OAAOA,MAAM;EACjB;EACAZ,aAAaA,CAACc,YAAY,GAAG,KAAK,EAAE;IAChC,IAAI,CAAClC,eAAe,CAAC1M,KAAK,CAAC4O,YAAY,CAAC;IACxC,IAAI,CAACjC,MAAM,CAACkC,OAAO,CAAC9O,IAAI,IAAI;MACxBA,IAAI,CAAC+D,QAAQ,GAAG8K,YAAY,IAAI7O,IAAI,CAAC+D,QAAQ,IAAI/D,IAAI,CAAC8B,QAAQ;MAC9D9B,IAAI,CAAC+O,MAAM,GAAG,KAAK;IACvB,CAAC,CAAC;IACF,IAAI,IAAI,CAACrC,SAAS,CAACO,YAAY,EAAE;MAC7B,IAAI,CAAC+B,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAC,WAAWA,CAACC,IAAI,EAAE;IACdA,IAAI,GAAG7C,iBAAiB,CAAC6C,IAAI,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAClD,OAAO,IAAI,CAAC1H,aAAa,CAACgH,IAAI,CAACzO,IAAI,IAAI;MACnC,MAAME,KAAK,GAAGmM,iBAAiB,CAACrM,IAAI,CAACE,KAAK,CAAC,CAACiP,iBAAiB,CAAC,CAAC;MAC/D,OAAOjP,KAAK,CAACkP,MAAM,CAAC,CAAC,EAAEF,IAAI,CAAClH,MAAM,CAAC,KAAKkH,IAAI;IAChD,CAAC,CAAC;EACN;EACAvR,MAAMA,CAACuR,IAAI,EAAE;IACT,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACF,kBAAkB,CAAC,CAAC;MACzB;IACJ;IACA,IAAI,CAACnC,cAAc,GAAG,EAAE;IACxBqC,IAAI,GAAG,IAAI,CAACxC,SAAS,CAAC2C,QAAQ,GAAGH,IAAI,GAAG7C,iBAAiB,CAAC6C,IAAI,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACnF,MAAM5C,KAAK,GAAG,IAAI,CAACG,SAAS,CAAC2C,QAAQ,IAAI,IAAI,CAACC,gBAAgB;IAC9D,MAAMrC,YAAY,GAAG,IAAI,CAACP,SAAS,CAACO,YAAY;IAChD,KAAK,MAAMsC,GAAG,IAAIC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChC,OAAO,CAACiC,IAAI,CAAC,CAAC,CAAC,EAAE;MAC/C,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,MAAM3P,IAAI,IAAI,IAAI,CAACyN,OAAO,CAACmC,GAAG,CAACL,GAAG,CAAC,EAAE;QACtC,IAAItC,YAAY,KAAKjN,IAAI,CAACiE,MAAM,IAAIjE,IAAI,CAACiE,MAAM,CAACF,QAAQ,IAAI/D,IAAI,CAAC+D,QAAQ,CAAC,EAAE;UACxE;QACJ;QACA,MAAM8L,UAAU,GAAG,IAAI,CAACnD,SAAS,CAAC2C,QAAQ,GAAGrP,IAAI,CAACiC,KAAK,GAAGjC,IAAI;QAC9D,IAAIuM,KAAK,CAAC2C,IAAI,EAAEW,UAAU,CAAC,EAAE;UACzBF,YAAY,CAACf,IAAI,CAAC5O,IAAI,CAAC;QAC3B;MACJ;MACA,IAAI2P,YAAY,CAAC3H,MAAM,GAAG,CAAC,EAAE;QACzB,MAAM,CAAC8H,IAAI,CAAC,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAID,IAAI,CAAC7L,MAAM,EAAE;UACb,MAAM+L,IAAI,GAAG,IAAI,CAACpD,MAAM,CAAC6B,IAAI,CAACwB,CAAC,IAAIA,CAAC,KAAKH,IAAI,CAAC7L,MAAM,CAAC;UACrD,IAAI,CAAC4I,cAAc,CAAC+B,IAAI,CAACoB,IAAI,CAAC;QAClC;QACA,IAAI,CAACnD,cAAc,CAAC+B,IAAI,CAAC,GAAGe,YAAY,CAAC;MAC7C;IACJ;EACJ;EACAX,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACnC,cAAc,CAAC7E,MAAM,KAAK,IAAI,CAAC4E,MAAM,CAAC5E,MAAM,EAAE;MACnD;IACJ;IACA,IAAI,IAAI,CAAC0E,SAAS,CAACO,YAAY,IAAI,IAAI,CAAC3K,aAAa,CAAC0F,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAAC6E,cAAc,GAAG,IAAI,CAACD,MAAM,CAACjP,MAAM,CAACsS,CAAC,IAAI,CAACA,CAAC,CAAClM,QAAQ,CAAC;IAC9D,CAAC,MACI;MACD,IAAI,CAAC8I,cAAc,GAAG,IAAI,CAACD,MAAM;IACrC;EACJ;EACA3H,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC6H,YAAY,GAAG,CAAC,CAAC;EAC1B;EACAoD,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;EACxB;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC,CAAC;EACxB;EACAE,QAAQA,CAACrQ,IAAI,EAAE;IACX,IAAI,CAAC8M,YAAY,GAAG,IAAI,CAACD,cAAc,CAACyD,OAAO,CAACtQ,IAAI,CAAC;EACzD;EACAuQ,qBAAqBA,CAACC,WAAW,EAAE;IAC/B,IAAI,IAAI,CAAC3D,cAAc,CAAC7E,MAAM,KAAK,CAAC,EAAE;MAClC;IACJ;IACA,MAAMyI,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAClD,IAAID,eAAe,GAAG,CAAC,CAAC,EAAE;MACtB,IAAI,CAAC3D,YAAY,GAAG2D,eAAe;IACvC,CAAC,MACI;MACD,IAAI,CAAC3D,YAAY,GAAG0D,WAAW,GAAG,IAAI,CAAC/I,aAAa,CAACkJ,SAAS,CAACV,CAAC,IAAI,CAACA,CAAC,CAACnO,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzF;EACJ;EACAyM,aAAaA,CAACI,MAAM,EAAEY,GAAG,EAAE;IACvB,IAAI,CAAC5G,QAAQ,CAACgG,MAAM,CAAC,EAAE;MACnB,OAAOA,MAAM;IACjB;IACA,IAAIY,GAAG,CAACe,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACzB,OAAO3B,MAAM,CAACY,GAAG,CAAC;IACtB,CAAC,MACI;MACD,MAAMG,IAAI,GAAGH,GAAG,CAACqB,KAAK,CAAC,GAAG,CAAC;MAC3B,IAAI3O,KAAK,GAAG0M,MAAM;MAClB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEwD,GAAG,GAAGnB,IAAI,CAAC1H,MAAM,EAAEqF,CAAC,GAAGwD,GAAG,EAAE,EAAExD,CAAC,EAAE;QAC7C,IAAIpL,KAAK,IAAI,IAAI,EAAE;UACf,OAAO,IAAI;QACf;QACAA,KAAK,GAAGA,KAAK,CAACyN,IAAI,CAACrC,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOpL,KAAK;IAChB;EACJ;EACAsL,OAAOA,CAACvN,IAAI,EAAEQ,KAAK,EAAE;IACjB,MAAMN,KAAK,GAAGuI,SAAS,CAACzI,IAAI,CAAC8Q,cAAc,CAAC,GAAG9Q,IAAI,CAAC8Q,cAAc,GAAG,IAAI,CAACvC,aAAa,CAACvO,IAAI,EAAE,IAAI,CAAC0M,SAAS,CAAC8B,SAAS,CAAC;IACvH,MAAMvM,KAAK,GAAGwG,SAAS,CAACzI,IAAI,CAAC+Q,cAAc,CAAC,GAAG/Q,IAAI,CAAC+Q,cAAc,GAAG/Q,IAAI;IACzE,OAAO;MACHQ,KAAK;MACLN,KAAK,EAAEuI,SAAS,CAACvI,KAAK,CAAC,GAAGA,KAAK,CAACiM,QAAQ,CAAC,CAAC,GAAG,EAAE;MAC/ClK,KAAK;MACLH,QAAQ,EAAE9B,IAAI,CAAC8B,QAAQ;MACvBuC,MAAM,EAAG,GAAE,IAAI,CAACqI,SAAS,CAAC/E,UAAW,IAAGnH,KAAM;IAClD,CAAC;EACL;EACAwQ,gBAAgBA,CAAA,EAAG;IACf,MAAM5J,QAAQ,GAAG,IAAI,CAACsF,SAAS,CAACtF,QAAQ;IACxC,KAAK,MAAMrD,QAAQ,IAAI,IAAI,CAACzB,aAAa,EAAE;MACvC,MAAML,KAAK,GAAG,IAAI,CAACyK,SAAS,CAAC4B,SAAS,GAAG,IAAI,CAACC,aAAa,CAACxK,QAAQ,CAAC9B,KAAK,EAAE,IAAI,CAACyK,SAAS,CAAC4B,SAAS,CAAC,GAAGvK,QAAQ,CAAC9B,KAAK;MACtH,MAAMjC,IAAI,GAAGyI,SAAS,CAACxG,KAAK,CAAC,GAAG,IAAI,CAACkM,QAAQ,CAAClM,KAAK,CAAC,GAAG,IAAI;MAC3D,IAAI,CAAC0K,eAAe,CAACxL,QAAQ,CAAC4C,QAAQ,EAAEqD,QAAQ,CAAC;MACjD,IAAI,CAACuF,eAAe,CAACmB,MAAM,CAAC9N,IAAI,IAAI+D,QAAQ,EAAEqD,QAAQ,EAAE,IAAI,CAACsF,SAAS,CAACsB,sBAAsB,CAAC;IAClG;IACA,IAAI,IAAI,CAACtB,SAAS,CAACO,YAAY,EAAE;MAC7B,IAAI,CAACJ,cAAc,GAAG,IAAI,CAACpF,aAAa,CAAC9J,MAAM,CAACsS,CAAC,IAAI,IAAI,CAAC3N,aAAa,CAACgO,OAAO,CAACL,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9F;EACJ;EACA/B,aAAaA,CAAClO,IAAI,EAAE;IAChB,IAAI,CAAC6M,cAAc,CAAC+B,IAAI,CAAC5O,IAAI,CAAC;IAC9B,IAAIA,IAAI,CAACiE,MAAM,EAAE;MACb,MAAMA,MAAM,GAAGjE,IAAI,CAACiE,MAAM;MAC1B,MAAMgN,YAAY,GAAG,IAAI,CAACpE,cAAc,CAAC4B,IAAI,CAACwB,CAAC,IAAIA,CAAC,KAAKhM,MAAM,CAAC;MAChE,IAAI,CAACgN,YAAY,EAAE;QACf,IAAI,CAACpE,cAAc,CAAC+B,IAAI,CAAC3K,MAAM,CAAC;MACpC;IACJ,CAAC,MACI,IAAIjE,IAAI,CAACgE,QAAQ,EAAE;MACpB,KAAK,MAAMkN,KAAK,IAAIlR,IAAI,CAACgE,QAAQ,EAAE;QAC/BkN,KAAK,CAACnN,QAAQ,GAAG,KAAK;QACtB,IAAI,CAAC8I,cAAc,CAAC+B,IAAI,CAACsC,KAAK,CAAC;MACnC;IACJ;IACA,IAAI,CAACrE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACA,cAAc,CAACsE,IAAI,CAAC,CAAC3E,CAAC,EAAE4E,CAAC,KAAM5E,CAAC,CAAChM,KAAK,GAAG4Q,CAAC,CAAC5Q,KAAM,CAAC,CAAC;EACtF;EACAyN,aAAaA,CAACjO,IAAI,EAAE;IAChB,IAAI,CAAC6M,cAAc,GAAG,IAAI,CAACA,cAAc,CAAClP,MAAM,CAACsS,CAAC,IAAIA,CAAC,KAAKjQ,IAAI,CAAC;IACjE,IAAIA,IAAI,CAACiE,MAAM,EAAE;MACb,MAAMD,QAAQ,GAAGhE,IAAI,CAACiE,MAAM,CAACD,QAAQ;MACrC,IAAIA,QAAQ,CAACqN,KAAK,CAACpB,CAAC,IAAIA,CAAC,CAAClM,QAAQ,CAAC,EAAE;QACjC,IAAI,CAAC8I,cAAc,GAAG,IAAI,CAACA,cAAc,CAAClP,MAAM,CAACsS,CAAC,IAAIA,CAAC,KAAKjQ,IAAI,CAACiE,MAAM,CAAC;MAC5E;IACJ,CAAC,MACI,IAAIjE,IAAI,CAACgE,QAAQ,EAAE;MACpB,IAAI,CAAC6I,cAAc,GAAG,IAAI,CAACpF,aAAa,CAAC9J,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAAChM,MAAM,KAAKjE,IAAI,CAAC;IAC3E;EACJ;EACAsP,gBAAgBA,CAACgC,MAAM,EAAEC,GAAG,EAAE;IAC1B,MAAMrR,KAAK,GAAGmM,iBAAiB,CAACkF,GAAG,CAACrR,KAAK,CAAC,CAACiP,iBAAiB,CAAC,CAAC;IAC9D,OAAOjP,KAAK,CAACoQ,OAAO,CAACgB,MAAM,CAAC,GAAG,CAAC,CAAC;EACrC;EACAE,iBAAiBA,CAACC,KAAK,EAAE;IACrB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAQ,IAAI,CAAC3E,YAAY,IAAI,IAAI,CAACD,cAAc,CAAC7E,MAAM,GAAG,CAAC,GAAI,CAAC,GAAI,IAAI,CAAC8E,YAAY,GAAG,CAAE;IAC9F;IACA,OAAQ,IAAI,CAACA,YAAY,IAAI,CAAC,GAAK,IAAI,CAACD,cAAc,CAAC7E,MAAM,GAAG,CAAC,GAAK,IAAI,CAAC8E,YAAY,GAAG,CAAE;EAChG;EACAqD,WAAWA,CAACsB,KAAK,EAAE;IACf,IAAI,IAAI,CAAC5E,cAAc,CAAC7E,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC6E,cAAc,CAACwE,KAAK,CAACpB,CAAC,IAAIA,CAAC,CAACnO,QAAQ,CAAC,EAAE;MAChF;IACJ;IACA,IAAI,CAACgL,YAAY,GAAG,IAAI,CAAC0E,iBAAiB,CAACC,KAAK,CAAC;IACjD,IAAI,IAAI,CAACtN,UAAU,CAACrC,QAAQ,EAAE;MAC1B,IAAI,CAACqO,WAAW,CAACsB,KAAK,CAAC;IAC3B;EACJ;EACAf,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAChE,SAAS,CAACO,YAAY,EAAE;MAC7B,OAAO,CAAC,CAAC;IACb;IACA,IAAI,IAAI,CAACH,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC3I,UAAU,KAAKuE,SAAS,EAAE;MACzD,OAAO,CAAC,CAAC;IACb;IACA,MAAMgJ,aAAa,GAAG,IAAI,CAAC7E,cAAc,CAACyD,OAAO,CAAC,IAAI,CAAClD,gBAAgB,CAAC;IACxE,IAAI,IAAI,CAACA,gBAAgB,IAAIsE,aAAa,GAAG,CAAC,EAAE;MAC5C,OAAO,CAAC,CAAC;IACb;IACA,OAAOzF,IAAI,CAAC0F,GAAG,CAAC,IAAI,CAAC5E,WAAW,EAAE2E,aAAa,CAAC;EACpD;EACAhE,QAAQA,CAACtN,KAAK,EAAEwR,IAAI,EAAE;IAClB,MAAMC,MAAM,GAAG,IAAIjE,GAAG,CAAC,CAAC;IACxB,IAAIxN,KAAK,CAAC4H,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO6J,MAAM;IACjB;IACA;IACA,IAAIrC,KAAK,CAACsC,OAAO,CAAC1R,KAAK,CAAC,CAAC,CAAC,CAAC6B,KAAK,CAAC2P,IAAI,CAAC,CAAC,EAAE;MACrC,KAAK,MAAM5R,IAAI,IAAII,KAAK,EAAE;QACtB,MAAM4D,QAAQ,GAAG,CAAChE,IAAI,CAACiC,KAAK,CAAC2P,IAAI,CAAC,IAAI,EAAE,EAAEhU,GAAG,CAAC,CAACqS,CAAC,EAAEzP,KAAK,KAAK,IAAI,CAAC+M,OAAO,CAAC0C,CAAC,EAAEzP,KAAK,CAAC,CAAC;QACnFqR,MAAM,CAAChE,GAAG,CAAC7N,IAAI,EAAEgE,QAAQ,CAAC;MAC9B;MACA,OAAO6N,MAAM;IACjB;IACA,MAAME,OAAO,GAAGjJ,UAAU,CAAC,IAAI,CAAC4D,SAAS,CAACc,OAAO,CAAC;IAClD,MAAMwE,KAAK,GAAIhS,IAAI,IAAK;MACpB,MAAMuP,GAAG,GAAGwC,OAAO,GAAGH,IAAI,CAAC5R,IAAI,CAACiC,KAAK,CAAC,GAAGjC,IAAI,CAACiC,KAAK,CAAC2P,IAAI,CAAC;MACzD,OAAOnJ,SAAS,CAAC8G,GAAG,CAAC,GAAGA,GAAG,GAAG7G,SAAS;IAC3C,CAAC;IACD;IACA,KAAK,MAAM1I,IAAI,IAAII,KAAK,EAAE;MACtB,MAAMmP,GAAG,GAAGyC,KAAK,CAAChS,IAAI,CAAC;MACvB,MAAMiS,KAAK,GAAGJ,MAAM,CAACjC,GAAG,CAACL,GAAG,CAAC;MAC7B,IAAI0C,KAAK,EAAE;QACPA,KAAK,CAACrD,IAAI,CAAC5O,IAAI,CAAC;MACpB,CAAC,MACI;QACD6R,MAAM,CAAChE,GAAG,CAAC0B,GAAG,EAAE,CAACvP,IAAI,CAAC,CAAC;MAC3B;IACJ;IACA,OAAO6R,MAAM;EACjB;EACAlE,QAAQA,CAACkE,MAAM,EAAE;IACb,MAAMK,WAAW,GAAGpJ,UAAU,CAAC,IAAI,CAAC4D,SAAS,CAACc,OAAO,CAAC;IACtD,MAAMpN,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMmP,GAAG,IAAIC,KAAK,CAACC,IAAI,CAACoC,MAAM,CAACnC,IAAI,CAAC,CAAC,CAAC,EAAE;MACzC,IAAIrC,CAAC,GAAGjN,KAAK,CAAC4H,MAAM;MACpB,IAAIuH,GAAG,KAAK7G,SAAS,EAAE;QACnB,MAAMyJ,YAAY,GAAGN,MAAM,CAACjC,GAAG,CAAClH,SAAS,CAAC,IAAI,EAAE;QAChDtI,KAAK,CAACwO,IAAI,CAAC,GAAGuD,YAAY,CAACvU,GAAG,CAACqS,CAAC,IAAI;UAChCA,CAAC,CAACzP,KAAK,GAAG6M,CAAC,EAAE;UACb,OAAO4C,CAAC;QACZ,CAAC,CAAC,CAAC;QACH;MACJ;MACA,MAAMmC,WAAW,GAAGzJ,QAAQ,CAAC4G,GAAG,CAAC;MACjC,MAAMtL,MAAM,GAAG;QACX/D,KAAK,EAAEkS,WAAW,GAAG,EAAE,GAAGC,MAAM,CAAC9C,GAAG,CAAC;QACrCvL,QAAQ,EAAE0E,SAAS;QACnBzE,MAAM,EAAE,IAAI;QACZzD,KAAK,EAAE6M,CAAC,EAAE;QACVvL,QAAQ,EAAE,CAAC,IAAI,CAAC4K,SAAS,CAAC4F,eAAe;QACzCjO,MAAM,EAAE0H,KAAK,CAAC;MAClB,CAAC;MACD,MAAMwG,QAAQ,GAAGL,WAAW,GAAG,IAAI,CAACxF,SAAS,CAAC8B,SAAS,GAAG,IAAI,CAAC9B,SAAS,CAACc,OAAO;MAChF,MAAMgF,UAAU,GAAG,IAAI,CAAC9F,SAAS,CAAC8F,UAAU,KAAK,MAAM;QACnD,IAAIJ,WAAW,EAAE;UACb,OAAO7C,GAAG,CAACtN,KAAK;QACpB;QACA,OAAO;UAAE,CAACsQ,QAAQ,GAAGhD;QAAI,CAAC;MAC9B,CAAC,CAAC;MACF,MAAMvL,QAAQ,GAAG6N,MAAM,CAACjC,GAAG,CAACL,GAAG,CAAC,CAAC3R,GAAG,CAACqS,CAAC,IAAI;QACtCA,CAAC,CAAChM,MAAM,GAAGA,MAAM;QACjBgM,CAAC,CAACjM,QAAQ,GAAG0E,SAAS;QACtBuH,CAAC,CAACzP,KAAK,GAAG6M,CAAC,EAAE;QACb,OAAO4C,CAAC;MACZ,CAAC,CAAC;MACFhM,MAAM,CAACD,QAAQ,GAAGA,QAAQ;MAC1BC,MAAM,CAAChC,KAAK,GAAGuQ,UAAU,CAACjD,GAAG,EAAEvL,QAAQ,CAACpG,GAAG,CAACqS,CAAC,IAAIA,CAAC,CAAChO,KAAK,CAAC,CAAC;MAC1D7B,KAAK,CAACwO,IAAI,CAAC3K,MAAM,CAAC;MAClB7D,KAAK,CAACwO,IAAI,CAAC,GAAG5K,QAAQ,CAAC;IAC3B;IACA,OAAO5D,KAAK;EAChB;AACJ;AAEA,IAAIqS,OAAO;AACX,CAAC,UAAUA,OAAO,EAAE;EAChBA,OAAO,CAACA,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACnCA,OAAO,CAACA,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACxCA,OAAO,CAACA,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK;EACpCA,OAAO,CAACA,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACxCA,OAAO,CAACA,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS;EAC5CA,OAAO,CAACA,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;EAChDA,OAAO,CAACA,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;AACnD,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7B,MAAMC,sBAAsB,CAAC;EACzBzJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0J,WAAW,GAAG;MACfC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,gBAAgB,EAAE;IACtB,CAAC;EACL;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,WAAW;EAC3B;EACAK,cAAcA,CAACC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAE;IAC3C,MAAMC,CAAC,GAAG,IAAI,CAACT,WAAW;IAC1B,MAAMU,YAAY,GAAGD,CAAC,CAACR,UAAU,GAAGM,WAAW;IAC/C,MAAMI,SAAS,GAAGrH,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAEsB,SAAS,CAAC;IACxC,MAAMM,gBAAgB,GAAGD,SAAS,GAAGD,YAAY,GAAGH,WAAW;IAC/D,IAAIM,GAAG,GAAGvH,IAAI,CAACwH,GAAG,CAACP,WAAW,EAAEjH,IAAI,CAACyH,IAAI,CAACH,gBAAgB,CAAC,IAAIH,CAAC,CAACN,gBAAgB,GAAG,CAAC,CAAC,CAAC;IACvF,MAAMa,WAAW,GAAGH,GAAG;IACvB,MAAMI,QAAQ,GAAG3H,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAEgC,WAAW,GAAGP,CAAC,CAACN,gBAAgB,CAAC;IAC9D,IAAIe,KAAK,GAAG5H,IAAI,CAACwH,GAAG,CAACG,QAAQ,EAAE3H,IAAI,CAAC6H,KAAK,CAACP,gBAAgB,CAAC,CAAC;IAC5D,IAAIQ,UAAU,GAAGX,CAAC,CAACR,UAAU,GAAG3G,IAAI,CAACyH,IAAI,CAACG,KAAK,CAAC,GAAIT,CAAC,CAACR,UAAU,GAAG3G,IAAI,CAACwH,GAAG,CAACI,KAAK,EAAEV,MAAM,CAAE;IAC3FY,UAAU,GAAG,CAACC,KAAK,CAACD,UAAU,CAAC,GAAGA,UAAU,GAAG,CAAC;IAChDF,KAAK,GAAG,CAACG,KAAK,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;IAClCL,GAAG,GAAG,CAACQ,KAAK,CAACR,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;IAC5BK,KAAK,IAAIV,MAAM;IACfU,KAAK,GAAG5H,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAEkC,KAAK,CAAC;IAC1BL,GAAG,IAAIL,MAAM;IACbK,GAAG,GAAGvH,IAAI,CAACwH,GAAG,CAACP,WAAW,EAAEM,GAAG,CAAC;IAChC,OAAO;MACHO,UAAU;MACVV,YAAY;MACZQ,KAAK;MACLL;IACJ,CAAC;EACL;EACAS,aAAaA,CAACrB,UAAU,EAAEC,WAAW,EAAE;IACnC,MAAMC,gBAAgB,GAAG7G,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAE1F,IAAI,CAAC6H,KAAK,CAACjB,WAAW,GAAGD,UAAU,CAAC,CAAC;IAC1E,IAAI,CAACD,WAAW,GAAG;MACfC,UAAU;MACVC,WAAW;MACXC;IACJ,CAAC;EACL;EACAoB,WAAWA,CAACC,OAAO,EAAEvB,UAAU,EAAEwB,UAAU,EAAE;IACzC,MAAM;MAAEvB;IAAY,CAAC,GAAG,IAAI,CAACE,UAAU;IACvC,MAAMsB,UAAU,GAAGF,OAAO,GAAGvB,UAAU;IACvC,MAAM0B,GAAG,GAAGF,UAAU;IACtB,MAAMG,MAAM,GAAGD,GAAG,GAAGzB,WAAW;IAChC,IAAIA,WAAW,IAAIwB,UAAU,IAAID,UAAU,KAAKD,OAAO,EAAE;MACrD,OAAO,IAAI;IACf;IACA,IAAIE,UAAU,GAAGE,MAAM,EAAE;MACrB,OAAOD,GAAG,GAAGD,UAAU,GAAGE,MAAM;IACpC,CAAC,MACI,IAAIJ,OAAO,IAAIG,GAAG,EAAE;MACrB,OAAOH,OAAO;IAClB;IACA,OAAO,IAAI;EACf;EACA;IAAS,IAAI,CAAC1K,IAAI,YAAA+K,+BAAA7K,CAAA;MAAA,YAAAA,CAAA,IAAwF+I,sBAAsB;IAAA,CAAoD;EAAE;EACtL;IAAS,IAAI,CAAC+B,KAAK,kBA/4C6E1Y,EAAE,CAAA2Y,kBAAA;MAAAC,KAAA,EA+4CYjC,sBAAsB;MAAAkC,OAAA,EAAtBlC,sBAAsB,CAAAjJ;IAAA,EAAG;EAAE;AAC7I;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAj5CoGrO,EAAE,CAAAsO,iBAAA,CAi5CXqI,sBAAsB,EAAc,CAAC;IACpH3I,IAAI,EAAE7N;EACV,CAAC,CAAC;AAAA;AAEV,MAAM2Y,aAAa,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AACxD,MAAMC,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAGlX,uBAAuB,GAAGC,aAAa;AAC/G,MAAMkX,wBAAwB,CAAC;EAC3B/L,WAAWA,CAACgM,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,EAAE;IACjE,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACjV,KAAK,GAAG,EAAE;IACf,IAAI,CAACkV,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACjO,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC7H,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC+V,MAAM,GAAG,IAAIpZ,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC2K,MAAM,GAAG,IAAI3K,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC8K,WAAW,GAAG,IAAI9K,YAAY,CAAC,CAAC;IACrC,IAAI,CAACqZ,YAAY,GAAG,IAAIrZ,YAAY,CAAC,CAAC;IACtC,IAAI,CAACsZ,SAAS,GAAG,IAAI1X,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC2X,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,SAAS,GAAGT,WAAW,CAAC9L,aAAa;EAC9C;EACA,IAAIwM,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAI7C,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8C,YAAY;EAC5B;EACA,IAAI9C,WAAWA,CAACjR,KAAK,EAAE;IACnB,IAAIA,KAAK,KAAK,IAAI,CAAC+T,YAAY,EAAE;MAC7B,IAAI,CAACA,YAAY,GAAG/T,KAAK;MACzB,IAAI,CAACgU,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC/R,UAAU,EAAE;MACjB,MAAM;QAAEyO,UAAU;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACsC,aAAa,CAACpC,UAAU;MACjE,MAAMoD,MAAM,GAAG,IAAI,CAAChS,UAAU,CAAC3D,KAAK,GAAGoS,UAAU;MACjD,OAAOC,WAAW,GAAGsD,MAAM,GAAG,CAAC,GAAGA,MAAM;IAC5C;IACA,OAAO,CAAC;EACZ;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,OAAO,GAAG,IAAI,CAACR,SAAS,CAACS,aAAa;IAC3C,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,iBAAiB,CAAClN,aAAa;IAC3D,IAAI,CAACmN,gBAAgB,GAAG,IAAI,CAACC,gBAAgB,CAACpN,aAAa;IAC3D,IAAI,CAACqN,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAACtN,aAAa;IACzD,IAAI,CAACuN,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAClC;EACA5N,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACjJ,KAAK,EAAE;MACf,MAAM6W,MAAM,GAAG5N,OAAO,CAACjJ,KAAK;MAC5B,IAAI,CAAC8W,cAAc,CAACD,MAAM,CAACE,YAAY,EAAEF,MAAM,CAACG,WAAW,CAAC;IAChE;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC7B,SAAS,CAAC8B,QAAQ,CAAC,CAAC;IACzB,IAAI,CAAC9B,SAAS,CAAC+B,WAAW,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACjQ,QAAQ,EAAE;MACf,IAAI,CAAC0N,SAAS,CAACwC,WAAW,CAAC,IAAI,CAAC5B,SAAS,CAAC6B,UAAU,EAAE,IAAI,CAAC7B,SAAS,CAAC;IACzE;EACJ;EACA8B,QAAQA,CAAChJ,MAAM,EAAEiJ,eAAe,GAAG,KAAK,EAAE;IACtC,IAAI,CAACjJ,MAAM,EAAE;MACT;IACJ;IACA,MAAMnO,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACkQ,OAAO,CAAC3B,MAAM,CAAC;IACxC,IAAInO,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC0S,WAAW,EAAE;MACxC;IACJ;IACA,IAAIyE,QAAQ;IACZ,IAAI,IAAI,CAACtQ,aAAa,EAAE;MACpB,MAAMuL,UAAU,GAAG,IAAI,CAACuC,aAAa,CAACpC,UAAU,CAACH,UAAU;MAC3D+E,QAAQ,GAAG,IAAI,CAACxC,aAAa,CAACjB,WAAW,CAAC1T,KAAK,GAAGoS,UAAU,EAAEA,UAAU,EAAE,IAAI,CAACgD,mBAAmB,CAAC;IACvG,CAAC,MACI;MACD,MAAM5V,IAAI,GAAG,IAAI,CAAC6V,SAAS,CAACgC,aAAa,CAAE,IAAGlJ,MAAM,CAACtK,MAAO,EAAC,CAAC;MAC9D,MAAM+P,UAAU,GAAGwD,eAAe,GAAG5X,IAAI,CAAC8X,SAAS,GAAG,IAAI,CAAClC,mBAAmB;MAC9E+B,QAAQ,GAAG,IAAI,CAACxC,aAAa,CAACjB,WAAW,CAAClU,IAAI,CAAC8X,SAAS,EAAE9X,IAAI,CAAC+X,YAAY,EAAE3D,UAAU,CAAC;IAC5F;IACA,IAAI3L,SAAS,CAACkP,QAAQ,CAAC,EAAE;MACrB,IAAI,CAAClB,gBAAgB,CAACnD,SAAS,GAAGqE,QAAQ;IAC9C;EACJ;EACAK,WAAWA,CAAA,EAAG;IACV,MAAMC,KAAK,GAAG,IAAI,CAACxB,gBAAgB;IACnCwB,KAAK,CAAC3E,SAAS,GAAG2E,KAAK,CAAC5E,YAAY,GAAG4E,KAAK,CAACF,YAAY;EAC7D;EACAG,cAAcA,CAAA,EAAG;IACb,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACsC,yBAAyB,CAAC,IAAI,CAACxC,SAAS,CAAC;IACtE,IAAIhB,aAAa,CAACyD,QAAQ,CAAC,IAAI,CAACvC,gBAAgB,CAAC,EAAE;MAC/C,IAAI,CAACwC,oBAAoB,CAAC,IAAI,CAACxC,gBAAgB,CAAC;IACpD,CAAC,MACI;MACD,IAAI,CAACwC,oBAAoB,CAAC,QAAQ,CAAC;IACvC;IACA,IAAI,IAAI,CAAChR,QAAQ,EAAE;MACf,IAAI,CAAC4Q,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,CAACtC,SAAS,CAAC2C,KAAK,CAACC,OAAO,GAAG,GAAG;EACtC;EACAF,oBAAoBA,CAACzC,eAAe,EAAE;IAClCjB,aAAa,CAAC/F,OAAO,CAAEwG,QAAQ,IAAK;MAChC,MAAMoD,gBAAgB,GAAI,aAAYpD,QAAS,EAAC;MAChD,IAAI,CAACL,SAAS,CAAC0D,WAAW,CAAC,IAAI,CAAC9C,SAAS,EAAE6C,gBAAgB,CAAC;MAC5D,IAAI,CAACzD,SAAS,CAAC0D,WAAW,CAAC,IAAI,CAACtC,OAAO,EAAEqC,gBAAgB,CAAC;IAC9D,CAAC,CAAC;IACF,MAAME,aAAa,GAAI,aAAY9C,eAAgB,EAAC;IACpD,IAAI,CAACb,SAAS,CAAC4D,QAAQ,CAAC,IAAI,CAAChD,SAAS,EAAE+C,aAAa,CAAC;IACtD,IAAI,CAAC3D,SAAS,CAAC4D,QAAQ,CAAC,IAAI,CAACxC,OAAO,EAAEuC,aAAa,CAAC;EACxD;EACA/B,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC3B,KAAK,CAAC4D,iBAAiB,CAAC,MAAM;MAC/B9a,SAAS,CAAC,IAAI,CAAC0Y,gBAAgB,CAACpN,aAAa,EAAE,QAAQ,CAAC,CACnDyP,IAAI,CAACzb,SAAS,CAAC,IAAI,CAACmY,SAAS,CAAC,EAAElY,SAAS,CAAC,CAAC,EAAEuX,gBAAgB,CAAC,CAAC,CAC/DkE,SAAS,CAAEC,CAAC,IAAK;QAClB,MAAMC,IAAI,GAAGD,CAAC,CAACC,IAAI,IAAKD,CAAC,CAACE,YAAY,IAAIF,CAAC,CAACE,YAAY,CAAC,CAAE;QAC3D,MAAM7F,SAAS,GAAG,CAAC4F,IAAI,IAAIA,IAAI,CAAClR,MAAM,KAAK,CAAC,GAAGiR,CAAC,CAACG,MAAM,CAAC9F,SAAS,GAAG4F,IAAI,CAAC,CAAC,CAAC,CAAC5F,SAAS;QACrF,IAAI,CAAC+F,kBAAkB,CAAC/F,SAAS,CAAC;MACtC,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAwD,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACzB,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACH,KAAK,CAAC4D,iBAAiB,CAAC,MAAM;MAC/B7a,KAAK,CAACD,SAAS,CAAC,IAAI,CAACqX,SAAS,EAAE,YAAY,EAAE;QAAEiE,OAAO,EAAE;MAAK,CAAC,CAAC,EAAEtb,SAAS,CAAC,IAAI,CAACqX,SAAS,EAAE,WAAW,EAAE;QAAEiE,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC,CAACP,IAAI,CAACzb,SAAS,CAAC,IAAI,CAACmY,SAAS,CAAC,CAAC,CACvJuD,SAAS,CAACrS,MAAM,IAAI,IAAI,CAAC4S,aAAa,CAAC5S,MAAM,CAAC,CAAC;IACxD,CAAC,CAAC;EACN;EACA4S,aAAaA,CAAC5S,MAAM,EAAE;IAClB,IAAI,IAAI,CAAC0P,OAAO,CAACmD,QAAQ,CAAC7S,MAAM,CAACyS,MAAM,CAAC,IAAI,IAAI,CAACvD,SAAS,CAAC2D,QAAQ,CAAC7S,MAAM,CAACyS,MAAM,CAAC,EAAE;MAChF;IACJ;IACA,MAAMF,IAAI,GAAGvS,MAAM,CAACuS,IAAI,IAAKvS,MAAM,CAACwS,YAAY,IAAIxS,MAAM,CAACwS,YAAY,CAAC,CAAE;IAC1E,IAAIxS,MAAM,CAACyS,MAAM,IAAIzS,MAAM,CAACyS,MAAM,CAACK,UAAU,IAAIP,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC7C,OAAO,CAACmD,QAAQ,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAChG;IACJ;IACA,IAAI,CAAChE,KAAK,CAACwE,GAAG,CAAC,MAAM,IAAI,CAAClE,YAAY,CAACzO,IAAI,CAAC,CAAC,CAAC;EAClD;EACAmQ,cAAcA,CAAC9W,KAAK,EAAEgX,WAAW,EAAE;IAC/B,IAAI,CAAChX,KAAK,GAAGA,KAAK,IAAI,EAAE;IACxB,IAAI,CAACsV,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACxC,WAAW,GAAG9S,KAAK,CAAC4H,MAAM;IAC/B,IAAI,IAAI,CAACX,aAAa,EAAE;MACpB,IAAI,CAACsS,iBAAiB,CAACvC,WAAW,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAACwC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACC,YAAY,CAACzC,WAAW,CAAC;IAClC;EACJ;EACAyC,YAAYA,CAACzC,WAAW,EAAE;IACtB,IAAI,CAAC7B,MAAM,CAACxO,IAAI,CAAC,IAAI,CAAC3G,KAAK,CAAC;IAC5B,IAAIgX,WAAW,KAAK,KAAK,EAAE;MACvB;IACJ;IACA,IAAI,CAAClC,KAAK,CAAC4D,iBAAiB,CAAC,MAAM;MAC/BjQ,OAAO,CAACiR,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,MAAMlH,WAAW,GAAG,IAAI,CAAC4D,gBAAgB,CAACsB,YAAY;QACtD,IAAI,CAAC5C,aAAa,CAAClB,aAAa,CAAC,CAAC,EAAEpB,WAAW,CAAC;QAChD,IAAI,CAACuF,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACT,QAAQ,CAAC,IAAI,CAACxT,UAAU,EAAEiT,WAAW,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAuC,iBAAiBA,CAACvC,WAAW,EAAE;IAC3B,IAAI,CAAClC,KAAK,CAAC4D,iBAAiB,CAAC,MAAM;MAC/B,IAAI,CAACkB,kBAAkB,CAAC,CAAC,CAACD,IAAI,CAAC,MAAM;QACjC,IAAI3C,WAAW,EAAE;UACb,IAAI,CAAC6C,iBAAiB,CAAC,IAAI,CAAC/D,YAAY,CAAC;UACzC,IAAI,CAACkC,uBAAuB,CAAC,CAAC;QAClC,CAAC,MACI;UACD,IAAI,CAAC6B,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAZ,kBAAkBA,CAAC/F,SAAS,EAAE;IAC1B,IAAI,IAAI,CAACjM,aAAa,EAAE;MACpB,IAAI,CAAC4S,iBAAiB,CAAC3G,SAAS,CAAC;IACrC;IACA,IAAI,CAACsC,mBAAmB,GAAGtC,SAAS;IACpC,IAAI,CAAC4G,gBAAgB,CAAC5G,SAAS,CAAC;EACpC;EACA6G,oBAAoBA,CAACC,MAAM,EAAE;IACzB,IAAI,IAAI,CAACzE,mBAAmB,EAAE;MAC1B,IAAI,CAACY,eAAe,CAACiC,KAAK,CAAC4B,MAAM,GAAI,GAAEA,MAAO,IAAG;MACjD,IAAI,CAACzE,mBAAmB,GAAG,KAAK;IACpC;EACJ;EACAiE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACrD,eAAe,EAAE;MACvB;IACJ;IACA,IAAI,CAACA,eAAe,CAACiC,KAAK,CAAC4B,MAAM,GAAI,KAAI;EAC7C;EACAnE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACN,mBAAmB,GAAG,IAAI;EACnC;EACAsE,iBAAiBA,CAAC3G,SAAS,GAAG,IAAI,EAAE;IAChC,IAAIA,SAAS,IAAI,IAAI,CAACsC,mBAAmB,KAAKtC,SAAS,EAAE;MACrD;IACJ;IACAA,SAAS,GAAGA,SAAS,IAAI,IAAI,CAACmD,gBAAgB,CAACnD,SAAS;IACxD,MAAM+G,KAAK,GAAG,IAAI,CAAClF,aAAa,CAACnC,cAAc,CAACM,SAAS,EAAE,IAAI,CAACJ,WAAW,EAAE,IAAI,CAAC5L,YAAY,CAAC;IAC/F,IAAI,CAAC6S,oBAAoB,CAACE,KAAK,CAAChH,YAAY,CAAC;IAC7C,IAAI,CAACsD,aAAa,CAAC6B,KAAK,CAAC8B,SAAS,GAAI,cAAaD,KAAK,CAACtG,UAAW,KAAI;IACxE,IAAI,CAACmB,KAAK,CAACwE,GAAG,CAAC,MAAM;MACjB,IAAI,CAACnE,MAAM,CAACxO,IAAI,CAAC,IAAI,CAAC3G,KAAK,CAAC2P,KAAK,CAACsK,KAAK,CAACxG,KAAK,EAAEwG,KAAK,CAAC7G,GAAG,CAAC,CAAC;MAC1D,IAAI,CAAC1M,MAAM,CAACC,IAAI,CAAC;QAAE8M,KAAK,EAAEwG,KAAK,CAACxG,KAAK;QAAEL,GAAG,EAAE6G,KAAK,CAAC7G;MAAI,CAAC,CAAC;IAC5D,CAAC,CAAC;IACF,IAAI/K,SAAS,CAAC6K,SAAS,CAAC,IAAI,IAAI,CAACsC,mBAAmB,KAAK,CAAC,EAAE;MACxD,IAAI,CAACa,gBAAgB,CAACnD,SAAS,GAAGA,SAAS;MAC3C,IAAI,CAACsC,mBAAmB,GAAGtC,SAAS;IACxC;EACJ;EACA0G,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC7E,aAAa,CAACpC,UAAU,CAACH,UAAU,GAAG,CAAC,IAAI,IAAI,CAACM,WAAW,KAAK,CAAC,EAAE;MACxE,OAAOrK,OAAO,CAACiR,OAAO,CAAC,IAAI,CAAC3E,aAAa,CAACpC,UAAU,CAAC;IACzD;IACA,MAAM,CAACwH,KAAK,CAAC,GAAG,IAAI,CAACna,KAAK;IAC1B,IAAI,CAACmV,MAAM,CAACxO,IAAI,CAAC,CAACwT,KAAK,CAAC,CAAC;IACzB,OAAO1R,OAAO,CAACiR,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAChC,MAAMpL,MAAM,GAAG,IAAI,CAACkH,SAAS,CAACgC,aAAa,CAAE,IAAG0C,KAAK,CAAClW,MAAO,EAAC,CAAC;MAC/D,MAAMmW,YAAY,GAAG7L,MAAM,CAACoJ,YAAY;MACxC,IAAI,CAACxB,eAAe,CAACiC,KAAK,CAAC4B,MAAM,GAAI,GAAEI,YAAY,GAAG,IAAI,CAACtH,WAAY,IAAG;MAC1E,MAAML,WAAW,GAAG,IAAI,CAAC4D,gBAAgB,CAACsB,YAAY;MACtD,IAAI,CAAC5C,aAAa,CAAClB,aAAa,CAACuG,YAAY,EAAE3H,WAAW,CAAC;MAC3D,OAAO,IAAI,CAACsC,aAAa,CAACpC,UAAU;IACxC,CAAC,CAAC;EACN;EACAmH,gBAAgBA,CAAC5G,SAAS,EAAE;IACxB,IAAI,IAAI,CAACoC,iBAAiB,IAAIpC,SAAS,KAAK,CAAC,EAAE;MAC3C;IACJ;IACA,MAAMmH,OAAO,GAAG,IAAI,CAACpT,aAAa,GAC9B,IAAI,CAACkP,eAAe,GACpB,IAAI,CAACI,aAAa;IACtB,IAAIrD,SAAS,GAAG,IAAI,CAACuC,SAAS,CAACkC,YAAY,IAAI0C,OAAO,CAAC1C,YAAY,GAAG,CAAC,EAAE;MACrE,IAAI,CAAC7C,KAAK,CAACwE,GAAG,CAAC,MAAM,IAAI,CAACzS,WAAW,CAACF,IAAI,CAAC,CAAC,CAAC;MAC7C,IAAI,CAAC2O,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA2C,yBAAyBA,CAACqC,UAAU,EAAE;IAClC,IAAI,IAAI,CAACpF,QAAQ,KAAK,MAAM,EAAE;MAC1B,OAAO,IAAI,CAACA,QAAQ;IACxB;IACA,MAAMqF,UAAU,GAAG,IAAI,CAACtE,OAAO,CAACuE,qBAAqB,CAAC,CAAC;IACvD,MAAMtH,SAAS,GAAGuH,QAAQ,CAACC,eAAe,CAACxH,SAAS,IAAIuH,QAAQ,CAACE,IAAI,CAACzH,SAAS;IAC/E,MAAMwE,SAAS,GAAG6C,UAAU,CAACrG,GAAG,GAAG0G,MAAM,CAACC,WAAW;IACrD,MAAMb,MAAM,GAAGO,UAAU,CAACP,MAAM;IAChC,MAAMc,cAAc,GAAGR,UAAU,CAACE,qBAAqB,CAAC,CAAC,CAACR,MAAM;IAChE,IAAItC,SAAS,GAAGsC,MAAM,GAAGc,cAAc,GAAG5H,SAAS,GAAGuH,QAAQ,CAACC,eAAe,CAAC/C,YAAY,EAAE;MACzF,OAAO,KAAK;IAChB,CAAC,MACI;MACD,OAAO,QAAQ;IACnB;EACJ;EACAhB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACxP,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAAC4T,OAAO,GAAGN,QAAQ,CAAChD,aAAa,CAAC,IAAI,CAACtQ,QAAQ,CAAC;IACpD,IAAI,CAAC,IAAI,CAAC4T,OAAO,EAAE;MACf,MAAM,IAAIC,KAAK,CAAE,qBAAoB,IAAI,CAAC7T,QAAS,mCAAkC,CAAC;IAC1F;IACA,IAAI,CAAC8T,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACF,OAAO,CAACG,WAAW,CAAC,IAAI,CAACzF,SAAS,CAAC;EAC5C;EACAwF,gBAAgBA,CAAA,EAAG;IACf,MAAMvN,MAAM,GAAG,IAAI,CAACuI,OAAO,CAACuE,qBAAqB,CAAC,CAAC;IACnD,MAAM3W,MAAM,GAAG,IAAI,CAACkX,OAAO,CAACP,qBAAqB,CAAC,CAAC;IACnD,MAAMW,UAAU,GAAGzN,MAAM,CAAC0N,IAAI,GAAGvX,MAAM,CAACuX,IAAI;IAC5C,IAAI,CAAC3F,SAAS,CAAC2C,KAAK,CAACgD,IAAI,GAAGD,UAAU,GAAG,IAAI;IAC7C,IAAI,CAAC1F,SAAS,CAAC2C,KAAK,CAACiD,KAAK,GAAG3N,MAAM,CAAC2N,KAAK,GAAG,IAAI;IAChD,IAAI,CAAC5F,SAAS,CAAC2C,KAAK,CAACkD,QAAQ,GAAG5N,MAAM,CAAC2N,KAAK,GAAG,IAAI;EACvD;EACAtD,gBAAgBA,CAAA,EAAG;IACf,MAAMrK,MAAM,GAAG,IAAI,CAACuI,OAAO,CAACuE,qBAAqB,CAAC,CAAC;IACnD,MAAM3W,MAAM,GAAG,IAAI,CAACkX,OAAO,CAACP,qBAAqB,CAAC,CAAC;IACnD,MAAMe,KAAK,GAAG7N,MAAM,CAACsM,MAAM;IAC3B,IAAI,IAAI,CAACrE,gBAAgB,KAAK,KAAK,EAAE;MACjC,MAAM6F,YAAY,GAAG3X,MAAM,CAACsQ,MAAM,GAAGzG,MAAM,CAACyG,MAAM;MAClD,IAAI,CAACsB,SAAS,CAAC2C,KAAK,CAACjE,MAAM,GAAGqH,YAAY,GAAGD,KAAK,GAAG,IAAI;MACzD,IAAI,CAAC9F,SAAS,CAAC2C,KAAK,CAAClE,GAAG,GAAG,MAAM;IACrC,CAAC,MACI,IAAI,IAAI,CAACyB,gBAAgB,KAAK,QAAQ,EAAE;MACzC,MAAM+B,SAAS,GAAGhK,MAAM,CAACwG,GAAG,GAAGrQ,MAAM,CAACqQ,GAAG;MACzC,IAAI,CAACuB,SAAS,CAAC2C,KAAK,CAAClE,GAAG,GAAGwD,SAAS,GAAG6D,KAAK,GAAG,IAAI;MACnD,IAAI,CAAC9F,SAAS,CAAC2C,KAAK,CAACjE,MAAM,GAAG,MAAM;IACxC;EACJ;EACAyC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC9B,KAAK,CAAC4D,iBAAiB,CAAC,MAAM;MAC/B9a,SAAS,CAAC,IAAI,CAAC6X,SAAS,EAAE,WAAW,CAAC,CACjCkD,IAAI,CAACzb,SAAS,CAAC,IAAI,CAACmY,SAAS,CAAC,CAAC,CAC/BuD,SAAS,CAAE6C,KAAK,IAAK;QACtB,MAAMzC,MAAM,GAAGyC,KAAK,CAACzC,MAAM;QAC3B,IAAIA,MAAM,CAAC0C,OAAO,KAAK,OAAO,EAAE;UAC5B;QACJ;QACAD,KAAK,CAACE,cAAc,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACtS,IAAI,YAAAuS,iCAAArS,CAAA;MAAA,YAAAA,CAAA,IAAwFqL,wBAAwB,EAjtDlCjZ,EAAE,CAAA6N,iBAAA,CAitDkD7N,EAAE,CAACkgB,SAAS,GAjtDhElgB,EAAE,CAAA6N,iBAAA,CAitD2E7N,EAAE,CAACmgB,MAAM,GAjtDtFngB,EAAE,CAAA6N,iBAAA,CAitDiG8I,sBAAsB,GAjtDzH3W,EAAE,CAAA6N,iBAAA,CAitDoI7N,EAAE,CAACK,UAAU,GAjtDnJL,EAAE,CAAA6N,iBAAA,CAitD8JzL,QAAQ;IAAA,CAA4D;EAAE;EACtU;IAAS,IAAI,CAACge,IAAI,kBAltD8EpgB,EAAE,CAAAqgB,iBAAA;MAAArS,IAAA,EAktDJiL,wBAAwB;MAAAhL,SAAA;MAAAqS,SAAA,WAAAC,+BAAAzd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAltDtB9C,EAAE,CAAAwgB,WAAA,CAAAle,GAAA,KAktDihBjC,UAAU;UAltD7hBL,EAAE,CAAAwgB,WAAA,CAAAje,GAAA,KAktD8oBlC,UAAU;UAltD1pBL,EAAE,CAAAwgB,WAAA,CAAAhe,GAAA,KAktD6wBnC,UAAU;QAAA;QAAA,IAAAyC,EAAA;UAAA,IAAA2d,EAAA;UAltDzxBzgB,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA8X,iBAAA,GAAA4F,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA4X,gBAAA,GAAA8F,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA0X,iBAAA,GAAAgG,EAAA,CAAAjC,KAAA;QAAA;MAAA;MAAAtQ,MAAA;QAAA7J,KAAA;QAAA+D,UAAA;QAAAmR,QAAA;QAAA/N,QAAA;QAAAD,YAAA;QAAAD,aAAA;QAAA/H,cAAA;QAAAI,cAAA;QAAAF,WAAA;MAAA;MAAAmd,OAAA;QAAApH,MAAA;QAAAzO,MAAA;QAAAG,WAAA;QAAAuO,YAAA;MAAA;MAAAtL,QAAA,GAAFnO,EAAE,CAAAoO,oBAAA;MAAAyS,kBAAA,EAAApe,GAAA;MAAAqe,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtS,QAAA,WAAAuS,kCAAAne,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAkhB,eAAA;UAAFlhB,EAAE,CAAA0F,UAAA,IAAA7C,uCAAA,gBAmtDxC,CAAC;UAntDqC7C,EAAE,CAAAgD,cAAA,eAstDvB,CAAC;UAttDoBhD,EAAE,CAAAsF,SAAA,kBAutDjC,CAAC;UAvtD8BtF,EAAE,CAAAgD,cAAA,kBAwtDlB,CAAC;UAxtDehD,EAAE,CAAAmhB,YAAA,EAytD9D,CAAC;UAztD2DnhB,EAAE,CAAAkD,YAAA,CA0tDrF,CAAC,CACL,CAAC;UA3tDsFlD,EAAE,CAAA0F,UAAA,IAAAhC,uCAAA,gBA4tDxC,CAAC;QAAA;QAAA,IAAAZ,EAAA;UA5tDqC9C,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAQ,cAmtDrE,CAAC;UAntDkEvD,EAAE,CAAAqD,SAAA,EAutDxC,CAAC;UAvtDqCrD,EAAE,CAAA8F,WAAA,kBAAA/C,GAAA,CAAAuI,aAutDxC,CAAC;UAvtDqCtL,EAAE,CAAAqD,SAAA,EAwtDnB,CAAC;UAxtDgBrD,EAAE,CAAA8F,WAAA,uBAAA/C,GAAA,CAAAuI,aAAA,IAAAvI,GAAA,CAAAsB,KAAA,CAAA4H,MAwtDnB,CAAC;UAxtDgBjM,EAAE,CAAAqD,SAAA,EA4tDrE,CAAC;UA5tDkErD,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAY,cA4tDrE,CAAC;QAAA;MAAA;MAAAyd,YAAA,GAG+Bjf,EAAE,CAACkf,IAAI,EAA6Flf,EAAE,CAACmf,gBAAgB;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyN;EAAE;AACnZ;AACA;EAAA,QAAAnT,SAAA,oBAAAA,SAAA,KAjuDoGrO,EAAE,CAAAsO,iBAAA,CAiuDX2K,wBAAwB,EAAc,CAAC;IACtHjL,IAAI,EAAE1N,SAAS;IACfiO,IAAI,EAAE,CAAC;MACCiT,eAAe,EAAEjhB,uBAAuB,CAACkhB,MAAM;MAC/CF,aAAa,EAAE/gB,iBAAiB,CAACkhB,IAAI;MACrClT,QAAQ,EAAE,mBAAmB;MAC7BE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEV,IAAI,EAAEhO,EAAE,CAACkgB;EAAU,CAAC,EAAE;IAAElS,IAAI,EAAEhO,EAAE,CAACmgB;EAAO,CAAC,EAAE;IAAEnS,IAAI,EAAE2I;EAAuB,CAAC,EAAE;IAAE3I,IAAI,EAAEhO,EAAE,CAACK;EAAW,CAAC,EAAE;IAAE2N,IAAI,EAAErB,SAAS;IAAEgV,UAAU,EAAE,CAAC;MACvJ3T,IAAI,EAAEvN;IACV,CAAC,EAAE;MACCuN,IAAI,EAAEtN,MAAM;MACZ6N,IAAI,EAAE,CAACnM,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiC,KAAK,EAAE,CAAC;MACjC2J,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEkI,UAAU,EAAE,CAAC;MACb4F,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqZ,QAAQ,EAAE,CAAC;MACXvL,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEsL,QAAQ,EAAE,CAAC;MACXwC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqL,YAAY,EAAE,CAAC;MACfyC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChB0C,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqD,cAAc,EAAE,CAAC;MACjByK,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEyD,cAAc,EAAE,CAAC;MACjBqK,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEuD,WAAW,EAAE,CAAC;MACduK,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEsZ,MAAM,EAAE,CAAC;MACTxL,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEoK,MAAM,EAAE,CAAC;MACTiD,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEuK,WAAW,EAAE,CAAC;MACd8C,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE8Y,YAAY,EAAE,CAAC;MACfzL,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEka,iBAAiB,EAAE,CAAC;MACpB7M,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqT,IAAI,EAAEvhB,UAAU;QAAEwhB,MAAM,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAAElH,gBAAgB,EAAE,CAAC;MACnB3M,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAAC,QAAQ,EAAE;QAAEqT,IAAI,EAAEvhB,UAAU;QAAEwhB,MAAM,EAAE;MAAK,CAAC;IACvD,CAAC,CAAC;IAAEpH,iBAAiB,EAAE,CAAC;MACpBzM,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqT,IAAI,EAAEvhB,UAAU;QAAEwhB,MAAM,EAAE;MAAK,CAAC;IACxD,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,iBAAiB,CAAC;EACpB,IAAI/b,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACgc,SAAS;EAAE;EACxC,IAAIhc,QAAQA,CAACG,KAAK,EAAE;IAAE,IAAI,CAAC6b,SAAS,GAAG,IAAI,CAACC,WAAW,CAAC9b,KAAK,CAAC;EAAE;EAChEgH,WAAWA,CAAC+U,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,YAAY,GAAG,IAAIlgB,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC+f,SAAS,GAAG,KAAK;EAC1B;EACA,IAAI5d,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAAC8d,UAAU,CAAC1U,aAAa,CAAC4U,WAAW,IAAI,EAAE,EAAEC,IAAI,CAAC,CAAC;EACnE;EACA/U,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACvH,QAAQ,EAAE;MAClB,IAAI,CAACmc,YAAY,CAAC3G,IAAI,CAAC;QACnBrV,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBH,QAAQ,EAAE,IAAI,CAACgc;MACnB,CAAC,CAAC;IACN;EACJ;EACAM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACle,KAAK,KAAK,IAAI,CAACme,cAAc,EAAE;MACpC,IAAI,CAACA,cAAc,GAAG,IAAI,CAACne,KAAK;MAChC,IAAI,CAAC+d,YAAY,CAAC3G,IAAI,CAAC;QACnBrV,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBH,QAAQ,EAAE,IAAI,CAACgc,SAAS;QACxB5d,KAAK,EAAE,IAAI,CAAC8d,UAAU,CAAC1U,aAAa,CAACC;MACzC,CAAC,CAAC;IACN;EACJ;EACA8N,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4G,YAAY,CAAC1G,QAAQ,CAAC,CAAC;EAChC;EACAwG,WAAWA,CAAC9b,KAAK,EAAE;IACf,OAAOA,KAAK,IAAI,IAAI,IAAK,GAAEA,KAAM,EAAC,KAAK,OAAO;EAClD;EACA;IAAS,IAAI,CAACwH,IAAI,YAAA6U,0BAAA3U,CAAA;MAAA,YAAAA,CAAA,IAAwFkU,iBAAiB,EAn0D3B9hB,EAAE,CAAA6N,iBAAA,CAm0D2C7N,EAAE,CAACK,UAAU;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAAC+f,IAAI,kBAp0D8EpgB,EAAE,CAAAqgB,iBAAA;MAAArS,IAAA,EAo0DJ8T,iBAAiB;MAAA7T,SAAA;MAAAC,MAAA;QAAAhI,KAAA;QAAAH,QAAA;MAAA;MAAAoI,QAAA,GAp0DfnO,EAAE,CAAAoO,oBAAA;MAAAyS,kBAAA,EAAApe,GAAA;MAAAqe,KAAA;MAAAC,IAAA;MAAArS,QAAA,WAAA8T,2BAAA1f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAkhB,eAAA;UAAFlhB,EAAE,CAAAmhB,YAAA,EAo0D8J,CAAC;QAAA;MAAA;MAAAI,aAAA;MAAAC,eAAA;IAAA,EAAwE;EAAE;AAC/U;AACA;EAAA,QAAAnT,SAAA,oBAAAA,SAAA,KAt0DoGrO,EAAE,CAAAsO,iBAAA,CAs0DXwT,iBAAiB,EAAc,CAAC;IAC/G9T,IAAI,EAAE1N,SAAS;IACfiO,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBgT,eAAe,EAAEjhB,uBAAuB,CAACkhB,MAAM;MAC/C/S,QAAQ,EAAG;IACf,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEV,IAAI,EAAEhO,EAAE,CAACK;EAAW,CAAC,CAAC,EAAkB;IAAE6F,KAAK,EAAE,CAAC;MACvE8H,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE6F,QAAQ,EAAE,CAAC;MACXiI,IAAI,EAAE9N;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuiB,cAAc,CAAC;EACjBvV,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1D,YAAY,GAAG,gBAAgB;IACpC,IAAI,CAACM,gBAAgB,GAAG,gBAAgB;IACxC,IAAI,CAAClB,UAAU,GAAG,UAAU;IAC5B,IAAI,CAACwB,WAAW,GAAG,YAAY;IAC/B,IAAI,CAAC/C,YAAY,GAAG,WAAW;IAC/B,IAAI,CAACqb,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,WAAW;EACjC;EACA;IAAS,IAAI,CAAClV,IAAI,YAAAmV,uBAAAjV,CAAA;MAAA,YAAAA,CAAA,IAAwF6U,cAAc;IAAA,CAAoD;EAAE;EAC9K;IAAS,IAAI,CAAC/J,KAAK,kBA/1D6E1Y,EAAE,CAAA2Y,kBAAA;MAAAC,KAAA,EA+1DY6J,cAAc;MAAA5J,OAAA,EAAd4J,cAAc,CAAA/U,IAAA;MAAAoV,UAAA,EAAc;IAAM,EAAG;EAAE;AACzJ;AACA;EAAA,QAAAzU,SAAA,oBAAAA,SAAA,KAj2DoGrO,EAAE,CAAAsO,iBAAA,CAi2DXmU,cAAc,EAAc,CAAC;IAC5GzU,IAAI,EAAE7N,UAAU;IAChBoO,IAAI,EAAE,CAAC;MAAEuU,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,MAAMC,cAAc,CAAC;EACjBC,IAAIA,CAACC,OAAO,EAAE;IACVC,OAAO,CAACF,IAAI,CAACC,OAAO,CAAC;EACzB;EACA;IAAS,IAAI,CAACvV,IAAI,YAAAyV,uBAAAvV,CAAA;MAAA,YAAAA,CAAA,IAAwFmV,cAAc;IAAA,CAAoD;EAAE;EAC9K;IAAS,IAAI,CAACrK,KAAK,kBA32D6E1Y,EAAE,CAAA2Y,kBAAA;MAAAC,KAAA,EA22DYmK,cAAc;MAAAlK,OAAA,EAAdkK,cAAc,CAAArV,IAAA;MAAAoV,UAAA,EAAc;IAAM,EAAG;EAAE;AACzJ;AACA;EAAA,QAAAzU,SAAA,oBAAAA,SAAA,KA72DoGrO,EAAE,CAAAsO,iBAAA,CA62DXyU,cAAc,EAAc,CAAC;IAC5G/U,IAAI,EAAE7N,UAAU;IAChBoO,IAAI,EAAE,CAAC;MAAEuU,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,MAAMM,uBAAuB,GAAG,IAAIviB,cAAc,CAAC,2BAA2B,CAAC;AAC/E,MAAMwiB,iBAAiB,CAAC;EACpB,IAAIhf,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACwM,MAAM;EAAE;EAElC,IAAIxM,KAAKA,CAAC6B,KAAK,EAAE;IACb,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChBA,KAAK,GAAG,EAAE;IACd;IACA,IAAI,CAACod,aAAa,GAAG,IAAI;IACzB,IAAI,CAACzS,MAAM,GAAG3K,KAAK;EACvB;EAEA,IAAIoM,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACiR,YAAY;EAAE;EAC9C,IAAIjR,WAAWA,CAACkR,EAAE,EAAE;IAChB,IAAIA,EAAE,KAAK7W,SAAS,IAAI6W,EAAE,KAAK,IAAI,IAAI,CAACzW,UAAU,CAACyW,EAAE,CAAC,EAAE;MACpD,MAAMnE,KAAK,CAAC,mCAAmC,CAAC;IACpD;IACA,IAAI,CAACkE,YAAY,GAAGC,EAAE;EAC1B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,IAAI/W,SAAS,CAAC,IAAI,CAACgX,iBAAiB,CAAC,EAAE;MACnC,OAAO,IAAI,CAACA,iBAAiB;IACjC,CAAC,MACI,IAAIhX,SAAS,CAAC,IAAI,CAACiX,MAAM,CAACF,gBAAgB,CAAC,EAAE;MAC9C,OAAO,IAAI,CAACE,MAAM,CAACF,gBAAgB;IACvC;IACA,OAAO,IAAI,CAACG,aAAa;EAC7B;EAEA,IAAIH,gBAAgBA,CAACvd,KAAK,EAAE;IACxB,IAAI,CAACwd,iBAAiB,GAAGxd,KAAK;EAClC;EAEA,IAAI2d,eAAeA,CAAA,EAAG;IAClB,IAAInX,SAAS,CAAC,IAAI,CAACoX,gBAAgB,CAAC,EAAE;MAClC,OAAO,IAAI,CAACA,gBAAgB;IAChC,CAAC,MACI,IAAIpX,SAAS,CAAC,IAAI,CAACiX,MAAM,CAACE,eAAe,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACF,MAAM,CAACE,eAAe;IACtC;IACA,OAAO,IAAI,CAACxY,QAAQ;EACxB;EAEA,IAAIwY,eAAeA,CAAC3d,KAAK,EAAE;IACvB,IAAI,CAAC4d,gBAAgB,GAAG5d,KAAK;EACjC;EAEA,IAAIH,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACge,QAAQ,IAAI,IAAI,CAAChC,SAAS;EAAE;EAEzD,IAAIiC,QAAQA,CAAA,EAAG;IAAE,OAAQ,CAAC,CAAC,IAAI,CAACphB,UAAU,IAAI,IAAI,CAACqhB,UAAU,IAAI,IAAI,CAACC,YAAY;EAAG;EAErF,IAAIC,MAAMA,CAAA,EAAG;IAAE,OAAO,CAAC,IAAI,CAAC9Y,QAAQ;EAAE;EAEtC,IAAI+Y,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAAChZ,QAAQ;EACpD;EACA6B,WAAWA,CAACvB,OAAO,EAAE2Y,SAAS,EAAEX,MAAM,EAAEY,iBAAiB,EAAElL,WAAW,EAAEmL,GAAG,EAAEC,QAAQ,EAAE;IACnF,IAAI,CAAC9Y,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2Y,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACa,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACjZ,gBAAgB,GAAG,MAAM;IAC9B,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC4X,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC1S,YAAY,GAAG,KAAK;IACzB,IAAI,CAACyT,WAAW,GAAG,KAAK;IACxB,IAAI,CAACpZ,YAAY,GAAG,CAAC;IACrB,IAAI,CAACgL,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACtE,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACqB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACsR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAAChB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiB,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACZ,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACa,SAAS,GAAIC,CAAC,IAAK,IAAI;IAC5B,IAAI,CAAC9Z,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC+Z,MAAM,GAAG,KAAK;IACnB,IAAI,CAACnB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACoB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,SAAS,GAAG,IAAInlB,YAAY,CAAC,CAAC;IACnC,IAAI,CAAColB,UAAU,GAAG,IAAIplB,YAAY,CAAC,CAAC;IACpC,IAAI,CAACqlB,WAAW,GAAG,IAAIrlB,YAAY,CAAC,CAAC;IACrC,IAAI,CAACslB,SAAS,GAAG,IAAItlB,YAAY,CAAC,CAAC;IACnC,IAAI,CAACulB,UAAU,GAAG,IAAIvlB,YAAY,CAAC,CAAC;IACpC,IAAI,CAACwlB,WAAW,GAAG,IAAIxlB,YAAY,CAAC,CAAC;IACrC,IAAI,CAACylB,UAAU,GAAG,IAAIzlB,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC0lB,QAAQ,GAAG,IAAI1lB,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC2lB,WAAW,GAAG,IAAI3lB,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC2K,MAAM,GAAG,IAAI3K,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC8K,WAAW,GAAG,IAAI9K,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC4lB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACnb,aAAa,GAAG,EAAE;IACvB,IAAI,CAACjI,UAAU,GAAG,IAAI;IACtB,IAAI,CAACgJ,UAAU,GAAGoE,KAAK,CAAC,CAAC;IACzB,IAAI,CAACzK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACsL,MAAM,GAAG,EAAE;IAChB,IAAI,CAACoV,aAAa,GAAG,OAAO;IAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAAChC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACxK,SAAS,GAAG,IAAI1X,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACmkB,UAAU,GAAG,IAAInkB,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACokB,SAAS,GAAIjB,CAAC,IAAK,CAAE,CAAC;IAC3B,IAAI,CAACkB,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAAClgB,SAAS,GAAIlC,IAAI,IAAK;MACvB,MAAM2O,MAAM,GAAG,IAAI,CAACrM,aAAa,CAACmM,IAAI,CAACwB,CAAC,IAAIA,CAAC,CAAChO,KAAK,KAAKjC,IAAI,CAAC;MAC7D,IAAI,CAACmB,QAAQ,CAACwN,MAAM,CAAC;IACzB,CAAC;IACD,IAAI,CAACpM,aAAa,GAAG,CAAC2e,CAAC,EAAElhB,IAAI,KAAK;MAC9B,IAAI,IAAI,CAAC2gB,SAAS,EAAE;QAChB,OAAO,IAAI,CAACA,SAAS,CAAC3gB,IAAI,CAACiC,KAAK,CAAC;MACrC;MACA,OAAOjC,IAAI;IACf,CAAC;IACD,IAAI,CAACqiB,kBAAkB,CAAC3C,MAAM,CAAC;IAC/B,IAAI,CAACxb,SAAS,GAAG,IAAIuI,SAAS,CAAC,IAAI,EAAE6T,iBAAiB,CAAC,CAAC,CAAC;IACzD,IAAI,CAACpX,OAAO,GAAGkM,WAAW,CAAC9L,aAAa;EAC5C;EACA,IAAIhH,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC4B,SAAS,CAAC5B,aAAa;EACvC;EACA,IAAIM,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACN,aAAa,CAAC1E,GAAG,CAACqS,CAAC,IAAIA,CAAC,CAAChO,KAAK,CAAC;EAC/C;EACA,IAAIqgB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChgB,aAAa,CAAC0F,MAAM,GAAG,CAAC;EACxC;EACA,IAAIua,oBAAoBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACC,aAAa,EAAE;MACpB,OAAO,IAAI,CAACA,aAAa,CAAC1M,eAAe;IAC7C;IACA,OAAOpN,SAAS;EACpB;EACA0N,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqM,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAtZ,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACjC,QAAQ,EAAE;MAClB,IAAI,CAAClD,SAAS,CAAC6J,aAAa,CAAC,CAAC;IAClC;IACA,IAAI1E,OAAO,CAACjJ,KAAK,EAAE;MACf,IAAI,CAACuiB,SAAS,CAACtZ,OAAO,CAACjJ,KAAK,CAAC+W,YAAY,IAAI,EAAE,CAAC;IACpD;IACA,IAAI9N,OAAO,CAACgY,MAAM,EAAE;MAChB,IAAI,CAACuB,WAAW,GAAGna,SAAS,CAACY,OAAO,CAACgY,MAAM,CAAClK,YAAY,CAAC;IAC7D;EACJ;EACA0L,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACxD,aAAa,EAAE;MACrB,IAAI,CAAC/d,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwhB,sBAAsB,CAAC,CAAC;IACjC;IACA,IAAIra,SAAS,CAAC,IAAI,CAAC4X,SAAS,CAAC,EAAE;MAC3B,IAAI,CAAC0C,KAAK,CAAC,CAAC;IAChB;EACJ;EACA1L,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC7B,SAAS,CAAC8B,QAAQ,CAAC,CAAC;EAC7B;EACAyL,aAAaA,CAACrc,MAAM,EAAE;IAClB,MAAMsc,OAAO,GAAGxQ,OAAO,CAAC9L,MAAM,CAACuc,KAAK,CAAC;IACrC,IAAID,OAAO,EAAE;MACT,IAAI,IAAI,CAAChC,SAAS,CAACta,MAAM,CAAC,KAAK,KAAK,EAAE;QAClC;MACJ;MACA,IAAI,CAACwc,aAAa,CAACxc,MAAM,CAAC;IAC9B,CAAC,MACI,IAAIA,MAAM,CAAC4I,GAAG,IAAI5I,MAAM,CAAC4I,GAAG,CAACvH,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACka,UAAU,CAAC5K,IAAI,CAAC3Q,MAAM,CAAC4I,GAAG,CAACJ,iBAAiB,CAAC,CAAC,CAAC;IACxD;EACJ;EACAgU,aAAaA,CAACxc,MAAM,EAAE;IAClB,MAAMyS,MAAM,GAAGzS,MAAM,CAACyS,MAAM;IAC5B,IAAI,IAAI,CAACgK,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC9Z,aAAa,KAAK8P,MAAM,EAAE;MAC/D,IAAI,CAACiK,kBAAkB,CAAC1c,MAAM,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAAC2c,kBAAkB,CAAC3c,MAAM,CAAC;IACnC;EACJ;EACA2c,kBAAkBA,CAAC3c,MAAM,EAAE;IACvB,QAAQA,MAAM,CAACuc,KAAK;MAChB,KAAKzQ,OAAO,CAAC8Q,SAAS;QAClB,IAAI,CAACC,gBAAgB,CAAC7c,MAAM,CAAC;QAC7B;MACJ,KAAK8L,OAAO,CAACgR,OAAO;QAChB,IAAI,CAACC,cAAc,CAAC/c,MAAM,CAAC;QAC3B;MACJ,KAAK8L,OAAO,CAACkR,KAAK;QACd,IAAI,CAACC,YAAY,CAACjd,MAAM,CAAC;QACzB;MACJ,KAAK8L,OAAO,CAACoR,KAAK;QACd,IAAI,CAACC,YAAY,CAACnd,MAAM,CAAC;QACzB;MACJ,KAAK8L,OAAO,CAACsR,GAAG;QACZ,IAAI,CAACC,UAAU,CAACrd,MAAM,CAAC;QACvB;MACJ,KAAK8L,OAAO,CAACwR,GAAG;QACZ,IAAI,CAAC9c,KAAK,CAAC,CAAC;QACZR,MAAM,CAACoV,cAAc,CAAC,CAAC;QACvB;MACJ,KAAKtJ,OAAO,CAACyR,SAAS;QAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACvB;IACR;EACJ;EACAd,kBAAkBA,CAAC1c,MAAM,EAAE;IACvB,QAAQA,MAAM,CAACuc,KAAK;MAChB,KAAKzQ,OAAO,CAACoR,KAAK;QACd,IAAI,CAACO,gBAAgB,CAAC,CAAC;QACvBzd,MAAM,CAACoV,cAAc,CAAC,CAAC;QACvB;IACR;EACJ;EACAsI,eAAeA,CAAC1d,MAAM,EAAE;IACpB,MAAMyS,MAAM,GAAGzS,MAAM,CAACyS,MAAM;IAC5B,IAAIA,MAAM,CAAC0C,OAAO,KAAK,OAAO,EAAE;MAC5BnV,MAAM,CAACoV,cAAc,CAAC,CAAC;IAC3B;IACA,IAAI3C,MAAM,CAACkL,SAAS,CAAC9K,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC/C,IAAI,CAAC4K,gBAAgB,CAAC,CAAC;MACvB;IACJ;IACA,IAAIhL,MAAM,CAACkL,SAAS,CAAC9K,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC/C,IAAI,CAAC+K,gBAAgB,CAAC,CAAC;MACvB;IACJ;IACA,IAAInL,MAAM,CAACkL,SAAS,CAAC9K,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC5C;IACJ;IACA,IAAI,CAAC,IAAI,CAACgL,OAAO,EAAE;MACf,IAAI,CAACzB,KAAK,CAAC,CAAC;IAChB;IACA,IAAI,IAAI,CAAC/C,UAAU,EAAE;MACjB,IAAI,CAACyE,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;EACJ;EACAH,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAClD,MAAM,EAAE;MACb,IAAI,CAACla,KAAK,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACsd,IAAI,CAAC,CAAC;IACf;EACJ;EACAL,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC9B,QAAQ,EAAE;MACf,IAAI,CAACpe,SAAS,CAAC6J,aAAa,CAAC,IAAI,CAAC;MAClC,IAAI,CAAC4W,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC7B,KAAK,CAAC,CAAC;IACZ,IAAI,CAACnB,UAAU,CAAC7a,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC8d,mBAAmB,CAAC,CAAC;EAC9B;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC1D,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACld,SAAS,CAAC6J,aAAa,CAAC,CAAC;IAC9B,IAAI,CAAC4W,cAAc,CAAC,CAAC;EACzB;EACAI,UAAUA,CAAC9iB,KAAK,EAAE;IACd,IAAI,CAACiC,SAAS,CAAC6J,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACiX,iBAAiB,CAAC/iB,KAAK,CAAC;IAC7B,IAAI,CAACse,GAAG,CAAC0E,YAAY,CAAC,CAAC;EAC3B;EACAC,gBAAgBA,CAAC3F,EAAE,EAAE;IACjB,IAAI,CAAC4C,SAAS,GAAG5C,EAAE;EACvB;EACA4F,iBAAiBA,CAAC5F,EAAE,EAAE;IAClB,IAAI,CAAC6C,UAAU,GAAG7C,EAAE;EACxB;EACA6F,gBAAgBA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACvH,SAAS,GAAGuH,KAAK;IACtB,IAAI,CAAC9E,GAAG,CAAC0E,YAAY,CAAC,CAAC;EAC3B;EACAP,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACrD,MAAM,EAAE;MACd,IAAI,CAACoD,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACtd,KAAK,CAAC,CAAC;IAChB;EACJ;EACAsd,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC3iB,QAAQ,IAAI,IAAI,CAACuf,MAAM,IAAI,IAAI,CAACuB,WAAW,EAAE;MAClD;IACJ;IACA,IAAI,CAAC,IAAI,CAAC0C,YAAY,IAAI,CAAC,IAAI,CAACnE,MAAM,IAAI,IAAI,CAACjd,SAAS,CAAC8I,eAAe,EAAE;MACtE;IACJ;IACA,IAAI,CAACqU,MAAM,GAAG,IAAI;IAClB,IAAI,CAACnd,SAAS,CAACqM,qBAAqB,CAAC,IAAI,CAACkQ,SAAS,CAAC;IACpD,IAAI,CAACgB,SAAS,CAAC1a,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAACpI,UAAU,EAAE;MAClB,IAAI,CAACokB,KAAK,CAAC,CAAC;IAChB;IACA,IAAI,CAACwC,aAAa,CAAC,CAAC;EACxB;EACApe,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACka,MAAM,IAAI,IAAI,CAACuB,WAAW,EAAE;MAClC;IACJ;IACA,IAAI,CAACvB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACpB,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC,IAAI,CAACE,mBAAmB,EAAE;MAC3B,IAAI,CAACyE,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAAC1gB,SAAS,CAAC8K,kBAAkB,CAAC,CAAC;IACvC;IACA,IAAI,CAAC9K,SAAS,CAACe,UAAU,CAAC,CAAC;IAC3B,IAAI,CAACmd,UAAU,CAAC,CAAC;IACjB,IAAI,CAACV,UAAU,CAAC3a,IAAI,CAAC,CAAC;IACtB,IAAI,CAACwZ,GAAG,CAAC0E,YAAY,CAAC,CAAC;EAC3B;EACAthB,UAAUA,CAAC3D,IAAI,EAAE;IACb,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC8B,QAAQ,IAAI,IAAI,CAACA,QAAQ,EAAE;MACzC;IACJ;IACA,IAAI,IAAI,CAAC8d,eAAe,IAAI5f,IAAI,CAAC+D,QAAQ,EAAE;MACvC,IAAI,CAAC5C,QAAQ,CAACnB,IAAI,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAAC8N,MAAM,CAAC9N,IAAI,CAAC;IACrB;IACA,IAAI,IAAI,CAACmgB,mBAAmB,EAAE;MAC1B,IAAI,CAACqF,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAACX,mBAAmB,CAAC,CAAC;EAC9B;EACA/W,MAAMA,CAAC9N,IAAI,EAAE;IACT,IAAI,CAACA,IAAI,CAAC+D,QAAQ,EAAE;MAChB,IAAI,CAACG,SAAS,CAAC4J,MAAM,CAAC9N,IAAI,CAAC;MAC3B,IAAI,IAAI,CAACwf,gBAAgB,IAAI,CAAC,IAAI,CAACW,mBAAmB,EAAE;QACpD,IAAI,CAACyE,YAAY,CAAC,CAAC;MACvB;MACA,IAAI,CAACD,cAAc,CAAC,CAAC;MACrB,IAAI,IAAI,CAACvd,QAAQ,EAAE;QACf,IAAI,CAACya,QAAQ,CAAC9a,IAAI,CAAC/G,IAAI,CAACiC,KAAK,CAAC;MAClC;IACJ;IACA,IAAI,IAAI,CAAC0d,aAAa,IAAI,IAAI,CAACzb,SAAS,CAAC8I,eAAe,EAAE;MACtD,IAAI,CAAC7F,KAAK,CAAC,CAAC;IAChB;EACJ;EACA4b,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC0C,WAAW,CAACnc,aAAa,CAACyZ,KAAK,CAAC,CAAC;EAC1C;EACA2C,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,WAAW,CAACnc,aAAa,CAACoc,IAAI,CAAC,CAAC;EACzC;EACAvkB,QAAQA,CAACnB,IAAI,EAAE;IACX,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACkE,SAAS,CAAC/C,QAAQ,CAACnB,IAAI,CAAC;IAC7B,IAAI,CAAC+iB,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC4B,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC7C,WAAW,CAAC/a,IAAI,CAAC/G,IAAI,CAACiC,KAAK,CAAC;EACrC;EACAkD,SAASA,CAAA,EAAG;IACR,IAAIwgB,GAAG;IACP,IAAI7c,UAAU,CAAC,IAAI,CAACqY,MAAM,CAAC,EAAE;MACzBwE,GAAG,GAAG,IAAI,CAACxE,MAAM,CAAC,IAAI,CAACxiB,UAAU,CAAC;IACtC,CAAC,MACI;MACDgnB,GAAG,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjnB,UAAU,GAAG;QAAE,CAAC,IAAI,CAAC6P,SAAS,GAAG,IAAI,CAAC7P;MAAW,CAAC;IACnF;IACA,MAAMknB,SAAS,GAAI7lB,IAAI,IAAK,IAAI,CAACslB,YAAY,IAAI,CAAC,IAAI,CAACjE,MAAM,GAAG,IAAI,CAACnd,SAAS,CAACqJ,OAAO,CAACvN,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAACkE,SAAS,CAACwK,OAAO,CAAC1O,IAAI,CAAC;IACjI,IAAI4I,SAAS,CAAC+c,GAAG,CAAC,EAAE;MAChBA,GAAG,CAAC5L,IAAI,CAAC/Z,IAAI,IAAI,IAAI,CAAC8N,MAAM,CAAC+X,SAAS,CAAC7lB,IAAI,CAAC,CAAC,CAAC,CAAC8lB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACnE,CAAC,MACI,IAAIH,GAAG,EAAE;MACV,IAAI,CAAC7X,MAAM,CAAC+X,SAAS,CAACF,GAAG,CAAC,CAAC;IAC/B;EACJ;EACAI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC3E,SAAS,KAAK,IAAI,CAACkB,QAAQ,IAAI,IAAI,CAAC3jB,UAAU,CAAC,IAAI,CAAC,IAAI,CAACmD,QAAQ;EACjF;EACAkkB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACN,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACtC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC9Z,aAAa,CAACyZ,KAAK,CAAC,CAAC;IAC1C;EACJ;EACA,IAAInb,UAAUA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACqe,UAAU,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,MAAM/W,IAAI,GAAG,IAAI,CAACvQ,UAAU,CAACunB,WAAW,CAAC,CAAC,CAAC/H,IAAI,CAAC,CAAC;IACjD,OAAO,IAAI,CAACgD,MAAM,IACb,CAAC,IAAI,CAACjd,SAAS,CAACuD,aAAa,CAAC0e,IAAI,CAAClW,CAAC,IAAIA,CAAC,CAAC/P,KAAK,CAACgmB,WAAW,CAAC,CAAC,KAAKhX,IAAI,CAAC,KACnE,CAAC,IAAI,CAACjC,YAAY,IAAI,IAAI,CAACoU,MAAM,IAAI,CAAC,IAAI,CAAC/e,aAAa,CAAC6jB,IAAI,CAAClW,CAAC,IAAIA,CAAC,CAAC/P,KAAK,CAACgmB,WAAW,CAAC,CAAC,KAAKhX,IAAI,CAAC,CAAE,IACzG,CAAC,IAAI,CAACnH,OAAO;EACrB;EACAF,gBAAgBA,CAAA,EAAG;IACf,MAAMue,KAAK,GAAG,IAAI,CAACliB,SAAS,CAACuD,aAAa,CAACO,MAAM,KAAK,CAAC;IACvD,OAAO,CAAEoe,KAAK,IAAI,CAAC,IAAI,CAACd,YAAY,IAAI,CAAC,IAAI,CAACvd,OAAO,IAChDqe,KAAK,IAAI,IAAI,CAACd,YAAY,IAAI,IAAI,CAACW,UAAU,IAAI,CAAC,IAAI,CAACle,OAAQ,KAChE,CAAC,IAAI,CAACH,UAAU;EACxB;EACAE,gBAAgBA,CAAA,EAAG;IACf,MAAMse,KAAK,GAAG,IAAI,CAACliB,SAAS,CAACuD,aAAa,CAACO,MAAM,KAAK,CAAC;IACvD,OAAOoe,KAAK,IAAI,IAAI,CAACd,YAAY,IAAI,CAAC,IAAI,CAACW,UAAU,IAAI,CAAC,IAAI,CAACle,OAAO;EAC1E;EACAse,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpG,YAAY,GAAG,IAAI;EAC5B;EACAqG,gBAAgBA,CAACpX,IAAI,EAAE;IACnB,IAAI,CAAC+Q,YAAY,GAAG,KAAK;IACzB,IAAI,IAAI,CAACc,oBAAoB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACpjB,MAAM,CAACuR,IAAI,CAAC;EACrB;EACAvR,MAAMA,CAACuR,IAAI,EAAE;IACT,IAAI,IAAI,CAAC+Q,YAAY,IAAI,CAAC,IAAI,CAACc,oBAAoB,EAAE;MACjD;IACJ;IACA,IAAI,CAACpiB,UAAU,GAAGuQ,IAAI;IACtB,IAAI,IAAI,CAACoW,YAAY,KAAK,IAAI,CAACW,UAAU,IAAI,IAAI,CAACjF,aAAa,KAAK,CAAC,CAAC,EAAE;MACpE,IAAI,CAACuF,SAAS,CAACjP,IAAI,CAACpI,IAAI,CAAC;IAC7B;IACA,IAAI,CAAC,IAAI,CAACoW,YAAY,EAAE;MACpB,IAAI,CAACphB,SAAS,CAACvG,MAAM,CAAC,IAAI,CAACgB,UAAU,CAAC;MACtC,IAAI,IAAI,CAAC0iB,MAAM,EAAE;QACb,IAAI,CAACnd,SAAS,CAACqM,qBAAqB,CAAC,IAAI,CAACkQ,SAAS,CAAC;MACxD;IACJ;IACA,IAAI,CAACkB,WAAW,CAAC5a,IAAI,CAAC;MAAEmI,IAAI;MAAE9O,KAAK,EAAE,IAAI,CAAC8D,SAAS,CAACuD,aAAa,CAAC7J,GAAG,CAACqS,CAAC,IAAIA,CAAC,CAAChO,KAAK;IAAE,CAAC,CAAC;IACtF,IAAI,CAACwiB,IAAI,CAAC,CAAC;EACf;EACA+B,YAAYA,CAAC7f,MAAM,EAAE;IACjB,IAAI,IAAI,CAAC6d,OAAO,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACrE,mBAAmB,EAAE;MAC1B,IAAI,CAACqF,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAACtc,OAAO,CAACob,SAAS,CAACmC,GAAG,CAAC,mBAAmB,CAAC;IAC/C,IAAI,CAAClF,UAAU,CAACxa,IAAI,CAACJ,MAAM,CAAC;IAC5B,IAAI,CAAC6d,OAAO,GAAG,IAAI;EACvB;EACAkC,WAAWA,CAAC/f,MAAM,EAAE;IAChB,IAAI,CAACuC,OAAO,CAACob,SAAS,CAACqC,MAAM,CAAC,mBAAmB,CAAC;IAClD,IAAI,CAACrF,SAAS,CAACva,IAAI,CAACJ,MAAM,CAAC;IAC3B,IAAI,CAAC,IAAI,CAAC0a,MAAM,IAAI,CAAC,IAAI,CAACvf,QAAQ,EAAE;MAChC,IAAI,CAACsgB,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,IAAI,CAACjC,mBAAmB,EAAE;MAC1B,IAAI,CAACqF,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAAChB,OAAO,GAAG,KAAK;EACxB;EACA3gB,WAAWA,CAAC7D,IAAI,EAAE;IACd,IAAIA,IAAI,CAAC8B,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACoC,SAAS,CAACmM,QAAQ,CAACrQ,IAAI,CAAC;EACjC;EACAulB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAChF,GAAG,CAACqG,SAAS,EAAE;MACrB,IAAI,CAACrG,GAAG,CAACgF,aAAa,CAAC,CAAC;IAC5B;EACJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB,MAAMzhB,QAAQ,GAAG,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC3D,UAAU,GAAIoF,QAAQ,IAAIA,QAAQ,CAAC7D,KAAK,IAAK,IAAI;EAC1D;EACAyiB,SAASA,CAACviB,KAAK,EAAE;IACb,MAAMymB,SAAS,GAAGzmB,KAAK,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACoO,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI,CAACwT,aAAa;IACrD,IAAI,CAAC4D,UAAU,GAAGnd,SAAS,CAACoe,SAAS,CAAC,GAAG,CAACle,QAAQ,CAACke,SAAS,CAAC,GAAG,IAAI,CAACjB,UAAU,IAAI,IAAI,CAACpX,SAAS,KAAK,IAAI,CAACwT,aAAa;IACxH,IAAI,CAAC9d,SAAS,CAACoJ,QAAQ,CAAClN,KAAK,CAAC;IAC9B,IAAIA,KAAK,CAAC4H,MAAM,GAAG,CAAC,IAAI,IAAI,CAACsa,QAAQ,EAAE;MACnC,IAAI,CAACpe,SAAS,CAAC8M,gBAAgB,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAACqQ,MAAM,IAAI5Y,SAAS,CAAC,IAAI,CAAC9J,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC2mB,YAAY,EAAE;MACjE,IAAI,CAACphB,SAAS,CAACvG,MAAM,CAAC,IAAI,CAACgB,UAAU,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC2mB,YAAY,IAAI,IAAI,CAACjE,MAAM,EAAE;MAClC,IAAI,CAACnd,SAAS,CAACqM,qBAAqB,CAAC,IAAI,CAACkQ,SAAS,CAAC;IACxD;EACJ;EACAqC,sBAAsBA,CAAA,EAAG;IACrB,MAAMgE,YAAY,GAAIC,OAAO,IAAK;MAC9B,IAAI,CAAC3mB,KAAK,GAAG2mB,OAAO,CAACnpB,GAAG,CAAC+Q,MAAM,KAAK;QAChCoC,cAAc,EAAEpC,MAAM,CAAC1M,KAAK;QAC5B6O,cAAc,EAAEnC,MAAM,CAACqP,UAAU,CAAC1U,aAAa,CAACC,SAAS;QACzDzH,QAAQ,EAAE6M,MAAM,CAAC7M;MACrB,CAAC,CAAC,CAAC;MACH,IAAI,CAACoC,SAAS,CAACoJ,QAAQ,CAAC,IAAI,CAAClN,KAAK,CAAC;MACnC,IAAI,IAAI,CAACkiB,QAAQ,EAAE;QACf,IAAI,CAACpe,SAAS,CAAC8M,gBAAgB,CAAC,CAAC;MACrC;MACA,IAAI,CAACuU,aAAa,CAAC,CAAC;IACxB,CAAC;IACD,MAAMyB,kBAAkB,GAAGA,CAAA,KAAM;MAC7B,MAAMC,kBAAkB,GAAGhpB,KAAK,CAAC,IAAI,CAACipB,SAAS,CAAC7d,OAAO,EAAE,IAAI,CAACoM,SAAS,CAAC;MACxExX,KAAK,CAAC,GAAG,IAAI,CAACipB,SAAS,CAACtpB,GAAG,CAAC+Q,MAAM,IAAIA,MAAM,CAACsP,YAAY,CAAC,CAAC,CACtDlF,IAAI,CAACzb,SAAS,CAAC2pB,kBAAkB,CAAC,CAAC,CACnCjO,SAAS,CAACrK,MAAM,IAAI;QACrB,MAAM3O,IAAI,GAAG,IAAI,CAACkE,SAAS,CAACiK,QAAQ,CAACQ,MAAM,CAAC1M,KAAK,CAAC;QAClDjC,IAAI,CAAC8B,QAAQ,GAAG6M,MAAM,CAAC7M,QAAQ;QAC/B9B,IAAI,CAACE,KAAK,GAAGyO,MAAM,CAACzO,KAAK,IAAIF,IAAI,CAACE,KAAK;QACvC,IAAI,CAACqgB,GAAG,CAACgF,aAAa,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC2B,SAAS,CAAC7d,OAAO,CACjB0P,IAAI,CAACvb,SAAS,CAAC,IAAI,CAAC0pB,SAAS,CAAC,EAAE5pB,SAAS,CAAC,IAAI,CAACmY,SAAS,CAAC,CAAC,CAC1DuD,SAAS,CAAC+N,OAAO,IAAI;MACtB,IAAI,CAACvY,SAAS,GAAG,IAAI,CAACwT,aAAa;MACnC8E,YAAY,CAACC,OAAO,CAAC;MACrBC,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACAG,kBAAkBA,CAACllB,KAAK,EAAE;IACtB,IAAI,CAACwG,SAAS,CAACxG,KAAK,CAAC,IAAK,IAAI,CAACmF,QAAQ,IAAInF,KAAK,KAAK,EAAG,IAAIuN,KAAK,CAACsC,OAAO,CAAC7P,KAAK,CAAC,IAAIA,KAAK,CAAC+F,MAAM,KAAK,CAAC,EAAE;MACpG,OAAO,KAAK;IAChB;IACA,MAAMof,eAAe,GAAIpnB,IAAI,IAAK;MAC9B,IAAI,CAACyI,SAAS,CAAC,IAAI,CAAC4F,WAAW,CAAC,IAAI1F,QAAQ,CAAC3I,IAAI,CAAC,IAAI,IAAI,CAACsO,SAAS,EAAE;QAClE,IAAI,CAACkS,QAAQ,CAACzB,IAAI,CAAE,kBAAiBsI,IAAI,CAACC,SAAS,CAACtnB,IAAI,CAAE,6EAA4E,CAAC;QACvI,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC;IACD,IAAI,IAAI,CAACoH,QAAQ,EAAE;MACf,IAAI,CAACoI,KAAK,CAACsC,OAAO,CAAC7P,KAAK,CAAC,EAAE;QACvB,IAAI,CAACue,QAAQ,CAACzB,IAAI,CAAC,0CAA0C,CAAC;QAC9D,OAAO,KAAK;MAChB;MACA,OAAO9c,KAAK,CAACoP,KAAK,CAACrR,IAAI,IAAIonB,eAAe,CAACpnB,IAAI,CAAC,CAAC;IACrD,CAAC,MACI;MACD,OAAOonB,eAAe,CAACnlB,KAAK,CAAC;IACjC;EACJ;EACA+iB,iBAAiBA,CAACuC,OAAO,EAAE;IACvB,IAAI,CAAC,IAAI,CAACJ,kBAAkB,CAACI,OAAO,CAAC,EAAE;MACnC;IACJ;IACA,MAAMzZ,MAAM,GAAI9B,GAAG,IAAK;MACpB,IAAIhM,IAAI,GAAG,IAAI,CAACkE,SAAS,CAACiK,QAAQ,CAACnC,GAAG,CAAC;MACvC,IAAIhM,IAAI,EAAE;QACN,IAAI,CAACkE,SAAS,CAAC4J,MAAM,CAAC9N,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,MAAMwnB,WAAW,GAAG7e,QAAQ,CAACqD,GAAG,CAAC;QACjC,MAAMyb,WAAW,GAAG,CAACD,WAAW,IAAI,CAAC,IAAI,CAAClZ,SAAS;QACnD,IAAKkZ,WAAW,IAAIC,WAAW,EAAG;UAC9B,IAAI,CAACvjB,SAAS,CAAC4J,MAAM,CAAC,IAAI,CAAC5J,SAAS,CAACqJ,OAAO,CAACvB,GAAG,EAAE,IAAI,CAAC,CAAC;QAC5D,CAAC,MACI,IAAI,IAAI,CAACsC,SAAS,EAAE;UACrBtO,IAAI,GAAG;YACH,CAAC,IAAI,CAACwO,SAAS,GAAG,IAAI;YACtB,CAAC,IAAI,CAACF,SAAS,GAAGtC;UACtB,CAAC;UACD,IAAI,CAAC9H,SAAS,CAAC4J,MAAM,CAAC,IAAI,CAAC5J,SAAS,CAACqJ,OAAO,CAACvN,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D;MACJ;IACJ,CAAC;IACD,IAAI,IAAI,CAACoH,QAAQ,EAAE;MACfmgB,OAAO,CAACzY,OAAO,CAAC9O,IAAI,IAAI8N,MAAM,CAAC9N,IAAI,CAAC,CAAC;IACzC,CAAC,MACI;MACD8N,MAAM,CAACyZ,OAAO,CAAC;IACnB;EACJ;EACA9E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACzC,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACkC,UAAU,CACVnJ,IAAI,CAACzb,SAAS,CAAC,IAAI,CAACmY,SAAS,CAAC,EAAEhY,GAAG,CAACiqB,MAAM,IAAI,IAAI,CAACzF,YAAY,CAACrT,IAAI,CAAC8Y,MAAM,CAAC,CAAC,EAAEhqB,YAAY,CAAC,GAAG,CAAC,EAAEC,MAAM,CAAC,MAAM,IAAI,CAACskB,YAAY,CAACja,MAAM,GAAG,CAAC,CAAC,EAAEpK,GAAG,CAAC,MAAM,IAAI,CAACqkB,YAAY,CAAC0F,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CACpL3O,SAAS,CAAC9J,IAAI,IAAI;MACnB,MAAMlP,IAAI,GAAG,IAAI,CAACkE,SAAS,CAAC+K,WAAW,CAACC,IAAI,CAAC;MAC7C,IAAIlP,IAAI,EAAE;QACN,IAAI,IAAI,CAACqhB,MAAM,EAAE;UACb,IAAI,CAACnd,SAAS,CAACmM,QAAQ,CAACrQ,IAAI,CAAC;UAC7B,IAAI,CAAC4nB,eAAe,CAAC,CAAC;UACtB,IAAI,CAACrH,GAAG,CAAC0E,YAAY,CAAC,CAAC;QAC3B,CAAC,MACI;UACD,IAAI,CAACnX,MAAM,CAAC9N,IAAI,CAAC;QACrB;MACJ;MACA,IAAI,CAACiiB,YAAY,GAAG,EAAE;IAC1B,CAAC,CAAC;EACN;EACAS,mBAAmBA,CAAA,EAAG;IAClB,MAAMmF,KAAK,GAAG,IAAI,CAACpC,WAAW,CAACnc,aAAa;IAC5C,MAAMwe,UAAU,GAAG;MACf/d,IAAI,EAAE,MAAM;MACZge,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,IAAI,CAACpH,UAAU,GAAG,KAAK,GAAG,IAAI,CAAClZ,UAAU;MACvD,GAAG,IAAI,CAACmZ;IACZ,CAAC;IACD,KAAK,MAAMvR,GAAG,IAAI2Y,MAAM,CAACxY,IAAI,CAACoY,UAAU,CAAC,EAAE;MACvCD,KAAK,CAACM,YAAY,CAAC5Y,GAAG,EAAEuY,UAAU,CAACvY,GAAG,CAAC,CAAC;IAC5C;EACJ;EACAoV,cAAcA,CAAA,EAAG;IACb,MAAMyD,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMpoB,IAAI,IAAI,IAAI,CAACsC,aAAa,EAAE;MACnC,IAAI,IAAI,CAACgM,SAAS,EAAE;QAChB,IAAIrM,KAAK,GAAG,IAAI;QAChB,IAAIjC,IAAI,CAACgE,QAAQ,EAAE;UACf,MAAMuO,QAAQ,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI,CAAClE,SAAS,GAAG,IAAI,CAACd,OAAO;UAChEvL,KAAK,GAAGjC,IAAI,CAACiC,KAAK,CAACsQ,QAAQ,IAAI,IAAI,CAAC/E,OAAO,CAAC;QAChD,CAAC,MACI;UACDvL,KAAK,GAAG,IAAI,CAACiC,SAAS,CAACqK,aAAa,CAACvO,IAAI,CAACiC,KAAK,EAAE,IAAI,CAACqM,SAAS,CAAC;QACpE;QACA8Z,KAAK,CAACxZ,IAAI,CAAC3M,KAAK,CAAC;MACrB,CAAC,MACI;QACDmmB,KAAK,CAACxZ,IAAI,CAAC5O,IAAI,CAACiC,KAAK,CAAC;MAC1B;IACJ;IACA,MAAM8B,QAAQ,GAAG,IAAI,CAACzB,aAAa,CAAC1E,GAAG,CAACqS,CAAC,IAAIA,CAAC,CAAChO,KAAK,CAAC;IACrD,IAAI,IAAI,CAACmF,QAAQ,EAAE;MACf,IAAI,CAAC+a,SAAS,CAACiG,KAAK,CAAC;MACrB,IAAI,CAAC5G,WAAW,CAACza,IAAI,CAAChD,QAAQ,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAACoe,SAAS,CAAC1Z,SAAS,CAAC2f,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACrD,IAAI,CAAC5G,WAAW,CAACza,IAAI,CAAChD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAACwc,GAAG,CAAC0E,YAAY,CAAC,CAAC;EAC3B;EACAL,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACjmB,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAAC0pB,aAAa,CAAC,IAAI,CAAC;IACxB,IAAI,CAACnkB,SAAS,CAAC8K,kBAAkB,CAAC,CAAC;EACvC;EACAqZ,aAAaA,CAAC1pB,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAAC2mB,YAAY,EAAE;MACnB,IAAI,CAACiB,SAAS,CAACjP,IAAI,CAAC3Y,UAAU,CAAC;IACnC;EACJ;EACAipB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACvG,MAAM,IAAI,CAAC,IAAI,CAACmB,aAAa,EAAE;MACrC;IACJ;IACA,IAAI,CAACA,aAAa,CAAC7K,QAAQ,CAAC,IAAI,CAACzT,SAAS,CAACC,UAAU,CAAC;EAC1D;EACAmkB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACjH,MAAM,IAAI,CAAC,IAAI,CAACmB,aAAa,EAAE;MACrC;IACJ;IACA,IAAI,CAACA,aAAa,CAACxK,WAAW,CAAC,CAAC;EACpC;EACA6M,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACxD,MAAM,IAAI,IAAI,CAACzB,eAAe,IAAI,IAAI,CAACrY,QAAQ,EAAE;MACtD;MACA,IAAI,CAACgZ,GAAG,CAACgF,aAAa,CAAC,CAAC;MACxB,IAAI,CAAC/C,aAAa,CAACtK,cAAc,CAAC,CAAC;IACvC;EACJ;EACA8L,UAAUA,CAACrd,MAAM,EAAE;IACf,IAAI,IAAI,CAAC0a,MAAM,KAAK,KAAK,EAAE;MACvB,IAAI,IAAI,CAAC0E,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACC,YAAY,CAAC,CAAC;QACnBrf,MAAM,CAACoV,cAAc,CAAC,CAAC;MAC3B,CAAC,MACI,IAAI,CAAC,IAAI,CAACoF,MAAM,EAAE;QACnB;MACJ;IACJ;IACA,IAAI,IAAI,CAACT,WAAW,EAAE;MAClB,IAAI,IAAI,CAACxc,SAAS,CAACC,UAAU,EAAE;QAC3B,IAAI,CAACR,UAAU,CAAC,IAAI,CAACO,SAAS,CAACC,UAAU,CAAC;QAC1CwC,MAAM,CAACoV,cAAc,CAAC,CAAC;MAC3B,CAAC,MACI,IAAI,IAAI,CAACnU,UAAU,EAAE;QACtB,IAAI,CAACzC,SAAS,CAAC,CAAC;QAChBwB,MAAM,CAACoV,cAAc,CAAC,CAAC;MAC3B,CAAC,MACI;QACD,IAAI,CAAC5U,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,MACI;MACD,IAAI,CAACA,KAAK,CAAC,CAAC;IAChB;EACJ;EACA2c,YAAYA,CAACnd,MAAM,EAAE;IACjB,IAAI,IAAI,CAAC0a,MAAM,IAAI,IAAI,CAACuB,WAAW,EAAE;MACjC,IAAI,IAAI,CAAC1e,SAAS,CAACC,UAAU,EAAE;QAC3B,IAAI,CAACR,UAAU,CAAC,IAAI,CAACO,SAAS,CAACC,UAAU,CAAC;MAC9C,CAAC,MACI,IAAI,IAAI,CAACyD,UAAU,EAAE;QACtB,IAAI,CAACzC,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC,MACI,IAAI,IAAI,CAACuZ,WAAW,EAAE;MACvB,IAAI,CAAC+F,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD;IACJ;IACA9d,MAAM,CAACoV,cAAc,CAAC,CAAC;EAC3B;EACA6H,YAAYA,CAACjd,MAAM,EAAE;IACjB,IAAI,IAAI,CAAC0a,MAAM,IAAI,IAAI,CAACuB,WAAW,EAAE;MACjC;IACJ;IACA,IAAI,CAAC6B,IAAI,CAAC,CAAC;IACX9d,MAAM,CAACoV,cAAc,CAAC,CAAC;EAC3B;EACAyH,gBAAgBA,CAAC7c,MAAM,EAAE;IACrB,IAAI,IAAI,CAAC4hB,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,IAAI,CAACrkB,SAAS,CAACe,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACqjB,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACpkB,SAAS,CAACgM,YAAY,CAAC,CAAC;MAC7B,IAAI,CAAC0X,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,CAACnD,IAAI,CAAC,CAAC;IACX9d,MAAM,CAACoV,cAAc,CAAC,CAAC;EAC3B;EACA2H,cAAcA,CAAC/c,MAAM,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC0a,MAAM,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACkH,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,IAAI,CAACrkB,SAAS,CAACe,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACqjB,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACpkB,SAAS,CAACkM,gBAAgB,CAAC,CAAC;MACjC,IAAI,CAACwX,eAAe,CAAC,CAAC;IAC1B;IACAjhB,MAAM,CAACoV,cAAc,CAAC,CAAC;EAC3B;EACAwM,cAAcA,CAACC,QAAQ,EAAE;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACvkB,SAAS,CAAC6I,WAAW,GAAGyb,QAAQ;IACvD,OAAO,IAAI,CAACrH,MAAM,IAAI,IAAI,CAACxiB,UAAU,IAC9B,IAAI,CAACuF,SAAS,CAACC,UAAU,KACxBskB,SAAS,GAAG,CAAC,IAAIA,SAAS,KAAK,IAAI,CAACvkB,SAAS,CAACuD,aAAa,CAACO,MAAM,CAAC;EAC/E;EACAmc,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACxlB,UAAU,IAAI,CAAC,IAAI,CAACyiB,SAAS,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,CAAC,IAAI,CAAC0B,QAAQ,EAAE;MAChF;IACJ;IACA,IAAI,IAAI,CAAClb,QAAQ,EAAE;MACf,IAAI,CAACjG,QAAQ,CAAC,IAAI,CAAC+C,SAAS,CAACkJ,gBAAgB,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC0X,UAAU,CAAC,CAAC;IACrB;EACJ;EACA,IAAIQ,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACiB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACmC,SAAS,CAAC1gB,MAAM,GAAG,CAAC;EAChE;EACA,IAAIie,UAAUA,CAAA,EAAG;IACb,MAAM/W,IAAI,GAAG,IAAI,CAACvQ,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwf,IAAI,CAAC,CAAC;IACtD,OAAOjP,IAAI,IAAIA,IAAI,CAAClH,MAAM,IAAI,IAAI,CAACgZ,aAAa;EACpD;EACAqB,kBAAkBA,CAAC3C,MAAM,EAAE;IACvB,IAAI,CAACiJ,WAAW,GAAG,IAAI,CAACA,WAAW,IAAIjJ,MAAM,CAACiJ,WAAW;IACzD,IAAI,CAACpjB,YAAY,GAAG,IAAI,CAACA,YAAY,IAAIma,MAAM,CAACna,YAAY;IAC5D,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI6Z,MAAM,CAAC7Z,gBAAgB;IACxE,IAAI,CAAClB,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI+a,MAAM,CAAC/a,UAAU;IACtD,IAAI,CAACwB,WAAW,GAAG,IAAI,CAACA,WAAW,IAAIuZ,MAAM,CAACvZ,WAAW;IACzD,IAAI,CAAC/C,YAAY,GAAG,IAAI,CAACA,YAAY,IAAIsc,MAAM,CAACtc,YAAY;IAC5D,IAAI,CAACiE,aAAa,GAAGoB,SAAS,CAAC,IAAI,CAACpB,aAAa,CAAC,GAC5C,IAAI,CAACA,aAAa,GAClBoB,SAAS,CAACiX,MAAM,CAACjB,oBAAoB,CAAC,GAAG,CAACiB,MAAM,CAACjB,oBAAoB,GAAG,KAAK;IACnF,IAAI,CAACC,WAAW,GAAGjW,SAAS,CAAC,IAAI,CAACiW,WAAW,CAAC,GAAG,IAAI,CAACA,WAAW,GAAGgB,MAAM,CAAChB,WAAW;IACtF,IAAI,CAACnX,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAImY,MAAM,CAACnY,QAAQ;IAChD,IAAI,CAAC+G,SAAS,GAAG,IAAI,CAACA,SAAS,IAAIoR,MAAM,CAACpR,SAAS;IACnD,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,IAAIkR,MAAM,CAAClR,SAAS;IACnD,IAAI,CAACmQ,UAAU,GAAG,IAAI,CAACA,UAAU,IAAIe,MAAM,CAACf,UAAU;EAC1D;EACA;IAAS,IAAI,CAAClV,IAAI,YAAAmf,0BAAAjf,CAAA;MAAA,YAAAA,CAAA,IAAwFyV,iBAAiB,EA7oF3BrjB,EAAE,CAAA8sB,iBAAA,CA6oF2C,OAAO,GA7oFpD9sB,EAAE,CAAA8sB,iBAAA,CA6oFgF,WAAW,GA7oF7F9sB,EAAE,CAAA6N,iBAAA,CA6oFyH4U,cAAc,GA7oFzIziB,EAAE,CAAA6N,iBAAA,CA6oFoJuV,uBAAuB,GA7oF7KpjB,EAAE,CAAA6N,iBAAA,CA6oFwL7N,EAAE,CAACK,UAAU,GA7oFvML,EAAE,CAAA6N,iBAAA,CA6oFkN7N,EAAE,CAAC+sB,iBAAiB,GA7oFxO/sB,EAAE,CAAA6N,iBAAA,CA6oFmPkV,cAAc;IAAA,CAA4C;EAAE;EACjZ;IAAS,IAAI,CAAC3C,IAAI,kBA9oF8EpgB,EAAE,CAAAqgB,iBAAA;MAAArS,IAAA,EA8oFJqV,iBAAiB;MAAApV,SAAA;MAAA+e,cAAA,WAAAC,iCAAAnqB,EAAA,EAAAC,GAAA,EAAAmqB,QAAA;QAAA,IAAApqB,EAAA;UA9oFf9C,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFMze,yBAAyB,KAA2B1N,WAAW;UAlpFvEf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFqIte,2BAA2B,KAA2B7N,WAAW;UAlpFxMf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFmQpe,wBAAwB,KAA2B/N,WAAW;UAlpFnUf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFmYle,6BAA6B,KAA2BjO,WAAW;UAlpFxcf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFogBhe,yBAAyB,KAA2BnO,WAAW;UAlpFrkBf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFioB9d,yBAAyB,KAA2BrO,WAAW;UAlpFlsBf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFgwB5d,2BAA2B,KAA2BvO,WAAW;UAlpFn0Bf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFq4B1d,+BAA+B,KAA2BzO,WAAW;UAlpF58Bf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpF6gCxd,8BAA8B,KAA2B3O,WAAW;UAlpFnlCf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpF4oCtd,sBAAsB,KAA2B7O,WAAW;UAlpF1sCf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpF8wCpd,iCAAiC,KAA2B/O,WAAW;UAlpFv1Cf,EAAE,CAAAmtB,cAAA,CAAAD,QAAA,EAkpFi4CpL,iBAAiB;QAAA;QAAA,IAAAhf,EAAA;UAAA,IAAA2d,EAAA;UAlpFp5CzgB,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAyF,cAAA,GAAAiY,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAwF,gBAAA,GAAAkY,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAiD,aAAA,GAAAya,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA4D,kBAAA,GAAA8Z,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAQ,cAAA,GAAAkd,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAY,cAAA,GAAA8c,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA6G,gBAAA,GAAA6W,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAmH,oBAAA,GAAAuW,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAyH,mBAAA,GAAAiW,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAuG,WAAA,GAAAmX,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAmE,sBAAA,GAAAuZ,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAooB,SAAA,GAAA1K,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAA8M,wBAAAtqB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAwgB,WAAA,CAkpFqgDvH,wBAAwB;UAlpF/hDjZ,EAAE,CAAAwgB,WAAA,CAAA5c,GAAA;UAAF5D,EAAE,CAAAwgB,WAAA,CAAA3c,GAAA;QAAA;QAAA,IAAAf,EAAA;UAAA,IAAA2d,EAAA;UAAFzgB,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA0jB,aAAA,GAAAhG,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAA2mB,WAAA,GAAAjJ,EAAA,CAAAjC,KAAA;UAAFxe,EAAE,CAAA0gB,cAAA,CAAAD,EAAA,GAAFzgB,EAAE,CAAA2gB,WAAA,QAAA5d,GAAA,CAAAskB,WAAA,GAAA5G,EAAA,CAAAjC,KAAA;QAAA;MAAA;MAAA6O,QAAA;MAAAC,YAAA,WAAAC,+BAAAzqB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA6E,UAAA,qBAAA2oB,6CAAA5iB,MAAA;YAAA,OA8oFJ7H,GAAA,CAAAkkB,aAAA,CAAArc,MAAoB,CAAC;UAAA,CAAL,CAAC;QAAA;QAAA,IAAA9H,EAAA;UA9oFf9C,EAAE,CAAA8F,WAAA,wBAAA/C,GAAA,CAAAynB,SA8oFY,CAAC,uBAAAznB,GAAA,CAAAsI,QAAD,CAAC,uBAAAtI,GAAA,CAAAqiB,MAAD,CAAC,yBAAAriB,GAAA,CAAAkhB,UAAD,CAAC,wBAAAlhB,GAAA,CAAAsiB,SAAD,CAAC,qBAAAtiB,GAAA,CAAAuiB,MAAD,CAAC,cAAAviB,GAAA,CAAAijB,eAAD,CAAC,uBAAAjjB,GAAA,CAAAgD,QAAD,CAAC,uBAAAhD,GAAA,CAAAihB,QAAD,CAAC,qBAAAjhB,GAAA,CAAAohB,MAAD,CAAC;QAAA;MAAA;MAAAjW,MAAA;QAAAuE,SAAA;QAAAF,SAAA;QAAAmS,SAAA;QAAAkI,WAAA;QAAApjB,YAAA;QAAAM,gBAAA;QAAAlB,UAAA;QAAAwB,WAAA;QAAA/C,YAAA;QAAAub,UAAA;QAAAnX,gBAAA;QAAAD,QAAA;QAAAQ,OAAA;QAAA4X,aAAA;QAAA1S,YAAA;QAAAyT,WAAA;QAAAhC,WAAA;QAAAvR,gBAAA;QAAAK,OAAA;QAAAgF,UAAA;QAAAlL,YAAA;QAAAD,aAAA;QAAAiL,eAAA;QAAAtE,sBAAA;QAAAqB,QAAA;QAAAsR,SAAA;QAAAC,gBAAA;QAAAC,UAAA;QAAAC,UAAA;QAAA0I,QAAA;QAAA1J,QAAA;QAAAiB,oBAAA;QAAAC,aAAA;QAAAZ,kBAAA;QAAAa,SAAA;QAAAsF,SAAA;QAAAnf,QAAA;QAAA+Z,MAAA;QAAAnB,UAAA;QAAAoB,SAAA;QAAAC,MAAA;QAAAjhB,KAAA;QAAAiO,WAAA;QAAAmR,gBAAA;QAAAI,eAAA;MAAA;MAAAjD,OAAA;QAAA2E,SAAA;QAAAC,UAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,UAAA;QAAAC,WAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,WAAA;QAAAhb,MAAA;QAAAG,WAAA;MAAA;MAAAiD,QAAA,GA9oFfnO,EAAE,CAAA0tB,kBAAA,CA8oFgkE,CAAC;QACvpEC,OAAO,EAAErsB,iBAAiB;QAC1BssB,WAAW,EAAE9sB,UAAU,CAAC,MAAMuiB,iBAAiB,CAAC;QAChDwK,KAAK,EAAE;MACX,CAAC,EAAElX,sBAAsB,CAAC,GAlpF8D3W,EAAE,CAAAoO,oBAAA;MAAA0S,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtS,QAAA,WAAAof,2BAAAhrB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAirB,GAAA,GAAF/tB,EAAE,CAAA4E,gBAAA;UAAF5E,EAAE,CAAAgD,cAAA,YAkpFu+D,CAAC;UAlpF1+DhD,EAAE,CAAA6E,UAAA,uBAAAmpB,oDAAApjB,MAAA;YAAF5K,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,OAAF/tB,EAAE,CAAAmF,WAAA,CAkpFo0DpC,GAAA,CAAAulB,eAAA,CAAA1d,MAAsB,CAAC;UAAA,CAAC,CAAC;UAlpF/1D5K,EAAE,CAAAgD,cAAA,aAkpFihE,CAAC,aAAuC,CAAC;UAlpF5jEhD,EAAE,CAAAqF,MAAA,EAkpFwkE,CAAC;UAlpF3kErF,EAAE,CAAAkD,YAAA,CAkpF8kE,CAAC;UAlpFjlElD,EAAE,CAAA0F,UAAA,IAAAU,yCAAA,0BAkpFmrE,CAAC,IAAAM,4BAAA,gBAA6+B,CAAC;UAlpFpqG1G,EAAE,CAAAgD,cAAA,aAkpFg5G,CAAC,kBAA03B,CAAC;UAlpF9wIhD,EAAE,CAAA6E,UAAA,mBAAAopB,kDAAA;YAAFjuB,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,MAAAG,cAAA,GAAFluB,EAAE,CAAA6F,WAAA;YAAA,OAAF7F,EAAE,CAAAmF,WAAA,CAkpFuuHpC,GAAA,CAAAnB,MAAA,CAAAssB,cAAA,CAAAhoB,KAAwB,CAAC;UAAA,CAAC,CAAC,8BAAAioB,6DAAA;YAlpFpwHnuB,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,OAAF/tB,EAAE,CAAAmF,WAAA,CAkpF4yHpC,GAAA,CAAAunB,kBAAA,CAAmB,CAAC;UAAA,CAAC,CAAC,4BAAA8D,2DAAA;YAlpFp0HpuB,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,MAAAG,cAAA,GAAFluB,EAAE,CAAA6F,WAAA;YAAA,OAAF7F,EAAE,CAAAmF,WAAA,CAkpF02HpC,GAAA,CAAAwnB,gBAAA,CAAA2D,cAAA,CAAAhoB,KAAkC,CAAC;UAAA,CAAC,CAAC,mBAAAmoB,kDAAAzjB,MAAA;YAlpFj5H5K,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,OAAF/tB,EAAE,CAAAmF,WAAA,CAkpF86HpC,GAAA,CAAA0nB,YAAA,CAAA7f,MAAmB,CAAC;UAAA,CAAC,CAAC,kBAAA0jB,iDAAA1jB,MAAA;YAlpFt8H5K,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,OAAF/tB,EAAE,CAAAmF,WAAA,CAkpFk+HpC,GAAA,CAAA4nB,WAAA,CAAA/f,MAAkB,CAAC;UAAA,CAAC,CAAC,oBAAA2jB,mDAAA3jB,MAAA;YAlpFz/H5K,EAAE,CAAA+E,aAAA,CAAAgpB,GAAA;YAAA,OAAF/tB,EAAE,CAAAmF,WAAA,CAkpFuhIyF,MAAA,CAAA4jB,eAAA,CAAuB,CAAC;UAAA,CAAC,CAAC;UAlpFnjIxuB,EAAE,CAAAkD,YAAA,CAkpF2wI,CAAC,CAAe,CAAC,CAAW,CAAC;UAlpF1yIlD,EAAE,CAAA0F,UAAA,IAAAsB,yCAAA,0BAkpF+0I,CAAC,KAAAG,kCAAA,kBAA0Z,CAAC;UAlpF7uJnH,EAAE,CAAAgD,cAAA,eAkpFq2J,CAAC;UAlpFx2JhD,EAAE,CAAAsF,SAAA,eAkpF+4J,CAAC;UAlpFl5JtF,EAAE,CAAAkD,YAAA,CAkpF45J,CAAC,CAAO,CAAC;UAlpFv6JlD,EAAE,CAAA0F,UAAA,KAAA+E,+CAAA,gCAkpFy6L,CAAC;QAAA;QAAA,IAAA3H,EAAA;UAlpF56L9C,EAAE,CAAA8F,WAAA,0BAAA/C,GAAA,CAAA6f,UAAA,cAkpF45D,CAAC,iBAAA7f,GAAA,CAAAwjB,QAAsC,CAAC;UAlpFt8DvmB,EAAE,CAAAqD,SAAA,EAkpFwkE,CAAC;UAlpF3kErD,EAAE,CAAA2I,iBAAA,CAAA5F,GAAA,CAAA6pB,WAkpFwkE,CAAC;UAlpF3kE5sB,EAAE,CAAAqD,SAAA,CAkpFgrE,CAAC;UAlpFnrErD,EAAE,CAAAsD,UAAA,WAAAP,GAAA,CAAA4D,kBAAA,KAAA5D,GAAA,CAAAsI,QAAA,KAAAtI,GAAA,CAAAwD,aAAA,CAAA0F,MAAA,IAkpFgrE,CAAC;UAlpFnrEjM,EAAE,CAAAqD,SAAA,CAkpFwgG,CAAC;UAlpF3gGrD,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAsI,QAAA,IAAAtI,GAAA,CAAA4D,kBAAA,IAAA5D,GAAA,CAAA8D,cAAA,CAAAoF,MAAA,IAkpFwgG,CAAC;UAlpF3gGjM,EAAE,CAAAqD,SAAA,CAkpFyyG,CAAC;UAlpF5yGrD,EAAE,CAAAqI,WAAA,kBAAAtF,GAAA,CAAAuiB,MAAA,eAAAviB,GAAA,CAAAuiB,MAAA,GAAAviB,GAAA,CAAA6I,UAAA;UAAF5L,EAAE,CAAAqD,SAAA,CAkpF8lH,CAAC;UAlpFjmHrD,EAAE,CAAAsD,UAAA,cAAAP,GAAA,CAAAkhB,UAAA,IAAAlhB,GAAA,CAAAoF,SAAA,CAAAgJ,gBAkpF8lH,CAAC,aAAApO,GAAA,CAAAgD,QAA2C,CAAC,UAAAhD,GAAA,CAAAH,UAAA,GAAAG,GAAA,CAAAH,UAAA,KAA4D,CAAC;UAlpF1sH5C,EAAE,CAAAqI,WAAA,OAAAtF,GAAA,CAAA+hB,UAAA,cAAA/hB,GAAA,CAAA0qB,QAAA,2BAAA1qB,GAAA,CAAAuiB,MAAA,GAAAviB,GAAA,CAAAoF,SAAA,kBAAApF,GAAA,CAAAoF,SAAA,CAAAC,UAAA,kBAAArF,GAAA,CAAAoF,SAAA,CAAAC,UAAA,CAAAE,MAAA,0BAAAvF,GAAA,CAAAuiB,MAAA,GAAAviB,GAAA,CAAA6I,UAAA;UAAF5L,EAAE,CAAAqD,SAAA,EAkpF40I,CAAC;UAlpF/0IrD,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAiJ,OAkpF40I,CAAC;UAlpF/0IhM,EAAE,CAAAqD,SAAA,CAkpFqpJ,CAAC;UAlpFxpJrD,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAinB,SAAA,EAkpFqpJ,CAAC;UAlpFxpJhqB,EAAE,CAAAqD,SAAA,EAkpFy8J,CAAC;UAlpF58JrD,EAAE,CAAAsD,UAAA,SAAAP,GAAA,CAAAuiB,MAkpFy8J,CAAC;QAAA;MAAA;MAAAlE,YAAA,GAAwzRjf,EAAE,CAACssB,OAAO,EAAoFtsB,EAAE,CAACusB,OAAO,EAAmHvsB,EAAE,CAACkf,IAAI,EAA6Flf,EAAE,CAACmf,gBAAgB,EAAoJrI,wBAAwB,EAAyQhM,oBAAoB;MAAA0hB,MAAA;MAAApN,aAAA;MAAAC,eAAA;IAAA,EAAkK;EAAE;AACvyd;AACA;EAAA,QAAAnT,SAAA,oBAAAA,SAAA,KAppFoGrO,EAAE,CAAAsO,iBAAA,CAopFX+U,iBAAiB,EAAc,CAAC;IAC/GrV,IAAI,EAAE1N,SAAS;IACfiO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAEogB,SAAS,EAAE,CAAC;QAC5BjB,OAAO,EAAErsB,iBAAiB;QAC1BssB,WAAW,EAAE9sB,UAAU,CAAC,MAAMuiB,iBAAiB,CAAC;QAChDwK,KAAK,EAAE;MACX,CAAC,EAAElX,sBAAsB,CAAC;MAAE4K,aAAa,EAAE/gB,iBAAiB,CAACkhB,IAAI;MAAEF,eAAe,EAAEjhB,uBAAuB,CAACkhB,MAAM;MAAE/S,QAAQ,EAAE,yrOAAyrO;MAAEigB,MAAM,EAAE,CAAC,suJAAsuJ;IAAE,CAAC;EAC3jY,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3gB,IAAI,EAAErB,SAAS;IAAEgV,UAAU,EAAE,CAAC;MAC/C3T,IAAI,EAAEhN,SAAS;MACfuN,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC,EAAE;IAAEP,IAAI,EAAErB,SAAS;IAAEgV,UAAU,EAAE,CAAC;MAClC3T,IAAI,EAAEhN,SAAS;MACfuN,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEP,IAAI,EAAEyU;EAAe,CAAC,EAAE;IAAEzU,IAAI,EAAErB,SAAS;IAAEgV,UAAU,EAAE,CAAC;MAC5D3T,IAAI,EAAEtN,MAAM;MACZ6N,IAAI,EAAE,CAAC6U,uBAAuB;IAClC,CAAC;EAAE,CAAC,EAAE;IAAEpV,IAAI,EAAEhO,EAAE,CAACK;EAAW,CAAC,EAAE;IAAE2N,IAAI,EAAEhO,EAAE,CAAC+sB;EAAkB,CAAC,EAAE;IAAE/e,IAAI,EAAE+U;EAAe,CAAC,CAAC,EAAkB;IAAEtQ,SAAS,EAAE,CAAC;MACxHzE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqS,SAAS,EAAE,CAAC;MACZvE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEwkB,SAAS,EAAE,CAAC;MACZ1W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE0sB,WAAW,EAAE,CAAC;MACd5e,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEsJ,YAAY,EAAE,CAAC;MACfwE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE4J,gBAAgB,EAAE,CAAC;MACnBkE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE0I,UAAU,EAAE,CAAC;MACboF,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEkK,WAAW,EAAE,CAAC;MACd4D,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEmH,YAAY,EAAE,CAAC;MACf2G,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE0iB,UAAU,EAAE,CAAC;MACb5U,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEuL,gBAAgB,EAAE,CAAC;MACnBuC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEsL,QAAQ,EAAE,CAAC;MACXwC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE8L,OAAO,EAAE,CAAC;MACVgC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE0jB,aAAa,EAAE,CAAC;MAChB5V,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEgR,YAAY,EAAE,CAAC;MACflD,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEykB,WAAW,EAAE,CAAC;MACd3W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEyiB,WAAW,EAAE,CAAC;MACd3U,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEkR,gBAAgB,EAAE,CAAC;MACnBpD,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEuR,OAAO,EAAE,CAAC;MACVzD,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEuW,UAAU,EAAE,CAAC;MACbzI,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqL,YAAY,EAAE,CAAC;MACfyC,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChB0C,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqW,eAAe,EAAE,CAAC;MAClBvI,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE+R,sBAAsB,EAAE,CAAC;MACzBjE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEoT,QAAQ,EAAE,CAAC;MACXtF,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE0kB,SAAS,EAAE,CAAC;MACZ5W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE2kB,gBAAgB,EAAE,CAAC;MACnB7W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE4kB,UAAU,EAAE,CAAC;MACb9W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE6kB,UAAU,EAAE,CAAC;MACb/W,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEutB,QAAQ,EAAE,CAAC;MACXzf,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE6jB,QAAQ,EAAE,CAAC;MACX/V,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE8kB,oBAAoB,EAAE,CAAC;MACvBhX,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE+kB,aAAa,EAAE,CAAC;MAChBjX,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEmkB,kBAAkB,EAAE,CAAC;MACrBrW,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEglB,SAAS,EAAE,CAAC;MACZlX,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEsqB,SAAS,EAAE,CAAC;MACZxc,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAElD,QAAQ,EAAE,CAAC;MACX2C,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE6W,MAAM,EAAE,CAAC;MACTpX,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE0V,UAAU,EAAE,CAAC;MACbjW,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE8W,SAAS,EAAE,CAAC;MACZrX,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE+W,MAAM,EAAE,CAAC;MACTtX,IAAI,EAAE9N;IACV,CAAC,EAAE;MACC8N,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAElK,KAAK,EAAE,CAAC;MACR2J,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEoS,WAAW,EAAE,CAAC;MACdtE,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEujB,gBAAgB,EAAE,CAAC;MACnBzV,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAE2jB,eAAe,EAAE,CAAC;MAClB7V,IAAI,EAAE9N;IACV,CAAC,CAAC;IAAEqlB,SAAS,EAAE,CAAC;MACZvX,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAEiX,UAAU,EAAE,CAAC;MACbxX,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkX,WAAW,EAAE,CAAC;MACdzX,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEmX,SAAS,EAAE,CAAC;MACZ1X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAEoX,UAAU,EAAE,CAAC;MACb3X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEqX,WAAW,EAAE,CAAC;MACd5X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEsX,UAAU,EAAE,CAAC;MACb7X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEuX,QAAQ,EAAE,CAAC;MACX9X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAEwX,WAAW,EAAE,CAAC;MACd/X,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAExD,MAAM,EAAE,CAAC;MACTiD,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAErD,WAAW,EAAE,CAAC;MACd8C,IAAI,EAAErN,MAAM;MACZ4N,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAE/F,cAAc,EAAE,CAAC;MACjBwF,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACE,yBAAyB,EAAE;QAAEmT,IAAI,EAAE7gB;MAAY,CAAC;IAC3D,CAAC,CAAC;IAAEwH,gBAAgB,EAAE,CAAC;MACnByF,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACK,2BAA2B,EAAE;QAAEgT,IAAI,EAAE7gB;MAAY,CAAC;IAC7D,CAAC,CAAC;IAAEiF,aAAa,EAAE,CAAC;MAChBgI,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACO,wBAAwB,EAAE;QAAE8S,IAAI,EAAE7gB;MAAY,CAAC;IAC1D,CAAC,CAAC;IAAE4F,kBAAkB,EAAE,CAAC;MACrBqH,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACS,6BAA6B,EAAE;QAAE4S,IAAI,EAAE7gB;MAAY,CAAC;IAC/D,CAAC,CAAC;IAAEwC,cAAc,EAAE,CAAC;MACjByK,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACW,yBAAyB,EAAE;QAAE0S,IAAI,EAAE7gB;MAAY,CAAC;IAC3D,CAAC,CAAC;IAAE4C,cAAc,EAAE,CAAC;MACjBqK,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACa,yBAAyB,EAAE;QAAEwS,IAAI,EAAE7gB;MAAY,CAAC;IAC3D,CAAC,CAAC;IAAE6I,gBAAgB,EAAE,CAAC;MACnBoE,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACe,2BAA2B,EAAE;QAAEsS,IAAI,EAAE7gB;MAAY,CAAC;IAC7D,CAAC,CAAC;IAAEmJ,oBAAoB,EAAE,CAAC;MACvB8D,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACiB,+BAA+B,EAAE;QAAEoS,IAAI,EAAE7gB;MAAY,CAAC;IACjE,CAAC,CAAC;IAAEyJ,mBAAmB,EAAE,CAAC;MACtBwD,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACmB,8BAA8B,EAAE;QAAEkS,IAAI,EAAE7gB;MAAY,CAAC;IAChE,CAAC,CAAC;IAAEuI,WAAW,EAAE,CAAC;MACd0E,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACqB,sBAAsB,EAAE;QAAEgS,IAAI,EAAE7gB;MAAY,CAAC;IACxD,CAAC,CAAC;IAAEmG,sBAAsB,EAAE,CAAC;MACzB8G,IAAI,EAAE9M,YAAY;MAClBqN,IAAI,EAAE,CAACuB,iCAAiC,EAAE;QAAE8R,IAAI,EAAE7gB;MAAY,CAAC;IACnE,CAAC,CAAC;IAAE0lB,aAAa,EAAE,CAAC;MAChBzY,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAACzN,UAAU,CAAC,MAAMmY,wBAAwB,CAAC;IACrD,CAAC,CAAC;IAAEyQ,WAAW,EAAE,CAAC;MACd1b,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAAC,aAAa,EAAE;QAAEsT,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAEwF,WAAW,EAAE,CAAC;MACdrZ,IAAI,EAAEpN,SAAS;MACf2N,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAE4c,SAAS,EAAE,CAAC;MACZnd,IAAI,EAAE7M,eAAe;MACrBoN,IAAI,EAAE,CAACuT,iBAAiB,EAAE;QAAE+M,WAAW,EAAE;MAAK,CAAC;IACnD,CAAC,CAAC;IAAE7I,eAAe,EAAE,CAAC;MAClBhY,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAExI,QAAQ,EAAE,CAAC;MACXiI,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEyV,QAAQ,EAAE,CAAC;MACXhW,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE4V,MAAM,EAAE,CAAC;MACTnW,IAAI,EAAE/M,WAAW;MACjBsN,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAE0Y,aAAa,EAAE,CAAC;MAChBjZ,IAAI,EAAE5M,YAAY;MAClBmN,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASugB,4BAA4BA,CAAA,EAAG;EACpC,OAAO,IAAIC,qBAAqB,CAAC,CAAC;AACtC;AACA,MAAMA,qBAAqB,CAAC;EACxB7hB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8hB,SAAS,GAAG,EAAE;EACvB;EACA,IAAI9oB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC8oB,SAAS;EACzB;EACAjd,MAAMA,CAAC9N,IAAI,EAAEoH,QAAQ,EAAE4jB,YAAY,EAAE;IACjChrB,IAAI,CAAC+D,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/D,IAAI,CAACgE,QAAQ,IAAK,CAACoD,QAAQ,IAAI4jB,YAAa,EAAE;MAC/C,IAAI,CAACD,SAAS,CAACnc,IAAI,CAAC5O,IAAI,CAAC;IAC7B;IACA,IAAIoH,QAAQ,EAAE;MACV,IAAIpH,IAAI,CAACiE,MAAM,EAAE;QACb,MAAMgnB,aAAa,GAAGjrB,IAAI,CAACiE,MAAM,CAACD,QAAQ,CAACgE,MAAM;QACjD,MAAMkjB,aAAa,GAAGlrB,IAAI,CAACiE,MAAM,CAACD,QAAQ,CAACrG,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAAClM,QAAQ,CAAC,CAACiE,MAAM;QACzEhI,IAAI,CAACiE,MAAM,CAACF,QAAQ,GAAGknB,aAAa,KAAKC,aAAa;MAC1D,CAAC,MACI,IAAIlrB,IAAI,CAACgE,QAAQ,EAAE;QACpB,IAAI,CAACmnB,yBAAyB,CAACnrB,IAAI,CAACgE,QAAQ,EAAE,IAAI,CAAC;QACnD,IAAI,CAAConB,eAAe,CAACprB,IAAI,CAAC;QAC1B,IAAIgrB,YAAY,IAAI,IAAI,CAACK,eAAe,CAACrrB,IAAI,CAAC,EAAE;UAC5C,IAAI,CAAC+qB,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAACptB,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAAChM,MAAM,KAAKjE,IAAI,CAAC,EAAEA,IAAI,CAAC;QAC7E,CAAC,MACI;UACD,IAAI,CAAC+qB,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,EAAE,GAAG/qB,IAAI,CAACgE,QAAQ,CAACrG,MAAM,CAACsS,CAAC,IAAI,CAACA,CAAC,CAACnO,QAAQ,CAAC,CAAC;QACnF;MACJ;IACJ;EACJ;EACAX,QAAQA,CAACnB,IAAI,EAAEoH,QAAQ,EAAE;IACrB,IAAI,CAAC2jB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACptB,MAAM,CAACsS,CAAC,IAAIA,CAAC,KAAKjQ,IAAI,CAAC;IACvDA,IAAI,CAAC+D,QAAQ,GAAG,KAAK;IACrB,IAAIqD,QAAQ,EAAE;MACV,IAAIpH,IAAI,CAACiE,MAAM,IAAIjE,IAAI,CAACiE,MAAM,CAACF,QAAQ,EAAE;QACrC,MAAMC,QAAQ,GAAGhE,IAAI,CAACiE,MAAM,CAACD,QAAQ;QACrC,IAAI,CAACsnB,aAAa,CAACtrB,IAAI,CAACiE,MAAM,CAAC;QAC/B,IAAI,CAACmnB,eAAe,CAACprB,IAAI,CAACiE,MAAM,CAAC;QACjC,IAAI,CAAC8mB,SAAS,CAACnc,IAAI,CAAC,GAAG5K,QAAQ,CAACrG,MAAM,CAACsS,CAAC,IAAIA,CAAC,KAAKjQ,IAAI,IAAI,CAACiQ,CAAC,CAACnO,QAAQ,CAAC,CAAC;QACvE9B,IAAI,CAACiE,MAAM,CAACF,QAAQ,GAAG,KAAK;MAChC,CAAC,MACI,IAAI/D,IAAI,CAACgE,QAAQ,EAAE;QACpB,IAAI,CAACmnB,yBAAyB,CAACnrB,IAAI,CAACgE,QAAQ,EAAE,KAAK,CAAC;QACpD,IAAI,CAAConB,eAAe,CAACprB,IAAI,CAAC;MAC9B;IACJ;EACJ;EACAC,KAAKA,CAAC4O,YAAY,EAAE;IAChB,IAAI,CAACkc,SAAS,GAAGlc,YAAY,GAAG,IAAI,CAACkc,SAAS,CAACptB,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAACnO,QAAQ,CAAC,GAAG,EAAE;EAC/E;EACAqpB,yBAAyBA,CAACnnB,QAAQ,EAAED,QAAQ,EAAE;IAC1C,KAAK,MAAMmN,KAAK,IAAIlN,QAAQ,EAAE;MAC1B,IAAIkN,KAAK,CAACpP,QAAQ,EAAE;QAChB;MACJ;MACAoP,KAAK,CAACnN,QAAQ,GAAGA,QAAQ;IAC7B;EACJ;EACAqnB,eAAeA,CAACnnB,MAAM,EAAE;IACpB,IAAI,CAAC8mB,SAAS,GAAG,CACb,GAAG,IAAI,CAACA,SAAS,CAACptB,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAAChM,MAAM,KAAKA,MAAM,CAAC,EAClD,GAAGA,MAAM,CAACD,QAAQ,CAACrG,MAAM,CAACsS,CAAC,IAAIA,CAAC,CAAChM,MAAM,KAAKA,MAAM,IAAIgM,CAAC,CAACnO,QAAQ,IAAImO,CAAC,CAAClM,QAAQ,CAAC,CAClF;EACL;EACAunB,aAAaA,CAACrnB,MAAM,EAAE;IAClB,IAAI,CAAC8mB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACptB,MAAM,CAACsS,CAAC,IAAIA,CAAC,KAAKhM,MAAM,CAAC;EAC7D;EACAonB,eAAeA,CAACrrB,IAAI,EAAE;IAClB,OAAOA,IAAI,CAACgE,QAAQ,CAACqN,KAAK,CAACpB,CAAC,IAAI,CAACA,CAAC,CAACnO,QAAQ,IAAImO,CAAC,CAAClM,QAAQ,CAAC;EAC9D;AACJ;AAEA,MAAMwnB,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC9hB,IAAI,YAAA+hB,uBAAA7hB,CAAA;MAAA,YAAAA,CAAA,IAAwF4hB,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBA57F8E1vB,EAAE,CAAA2vB,gBAAA;MAAA3hB,IAAA,EA47FSwhB;IAAc,EA0B5E;EAAE;EAC/C;IAAS,IAAI,CAACI,IAAI,kBAv9F8E5vB,EAAE,CAAA6vB,gBAAA;MAAAjB,SAAA,EAu9FoC,CAC9H;QAAEjB,OAAO,EAAEvK,uBAAuB;QAAE0M,QAAQ,EAAEhB;MAA6B,CAAC,CAC/E;MAAAiB,OAAA,GAAY1tB,YAAY;IAAA,EAAI;EAAE;AACvC;AACA;EAAA,QAAAgM,SAAA,oBAAAA,SAAA,KA39FoGrO,EAAE,CAAAsO,iBAAA,CA29FXkhB,cAAc,EAAc,CAAC;IAC5GxhB,IAAI,EAAE3M,QAAQ;IACdkN,IAAI,EAAE,CAAC;MACCyhB,YAAY,EAAE,CACV/W,wBAAwB,EACxB6I,iBAAiB,EACjBuB,iBAAiB,EACjBzU,2BAA2B,EAC3BH,yBAAyB,EACzBK,wBAAwB,EACxBE,6BAA6B,EAC7BE,yBAAyB,EACzBE,yBAAyB,EACzBE,2BAA2B,EAC3BE,+BAA+B,EAC/BE,8BAA8B,EAC9BE,sBAAsB,EACtBE,iCAAiC,EACjC7C,oBAAoB,CACvB;MACD8iB,OAAO,EAAE,CACL1tB,YAAY,CACf;MACD4tB,OAAO,EAAE,CACL5M,iBAAiB,EACjBvB,iBAAiB,EACjBlT,2BAA2B,EAC3BH,yBAAyB,EACzBK,wBAAwB,EACxBE,6BAA6B,EAC7BE,yBAAyB,EACzBE,yBAAyB,EACzBE,2BAA2B,EAC3BE,+BAA+B,EAC/BE,8BAA8B,EAC9BE,sBAAsB,EACtBE,iCAAiC,CACpC;MACD8e,SAAS,EAAE,CACP;QAAEjB,OAAO,EAAEvK,uBAAuB;QAAE0M,QAAQ,EAAEhB;MAA6B,CAAC;IAEpF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS1f,yBAAyB,EAAEF,yBAAyB,EAAEjC,oBAAoB,EAAE6B,wBAAwB,EAAEgB,iCAAiC,EAAEJ,8BAA8B,EAAEV,6BAA6B,EAAEM,2BAA2B,EAAEV,2BAA2B,EAAEkT,iBAAiB,EAAErT,yBAAyB,EAAE4U,iBAAiB,EAAEZ,cAAc,EAAE+M,cAAc,EAAE5f,sBAAsB,EAAEJ,+BAA+B,EAAE4T,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}