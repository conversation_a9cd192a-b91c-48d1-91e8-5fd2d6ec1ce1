{"ast": null, "code": "import { forkJoin, Subject, takeUntil } from 'rxjs';\nimport { stringify } from 'qs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"src/app/store/services/setting.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/progressspinner\";\nfunction AccountInvoicesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountInvoicesComponent_p_table_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 12);\n    i0.ɵɵtext(2, \"Billing Doc # \");\n    i0.ɵɵelement(3, \"p-sortIcon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\");\n    i0.ɵɵtext(5, \"Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 14);\n    i0.ɵɵtext(7, \"PO # \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Doc Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 15);\n    i0.ɵɵtext(12, \"Total Amount \");\n    i0.ɵɵelement(13, \"p-sortIcon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Open Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 16);\n    i0.ɵɵtext(17, \"Billing Date \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 18);\n    i0.ɵɵtext(20, \"Due Date \");\n    i0.ɵɵelement(21, \"p-sortIcon\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 20);\n    i0.ɵɵtext(23, \"Days Past Due \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\");\n    i0.ɵɵtext(26, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountInvoicesComponent_p_table_6_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AccountInvoicesComponent_p_table_6_ng_template_3_Template_button_click_21_listener() {\n      const invoice_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadPDF(invoice_r4.INVOICE));\n    });\n    i0.ɵɵtext(22, \"View Details/PDF\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invoice_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", invoice_r4.INVOICE, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(invoice_r4.PURCH_NO);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.typeByCode[invoice_r4.DOC_TYPE]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 5, invoice_r4.AMOUNT, invoice_r4.CURRENCY), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatDate(invoice_r4.DOC_DATE), \" \");\n  }\n}\nfunction AccountInvoicesComponent_p_table_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 9, 0);\n    i0.ɵɵlistener(\"sortFunction\", function AccountInvoicesComponent_p_table_6_Template_p_table_sortFunction_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort($event));\n    });\n    i0.ɵɵtemplate(2, AccountInvoicesComponent_p_table_6_ng_template_2_Template, 27, 0, \"ng-template\", 10)(3, AccountInvoicesComponent_p_table_6_ng_template_3_Template, 23, 8, \"ng-template\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.invoices)(\"rows\", 10)(\"rowHover\", true)(\"loading\", ctx_r1.loading)(\"paginator\", true)(\"customSort\", true);\n  }\n}\nfunction AccountInvoicesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(\"No records found.\");\n  }\n}\nexport class AccountInvoicesComponent {\n  constructor(accountservice, settingsservice, router) {\n    this.accountservice = accountservice;\n    this.settingsservice = settingsservice;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.invoices = [];\n    this.loading = false;\n    this.customer = {};\n    this.statuses = '';\n    this.types = '';\n    this.isSidebarHidden = false;\n  }\n  ngOnInit() {\n    this.loading = true;\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.loadInitialData(response.customer.customer_id);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  loadInitialData(soldToParty) {\n    forkJoin({\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\n      invoiceStatuses: this.accountservice.fetchOrderStatuses({\n        'filters[type][$eq]': 'INVOICE_STATUS'\n      }),\n      invoiceTypes: this.accountservice.fetchOrderStatuses({\n        'filters[type][$eq]': 'INVOICE_TYPE'\n      })\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: ({\n        partnerFunction,\n        invoiceStatuses,\n        invoiceTypes\n      }) => {\n        this.statuses = (invoiceStatuses?.data || []).map(val => val.code).join(';');\n        this.types = (invoiceTypes?.data || []).map(val => val.code).join(';');\n        this.customer = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n        if (this.customer) {\n          this.fetchInvoices();\n        }\n      },\n      error: error => {\n        console.error('Error fetching initial data:', error);\n      }\n    });\n  }\n  fetchInvoices() {\n    this.accountservice.getInvoices({\n      DOC_STATUS: this.statuses,\n      DOC_TYPE: this.types,\n      SOLDTO: this.customer?.customer_id,\n      VKORG: this.customer?.sales_organization,\n      COUNT: 1000,\n      DOCUMENT_DATE: '',\n      DOCUMENT_DATE_TO: ''\n    }).subscribe(response => {\n      this.loading = false;\n      this.invoices = response?.INVOICELIST || [];\n    }, () => {\n      this.loading = false;\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  goToTicket(event) {\n    const params = stringify({\n      filters: {\n        $and: [{\n          bp_id: {\n            $eq: [event.data.account_id]\n          }\n        }]\n      }\n    });\n    this.accountservice.search(params).subscribe(res => {\n      if (res?.length) {\n        this.router.navigate(['/store/service-ticket-details', event.data.id, res[0].documentId]);\n      }\n    });\n  }\n  customSort(event) {\n    const sort = {\n      All: (a, b) => {\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\n        return 0;\n      },\n      Support_Team: (a, b) => {\n        const field = event.field || '';\n        const aValue = a[field] ?? '';\n        const bValue = b[field] ?? '';\n        return aValue.toString().localeCompare(bValue.toString()) * (event.order || 1);\n      }\n    };\n    event.data?.sort(event.field == \"support_team\" || event.field == \"assigned_to\" || event.field == \"subject\" ? sort.Support_Team : sort.All);\n  }\n  static {\n    this.ɵfac = function AccountInvoicesComponent_Factory(t) {\n      return new (t || AccountInvoicesComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.SettingsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountInvoicesComponent,\n      selectors: [[\"app-account-invoices\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[\"myTab\", \"\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\", \"sortFunction\", 4, \"ngIf\"], [\"class\", \"w-100\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"responsiveLayout\", \"scroll\", 3, \"sortFunction\", \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pSortableColumn\", \"INVOICE\"], [\"field\", \"SD_DOC\"], [\"pSortableColumn\", \"PURCH_NO\"], [\"pSortableColumn\", \"AMOUNT\"], [\"pSortableColumn\", \"DOC_DATE\"], [\"field\", \"DOC_DATE\"], [\"pSortableColumn\", \"DUE_DATE\"], [\"field\", \"DUE_DATE\"], [\"pSortableColumn\", \"DAYS_PAST_DUE\"], [\"field\", \"DAYS_PAST_DUE\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-semibold\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"justify-content-center\", \"h-2rem\", 3, \"click\"], [1, \"w-100\"]],\n      template: function AccountInvoicesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Invoices\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, AccountInvoicesComponent_div_5_Template, 2, 0, \"div\", 5)(6, AccountInvoicesComponent_p_table_6_Template, 4, 6, \"p-table\", 6)(7, AccountInvoicesComponent_div_7_Template, 2, 1, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.invoices.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.invoices.length);\n        }\n      },\n      dependencies: [i4.NgIf, i5.PrimeTemplate, i6.Table, i6.SortableColumn, i6.SortIcon, i7.ProgressSpinner, i4.CurrencyPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["fork<PERSON><PERSON>n", "Subject", "takeUntil", "stringify", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "AccountInvoicesComponent_p_table_6_ng_template_3_Template_button_click_21_listener", "invoice_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "downloadPDF", "INVOICE", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "PURCH_NO", "typeByCode", "DOC_TYPE", "ɵɵpipeBind2", "AMOUNT", "CURRENCY", "formatDate", "DOC_DATE", "AccountInvoicesComponent_p_table_6_Template_p_table_sortFunction_0_listener", "$event", "_r1", "customSort", "ɵɵtemplate", "AccountInvoicesComponent_p_table_6_ng_template_2_Template", "AccountInvoicesComponent_p_table_6_ng_template_3_Template", "ɵɵproperty", "invoices", "loading", "AccountInvoicesComponent", "constructor", "accountservice", "settingsservice", "router", "unsubscribe$", "customer", "statuses", "types", "isSidebarHidden", "ngOnInit", "account", "pipe", "subscribe", "response", "loadInitialData", "customer_id", "ngOnDestroy", "next", "complete", "soldToParty", "partnerFunction", "getPartnerFunction", "invoiceStatuses", "fetchOrderStatuses", "invoiceTypes", "data", "map", "val", "code", "join", "find", "o", "partner_function", "fetchInvoices", "error", "console", "getInvoices", "DOC_STATUS", "SOLDTO", "VKORG", "sales_organization", "COUNT", "DOCUMENT_DATE", "DOCUMENT_DATE_TO", "INVOICELIST", "toggleSidebar", "goToTicket", "event", "params", "filters", "$and", "bp_id", "$eq", "account_id", "search", "res", "length", "navigate", "id", "documentId", "sort", "All", "a", "b", "field", "order", "Support_Team", "aValue", "bValue", "toString", "localeCompare", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "SettingsService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "AccountInvoicesComponent_Template", "rf", "ctx", "AccountInvoicesComponent_div_5_Template", "AccountInvoicesComponent_p_table_6_Template", "AccountInvoicesComponent_div_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { forkJoin, Subject, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\nimport { Router } from '@angular/router';\r\nimport { SortEvent } from 'primeng/api';\r\nimport { stringify } from 'qs';\r\nimport { ServiceTicketService } from 'src/app/store/services/service-ticket.service';\r\nimport { SettingsService } from 'src/app/store/services/setting.service';\r\n\r\n@Component({\r\n  selector: 'app-account-invoices',\r\n  templateUrl: './account-invoices.component.html',\r\n  styleUrl: './account-invoices.component.scss',\r\n})\r\nexport class AccountInvoicesComponent implements OnInit {\r\n\r\n  private unsubscribe$ = new Subject<void>();\r\n\r\n  invoices: any[] = [];\r\n  loading = false;\r\n  public customer: any = {};\r\n  statuses = '';\r\n  types = '';\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private settingsservice: SettingsService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loading = true;\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.loadInitialData(response.customer.customer_id);\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n\r\n  loadInitialData(soldToParty: string) {\r\n    forkJoin({\r\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\r\n      invoiceStatuses: this.accountservice.fetchOrderStatuses({ 'filters[type][$eq]': 'INVOICE_STATUS' }),\r\n      invoiceTypes: this.accountservice.fetchOrderStatuses({ 'filters[type][$eq]': 'INVOICE_TYPE' })\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: ({ partnerFunction, invoiceStatuses, invoiceTypes }) => {\r\n          this.statuses = (invoiceStatuses?.data || []).map((val: any) => val.code).join(';');\r\n          this.types = (invoiceTypes?.data || []).map((val: any) => val.code).join(';');\r\n          this.customer = partnerFunction.find(\r\n            (o: any) =>\r\n              o.customer_id === soldToParty && o.partner_function === 'SH'\r\n          );\r\n\r\n          if (this.customer) {\r\n            this.fetchInvoices();\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching initial data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchInvoices() {\r\n    this.accountservice.getInvoices({\r\n      DOC_STATUS: this.statuses,\r\n      DOC_TYPE: this.types,\r\n      SOLDTO: this.customer?.customer_id,\r\n      VKORG: this.customer?.sales_organization,\r\n      COUNT: 1000,\r\n      DOCUMENT_DATE: '',\r\n      DOCUMENT_DATE_TO: '',\r\n    }).subscribe((response: any) => {\r\n      this.loading = false;\r\n      this.invoices = response?.INVOICELIST || [];\r\n    }, () => {\r\n      this.loading = false;\r\n    });\r\n  }\r\n\r\n\r\n  isSidebarHidden = false;\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  goToTicket(event: any) {\r\n    const params = stringify({\r\n      filters: {\r\n        $and: [\r\n          {\r\n            bp_id: {\r\n              $eq: [event.data.account_id]\r\n            }\r\n          }\r\n        ]\r\n      },\r\n    });\r\n    this.accountservice.search(params).subscribe((res: any) => {\r\n      if (res?.length) {\r\n        this.router.navigate(['/store/service-ticket-details', event.data.id, res[0].documentId]);\r\n      }\r\n    });\r\n  }\r\n\r\n  customSort(event: SortEvent) {\r\n    const sort = {\r\n      All: (a: any, b: any) => {\r\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\r\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\r\n        return 0;\r\n      },\r\n      Support_Team: (a: any, b: any) => {\r\n        const field = event.field || '';\r\n        const aValue = a[field] ?? '';\r\n        const bValue = b[field] ?? '';\r\n        return aValue.toString().localeCompare(bValue.toString()) * (event.order || 1);\r\n      }\r\n    };\r\n    event.data?.sort(event.field == \"support_team\" || event.field == \"assigned_to\" || event.field == \"subject\" ? sort.Support_Team : sort.All);\r\n  }\r\n\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Invoices</h4>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"loading\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n        <p-table #myTab [value]=\"invoices\" dataKey=\"id\" [rows]=\"10\" [rowHover]=\"true\" [loading]=\"loading\"\r\n            styleClass=\"p-datatable-gridlines\" [paginator]=\"true\" responsiveLayout=\"scroll\"\r\n            *ngIf=\"!loading && invoices.length\" (sortFunction)=\"customSort($event)\" [customSort]=\"true\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n              <tr>\r\n                <th pSortableColumn=\"INVOICE\">Billing Doc # <p-sortIcon field=\"SD_DOC\" /></th>\r\n                <th>Order #</th>\r\n                <th pSortableColumn=\"PURCH_NO\">PO # <p-sortIcon field=\"SD_DOC\" /></th>\r\n                <th>Doc Type</th>\r\n                <th pSortableColumn=\"AMOUNT\">Total Amount <p-sortIcon field=\"SD_DOC\" /></th>\r\n                <th>Open Amount</th>\r\n                <th pSortableColumn=\"DOC_DATE\">Billing Date <p-sortIcon field=\"DOC_DATE\" /></th>\r\n                <th pSortableColumn=\"DUE_DATE\">Due Date <p-sortIcon field=\"DUE_DATE\" /></th>\r\n                <th pSortableColumn=\"DAYS_PAST_DUE\">Days Past Due <p-sortIcon field=\"DAYS_PAST_DUE\" /></th>\r\n                <th>Action</th>\r\n              </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-invoice>\r\n              <tr>\r\n                <td class=\"text-orange-600 cursor-pointer font-semibold\">\r\n                  {{ invoice.INVOICE }}\r\n                </td>\r\n                <td>-</td>\r\n                <td>{{ invoice.PURCH_NO }}</td>\r\n                <td>{{ typeByCode[invoice.DOC_TYPE] }}</td>\r\n                <td>\r\n                  {{ invoice.AMOUNT | currency: invoice.CURRENCY }}\r\n                </td>\r\n                <td>-</td>\r\n                <td>\r\n                  {{ formatDate(invoice.DOC_DATE) }}\r\n                </td>\r\n                <td>-</td>\r\n                <td>-</td>\r\n                <td>\r\n                  <button type=\"button\" class=\"p-element p-ripple p-button p-component justify-content-center h-2rem\"\r\n                    (click)=\"downloadPDF(invoice.INVOICE)\">View Details/PDF</button>\r\n                </td>\r\n              </tr>\r\n            </ng-template>\r\n          </p-table>\r\n        <div class=\"w-100\" *ngIf=\"!loading && !invoices.length\">{{ 'No records found.'}}</div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAInD,SAASC,SAAS,QAAQ,IAAI;;;;;;;;;;;ICCtBC,EAAA,CAAAC,cAAA,aAAwF;IACpFD,EAAA,CAAAE,SAAA,wBAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOEH,EADF,CAAAC,cAAA,SAAI,aAC4B;IAAAD,EAAA,CAAAI,MAAA,qBAAc;IAAAJ,EAAA,CAAAE,SAAA,qBAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9EH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAI,MAAA,YAAK;IAAAJ,EAAA,CAAAE,SAAA,qBAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAE,SAAA,sBAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAE,SAAA,sBAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChFH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAE,SAAA,sBAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAE,SAAA,sBAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3FH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,cAAM;IACZJ,EADY,CAAAG,YAAA,EAAK,EACZ;;;;;;IAIHH,EADF,CAAAC,cAAA,SAAI,aACuD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,QAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAERH,EADF,CAAAC,cAAA,UAAI,kBAEuC;IAAvCD,EAAA,CAAAK,UAAA,mBAAAC,mFAAA;MAAA,MAAAC,UAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,UAAA,CAAAQ,OAAA,CAA4B;IAAA,EAAC;IAACf,EAAA,CAAAI,MAAA,wBAAgB;IAE7DJ,EAF6D,CAAAG,YAAA,EAAS,EAC/D,EACF;;;;;IAlBDH,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAV,UAAA,CAAAQ,OAAA,MACF;IAEIf,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAkB,iBAAA,CAAAX,UAAA,CAAAY,QAAA,CAAsB;IACtBnB,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAkB,iBAAA,CAAAP,MAAA,CAAAS,UAAA,CAAAb,UAAA,CAAAc,QAAA,EAAkC;IAEpCrB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAsB,WAAA,QAAAf,UAAA,CAAAgB,MAAA,EAAAhB,UAAA,CAAAiB,QAAA,OACF;IAGExB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAN,MAAA,CAAAc,UAAA,CAAAlB,UAAA,CAAAmB,QAAA,OACF;;;;;;IAhCR1B,EAAA,CAAAC,cAAA,oBAEgG;IAAxDD,EAAA,CAAAK,UAAA,0BAAAsB,4EAAAC,MAAA;MAAA5B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAgBF,MAAA,CAAAmB,UAAA,CAAAF,MAAA,CAAkB;IAAA,EAAC;IAgBvE5B,EAdA,CAAA+B,UAAA,IAAAC,yDAAA,2BAAgC,IAAAC,yDAAA,2BAcU;IAuB5CjC,EAAA,CAAAG,YAAA,EAAU;;;;IAvCgEH,EAF5D,CAAAkC,UAAA,UAAAvB,MAAA,CAAAwB,QAAA,CAAkB,YAAyB,kBAAkB,YAAAxB,MAAA,CAAAyB,OAAA,CAAoB,mBACxC,oBACsC;;;;;IAwC/FpC,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;IAA9BH,EAAA,CAAAgB,SAAA,EAAwB;IAAxBhB,EAAA,CAAAkB,iBAAA,qBAAwB;;;ADrCxF,OAAM,MAAOmB,wBAAwB;EAUnCC,YACUC,cAA8B,EAC9BC,eAAgC,EAChCC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAXR,KAAAC,YAAY,GAAG,IAAI7C,OAAO,EAAQ;IAE1C,KAAAsC,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAG,KAAK;IACR,KAAAO,QAAQ,GAAQ,EAAE;IACzB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,KAAK,GAAG,EAAE;IAoEV,KAAAC,eAAe,GAAG,KAAK;EA9DnB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,IAAI,CAACG,cAAc,CAACS,OAAO,CACxBC,IAAI,CAACnD,SAAS,CAAC,IAAI,CAAC4C,YAAY,CAAC,CAAC,CAClCQ,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACC,eAAe,CAACD,QAAQ,CAACR,QAAQ,CAACU,WAAW,CAAC;MACrD;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,YAAY,CAACa,IAAI,EAAE;IACxB,IAAI,CAACb,YAAY,CAACc,QAAQ,EAAE;EAC9B;EAEAJ,eAAeA,CAACK,WAAmB;IACjC7D,QAAQ,CAAC;MACP8D,eAAe,EAAE,IAAI,CAACnB,cAAc,CAACoB,kBAAkB,CAACF,WAAW,CAAC;MACpEG,eAAe,EAAE,IAAI,CAACrB,cAAc,CAACsB,kBAAkB,CAAC;QAAE,oBAAoB,EAAE;MAAgB,CAAE,CAAC;MACnGC,YAAY,EAAE,IAAI,CAACvB,cAAc,CAACsB,kBAAkB,CAAC;QAAE,oBAAoB,EAAE;MAAc,CAAE;KAC9F,CAAC,CACCZ,IAAI,CAACnD,SAAS,CAAC,IAAI,CAAC4C,YAAY,CAAC,CAAC,CAClCQ,SAAS,CAAC;MACTK,IAAI,EAAEA,CAAC;QAAEG,eAAe;QAAEE,eAAe;QAAEE;MAAY,CAAE,KAAI;QAC3D,IAAI,CAAClB,QAAQ,GAAG,CAACgB,eAAe,EAAEG,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QACnF,IAAI,CAACtB,KAAK,GAAG,CAACiB,YAAY,EAAEC,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAC7E,IAAI,CAACxB,QAAQ,GAAGe,eAAe,CAACU,IAAI,CACjCC,CAAM,IACLA,CAAC,CAAChB,WAAW,KAAKI,WAAW,IAAIY,CAAC,CAACC,gBAAgB,KAAK,IAAI,CAC/D;QAED,IAAI,IAAI,CAAC3B,QAAQ,EAAE;UACjB,IAAI,CAAC4B,aAAa,EAAE;QACtB;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACN;EAEAD,aAAaA,CAAA;IACX,IAAI,CAAChC,cAAc,CAACmC,WAAW,CAAC;MAC9BC,UAAU,EAAE,IAAI,CAAC/B,QAAQ;MACzBvB,QAAQ,EAAE,IAAI,CAACwB,KAAK;MACpB+B,MAAM,EAAE,IAAI,CAACjC,QAAQ,EAAEU,WAAW;MAClCwB,KAAK,EAAE,IAAI,CAAClC,QAAQ,EAAEmC,kBAAkB;MACxCC,KAAK,EAAE,IAAI;MACXC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE;KACnB,CAAC,CAAC/B,SAAS,CAAEC,QAAa,IAAI;MAC7B,IAAI,CAACf,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,QAAQ,GAAGgB,QAAQ,EAAE+B,WAAW,IAAI,EAAE;IAC7C,CAAC,EAAE,MAAK;MACN,IAAI,CAAC9C,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAKA+C,aAAaA,CAAA;IACX,IAAI,CAACrC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAsC,UAAUA,CAACC,KAAU;IACnB,MAAMC,MAAM,GAAGvF,SAAS,CAAC;MACvBwF,OAAO,EAAE;QACPC,IAAI,EAAE,CACJ;UACEC,KAAK,EAAE;YACLC,GAAG,EAAE,CAACL,KAAK,CAACtB,IAAI,CAAC4B,UAAU;;SAE9B;;KAGN,CAAC;IACF,IAAI,CAACpD,cAAc,CAACqD,MAAM,CAACN,MAAM,CAAC,CAACpC,SAAS,CAAE2C,GAAQ,IAAI;MACxD,IAAIA,GAAG,EAAEC,MAAM,EAAE;QACf,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,+BAA+B,EAAEV,KAAK,CAACtB,IAAI,CAACiC,EAAE,EAAEH,GAAG,CAAC,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC;MAC3F;IACF,CAAC,CAAC;EACJ;EAEAnE,UAAUA,CAACuD,KAAgB;IACzB,MAAMa,IAAI,GAAG;MACXC,GAAG,EAAEA,CAACC,CAAM,EAAEC,CAAM,KAAI;QACtB,IAAID,CAAC,CAACf,KAAK,CAACiB,KAAK,IAAI,EAAE,CAAC,GAAGD,CAAC,CAAChB,KAAK,CAACiB,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,IAAIjB,KAAK,CAACkB,KAAK,IAAI,CAAC,CAAC;QAC/E,IAAIH,CAAC,CAACf,KAAK,CAACiB,KAAK,IAAI,EAAE,CAAC,GAAGD,CAAC,CAAChB,KAAK,CAACiB,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAIjB,KAAK,CAACkB,KAAK,IAAI,CAAC,CAAC;QAC9E,OAAO,CAAC;MACV,CAAC;MACDC,YAAY,EAAEA,CAACJ,CAAM,EAAEC,CAAM,KAAI;QAC/B,MAAMC,KAAK,GAAGjB,KAAK,CAACiB,KAAK,IAAI,EAAE;QAC/B,MAAMG,MAAM,GAAGL,CAAC,CAACE,KAAK,CAAC,IAAI,EAAE;QAC7B,MAAMI,MAAM,GAAGL,CAAC,CAACC,KAAK,CAAC,IAAI,EAAE;QAC7B,OAAOG,MAAM,CAACE,QAAQ,EAAE,CAACC,aAAa,CAACF,MAAM,CAACC,QAAQ,EAAE,CAAC,IAAItB,KAAK,CAACkB,KAAK,IAAI,CAAC,CAAC;MAChF;KACD;IACDlB,KAAK,CAACtB,IAAI,EAAEmC,IAAI,CAACb,KAAK,CAACiB,KAAK,IAAI,cAAc,IAAIjB,KAAK,CAACiB,KAAK,IAAI,aAAa,IAAIjB,KAAK,CAACiB,KAAK,IAAI,SAAS,GAAGJ,IAAI,CAACM,YAAY,GAAGN,IAAI,CAACC,GAAG,CAAC;EAC5I;;;uBApHW9D,wBAAwB,EAAArC,EAAA,CAAA6G,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/G,EAAA,CAAA6G,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjH,EAAA,CAAA6G,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxB9E,wBAAwB;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ7B1H,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAI,MAAA,eAAQ;UAC3DJ,EAD2D,CAAAG,YAAA,EAAK,EAC1D;UAENH,EAAA,CAAAC,cAAA,aAAuB;UA8CnBD,EA7CA,CAAA+B,UAAA,IAAA6F,uCAAA,iBAAwF,IAAAC,2CAAA,qBAKQ,IAAAC,uCAAA,iBAwCxC;UAEhE9H,EADI,CAAAG,YAAA,EAAM,EACJ;;;UA/C2EH,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAkC,UAAA,SAAAyF,GAAA,CAAAvF,OAAA,CAAa;UAKjFpC,EAAA,CAAAgB,SAAA,EAAiC;UAAjChB,EAAA,CAAAkC,UAAA,UAAAyF,GAAA,CAAAvF,OAAA,IAAAuF,GAAA,CAAAxF,QAAA,CAAA2D,MAAA,CAAiC;UAwClB9F,EAAA,CAAAgB,SAAA,EAAkC;UAAlChB,EAAA,CAAAkC,UAAA,UAAAyF,GAAA,CAAAvF,OAAA,KAAAuF,GAAA,CAAAxF,QAAA,CAAA2D,MAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}