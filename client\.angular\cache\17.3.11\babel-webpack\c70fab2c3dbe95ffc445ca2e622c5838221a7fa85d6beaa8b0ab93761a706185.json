{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nlet AutoFocus = /*#__PURE__*/(() => {\n  class AutoFocus {\n    host;\n    constructor(host) {\n      this.host = host;\n    }\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    focused = false;\n    ngAfterContentChecked() {\n      if (!this.focused) {\n        if (this.autofocus) {\n          const focusableElements = DomHandler.getFocusableElements(this.host.nativeElement);\n          if (focusableElements.length === 0) {\n            this.host.nativeElement.focus();\n          }\n          if (focusableElements.length > 0) {\n            focusableElements[0].focus();\n          }\n          this.focused = true;\n        }\n      }\n    }\n    static ɵfac = function AutoFocus_Factory(t) {\n      return new (t || AutoFocus)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AutoFocus,\n      selectors: [[\"\", \"pAutoFocus\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        autofocus: \"autofocus\"\n      }\n    });\n  }\n  return AutoFocus;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AutoFocusModule = /*#__PURE__*/(() => {\n  class AutoFocusModule {\n    static ɵfac = function AutoFocusModule_Factory(t) {\n      return new (t || AutoFocusModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AutoFocusModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return AutoFocusModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n//# sourceMappingURL=primeng-autofocus.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}