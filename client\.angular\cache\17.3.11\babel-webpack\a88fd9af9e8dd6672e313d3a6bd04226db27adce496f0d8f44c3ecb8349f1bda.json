{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SessionRoutingModule } from './session-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { MenubarModule } from 'primeng/menubar';\nimport { GalleriaModule } from 'primeng/galleria';\nimport { CarouselModule } from 'primeng/carousel';\nimport { TagModule } from 'primeng/tag';\nimport * as i0 from \"@angular/core\";\nexport let SessionModule = /*#__PURE__*/(() => {\n  class SessionModule {\n    static {\n      this.ɵfac = function SessionModule_Factory(t) {\n        return new (t || SessionModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SessionModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, SessionRoutingModule, DialogModule, InputTextModule, ButtonModule, DropdownModule, MenubarModule, GalleriaModule, CarouselModule, TagModule]\n      });\n    }\n  }\n  return SessionModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}