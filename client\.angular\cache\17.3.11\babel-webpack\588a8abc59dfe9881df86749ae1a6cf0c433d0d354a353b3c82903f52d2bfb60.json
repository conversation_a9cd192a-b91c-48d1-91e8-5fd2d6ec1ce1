{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./activities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = () => [\"/auth/signup\"];\nfunction ActivitiesComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 21)(4, \"div\", 22);\n    i0.ɵɵtext(5, \" Subject \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 24)(8, \"div\", 22);\n    i0.ɵɵtext(9, \" Status \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 26)(12, \"div\", 22);\n    i0.ɵɵtext(13, \" Start Date/Time \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 28)(16, \"div\", 22);\n    i0.ɵɵtext(17, \" End Date/Time \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 30)(24, \"div\", 22);\n    i0.ɵɵtext(25, \" Category \");\n    i0.ɵɵelement(26, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\");\n    i0.ɵɵtext(28, \"Notes\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActivitiesComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 32)(1, \"td\", 20);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/\" + activity_r3.bp_id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", activity_r3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.description) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.start_date) ? i0.ɵɵpipeBind3(9, 10, activity_r3.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.end_date) ? i0.ɵɵpipeBind3(12, 14, activity_r3.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.account) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.contact) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.category) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r3 == null ? null : activity_r3.notes) || \"-\", \" \");\n  }\n}\nfunction ActivitiesComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"No activities found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActivitiesComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"Loading activities data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ActivitiesComponent {\n  constructor(activitiesservice) {\n    this.activitiesservice = activitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.activities = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Activities',\n      routerLink: ['/store/activities']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Appointments',\n      code: 'MA'\n    }, {\n      name: 'My Appointments This Month',\n      code: 'MAM'\n    }, {\n      name: 'My Appointments This Week',\n      code: 'MAW'\n    }, {\n      name: 'My Appointments Today',\n      code: 'MAT'\n    }, {\n      name: 'My Completed Appointments',\n      code: 'MCA'\n    }];\n  }\n  loadActivities(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.activitiesservice.getActivities(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.activities = response?.data || [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching activities', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadActivities({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ActivitiesComponent_Factory(t) {\n      return new (t || ActivitiesComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesComponent,\n      selectors: [[\"app-activities\"]],\n      decls: 22,\n      vars: 15,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Prospects\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"description\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"description\"], [\"pSortableColumn\", \"activity_status\"], [\"field\", \"activity_status\"], [\"pSortableColumn\", \"start_date\"], [\"field\", \"start_date\"], [\"pSortableColumn\", \"end_date\"], [\"field\", \"end_date\"], [\"pSortableColumn\", \"category\"], [\"field\", \"category\"], [1, \"cursor-pointer\", 3, \"routerLink\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\"], [\"colspan\", \"10\"]],\n      template: function ActivitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivitiesComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function ActivitiesComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(17);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivitiesComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12)(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"p-table\", 15, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function ActivitiesComponent_Template_p_table_onLazyLoad_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadActivities($event));\n          });\n          i0.ɵɵtemplate(18, ActivitiesComponent_ng_template_18_Template, 29, 0, \"ng-template\", 16)(19, ActivitiesComponent_ng_template_19_Template, 21, 18, \"ng-template\", 17)(20, ActivitiesComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18)(21, ActivitiesComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.activities)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i2.RouterLink, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.Table, i5.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i4.TableCheckbox, i4.TableHeaderCheckbox, i6.Dropdown, i7.Breadcrumb, i8.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "activity_r3", "bp_id", "ɵɵadvance", "ɵɵtextInterpolate1", "description", "activity_status", "start_date", "ɵɵpipeBind3", "end_date", "account", "contact", "category", "notes", "ActivitiesComponent", "constructor", "activitiesservice", "unsubscribe$", "activities", "totalRecords", "loading", "globalSearchTerm", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "loadActivities", "event", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getActivities", "subscribe", "next", "response", "data", "meta", "pagination", "total", "error", "console", "onGlobalFilter", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "selectors", "decls", "vars", "consts", "template", "ActivitiesComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ActivitiesComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "ActivitiesComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "ActivitiesComponent_Template_p_dropdown_ngModelChange_10_listener", "selectedActions", "ActivitiesComponent_Template_p_table_onLazyLoad_16_listener", "ɵɵtemplate", "ActivitiesComponent_ng_template_18_Template", "ActivitiesComponent_ng_template_19_Template", "ActivitiesComponent_ng_template_20_Template", "ActivitiesComponent_ng_template_21_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject } from 'rxjs';\r\nimport { ActivitiesService } from './activities.service';\r\nimport { Table } from 'primeng/table';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-activities',\r\n  templateUrl: './activities.component.html',\r\n  styleUrl: './activities.component.scss',\r\n})\r\nexport class ActivitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public activities: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n\r\n  constructor(private activitiesservice: ActivitiesService) {}\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Activities', routerLink: ['/store/activities'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Appointments', code: 'MA' },\r\n      { name: 'My Appointments This Month', code: 'MAM' },\r\n      { name: 'My Appointments This Week', code: 'MAW' },\r\n      { name: 'My Appointments Today', code: 'MAT' },\r\n      { name: 'My Completed Appointments', code: 'MCA' },\r\n    ];\r\n  }\r\n\r\n  loadActivities(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.activitiesservice\r\n      .getActivities(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.activities = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching activities', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadActivities({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        placeholder=\"Search Prospects\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n            <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"activities\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadActivities($event)\"\r\n            [loading]=\"loading\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pSortableColumn=\"description\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Subject\r\n                            <p-sortIcon field=\"description\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"activity_status\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Status\r\n                            <p-sortIcon field=\"activity_status\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"start_date\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Start Date/Time\r\n                            <p-sortIcon field=\"start_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"end_date\">\r\n                        <div class=\"flex align-items-center\">\r\n                            End Date/Time\r\n                            <p-sortIcon field=\"end_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Account</th>\r\n                    <th>Contact</th>\r\n                    <th pSortableColumn=\"category\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Category\r\n                            <p-sortIcon field=\"category\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Notes</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-activity>\r\n                <tr class=\"cursor-pointer\" [routerLink]=\"'/store/activities/' + activity.bp_id\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"activity\" />\r\n                    </td>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline\">\r\n                        {{ activity?.description || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.activity_status || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.start_date ? (activity.start_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' :\r\n                        '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.end_date ? (activity.end_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.account || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.contact || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.category || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.notes || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"10\">No activities found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"10\">Loading activities data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;;;;;;;;;;;;;IC6BVC,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAkC,cACO;IACjCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA6C;IAErDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsC,cACG;IACjCD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAiD;IAEzDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAiC,eACQ;IACjCD,EAAA,CAAAI,MAAA,yBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA4C;IAEpDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA+B,eACU;IACjCD,EAAA,CAAAI,MAAA,uBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA0C;IAElDF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEZH,EADJ,CAAAC,cAAA,cAA+B,eACU;IACjCD,EAAA,CAAAI,MAAA,kBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA0C;IAElDF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,aAAK;IACbJ,EADa,CAAAG,YAAA,EAAK,EACb;;;;;IAKDH,EADJ,CAAAC,cAAA,aAAgF,aACnB;IACrDD,EAAA,CAAAE,SAAA,0BAAsC;IAC1CF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAiE;IAC7DD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GAEJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IAEJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA9BsBH,EAAA,CAAAK,UAAA,sCAAAC,WAAA,CAAAC,KAAA,CAAoD;IAEtDP,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAK,UAAA,UAAAC,WAAA,CAAkB;IAGnCN,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAI,WAAA,cACJ;IAEIV,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAK,eAAA,cACJ;IAEIX,EAAA,CAAAQ,SAAA,GAEJ;IAFIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAM,UAAA,IAAAZ,EAAA,CAAAa,WAAA,QAAAP,WAAA,CAAAM,UAAA,mDAEJ;IAEIZ,EAAA,CAAAQ,SAAA,GAEJ;IAFIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAQ,QAAA,IAAAd,EAAA,CAAAa,WAAA,SAAAP,WAAA,CAAAQ,QAAA,mDAEJ;IAEId,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAS,OAAA,cACJ;IAEIf,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAU,OAAA,cACJ;IAEIhB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAW,QAAA,cACJ;IAEIjB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAY,KAAA,cACJ;;;;;IAKAlB,EADJ,CAAAC,cAAA,SAAI,aACiB;IAAAD,EAAA,CAAAI,MAAA,2BAAoB;IACzCJ,EADyC,CAAAG,YAAA,EAAK,EACzC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACiB;IAAAD,EAAA,CAAAI,MAAA,4CAAqC;IAC1DJ,EAD0D,CAAAG,YAAA,EAAK,EAC1D;;;AD/FrB,OAAM,MAAOgB,mBAAmB;EAW9BC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAV7B,KAAAC,YAAY,GAAG,IAAIvB,OAAO,EAAQ;IAGnC,KAAAwB,UAAU,GAAU,EAAE;IACtB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;EAIuB;EAE3DC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,YAAY;MAAEC,UAAU,EAAE,CAAC,mBAAmB;IAAC,CAAE,CAC3D;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAI,CAAE,EACvC;MAAED,IAAI,EAAE,4BAA4B;MAAEC,IAAI,EAAE;IAAK,CAAE,EACnD;MAAED,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAAK,CAAE,EAClD;MAAED,IAAI,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC9C;MAAED,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAAK,CAAE,CACnD;EACH;EAEAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAMa,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,MAAME,SAAS,GAAGL,KAAK,CAACK,SAAS;IACjC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAEjC,IAAI,CAACtB,iBAAiB,CACnBuB,aAAa,CACZN,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAACjB,gBAAgB,CACtB,CACAmB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACxB,UAAU,GAAGwB,QAAQ,EAAEC,IAAI,IAAI,EAAE;QACtC,IAAI,CAACxB,YAAY,GAAGuB,QAAQ,EAAEE,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA6B,cAAcA,CAACC,KAAY,EAAElB,KAAY;IACvC,IAAI,CAACD,cAAc,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC7C;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAAClC,YAAY,CAACwB,IAAI,EAAE;IACxB,IAAI,CAACxB,YAAY,CAACmC,QAAQ,EAAE;EAC9B;;;uBA/DWtC,mBAAmB,EAAAnB,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAnBzC,mBAAmB;MAAA0C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCdxBnE,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAG8E;UAFhFD,EAAA,CAAAqE,gBAAA,2BAAAC,4DAAAC,MAAA;YAAAvE,EAAA,CAAAwE,aAAA,CAAAC,GAAA;YAAAzE,EAAA,CAAA0E,kBAAA,CAAAN,GAAA,CAAA1C,gBAAA,EAAA6C,MAAA,MAAAH,GAAA,CAAA1C,gBAAA,GAAA6C,MAAA;YAAA,OAAAvE,EAAA,CAAA2E,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UAACvE,EAAA,CAAA4E,UAAA,mBAAAC,oDAAAN,MAAA;YAAAvE,EAAA,CAAAwE,aAAA,CAAAC,GAAA;YAAA,MAAAK,MAAA,GAAA9E,EAAA,CAAA+E,WAAA;YAAA,OAAA/E,EAAA,CAAA2E,WAAA,CAASP,GAAA,CAAAd,cAAA,CAAAwB,MAAA,EAAAP,MAAA,CAA2B;UAAA,EAAC;UAA/FvE,EAAA,CAAAG,YAAA,EAE2G;UAC3GH,EAAA,CAAAE,SAAA,YAAiD;UAEzDF,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAC,cAAA,sBACyG;UADzED,EAAA,CAAAqE,gBAAA,2BAAAW,kEAAAT,MAAA;YAAAvE,EAAA,CAAAwE,aAAA,CAAAC,GAAA;YAAAzE,EAAA,CAAA0E,kBAAA,CAAAN,GAAA,CAAAa,eAAA,EAAAV,MAAA,MAAAH,GAAA,CAAAa,eAAA,GAAAV,MAAA;YAAA,OAAAvE,EAAA,CAAA2E,WAAA,CAAAJ,MAAA;UAAA,EAA6B;UAA7DvE,EAAA,CAAAG,YAAA,EACyG;UAGrGH,EAFJ,CAAAC,cAAA,kBAC0I,gBACtF;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAGW;UAF8BD,EAAA,CAAA4E,UAAA,wBAAAM,4DAAAX,MAAA;YAAAvE,EAAA,CAAAwE,aAAA,CAAAC,GAAA;YAAA,OAAAzE,EAAA,CAAA2E,WAAA,CAAcP,GAAA,CAAAhC,cAAA,CAAAmC,MAAA,CAAsB;UAAA,EAAC;UAmF7FvE,EA/EA,CAAAmF,UAAA,KAAAC,2CAAA,2BAAgC,KAAAC,2CAAA,4BAyCW,KAAAC,2CAAA,0BAiCL,KAAAC,2CAAA,0BAKD;UAOjDvF,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAhHoBH,EAAA,CAAAQ,SAAA,GAAyB;UAAeR,EAAxC,CAAAK,UAAA,UAAA+D,GAAA,CAAAxC,eAAA,CAAyB,SAAAwC,GAAA,CAAArC,IAAA,CAAc,uCAAuC;UAMzD/B,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAwF,gBAAA,YAAApB,GAAA,CAAA1C,gBAAA,CAA8B;UAMrD1B,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAK,UAAA,YAAA+D,GAAA,CAAAnC,OAAA,CAAmB;UAACjC,EAAA,CAAAwF,gBAAA,YAAApB,GAAA,CAAAa,eAAA,CAA6B;UACzDjF,EAAA,CAAAK,UAAA,mGAAkG;UAChFL,EAAA,CAAAQ,SAAA,EAA+B;UAA/BR,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAyF,eAAA,KAAAC,GAAA,EAA+B;UAQ3C1F,EAAA,CAAAQ,SAAA,GAAoB;UACuCR,EAD3D,CAAAK,UAAA,UAAA+D,GAAA,CAAA7C,UAAA,CAAoB,YAAyB,YAAA6C,GAAA,CAAA3C,OAAA,CACpC,mBAAmB,iBAAA2C,GAAA,CAAA5C,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}