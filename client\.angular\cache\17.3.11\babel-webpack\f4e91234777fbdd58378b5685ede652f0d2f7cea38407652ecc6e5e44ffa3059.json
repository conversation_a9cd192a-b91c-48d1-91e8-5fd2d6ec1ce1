{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { LoginComponent } from \"./login/login.component\";\nimport { AuthGuard } from \"src/app/core/authentication/auth.guard\";\nimport { SessionComponent } from \"./session.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SessionComponent,\n  children: [{\n    path: \"login\",\n    canActivate: [AuthGuard],\n    component: LoginComponent\n  }, {\n    path: \"reset-password\",\n    canActivate: [AuthGuard],\n    loadChildren: () => import(\"./reset-password/reset-password.module\").then(m => m.ResetPasswordModule)\n  }, {\n    path: \"signup\",\n    canActivate: [AuthGuard],\n    loadChildren: () => import(\"./signup/signup.module\").then(m => m.SignupModule)\n  }, {\n    path: \"\",\n    redirectTo: \"login\",\n    pathMatch: \"full\"\n  }]\n}];\nexport let SessionRoutingModule = /*#__PURE__*/(() => {\n  class SessionRoutingModule {\n    static {\n      this.ɵfac = function SessionRoutingModule_Factory(t) {\n        return new (t || SessionRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SessionRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return SessionRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}