{"ast": null, "code": "'use strict';\n\nvar $defineProperty = require('es-define-property');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar gopd = require('gopd');\n\n/** @type {import('.')} */\nmodule.exports = function defineDataProperty(obj, property, value) {\n  if (!obj || typeof obj !== 'object' && typeof obj !== 'function') {\n    throw new $TypeError('`obj` must be an object or a function`');\n  }\n  if (typeof property !== 'string' && typeof property !== 'symbol') {\n    throw new $TypeError('`property` must be a string or a symbol`');\n  }\n  if (arguments.length > 3 && typeof arguments[3] !== 'boolean' && arguments[3] !== null) {\n    throw new $TypeError('`nonEnumerable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 4 && typeof arguments[4] !== 'boolean' && arguments[4] !== null) {\n    throw new $TypeError('`nonWritable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 5 && typeof arguments[5] !== 'boolean' && arguments[5] !== null) {\n    throw new $TypeError('`nonConfigurable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 6 && typeof arguments[6] !== 'boolean') {\n    throw new $TypeError('`loose`, if provided, must be a boolean');\n  }\n  var nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n  var nonWritable = arguments.length > 4 ? arguments[4] : null;\n  var nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n  var loose = arguments.length > 6 ? arguments[6] : false;\n\n  /* @type {false | TypedPropertyDescriptor<unknown>} */\n  var desc = !!gopd && gopd(obj, property);\n  if ($defineProperty) {\n    $defineProperty(obj, property, {\n      configurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n      enumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n      value: value,\n      writable: nonWritable === null && desc ? desc.writable : !nonWritable\n    });\n  } else if (loose || !nonEnumerable && !nonWritable && !nonConfigurable) {\n    // must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n    obj[property] = value; // eslint-disable-line no-param-reassign\n  } else {\n    throw new $SyntaxError('This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.');\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}