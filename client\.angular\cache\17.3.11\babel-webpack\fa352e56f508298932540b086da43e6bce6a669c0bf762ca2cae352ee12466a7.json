{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Klingon [tlh]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/amaranthrose\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var numbersNouns = 'pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut'.split('_');\n  function translateFuture(output) {\n    var time = output;\n    time = output.indexOf('jaj') !== -1 ? time.slice(0, -3) + 'leS' : output.indexOf('jar') !== -1 ? time.slice(0, -3) + 'waQ' : output.indexOf('DIS') !== -1 ? time.slice(0, -3) + 'nem' : time + ' pIq';\n    return time;\n  }\n  function translatePast(output) {\n    var time = output;\n    time = output.indexOf('jaj') !== -1 ? time.slice(0, -3) + 'Hu’' : output.indexOf('jar') !== -1 ? time.slice(0, -3) + 'wen' : output.indexOf('DIS') !== -1 ? time.slice(0, -3) + 'ben' : time + ' ret';\n    return time;\n  }\n  function translate(number, withoutSuffix, string, isFuture) {\n    var numberNoun = numberAsNoun(number);\n    switch (string) {\n      case 'ss':\n        return numberNoun + ' lup';\n      case 'mm':\n        return numberNoun + ' tup';\n      case 'hh':\n        return numberNoun + ' rep';\n      case 'dd':\n        return numberNoun + ' jaj';\n      case 'MM':\n        return numberNoun + ' jar';\n      case 'yy':\n        return numberNoun + ' DIS';\n    }\n  }\n  function numberAsNoun(number) {\n    var hundred = Math.floor(number % 1000 / 100),\n      ten = Math.floor(number % 100 / 10),\n      one = number % 10,\n      word = '';\n    if (hundred > 0) {\n      word += numbersNouns[hundred] + 'vatlh';\n    }\n    if (ten > 0) {\n      word += (word !== '' ? ' ' : '') + numbersNouns[ten] + 'maH';\n    }\n    if (one > 0) {\n      word += (word !== '' ? ' ' : '') + numbersNouns[one];\n    }\n    return word === '' ? 'pagh' : word;\n  }\n  var tlh = moment.defineLocale('tlh', {\n    months: 'tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’'.split('_'),\n    monthsShort: 'jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    weekdaysShort: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    weekdaysMin: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[DaHjaj] LT',\n      nextDay: '[wa’leS] LT',\n      nextWeek: 'LLL',\n      lastDay: '[wa’Hu’] LT',\n      lastWeek: 'LLL',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: translateFuture,\n      past: translatePast,\n      s: 'puS lup',\n      ss: translate,\n      m: 'wa’ tup',\n      mm: translate,\n      h: 'wa’ rep',\n      hh: translate,\n      d: 'wa’ jaj',\n      dd: translate,\n      M: 'wa’ jar',\n      MM: translate,\n      y: 'wa’ DIS',\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return tlh;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "numbersNouns", "split", "translateFuture", "output", "time", "indexOf", "slice", "translatePast", "translate", "number", "withoutSuffix", "string", "isFuture", "numberNoun", "numberAsNoun", "hundred", "Math", "floor", "ten", "one", "word", "tlh", "defineLocale", "months", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/tlh.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Klingon [tlh]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/amaranthrose\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var numbersNouns = 'pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut'.split('_');\n\n    function translateFuture(output) {\n        var time = output;\n        time =\n            output.indexOf('jaj') !== -1\n                ? time.slice(0, -3) + 'leS'\n                : output.indexOf('jar') !== -1\n                  ? time.slice(0, -3) + 'waQ'\n                  : output.indexOf('DIS') !== -1\n                    ? time.slice(0, -3) + 'nem'\n                    : time + ' pIq';\n        return time;\n    }\n\n    function translatePast(output) {\n        var time = output;\n        time =\n            output.indexOf('jaj') !== -1\n                ? time.slice(0, -3) + 'Hu’'\n                : output.indexOf('jar') !== -1\n                  ? time.slice(0, -3) + 'wen'\n                  : output.indexOf('DIS') !== -1\n                    ? time.slice(0, -3) + 'ben'\n                    : time + ' ret';\n        return time;\n    }\n\n    function translate(number, withoutSuffix, string, isFuture) {\n        var numberNoun = numberAsNoun(number);\n        switch (string) {\n            case 'ss':\n                return numberNoun + ' lup';\n            case 'mm':\n                return numberNoun + ' tup';\n            case 'hh':\n                return numberNoun + ' rep';\n            case 'dd':\n                return numberNoun + ' jaj';\n            case 'MM':\n                return numberNoun + ' jar';\n            case 'yy':\n                return numberNoun + ' DIS';\n        }\n    }\n\n    function numberAsNoun(number) {\n        var hundred = Math.floor((number % 1000) / 100),\n            ten = Math.floor((number % 100) / 10),\n            one = number % 10,\n            word = '';\n        if (hundred > 0) {\n            word += numbersNouns[hundred] + 'vatlh';\n        }\n        if (ten > 0) {\n            word += (word !== '' ? ' ' : '') + numbersNouns[ten] + 'maH';\n        }\n        if (one > 0) {\n            word += (word !== '' ? ' ' : '') + numbersNouns[one];\n        }\n        return word === '' ? 'pagh' : word;\n    }\n\n    var tlh = moment.defineLocale('tlh', {\n        months: 'tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’'.split(\n            '_'\n        ),\n        monthsShort:\n            'jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split(\n            '_'\n        ),\n        weekdaysShort:\n            'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n        weekdaysMin:\n            'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[DaHjaj] LT',\n            nextDay: '[wa’leS] LT',\n            nextWeek: 'LLL',\n            lastDay: '[wa’Hu’] LT',\n            lastWeek: 'LLL',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: translateFuture,\n            past: translatePast,\n            s: 'puS lup',\n            ss: translate,\n            m: 'wa’ tup',\n            mm: translate,\n            h: 'wa’ rep',\n            hh: translate,\n            d: 'wa’ jaj',\n            dd: translate,\n            M: 'wa’ jar',\n            MM: translate,\n            y: 'wa’ DIS',\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return tlh;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,YAAY,GAAG,gDAAgD,CAACC,KAAK,CAAC,GAAG,CAAC;EAE9E,SAASC,eAAeA,CAACC,MAAM,EAAE;IAC7B,IAAIC,IAAI,GAAGD,MAAM;IACjBC,IAAI,GACAD,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GACtBD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBH,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAC1BD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBH,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAC1BD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBF,IAAI,GAAG,MAAM;IAC3B,OAAOA,IAAI;EACf;EAEA,SAASG,aAAaA,CAACJ,MAAM,EAAE;IAC3B,IAAIC,IAAI,GAAGD,MAAM;IACjBC,IAAI,GACAD,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GACtBD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBH,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAC1BD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBH,MAAM,CAACE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAC1BD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GACzBF,IAAI,GAAG,MAAM;IAC3B,OAAOA,IAAI;EACf;EAEA,SAASI,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IACxD,IAAIC,UAAU,GAAGC,YAAY,CAACL,MAAM,CAAC;IACrC,QAAQE,MAAM;MACV,KAAK,IAAI;QACL,OAAOE,UAAU,GAAG,MAAM;MAC9B,KAAK,IAAI;QACL,OAAOA,UAAU,GAAG,MAAM;MAC9B,KAAK,IAAI;QACL,OAAOA,UAAU,GAAG,MAAM;MAC9B,KAAK,IAAI;QACL,OAAOA,UAAU,GAAG,MAAM;MAC9B,KAAK,IAAI;QACL,OAAOA,UAAU,GAAG,MAAM;MAC9B,KAAK,IAAI;QACL,OAAOA,UAAU,GAAG,MAAM;IAClC;EACJ;EAEA,SAASC,YAAYA,CAACL,MAAM,EAAE;IAC1B,IAAIM,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAER,MAAM,GAAG,IAAI,GAAI,GAAG,CAAC;MAC3CS,GAAG,GAAGF,IAAI,CAACC,KAAK,CAAER,MAAM,GAAG,GAAG,GAAI,EAAE,CAAC;MACrCU,GAAG,GAAGV,MAAM,GAAG,EAAE;MACjBW,IAAI,GAAG,EAAE;IACb,IAAIL,OAAO,GAAG,CAAC,EAAE;MACbK,IAAI,IAAIpB,YAAY,CAACe,OAAO,CAAC,GAAG,OAAO;IAC3C;IACA,IAAIG,GAAG,GAAG,CAAC,EAAE;MACTE,IAAI,IAAI,CAACA,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,IAAIpB,YAAY,CAACkB,GAAG,CAAC,GAAG,KAAK;IAChE;IACA,IAAIC,GAAG,GAAG,CAAC,EAAE;MACTC,IAAI,IAAI,CAACA,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,IAAIpB,YAAY,CAACmB,GAAG,CAAC;IACxD;IACA,OAAOC,IAAI,KAAK,EAAE,GAAG,MAAM,GAAGA,IAAI;EACtC;EAEA,IAAIC,GAAG,GAAGtB,MAAM,CAACuB,YAAY,CAAC,KAAK,EAAE;IACjCC,MAAM,EAAE,iMAAiM,CAACtB,KAAK,CAC3M,GACJ,CAAC;IACDuB,WAAW,EACP,yHAAyH,CAACvB,KAAK,CAC3H,GACJ,CAAC;IACLwB,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,0DAA0D,CAACzB,KAAK,CACtE,GACJ,CAAC;IACD0B,aAAa,EACT,0DAA0D,CAAC1B,KAAK,CAAC,GAAG,CAAC;IACzE2B,WAAW,EACP,0DAA0D,CAAC3B,KAAK,CAAC,GAAG,CAAC;IACzE4B,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE1C,eAAe;MACvB2C,IAAI,EAAEtC,aAAa;MACnBuC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAEvC,SAAS;MACbwC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAEzC,SAAS;MACb0C,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE3C,SAAS;MACb4C,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE7C,SAAS;MACb8C,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE/C,SAAS;MACbgD,CAAC,EAAE,SAAS;MACZC,EAAE,EAAEjD;IACR,CAAC;IACDkD,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,GAAG;AAEd,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}