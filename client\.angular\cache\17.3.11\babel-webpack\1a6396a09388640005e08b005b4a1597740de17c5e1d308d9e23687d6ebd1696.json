{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const windows = [];\n    const handleError = err => {\n      while (0 < windows.length) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    };\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, openValue => {\n      const window = new Subject();\n      windows.push(window);\n      const closingSubscription = new Subscription();\n      const closeWindow = () => {\n        arrRemove(windows, window);\n        window.complete();\n        closingSubscription.unsubscribe();\n      };\n      let closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector(openValue));\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      subscriber.next(window.asObservable());\n      closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const windowsCopy = windows.slice();\n      for (const window of windowsCopy) {\n        window.next(value);\n      }\n    }, () => {\n      while (0 < windows.length) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, handleError, () => {\n      while (0 < windows.length) {\n        windows.shift().unsubscribe();\n      }\n    }));\n  });\n}\n//# sourceMappingURL=windowToggle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}