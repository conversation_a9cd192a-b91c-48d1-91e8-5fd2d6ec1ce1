import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import { ActivitiesService } from '../../../activities.service';
import { MessageService, ConfirmationService } from 'primeng/api';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  startWith,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';

interface NotesColumn {
  field: string;
  header: string;
}

@Component({
  selector: 'app-task-overview',
  templateUrl: './task-overview.component.html',
  styleUrl: './task-overview.component.scss',
})
export class TaskOverviewComponent implements OnInit {
  private ngUnsubscribe = new Subject<void>();
  public overviewDetails: any = null;
  public accounts$?: Observable<any[]>;
  public accountLoading = false;
  public accountInput$ = new Subject<string>();
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  public employees$?: Observable<any[]>;
  public employeeLoading = false;
  public employeeInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public submitted = false;
  public saving = false;
  public existingActivity: any;
  public id: string = '';
  public editid: string = '';
  public isEditMode = false;
  public notedetails: any = null;
  public notevisible: boolean = false;
  public noteposition: string = 'right';
  public notesubmitted = false;
  public notesaving = false;
  public noteeditid: string = '';

  public dropdowns: Record<string, any[]> = {
    activityDocumentType: [],
    activityStatus: [],
    activityCategory: [],
    activityPriority: [],
  };

  public TaskOverviewForm: FormGroup = this.formBuilder.group({
    subject: ['', [Validators.required]],
    main_account_party_id: ['', [Validators.required]],
    main_contact_party_id: ['', [Validators.required]],
    task_category: ['', [Validators.required]],
    processor_party_id: [''],
    start_date: [''],
    end_date: [''],
    priority: [''],
    activity_status: ['', [Validators.required]],
  });

  public NoteForm: FormGroup = this.formBuilder.group({
    note: ['', [Validators.required]],
  });

  constructor(
    private formBuilder: FormBuilder,
    private activitiesservice: ActivitiesService,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedNotesColumns: NotesColumn[] = [];

  public NotesCols: NotesColumn[] = [
    { field: 'updatedAt', header: 'Last Updated On' },
    { field: 'updatedBy', header: 'Updated By' },
  ];

  sortFieldNotes: string = '';
  sortOrderNotes: number = 1;

  ngOnInit(): void {
    // task successfully added message.
    setTimeout(() => {
      const successMessage = sessionStorage.getItem('taskMessage');
      if (successMessage) {
        this.messageservice.add({
          severity: 'success',
          detail: successMessage,
        });
        sessionStorage.removeItem('taskMessage');
      }
    }, 100);
    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');
    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');
    this.loadActivityDropDown(
      'activityDocumentType',
      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'
    );
    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');
    this.TaskOverviewForm.get('main_account_party_id')
      ?.valueChanges.pipe(
        takeUntil(this.ngUnsubscribe),
        tap((selectedBpId) => {
          if (selectedBpId) {
            this.loadAccountByContacts(selectedBpId);
          } else {
            this.contacts$ = of(this.defaultOptions);
          }
        }),
        catchError((err) => {
          console.error('Account selection error:', err);
          this.contacts$ = of(this.defaultOptions);
          return of();
        })
      )
      .subscribe();
    this.loadAccounts();
    this.loadEmployees();
    this.activitiesservice.activity
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (!response) return;
        this.id = response?.activity_id;
        this.overviewDetails = response;
        this.notedetails = response?.notes;
        if (this.overviewDetails) {
          this.fetchOverviewData(this.overviewDetails);
        }
      });

    this._selectedNotesColumns = this.NotesCols;
  }

  get selectedNotesColumns(): any[] {
    return this._selectedNotesColumns;
  }

  set selectedNotesColumns(val: any[]) {
    this._selectedNotesColumns = this.NotesCols.filter((col) =>
      val.includes(col)
    );
  }

  onNotesColumnReorder(event: any) {
    const draggedCol = this.NotesCols[event.dragIndex];
    this.NotesCols.splice(event.dragIndex, 1);
    this.NotesCols.splice(event.dropIndex, 0, draggedCol);
  }

  customSort(field: string, data: any[], type: 'Notes') {
    if (type === 'Notes') {
      this.sortFieldNotes = field;
      this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;
    }

    data.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrderNotes * result;
    });
  }

  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;

    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      let fields = field.split('.');
      let value = data;
      for (let i = 0; i < fields.length; i++) {
        if (value == null) return null;
        value = value[fields[i]];
      }
      return value;
    }
  }

  loadActivityDropDown(target: string, type: string): void {
    this.activitiesservice
      .getActivityDropdownOptions(type)
      .subscribe((res: any) => {
        this.dropdowns[target] =
          res?.data?.map((attr: any) => ({
            label: attr.description,
            value: attr.code,
          })) ?? [];
      });
  }

  getLabelFromDropdown(dropdownKey: string, value: string): string {
    const item = this.dropdowns[dropdownKey]?.find(
      (opt) => opt.value === value
    );
    return item?.label || value;
  }

  editNote(note: any) {
    this.notevisible = true;
    this.noteeditid = note?.documentId;
    this.NoteForm.patchValue(note);
  }

  fetchOverviewData(activity: any) {
    const mainAccount = activity?.main_account_party_id
      ? {
          bp_id: activity.main_account_party_id,
          bp_full_name: activity.business_partner?.bp_full_name || '',
        }
      : null;

    const mainContact = activity?.main_contact_party_id
      ? {
          bp_id: activity.main_contact_party_id,
          bp_full_name: activity.business_partner_contact?.bp_full_name || '',
        }
      : null;
    const mainprocessor = activity?.processor_party_id
      ? {
          bp_id: activity.processor_party_id,
          bp_full_name: activity.business_partner_processor?.bp_full_name || '',
        }
      : null;
    this.existingActivity = {
      main_account_party_id: mainAccount,
      main_contact_party_id: mainContact,
      processor_party_id: mainprocessor,
      subject: activity?.subject,
      task_category: activity?.task_category,
      start_date: activity?.start_date ? new Date(activity?.start_date) : null,
      end_date: activity?.end_date ? new Date(activity?.end_date) : null,
      activity_status: activity?.activity_status,
    };

    this.editid = activity.documentId;
    this.TaskOverviewForm.patchValue(this.existingActivity);
  }

  private loadAccounts() {
    this.accounts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.accountInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.accountLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',
            [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
          }
          return this.activitiesservice.getPartners(params).pipe(
            map((data: any) => {
              return data || []; // Make sure to return correct data structure
            }),
            tap(() => (this.accountLoading = false)),
            catchError((error) => {
              this.accountLoading = false;
              return of([]);
            })
          );
        })
      )
    );
  }

  private loadAccountByContacts(bpId: string): void {
    this.contacts$ = this.contactInput$.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.contactLoading = true)),
      switchMap((term: string) => {
        const params: any = {
          'filters[bp_company_id][$eq]': bpId,
          'populate[business_partner_person][populate][addresses][populate]':
            '*',
        };

        if (term) {
          params[
            'filters[$or][0][business_partner_person][bp_id][$containsi]'
          ] = term;
          params[
            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'
          ] = term;
        }

        return this.activitiesservice.getPartnersContact(params).pipe(
          map((response: any[]) => response || []),
          tap((contacts: any[]) => {
            this.contactLoading = false;
          }),
          catchError((error) => {
            console.error('Contact loading failed:', error);
            this.contactLoading = false;
            return of([]);
          })
        );
      })
    );
  }

  private loadEmployees(): void {
    this.employees$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.employeeInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.employeeLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'BUP003',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Employee fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.employeeLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  async onNoteSubmit() {
    this.notesubmitted = true;
    this.notevisible = true;

    if (this.NoteForm.invalid) {
      this.notevisible = true;
      return;
    }

    this.notesaving = true;
    const value = { ...this.NoteForm.value };

    const data = {
      activity_id: this.id,
      note: value?.note,
    };

    if (this.noteeditid) {
      this.activitiesservice
        .updateNote(this.noteeditid, data)
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe({
          complete: () => {
            this.notesaving = false;
            this.notevisible = false;
            this.NoteForm.reset();
            this.messageservice.add({
              severity: 'success',
              detail: 'Note Updated Successfully!.',
            });
            this.activitiesservice
              .getActivityByID(this.id)
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe();
          },
          error: (res: any) => {
            this.notesaving = false;
            this.notevisible = true;
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          },
        });
    } else {
      this.activitiesservice
        .createNote(data)
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe({
          complete: () => {
            this.notesaving = false;
            this.notevisible = false;
            this.NoteForm.reset();
            this.messageservice.add({
              severity: 'success',
              detail: 'Note Created Successfully!.',
            });
            this.activitiesservice
              .getActivityByID(this.id)
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe();
          },
          error: (res: any) => {
            this.notesaving = false;
            this.notevisible = true;
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          },
        });
    }
  }

  async onUpdate() {
    this.submitted = true;

    if (this.TaskOverviewForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.TaskOverviewForm.value };

    const data = {
      subject: value?.subject,
      main_account_party_id:
        value?.main_account_party_id?.bp_id ?? value?.main_account_party_id,
      main_contact_party_id:
        value?.main_contact_party_id?.bp_id ?? value?.main_contact_party_id,
      task_category: value?.task_category,
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      processor_party_id:
        value?.processor_party_id?.bp_id ?? value?.processor_party_id,
      priority: value?.priority,
      activity_status: value?.activity_status,
    };

    this.activitiesservice
      .updateActivity(this.editid, data)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (response: any) => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Task Updated successFully!',
          });
          this.activitiesservice
            .getActivityByID(this.id)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe();
          this.isEditMode = false;
        },
        error: (res: any) => {
          this.saving = false;
          this.isEditMode = true;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  confirmRemove(item: any) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item);
      },
    });
  }

  remove(item: any) {
    this.activitiesservice
      .deleteNote(item.documentId)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Record Deleted Successfully!',
          });
          this.activitiesservice
            .getActivityByID(this.id)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe();
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  stripHtml(html: string): string {
    const temp = document.createElement('div');
    temp.innerHTML = html;
    return temp.textContent || temp.innerText || '';
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  get f(): any {
    return this.TaskOverviewForm.controls;
  }

  get fNote(): any {
    return this.NoteForm.controls;
  }

  showDialog(position: string) {
    this.noteposition = position;
    this.notevisible = true;
    this.notesubmitted = false;
    this.NoteForm.reset();
  }

  toggleEdit() {
    this.isEditMode = !this.isEditMode;
  }

  compareById = (a: any, b: any) => a === b;

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
