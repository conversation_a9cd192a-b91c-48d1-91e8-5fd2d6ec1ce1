{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./layout/service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nexport class StoreComponent {\n  constructor(primengConfig, renderer, layoutService) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.layoutService = layoutService;\n  }\n  ngOnInit() {\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n    //optional configuration with the default configuration\n    const config = {\n      ripple: false,\n      //toggles ripple on and off\n      menuMode: 'reveal',\n      //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\n      colorScheme: 'light',\n      //color scheme of the template, valid values are \"light\" and \"dark\"\n      theme: 'snjya',\n      //default component theme for PrimeNG\n      scale: 14 //size of the body font size to scale the whole application\n    };\n    this.layoutService.config.set(config);\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function StoreComponent_Factory(t) {\n      return new (t || StoreComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoreComponent,\n      selectors: [[\"app-store\"]],\n      decls: 1,\n      vars: 0,\n      template: function StoreComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      styles: [\"{\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n  .bg-light-blue {\\n  background: #c3dbff !important;\\n}\\n  .surface-25 {\\n  background-color: rgba(225, 226, 232, 0.5215686275) !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .h-36rem {\\n  height: 36rem !important;\\n}\\n  .h-34rem {\\n  height: 34rem !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n  .min-w-3rem {\\n  min-width: 3rem !important;\\n}\\n  .min-w-16 {\\n  min-width: 16rem !important;\\n}\\n  .min-w-14 {\\n  min-width: 14rem !important;\\n}\\n  .min-h-14 {\\n  min-height: 14rem !important;\\n}\\n  .min-h-18 {\\n  min-height: 18rem !important;\\n}\\n  .min-h-30 {\\n  min-height: 30rem !important;\\n}\\n  .font-900 {\\n  font-weight: 900 !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .p-datatable-wrapper thead p-sorticon svg {\\n  color: var(--white);\\n}\\n  .header-title:before,   .left-border:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  width: 5px;\\n  height: 24px;\\n  background: var(--primary-color);\\n  border-radius: 50px;\\n}\\n  .layout-sidebar {\\n  width: 20rem;\\n}\\n  .layout-content-wrapper {\\n  background: var(--surface-0);\\n}\\n  .bg-whight-light {\\n  background: #f6f7f9;\\n}\\n  .all-overview-body {\\n  min-height: calc(100vh - 90px);\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav {\\n  color: var(--text-color) !important;\\n  background: var(--surface-0) !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  transform: translateY(-50%);\\n  z-index: 99;\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n  .all-overview-body .card-list .v-details-list .v-details-box .text {\\n  min-width: 120px;\\n}\\n  .all-overview-body p-table table thead th {\\n  height: 44px;\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n  .all-overview-body p-table table tbody td {\\n  height: 44px;\\n}\\n  .all-overview-body .v-details-list .v-details-box .text {\\n  min-width: 182px;\\n  min-width: 182px;\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .p-inputtext {\\n  appearance: auto !important;\\n}\\n  .border-left-5 {\\n  border-left: 5px solid var(--orange-200);\\n}\\n  .p-calendar {\\n  display: flex;\\n}\\n  .p-calendar .p-button-icon-only {\\n  width: 3rem;\\n}\\n  .max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .text-shadow-l-blue {\\n  text-shadow: 0 2px 6px rgba(0, 63, 147, 0.8);\\n}\\n  .h-32rem {\\n  height: 32rem !important;\\n}\\n  .h-2-8rem {\\n  height: 2.8rem !important;\\n}\\n  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {\\n  color: var(--text-color);\\n  font-weight: 600;\\n}\\n  p-paginator .p-paginator {\\n  padding: 20px 0;\\n  margin: 1.5rem 0 0 0;\\n  border-top: 1px solid var(--surface-d);\\n  border-radius: 0;\\n}\\n  p-paginator .p-paginator button {\\n  width: 3rem;\\n  height: 2rem;\\n  border-radius: 0.3rem;\\n  border: 1px solid var(--surface-c);\\n}\\n  p-paginator .p-paginator p-dropdown {\\n  display: none;\\n}\\n  .table-sec tbody:before {\\n  line-height: 20px;\\n  content: \\\"_\\\";\\n  color: transparent;\\n  display: block;\\n}\\n  .table-sec tbody tr:nth-child(odd) td {\\n  background: var(--surface-b);\\n}\\n  .table-sec thead th .p-checkbox .p-checkbox-box.p-highlight {\\n  border-color: var(--surface-0);\\n}\\n  .table-sec thead th:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .table-sec tbody td:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .p-datatable-scrollable > .p-datatable-wrapper {\\n  padding-bottom: 12px;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav {\\n  padding: 0 16px;\\n  border-color: var(--surface-100);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li {\\n  position: relative;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  right: -1px;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  background: var(--surface-50);\\n  width: 1px;\\n  height: 20px;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link {\\n  border: none;\\n  padding: 0;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link {\\n  padding: 8px 14px;\\n  min-height: 40px;\\n  color: var(--gray-600);\\n  gap: 0 6px;\\n  border-radius: 10px 10px 0 0;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link:hover {\\n  color: var(--primary-color);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab {\\n  background: #f6f7f9;\\n  border: 2px solid var(--surface-100);\\n  border-bottom: none;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav .p-tabview-ink-bar {\\n  display: none !important;\\n}\\n  .details-tabs-list .p-tabview-panels {\\n  display: none;\\n}\\n  .details-tabs-list .p-tabview-nav-btn.p-link {\\n  background: var(--surface-0);\\n}\\n  .details-tabs-result {\\n  min-height: calc(100vh - 192px);\\n}\\n  .layout-sidebar .layout-menu li:nth-child(2) .layout-menuitem-root-text {\\n  margin: 12px 0;\\n  padding: 12px 0;\\n  border-top: 1px solid var(--surface-c);\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n  .card-heading h4 {\\n  margin-left: 10px !important;\\n  min-height: 30px;\\n  align-items: center;\\n}\\n  .sidebar-hide {\\n  display: none;\\n}\\n  .arrow-btn {\\n  top: 29px;\\n  left: 0;\\n  z-index: 99;\\n}\\n  .arrow-round {\\n  transform: rotate(180deg);\\n}\\n  .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  transform: rotateY(180deg);\\n  position: absolute;\\n  right: 11px;\\n  top: 14px;\\n  color: var(--primary-color);\\n}\\n  .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  display: none !important;\\n}\\n  .p-dropdown-label {\\n  display: flex;\\n  align-items: center;\\n}\\n  .filter-sec p-dropdown .p-dropdown-label,   .filter-sec p-dropdown .p-dropdown-trigger {\\n  color: var(--primary-700);\\n}\\n  .filter-sec .table-multiselect-dropdown .p-overlay.p-component {\\n  left: -140px !important;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-items-wrapper p-multiselectitem li {\\n  margin: 0 0 2px 0 !important;\\n  min-width: 164px;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label-container {\\n  display: none;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label {\\n  color: var(--primary-700);\\n  align-items: center;\\n  display: flex;\\n  text-transform: capitalize;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger {\\n  position: relative;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger chevrondownicon {\\n  display: none;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger::before {\\n  position: absolute;\\n  content: \\\"\\\\e5d4\\\";\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  font-family: \\\"Material Symbols Rounded\\\";\\n  font-size: 1.5rem;\\n  color: var(--primary-700);\\n  line-height: 22px;\\n}\\n  .scrollable-table table thead th {\\n  min-width: 150px;\\n  white-space: nowrap;\\n}\\n  .scrollable-table table thead th.table-checkbox {\\n  min-width: 32px;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:first-child {\\n  background: #f2f2f5;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2) {\\n  background: #f2f2f5;\\n}\\n  .scrollable-table table tbody td {\\n  white-space: nowrap;\\n}\\n  .all-page-details {\\n  width: calc(100% - 28rem);\\n}\\n  .layout-dark .bg-whight-light {\\n  background: var(--surface-0);\\n}\\n  .layout-dark .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab {\\n  background: var(--surface-0);\\n  color: var(--text-color);\\n}\\n  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:first-child {\\n  background: var(--surface-b);\\n}\\n  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2) {\\n  background: var(--surface-b);\\n}\\n  .layout-dark .table-sec tbody tr:nth-child(odd) td {\\n  background: rgba(255, 255, 255, 0.05);\\n}\\n  .p-sortable-column-badge {\\n  display: none !important;\\n}\\n  .note-text {\\n  max-width: 320px;\\n  width: 320px;\\n  white-space: normal !important;\\n}\\n  .multiselect-dropdown {\\n  padding: 0;\\n  height: 3rem;\\n}\\n  .multiselect-dropdown .ng-select-container {\\n  background: none !important;\\n  border: none;\\n  height: 3rem !important;\\n  align-items: center;\\n}\\n  .multiselect-dropdown .ng-select-container .ng-value-container {\\n  height: 2rem;\\n}\\n  .multiselect-dropdown .ng-select-container .ng-value-container .ng-input {\\n  top: 0 !important;\\n  bottom: 0;\\n  margin: auto;\\n  display: flex;\\n  align-items: center;\\n}\\n  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option input {\\n  min-width: 20px;\\n}\\n  .confirm-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .confirm-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .confirm-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .confirm-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button {\\n  height: 2.5rem !important;\\n  width: 8rem !important;\\n  gap: 0.25rem !important;\\n  font-weight: 500 !important;\\n  border: none;\\n  border-radius: 2rem;\\n  justify-content: center;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-reject {\\n  color: var(--red-500) !important;\\n  background-color: var(--red-100) !important;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-accept {\\n  color: var(--primary-700) !important;\\n  background-color: #c3dbff !important;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button .p-button-label {\\n  flex: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["StoreComponent", "constructor", "primengConfig", "renderer", "layoutService", "ngOnInit", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "config", "menuMode", "colorScheme", "theme", "scale", "set", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "LayoutService", "selectors", "decls", "vars", "template", "StoreComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { AppConfig, LayoutService } from './layout/service/app.layout.service';\r\n\r\n@Component({\r\n  selector: 'app-store',\r\n  templateUrl: './store.component.html',\r\n  styleUrl: './store.component.scss',\r\n})\r\nexport class StoreComponent {\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private layoutService: LayoutService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n    //optional configuration with the default configuration\r\n    const config: AppConfig = {\r\n      ripple: false, //toggles ripple on and off\r\n      menuMode: 'reveal', //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\r\n      colorScheme: 'light', //color scheme of the template, valid values are \"light\" and \"dark\"\r\n      theme: 'snjya', //default component theme for PrimeNG\r\n      scale: 14, //size of the body font size to scale the whole application\r\n    };\r\n    this.layoutService.config.set(config);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EACzBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,aAA4B;IAF5B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACH,QAAQ,CAACO,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACL,aAAa,CAACW,MAAM,GAAG,IAAI,CAAC,CAAC;IAClC;IACA,MAAMC,MAAM,GAAc;MACxBD,MAAM,EAAE,KAAK;MAAE;MACfE,QAAQ,EAAE,QAAQ;MAAE;MACpBC,WAAW,EAAE,OAAO;MAAE;MACtBC,KAAK,EAAE,OAAO;MAAE;MAChBC,KAAK,EAAE,EAAE,CAAE;KACZ;IACD,IAAI,CAACd,aAAa,CAACU,MAAM,CAACK,GAAG,CAACL,MAAM,CAAC;EACvC;EAEAM,WAAWA,CAAA;IACT;IACA,MAAMb,IAAI,GAAGI,QAAQ,CAACU,cAAc,CAAC,YAAY,CAAC;IAClD,IAAId,IAAI,EAAE;MACRA,IAAI,CAACe,MAAM,EAAE;IACf;EACF;;;uBArCWtB,cAAc,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAd7B,cAAc;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BZ,EAAA,CAAAc,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}