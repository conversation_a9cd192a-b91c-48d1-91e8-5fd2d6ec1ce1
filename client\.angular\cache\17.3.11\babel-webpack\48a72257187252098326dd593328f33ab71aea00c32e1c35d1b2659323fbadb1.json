{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../opportunities.service\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"@angular/common\";\nfunction OpportunitiesNotesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 13)(6, \"div\", 11);\n    i0.ɵɵtext(7, \" Created At \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 15)(10, \"div\", 11);\n    i0.ɵɵtext(11, \" Updated At \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notes_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stripHtml(notes_r1 == null ? null : notes_r1.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 3, notes_r1 == null ? null : notes_r1.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 6, notes_r1 == null ? null : notes_r1.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 18);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 18);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OpportunitiesNotesComponent {\n  constructor(opportunitiesservice) {\n    this.opportunitiesservice = opportunitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.notedetails = null;\n    this.bp_id = '';\n  }\n  ngOnInit() {\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.notedetails = response?.notes;\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesNotesComponent_Factory(t) {\n      return new (t || OpportunitiesNotesComponent)(i0.ɵɵdirectiveInject(i1.OpportunitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesNotesComponent,\n      selectors: [[\"app-opportunities-notes\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"note\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"note\"], [\"pSortableColumn\", \"createdAt\", 2, \"width\", \"10rem\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"updatedAt\", 2, \"width\", \"10rem\"], [\"field\", \"updatedAt\"], [1, \"border-round-left-lg\"], [\"colspan\", \"8\"]],\n      template: function OpportunitiesNotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, OpportunitiesNotesComponent_ng_template_7_Template, 13, 0, \"ng-template\", 6)(8, OpportunitiesNotesComponent_ng_template_8_Template, 9, 9, \"ng-template\", 7)(9, OpportunitiesNotesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, OpportunitiesNotesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true);\n        }\n      },\n      dependencies: [i2.Table, i3.PrimeTemplate, i2.SortableColumn, i2.SortIcon, i4.Button, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "stripHtml", "notes_r1", "note", "ɵɵpipeBind2", "createdAt", "updatedAt", "OpportunitiesNotesComponent", "constructor", "opportunitiesservice", "unsubscribe$", "notedetails", "bp_id", "ngOnInit", "opportunity", "pipe", "subscribe", "response", "notes", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "OpportunitiesService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesNotesComponent_Template", "rf", "ctx", "ɵɵtemplate", "OpportunitiesNotesComponent_ng_template_7_Template", "OpportunitiesNotesComponent_ng_template_8_Template", "OpportunitiesNotesComponent_ng_template_9_Template", "OpportunitiesNotesComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-notes\\opportunities-notes.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-notes\\opportunities-notes.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-notes',\r\n  templateUrl: './opportunities-notes.component.html',\r\n  styleUrl: './opportunities-notes.component.scss',\r\n})\r\nexport class OpportunitiesNotesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public notedetails: any = null;\r\n  public bp_id: string = '';\r\n\r\n  constructor(private opportunitiesservice: OpportunitiesService) {}\r\n  ngOnInit() {\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.notedetails = response?.notes;\r\n        }\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\"\r\n            [styleClass]=\"'w-5rem font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"notedetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"note\" style=\"width: 50rem;\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Note\r\n                            <p-sortIcon field=\"note\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Created At\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"updatedAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Updated At\r\n                            <p-sortIcon field=\"updatedAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ stripHtml(notes?.note) || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.createdAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;ICYjBC,EAFR,CAAAC,cAAA,SAAI,aAC8E,cAC/B;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAG,SAAA,qBAAsC;IAE9CH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAsD,cACP;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAG,SAAA,qBAA2C;IAEnDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAsD,eACP;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAGvDH,EAFQ,CAAAI,YAAA,EAAM,EACL,EACJ;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IARGJ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,kBAAAA,QAAA,CAAAC,IAAA,cACJ;IAEIV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAW,WAAA,OAAAF,QAAA,kBAAAA,QAAA,CAAAG,SAAA,8BACJ;IAEIZ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAW,WAAA,OAAAF,QAAA,kBAAAA,QAAA,CAAAI,SAAA,8BACJ;;;;;IAKAb,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACnCF,EADmC,CAAAI,YAAA,EAAK,EACnC;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACtDF,EADsD,CAAAI,YAAA,EAAK,EACtD;;;AD9CrB,OAAM,MAAOU,2BAA2B;EAKtCC,YAAoBC,oBAA0C;IAA1C,KAAAA,oBAAoB,GAApBA,oBAAoB;IAJhC,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;IACnC,KAAAoB,WAAW,GAAQ,IAAI;IACvB,KAAAC,KAAK,GAAW,EAAE;EAEwC;EACjEC,QAAQA,CAAA;IACN,IAAI,CAACJ,oBAAoB,CAACK,WAAW,CAClCC,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCM,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACL,KAAK,GAAGK,QAAQ,EAAEL,KAAK;QAC5B,IAAI,CAACD,WAAW,GAAGM,QAAQ,EAAEC,KAAK;MACpC;IACF,CAAC,CAAC;EACN;EAEAjB,SAASA,CAACkB,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,YAAY,CAACiB,IAAI,EAAE;IACxB,IAAI,CAACjB,YAAY,CAACkB,QAAQ,EAAE;EAC9B;;;uBA1BWrB,2BAA2B,EAAAd,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA3BxB,2BAA2B;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhC7C,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAG,SAAA,kBAC4C;UAChDH,EAAA,CAAAI,YAAA,EAAM;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UA2C1BD,EAzCA,CAAA+C,UAAA,IAAAC,kDAAA,0BAAgC,IAAAC,kDAAA,yBAuBQ,IAAAC,kDAAA,yBAaF,KAAAC,mDAAA,yBAKD;UAOjDnD,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;;;UAxDiEJ,EAAA,CAAAK,SAAA,GAAiB;UAC5EL,EAD2D,CAAAoD,UAAA,kBAAiB,sCACvC;UAIhCpD,EAAA,CAAAK,SAAA,GAAqB;UAAwCL,EAA7D,CAAAoD,UAAA,UAAAN,GAAA,CAAA5B,WAAA,CAAqB,YAAyB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}