{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ContactsComponent } from './contacts.component';\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\nimport { AddContactComponent } from './add-contact/add-contact.component';\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ContactsComponent\n}, {\n  path: 'create',\n  component: AddContactComponent\n}, {\n  path: ':id',\n  component: ContactsDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ContactsOverviewComponent\n  }, {\n    path: 'opportunities',\n    component: ContactsOpportunitiesComponent\n  }, {\n    path: 'attachments',\n    component: ContactsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ContactsNotesComponent\n  }, {\n    path: 'activities',\n    component: ContactsActivitiesComponent\n  }, {\n    path: 'activities/detail/:id',\n    component: ActivitiesItemDetailComponent\n  }, {\n    path: 'relationships',\n    component: ContactsRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: ContactsTicketsComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let ContactsRoutingModule = /*#__PURE__*/(() => {\n  class ContactsRoutingModule {\n    static {\n      this.ɵfac = function ContactsRoutingModule_Factory(t) {\n        return new (t || ContactsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ContactsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ContactsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}