{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let MenuService = /*#__PURE__*/(() => {\n  class MenuService {\n    constructor() {\n      this.menuSource = new Subject();\n      this.resetSource = new Subject();\n      this.menuSource$ = this.menuSource.asObservable();\n      this.resetSource$ = this.resetSource.asObservable();\n    }\n    onMenuStateChange(event) {\n      this.menuSource.next(event);\n    }\n    reset() {\n      this.resetSource.next(true);\n    }\n    static {\n      this.ɵfac = function MenuService_Factory(t) {\n        return new (t || MenuService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MenuService,\n        factory: MenuService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MenuService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}