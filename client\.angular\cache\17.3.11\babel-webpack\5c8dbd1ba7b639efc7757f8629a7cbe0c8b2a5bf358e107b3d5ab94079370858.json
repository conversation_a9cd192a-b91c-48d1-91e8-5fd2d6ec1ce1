{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { ActivitiesCallFollowupFormComponent } from 'src/app/store/common-form/activities-call-followup-form/activities-call-followup-form.component';\nimport { OpportunitiesFollowupFormComponent } from 'src/app/store/common-form/opportunities-followup-form/opportunities-followup-form.component';\nimport { ActivitiesTaskFollowupFormComponent } from 'src/app/store/common-form/activities-task-followup-form/activities-task-followup-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/multiselect\";\nimport * as i11 from \"../../../../common-form/activities-task-followup-form/activities-task-followup-form.component\";\nimport * as i12 from \"../../../../common-form/activities-call-followup-form/activities-call-followup-form.component\";\nimport * as i13 from \"../../../../common-form/opportunities-followup-form/opportunities-followup-form.component\";\nfunction TaskFollowItemsComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field, ctx_r1.followupdetails, \"activities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities !== col_r4.field);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"activity_transaction.subject\", ctx_r1.followupdetails, \"activities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TaskFollowItemsComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities === \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities !== \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", followup_r6 == null ? null : followup_r6.type_code) || \"-\", \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followup_r6 == null ? null : followup_r6.partner_name) || \"-\", \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, followup_r6 == null ? null : followup_r6.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 28);\n    i0.ɵɵtemplate(3, TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 29)(4, TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 29)(5, TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"type_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23)(2, \"div\", 24)(3, \"a\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, TaskFollowItemsComponent_ng_template_10_ng_container_5_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(6, \"td\", 26)(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_10_Template_button_click_7_listener($event) {\n      const followup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(followup_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followup_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/activities/calls/\" + (followup_r6 == null ? null : followup_r6.activity_transaction == null ? null : followup_r6.activity_transaction.activity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followup_r6 == null ? null : followup_r6.activity_transaction == null ? null : followup_r6.activity_transaction.subject) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"No sales call follow-up found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"Loading sales call follow-up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderOpportunities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderOpportunities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_22_ng_container_6_Template_th_click_1_listener() {\n      const col_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r10.field, ctx_r1.followupopportunitydetails, \"opportunities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_22_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_22_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r10.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r10.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities === col_r10.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities !== col_r10.field);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_22_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"opportunity.name\", ctx_r1.followupopportunitydetails, \"opportunities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_22_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_22_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TaskFollowItemsComponent_ng_template_22_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities === \"opportunity.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities !== \"opportunity.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedOpportunitiesColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", followupopportunity_r12 == null ? null : followupopportunity_r12.type_code) || \"-\", \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followupopportunity_r12 == null ? null : followupopportunity_r12.partner_name) || \"-\", \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, followupopportunity_r12 == null ? null : followupopportunity_r12.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_23_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 28);\n    i0.ɵɵtemplate(3, TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 29)(4, TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 29)(5, TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r13.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"type_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 31)(2, \"div\", 24)(3, \"a\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, TaskFollowItemsComponent_ng_template_23_ng_container_5_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(6, \"td\", 26)(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_23_Template_button_click_7_listener($event) {\n      const followupopportunity_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmOpportunityRemove(followupopportunity_r12));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/opportunities/\" + (followupopportunity_r12 == null ? null : followupopportunity_r12.opportunity == null ? null : followupopportunity_r12.opportunity.opportunity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followupopportunity_r12 == null ? null : followupopportunity_r12.opportunity == null ? null : followupopportunity_r12.opportunity.name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedOpportunitiesColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"No opportunities follow-up found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"Loading opportunities follow-up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivitiesTask === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivitiesTask === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_35_ng_container_6_Template_th_click_1_listener() {\n      const col_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r16.field, ctx_r1.followuptaskdetails, \"task\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_35_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_35_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r16.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r16.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivitiesTask === col_r16.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivitiesTask !== col_r16.field);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_35_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"activity_transaction.subject\", ctx_r1.followupdetails, \"task\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, TaskFollowItemsComponent_ng_template_35_i_4_Template, 1, 1, \"i\", 15)(5, TaskFollowItemsComponent_ng_template_35_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TaskFollowItemsComponent_ng_template_35_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivitiesTask === \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivitiesTask !== \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesTaskColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Activity Task \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followuptask_r18 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followuptask_r18 == null ? null : followuptask_r18.partner_name) || \"-\", \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followuptask_r18 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, followuptask_r18 == null ? null : followuptask_r18.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_36_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 28);\n    i0.ɵɵtemplate(3, TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_3_Template, 2, 0, \"ng-container\", 29)(4, TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 29)(5, TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r19.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"type_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23)(2, \"div\", 24)(3, \"a\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, TaskFollowItemsComponent_ng_template_36_ng_container_5_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(6, \"td\", 26)(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_ng_template_36_Template_button_click_7_listener($event) {\n      const followuptask_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(followuptask_r18));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followuptask_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/activities/tasks/\" + (followuptask_r18 == null ? null : followuptask_r18.activity_transaction == null ? null : followuptask_r18.activity_transaction.activity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followuptask_r18 == null ? null : followuptask_r18.activity_transaction == null ? null : followuptask_r18.activity_transaction.subject) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesTaskColumns);\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"No tasks follow-up found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskFollowItemsComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"Loading tasks follow-up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TaskFollowItemsComponent {\n  constructor(router, route, activitiesservice, opportunitiesservice, messageservice, confirmationservice) {\n    this.router = router;\n    this.route = route;\n    this.activitiesservice = activitiesservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.followupdetails = [];\n    this.followupopportunitydetails = [];\n    this.followuptaskdetails = [];\n    this.activity_id = '';\n    this.submitted = false;\n    this.saving = false;\n    this.visible = false;\n    this.dropdowns = {\n      activityDocumentType: []\n    };\n    this._selectedActivitiesColumns = [];\n    this._selectedActivitiesTaskColumns = [];\n    this._selectedOpportunitiesColumn = [];\n    this.ActivitiesCols = [{\n      field: 'type_code',\n      header: 'Type'\n    }, {\n      field: 'partner_name',\n      header: 'Responsible'\n    }, {\n      field: 'createdAt',\n      header: 'Created On'\n    }];\n    this.ActivitiesTaskCols = [{\n      field: 'type_code',\n      header: 'Type'\n    }, {\n      field: 'partner_name',\n      header: 'Responsible'\n    }, {\n      field: 'createdAt',\n      header: 'Created On'\n    }];\n    this.OpportunitiesCols = [{\n      field: 'type_code',\n      header: 'Type'\n    }, {\n      field: 'partner_name',\n      header: 'Responsible'\n    }, {\n      field: 'createdAt',\n      header: 'Created On'\n    }];\n    this.sortFieldActivities = '';\n    this.sortOrderActivities = 1;\n    this.sortFieldActivitiesTask = '';\n    this.sortOrderActivitiesTask = 1;\n    this.sortFieldOpportunities = '';\n    this.sortOrderOpportunities = 1;\n  }\n  ngOnInit() {\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM');\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response?.activity_id;\n        const allActivityItems = response?.follow_up_and_related_items || [];\n        const allOpportunityItems = response?.opportunity_followups || [];\n        // Filter only FOLLOW_UP items and inject individual partner_name from each item\n        this.followupdetails = allActivityItems.filter(item => item?.type_code === '0002').map(item => {\n          const partnerFn = item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n          const partnerName = partnerFn?.bp_full_name || null;\n          return {\n            ...item,\n            partner_name: partnerName\n          };\n        });\n        this.followuptaskdetails = allActivityItems.filter(item => item?.type_code === '0006').map(item => {\n          const partnerFn = item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n          const partnerName = partnerFn?.bp_full_name || null;\n          return {\n            ...item,\n            partner_name: partnerName\n          };\n        });\n        this.followupopportunitydetails = allOpportunityItems.filter(item => item?.type_code === '0005').map(item => {\n          const partnerFn = item?.opportunity?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n          const partnerName = partnerFn?.bp_full_name || null;\n          return {\n            ...item,\n            partner_name: partnerName\n          };\n        });\n      }\n    });\n    this._selectedActivitiesColumns = this.ActivitiesCols;\n    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols;\n    this._selectedOpportunitiesColumn = this.OpportunitiesCols;\n  }\n  get selectedActivitiesColumns() {\n    return this._selectedActivitiesColumns;\n  }\n  get selectedActivitiesTaskColumns() {\n    return this._selectedActivitiesTaskColumns;\n  }\n  get selectedOpportunitiesColumns() {\n    return this._selectedOpportunitiesColumn;\n  }\n  set selectedActivitiesColumns(val) {\n    this._selectedActivitiesColumns = this.ActivitiesCols.filter(col => val.includes(col));\n  }\n  set selectedActivitiesTaskColumns(val) {\n    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols.filter(col => val.includes(col));\n  }\n  set selectedOpportunitiesColumns(val) {\n    this._selectedOpportunitiesColumn = this.OpportunitiesCols.filter(col => val.includes(col));\n  }\n  onActivitiesColumnReorder(event) {\n    const draggedCol = this.ActivitiesCols[event.dragIndex];\n    this.ActivitiesCols.splice(event.dragIndex, 1);\n    this.ActivitiesCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  onActivitiesTaskColumnReorder(event) {\n    const draggedCol = this.ActivitiesTaskCols[event.dragIndex];\n    this.ActivitiesTaskCols.splice(event.dragIndex, 1);\n    this.ActivitiesTaskCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  onOpportunitiesColumnReorder(event) {\n    const draggedCol = this.OpportunitiesCols[event.dragIndex];\n    this.OpportunitiesCols.splice(event.dragIndex, 1);\n    this.OpportunitiesCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'activities') {\n      this.sortFieldActivities = field;\n      this.sortOrderActivities = this.sortOrderActivities === 1 ? -1 : 1;\n    } else if (type === 'task') {\n      this.sortFieldActivitiesTask = field;\n      this.sortOrderActivitiesTask = this.sortOrderActivitiesTask === 1 ? -1 : 1;\n    } else {\n      this.sortFieldOpportunities = field;\n      this.sortOrderOpportunities = this.sortOrderOpportunities === 1 ? -1 : 1;\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = null;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return (type === 'activities' ? this.sortOrderActivities : type === 'task' ? this.sortOrderActivitiesTask : this.sortOrderOpportunities) * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  confirmOpportunityRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  removeOpportunity(item) {\n    this.opportunitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  openActivityFollowUpDialog() {\n    this.activityFollowupDialog.showDialog('right');\n  }\n  openOpportunityFollowUpDialog() {\n    this.oppotunityFollowupDialog.showDialog('right');\n  }\n  openActivityTaskFollowUpDialog() {\n    this.activityTaskFollowupDialog.showDialog('right');\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function TaskFollowItemsComponent_Factory(t) {\n      return new (t || TaskFollowItemsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.OpportunitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskFollowItemsComponent,\n      selectors: [[\"app-task-follow-items\"]],\n      viewQuery: function TaskFollowItemsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ActivitiesCallFollowupFormComponent, 5);\n          i0.ɵɵviewQuery(OpportunitiesFollowupFormComponent, 5);\n          i0.ɵɵviewQuery(ActivitiesTaskFollowupFormComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.activityFollowupDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.oppotunityFollowupDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.activityTaskFollowupDialog = _t.first);\n        }\n      },\n      decls: 42,\n      vars: 30,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mb-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [1, \"border-round-right-lg\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"5\", 1, \"border-round-left-lg\", \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"]],\n      template: function TaskFollowItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Follow Up Activities Sales Call\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_Template_p_button_click_5_listener() {\n            return ctx.openActivityFollowUpDialog();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActivitiesColumns, $event) || (ctx.selectedActivitiesColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function TaskFollowItemsComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onActivitiesColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, TaskFollowItemsComponent_ng_template_9_Template, 9, 3, \"ng-template\", 8)(10, TaskFollowItemsComponent_ng_template_10_Template, 8, 3, \"ng-template\", 9)(11, TaskFollowItemsComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, TaskFollowItemsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 1)(15, \"h4\", 2);\n          i0.ɵɵtext(16, \"Follow Up Opportunities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 3)(18, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_Template_p_button_click_18_listener() {\n            return ctx.openOpportunityFollowUpDialog();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_19_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedOpportunitiesColumns, $event) || (ctx.selectedOpportunitiesColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 6)(21, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function TaskFollowItemsComponent_Template_p_table_onColReorder_21_listener($event) {\n            return ctx.onOpportunitiesColumnReorder($event);\n          });\n          i0.ɵɵtemplate(22, TaskFollowItemsComponent_ng_template_22_Template, 9, 3, \"ng-template\", 8)(23, TaskFollowItemsComponent_ng_template_23_Template, 8, 3, \"ng-template\", 9)(24, TaskFollowItemsComponent_ng_template_24_Template, 3, 0, \"ng-template\", 10)(25, TaskFollowItemsComponent_ng_template_25_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 0)(27, \"div\", 1)(28, \"h4\", 2);\n          i0.ɵɵtext(29, \"Follow Up Task\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 3)(31, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function TaskFollowItemsComponent_Template_p_button_click_31_listener() {\n            return ctx.openActivityTaskFollowUpDialog();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_32_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActivitiesTaskColumns, $event) || (ctx.selectedActivitiesTaskColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 6)(34, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function TaskFollowItemsComponent_Template_p_table_onColReorder_34_listener($event) {\n            return ctx.onActivitiesTaskColumnReorder($event);\n          });\n          i0.ɵɵtemplate(35, TaskFollowItemsComponent_ng_template_35_Template, 9, 3, \"ng-template\", 8)(36, TaskFollowItemsComponent_ng_template_36_Template, 8, 3, \"ng-template\", 9)(37, TaskFollowItemsComponent_ng_template_37_Template, 3, 0, \"ng-template\", 10)(38, TaskFollowItemsComponent_ng_template_38_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(39, \"app-opportunities-followup-form\")(40, \"app-activities-call-followup-form\")(41, \"app-activities-task-followup-form\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.ActivitiesCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActivitiesColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.followupdetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.OpportunitiesCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedOpportunitiesColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.followupopportunitydetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.ActivitiesTaskCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActivitiesTaskColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.followuptaskdetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i6.NgControlStatus, i6.NgModel, i7.Table, i4.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i8.ButtonDirective, i8.Button, i9.Tooltip, i10.MultiSelect, i11.ActivitiesTaskFollowupFormComponent, i12.ActivitiesCallFollowupFormComponent, i13.OpportunitiesFollowupFormComponent, i5.DatePipe],\n      styles: [\".followup-popup .p-dialog.p-component.p-dialog-resizable {\\n  width: calc(100vw - 510px) !important;\\n}\\n  .followup-popup .field {\\n  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy90YXNrL3Rhc2stZGV0YWlscy90YXNrLWZvbGxvdy1pdGVtcy90YXNrLWZvbGxvdy1pdGVtcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFUTtFQUNJLHFDQUFBO0FBRFo7QUFJUTtFQUNJLDREQUFBO0FBRloiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLmZvbGxvd3VwLXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cucC1jb21wb25lbnQucC1kaWFsb2ctcmVzaXphYmxlIHtcclxuICAgICAgICAgICAgd2lkdGg6IGNhbGMoMTAwdncgLSA1MTBweCkgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5maWVsZCB7XHJcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDM2MHB4LCAxZnIpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "ActivitiesCallFollowupFormComponent", "OpportunitiesFollowupFormComponent", "ActivitiesTaskFollowupFormComponent", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrderActivities", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "TaskFollowItemsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "followupdetails", "ɵɵtext", "ɵɵtemplate", "TaskFollowItemsComponent_ng_template_9_ng_container_6_i_4_Template", "TaskFollowItemsComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortFieldActivities", "TaskFollowItemsComponent_ng_template_9_Template_th_click_1_listener", "_r1", "TaskFollowItemsComponent_ng_template_9_i_4_Template", "TaskFollowItemsComponent_ng_template_9_i_5_Template", "TaskFollowItemsComponent_ng_template_9_ng_container_6_Template", "selectedActivitiesColumns", "getLabelFromDropdown", "followup_r6", "type_code", "partner_name", "ɵɵpipeBind2", "createdAt", "TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_3_Template", "TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_4_Template", "TaskFollowItemsComponent_ng_template_10_ng_container_5_ng_container_5_Template", "col_r7", "TaskFollowItemsComponent_ng_template_10_ng_container_5_Template", "TaskFollowItemsComponent_ng_template_10_Template_button_click_7_listener", "$event", "_r5", "stopPropagation", "confirmRemove", "activity_transaction", "activity_id", "ɵɵsanitizeUrl", "subject", "sortOrderOpportunities", "TaskFollowItemsComponent_ng_template_22_ng_container_6_Template_th_click_1_listener", "col_r10", "_r9", "followupopportunitydetails", "TaskFollowItemsComponent_ng_template_22_ng_container_6_i_4_Template", "TaskFollowItemsComponent_ng_template_22_ng_container_6_i_5_Template", "sortFieldOpportunities", "TaskFollowItemsComponent_ng_template_22_Template_th_click_1_listener", "_r8", "TaskFollowItemsComponent_ng_template_22_i_4_Template", "TaskFollowItemsComponent_ng_template_22_i_5_Template", "TaskFollowItemsComponent_ng_template_22_ng_container_6_Template", "selectedOpportunitiesColumns", "followupopportunity_r12", "TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_3_Template", "TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_4_Template", "TaskFollowItemsComponent_ng_template_23_ng_container_5_ng_container_5_Template", "col_r13", "TaskFollowItemsComponent_ng_template_23_ng_container_5_Template", "TaskFollowItemsComponent_ng_template_23_Template_button_click_7_listener", "_r11", "confirmOpportunityRemove", "opportunity", "opportunity_id", "name", "sortOrderActivitiesTask", "TaskFollowItemsComponent_ng_template_35_ng_container_6_Template_th_click_1_listener", "col_r16", "_r15", "followuptaskdetails", "TaskFollowItemsComponent_ng_template_35_ng_container_6_i_4_Template", "TaskFollowItemsComponent_ng_template_35_ng_container_6_i_5_Template", "sortFieldActivitiesTask", "TaskFollowItemsComponent_ng_template_35_Template_th_click_1_listener", "_r14", "TaskFollowItemsComponent_ng_template_35_i_4_Template", "TaskFollowItemsComponent_ng_template_35_i_5_Template", "TaskFollowItemsComponent_ng_template_35_ng_container_6_Template", "selectedActivitiesTaskColumns", "followuptask_r18", "TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_3_Template", "TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_4_Template", "TaskFollowItemsComponent_ng_template_36_ng_container_5_ng_container_5_Template", "col_r19", "TaskFollowItemsComponent_ng_template_36_ng_container_5_Template", "TaskFollowItemsComponent_ng_template_36_Template_button_click_7_listener", "_r17", "TaskFollowItemsComponent", "constructor", "router", "route", "activitiesservice", "opportunitiesservice", "messageservice", "confirmationservice", "unsubscribe$", "submitted", "saving", "visible", "dropdowns", "activityDocumentType", "_selectedActivitiesColumns", "_selectedActivitiesTaskColumns", "_selectedOpportunitiesColumn", "ActivitiesCols", "ActivitiesTaskCols", "OpportunitiesCols", "ngOnInit", "loadActivityDropDown", "activity", "pipe", "subscribe", "response", "allActivityItems", "follow_up_and_related_items", "allOpportunityItems", "opportunity_followups", "filter", "item", "map", "partnerFn", "business_partner", "customer", "partner_functions", "find", "p", "partner_function", "partner<PERSON>ame", "bp_full_name", "val", "col", "includes", "onActivitiesColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "onActivitiesTaskColumnReorder", "onOpportunitiesColumnReorder", "data", "type", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "value", "i", "length", "target", "getActivityDropdownOptions", "res", "attr", "label", "description", "code", "dropdownKey", "opt", "confirm", "message", "icon", "accept", "remove", "deleteFollowupItem", "documentId", "next", "add", "severity", "detail", "getActivityByID", "error", "removeOpportunity", "openActivityFollowUpDialog", "activityFollowupDialog", "showDialog", "openOpportunityFollowUpDialog", "oppotunityFollowupDialog", "openActivityTaskFollowUpDialog", "activityTaskFollowupDialog", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "ActivitiesService", "i3", "OpportunitiesService", "i4", "MessageService", "ConfirmationService", "selectors", "viewQuery", "TaskFollowItemsComponent_Query", "rf", "ctx", "TaskFollowItemsComponent_Template_p_button_click_5_listener", "ɵɵtwoWayListener", "TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "TaskFollowItemsComponent_Template_p_table_onColReorder_8_listener", "TaskFollowItemsComponent_ng_template_9_Template", "TaskFollowItemsComponent_ng_template_10_Template", "TaskFollowItemsComponent_ng_template_11_Template", "TaskFollowItemsComponent_ng_template_12_Template", "TaskFollowItemsComponent_Template_p_button_click_18_listener", "TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_19_listener", "TaskFollowItemsComponent_Template_p_table_onColReorder_21_listener", "TaskFollowItemsComponent_ng_template_22_Template", "TaskFollowItemsComponent_ng_template_23_Template", "TaskFollowItemsComponent_ng_template_24_Template", "TaskFollowItemsComponent_ng_template_25_Template", "TaskFollowItemsComponent_Template_p_button_click_31_listener", "TaskFollowItemsComponent_Template_p_multiSelect_ngModelChange_32_listener", "TaskFollowItemsComponent_Template_p_table_onColReorder_34_listener", "TaskFollowItemsComponent_ng_template_35_Template", "TaskFollowItemsComponent_ng_template_36_Template", "TaskFollowItemsComponent_ng_template_37_Template", "TaskFollowItemsComponent_ng_template_38_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-follow-items\\task-follow-items.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-follow-items\\task-follow-items.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { ActivitiesCallFollowupFormComponent } from 'src/app/store/common-form/activities-call-followup-form/activities-call-followup-form.component';\r\nimport { OpportunitiesFollowupFormComponent } from 'src/app/store/common-form/opportunities-followup-form/opportunities-followup-form.component';\r\nimport { ActivitiesTaskFollowupFormComponent } from 'src/app/store/common-form/activities-task-followup-form/activities-task-followup-form.component';\r\n\r\ninterface ActivitiesColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\ninterface ActivitiesColumnTask {\r\n  field: string;\r\n  header: string;\r\n}\r\ninterface OpportunitiesColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-task-follow-items',\r\n  templateUrl: './task-follow-items.component.html',\r\n  styleUrl: './task-follow-items.component.scss',\r\n})\r\nexport class TaskFollowItemsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild(ActivitiesCallFollowupFormComponent)\r\n  activityFollowupDialog!: ActivitiesCallFollowupFormComponent;\r\n  @ViewChild(OpportunitiesFollowupFormComponent)\r\n  oppotunityFollowupDialog!: OpportunitiesFollowupFormComponent;\r\n  @ViewChild(ActivitiesTaskFollowupFormComponent)\r\n  activityTaskFollowupDialog!: ActivitiesTaskFollowupFormComponent;\r\n  public followupdetails: any[] = [];\r\n  public followupopportunitydetails: any[] = [];\r\n  public followuptaskdetails: any[] = [];\r\n  public activity_id: string = '';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public visible: boolean = false;\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private activitiesservice: ActivitiesService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  private _selectedActivitiesColumns: ActivitiesColumn[] = [];\r\n\r\n  private _selectedActivitiesTaskColumns: ActivitiesColumnTask[] = [];\r\n\r\n  private _selectedOpportunitiesColumn: OpportunitiesColumn[] = [];\r\n\r\n  public ActivitiesCols: ActivitiesColumn[] = [\r\n    { field: 'type_code', header: 'Type' },\r\n    { field: 'partner_name', header: 'Responsible' },\r\n    { field: 'createdAt', header: 'Created On' },\r\n  ];\r\n\r\n  public ActivitiesTaskCols: ActivitiesColumnTask[] = [\r\n    { field: 'type_code', header: 'Type' },\r\n    { field: 'partner_name', header: 'Responsible' },\r\n    { field: 'createdAt', header: 'Created On' },\r\n  ];\r\n\r\n  public OpportunitiesCols: OpportunitiesColumn[] = [\r\n    { field: 'type_code', header: 'Type' },\r\n    { field: 'partner_name', header: 'Responsible' },\r\n    { field: 'createdAt', header: 'Created On' },\r\n  ];\r\n\r\n  sortFieldActivities: string = '';\r\n  sortOrderActivities: number = 1;\r\n\r\n  sortFieldActivitiesTask: string = '';\r\n  sortOrderActivitiesTask: number = 1;\r\n\r\n  sortFieldOpportunities: string = '';\r\n  sortOrderOpportunities: number = 1;\r\n\r\n  ngOnInit() {\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM'\r\n    );\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response?.activity_id;\r\n\r\n          const allActivityItems = response?.follow_up_and_related_items || [];\r\n          const allOpportunityItems = response?.opportunity_followups || [];\r\n\r\n          // Filter only FOLLOW_UP items and inject individual partner_name from each item\r\n          this.followupdetails = allActivityItems\r\n            .filter((item: any) => item?.type_code === '0002')\r\n            .map((item: any) => {\r\n              const partnerFn =\r\n                item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) => p?.partner_function === 'YI'\r\n                );\r\n              const partnerName = partnerFn?.bp_full_name || null;\r\n              return {\r\n                ...item,\r\n                partner_name: partnerName,\r\n              };\r\n            });\r\n\r\n          this.followuptaskdetails = allActivityItems\r\n            .filter((item: any) => item?.type_code === '0006')\r\n            .map((item: any) => {\r\n              const partnerFn =\r\n                item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) => p?.partner_function === 'YI'\r\n                );\r\n              const partnerName = partnerFn?.bp_full_name || null;\r\n              return {\r\n                ...item,\r\n                partner_name: partnerName,\r\n              };\r\n            });\r\n\r\n          this.followupopportunitydetails = allOpportunityItems\r\n            .filter((item: any) => item?.type_code === '0005')\r\n            .map((item: any) => {\r\n              const partnerFn =\r\n                item?.opportunity?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) => p?.partner_function === 'YI'\r\n                );\r\n              const partnerName = partnerFn?.bp_full_name || null;\r\n\r\n              return {\r\n                ...item,\r\n                partner_name: partnerName,\r\n              };\r\n            });\r\n        }\r\n      });\r\n\r\n    this._selectedActivitiesColumns = this.ActivitiesCols;\r\n\r\n    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols;\r\n\r\n    this._selectedOpportunitiesColumn = this.OpportunitiesCols;\r\n  }\r\n\r\n  get selectedActivitiesColumns(): any[] {\r\n    return this._selectedActivitiesColumns;\r\n  }\r\n\r\n  get selectedActivitiesTaskColumns(): any[] {\r\n    return this._selectedActivitiesTaskColumns;\r\n  }\r\n\r\n  get selectedOpportunitiesColumns(): any[] {\r\n    return this._selectedOpportunitiesColumn;\r\n  }\r\n\r\n  set selectedActivitiesColumns(val: any[]) {\r\n    this._selectedActivitiesColumns = this.ActivitiesCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  set selectedActivitiesTaskColumns(val: any[]) {\r\n    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols.filter(\r\n      (col) => val.includes(col)\r\n    );\r\n  }\r\n\r\n  set selectedOpportunitiesColumns(val: any[]) {\r\n    this._selectedOpportunitiesColumn = this.OpportunitiesCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  onActivitiesColumnReorder(event: any) {\r\n    const draggedCol = this.ActivitiesCols[event.dragIndex];\r\n    this.ActivitiesCols.splice(event.dragIndex, 1);\r\n    this.ActivitiesCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  onActivitiesTaskColumnReorder(event: any) {\r\n    const draggedCol = this.ActivitiesTaskCols[event.dragIndex];\r\n    this.ActivitiesTaskCols.splice(event.dragIndex, 1);\r\n    this.ActivitiesTaskCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  onOpportunitiesColumnReorder(event: any) {\r\n    const draggedCol = this.OpportunitiesCols[event.dragIndex];\r\n    this.OpportunitiesCols.splice(event.dragIndex, 1);\r\n    this.OpportunitiesCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(\r\n    field: string,\r\n    data: any[],\r\n    type: 'activities' | 'task' | 'opportunities'\r\n  ) {\r\n    if (type === 'activities') {\r\n      this.sortFieldActivities = field;\r\n      this.sortOrderActivities = this.sortOrderActivities === 1 ? -1 : 1;\r\n    } else if (type === 'task') {\r\n      this.sortFieldActivitiesTask = field;\r\n      this.sortOrderActivitiesTask =\r\n        this.sortOrderActivitiesTask === 1 ? -1 : 1;\r\n    } else {\r\n      this.sortFieldOpportunities = field;\r\n      this.sortOrderOpportunities = this.sortOrderOpportunities === 1 ? -1 : 1;\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = null;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return (\r\n        (type === 'activities'\r\n          ? this.sortOrderActivities\r\n          : type === 'task'\r\n          ? this.sortOrderActivitiesTask\r\n          : this.sortOrderOpportunities) * result\r\n      );\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteFollowupItem(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmOpportunityRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  removeOpportunity(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteFollowupItem(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  openActivityFollowUpDialog() {\r\n    this.activityFollowupDialog.showDialog('right');\r\n  }\r\n\r\n  openOpportunityFollowUpDialog() {\r\n    this.oppotunityFollowupDialog.showDialog('right');\r\n  }\r\n\r\n  openActivityTaskFollowUpDialog() {\r\n    this.activityTaskFollowupDialog.showDialog('right');\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1 mb-5\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Follow Up Activities Sales Call</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"openActivityFollowUpDialog()\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"ActivitiesCols\" [(ngModel)]=\"selectedActivitiesColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"followupdetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onActivitiesColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg\"\r\n                        (click)=\"customSort('activity_transaction.subject', followupdetails, 'activities')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortFieldActivities === 'activity_transaction.subject'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldActivities !== 'activity_transaction.subject'\"\r\n                                class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedActivitiesColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, followupdetails, 'activities')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldActivities === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldActivities !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-followup>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                            <a [href]=\"'/#/store/activities/calls/' + followup?.activity_transaction?.activity_id + '/overview'\"\r\n                                style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                                {{ followup?.activity_transaction?.subject || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedActivitiesColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'type_code'\">\r\n                                    {{ getLabelFromDropdown('activityDocumentType',followup?.type_code) || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'partner_name'\">\r\n                                    {{ followup?.partner_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ followup?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();confirmRemove(followup);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">No sales call follow-up found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">Loading sales call follow-up\r\n                        data. Please\r\n                        wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Follow Up Opportunities</h4>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"openOpportunityFollowUpDialog()\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"OpportunitiesCols\" [(ngModel)]=\"selectedOpportunitiesColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"followupopportunitydetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onOpportunitiesColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg\"\r\n                        (click)=\"customSort('opportunity.name', followupopportunitydetails, 'opportunities')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortFieldOpportunities === 'opportunity.name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderOpportunities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldOpportunities !== 'opportunity.name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedOpportunitiesColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, followupopportunitydetails, 'opportunities')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldOpportunities === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderOpportunities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldOpportunities !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-followupopportunity>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                            <a [href]=\"'/#/store/opportunities/' + followupopportunity?.opportunity?.opportunity_id + '/overview'\"\r\n                                style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                                {{ followupopportunity?.opportunity?.name || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedOpportunitiesColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'type_code'\">\r\n                                    {{ getLabelFromDropdown('activityDocumentType',followupopportunity?.type_code) ||\r\n                                    '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'partner_name'\">\r\n                                    {{ followupopportunity?.partner_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ followupopportunity?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();confirmOpportunityRemove(followupopportunity);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">No opportunities follow-up found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">Loading opportunities follow-up\r\n                        data. Please\r\n                        wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1 mb-5\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Follow Up Task</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"openActivityTaskFollowUpDialog()\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"ActivitiesTaskCols\" [(ngModel)]=\"selectedActivitiesTaskColumns\"\r\n                optionLabel=\"header\" class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"followuptaskdetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onActivitiesTaskColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg\"\r\n                        (click)=\"customSort('activity_transaction.subject', followupdetails, 'task')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortFieldActivitiesTask === 'activity_transaction.subject'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldActivitiesTask !== 'activity_transaction.subject'\"\r\n                                class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedActivitiesTaskColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, followuptaskdetails, 'task')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldActivitiesTask === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldActivitiesTask !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-followuptask>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                            <a [href]=\"'/#/store/activities/tasks/' + followuptask?.activity_transaction?.activity_id + '/overview'\"\r\n                                style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                                {{ followuptask?.activity_transaction?.subject || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedActivitiesTaskColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'type_code'\">\r\n                                    Activity Task\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'partner_name'\">\r\n                                    {{ followuptask?.partner_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ followuptask?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();confirmRemove(followuptask);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">No tasks follow-up found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">Loading tasks follow-up data.\r\n                        Please\r\n                        wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n<app-opportunities-followup-form></app-opportunities-followup-form>\r\n\r\n<app-activities-call-followup-form></app-activities-call-followup-form>\r\n\r\n<app-activities-task-followup-form></app-activities-task-followup-form>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAKzC,SAASC,mCAAmC,QAAQ,iGAAiG;AACrJ,SAASC,kCAAkC,QAAQ,6FAA6F;AAChJ,SAASC,mCAAmC,QAAQ,iGAAiG;;;;;;;;;;;;;;;;;ICmBzHC,EAAA,CAAAC,SAAA,YACgG;;;;IAA5FD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,mBAAA,yDAAuF;;;;;IAC3FJ,EAAA,CAAAC,SAAA,YACgC;;;;;IAS5BD,EAAA,CAAAC,SAAA,YACgG;;;;IAA5FD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,mBAAA,yDAAuF;;;;;IAC3FJ,EAAA,CAAAC,SAAA,YAAyE;;;;;;IAPrFD,EAAA,CAAAK,uBAAA,GAA4D;IACxDL,EAAA,CAAAM,cAAA,aACmE;IAA/DN,EAAA,CAAAO,UAAA,mBAAAC,mFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,EAAAb,MAAA,CAAAc,eAAA,EAAuC,YAAY,CAAC;IAAA,EAAC;IAC9DjB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,GACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAC,kEAAA,gBAC4F,IAAAC,kEAAA,gBACvB;IAE7ErB,EADI,CAAAsB,YAAA,EAAM,EACL;;;;;;IARDtB,EAAA,CAAAuB,SAAA,EAA6B;IAA7BvB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAGzBhB,EAAA,CAAAuB,SAAA,GACA;IADAvB,EAAA,CAAAwB,kBAAA,MAAAf,MAAA,CAAAgB,MAAA,MACA;IAAIzB,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,mBAAA,KAAAjB,MAAA,CAAAO,KAAA,CAAuC;IAEvChB,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,mBAAA,KAAAjB,MAAA,CAAAO,KAAA,CAAuC;;;;;;IAlBvDhB,EADJ,CAAAM,cAAA,SAAI,aAEwF;IAApFN,EAAA,CAAAO,UAAA,mBAAAoB,oEAAA;MAAA3B,EAAA,CAAAU,aAAA,CAAAkB,GAAA;MAAA,MAAAzB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,8BAA8B,EAAAZ,MAAA,CAAAc,eAAA,EAAmB,YAAY,CAAC;IAAA,EAAC;IACnFjB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,aACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAU,mDAAA,gBAC4F,IAAAC,mDAAA,gBAEhE;IAEpC9B,EADI,CAAAsB,YAAA,EAAM,EACL;IAELtB,EAAA,CAAAmB,UAAA,IAAAY,8DAAA,2BAA4D;IAW5D/B,EAAA,CAAAM,cAAA,aAAkC;IAAAN,EAAA,CAAAkB,MAAA,cAAO;IAC7ClB,EAD6C,CAAAsB,YAAA,EAAK,EAC7C;;;;IAnBWtB,EAAA,CAAAuB,SAAA,GAA4D;IAA5DvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,mBAAA,oCAA4D;IAE5D1B,EAAA,CAAAuB,SAAA,EAA4D;IAA5DvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,mBAAA,oCAA4D;IAK1C1B,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6B,yBAAA,CAA4B;;;;;IA4B9ChC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,GACJ;;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAArB,MAAA,CAAA8B,oBAAA,yBAAAC,WAAA,kBAAAA,WAAA,CAAAC,SAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAU,WAAA,kBAAAA,WAAA,CAAAE,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,GACJ;;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAqC,WAAA,OAAAH,WAAA,kBAAAA,WAAA,CAAAI,SAAA,8BACJ;;;;;IAbZtC,EAAA,CAAAK,uBAAA,GAA4D;IACxDL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAmB,UAAA,IAAAoB,8EAAA,2BAA0C,IAAAC,8EAAA,2BAIG,IAAAC,8EAAA,2BAIH;;IAKlDzC,EAAA,CAAAsB,YAAA,EAAK;;;;;IAdatB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,aAAAwC,MAAA,CAAA1B,KAAA,CAAsB;IACjBhB,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;;;;;;IAjB5CF,EAHZ,CAAAM,cAAA,aAA2B,aAC6E,cACrB,YAEJ;IAC/DN,EAAA,CAAAkB,MAAA,GACJ;IAERlB,EAFQ,CAAAsB,YAAA,EAAI,EACF,EACL;IACLtB,EAAA,CAAAmB,UAAA,IAAAwB,+DAAA,2BAA4D;IAmBxD3C,EADJ,CAAAM,cAAA,aAA8C,iBAEsB;IAA5DN,EAAA,CAAAO,UAAA,mBAAAqC,yEAAAC,MAAA;MAAA,MAAAX,WAAA,GAAAlC,EAAA,CAAAU,aAAA,CAAAoC,GAAA,EAAAlC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASgC,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAACX,MAAA,CAAA6C,aAAA,CAAAd,WAAA,CAAuB;IAAA,EAAE;IAEvElC,EAFwE,CAAAsB,YAAA,EAAS,EACxE,EACJ;;;;;IA5BUtB,EAAA,CAAAuB,SAAA,GAAiG;IAAjGvB,EAAA,CAAAE,UAAA,yCAAAgC,WAAA,kBAAAA,WAAA,CAAAe,oBAAA,kBAAAf,WAAA,CAAAe,oBAAA,CAAAC,WAAA,iBAAAlD,EAAA,CAAAmD,aAAA,CAAiG;IAEhGnD,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAU,WAAA,kBAAAA,WAAA,CAAAe,oBAAA,kBAAAf,WAAA,CAAAe,oBAAA,CAAAG,OAAA,cACJ;IAGsBpD,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6B,yBAAA,CAA4B;;;;;IA2B1DhC,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,sCACnE;IACJlB,EADI,CAAAsB,YAAA,EAAK,EACJ;;;;;IAIDtB,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,sDAE1D;IACblB,EADa,CAAAsB,YAAA,EAAK,EACb;;;;;IAgCOtB,EAAA,CAAAC,SAAA,YACmG;;;;IAA/FD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAkD,sBAAA,yDAA0F;;;;;IAC9FrD,EAAA,CAAAC,SAAA,YAAqF;;;;;IASjFD,EAAA,CAAAC,SAAA,YACmG;;;;IAA/FD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAkD,sBAAA,yDAA0F;;;;;IAC9FrD,EAAA,CAAAC,SAAA,YAA4E;;;;;;IAPxFD,EAAA,CAAAK,uBAAA,GAA+D;IAC3DL,EAAA,CAAAM,cAAA,aACiF;IAA7EN,EAAA,CAAAO,UAAA,mBAAA+C,oFAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAU,aAAA,CAAA8C,GAAA,EAAA5C,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAwC,OAAA,CAAAvC,KAAA,EAAAb,MAAA,CAAAsD,0BAAA,EAAkD,eAAe,CAAC;IAAA,EAAC;IAC5EzD,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,GACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAuC,mEAAA,gBAC+F,IAAAC,mEAAA,gBACvB;IAEhF3D,EADI,CAAAsB,YAAA,EAAM,EACL;;;;;;IARDtB,EAAA,CAAAuB,SAAA,EAA6B;IAA7BvB,EAAA,CAAAE,UAAA,oBAAAqD,OAAA,CAAAvC,KAAA,CAA6B;IAGzBhB,EAAA,CAAAuB,SAAA,GACA;IADAvB,EAAA,CAAAwB,kBAAA,MAAA+B,OAAA,CAAA9B,MAAA,MACA;IAAIzB,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyD,sBAAA,KAAAL,OAAA,CAAAvC,KAAA,CAA0C;IAE1ChB,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyD,sBAAA,KAAAL,OAAA,CAAAvC,KAAA,CAA0C;;;;;;IAjB1DhB,EADJ,CAAAM,cAAA,SAAI,aAE0F;IAAtFN,EAAA,CAAAO,UAAA,mBAAAsD,qEAAA;MAAA7D,EAAA,CAAAU,aAAA,CAAAoD,GAAA;MAAA,MAAA3D,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,kBAAkB,EAAAZ,MAAA,CAAAsD,0BAAA,EAA8B,eAAe,CAAC;IAAA,EAAC;IACrFzD,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,aACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAA4C,oDAAA,gBAC+F,IAAAC,oDAAA,gBACd;IAEzFhE,EADI,CAAAsB,YAAA,EAAM,EACL;IAELtB,EAAA,CAAAmB,UAAA,IAAA8C,+DAAA,2BAA+D;IAW/DjE,EAAA,CAAAM,cAAA,aAAkC;IAAAN,EAAA,CAAAkB,MAAA,cAAO;IAC7ClB,EAD6C,CAAAsB,YAAA,EAAK,EAC7C;;;;IAlBWtB,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyD,sBAAA,wBAAmD;IAEnD5D,EAAA,CAAAuB,SAAA,EAAmD;IAAnDvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyD,sBAAA,wBAAmD;IAIjC5D,EAAA,CAAAuB,SAAA,EAA+B;IAA/BvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA+D,4BAAA,CAA+B;;;;;IA4BjDlE,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,GAEJ;;;;;;IAFIlB,EAAA,CAAAuB,SAAA,EAEJ;IAFIvB,EAAA,CAAAwB,kBAAA,MAAArB,MAAA,CAAA8B,oBAAA,yBAAAkC,uBAAA,kBAAAA,uBAAA,CAAAhC,SAAA,cAEJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAA2C,uBAAA,kBAAAA,uBAAA,CAAA/B,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,GACJ;;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAqC,WAAA,OAAA8B,uBAAA,kBAAAA,uBAAA,CAAA7B,SAAA,8BACJ;;;;;IAdZtC,EAAA,CAAAK,uBAAA,GAA+D;IAC3DL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAUjCL,EATA,CAAAmB,UAAA,IAAAiD,8EAAA,2BAA0C,IAAAC,8EAAA,2BAKG,IAAAC,8EAAA,2BAIH;;IAKlDtE,EAAA,CAAAsB,YAAA,EAAK;;;;;IAfatB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,aAAAqE,OAAA,CAAAvD,KAAA,CAAsB;IACjBhB,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;IAKzBF,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;;;;;;IAlB5CF,EAHZ,CAAAM,cAAA,aAA2B,aAC+D,cACP,YAEJ;IAC/DN,EAAA,CAAAkB,MAAA,GACJ;IAERlB,EAFQ,CAAAsB,YAAA,EAAI,EACF,EACL;IACLtB,EAAA,CAAAmB,UAAA,IAAAqD,+DAAA,2BAA+D;IAoB3DxE,EADJ,CAAAM,cAAA,aAA8C,iBAE4C;IAAlFN,EAAA,CAAAO,UAAA,mBAAAkE,yEAAA5B,MAAA;MAAA,MAAAsB,uBAAA,GAAAnE,EAAA,CAAAU,aAAA,CAAAgE,IAAA,EAAA9D,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASgC,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAACX,MAAA,CAAAwE,wBAAA,CAAAR,uBAAA,CAA6C;IAAA,EAAE;IAE7FnE,EAF8F,CAAAsB,YAAA,EAAS,EAC9F,EACJ;;;;;IA7BUtB,EAAA,CAAAuB,SAAA,GAAmG;IAAnGvB,EAAA,CAAAE,UAAA,sCAAAiE,uBAAA,kBAAAA,uBAAA,CAAAS,WAAA,kBAAAT,uBAAA,CAAAS,WAAA,CAAAC,cAAA,iBAAA7E,EAAA,CAAAmD,aAAA,CAAmG;IAElGnD,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAA2C,uBAAA,kBAAAA,uBAAA,CAAAS,WAAA,kBAAAT,uBAAA,CAAAS,WAAA,CAAAE,IAAA,cACJ;IAGsB9E,EAAA,CAAAuB,SAAA,EAA+B;IAA/BvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA+D,4BAAA,CAA+B;;;;;IA4B7DlE,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,yCACnE;IACJlB,EADI,CAAAsB,YAAA,EAAK,EACJ;;;;;IAIDtB,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,yDAE1D;IACblB,EADa,CAAAsB,YAAA,EAAK,EACb;;;;;IAiCOtB,EAAA,CAAAC,SAAA,YACoG;;;;IAAhGD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4E,uBAAA,yDAA2F;;;;;IAC/F/E,EAAA,CAAAC,SAAA,YACgC;;;;;IAS5BD,EAAA,CAAAC,SAAA,YACoG;;;;IAAhGD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4E,uBAAA,yDAA2F;;;;;IAC/F/E,EAAA,CAAAC,SAAA,YAA6E;;;;;;IAPzFD,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAM,cAAA,aACiE;IAA7DN,EAAA,CAAAO,UAAA,mBAAAyE,oFAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAAU,aAAA,CAAAwE,IAAA,EAAAtE,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAkE,OAAA,CAAAjE,KAAA,EAAAb,MAAA,CAAAgF,mBAAA,EAA2C,MAAM,CAAC;IAAA,EAAC;IAC5DnF,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,GACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAiE,mEAAA,gBACgG,IAAAC,mEAAA,gBACvB;IAEjFrF,EADI,CAAAsB,YAAA,EAAM,EACL;;;;;;IARDtB,EAAA,CAAAuB,SAAA,EAA6B;IAA7BvB,EAAA,CAAAE,UAAA,oBAAA+E,OAAA,CAAAjE,KAAA,CAA6B;IAGzBhB,EAAA,CAAAuB,SAAA,GACA;IADAvB,EAAA,CAAAwB,kBAAA,MAAAyD,OAAA,CAAAxD,MAAA,MACA;IAAIzB,EAAA,CAAAuB,SAAA,EAA2C;IAA3CvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmF,uBAAA,KAAAL,OAAA,CAAAjE,KAAA,CAA2C;IAE3ChB,EAAA,CAAAuB,SAAA,EAA2C;IAA3CvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmF,uBAAA,KAAAL,OAAA,CAAAjE,KAAA,CAA2C;;;;;;IAlB3DhB,EADJ,CAAAM,cAAA,SAAI,aAEkF;IAA9EN,EAAA,CAAAO,UAAA,mBAAAgF,qEAAA;MAAAvF,EAAA,CAAAU,aAAA,CAAA8E,IAAA;MAAA,MAAArF,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,8BAA8B,EAAAZ,MAAA,CAAAc,eAAA,EAAmB,MAAM,CAAC;IAAA,EAAC;IAC7EjB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,aACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAsE,oDAAA,gBACgG,IAAAC,oDAAA,gBAEpE;IAEpC1F,EADI,CAAAsB,YAAA,EAAM,EACL;IAELtB,EAAA,CAAAmB,UAAA,IAAAwE,+DAAA,2BAAgE;IAWhE3F,EAAA,CAAAM,cAAA,aAAkC;IAAAN,EAAA,CAAAkB,MAAA,cAAO;IAC7ClB,EAD6C,CAAAsB,YAAA,EAAK,EAC7C;;;;IAnBWtB,EAAA,CAAAuB,SAAA,GAAgE;IAAhEvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmF,uBAAA,oCAAgE;IAEhEtF,EAAA,CAAAuB,SAAA,EAAgE;IAAhEvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmF,uBAAA,oCAAgE;IAK9CtF,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAyF,6BAAA,CAAgC;;;;;IA4BlD5F,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,sBACJ;;;;;;IAEAlB,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAqE,gBAAA,kBAAAA,gBAAA,CAAAzD,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAkB,MAAA,GACJ;;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAqC,WAAA,OAAAwD,gBAAA,kBAAAA,gBAAA,CAAAvD,SAAA,8BACJ;;;;;IAbZtC,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAmB,UAAA,IAAA2E,8EAAA,2BAA0C,IAAAC,8EAAA,2BAIG,IAAAC,8EAAA,2BAIH;;IAKlDhG,EAAA,CAAAsB,YAAA,EAAK;;;;;IAdatB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,aAAA+F,OAAA,CAAAjF,KAAA,CAAsB;IACjBhB,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAuB,SAAA,EAAyB;IAAzBvB,EAAA,CAAAE,UAAA,6BAAyB;;;;;;IAjB5CF,EAHZ,CAAAM,cAAA,aAA2B,aAC6E,cACrB,YAEJ;IAC/DN,EAAA,CAAAkB,MAAA,GACJ;IAERlB,EAFQ,CAAAsB,YAAA,EAAI,EACF,EACL;IACLtB,EAAA,CAAAmB,UAAA,IAAA+E,+DAAA,2BAAgE;IAmB5DlG,EADJ,CAAAM,cAAA,aAA8C,iBAE0B;IAAhEN,EAAA,CAAAO,UAAA,mBAAA4F,yEAAAtD,MAAA;MAAA,MAAAgD,gBAAA,GAAA7F,EAAA,CAAAU,aAAA,CAAA0F,IAAA,EAAAxF,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASgC,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAACX,MAAA,CAAA6C,aAAA,CAAA6C,gBAAA,CAA2B;IAAA,EAAE;IAE3E7F,EAF4E,CAAAsB,YAAA,EAAS,EAC5E,EACJ;;;;;IA5BUtB,EAAA,CAAAuB,SAAA,GAAqG;IAArGvB,EAAA,CAAAE,UAAA,yCAAA2F,gBAAA,kBAAAA,gBAAA,CAAA5C,oBAAA,kBAAA4C,gBAAA,CAAA5C,oBAAA,CAAAC,WAAA,iBAAAlD,EAAA,CAAAmD,aAAA,CAAqG;IAEpGnD,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAqE,gBAAA,kBAAAA,gBAAA,CAAA5C,oBAAA,kBAAA4C,gBAAA,CAAA5C,oBAAA,CAAAG,OAAA,cACJ;IAGsBpD,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAyF,6BAAA,CAAgC;;;;;IA2B9D5F,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,gCAAyB;IAChGlB,EADgG,CAAAsB,YAAA,EAAK,EAChG;;;;;IAIDtB,EADJ,CAAAM,cAAA,SAAI,aACmE;IAAAN,EAAA,CAAAkB,MAAA,iDAE1D;IACblB,EADa,CAAAsB,YAAA,EAAK,EACb;;;AD1QrB,OAAM,MAAO+E,wBAAwB;EAmBnCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,iBAAoC,EACpCC,oBAA0C,EAC1CC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxBrB,KAAAC,YAAY,GAAG,IAAIlH,OAAO,EAAQ;IAOnC,KAAAsB,eAAe,GAAU,EAAE;IAC3B,KAAAwC,0BAA0B,GAAU,EAAE;IACtC,KAAA0B,mBAAmB,GAAU,EAAE;IAC/B,KAAAjC,WAAW,GAAW,EAAE;IACxB,KAAA4D,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,SAAS,GAA0B;MACxCC,oBAAoB,EAAE;KACvB;IAWO,KAAAC,0BAA0B,GAAuB,EAAE;IAEnD,KAAAC,8BAA8B,GAA2B,EAAE;IAE3D,KAAAC,4BAA4B,GAA0B,EAAE;IAEzD,KAAAC,cAAc,GAAuB,CAC1C;MAAEtG,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAM,CAAE,EACtC;MAAET,KAAK,EAAE,cAAc;MAAES,MAAM,EAAE;IAAa,CAAE,EAChD;MAAET,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAY,CAAE,CAC7C;IAEM,KAAA8F,kBAAkB,GAA2B,CAClD;MAAEvG,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAM,CAAE,EACtC;MAAET,KAAK,EAAE,cAAc;MAAES,MAAM,EAAE;IAAa,CAAE,EAChD;MAAET,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAY,CAAE,CAC7C;IAEM,KAAA+F,iBAAiB,GAA0B,CAChD;MAAExG,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAM,CAAE,EACtC;MAAET,KAAK,EAAE,cAAc;MAAES,MAAM,EAAE;IAAa,CAAE,EAChD;MAAET,KAAK,EAAE,WAAW;MAAES,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAtB,mBAAmB,GAAW,CAAC;IAE/B,KAAAkF,uBAAuB,GAAW,EAAE;IACpC,KAAAP,uBAAuB,GAAW,CAAC;IAEnC,KAAAnB,sBAAsB,GAAW,EAAE;IACnC,KAAAP,sBAAsB,GAAW,CAAC;EAjC/B;EAmCHoE,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,6CAA6C,CAC9C;IACD,IAAI,CAACjB,iBAAiB,CAACkB,QAAQ,CAC5BC,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACiH,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC5E,WAAW,GAAG4E,QAAQ,EAAE5E,WAAW;QAExC,MAAM6E,gBAAgB,GAAGD,QAAQ,EAAEE,2BAA2B,IAAI,EAAE;QACpE,MAAMC,mBAAmB,GAAGH,QAAQ,EAAEI,qBAAqB,IAAI,EAAE;QAEjE;QACA,IAAI,CAACjH,eAAe,GAAG8G,gBAAgB,CACpCI,MAAM,CAAEC,IAAS,IAAKA,IAAI,EAAEjG,SAAS,KAAK,MAAM,CAAC,CACjDkG,GAAG,CAAED,IAAS,IAAI;UACjB,MAAME,SAAS,GACbF,IAAI,EAAEnF,oBAAoB,EAAEsF,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC5EC,CAAM,IAAKA,CAAC,EAAEC,gBAAgB,KAAK,IAAI,CACzC;UACH,MAAMC,WAAW,GAAGP,SAAS,EAAEQ,YAAY,IAAI,IAAI;UACnD,OAAO;YACL,GAAGV,IAAI;YACPhG,YAAY,EAAEyG;WACf;QACH,CAAC,CAAC;QAEJ,IAAI,CAAC1D,mBAAmB,GAAG4C,gBAAgB,CACxCI,MAAM,CAAEC,IAAS,IAAKA,IAAI,EAAEjG,SAAS,KAAK,MAAM,CAAC,CACjDkG,GAAG,CAAED,IAAS,IAAI;UACjB,MAAME,SAAS,GACbF,IAAI,EAAEnF,oBAAoB,EAAEsF,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC5EC,CAAM,IAAKA,CAAC,EAAEC,gBAAgB,KAAK,IAAI,CACzC;UACH,MAAMC,WAAW,GAAGP,SAAS,EAAEQ,YAAY,IAAI,IAAI;UACnD,OAAO;YACL,GAAGV,IAAI;YACPhG,YAAY,EAAEyG;WACf;QACH,CAAC,CAAC;QAEJ,IAAI,CAACpF,0BAA0B,GAAGwE,mBAAmB,CAClDE,MAAM,CAAEC,IAAS,IAAKA,IAAI,EAAEjG,SAAS,KAAK,MAAM,CAAC,CACjDkG,GAAG,CAAED,IAAS,IAAI;UACjB,MAAME,SAAS,GACbF,IAAI,EAAExD,WAAW,EAAE2D,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CACnEC,CAAM,IAAKA,CAAC,EAAEC,gBAAgB,KAAK,IAAI,CACzC;UACH,MAAMC,WAAW,GAAGP,SAAS,EAAEQ,YAAY,IAAI,IAAI;UAEnD,OAAO;YACL,GAAGV,IAAI;YACPhG,YAAY,EAAEyG;WACf;QACH,CAAC,CAAC;MACN;IACF,CAAC,CAAC;IAEJ,IAAI,CAAC1B,0BAA0B,GAAG,IAAI,CAACG,cAAc;IAErD,IAAI,CAACF,8BAA8B,GAAG,IAAI,CAACG,kBAAkB;IAE7D,IAAI,CAACF,4BAA4B,GAAG,IAAI,CAACG,iBAAiB;EAC5D;EAEA,IAAIxF,yBAAyBA,CAAA;IAC3B,OAAO,IAAI,CAACmF,0BAA0B;EACxC;EAEA,IAAIvB,6BAA6BA,CAAA;IAC/B,OAAO,IAAI,CAACwB,8BAA8B;EAC5C;EAEA,IAAIlD,4BAA4BA,CAAA;IAC9B,OAAO,IAAI,CAACmD,4BAA4B;EAC1C;EAEA,IAAIrF,yBAAyBA,CAAC+G,GAAU;IACtC,IAAI,CAAC5B,0BAA0B,GAAG,IAAI,CAACG,cAAc,CAACa,MAAM,CAAEa,GAAG,IAC/DD,GAAG,CAACE,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEA,IAAIpD,6BAA6BA,CAACmD,GAAU;IAC1C,IAAI,CAAC3B,8BAA8B,GAAG,IAAI,CAACG,kBAAkB,CAACY,MAAM,CACjEa,GAAG,IAAKD,GAAG,CAACE,QAAQ,CAACD,GAAG,CAAC,CAC3B;EACH;EAEA,IAAI9E,4BAA4BA,CAAC6E,GAAU;IACzC,IAAI,CAAC1B,4BAA4B,GAAG,IAAI,CAACG,iBAAiB,CAACW,MAAM,CAAEa,GAAG,IACpED,GAAG,CAACE,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEAE,yBAAyBA,CAACC,KAAU;IAClC,MAAMC,UAAU,GAAG,IAAI,CAAC9B,cAAc,CAAC6B,KAAK,CAACE,SAAS,CAAC;IACvD,IAAI,CAAC/B,cAAc,CAACgC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC/B,cAAc,CAACgC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC5D;EAEAI,6BAA6BA,CAACL,KAAU;IACtC,MAAMC,UAAU,GAAG,IAAI,CAAC7B,kBAAkB,CAAC4B,KAAK,CAACE,SAAS,CAAC;IAC3D,IAAI,CAAC9B,kBAAkB,CAAC+B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC9B,kBAAkB,CAAC+B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAChE;EAEAK,4BAA4BA,CAACN,KAAU;IACrC,MAAMC,UAAU,GAAG,IAAI,CAAC5B,iBAAiB,CAAC2B,KAAK,CAACE,SAAS,CAAC;IAC1D,IAAI,CAAC7B,iBAAiB,CAAC8B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACjD,IAAI,CAAC7B,iBAAiB,CAAC8B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC/D;EAEArI,UAAUA,CACRC,KAAa,EACb0I,IAAW,EACXC,IAA6C;IAE7C,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB,IAAI,CAACjI,mBAAmB,GAAGV,KAAK;MAChC,IAAI,CAACZ,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,MAAM,IAAIuJ,IAAI,KAAK,MAAM,EAAE;MAC1B,IAAI,CAACrE,uBAAuB,GAAGtE,KAAK;MACpC,IAAI,CAAC+D,uBAAuB,GAC1B,IAAI,CAACA,uBAAuB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACnB,sBAAsB,GAAG5C,KAAK;MACnC,IAAI,CAACqC,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1E;IAEAqG,IAAI,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE7I,KAAK,CAAC;MAC9C,MAAMiJ,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAE9I,KAAK,CAAC;MAE9C,IAAIkJ,MAAM,GAAG,IAAI;MAEjB,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OACE,CAACN,IAAI,KAAK,YAAY,GAClB,IAAI,CAACvJ,mBAAmB,GACxBuJ,IAAI,KAAK,MAAM,GACf,IAAI,CAAC5E,uBAAuB,GAC5B,IAAI,CAAC1B,sBAAsB,IAAI6G,MAAM;IAE7C,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAACN,IAAS,EAAE1I,KAAa;IACvC,IAAI,CAAC0I,IAAI,IAAI,CAAC1I,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAACoJ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOV,IAAI,CAAC1I,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAIqJ,MAAM,GAAGrJ,KAAK,CAACsJ,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIC,KAAK,GAAGb,IAAI;MAChB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAID,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOD,KAAK;IACd;EACF;EAEA7C,oBAAoBA,CAACgD,MAAc,EAAEf,IAAY;IAC/C,IAAI,CAAClD,iBAAiB,CACnBkE,0BAA0B,CAAChB,IAAI,CAAC,CAChC9B,SAAS,CAAE+C,GAAQ,IAAI;MACtB,IAAI,CAAC3D,SAAS,CAACyD,MAAM,CAAC,GACpBE,GAAG,EAAElB,IAAI,EAAErB,GAAG,CAAEwC,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBR,KAAK,EAAEM,IAAI,CAACG;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEA/I,oBAAoBA,CAACgJ,WAAmB,EAAEV,KAAa;IACrD,MAAMnC,IAAI,GAAG,IAAI,CAACnB,SAAS,CAACgE,WAAW,CAAC,EAAEvC,IAAI,CAC3CwC,GAAG,IAAKA,GAAG,CAACX,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOnC,IAAI,EAAE0C,KAAK,IAAIP,KAAK;EAC7B;EAEAvH,aAAaA,CAACoF,IAAS;IACrB,IAAI,CAACxB,mBAAmB,CAACuE,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChE3J,MAAM,EAAE,SAAS;MACjB4J,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACnD,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAmD,MAAMA,CAACnD,IAAS;IACd,IAAI,CAAC3B,iBAAiB,CACnB+E,kBAAkB,CAACpD,IAAI,CAACqD,UAAU,CAAC,CACnC7D,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACiH,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACT6D,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/E,cAAc,CAACgF,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACpF,iBAAiB,CACnBqF,eAAe,CAAC,IAAI,CAAC5I,WAAW,CAAC,CACjC0E,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACiH,YAAY,CAAC,CAAC,CAClCgB,SAAS,EAAE;MAChB,CAAC;MACDkE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpF,cAAc,CAACgF,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAlH,wBAAwBA,CAACyD,IAAS;IAChC,IAAI,CAACxB,mBAAmB,CAACuE,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChE3J,MAAM,EAAE,SAAS;MACjB4J,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACnD,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA4D,iBAAiBA,CAAC5D,IAAS;IACzB,IAAI,CAAC1B,oBAAoB,CACtB8E,kBAAkB,CAACpD,IAAI,CAACqD,UAAU,CAAC,CACnC7D,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACiH,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACT6D,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/E,cAAc,CAACgF,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACpF,iBAAiB,CACnBqF,eAAe,CAAC,IAAI,CAAC5I,WAAW,CAAC,CACjC0E,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACiH,YAAY,CAAC,CAAC,CAClCgB,SAAS,EAAE;MAChB,CAAC;MACDkE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpF,cAAc,CAACgF,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAI,0BAA0BA,CAAA;IACxB,IAAI,CAACC,sBAAsB,CAACC,UAAU,CAAC,OAAO,CAAC;EACjD;EAEAC,6BAA6BA,CAAA;IAC3B,IAAI,CAACC,wBAAwB,CAACF,UAAU,CAAC,OAAO,CAAC;EACnD;EAEAG,8BAA8BA,CAAA;IAC5B,IAAI,CAACC,0BAA0B,CAACJ,UAAU,CAAC,OAAO,CAAC;EACrD;EAEAK,WAAWA,CAAA;IACT,IAAI,CAAC3F,YAAY,CAAC6E,IAAI,EAAE;IACxB,IAAI,CAAC7E,YAAY,CAAC4F,QAAQ,EAAE;EAC9B;;;uBAhVWpG,wBAAwB,EAAArG,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5M,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA7M,EAAA,CAAA0M,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAA/M,EAAA,CAAA0M,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAjN,EAAA,CAAA0M,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAnN,EAAA,CAAA0M,iBAAA,CAAAQ,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAxB/G,wBAAwB;MAAAgH,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAExB3N,mCAAmC;yBAEnCC,kCAAkC;yBAElCC,mCAAmC;;;;;;;;;;;;;;UCjCxCC,EAFR,CAAAM,cAAA,aAAgE,aACoC,YAC7C;UAAAN,EAAA,CAAAkB,MAAA,sCAA+B;UAAAlB,EAAA,CAAAsB,YAAA,EAAK;UAG/EtB,EADJ,CAAAM,cAAA,aAA2C,kBAEoC;UADrDN,EAAA,CAAAO,UAAA,mBAAAmN,4DAAA;YAAA,OAASD,GAAA,CAAAxB,0BAAA,EAA4B;UAAA,EAAC;UAA5DjM,EAAA,CAAAsB,YAAA,EAC2E;UAE3EtB,EAAA,CAAAM,cAAA,uBAE+I;UAFrGN,EAAA,CAAA2N,gBAAA,2BAAAC,yEAAA/K,MAAA;YAAA7C,EAAA,CAAA6N,kBAAA,CAAAJ,GAAA,CAAAzL,yBAAA,EAAAa,MAAA,MAAA4K,GAAA,CAAAzL,yBAAA,GAAAa,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAuC;UAKzF7C,EAFQ,CAAAsB,YAAA,EAAgB,EACd,EACJ;UAIFtB,EAFJ,CAAAM,cAAA,aAAuB,iBAIoC;UADHN,EAAA,CAAAO,UAAA,0BAAAuN,kEAAAjL,MAAA;YAAA,OAAgB4K,GAAA,CAAAvE,yBAAA,CAAArG,MAAA,CAAiC;UAAA,EAAC;UAwElG7C,EArEA,CAAAmB,UAAA,IAAA4M,+CAAA,yBAAgC,KAAAC,gDAAA,yBA4BW,KAAAC,gDAAA,0BAmCL,KAAAC,gDAAA,0BAMD;UASjDlO,EAFQ,CAAAsB,YAAA,EAAU,EACR,EACJ;UAKEtB,EAFR,CAAAM,cAAA,eAA2D,cACyC,aAC7C;UAAAN,EAAA,CAAAkB,MAAA,+BAAuB;UAAAlB,EAAA,CAAAsB,YAAA,EAAK;UAEvEtB,EADJ,CAAAM,cAAA,cAA2C,mBAEoC;UADrDN,EAAA,CAAAO,UAAA,mBAAA4N,6DAAA;YAAA,OAASV,GAAA,CAAArB,6BAAA,EAA+B;UAAA,EAAC;UAA/DpM,EAAA,CAAAsB,YAAA,EAC2E;UAE3EtB,EAAA,CAAAM,cAAA,wBAE+I;UAFlGN,EAAA,CAAA2N,gBAAA,2BAAAS,0EAAAvL,MAAA;YAAA7C,EAAA,CAAA6N,kBAAA,CAAAJ,GAAA,CAAAvJ,4BAAA,EAAArB,MAAA,MAAA4K,GAAA,CAAAvJ,4BAAA,GAAArB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0C;UAK/F7C,EAFQ,CAAAsB,YAAA,EAAgB,EACd,EACJ;UAGFtB,EADJ,CAAAM,cAAA,cAAuB,kBAGoC;UADHN,EAAA,CAAAO,UAAA,0BAAA8N,mEAAAxL,MAAA;YAAA,OAAgB4K,GAAA,CAAAhE,4BAAA,CAAA5G,MAAA,CAAoC;UAAA,EAAC;UAwErG7C,EArEA,CAAAmB,UAAA,KAAAmN,gDAAA,yBAAgC,KAAAC,gDAAA,yBA2BsB,KAAAC,gDAAA,0BAoChB,KAAAC,gDAAA,0BAMD;UASjDzO,EAFQ,CAAAsB,YAAA,EAAU,EACR,EACJ;UAIEtB,EAFR,CAAAM,cAAA,cAAgE,cACoC,aAC7C;UAAAN,EAAA,CAAAkB,MAAA,sBAAc;UAAAlB,EAAA,CAAAsB,YAAA,EAAK;UAG9DtB,EADJ,CAAAM,cAAA,cAA2C,mBAEoC;UADrDN,EAAA,CAAAO,UAAA,mBAAAmO,6DAAA;YAAA,OAASjB,GAAA,CAAAnB,8BAAA,EAAgC;UAAA,EAAC;UAAhEtM,EAAA,CAAAsB,YAAA,EAC2E;UAE3EtB,EAAA,CAAAM,cAAA,wBAE+I;UAFjGN,EAAA,CAAA2N,gBAAA,2BAAAgB,0EAAA9L,MAAA;YAAA7C,EAAA,CAAA6N,kBAAA,CAAAJ,GAAA,CAAA7H,6BAAA,EAAA/C,MAAA,MAAA4K,GAAA,CAAA7H,6BAAA,GAAA/C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2C;UAKjG7C,EAFQ,CAAAsB,YAAA,EAAgB,EACd,EACJ;UAIFtB,EAFJ,CAAAM,cAAA,cAAuB,kBAIoC;UADHN,EAAA,CAAAO,UAAA,0BAAAqO,mEAAA/L,MAAA;YAAA,OAAgB4K,GAAA,CAAAjE,6BAAA,CAAA3G,MAAA,CAAqC;UAAA,EAAC;UAuEtG7C,EApEA,CAAAmB,UAAA,KAAA0N,gDAAA,yBAAgC,KAAAC,gDAAA,yBA4Be,KAAAC,gDAAA,0BAmCT,KAAAC,gDAAA,0BAKD;UASjDhP,EAFQ,CAAAsB,YAAA,EAAU,EACR,EACJ;UAMNtB,EAJA,CAAAC,SAAA,uCAAmE,yCAEI,yCAEA;;;UA3SvCD,EAAA,CAAAuB,SAAA,GAAmC;UAACvB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzDF,EAAA,CAAAuB,SAAA,EAA0B;UAA1BvB,EAAA,CAAAE,UAAA,YAAAuN,GAAA,CAAAnG,cAAA,CAA0B;UAACtH,EAAA,CAAAiP,gBAAA,YAAAxB,GAAA,CAAAzL,yBAAA,CAAuC;UAE7EhC,EAAA,CAAAE,UAAA,2IAA0I;UAOzIF,EAAA,CAAAuB,SAAA,GAAyB;UACVvB,EADf,CAAAE,UAAA,UAAAuN,GAAA,CAAAxM,eAAA,CAAyB,YAA4B,mBAAuC,oBAC9E,4BAA4B;UAyF3BjB,EAAA,CAAAuB,SAAA,IAAmC;UAACvB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzDF,EAAA,CAAAuB,SAAA,EAA6B;UAA7BvB,EAAA,CAAAE,UAAA,YAAAuN,GAAA,CAAAjG,iBAAA,CAA6B;UAACxH,EAAA,CAAAiP,gBAAA,YAAAxB,GAAA,CAAAvJ,4BAAA,CAA0C;UAEnFlE,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAuB,SAAA,GAAoC;UACrBvB,EADf,CAAAE,UAAA,UAAAuN,GAAA,CAAAhK,0BAAA,CAAoC,YAA4B,mBAAuC,oBACzF,4BAA4B;UAyF3BzD,EAAA,CAAAuB,SAAA,IAAmC;UAACvB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzDF,EAAA,CAAAuB,SAAA,EAA8B;UAA9BvB,EAAA,CAAAE,UAAA,YAAAuN,GAAA,CAAAlG,kBAAA,CAA8B;UAACvH,EAAA,CAAAiP,gBAAA,YAAAxB,GAAA,CAAA7H,6BAAA,CAA2C;UAErF5F,EAAA,CAAAE,UAAA,2IAA0I;UAOzIF,EAAA,CAAAuB,SAAA,GAA6B;UACdvB,EADf,CAAAE,UAAA,UAAAuN,GAAA,CAAAtI,mBAAA,CAA6B,YAA4B,mBAAuC,oBAClF,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}