{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let DashboardModule = /*#__PURE__*/(() => {\n  class DashboardModule {\n    static {\n      this.ɵfac = function DashboardModule_Factory(t) {\n        return new (t || DashboardModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: DashboardModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, DashboardRoutingModule]\n      });\n    }\n  }\n  return DashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}