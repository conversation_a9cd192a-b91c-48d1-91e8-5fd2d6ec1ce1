{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic [ar]\n//! author : <PERSON><PERSON> <PERSON>: https://github.com/abdelsaid\n//! author : <PERSON>\n//! author : forabi https://github.com/forabi\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '١',\n      2: '٢',\n      3: '٣',\n      4: '٤',\n      5: '٥',\n      6: '٦',\n      7: '٧',\n      8: '٨',\n      9: '٩',\n      0: '٠'\n    },\n    numberMap = {\n      '١': '1',\n      '٢': '2',\n      '٣': '3',\n      '٤': '4',\n      '٥': '5',\n      '٦': '6',\n      '٧': '7',\n      '٨': '8',\n      '٩': '9',\n      '٠': '0'\n    },\n    pluralForm = function (n) {\n      return n === 0 ? 0 : n === 1 ? 1 : n === 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5;\n    },\n    plurals = {\n      s: ['أقل من ثانية', 'ثانية واحدة', ['ثانيتان', 'ثانيتين'], '%d ثوان', '%d ثانية', '%d ثانية'],\n      m: ['أقل من دقيقة', 'دقيقة واحدة', ['دقيقتان', 'دقيقتين'], '%d دقائق', '%d دقيقة', '%d دقيقة'],\n      h: ['أقل من ساعة', 'ساعة واحدة', ['ساعتان', 'ساعتين'], '%d ساعات', '%d ساعة', '%d ساعة'],\n      d: ['أقل من يوم', 'يوم واحد', ['يومان', 'يومين'], '%d أيام', '%d يومًا', '%d يوم'],\n      M: ['أقل من شهر', 'شهر واحد', ['شهران', 'شهرين'], '%d أشهر', '%d شهرا', '%d شهر'],\n      y: ['أقل من عام', 'عام واحد', ['عامان', 'عامين'], '%d أعوام', '%d عامًا', '%d عام']\n    },\n    pluralize = function (u) {\n      return function (number, withoutSuffix, string, isFuture) {\n        var f = pluralForm(number),\n          str = plurals[u][pluralForm(number)];\n        if (f === 2) {\n          str = str[withoutSuffix ? 0 : 1];\n        }\n        return str.replace(/%d/i, number);\n      };\n    },\n    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];\n  var ar = moment.defineLocale('ar', {\n    months: months,\n    monthsShort: months,\n    weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'D/\\u200FM/\\u200FYYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ص|م/,\n    isPM: function (input) {\n      return 'م' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ص';\n      } else {\n        return 'م';\n      }\n    },\n    calendar: {\n      sameDay: '[اليوم عند الساعة] LT',\n      nextDay: '[غدًا عند الساعة] LT',\n      nextWeek: 'dddd [عند الساعة] LT',\n      lastDay: '[أمس عند الساعة] LT',\n      lastWeek: 'dddd [عند الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'بعد %s',\n      past: 'منذ %s',\n      s: pluralize('s'),\n      ss: pluralize('s'),\n      m: pluralize('m'),\n      mm: pluralize('m'),\n      h: pluralize('h'),\n      hh: pluralize('h'),\n      d: pluralize('d'),\n      dd: pluralize('d'),\n      M: pluralize('M'),\n      MM: pluralize('M'),\n      y: pluralize('y'),\n      yy: pluralize('y')\n    },\n    preparse: function (string) {\n      return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return ar;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}