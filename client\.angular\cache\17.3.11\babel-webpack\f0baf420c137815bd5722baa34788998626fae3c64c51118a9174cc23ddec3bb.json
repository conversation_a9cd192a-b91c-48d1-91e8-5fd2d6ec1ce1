{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../prospects.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"45rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ProspectsSalesTeamComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 35);\n    i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ProspectsSalesTeamComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 29)(5, ProspectsSalesTeamComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 27);\n    i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"first_name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3, \" First Name \");\n    i0.ɵɵtemplate(4, ProspectsSalesTeamComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 29)(5, ProspectsSalesTeamComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProspectsSalesTeamComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 31);\n    i0.ɵɵelementStart(7, \"th\")(8, \"div\", 32);\n    i0.ɵɵtext(9, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const employee_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (employee_r6 == null ? null : employee_r6.business_partner == null ? null : employee_r6.business_partner.last_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const employee_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (employee_r6 == null ? null : employee_r6.partner_role) || \"-\", \" \");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const employee_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (employee_r6 == null ? null : employee_r6.business_partner == null ? null : employee_r6.business_partner.addresses == null ? null : employee_r6.business_partner.addresses[0] == null ? null : employee_r6.business_partner.addresses[0].emails == null ? null : employee_r6.business_partner.addresses[0].emails[0] == null ? null : employee_r6.business_partner.addresses[0].emails[0].email_address) || \"-\", \" \");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_10_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 40);\n    i0.ɵɵtemplate(3, ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 41)(4, ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 41)(5, ProspectsSalesTeamComponent_ng_template_10_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 41);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"last_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_role\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ProspectsSalesTeamComponent_ng_template_10_ng_container_3_Template, 6, 4, \"ng-container\", 31);\n    i0.ɵɵelementStart(4, \"td\")(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_ng_template_10_Template_button_click_5_listener() {\n      const employee_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editemployee(employee_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_ng_template_10_Template_button_click_6_listener($event) {\n      const employee_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(employee_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const employee_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (employee_r6 == null ? null : employee_r6.business_partner == null ? null : employee_r6.business_partner.first_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 42);\n    i0.ɵɵtext(2, \"No Sales Teams found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 42);\n    i0.ɵɵtext(2, \"Loading Sales Teams data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Sales Team\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProspectsSalesTeamComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Role is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProspectsSalesTeamComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ProspectsSalesTeamComponent_div_25_div_1_Template, 2, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"partner_function\"].errors[\"required\"]);\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r8.bp_full_name, \"\");\n  }\n}\nfunction ProspectsSalesTeamComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ProspectsSalesTeamComponent_ng_template_36_span_3_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.bp_full_name);\n  }\n}\nfunction ProspectsSalesTeamComponent_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Employee is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProspectsSalesTeamComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ProspectsSalesTeamComponent_div_37_div_1_Template, 2, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"bp_customer_number\"].errors[\"required\"]);\n  }\n}\nexport let ProspectsSalesTeamComponent = /*#__PURE__*/(() => {\n  class ProspectsSalesTeamComponent {\n    constructor(formBuilder, prospectsservice, messageservice, confirmationservice) {\n      this.formBuilder = formBuilder;\n      this.prospectsservice = prospectsservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.employeeDetails = [];\n      this.addDialogVisible = false;\n      this.visible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.id = '';\n      this.editid = '';\n      this.partnerfunction = [];\n      this.partnerLoading = false;\n      this.employeeLoading = false;\n      this.employeeInput$ = new Subject();\n      this.defaultOptions = [];\n      this.EmployeeForm = this.formBuilder.group({\n        partner_function: [null, Validators.required],\n        bp_customer_number: [null, Validators.required]\n      });\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'last_name',\n        header: 'Last Name'\n      }, {\n        field: 'partner_role',\n        header: 'Role'\n      }, {\n        field: 'email_address',\n        header: 'Email'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.employeeDetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadPartners();\n      this.loadEmployees();\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadPartners() {\n      this.partnerLoading = true;\n      this.prospectsservice.getPartnerfunction().pipe(takeUntil(this.unsubscribe$), switchMap(partners => {\n        this.partnerfunction = partners || [];\n        return this.prospectsservice.prospect.pipe(takeUntil(this.unsubscribe$));\n      })).subscribe({\n        next: response => {\n          if (!response) return;\n          this.id = response?.customer?.customer_id;\n          const filteredPartners = response.customer.partner_functions.filter(pf => this.partnerfunction.some(partner => partner?.value === pf.partner_function));\n          if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\n            this.employeeDetails = filteredPartners.map(pf => {\n              const matchedPartner = this.partnerfunction.find(partner => partner?.value === pf.partner_function);\n              return {\n                ...pf,\n                partner_role: matchedPartner ? matchedPartner?.label : null,\n                // Adding partner label\n                addresses: this.filterXXDefaultAddresses(response?.customer?.partner_functions?.business_partner?.addresses || [])\n              };\n            });\n          } else {\n            this.employeeDetails = [];\n          }\n          this.partnerLoading = false;\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n          this.partnerfunction = [];\n          this.employeeDetails = [];\n          this.partnerLoading = false;\n        },\n        complete: () => {\n          console.log('Partner function and employee details loaded successfully.');\n        }\n      });\n    }\n    filterXXDefaultAddresses(addresses) {\n      return addresses.filter(address => address.address_usages && address.address_usages.some(usage => usage.address_usage === 'XXDEFAULT'));\n    }\n    loadEmployees() {\n      this.employees$ = concat(of(this.defaultOptions), this.employeeInput$.pipe(distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP003',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n          return this.prospectsservice.getEmployee(params).pipe(map(data => {\n            return data;\n          }), tap(() => this.employeeLoading = false));\n        }\n        return of([]).pipe(tap(() => this.employeeLoading = false));\n      })));\n    }\n    editemployee(employee) {\n      this.visible = true;\n      this.editid = employee?.documentId;\n      this.defaultOptions = [];\n      this.defaultOptions.push({\n        bp_full_name: employee?.business_partner?.bp_full_name,\n        bp_id: employee?.bp_customer_number\n      });\n      this.loadEmployees();\n      // Patch the form with existing employee data\n      this.EmployeeForm.patchValue({\n        ...employee\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        _this.visible = true;\n        if (_this.EmployeeForm.invalid) {\n          _this.visible = true;\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.EmployeeForm.value\n        };\n        const data = {\n          ...(!_this.editid ? {\n            customer_id: _this.id\n          } : {}),\n          partner_function: value?.partner_function,\n          bp_customer_number: value?.bp_customer_number\n        };\n        if (_this.editid) {\n          _this.prospectsservice.updateEmployee(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.editid = '';\n              _this.EmployeeForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Employee Updated successfully!.'\n              });\n              _this.prospectsservice.getProspectByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.prospectsservice.createEmployee(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.editid = '';\n              _this.EmployeeForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Employee created successfully!.'\n              });\n              _this.prospectsservice.getProspectByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    get f() {\n      return this.EmployeeForm.controls;\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.prospectsservice.deleteEmployee(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.prospectsservice.getProspectByID(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    showNewDialog(position) {\n      this.editid = '';\n      this.EmployeeForm.patchValue({\n        partner_function: null,\n        bp_customer_number: null\n      });\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.EmployeeForm.reset();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ProspectsSalesTeamComponent_Factory(t) {\n        return new (t || ProspectsSalesTeamComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProspectsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProspectsSalesTeamComponent,\n        selectors: [[\"app-prospects-sales-team\"]],\n        decls: 43,\n        vars: 39,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add Employee\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", \"font-semibold\", 3, \"click\", \"rounded\", \"styleClass\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Role\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"optionLabel\", \"label\", \"optionValue\", \"value\", \"appendTo\", \"body\", \"formControlName\", \"partner_function\", \"loading\", \"partnerLoading\", \"placeholder\", \"Select Partner Function\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"class\", \"invalid-feedback top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [\"for\", \"Employee\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"bp_customer_number\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"font-medium\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"tooltipPosition\", \"top\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"tooltipPosition\", \"top\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"invalid-feedback\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n        template: function ProspectsSalesTeamComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Sales Team\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_Template_p_button_click_5_listener() {\n              return ctx.showNewDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsSalesTeamComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function ProspectsSalesTeamComponent_Template_p_table_onColReorder_8_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(9, ProspectsSalesTeamComponent_ng_template_9_Template, 10, 3, \"ng-template\", 8)(10, ProspectsSalesTeamComponent_ng_template_10_Template, 7, 2, \"ng-template\", 9)(11, ProspectsSalesTeamComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, ProspectsSalesTeamComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"p-dialog\", 12);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function ProspectsSalesTeamComponent_Template_p_dialog_visibleChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(14, ProspectsSalesTeamComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n            i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"label\", 15)(18, \"span\", 16);\n            i0.ɵɵtext(19, \"supervisor_account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(20, \"Role\");\n            i0.ɵɵelementStart(21, \"span\", 17);\n            i0.ɵɵtext(22, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 18);\n            i0.ɵɵelement(24, \"p-dropdown\", 19);\n            i0.ɵɵtemplate(25, ProspectsSalesTeamComponent_div_25_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"label\", 21)(28, \"span\", 16);\n            i0.ɵɵtext(29, \"badge\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \"Employee\");\n            i0.ɵɵelementStart(31, \"span\", 17);\n            i0.ɵɵtext(32, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 18)(34, \"ng-select\", 22);\n            i0.ɵɵpipe(35, \"async\");\n            i0.ɵɵtemplate(36, ProspectsSalesTeamComponent_ng_template_36_Template, 4, 2, \"ng-template\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, ProspectsSalesTeamComponent_div_37_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 24)(39, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_Template_button_click_39_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵtext(40, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function ProspectsSalesTeamComponent_Template_button_click_41_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(42, \" Save \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"px-3\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.employeeDetails)(\"rows\", 8)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(34, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.EmployeeForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.partnerfunction)(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(35, _c1, ctx.submitted && ctx.f[\"partner_function\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"partner_function\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(35, 32, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(37, _c1, ctx.submitted && ctx.f[\"bp_customer_number\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"bp_customer_number\"].errors);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i1.FormGroupDirective, i1.FormControlName, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.InputText, i11.Dialog, i12.MultiSelect, i4.AsyncPipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}\"]\n      });\n    }\n  }\n  return ProspectsSalesTeamComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}