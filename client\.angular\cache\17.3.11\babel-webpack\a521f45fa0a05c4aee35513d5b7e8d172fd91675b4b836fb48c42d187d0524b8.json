{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"../../../opportunities.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/editor\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"primeng/calendar\";\nimport * as i14 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nconst _c3 = () => ({\n  width: \"45rem\"\n});\nfunction ActivitiesSalesCallFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Sales Call\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Document Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_12_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"document_type\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_21_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_32_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesSalesCallFormComponent_ng_template_42_span_3_Template, 2, 1, \"span\", 54)(4, ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bp_id, \": \", item_r4.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_43_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_52_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_81_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_81_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_90_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_99_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_99_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"notes\"].errors && ctx_r1.f[\"notes\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_109_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 59)(2, \"div\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 56);\n    i0.ɵɵtext(2, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesSalesCallFormComponent_ng_template_109_ng_container_3_Template, 4, 1, \"ng-container\", 57);\n    i0.ɵɵelementStart(4, \"th\", 58);\n    i0.ɵɵtext(5, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"input\", 66);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 64);\n    i0.ɵɵtemplate(3, ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_ng_container_3_Template, 2, 0, \"ng-container\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_110_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_ng_template_110_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r8 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r8));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 61)(1, \"td\", 56);\n    i0.ɵɵelement(2, \"input\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_Template, 4, 2, \"ng-container\", 57);\n    i0.ɵɵelementStart(4, \"td\", 58);\n    i0.ɵɵtemplate(5, ActivitiesSalesCallFormComponent_ng_template_110_button_5_Template, 1, 0, \"button\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.involved_parties.length > 1);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_122_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_122_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.email, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_122_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.mobile, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesSalesCallFormComponent_ng_template_122_span_2_Template, 2, 1, \"span\", 54)(3, ActivitiesSalesCallFormComponent_ng_template_122_span_3_Template, 2, 1, \"span\", 54)(4, ActivitiesSalesCallFormComponent_ng_template_122_span_4_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.mobile);\n  }\n}\nexport class ActivitiesSalesCallFormComponent {\n  constructor(formBuilder, route, activitiesservice, opportunitiesservice, messageservice) {\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.activitiesservice = activitiesservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.visible = false;\n    this.onClose = new EventEmitter();\n    this.opportunity_id = '';\n    this.submitted = false;\n    this.saving = false;\n    this.position = 'right';\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.defaultOptions = [];\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.existingcontactLoading = false;\n    this.existingcontactInput$ = new Subject();\n    this.owner_id = null;\n    this.FollowUpForm = this.formBuilder.group({\n      document_type: ['', [Validators.required]],\n      subject: ['', [Validators.required]],\n      main_account_party_id: [null, [Validators.required]],\n      main_contact_party_id: [null, [Validators.required]],\n      phone_call_category: ['', [Validators.required]],\n      disposition_code: [''],\n      start_date: [''],\n      end_date: [''],\n      initiator_code: ['', [Validators.required]],\n      activity_status: ['', [Validators.required]],\n      notes: ['', [Validators.required]],\n      contactexisting: [null],\n      involved_parties: this.formBuilder.array([this.createContactFormGroup()])\n    });\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityCategory: [],\n      activityStatus: [],\n      activitydisposition: [],\n      activityInitiatorCode: []\n    };\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'email',\n      header: 'Email Address'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  ngOnInit() {\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    setTimeout(() => {\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n    }, 50);\n    this.FollowUpForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.getOwner().subscribe({\n      next: response => {\n        this.owner_id = response;\n      },\n      error: err => {\n        console.error('Error fetching bp_id:', err);\n      }\n    });\n    this.loadAccounts();\n    this.loadExistingContacts();\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  TableColumnReorder(event) {\n    const draggedCol = this.cols[event.dragIndex];\n    this.cols.splice(event.dragIndex, 1);\n    this.cols.splice(event.dropIndex, 0, draggedCol);\n  }\n  getOwner() {\n    return this.activitiesservice.getEmailwisePartner();\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      const options = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n      // Assign options to dropdown object\n      this.dropdowns[target] = options;\n      // Set 'Open' as default selected for activityStatus only\n      if (target === 'activityStatus') {\n        const openOption = options.find(opt => opt.label.toLowerCase() === 'open');\n        if (openOption) {\n          const control = this.FollowUpForm?.get('activity_status');\n          if (control) {\n            control.setValue(openOption.value);\n          }\n        }\n      }\n    });\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'filters[roles][bp_role][$in][2]': 'PRO001',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadExistingContacts() {\n    this.existingcontacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.existingcontactInput$.pipe(distinctUntilChanged(), tap(() => this.existingcontactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.existingcontactLoading = false), catchError(error => {\n        this.existingcontactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  selectExistingContact() {\n    this.addExistingContact(this.FollowUpForm.value);\n    this.existingDialogVisible = false; // Close dialog\n  }\n  addExistingContact(existing) {\n    const contactForm = this.formBuilder.group({\n      bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\n      email_address: [existing?.contactexisting?.email || ''],\n      role_code: 'BUP001',\n      party_id: existing?.contactexisting?.bp_id || ''\n    });\n    const firstGroup = this.involved_parties.at(0);\n    const bpName = firstGroup?.get('bp_full_name')?.value;\n    if (!bpName && this.involved_parties.length === 1) {\n      // Replace the default empty group\n      this.involved_parties.setControl(0, contactForm);\n    } else {\n      // Otherwise, add a new contact\n      this.involved_parties.push(contactForm);\n    }\n    this.existingDialogVisible = false; // Close dialog\n  }\n  deleteContact(index) {\n    if (this.involved_parties.length > 1) {\n      this.involved_parties.removeAt(index);\n    }\n  }\n  createContactFormGroup() {\n    return this.formBuilder.group({\n      bp_full_name: [''],\n      email_address: ['']\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.FollowUpForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.FollowUpForm.value\n      };\n      const data = {\n        document_type: value?.document_type,\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        phone_call_category: value?.phone_call_category,\n        start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n        disposition_code: value?.disposition_code,\n        initiator_code: value?.initiator_code,\n        owner_party_id: _this.owner_id,\n        activity_status: value?.activity_status,\n        note: value?.notes,\n        involved_parties: Array.isArray(value.involved_parties) ? [...value.involved_parties, ...(value?.main_account_party_id ? [{\n          role_code: 'FLCU01',\n          party_id: value.main_account_party_id\n        }] : []), ...(Array.isArray(value.main_contact_party_id) ? value.main_contact_party_id.map(id => ({\n          role_code: 'BUP001',\n          party_id: id\n        })) : []), ...(_this.owner_id ? [{\n          role_code: 'BUP003',\n          party_id: _this.owner_id\n        }] : [])] : [],\n        type_code: '0002',\n        opportunity_id: _this.opportunity_id\n      };\n      _this.activitiesservice.createOpportuniyFollowup(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.addDialogVisible = false;\n          _this.saving = false;\n          _this.visible = false;\n          _this.FollowUpForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Follow Up Added Successfully!'\n          });\n          _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.FollowUpForm.controls;\n  }\n  get involved_parties() {\n    return this.FollowUpForm.get('involved_parties');\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  hideDialog() {\n    this.onClose.emit();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ActivitiesSalesCallFormComponent_Factory(t) {\n      return new (t || ActivitiesSalesCallFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.OpportunitiesService), i0.ɵɵdirectiveInject(i5.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesSalesCallFormComponent,\n      selectors: [[\"app-opportunities-sales-call-form\"]],\n      inputs: {\n        visible: \"visible\"\n      },\n      outputs: {\n        onClose: \"onClose\"\n      },\n      decls: 131,\n      vars: 98,\n      consts: [[\"dt\", \"\"], [1, \"followup-popup\", 3, \"onHide\", \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [\"pTemplate\", \"header\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Transaction Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"formControlName\", \"document_type\", \"placeholder\", \"Select a Document Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Subject\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", \"placeholder\", \"Select a Contact\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Disposition Code\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"disposition_code\", \"placeholder\", \"Select a Disposition Code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"start_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Call Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"end_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"End Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"notes\", \"placeholder\", \"Enter your note here...\", \"styleClass\", \"w-full\", 3, \"ngClass\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", \"followup-add-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"body\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", \"placeholder\", \"Search for a contact\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"font-medium\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"border-round-left-lg\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [\"pReorderableColumn\", \"\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [3, \"formGroup\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Enter a Name\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"]],\n      template: function ActivitiesSalesCallFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p-dialog\", 1);\n          i0.ɵɵlistener(\"onHide\", function ActivitiesSalesCallFormComponent_Template_p_dialog_onHide_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideDialog());\n          });\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", 2);\n          i0.ɵɵelementStart(2, \"form\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"label\", 6)(6, \"span\", 7);\n          i0.ɵɵtext(7, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \"Transaction Type \");\n          i0.ɵɵelementStart(9, \"span\", 8);\n          i0.ɵɵtext(10, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(11, \"p-dropdown\", 9);\n          i0.ɵɵtemplate(12, ActivitiesSalesCallFormComponent_div_12_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 11)(15, \"span\", 7);\n          i0.ɵɵtext(16, \"subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \"Subject \");\n          i0.ɵɵelementStart(18, \"span\", 8);\n          i0.ɵɵtext(19, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"input\", 12);\n          i0.ɵɵtemplate(21, ActivitiesSalesCallFormComponent_div_21_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 13)(24, \"span\", 7);\n          i0.ɵɵtext(25, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \"Account \");\n          i0.ɵɵelementStart(27, \"span\", 8);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"ng-select\", 14);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵtemplate(31, ActivitiesSalesCallFormComponent_ng_template_31_Template, 3, 2, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ActivitiesSalesCallFormComponent_div_32_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 16)(34, \"label\", 17)(35, \"span\", 7);\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \"Contact \");\n          i0.ɵɵelementStart(38, \"span\", 8);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"ng-select\", 18);\n          i0.ɵɵpipe(41, \"async\");\n          i0.ɵɵtemplate(42, ActivitiesSalesCallFormComponent_ng_template_42_Template, 5, 4, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, ActivitiesSalesCallFormComponent_div_43_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 16)(45, \"label\", 19)(46, \"span\", 7);\n          i0.ɵɵtext(47, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \"Category \");\n          i0.ɵɵelementStart(49, \"span\", 8);\n          i0.ɵɵtext(50, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(51, \"p-dropdown\", 20);\n          i0.ɵɵtemplate(52, ActivitiesSalesCallFormComponent_div_52_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 16)(54, \"label\", 21)(55, \"span\", 7);\n          i0.ɵɵtext(56, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \"Disposition Code \");\n          i0.ɵɵelementStart(58, \"span\", 8);\n          i0.ɵɵtext(59, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(60, \"p-dropdown\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 16)(62, \"label\", 23)(63, \"span\", 7);\n          i0.ɵɵtext(64, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \"Call Date/Time \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(66, \"p-calendar\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 16)(68, \"label\", 25)(69, \"span\", 7);\n          i0.ɵɵtext(70, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \"End Date/Time \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"p-calendar\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 16)(74, \"label\", 27)(75, \"span\", 7);\n          i0.ɵɵtext(76, \"label\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \"Type \");\n          i0.ɵɵelementStart(78, \"span\", 8);\n          i0.ɵɵtext(79, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(80, \"p-dropdown\", 28);\n          i0.ɵɵtemplate(81, ActivitiesSalesCallFormComponent_div_81_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 16)(83, \"label\", 29)(84, \"span\", 7);\n          i0.ɵɵtext(85, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(86, \"Status \");\n          i0.ɵɵelementStart(87, \"span\", 8);\n          i0.ɵɵtext(88, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(89, \"p-dropdown\", 30);\n          i0.ɵɵtemplate(90, ActivitiesSalesCallFormComponent_div_90_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 31)(92, \"label\", 32)(93, \"span\", 7);\n          i0.ɵɵtext(94, \"notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(95, \"Notes \");\n          i0.ɵɵelementStart(96, \"span\", 8);\n          i0.ɵɵtext(97, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(98, \"p-editor\", 33);\n          i0.ɵɵtemplate(99, ActivitiesSalesCallFormComponent_div_99_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"div\", 34)(101, \"div\", 35)(102, \"h4\", 36);\n          i0.ɵɵtext(103, \"Additional Contacts Persons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 37)(105, \"p-button\", 38);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_p_button_click_105_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(106, \"div\", 39)(107, \"p-table\", 40, 0);\n          i0.ɵɵlistener(\"onColReorder\", function ActivitiesSalesCallFormComponent_Template_p_table_onColReorder_107_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.TableColumnReorder($event));\n          });\n          i0.ɵɵtemplate(109, ActivitiesSalesCallFormComponent_ng_template_109_Template, 6, 1, \"ng-template\", 2)(110, ActivitiesSalesCallFormComponent_ng_template_110_Template, 6, 3, \"ng-template\", 41);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(111, \"p-dialog\", 42);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_111_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(112, ActivitiesSalesCallFormComponent_ng_template_112_Template, 2, 0, \"ng-template\", 2);\n          i0.ɵɵelementStart(113, \"form\", 3)(114, \"div\", 43)(115, \"label\", 44)(116, \"span\", 7);\n          i0.ɵɵtext(117, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(118, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"div\", 45)(120, \"ng-select\", 46);\n          i0.ɵɵpipe(121, \"async\");\n          i0.ɵɵtemplate(122, ActivitiesSalesCallFormComponent_ng_template_122_Template, 5, 4, \"ng-template\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(123, \"div\", 47)(124, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_124_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n          });\n          i0.ɵɵtext(125, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_126_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectExistingContact());\n          });\n          i0.ɵɵtext(127, \" Save \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(128, \"div\", 50)(129, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_129_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.visible = false);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_130_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(79, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.FollowUpForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"])(\"ngClass\", i0.ɵɵpureFunction1(80, _c1, ctx.submitted && ctx.f[\"document_type\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"document_type\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(82, _c1, ctx.submitted && ctx.f[\"subject\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"subject\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(30, 73, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(84, _c1, ctx.submitted && ctx.f[\"main_account_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_account_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(41, 75, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(86, _c1, ctx.submitted && ctx.f[\"main_contact_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_contact_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityCategory\"])(\"ngClass\", i0.ɵɵpureFunction1(88, _c1, ctx.submitted && ctx.f[\"phone_call_category\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"phone_call_category\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activitydisposition\"]);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(90, _c1, ctx.submitted && ctx.f[\"initiator_code\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"initiator_code\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(92, _c1, ctx.submitted && ctx.f[\"activity_status\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"activity_status\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(94, _c2));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(95, _c1, ctx.submitted && ctx.f[\"notes\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"notes\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.involved_parties == null ? null : ctx.involved_parties.controls)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(97, _c3));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.FollowUpForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(121, 77, ctx.existingcontacts$))(\"hideSelected\", true)(\"loading\", ctx.existingcontactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.existingcontactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.Table, i5.PrimeTemplate, i7.ReorderableColumn, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.Editor, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.Calendar, i14.InputText, i6.AsyncPipe],\n      styles: [\".followup-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .followup-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .followup-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .followup-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n  .followup-popup .p-dialog.p-component.p-dialog-resizable {\\n  width: calc(100vw - 510px) !important;\\n}\\n  .followup-popup .field {\\n  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1mb2xsb3ctdXAvYWN0aXZpdGllcy1zYWxlcy1jYWxsLWZvcm0vYWN0aXZpdGllcy1zYWxlcy1jYWxsLWZvcm0uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRVE7RUFDSSxrQkFBQTtBQURaO0FBR1k7RUFDSSw0QkFBQTtFQUNBLDJDQUFBO0FBRGhCO0FBR2dCO0VBQ0ksU0FBQTtBQURwQjtBQUtZO0VBQ0ksNEJBQUE7RUFDQSxpQkFBQTtBQUhoQjtBQU9RO0VBQ0kscUNBQUE7QUFMWjtBQVFRO0VBQ0ksNERBQUE7QUFOWiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCB7XHJcbiAgICAuZm9sbG93dXAtcG9wdXAge1xyXG4gICAgICAgIC5wLWRpYWxvZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNTBweDtcclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucC1kaWFsb2cucC1jb21wb25lbnQucC1kaWFsb2ctcmVzaXphYmxlIHtcclxuICAgICAgICAgICAgd2lkdGg6IGNhbGMoMTAwdncgLSA1MTBweCkgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5maWVsZCB7XHJcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDM2MHB4LCAxZnIpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subject", "takeUntil", "concat", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ActivitiesSalesCallFormComponent_div_12_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "submitted", "f", "errors", "ActivitiesSalesCallFormComponent_div_21_div_1_Template", "ɵɵtextInterpolate1", "item_r3", "bp_full_name", "ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template", "ɵɵtextInterpolate", "bp_id", "ActivitiesSalesCallFormComponent_div_32_div_1_Template", "item_r4", "email", "mobile", "ActivitiesSalesCallFormComponent_ng_template_42_span_3_Template", "ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template", "ɵɵtextInterpolate2", "ActivitiesSalesCallFormComponent_div_43_div_1_Template", "ActivitiesSalesCallFormComponent_div_52_div_1_Template", "ActivitiesSalesCallFormComponent_div_81_div_1_Template", "ActivitiesSalesCallFormComponent_div_90_div_1_Template", "ActivitiesSalesCallFormComponent_div_99_div_1_Template", "ɵɵelementContainerStart", "col_r5", "header", "ActivitiesSalesCallFormComponent_ng_template_109_ng_container_3_Template", "selectedColumns", "ɵɵelement", "ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_ng_container_3_Template", "col_r6", "field", "ɵɵlistener", "ActivitiesSalesCallFormComponent_ng_template_110_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "i_r8", "ɵɵnextContext", "rowIndex", "ɵɵresetView", "deleteContact", "ActivitiesSalesCallFormComponent_ng_template_110_ng_container_3_Template", "ActivitiesSalesCallFormComponent_ng_template_110_button_5_Template", "contact_r9", "involved_parties", "length", "item_r10", "ActivitiesSalesCallFormComponent_ng_template_122_span_2_Template", "ActivitiesSalesCallFormComponent_ng_template_122_span_3_Template", "ActivitiesSalesCallFormComponent_ng_template_122_span_4_Template", "ActivitiesSalesCallFormComponent", "constructor", "formBuilder", "route", "activitiesservice", "opportunitiesservice", "messageservice", "unsubscribe$", "visible", "onClose", "opportunity_id", "saving", "position", "addDialogVisible", "existingDialogVisible", "defaultOptions", "accountLoading", "accountInput$", "contactLoading", "contactInput$", "existingcontactLoading", "existingcontactInput$", "owner_id", "FollowUpForm", "group", "document_type", "required", "subject", "main_account_party_id", "main_contact_party_id", "phone_call_category", "disposition_code", "start_date", "end_date", "initiator_code", "activity_status", "notes", "contactexisting", "array", "createContactFormGroup", "dropdowns", "activityDocumentType", "activityCategory", "activityStatus", "activitydisposition", "activityInitiatorCode", "_selectedColumns", "cols", "sortField", "sortOrder", "ngOnInit", "parent", "snapshot", "paramMap", "get", "setTimeout", "loadActivityDropDown", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "contacts$", "err", "console", "error", "subscribe", "get<PERSON>wner", "next", "response", "loadAccounts", "loadExistingContacts", "val", "filter", "col", "includes", "TableColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "getEmail<PERSON><PERSON><PERSON><PERSON>", "target", "type", "getActivityDropdownOptions", "res", "options", "data", "attr", "label", "description", "value", "code", "openOption", "find", "opt", "toLowerCase", "control", "setValue", "accounts$", "term", "params", "getPartners", "existingcontacts$", "bpId", "getPartnersContact", "contacts", "selectExistingContact", "addExistingContact", "existing", "contactForm", "email_address", "role_code", "party_id", "firstGroup", "at", "bpName", "setControl", "push", "index", "removeAt", "onSubmit", "_this", "_asyncToGenerator", "invalid", "formatDate", "owner_party_id", "note", "Array", "isArray", "id", "type_code", "createOpportuniyFollowup", "reset", "add", "severity", "detail", "getOpportunityByID", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showExistingDialog", "hideDialog", "emit", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "ActivitiesService", "i4", "OpportunitiesService", "i5", "MessageService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ActivitiesSalesCallFormComponent_Template", "rf", "ctx", "ActivitiesSalesCallFormComponent_Template_p_dialog_onHide_0_listener", "_r1", "ɵɵtwoWayListener", "ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ActivitiesSalesCallFormComponent_ng_template_1_Template", "ActivitiesSalesCallFormComponent_div_12_Template", "ActivitiesSalesCallFormComponent_div_21_Template", "ActivitiesSalesCallFormComponent_ng_template_31_Template", "ActivitiesSalesCallFormComponent_div_32_Template", "ActivitiesSalesCallFormComponent_ng_template_42_Template", "ActivitiesSalesCallFormComponent_div_43_Template", "ActivitiesSalesCallFormComponent_div_52_Template", "ActivitiesSalesCallFormComponent_div_81_Template", "ActivitiesSalesCallFormComponent_div_90_Template", "ActivitiesSalesCallFormComponent_div_99_Template", "ActivitiesSalesCallFormComponent_Template_p_button_click_105_listener", "ActivitiesSalesCallFormComponent_Template_p_table_onColReorder_107_listener", "ActivitiesSalesCallFormComponent_ng_template_109_Template", "ActivitiesSalesCallFormComponent_ng_template_110_Template", "ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_111_listener", "ActivitiesSalesCallFormComponent_ng_template_112_Template", "ActivitiesSalesCallFormComponent_ng_template_122_Template", "ActivitiesSalesCallFormComponent_Template_button_click_124_listener", "ActivitiesSalesCallFormComponent_Template_button_click_126_listener", "ActivitiesSalesCallFormComponent_Template_button_click_129_listener", "ActivitiesSalesCallFormComponent_Template_button_click_130_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c1", "ɵɵclassMap", "ɵɵpipeBind1", "_c2", "_c3"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\activities-sales-call-form\\activities-sales-call-form.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\activities-sales-call-form\\activities-sales-call-form.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { OpportunitiesService } from '../../../opportunities.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\ninterface DropdownOption {\r\n  label: string;\r\n  value: string;\r\n}\r\n@Component({\r\n  selector: 'app-opportunities-sales-call-form',\r\n  templateUrl: './activities-sales-call-form.component.html',\r\n  styleUrl: './activities-sales-call-form.component.scss',\r\n})\r\nexport class ActivitiesSalesCallFormComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @Input() visible: boolean = false;\r\n  @Output() onClose = new EventEmitter<void>();\r\n  public opportunity_id: string = '';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public position: string = 'right';\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  private defaultOptions: any = [];\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public existingcontacts$?: Observable<any[]>;\r\n  public existingcontactLoading = false;\r\n  public existingcontactInput$ = new Subject<string>();\r\n  private owner_id: string | null = null;\r\n\r\n  public FollowUpForm: FormGroup = this.formBuilder.group({\r\n    document_type: ['', [Validators.required]],\r\n    subject: ['', [Validators.required]],\r\n    main_account_party_id: [null, [Validators.required]],\r\n    main_contact_party_id: [null, [Validators.required]],\r\n    phone_call_category: ['', [Validators.required]],\r\n    disposition_code: [''],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    initiator_code: ['', [Validators.required]],\r\n    activity_status: ['', [Validators.required]],\r\n    notes: ['', [Validators.required]],\r\n    contactexisting: [null],\r\n    involved_parties: this.formBuilder.array([this.createContactFormGroup()]),\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activitydisposition: [],\r\n    activityInitiatorCode: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private activitiesservice: ActivitiesService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [{ field: 'email', header: 'Email Address' }];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  ngOnInit() {\r\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    setTimeout(() => {\r\n      this.loadActivityDropDown(\r\n        'activityDocumentType',\r\n        'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n      );\r\n      this.loadActivityDropDown(\r\n        'activityCategory',\r\n        'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n      );\r\n      this.loadActivityDropDown(\r\n        'activitydisposition',\r\n        'CRM_ACTIVITY_DISPOSITION_CODE'\r\n      );\r\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n      this.loadActivityDropDown(\r\n        'activityInitiatorCode',\r\n        'CRM_ACTIVITY_INITIATOR_CODE'\r\n      );\r\n    }, 50);\r\n    this.FollowUpForm.get('main_account_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.unsubscribe$),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.getOwner().subscribe({\r\n      next: (response: string | null) => {\r\n        this.owner_id = response;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching bp_id:', err);\r\n      },\r\n    });\r\n    this.loadAccounts();\r\n    this.loadExistingContacts();\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n  TableColumnReorder(event: any) {\r\n    const draggedCol = this.cols[event.dragIndex];\r\n    this.cols.splice(event.dragIndex, 1);\r\n    this.cols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  private getOwner(): Observable<string | null> {\r\n    return this.activitiesservice.getEmailwisePartner();\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        const options: DropdownOption[] =\r\n          res?.data?.map(\r\n            (attr: { description: string; code: string }): DropdownOption => ({\r\n              label: attr.description,\r\n              value: attr.code,\r\n            })\r\n          ) ?? [];\r\n\r\n        // Assign options to dropdown object\r\n        this.dropdowns[target] = options;\r\n\r\n        // Set 'Open' as default selected for activityStatus only\r\n        if (target === 'activityStatus') {\r\n          const openOption = options.find(\r\n            (opt) => opt.label.toLowerCase() === 'open'\r\n          );\r\n\r\n          if (openOption) {\r\n            const control = this.FollowUpForm?.get('activity_status');\r\n            if (control) {\r\n              control.setValue(openOption.value);\r\n            }\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'filters[roles][bp_role][$in][2]': 'PRO001',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadExistingContacts() {\r\n    this.existingcontacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.existingcontactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.existingcontactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.existingcontactLoading = false)),\r\n            catchError((error) => {\r\n              this.existingcontactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  selectExistingContact() {\r\n    this.addExistingContact(this.FollowUpForm.value);\r\n    this.existingDialogVisible = false; // Close dialog\r\n  }\r\n\r\n  addExistingContact(existing: any) {\r\n    const contactForm = this.formBuilder.group({\r\n      bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\r\n      email_address: [existing?.contactexisting?.email || ''],\r\n      role_code: 'BUP001',\r\n      party_id: existing?.contactexisting?.bp_id || '',\r\n    });\r\n\r\n    const firstGroup = this.involved_parties.at(0) as FormGroup;\r\n    const bpName = firstGroup?.get('bp_full_name')?.value;\r\n\r\n    if (!bpName && this.involved_parties.length === 1) {\r\n      // Replace the default empty group\r\n      this.involved_parties.setControl(0, contactForm);\r\n    } else {\r\n      // Otherwise, add a new contact\r\n      this.involved_parties.push(contactForm);\r\n    }\r\n\r\n    this.existingDialogVisible = false; // Close dialog\r\n  }\r\n\r\n  deleteContact(index: number) {\r\n    if (this.involved_parties.length > 1) {\r\n      this.involved_parties.removeAt(index);\r\n    }\r\n  }\r\n\r\n  createContactFormGroup(): FormGroup {\r\n    return this.formBuilder.group({\r\n      bp_full_name: [''],\r\n      email_address: [''],\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.FollowUpForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.FollowUpForm.value };\r\n\r\n    const data = {\r\n      document_type: value?.document_type,\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      phone_call_category: value?.phone_call_category,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      disposition_code: value?.disposition_code,\r\n      initiator_code: value?.initiator_code,\r\n      owner_party_id: this.owner_id,\r\n      activity_status: value?.activity_status,\r\n      note: value?.notes,\r\n      involved_parties: Array.isArray(value.involved_parties)\r\n        ? [\r\n            ...value.involved_parties,\r\n            ...(value?.main_account_party_id\r\n              ? [{ role_code: 'FLCU01', party_id: value.main_account_party_id }]\r\n              : []),\r\n            ...(Array.isArray(value.main_contact_party_id)\r\n              ? value.main_contact_party_id.map((id: any) => ({\r\n                  role_code: 'BUP001',\r\n                  party_id: id,\r\n                }))\r\n              : []),\r\n            ...(this.owner_id\r\n              ? [{ role_code: 'BUP003', party_id: this.owner_id }]\r\n              : []),\r\n          ]\r\n        : [],\r\n      type_code: '0002',\r\n      opportunity_id: this.opportunity_id,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createOpportuniyFollowup(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.addDialogVisible = false;\r\n          this.saving = false;\r\n          this.visible = false;\r\n          this.FollowUpForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Follow Up Added Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.FollowUpForm.controls;\r\n  }\r\n\r\n  get involved_parties(): any {\r\n    return this.FollowUpForm.get('involved_parties') as FormArray;\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  hideDialog(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-dialog (onHide)=\"hideDialog()\" [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"followup-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Sales Call</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field grid mt-0 text-base\">\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Transaction Type\">\r\n                    <span class=\"material-symbols-rounded\">description</span>Transaction Type\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityDocumentType']\" formControlName=\"document_type\"\r\n                    placeholder=\"Select a Document Type\" optionLabel=\"label\" optionValue=\"value\"\r\n                    styleClass=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['document_type'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['document_type'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['document_type'].errors['required']\">\r\n                        Document Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Subject\">\r\n                    <span class=\"material-symbols-rounded\">subject</span>Subject\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                    class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['subject'].errors['required']\">\r\n                        Subject is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 \">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Account\">\r\n                    <span class=\"material-symbols-rounded\">account_circle</span>Account\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_account_party_id\" [typeahead]=\"accountInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\"\r\n                    [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors['required']\">\r\n                        Account is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Contact\">\r\n                    <span class=\"material-symbols-rounded\">person</span>Contact\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\"\r\n                    [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\" placeholder=\"Select a Contact\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </div>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors['required']\">\r\n                        Contact is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Category\">\r\n                    <span class=\"material-symbols-rounded\">category</span>Category\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"phone_call_category\"\r\n                    placeholder=\"Select a Category\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['phone_call_category'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['phone_call_category'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['phone_call_category'].errors['required']\">\r\n                        Category is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Disposition Code\">\r\n                    <span class=\"material-symbols-rounded\">code</span>Disposition Code\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activitydisposition']\" formControlName=\"disposition_code\"\r\n                    placeholder=\"Select a Disposition Code\" optionLabel=\"label\" optionValue=\"value\"\r\n                    styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"start_date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>Call Date/Time\r\n                </label>\r\n                <p-calendar formControlName=\"start_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"Call Date/Time\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"end_date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>End Date/Time\r\n                </label>\r\n                <p-calendar formControlName=\"end_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"End Date/Time\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Type\">\r\n                    <span class=\"material-symbols-rounded\">label</span>Type\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityInitiatorCode']\" formControlName=\"initiator_code\"\r\n                    placeholder=\"Select a Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['initiator_code'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['initiator_code'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['initiator_code'].errors['required']\">\r\n                        Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Status\">\r\n                    <span class=\"material-symbols-rounded\">check_circle</span>Status\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                    placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['activity_status'].errors['required']\">\r\n                        Status is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Notes\">\r\n                    <span class=\"material-symbols-rounded\">notes</span>Notes\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-editor formControlName=\"notes\" placeholder=\"Enter your note here...\" [style]=\"{ height: '125px'}\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['notes'].errors }\" styleClass=\"w-full\" />\r\n                <div *ngIf=\"submitted && f['notes'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['notes'].errors &&\r\n                                    f['notes'].errors['required']\r\n                                  \">\r\n                        Notes is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n            <div class=\"flex justify-content-between align-items-center mb-3\">\r\n                <h4 class=\"m-0 flex align-items-center h-3rem\">Additional Contacts Persons</h4>\r\n\r\n                <div class=\"flex align-items-center gap-3\">\r\n                    <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\"\r\n                        iconPos=\"right\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\"></p-button>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"table-sec\">\r\n                <p-table #dt [value]=\"involved_parties?.controls\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n                    [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"TableColumnReorder($event)\"\r\n                    responsiveLayout=\"scroll\" class=\"scrollable-table followup-add-table\">\r\n\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th class=\"border-round-left-lg\">Name</th>\r\n                            <ng-container *ngFor=\"let col of selectedColumns\">\r\n                                <th pReorderableColumn>\r\n                                    <div class=\"flex align-items-center cursor-pointer\">\r\n                                        {{ col.header }}\r\n                                    </div>\r\n                                </th>\r\n                            </ng-container>\r\n                            <th class=\"border-round-right-lg\">Actions</th>\r\n                        </tr>\r\n                    </ng-template>\r\n\r\n                    <ng-template pTemplate=\"body\" let-contact let-columns=\"columns\" let-i=\"rowIndex\">\r\n                        <tr [formGroup]=\"contact\">\r\n                            <td class=\"border-round-left-lg\">\r\n                                <input pInputText type=\"text\" class=\"h-3rem w-full\" formControlName=\"bp_full_name\"\r\n                                    placeholder=\"Enter a Name\" readonly />\r\n                            </td>\r\n                            <ng-container *ngFor=\"let col of selectedColumns\">\r\n                                <td>\r\n                                    <ng-container [ngSwitch]=\"col.field\">\r\n                                        <ng-container *ngSwitchCase=\"'email'\">\r\n                                            <input pInputText type=\"email\" class=\"h-3rem w-full\"\r\n                                                formControlName=\"email_address\" placeholder=\"Enter Email\" readonly />\r\n                                        </ng-container>\r\n                                    </ng-container>\r\n                                </td>\r\n                            </ng-container>\r\n\r\n                            <td class=\"border-round-right-lg\">\r\n                                <button pButton pRipple type=\"button\" icon=\"pi pi-trash\"\r\n                                    class=\"p-button-rounded p-button-danger\" (click)=\"deleteContact(i)\" title=\"Delete\"\r\n                                    *ngIf=\"involved_parties.length > 1\"></button>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n        </div>\r\n        <p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n            [draggable]=\"false\" class=\"prospect-popup\">\r\n            <ng-template pTemplate=\"header\">\r\n                <h4>Contact Information</h4>\r\n            </ng-template>\r\n\r\n            <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n                <div class=\"field flex align-items-center text-base\">\r\n                    <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                        <span class=\"material-symbols-rounded\">person</span>Contacts\r\n                    </label>\r\n                    <div class=\"form-input flex-1 relative\">\r\n                        <ng-select pInputText [items]=\"existingcontacts$ | async\" bindLabel=\"bp_full_name\"\r\n                            [hideSelected]=\"true\" [loading]=\"existingcontactLoading\" [minTermLength]=\"0\"\r\n                            formControlName=\"contactexisting\" [typeahead]=\"existingcontactInput$\"\r\n                            [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                            [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\" placeholder=\"Search for a contact\">\r\n                            <ng-template ng-option-tmp let-item=\"item\">\r\n                                <span>{{ item.bp_id }}</span>\r\n                                <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </ng-template>\r\n                        </ng-select>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-end gap-2 mt-3\">\r\n                    <button pButton type=\"button\"\r\n                        class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center font-medium w-9rem h-3rem\"\r\n                        (click)=\"existingDialogVisible = false\">\r\n                        Cancel\r\n                    </button>\r\n                    <button pButton type=\"button\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                        (click)=\"selectExistingContact()\">\r\n                        Save\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </p-dialog>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAItE,SAAiCC,UAAU,QAAmB,gBAAgB;AAC9E,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICXfC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAePH,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,mCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAI,UAAA,IAAAC,sDAAA,kBAAgE;IAGpEL,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,kBAAAC,MAAA,aAAwD;;;;;IAa9DX,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAI,UAAA,IAAAQ,sDAAA,kBAA0D;IAG9DZ,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,YAAAC,MAAA,aAAkD;;;;;IAiBpDX,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAa,kBAAA,QAAAC,OAAA,CAAAC,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAI,UAAA,IAAAY,+DAAA,mBAAgC;;;;IAD1BhB,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAiB,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACflB,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAO,OAAA,CAAAC,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAe,sDAAA,kBAAwE;IAG5EnB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAmB9DX,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAE,MAAA,KAAmB;;;;;IAF9CtB,EADJ,CAAAC,cAAA,cAA2C,WACjC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAI,UAAA,IAAAmB,+DAAA,mBAAyB,IAAAC,+DAAA,mBACC;IAC9BxB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyB,kBAAA,KAAAL,OAAA,CAAAF,KAAA,QAAAE,OAAA,CAAAL,YAAA,KAAyC;IACxCf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAC,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhCtB,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAsB,sDAAA,kBAAwE;IAG5E1B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAetEX,EAAA,CAAAC,cAAA,UAAsE;IAClED,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAI,UAAA,IAAAuB,sDAAA,kBAAsE;IAG1E3B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,wBAAAC,MAAA,aAA8D;;;;;IAuCpEX,EAAA,CAAAC,cAAA,UAAiE;IAC7DD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAI,UAAA,IAAAwB,sDAAA,kBAAiE;IAGrE5B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAyD;IAAzDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,mBAAAC,MAAA,aAAyD;;;;;IAe/DX,EAAA,CAAAC,cAAA,UAAkE;IAC9DD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAI,UAAA,IAAAyB,sDAAA,kBAAkE;IAGtE7B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,oBAAAC,MAAA,aAA0D;;;;;IAahEX,EAAA,CAAAC,cAAA,UAIgB;IACZD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAI,UAAA,IAAA0B,sDAAA,kBAIgB;IAGpB9B,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIO;IAJPN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,UAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,UAAAC,MAAA,aAIO;;;;;IAwBLX,EAAA,CAAA+B,uBAAA,GAAkD;IAE1C/B,EADJ,CAAAC,cAAA,aAAuB,cACiC;IAChDD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACL;;;;;IAFGH,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAa,kBAAA,MAAAmB,MAAA,CAAAC,MAAA,MACJ;;;;;IALRjC,EADJ,CAAAC,cAAA,SAAI,aACiC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAI,UAAA,IAAA8B,wEAAA,2BAAkD;IAOlDlC,EAAA,CAAAC,cAAA,aAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAC7CF,EAD6C,CAAAG,YAAA,EAAK,EAC7C;;;;IAR6BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAA2B,eAAA,CAAkB;;;;;IAoBpCnC,EAAA,CAAA+B,uBAAA,GAAsC;IAClC/B,EAAA,CAAAoC,SAAA,gBACyE;;;;;;IALzFpC,EAAA,CAAA+B,uBAAA,GAAkD;IAC9C/B,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA+B,uBAAA,OAAqC;IACjC/B,EAAA,CAAAI,UAAA,IAAAiC,uFAAA,2BAAsC;;IAK9CrC,EAAA,CAAAG,YAAA,EAAK;;;;;IANaH,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,UAAA,aAAA+B,MAAA,CAAAC,KAAA,CAAsB;IACjBvC,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,yBAAqB;;;;;;IAS5CP,EAAA,CAAAC,cAAA,iBAEwC;IADKD,EAAA,CAAAwC,UAAA,mBAAAC,2FAAA;MAAAzC,EAAA,CAAA0C,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAA5C,EAAA,CAAA6C,aAAA,GAAAC,QAAA;MAAA,MAAAtC,MAAA,GAAAR,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA+C,WAAA,CAASvC,MAAA,CAAAwC,aAAA,CAAAJ,IAAA,CAAgB;IAAA,EAAC;IAC/B5C,EAAA,CAAAG,YAAA,EAAS;;;;;IAlBrDH,EADJ,CAAAC,cAAA,aAA0B,aACW;IAC7BD,EAAA,CAAAoC,SAAA,gBAC0C;IAC9CpC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAI,UAAA,IAAA6C,wEAAA,2BAAkD;IAWlDjD,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAI,UAAA,IAAA8C,kEAAA,qBAEwC;IAEhDlD,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;IArBDH,EAAA,CAAAO,UAAA,cAAA4C,UAAA,CAAqB;IAKSnD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAA2B,eAAA,CAAkB;IAcvCnC,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA4C,gBAAA,CAAAC,MAAA,KAAiC;;;;;IAUtDrD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAgBZH,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAa,kBAAA,QAAAyC,QAAA,CAAAvC,YAAA,KAAyB;;;;;IAC1Df,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAyC,QAAA,CAAAjC,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAyC,QAAA,CAAAhC,MAAA,KAAmB;;;;;IAH9CtB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAFA,CAAAI,UAAA,IAAAmD,gEAAA,mBAAgC,IAAAC,gEAAA,mBACP,IAAAC,gEAAA,mBACC;;;;IAHpBzD,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAiB,iBAAA,CAAAqC,QAAA,CAAApC,KAAA,CAAgB;IACflB,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAA+C,QAAA,CAAAvC,YAAA,CAAuB;IACvBf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAA+C,QAAA,CAAAjC,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAA+C,QAAA,CAAAhC,MAAA,CAAiB;;;ADzNxD,OAAM,MAAOoC,gCAAgC;EA8C3CC,YACUC,WAAwB,EACxBC,KAAqB,EACrBC,iBAAoC,EACpCC,oBAA0C,EAC1CC,cAA8B;IAJ9B,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IAlDhB,KAAAC,YAAY,GAAG,IAAI9E,OAAO,EAAQ;IACjC,KAAA+E,OAAO,GAAY,KAAK;IACvB,KAAAC,OAAO,GAAG,IAAIjF,YAAY,EAAQ;IACrC,KAAAkF,cAAc,GAAW,EAAE;IAC3B,KAAA3D,SAAS,GAAG,KAAK;IACjB,KAAA4D,MAAM,GAAG,KAAK;IACd,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACrC,KAAAC,cAAc,GAAQ,EAAE;IAEzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIxF,OAAO,EAAU;IAErC,KAAAyF,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI1F,OAAO,EAAU;IAErC,KAAA2F,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,qBAAqB,GAAG,IAAI5F,OAAO,EAAU;IAC5C,KAAA6F,QAAQ,GAAkB,IAAI;IAE/B,KAAAC,YAAY,GAAc,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAAC;MACtDC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MAC1CC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MACpCE,qBAAqB,EAAE,CAAC,IAAI,EAAE,CAAC9F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MACpDG,qBAAqB,EAAE,CAAC,IAAI,EAAE,CAAC/F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MACpDI,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAChG,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MAChDK,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC,EAAE,EAAE,CAACpG,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MAC3CS,eAAe,EAAE,CAAC,EAAE,EAAE,CAACrG,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MAC5CU,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MAClCW,eAAe,EAAE,CAAC,IAAI,CAAC;MACvB3C,gBAAgB,EAAE,IAAI,CAACQ,WAAW,CAACoC,KAAK,CAAC,CAAC,IAAI,CAACC,sBAAsB,EAAE,CAAC;KACzE,CAAC;IAEK,KAAAC,SAAS,GAA0B;MACxCC,oBAAoB,EAAE,EAAE;MACxBC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE;KACxB;IAUO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CAAC;MAAElE,KAAK,EAAE,OAAO;MAAEN,MAAM,EAAE;IAAe,CAAE,CAAC;IAErE,KAAAyE,SAAS,GAAW,EAAE;IACtB,KAAAC,SAAS,GAAW,CAAC;EAPlB;EASHC,QAAQA,CAAA;IACN,IAAI,CAACxC,cAAc,GAAG,IAAI,CAACP,KAAK,CAACgD,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1EC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;MACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;MACD,IAAI,CAACA,oBAAoB,CACvB,qBAAqB,EACrB,+BAA+B,CAChC;MACD,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;MAClE,IAAI,CAACA,oBAAoB,CACvB,uBAAuB,EACvB,6BAA6B,CAC9B;IACH,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAACjC,YAAY,CAAC+B,GAAG,CAAC,uBAAuB,CAAC,EAC1CG,YAAY,CAACC,IAAI,CACjBhI,SAAS,CAAC,IAAI,CAAC6E,YAAY,CAAC,EAC5BtE,GAAG,CAAE0H,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACE,SAAS,GAAGhI,EAAE,CAAC,IAAI,CAACkF,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACF5E,UAAU,CAAE2H,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACD,SAAS,GAAGhI,EAAE,CAAC,IAAI,CAACkF,cAAc,CAAC;MACxC,OAAOlF,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACAoI,SAAS,EAAE;IACd,IAAI,CAACC,QAAQ,EAAE,CAACD,SAAS,CAAC;MACxBE,IAAI,EAAGC,QAAuB,IAAI;QAChC,IAAI,CAAC9C,QAAQ,GAAG8C,QAAQ;MAC1B,CAAC;MACDJ,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C;KACD,CAAC;IACF,IAAI,CAACO,YAAY,EAAE;IACnB,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,CAACxB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAItE,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACqE,gBAAgB;EAC9B;EAEA,IAAIrE,eAAeA,CAAC8F,GAAU;IAC5B,IAAI,CAACzB,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACyB,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EACAE,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,UAAU,GAAG,IAAI,CAAC9B,IAAI,CAAC6B,KAAK,CAACE,SAAS,CAAC;IAC7C,IAAI,CAAC/B,IAAI,CAACgC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACpC,IAAI,CAAC/B,IAAI,CAACgC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAClD;EAEQX,QAAQA,CAAA;IACd,OAAO,IAAI,CAAC9D,iBAAiB,CAAC6E,mBAAmB,EAAE;EACrD;EAEAzB,oBAAoBA,CAAC0B,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAC/E,iBAAiB,CACnBgF,0BAA0B,CAACD,IAAI,CAAC,CAChClB,SAAS,CAAEoB,GAAQ,IAAI;MACtB,MAAMC,OAAO,GACXD,GAAG,EAAEE,IAAI,EAAE3J,GAAG,CACX4J,IAA2C,KAAsB;QAChEC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CACH,IAAI,EAAE;MAET;MACA,IAAI,CAACpD,SAAS,CAAC0C,MAAM,CAAC,GAAGI,OAAO;MAEhC;MACA,IAAIJ,MAAM,KAAK,gBAAgB,EAAE;QAC/B,MAAMW,UAAU,GAAGP,OAAO,CAACQ,IAAI,CAC5BC,GAAG,IAAKA,GAAG,CAACN,KAAK,CAACO,WAAW,EAAE,KAAK,MAAM,CAC5C;QAED,IAAIH,UAAU,EAAE;UACd,MAAMI,OAAO,GAAG,IAAI,CAAC1E,YAAY,EAAE+B,GAAG,CAAC,iBAAiB,CAAC;UACzD,IAAI2C,OAAO,EAAE;YACXA,OAAO,CAACC,QAAQ,CAACL,UAAU,CAACF,KAAK,CAAC;UACpC;QACF;MACF;IACF,CAAC,CAAC;EACN;EAEQtB,YAAYA,CAAA;IAClB,IAAI,CAAC8B,SAAS,GAAGxK,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACkF,cAAc,CAAC;IAAE;IACzB,IAAI,CAACE,aAAa,CAACyC,IAAI,CACrBtH,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC+E,cAAc,GAAG,IAAK,CAAC,EACvChF,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAChG,iBAAiB,CAACkG,WAAW,CAACD,MAAM,CAAC,CAAC3C,IAAI,CACpD9H,GAAG,CAAEwI,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxCjI,UAAU,CAAE6H,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAOnI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFQ,QAAQ,CAAC,MAAO,IAAI,CAAC2E,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQsD,oBAAoBA,CAAA;IAC1B,IAAI,CAACiC,iBAAiB,GAAG5K,MAAM,CAC7BE,EAAE,CAAC,IAAI,CAACkF,cAAc,CAAC;IAAE;IACzB,IAAI,CAACM,qBAAqB,CAACqC,IAAI,CAC7B3H,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACmF,sBAAsB,GAAG,IAAK,CAAC,EAC/CpF,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAChG,iBAAiB,CAACkG,WAAW,CAACD,MAAM,CAAC,CAAC3C,IAAI,CACpD9H,GAAG,CAAE2J,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFtJ,GAAG,CAAC,MAAO,IAAI,CAACmF,sBAAsB,GAAG,KAAM,CAAC,EAChDjF,UAAU,CAAE6H,KAAK,IAAI;QACnB,IAAI,CAAC5C,sBAAsB,GAAG,KAAK;QACnC,OAAOvF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQ+H,qBAAqBA,CAAC4C,IAAY;IACxC,IAAI,CAAC3C,SAAS,GAAG,IAAI,CAAC1C,aAAa,CAACuC,IAAI,CACtCxH,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACiF,cAAc,GAAG,IAAK,CAAC,EACvClF,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEG,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIJ,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAChG,iBAAiB,CAACqG,kBAAkB,CAACJ,MAAM,CAAC,CAAC3C,IAAI,CAC3D9H,GAAG,CAAEwI,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxCnI,GAAG,CAAEyK,QAAe,IAAI;QACtB,IAAI,CAACxF,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACF/E,UAAU,CAAE6H,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC9C,cAAc,GAAG,KAAK;QAC3B,OAAOrF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA8K,qBAAqBA,CAAA;IACnB,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACrF,YAAY,CAACoE,KAAK,CAAC;IAChD,IAAI,CAAC7E,qBAAqB,GAAG,KAAK,CAAC,CAAC;EACtC;EAEA8F,kBAAkBA,CAACC,QAAa;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAAC5G,WAAW,CAACsB,KAAK,CAAC;MACzCnE,YAAY,EAAE,CAACwJ,QAAQ,EAAExE,eAAe,EAAEhF,YAAY,IAAI,EAAE,CAAC;MAC7D0J,aAAa,EAAE,CAACF,QAAQ,EAAExE,eAAe,EAAE1E,KAAK,IAAI,EAAE,CAAC;MACvDqJ,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAEJ,QAAQ,EAAExE,eAAe,EAAE7E,KAAK,IAAI;KAC/C,CAAC;IAEF,MAAM0J,UAAU,GAAG,IAAI,CAACxH,gBAAgB,CAACyH,EAAE,CAAC,CAAC,CAAc;IAC3D,MAAMC,MAAM,GAAGF,UAAU,EAAE5D,GAAG,CAAC,cAAc,CAAC,EAAEqC,KAAK;IAErD,IAAI,CAACyB,MAAM,IAAI,IAAI,CAAC1H,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE;MACjD;MACA,IAAI,CAACD,gBAAgB,CAAC2H,UAAU,CAAC,CAAC,EAAEP,WAAW,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAACpH,gBAAgB,CAAC4H,IAAI,CAACR,WAAW,CAAC;IACzC;IAEA,IAAI,CAAChG,qBAAqB,GAAG,KAAK,CAAC,CAAC;EACtC;EAEAxB,aAAaA,CAACiI,KAAa;IACzB,IAAI,IAAI,CAAC7H,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACpC,IAAI,CAACD,gBAAgB,CAAC8H,QAAQ,CAACD,KAAK,CAAC;IACvC;EACF;EAEAhF,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACrC,WAAW,CAACsB,KAAK,CAAC;MAC5BnE,YAAY,EAAE,CAAC,EAAE,CAAC;MAClB0J,aAAa,EAAE,CAAC,EAAE;KACnB,CAAC;EACJ;EAEMU,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC3K,SAAS,GAAG,IAAI;MAErB,IAAI2K,KAAI,CAACnG,YAAY,CAACqG,OAAO,EAAE;QAC7B;MACF;MAEAF,KAAI,CAAC/G,MAAM,GAAG,IAAI;MAClB,MAAMgF,KAAK,GAAG;QAAE,GAAG+B,KAAI,CAACnG,YAAY,CAACoE;MAAK,CAAE;MAE5C,MAAMJ,IAAI,GAAG;QACX9D,aAAa,EAAEkE,KAAK,EAAElE,aAAa;QACnCE,OAAO,EAAEgE,KAAK,EAAEhE,OAAO;QACvBC,qBAAqB,EAAE+D,KAAK,EAAE/D,qBAAqB;QACnDC,qBAAqB,EAAE8D,KAAK,EAAE9D,qBAAqB;QACnDC,mBAAmB,EAAE6D,KAAK,EAAE7D,mBAAmB;QAC/CE,UAAU,EAAE2D,KAAK,EAAE3D,UAAU,GAAG0F,KAAI,CAACG,UAAU,CAAClC,KAAK,CAAC3D,UAAU,CAAC,GAAG,IAAI;QACxEC,QAAQ,EAAE0D,KAAK,EAAE1D,QAAQ,GAAGyF,KAAI,CAACG,UAAU,CAAClC,KAAK,CAAC1D,QAAQ,CAAC,GAAG,IAAI;QAClEF,gBAAgB,EAAE4D,KAAK,EAAE5D,gBAAgB;QACzCG,cAAc,EAAEyD,KAAK,EAAEzD,cAAc;QACrC4F,cAAc,EAAEJ,KAAI,CAACpG,QAAQ;QAC7Ba,eAAe,EAAEwD,KAAK,EAAExD,eAAe;QACvC4F,IAAI,EAAEpC,KAAK,EAAEvD,KAAK;QAClB1C,gBAAgB,EAAEsI,KAAK,CAACC,OAAO,CAACtC,KAAK,CAACjG,gBAAgB,CAAC,GACnD,CACE,GAAGiG,KAAK,CAACjG,gBAAgB,EACzB,IAAIiG,KAAK,EAAE/D,qBAAqB,GAC5B,CAAC;UAAEoF,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAEtB,KAAK,CAAC/D;QAAqB,CAAE,CAAC,GAChE,EAAE,CAAC,EACP,IAAIoG,KAAK,CAACC,OAAO,CAACtC,KAAK,CAAC9D,qBAAqB,CAAC,GAC1C8D,KAAK,CAAC9D,qBAAqB,CAACjG,GAAG,CAAEsM,EAAO,KAAM;UAC5ClB,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAEiB;SACX,CAAC,CAAC,GACH,EAAE,CAAC,EACP,IAAIR,KAAI,CAACpG,QAAQ,GACb,CAAC;UAAE0F,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAES,KAAI,CAACpG;QAAQ,CAAE,CAAC,GAClD,EAAE,CAAC,CACR,GACD,EAAE;QACN6G,SAAS,EAAE,MAAM;QACjBzH,cAAc,EAAEgH,KAAI,CAAChH;OACtB;MAEDgH,KAAI,CAACtH,iBAAiB,CACnBgI,wBAAwB,CAAC7C,IAAI,CAAC,CAC9B7B,IAAI,CAAChI,SAAS,CAACgM,KAAI,CAACnH,YAAY,CAAC,CAAC,CAClC0D,SAAS,CAAC;QACTE,IAAI,EAAEA,CAAA,KAAK;UACTuD,KAAI,CAAC7G,gBAAgB,GAAG,KAAK;UAC7B6G,KAAI,CAAC/G,MAAM,GAAG,KAAK;UACnB+G,KAAI,CAAClH,OAAO,GAAG,KAAK;UACpBkH,KAAI,CAACnG,YAAY,CAAC8G,KAAK,EAAE;UACzBX,KAAI,CAACpH,cAAc,CAACgI,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFd,KAAI,CAACrH,oBAAoB,CACtBoI,kBAAkB,CAACf,KAAI,CAAChH,cAAc,CAAC,CACvCgD,IAAI,CAAChI,SAAS,CAACgM,KAAI,CAACnH,YAAY,CAAC,CAAC,CAClC0D,SAAS,EAAE;QAChB,CAAC;QACDD,KAAK,EAAEA,CAAA,KAAK;UACV0D,KAAI,CAAC/G,MAAM,GAAG,KAAK;UACnB+G,KAAI,CAACpH,cAAc,CAACgI,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAX,UAAUA,CAACa,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAIjM,CAACA,CAAA;IACH,OAAO,IAAI,CAACuE,YAAY,CAAC4H,QAAQ;EACnC;EAEA,IAAIzJ,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC6B,YAAY,CAAC+B,GAAG,CAAC,kBAAkB,CAAc;EAC/D;EAEA8F,kBAAkBA,CAACxI,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,qBAAqB,GAAG,IAAI;EACnC;EAEAuI,UAAUA,CAAA;IACR,IAAI,CAAC5I,OAAO,CAAC6I,IAAI,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChJ,YAAY,CAAC4D,IAAI,EAAE;IACxB,IAAI,CAAC5D,YAAY,CAACiJ,QAAQ,EAAE;EAC9B;;;uBAzZWxJ,gCAAgC,EAAA1D,EAAA,CAAAmN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAAmN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAAmN,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAzN,EAAA,CAAAmN,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAA3N,EAAA,CAAAmN,iBAAA,CAAAS,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhCnK,gCAAgC;MAAAoK,SAAA;MAAAC,MAAA;QAAA7J,OAAA;MAAA;MAAA8J,OAAA;QAAA7J,OAAA;MAAA;MAAA8J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC7B7CtO,EAAA,CAAAC,cAAA,kBAC+C;UADrCD,EAAA,CAAAwC,UAAA,oBAAAgM,qEAAA;YAAAxO,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAAUwL,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAAgB/M,EAAA,CAAA0O,gBAAA,2BAAAC,4EAAAC,MAAA;YAAA5O,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAAzO,EAAA,CAAA6O,kBAAA,CAAAN,GAAA,CAAArK,OAAA,EAAA0K,MAAA,MAAAL,GAAA,CAAArK,OAAA,GAAA0K,MAAA;YAAA,OAAA5O,EAAA,CAAA+C,WAAA,CAAA6L,MAAA;UAAA,EAAqB;UAElE5O,EAAA,CAAAI,UAAA,IAAA0O,uDAAA,yBAAgC;UAQhB9O,EAJhB,CAAAC,cAAA,cAAyE,aAC9B,aACa,eAC2C,cAC5C;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,wBACzD;UAAAF,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,qBAGa;UACbpC,EAAA,CAAAI,UAAA,KAAA2O,gDAAA,kBAAoE;UAKxE/O,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAgD,iBACkC,eACnC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBACrD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,iBAC2F;UAC3FpC,EAAA,CAAAI,UAAA,KAAA4O,gDAAA,kBAA8D;UAKlEhP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAiD,iBACiC,eACnC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBAC5D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAIuE;;UACnED,EAAA,CAAAI,UAAA,KAAA6O,wDAAA,0BAA2C;UAI/CjP,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAA8O,gDAAA,kBAA4E;UAKhFlP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC6B,eACnC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBACpD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAKsG;;UAClGD,EAAA,CAAAI,UAAA,KAAA+O,wDAAA,0BAA2C;UAO/CnP,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAAgP,gDAAA,kBAA4E;UAKhFpP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eACpC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,iBACtD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,sBAGa;UACbpC,EAAA,CAAAI,UAAA,KAAAiP,gDAAA,kBAA0E;UAK9ErP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACsC,eAC5C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,yBAClD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,sBAGa;UACjBpC,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgC,eACtC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,uBAC1D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAoC,SAAA,sBACgF;UACpFpC,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eACpC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,sBAC1D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAoC,SAAA,sBAC+E;UACnFpC,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC0B,eAChC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,aACnD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,sBAGa;UACbpC,EAAA,CAAAI,UAAA,KAAAkP,gDAAA,kBAAqE;UAKzEtP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4B,eAClC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,eAC1D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,sBAGa;UACbpC,EAAA,CAAAI,UAAA,KAAAmP,gDAAA,kBAAsE;UAK1EvP,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAyB,iBACuD,eACjC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,cACnD;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAoC,SAAA,oBACuF;UACvFpC,EAAA,CAAAI,UAAA,KAAAoP,gDAAA,kBAA4D;UAUpExP,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAoF,gBACd,eACf;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3EH,EADJ,CAAAC,cAAA,gBAA2C,qBAEkC;UADtCD,EAAA,CAAAwC,UAAA,mBAAAiN,sEAAA;YAAAzP,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAASwL,GAAA,CAAAzB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAGhF9M,EAFiF,CAAAG,YAAA,EAAW,EAClF,EACJ;UAGFH,EADJ,CAAAC,cAAA,gBAAuB,uBAGuD;UADtBD,EAAA,CAAAwC,UAAA,0BAAAkN,4EAAAd,MAAA;YAAA5O,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAAgBwL,GAAA,CAAAlG,kBAAA,CAAAuG,MAAA,CAA0B;UAAA,EAAC;UAiB3F5O,EAdA,CAAAI,UAAA,MAAAuP,yDAAA,yBAAgC,MAAAC,yDAAA,0BAciD;UA0B7F5P,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,qBAC+C;UADtBD,EAAA,CAAA0O,gBAAA,2BAAAmB,8EAAAjB,MAAA;YAAA5O,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAAzO,EAAA,CAAA6O,kBAAA,CAAAN,GAAA,CAAA/J,qBAAA,EAAAoK,MAAA,MAAAL,GAAA,CAAA/J,qBAAA,GAAAoK,MAAA;YAAA,OAAA5O,EAAA,CAAA+C,WAAA,CAAA6L,MAAA;UAAA,EAAmC;UAExD5O,EAAA,CAAAI,UAAA,MAAA0P,yDAAA,yBAAgC;UAOpB9P,EAHZ,CAAAC,cAAA,gBAAyE,gBAChB,kBAC+C,gBACrD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,kBACxD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEJH,EADJ,CAAAC,cAAA,gBAAwC,sBAKsE;;UACtGD,EAAA,CAAAI,UAAA,MAAA2P,yDAAA,0BAA2C;UAQvD/P,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;UAEFH,EADJ,CAAAC,cAAA,gBAAiD,mBAGD;UAAxCD,EAAA,CAAAwC,UAAA,mBAAAwN,oEAAA;YAAAhQ,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAAAwL,GAAA,CAAA/J,qBAAA,GAAiC,KAAK;UAAA,EAAC;UACvCxE,EAAA,CAAAE,MAAA,iBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBACsC;UAAlCD,EAAA,CAAAwC,UAAA,mBAAAyN,oEAAA;YAAAjQ,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAASwL,GAAA,CAAAlE,qBAAA,EAAuB;UAAA,EAAC;UACjCrK,EAAA,CAAAE,MAAA,eACJ;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACH,EACA;UAEPH,EADJ,CAAAC,cAAA,gBAAgD,mBAGd;UAA1BD,EAAA,CAAAwC,UAAA,mBAAA0N,oEAAA;YAAAlQ,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAAAwL,GAAA,CAAArK,OAAA,GAAmB,KAAK;UAAA,EAAC;UAAClE,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAwC,UAAA,mBAAA2N,oEAAA;YAAAnQ,EAAA,CAAA0C,aAAA,CAAA+L,GAAA;YAAA,OAAAzO,EAAA,CAAA+C,WAAA,CAASwL,GAAA,CAAApD,QAAA,EAAU;UAAA,EAAC;UAIpCnL,EAJqC,CAAAG,YAAA,EAAS,EAChC,EACH,EAEA;;;UAjR4DH,EAAA,CAAAoQ,UAAA,CAAApQ,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAA4B;UAAjEtQ,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAAuQ,gBAAA,YAAAhC,GAAA,CAAArK,OAAA,CAAqB;UAClElE,EADgG,CAAAO,UAAA,qBAAoB,oBACjG;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAgO,GAAA,CAAAtJ,YAAA,CAA0B;UAORjF,EAAA,CAAAM,SAAA,GAA6C;UAE1BN,EAFnB,CAAAO,UAAA,YAAAgO,GAAA,CAAArI,SAAA,yBAA6C,YAAAlG,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,kBAAAC,MAAA,EAE0C;UAE7FX,EAAA,CAAAM,SAAA,EAA4C;UAA5CN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,kBAAAC,MAAA,CAA4C;UAYxBX,EAAA,CAAAM,SAAA,GAA8D;UAA9DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,YAAAC,MAAA,EAA8D;UAClFX,EAAA,CAAAM,SAAA,EAAsC;UAAtCN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,YAAAC,MAAA,CAAsC;UAexCX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAA0Q,UAAA,0DAAkE;UADlD1Q,EAHE,CAAAO,UAAA,UAAAP,EAAA,CAAA2Q,WAAA,SAAApC,GAAA,CAAA1E,SAAA,EAA2B,sBACxB,YAAA0E,GAAA,CAAA7J,cAAA,CAA2B,oBAAoB,cAAA6J,GAAA,CAAA5J,aAAA,CACD,wBAAwB,YAAA3E,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,0BAAAC,MAAA,EACC;UAO1FX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,0BAAAC,MAAA,CAAoD;UAgBtDX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAA0Q,UAAA,0DAAkE;UADlE1Q,EAJkB,CAAAO,UAAA,UAAAP,EAAA,CAAA2Q,WAAA,SAAApC,GAAA,CAAAhH,SAAA,EAA2B,sBACxB,YAAAgH,GAAA,CAAA3J,cAAA,CAA2B,oBAAoB,cAAA2J,GAAA,CAAA1J,aAAA,CACD,wBAAwB,wBACpD,YAAA7E,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,0BAAAC,MAAA,EACqC;UAU1EX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,0BAAAC,MAAA,CAAoD;UAW9CX,EAAA,CAAAM,SAAA,GAAyC;UAEjDN,EAFQ,CAAAO,UAAA,YAAAgO,GAAA,CAAArI,SAAA,qBAAyC,YAAAlG,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,wBAAAC,MAAA,EAEyB;UAExEX,EAAA,CAAAM,SAAA,EAAkD;UAAlDN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,wBAAAC,MAAA,CAAkD;UAW5CX,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAO,UAAA,YAAAgO,GAAA,CAAArI,SAAA,wBAA4C;UASQlG,EAAA,CAAAM,SAAA,GAAiB;UAC7EN,EAD4D,CAAAO,UAAA,kBAAiB,kBAC5D;UAMyCP,EAAA,CAAAM,SAAA,GAAiB;UAC3EN,EAD0D,CAAAO,UAAA,kBAAiB,kBAC1D;UAOTP,EAAA,CAAAM,SAAA,GAA8C;UAEtDN,EAFQ,CAAAO,UAAA,YAAAgO,GAAA,CAAArI,SAAA,0BAA8C,YAAAlG,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,mBAAAC,MAAA,EAEe;UAEnEX,EAAA,CAAAM,SAAA,EAA6C;UAA7CN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,mBAAAC,MAAA,CAA6C;UAWvCX,EAAA,CAAAM,SAAA,GAAuC;UAE/CN,EAFQ,CAAAO,UAAA,YAAAgO,GAAA,CAAArI,SAAA,mBAAuC,YAAAlG,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,oBAAAC,MAAA,EAEuB;UAEpEX,EAAA,CAAAM,SAAA,EAA8C;UAA9CN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,oBAAAC,MAAA,CAA8C;UAWoBX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAoQ,UAAA,CAAApQ,EAAA,CAAAqQ,eAAA,KAAAO,GAAA,EAA4B;UAChG5Q,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAwQ,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,UAAAC,MAAA,EAA4D;UAC1DX,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9N,SAAA,IAAA8N,GAAA,CAAA7N,CAAA,UAAAC,MAAA,CAAoC;UAiBlBX,EAAA,CAAAM,SAAA,GAAmC;UAACN,EAApC,CAAAO,UAAA,oCAAmC,iBAAiB;UAK/DP,EAAA,CAAAM,SAAA,GAAoC;UACzBN,EADX,CAAAO,UAAA,UAAAgO,GAAA,CAAAnL,gBAAA,kBAAAmL,GAAA,CAAAnL,gBAAA,CAAAyJ,QAAA,CAAoC,YAAY,mBAAuC,oBAC7E,4BAA4B;UA4CE7M,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAoQ,UAAA,CAAApQ,EAAA,CAAAqQ,eAAA,KAAAQ,GAAA,EAA4B;UAA/E7Q,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAAuQ,gBAAA,YAAAhC,GAAA,CAAA/J,qBAAA,CAAmC;UACxDxE,EADsF,CAAAO,UAAA,qBAAoB,oBACvF;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAgO,GAAA,CAAAtJ,YAAA,CAA0B;UAUhBjF,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAA0Q,UAAA,0DAAkE;UADlE1Q,EAHkB,CAAAO,UAAA,UAAAP,EAAA,CAAA2Q,WAAA,UAAApC,GAAA,CAAAtE,iBAAA,EAAmC,sBAChC,YAAAsE,GAAA,CAAAzJ,sBAAA,CAAmC,oBAAoB,cAAAyJ,GAAA,CAAAxJ,qBAAA,CACP,wBAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}