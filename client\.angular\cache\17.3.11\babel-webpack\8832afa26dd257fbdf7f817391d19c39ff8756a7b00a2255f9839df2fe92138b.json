{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ActivitiesService {\n  constructor(http) {\n    this.http = http;\n    this.activitySubject = new BehaviorSubject(null);\n    this.activity = this.activitySubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  getActivities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,start_date,end_date,category');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getSalesCall(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,createdAt,category');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getActivityByID(activityId) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', activityId).set('populate[notes][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    }).pipe(map(response => {\n      const activityDetails = response?.data[0] || null;\n      this.activitySubject.next(activityDetails);\n      return response;\n    }));\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  static {\n    this.ɵfac = function ActivitiesService_Factory(t) {\n      return new (t || ActivitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActivitiesService,\n      factory: ActivitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ActivitiesService", "constructor", "http", "activitySubject", "activity", "asObservable", "createNote", "data", "post", "CRM_NOTE", "updateNote", "Id", "put", "deleteNote", "id", "delete", "getActivities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "CRM_ACTIVITY", "getSalesCall", "getActivityByID", "activityId", "pipe", "response", "activityDetails", "next", "getPartners", "PARTNERS", "item", "bp_id", "bp_full_name", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ActivitiesService {\r\n  public activitySubject = new BehaviorSubject<any>(null);\r\n  public activity = this.activitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  getActivities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,subject,activity_status,start_date,end_date,category'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getSalesCall(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,subject,activity_status,createdAt,category'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getActivityByID(activityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', activityId)\r\n      .set('populate[notes][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const activityDetails = response?.data[0] || null;\r\n          this.activitySubject.next(activityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,iBAAiB;EAI5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,eAAe,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAO,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEd;EAEvCC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,UAAUA,CAACC,EAAU,EAAEJ,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACU,GAAG,CAAC,GAAGb,gBAAgB,CAACU,QAAQ,IAAIE,EAAE,EAAE,EAAE;MACzDJ;KACD,CAAC;EACJ;EAEAM,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACZ,IAAI,CAACa,MAAM,CAAM,GAAGhB,gBAAgB,CAACU,QAAQ,IAAIK,EAAE,EAAE,CAAC;EACpE;EAEAE,aAAaA,CACXC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC1B2B,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CACF,QAAQ,EACR,4EAA4E,CAC7E;IAEH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEF,UAAU,CAAC;MACvEC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAACnB,IAAI,CAACyB,GAAG,CAAQ,GAAG5B,gBAAgB,CAAC6B,YAAY,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC;EAC7E;EAEAO,YAAYA,CACVZ,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC1B2B,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CACF,QAAQ,EACR,kEAAkE,CACnE;IAEH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEF,UAAU,CAAC;MACvEC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAACnB,IAAI,CAACyB,GAAG,CAAQ,GAAG5B,gBAAgB,CAAC6B,YAAY,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC;EAC7E;EAEAQ,eAAeA,CAACC,UAAkB;IAChC,MAAMT,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC5B2B,GAAG,CAAC,qBAAqB,EAAEQ,UAAU,CAAC,CACtCR,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC;IAExC,OAAO,IAAI,CAACrB,IAAI,CACbyB,GAAG,CAAQ,GAAG5B,gBAAgB,CAAC6B,YAAY,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC,CAC1DU,IAAI,CACHlC,GAAG,CAAEmC,QAAa,IAAI;MACpB,MAAMC,eAAe,GAAGD,QAAQ,EAAE1B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACjD,IAAI,CAACJ,eAAe,CAACgC,IAAI,CAACD,eAAe,CAAC;MAC1C,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,WAAWA,CAACd,MAAW;IACrB,OAAO,IAAI,CAACpB,IAAI,CAACyB,GAAG,CAAM,GAAG5B,gBAAgB,CAACsC,QAAQ,EAAE,EAAE;MAAEf;IAAM,CAAE,CAAC,CAACU,IAAI,CACxElC,GAAG,CAAEmC,QAAQ,IACX,CAACA,QAAQ,EAAE1B,IAAI,IAAI,EAAE,EAAET,GAAG,CAAEwC,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;;;uBAjHWxC,iBAAiB,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjB5C,iBAAiB;MAAA6C,OAAA,EAAjB7C,iBAAiB,CAAA8C,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}