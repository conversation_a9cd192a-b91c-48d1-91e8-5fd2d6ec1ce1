{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./sales-quotes.service\";\nimport * as i3 from \"../services/setting.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/breadcrumb\";\nimport * as i11 from \"primeng/calendar\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/paginator\";\nimport * as i14 from \"primeng/progressspinner\";\nimport * as i15 from \"primeng/multiselect\";\nconst _c0 = a0 => [a0];\nfunction SalesQuotesComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 35);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 35);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 36);\n    i0.ɵɵlistener(\"click\", function SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_4_Template, 1, 1, \"i\", 31)(5, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_5_Template, 1, 0, \"i\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function SalesQuotesComponent_p_table_41_ng_template_1_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"SD_DOC\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5, \" Quote # \");\n    i0.ɵɵtemplate(6, SalesQuotesComponent_p_table_41_ng_template_1_i_6_Template, 1, 1, \"i\", 31)(7, SalesQuotesComponent_p_table_41_ng_template_1_i_7_Template, 1, 0, \"i\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template, 6, 4, \"ng-container\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_NAME, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_STATUS, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_DATE, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 41);\n    i0.ɵɵtemplate(3, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 42)(4, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 42)(5, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 42);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_NAME\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_STATUS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_DATE\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 37)(1, \"td\", 38);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_Template, 6, 4, \"ng-container\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", tableinfo_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, \"/store/sales-quotes/\" + tableinfo_r6.SD_DOC));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.SD_DOC, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 25);\n    i0.ɵɵlistener(\"onColReorder\", function SalesQuotesComponent_p_table_41_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(1, SalesQuotesComponent_p_table_41_ng_template_1_Template, 9, 3, \"ng-template\", 26)(2, SalesQuotesComponent_p_table_41_ng_template_2_Template, 6, 6, \"ng-template\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.tableData)(\"rows\", 14)(\"paginator\", true)(\"totalRecords\", ctx_r1.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction SalesQuotesComponent_p_paginator_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 43);\n    i0.ɵɵlistener(\"onPageChange\", function SalesQuotesComponent_p_paginator_42_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"first\", ctx_r1.first)(\"rows\", ctx_r1.rows)(\"totalRecords\", ctx_r1.totalRecords);\n  }\n}\nexport class SalesQuotesComponent {\n  constructor(fb, SalesQuotesService, settingManager) {\n    this.fb = fb;\n    this.SalesQuotesService = SalesQuotesService;\n    this.settingManager = settingManager;\n    this.unsubscribe$ = new Subject();\n    this.items = [];\n    this.home = {};\n    this.allData = [];\n    this.tableData = [];\n    this.totalRecords = 1000;\n    this.loading = false;\n    this.first = 0;\n    this.rows = 10;\n    this.channels = ['Web Order', 'S4 Order'];\n    this.QuoteStatus = ['All'];\n    this.orderStatusesValue = {};\n    this.orderValue = {};\n    this.orderType = '';\n    this.currentPage = 1;\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'DOC_NAME',\n      header: 'Quote Name'\n    }, {\n      field: 'DOC_STATUS',\n      header: 'Quote Status'\n    }, {\n      field: 'DOC_DATE',\n      header: 'Date Placed'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n    this.filterForm = this.fb.group({\n      dateFrom: [''],\n      dateTo: [''],\n      QuoteStatus: ['All'],\n      Quote: ['']\n    });\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.tableData.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.items = [{\n      label: 'Sales Quotes',\n      routerLink: ['/store/sales-quotes']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.loading = true;\n    this.SalesQuotesService.fetchOrderStatuses({\n      'filters[type][$eq]': 'QUOTE_STATUS'\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response?.data.length) {\n          this.QuoteStatus = response?.data.map(val => {\n            this.orderStatusesValue[val.description] = val.code;\n            this.orderValue[val.code] = val.description;\n            this.orderStatusesValue['All'] = this.orderStatusesValue['All'] ? `${this.orderStatusesValue['All']};${val.code}` : val.code;\n            return val.description;\n          });\n          this.QuoteStatus = ['All', ...this.QuoteStatus];\n        }\n      },\n      error: error => {\n        console.error('Error fetching avatars:', error);\n      }\n    });\n    this.settingManager.getSettings().pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        console.log('f-f-f-f-', response, response?.data);\n        if (response && response[0]) {\n          this.orderType = response[0].sales_quote_type_code;\n        }\n        this.onPageChange({\n          first: this.first,\n          rows: this.rows\n        });\n      },\n      error: error => {\n        this.onPageChange({\n          first: this.first,\n          rows: this.rows\n        });\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  fetchOrders(count) {\n    this.loading = true;\n    const filterValues = this.filterForm.value;\n    const rawParams = {\n      // SOLDTO: this.sellerDetails.customer_id,\n      // SOLDTO: '00830VGB',\n      // VKORG: 1000,\n      COUNT: count,\n      SD_DOC: filterValues.Quote,\n      DOCUMENT_DATE: filterValues.dateFrom ? new Date(filterValues.dateFrom).toISOString().slice(0, 10) : '',\n      DOCUMENT_DATE_TO: filterValues.dateTo ? new Date(filterValues.dateTo).toISOString().slice(0, 10) : '',\n      DOC_STATUS: this.orderStatusesValue[filterValues.QuoteStatus] || 'A;C',\n      DOC_TYPE: this.orderType\n    };\n    // Remove empty or undefined values from params\n    const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n    this.SalesQuotesService.fetchSalesquoteOrders(params).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response?.SALESQUOTES) {\n          this.tableData = response.SALESQUOTES.map(record => ({\n            SD_DOC: record?.SD_DOC || '-',\n            DOC_NAME: record?.DOC_NAME || '-',\n            DOC_TYPE: record?.DOC_TYPE || '-',\n            // DOC_STATUS: record?.DOC_STATUS || '-',\n            DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n            DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-'\n          }));\n          const newRecords = response.SALESQUOTES.length;\n          const totalFetched = this.allData.length + newRecords;\n          const skipCount = totalFetched - newRecords;\n          this.allData.push(...this.tableData.slice(skipCount));\n          this.totalRecords = this.allData.length;\n          this.paginateData();\n        } else {\n          this.allData = [];\n          this.totalRecords = 0;\n          this.paginateData();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching avatars:', error);\n        this.loading = false;\n      }\n    });\n  }\n  onPageChange(event) {\n    this.first = event.first;\n    this.rows = event.rows;\n    this.currentPage = this.first / this.rows + 1;\n    if (this.first + this.rows >= this.allData.length && this.allData.length % 100 == 0) {\n      this.fetchOrders(this.allData.length + 1000);\n    }\n    this.paginateData();\n  }\n  paginateData() {\n    this.tableData = this.allData.slice(this.first, this.first + this.rows);\n  }\n  onSearch() {\n    this.allData = [];\n    this.totalRecords = 1000;\n    this.fetchOrders(this.totalRecords);\n  }\n  onClear() {\n    this.allData = [];\n    this.totalRecords = 1000;\n    this.filterForm.reset({\n      dateFrom: '',\n      dateTo: '',\n      QuoteStatus: 'All',\n      Quote: ''\n    });\n    this.fetchOrders(this.totalRecords);\n  }\n  createQuote() {\n    const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2`;\n    window.open(url, '_blank');\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesQuotesComponent_Factory(t) {\n      return new (t || SalesQuotesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SalesQuotesService), i0.ɵɵdirectiveInject(i3.SettingsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesQuotesComponent,\n      selectors: [[\"app-sales-quotes\"]],\n      decls: 43,\n      vars: 14,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\", 3, \"ngSubmit\", \"formGroup\"], [1, \"filter-sec\", \"mb-5\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"formControlName\", \"dateFrom\", \"placeholder\", \"Date From\", \"styleClass\", \"h-3rem w-full\", 3, \"showIcon\"], [\"formControlName\", \"dateTo\", \"placeholder\", \"Date To\", \"styleClass\", \"h-3rem w-full\", 3, \"showIcon\"], [\"formControlName\", \"QuoteStatus\", \"placeholder\", \"Quote Status\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"formControlName\", \"Quote\", \"placeholder\", \"Quote #\", 1, \"p-inputtext\", \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Clear\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Search\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\", \"onColReorder\", 4, \"ngIf\"], [3, \"first\", \"rows\", \"totalRecords\", \"onPageChange\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"onPageChange\", \"first\", \"rows\", \"totalRecords\"]],\n      template: function SalesQuotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SalesQuotesComponent_Template_button_click_5_listener() {\n            return ctx.createQuote();\n          });\n          i0.ɵɵelementStart(6, \"span\", 6);\n          i0.ɵɵtext(7, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Create \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p-multiSelect\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesQuotesComponent_Template_p_multiSelect_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function SalesQuotesComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11)(14, \"span\", 12);\n          i0.ɵɵtext(15, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Date From \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"p-calendar\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"label\", 11)(20, \"span\", 12);\n          i0.ɵɵtext(21, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22, \" Date To \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"p-calendar\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"label\", 11)(26, \"span\", 12);\n          i0.ɵɵtext(27, \"price_change\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" Quote Status \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"p-dropdown\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 10)(31, \"label\", 11)(32, \"span\", 12);\n          i0.ɵɵtext(33, \"request_quote\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" Quote # \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SalesQuotesComponent_Template_button_click_37_listener() {\n            return ctx.onClear();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"button\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 20);\n          i0.ɵɵtemplate(40, SalesQuotesComponent_div_40_Template, 2, 0, \"div\", 21)(41, SalesQuotesComponent_p_table_41_Template, 3, 7, \"p-table\", 22)(42, SalesQuotesComponent_p_paginator_42_Template, 1, 3, \"p-paginator\", 23);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"options\", ctx.QuoteStatus)(\"styleClass\", \"h-3rem w-full flex align-items-center\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i6.Table, i7.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i6.TableCheckbox, i6.TableHeaderCheckbox, i8.ButtonDirective, i9.Dropdown, i10.Breadcrumb, i11.Calendar, i12.InputText, i13.Paginator, i1.FormGroupDirective, i1.FormControlName, i14.ProgressSpinner, i15.MultiSelect],\n      styles: [\".filter-sec[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 18px;\\n}\\n.filter-sec[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%]   p-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.customer-info[_ngcontent-%COMP%] {\\n  border: 1px solid #ccc;\\n  background-color: #E7ECF2;\\n  padding: 15px;\\n  display: flex;\\n  margin-bottom: 30px;\\n  justify-content: space-between;\\n  gap: 18px !important;\\n  border-radius: 15px;\\n  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n.form-info[_ngcontent-%COMP%] {\\n  border: 1px solid #ccc;\\n  margin-bottom: 50px;\\n  padding: 10px;\\n  padding-top: 20px;\\n  border-radius: 15px;\\n  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvc2FsZXMtcXVvdGVzL3NhbGVzLXF1b3Rlcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxTQUFBO0FBQ0o7QUFDSTtFQUNJLFdBQUE7QUFDUjtBQUVJO0VBQ0ksV0FBQTtBQUFSO0FBRVE7RUFDSSxXQUFBO0FBQVo7O0FBS0E7RUFDSSxzQkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkFBQTtFQUNBLDJDQUFBO0FBRko7O0FBS0E7RUFDSSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSwyQ0FBQTtBQUZKIiwic291cmNlc0NvbnRlbnQiOlsiLmZpbHRlci1zZWMge1xyXG4gICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgZ2FwOiAxOHB4O1xyXG5cclxuICAgIGlucHV0IHtcclxuICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgIH1cclxuXHJcbiAgICAuaW5wdXQtbWFpbiB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcblxyXG4gICAgICAgIHAtZHJvcGRvd24ge1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5jdXN0b21lci1pbmZvIHtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRTdFQ0YyO1xyXG4gICAgcGFkZGluZzogMTVweDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgZ2FwOiAxOHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgYm94LXNoYWRvdzogNXB4IDVweCAxMHB4IHJnYmEoMCwwLDAsMC4yKTtcclxufVxyXG5cclxuLmZvcm0taW5mbyB7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjY2NjO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogNTBweDtcclxuICAgIHBhZGRpbmc6IDEwcHg7XHJcbiAgICBwYWRkaW5nLXRvcDogMjBweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDE1cHg7XHJcbiAgICBib3gtc2hhZG93OiA1cHggNXB4IDEwcHggcmdiYSgwLDAsMCwwLjIpO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵlistener", "SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_4_Template", "SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_5_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "SalesQuotesComponent_p_table_41_ng_template_1_Template_th_click_3_listener", "_r3", "SalesQuotesComponent_p_table_41_ng_template_1_i_6_Template", "SalesQuotesComponent_p_table_41_ng_template_1_i_7_Template", "SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template", "selectedColumns", "tableinfo_r6", "DOC_NAME", "DOC_STATUS", "DOC_DATE", "SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_3_Template", "SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_4_Template", "SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_5_Template", "col_r7", "SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_Template", "ɵɵpureFunction1", "_c0", "SD_DOC", "SalesQuotesComponent_p_table_41_Template_p_table_onColReorder_0_listener", "$event", "_r1", "onColumnReorder", "SalesQuotesComponent_p_table_41_ng_template_1_Template", "SalesQuotesComponent_p_table_41_ng_template_2_Template", "tableData", "totalRecords", "SalesQuotesComponent_p_paginator_42_Template_p_paginator_onPageChange_0_listener", "_r8", "onPageChange", "first", "rows", "SalesQuotesComponent", "constructor", "fb", "SalesQuotesService", "settingManager", "unsubscribe$", "items", "home", "allData", "loading", "channels", "Quote<PERSON><PERSON><PERSON>", "orderStatusesValue", "orderValue", "orderType", "currentPage", "_selectedColumns", "cols", "filterForm", "group", "dateFrom", "dateTo", "Quote", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "label", "routerLink", "icon", "fetchOrderStatuses", "pipe", "subscribe", "next", "response", "length", "map", "val", "description", "code", "error", "console", "getSettings", "log", "sales_quote_type_code", "filter", "col", "includes", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "fetchOrders", "count", "filterValues", "value", "rawParams", "COUNT", "DOCUMENT_DATE", "Date", "toISOString", "slice", "DOCUMENT_DATE_TO", "DOC_TYPE", "params", "Object", "fromEntries", "entries", "_", "undefined", "fetchSalesquoteOrders", "SALESQUOTES", "record", "substring", "newRecords", "totalFetched", "skip<PERSON><PERSON>nt", "push", "paginateData", "onSearch", "onClear", "reset", "createQuote", "url", "crms4Endpoint", "window", "open", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "i3", "SettingsService", "selectors", "decls", "vars", "consts", "template", "SalesQuotesComponent_Template", "rf", "ctx", "SalesQuotesComponent_Template_button_click_5_listener", "ɵɵtwoWayListener", "SalesQuotesComponent_Template_p_multiSelect_ngModelChange_9_listener", "ɵɵtwoWayBindingSet", "SalesQuotesComponent_Template_form_ngSubmit_10_listener", "SalesQuotesComponent_Template_button_click_37_listener", "SalesQuotesComponent_div_40_Template", "SalesQuotesComponent_p_table_41_Template", "SalesQuotesComponent_p_paginator_42_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup } from '@angular/forms';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { SalesQuotesService } from './sales-quotes.service';\r\nimport { SettingsService } from '../services/setting.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\n\r\ninterface SalesQuoteData {\r\n  SD_DOC?: string;\r\n  DOC_NAME?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_DATE?: string;\r\n  DOC_STATUS?: string;\r\n}\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-quotes',\r\n  templateUrl: './sales-quotes.component.html',\r\n  styleUrl: './sales-quotes.component.scss',\r\n})\r\nexport class SalesQuotesComponent {\r\n  private unsubscribe$ = new Subject<void>();\r\n  items: MenuItem[] = [];\r\n  home: MenuItem = {};\r\n  allData: SalesQuoteData[] = [];\r\n  tableData: SalesQuoteData[] = [];\r\n  totalRecords: number = 1000;\r\n  loading: boolean = false;\r\n  first: number = 0;\r\n  rows: number = 10;\r\n  channels: any[] = ['Web Order', 'S4 Order'];\r\n  QuoteStatus: any[] = ['All'];\r\n  orderStatusesValue: any = {};\r\n  orderValue: any = {};\r\n  filterForm: FormGroup;\r\n  orderType: string = '';\r\n  currentPage: number = 1;\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private SalesQuotesService: SalesQuotesService,\r\n    private settingManager: SettingsService\r\n  ) {\r\n    this.filterForm = this.fb.group({\r\n      dateFrom: [''],\r\n      dateTo: [''],\r\n      QuoteStatus: ['All'],\r\n      Quote: [''],\r\n    });\r\n  }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'DOC_NAME', header: 'Quote Name' },\r\n    { field: 'DOC_STATUS', header: 'Quote Status' },\r\n    { field: 'DOC_DATE', header: 'Date Placed' }\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.tableData.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.items = [\r\n      { label: 'Sales Quotes', routerLink: ['/store/sales-quotes'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.loading = true;\r\n    this.SalesQuotesService.fetchOrderStatuses({\r\n      'filters[type][$eq]': 'QUOTE_STATUS',\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data.length) {\r\n            this.QuoteStatus = response?.data.map((val: any) => {\r\n              this.orderStatusesValue[val.description] = val.code;\r\n              this.orderValue[val.code] = val.description;\r\n              this.orderStatusesValue['All'] = this.orderStatusesValue['All']\r\n                ? `${this.orderStatusesValue['All']};${val.code}`\r\n                : val.code;\r\n              return val.description;\r\n            });\r\n            this.QuoteStatus = ['All', ...this.QuoteStatus];\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching avatars:', error);\r\n        },\r\n      });\r\n    this.settingManager\r\n      .getSettings()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          console.log('f-f-f-f-', response, response?.data);\r\n          if (response && response[0]) {\r\n            this.orderType = response[0].sales_quote_type_code;\r\n          }\r\n          this.onPageChange({ first: this.first, rows: this.rows });\r\n        },\r\n        error: (error) => {\r\n          this.onPageChange({ first: this.first, rows: this.rows });\r\n        },\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  fetchOrders(count: number) {\r\n    this.loading = true;\r\n    const filterValues = this.filterForm.value;\r\n\r\n    const rawParams = {\r\n      // SOLDTO: this.sellerDetails.customer_id,\r\n      // SOLDTO: '00830VGB',\r\n      // VKORG: 1000,\r\n      COUNT: count,\r\n      SD_DOC: filterValues.Quote,\r\n      DOCUMENT_DATE: filterValues.dateFrom\r\n        ? new Date(filterValues.dateFrom).toISOString().slice(0, 10)\r\n        : '',\r\n      DOCUMENT_DATE_TO: filterValues.dateTo\r\n        ? new Date(filterValues.dateTo).toISOString().slice(0, 10)\r\n        : '',\r\n      DOC_STATUS: this.orderStatusesValue[filterValues.QuoteStatus] || 'A;C',\r\n      DOC_TYPE: this.orderType,\r\n    };\r\n\r\n    // Remove empty or undefined values from params\r\n    const params: any = Object.fromEntries(\r\n      Object.entries(rawParams).filter(\r\n        ([_, value]) => value !== undefined && value !== ''\r\n      )\r\n    );\r\n\r\n    this.SalesQuotesService.fetchSalesquoteOrders(params)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response?.SALESQUOTES) {\r\n            this.tableData = response.SALESQUOTES.map((record) => ({\r\n              SD_DOC: record?.SD_DOC || '-',\r\n              DOC_NAME: record?.DOC_NAME || '-',\r\n              DOC_TYPE: record?.DOC_TYPE || '-',\r\n              // DOC_STATUS: record?.DOC_STATUS || '-',\r\n              DOC_STATUS: record.DOC_STATUS\r\n                ? this.orderValue[record.DOC_STATUS]\r\n                : '-',\r\n              DOC_DATE: record?.DOC_DATE\r\n                ? `${record.DOC_DATE.substring(\r\n                  0,\r\n                  4\r\n                )}-${record.DOC_DATE.substring(\r\n                  4,\r\n                  6\r\n                )}-${record.DOC_DATE.substring(6, 8)}`\r\n                : '-',\r\n            }));\r\n            const newRecords = response.SALESQUOTES.length;\r\n            const totalFetched = this.allData.length + newRecords;\r\n            const skipCount = totalFetched - newRecords;\r\n            this.allData.push(...this.tableData.slice(skipCount));\r\n            this.totalRecords = this.allData.length;\r\n            this.paginateData();\r\n          } else {\r\n            this.allData = [];\r\n            this.totalRecords = 0;\r\n            this.paginateData();\r\n          }\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching avatars:', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onPageChange(event: any) {\r\n    this.first = event.first;\r\n    this.rows = event.rows;\r\n    this.currentPage = this.first / this.rows + 1;\r\n\r\n    if (\r\n      this.first + this.rows >= this.allData.length &&\r\n      this.allData.length % 100 == 0\r\n    ) {\r\n      this.fetchOrders(this.allData.length + 1000);\r\n    }\r\n    this.paginateData();\r\n  }\r\n\r\n  paginateData() {\r\n    this.tableData = this.allData.slice(this.first, this.first + this.rows);\r\n  }\r\n\r\n  onSearch() {\r\n    this.allData = [];\r\n    this.totalRecords = 1000;\r\n    this.fetchOrders(this.totalRecords);\r\n  }\r\n\r\n  onClear() {\r\n    this.allData = [];\r\n    this.totalRecords = 1000;\r\n    this.filterForm.reset({\r\n      dateFrom: '',\r\n      dateTo: '',\r\n      QuoteStatus: 'All',\r\n      Quote: '',\r\n    });\r\n    this.fetchOrders(this.totalRecords);\r\n  }\r\n\r\n  createQuote() {\r\n    const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2`;\r\n    window.open(url, '_blank');\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <button type=\"button\" (click)=\"createQuote()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Filter Section -->\r\n    <form class=\"shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50\" [formGroup]=\"filterForm\"\r\n        (ngSubmit)=\"onSearch()\">\r\n        <div class=\"filter-sec mb-5\">\r\n            <!-- Date From -->\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-600\">calendar_month</span> Date From\r\n                </label>\r\n                <p-calendar formControlName=\"dateFrom\" placeholder=\"Date From\" [showIcon]=\"true\"\r\n                    styleClass=\"h-3rem w-full\"></p-calendar>\r\n            </div>\r\n\r\n            <!-- Date To -->\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-600\">calendar_month</span> Date To\r\n                </label>\r\n                <p-calendar formControlName=\"dateTo\" placeholder=\"Date To\" [showIcon]=\"true\"\r\n                    styleClass=\"h-3rem w-full\"></p-calendar>\r\n            </div>\r\n\r\n            <!-- Quote Status -->\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-600\">price_change</span> Quote Status\r\n                </label>\r\n                <p-dropdown [options]=\"QuoteStatus\" formControlName=\"QuoteStatus\" placeholder=\"Quote Status\"\r\n                    [styleClass]=\"'h-3rem w-full flex align-items-center'\"></p-dropdown>\r\n            </div>\r\n\r\n            <!-- Quote -->\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-600\">request_quote</span> Quote #\r\n                </label>\r\n                <input pInputText formControlName=\"Quote\" placeholder=\"Quote #\" class=\"p-inputtext h-3rem w-full\" />\r\n            </div>\r\n\r\n        </div>\r\n\r\n        <!-- Buttons -->\r\n        <div class=\"flex align-items-center justify-content-center gap-3\">\r\n            <button pButton type=\"button\" label=\"Clear\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onClear()\"></button>\r\n            <button pButton type=\"submit\" label=\"Search\"\r\n                class=\"p-button-rounded justify-content-center w-9rem h-3rem\"></button>\r\n        </div>\r\n    </form>\r\n\r\n    <div class=\"table-sec\">\r\n        <!-- Loader Spinner -->\r\n        <div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"loading\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n\r\n        <p-table *ngIf=\"!loading\" [value]=\"tableData\" dataKey=\"id\" [rows]=\"14\" [paginator]=\"true\"\r\n            [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\" [scrollable]=\"true\"\r\n            class=\"scrollable-table\" [reorderableColumns]=\"true\" (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('SD_DOC')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Quote #\r\n                            <i *ngIf=\"sortField === 'SD_DOC'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'SD_DOC'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-tableinfo let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"tableinfo\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline\"\r\n                        [routerLink]=\"['/store/sales-quotes/' + tableinfo.SD_DOC]\">\r\n                        {{ tableinfo.SD_DOC }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'DOC_NAME'\">\r\n                                    {{ tableinfo.DOC_NAME }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'DOC_STATUS'\">\r\n                                    {{ tableinfo.DOC_STATUS }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'DOC_DATE'\">\r\n                                    {{ tableinfo.DOC_DATE }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n        </p-table>\r\n        <p-paginator *ngIf=\"!loading\" (onPageChange)=\"onPageChange($event)\" [first]=\"first\" [rows]=\"rows\"\r\n            [totalRecords]=\"totalRecords\" />\r\n\r\n    </div>\r\n</div>"], "mappings": "AAKA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;ICiElDC,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAE,SAAA,wBAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAccH,EAAA,CAAAE,SAAA,YAEI;;;;IADAF,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFN,EAAA,CAAAE,SAAA,YAA8D;;;;;IAO1DF,EAAA,CAAAE,SAAA,YAEI;;;;IADAF,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFN,EAAA,CAAAE,SAAA,YAA+D;;;;;;IAP3EF,EAAA,CAAAO,uBAAA,GAAkD;IAC9CP,EAAA,CAAAC,cAAA,aAAqF;IAAhCD,EAAA,CAAAQ,UAAA,mBAAAC,0FAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAR,MAAA,GAAAL,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASV,MAAA,CAAAW,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFjB,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAkB,MAAA,GACA;IAGAlB,EAHA,CAAAmB,UAAA,IAAAC,yEAAA,gBACkF,IAAAC,yEAAA,gBAEvB;IAEnErB,EADI,CAAAG,YAAA,EAAM,EACL;;;;;;IARDH,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAI,UAAA,oBAAAM,MAAA,CAAAO,KAAA,CAA6B;IAEzBjB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAc,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAoB,SAAA,KAAAf,MAAA,CAAAO,KAAA,CAA6B;IAG7BjB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAoB,SAAA,KAAAf,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7CjB,EADJ,CAAAC,cAAA,SAAI,aACsF;IAClFD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAiD;IAA/BD,EAAA,CAAAQ,UAAA,mBAAAkB,2EAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAAtB,MAAA,GAAAL,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASV,MAAA,CAAAW,UAAA,CAAW,QAAQ,CAAC;IAAA,EAAC;IAC5ChB,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAkB,MAAA,gBACA;IAGAlB,EAHA,CAAAmB,UAAA,IAAAS,0DAAA,gBACkF,IAAAC,0DAAA,gBAExB;IAElE7B,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAmB,UAAA,IAAAW,qEAAA,2BAAkD;IAWtD9B,EAAA,CAAAG,YAAA,EAAK;;;;IAjBWH,EAAA,CAAAsB,SAAA,GAA4B;IAA5BtB,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAoB,SAAA,cAA4B;IAG5BzB,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAoB,SAAA,cAA4B;IAGVzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAA0B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAO,uBAAA,GAAyC;IACrCP,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAC,QAAA,MACJ;;;;;IAEAjC,EAAA,CAAAO,uBAAA,GAA2C;IACvCP,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAE,UAAA,MACJ;;;;;IAEAlC,EAAA,CAAAO,uBAAA,GAAyC;IACrCP,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAG,QAAA,MACJ;;;;;IAdZnC,EAAA,CAAAO,uBAAA,GAAkD;IAC9CP,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAO,uBAAA,OAAqC;IAUjCP,EARA,CAAAmB,UAAA,IAAAiB,oFAAA,2BAAyC,IAAAC,oFAAA,2BAIE,IAAAC,oFAAA,2BAIF;;IAKjDtC,EAAA,CAAAG,YAAA,EAAK;;;;;IAfaH,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAI,UAAA,aAAAmC,MAAA,CAAAtB,KAAA,CAAsB;IAEjBjB,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAI,UAAA,4BAAwB;IAIxBJ,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAI,UAAA,8BAA0B;IAI1BJ,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAI,UAAA,4BAAwB;;;;;IApBnDJ,EADJ,CAAAC,cAAA,aAA2B,aACgD;IACnED,EAAA,CAAAE,SAAA,0BAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAC+D;IAC3DD,EAAA,CAAAkB,MAAA,GACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAmB,UAAA,IAAAqB,qEAAA,2BAAkD;IAmBtDxC,EAAA,CAAAG,YAAA,EAAK;;;;;IA1BoBH,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAI,UAAA,UAAA4B,YAAA,CAAmB;IAGpChC,EAAA,CAAAsB,SAAA,EAA0D;IAA1DtB,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAyC,eAAA,IAAAC,GAAA,2BAAAV,YAAA,CAAAW,MAAA,EAA0D;IAC1D3C,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAW,MAAA,MACJ;IAE8B3C,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAA0B,eAAA,CAAkB;;;;;;IA1C5D/B,EAAA,CAAAC,cAAA,kBAEkG;IAAzCD,EAAA,CAAAQ,UAAA,0BAAAoC,yEAAAC,MAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAAmC,GAAA;MAAA,MAAAzC,MAAA,GAAAL,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAgBV,MAAA,CAAA0C,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IA8B7F7C,EA5BA,CAAAmB,UAAA,IAAA6B,sDAAA,0BAAgC,IAAAC,sDAAA,0BA4BkC;IAgCtEjD,EAAA,CAAAG,YAAA,EAAU;;;;IA9DmBH,EAFH,CAAAI,UAAA,UAAAC,MAAA,CAAA6C,SAAA,CAAmB,YAAyB,mBAAmB,iBAAA7C,MAAA,CAAA8C,YAAA,CACxD,cAAc,oBAA8C,4BACrC;;;;;;IA+DxDnD,EAAA,CAAAC,cAAA,sBACoC;IADND,EAAA,CAAAQ,UAAA,0BAAA4C,iFAAAP,MAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAAhD,MAAA,GAAAL,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAgBV,MAAA,CAAAiD,YAAA,CAAAT,MAAA,CAAoB;IAAA,EAAC;IAAnE7C,EAAA,CAAAG,YAAA,EACoC;;;;IAAhCH,EADgE,CAAAI,UAAA,UAAAC,MAAA,CAAAkD,KAAA,CAAe,SAAAlD,MAAA,CAAAmD,IAAA,CAAc,iBAAAnD,MAAA,CAAA8C,YAAA,CAChE;;;ADnHzC,OAAM,MAAOM,oBAAoB;EAiB/BC,YACUC,EAAe,EACfC,kBAAsC,EACtCC,cAA+B;IAF/B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,cAAc,GAAdA,cAAc;IAnBhB,KAAAC,YAAY,GAAG,IAAIjE,OAAO,EAAQ;IAC1C,KAAAkE,KAAK,GAAe,EAAE;IACtB,KAAAC,IAAI,GAAa,EAAE;IACnB,KAAAC,OAAO,GAAqB,EAAE;IAC9B,KAAAf,SAAS,GAAqB,EAAE;IAChC,KAAAC,YAAY,GAAW,IAAI;IAC3B,KAAAe,OAAO,GAAY,KAAK;IACxB,KAAAX,KAAK,GAAW,CAAC;IACjB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAW,QAAQ,GAAU,CAAC,WAAW,EAAE,UAAU,CAAC;IAC3C,KAAAC,WAAW,GAAU,CAAC,KAAK,CAAC;IAC5B,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,UAAU,GAAQ,EAAE;IAEpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,WAAW,GAAW,CAAC;IAcf,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEzD,KAAK,EAAE,UAAU;MAAEO,MAAM,EAAE;IAAY,CAAE,EAC3C;MAAEP,KAAK,EAAE,YAAY;MAAEO,MAAM,EAAE;IAAc,CAAE,EAC/C;MAAEP,KAAK,EAAE,UAAU;MAAEO,MAAM,EAAE;IAAa,CAAE,CAC7C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAnB,SAAS,GAAW,CAAC;IAjBnB,IAAI,CAACqE,UAAU,GAAG,IAAI,CAAChB,EAAE,CAACiB,KAAK,CAAC;MAC9BC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZV,WAAW,EAAE,CAAC,KAAK,CAAC;MACpBW,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAaA/D,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACQ,SAAS,KAAKR,KAAK,EAAE;MAC5B,IAAI,CAACX,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACmB,SAAS,GAAGR,KAAK;MACtB,IAAI,CAACX,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC4C,SAAS,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEhE,KAAK,CAAC;MAC9C,MAAMoE,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEjE,KAAK,CAAC;MAE9C,IAAIqE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAAC/E,SAAS,GAAGgF,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAEvE,KAAa;IACvC,IAAI,CAACuE,IAAI,IAAI,CAACvE,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACwE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAACvE,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACyE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC/B,KAAK,GAAG,CACX;MAAEgC,KAAK,EAAE,cAAc;MAAEC,UAAU,EAAE,CAAC,qBAAqB;IAAC,CAAE,CAC/D;IACD,IAAI,CAAChC,IAAI,GAAG;MAAEiC,IAAI,EAAE,oBAAoB;MAAED,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,kBAAkB,CAACsC,kBAAkB,CAAC;MACzC,oBAAoB,EAAE;KACvB,CAAC,CACCC,IAAI,CAACrG,SAAS,CAAC,IAAI,CAACgE,YAAY,CAAC,CAAC,CAClCsC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEd,IAAI,CAACe,MAAM,EAAE;UACzB,IAAI,CAACnC,WAAW,GAAGkC,QAAQ,EAAEd,IAAI,CAACgB,GAAG,CAAEC,GAAQ,IAAI;YACjD,IAAI,CAACpC,kBAAkB,CAACoC,GAAG,CAACC,WAAW,CAAC,GAAGD,GAAG,CAACE,IAAI;YACnD,IAAI,CAACrC,UAAU,CAACmC,GAAG,CAACE,IAAI,CAAC,GAAGF,GAAG,CAACC,WAAW;YAC3C,IAAI,CAACrC,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,KAAK,CAAC,GAC3D,GAAG,IAAI,CAACA,kBAAkB,CAAC,KAAK,CAAC,IAAIoC,GAAG,CAACE,IAAI,EAAE,GAC/CF,GAAG,CAACE,IAAI;YACZ,OAAOF,GAAG,CAACC,WAAW;UACxB,CAAC,CAAC;UACF,IAAI,CAACtC,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,CAACA,WAAW,CAAC;QACjD;MACF,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;IACJ,IAAI,CAAC/C,cAAc,CAChBiD,WAAW,EAAE,CACbX,IAAI,CAACrG,SAAS,CAAC,IAAI,CAACgE,YAAY,CAAC,CAAC,CAClCsC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtBO,OAAO,CAACE,GAAG,CAAC,UAAU,EAAET,QAAQ,EAAEA,QAAQ,EAAEd,IAAI,CAAC;QACjD,IAAIc,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,EAAE;UAC3B,IAAI,CAAC/B,SAAS,GAAG+B,QAAQ,CAAC,CAAC,CAAC,CAACU,qBAAqB;QACpD;QACA,IAAI,CAAC1D,YAAY,CAAC;UAAEC,KAAK,EAAE,IAAI,CAACA,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACA;QAAI,CAAE,CAAC;MAC3D,CAAC;MACDoD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtD,YAAY,CAAC;UAAEC,KAAK,EAAE,IAAI,CAACA,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACA;QAAI,CAAE,CAAC;MAC3D;KACD,CAAC;IAEJ,IAAI,CAACiB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAGA,IAAI3C,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC0C,gBAAgB;EAC9B;EAEA,IAAI1C,eAAeA,CAAC0E,GAAU;IAC5B,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACuC,MAAM,CAACC,GAAG,IAAIT,GAAG,CAACU,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAnE,eAAeA,CAACqE,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAC5C,gBAAgB,CAAC2C,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAAC7C,gBAAgB,CAAC8C,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC7C,gBAAgB,CAAC8C,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAI,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACxD,OAAO,GAAG,IAAI;IACnB,MAAMyD,YAAY,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IAE1C,MAAMC,SAAS,GAAG;MAChB;MACA;MACA;MACAC,KAAK,EAAEJ,KAAK;MACZ/E,MAAM,EAAEgF,YAAY,CAAC5C,KAAK;MAC1BgD,aAAa,EAAEJ,YAAY,CAAC9C,QAAQ,GAChC,IAAImD,IAAI,CAACL,YAAY,CAAC9C,QAAQ,CAAC,CAACoD,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAC1D,EAAE;MACNC,gBAAgB,EAAER,YAAY,CAAC7C,MAAM,GACjC,IAAIkD,IAAI,CAACL,YAAY,CAAC7C,MAAM,CAAC,CAACmD,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GACxD,EAAE;MACNhG,UAAU,EAAE,IAAI,CAACmC,kBAAkB,CAACsD,YAAY,CAACvD,WAAW,CAAC,IAAI,KAAK;MACtEgE,QAAQ,EAAE,IAAI,CAAC7D;KAChB;IAED;IACA,MAAM8D,MAAM,GAAQC,MAAM,CAACC,WAAW,CACpCD,MAAM,CAACE,OAAO,CAACX,SAAS,CAAC,CAACZ,MAAM,CAC9B,CAAC,CAACwB,CAAC,EAAEb,KAAK,CAAC,KAAKA,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,EAAE,CACpD,CACF;IAED,IAAI,CAAChE,kBAAkB,CAAC+E,qBAAqB,CAACN,MAAM,CAAC,CAClDlC,IAAI,CAACrG,SAAS,CAAC,IAAI,CAACgE,YAAY,CAAC,CAAC,CAClCsC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAEsC,WAAW,EAAE;UACzB,IAAI,CAAC1F,SAAS,GAAGoD,QAAQ,CAACsC,WAAW,CAACpC,GAAG,CAAEqC,MAAM,KAAM;YACrDlG,MAAM,EAAEkG,MAAM,EAAElG,MAAM,IAAI,GAAG;YAC7BV,QAAQ,EAAE4G,MAAM,EAAE5G,QAAQ,IAAI,GAAG;YACjCmG,QAAQ,EAAES,MAAM,EAAET,QAAQ,IAAI,GAAG;YACjC;YACAlG,UAAU,EAAE2G,MAAM,CAAC3G,UAAU,GACzB,IAAI,CAACoC,UAAU,CAACuE,MAAM,CAAC3G,UAAU,CAAC,GAClC,GAAG;YACPC,QAAQ,EAAE0G,MAAM,EAAE1G,QAAQ,GACtB,GAAG0G,MAAM,CAAC1G,QAAQ,CAAC2G,SAAS,CAC5B,CAAC,EACD,CAAC,CACF,IAAID,MAAM,CAAC1G,QAAQ,CAAC2G,SAAS,CAC5B,CAAC,EACD,CAAC,CACF,IAAID,MAAM,CAAC1G,QAAQ,CAAC2G,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACpC;WACL,CAAC,CAAC;UACH,MAAMC,UAAU,GAAGzC,QAAQ,CAACsC,WAAW,CAACrC,MAAM;UAC9C,MAAMyC,YAAY,GAAG,IAAI,CAAC/E,OAAO,CAACsC,MAAM,GAAGwC,UAAU;UACrD,MAAME,SAAS,GAAGD,YAAY,GAAGD,UAAU;UAC3C,IAAI,CAAC9E,OAAO,CAACiF,IAAI,CAAC,GAAG,IAAI,CAAChG,SAAS,CAACgF,KAAK,CAACe,SAAS,CAAC,CAAC;UACrD,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAACc,OAAO,CAACsC,MAAM;UACvC,IAAI,CAAC4C,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAAClF,OAAO,GAAG,EAAE;UACjB,IAAI,CAACd,YAAY,GAAG,CAAC;UACrB,IAAI,CAACgG,YAAY,EAAE;QACrB;QACA,IAAI,CAACjF,OAAO,GAAG,KAAK;MACtB,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC1C,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAZ,YAAYA,CAAC8D,KAAU;IACrB,IAAI,CAAC7D,KAAK,GAAG6D,KAAK,CAAC7D,KAAK;IACxB,IAAI,CAACC,IAAI,GAAG4D,KAAK,CAAC5D,IAAI;IACtB,IAAI,CAACgB,WAAW,GAAG,IAAI,CAACjB,KAAK,GAAG,IAAI,CAACC,IAAI,GAAG,CAAC;IAE7C,IACE,IAAI,CAACD,KAAK,GAAG,IAAI,CAACC,IAAI,IAAI,IAAI,CAACS,OAAO,CAACsC,MAAM,IAC7C,IAAI,CAACtC,OAAO,CAACsC,MAAM,GAAG,GAAG,IAAI,CAAC,EAC9B;MACA,IAAI,CAACkB,WAAW,CAAC,IAAI,CAACxD,OAAO,CAACsC,MAAM,GAAG,IAAI,CAAC;IAC9C;IACA,IAAI,CAAC4C,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACjG,SAAS,GAAG,IAAI,CAACe,OAAO,CAACiE,KAAK,CAAC,IAAI,CAAC3E,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,IAAI,CAAC;EACzE;EAEA4F,QAAQA,CAAA;IACN,IAAI,CAACnF,OAAO,GAAG,EAAE;IACjB,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAACsE,WAAW,CAAC,IAAI,CAACtE,YAAY,CAAC;EACrC;EAEAkG,OAAOA,CAAA;IACL,IAAI,CAACpF,OAAO,GAAG,EAAE;IACjB,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAACwB,UAAU,CAAC2E,KAAK,CAAC;MACpBzE,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVV,WAAW,EAAE,KAAK;MAClBW,KAAK,EAAE;KACR,CAAC;IACF,IAAI,CAAC0C,WAAW,CAAC,IAAI,CAACtE,YAAY,CAAC;EACrC;EAEAoG,WAAWA,CAAA;IACT,MAAMC,GAAG,GAAG,GAAGzJ,WAAW,CAAC0J,aAAa,6BAA6B;IACrEC,MAAM,CAACC,IAAI,CAACH,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC9F,YAAY,CAACuC,IAAI,EAAE;IACxB,IAAI,CAACvC,YAAY,CAAC+F,QAAQ,EAAE;EAC9B;;;uBA5PWpG,oBAAoB,EAAAzD,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhK,EAAA,CAAA8J,iBAAA,CAAAG,EAAA,CAAArG,kBAAA,GAAA5D,EAAA,CAAA8J,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApB1G,oBAAoB;MAAA2G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBzB1K,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAAqF;UACzFF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,aAA2C,gBAEmG;UADpHD,EAAA,CAAAQ,UAAA,mBAAAoK,sDAAA;YAAA,OAASD,GAAA,CAAApB,WAAA,EAAa;UAAA,EAAC;UAEzCvJ,EAAA,CAAAC,cAAA,cAAgD;UAAAD,EAAA,CAAAkB,MAAA,eAAQ;UAAAlB,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAkB,MAAA,eACpE;UAAAlB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,uBAE+I;UAF/GD,EAAA,CAAA6K,gBAAA,2BAAAC,qEAAAjI,MAAA;YAAA7C,EAAA,CAAA+K,kBAAA,CAAAJ,GAAA,CAAA5I,eAAA,EAAAc,MAAA,MAAA8H,GAAA,CAAA5I,eAAA,GAAAc,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE7C,EAFQ,CAAAG,YAAA,EAAgB,EACd,EACJ;UAGNH,EAAA,CAAAC,cAAA,eAC4B;UAAxBD,EAAA,CAAAQ,UAAA,sBAAAwK,wDAAA;YAAA,OAAYL,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UAKXpJ,EAJZ,CAAAC,cAAA,cAA6B,eAED,iBAC0C,gBACD;UAAAD,EAAA,CAAAkB,MAAA,sBAAc;UAAAlB,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAkB,MAAA,mBACnF;UAAAlB,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAE,SAAA,sBAC4C;UAChDF,EAAA,CAAAG,YAAA,EAAM;UAKEH,EAFR,CAAAC,cAAA,eAAwB,iBAC0C,gBACD;UAAAD,EAAA,CAAAkB,MAAA,sBAAc;UAAAlB,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAkB,MAAA,iBACnF;UAAAlB,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAE,SAAA,sBAC4C;UAChDF,EAAA,CAAAG,YAAA,EAAM;UAKEH,EAFR,CAAAC,cAAA,eAAwB,iBAC0C,gBACD;UAAAD,EAAA,CAAAkB,MAAA,oBAAY;UAAAlB,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAkB,MAAA,sBACjF;UAAAlB,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAE,SAAA,sBACwE;UAC5EF,EAAA,CAAAG,YAAA,EAAM;UAKEH,EAFR,CAAAC,cAAA,eAAwB,iBAC0C,gBACD;UAAAD,EAAA,CAAAkB,MAAA,qBAAa;UAAAlB,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAkB,MAAA,iBAClF;UAAAlB,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAE,SAAA,iBAAoG;UAG5GF,EAFI,CAAAG,YAAA,EAAM,EAEJ;UAIFH,EADJ,CAAAC,cAAA,eAAkE,kBAGtC;UAApBD,EAAA,CAAAQ,UAAA,mBAAAyK,uDAAA;YAAA,OAASN,GAAA,CAAAtB,OAAA,EAAS;UAAA,EAAC;UAACrJ,EAAA,CAAAG,YAAA,EAAS;UACjCH,EAAA,CAAAE,SAAA,kBAC2E;UAEnFF,EADI,CAAAG,YAAA,EAAM,EACH;UAEPH,EAAA,CAAAC,cAAA,eAAuB;UAuEnBD,EArEA,CAAAmB,UAAA,KAAA+J,oCAAA,kBAAwF,KAAAC,wCAAA,sBAMU,KAAAC,4CAAA,0BAgE9D;UAG5CpL,EADI,CAAAG,YAAA,EAAM,EACJ;;;UA7IoBH,EAAA,CAAAsB,SAAA,GAAe;UAAetB,EAA9B,CAAAI,UAAA,UAAAuK,GAAA,CAAA5G,KAAA,CAAe,SAAA4G,GAAA,CAAA3G,IAAA,CAAc,uCAAuC;UAQnEhE,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAAjG,IAAA,CAAgB;UAAC1E,EAAA,CAAAqL,gBAAA,YAAAV,GAAA,CAAA5I,eAAA,CAA6B;UAEzD/B,EAAA,CAAAI,UAAA,2IAA0I;UAM5DJ,EAAA,CAAAsB,SAAA,EAAwB;UAAxBtB,EAAA,CAAAI,UAAA,cAAAuK,GAAA,CAAAhG,UAAA,CAAwB;UAQvC3E,EAAA,CAAAsB,SAAA,GAAiB;UAAjBtB,EAAA,CAAAI,UAAA,kBAAiB;UASrBJ,EAAA,CAAAsB,SAAA,GAAiB;UAAjBtB,EAAA,CAAAI,UAAA,kBAAiB;UAShEJ,EAAA,CAAAsB,SAAA,GAAuB;UAC/BtB,EADQ,CAAAI,UAAA,YAAAuK,GAAA,CAAAvG,WAAA,CAAuB,uDACuB;UAyBOpE,EAAA,CAAAsB,SAAA,IAAa;UAAbtB,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAAzG,OAAA,CAAa;UAI5ElE,EAAA,CAAAsB,SAAA,EAAc;UAAdtB,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAzG,OAAA,CAAc;UAiEVlE,EAAA,CAAAsB,SAAA,EAAc;UAAdtB,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAzG,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}