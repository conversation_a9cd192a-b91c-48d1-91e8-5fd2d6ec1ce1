{"ast": null, "code": "import { Subject, takeUntil, fork<PERSON>oin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"src/app/store/services/setting.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/progressspinner\";\nfunction AccountSalesQuotesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14)(2, \"div\", 15);\n    i0.ɵɵtext(3, \" Quote # \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 17)(6, \"div\", 15);\n    i0.ɵɵtext(7, \" Quote Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 19)(10, \"div\", 15);\n    i0.ɵɵtext(11, \" Quote Status \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 21)(14, \"div\", 15);\n    i0.ɵɵtext(15, \" Date Placed \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_7_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 23);\n    i0.ɵɵlistener(\"click\", function AccountSalesQuotesComponent_p_table_7_ng_template_3_Template_tr_click_0_listener() {\n      const quote_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToQuoteDetail(quote_r2));\n    });\n    i0.ɵɵelementStart(1, \"td\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quote_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", quote_r2 == null ? null : quote_r2.SD_DOC, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", quote_r2 == null ? null : quote_r2.DOC_NAME, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", quote_r2 == null ? null : quote_r2.DOC_STATUS, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 4, quote_r2 == null ? null : quote_r2.DOC_DATE, \"MM/dd/yyyy\"), \" \");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_7_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"No quotes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_7_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"Loading quotes data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 9, 0);\n    i0.ɵɵtemplate(2, AccountSalesQuotesComponent_p_table_7_ng_template_2_Template, 17, 0, \"ng-template\", 10)(3, AccountSalesQuotesComponent_p_table_7_ng_template_3_Template, 10, 7, \"ng-template\", 11)(4, AccountSalesQuotesComponent_p_table_7_ng_template_4_Template, 3, 0, \"ng-template\", 12)(5, AccountSalesQuotesComponent_p_table_7_ng_template_5_Template, 3, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r2.QuoteData)(\"rows\", 8)(\"rowHover\", true)(\"paginator\", true)(\"totalRecords\", ctx_r2.totalRecords);\n  }\n}\nexport class AccountSalesQuotesComponent {\n  constructor(accountservice, settingsservice, router, route) {\n    this.accountservice = accountservice;\n    this.settingsservice = settingsservice;\n    this.router = router;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.totalRecords = 0;\n    this.loading = true;\n    this.allData = [];\n    this.QuoteData = [];\n    this.first = 0;\n    this.rows = 10;\n    this.currentPage = 1;\n    this.orderStatusesValue = {};\n    this.statuses = [];\n    this.orderValue = {};\n    this.orderType = '';\n    this.QuoteStatus = ['All'];\n    this.customer = {};\n    this.quoteDetail = null;\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.loadInitialData(response.customer.customer_id);\n      }\n    });\n    this.accountservice.fetchOrderStatuses({\n      'filters[type][$eq]': 'QUOTE_STATUS'\n    }).subscribe({\n      next: response => {\n        if (response?.data.length) {\n          this.QuoteStatus = response?.data.map(val => {\n            this.orderStatusesValue[val.description] = val.code;\n            this.orderValue[val.code] = val.description;\n            this.orderStatusesValue['All'] = this.orderStatusesValue['All'] ? `${this.orderStatusesValue['All']};${val.code}` : val.code;\n            return val.description;\n          });\n          this.QuoteStatus = ['All', ...this.QuoteStatus];\n        }\n      },\n      error: error => {\n        console.error('Error fetching order statuses:', error);\n      }\n    });\n  }\n  loadInitialData(soldToParty) {\n    forkJoin({\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\n      settings: this.settingsservice.getSettings()\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: ({\n        partnerFunction,\n        settings\n      }) => {\n        this.customer = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n        this.orderType = settings?.[0]?.sales_quote_type_code || '';\n        if (this.customer) {\n          this.fetchQuotes(1000);\n        }\n      },\n      error: error => {\n        console.error('Error fetching initial data:', error);\n      }\n    });\n  }\n  fetchQuotes(count) {\n    this.loading = true;\n    const rawParams = {\n      SOLDTO: this.customer?.customer_id,\n      VKORG: this.customer?.sales_organization,\n      COUNT: count,\n      DOC_STATUS: Object.keys(this.orderValue).join(';'),\n      DOC_TYPE: this.orderType\n    };\n    const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n    this.accountservice.fetchSalesquoteOrders(params).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response?.SALESQUOTES) {\n          this.QuoteData = response.SALESQUOTES.map(record => ({\n            SD_DOC: record?.SD_DOC || '-',\n            DOC_NAME: record?.DOC_NAME || '-',\n            DOC_TYPE: record?.DOC_TYPE || '-',\n            DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n            DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-'\n          }));\n          this.allData = [...this.QuoteData];\n          this.totalRecords = this.allData.length;\n          this.paginateData();\n        } else {\n          this.allData = [];\n          this.totalRecords = 0;\n          this.paginateData();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching sales quotes:', error);\n        this.loading = false;\n      }\n    });\n  }\n  onPageChange(event) {\n    this.first = event.first;\n    this.rows = event.rows;\n    this.currentPage = this.first / this.rows + 1;\n    if (this.first + this.rows >= this.allData.length && this.allData.length % 100 == 0) {\n      this.fetchQuotes(this.allData.length + 1000);\n    }\n    this.paginateData();\n  }\n  paginateData() {\n    this.QuoteData = this.allData.slice(this.first, this.first + this.rows);\n  }\n  navigateToQuoteDetail(quote) {\n    this.router.navigate([quote.SD_DOC], {\n      relativeTo: this.route,\n      state: {\n        quoteData: quote,\n        customerData: this.customer?.customer\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AccountSalesQuotesComponent_Factory(t) {\n      return new (t || AccountSalesQuotesComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.SettingsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSalesQuotesComponent,\n      selectors: [[\"app-account-sales-quotes\"]],\n      decls: 8,\n      vars: 4,\n      consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Create\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"SD_DOC\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"SD_DOC\"], [\"pSortableColumn\", \"DOC_NAME\"], [\"field\", \"DOC_NAME\"], [\"pSortableColumn\", \"DOC_STATUS\"], [\"field\", \"DOC_STATUS\"], [\"pSortableColumn\", \"DOC_DATE\"], [\"field\", \"DOC_DATE\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"colspan\", \"5\", 1, \"border-round-left-lg\"]],\n      template: function AccountSalesQuotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Sales Quotes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtemplate(6, AccountSalesQuotesComponent_div_6_Template, 2, 0, \"div\", 6)(7, AccountSalesQuotesComponent_p_table_7_Template, 6, 5, \"p-table\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgIf, i5.PrimeTemplate, i6.Table, i6.SortableColumn, i6.SortIcon, i7.Button, i8.ProgressSpinner, i4.DatePipe],\n      styles: [\".p-sidebar-header {\\n  display: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWNjb3VudC9hY2NvdW50LWRldGFpbHMvYWNjb3VudC1zYWxlcy1xdW90ZXMvYWNjb3VudC1zYWxlcy1xdW90ZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSx3QkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5wLXNpZGViYXItaGVhZGVyIHtcclxuICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "fork<PERSON><PERSON>n", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "AccountSalesQuotesComponent_p_table_7_ng_template_3_Template_tr_click_0_listener", "quote_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToQuoteDetail", "ɵɵadvance", "ɵɵtextInterpolate1", "SD_DOC", "DOC_NAME", "DOC_STATUS", "ɵɵpipeBind2", "DOC_DATE", "ɵɵtemplate", "AccountSalesQuotesComponent_p_table_7_ng_template_2_Template", "AccountSalesQuotesComponent_p_table_7_ng_template_3_Template", "AccountSalesQuotesComponent_p_table_7_ng_template_4_Template", "AccountSalesQuotesComponent_p_table_7_ng_template_5_Template", "ɵɵproperty", "QuoteData", "totalRecords", "AccountSalesQuotesComponent", "constructor", "accountservice", "settingsservice", "router", "route", "unsubscribe$", "loading", "allData", "first", "rows", "currentPage", "orderStatusesValue", "statuses", "orderValue", "orderType", "Quote<PERSON><PERSON><PERSON>", "customer", "quoteDetail", "ngOnInit", "account", "pipe", "subscribe", "response", "loadInitialData", "customer_id", "fetchOrderStatuses", "next", "data", "length", "map", "val", "description", "code", "error", "console", "soldToParty", "partnerFunction", "getPartnerFunction", "settings", "getSettings", "find", "o", "partner_function", "sales_quote_type_code", "fetchQuotes", "count", "rawParams", "SOLDTO", "VKORG", "sales_organization", "COUNT", "Object", "keys", "join", "DOC_TYPE", "params", "fromEntries", "entries", "filter", "_", "value", "undefined", "fetchSalesquoteOrders", "SALESQUOTES", "record", "substring", "paginateData", "onPageChange", "event", "slice", "quote", "navigate", "relativeTo", "state", "quoteData", "customerData", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "SettingsService", "i3", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "AccountSalesQuotesComponent_Template", "rf", "ctx", "AccountSalesQuotesComponent_div_6_Template", "AccountSalesQuotesComponent_p_table_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-quotes\\account-sales-quotes.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-quotes\\account-sales-quotes.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AccountService } from '../../account.service';\r\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\r\nimport { SettingsService } from 'src/app/store/services/setting.service';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\n\r\ninterface SalesQuoteData {\r\n  SD_DOC?: string;\r\n  DOC_NAME?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_DATE?: string;\r\n  DOC_STATUS?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-account-sales-quotes',\r\n  templateUrl: './account-sales-quotes.component.html',\r\n  styleUrl: './account-sales-quotes.component.scss',\r\n})\r\nexport class AccountSalesQuotesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public allData: SalesQuoteData[] = [];\r\n  public QuoteData: SalesQuoteData[] = [];\r\n  public first: number = 0;\r\n  public rows: number = 10;\r\n  public currentPage: number = 1;\r\n  public orderStatusesValue: any = {};\r\n  public statuses: any = [];\r\n  public orderValue: any = {};\r\n  public orderType: string = '';\r\n  public QuoteStatus: any[] = ['All'];\r\n  public customer: any = {};\r\n  public quoteDetail: any = null;\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private settingsservice: SettingsService,\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.loadInitialData(response.customer.customer_id);\r\n        }\r\n      });\r\n\r\n    this.accountservice\r\n      .fetchOrderStatuses({\r\n        'filters[type][$eq]': 'QUOTE_STATUS',\r\n      })\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data.length) {\r\n            this.QuoteStatus = response?.data.map((val: any) => {\r\n              this.orderStatusesValue[val.description] = val.code;\r\n              this.orderValue[val.code] = val.description;\r\n              this.orderStatusesValue['All'] = this.orderStatusesValue['All']\r\n                ? `${this.orderStatusesValue['All']};${val.code}`\r\n                : val.code;\r\n              return val.description;\r\n            });\r\n            this.QuoteStatus = ['All', ...this.QuoteStatus];\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching order statuses:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  loadInitialData(soldToParty: string) {\r\n    forkJoin({\r\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\r\n      settings: this.settingsservice.getSettings(),\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: ({ partnerFunction, settings }) => {\r\n          this.customer = partnerFunction.find(\r\n            (o: any) =>\r\n              o.customer_id === soldToParty && o.partner_function === 'SH'\r\n          );\r\n          this.orderType = settings?.[0]?.sales_quote_type_code || '';\r\n\r\n          if (this.customer) {\r\n            this.fetchQuotes(1000);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching initial data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchQuotes(count: number) {\r\n    this.loading = true;\r\n\r\n    const rawParams = {\r\n      SOLDTO: this.customer?.customer_id,\r\n      VKORG: this.customer?.sales_organization,\r\n      COUNT: count,\r\n      DOC_STATUS: Object.keys(this.orderValue).join(';'),\r\n      DOC_TYPE: this.orderType,\r\n    };\r\n\r\n    const params: any = Object.fromEntries(\r\n      Object.entries(rawParams).filter(\r\n        ([_, value]) => value !== undefined && value !== ''\r\n      )\r\n    );\r\n\r\n    this.accountservice\r\n      .fetchSalesquoteOrders(params)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response?.SALESQUOTES) {\r\n            this.QuoteData = response.SALESQUOTES.map((record) => ({\r\n              SD_DOC: record?.SD_DOC || '-',\r\n              DOC_NAME: record?.DOC_NAME || '-',\r\n              DOC_TYPE: record?.DOC_TYPE || '-',\r\n              DOC_STATUS: record.DOC_STATUS\r\n                ? this.orderValue[record.DOC_STATUS]\r\n                : '-',\r\n              DOC_DATE: record?.DOC_DATE\r\n                ? `${record.DOC_DATE.substring(\r\n                    0,\r\n                    4\r\n                  )}-${record.DOC_DATE.substring(\r\n                    4,\r\n                    6\r\n                  )}-${record.DOC_DATE.substring(6, 8)}`\r\n                : '-',\r\n            }));\r\n\r\n            this.allData = [...this.QuoteData];\r\n            this.totalRecords = this.allData.length;\r\n            this.paginateData();\r\n          } else {\r\n            this.allData = [];\r\n            this.totalRecords = 0;\r\n            this.paginateData();\r\n          }\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching sales quotes:', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onPageChange(event: any) {\r\n    this.first = event.first;\r\n    this.rows = event.rows;\r\n    this.currentPage = this.first / this.rows + 1;\r\n\r\n    if (\r\n      this.first + this.rows >= this.allData.length &&\r\n      this.allData.length % 100 == 0\r\n    ) {\r\n      this.fetchQuotes(this.allData.length + 1000);\r\n    }\r\n    this.paginateData();\r\n  }\r\n\r\n  paginateData() {\r\n    this.QuoteData = this.allData.slice(this.first, this.first + this.rows);\r\n  }\r\n\r\n  navigateToQuoteDetail(quote: any) {\r\n    this.router.navigate([quote.SD_DOC], {\r\n      relativeTo: this.route,\r\n      state: { quoteData: quote, customerData: this.customer?.customer },\r\n    });\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Quotes</h4>\r\n        <p-button label=\"Create\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"loading\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n        <p-table #dt1 *ngIf=\"!loading\" [value]=\"QuoteData\" dataKey=\"id\" [rows]=\"8\" [rowHover]=\"true\" [paginator]=\"true\"\r\n            [totalRecords]=\"totalRecords\" responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\" pSortableColumn=\"SD_DOC\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Quote #\r\n                            <p-sortIcon field=\"SD_DOC\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"DOC_NAME\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Quote Name\r\n                            <p-sortIcon field=\"DOC_NAME\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"DOC_STATUS\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Quote Status\r\n                            <p-sortIcon field=\"DOC_STATUS\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"DOC_DATE\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Date Placed\r\n                            <p-sortIcon field=\"DOC_DATE\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-quote>\r\n                <tr (click)=\"navigateToQuoteDetail(quote)\" class=\"cursor-pointer\">\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ quote?.SD_DOC }}\r\n                    </td>\r\n                    <td>\r\n                        {{ quote?.DOC_NAME }}\r\n                    </td>\r\n                    <td>\r\n                        {{ quote?.DOC_STATUS }}\r\n                    </td>\r\n                    <td>\r\n                        {{ quote?.DOC_DATE | date : \"MM/dd/yyyy\" }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg\">No quotes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg\">Loading quotes data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;ICM3CC,EAAA,CAAAC,cAAA,aAAwF;IACpFD,EAAA,CAAAE,SAAA,wBAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMUH,EAFR,CAAAC,cAAA,SAAI,aAC0D,cACX;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAAwC;IAEhDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAA+B,cACgB;IACvCD,EAAA,CAAAI,MAAA,mBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA0C;IAElDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAiC,eACc;IACvCD,EAAA,CAAAI,MAAA,sBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA4C;IAEpDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA+B,eACgB;IACvCD,EAAA,CAAAI,MAAA,qBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA0C;IAGtDF,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;;IAILH,EAAA,CAAAC,cAAA,aAAkE;IAA9DD,EAAA,CAAAK,UAAA,mBAAAC,iFAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,qBAAA,CAAAP,QAAA,CAA4B;IAAA,EAAC;IACtCP,EAAA,CAAAC,cAAA,aAAsF;IAClFD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IAXGH,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAU,MAAA,MACJ;IAEIjB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAW,QAAA,MACJ;IAEIlB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAY,UAAA,MACJ;IAEInB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAoB,WAAA,OAAAb,QAAA,kBAAAA,QAAA,CAAAc,QAAA,qBACJ;;;;;IAKArB,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAI,MAAA,uBAAgB;IACjEJ,EADiE,CAAAG,YAAA,EAAK,EACjE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAI,MAAA,wCAAiC;IAClFJ,EADkF,CAAAG,YAAA,EAAK,EAClF;;;;;IAvDbH,EAAA,CAAAC,cAAA,oBAC4D;IAmDxDD,EAlDA,CAAAsB,UAAA,IAAAC,4DAAA,2BAAgC,IAAAC,4DAAA,2BA6BQ,IAAAC,4DAAA,0BAgBF,IAAAC,4DAAA,0BAKD;IAKzC1B,EAAA,CAAAG,YAAA,EAAU;;;;IAxDNH,EAD2B,CAAA2B,UAAA,UAAAhB,MAAA,CAAAiB,SAAA,CAAmB,WAAwB,kBAAkB,mBAAmB,iBAAAjB,MAAA,CAAAkB,YAAA,CAC9E;;;ADOzC,OAAM,MAAOC,2BAA2B;EAiBtCC,YACUC,cAA8B,EAC9BC,eAAgC,EAChCC,MAAc,EACdC,KAAqB;IAHrB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IApBP,KAAAC,YAAY,GAAG,IAAIvC,OAAO,EAAQ;IACnC,KAAAgC,YAAY,GAAW,CAAC;IACxB,KAAAQ,OAAO,GAAY,IAAI;IACvB,KAAAC,OAAO,GAAqB,EAAE;IAC9B,KAAAV,SAAS,GAAqB,EAAE;IAChC,KAAAW,KAAK,GAAW,CAAC;IACjB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,WAAW,GAAU,CAAC,KAAK,CAAC;IAC5B,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,WAAW,GAAQ,IAAI;EAO3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACjB,cAAc,CAACkB,OAAO,CACxBC,IAAI,CAACrD,SAAS,CAAC,IAAI,CAACsC,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACC,eAAe,CAACD,QAAQ,CAACN,QAAQ,CAACQ,WAAW,CAAC;MACrD;IACF,CAAC,CAAC;IAEJ,IAAI,CAACvB,cAAc,CAChBwB,kBAAkB,CAAC;MAClB,oBAAoB,EAAE;KACvB,CAAC,CACDJ,SAAS,CAAC;MACTK,IAAI,EAAGJ,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEK,IAAI,CAACC,MAAM,EAAE;UACzB,IAAI,CAACb,WAAW,GAAGO,QAAQ,EAAEK,IAAI,CAACE,GAAG,CAAEC,GAAQ,IAAI;YACjD,IAAI,CAACnB,kBAAkB,CAACmB,GAAG,CAACC,WAAW,CAAC,GAAGD,GAAG,CAACE,IAAI;YACnD,IAAI,CAACnB,UAAU,CAACiB,GAAG,CAACE,IAAI,CAAC,GAAGF,GAAG,CAACC,WAAW;YAC3C,IAAI,CAACpB,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,KAAK,CAAC,GAC3D,GAAG,IAAI,CAACA,kBAAkB,CAAC,KAAK,CAAC,IAAImB,GAAG,CAACE,IAAI,EAAE,GAC/CF,GAAG,CAACE,IAAI;YACZ,OAAOF,GAAG,CAACC,WAAW;UACxB,CAAC,CAAC;UACF,IAAI,CAAChB,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,CAACA,WAAW,CAAC;QACjD;MACF,CAAC;MACDkB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACN;EAEAV,eAAeA,CAACY,WAAmB;IACjCnE,QAAQ,CAAC;MACPoE,eAAe,EAAE,IAAI,CAACnC,cAAc,CAACoC,kBAAkB,CAACF,WAAW,CAAC;MACpEG,QAAQ,EAAE,IAAI,CAACpC,eAAe,CAACqC,WAAW;KAC3C,CAAC,CACCnB,IAAI,CAACrD,SAAS,CAAC,IAAI,CAACsC,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACTK,IAAI,EAAEA,CAAC;QAAEU,eAAe;QAAEE;MAAQ,CAAE,KAAI;QACtC,IAAI,CAACtB,QAAQ,GAAGoB,eAAe,CAACI,IAAI,CACjCC,CAAM,IACLA,CAAC,CAACjB,WAAW,KAAKW,WAAW,IAAIM,CAAC,CAACC,gBAAgB,KAAK,IAAI,CAC/D;QACD,IAAI,CAAC5B,SAAS,GAAGwB,QAAQ,GAAG,CAAC,CAAC,EAAEK,qBAAqB,IAAI,EAAE;QAE3D,IAAI,IAAI,CAAC3B,QAAQ,EAAE;UACjB,IAAI,CAAC4B,WAAW,CAAC,IAAI,CAAC;QACxB;MACF,CAAC;MACDX,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACN;EAEAW,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACvC,OAAO,GAAG,IAAI;IAEnB,MAAMwC,SAAS,GAAG;MAChBC,MAAM,EAAE,IAAI,CAAC/B,QAAQ,EAAEQ,WAAW;MAClCwB,KAAK,EAAE,IAAI,CAAChC,QAAQ,EAAEiC,kBAAkB;MACxCC,KAAK,EAAEL,KAAK;MACZzD,UAAU,EAAE+D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvC,UAAU,CAAC,CAACwC,IAAI,CAAC,GAAG,CAAC;MAClDC,QAAQ,EAAE,IAAI,CAACxC;KAChB;IAED,MAAMyC,MAAM,GAAQJ,MAAM,CAACK,WAAW,CACpCL,MAAM,CAACM,OAAO,CAACX,SAAS,CAAC,CAACY,MAAM,CAC9B,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,CACpD,CACF;IAED,IAAI,CAAC3D,cAAc,CAChB6D,qBAAqB,CAACP,MAAM,CAAC,CAC7BnC,IAAI,CAACrD,SAAS,CAAC,IAAI,CAACsC,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACTK,IAAI,EAAGJ,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAEyC,WAAW,EAAE;UACzB,IAAI,CAAClE,SAAS,GAAGyB,QAAQ,CAACyC,WAAW,CAAClC,GAAG,CAAEmC,MAAM,KAAM;YACrD9E,MAAM,EAAE8E,MAAM,EAAE9E,MAAM,IAAI,GAAG;YAC7BC,QAAQ,EAAE6E,MAAM,EAAE7E,QAAQ,IAAI,GAAG;YACjCmE,QAAQ,EAAEU,MAAM,EAAEV,QAAQ,IAAI,GAAG;YACjClE,UAAU,EAAE4E,MAAM,CAAC5E,UAAU,GACzB,IAAI,CAACyB,UAAU,CAACmD,MAAM,CAAC5E,UAAU,CAAC,GAClC,GAAG;YACPE,QAAQ,EAAE0E,MAAM,EAAE1E,QAAQ,GACtB,GAAG0E,MAAM,CAAC1E,QAAQ,CAAC2E,SAAS,CAC1B,CAAC,EACD,CAAC,CACF,IAAID,MAAM,CAAC1E,QAAQ,CAAC2E,SAAS,CAC5B,CAAC,EACD,CAAC,CACF,IAAID,MAAM,CAAC1E,QAAQ,CAAC2E,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACtC;WACL,CAAC,CAAC;UAEH,IAAI,CAAC1D,OAAO,GAAG,CAAC,GAAG,IAAI,CAACV,SAAS,CAAC;UAClC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACS,OAAO,CAACqB,MAAM;UACvC,IAAI,CAACsC,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAAC3D,OAAO,GAAG,EAAE;UACjB,IAAI,CAACT,YAAY,GAAG,CAAC;UACrB,IAAI,CAACoE,YAAY,EAAE;QACrB;QACA,IAAI,CAAC5D,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA6D,YAAYA,CAACC,KAAU;IACrB,IAAI,CAAC5D,KAAK,GAAG4D,KAAK,CAAC5D,KAAK;IACxB,IAAI,CAACC,IAAI,GAAG2D,KAAK,CAAC3D,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,KAAK,GAAG,IAAI,CAACC,IAAI,GAAG,CAAC;IAE7C,IACE,IAAI,CAACD,KAAK,GAAG,IAAI,CAACC,IAAI,IAAI,IAAI,CAACF,OAAO,CAACqB,MAAM,IAC7C,IAAI,CAACrB,OAAO,CAACqB,MAAM,GAAG,GAAG,IAAI,CAAC,EAC9B;MACA,IAAI,CAACgB,WAAW,CAAC,IAAI,CAACrC,OAAO,CAACqB,MAAM,GAAG,IAAI,CAAC;IAC9C;IACA,IAAI,CAACsC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACrE,SAAS,GAAG,IAAI,CAACU,OAAO,CAAC8D,KAAK,CAAC,IAAI,CAAC7D,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,IAAI,CAAC;EACzE;EAEA1B,qBAAqBA,CAACuF,KAAU;IAC9B,IAAI,CAACnE,MAAM,CAACoE,QAAQ,CAAC,CAACD,KAAK,CAACpF,MAAM,CAAC,EAAE;MACnCsF,UAAU,EAAE,IAAI,CAACpE,KAAK;MACtBqE,KAAK,EAAE;QAAEC,SAAS,EAAEJ,KAAK;QAAEK,YAAY,EAAE,IAAI,CAAC3D,QAAQ,EAAEA;MAAQ;KACjE,CAAC;EACJ;;;uBAlKWjB,2BAA2B,EAAA9B,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAA3BpF,2BAA2B;MAAAqF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBhCzH,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAI,MAAA,mBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAE,SAAA,kBAC2D;UAC/DF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UAInBD,EAHA,CAAAsB,UAAA,IAAAqG,0CAAA,iBAAwF,IAAAC,8CAAA,qBAI5B;UA0DpE5H,EADI,CAAAG,YAAA,EAAM,EACJ;;;UAlEMH,EAAA,CAAAe,SAAA,GAAmC;UAACf,EAApC,CAAA2B,UAAA,oCAAmC,iBAAiB;UAIiB3B,EAAA,CAAAe,SAAA,GAAa;UAAbf,EAAA,CAAA2B,UAAA,SAAA+F,GAAA,CAAArF,OAAA,CAAa;UAGvErC,EAAA,CAAAe,SAAA,EAAc;UAAdf,EAAA,CAAA2B,UAAA,UAAA+F,GAAA,CAAArF,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}