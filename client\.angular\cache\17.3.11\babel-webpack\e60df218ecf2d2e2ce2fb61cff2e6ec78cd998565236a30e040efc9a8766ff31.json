{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport { StoreComponent } from './store.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StoreComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),\n      resolve: {\n        content: contentResolver\n      },\n      data: {\n        breadcrumb: 'Home',\n        slug: 'home'\n      }\n    }, {\n      path: 'profile',\n      data: {\n        breadcrumb: 'Profile'\n      },\n      loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n    }, {\n      path: 'dashboard',\n      data: {\n        breadcrumb: 'Dashboard'\n      },\n      loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule)\n    }, {\n      path: 'prospects',\n      data: {\n        breadcrumb: 'Prospects'\n      },\n      loadChildren: () => import('./prospects/prospects.module').then(m => m.ProspectsModule)\n    }, {\n      path: 'account',\n      data: {\n        breadcrumb: 'Account'\n      },\n      loadChildren: () => import('./account/account.module').then(m => m.AccountModule)\n    }, {\n      path: 'contacts',\n      data: {\n        breadcrumb: 'Contacts'\n      },\n      loadChildren: () => import('./contacts/contacts.module').then(m => m.ContactsModule)\n    }, {\n      path: 'activities',\n      data: {\n        breadcrumb: 'Activities'\n      },\n      loadChildren: () => import('./activities/activities.module').then(m => m.ActivitiesModule)\n    }, {\n      path: 'opportunities',\n      data: {\n        breadcrumb: 'Opportunities'\n      },\n      loadChildren: () => import('./opportunities/opportunities.module').then(m => m.OpportunitiesModule)\n    }, {\n      path: 'ai-insights',\n      data: {\n        breadcrumb: 'AI Insights'\n      },\n      loadChildren: () => import('./ai-insights/ai-insights.module').then(m => m.AiInsightsModule)\n    }, {\n      path: 'sales-quotes',\n      data: {\n        breadcrumb: 'Sales Quotes'\n      },\n      loadChildren: () => import('./sales-quotes/sales-quotes.module').then(m => m.SalesQuotesModule)\n    }, {\n      path: 'sales-orders',\n      data: {\n        breadcrumb: 'Sales Orders'\n      },\n      loadChildren: () => import('./sales-orders/sales-orders.module').then(m => m.SalesOrdersModule)\n    }, {\n      path: 'service-ticket-details',\n      data: {\n        breadcrumb: 'Service Ticket Details'\n      },\n      loadChildren: () => import('./service-tickets/service-tickets.module').then(m => m.ServiceTicketsModule)\n    }, {\n      path: 'service-tickets',\n      data: {\n        breadcrumb: 'Service Tickets'\n      },\n      loadChildren: () => import('./service-tickets-listing/service-tickets-listing.module').then(m => m.ServiceTicketsListingModule)\n    }, {\n      path: 'identify-account',\n      data: {\n        breadcrumb: 'Identify Account'\n      },\n      loadChildren: () => import('./identify-account/identify-account.module').then(m => m.IdentifyAccountModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class StoreRoutingModule {\n  static {\n    this.ɵfac = function StoreRoutingModule_Factory(t) {\n      return new (t || StoreRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StoreRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoreRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppLayoutComponent", "StoreComponent", "contentResolver", "routes", "path", "component", "children", "loadChildren", "then", "m", "HomeModule", "resolve", "content", "data", "breadcrumb", "slug", "ProfileModule", "DashboardModule", "ProspectsModule", "AccountModule", "ContactsModule", "ActivitiesModule", "OpportunitiesModule", "AiInsightsModule", "SalesQuotesModule", "SalesOrdersModule", "ServiceTicketsModule", "ServiceTicketsListingModule", "IdentifyAccountModule", "redirectTo", "StoreRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\nimport { StoreComponent } from './store.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: StoreComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            loadChildren: () =>\r\n              import('./home/<USER>').then((m) => m.HomeModule),\r\n            resolve: {\r\n              content: contentResolver,\r\n            },\r\n            data: {\r\n              breadcrumb: 'Home',\r\n              slug: 'home',\r\n            },\r\n          },\r\n          {\r\n            path: 'profile',\r\n            data: { breadcrumb: 'Profile' },\r\n            loadChildren: () =>\r\n              import('./profile/profile.module').then((m) => m.ProfileModule),\r\n          },\r\n          {\r\n            path: 'dashboard',\r\n            data: { breadcrumb: 'Dashboard' },\r\n            loadChildren: () =>\r\n              import('./dashboard/dashboard.module').then((m) => m.DashboardModule),\r\n          },\r\n          {\r\n            path: 'prospects',\r\n            data: { breadcrumb: 'Prospects' },\r\n            loadChildren: () =>\r\n              import('./prospects/prospects.module').then((m) => m.ProspectsModule),\r\n          },\r\n          {\r\n            path: 'account',\r\n            data: { breadcrumb: 'Account' },\r\n            loadChildren: () =>\r\n              import('./account/account.module').then((m) => m.AccountModule),\r\n          },\r\n          {\r\n            path: 'contacts',\r\n            data: { breadcrumb: 'Contacts' },\r\n            loadChildren: () =>\r\n              import('./contacts/contacts.module').then((m) => m.ContactsModule),\r\n          },\r\n          {\r\n            path: 'activities',\r\n            data: { breadcrumb: 'Activities' },\r\n            loadChildren: () =>\r\n              import('./activities/activities.module').then((m) => m.ActivitiesModule),\r\n          },\r\n          {\r\n            path: 'opportunities',\r\n            data: { breadcrumb: 'Opportunities' },\r\n            loadChildren: () =>\r\n              import('./opportunities/opportunities.module').then((m) => m.OpportunitiesModule),\r\n          },\r\n          {\r\n            path: 'ai-insights',\r\n            data: { breadcrumb: 'AI Insights' },\r\n            loadChildren: () =>\r\n              import('./ai-insights/ai-insights.module').then((m) => m.AiInsightsModule),\r\n          },\r\n          {\r\n            path: 'sales-quotes',\r\n            data: { breadcrumb: 'Sales Quotes' },\r\n            loadChildren: () =>\r\n              import('./sales-quotes/sales-quotes.module').then((m) => m.SalesQuotesModule),\r\n          },\r\n          {\r\n            path: 'sales-orders',\r\n            data: { breadcrumb: 'Sales Orders' },\r\n            loadChildren: () =>\r\n              import('./sales-orders/sales-orders.module').then((m) => m.SalesOrdersModule),\r\n          },\r\n          {\r\n            path: 'service-ticket-details',\r\n            data: { breadcrumb: 'Service Ticket Details' },\r\n            loadChildren: () =>\r\n              import('./service-tickets/service-tickets.module').then((m) => m.ServiceTicketsModule),\r\n          },\r\n          {\r\n            path: 'service-tickets',\r\n            data: { breadcrumb: 'Service Tickets' },\r\n            loadChildren: () =>\r\n              import('./service-tickets-listing/service-tickets-listing.module').then((m) => m.ServiceTicketsListingModule),\r\n          },\r\n          {\r\n            path: 'identify-account',\r\n            data: { breadcrumb: 'Identify Account' },\r\n            loadChildren: () =>\r\n              import('./identify-account/identify-account.module').then((m) => m.IdentifyAccountModule),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class StoreRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,cAAc;EACzBK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEL,kBAAkB;IAC7BM,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC;MACxDC,OAAO,EAAE;QACPC,OAAO,EAAEV;OACV;MACDW,IAAI,EAAE;QACJC,UAAU,EAAE,MAAM;QAClBC,IAAI,EAAE;;KAET,EACD;MACEX,IAAI,EAAE,SAAS;MACfS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAC/BP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,aAAa;KACjE,EACD;MACEZ,IAAI,EAAE,WAAW;MACjBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,eAAe;KACvE,EACD;MACEb,IAAI,EAAE,WAAW;MACjBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,eAAe;KACvE,EACD;MACEd,IAAI,EAAE,SAAS;MACfS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAC/BP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,aAAa;KACjE,EACD;MACEf,IAAI,EAAE,UAAU;MAChBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACW,cAAc;KACpE,EACD;MACEhB,IAAI,EAAE,YAAY;MAClBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAY,CAAE;MAClCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACY,gBAAgB;KAC1E,EACD;MACEjB,IAAI,EAAE,eAAe;MACrBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAe,CAAE;MACrCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACa,mBAAmB;KACnF,EACD;MACElB,IAAI,EAAE,aAAa;MACnBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAa,CAAE;MACnCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACc,gBAAgB;KAC5E,EACD;MACEnB,IAAI,EAAE,cAAc;MACpBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAc,CAAE;MACpCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACe,iBAAiB;KAC/E,EACD;MACEpB,IAAI,EAAE,cAAc;MACpBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAc,CAAE;MACpCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACgB,iBAAiB;KAC/E,EACD;MACErB,IAAI,EAAE,wBAAwB;MAC9BS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAwB,CAAE;MAC9CP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACiB,oBAAoB;KACxF,EACD;MACEtB,IAAI,EAAE,iBAAiB;MACvBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0DAA0D,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACkB,2BAA2B;KAC/G,EACD;MACEvB,IAAI,EAAE,kBAAkB;MACxBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAkB,CAAE;MACxCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACmB,qBAAqB;KAC3F;GAEJ,EACD;IAAExB,IAAI,EAAE,IAAI;IAAEyB,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnB/B,YAAY,CAACgC,QAAQ,CAAC5B,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEX+B,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAAlC,YAAA;IAAAmC,OAAA,GAFnBnC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}