{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport Quill from 'quill';\nconst _c0 = [[[\"p-header\"]]];\nconst _c1 = [\"p-header\"];\nfunction Editor_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Editor_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Editor_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\nfunction Editor_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 5)(2, \"select\", 6)(3, \"option\", 7);\n    i0.ɵɵtext(4, \"Heading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 8);\n    i0.ɵɵtext(6, \"Subheading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 9);\n    i0.ɵɵtext(8, \"Normal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"select\", 10)(10, \"option\", 9);\n    i0.ɵɵtext(11, \"Sans Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 11);\n    i0.ɵɵtext(13, \"Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Monospace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"span\", 5);\n    i0.ɵɵelement(17, \"button\", 13)(18, \"button\", 14)(19, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 5);\n    i0.ɵɵelement(21, \"select\", 16)(22, \"select\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 5);\n    i0.ɵɵelement(24, \"button\", 18)(25, \"button\", 19);\n    i0.ɵɵelementStart(26, \"select\", 20);\n    i0.ɵɵelement(27, \"option\", 9);\n    i0.ɵɵelementStart(28, \"option\", 21);\n    i0.ɵɵtext(29, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 22);\n    i0.ɵɵtext(31, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 23);\n    i0.ɵɵtext(33, \"justify\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"span\", 5);\n    i0.ɵɵelement(35, \"button\", 24)(36, \"button\", 25)(37, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 5);\n    i0.ɵɵelement(39, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst EDITOR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Editor),\n  multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nclass Editor {\n  platformId;\n  el;\n  /**\n   * Inline style of the container.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the container.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Placeholder text to show when editor is empty.\n   * @group Props\n   */\n  placeholder;\n  /**\n   * Whitelist of formats to display, see here for available options.\n   * @group Props\n   */\n  formats;\n  /**\n   * Modules configuration of Editor, see here for available options.\n   * @group Props\n   */\n  modules;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n   * @group Props\n   */\n  bounds;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n   * @group Props\n   */\n  scrollingContainer;\n  /**\n   * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n   * @group Props\n   */\n  debug;\n  /**\n   * Whether to instantiate the editor to read-only mode.\n   * @group Props\n   */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(val) {\n    this._readonly = val;\n    // if (this.quill) {\n    //     if (this._readonly) this.quill.disable();\n    //     else this.quill.enable();\n    // }\n  }\n  /**\n   * Callback to invoke when the quill modules are loaded.\n   * @param {EditorInitEvent} event - custom event.\n   * @group Emits\n   */\n  onInit = new EventEmitter();\n  /**\n   * Callback to invoke when text of editor changes.\n   * @param {EditorTextChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onTextChange = new EventEmitter();\n  /**\n   * Callback to invoke when selection of the text changes.\n   * @param {EditorSelectionChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onSelectionChange = new EventEmitter();\n  templates;\n  toolbar;\n  value;\n  delayedCommand = null;\n  _readonly = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  quill;\n  headerTemplate;\n  get isAttachedQuillEditorToDOM() {\n    return this.quillElements?.editorElement?.isConnected;\n  }\n  quillElements;\n  constructor(platformId, el) {\n    this.platformId = platformId;\n    this.el = el;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.initQuillElements();\n      if (this.isAttachedQuillEditorToDOM) {\n        this.initQuillEditor();\n      }\n    }\n  }\n  ngAfterViewChecked() {\n    if (isPlatformBrowser(this.platformId)) {\n      // The problem is inside the `quill` library, we need to wait for a new release.\n      // Function `isLine` - used `getComputedStyle`, it was rewritten in the next release.\n      // (We need to wait for a release higher than 1.3.7).\n      // These checks and code can be removed.\n      if (!this.quill && this.isAttachedQuillEditorToDOM) {\n        this.initQuillEditor();\n      }\n      // Can also be deleted after updating `quill`.\n      if (this.delayedCommand && this.isAttachedQuillEditorToDOM) {\n        this.delayedCommand();\n        this.delayedCommand = null;\n      }\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n      }\n    });\n  }\n  writeValue(value) {\n    this.value = value;\n    if (this.quill) {\n      if (value) {\n        const command = () => {\n          this.quill.setContents(this.quill.clipboard.convert(this.value));\n        };\n        if (this.isAttachedQuillEditorToDOM) {\n          command();\n        } else {\n          this.delayedCommand = command;\n        }\n      } else {\n        const command = () => {\n          this.quill.setText('');\n        };\n        if (this.isAttachedQuillEditorToDOM) {\n          command();\n        } else {\n          this.delayedCommand = command;\n        }\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  getQuill() {\n    return this.quill;\n  }\n  initQuillEditor() {\n    this.initQuillElements();\n    const {\n      toolbarElement,\n      editorElement\n    } = this.quillElements;\n    let defaultModule = {\n      toolbar: toolbarElement\n    };\n    let modules = this.modules ? {\n      ...defaultModule,\n      ...this.modules\n    } : defaultModule;\n    this.quill = new Quill(editorElement, {\n      modules: modules,\n      placeholder: this.placeholder,\n      readOnly: this.readonly,\n      theme: 'snow',\n      formats: this.formats,\n      bounds: this.bounds,\n      debug: this.debug,\n      scrollingContainer: this.scrollingContainer\n    });\n    if (this.value) {\n      this.quill.setContents(this.quill.clipboard.convert(this.value));\n    }\n    this.quill.on('text-change', (delta, oldContents, source) => {\n      if (source === 'user') {\n        let html = DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n        let text = this.quill.getText().trim();\n        if (html === '<p><br></p>') {\n          html = null;\n        }\n        this.onTextChange.emit({\n          htmlValue: html,\n          textValue: text,\n          delta: delta,\n          source: source\n        });\n        this.onModelChange(html);\n        this.onModelTouched();\n      }\n    });\n    this.quill.on('selection-change', (range, oldRange, source) => {\n      this.onSelectionChange.emit({\n        range: range,\n        oldRange: oldRange,\n        source: source\n      });\n    });\n    this.onInit.emit({\n      editor: this.quill\n    });\n  }\n  initQuillElements() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.quillElements) {\n        this.quillElements = {\n          editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n          toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n        };\n      }\n    }\n  }\n  static ɵfac = function Editor_Factory(t) {\n    return new (t || Editor)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Editor,\n    selectors: [[\"p-editor\"]],\n    contentQueries: function Editor_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.toolbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      placeholder: \"placeholder\",\n      formats: \"formats\",\n      modules: \"modules\",\n      bounds: \"bounds\",\n      scrollingContainer: \"scrollingContainer\",\n      debug: \"debug\",\n      readonly: \"readonly\"\n    },\n    outputs: {\n      onInit: \"onInit\",\n      onTextChange: \"onTextChange\",\n      onSelectionChange: \"onSelectionChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([EDITOR_VALUE_ACCESSOR])],\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 6,\n    consts: [[3, \"ngClass\"], [\"class\", \"p-editor-toolbar\", 4, \"ngIf\"], [1, \"p-editor-content\", 3, \"ngStyle\"], [1, \"p-editor-toolbar\"], [4, \"ngTemplateOutlet\"], [1, \"ql-formats\"], [1, \"ql-header\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"selected\", \"\"], [1, \"ql-font\"], [\"value\", \"serif\"], [\"value\", \"monospace\"], [\"aria-label\", \"Bold\", \"type\", \"button\", 1, \"ql-bold\"], [\"aria-label\", \"Italic\", \"type\", \"button\", 1, \"ql-italic\"], [\"aria-label\", \"Underline\", \"type\", \"button\", 1, \"ql-underline\"], [1, \"ql-color\"], [1, \"ql-background\"], [\"value\", \"ordered\", \"aria-label\", \"Ordered List\", \"type\", \"button\", 1, \"ql-list\"], [\"value\", \"bullet\", \"aria-label\", \"Unordered List\", \"type\", \"button\", 1, \"ql-list\"], [1, \"ql-align\"], [\"value\", \"center\"], [\"value\", \"right\"], [\"value\", \"justify\"], [\"aria-label\", \"Insert Link\", \"type\", \"button\", 1, \"ql-link\"], [\"aria-label\", \"Insert Image\", \"type\", \"button\", 1, \"ql-image\"], [\"aria-label\", \"Insert Code Block\", \"type\", \"button\", 1, \"ql-code-block\"], [\"aria-label\", \"Remove Styles\", \"type\", \"button\", 1, \"ql-clean\"]],\n    template: function Editor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, Editor_div_1_Template, 3, 1, \"div\", 1)(2, Editor_div_2_Template, 40, 0, \"div\", 1);\n        i0.ɵɵelement(3, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-editor-container\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.toolbar || ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.toolbar && !ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", ctx.style);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n    styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Editor, [{\n    type: Component,\n    args: [{\n      selector: 'p-editor',\n      template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `,\n      providers: [EDITOR_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    formats: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    scrollingContainer: [{\n      type: Input\n    }],\n    debug: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    onInit: [{\n      type: Output\n    }],\n    onTextChange: [{\n      type: Output\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    toolbar: [{\n      type: ContentChild,\n      args: [Header]\n    }]\n  });\n})();\nclass EditorModule {\n  static ɵfac = function EditorModule_Factory(t) {\n    return new (t || EditorModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: EditorModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EditorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Editor, SharedModule],\n      declarations: [Editor]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "ContentChild", "NgModule", "i1", "isPlatformBrowser", "CommonModule", "Header", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "NG_VALUE_ACCESSOR", "<PERSON><PERSON><PERSON>", "_c0", "_c1", "Editor_div_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainer", "Editor_div_1_Template", "ɵɵelementStart", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "headerTemplate", "Editor_div_2_Template", "ɵɵtext", "ɵɵelement", "EDITOR_VALUE_ACCESSOR", "provide", "useExisting", "Editor", "multi", "platformId", "el", "style", "styleClass", "placeholder", "formats", "modules", "bounds", "scrollingContainer", "debug", "readonly", "_readonly", "val", "onInit", "onTextChange", "onSelectionChange", "templates", "toolbar", "value", "delayedCommand", "onModelChange", "onModelTouched", "quill", "isAttachedQuillEditorToDOM", "quillElements", "editor<PERSON><PERSON>", "isConnected", "constructor", "ngAfterViewInit", "initQuillElements", "initQuillEditor", "ngAfterViewChecked", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "writeValue", "command", "setContents", "clipboard", "convert", "setText", "registerOnChange", "fn", "registerOnTouched", "getQuill", "toolbarElement", "defaultModule", "readOnly", "theme", "on", "delta", "oldContents", "source", "html", "findSingle", "innerHTML", "text", "getText", "trim", "emit", "htmlValue", "textValue", "range", "oldRange", "editor", "nativeElement", "ɵfac", "Editor_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Editor_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "Editor_Template", "ɵɵprojectionDef", "ɵɵclassMap", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "OnPush", "None", "host", "class", "undefined", "decorators", "EditorModule", "EditorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-editor.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport Quill from 'quill';\n\nconst EDITOR_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Editor),\n    multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nclass Editor {\n    platformId;\n    el;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Placeholder text to show when editor is empty.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Whitelist of formats to display, see here for available options.\n     * @group Props\n     */\n    formats;\n    /**\n     * Modules configuration of Editor, see here for available options.\n     * @group Props\n     */\n    modules;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n     * @group Props\n     */\n    bounds;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n     * @group Props\n     */\n    scrollingContainer;\n    /**\n     * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n     * @group Props\n     */\n    debug;\n    /**\n     * Whether to instantiate the editor to read-only mode.\n     * @group Props\n     */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(val) {\n        this._readonly = val;\n        // if (this.quill) {\n        //     if (this._readonly) this.quill.disable();\n        //     else this.quill.enable();\n        // }\n    }\n    /**\n     * Callback to invoke when the quill modules are loaded.\n     * @param {EditorInitEvent} event - custom event.\n     * @group Emits\n     */\n    onInit = new EventEmitter();\n    /**\n     * Callback to invoke when text of editor changes.\n     * @param {EditorTextChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onTextChange = new EventEmitter();\n    /**\n     * Callback to invoke when selection of the text changes.\n     * @param {EditorSelectionChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onSelectionChange = new EventEmitter();\n    templates;\n    toolbar;\n    value;\n    delayedCommand = null;\n    _readonly = false;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    quill;\n    headerTemplate;\n    get isAttachedQuillEditorToDOM() {\n        return this.quillElements?.editorElement?.isConnected;\n    }\n    quillElements;\n    constructor(platformId, el) {\n        this.platformId = platformId;\n        this.el = el;\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.initQuillElements();\n            if (this.isAttachedQuillEditorToDOM) {\n                this.initQuillEditor();\n            }\n        }\n    }\n    ngAfterViewChecked() {\n        if (isPlatformBrowser(this.platformId)) {\n            // The problem is inside the `quill` library, we need to wait for a new release.\n            // Function `isLine` - used `getComputedStyle`, it was rewritten in the next release.\n            // (We need to wait for a release higher than 1.3.7).\n            // These checks and code can be removed.\n            if (!this.quill && this.isAttachedQuillEditorToDOM) {\n                this.initQuillEditor();\n            }\n            // Can also be deleted after updating `quill`.\n            if (this.delayedCommand && this.isAttachedQuillEditorToDOM) {\n                this.delayedCommand();\n                this.delayedCommand = null;\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    writeValue(value) {\n        this.value = value;\n        if (this.quill) {\n            if (value) {\n                const command = () => {\n                    this.quill.setContents(this.quill.clipboard.convert(this.value));\n                };\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                }\n                else {\n                    this.delayedCommand = command;\n                }\n            }\n            else {\n                const command = () => {\n                    this.quill.setText('');\n                };\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                }\n                else {\n                    this.delayedCommand = command;\n                }\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    getQuill() {\n        return this.quill;\n    }\n    initQuillEditor() {\n        this.initQuillElements();\n        const { toolbarElement, editorElement } = this.quillElements;\n        let defaultModule = { toolbar: toolbarElement };\n        let modules = this.modules ? { ...defaultModule, ...this.modules } : defaultModule;\n        this.quill = new Quill(editorElement, {\n            modules: modules,\n            placeholder: this.placeholder,\n            readOnly: this.readonly,\n            theme: 'snow',\n            formats: this.formats,\n            bounds: this.bounds,\n            debug: this.debug,\n            scrollingContainer: this.scrollingContainer\n        });\n        if (this.value) {\n            this.quill.setContents(this.quill.clipboard.convert(this.value));\n        }\n        this.quill.on('text-change', (delta, oldContents, source) => {\n            if (source === 'user') {\n                let html = DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n                let text = this.quill.getText().trim();\n                if (html === '<p><br></p>') {\n                    html = null;\n                }\n                this.onTextChange.emit({\n                    htmlValue: html,\n                    textValue: text,\n                    delta: delta,\n                    source: source\n                });\n                this.onModelChange(html);\n                this.onModelTouched();\n            }\n        });\n        this.quill.on('selection-change', (range, oldRange, source) => {\n            this.onSelectionChange.emit({\n                range: range,\n                oldRange: oldRange,\n                source: source\n            });\n        });\n        this.onInit.emit({\n            editor: this.quill\n        });\n    }\n    initQuillElements() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.quillElements) {\n                this.quillElements = {\n                    editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n                    toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n                };\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Editor, deps: [{ token: PLATFORM_ID }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Editor, selector: \"p-editor\", inputs: { style: \"style\", styleClass: \"styleClass\", placeholder: \"placeholder\", formats: \"formats\", modules: \"modules\", bounds: \"bounds\", scrollingContainer: \"scrollingContainer\", debug: \"debug\", readonly: \"readonly\" }, outputs: { onInit: \"onInit\", onTextChange: \"onTextChange\", onSelectionChange: \"onSelectionChange\" }, host: { classAttribute: \"p-element\" }, providers: [EDITOR_VALUE_ACCESSOR], queries: [{ propertyName: \"toolbar\", first: true, predicate: Header, descendants: true }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `, isInline: true, styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Editor, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-editor', template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `, providers: [EDITOR_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], formats: [{\n                type: Input\n            }], modules: [{\n                type: Input\n            }], bounds: [{\n                type: Input\n            }], scrollingContainer: [{\n                type: Input\n            }], debug: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], onInit: [{\n                type: Output\n            }], onTextChange: [{\n                type: Output\n            }], onSelectionChange: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], toolbar: [{\n                type: ContentChild,\n                args: [Header]\n            }] } });\nclass EditorModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: EditorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: EditorModule, declarations: [Editor], imports: [CommonModule], exports: [Editor, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: EditorModule, imports: [CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: EditorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Editor, SharedModule],\n                    declarations: [Editor]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC5L,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACjE,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoOmEzB,EAAE,CAAA2B,kBAAA,EAKhB,CAAC;EAAA;AAAA;AAAA,SAAAC,sBAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALazB,EAAE,CAAA6B,cAAA,YAGpB,CAAC;IAHiB7B,EAAE,CAAA8B,YAAA,EAIrC,CAAC;IAJkC9B,EAAE,CAAA+B,UAAA,IAAAP,oCAAA,yBAK/B,CAAC;IAL4BxB,EAAE,CAAAgC,YAAA,CAM9E,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAN2EjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAAmC,SAAA,EAKjC,CAAC;IAL8BnC,EAAE,CAAAoC,UAAA,qBAAAH,MAAA,CAAAI,cAKjC,CAAC;EAAA;AAAA;AAAA,SAAAC,sBAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAL8BzB,EAAE,CAAA6B,cAAA,YAOlB,CAAC,aACtC,CAAC,eACI,CAAC,eACL,CAAC;IAVmD7B,EAAE,CAAAuC,MAAA,aAU/C,CAAC;IAV4CvC,EAAE,CAAAgC,YAAA,CAUtC,CAAC;IAVmChC,EAAE,CAAA6B,cAAA,eAWtD,CAAC;IAXmD7B,EAAE,CAAAuC,MAAA,gBAW5C,CAAC;IAXyCvC,EAAE,CAAAgC,YAAA,CAWnC,CAAC;IAXgChC,EAAE,CAAA6B,cAAA,eAYvD,CAAC;IAZoD7B,EAAE,CAAAuC,MAAA,YAYjD,CAAC;IAZ8CvC,EAAE,CAAAgC,YAAA,CAYxC,CAAC,CAC5B,CAAC;IAbgEhC,EAAE,CAAA6B,cAAA,gBAcpD,CAAC,gBACJ,CAAC;IAfoD7B,EAAE,CAAAuC,MAAA,iBAe7C,CAAC;IAf0CvC,EAAE,CAAAgC,YAAA,CAepC,CAAC;IAfiChC,EAAE,CAAA6B,cAAA,iBAgBlD,CAAC;IAhB+C7B,EAAE,CAAAuC,MAAA,YAgB7C,CAAC;IAhB0CvC,EAAE,CAAAgC,YAAA,CAgBpC,CAAC;IAhBiChC,EAAE,CAAA6B,cAAA,iBAiB9C,CAAC;IAjB2C7B,EAAE,CAAAuC,MAAA,gBAiBrC,CAAC;IAjBkCvC,EAAE,CAAAgC,YAAA,CAiB5B,CAAC,CACxC,CAAC,CACP,CAAC;IAnBsEhC,EAAE,CAAA6B,cAAA,cAoBvD,CAAC;IApBoD7B,EAAE,CAAAwC,SAAA,iBAqBX,CAAC,iBACG,CAAC,iBACK,CAAC;IAvBFxC,EAAE,CAAAgC,YAAA,CAwBzE,CAAC;IAxBsEhC,EAAE,CAAA6B,cAAA,cAyBvD,CAAC;IAzBoD7B,EAAE,CAAAwC,SAAA,iBA0B1C,CAAC,iBACI,CAAC;IA3BkCxC,EAAE,CAAAgC,YAAA,CA4BzE,CAAC;IA5BsEhC,EAAE,CAAA6B,cAAA,cA6BvD,CAAC;IA7BoD7B,EAAE,CAAAwC,SAAA,iBA8Ba,CAAC,iBACA,CAAC;IA/BjBxC,EAAE,CAAA6B,cAAA,iBAgCnD,CAAC;IAhCgD7B,EAAE,CAAAwC,SAAA,gBAiC9C,CAAC;IAjC2CxC,EAAE,CAAA6B,cAAA,iBAkCjD,CAAC;IAlC8C7B,EAAE,CAAAuC,MAAA,aAkC3C,CAAC;IAlCwCvC,EAAE,CAAAgC,YAAA,CAkClC,CAAC;IAlC+BhC,EAAE,CAAA6B,cAAA,iBAmClD,CAAC;IAnC+C7B,EAAE,CAAAuC,MAAA,YAmC7C,CAAC;IAnC0CvC,EAAE,CAAAgC,YAAA,CAmCpC,CAAC;IAnCiChC,EAAE,CAAA6B,cAAA,iBAoChD,CAAC;IApC6C7B,EAAE,CAAAuC,MAAA,cAoCzC,CAAC;IApCsCvC,EAAE,CAAAgC,YAAA,CAoChC,CAAC,CACpC,CAAC,CACP,CAAC;IAtCsEhC,EAAE,CAAA6B,cAAA,cAuCvD,CAAC;IAvCoD7B,EAAE,CAAAwC,SAAA,iBAwCJ,CAAC,iBACC,CAAC,iBACS,CAAC;IA1CXxC,EAAE,CAAAgC,YAAA,CA2CzE,CAAC;IA3CsEhC,EAAE,CAAA6B,cAAA,cA4CvD,CAAC;IA5CoD7B,EAAE,CAAAwC,SAAA,iBA6CD,CAAC;IA7CFxC,EAAE,CAAAgC,YAAA,CA8CzE,CAAC,CACN,CAAC;EAAA;AAAA;AAjRlB,MAAMS,qBAAqB,GAAG;EAC1BC,OAAO,EAAEtB,iBAAiB;EAC1BuB,WAAW,EAAE1C,UAAU,CAAC,MAAM2C,MAAM,CAAC;EACrCC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,MAAM,CAAC;EACTE,UAAU;EACVC,EAAE;EACF;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,kBAAkB;EAClB;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,GAAG,EAAE;IACd,IAAI,CAACD,SAAS,GAAGC,GAAG;IACpB;IACA;IACA;IACA;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAM,GAAG,IAAIzD,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI0D,YAAY,GAAG,IAAI1D,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACI2D,iBAAiB,GAAG,IAAI3D,YAAY,CAAC,CAAC;EACtC4D,SAAS;EACTC,OAAO;EACPC,KAAK;EACLC,cAAc,GAAG,IAAI;EACrBR,SAAS,GAAG,KAAK;EACjBS,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC1BC,KAAK;EACL/B,cAAc;EACd,IAAIgC,0BAA0BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACC,aAAa,EAAEC,aAAa,EAAEC,WAAW;EACzD;EACAF,aAAa;EACbG,WAAWA,CAAC3B,UAAU,EAAEC,EAAE,EAAE;IACxB,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;EAChB;EACA2B,eAAeA,CAAA,EAAG;IACd,IAAI5D,iBAAiB,CAAC,IAAI,CAACgC,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC6B,iBAAiB,CAAC,CAAC;MACxB,IAAI,IAAI,CAACN,0BAA0B,EAAE;QACjC,IAAI,CAACO,eAAe,CAAC,CAAC;MAC1B;IACJ;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI/D,iBAAiB,CAAC,IAAI,CAACgC,UAAU,CAAC,EAAE;MACpC;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACsB,KAAK,IAAI,IAAI,CAACC,0BAA0B,EAAE;QAChD,IAAI,CAACO,eAAe,CAAC,CAAC;MAC1B;MACA;MACA,IAAI,IAAI,CAACX,cAAc,IAAI,IAAI,CAACI,0BAA0B,EAAE;QACxD,IAAI,CAACJ,cAAc,CAAC,CAAC;QACrB,IAAI,CAACA,cAAc,GAAG,IAAI;MAC9B;IACJ;EACJ;EACAa,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAChB,SAAS,CAACiB,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,QAAQ;UACT,IAAI,CAAC5C,cAAc,GAAG2C,IAAI,CAACE,QAAQ;UACnC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,UAAUA,CAACnB,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,IAAI,CAACI,KAAK,EAAE;MACZ,IAAIJ,KAAK,EAAE;QACP,MAAMoB,OAAO,GAAGA,CAAA,KAAM;UAClB,IAAI,CAAChB,KAAK,CAACiB,WAAW,CAAC,IAAI,CAACjB,KAAK,CAACkB,SAAS,CAACC,OAAO,CAAC,IAAI,CAACvB,KAAK,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,IAAI,CAACK,0BAA0B,EAAE;UACjCe,OAAO,CAAC,CAAC;QACb,CAAC,MACI;UACD,IAAI,CAACnB,cAAc,GAAGmB,OAAO;QACjC;MACJ,CAAC,MACI;QACD,MAAMA,OAAO,GAAGA,CAAA,KAAM;UAClB,IAAI,CAAChB,KAAK,CAACoB,OAAO,CAAC,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAACnB,0BAA0B,EAAE;UACjCe,OAAO,CAAC,CAAC;QACb,CAAC,MACI;UACD,IAAI,CAACnB,cAAc,GAAGmB,OAAO;QACjC;MACJ;IACJ;EACJ;EACAK,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACxB,aAAa,GAAGwB,EAAE;EAC3B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACvB,cAAc,GAAGuB,EAAE;EAC5B;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,KAAK;EACrB;EACAQ,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,iBAAiB,CAAC,CAAC;IACxB,MAAM;MAAEkB,cAAc;MAAEtB;IAAc,CAAC,GAAG,IAAI,CAACD,aAAa;IAC5D,IAAIwB,aAAa,GAAG;MAAE/B,OAAO,EAAE8B;IAAe,CAAC;IAC/C,IAAIzC,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG;MAAE,GAAG0C,aAAa;MAAE,GAAG,IAAI,CAAC1C;IAAQ,CAAC,GAAG0C,aAAa;IAClF,IAAI,CAAC1B,KAAK,GAAG,IAAI/C,KAAK,CAACkD,aAAa,EAAE;MAClCnB,OAAO,EAAEA,OAAO;MAChBF,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B6C,QAAQ,EAAE,IAAI,CAACvC,QAAQ;MACvBwC,KAAK,EAAE,MAAM;MACb7C,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBD,kBAAkB,EAAE,IAAI,CAACA;IAC7B,CAAC,CAAC;IACF,IAAI,IAAI,CAACU,KAAK,EAAE;MACZ,IAAI,CAACI,KAAK,CAACiB,WAAW,CAAC,IAAI,CAACjB,KAAK,CAACkB,SAAS,CAACC,OAAO,CAAC,IAAI,CAACvB,KAAK,CAAC,CAAC;IACpE;IACA,IAAI,CAACI,KAAK,CAAC6B,EAAE,CAAC,aAAa,EAAE,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,KAAK;MACzD,IAAIA,MAAM,KAAK,MAAM,EAAE;QACnB,IAAIC,IAAI,GAAGlF,UAAU,CAACmF,UAAU,CAAC/B,aAAa,EAAE,YAAY,CAAC,CAACgC,SAAS;QACvE,IAAIC,IAAI,GAAG,IAAI,CAACpC,KAAK,CAACqC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QACtC,IAAIL,IAAI,KAAK,aAAa,EAAE;UACxBA,IAAI,GAAG,IAAI;QACf;QACA,IAAI,CAACzC,YAAY,CAAC+C,IAAI,CAAC;UACnBC,SAAS,EAAEP,IAAI;UACfQ,SAAS,EAAEL,IAAI;UACfN,KAAK,EAAEA,KAAK;UACZE,MAAM,EAAEA;QACZ,CAAC,CAAC;QACF,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAAC;QACxB,IAAI,CAAClC,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,CAAC6B,EAAE,CAAC,kBAAkB,EAAE,CAACa,KAAK,EAAEC,QAAQ,EAAEX,MAAM,KAAK;MAC3D,IAAI,CAACvC,iBAAiB,CAAC8C,IAAI,CAAC;QACxBG,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA,QAAQ;QAClBX,MAAM,EAAEA;MACZ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACzC,MAAM,CAACgD,IAAI,CAAC;MACbK,MAAM,EAAE,IAAI,CAAC5C;IACjB,CAAC,CAAC;EACN;EACAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI7D,iBAAiB,CAAC,IAAI,CAACgC,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACwB,aAAa,EAAE;QACrB,IAAI,CAACA,aAAa,GAAG;UACjBC,aAAa,EAAEpD,UAAU,CAACmF,UAAU,CAAC,IAAI,CAACvD,EAAE,CAACkE,aAAa,EAAE,sBAAsB,CAAC;UACnFpB,cAAc,EAAE1E,UAAU,CAACmF,UAAU,CAAC,IAAI,CAACvD,EAAE,CAACkE,aAAa,EAAE,sBAAsB;QACvF,CAAC;MACL;IACJ;EACJ;EACA,OAAOC,IAAI,YAAAC,eAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFxE,MAAM,EAAhB5C,EAAE,CAAAqH,iBAAA,CAAgClH,WAAW,GAA7CH,EAAE,CAAAqH,iBAAA,CAAwDrH,EAAE,CAACsH,UAAU;EAAA;EAChK,OAAOC,IAAI,kBAD8EvH,EAAE,CAAAwH,iBAAA;IAAAC,IAAA,EACJ7E,MAAM;IAAA8E,SAAA;IAAAC,cAAA,WAAAC,sBAAAnG,EAAA,EAAAC,GAAA,EAAAmG,QAAA;MAAA,IAAApG,EAAA;QADJzB,EAAE,CAAA8H,cAAA,CAAAD,QAAA,EACme7G,MAAM;QAD3ehB,EAAE,CAAA8H,cAAA,CAAAD,QAAA,EACwiB5G,aAAa;MAAA;MAAA,IAAAQ,EAAA;QAAA,IAAAsG,EAAA;QADvjB/H,EAAE,CAAAgI,cAAA,CAAAD,EAAA,GAAF/H,EAAE,CAAAiI,WAAA,QAAAvG,GAAA,CAAAqC,OAAA,GAAAgE,EAAA,CAAAG,KAAA;QAAFlI,EAAE,CAAAgI,cAAA,CAAAD,EAAA,GAAF/H,EAAE,CAAAiI,WAAA,QAAAvG,GAAA,CAAAoC,SAAA,GAAAiE,EAAA;MAAA;IAAA;IAAAI,SAAA;IAAAC,MAAA;MAAApF,KAAA;MAAAC,UAAA;MAAAC,WAAA;MAAAC,OAAA;MAAAC,OAAA;MAAAC,MAAA;MAAAC,kBAAA;MAAAC,KAAA;MAAAC,QAAA;IAAA;IAAA6E,OAAA;MAAA1E,MAAA;MAAAC,YAAA;MAAAC,iBAAA;IAAA;IAAAyE,QAAA,GAAFtI,EAAE,CAAAuI,kBAAA,CAC6Y,CAAC9F,qBAAqB,CAAC;IAAA+F,kBAAA,EAAAjH,GAAA;IAAAkH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAzD,QAAA,WAAA0D,gBAAAnH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADtazB,EAAE,CAAA6I,eAAA,CAAAvH,GAAA;QAAFtB,EAAE,CAAA6B,cAAA,YAE7B,CAAC;QAF0B7B,EAAE,CAAA+B,UAAA,IAAAH,qBAAA,gBAGpB,CAAC,IAAAU,qBAAA,iBAIC,CAAC;QAPetC,EAAE,CAAAwC,SAAA,YAgD9B,CAAC;QAhD2BxC,EAAE,CAAAgC,YAAA,CAiDlF,CAAC;MAAA;MAAA,IAAAP,EAAA;QAjD+EzB,EAAE,CAAA8I,UAAA,CAAApH,GAAA,CAAAuB,UAE9B,CAAC;QAF2BjD,EAAE,CAAAoC,UAAA,gCAEnD,CAAC;QAFgDpC,EAAE,CAAAmC,SAAA,CAGtB,CAAC;QAHmBnC,EAAE,CAAAoC,UAAA,SAAAV,GAAA,CAAAqC,OAAA,IAAArC,GAAA,CAAAW,cAGtB,CAAC;QAHmBrC,EAAE,CAAAmC,SAAA,CAOpB,CAAC;QAPiBnC,EAAE,CAAAoC,UAAA,UAAAV,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAAW,cAOpB,CAAC;QAPiBrC,EAAE,CAAAmC,SAAA,CAgDrC,CAAC;QAhDkCnC,EAAE,CAAAoC,UAAA,YAAAV,GAAA,CAAAsB,KAgDrC,CAAC;MAAA;IAAA;IAAA+F,YAAA,GAEqJlI,EAAE,CAACmI,OAAO,EAAoFnI,EAAE,CAACoI,IAAI,EAA6FpI,EAAE,CAACqI,gBAAgB,EAAoJrI,EAAE,CAACsI,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACnkB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApD6FvJ,EAAE,CAAAwJ,iBAAA,CAoDJ5G,MAAM,EAAc,CAAC;IACpG6E,IAAI,EAAErH,SAAS;IACfqJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAExE,QAAQ,EAAG;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEyE,SAAS,EAAE,CAAClH,qBAAqB,CAAC;MAAE6G,eAAe,EAAEjJ,uBAAuB,CAACuJ,MAAM;MAAEP,aAAa,EAAE/I,iBAAiB,CAACuJ,IAAI;MAAEC,IAAI,EAAE;QACjHC,KAAK,EAAE;MACX,CAAC;MAAEX,MAAM,EAAE,CAAC,mIAAmI;IAAE,CAAC;EAC9J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3B,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxC,IAAI,EAAElH,MAAM;MACZkJ,IAAI,EAAE,CAACtJ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEsH,IAAI,EAAEzH,EAAE,CAACsH;EAAW,CAAC,CAAC,EAAkB;IAAEtE,KAAK,EAAE,CAAC;MAC1DyE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAEyC,UAAU,EAAE,CAAC;MACbwE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE0C,WAAW,EAAE,CAAC;MACduE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE2C,OAAO,EAAE,CAAC;MACVsE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE4C,OAAO,EAAE,CAAC;MACVqE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE6C,MAAM,EAAE,CAAC;MACToE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE8C,kBAAkB,EAAE,CAAC;MACrBmE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAE+C,KAAK,EAAE,CAAC;MACRkE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAEgD,QAAQ,EAAE,CAAC;MACXiE,IAAI,EAAEjH;IACV,CAAC,CAAC;IAAEmD,MAAM,EAAE,CAAC;MACT8D,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEmD,YAAY,EAAE,CAAC;MACf6D,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEoD,iBAAiB,EAAE,CAAC;MACpB4D,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEqD,SAAS,EAAE,CAAC;MACZ2D,IAAI,EAAE/G,eAAe;MACrB+I,IAAI,EAAE,CAACxI,aAAa;IACxB,CAAC,CAAC;IAAE8C,OAAO,EAAE,CAAC;MACV0D,IAAI,EAAE9G,YAAY;MAClB8I,IAAI,EAAE,CAACzI,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkJ,YAAY,CAAC;EACf,OAAOhD,IAAI,YAAAiD,qBAAA/C,CAAA;IAAA,YAAAA,CAAA,IAAwF8C,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBA9I8EpK,EAAE,CAAAqK,gBAAA;IAAA5C,IAAA,EA8ISyC;EAAY;EAChH,OAAOI,IAAI,kBA/I8EtK,EAAE,CAAAuK,gBAAA;IAAAC,OAAA,GA+IiCzJ,YAAY,EAAEG,YAAY;EAAA;AAC1J;AACA;EAAA,QAAAqI,SAAA,oBAAAA,SAAA,KAjJ6FvJ,EAAE,CAAAwJ,iBAAA,CAiJJU,YAAY,EAAc,CAAC;IAC1GzC,IAAI,EAAE7G,QAAQ;IACd6I,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAACzJ,YAAY,CAAC;MACvB0J,OAAO,EAAE,CAAC7H,MAAM,EAAE1B,YAAY,CAAC;MAC/BwJ,YAAY,EAAE,CAAC9H,MAAM;IACzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,qBAAqB,EAAEG,MAAM,EAAEsH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}