import { Component, OnInit,ViewChild } from '@angular/core';
import { AccountService } from '../../account.service';
import { ProspectsService } from 'src/app/store/prospects/prospects.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  Subject,
  takeUntil,
  Observable,
  concat,
  map,
  of,
  forkJoin,
} from 'rxjs';
import { MessageService } from 'primeng/api';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
} from 'rxjs/operators';
import { Country, State } from 'country-state-city';
import { CountryWiseMobileComponent } from 'src/app/store/common-form/country-wise-mobile/country-wise-mobile.component';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-account-contacts',
  templateUrl: './account-contacts.component.html',
  styleUrl: './account-contacts.component.scss',
})
export class AccountContactsComponent implements OnInit {
  @ViewChild(CountryWiseMobileComponent)
    countryMobileComponent!: CountryWiseMobileComponent;
  private unsubscribe$ = new Subject<void>();
  public contactDetails: any[] = [];
  public id: string = '';
  public departments: any = null;
  public functions: any = null;
  public addDialogVisible: boolean = false;
  public existingDialogVisible: boolean = false;
  public visible: boolean = false;
  public position: string = 'right';
  public submitted = false;
  public editid: string = '';
  public documentId: string = '';
  public saving = false;
  public cpDepartments: { name: string; value: string }[] = [];
  public cpFunctions: { name: string; value: string }[] = [];
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public selectedContacts = [];
  public countries: any[] = [];
  public selectedCountry: string = '';
  public phoneValidationMessage: string | null = null;
  public mobileValidationMessage: string | null = null;

  // Pagination properties
  public loading: boolean = false;
  public totalRecords: number = 0;
  public first: number = 0;
  public rows: number = 10;
  

  public ContactForm: FormGroup = this.formBuilder.group({
    first_name: ['', [Validators.required]],
    middle_name: [''],
    last_name: ['', [Validators.required]],
    job_title: [''],
    contact_person_function_name: [''],
    contact_person_department_name: [''],
    destination_location_country: ['', [Validators.required]],
    email_address: ['', [Validators.required, Validators.email]],
    phone_number: ['', [Validators.pattern(/^[\d\+\-\s]*$/)]],
    mobile: ['', [Validators.required, Validators.pattern(/^[\d\+\-\s]*$/)]],
    contact_person_vip_type: [''],
    validity_end_date: [''],
    contactexisting: [null],
  });

  public selectedCountryForMobile: string =
  this.ContactForm.get('destination_location_country')?.value;

  constructor(
    private accountservice: AccountService,
    private prospectsservice: ProspectsService,
    private formBuilder: FormBuilder,
    private messageservice: MessageService
  ) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'job_title', header: 'Job Title' },
    { field: 'phone_number', header: 'Phone' },
    { field: 'mobile', header: 'Mobile' },
    { field: 'email_address', header: 'E-Mail' },
    { field: 'contact_person_function_name.name', header: 'Function' },
    { field: 'contact_person_department_name.name', header: 'Department' },
    { field: 'web_registered', header: 'Web Registered' },
    { field: 'vip_contact', header: 'VIP Contact' },
    { field: 'deactivate', header: 'Deactivate' },
    { field: 'communication_preference', header: 'Comm. Preference' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.contactDetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit(): void {
    this.ContactForm.get('destination_location_country')?.valueChanges.subscribe((countryCode) => {
      this.selectedCountryForMobile = countryCode;
    });
    this.loadContacts();
    this.loadCountries();
    forkJoin({
      departments: this.accountservice.getCPDepartment(),
      functions: this.accountservice.getCPFunction(),
    })
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ departments, functions }) => {
        // Load departments
        this.cpDepartments = (departments?.data || []).map((item: any) => ({
          name: item.description,
          value: item.code,
        }));

        // Load functions
        this.cpFunctions = (functions?.data || []).map((item: any) => ({
          name: item.description,
          value: item.code,
        }));
        this.accountservice.account
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe((response: any) => {
            if (response) {
              this.id = response?.bp_id;
              this.documentId = response?.documentId;
              this.contactDetails = response?.contact_companies || [];

              this.contactDetails = this.contactDetails.map((contact: any) => {
                return {
                  ...contact,
                  full_name: [
                    contact?.business_partner_person?.first_name,
                    contact?.business_partner_person?.middle_name,
                    contact?.business_partner_person?.last_name,
                  ]
                    .filter(Boolean)
                    .join(' '),

                  first_name:
                    contact?.business_partner_person?.first_name || '',
                  middle_name:
                    contact?.business_partner_person?.middle_name || '',
                  last_name: contact?.business_partner_person?.last_name || '',
                  email_address:
                    contact?.business_partner_person?.addresses?.[0]
                      ?.emails?.[0]?.email_address || '',
                  destination_location_country: (
                    contact?.business_partner_person?.addresses?.[0]
                      ?.phone_numbers || []
                  ).find((item: any) => item.phone_number_type === '3')
                    ?.destination_location_country,
                  country_phone_number: (
                    contact?.business_partner_person?.addresses?.[0]
                      ?.phone_numbers || []
                  ).find((item: any) => item.phone_number_type === '1')
                    ?.phone_number,
                  phone_number: (() => {
                    const phoneList =
                      contact?.business_partner_person?.addresses?.[0]
                        ?.phone_numbers ?? [];
                    const mobilePhone = phoneList.find(
                      (p: any) => p.phone_number_type === '1'
                    );
                    const countryCode =
                      mobilePhone?.destination_location_country;
                    const rawNumber = mobilePhone?.phone_number;
                    if (!rawNumber) {
                      return '-';
                    }
                    return this.prospectsservice.getDialCode(
                      countryCode,
                      rawNumber
                    );
                  })(),
                  country_mobile: (
                    contact?.business_partner_person?.addresses?.[0]
                      ?.phone_numbers || []
                  ).find((item: any) => item.phone_number_type === '3')
                    ?.phone_number,
                  mobile: (() => {
                    const phoneList =
                      contact?.business_partner_person?.addresses?.[0]
                        ?.phone_numbers ?? [];
                    const mobilePhone = phoneList.find(
                      (p: any) => p.phone_number_type === '3'
                    );
                    const countryCode =
                      mobilePhone?.destination_location_country;
                    const rawNumber = mobilePhone?.phone_number;
                    if (!rawNumber) {
                      return '-';
                    }
                    return this.prospectsservice.getDialCode(
                      countryCode,
                      rawNumber
                    );
                  })(),

                  // Ensure department & function values are set correctly
                  contact_person_department_name:
                    this.cpDepartments?.find(
                      (d: any) =>
                        d.value ===
                        contact?.person_func_and_dept?.contact_person_department
                    ) || null, // Default value if not found

                  contact_person_function_name:
                    this.cpFunctions?.find(
                      (f: any) =>
                        f.value ===
                        contact?.person_func_and_dept?.contact_person_function
                    ) || null, // Default value if not found
                  job_title:
                    contact?.business_partner_person?.bp_extension?.job_title ||
                    '',
                  contact_person_vip_type: contact?.person_func_and_dept
                    ?.contact_person_vip_type
                    ? true
                    : false,
                  web_registered: contact?.business_partner_person?.bp_extension
                    ?.web_registered
                    ? true
                    : false,
                  communication_preference:
                    contact?.business_partner_person?.addresses?.[0]
                      ?.prfrd_comm_medium_type || '-',
                  validity_end_date:
                    new Date().toISOString().split('T')[0] <
                    contact?.validity_end_date?.split('T')[0]
                      ? false
                      : true,
                };
              });

              // Initialize pagination after data is loaded
              this.paginateContacts();
            }
          });
      });

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }

  onLazyLoad(event: any) {
    this.loading = true;
    this.first = event.first;
    this.rows = event.rows;

    // Since the data is already loaded from the account service,
    // we just need to paginate the existing contactDetails array
    this.paginateContacts();
  }

  private paginateContacts() {
    // Set total records to the full length of contactDetails
    this.totalRecords = this.contactDetails.length;
    this.loading = false;
  }

  public reactivateSelectedContacts() {
    if (!this.selectedContacts || this.selectedContacts.length === 0) {
      return;
    }
    const reactivateRequests = this.selectedContacts.map((contact) =>
      this.accountservice.updateReactivate(contact).toPromise()
    );
    Promise.all(reactivateRequests)
      .then(() => {
        this.messageservice.add({
          severity: 'success',
          detail: 'Contacts Reactivated successfully!.',
        });
        this.accountservice
          .getAccountByID(this.documentId)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe(() => {
            // Refresh pagination after data update
            this.paginateContacts();
          });
        this.selectedContacts = [];
      })
      .catch((error) => {
        this.messageservice.add({
          severity: 'error',
          detail: 'Error during bulk update :' + error,
        });
      });
  }

  private loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  triggerMobileValidation() {
    this.countryMobileComponent.validatePhone();
  }

  private loadContacts() {
    this.contacts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.contactInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.contactLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq]`]: 'BUP001',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
          }

          return this.accountservice.getContacts(params).pipe(
            map((data: any) => {
              return data || []; // Make sure to return correct data structure
            }),
            tap(() => (this.contactLoading = false)),
            catchError((error) => {
              this.contactLoading = false;
              return of([]);
            })
          );
        })
      )
    );
  }

  editContact(contact: any) {
    this.addDialogVisible = true;
    this.editid = contact?.documentId;

    this.ContactForm.patchValue({
      first_name: contact.first_name,
      middle_name: contact.middle_name,
      last_name: contact.last_name,
      job_title: contact.job_title,
      email_address: contact.email_address,
      phone_number: contact.country_phone_number,
      mobile: contact.country_mobile,
      destination_location_country: contact.destination_location_country,
      validity_end_date: contact.validity_end_date,
      contact_person_vip_type: contact.contact_person_vip_type,
      contactexisting: '',

      // Ensure department & function are set correctly
      contact_person_function_name:
        this.cpFunctions.find(
          (f) => f.value === contact?.contact_person_function_name?.value
        ) || null,
      contact_person_department_name:
        this.cpDepartments.find(
          (d) => d.value === contact?.contact_person_department_name?.value
        ) || null,
    });
  }

  async onSubmit() {
    this.submitted = true;
    this.visible = true;
    if (this.ContactForm.value?.contactexisting) {
      const existing = this.ContactForm.value.contactexisting;

      const data = {
        bp_person_id: existing?.bp_id,
        bp_id: this.id,
      };

      this.saving = true;

      this.accountservice
        .createExistingContact(data)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe({
          complete: () => {
            this.saving = false;
            this.existingDialogVisible = false;
            this.ContactForm.reset();
            this.messageservice.add({
              severity: 'success',
              detail: 'Contact Added successfully!.',
            });
            this.accountservice
              .getAccountByID(this.documentId)
              .pipe(takeUntil(this.unsubscribe$))
              .subscribe(() => {
                // Refresh pagination after data update
                this.paginateContacts();
              });
          },
          error: () => {
            this.saving = false;
            this.addDialogVisible = false;
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          },
        });

      // Skip rest of logic for new contact
      return;
    }

    if (this.ContactForm.invalid) {
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.ContactForm.value };

    const selectedcodewisecountry = this.countries.find(
      (c) => c.isoCode === this.selectedCountry
    );

    const data = {
      bp_id: this.id,
      first_name: value?.first_name || '',
      middle_name: value?.middle_name,
      last_name: value?.last_name || '',
      job_title: value?.job_title || '',
      contact_person_function_name:
        value?.contact_person_function_name?.name || '',
      contact_person_function: value?.contact_person_function_name?.value || '',
      contact_person_department_name:
        value?.contact_person_department_name?.name || '',
      contact_person_department:
        value?.contact_person_department_name?.value || '',
      destination_location_country: selectedcodewisecountry?.isoCode,
      email_address: value?.email_address,
      phone_number: value?.phone_number,
      mobile: value?.mobile,
      contact_person_vip_type: value?.contact_person_vip_type,
      validity_end_date: value?.validity_end_date
        ? new Date().toISOString().split('T')[0]
        : '9999-12-29',
    };

    if (this.editid) {
      this.accountservice
        .updateContact(this.editid, data)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe({
          complete: () => {
            this.saving = false;
            this.addDialogVisible = false;
            this.ContactForm.reset();
            this.messageservice.add({
              severity: 'success',
              detail: 'Contact Updated successfully!.',
            });
            this.accountservice
              .getAccountByID(this.documentId)
              .pipe(takeUntil(this.unsubscribe$))
              .subscribe(() => {
                // Refresh pagination after data update
                this.paginateContacts();
              });
          },
          error: (res: any) => {
            this.saving = false;
            this.addDialogVisible = false;
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          },
        });
    } else {
      this.accountservice
        .createContact(data)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe({
          complete: () => {
            this.saving = false;
            this.addDialogVisible = false;
            this.existingDialogVisible = false;
            this.ContactForm.reset();
            this.messageservice.add({
              severity: 'success',
              detail: 'Contact created successfully!.',
            });
            this.accountservice
              .getAccountByID(this.documentId)
              .pipe(takeUntil(this.unsubscribe$))
              .subscribe(() => {
                // Refresh pagination after data update
                this.paginateContacts();
              });
          },
          error: (res: any) => {
            this.saving = false;
            this.addDialogVisible = false;
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          },
        });
    }
  }

  showNewDialog(position: string) {
    this.position = position;
    this.addDialogVisible = true;
    this.submitted = false;
    this.ContactForm.reset();
  }

  showExistingDialog(position: string) {
    this.position = position;
    this.existingDialogVisible = true;
  }

  get f(): any {
    return this.ContactForm.controls;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
