{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of, forkJoin } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/account/account.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/tooltip\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/checkbox\";\nimport * as i15 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"38rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  width: \"50rem\"\n});\nfunction OpportunitiesContactsComponent_ng_template_10_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 54);\n    i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_ng_template_10_ng_container_8_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_4_Template, 1, 1, \"i\", 48)(5, OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_5_Template, 1, 0, \"i\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 45);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 46);\n    i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_ng_template_10_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"business_partner.bp_full_name\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 47);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵtemplate(6, OpportunitiesContactsComponent_ng_template_10_i_6_Template, 1, 1, \"i\", 48)(7, OpportunitiesContactsComponent_ng_template_10_i_7_Template, 1, 0, \"i\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, OpportunitiesContactsComponent_ng_template_10_ng_container_8_Template, 6, 4, \"ng-container\", 50);\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 51);\n    i0.ɵɵtext(11, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.contact_person_function_name == null ? null : contact_r6.contact_person_function_name.name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.phone_number) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.mobile) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.email_address) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 62);\n    i0.ɵɵtemplate(3, OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_3_Template, 2, 1, \"ng-container\", 63)(4, OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_4_Template, 2, 1, \"ng-container\", 63)(5, OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_5_Template, 2, 1, \"ng-container\", 63)(6, OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_6_Template, 2, 1, \"ng-container\", 63);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_person_department_name.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 55)(1, \"td\", 56);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 58)(4, \"div\", 59)(5, \"a\", 60);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, OpportunitiesContactsComponent_ng_template_11_ng_container_7_Template, 7, 5, \"ng-container\", 50);\n    i0.ɵɵelementStart(8, \"td\")(9, \"div\", 51)(10, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_ng_template_11_Template_button_click_10_listener($event) {\n      const contact_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(contact_r6));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const contact_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (contact_r6 == null ? null : contact_r6.business_partner == null ? null : contact_r6.business_partner.contact_persons == null ? null : contact_r6.business_partner.contact_persons[0] == null ? null : contact_r6.business_partner.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.full_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 64);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 64);\n    i0.ɵɵtext(2, \" Loading contacts data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_26_div_1_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"first_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_43_div_1_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"last_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_74_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_74_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_74_div_1_Template, 2, 0, \"div\", 36)(2, OpportunitiesContactsComponent_div_74_div_2_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"email_address\"].errors && ctx_r1.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_82_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_82_div_1_Template, 2, 0, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.ContactForm.get(\"phone_number\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_92_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mobile is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_92_div_1_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"mobile\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_93_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Please enter a valid Mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_93_div_1_Template, 2, 0, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.ContactForm.get(\"mobile\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 15)(2, \"label\", 68)(3, \"span\", 17);\n    i0.ɵɵtext(4, \"remove_circle_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \"DeActivate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵelement(7, \"p-checkbox\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"label\", 70)(10, \"span\", 17);\n    i0.ɵɵtext(11, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \"VIP Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 19);\n    i0.ɵɵelement(14, \"p-checkbox\", 71);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_109_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_109_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.email, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_109_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.phone, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesContactsComponent_ng_template_109_span_2_Template, 2, 1, \"span\", 36)(3, OpportunitiesContactsComponent_ng_template_109_span_3_Template, 2, 1, \"span\", 36)(4, OpportunitiesContactsComponent_ng_template_109_span_4_Template, 2, 1, \"span\", 36);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.phone);\n  }\n}\nexport class OpportunitiesContactsComponent {\n  constructor(route, opportunitiesservice, accountservice, formBuilder, messageservice, confirmationservice) {\n    this.route = route;\n    this.opportunitiesservice = opportunitiesservice;\n    this.accountservice = accountservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.contactDetails = [];\n    this.id = '';\n    this.opportunity_id = '';\n    this.bp_id = '';\n    this.departments = null;\n    this.functions = null;\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.visible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.editid = '';\n    this.documentId = '';\n    this.saving = false;\n    this.cpDepartments = [];\n    this.cpFunctions = [];\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.selectedContacts = [];\n    this.ContactForm = this.formBuilder.group({\n      first_name: ['', [Validators.required]],\n      middle_name: [''],\n      last_name: ['', [Validators.required]],\n      job_title: [''],\n      contact_person_function_name: [''],\n      contact_person_department_name: [''],\n      email_address: ['', [Validators.required, Validators.email]],\n      phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      contact_person_vip_type: [''],\n      validity_end_date: [''],\n      contactexisting: ['']\n    });\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'contact_person_function_name.name',\n      header: 'Function'\n    }, {\n      field: 'contact_person_department_name.name',\n      header: 'Department'\n    }, {\n      field: 'phone_number',\n      header: 'Phone'\n    }, {\n      field: 'mobile',\n      header: 'Mobile'\n    }, {\n      field: 'email_address',\n      header: 'E-Mail'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.contactDetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.loadContacts();\n    forkJoin({\n      departments: this.accountservice.getCPDepartment(),\n      functions: this.accountservice.getCPFunction()\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n      departments,\n      functions\n    }) => {\n      // Load departments\n      this.cpDepartments = (departments?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      // Load functions\n      this.cpFunctions = (functions?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.id = response?.opportunity_id;\n          this.bp_id = response?.business_partner?.bp_id;\n          this.documentId = response?.documentId;\n          this.contactDetails = response?.opportunity_contact_parties || [];\n          this.contactDetails = this.contactDetails.map(contact => {\n            return {\n              ...contact,\n              full_name: contact?.business_partner?.bp_full_name || '',\n              email_address: contact?.business_partner?.addresses?.[0]?.emails?.[0]?.email_address || '',\n              phone_number: (contact?.business_partner?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number,\n              mobile: (contact?.business_partner?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '3')?.phone_number,\n              // Ensure department & function values are set correctly\n              contact_person_department_name: this.cpDepartments?.find(d => d.value === contact?.business_partner?.contact_person_func_and_depts?.[0]?.contact_person_department) || null,\n              contact_person_function_name: this.cpFunctions?.find(f => f.value === contact?.business_partner?.contact_person_func_and_depts?.[0]?.contact_person_function) || null\n            };\n          });\n        }\n      });\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.accountservice.getContacts(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.contactLoading = false), catchError(error => {\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.visible = true;\n      if (_this.ContactForm.value?.contactexisting) {\n        const existing = _this.ContactForm.value.contactexisting;\n        const data = {\n          opportunity_party_contact_party_id: existing?.bp_id,\n          opportunity_id: _this.opportunity_id,\n          role_code: ''\n        };\n        _this.saving = true;\n        _this.opportunitiesservice.createExistingContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.existingDialogVisible = false;\n            _this.ContactForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Contact Added successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n        // Skip rest of logic for new contact\n        return;\n      }\n      if (_this.ContactForm.invalid) {\n        console.log('Form is invalid:', _this.ContactForm.errors);\n        _this.visible = true;\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.ContactForm.value\n      };\n      const data = {\n        bp_id: _this.bp_id,\n        first_name: value?.first_name || '',\n        middle_name: value?.middle_name,\n        last_name: value?.last_name || '',\n        job_title: value?.job_title || '',\n        contact_person_function_name: value?.contact_person_function_name?.name || '',\n        contact_person_function: value?.contact_person_function_name?.value || '',\n        contact_person_department_name: value?.contact_person_department_name?.name || '',\n        contact_person_department: value?.contact_person_department_name?.value || '',\n        email_address: value?.email_address,\n        phone_number: value?.phone_number,\n        mobile: value?.mobile,\n        contact_person_vip_type: value?.contact_person_vip_type,\n        opportunity_party_contact_main_indicator: '',\n        role_code: '',\n        opportunity_id: _this.opportunity_id\n      };\n      _this.opportunitiesservice.createOpportunityContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        complete: () => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.existingDialogVisible = false;\n          _this.ContactForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Contact created successfully!.'\n          });\n          _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteContact(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.ContactForm.reset();\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  get f() {\n    return this.ContactForm.controls;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesContactsComponent_Factory(t) {\n      return new (t || OpportunitiesContactsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i5.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesContactsComponent,\n      selectors: [[\"app-opportunities-contacts\"]],\n      decls: 113,\n      vars: 61,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add New\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"selectionChange\", \"onColReorder\", \"value\", \"selection\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-contact-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"First Name \", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"first_name\", \"formControlName\", \"first_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [\"for\", \"Middle Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"middle_name\", \"formControlName\", \"middle_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Last Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"last_name\", \"formControlName\", \"last_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Job Title\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"job_title\", \"formControlName\", \"job_title\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Function\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_function_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Function\", 3, \"options\", \"styleClass\"], [\"for\", \"Department\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_department_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Department\", 3, \"options\", \"styleClass\"], [\"for\", \"Email\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"email\", \"id\", \"email_address\", \"formControlName\", \"email_address\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Phone\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"phone_number\", \"formControlName\", \"phone_number\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"for\", \"Mobile\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"mobile\", \"formControlName\", \"mobile\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"table-checkbox\", \"text-center\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"font-medium\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"p-error\"], [\"for\", \"DeActivate\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"validity_end_date\", \"formControlName\", \"validity_end_date\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"], [\"for\", \"VIP Contact\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"contact_person_vip_type\", \"formControlName\", \"contact_person_vip_type\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"]],\n      template: function OpportunitiesContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Contacts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_p_button_click_5_listener() {\n            return ctx.showNewDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_p_button_click_6_listener() {\n            return ctx.showExistingDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p-multiSelect\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesContactsComponent_Template_p_multiSelect_ngModelChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"p-table\", 8);\n          i0.ɵɵtwoWayListener(\"selectionChange\", function OpportunitiesContactsComponent_Template_p_table_selectionChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedContacts, $event) || (ctx.selectedContacts = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onColReorder\", function OpportunitiesContactsComponent_Template_p_table_onColReorder_9_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(10, OpportunitiesContactsComponent_ng_template_10_Template, 12, 3, \"ng-template\", 9)(11, OpportunitiesContactsComponent_ng_template_11_Template, 11, 4, \"ng-template\", 10)(12, OpportunitiesContactsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11)(13, OpportunitiesContactsComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"p-dialog\", 13);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(15, OpportunitiesContactsComponent_ng_template_15_Template, 2, 0, \"ng-template\", 9);\n          i0.ɵɵelementStart(16, \"form\", 14)(17, \"div\", 15)(18, \"label\", 16)(19, \"span\", 17);\n          i0.ɵɵtext(20, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \"First Name \");\n          i0.ɵɵelementStart(22, \"span\", 18);\n          i0.ɵɵtext(23, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵelement(25, \"input\", 20);\n          i0.ɵɵtemplate(26, OpportunitiesContactsComponent_div_26_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"label\", 22)(29, \"span\", 17);\n          i0.ɵɵtext(30, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \"Middle Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 15)(35, \"label\", 24)(36, \"span\", 17);\n          i0.ɵɵtext(37, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \"Last Name \");\n          i0.ɵɵelementStart(39, \"span\", 18);\n          i0.ɵɵtext(40, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 19);\n          i0.ɵɵelement(42, \"input\", 25);\n          i0.ɵɵtemplate(43, OpportunitiesContactsComponent_div_43_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"label\", 26)(46, \"span\", 17);\n          i0.ɵɵtext(47, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \"Job Title \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 19);\n          i0.ɵɵelement(50, \"input\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 15)(52, \"label\", 28)(53, \"span\", 17);\n          i0.ɵɵtext(54, \"functions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \"Function \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 19);\n          i0.ɵɵelement(57, \"p-dropdown\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 15)(59, \"label\", 30)(60, \"span\", 17);\n          i0.ɵɵtext(61, \"inbox_text_person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \"Department \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 19);\n          i0.ɵɵelement(64, \"p-dropdown\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 15)(66, \"label\", 32)(67, \"span\", 17);\n          i0.ɵɵtext(68, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \"Email\");\n          i0.ɵɵelementStart(70, \"span\", 18);\n          i0.ɵɵtext(71, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 19);\n          i0.ɵɵelement(73, \"input\", 33);\n          i0.ɵɵtemplate(74, OpportunitiesContactsComponent_div_74_Template, 3, 2, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 15)(76, \"label\", 34)(77, \"span\", 17);\n          i0.ɵɵtext(78, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(79, \"Phone \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 19);\n          i0.ɵɵelement(81, \"input\", 35);\n          i0.ɵɵtemplate(82, OpportunitiesContactsComponent_div_82_Template, 2, 1, \"div\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 15)(84, \"label\", 37)(85, \"span\", 17);\n          i0.ɵɵtext(86, \"smartphone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(87, \"Mobile # \");\n          i0.ɵɵelementStart(88, \"span\", 18);\n          i0.ɵɵtext(89, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"div\", 19);\n          i0.ɵɵelement(91, \"input\", 38);\n          i0.ɵɵtemplate(92, OpportunitiesContactsComponent_div_92_Template, 2, 1, \"div\", 21)(93, OpportunitiesContactsComponent_div_93_Template, 2, 1, \"div\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(94, OpportunitiesContactsComponent_div_94_Template, 15, 2, \"div\", 36);\n          i0.ɵɵelementStart(95, \"div\", 39)(96, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_96_listener() {\n            return ctx.addDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_97_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(98, \"p-dialog\", 13);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_98_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(99, OpportunitiesContactsComponent_ng_template_99_Template, 2, 0, \"ng-template\", 9);\n          i0.ɵɵelementStart(100, \"form\", 14)(101, \"div\", 15)(102, \"label\", 42)(103, \"span\", 17);\n          i0.ɵɵtext(104, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(105, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"div\", 19)(107, \"ng-select\", 43);\n          i0.ɵɵpipe(108, \"async\");\n          i0.ɵɵtemplate(109, OpportunitiesContactsComponent_ng_template_109_Template, 5, 4, \"ng-template\", 44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"div\", 39)(111, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_111_listener() {\n            return ctx.existingDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_112_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_30_0;\n          let tmp_33_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.contactDetails);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedContacts);\n          i0.ɵɵproperty(\"rows\", 14)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(51, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c1, ctx.submitted && ctx.f[\"first_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"first_name\"].errors);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c1, ctx.submitted && ctx.f[\"last_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"last_name\"].errors);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", ctx.cpFunctions)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"options\", ctx.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(56, _c1, ctx.submitted && ctx.f[\"email_address\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_30_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_30_0.touched) && ((tmp_30_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_30_0.invalid));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(58, _c1, ctx.submitted && ctx.f[\"mobile\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"mobile\"].errors);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_33_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_33_0.touched) && ((tmp_33_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_33_0.invalid));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editid && ctx.ContactForm.value.first_name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(60, _c2));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(108, 49, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.FormGroupDirective, i4.FormControlName, i7.Table, i5.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.Tooltip, i13.InputText, i14.Checkbox, i15.MultiSelect, i6.AsyncPipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .opportunity-contact-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1jb250YWN0cy9vcHBvcnR1bml0aWVzLWNvbnRhY3RzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUUscUJBQUE7RUFDQSxXQUFBO0FBQ0Y7O0FBSUk7RUFDRSxrQkFBQTtBQUROO0FBR007RUFDRSw0QkFBQTtFQUNBLDJDQUFBO0FBRFI7QUFHUTtFQUNFLFNBQUE7QUFEVjtBQUtNO0VBQ0UsNEJBQUE7RUFDQSxpQkFBQTtBQUhSIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICByaWdodDogMTBweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIHtcclxuICAub3Bwb3J0dW5pdHktY29udGFjdC1wb3B1cCB7XHJcbiAgICAucC1kaWFsb2cge1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgIGg0IHtcclxuICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "fork<PERSON><PERSON>n", "distinctUntilChanged", "switchMap", "tap", "catchError", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "OpportunitiesContactsComponent_ng_template_10_ng_container_8_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_4_Template", "OpportunitiesContactsComponent_ng_template_10_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "OpportunitiesContactsComponent_ng_template_10_Template_th_click_3_listener", "_r1", "OpportunitiesContactsComponent_ng_template_10_i_6_Template", "OpportunitiesContactsComponent_ng_template_10_i_7_Template", "OpportunitiesContactsComponent_ng_template_10_ng_container_8_Template", "selectedColumns", "contact_r6", "contact_person_function_name", "name", "phone_number", "mobile", "email_address", "OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_3_Template", "OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_4_Template", "OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_5_Template", "OpportunitiesContactsComponent_ng_template_11_ng_container_7_ng_container_6_Template", "col_r7", "OpportunitiesContactsComponent_ng_template_11_ng_container_7_Template", "OpportunitiesContactsComponent_ng_template_11_Template_button_click_10_listener", "$event", "_r5", "stopPropagation", "confirmRemove", "business_partner", "contact_persons", "documentId", "ɵɵsanitizeUrl", "full_name", "OpportunitiesContactsComponent_div_26_div_1_Template", "f", "errors", "OpportunitiesContactsComponent_div_43_div_1_Template", "OpportunitiesContactsComponent_div_74_div_1_Template", "OpportunitiesContactsComponent_div_74_div_2_Template", "submitted", "OpportunitiesContactsComponent_div_82_div_1_Template", "tmp_1_0", "ContactForm", "get", "OpportunitiesContactsComponent_div_92_div_1_Template", "OpportunitiesContactsComponent_div_93_div_1_Template", "item_r8", "bp_full_name", "email", "phone", "OpportunitiesContactsComponent_ng_template_109_span_2_Template", "OpportunitiesContactsComponent_ng_template_109_span_3_Template", "OpportunitiesContactsComponent_ng_template_109_span_4_Template", "ɵɵtextInterpolate", "bp_id", "OpportunitiesContactsComponent", "constructor", "route", "opportunitiesservice", "accountservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "contactDetails", "id", "opportunity_id", "departments", "functions", "addDialogVisible", "existingDialogVisible", "visible", "position", "editid", "saving", "cpDepartments", "cpFunctions", "contactLoading", "contactInput$", "defaultOptions", "selectedContacts", "group", "first_name", "required", "middle_name", "last_name", "job_title", "contact_person_department_name", "pattern", "contact_person_vip_type", "validity_end_date", "contactexisting", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "parent", "snapshot", "paramMap", "loadContacts", "getCPDepartment", "getCPFunction", "pipe", "subscribe", "item", "description", "value", "code", "opportunity", "response", "opportunity_contact_parties", "contact", "addresses", "emails", "phone_numbers", "find", "phone_number_type", "d", "contact_person_func_and_depts", "contact_person_department", "contact_person_function", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "contacts$", "term", "params", "getContacts", "error", "onSubmit", "_this", "_asyncToGenerator", "existing", "opportunity_party_contact_party_id", "role_code", "createExistingContact", "complete", "reset", "add", "severity", "detail", "getOpportunityByID", "invalid", "console", "log", "opportunity_party_contact_main_indicator", "createOpportunityContact", "confirm", "message", "icon", "accept", "remove", "deleteContact", "next", "showNewDialog", "showExistingDialog", "controls", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "OpportunitiesService", "i3", "AccountService", "i4", "FormBuilder", "i5", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesContactsComponent_Template", "rf", "ctx", "OpportunitiesContactsComponent_Template_p_button_click_5_listener", "OpportunitiesContactsComponent_Template_p_button_click_6_listener", "ɵɵtwoWayListener", "OpportunitiesContactsComponent_Template_p_multiSelect_ngModelChange_7_listener", "ɵɵtwoWayBindingSet", "OpportunitiesContactsComponent_Template_p_table_selectionChange_9_listener", "OpportunitiesContactsComponent_Template_p_table_onColReorder_9_listener", "OpportunitiesContactsComponent_ng_template_10_Template", "OpportunitiesContactsComponent_ng_template_11_Template", "OpportunitiesContactsComponent_ng_template_12_Template", "OpportunitiesContactsComponent_ng_template_13_Template", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_14_listener", "OpportunitiesContactsComponent_ng_template_15_Template", "OpportunitiesContactsComponent_div_26_Template", "OpportunitiesContactsComponent_div_43_Template", "OpportunitiesContactsComponent_div_74_Template", "OpportunitiesContactsComponent_div_82_Template", "OpportunitiesContactsComponent_div_92_Template", "OpportunitiesContactsComponent_div_93_Template", "OpportunitiesContactsComponent_div_94_Template", "OpportunitiesContactsComponent_Template_button_click_96_listener", "OpportunitiesContactsComponent_Template_button_click_97_listener", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_98_listener", "OpportunitiesContactsComponent_ng_template_99_Template", "OpportunitiesContactsComponent_ng_template_109_Template", "OpportunitiesContactsComponent_Template_button_click_111_listener", "OpportunitiesContactsComponent_Template_button_click_112_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "tmp_30_0", "touched", "tmp_33_0", "_c2", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { AccountService } from 'src/app/store/account/account.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of, forkJoin, } from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { distinctUntilChanged, switchMap, tap, catchError, } from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-opportunities-contacts',\r\n  templateUrl: './opportunities-contacts.component.html',\r\n  styleUrl: './opportunities-contacts.component.scss',\r\n})\r\nexport class OpportunitiesContactsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contactDetails: any[] = [];\r\n  public id: string = '';\r\n  public opportunity_id: string = '';\r\n  public bp_id: string = '';\r\n  public departments: any = null;\r\n  public functions: any = null;\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  public visible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public editid: string = '';\r\n  public documentId: string = '';\r\n  public saving = false;\r\n  public cpDepartments: { name: string; value: string }[] = [];\r\n  public cpFunctions: { name: string; value: string }[] = [];\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public selectedContacts = [];\r\n\r\n  public ContactForm: FormGroup = this.formBuilder.group({\r\n    first_name: ['', [Validators.required]],\r\n    middle_name: [''],\r\n    last_name: ['', [Validators.required]],\r\n    job_title: [''],\r\n    contact_person_function_name: [''],\r\n    contact_person_department_name: [''],\r\n    email_address: ['', [Validators.required, Validators.email]],\r\n    phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    contact_person_vip_type: [''],\r\n    validity_end_date: [''],\r\n    contactexisting: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private accountservice: AccountService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'contact_person_function_name.name', header: 'Function' },\r\n    { field: 'contact_person_department_name.name', header: 'Department' },\r\n    { field: 'phone_number', header: 'Phone' },\r\n    { field: 'mobile', header: 'Mobile' },\r\n    { field: 'email_address', header: 'E-Mail' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.contactDetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.loadContacts();\r\n    forkJoin({\r\n      departments: this.accountservice.getCPDepartment(),\r\n      functions: this.accountservice.getCPFunction(),\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(({ departments, functions }) => {\r\n        // Load departments\r\n        this.cpDepartments = (departments?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n\r\n        // Load functions\r\n        this.cpFunctions = (functions?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n        this.opportunitiesservice.opportunity\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe((response: any) => {\r\n            if (response) {\r\n              this.id = response?.opportunity_id;\r\n              this.bp_id = response?.business_partner?.bp_id;\r\n              this.documentId = response?.documentId;\r\n              this.contactDetails = response?.opportunity_contact_parties || [];\r\n\r\n              this.contactDetails = this.contactDetails.map((contact: any) => {\r\n                return {\r\n                  ...contact,\r\n                  full_name : contact?.business_partner?.bp_full_name || '',\r\n                  email_address:\r\n                    contact?.business_partner?.addresses?.[0]?.emails?.[0]\r\n                      ?.email_address || '',\r\n                  phone_number: (\r\n                    contact?.business_partner?.addresses?.[0]?.phone_numbers ||\r\n                    []\r\n                  ).find((item: any) => item.phone_number_type === '1')\r\n                    ?.phone_number,\r\n                  mobile: (\r\n                    contact?.business_partner?.addresses?.[0]?.phone_numbers ||\r\n                    []\r\n                  ).find((item: any) => item.phone_number_type === '3')\r\n                    ?.phone_number,\r\n\r\n                  // Ensure department & function values are set correctly\r\n                  contact_person_department_name:\r\n                    this.cpDepartments?.find(\r\n                      (d: any) =>\r\n                        d.value ===\r\n                        contact?.business_partner\r\n                          ?.contact_person_func_and_depts?.[0]\r\n                          ?.contact_person_department\r\n                    ) || null,\r\n\r\n                  contact_person_function_name:\r\n                    this.cpFunctions?.find(\r\n                      (f: any) =>\r\n                        f.value ===\r\n                        contact?.business_partner\r\n                          ?.contact_person_func_and_depts?.[0]\r\n                          ?.contact_person_function\r\n                    ) || null,\r\n                };\r\n              });\r\n            }\r\n          });\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  private loadContacts() {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.contactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.accountservice.getContacts(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n    if (this.ContactForm.value?.contactexisting) {\r\n      const existing = this.ContactForm.value.contactexisting;\r\n\r\n      const data = {\r\n        opportunity_party_contact_party_id: existing?.bp_id,\r\n        opportunity_id: this.opportunity_id,\r\n        role_code: '',\r\n      };\r\n\r\n      this.saving = true;\r\n\r\n      this.opportunitiesservice\r\n        .createExistingContact(data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.existingDialogVisible = false;\r\n            this.ContactForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Contact Added successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n\r\n      // Skip rest of logic for new contact\r\n      return;\r\n    }\r\n    if (this.ContactForm.invalid) {\r\n      console.log('Form is invalid:', this.ContactForm.errors);\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.ContactForm.value };\r\n\r\n    const data = {\r\n      bp_id: this.bp_id,\r\n      first_name: value?.first_name || '',\r\n      middle_name: value?.middle_name,\r\n      last_name: value?.last_name || '',\r\n      job_title: value?.job_title || '',\r\n      contact_person_function_name:\r\n        value?.contact_person_function_name?.name || '',\r\n      contact_person_function: value?.contact_person_function_name?.value || '',\r\n      contact_person_department_name:\r\n        value?.contact_person_department_name?.name || '',\r\n      contact_person_department:\r\n        value?.contact_person_department_name?.value || '',\r\n      email_address: value?.email_address,\r\n      phone_number: value?.phone_number,\r\n      mobile: value?.mobile,\r\n      contact_person_vip_type: value?.contact_person_vip_type,\r\n      opportunity_party_contact_main_indicator: '',\r\n      role_code: '',\r\n      opportunity_id: this.opportunity_id,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .createOpportunityContact(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        complete: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.existingDialogVisible = false;\r\n          this.ContactForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Contact created successfully!.',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteContact(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.ContactForm.reset();\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.ContactForm.controls;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Contacts</h4>\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <p-button label=\"Add New\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n            <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\" iconPos=\"right\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"contactDetails\" [(selection)]=\"selectedContacts\" dataKey=\"id\" [rows]=\"14\" [paginator]=\"true\"\r\n            [lazy]=\"true\" responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\"\r\n            [reorderableColumns]=\"true\" (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem table-checkbox text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('business_partner.bp_full_name')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortField === 'business_partner.bp_full_name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'business_partner.bp_full_name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Actions\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"font-medium\">\r\n                        <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                            <a [href]=\"'/#/store/contacts/' + contact?.business_partner?.contact_persons?.[0]?.documentId + '/overview'\"\r\n                                style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                                {{ contact?.full_name || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'contact_person_department_name.name'\">\r\n                                    {{ contact?.contact_person_function_name?.name || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'phone_number'\">\r\n                                    {{ contact?.phone_number || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'mobile'\">\r\n                                    {{ contact?.mobile || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'email_address'\">\r\n                                    {{ contact?.email_address || \"-\" }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n\r\n                    <td>\r\n                        <div class=\"flex align-items-center\">\r\n                            <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                                (click)=\"$event.stopPropagation(); confirmRemove(contact);\"></button>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" colspan=\"13\">No contacts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"13\" class=\"border-round-left-lg\">\r\n                        Loading contacts data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '38rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"First Name \">\r\n                <span class=\"material-symbols-rounded\">person</span>First Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"first_name\" formControlName=\"first_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['first_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['first_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['first_name'].errors['required']\">\r\n                        First Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Middle Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Middle Name\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"middle_name\" formControlName=\"middle_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Last Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Last Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"last_name\" formControlName=\"last_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['last_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['last_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['last_name'].errors['required']\">\r\n                        Last Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Job Title\">\r\n                <span class=\"material-symbols-rounded\">work</span>Job Title\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"job_title\" formControlName=\"job_title\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Function\">\r\n                <span class=\"material-symbols-rounded\">functions</span>Function\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpFunctions\" formControlName=\"contact_person_function_name\" optionLabel=\"name\"\r\n                    dataKey=\"value\" placeholder=\"Select Function\" [styleClass]=\"'h-3rem w-full'\"></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Department\">\r\n                <span class=\"material-symbols-rounded\">inbox_text_person</span>Department\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpDepartments\" formControlName=\"contact_person_department_name\"\r\n                    optionLabel=\"name\" dataKey=\"value\" placeholder=\"Select Department\" [styleClass]=\"'h-3rem w-full'\">\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Email\">\r\n                <span class=\"material-symbols-rounded\">mail</span>Email<span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"email\" id=\"email_address\" formControlName=\"email_address\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['email_address'].errors }\" />\r\n                <div *ngIf=\"submitted && f['email_address'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"\r\n              submitted &&\r\n              f['email_address'].errors &&\r\n              f['email_address'].errors['required']\r\n            \">\r\n                        Email is required.\r\n                    </div>\r\n                    <div *ngIf=\"f['email_address'].errors['email']\">\r\n                        Email is invalid.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Phone\">\r\n                <span class=\"material-symbols-rounded\">phone_in_talk</span>Phone\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"phone_number\" formControlName=\"phone_number\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n                <div *ngIf=\"ContactForm.get('phone_number')?.touched && ContactForm.get('phone_number')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('phone_number')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Phone number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Mobile\">\r\n                <span class=\"material-symbols-rounded\">smartphone</span>Mobile #\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"mobile\" formControlName=\"mobile\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['mobile'].errors }\" />\r\n                <div *ngIf=\"submitted && f['mobile'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['mobile'].errors['required']\">\r\n                        Mobile is required.\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"ContactForm.get('mobile')?.touched && ContactForm.get('mobile')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('mobile')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Mobile number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"editid && ContactForm.value.first_name\">\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"DeActivate\">\r\n                    <span class=\"material-symbols-rounded\">remove_circle_outline</span>DeActivate\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"validity_end_date\" formControlName=\"validity_end_date\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"VIP Contact\">\r\n                    <span class=\"material-symbols-rounded\">star</span>VIP Contact\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"contact_person_vip_type\" formControlName=\"contact_person_vip_type\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                <span class=\"material-symbols-rounded\">person</span>Contacts\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" [hideSelected]=\"true\"\r\n                    [loading]=\"contactLoading\" [minTermLength]=\"0\" formControlName=\"contactexisting\"\r\n                    [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\" appendTo=\"body\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.phone\"> : {{ item.phone }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"existingDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AAGA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,EAAEC,QAAQ,QAAS,MAAM;AAEjF,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,EAAEC,UAAU,QAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICuBtDC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAqF;;;;;IAOjFD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,0FAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,yEAAA,gBACkF,IAAAC,yEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAwE;IAAtDN,EAAA,CAAAO,UAAA,mBAAAmB,2EAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,+BAA+B,CAAC;IAAA,EAAC;IACnEf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,aACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,0DAAA,gBACkF,IAAAC,0DAAA,gBAED;IAEzF7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,qEAAA,2BAAkD;IAY9C9B,EADJ,CAAAM,cAAA,SAAI,eACqC;IACjCN,EAAA,CAAAiB,MAAA,iBACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAM,EACL,EACJ;;;;IAtBWrB,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,qCAAmD;IAGnDzB,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,qCAAmD;IAGjCzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAoCpC/B,EAAA,CAAAK,uBAAA,GAAoE;IAChEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAC,4BAAA,kBAAAD,UAAA,CAAAC,4BAAA,CAAAC,IAAA,cACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAG,YAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAAuC;IACnCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAI,MAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAK,aAAA,cACJ;;;;;IAjBZrC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAajCL,EAZA,CAAAkB,UAAA,IAAAoB,oFAAA,2BAAoE,IAAAC,oFAAA,2BAIvB,IAAAC,oFAAA,2BAIN,IAAAC,oFAAA,2BAIO;;IAKtDzC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAlBarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAwC,MAAA,CAAA1B,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,uDAAmD;IAInDF,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAsB,SAAA,EAAsB;IAAtBtB,EAAA,CAAAE,UAAA,0BAAsB;IAItBF,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,iCAA6B;;;;;;IA3BxDF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAqC;IACzCD,EAAA,CAAAqB,YAAA,EAAK;IAGGrB,EAFR,CAAAM,cAAA,aAAsC,cACyC,YAEJ;IAC/DN,EAAA,CAAAiB,MAAA,GACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAI,EACF,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAyB,qEAAA,2BAAkD;IAyB1C3C,EAFR,CAAAM,cAAA,SAAI,cACqC,kBAE+B;IAA5DN,EAAA,CAAAO,UAAA,mBAAAqC,gFAAAC,MAAA;MAAA,MAAAb,UAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAAoC,GAAA,EAAAlC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASgC,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAAEX,MAAA,CAAA6C,aAAA,CAAAhB,UAAA,CAAsB;IAAA,EAAE;IAG3EhC,EAH4E,CAAAqB,YAAA,EAAS,EACvE,EACL,EACJ;;;;;IAxCoBrB,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAAE,UAAA,UAAA8B,UAAA,CAAiB;IAI3BhC,EAAA,CAAAsB,SAAA,GAAyG;IAAzGtB,EAAA,CAAAE,UAAA,iCAAA8B,UAAA,kBAAAA,UAAA,CAAAiB,gBAAA,kBAAAjB,UAAA,CAAAiB,gBAAA,CAAAC,eAAA,kBAAAlB,UAAA,CAAAiB,gBAAA,CAAAC,eAAA,qBAAAlB,UAAA,CAAAiB,gBAAA,CAAAC,eAAA,IAAAC,UAAA,iBAAAnD,EAAA,CAAAoD,aAAA,CAAyG;IAExGpD,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAqB,SAAA,cACJ;IAIsBrD,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAkChD/B,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,yBAAkB;IACpEjB,EADoE,CAAAqB,YAAA,EAAK,EACpE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAC1CN,EAAA,CAAAiB,MAAA,8CACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAQbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,0BAAmB;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAchBrB,EAAA,CAAAM,cAAA,UAAgD;IAC5CN,EAAA,CAAAiB,MAAA,gCACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAJVrB,EAAA,CAAAM,cAAA,cACmE;IAC/DN,EAAA,CAAAkB,UAAA,IAAAoC,oDAAA,kBAAgD;IAGpDtD,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoD,CAAA,eAAAC,MAAA,aAAwC;;;;;IAyB9CxD,EAAA,CAAAM,cAAA,UAA+C;IAC3CN,EAAA,CAAAiB,MAAA,+BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAJVrB,EAAA,CAAAM,cAAA,cACmE;IAC/DN,EAAA,CAAAkB,UAAA,IAAAuC,oDAAA,kBAA+C;IAGnDzD,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAuC;IAAvCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoD,CAAA,cAAAC,MAAA,aAAuC;;;;;IA2C7CxD,EAAA,CAAAM,cAAA,UAIN;IACUN,EAAA,CAAAiB,MAAA,2BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IACNrB,EAAA,CAAAM,cAAA,UAAgD;IAC5CN,EAAA,CAAAiB,MAAA,0BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAXVrB,EAAA,CAAAM,cAAA,cACmE;IAQ/DN,EAPA,CAAAkB,UAAA,IAAAwC,oDAAA,kBAIN,IAAAC,oDAAA,kBAGsD;IAGpD3D,EAAA,CAAAqB,YAAA,EAAM;;;;IAVIrB,EAAA,CAAAsB,SAAA,EAIf;IAJetB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyD,SAAA,IAAAzD,MAAA,CAAAoD,CAAA,kBAAAC,MAAA,IAAArD,MAAA,CAAAoD,CAAA,kBAAAC,MAAA,aAIf;IAGexD,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoD,CAAA,kBAAAC,MAAA,UAAwC;;;;;IAc9CxD,EAAA,CAAAM,cAAA,cAAkF;IAC9EN,EAAA,CAAAiB,MAAA,2CACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHVrB,EAAA,CAAAM,cAAA,UAAkG;IAC9FN,EAAA,CAAAkB,UAAA,IAAA2C,oDAAA,kBAAkF;IAGtF7D,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAA0D;IAA1DtB,EAAA,CAAAE,UAAA,UAAA4D,OAAA,GAAA3D,MAAA,CAAA4D,WAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAA0D;;;;;IAgBhExD,EAAA,CAAAM,cAAA,UAA4C;IACxCN,EAAA,CAAAiB,MAAA,4BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAJVrB,EAAA,CAAAM,cAAA,cACmE;IAC/DN,EAAA,CAAAkB,UAAA,IAAA+C,oDAAA,kBAA4C;IAGhDjE,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAoC;IAApCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoD,CAAA,WAAAC,MAAA,aAAoC;;;;;IAK1CxD,EAAA,CAAAM,cAAA,cAA4E;IACxEN,EAAA,CAAAiB,MAAA,4CACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHVrB,EAAA,CAAAM,cAAA,UAAsF;IAClFN,EAAA,CAAAkB,UAAA,IAAAgD,oDAAA,kBAA4E;IAGhFlE,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAoD;IAApDtB,EAAA,CAAAE,UAAA,UAAA4D,OAAA,GAAA3D,MAAA,CAAA4D,WAAA,CAAAC,GAAA,6BAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAAoD;;;;;IAS1DxD,EAHZ,CAAAM,cAAA,UAAoD,cACK,gBACiD,eACvD;IAAAN,EAAA,CAAAiB,MAAA,4BAAqB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAAArB,EAAA,CAAAiB,MAAA,kBACvE;IAAAjB,EAAA,CAAAqB,YAAA,EAAQ;IACRrB,EAAA,CAAAM,cAAA,cAAwC;IACpCN,EAAA,CAAAC,SAAA,qBACuC;IAE/CD,EADI,CAAAqB,YAAA,EAAM,EACJ;IAGErB,EAFR,CAAAM,cAAA,cAAqD,gBACkD,gBACxD;IAAAN,EAAA,CAAAiB,MAAA,YAAI;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAAArB,EAAA,CAAAiB,MAAA,oBACtD;IAAAjB,EAAA,CAAAqB,YAAA,EAAQ;IACRrB,EAAA,CAAAM,cAAA,eAAwC;IACpCN,EAAA,CAAAC,SAAA,sBACuC;IAGnDD,EAFQ,CAAAqB,YAAA,EAAM,EACJ,EACJ;;;IAb6ErB,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAE,UAAA,gBAAe;IASHF,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAE,UAAA,gBAAe;;;;;IAiB9GF,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,0BAAmB;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAcZrB,EAAA,CAAAM,cAAA,WAAgC;IAACN,EAAA,CAAAiB,MAAA,GAAyB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAhCrB,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAuB,kBAAA,QAAA4C,OAAA,CAAAC,YAAA,KAAyB;;;;;IAC1DpE,EAAA,CAAAM,cAAA,WAAyB;IAACN,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAzBrB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAuB,kBAAA,QAAA4C,OAAA,CAAAE,KAAA,KAAkB;;;;;IAC5CrE,EAAA,CAAAM,cAAA,WAAyB;IAACN,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAzBrB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAuB,kBAAA,QAAA4C,OAAA,CAAAG,KAAA,KAAkB;;;;;IAH5CtE,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAiB,MAAA,GAAgB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAG7BrB,EAFA,CAAAkB,UAAA,IAAAqD,8DAAA,mBAAgC,IAAAC,8DAAA,mBACP,IAAAC,8DAAA,mBACA;;;;IAHnBzE,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAA0E,iBAAA,CAAAP,OAAA,CAAAQ,KAAA,CAAgB;IACf3E,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,SAAAiE,OAAA,CAAAC,YAAA,CAAuB;IACvBpE,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAE,UAAA,SAAAiE,OAAA,CAAAE,KAAA,CAAgB;IAChBrE,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAE,UAAA,SAAAiE,OAAA,CAAAG,KAAA,CAAgB;;;ADrR/C,OAAM,MAAOM,8BAA8B;EAuCzCC,YACUC,KAAqB,EACrBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA5CrB,KAAAC,YAAY,GAAG,IAAI9F,OAAO,EAAQ;IACnC,KAAA+F,cAAc,GAAU,EAAE;IAC1B,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAZ,KAAK,GAAW,EAAE;IAClB,KAAAa,WAAW,GAAQ,IAAI;IACvB,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAjC,SAAS,GAAG,KAAK;IACjB,KAAAkC,MAAM,GAAW,EAAE;IACnB,KAAA3C,UAAU,GAAW,EAAE;IACvB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAC,aAAa,GAAsC,EAAE;IACrD,KAAAC,WAAW,GAAsC,EAAE;IAEnD,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI7G,OAAO,EAAU;IACpC,KAAA8G,cAAc,GAAQ,EAAE;IACzB,KAAAC,gBAAgB,GAAG,EAAE;IAErB,KAAAtC,WAAW,GAAc,IAAI,CAACkB,WAAW,CAACqB,KAAK,CAAC;MACrDC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAClH,UAAU,CAACmH,QAAQ,CAAC,CAAC;MACvCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACrH,UAAU,CAACmH,QAAQ,CAAC,CAAC;MACtCG,SAAS,EAAE,CAAC,EAAE,CAAC;MACf1E,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClC2E,8BAA8B,EAAE,CAAC,EAAE,CAAC;MACpCvE,aAAa,EAAE,CAAC,EAAE,EAAE,CAAChD,UAAU,CAACmH,QAAQ,EAAEnH,UAAU,CAACgF,KAAK,CAAC,CAAC;MAC5DlC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAACwH,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACzDzE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAACmH,QAAQ,EAAEnH,UAAU,CAACwH,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACxEC,uBAAuB,EAAE,CAAC,EAAE,CAAC;MAC7BC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,eAAe,EAAE,CAAC,EAAE;KACrB,CAAC;IAWM,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAElG,KAAK,EAAE,mCAAmC;MAAEQ,MAAM,EAAE;IAAU,CAAE,EAClE;MAAER,KAAK,EAAE,qCAAqC;MAAEQ,MAAM,EAAE;IAAY,CAAE,EACtE;MAAER,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAC1C;MAAER,KAAK,EAAE,QAAQ;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrC;MAAER,KAAK,EAAE,eAAe;MAAEQ,MAAM,EAAE;IAAQ,CAAE,CAC7C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAbjB;EAeJW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACiF,cAAc,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEpG,KAAK,CAAC;MAC9C,MAAMwG,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAErG,KAAK,CAAC;MAE9C,IAAIyG,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACpH,SAAS,GAAGqH,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE3G,KAAa;IACvC,IAAI,CAAC2G,IAAI,IAAI,CAAC3G,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC4G,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC3G,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC6G,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC1C,cAAc,GAAG,IAAI,CAACT,KAAK,CAACoD,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACpE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1E,IAAI,CAACqE,YAAY,EAAE;IACnB1I,QAAQ,CAAC;MACP6F,WAAW,EAAE,IAAI,CAACR,cAAc,CAACsD,eAAe,EAAE;MAClD7C,SAAS,EAAE,IAAI,CAACT,cAAc,CAACuD,aAAa;KAC7C,CAAC,CACCC,IAAI,CAACjJ,SAAS,CAAC,IAAI,CAAC6F,YAAY,CAAC,CAAC,CAClCqD,SAAS,CAAC,CAAC;MAAEjD,WAAW;MAAEC;IAAS,CAAE,KAAI;MACxC;MACA,IAAI,CAACO,aAAa,GAAG,CAACR,WAAW,EAAEmC,IAAI,IAAI,EAAE,EAAElI,GAAG,CAAEiJ,IAAS,KAAM;QACjExG,IAAI,EAAEwG,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MAEH;MACA,IAAI,CAAC5C,WAAW,GAAG,CAACR,SAAS,EAAEkC,IAAI,IAAI,EAAE,EAAElI,GAAG,CAAEiJ,IAAS,KAAM;QAC7DxG,IAAI,EAAEwG,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MACH,IAAI,CAAC9D,oBAAoB,CAAC+D,WAAW,CAClCN,IAAI,CAACjJ,SAAS,CAAC,IAAI,CAAC6F,YAAY,CAAC,CAAC,CAClCqD,SAAS,CAAEM,QAAa,IAAI;QAC3B,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACzD,EAAE,GAAGyD,QAAQ,EAAExD,cAAc;UAClC,IAAI,CAACZ,KAAK,GAAGoE,QAAQ,EAAE9F,gBAAgB,EAAE0B,KAAK;UAC9C,IAAI,CAACxB,UAAU,GAAG4F,QAAQ,EAAE5F,UAAU;UACtC,IAAI,CAACkC,cAAc,GAAG0D,QAAQ,EAAEC,2BAA2B,IAAI,EAAE;UAEjE,IAAI,CAAC3D,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC5F,GAAG,CAAEwJ,OAAY,IAAI;YAC7D,OAAO;cACL,GAAGA,OAAO;cACV5F,SAAS,EAAG4F,OAAO,EAAEhG,gBAAgB,EAAEmB,YAAY,IAAI,EAAE;cACzD/B,aAAa,EACX4G,OAAO,EAAEhG,gBAAgB,EAAEiG,SAAS,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,EAClD9G,aAAa,IAAI,EAAE;cACzBF,YAAY,EAAE,CACZ8G,OAAO,EAAEhG,gBAAgB,EAAEiG,SAAS,GAAG,CAAC,CAAC,EAAEE,aAAa,IACxD,EAAE,EACFC,IAAI,CAAEX,IAAS,IAAKA,IAAI,CAACY,iBAAiB,KAAK,GAAG,CAAC,EACjDnH,YAAY;cAChBC,MAAM,EAAE,CACN6G,OAAO,EAAEhG,gBAAgB,EAAEiG,SAAS,GAAG,CAAC,CAAC,EAAEE,aAAa,IACxD,EAAE,EACFC,IAAI,CAAEX,IAAS,IAAKA,IAAI,CAACY,iBAAiB,KAAK,GAAG,CAAC,EACjDnH,YAAY;cAEhB;cACAyE,8BAA8B,EAC5B,IAAI,CAACZ,aAAa,EAAEqD,IAAI,CACrBE,CAAM,IACLA,CAAC,CAACX,KAAK,KACPK,OAAO,EAAEhG,gBAAgB,EACrBuG,6BAA6B,GAAG,CAAC,CAAC,EAClCC,yBAAyB,CAChC,IAAI,IAAI;cAEXxH,4BAA4B,EAC1B,IAAI,CAACgE,WAAW,EAAEoD,IAAI,CACnB9F,CAAM,IACLA,CAAC,CAACqF,KAAK,KACPK,OAAO,EAAEhG,gBAAgB,EACrBuG,6BAA6B,GAAG,CAAC,CAAC,EAClCE,uBAAuB,CAC9B,IAAI;aACR;UACH,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACN,CAAC,CAAC;IAEJ,IAAI,CAACzC,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAInF,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACkF,gBAAgB;EAC9B;EAEA,IAAIlF,eAAeA,CAAC4H,GAAU;IAC5B,IAAI,CAAC1C,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC0C,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAChD,gBAAgB,CAAC+C,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACjD,gBAAgB,CAACkD,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACjD,gBAAgB,CAACkD,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEQ5B,YAAYA,CAAA;IAClB,IAAI,CAACgC,SAAS,GAAG7K,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC0G,cAAc,CAAC;IAAE;IACzB,IAAI,CAACD,aAAa,CAACqC,IAAI,CACrB5I,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACoG,cAAc,GAAG,IAAK,CAAC,EACvCrG,SAAS,CAAEyK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACtF,cAAc,CAACwF,WAAW,CAACD,MAAM,CAAC,CAAC/B,IAAI,CACjD/I,GAAG,CAAEkI,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF7H,GAAG,CAAC,MAAO,IAAI,CAACoG,cAAc,GAAG,KAAM,CAAC,EACxCnG,UAAU,CAAE0K,KAAK,IAAI;QACnB,IAAI,CAACvE,cAAc,GAAG,KAAK;QAC3B,OAAOxG,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEMgL,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC/G,SAAS,GAAG,IAAI;MACrB+G,KAAI,CAAC/E,OAAO,GAAG,IAAI;MACnB,IAAI+E,KAAI,CAAC5G,WAAW,CAAC6E,KAAK,EAAE5B,eAAe,EAAE;QAC3C,MAAM6D,QAAQ,GAAGF,KAAI,CAAC5G,WAAW,CAAC6E,KAAK,CAAC5B,eAAe;QAEvD,MAAMW,IAAI,GAAG;UACXmD,kCAAkC,EAAED,QAAQ,EAAElG,KAAK;UACnDY,cAAc,EAAEoF,KAAI,CAACpF,cAAc;UACnCwF,SAAS,EAAE;SACZ;QAEDJ,KAAI,CAAC5E,MAAM,GAAG,IAAI;QAElB4E,KAAI,CAAC5F,oBAAoB,CACtBiG,qBAAqB,CAACrD,IAAI,CAAC,CAC3Ba,IAAI,CAACjJ,SAAS,CAACoL,KAAI,CAACvF,YAAY,CAAC,CAAC,CAClCqD,SAAS,CAAC;UACTwC,QAAQ,EAAEA,CAAA,KAAK;YACbN,KAAI,CAAC5E,MAAM,GAAG,KAAK;YACnB4E,KAAI,CAAChF,qBAAqB,GAAG,KAAK;YAClCgF,KAAI,CAAC5G,WAAW,CAACmH,KAAK,EAAE;YACxBP,KAAI,CAACzF,cAAc,CAACiG,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFV,KAAI,CAAC5F,oBAAoB,CACtBuG,kBAAkB,CAACX,KAAI,CAACpF,cAAc,CAAC,CACvCiD,IAAI,CAACjJ,SAAS,CAACoL,KAAI,CAACvF,YAAY,CAAC,CAAC,CAClCqD,SAAS,EAAE;UAChB,CAAC;UACDgC,KAAK,EAAEA,CAAA,KAAK;YACVE,KAAI,CAAC5E,MAAM,GAAG,KAAK;YACnB4E,KAAI,CAACjF,gBAAgB,GAAG,KAAK;YAC7BiF,KAAI,CAACzF,cAAc,CAACiG,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;QAEJ;QACA;MACF;MACA,IAAIV,KAAI,CAAC5G,WAAW,CAACwH,OAAO,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEd,KAAI,CAAC5G,WAAW,CAACP,MAAM,CAAC;QACxDmH,KAAI,CAAC/E,OAAO,GAAG,IAAI;QACnB;MACF;MAEA+E,KAAI,CAAC5E,MAAM,GAAG,IAAI;MAClB,MAAM6C,KAAK,GAAG;QAAE,GAAG+B,KAAI,CAAC5G,WAAW,CAAC6E;MAAK,CAAE;MAE3C,MAAMjB,IAAI,GAAG;QACXhD,KAAK,EAAEgG,KAAI,CAAChG,KAAK;QACjB4B,UAAU,EAAEqC,KAAK,EAAErC,UAAU,IAAI,EAAE;QACnCE,WAAW,EAAEmC,KAAK,EAAEnC,WAAW;QAC/BC,SAAS,EAAEkC,KAAK,EAAElC,SAAS,IAAI,EAAE;QACjCC,SAAS,EAAEiC,KAAK,EAAEjC,SAAS,IAAI,EAAE;QACjC1E,4BAA4B,EAC1B2G,KAAK,EAAE3G,4BAA4B,EAAEC,IAAI,IAAI,EAAE;QACjDwH,uBAAuB,EAAEd,KAAK,EAAE3G,4BAA4B,EAAE2G,KAAK,IAAI,EAAE;QACzEhC,8BAA8B,EAC5BgC,KAAK,EAAEhC,8BAA8B,EAAE1E,IAAI,IAAI,EAAE;QACnDuH,yBAAyB,EACvBb,KAAK,EAAEhC,8BAA8B,EAAEgC,KAAK,IAAI,EAAE;QACpDvG,aAAa,EAAEuG,KAAK,EAAEvG,aAAa;QACnCF,YAAY,EAAEyG,KAAK,EAAEzG,YAAY;QACjCC,MAAM,EAAEwG,KAAK,EAAExG,MAAM;QACrB0E,uBAAuB,EAAE8B,KAAK,EAAE9B,uBAAuB;QACvD4E,wCAAwC,EAAE,EAAE;QAC5CX,SAAS,EAAE,EAAE;QACbxF,cAAc,EAAEoF,KAAI,CAACpF;OACtB;MAEDoF,KAAI,CAAC5F,oBAAoB,CACtB4G,wBAAwB,CAAChE,IAAI,CAAC,CAC9Ba,IAAI,CAACjJ,SAAS,CAACoL,KAAI,CAACvF,YAAY,CAAC,CAAC,CAClCqD,SAAS,CAAC;QACTwC,QAAQ,EAAEA,CAAA,KAAK;UACbN,KAAI,CAAC5E,MAAM,GAAG,KAAK;UACnB4E,KAAI,CAACjF,gBAAgB,GAAG,KAAK;UAC7BiF,KAAI,CAAChF,qBAAqB,GAAG,KAAK;UAClCgF,KAAI,CAAC5G,WAAW,CAACmH,KAAK,EAAE;UACxBP,KAAI,CAACzF,cAAc,CAACiG,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFV,KAAI,CAAC5F,oBAAoB,CACtBuG,kBAAkB,CAACX,KAAI,CAACpF,cAAc,CAAC,CACvCiD,IAAI,CAACjJ,SAAS,CAACoL,KAAI,CAACvF,YAAY,CAAC,CAAC,CAClCqD,SAAS,EAAE;QAChB,CAAC;QACDgC,KAAK,EAAEA,CAAA,KAAK;UACVE,KAAI,CAAC5E,MAAM,GAAG,KAAK;UACnB4E,KAAI,CAACjF,gBAAgB,GAAG,KAAK;UAC7BiF,KAAI,CAACzF,cAAc,CAACiG,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEArI,aAAaA,CAAC0F,IAAS;IACrB,IAAI,CAACvD,mBAAmB,CAACyG,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChErK,MAAM,EAAE,SAAS;MACjBsK,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACtD,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAsD,MAAMA,CAACtD,IAAS;IACd,IAAI,CAAC3D,oBAAoB,CACtBkH,aAAa,CAACvD,IAAI,CAACvF,UAAU,CAAC,CAC9BqF,IAAI,CAACjJ,SAAS,CAAC,IAAI,CAAC6F,YAAY,CAAC,CAAC,CAClCqD,SAAS,CAAC;MACTyD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChH,cAAc,CAACiG,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACtG,oBAAoB,CACtBuG,kBAAkB,CAAC,IAAI,CAAC/F,cAAc,CAAC,CACvCiD,IAAI,CAACjJ,SAAS,CAAC,IAAI,CAAC6F,YAAY,CAAC,CAAC,CAClCqD,SAAS,EAAE;MAChB,CAAC;MACDgC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvF,cAAc,CAACiG,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAc,aAAaA,CAACtG,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC9B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,WAAW,CAACmH,KAAK,EAAE;EAC1B;EAEAkB,kBAAkBA,CAACvG,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACF,qBAAqB,GAAG,IAAI;EACnC;EAEA,IAAIpC,CAACA,CAAA;IACH,OAAO,IAAI,CAACQ,WAAW,CAACsI,QAAQ;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClH,YAAY,CAAC8G,IAAI,EAAE;IACxB,IAAI,CAAC9G,YAAY,CAAC6F,QAAQ,EAAE;EAC9B;;;uBAzXWrG,8BAA8B,EAAA5E,EAAA,CAAAuM,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzM,EAAA,CAAAuM,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAA3M,EAAA,CAAAuM,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7M,EAAA,CAAAuM,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA/M,EAAA,CAAAuM,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAjN,EAAA,CAAAuM,iBAAA,CAAAS,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAA9BtI,8BAA8B;MAAAuI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBnCzN,EAFR,CAAAM,cAAA,aAA2D,aACuC,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,eAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAExDrB,EADJ,CAAAM,cAAA,aAAmD,kBAE4B;UADjDN,EAAA,CAAAO,UAAA,mBAAAoN,kEAAA;YAAA,OAASD,GAAA,CAAAvB,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAA1DnM,EAAA,CAAAqB,YAAA,EAC2E;UAC3ErB,EAAA,CAAAM,cAAA,kBAC2D;UADxBN,EAAA,CAAAO,UAAA,mBAAAqN,kEAAA;YAAA,OAASF,GAAA,CAAAtB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAAxEpM,EAAA,CAAAqB,YAAA,EAC2D;UAE3DrB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAA6N,gBAAA,2BAAAC,+EAAAjL,MAAA;YAAA7C,EAAA,CAAA+N,kBAAA,CAAAL,GAAA,CAAA3L,eAAA,EAAAc,MAAA,MAAA6K,GAAA,CAAA3L,eAAA,GAAAc,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE7C,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAGsD;UAFvCN,EAAA,CAAA6N,gBAAA,6BAAAG,2EAAAnL,MAAA;YAAA7C,EAAA,CAAA+N,kBAAA,CAAAL,GAAA,CAAArH,gBAAA,EAAAxD,MAAA,MAAA6K,GAAA,CAAArH,gBAAA,GAAAxD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAElC7C,EAAA,CAAAO,UAAA,0BAAA0N,wEAAApL,MAAA;YAAA,OAAgB6K,GAAA,CAAA3D,eAAA,CAAAlH,MAAA,CAAuB;UAAA,EAAC;UAsFpE7C,EApFA,CAAAkB,UAAA,KAAAgN,sDAAA,0BAAgC,KAAAC,sDAAA,2BAiCgC,KAAAC,sDAAA,0BA8C1B,KAAAC,sDAAA,0BAKD;UASjDrO,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAM,cAAA,oBAC0D;UADjCN,EAAA,CAAA6N,gBAAA,2BAAAS,2EAAAzL,MAAA;YAAA7C,EAAA,CAAA+N,kBAAA,CAAAL,GAAA,CAAAhI,gBAAA,EAAA7C,MAAA,MAAA6K,GAAA,CAAAhI,gBAAA,GAAA7C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAEnD7C,EAAA,CAAAkB,UAAA,KAAAqN,sDAAA,yBAAgC;UAOpBvO,EAHZ,CAAAM,cAAA,gBAAwE,eACf,iBACkD,gBACxD;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,mBACpD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBAC2F;UAC3FD,EAAA,CAAAkB,UAAA,KAAAsN,8CAAA,kBACmE;UAM3ExO,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACkD,gBACxD;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,oBACxD;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBACyB;UAEjCD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACgD,gBACtD;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,kBACpD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBAC0F;UAC1FD,EAAA,CAAAkB,UAAA,KAAAuN,8CAAA,kBACmE;UAM3EzO,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACgD,gBACtD;UAAAN,EAAA,CAAAiB,MAAA,YAAI;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,kBACtD;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBACyB;UAEjCD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBAC+C,gBACrD;UAAAN,EAAA,CAAAiB,MAAA,iBAAS;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,iBAC3D;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,sBAC8F;UAEtGD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACiD,gBACvD;UAAAN,EAAA,CAAAiB,MAAA,yBAAiB;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,mBACnE;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,sBAEa;UAErBD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBAC4C,gBAClD;UAAAN,EAAA,CAAAiB,MAAA,YAAI;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UACvFjB,EADuF,CAAAqB,YAAA,EAAO,EACtF;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBAC8F;UAC9FD,EAAA,CAAAkB,UAAA,KAAAwN,8CAAA,kBACmE;UAa3E1O,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBAC4C,gBAClD;UAAAN,EAAA,CAAAiB,MAAA,qBAAa;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,cAC/D;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBACyB;UACzBD,EAAA,CAAAkB,UAAA,KAAAyN,8CAAA,kBAAkG;UAM1G3O,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBAC6C,gBACnD;UAAAN,EAAA,CAAAiB,MAAA,kBAAU;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,iBACxD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,iBACuF;UAOvFD,EANA,CAAAkB,UAAA,KAAA0N,8CAAA,kBACmE,KAAAC,8CAAA,kBAKmB;UAM9F7O,EADI,CAAAqB,YAAA,EAAM,EACJ;UACNrB,EAAA,CAAAkB,UAAA,KAAA4N,8CAAA,mBAAoD;UAqBhD9O,EADJ,CAAAM,cAAA,eAAoD,kBAGT;UAAnCN,EAAA,CAAAO,UAAA,mBAAAwO,iEAAA;YAAA,OAAArB,GAAA,CAAAhI,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAC1F,EAAA,CAAAqB,YAAA,EAAS;UAChDrB,EAAA,CAAAM,cAAA,kBACyB;UAArBN,EAAA,CAAAO,UAAA,mBAAAyO,iEAAA;YAAA,OAAStB,GAAA,CAAAhD,QAAA,EAAU;UAAA,EAAC;UAGpC1K,EAHqC,CAAAqB,YAAA,EAAS,EAChC,EACH,EACA;UACXrB,EAAA,CAAAM,cAAA,oBAC0D;UADjCN,EAAA,CAAA6N,gBAAA,2BAAAoB,2EAAApM,MAAA;YAAA7C,EAAA,CAAA+N,kBAAA,CAAAL,GAAA,CAAA/H,qBAAA,EAAA9C,MAAA,MAAA6K,GAAA,CAAA/H,qBAAA,GAAA9C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAExD7C,EAAA,CAAAkB,UAAA,KAAAgO,sDAAA,yBAAgC;UAOpBlP,EAHZ,CAAAM,cAAA,iBAAwE,gBACf,kBAC+C,iBACrD;UAAAN,EAAA,CAAAiB,MAAA,eAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,kBACxD;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UAEJrB,EADJ,CAAAM,cAAA,gBAAwC,sBAGoC;;UACpEN,EAAA,CAAAkB,UAAA,MAAAiO,uDAAA,0BAA2C;UAQvDnP,EAFQ,CAAAqB,YAAA,EAAY,EACV,EACJ;UAEFrB,EADJ,CAAAM,cAAA,gBAAoD,mBAGJ;UAAxCN,EAAA,CAAAO,UAAA,mBAAA6O,kEAAA;YAAA,OAAA1B,GAAA,CAAA/H,qBAAA,GAAiC,KAAK;UAAA,EAAC;UAAC3F,EAAA,CAAAqB,YAAA,EAAS;UACrDrB,EAAA,CAAAM,cAAA,mBACyB;UAArBN,EAAA,CAAAO,UAAA,mBAAA8O,kEAAA;YAAA,OAAS3B,GAAA,CAAAhD,QAAA,EAAU;UAAA,EAAC;UAGpC1K,EAHqC,CAAAqB,YAAA,EAAS,EAChC,EACH,EACA;;;;;UAhTqBrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEpEF,EAAA,CAAAsB,SAAA,EAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzCF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAwN,GAAA,CAAAxG,IAAA,CAAgB;UAAClH,EAAA,CAAAsP,gBAAA,YAAA5B,GAAA,CAAA3L,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAAwB;UAAxBtB,EAAA,CAAAE,UAAA,UAAAwN,GAAA,CAAArI,cAAA,CAAwB;UAACrF,EAAA,CAAAsP,gBAAA,cAAA5B,GAAA,CAAArH,gBAAA,CAAgC;UAE9DrG,EAF4E,CAAAE,UAAA,YAAW,mBAAmB,cAC7F,oBAA8C,4BAChC;UAgGiBF,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAuP,UAAA,CAAAvP,EAAA,CAAAwP,eAAA,KAAAC,GAAA,EAA4B;UAA1EzP,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAAsP,gBAAA,YAAA5B,GAAA,CAAAhI,gBAAA,CAA8B;UACnD1F,EADiF,CAAAE,UAAA,qBAAoB,oBAClF;UAKbF,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAE,UAAA,cAAAwN,GAAA,CAAA3J,WAAA,CAAyB;UAQI/D,EAAA,CAAAsB,SAAA,GAAiE;UAAjEtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA0P,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,eAAAC,MAAA,EAAiE;UAClFxD,EAAA,CAAAsB,SAAA,EAAyC;UAAzCtB,EAAA,CAAAE,UAAA,SAAAwN,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,eAAAC,MAAA,CAAyC;UAwBxBxD,EAAA,CAAAsB,SAAA,IAAgE;UAAhEtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA0P,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,cAAAC,MAAA,EAAgE;UACjFxD,EAAA,CAAAsB,SAAA,EAAwC;UAAxCtB,EAAA,CAAAE,UAAA,SAAAwN,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,cAAAC,MAAA,CAAwC;UAsBlCxD,EAAA,CAAAsB,SAAA,IAAuB;UACetB,EADtC,CAAAE,UAAA,YAAAwN,GAAA,CAAAzH,WAAA,CAAuB,+BAC6C;UAQpEjG,EAAA,CAAAsB,SAAA,GAAyB;UACkCtB,EAD3D,CAAAE,UAAA,YAAAwN,GAAA,CAAA1H,aAAA,CAAyB,+BACgE;UAU9EhG,EAAA,CAAAsB,SAAA,GAAoE;UAApEtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA0P,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,kBAAAC,MAAA,EAAoE;UACrFxD,EAAA,CAAAsB,SAAA,EAA4C;UAA5CtB,EAAA,CAAAE,UAAA,SAAAwN,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,kBAAAC,MAAA,CAA4C;UAsB5CxD,EAAA,CAAAsB,SAAA,GAA0F;UAA1FtB,EAAA,CAAAE,UAAA,WAAA0P,QAAA,GAAAlC,GAAA,CAAA3J,WAAA,CAAAC,GAAA,mCAAA4L,QAAA,CAAAC,OAAA,OAAAD,QAAA,GAAAlC,GAAA,CAAA3J,WAAA,CAAAC,GAAA,mCAAA4L,QAAA,CAAArE,OAAA,EAA0F;UAczEvL,EAAA,CAAAsB,SAAA,GAA6D;UAA7DtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA0P,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,WAAAC,MAAA,EAA6D;UAC9ExD,EAAA,CAAAsB,SAAA,EAAqC;UAArCtB,EAAA,CAAAE,UAAA,SAAAwN,GAAA,CAAA9J,SAAA,IAAA8J,GAAA,CAAAnK,CAAA,WAAAC,MAAA,CAAqC;UAMrCxD,EAAA,CAAAsB,SAAA,EAA8E;UAA9EtB,EAAA,CAAAE,UAAA,WAAA4P,QAAA,GAAApC,GAAA,CAAA3J,WAAA,CAAAC,GAAA,6BAAA8L,QAAA,CAAAD,OAAA,OAAAC,QAAA,GAAApC,GAAA,CAAA3J,WAAA,CAAAC,GAAA,6BAAA8L,QAAA,CAAAvE,OAAA,EAA8E;UAOtFvL,EAAA,CAAAsB,SAAA,EAA4C;UAA5CtB,EAAA,CAAAE,UAAA,SAAAwN,GAAA,CAAA5H,MAAA,IAAA4H,GAAA,CAAA3J,WAAA,CAAA6E,KAAA,CAAArC,UAAA,CAA4C;UA6BGvG,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAuP,UAAA,CAAAvP,EAAA,CAAAwP,eAAA,KAAAO,GAAA,EAA4B;UAA/E/P,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAAsP,gBAAA,YAAA5B,GAAA,CAAA/H,qBAAA,CAAmC;UACxD3F,EADsF,CAAAE,UAAA,qBAAoB,oBACvF;UAKbF,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAE,UAAA,cAAAwN,GAAA,CAAA3J,WAAA,CAAyB;UAMG/D,EAAA,CAAAsB,SAAA,GAA2B;UAEjBtB,EAFV,CAAAE,UAAA,UAAAF,EAAA,CAAAgQ,WAAA,UAAAtC,GAAA,CAAArD,SAAA,EAA2B,sBAA+C,YAAAqD,GAAA,CAAAxH,cAAA,CAClE,oBAAoB,cAAAwH,GAAA,CAAAvH,aAAA,CACnB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}