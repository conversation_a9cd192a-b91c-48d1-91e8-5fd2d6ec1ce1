{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/table\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/button\";\nfunction TasksContactsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 13);\n    i0.ɵɵtext(6, \" Job Title \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 14)(8, \"div\", 11);\n    i0.ɵɵtext(9, \" Phone \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 16);\n    i0.ɵɵtext(12, \" Mobile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 17)(14, \"div\", 11);\n    i0.ɵɵtext(15, \" E-Mail \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 19);\n    i0.ɵɵtext(18, \" Web Registered \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 19);\n    i0.ɵɵtext(20, \" Function \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 20)(22, \"div\", 11);\n    i0.ɵɵtext(23, \" Department \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\", 22);\n    i0.ɵɵtext(26, \" VIP Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 23);\n    i0.ɵɵtext(28, \" Comm. Preference \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksContactsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.job_title) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.email_address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.web_registered) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.contact_person_department_name == null ? null : contact_r1.contact_person_department_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.contact_person_department_name == null ? null : contact_r1.contact_person_department_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.vip_contact) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r1 == null ? null : contact_r1.communication_preference) || \"-\", \" \");\n  }\n}\nfunction TasksContactsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksContactsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"Loading contacts data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TasksContactsComponent = /*#__PURE__*/(() => {\n  class TasksContactsComponent {\n    constructor() {\n      this.contactDetails = null;\n    }\n    ngOnInit() {}\n    static {\n      this.ɵfac = function TasksContactsComponent_Factory(t) {\n        return new (t || TasksContactsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TasksContactsComponent,\n        selectors: [[\"app-tasks-contacts\"]],\n        decls: 11,\n        vars: 5,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"full_name\", 1, \"border-round-left-lg\", 2, \"width\", \"10%\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"full_name\"], [\"pSortableColumn\", \"job_title\", 2, \"width\", \"10%\"], [\"pSortableColumn\", \"phone_number\", 2, \"width\", \"10%\"], [\"field\", \"phone_number\"], [\"pSortableColumn\", \"mobile\", 2, \"width\", \"10%\"], [\"pSortableColumn\", \"email_address\", 2, \"width\", \"15%\"], [\"field\", \"email_address\"], [2, \"width\", \"10%\"], [\"pSortableColumn\", \"contact_person_department_name\", 2, \"width\", \"10%\"], [\"field\", \"contact_person_department_name\"], [2, \"width\", \"7%\"], [2, \"width\", \"8%\"], [1, \"border-round-left-lg\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\"]],\n        template: function TasksContactsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Contacts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"p-button\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n            i0.ɵɵtemplate(7, TasksContactsComponent_ng_template_7_Template, 29, 0, \"ng-template\", 6)(8, TasksContactsComponent_ng_template_8_Template, 21, 10, \"ng-template\", 7)(9, TasksContactsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, TasksContactsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.contactDetails)(\"rows\", 10)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.Table, i2.PrimeTemplate, i1.SortableColumn, i1.SortIcon, i3.Button]\n      });\n    }\n  }\n  return TasksContactsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}