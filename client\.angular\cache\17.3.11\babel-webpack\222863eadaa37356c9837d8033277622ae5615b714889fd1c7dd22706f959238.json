{"ast": null, "code": "import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport const queueScheduler = new QueueScheduler(QueueAction);\nexport const queue = queueScheduler;", "map": {"version": 3, "names": ["QueueAction", "QueueScheduler", "queueScheduler", "queue"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/rxjs/dist/esm/internal/scheduler/queue.js"], "sourcesContent": ["import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport const queueScheduler = new QueueScheduler(QueueAction);\nexport const queue = queueScheduler;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,cAAc,GAAG,IAAID,cAAc,CAACD,WAAW,CAAC;AAC7D,OAAO,MAAMG,KAAK,GAAGD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}