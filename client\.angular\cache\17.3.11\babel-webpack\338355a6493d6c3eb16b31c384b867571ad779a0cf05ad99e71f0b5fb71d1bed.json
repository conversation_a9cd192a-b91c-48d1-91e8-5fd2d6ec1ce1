{"ast": null, "code": "import { HttpParams } from \"@angular/common/http\";\nimport { ApiConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ReturnOrderService = /*#__PURE__*/(() => {\n  class ReturnOrderService {\n    constructor(http) {\n      this.http = http;\n    }\n    getAll(data) {\n      let params = new HttpParams().appendAll(data);\n      return this.http.get(`${ApiConstant['RETURN_ORDER']}`, {\n        params\n      });\n    }\n    getAllRefundProgress() {\n      return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REFUND_PROGRESS');\n    }\n    getAllStatus() {\n      return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_STATUS');\n    }\n    getRetrunOrderDetails(data) {\n      return this.http.get(ApiConstant['RETURN_ORDER'] + `/${data.SD_DOC}`, data);\n    }\n    getAllReturnReason() {\n      return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REASON');\n    }\n    static {\n      this.ɵfac = function ReturnOrderService_Factory(t) {\n        return new (t || ReturnOrderService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ReturnOrderService,\n        factory: ReturnOrderService.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return ReturnOrderService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}