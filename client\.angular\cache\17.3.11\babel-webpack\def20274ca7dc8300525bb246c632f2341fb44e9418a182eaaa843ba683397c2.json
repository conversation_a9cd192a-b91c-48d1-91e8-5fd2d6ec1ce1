{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../account.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/dropdown\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/common\";\nfunction AccountSalesQuoteDetailsComponent_ng_template_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 36);\n    i0.ɵɵtext(2, \"Item Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 37);\n    i0.ɵɵtext(4, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 38);\n    i0.ɵɵtext(6, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuoteDetailsComponent_ng_template_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39)(2, \"div\", 40)(3, \"div\", 41)(4, \"h5\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 44)(9, \"p\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 46)(13, \"p\", 47);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 48);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const items_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(items_r1.SHORT_TEXT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1.MATERIAL, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 6, items_r1.REQ_QTY), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.formatted_base_price, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.formatted_base_price_each, \" each \");\n  }\n}\nexport class AccountSalesQuoteDetailsComponent {\n  constructor(route, router, accountservice) {\n    this.route = route;\n    this.router = router;\n    this.accountservice = accountservice;\n    this.quoteDetail = null;\n    this.unsubscribe$ = new Subject();\n    this.statuses = [];\n  }\n  ngOnInit() {\n    this.Actions = [{\n      name: 'Change Quote',\n      code: 'CQ'\n    }];\n    this.quoteData = history.state.quoteData;\n    this.customerData = history.state.customerData;\n    this.accountservice.getQuoteDetails(this.quoteData).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      this.quoteDetail = response?.SALESQUOTE;\n      if (this.quoteDetail?.QUOTE_HDR?.DOC_DATE) {\n        const rawDate = this.quoteDetail.QUOTE_HDR.DOC_DATE.toString(); // Ensure it's a string\n        if (rawDate.length === 8) {\n          this.quoteDetail.QUOTE_HDR.DOC_DATE = `${rawDate.substring(4, 6)}/${rawDate.substring(6, 8)}/${rawDate.substring(0, 4)}`;\n        } else {\n          this.quoteDetail.QUOTE_HDR.DOC_DATE = '-';\n        }\n      }\n    }, error => {\n      console.error('Error fetching quote details:', error);\n    });\n  }\n  getStatusName(code) {\n    const status = this.statuses.find(o => o.code === code);\n    if (status) {\n      return status.description;\n    }\n    return '';\n  }\n  NavigatetoChangeQuote(event) {\n    if (!event) return;\n    if (event.code == 'CQ') {\n      const url = `https://my417602.s4hana.cloud.sap/ui#SalesQuotation-manageV2&/SalesQuotationManage('${this.quoteData.SD_DOC}')`;\n      window.open(url, '_blank');\n    }\n  }\n  goToBack() {\n    window.history.back();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountSalesQuoteDetailsComponent_Factory(t) {\n      return new (t || AccountSalesQuoteDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSalesQuoteDetailsComponent,\n      selectors: [[\"app-account-sales-quote-details\"]],\n      decls: 106,\n      vars: 13,\n      consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"pt-0\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"filter-sec\", \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"pt-0\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"gap-5\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"min-w-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"table-data\", \"border-round\", \"overflow-hidden\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"lg:w-30rem\", \"md:w-30rem\", \"sm:w-full\", \"pt-0\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"mt-2\", \"mb-4\", \"uppercase\", \"text-center\", \"text-primary\"], [1, \"cart-sidebar-price\", \"py-4\", \"border-none\", \"border-y-1\", \"border-solid\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"font-semibold\"], [1, \"text-color-secondary\"], [1, \"cart-sidebar-t-price\", \"py-4\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"text-primary\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"]],\n      template: function AccountSalesQuoteDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n          i0.ɵɵtext(5, \"Quote Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function AccountSalesQuoteDetailsComponent_Template_button_click_7_listener() {\n            return ctx.goToBack();\n          });\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-dropdown\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSalesQuoteDetailsComponent_Template_p_dropdown_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function AccountSalesQuoteDetailsComponent_Template_p_dropdown_onChange_11_listener($event) {\n            return ctx.NavigatetoChangeQuote($event.value);\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 2)(14, \"div\", 10)(15, \"h4\", 4);\n          i0.ɵɵtext(16, \"Quote Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"ul\", 12)(19, \"li\", 13)(20, \"div\", 14)(21, \"i\", 15);\n          i0.ɵɵtext(22, \"tag\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"h6\", 17);\n          i0.ɵɵtext(25, \"Quote #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 18);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"li\", 13)(29, \"div\", 14)(30, \"i\", 15);\n          i0.ɵɵtext(31, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"h6\", 17);\n          i0.ɵɵtext(34, \"Customer #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\", 18);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"li\", 13)(38, \"div\", 14)(39, \"i\", 15);\n          i0.ɵɵtext(40, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 16)(42, \"h6\", 17);\n          i0.ɵɵtext(43, \"Customer Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\", 18);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"li\", 13)(47, \"div\", 14)(48, \"i\", 15);\n          i0.ɵɵtext(49, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 16)(51, \"h6\", 17);\n          i0.ɵɵtext(52, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"p\", 18);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"li\", 13)(56, \"div\", 14)(57, \"i\", 15);\n          i0.ɵɵtext(58, \"event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 16)(60, \"h6\", 17);\n          i0.ɵɵtext(61, \"Date Placed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\", 18);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(64, \"div\", 2)(65, \"div\", 10)(66, \"h4\", 4);\n          i0.ɵɵtext(67, \"Quote Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 11)(69, \"ul\", 19)(70, \"li\", 13)(71, \"div\", 20)(72, \"i\", 15);\n          i0.ɵɵtext(73, \"tag\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 16)(75, \"h6\", 17);\n          i0.ɵɵtext(76, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\", 18);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(79, \"div\", 21)(80, \"div\", 22)(81, \"h4\", 4);\n          i0.ɵɵtext(82, \"Items\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 23)(84, \"p-table\", 24);\n          i0.ɵɵtemplate(85, AccountSalesQuoteDetailsComponent_ng_template_85_Template, 7, 0, \"ng-template\", 25)(86, AccountSalesQuoteDetailsComponent_ng_template_86_Template, 17, 8, \"ng-template\", 26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(87, \"div\", 27)(88, \"div\", 28)(89, \"h5\", 29);\n          i0.ɵɵtext(90, \"Quote Summary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 30)(92, \"ul\", 31)(93, \"li\", 32)(94, \"span\", 33);\n          i0.ɵɵtext(95, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(96);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"li\", 32)(98, \"span\", 33);\n          i0.ɵɵtext(99, \"Subtotal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(100);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 34)(102, \"h5\", 35);\n          i0.ɵɵtext(103, \"Total \");\n          i0.ɵɵelementStart(104, \"span\");\n          i0.ɵɵtext(105);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_NUMBER) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.customerData == null ? null : ctx.customerData.customer_id) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.customerData == null ? null : ctx.customerData.customer_name) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_NAME) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_DATE);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR_TEXT[0] == null ? null : ctx.quoteDetail.QUOTE_HDR_TEXT[0].TEXT);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_LINE_DETAIL);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusName(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_STAT) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.quoteDetail == null ? null : ctx.quoteDetail.formatted_sub_total) || \"$0.00\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.formatted_total) || \"$0.00\");\n        }\n      },\n      dependencies: [i3.PrimeTemplate, i4.Dropdown, i5.Table, i6.NgControlStatus, i6.NgModel, i7.DecimalPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "items_r1", "SHORT_TEXT", "ɵɵtextInterpolate1", "MATERIAL", "ɵɵpipeBind1", "REQ_QTY", "formatted_base_price", "formatted_base_price_each", "AccountSalesQuoteDetailsComponent", "constructor", "route", "router", "accountservice", "quoteDetail", "unsubscribe$", "statuses", "ngOnInit", "Actions", "name", "code", "quoteData", "history", "state", "customerData", "getQuoteDetails", "pipe", "subscribe", "response", "SALESQUOTE", "QUOTE_HDR", "DOC_DATE", "rawDate", "toString", "length", "substring", "error", "console", "getStatusName", "status", "find", "o", "description", "NavigatetoChangeQuote", "event", "url", "SD_DOC", "window", "open", "goToBack", "back", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountSalesQuoteDetailsComponent_Template", "rf", "ctx", "ɵɵlistener", "AccountSalesQuoteDetailsComponent_Template_button_click_7_listener", "ɵɵtwoWayListener", "AccountSalesQuoteDetailsComponent_Template_p_dropdown_ngModelChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "AccountSalesQuoteDetailsComponent_Template_p_dropdown_onChange_11_listener", "value", "ɵɵtemplate", "AccountSalesQuoteDetailsComponent_ng_template_85_Template", "AccountSalesQuoteDetailsComponent_ng_template_86_Template", "ɵɵtwoWayProperty", "DOC_NUMBER", "customer_id", "customer_name", "DOC_NAME", "QUOTE_HDR_TEXT", "TEXT", "QUOTE_LINE_DETAIL", "DOC_STAT", "formatted_sub_total", "formatted_total"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-quotes\\account-sales-quote-details\\account-sales-quote-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-quotes\\account-sales-quote-details\\account-sales-quote-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AccountService } from '../../../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-account-sales-quote-details',\r\n  templateUrl: './account-sales-quote-details.component.html',\r\n  styleUrl: './account-sales-quote-details.component.scss',\r\n})\r\nexport class AccountSalesQuoteDetailsComponent implements OnInit {\r\n  public quoteDetail: any = null;\r\n  public quoteData: any;\r\n  public customerData: any;\r\n  private unsubscribe$ = new Subject<void>();\r\n  public statuses: any = [];\r\n  Actions: Actions[] | undefined;\r\n  selectedActions: Actions | undefined;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private accountservice: AccountService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.Actions = [{ name: 'Change Quote', code: 'CQ' }];\r\n    this.quoteData = history.state.quoteData;\r\n    this.customerData = history.state.customerData;\r\n    this.accountservice\r\n      .getQuoteDetails(this.quoteData)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(\r\n        (response) => {\r\n          this.quoteDetail = response?.SALESQUOTE;\r\n          if (this.quoteDetail?.QUOTE_HDR?.DOC_DATE) {\r\n            const rawDate = this.quoteDetail.QUOTE_HDR.DOC_DATE.toString(); // Ensure it's a string\r\n            if (rawDate.length === 8) {\r\n              this.quoteDetail.QUOTE_HDR.DOC_DATE = `${rawDate.substring(\r\n                4,\r\n                6\r\n              )}/${rawDate.substring(6, 8)}/${rawDate.substring(0, 4)}`;\r\n            } else {\r\n              this.quoteDetail.QUOTE_HDR.DOC_DATE = '-';\r\n            }\r\n          }\r\n        },\r\n        (error) => {\r\n          console.error('Error fetching quote details:', error);\r\n        }\r\n      );\r\n  }\r\n\r\n  getStatusName(code: string) {\r\n    const status = this.statuses.find((o: any) => o.code === code);\r\n    if (status) {\r\n      return status.description;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  NavigatetoChangeQuote(event: any): void {\r\n    if (!event) return;\r\n    if (event.code == 'CQ') {\r\n      const url = `https://my417602.s4hana.cloud.sap/ui#SalesQuotation-manageV2&/SalesQuotationManage('${this.quoteData.SD_DOC}')`;\r\n      window.open(url, '_blank');\r\n    }\r\n  }\r\n\r\n  goToBack() {\r\n    window.history.back();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"grid mt-0 relative\">\r\n    <div class=\"col-12 pt-0\">\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-between gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Quote Details</h4>\r\n                <div class=\"filter-sec flex align-items-center gap-2\">\r\n                    <button type=\"button\" (click)=\"goToBack()\"\r\n                    class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                        <span class=\"material-symbols-rounded text-2xl\">arrow_back</span> Back\r\n                    </button>\r\n                    <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\"\r\n                        placeholder=\"Action\" (onChange)=\"NavigatetoChangeQuote($event.value)\"\r\n                        [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:flex-1 md:flex-1 pt-0\">\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Quote Information</h4>\r\n            </div>\r\n\r\n            <div class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <ul class=\"order-details-list m-0 p-0 d-grid gap-5\">\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">tag</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Quote #</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ quoteDetail?.QUOTE_HDR?.DOC_NUMBER || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">person</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Customer #</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ customerData?.customer_id || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">person</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Customer Name</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ customerData?.customer_name || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">person</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Name</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ quoteDetail?.QUOTE_HDR?.DOC_NAME || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">event</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Date Placed</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ quoteDetail?.QUOTE_HDR?.DOC_DATE }}</p>\r\n                        </div>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Quote Description</h4>\r\n            </div>\r\n            <div class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <ul class=\"order-details-list m-0 p-0 gap-5\">\r\n                    <li class=\"flex align-items-center gap-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem min-w-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">tag</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Description</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ quoteDetail?.QUOTE_HDR_TEXT[0]?.TEXT }}</p>\r\n                        </div>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading mb-3 flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Items</h4>\r\n            </div>\r\n            <div class=\"table-data border-round overflow-hidden\">\r\n                <p-table [value]=\"quoteDetail?.QUOTE_LINE_DETAIL\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th class=\"surface-50 px-4 py-3 text-700 font-semibold uppercase\">Item Details</th>\r\n                            <th class=\"surface-50 py-3 text-700 font-semibold uppercase\">Quantity</th>\r\n                            <th class=\"surface-50 py-3 px-4 text-700 font-semibold uppercase text-right\">Price</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-items>\r\n                        <tr>\r\n                            <td class=\"px-0 py-4 border-none border-bottom-1 border-solid border-50\" [width]=\"'60%'\">\r\n                                <div class=\"relative flex gap-3\">\r\n                                    <!-- <div\r\n                                        class=\"flex align-items-center justify-content-center w-9rem h-9rem overflow-hidden border-round border-1 border-solid border-50\">\r\n                                        <img [src]=\"items.imageUrl\" class=\"w-full h-full object-fit-contain\" />\r\n                                    </div> -->\r\n                                    <div class=\"flex flex-column\">\r\n                                        <h5 class=\"my-2 text-lg\">{{items.SHORT_TEXT}}</h5>\r\n                                        <p class=\"m-0 text-sm font-semibold text-color-secondary\">\r\n                                            {{items.MATERIAL}}\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            </td>\r\n                            <td class=\"py-4 border-none border-bottom-1 border-solid border-50 vertical-align-top\">\r\n                                <p\r\n                                    class=\"m-0 py-2 font-semibold text-color-secondary border-1 border-round surface-border text-center\">\r\n                                    {{items.REQ_QTY | number }}\r\n                                </p>\r\n                            </td>\r\n                            <td class=\"py-4 px-4 border-none border-bottom-1 border-solid border-50 vertical-align-top\">\r\n                                <p class=\"m-0 text-lg font-semibold text-right\">\r\n                                    {{items?.formatted_base_price}}\r\n                                </p>\r\n                                <p class=\"m-0 font-semibold text-color-secondary text-right\">\r\n                                    {{items?.formatted_base_price_each}} each\r\n                                </p>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:w-30rem md:w-30rem sm:w-full pt-0\">\r\n        <div class=\"p-4 mb-0 w-full bg-white border-round shadow-1 overflow-hidden\">\r\n            <h5 class=\"mt-2 mb-4 uppercase text-center text-primary\">Quote Summary</h5>\r\n            <div class=\"cart-sidebar-price py-4 border-none border-y-1 border-solid surface-border\">\r\n                <ul class=\"flex flex-column gap-3 p-0 m-0\">\r\n                    <li class=\"flex align-items-center justify-content-between font-semibold\">\r\n                        <span class=\"text-color-secondary\">Status</span>\r\n                        {{ getStatusName(quoteDetail?.QUOTE_HDR?.DOC_STAT) || '-' }}\r\n                    </li>\r\n                    <li class=\"flex align-items-center justify-content-between font-semibold\">\r\n                        <span class=\"text-color-secondary\">Subtotal</span> {{ quoteDetail?.formatted_sub_total ||\r\n                        \"$0.00\" }}\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"cart-sidebar-t-price py-4\">\r\n                <h5 class=\"mb-2 flex align-items-center justify-content-between text-primary\">Total\r\n                    <span>{{ quoteDetail?.formatted_total || \"$0.00\" }}</span>\r\n                </h5>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICwGbC,EADJ,CAAAC,cAAA,SAAI,aACkE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,aAA6D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,aAA6E;IAAAD,EAAA,CAAAE,MAAA,YAAK;IACtFF,EADsF,CAAAG,YAAA,EAAK,EACtF;;;;;IAWWH,EARhB,CAAAC,cAAA,SAAI,aACyF,cACpD,cAKC,aACD;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,YAA0D;IACtDD,EAAA,CAAAE,MAAA,GACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAuF,YAEsB;IACrGD,EAAA,CAAAE,MAAA,IACJ;;IACJF,EADI,CAAAG,YAAA,EAAI,EACH;IAEDH,EADJ,CAAAC,cAAA,cAA4F,aACxC;IAC5CD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAA6D;IACzDD,EAAA,CAAAE,MAAA,IACJ;IAERF,EAFQ,CAAAG,YAAA,EAAI,EACH,EACJ;;;;IA5BwEH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAOnDL,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAM,iBAAA,CAAAC,QAAA,CAAAC,UAAA,CAAoB;IAEzCR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAS,kBAAA,MAAAF,QAAA,CAAAG,QAAA,MACJ;IAOJV,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAW,WAAA,QAAAJ,QAAA,CAAAK,OAAA,OACJ;IAIIZ,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAS,kBAAA,MAAAF,QAAA,kBAAAA,QAAA,CAAAM,oBAAA,MACJ;IAEIb,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAS,kBAAA,MAAAF,QAAA,kBAAAA,QAAA,CAAAO,yBAAA,WACJ;;;AD7HhC,OAAM,MAAOC,iCAAiC;EAS5CC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B;IAF9B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAXjB,KAAAC,WAAW,GAAQ,IAAI;IAGtB,KAAAC,YAAY,GAAG,IAAIvB,OAAO,EAAQ;IACnC,KAAAwB,QAAQ,GAAQ,EAAE;EAQtB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,OAAO,GAAG,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE,CAAC;IACrD,IAAI,CAACC,SAAS,GAAGC,OAAO,CAACC,KAAK,CAACF,SAAS;IACxC,IAAI,CAACG,YAAY,GAAGF,OAAO,CAACC,KAAK,CAACC,YAAY;IAC9C,IAAI,CAACX,cAAc,CAChBY,eAAe,CAAC,IAAI,CAACJ,SAAS,CAAC,CAC/BK,IAAI,CAACjC,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClCY,SAAS,CACPC,QAAQ,IAAI;MACX,IAAI,CAACd,WAAW,GAAGc,QAAQ,EAAEC,UAAU;MACvC,IAAI,IAAI,CAACf,WAAW,EAAEgB,SAAS,EAAEC,QAAQ,EAAE;QACzC,MAAMC,OAAO,GAAG,IAAI,CAAClB,WAAW,CAACgB,SAAS,CAACC,QAAQ,CAACE,QAAQ,EAAE,CAAC,CAAC;QAChE,IAAID,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;UACxB,IAAI,CAACpB,WAAW,CAACgB,SAAS,CAACC,QAAQ,GAAG,GAAGC,OAAO,CAACG,SAAS,CACxD,CAAC,EACD,CAAC,CACF,IAAIH,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIH,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3D,CAAC,MAAM;UACL,IAAI,CAACrB,WAAW,CAACgB,SAAS,CAACC,QAAQ,GAAG,GAAG;QAC3C;MACF;IACF,CAAC,EACAK,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,CACF;EACL;EAEAE,aAAaA,CAAClB,IAAY;IACxB,MAAMmB,MAAM,GAAG,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACrB,IAAI,KAAKA,IAAI,CAAC;IAC9D,IAAImB,MAAM,EAAE;MACV,OAAOA,MAAM,CAACG,WAAW;IAC3B;IACA,OAAO,EAAE;EACX;EAEAC,qBAAqBA,CAACC,KAAU;IAC9B,IAAI,CAACA,KAAK,EAAE;IACZ,IAAIA,KAAK,CAACxB,IAAI,IAAI,IAAI,EAAE;MACtB,MAAMyB,GAAG,GAAG,uFAAuF,IAAI,CAACxB,SAAS,CAACyB,MAAM,IAAI;MAC5HC,MAAM,CAACC,IAAI,CAACH,GAAG,EAAE,QAAQ,CAAC;IAC5B;EACF;EAEAI,QAAQA,CAAA;IACNF,MAAM,CAACzB,OAAO,CAAC4B,IAAI,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpC,YAAY,CAACqC,IAAI,EAAE;IACxB,IAAI,CAACrC,YAAY,CAACsC,QAAQ,EAAE;EAC9B;;;uBAlEW5C,iCAAiC,EAAAf,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/D,EAAA,CAAA4D,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjClD,iCAAiC;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9BxE,EAJhB,CAAAC,cAAA,aAAgC,aACH,aACuC,aACwB,YACxB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElEH,EADJ,CAAAC,cAAA,aAAsD,gBAEoF;UADhHD,EAAA,CAAA0E,UAAA,mBAAAC,mEAAA;YAAA,OAASF,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAEtCvD,EAAA,CAAAC,cAAA,cAAgD;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACtE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,qBAEyG;UAFzED,EAAA,CAAA4E,gBAAA,2BAAAC,gFAAAC,MAAA;YAAA9E,EAAA,CAAA+E,kBAAA,CAAAN,GAAA,CAAAO,eAAA,EAAAF,MAAA,MAAAL,GAAA,CAAAO,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UACpC9E,EAAA,CAAA0E,UAAA,sBAAAO,2EAAAH,MAAA;YAAA,OAAYL,GAAA,CAAAxB,qBAAA,CAAA6B,MAAA,CAAAI,KAAA,CAAmC;UAAA,EAAC;UAKzFlF,EANgB,CAAAG,YAAA,EAEyG,EACvG,EACJ,EACJ,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA6C,cACmB,eACsB,aACtB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzEF,EADyE,CAAAG,YAAA,EAAK,EACxE;UAOUH,EALhB,CAAAC,cAAA,eAAiG,cACzC,cACN,eAEkE,aAChE;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EACzC;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAE3FF,EAF2F,CAAAG,YAAA,EAAI,EACrF,EACL;UAIGH,EAHR,CAAAC,cAAA,cAA0C,eAEkE,aAChE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAElFF,EAFkF,CAAAG,YAAA,EAAI,EAC5E,EACL;UAIGH,EAHR,CAAAC,cAAA,cAA0C,eAEkE,aAChE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAAwC;UAEpFF,EAFoF,CAAAG,YAAA,EAAI,EAC9E,EACL;UAIGH,EAHR,CAAAC,cAAA,cAA0C,eAEkE,aAChE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UAEzFF,EAFyF,CAAAG,YAAA,EAAI,EACnF,EACL;UAIGH,EAHR,CAAAC,cAAA,cAA0C,eAEkE,aAChE;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAC7CF,EAD6C,CAAAG,YAAA,EAAI,EAC3C;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAK9FF,EAL8F,CAAAG,YAAA,EAAI,EAC5E,EACL,EACJ,EACH,EACJ;UAIEH,EAFR,CAAAC,cAAA,cAA4D,eACsB,aACtB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzEF,EADyE,CAAAG,YAAA,EAAK,EACxE;UAMUH,EALhB,CAAAC,cAAA,eAAiG,cAChD,cACC,eAE6E,aAC3E;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EACzC;UAEFH,EADJ,CAAAC,cAAA,eAAkB,cACE;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,IAA0C;UAKlGF,EALkG,CAAAG,YAAA,EAAI,EAChF,EACL,EACJ,EACH,EACJ;UAIEH,EAFR,CAAAC,cAAA,eAAuD,eACgC,aAC3B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAC7DF,EAD6D,CAAAG,YAAA,EAAK,EAC5D;UAEFH,EADJ,CAAAC,cAAA,eAAqD,mBACC;UAQ9CD,EAPA,CAAAmF,UAAA,KAAAC,yDAAA,0BAAgC,KAAAC,yDAAA,2BAOQ;UAoCxDrF,EAJY,CAAAG,YAAA,EAAU,EACR,EAEJ,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAyD,eACuB,cACf;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI/DH,EAHZ,CAAAC,cAAA,eAAwF,cACzC,cACmC,gBACnC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0E,gBACnC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,KAEvD;UAERF,EAFQ,CAAAG,YAAA,EAAK,EACJ,EACH;UAEFH,EADJ,CAAAC,cAAA,gBAAuC,eAC2C;UAAAD,EAAA,CAAAE,MAAA,eAC1E;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAA6C;UAKvEF,EALuE,CAAAG,YAAA,EAAO,EACzD,EACH,EACJ,EACJ,EACJ;;;UAjK0BH,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,UAAA,YAAAoE,GAAA,CAAAjD,OAAA,CAAmB;UAACxB,EAAA,CAAAsF,gBAAA,YAAAb,GAAA,CAAAO,eAAA,CAA6B;UAEzDhF,EAAA,CAAAK,UAAA,mGAAkG;UAoB1DL,EAAA,CAAAI,SAAA,IAA+C;UAA/CJ,EAAA,CAAAM,iBAAA,EAAAmE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAgB,SAAA,kBAAAqC,GAAA,CAAArD,WAAA,CAAAgB,SAAA,CAAAmD,UAAA,SAA+C;UAU/CvF,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAM,iBAAA,EAAAmE,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAA0D,WAAA,SAAsC;UAUtCxF,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAM,iBAAA,EAAAmE,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAA2D,aAAA,SAAwC;UAUxCzF,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAM,iBAAA,EAAAmE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAgB,SAAA,kBAAAqC,GAAA,CAAArD,WAAA,CAAAgB,SAAA,CAAAsD,QAAA,SAA6C;UAU7C1F,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAM,iBAAA,CAAAmE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAgB,SAAA,kBAAAqC,GAAA,CAAArD,WAAA,CAAAgB,SAAA,CAAAC,QAAA,CAAsC;UAoBtCrC,EAAA,CAAAI,SAAA,IAA0C;UAA1CJ,EAAA,CAAAM,iBAAA,CAAAmE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAuE,cAAA,qBAAAlB,GAAA,CAAArD,WAAA,CAAAuE,cAAA,IAAAC,IAAA,CAA0C;UAYjF5F,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAK,UAAA,UAAAoE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAyE,iBAAA,CAAwC;UAoDzC7F,EAAA,CAAAI,SAAA,IACJ;UADIJ,EAAA,CAAAS,kBAAA,MAAAgE,GAAA,CAAA7B,aAAA,CAAA6B,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAAgB,SAAA,kBAAAqC,GAAA,CAAArD,WAAA,CAAAgB,SAAA,CAAA0D,QAAA,cACJ;UAEuD9F,EAAA,CAAAI,SAAA,GAEvD;UAFuDJ,EAAA,CAAAS,kBAAA,OAAAgE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAA2E,mBAAA,kBAEvD;UAKM/F,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAM,iBAAA,EAAAmE,GAAA,CAAArD,WAAA,kBAAAqD,GAAA,CAAArD,WAAA,CAAA4E,eAAA,aAA6C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}