{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"src/app/store/account/account.service\";\nimport * as i5 from \"src/app/store/prospects/prospects.service\";\nimport * as i6 from \"../../contacts/contacts.service\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"primeng/editor\";\nimport * as i14 from \"primeng/dropdown\";\nimport * as i15 from \"primeng/calendar\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nfunction ActivitiesFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Sales Call\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Document Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_12_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"document_type\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_21_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_ng_template_37_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\":\", item_r3.email, \"\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_37_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\":\", item_r3.mobile, \"\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesFormComponent_ng_template_37_span_3_Template, 2, 1, \"span\", 54)(4, ActivitiesFormComponent_ng_template_37_span_4_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r3.bp_id, \":\", item_r3.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.mobile);\n  }\n}\nfunction ActivitiesFormComponent_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_38_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_47_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_div_76_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_76_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_div_85_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_85_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_div_94_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesFormComponent_div_94_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"notes\"].errors && ctx_r1.f[\"notes\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesFormComponent_ng_template_104_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 59)(2, \"div\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 56);\n    i0.ɵɵtext(2, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesFormComponent_ng_template_104_ng_container_3_Template, 4, 1, \"ng-container\", 57);\n    i0.ɵɵelementStart(4, \"th\", 58);\n    i0.ɵɵtext(5, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ActivitiesFormComponent_ng_template_105_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"input\", 66);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ActivitiesFormComponent_ng_template_105_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 64);\n    i0.ɵɵtemplate(3, ActivitiesFormComponent_ng_template_105_ng_container_3_ng_container_3_Template, 2, 0, \"ng-container\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_105_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_ng_template_105_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r7 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_ng_template_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 61)(1, \"td\", 56);\n    i0.ɵɵelement(2, \"input\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesFormComponent_ng_template_105_ng_container_3_Template, 4, 2, \"ng-container\", 57);\n    i0.ɵɵelementStart(4, \"td\", 58);\n    i0.ɵɵtemplate(5, ActivitiesFormComponent_ng_template_105_button_5_Template, 1, 0, \"button\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.involved_parties.length > 1);\n  }\n}\nfunction ActivitiesFormComponent_ng_template_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesFormComponent_ng_template_117_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_117_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_117_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction ActivitiesFormComponent_ng_template_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesFormComponent_ng_template_117_span_2_Template, 2, 1, \"span\", 54)(3, ActivitiesFormComponent_ng_template_117_span_3_Template, 2, 1, \"span\", 54)(4, ActivitiesFormComponent_ng_template_117_span_4_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile);\n  }\n}\nexport let ActivitiesFormComponent = /*#__PURE__*/(() => {\n  class ActivitiesFormComponent {\n    constructor(formBuilder, route, activitiesservice, accountservice, prospectsservice, contactsservice, messageservice) {\n      this.formBuilder = formBuilder;\n      this.route = route;\n      this.activitiesservice = activitiesservice;\n      this.accountservice = accountservice;\n      this.prospectsservice = prospectsservice;\n      this.contactsservice = contactsservice;\n      this.messageservice = messageservice;\n      this.unsubscribe$ = new Subject();\n      this.account_id = '';\n      this.route_id = '';\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.visible = false;\n      this.addDialogVisible = false;\n      this.existingDialogVisible = false;\n      this.defaultOptions = [];\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.existingcontactLoading = false;\n      this.existingcontactInput$ = new Subject();\n      this.owner_id = null;\n      this.ActivityForm = this.formBuilder.group({\n        document_type: ['', [Validators.required]],\n        subject: ['', [Validators.required]],\n        main_contact_party_id: ['', [Validators.required]],\n        phone_call_category: ['', [Validators.required]],\n        disposition_code: [''],\n        start_date: [''],\n        end_date: [''],\n        initiator_code: ['', [Validators.required]],\n        activity_status: ['', [Validators.required]],\n        notes: ['', [Validators.required]],\n        contactexisting: [''],\n        involved_parties: this.formBuilder.array([this.createContactFormGroup()])\n      });\n      this.dropdowns = {\n        activityDocumentType: [],\n        activityCategory: [],\n        activityStatus: [],\n        activitydisposition: [],\n        activityInitiatorCode: []\n      };\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'email',\n        header: 'Email Address'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    ngOnInit() {\n      this.route_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n      if (this.account_id) {\n        this.loadAccountByContacts(this.account_id);\n      }\n      this.loadExistingContacts();\n      this.getOwner().subscribe({\n        next: response => {\n          this.owner_id = response;\n        },\n        error: err => {\n          console.error('Error fetching bp_id:', err);\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    TableColumnReorder(event) {\n      const draggedCol = this.cols[event.dragIndex];\n      this.cols.splice(event.dragIndex, 1);\n      this.cols.splice(event.dropIndex, 0, draggedCol);\n    }\n    getOwner() {\n      return this.activitiesservice.getEmailwisePartner();\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        const options = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n        // Assign options to dropdown object\n        this.dropdowns[target] = options;\n        // Set 'Open' as default selected for activityStatus only\n        if (target === 'activityStatus') {\n          const openOption = options.find(opt => opt.label.toLowerCase() === 'open');\n          if (openOption) {\n            const control = this.ActivityForm?.get('activity_status');\n            if (control) {\n              control.setValue(openOption.value);\n            }\n          }\n        }\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadExistingContacts() {\n      this.existingcontacts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.existingcontactInput$.pipe(distinctUntilChanged(), tap(() => this.existingcontactLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP001',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.activitiesservice.getPartners(params).pipe(map(data => {\n          return data || []; // Make sure to return correct data structure\n        }), tap(() => this.existingcontactLoading = false), catchError(error => {\n          this.existingcontactLoading = false;\n          return of([]);\n        }));\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    selectExistingContact() {\n      this.addExistingContact(this.ActivityForm.value);\n      this.existingDialogVisible = false; // Close dialog\n    }\n    addExistingContact(existing) {\n      const contactForm = this.formBuilder.group({\n        bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\n        email_address: [existing?.contactexisting?.email || ''],\n        role_code: 'BUP001',\n        party_id: existing?.contactexisting?.bp_id || ''\n      });\n      const firstGroup = this.involved_parties.at(0);\n      const bpName = firstGroup?.get('bp_full_name')?.value;\n      if (!bpName && this.involved_parties.length === 1) {\n        // Replace the default empty group\n        this.involved_parties.setControl(0, contactForm);\n      } else {\n        // Otherwise, add a new contact\n        this.involved_parties.push(contactForm);\n      }\n      this.existingDialogVisible = false; // Close dialog\n    }\n    deleteContact(index) {\n      if (this.involved_parties.length > 1) {\n        this.involved_parties.removeAt(index);\n      }\n    }\n    createContactFormGroup() {\n      return this.formBuilder.group({\n        bp_full_name: [''],\n        email_address: ['']\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.ActivityForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.ActivityForm.value\n        };\n        const data = {\n          document_type: value?.document_type,\n          subject: value?.subject,\n          main_account_party_id: _this.account_id,\n          main_contact_party_id: value?.main_contact_party_id,\n          phone_call_category: value?.phone_call_category,\n          start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n          end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n          disposition_code: value?.disposition_code,\n          initiator_code: value?.initiator_code,\n          owner_party_id: _this.owner_id,\n          activity_status: value?.activity_status,\n          note: value?.notes,\n          involved_parties: Array.isArray(value.involved_parties) ? [...value.involved_parties, ...(value?.main_account_party_id ? [{\n            role_code: 'FLCU01',\n            party_id: value.main_account_party_id\n          }] : []), ...(Array.isArray(value.main_contact_party_id) ? value.main_contact_party_id.map(id => ({\n            role_code: 'BUP001',\n            party_id: id\n          })) : []), ...(_this.owner_id ? [{\n            role_code: 'BUP003',\n            party_id: _this.owner_id\n          }] : [])] : []\n        };\n        _this.activitiesservice.createActivity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.addDialogVisible = false;\n            _this.saving = false;\n            _this.visible = false;\n            _this.ActivityForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Activities Added Successfully!'\n            });\n            if (_this.route.parent?.snapshot.data['breadcrumb'] === 'Account') {\n              _this.accountservice.getAccountByID(_this.route_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            } else if (_this.route.parent?.snapshot.data['breadcrumb'] === 'Prospects') {\n              _this.prospectsservice.getProspectByID(_this.route_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            } else {\n              _this.contactsservice.getContactByID(_this.route_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            }\n          },\n          error: res => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.ActivityForm.controls;\n    }\n    get involved_parties() {\n      return this.ActivityForm.get('involved_parties');\n    }\n    showExistingDialog(position) {\n      this.position = position;\n      this.existingDialogVisible = true;\n    }\n    showDialog(position) {\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.ActivityForm.reset();\n      setTimeout(() => {\n        this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n        this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n        this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n        this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n        this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n      }, 50);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ActivitiesFormComponent_Factory(t) {\n        return new (t || ActivitiesFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.AccountService), i0.ɵɵdirectiveInject(i5.ProspectsService), i0.ɵɵdirectiveInject(i6.ContactsService), i0.ɵɵdirectiveInject(i7.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ActivitiesFormComponent,\n        selectors: [[\"app-activities-form\"]],\n        inputs: {\n          account_id: \"account_id\"\n        },\n        decls: 126,\n        vars: 82,\n        consts: [[\"dt\", \"\"], [1, \"activity-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [\"pTemplate\", \"header\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Transaction Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"formControlName\", \"document_type\", \"placeholder\", \"Select a Document Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Subject\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"readonly\", \"\", \"pTooltip\", \"Account ID\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"h-3rem\", \"w-full\", 3, \"value\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Disposition Code\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"disposition_code\", \"placeholder\", \"Select a Disposition Code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"start_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Call Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"end_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"End Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"notes\", \"placeholder\", \"Enter your note here...\", \"styleClass\", \"w-full\", 3, \"ngClass\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"flex\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", \"followup-add-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"body\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"font-medium\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"border-round-left-lg\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [\"pReorderableColumn\", \"\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [3, \"formGroup\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Enter a Name\", \"readonly\", \"\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"h-3rem\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", \"readonly\", \"\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"h-3rem\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"]],\n        template: function ActivitiesFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"p-dialog\", 1);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesFormComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(1, ActivitiesFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", 2);\n            i0.ɵɵelementStart(2, \"form\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"label\", 6)(6, \"span\", 7);\n            i0.ɵɵtext(7, \"description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(8, \"Transaction Type \");\n            i0.ɵɵelementStart(9, \"span\", 8);\n            i0.ɵɵtext(10, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(11, \"p-dropdown\", 9);\n            i0.ɵɵtemplate(12, ActivitiesFormComponent_div_12_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 11)(15, \"span\", 7);\n            i0.ɵɵtext(16, \"subject\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(17, \"Subject \");\n            i0.ɵɵelementStart(18, \"span\", 8);\n            i0.ɵɵtext(19, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(20, \"input\", 12);\n            i0.ɵɵtemplate(21, ActivitiesFormComponent_div_21_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 13)(24, \"span\", 7);\n            i0.ɵɵtext(25, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(26, \"Account \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"input\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 15)(29, \"label\", 16)(30, \"span\", 7);\n            i0.ɵɵtext(31, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32, \"Contact \");\n            i0.ɵɵelementStart(33, \"span\", 8);\n            i0.ɵɵtext(34, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"ng-select\", 17);\n            i0.ɵɵpipe(36, \"async\");\n            i0.ɵɵtemplate(37, ActivitiesFormComponent_ng_template_37_Template, 5, 4, \"ng-template\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(38, ActivitiesFormComponent_div_38_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 15)(40, \"label\", 19)(41, \"span\", 7);\n            i0.ɵɵtext(42, \"category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(43, \"Category \");\n            i0.ɵɵelementStart(44, \"span\", 8);\n            i0.ɵɵtext(45, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(46, \"p-dropdown\", 20);\n            i0.ɵɵtemplate(47, ActivitiesFormComponent_div_47_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 15)(49, \"label\", 21)(50, \"span\", 7);\n            i0.ɵɵtext(51, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(52, \"Disposition Code \");\n            i0.ɵɵelementStart(53, \"span\", 8);\n            i0.ɵɵtext(54, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(55, \"p-dropdown\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"div\", 15)(57, \"label\", 23)(58, \"span\", 7);\n            i0.ɵɵtext(59, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(60, \"Call Date/Time \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(61, \"p-calendar\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"div\", 15)(63, \"label\", 25)(64, \"span\", 7);\n            i0.ɵɵtext(65, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(66, \"End Date/Time \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(67, \"p-calendar\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"div\", 15)(69, \"label\", 27)(70, \"span\", 7);\n            i0.ɵɵtext(71, \"label\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(72, \"Type \");\n            i0.ɵɵelementStart(73, \"span\", 8);\n            i0.ɵɵtext(74, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(75, \"p-dropdown\", 28);\n            i0.ɵɵtemplate(76, ActivitiesFormComponent_div_76_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"div\", 15)(78, \"label\", 29)(79, \"span\", 7);\n            i0.ɵɵtext(80, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(81, \"Status \");\n            i0.ɵɵelementStart(82, \"span\", 8);\n            i0.ɵɵtext(83, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(84, \"p-dropdown\", 30);\n            i0.ɵɵtemplate(85, ActivitiesFormComponent_div_85_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"div\", 31)(87, \"label\", 32)(88, \"span\", 7);\n            i0.ɵɵtext(89, \"notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(90, \"Notes \");\n            i0.ɵɵelementStart(91, \"span\", 8);\n            i0.ɵɵtext(92, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(93, \"p-editor\", 33);\n            i0.ɵɵtemplate(94, ActivitiesFormComponent_div_94_Template, 2, 1, \"div\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 34)(96, \"div\", 35)(97, \"h4\", 36);\n            i0.ɵɵtext(98, \"Additional Contacts Persons\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"div\", 37)(100, \"p-button\", 38);\n            i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_Template_p_button_click_100_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(101, \"div\", 39)(102, \"p-table\", 40, 0);\n            i0.ɵɵlistener(\"onColReorder\", function ActivitiesFormComponent_Template_p_table_onColReorder_102_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.TableColumnReorder($event));\n            });\n            i0.ɵɵtemplate(104, ActivitiesFormComponent_ng_template_104_Template, 6, 1, \"ng-template\", 2)(105, ActivitiesFormComponent_ng_template_105_Template, 6, 3, \"ng-template\", 41);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(106, \"p-dialog\", 42);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesFormComponent_Template_p_dialog_visibleChange_106_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(107, ActivitiesFormComponent_ng_template_107_Template, 2, 0, \"ng-template\", 2);\n            i0.ɵɵelementStart(108, \"form\", 3)(109, \"div\", 43)(110, \"label\", 44)(111, \"span\", 7);\n            i0.ɵɵtext(112, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(113, \"Contacts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"div\", 45)(115, \"ng-select\", 46);\n            i0.ɵɵpipe(116, \"async\");\n            i0.ɵɵtemplate(117, ActivitiesFormComponent_ng_template_117_Template, 5, 4, \"ng-template\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(118, \"div\", 47)(119, \"button\", 48);\n            i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_Template_button_click_119_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n            });\n            i0.ɵɵtext(120, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(121, \"button\", 49);\n            i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_Template_button_click_121_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectExistingContact());\n            });\n            i0.ɵɵtext(122, \" Save \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(123, \"div\", 50)(124, \"button\", 51);\n            i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_Template_button_click_124_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.visible = false);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function ActivitiesFormComponent_Template_button_click_125_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(66, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.ActivityForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"])(\"ngClass\", i0.ɵɵpureFunction1(67, _c1, ctx.submitted && ctx.f[\"document_type\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"document_type\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(69, _c1, ctx.submitted && ctx.f[\"subject\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"subject\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"value\", ctx.account_id);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(36, 62, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(71, _c1, ctx.submitted && ctx.f[\"main_contact_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_contact_party_id\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityCategory\"])(\"ngClass\", i0.ɵɵpureFunction1(73, _c1, ctx.submitted && ctx.f[\"phone_call_category\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"phone_call_category\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activitydisposition\"]);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(75, _c1, ctx.submitted && ctx.f[\"initiator_code\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"initiator_code\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(77, _c1, ctx.submitted && ctx.f[\"activity_status\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"activity_status\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(79, _c2));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(80, _c1, ctx.submitted && ctx.f[\"notes\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"notes\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.involved_parties == null ? null : ctx.involved_parties.controls)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.ActivityForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(116, 64, ctx.existingcontacts$))(\"hideSelected\", true)(\"loading\", ctx.existingcontactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.existingcontactInput$)(\"maxSelectedItems\", 10);\n          }\n        },\n        dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgSwitch, i8.NgSwitchCase, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i9.Dialog, i7.PrimeTemplate, i10.Table, i10.ReorderableColumn, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.ButtonDirective, i12.Button, i13.Editor, i14.Dropdown, i15.Calendar, i8.AsyncPipe],\n        styles: [\".activity-popup .p-dialog{margin-right:50px}  .activity-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .activity-popup .p-dialog .p-dialog-header h4{margin:0}  .activity-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}  .activity-popup .p-dialog.p-component.p-dialog-resizable{width:calc(100vw - 510px)!important}  .activity-popup .field{grid-template-columns:repeat(auto-fill,minmax(360px,1fr))}\"]\n      });\n    }\n  }\n  return ActivitiesFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}