{"ast": null, "code": "import { HttpParams } from \"@angular/common/http\";\nimport { ApiConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ReturnOrderService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAll(data) {\n    let params = new HttpParams().appendAll(data);\n    return this.http.get(`${ApiConstant['RETURN_ORDER']}`, {\n      params\n    });\n  }\n  getAllRefundProgress() {\n    return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REFUND_PROGRESS');\n  }\n  getAllStatus() {\n    return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_STATUS');\n  }\n  getRetrunOrderDetails(data) {\n    return this.http.get(ApiConstant['RETURN_ORDER'] + `/${data.SD_DOC}`, data);\n  }\n  getAllReturnReason() {\n    return this.http.get(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REASON');\n  }\n  static {\n    this.ɵfac = function ReturnOrderService_Factory(t) {\n      return new (t || ReturnOrderService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReturnOrderService,\n      factory: ReturnOrderService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "ApiConstant", "CMS_APIContstant", "ReturnOrderService", "constructor", "http", "getAll", "data", "params", "appendAll", "get", "getAllRefundProgress", "getAllStatus", "getRetrunOrderDetails", "SD_DOC", "getAllReturnReason", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-returns\\return-order.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from \"@angular/common/http\";\r\nimport { Injectable } from \"@angular/core\";\r\nimport { ApiConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\r\n\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class ReturnOrderService {\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(data: any) {\r\n    let params = new HttpParams().appendAll(data);\r\n    return this.http.get<any>(`${ApiConstant['RETURN_ORDER']}`, { params });\r\n  }\r\n\r\n  getAllRefundProgress() {\r\n    return this.http.get<any>(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REFUND_PROGRESS');\r\n  }\r\n\r\n  getAllStatus() {\r\n    return this.http.get<any>(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_STATUS');\r\n  }\r\n\r\n  getRetrunOrderDetails(data: any) {\r\n    return this.http.get<any>(ApiConstant['RETURN_ORDER'] + `/${data.SD_DOC}`, data);\r\n  }\r\n\r\n  getAllReturnReason() {\r\n    return this.http.get<any>(CMS_APIContstant['CONFIG_DATA'] + '?filters[type][$eq]=RETURN_REASON');\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;;;AAM/E,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,MAAMA,CAACC,IAAS;IACd,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE,CAACS,SAAS,CAACF,IAAI,CAAC;IAC7C,OAAO,IAAI,CAACF,IAAI,CAACK,GAAG,CAAM,GAAGT,WAAW,CAAC,cAAc,CAAC,EAAE,EAAE;MAAEO;IAAM,CAAE,CAAC;EACzE;EAEAG,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,CAAMR,gBAAgB,CAAC,aAAa,CAAC,GAAG,4CAA4C,CAAC;EAC3G;EAEAU,YAAYA,CAAA;IACV,OAAO,IAAI,CAACP,IAAI,CAACK,GAAG,CAAMR,gBAAgB,CAAC,aAAa,CAAC,GAAG,mCAAmC,CAAC;EAClG;EAEAW,qBAAqBA,CAACN,IAAS;IAC7B,OAAO,IAAI,CAACF,IAAI,CAACK,GAAG,CAAMT,WAAW,CAAC,cAAc,CAAC,GAAG,IAAIM,IAAI,CAACO,MAAM,EAAE,EAAEP,IAAI,CAAC;EAClF;EAEAQ,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACV,IAAI,CAACK,GAAG,CAAMR,gBAAgB,CAAC,aAAa,CAAC,GAAG,mCAAmC,CAAC;EAClG;;;uBAtBWC,kBAAkB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBhB,kBAAkB;MAAAiB,OAAA,EAAlBjB,kBAAkB,CAAAkB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}