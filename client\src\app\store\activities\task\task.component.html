<div class="col-12 all-overview-body m-0 p-0 border-round-lg">
    <div
        class="filter-sec my-4 flex align-items-start justify-content-between flex-column gap-2 md:flex-row md:align-items-center">
        <div class="breadcrumb-sec">
            <p-breadcrumb [model]="breadcrumbitems" [home]="home" [styleClass]="'py-2 px-0 border-none'" />
        </div>
        <div class="flex align-items-start gap-3 md:flex-row md:align-items-center flex-wrap w-full md:w-auto">
            <!-- Search Box -->
            <div class="h-search-box w-full sm:w-24rem">
                <span class="p-input-icon-right w-full md:w-24rem">
                    <input type="text" #filter [(ngModel)]="globalSearchTerm" (input)="onGlobalFilter(dt1, $event)"
                        placeholder="Search Task"
                        class="p-inputtext p-component p-element w-full sm:w-24rem h-3rem px-3 border-round-3xl border-1 surface-border">
                    <i class="pi pi-search" style="right: 16px;"></i>
                </span>
            </div>
            <p-dropdown [options]="Actions" [(ngModel)]="selectedActions" (onChange)="onActionChange()"
                optionLabel="name" placeholder="Filter" class="w-full sm:w-13rem"
                [styleClass]="'w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'" />
            <button type="button" type="button" (click)="signup()"
                class="h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none ml-auto sm:ml-0">
                <span class="material-symbols-rounded text-2xl">box_edit</span> Create
            </button>

            <p-multiSelect [options]="cols" [(ngModel)]="selectedColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table #dt1 [value]="tasks" dataKey="id" [rows]="14" (onLazyLoad)="loadTasks($event)" [loading]="loading"
            [paginator]="true" [totalRecords]="totalRecords" [lazy]="true" responsiveLayout="scroll" [scrollable]="true"
            class="scrollable-table" [reorderableColumns]="true" (onColReorder)="onColumnReorder($event)">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center table-checkbox">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('main_account_party_id')">
                        <div class="flex align-items-center cursor-pointer">
                            Account ID
                            <i *ngIf="sortField === 'main_account_party_id'" class="ml-2 pi"
                                [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                            </i>
                            <i *ngIf="sortField !== 'main_account_party_id'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field)">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortField === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                                </i>
                                <i *ngIf="sortField !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-task let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="task" />
                    </td>
                    <td pFrozenColumn class="text-orange-600 cursor-pointer font-medium underline"
                        [routerLink]="'/store/activities/tasks/' + task.activity_id">
                        {{ task?.main_account_party_id || '-'}}
                    </td>
                    <ng-container *ngFor="let col of selectedColumns">
                        <td>
                            <!-- Use ngSwitch or conditional logic to handle special fields -->
                            <ng-container [ngSwitch]="col.field">

                                <ng-container *ngSwitchCase="'subject'">
                                    <span class="text-blue-600 font-medium underline cursor-pointer"
                                        [routerLink]="'/store/activities/tasks/' + task.activity_id">
                                        {{ task?.subject || '-' }}
                                    </span>
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner.bp_full_name'">
                                    {{ task?.business_partner?.bp_full_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner_contact.bp_full_name'">
                                    {{ task?.business_partner_contact?.bp_full_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'activity_status'">
                                    {{ getLabelFromDropdown('activityStatus', task?.activity_status) || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'notes.note'">
                                    <span pTooltip="{{ stripHtml(task?.globalNote?.note) }}" tooltipPosition="top"
                                        tooltipStyleClass="multi-line-tooltip">
                                        {{
                                        task?.globalNote?.note
                                        ? (stripHtml(task.globalNote.note) | slice: 0:80) + (task.globalNote.note.length
                                        > 80 ? '...' : '')
                                        : '-'
                                        }}
                                    </span>
                                </ng-container>

                                <ng-container *ngSwitchCase="'start_date'">
                                    {{ task?.start_date ? (task?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ task?.end_date ? (task?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'priority'">
                                    {{ getLabelFromDropdown('activityPriority', task?.priority) || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner_processor.bp_full_name'">
                                    {{ task?.business_partner_processor?.bp_full_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'task_category'">
                                    {{ getLabelFromDropdown('activityCategory', task?.task_category) || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner_owner.bp_full_name'">
                                    {{ task?.business_partner_owner?.bp_full_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchDefault>
                                    {{ task[col.field] || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="14" class="border-round-left-lg pl-3">No tasks found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="14" class="border-round-left-lg pl-3">Loading tasks data. Please wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

</div>