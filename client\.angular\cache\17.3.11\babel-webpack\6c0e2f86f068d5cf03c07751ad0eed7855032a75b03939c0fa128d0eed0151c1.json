{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n  host;\n  constructor(host) {\n    this.host = host;\n  }\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  focused = false;\n  ngAfterContentChecked() {\n    if (!this.focused) {\n      if (this.autofocus) {\n        const focusableElements = DomHandler.getFocusableElements(this.host.nativeElement);\n        if (focusableElements.length === 0) {\n          this.host.nativeElement.focus();\n        }\n        if (focusableElements.length > 0) {\n          focusableElements[0].focus();\n        }\n        this.focused = true;\n      }\n    }\n  }\n  static ɵfac = function AutoFocus_Factory(t) {\n    return new (t || AutoFocus)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AutoFocus,\n    selectors: [[\"\", \"pAutoFocus\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      autofocus: \"autofocus\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[pAutoFocus]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    autofocus: [{\n      type: Input\n    }]\n  });\n})();\nclass AutoFocusModule {\n  static ɵfac = function AutoFocusModule_Factory(t) {\n    return new (t || AutoFocusModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AutoFocusModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocusModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [AutoFocus],\n      declarations: [AutoFocus]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };", "map": {"version": 3, "names": ["CommonModule", "i0", "Directive", "Input", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "AutoFocus", "host", "constructor", "autofocus", "focused", "ngAfterContentChecked", "focusableElements", "getFocusableElements", "nativeElement", "length", "focus", "ɵfac", "AutoFocus_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "class", "AutoFocusModule", "AutoFocusModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n    host;\n    constructor(host) {\n        this.host = host;\n    }\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    focused = false;\n    ngAfterContentChecked() {\n        if (!this.focused) {\n            if (this.autofocus) {\n                const focusableElements = DomHandler.getFocusableElements(this.host.nativeElement);\n                if (focusableElements.length === 0) {\n                    this.host.nativeElement.focus();\n                }\n                if (focusableElements.length > 0) {\n                    focusableElements[0].focus();\n                }\n                this.focused = true;\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocus, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.5\", type: AutoFocus, selector: \"[pAutoFocus]\", inputs: { autofocus: \"autofocus\" }, host: { classAttribute: \"p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pAutoFocus]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { autofocus: [{\n                type: Input\n            }] } });\nclass AutoFocusModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocusModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocusModule, declarations: [AutoFocus], imports: [CommonModule], exports: [AutoFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocusModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AutoFocusModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [AutoFocus],\n                    declarations: [AutoFocus]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1D,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,IAAI;EACJC,WAAWA,CAACD,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;AACA;EACIE,SAAS;EACTC,OAAO,GAAG,KAAK;EACfC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE;MACf,IAAI,IAAI,CAACD,SAAS,EAAE;QAChB,MAAMG,iBAAiB,GAAGP,UAAU,CAACQ,oBAAoB,CAAC,IAAI,CAACN,IAAI,CAACO,aAAa,CAAC;QAClF,IAAIF,iBAAiB,CAACG,MAAM,KAAK,CAAC,EAAE;UAChC,IAAI,CAACR,IAAI,CAACO,aAAa,CAACE,KAAK,CAAC,CAAC;QACnC;QACA,IAAIJ,iBAAiB,CAACG,MAAM,GAAG,CAAC,EAAE;UAC9BH,iBAAiB,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC;QAChC;QACA,IAAI,CAACN,OAAO,GAAG,IAAI;MACvB;IACJ;EACJ;EACA,OAAOO,IAAI,YAAAC,kBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFb,SAAS,EAAnBL,EAAE,CAAAmB,iBAAA,CAAmCnB,EAAE,CAACoB,UAAU;EAAA;EAC3I,OAAOC,IAAI,kBAD8ErB,EAAE,CAAAsB,iBAAA;IAAAC,IAAA,EACJlB,SAAS;IAAAmB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAlB,SAAA;IAAA;EAAA;AACpG;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KAH6F3B,EAAE,CAAA4B,iBAAA,CAGJvB,SAAS,EAAc,CAAC;IACvGkB,IAAI,EAAEtB,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBxB,IAAI,EAAE;QACFyB,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAER,IAAI,EAAEvB,EAAE,CAACoB;EAAW,CAAC,CAAC,EAAkB;IAAEZ,SAAS,EAAE,CAAC;MAC3Ee,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8B,eAAe,CAAC;EAClB,OAAOhB,IAAI,YAAAiB,wBAAAf,CAAA;IAAA,YAAAA,CAAA,IAAwFc,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAhB8ElC,EAAE,CAAAmC,gBAAA;IAAAZ,IAAA,EAgBSS;EAAe;EACnH,OAAOI,IAAI,kBAjB8EpC,EAAE,CAAAqC,gBAAA;IAAAC,OAAA,GAiBoCvC,YAAY;EAAA;AAC/I;AACA;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KAnB6F3B,EAAE,CAAA4B,iBAAA,CAmBJI,eAAe,EAAc,CAAC;IAC7GT,IAAI,EAAEpB,QAAQ;IACd0B,IAAI,EAAE,CAAC;MACCS,OAAO,EAAE,CAACvC,YAAY,CAAC;MACvBwC,OAAO,EAAE,CAAClC,SAAS,CAAC;MACpBmC,YAAY,EAAE,CAACnC,SAAS;IAC5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAE2B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}