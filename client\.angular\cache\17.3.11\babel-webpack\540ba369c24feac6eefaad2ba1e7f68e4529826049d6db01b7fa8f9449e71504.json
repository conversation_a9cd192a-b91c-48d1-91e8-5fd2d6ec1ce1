{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../account.service\";\nimport * as i2 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nexport let OpportunityItemDetailComponent = /*#__PURE__*/(() => {\n  class OpportunityItemDetailComponent {\n    constructor(accountservice, opportunitiesservice, router) {\n      this.accountservice = accountservice;\n      this.opportunitiesservice = opportunitiesservice;\n      this.router = router;\n      this.opportunityDetail = null;\n      this.unsubscribe$ = new Subject();\n      this.dropdowns = {\n        opportunityCategory: [],\n        opportunityStatus: [],\n        opportunitySource: []\n      };\n    }\n    ngOnInit() {\n      this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n      this.opportunitydata = history.state.opportunitydata;\n      this.opportunitiesservice.getOpportunityByID(this.opportunitydata?.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (!response?.data?.length) return;\n        this.opportunityDetail = response.data[0];\n      });\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    goToBack() {\n      this.accountservice.getAccountByID(history.state.moduleId).pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.router.navigate(['/store/account', history.state.moduleId, 'opportunities']);\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OpportunityItemDetailComponent_Factory(t) {\n        return new (t || OpportunityItemDetailComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunityItemDetailComponent,\n        selectors: [[\"app-opportunity-item-detail\"]],\n        decls: 219,\n        vars: 31,\n        consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"pt-0\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"filter-sec\", \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"pt-0\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mb-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"]],\n        template: function OpportunityItemDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n            i0.ɵɵtext(5, \"Opportunities Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function OpportunityItemDetailComponent_Template_button_click_7_listener() {\n              return ctx.goToBack();\n            });\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9, \"arrow_back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Back \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 2)(13, \"div\", 9)(14, \"h4\", 4);\n            i0.ɵɵtext(15, \"Opportunities Information\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"ul\", 11)(18, \"li\", 12)(19, \"div\", 13)(20, \"i\", 14);\n            i0.ɵɵtext(21, \"tag\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 15)(23, \"h6\", 16);\n            i0.ɵɵtext(24, \"Opportunity ID #\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\", 17);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"li\", 12)(28, \"div\", 13)(29, \"i\", 14);\n            i0.ɵɵtext(30, \"badge\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 15)(32, \"h6\", 16);\n            i0.ɵɵtext(33, \"Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"p\", 17);\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"li\", 12)(37, \"div\", 13)(38, \"i\", 14);\n            i0.ɵɵtext(39, \"attach_money\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 15)(41, \"h6\", 16);\n            i0.ɵɵtext(42, \"Expected Value\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"p\", 17);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"li\", 12)(46, \"div\", 13)(47, \"i\", 14);\n            i0.ɵɵtext(48, \"event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 15)(50, \"h6\", 16);\n            i0.ɵɵtext(51, \"Expected Decision Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"p\", 17);\n            i0.ɵɵtext(53);\n            i0.ɵɵpipe(54, \"date\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"li\", 12)(56, \"div\", 13)(57, \"i\", 14);\n            i0.ɵɵtext(58, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\", 15)(60, \"h6\", 16);\n            i0.ɵɵtext(61, \"Account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\", 17);\n            i0.ɵɵtext(63);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(64, \"li\", 12)(65, \"div\", 13)(66, \"i\", 14);\n            i0.ɵɵtext(67, \"contact_phone\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 15)(69, \"h6\", 16);\n            i0.ɵɵtext(70, \"Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"p\", 17);\n            i0.ɵɵtext(72);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(73, \"li\", 12)(74, \"div\", 13)(75, \"i\", 14);\n            i0.ɵɵtext(76, \"category\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(77, \"div\", 15)(78, \"h6\", 16);\n            i0.ɵɵtext(79, \"Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"p\", 17);\n            i0.ɵɵtext(81);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(82, \"li\", 12)(83, \"div\", 13)(84, \"i\", 14);\n            i0.ɵɵtext(85, \"account_tree\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(86, \"div\", 15)(87, \"h6\", 16);\n            i0.ɵɵtext(88, \"Parent Opportunity\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"p\", 17);\n            i0.ɵɵtext(90);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(91, \"li\", 12)(92, \"div\", 13)(93, \"i\", 14);\n            i0.ɵɵtext(94, \"source\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 15)(96, \"h6\", 16);\n            i0.ɵɵtext(97, \"Source\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"p\", 17);\n            i0.ɵɵtext(99);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(100, \"li\", 12)(101, \"div\", 13)(102, \"i\", 14);\n            i0.ɵɵtext(103, \"scale\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(104, \"div\", 15)(105, \"h6\", 16);\n            i0.ɵɵtext(106, \"Weighted Value\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"p\", 17);\n            i0.ɵɵtext(108);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(109, \"li\", 12)(110, \"div\", 13)(111, \"i\", 14);\n            i0.ɵɵtext(112, \"lens\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(113, \"div\", 15)(114, \"h6\", 16);\n            i0.ɵɵtext(115, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(116, \"p\", 17);\n            i0.ɵɵtext(117);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(118, \"li\", 12)(119, \"div\", 13)(120, \"i\", 14);\n            i0.ɵɵtext(121, \"fact_check\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(122, \"div\", 15)(123, \"h6\", 16);\n            i0.ɵɵtext(124, \"Reason for Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"p\", 17);\n            i0.ɵɵtext(126);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(127, \"li\", 12)(128, \"div\", 13)(129, \"i\", 14);\n            i0.ɵɵtext(130, \"track_changes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(131, \"div\", 15)(132, \"h6\", 16);\n            i0.ɵɵtext(133, \"Days in Sale Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(134, \"p\", 17);\n            i0.ɵɵtext(135);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(136, \"li\", 12)(137, \"div\", 13)(138, \"i\", 14);\n            i0.ɵɵtext(139, \"trending_up\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(140, \"div\", 15)(141, \"h6\", 16);\n            i0.ɵɵtext(142, \"Probability\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"p\", 17);\n            i0.ɵɵtext(144);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(145, \"li\", 12)(146, \"div\", 13)(147, \"i\", 14);\n            i0.ɵɵtext(148, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(149, \"div\", 15)(150, \"h6\", 16);\n            i0.ɵɵtext(151, \"Owner\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(152, \"p\", 17);\n            i0.ɵɵtext(153);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(154, \"li\", 12)(155, \"div\", 13)(156, \"i\", 14);\n            i0.ɵɵtext(157, \"business\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(158, \"div\", 15)(159, \"h6\", 16);\n            i0.ɵɵtext(160, \"Sales Organization\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(161, \"p\", 17);\n            i0.ɵɵtext(162);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(163, \"li\", 12)(164, \"div\", 13)(165, \"i\", 14);\n            i0.ɵɵtext(166, \"sell\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(167, \"div\", 15)(168, \"h6\", 16);\n            i0.ɵɵtext(169, \"Sales Unit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(170, \"p\", 17);\n            i0.ɵɵtext(171);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(172, \"li\", 12)(173, \"div\", 13)(174, \"i\", 14);\n            i0.ɵɵtext(175, \"event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(176, \"div\", 15)(177, \"h6\", 16);\n            i0.ɵɵtext(178, \"Create Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(179, \"p\", 17);\n            i0.ɵɵtext(180);\n            i0.ɵɵpipe(181, \"date\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(182, \"li\", 12)(183, \"div\", 13)(184, \"i\", 14);\n            i0.ɵɵtext(185, \"event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(186, \"div\", 15)(187, \"h6\", 16);\n            i0.ɵɵtext(188, \"Last Updated Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(189, \"p\", 17);\n            i0.ɵɵtext(190);\n            i0.ɵɵpipe(191, \"date\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(192, \"li\", 12)(193, \"div\", 13)(194, \"i\", 14);\n            i0.ɵɵtext(195, \"update\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(196, \"div\", 15)(197, \"h6\", 16);\n            i0.ɵɵtext(198, \"Last Updated By\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(199, \"p\", 17);\n            i0.ɵɵtext(200);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(201, \"li\", 12)(202, \"div\", 13)(203, \"i\", 14);\n            i0.ɵɵtext(204, \"trending_up\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(205, \"div\", 15)(206, \"h6\", 16);\n            i0.ɵɵtext(207, \"Progress\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(208, \"p\", 17);\n            i0.ɵɵtext(209);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(210, \"li\", 12)(211, \"div\", 13)(212, \"i\", 14);\n            i0.ɵɵtext(213, \"help\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(214, \"div\", 15)(215, \"h6\", 16);\n            i0.ɵɵtext(216, \"Need Help\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(217, \"p\", 17);\n            i0.ɵɵtext(218);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(26);\n            i0.ɵɵtextInterpolate((ctx.opportunityDetail == null ? null : ctx.opportunityDetail.opportunity_id) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.opportunityDetail == null ? null : ctx.opportunityDetail.name) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.expected_revenue_amount) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.opportunityDetail == null ? null : ctx.opportunityDetail.expected_revenue_end_date) ? i0.ɵɵpipeBind2(54, 22, ctx.opportunityDetail.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate((ctx.opportunityDetail == null ? null : ctx.opportunityDetail.business_partner == null ? null : ctx.opportunityDetail.business_partner.bp_full_name) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.business_partner_contact == null ? null : ctx.opportunityDetail.business_partner_contact.bp_full_name) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunityCategory\", ctx.opportunityDetail == null ? null : ctx.opportunityDetail.group_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.parent_opportunity) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunitySource\", ctx.opportunityDetail == null ? null : ctx.opportunityDetail.origin_type_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.weighted_expected_net_amount) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunityStatus\", ctx.opportunityDetail == null ? null : ctx.opportunityDetail.life_cycle_status_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.result_reason_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.days_in_sales_status) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.probability_percent) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.business_partner_owner == null ? null : ctx.opportunityDetail.business_partner_owner.bp_full_name) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.sales_organisation_id) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.sales_unit_party_id) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.opportunityDetail == null ? null : ctx.opportunityDetail.expected_revenue_start_date) ? i0.ɵɵpipeBind2(181, 25, ctx.opportunityDetail.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.last_change_date) ? i0.ɵɵpipeBind2(191, 28, ctx.opportunityDetail.last_change_date, \"MM/dd/yyyy hh:mm a\") : \"-\", \"\");\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.last_changed_by) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.progress) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetail == null ? null : ctx.opportunityDetail.need_help) || \"-\", \"\");\n          }\n        },\n        dependencies: [i4.DatePipe]\n      });\n    }\n  }\n  return OpportunityItemDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}