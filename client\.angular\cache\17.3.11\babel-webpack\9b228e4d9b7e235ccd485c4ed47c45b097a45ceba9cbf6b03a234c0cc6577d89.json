{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/multiselect\";\nimport * as i11 from \"./opportunities-form/opportunities-form.component\";\nimport * as i12 from \"./activities-sales-call-form/activities-sales-call-form.component\";\nfunction SalesCallFollowItemsComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderActivities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field, ctx_r1.followupdetails, \"activities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallFollowItemsComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 16)(5, SalesCallFollowItemsComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities !== col_r4.field);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"activity_transaction.subject\", ctx_r1.followupdetails, \"activities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, SalesCallFollowItemsComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 16)(5, SalesCallFollowItemsComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallFollowItemsComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 18);\n    i0.ɵɵelementStart(7, \"th\", 19);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities === \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldActivities !== \"activity_transaction.subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesColumns);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", followup_r6 == null ? null : followup_r6.type_code) || \"-\", \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followup_r6 == null ? null : followup_r6.partner_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followup_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, followup_r6 == null ? null : followup_r6.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 29);\n    i0.ɵɵtemplate(3, SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 30)(4, SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 30)(5, SalesCallFollowItemsComponent_ng_template_10_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"type_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 23)(1, \"td\", 24)(2, \"div\", 25)(3, \"a\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, SalesCallFollowItemsComponent_ng_template_10_ng_container_5_Template, 6, 4, \"ng-container\", 18);\n    i0.ɵɵelementStart(6, \"td\", 27)(7, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_10_Template_button_click_7_listener($event) {\n      const followup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(followup_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followup_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/activities/calls/\" + (followup_r6 == null ? null : followup_r6.activity_transaction == null ? null : followup_r6.activity_transaction.activity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followup_r6 == null ? null : followup_r6.activity_transaction == null ? null : followup_r6.activity_transaction.subject) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedActivitiesColumns);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No follow up found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"Loading follow up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderOpportunities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderOpportunities === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_22_ng_container_6_Template_th_click_1_listener() {\n      const col_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r10.field, ctx_r1.followupopportunitydetails, \"opportunities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallFollowItemsComponent_ng_template_22_ng_container_6_i_4_Template, 1, 1, \"i\", 16)(5, SalesCallFollowItemsComponent_ng_template_22_ng_container_6_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r10.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r10.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities === col_r10.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities !== col_r10.field);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_22_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"opportunity.name\", ctx_r1.followupopportunitydetails, \"opportunities\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, SalesCallFollowItemsComponent_ng_template_22_i_4_Template, 1, 1, \"i\", 16)(5, SalesCallFollowItemsComponent_ng_template_22_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallFollowItemsComponent_ng_template_22_ng_container_6_Template, 6, 4, \"ng-container\", 18);\n    i0.ɵɵelementStart(7, \"th\", 19);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities === \"opportunity.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldOpportunities !== \"opportunity.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedOpportunitiesColumns);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", followupopportunity_r12 == null ? null : followupopportunity_r12.type_code) || \"-\", \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followupopportunity_r12 == null ? null : followupopportunity_r12.partner_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, followupopportunity_r12 == null ? null : followupopportunity_r12.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_23_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 29);\n    i0.ɵɵtemplate(3, SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 30)(4, SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 30)(5, SalesCallFollowItemsComponent_ng_template_23_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r13.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"type_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 23)(1, \"td\", 32)(2, \"div\", 25)(3, \"a\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, SalesCallFollowItemsComponent_ng_template_23_ng_container_5_Template, 6, 4, \"ng-container\", 18);\n    i0.ɵɵelementStart(6, \"td\", 27)(7, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_23_Template_button_click_7_listener($event) {\n      const followupopportunity_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmOpportunityRemove(followupopportunity_r12));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followupopportunity_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/opportunities/\" + (followupopportunity_r12 == null ? null : followupopportunity_r12.opportunity == null ? null : followupopportunity_r12.opportunity.opportunity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (followupopportunity_r12 == null ? null : followupopportunity_r12.opportunity == null ? null : followupopportunity_r12.opportunity.name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedOpportunitiesColumns);\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No follow up found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"Loading follow up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SalesCallFollowItemsComponent = /*#__PURE__*/(() => {\n  class SalesCallFollowItemsComponent {\n    constructor(router, route, activitiesservice, opportunitiesservice, messageservice, confirmationservice) {\n      this.router = router;\n      this.route = route;\n      this.activitiesservice = activitiesservice;\n      this.opportunitiesservice = opportunitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.followupdetails = [];\n      this.followupopportunitydetails = [];\n      this.activity_id = '';\n      this.submitted = false;\n      this.saving = false;\n      this.visible = false;\n      this.showOpportunitiesDialog = false;\n      this.showActivitiesDialog = false;\n      this.dropdowns = {\n        activityDocumentType: []\n      };\n      this._selectedActivitiesColumns = [];\n      this._selectedOpportunitiesColumn = [];\n      this.ActivitiesCols = [{\n        field: 'type_code',\n        header: 'Type'\n      }, {\n        field: 'partner_name',\n        header: 'Responsible'\n      }, {\n        field: 'createdAt',\n        header: 'Created On'\n      }];\n      this.OpportunitiesCols = [{\n        field: 'type_code',\n        header: 'Type'\n      }, {\n        field: 'partner_name',\n        header: 'Responsible'\n      }, {\n        field: 'createdAt',\n        header: 'Created On'\n      }];\n      this.sortFieldActivities = '';\n      this.sortOrderActivities = 1;\n      this.sortFieldOpportunities = '';\n      this.sortOrderOpportunities = 1;\n    }\n    ngOnInit() {\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.activity_id = response?.activity_id;\n          const allActivityItems = response?.follow_up_and_related_items || [];\n          const allOpportunityItems = response?.opportunity_followups || [];\n          // Filter only FOLLOW_UP items and inject individual partner_name from each item\n          this.followupdetails = allActivityItems.filter(item => item?.btd_role_code === '2').map(item => {\n            const partnerFn = item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n            const partnerName = partnerFn?.bp_full_name || null;\n            return {\n              ...item,\n              partner_name: partnerName\n            };\n          });\n          this.followupopportunitydetails = allOpportunityItems.filter(item => item?.type_code === '0005').map(item => {\n            const partnerFn = item?.opportunity?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n            const partnerName = partnerFn?.bp_full_name || null;\n            return {\n              ...item,\n              partner_name: partnerName\n            };\n          });\n        }\n      });\n      this._selectedActivitiesColumns = this.ActivitiesCols;\n      this._selectedOpportunitiesColumn = this.OpportunitiesCols;\n    }\n    get selectedActivitiesColumns() {\n      return this._selectedActivitiesColumns;\n    }\n    get selectedOpportunitiesColumns() {\n      return this._selectedOpportunitiesColumn;\n    }\n    set selectedActivitiesColumns(val) {\n      this._selectedActivitiesColumns = this.ActivitiesCols.filter(col => val.includes(col));\n    }\n    set selectedOpportunitiesColumns(val) {\n      this._selectedOpportunitiesColumn = this.OpportunitiesCols.filter(col => val.includes(col));\n    }\n    onActivitiesColumnReorder(event) {\n      const draggedCol = this.ActivitiesCols[event.dragIndex];\n      this.ActivitiesCols.splice(event.dragIndex, 1);\n      this.ActivitiesCols.splice(event.dropIndex, 0, draggedCol);\n    }\n    onOpportunitiesColumnReorder(event) {\n      const draggedCol = this.OpportunitiesCols[event.dragIndex];\n      this.OpportunitiesCols.splice(event.dragIndex, 1);\n      this.OpportunitiesCols.splice(event.dropIndex, 0, draggedCol);\n    }\n    customSort(field, data, type) {\n      if (type === 'activities') {\n        this.sortFieldActivities = field;\n        this.sortOrderActivities = this.sortOrderActivities === 1 ? -1 : 1;\n      } else {\n        this.sortFieldOpportunities = field;\n        this.sortOrderOpportunities = this.sortOrderOpportunities === 1 ? -1 : 1;\n      }\n      data.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return (type === 'activities' ? this.sortOrderActivities : this.sortOrderOpportunities) * result;\n      });\n    }\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        let fields = field.split('.');\n        let value = data;\n        for (let i = 0; i < fields.length; i++) {\n          if (value == null) return null;\n          value = value[fields[i]];\n        }\n        return value;\n      }\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.activitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    confirmOpportunityRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    removeOpportunity(item) {\n      this.opportunitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    showActivityDialog(position) {\n      this.showActivitiesDialog = true;\n      this.submitted = false;\n    }\n    showOpportunityDialog(position) {\n      this.showOpportunitiesDialog = true;\n      this.submitted = false;\n    }\n    navigateToFollowupDetail(item) {\n      this.router.navigate([item?.activity_transaction?.activity_id], {\n        relativeTo: this.route,\n        state: {\n          followupdata: item\n        }\n      });\n    }\n    navigateToOpportunityFollowupDetail(item) {\n      const opportunityId = item?.opportunity?.opportunity_id;\n      this.router.navigate(['opportunity', opportunityId], {\n        relativeTo: this.route,\n        state: {\n          followupdata: item\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallFollowItemsComponent_Factory(t) {\n        return new (t || SalesCallFollowItemsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.OpportunitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallFollowItemsComponent,\n        selectors: [[\"app-sales-call-follow-items\"]],\n        decls: 28,\n        vars: 22,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mb-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [3, \"onClose\", \"visible\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [1, \"border-round-right-lg\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"5\", 1, \"border-round-left-lg\", \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"]],\n        template: function SalesCallFollowItemsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Follow Up Activities\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_Template_p_button_click_5_listener() {\n              return ctx.showActivityDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallFollowItemsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActivitiesColumns, $event) || (ctx.selectedActivitiesColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function SalesCallFollowItemsComponent_Template_p_table_onColReorder_8_listener($event) {\n              return ctx.onActivitiesColumnReorder($event);\n            });\n            i0.ɵɵtemplate(9, SalesCallFollowItemsComponent_ng_template_9_Template, 9, 3, \"ng-template\", 8)(10, SalesCallFollowItemsComponent_ng_template_10_Template, 8, 3, \"ng-template\", 9)(11, SalesCallFollowItemsComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, SalesCallFollowItemsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 1)(15, \"h4\", 2);\n            i0.ɵɵtext(16, \"Follow Up Opportunities\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 3)(18, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_Template_p_button_click_18_listener() {\n              return ctx.showOpportunityDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallFollowItemsComponent_Template_p_multiSelect_ngModelChange_19_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedOpportunitiesColumns, $event) || (ctx.selectedOpportunitiesColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"div\", 6)(21, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function SalesCallFollowItemsComponent_Template_p_table_onColReorder_21_listener($event) {\n              return ctx.onOpportunitiesColumnReorder($event);\n            });\n            i0.ɵɵtemplate(22, SalesCallFollowItemsComponent_ng_template_22_Template, 9, 3, \"ng-template\", 8)(23, SalesCallFollowItemsComponent_ng_template_23_Template, 8, 3, \"ng-template\", 9)(24, SalesCallFollowItemsComponent_ng_template_24_Template, 3, 0, \"ng-template\", 10)(25, SalesCallFollowItemsComponent_ng_template_25_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"app-opportunities-form\", 13);\n            i0.ɵɵlistener(\"onClose\", function SalesCallFollowItemsComponent_Template_app_opportunities_form_onClose_26_listener() {\n              return ctx.showOpportunitiesDialog = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"app-activities-sales-call-form\", 13);\n            i0.ɵɵlistener(\"onClose\", function SalesCallFollowItemsComponent_Template_app_activities_sales_call_form_onClose_27_listener() {\n              return ctx.showActivitiesDialog = false;\n            });\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.ActivitiesCols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActivitiesColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.followupdetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.OpportunitiesCols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedOpportunitiesColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.followupopportunitydetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"visible\", ctx.showOpportunitiesDialog);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"visible\", ctx.showActivitiesDialog);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i6.NgControlStatus, i6.NgModel, i7.Table, i4.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i8.ButtonDirective, i8.Button, i9.Tooltip, i10.MultiSelect, i11.OpportunitiesFormComponent, i12.ActivitiesSalesCallFormComponent, i5.DatePipe],\n        styles: [\".followup-popup .p-dialog.p-component.p-dialog-resizable{width:calc(100vw - 510px)!important}  .followup-popup .field{grid-template-columns:repeat(auto-fill,minmax(360px,1fr))}\"]\n      });\n    }\n  }\n  return SalesCallFollowItemsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}