{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { BadgeModule } from 'primeng/badge';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { RippleModule } from 'primeng/ripple';\nimport { AppConfigModule } from './config/app.config.module';\nimport { AppMenuComponent } from './app.menu.component';\nimport { AppMenuitemComponent } from './app.menuitem.component';\nimport { RouterModule } from '@angular/router';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nexport let AppLayoutModule = /*#__PURE__*/(() => {\n  class AppLayoutModule {\n    static {\n      this.ɵfac = function AppLayoutModule_Factory(t) {\n        return new (t || AppLayoutModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppLayoutModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, ButtonModule, TooltipModule, RippleModule, RouterModule, AppConfigModule, StyleClassModule, CalendarModule]\n      });\n    }\n  }\n  return AppLayoutModule;\n})();\ni0.ɵɵsetComponentScope(AppMenuComponent, [i1.NgForOf, i1.NgIf, AppMenuitemComponent], []);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}