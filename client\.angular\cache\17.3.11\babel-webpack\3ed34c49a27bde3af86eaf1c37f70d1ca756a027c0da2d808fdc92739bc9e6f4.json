{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SessionsyncService {\n  constructor() {\n    this.initSessionSync();\n  }\n  initSessionSync() {\n    sessionStorage.removeItem('session_loaded_once');\n    localStorage.removeItem('sync_in_progress');\n    const hasSession = !!sessionStorage.getItem('userInfo') && !!sessionStorage.getItem('jwtToken');\n    if (!hasSession) {\n      localStorage.setItem('getSessionStorage', Date.now().toString());\n      localStorage.setItem('sync_in_progress', 'true');\n    }\n    window.addEventListener('storage', event => {\n      if (event.key === 'getSessionStorage') {\n        const userInfo = sessionStorage.getItem('userInfo');\n        const jwtToken = sessionStorage.getItem('jwtToken');\n        if (userInfo && jwtToken) {\n          const payload = JSON.stringify({\n            userInfo,\n            jwtToken\n          });\n          localStorage.setItem('sessionStorageData', payload);\n          localStorage.removeItem('sessionStorageData');\n        }\n      }\n      if (event.key === 'sessionStorageData' && event.newValue) {\n        const data = JSON.parse(event.newValue || '{}');\n        if (data?.userInfo && data?.jwtToken) {\n          sessionStorage.setItem('userInfo', data.userInfo);\n          sessionStorage.setItem('jwtToken', data.jwtToken);\n          if (!sessionStorage.getItem('session_loaded_once')) {\n            sessionStorage.setItem('session_loaded_once', 'true');\n            setTimeout(() => location.reload(), 200);\n          }\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SessionsyncService_Factory(t) {\n      return new (t || SessionsyncService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SessionsyncService,\n      factory: SessionsyncService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["SessionsyncService", "constructor", "initSessionSync", "sessionStorage", "removeItem", "localStorage", "hasSession", "getItem", "setItem", "Date", "now", "toString", "window", "addEventListener", "event", "key", "userInfo", "jwtToken", "payload", "JSON", "stringify", "newValue", "data", "parse", "setTimeout", "location", "reload", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\sessionsync.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SessionsyncService {\r\n  constructor() {\r\n    this.initSessionSync();\r\n  }\r\n\r\n  initSessionSync() {\r\n    sessionStorage.removeItem('session_loaded_once');\r\n    localStorage.removeItem('sync_in_progress');\r\n\r\n    const hasSession =\r\n      !!sessionStorage.getItem('userInfo') &&\r\n      !!sessionStorage.getItem('jwtToken');\r\n\r\n    if (!hasSession) {\r\n      localStorage.setItem('getSessionStorage', Date.now().toString());\r\n      localStorage.setItem('sync_in_progress', 'true');\r\n    }\r\n\r\n    window.addEventListener('storage', (event) => {\r\n      if (event.key === 'getSessionStorage') {\r\n        const userInfo = sessionStorage.getItem('userInfo');\r\n        const jwtToken = sessionStorage.getItem('jwtToken');\r\n        if (userInfo && jwtToken) {\r\n          const payload = JSON.stringify({ userInfo, jwtToken });\r\n          localStorage.setItem('sessionStorageData', payload);\r\n          localStorage.removeItem('sessionStorageData');\r\n        }\r\n      }\r\n\r\n      if (event.key === 'sessionStorageData' && event.newValue) {\r\n        const data = JSON.parse(event.newValue || '{}');\r\n        if (data?.userInfo && data?.jwtToken) {\r\n          sessionStorage.setItem('userInfo', data.userInfo);\r\n          sessionStorage.setItem('jwtToken', data.jwtToken);\r\n\r\n          if (!sessionStorage.getItem('session_loaded_once')) {\r\n            sessionStorage.setItem('session_loaded_once', 'true');\r\n            setTimeout(() => location.reload(), 200);\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAC7BC,YAAA;IACE,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACbC,cAAc,CAACC,UAAU,CAAC,qBAAqB,CAAC;IAChDC,YAAY,CAACD,UAAU,CAAC,kBAAkB,CAAC;IAE3C,MAAME,UAAU,GACd,CAAC,CAACH,cAAc,CAACI,OAAO,CAAC,UAAU,CAAC,IACpC,CAAC,CAACJ,cAAc,CAACI,OAAO,CAAC,UAAU,CAAC;IAEtC,IAAI,CAACD,UAAU,EAAE;MACfD,YAAY,CAACG,OAAO,CAAC,mBAAmB,EAAEC,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,EAAE,CAAC;MAChEN,YAAY,CAACG,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;IAClD;IAEAI,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAI;MAC3C,IAAIA,KAAK,CAACC,GAAG,KAAK,mBAAmB,EAAE;QACrC,MAAMC,QAAQ,GAAGb,cAAc,CAACI,OAAO,CAAC,UAAU,CAAC;QACnD,MAAMU,QAAQ,GAAGd,cAAc,CAACI,OAAO,CAAC,UAAU,CAAC;QACnD,IAAIS,QAAQ,IAAIC,QAAQ,EAAE;UACxB,MAAMC,OAAO,GAAGC,IAAI,CAACC,SAAS,CAAC;YAAEJ,QAAQ;YAAEC;UAAQ,CAAE,CAAC;UACtDZ,YAAY,CAACG,OAAO,CAAC,oBAAoB,EAAEU,OAAO,CAAC;UACnDb,YAAY,CAACD,UAAU,CAAC,oBAAoB,CAAC;QAC/C;MACF;MAEA,IAAIU,KAAK,CAACC,GAAG,KAAK,oBAAoB,IAAID,KAAK,CAACO,QAAQ,EAAE;QACxD,MAAMC,IAAI,GAAGH,IAAI,CAACI,KAAK,CAACT,KAAK,CAACO,QAAQ,IAAI,IAAI,CAAC;QAC/C,IAAIC,IAAI,EAAEN,QAAQ,IAAIM,IAAI,EAAEL,QAAQ,EAAE;UACpCd,cAAc,CAACK,OAAO,CAAC,UAAU,EAAEc,IAAI,CAACN,QAAQ,CAAC;UACjDb,cAAc,CAACK,OAAO,CAAC,UAAU,EAAEc,IAAI,CAACL,QAAQ,CAAC;UAEjD,IAAI,CAACd,cAAc,CAACI,OAAO,CAAC,qBAAqB,CAAC,EAAE;YAClDJ,cAAc,CAACK,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;YACrDgB,UAAU,CAAC,MAAMC,QAAQ,CAACC,MAAM,EAAE,EAAE,GAAG,CAAC;UAC1C;QACF;MACF;IACF,CAAC,CAAC;EACJ;;;uBA1CW1B,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAA2B,OAAA,EAAlB3B,kBAAkB,CAAA4B,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}