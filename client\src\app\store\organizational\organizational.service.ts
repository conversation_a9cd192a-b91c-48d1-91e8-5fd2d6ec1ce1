import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CMS_APIContstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root',
})
export class OrganizationalService {
  public organizationalSubject = new BehaviorSubject<any>(null);
  public organizational = this.organizationalSubject.asObservable();

  constructor(private http: HttpClient) {}

  createOrganization(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_REGISTRATION}`,
      data
    );
  }

  createParentUnit(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { data });
  }

  createFunction(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}`, {
      data,
    });
  }

  createEmployee(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}`, {
      data,
    });
  }

  createManager(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}`, {
      data,
    });
  }

  createAddress(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_ADDRESS}`, {
      data,
    });
  }

  updateParentUnit(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL}/${Id}`, {
      data,
    });
  }

  updateFunction(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${Id}`,
      {
        data,
      }
    );
  }

  updateEmployee(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${Id}`,
      {
        data,
      }
    );
  }

  updateManager(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${Id}`,
      {
        data,
      }
    );
  }

  updateAddress(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_ADDRESS}/${Id}`,
      {
        data,
      }
    );
  }

  deleteFunction(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${id}`
    );
  }

  deleteParentUnit(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL}/${id}`
    );
  }

  deleteEmployee(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${id}`
    );
  }

  deleteManager(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${id}`
    );
  }

  deleteAddress(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ORGANIZATIONAL_ADDRESS}/${id}`
    );
  }

  getOrganization(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString())
      .set(
        'fields',
        'organisational_unit_id,name,parent_organisational_unit_id'
      )
      .set('populate[child_organisational_units][fields][0]','organisational_unit_id')
      .set('populate[parent_organisational_unit][fields][0]', 'name')
      .set(
        'populate[crm_org_unit_managers][populate][business_partner][fields][0]',
        'bp_full_name'
      )
      .set('populate[crm_org_unit_functions][fields][0]', 'sales_indicator')
      .set(
        'populate[crm_org_unit_functions][fields][1]',
        'sales_organisation_indicator'
      )
      .set(
        'populate[crm_org_unit_functions][fields][2]',
        'reporting_line_indicator'
      );

    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    } else {
      params = params.set('sort', 'updatedAt:desc');
    }

    if (searchTerm) {
      params = params.set(
        'filters[$or][0][organisational_unit_id][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][1][name][$containsi]', searchTerm);
      params = params.set(
        'filters[$or][2][parent_organisational_unit_id][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][3][parent_organisational_unit][name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][4][crm_org_unit_managers][business_partner][bp_full_name][$containsi]',
        searchTerm
      );
    }

    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {
      params,
    });
  }

  getEmployees(params: any) {
    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(
      map((response) =>
        (response?.data || []).map((item: any) => {
          return {
            bp_id: item?.bp_id || '',
            bp_full_name: item?.bp_full_name || '',
          };
        })
      )
    );
  }

  getParentUnit(params: any) {
    return this.http
      .get<any>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { params })
      .pipe(
        map((response) =>
          (response?.data || []).map((item: any) => {
            return {
              organisational_unit_id: item?.organisational_unit_id || '',
              name: item?.name || '',
            };
          })
        )
      );
  }

  getManagers(params: any) {
    return this.http
      .get<any>(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}`, { params })
      .pipe(
        map((response) =>
          (response?.data || []).map((item: any) => {
            return {
              business_partner_internal_id:
                item?.business_partner_internal_id || '',
              bp_full_name: item?.business_partner?.bp_full_name || '',
            };
          })
        )
      );
  }

  getOrganizationByID(organizationId: string) {
    const params = new HttpParams()
      .set('filters[organisational_unit_id][$eq]', organizationId)
      .set('populate[crm_org_unit_functions][populate]', '*')
      .set('populate[addresses][populate]', '*')
      .set('populate[parent_organisational_unit][fields][0]', 'start_date')
      .set('populate[parent_organisational_unit][fields][1]', 'end_date')
      .set(
        'populate[parent_organisational_unit][fields][2]',
        'organisational_unit_id'
      )
      .set('populate[parent_organisational_unit][fields][3]', 'name')
      .set('populate[crm_org_unit_employees][fields][0]', 'start_date')
      .set('populate[crm_org_unit_employees][fields][1]', 'end_date')
      .set('populate[crm_org_unit_employees][fields][2]', 'job_id')
      .set('populate[crm_org_unit_employees][fields][3]', 'business_partner_internal_id')
      .set(
        'populate[crm_org_unit_employees][populate][business_partner][fields][0]',
        'bp_full_name'
      )
      .set('populate[crm_org_unit_managers][fields][0]', 'start_date')
      .set('populate[crm_org_unit_managers][fields][1]', 'end_date')
      .set('populate[crm_org_unit_managers][fields][2]', 'business_partner_internal_id')
      .set(
        'populate[crm_org_unit_managers][populate][business_partner][fields][0]',
        'bp_full_name'
      );

    return this.http
      .get<any[]>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { params })
      .pipe(
        map((response: any) => {
          const organizationDetails = response?.data[0] || null;
          this.organizationalSubject.next(organizationDetails);
          return response;
        })
      );
  }

  getOrganizationByChildID(organizationId: string) {
    const params = new HttpParams()
      .set('filters[organisational_unit_id][$eq]', organizationId)
      .set('populate[child_organisational_units][fields][0]','organisational_unit_id')
      .set(
        'fields',
        'organisational_unit_id,name,parent_organisational_unit_id'
      )
      .set('populate[parent_organisational_unit][fields][0]', 'name')
      .set(
        'populate[crm_org_unit_managers][populate][business_partner][fields][0]',
        'bp_full_name'
      )
      .set('populate[crm_org_unit_functions][fields][0]', 'sales_indicator')
      .set(
        'populate[crm_org_unit_functions][fields][1]',
        'sales_organisation_indicator'
      )
      .set(
        'populate[crm_org_unit_functions][fields][2]',
        'reporting_line_indicator'
      );

    return this.http
      .get<any[]>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { params })
      .pipe(
        map((response: any) => {
          const organizationDetails = response?.data[0] || null;
          this.organizationalSubject.next(organizationDetails);
          return response;
        })
      );
  }
}
