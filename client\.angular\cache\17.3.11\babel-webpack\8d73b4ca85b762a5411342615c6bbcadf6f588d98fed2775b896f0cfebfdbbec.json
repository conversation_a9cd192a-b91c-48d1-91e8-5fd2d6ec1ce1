{"ast": null, "code": "'use strict';\n\nvar setFunctionLength = require('set-function-length');\nvar $defineProperty = require('es-define-property');\nvar callBindBasic = require('call-bind-apply-helpers');\nvar applyBind = require('call-bind-apply-helpers/applyBind');\nmodule.exports = function callBind(originalFunction) {\n  var func = callBindBasic(arguments);\n  var adjustedLength = originalFunction.length - (arguments.length - 1);\n  return setFunctionLength(func, 1 + (adjustedLength > 0 ? adjustedLength : 0), true);\n};\nif ($defineProperty) {\n  $defineProperty(module.exports, 'apply', {\n    value: applyBind\n  });\n} else {\n  module.exports.apply = applyBind;\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}