{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ServiceTicketsRoutingModule } from './service-tickets-routing.module';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ServiceTicketsOverviewComponent } from './service-tickets-overview/service-tickets-overview.component';\nimport { ServiceTicketsContactsComponent } from './service-tickets-contacts/service-tickets-contacts.component';\nimport { ServiceTicketsAiInsightsComponent } from './service-tickets-ai-insights/service-tickets-ai-insights.component';\nimport { ServiceTicketsAttachmentsComponent } from './service-tickets-attachments/service-tickets-attachments.component';\nimport { ServiceTicketsNotesComponent } from './service-tickets-notes/service-tickets-notes.component';\nimport { ServiceTicketsActivitiesComponent } from './service-tickets-activities/service-tickets-activities.component';\nimport { ServiceTicketsRelationshipsComponent } from './service-tickets-relationships/service-tickets-relationships.component';\nimport { ServiceTicketsTicketsComponent } from './service-tickets-tickets/service-tickets-tickets.component';\nimport { ServiceTicketsSalesQuotesComponent } from './service-tickets-sales-quotes/service-tickets-sales-quotes.component';\nimport { ServiceTicketsSalesOrdersComponent } from './service-tickets-sales-orders/service-tickets-sales-orders.component';\nimport { ServiceTicketsReturnsComponent } from './service-tickets-returns/service-tickets-returns.component';\nimport { ServiceTicketsInvoicesComponent } from './service-tickets-invoices/service-tickets-invoices.component';\nimport { MessageService } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountModule } from '../account/account.module';\nimport * as i0 from \"@angular/core\";\nexport class ServiceTicketsModule {\n  static {\n    this.ɵfac = function ServiceTicketsModule_Factory(t) {\n      return new (t || ServiceTicketsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService],\n      imports: [CommonModule, AccountModule, ReactiveFormsModule, NgSelectModule, ServiceTicketsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, ToastModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsModule, {\n    declarations: [ServiceTicketsComponent, ServiceTicketsOverviewComponent, ServiceTicketsContactsComponent, ServiceTicketsAiInsightsComponent, ServiceTicketsAttachmentsComponent, ServiceTicketsNotesComponent, ServiceTicketsActivitiesComponent, ServiceTicketsRelationshipsComponent, ServiceTicketsTicketsComponent, ServiceTicketsSalesQuotesComponent, ServiceTicketsSalesOrdersComponent, ServiceTicketsReturnsComponent, ServiceTicketsInvoicesComponent],\n    imports: [CommonModule, AccountModule, ReactiveFormsModule, NgSelectModule, ServiceTicketsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, ToastModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ServiceTicketsRoutingModule", "ServiceTicketsComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "ServiceTicketsOverviewComponent", "ServiceTicketsContactsComponent", "ServiceTicketsAiInsightsComponent", "ServiceTicketsAttachmentsComponent", "ServiceTicketsNotesComponent", "ServiceTicketsActivitiesComponent", "ServiceTicketsRelationshipsComponent", "ServiceTicketsTicketsComponent", "ServiceTicketsSalesQuotesComponent", "ServiceTicketsSalesOrdersComponent", "ServiceTicketsReturnsComponent", "ServiceTicketsInvoicesComponent", "MessageService", "ToastModule", "NgSelectModule", "AccountModule", "ServiceTicketsModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ServiceTicketsRoutingModule } from './service-tickets-routing.module';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\nimport { ServiceTicketsOverviewComponent } from './service-tickets-overview/service-tickets-overview.component';\r\nimport { ServiceTicketsContactsComponent } from './service-tickets-contacts/service-tickets-contacts.component';\r\nimport { ServiceTicketsAiInsightsComponent } from './service-tickets-ai-insights/service-tickets-ai-insights.component';\r\nimport { ServiceTicketsAttachmentsComponent } from './service-tickets-attachments/service-tickets-attachments.component';\r\nimport { ServiceTicketsNotesComponent } from './service-tickets-notes/service-tickets-notes.component';\r\nimport { ServiceTicketsActivitiesComponent } from './service-tickets-activities/service-tickets-activities.component';\r\nimport { ServiceTicketsRelationshipsComponent } from './service-tickets-relationships/service-tickets-relationships.component';\r\nimport { ServiceTicketsTicketsComponent } from './service-tickets-tickets/service-tickets-tickets.component';\r\nimport { ServiceTicketsSalesQuotesComponent } from './service-tickets-sales-quotes/service-tickets-sales-quotes.component';\r\nimport { ServiceTicketsSalesOrdersComponent } from './service-tickets-sales-orders/service-tickets-sales-orders.component';\r\nimport { ServiceTicketsReturnsComponent } from './service-tickets-returns/service-tickets-returns.component';\r\nimport { ServiceTicketsInvoicesComponent } from './service-tickets-invoices/service-tickets-invoices.component';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { AccountModule } from '../account/account.module';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ServiceTicketsComponent,\r\n    ServiceTicketsOverviewComponent,\r\n    ServiceTicketsContactsComponent,\r\n    ServiceTicketsAiInsightsComponent,\r\n    ServiceTicketsAttachmentsComponent,\r\n    ServiceTicketsNotesComponent,\r\n    ServiceTicketsActivitiesComponent,\r\n    ServiceTicketsRelationshipsComponent,\r\n    ServiceTicketsTicketsComponent,\r\n    ServiceTicketsSalesQuotesComponent,\r\n    ServiceTicketsSalesOrdersComponent,\r\n    ServiceTicketsReturnsComponent,\r\n    ServiceTicketsInvoicesComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AccountModule,\r\n    ReactiveFormsModule,\r\n    NgSelectModule,\r\n    ServiceTicketsRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    ToastModule,\r\n    InputTextModule\r\n  ],\r\n  providers: [MessageService]\r\n})\r\nexport class ServiceTicketsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,kCAAkC,QAAQ,qEAAqE;AACxH,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,iCAAiC,QAAQ,mEAAmE;AACrH,SAASC,oCAAoC,QAAQ,yEAAyE;AAC9H,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,aAAa,QAAQ,2BAA2B;;AAuCzD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAFpB,CAACJ,cAAc,CAAC;MAAAK,OAAA,GAhBzB9B,YAAY,EACZ4B,aAAa,EACbxB,mBAAmB,EACnBuB,cAAc,EACd1B,2BAA2B,EAC3BK,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXR,WAAW,EACXK,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBqB,WAAW,EACXhB,eAAe;IAAA;EAAA;;;2EAINmB,oBAAoB;IAAAE,YAAA,GAjC7B7B,uBAAuB,EACvBW,+BAA+B,EAC/BC,+BAA+B,EAC/BC,iCAAiC,EACjCC,kCAAkC,EAClCC,4BAA4B,EAC5BC,iCAAiC,EACjCC,oCAAoC,EACpCC,8BAA8B,EAC9BC,kCAAkC,EAClCC,kCAAkC,EAClCC,8BAA8B,EAC9BC,+BAA+B;IAAAM,OAAA,GAG/B9B,YAAY,EACZ4B,aAAa,EACbxB,mBAAmB,EACnBuB,cAAc,EACd1B,2BAA2B,EAC3BK,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXR,WAAW,EACXK,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBqB,WAAW,EACXhB,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}