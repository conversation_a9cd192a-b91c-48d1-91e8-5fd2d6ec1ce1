{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ServiceTicketsListingComponent } from './service-tickets-listing/service-tickets-listing.component';\nimport { ServiceTicketsListingRoutingModule } from './service-tickets-listing-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class ServiceTicketsListingModule {\n  static {\n    this.ɵfac = function ServiceTicketsListingModule_Factory(t) {\n      return new (t || ServiceTicketsListingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsListingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, ServiceTicketsListingRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsListingModule, {\n    declarations: [ServiceTicketsListingComponent],\n    imports: [CommonModule, SharedModule, ServiceTicketsListingRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "ServiceTicketsListingComponent", "ServiceTicketsListingRoutingModule", "SharedModule", "ServiceTicketsListingModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets-listing\\service-tickets-listing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { FormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ServiceTicketsListingComponent } from './service-tickets-listing/service-tickets-listing.component';\r\nimport { ServiceTicketsListingRoutingModule } from './service-tickets-listing-routing.module';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ServiceTicketsListingComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    ServiceTicketsListingRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    InputTextModule\r\n  ]\r\n})\r\nexport class ServiceTicketsListingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,kCAAkC,QAAQ,0CAA0C;AAC7F,SAASC,YAAY,QAAQ,8BAA8B;;AAqB3D,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAdpCb,YAAY,EACZY,YAAY,EACZD,kCAAkC,EAClCR,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXP,WAAW,EACXI,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBK,eAAe;IAAA;EAAA;;;2EAGNM,2BAA2B;IAAAC,YAAA,GAjBpCJ,8BAA8B;IAAAK,OAAA,GAG9Bf,YAAY,EACZY,YAAY,EACZD,kCAAkC,EAClCR,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXP,WAAW,EACXI,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBK,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}