{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/dropdown\";\nimport * as i6 from \"primeng/tabview\";\nimport * as i7 from \"primeng/breadcrumb\";\nfunction ProspectsDetailsComponent_p_tabPanel_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.RouterLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ProspectsDetailsComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 10);\n    i0.ɵɵtemplate(1, ProspectsDetailsComponent_p_tabPanel_8_ng_template_1_Template, 2, 2, \"ng-template\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ProspectsDetailsComponent {\n  setActiveMenu(label) {\n    this.activeMenu = label;\n  }\n  constructor(router) {\n    this.router = router;\n    this.activeMenu = '';\n    this.activeIndex = 0;\n    this.scrollableTabs = [{\n      label: 'Overview',\n      RouterLink: '/store/prospects/overview'\n    }, {\n      label: 'Contacts',\n      RouterLink: '/store/prospects/contacts'\n    }, {\n      label: 'Sales Team',\n      RouterLink: '/store/prospects/sales-team'\n    }, {\n      label: 'AI Insights',\n      RouterLink: '/store/prospects/ai-insights'\n    }, {\n      label: 'Organization Data',\n      RouterLink: '/store/prospects/organization-data'\n    }, {\n      label: 'Attachments',\n      RouterLink: '/store/prospects/attachments'\n    }, {\n      label: 'Notes',\n      RouterLink: '/store/prospects/notes'\n    }];\n  }\n  ngOnInit() {\n    this.items = [{\n      label: 'Prospects',\n      routerLink: ['/store/prospects']\n    }, {\n      label: `${this.activeMenu}`\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'Convert to Customer',\n      code: 'CI'\n    }];\n  }\n  goToBack() {\n    this.router.navigate(['/store/prospects']);\n  }\n  static {\n    this.ɵfac = function ProspectsDetailsComponent_Factory(t) {\n      return new (t || ProspectsDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsDetailsComponent,\n      selectors: [[\"app-prospects-details\"]],\n      decls: 11,\n      vars: 8,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"scrollable\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ProspectsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-dropdown\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsDetailsComponent_Template_p_dropdown_ngModelChange_4_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"p-tabView\", 7);\n          i0.ɵɵtemplate(8, ProspectsDetailsComponent_p_tabPanel_8_Template, 2, 1, \"p-tabPanel\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-15rem h-3rem px-2 py-1 surface-0 border-1 border-primary font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.scrollableTabs);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.NgControlStatus, i3.NgModel, i4.PrimeTemplate, i5.Dropdown, i6.TabView, i6.TabPanel, i7.Breadcrumb],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "RouterLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ProspectsDetailsComponent_p_tabPanel_8_ng_template_1_Template", "ProspectsDetailsComponent", "setActiveMenu", "activeMenu", "constructor", "router", "activeIndex", "scrollableTabs", "ngOnInit", "items", "routerLink", "home", "icon", "Actions", "name", "code", "goToBack", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "ProspectsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "ProspectsDetailsComponent_Template_p_dropdown_ngModelChange_4_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "ProspectsDetailsComponent_p_tabPanel_8_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects-details',\r\n  templateUrl: './prospects-details.component.html',\r\n  styleUrl: './prospects-details.component.scss'\r\n})\r\nexport class ProspectsDetailsComponent {\r\n\r\n  items: MenuItem[] | any;\r\n    home: MenuItem | any;\r\n  \r\n    activeMenu: string = '';\r\n  \r\n    setActiveMenu(label: string): void {\r\n      this.activeMenu = label;\r\n    }\r\n  \r\n    Actions: Actions[] | undefined;\r\n    selectedActions: Actions | undefined;\r\n  \r\n    constructor(\r\n      private router: Router,\r\n    ) { }\r\n  \r\n    ngOnInit() {\r\n      this.items = [\r\n        { label: 'Prospects', routerLink: ['/store/prospects'] },\r\n        { label: `${this.activeMenu}` },\r\n      ];\r\n  \r\n      this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n  \r\n      this.Actions = [\r\n        { name: 'Convert to Customer', code: 'CI' },\r\n      ];\r\n    }\r\n  \r\n    activeIndex: number = 0;\r\n    scrollableTabs: any[] = [\r\n      {\r\n        label: 'Overview',\r\n        RouterLink: '/store/prospects/overview',\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        RouterLink: '/store/prospects/contacts'\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        RouterLink: '/store/prospects/sales-team'\r\n      },\r\n      {\r\n        label: 'AI Insights',\r\n        RouterLink: '/store/prospects/ai-insights'\r\n      },\r\n      {\r\n        label: 'Organization Data',\r\n        RouterLink: '/store/prospects/organization-data'\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        RouterLink: '/store/prospects/attachments'\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        RouterLink: '/store/prospects/notes'\r\n      },\r\n    ];\r\n  \r\n  \r\n  \r\n    goToBack() {\r\n      this.router.navigate(['/store/prospects']);\r\n    }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-15rem h-3rem px-2 py-1 surface-0 border-1 border-primary font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\">\r\n                <p-tabPanel *ngFor=\"let tab of scrollableTabs\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.RouterLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;ICgBwBA,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAA8E;IAC1ED,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANkCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADA7F,OAAM,MAAOQ,yBAAyB;EAOlCC,aAAaA,CAACJ,KAAa;IACzB,IAAI,CAACK,UAAU,GAAGL,KAAK;EACzB;EAKAM,YACUC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAVhB,KAAAF,UAAU,GAAW,EAAE;IA0BvB,KAAAG,WAAW,GAAW,CAAC;IACvB,KAAAC,cAAc,GAAU,CACtB;MACET,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE;KACb,CACF;EA7CG;EAEJa,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAG,CACX;MAAEX,KAAK,EAAE,WAAW;MAAEY,UAAU,EAAE,CAAC,kBAAkB;IAAC,CAAE,EACxD;MAAEZ,KAAK,EAAE,GAAG,IAAI,CAACK,UAAU;IAAE,CAAE,CAChC;IAED,IAAI,CAACQ,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,qBAAqB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC5C;EACH;EAoCAC,QAAQA,CAAA;IACN,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;;;uBAnEShB,yBAAyB,EAAAZ,EAAA,CAAA6B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBnB,yBAAyB;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9BtC,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAwC,SAAA,sBAAqF;UACzFxC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBACgG;UADhED,EAAA,CAAAyC,gBAAA,2BAAAC,uEAAAC,MAAA;YAAA3C,EAAA,CAAA4C,kBAAA,CAAAL,GAAA,CAAAM,eAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAM,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAEjE3C,EAFI,CAAAG,YAAA,EACgG,EAC9F;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACI;UAC3BD,EAAA,CAAAU,UAAA,IAAAoC,+CAAA,wBAA8E;UAQtF9C,EADI,CAAAG,YAAA,EAAY,EACV;UACNH,EAAA,CAAAC,cAAA,aAAqD;UACjDD,EAAA,CAAAwC,SAAA,qBAA+B;UAG3CxC,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;UAtBoBH,EAAA,CAAAO,SAAA,GAAe;UAAeP,EAA9B,CAAAI,UAAA,UAAAmC,GAAA,CAAAnB,KAAA,CAAe,SAAAmB,GAAA,CAAAjB,IAAA,CAAc,uCAAuC;UAE1EtB,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAmC,GAAA,CAAAf,OAAA,CAAmB;UAACxB,EAAA,CAAA+C,gBAAA,YAAAR,GAAA,CAAAM,eAAA,CAA6B;UACzD7C,EAAA,CAAAI,UAAA,0FAAyF;UAK9EJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UACEJ,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAmC,GAAA,CAAArB,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}