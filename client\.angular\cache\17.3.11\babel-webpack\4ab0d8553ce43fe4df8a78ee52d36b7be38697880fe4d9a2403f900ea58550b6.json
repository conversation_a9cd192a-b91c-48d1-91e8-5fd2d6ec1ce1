{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"../../activities.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/toast\";\nimport * as i14 from \"primeng/editor\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => ({\n  height: \"125px\"\n});\nconst _c2 = () => ({\n  width: \"42rem\"\n});\nfunction AddSalesCallComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Document Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_15_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"document_type\"].errors && ctx_r1.f[\"document_type\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_25_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"subject\"].errors && ctx_r1.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_ng_template_36_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddSalesCallComponent_ng_template_36_span_2_Template, 2, 1, \"span\", 48);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction AddSalesCallComponent_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_37_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_account_party_id\"].errors && ctx_r1.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_ng_template_48_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_48_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddSalesCallComponent_ng_template_48_span_3_Template, 2, 1, \"span\", 48)(4, AddSalesCallComponent_ng_template_48_span_4_Template, 2, 1, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bp_id, \": \", item_r4.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile);\n  }\n}\nfunction AddSalesCallComponent_div_49_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_49_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_contact_party_id\"].errors && ctx_r1.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_div_59_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_59_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"phone_call_category\"].errors && ctx_r1.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_div_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_90_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"initiator_code\"].errors && ctx_r1.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_div_100_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_100_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"activity_status\"].errors && ctx_r1.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_div_110_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, AddSalesCallComponent_div_110_div_1_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"notes\"].errors && ctx_r1.f[\"notes\"].errors[\"required\"]);\n  }\n}\nfunction AddSalesCallComponent_ng_template_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 50)(2, \"span\", 51)(3, \"span\", 52);\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"th\", 50)(7, \"span\", 51)(8, \"span\", 52);\n    i0.ɵɵtext(9, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Email Address \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 50)(12, \"span\", 51)(13, \"span\", 52);\n    i0.ɵɵtext(14, \"phone_iphone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Mobile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"th\", 53)(17, \"span\", 51)(18, \"span\", 52);\n    i0.ɵɵtext(19, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddSalesCallComponent_ng_template_121_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function AddSalesCallComponent_ng_template_121_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r6 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_ng_template_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2)(1, \"td\");\n    i0.ɵɵelement(2, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"input\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵelement(6, \"input\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 57);\n    i0.ɵɵtemplate(8, AddSalesCallComponent_ng_template_121_button_8_Template, 1, 0, \"button\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r7);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.involved_parties.length > 1);\n  }\n}\nfunction AddSalesCallComponent_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSalesCallComponent_ng_template_133_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.bp_full_name, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_133_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.email, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_133_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.mobile, \"\");\n  }\n}\nfunction AddSalesCallComponent_ng_template_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddSalesCallComponent_ng_template_133_span_2_Template, 2, 1, \"span\", 48)(3, AddSalesCallComponent_ng_template_133_span_3_Template, 2, 1, \"span\", 48)(4, AddSalesCallComponent_ng_template_133_span_4_Template, 2, 1, \"span\", 48);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.mobile);\n  }\n}\nexport let AddSalesCallComponent = /*#__PURE__*/(() => {\n  class AddSalesCallComponent {\n    constructor(formBuilder, router, messageservice, activitiesservice) {\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.existingcontactLoading = false;\n      this.existingcontactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.existingDialogVisible = false;\n      this.position = 'right';\n      this.owner_id = null;\n      this.SalesCallForm = this.formBuilder.group({\n        document_type: ['', [Validators.required]],\n        subject: ['', [Validators.required]],\n        main_account_party_id: ['', [Validators.required]],\n        main_contact_party_id: ['', [Validators.required]],\n        phone_call_category: ['', [Validators.required]],\n        disposition_code: [''],\n        start_date: [''],\n        end_date: [''],\n        initiator_code: ['', [Validators.required]],\n        activity_status: ['', [Validators.required]],\n        notes: ['', [Validators.required]],\n        contactexisting: [''],\n        involved_parties: this.formBuilder.array([this.createContactFormGroup()])\n      });\n      this.dropdowns = {\n        activityDocumentTypes: [],\n        activityCategory: [],\n        activityStatus: [],\n        activitydisposition: [],\n        activityInitiatorCode: []\n      };\n    }\n    ngOnInit() {\n      this.loadActivityDropDown('activityDocumentTypes', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n      this.SalesCallForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n        if (selectedBpId) {\n          this.loadAccountByContacts(selectedBpId);\n        } else {\n          this.contacts$ = of(this.defaultOptions);\n        }\n      }), catchError(err => {\n        console.error('Account selection error:', err);\n        this.contacts$ = of(this.defaultOptions);\n        return of();\n      })).subscribe();\n      this.loadAccounts();\n      this.loadExistingContacts();\n      this.getOwner().subscribe({\n        next: response => {\n          this.owner_id = response;\n        },\n        error: err => {\n          console.error('Error fetching bp_id:', err);\n        }\n      });\n    }\n    getOwner() {\n      return this.activitiesservice.getEmailwisePartner();\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        const options = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n        // Assign options to dropdown object\n        this.dropdowns[target] = options;\n        // Set 'Open' as default selected for activityStatus only\n        if (target === 'activityStatus') {\n          const openOption = options.find(opt => opt.label.toLowerCase() === 'open');\n          if (openOption) {\n            this.SalesCallForm.get('activity_status')?.setValue(openOption.value);\n          }\n        }\n      });\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Emit default empty options first\n      this.accountInput$.pipe(debounceTime(300),\n      // Add debounce to reduce API calls\n      distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          'filters[roles][bp_role][$in][0]': 'FLCU01',\n          'filters[roles][bp_role][$in][1]': 'FLCU00',\n          'fields[0]': 'bp_id',\n          'fields[1]': 'first_name',\n          'fields[2]': 'last_name',\n          'fields[3]': 'bp_full_name'\n        };\n        if (term) {\n          params['filters[$or][0][bp_id][$containsi]'] = term;\n          params['filters[$or][1][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n        // Ensure non-null\n        catchError(error => {\n          console.error('Account fetch error:', error);\n          return of([]); // Return empty list on error\n        }), finalize(() => this.accountLoading = false) // Always turn off loading\n        );\n      })));\n    }\n    loadExistingContacts() {\n      this.existingcontacts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.existingcontactInput$.pipe(distinctUntilChanged(), tap(() => this.existingcontactLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP001',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.activitiesservice.getPartners(params).pipe(map(data => {\n          return data || []; // Make sure to return correct data structure\n        }), tap(() => this.existingcontactLoading = false), catchError(error => {\n          this.existingcontactLoading = false;\n          return of([]);\n        }));\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    selectExistingContact() {\n      this.addExistingContact(this.SalesCallForm.value);\n      this.existingDialogVisible = false; // Close dialog\n    }\n    addExistingContact(existing) {\n      const contactForm = this.formBuilder.group({\n        bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\n        email_address: [existing?.contactexisting?.email || ''],\n        mobile: [existing?.contactexisting?.mobile?.[0] || ''],\n        role_code: 'BUP001',\n        party_id: existing?.contactexisting?.bp_id || ''\n      });\n      const firstGroup = this.involved_parties.at(0);\n      const bpName = firstGroup?.get('bp_full_name')?.value;\n      if (!bpName && this.involved_parties.length === 1) {\n        this.involved_parties.setControl(0, contactForm);\n      } else {\n        this.involved_parties.push(contactForm);\n      }\n      this.existingDialogVisible = false;\n    }\n    addNewContact() {\n      this.involved_parties.push(this.createContactFormGroup());\n    }\n    deleteContact(index) {\n      if (this.involved_parties.length > 1) {\n        this.involved_parties.removeAt(index);\n      }\n    }\n    createContactFormGroup() {\n      return this.formBuilder.group({\n        bp_full_name: [''],\n        email_address: [''],\n        mobile: ['']\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.SalesCallForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.SalesCallForm.value\n        };\n        const data = {\n          document_type: value?.document_type,\n          subject: value?.subject,\n          main_account_party_id: value?.main_account_party_id,\n          main_contact_party_id: value?.main_contact_party_id,\n          phone_call_category: value?.phone_call_category,\n          start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n          end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n          disposition_code: value?.disposition_code,\n          initiator_code: value?.initiator_code,\n          owner_party_id: _this.owner_id,\n          activity_status: value?.activity_status,\n          note: value?.notes,\n          involved_parties: Array.isArray(value.involved_parties) ? [...value.involved_parties, ...(value?.main_account_party_id ? [{\n            role_code: 'FLCU01',\n            party_id: value.main_account_party_id\n          }] : []), ...(Array.isArray(value.main_contact_party_id) ? value.main_contact_party_id.map(id => ({\n            role_code: 'BUP001',\n            party_id: id\n          })) : []), ...(_this.owner_id ? [{\n            role_code: 'BUP003',\n            party_id: _this.owner_id\n          }] : [])] : []\n        };\n        _this.activitiesservice.createActivity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: response => {\n            if (response?.data?.activity_id) {\n              sessionStorage.setItem('salescallMessage', 'Sales Call created successfully!');\n              window.location.href = `${window.location.origin}#/store/activities/calls/${response?.data?.activity_id}/overview`;\n            } else {\n              console.error('Missing activity_id in response:', response);\n            }\n          },\n          error: res => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.SalesCallForm.controls;\n    }\n    get involved_parties() {\n      return this.SalesCallForm.get('involved_parties');\n    }\n    showExistingDialog(position) {\n      this.position = position;\n      this.existingDialogVisible = true;\n    }\n    onCancel() {\n      this.router.navigate(['/store/activities/calls']);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AddSalesCallComponent_Factory(t) {\n        return new (t || AddSalesCallComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddSalesCallComponent,\n        selectors: [[\"app-add-sales-call\"]],\n        decls: 142,\n        vars: 93,\n        consts: [[\"dt\", \"\"], [\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"formControlName\", \"document_type\", \"placeholder\", \"Select a Document Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"formControlName\", \"disposition_code\", \"placeholder\", \"Select a Disposition Code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Call Date/Time\", 3, \"showTime\", \"showIcon\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"End Date/Time\", 3, \"showTime\", \"showIcon\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"col-12\", \"lg:col-8\", \"md:col-8\", \"sm:col-6\"], [\"formControlName\", \"notes\", \"placeholder\", \"Enter your note here...\", 3, \"ngClass\"], [1, \"border-top-2\", \"border-gray-400\", \"my-5\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"flex\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [\"responsiveLayout\", \"scroll\", 1, \"prospect-add-table\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"font-medium\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\", \"ml-auto\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Create\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"text-left\", \"w-3\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"text-color\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-left\", 2, \"width\", \"60px\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Enter a Name\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"mobile\", \"placeholder\", \"Enter Mobile\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [1, \"pl-5\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"]],\n        template: function AddSalesCallComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"p-toast\", 1);\n            i0.ɵɵelementStart(1, \"form\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n            i0.ɵɵtext(4, \"Create Sales Call\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"label\", 8)(9, \"span\", 9);\n            i0.ɵɵtext(10, \"description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Transaction Type \");\n            i0.ɵɵelementStart(12, \"span\", 10);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(14, \"p-dropdown\", 11);\n            i0.ɵɵtemplate(15, AddSalesCallComponent_div_15_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 6)(17, \"div\", 7)(18, \"label\", 8)(19, \"span\", 9);\n            i0.ɵɵtext(20, \"subject\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" Subject \");\n            i0.ɵɵelementStart(22, \"span\", 10);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(24, \"input\", 13);\n            i0.ɵɵtemplate(25, AddSalesCallComponent_div_25_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 6)(27, \"div\", 7)(28, \"label\", 8)(29, \"span\", 9);\n            i0.ɵɵtext(30, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31, \" Account \");\n            i0.ɵɵelementStart(32, \"span\", 10);\n            i0.ɵɵtext(33, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"ng-select\", 14);\n            i0.ɵɵpipe(35, \"async\");\n            i0.ɵɵtemplate(36, AddSalesCallComponent_ng_template_36_Template, 3, 2, \"ng-template\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, AddSalesCallComponent_div_37_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 6)(39, \"div\", 7)(40, \"label\", 8)(41, \"span\", 9);\n            i0.ɵɵtext(42, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(43, \" Contact \");\n            i0.ɵɵelementStart(44, \"span\", 10);\n            i0.ɵɵtext(45, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"ng-select\", 16);\n            i0.ɵɵpipe(47, \"async\");\n            i0.ɵɵtemplate(48, AddSalesCallComponent_ng_template_48_Template, 5, 4, \"ng-template\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(49, AddSalesCallComponent_div_49_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 6)(51, \"div\", 7)(52, \"label\", 8)(53, \"span\", 9);\n            i0.ɵɵtext(54, \"category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(55, \" Category \");\n            i0.ɵɵelementStart(56, \"span\", 10);\n            i0.ɵɵtext(57, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(58, \"p-dropdown\", 17);\n            i0.ɵɵtemplate(59, AddSalesCallComponent_div_59_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"div\", 6)(61, \"div\", 7)(62, \"label\", 8)(63, \"span\", 9);\n            i0.ɵɵtext(64, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(65, \" Disposition Code \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(66, \"p-dropdown\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 6)(68, \"div\", 7)(69, \"label\", 8)(70, \"span\", 9);\n            i0.ɵɵtext(71, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(72, \" Call Date/Time \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(73, \"p-calendar\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 6)(75, \"div\", 7)(76, \"label\", 8)(77, \"span\", 9);\n            i0.ɵɵtext(78, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(79, \" End Date/Time \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(80, \"p-calendar\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"div\", 6)(82, \"div\", 7)(83, \"label\", 8)(84, \"span\", 9);\n            i0.ɵɵtext(85, \"label\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(86, \" Type \");\n            i0.ɵɵelementStart(87, \"span\", 10);\n            i0.ɵɵtext(88, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(89, \"p-dropdown\", 21);\n            i0.ɵɵtemplate(90, AddSalesCallComponent_div_90_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(91, \"div\", 6)(92, \"div\", 7)(93, \"label\", 8)(94, \"span\", 9);\n            i0.ɵɵtext(95, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(96, \" Status \");\n            i0.ɵɵelementStart(97, \"span\", 10);\n            i0.ɵɵtext(98, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(99, \"p-dropdown\", 22);\n            i0.ɵɵtemplate(100, AddSalesCallComponent_div_100_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(101, \"div\", 23)(102, \"div\", 7)(103, \"label\", 8)(104, \"span\", 9);\n            i0.ɵɵtext(105, \"notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(106, \" Notes \");\n            i0.ɵɵelementStart(107, \"span\", 10);\n            i0.ɵɵtext(108, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(109, \"p-editor\", 24);\n            i0.ɵɵtemplate(110, AddSalesCallComponent_div_110_Template, 2, 1, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(111, \"div\", 25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(112, \"div\", 26)(113, \"div\", 27)(114, \"h3\", 28);\n            i0.ɵɵtext(115, \"Additional Contacts Persons\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(116, \"div\", 29)(117, \"p-button\", 30);\n            i0.ɵɵlistener(\"click\", function AddSalesCallComponent_Template_p_button_click_117_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(118, \"p-table\", 31, 0);\n            i0.ɵɵtemplate(120, AddSalesCallComponent_ng_template_120_Template, 21, 0, \"ng-template\", 32)(121, AddSalesCallComponent_ng_template_121_Template, 9, 2, \"ng-template\", 33);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(122, \"p-dialog\", 34);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AddSalesCallComponent_Template_p_dialog_visibleChange_122_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(123, AddSalesCallComponent_ng_template_123_Template, 2, 0, \"ng-template\", 32);\n            i0.ɵɵelementStart(124, \"form\", 35)(125, \"div\", 36)(126, \"label\", 37)(127, \"span\", 38);\n            i0.ɵɵtext(128, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(129, \"Contacts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"div\", 39)(131, \"ng-select\", 40);\n            i0.ɵɵpipe(132, \"async\");\n            i0.ɵɵtemplate(133, AddSalesCallComponent_ng_template_133_Template, 5, 4, \"ng-template\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(134, \"div\", 41)(135, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function AddSalesCallComponent_Template_button_click_135_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n            });\n            i0.ɵɵtext(136, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"button\", 43);\n            i0.ɵɵlistener(\"click\", function AddSalesCallComponent_Template_button_click_137_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectExistingContact());\n            });\n            i0.ɵɵtext(138, \" Save \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(139, \"div\", 44)(140, \"button\", 45);\n            i0.ɵɵlistener(\"click\", function AddSalesCallComponent_Template_button_click_140_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"button\", 46);\n            i0.ɵɵlistener(\"click\", function AddSalesCallComponent_Template_button_click_141_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.SalesCallForm);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentTypes\"])(\"ngClass\", i0.ɵɵpureFunction1(73, _c0, ctx.submitted && ctx.f[\"document_type\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"document_type\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c0, ctx.submitted && ctx.f[\"subject\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"subject\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(35, 67, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(77, _c0, ctx.submitted && ctx.f[\"main_account_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_account_party_id\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(47, 69, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(79, _c0, ctx.submitted && ctx.f[\"main_contact_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_contact_party_id\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityCategory\"])(\"ngClass\", i0.ɵɵpureFunction1(81, _c0, ctx.submitted && ctx.f[\"phone_call_category\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"phone_call_category\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activitydisposition\"]);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(83, _c0, ctx.submitted && ctx.f[\"initiator_code\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"initiator_code\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(85, _c0, ctx.submitted && ctx.f[\"activity_status\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"activity_status\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(87, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(88, _c0, ctx.submitted && ctx.f[\"notes\"].errors))(\"ngClass\", i0.ɵɵpureFunction1(90, _c0, ctx.submitted && ctx.f[\"notes\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"notes\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", ctx.involved_parties == null ? null : ctx.involved_parties.controls)(\"paginator\", false)(\"rows\", 10);\n            i0.ɵɵadvance(4);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(92, _c2));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.SalesCallForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(132, 71, ctx.existingcontacts$))(\"hideSelected\", true)(\"loading\", ctx.existingcontactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.existingcontactInput$)(\"maxSelectedItems\", 10);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.Table, i3.PrimeTemplate, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Calendar, i11.InputText, i12.Dialog, i13.Toast, i14.Editor, i5.AsyncPipe],\n        styles: [\".prospect-popup .p-dialog{margin-right:50px}  .prospect-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .prospect-popup .p-dialog .p-dialog-header h4{margin:0}  .prospect-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return AddSalesCallComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}