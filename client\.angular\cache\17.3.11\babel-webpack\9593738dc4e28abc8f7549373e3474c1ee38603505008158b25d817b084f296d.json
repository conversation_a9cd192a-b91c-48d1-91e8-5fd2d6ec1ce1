{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"primeng/api\";\nexport class AuthService {\n  constructor(http, messageService, ngZone) {\n    this.http = http;\n    this.messageService = messageService;\n    this.ngZone = ngZone;\n    this.permissions = new BehaviorSubject([]);\n    this.cmsTokenVal = new BehaviorSubject(\"\");\n    this.sessionChannel = new BroadcastChannel(\"session\");\n    this.logoutTriggered = false;\n    this.TokenKey = 'jwtToken';\n    this.UserDetailsKey = 'userInfo';\n    const user = this.getAuth();\n    this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : \"\");\n    this.bindUserActivityEvents();\n  }\n  checkAdminUser() {\n    const user = this.getAuth();\n    if (user && user[this.UserDetailsKey]?.documentId) {\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(tap(cartres => {\n        if (cartres) {\n          this.updateAuth({\n            userDetails: {\n              address: cartres.address,\n              email: cartres.email,\n              firstname: cartres.firstname,\n              lastname: cartres.lastname,\n              username: cartres.username\n            }\n          });\n        }\n      }));\n    } else {\n      return of(null);\n    }\n  }\n  bindUserActivityEvents() {\n    const events = [\"click\", \"keydown\"];\n    for (let i = 0; i < events.length; i++) {\n      const element = events[i];\n      fromEvent(document, element).subscribe(data => {\n        this.setInavtivityTimer();\n        this.sessionChannel.postMessage({\n          type: \"activityFound\"\n        });\n      });\n    }\n    this.sessionChannel.onmessage = event => {\n      if (event?.data?.type == \"activityFound\") {\n        this.ngZone.run(() => {\n          this.setInavtivityTimer();\n        });\n      }\n      if (event?.data?.type == \"logout\") {\n        this.logoutTriggered = true;\n        this.doLogout();\n      }\n    };\n    this.setInavtivityTimer();\n    this.sessionChannel.postMessage({\n      type: \"activityFound\"\n    });\n  }\n  setInavtivityTimer() {\n    clearTimeout(this.timer);\n    if (!this.isLoggedIn) {\n      return;\n    }\n    this.timer = setTimeout(() => {\n      this.doLogout();\n    }, AppConstant.SESSION_TIMEOUT);\n  }\n  login(username, password, rememberMe) {\n    return this.http.post(CMS_APIContstant.SINGIN, {\n      identifier: (username || \"\").toLowerCase(),\n      password\n    }).pipe(tap(res => {\n      if (res) {\n        this.setAuth(res.jwt, res.user, rememberMe);\n      }\n      return res;\n    }), switchMap(res => {\n      if (res?.user) {\n        return this.getCartDetails(res.user.documentId).pipe(map(data => {\n          if (data?.cart) {\n            res.user.cart = data.cart;\n            res.user.customer = data.cart.customer;\n          }\n          this.updateAuth(res.user);\n          return res;\n        }));\n      }\n      return of(null);\n    }));\n  }\n  getCartDetails(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n  }\n  getToken() {\n    const val = this.userSubject.value;\n    return val ? val[this.TokenKey] : null;\n  }\n  get partnerFunction() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n      return user[this.UserDetailsKey].customer.partner_functions[0];\n    }\n    return {};\n  }\n  get userDetail() {\n    const user = this.userSubject.value;\n    return user ? user[this.UserDetailsKey] : null;\n  }\n  get isLoggedIn() {\n    return !!this.userSubject.value;\n  }\n  updateAuth(user) {\n    const auth = this.getAuth();\n    if (user?.userDetails) {\n      auth[this.UserDetailsKey] = {\n        ...auth[this.UserDetailsKey],\n        ...user?.userDetails\n      };\n    }\n    if (user?.cart) {\n      auth[this.UserDetailsKey].cart = user?.cart;\n    }\n    if (user?.customer) {\n      auth[this.UserDetailsKey].customer = user?.customer;\n    }\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n  }\n  isRememberMeSelected() {\n    return !!localStorage.getItem(this.TokenKey);\n  }\n  doLogout() {\n    this.resetAuth();\n  }\n  resetAuth() {\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\n      this.removeAuthToken();\n      !this.logoutTriggered && this.sessionChannel.postMessage({\n        type: \"logout\"\n      });\n      this.userSubject.next(null);\n      window.location.href = \"#/auth/login\";\n      window.location.reload();\n    });\n  }\n  getAuth() {\n    const authtoken = this.getAuthToken();\n    const userDetails = this.getUserDetails();\n    if (authtoken && this.isJsonString(userDetails)) {\n      return {\n        [this.UserDetailsKey]: JSON.parse(userDetails),\n        [this.TokenKey]: JSON.parse(authtoken)\n      };\n    }\n    return {};\n  }\n  setAuth(token, user, rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    } else {\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    }\n    this.userSubject.next({\n      [this.UserDetailsKey]: user,\n      [this.TokenKey]: token\n    });\n  }\n  getAuthToken() {\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n  }\n  getUserDetails() {\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n  }\n  getUserEmail() {\n    const userData = sessionStorage.getItem('userInfo');\n    if (userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        return parsedUser.email || null;\n      } catch (error) {\n        return null;\n      }\n    }\n    return null;\n  }\n  removeAuthToken() {\n    localStorage.removeItem(this.TokenKey);\n    sessionStorage.removeItem(this.TokenKey);\n    localStorage.removeItem(this.UserDetailsKey);\n    sessionStorage.removeItem(this.UserDetailsKey);\n  }\n  isJsonString(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  getUserPermissions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const userDetails = _this.userDetail;\n      return yield lastValueFrom(_this.getUserPermissionsDB(userDetails));\n    })();\n  }\n  getUserPermissionsDB(userDetails) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`).pipe(map(res => {\n      if (res?.data?.length) {\n        const data = res?.data || [];\n        this.permissions.next(data);\n        return data;\n      }\n      return [];\n    })).pipe(catchError(error => {\n      this.permissions.next([]);\n      return error;\n    }));\n  }\n  get getPermissions() {\n    return this.permissions?.value || [];\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["AppConstant", "CMS_APIContstant", "BehaviorSubject", "catchError", "fromEvent", "lastValueFrom", "map", "of", "switchMap", "tap", "AuthService", "constructor", "http", "messageService", "ngZone", "permissions", "cmsTokenVal", "sessionChannel", "BroadcastChannel", "logoutTriggered", "TokenKey", "UserDetailsKey", "user", "getAuth", "userSubject", "Object", "keys", "length", "bindUserActivityEvents", "checkAdminUser", "documentId", "getCartDetails", "pipe", "cartres", "updateAuth", "userDetails", "address", "email", "firstname", "lastname", "username", "events", "i", "element", "document", "subscribe", "data", "setInavtivityTimer", "postMessage", "type", "onmessage", "event", "run", "doLogout", "clearTimeout", "timer", "isLoggedIn", "setTimeout", "SESSION_TIMEOUT", "login", "password", "rememberMe", "post", "SINGIN", "identifier", "toLowerCase", "res", "setAuth", "jwt", "cart", "customer", "userId", "get", "USER_DETAILS", "getToken", "val", "value", "partnerFunction", "partner_functions", "userDetail", "auth", "isRememberMeSelected", "localStorage", "getItem", "resetAuth", "removeAuthToken", "next", "window", "location", "href", "reload", "authtoken", "getAuthToken", "getUserDetails", "isJsonString", "JSON", "parse", "token", "setItem", "stringify", "sessionStorage", "getUserEmail", "userData", "parsedUser", "error", "removeItem", "str", "e", "getUserPermissions", "_this", "_asyncToGenerator", "getUserPermissionsDB", "getPermissions", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "MessageService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\authentication\\auth.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from \"@angular/core\";\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { ApiConstant, AppConstant, CMS_APIContstant, Permission } from \"src/app/constants/api.constants\";\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  fromEvent,\r\n  lastValueFrom,\r\n  map,\r\n  Observable,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from \"rxjs\";\r\nimport { MessageService } from \"primeng/api\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class AuthService {\r\n  public userSubject: BehaviorSubject<any>;\r\n  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);\r\n  private cmsTokenVal: BehaviorSubject<any> = new BehaviorSubject<any>(\"\");\r\n  private sessionChannel = new BroadcastChannel(\"session\");\r\n  private timer: any;\r\n  private logoutTriggered = false;\r\n  public TokenKey = 'jwtToken';\r\n  public UserDetailsKey = 'userInfo';\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private messageService: MessageService,\r\n    private ngZone: NgZone\r\n  ) {\r\n    const user: any = this.getAuth();\r\n    this.userSubject = new BehaviorSubject<any>(Object.keys(user).length ? user : \"\");\r\n    this.bindUserActivityEvents();\r\n  }\r\n\r\n  checkAdminUser() {\r\n    const user: any = this.getAuth();\r\n    if (user && user[this.UserDetailsKey]?.documentId) {\r\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(\r\n        tap((cartres: any) => {\r\n          if (cartres) {\r\n            this.updateAuth({\r\n              userDetails: {\r\n                address: cartres.address,\r\n                email: cartres.email,\r\n                firstname: cartres.firstname,\r\n                lastname: cartres.lastname,\r\n                username: cartres.username,\r\n              }\r\n            });\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n  bindUserActivityEvents() {\r\n    const events = [\"click\", \"keydown\"];\r\n    for (let i = 0; i < events.length; i++) {\r\n      const element = events[i];\r\n      fromEvent(document, element).subscribe((data) => {\r\n        this.setInavtivityTimer();\r\n        this.sessionChannel.postMessage({\r\n          type: \"activityFound\",\r\n        });\r\n      });\r\n    }\r\n    this.sessionChannel.onmessage = (event) => {\r\n      if (event?.data?.type == \"activityFound\") {\r\n        this.ngZone.run(() => {\r\n          this.setInavtivityTimer();\r\n        });\r\n      }\r\n      if (event?.data?.type == \"logout\") {\r\n        this.logoutTriggered = true;\r\n        this.doLogout();\r\n      }\r\n    };\r\n    this.setInavtivityTimer();\r\n    this.sessionChannel.postMessage({\r\n      type: \"activityFound\",\r\n    });\r\n  }\r\n\r\n  setInavtivityTimer() {\r\n    clearTimeout(this.timer);\r\n    if (!this.isLoggedIn) {\r\n      return;\r\n    }\r\n    this.timer = setTimeout(() => {\r\n      this.doLogout();\r\n    }, AppConstant.SESSION_TIMEOUT);\r\n  }\r\n\r\n  login(username: string, password: string, rememberMe: boolean) {\r\n    return this.http\r\n      .post<any>(CMS_APIContstant.SINGIN, {\r\n        identifier: (username || \"\").toLowerCase(),\r\n        password,\r\n      })\r\n      .pipe(\r\n        tap((res) => {\r\n          if (res) {\r\n            this.setAuth(res.jwt, res.user, rememberMe);\r\n          }\r\n          return res;\r\n        }),\r\n        switchMap((res) => {\r\n          if (res?.user) {\r\n            return this.getCartDetails(res.user.documentId).pipe(\r\n              map((data: any) => {\r\n                if (data?.cart) {\r\n                  res.user.cart = data.cart;\r\n                  res.user.customer = data.cart.customer;\r\n                }\r\n                this.updateAuth(res.user);\r\n                return res;\r\n              })\r\n            )\r\n          }\r\n          return of(null);\r\n        }),\r\n      );\r\n  }\r\n\r\n  getCartDetails(userId: string): any {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\r\n  }\r\n\r\n  getToken() {\r\n    const val = this.userSubject.value;\r\n    return val ? val[this.TokenKey] : null;\r\n  }\r\n\r\n  get partnerFunction() {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\r\n      return user[this.UserDetailsKey].customer.partner_functions[0];\r\n    }\r\n    return {};\r\n  }\r\n\r\n  get userDetail() {\r\n    const user = this.userSubject.value;\r\n    return user ? user[this.UserDetailsKey] : null;\r\n  }\r\n\r\n  get isLoggedIn(): boolean {\r\n    return !!this.userSubject.value;\r\n  }\r\n\r\n  updateAuth(user: any) {\r\n    const auth: any = this.getAuth();\r\n    if (user?.userDetails) {\r\n      auth[this.UserDetailsKey] = {\r\n        ...auth[this.UserDetailsKey],\r\n        ...user?.userDetails\r\n      };\r\n    }\r\n    if (user?.cart) {\r\n      auth[this.UserDetailsKey].cart = user?.cart;\r\n    }\r\n    if (user?.customer) {\r\n      auth[this.UserDetailsKey].customer = user?.customer;\r\n    }\r\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\r\n  }\r\n\r\n  isRememberMeSelected(): boolean {\r\n    return !!localStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  doLogout() {\r\n    this.resetAuth();\r\n  }\r\n\r\n  resetAuth() {\r\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\r\n      this.removeAuthToken();\r\n      !this.logoutTriggered &&\r\n        this.sessionChannel.postMessage({\r\n          type: \"logout\",\r\n        });\r\n      this.userSubject.next(null);\r\n      window.location.href = \"#/auth/login\";\r\n      window.location.reload();\r\n    });\r\n  }\r\n\r\n  getAuth(): any {\r\n    const authtoken: any = this.getAuthToken();\r\n    const userDetails: any = this.getUserDetails();\r\n    if (authtoken && this.isJsonString(userDetails)) {\r\n      return {\r\n        [this.UserDetailsKey]: JSON.parse(userDetails),\r\n        [this.TokenKey]: JSON.parse(authtoken)\r\n      }\r\n    }\r\n    return {};\r\n  }\r\n\r\n  setAuth(token: string, user: any, rememberMe: boolean) {\r\n    if (rememberMe) {\r\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    } else {\r\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    }\r\n    this.userSubject.next({\r\n      [this.UserDetailsKey]: user,\r\n      [this.TokenKey]: token\r\n    });\r\n  }\r\n\r\n  getAuthToken() {\r\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  getUserDetails() {\r\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\r\n  }\r\n\r\n  getUserEmail(): string | null {\r\n    const userData = sessionStorage.getItem('userInfo');\r\n\r\n    if (userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        return parsedUser.email || null;\r\n      } catch (error) {\r\n        return null;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  removeAuthToken() {\r\n    localStorage.removeItem(this.TokenKey);\r\n    sessionStorage.removeItem(this.TokenKey);\r\n    localStorage.removeItem(this.UserDetailsKey);\r\n    sessionStorage.removeItem(this.UserDetailsKey);\r\n  }\r\n\r\n  isJsonString(str: any) {\r\n    try {\r\n      JSON.parse(str);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async getUserPermissions() {\r\n    const userDetails = this.userDetail;\r\n    return await lastValueFrom(this.getUserPermissionsDB(userDetails));\r\n  }\r\n\r\n  getUserPermissionsDB(userDetails: any) {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`)\r\n      .pipe(\r\n        map((res) => {\r\n          if (res?.data?.length) {\r\n            const data = (res?.data || []);\r\n            this.permissions.next(data);\r\n            return data;\r\n          }\r\n          return [];\r\n        })\r\n      )\r\n      .pipe(\r\n        catchError((error) => {\r\n          this.permissions.next([]);\r\n          return error;\r\n        })\r\n      )\r\n  }\r\n\r\n  get getPermissions(): any[] {\r\n    return this.permissions?.value || [];\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAAsBA,WAAW,EAAEC,gBAAgB,QAAoB,iCAAiC;AACxG,SACEC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,GAAG,EAEHC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;;;;AAMb,OAAM,MAAOC,WAAW;EAUtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAXT,KAAAC,WAAW,GAAyB,IAAIb,eAAe,CAAM,EAAE,CAAC;IAC/D,KAAAc,WAAW,GAAyB,IAAId,eAAe,CAAM,EAAE,CAAC;IAChE,KAAAe,cAAc,GAAG,IAAIC,gBAAgB,CAAC,SAAS,CAAC;IAEhD,KAAAC,eAAe,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,UAAU;IACrB,KAAAC,cAAc,GAAG,UAAU;IAOhC,MAAMC,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,IAAItB,eAAe,CAAMuB,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAGL,IAAI,GAAG,EAAE,CAAC;IACjF,IAAI,CAACM,sBAAsB,EAAE;EAC/B;EAEAC,cAAcA,CAAA;IACZ,MAAMP,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAID,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,UAAU,EAAE;MACjD,OAAO,IAAI,CAACC,cAAc,CAACT,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACS,UAAU,CAAC,CAACE,IAAI,CACnEvB,GAAG,CAAEwB,OAAY,IAAI;QACnB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACC,UAAU,CAAC;YACdC,WAAW,EAAE;cACXC,OAAO,EAAEH,OAAO,CAACG,OAAO;cACxBC,KAAK,EAAEJ,OAAO,CAACI,KAAK;cACpBC,SAAS,EAAEL,OAAO,CAACK,SAAS;cAC5BC,QAAQ,EAAEN,OAAO,CAACM,QAAQ;cAC1BC,QAAQ,EAAEP,OAAO,CAACO;;WAErB,CAAC;QACJ;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL,OAAOjC,EAAE,CAAC,IAAI,CAAC;IACjB;EACF;EAEAqB,sBAAsBA,CAAA;IACpB,MAAMa,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACd,MAAM,EAAEe,CAAC,EAAE,EAAE;MACtC,MAAMC,OAAO,GAAGF,MAAM,CAACC,CAAC,CAAC;MACzBtC,SAAS,CAACwC,QAAQ,EAAED,OAAO,CAAC,CAACE,SAAS,CAAEC,IAAI,IAAI;QAC9C,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAAC9B,cAAc,CAAC+B,WAAW,CAAC;UAC9BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAAChC,cAAc,CAACiC,SAAS,GAAIC,KAAK,IAAI;MACxC,IAAIA,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,eAAe,EAAE;QACxC,IAAI,CAACnC,MAAM,CAACsC,GAAG,CAAC,MAAK;UACnB,IAAI,CAACL,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MACA,IAAII,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,QAAQ,EAAE;QACjC,IAAI,CAAC9B,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACkC,QAAQ,EAAE;MACjB;IACF,CAAC;IACD,IAAI,CAACN,kBAAkB,EAAE;IACzB,IAAI,CAAC9B,cAAc,CAAC+B,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAF,kBAAkBA,CAAA;IAChBO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAACD,KAAK,GAAGE,UAAU,CAAC,MAAK;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAErD,WAAW,CAAC0D,eAAe,CAAC;EACjC;EAEAC,KAAKA,CAACnB,QAAgB,EAAEoB,QAAgB,EAAEC,UAAmB;IAC3D,OAAO,IAAI,CAACjD,IAAI,CACbkD,IAAI,CAAM7D,gBAAgB,CAAC8D,MAAM,EAAE;MAClCC,UAAU,EAAE,CAACxB,QAAQ,IAAI,EAAE,EAAEyB,WAAW,EAAE;MAC1CL;KACD,CAAC,CACD5B,IAAI,CACHvB,GAAG,CAAEyD,GAAG,IAAI;MACV,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,OAAO,CAACD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC5C,IAAI,EAAEuC,UAAU,CAAC;MAC7C;MACA,OAAOK,GAAG;IACZ,CAAC,CAAC,EACF1D,SAAS,CAAE0D,GAAG,IAAI;MAChB,IAAIA,GAAG,EAAE5C,IAAI,EAAE;QACb,OAAO,IAAI,CAACS,cAAc,CAACmC,GAAG,CAAC5C,IAAI,CAACQ,UAAU,CAAC,CAACE,IAAI,CAClD1B,GAAG,CAAEwC,IAAS,IAAI;UAChB,IAAIA,IAAI,EAAEuB,IAAI,EAAE;YACdH,GAAG,CAAC5C,IAAI,CAAC+C,IAAI,GAAGvB,IAAI,CAACuB,IAAI;YACzBH,GAAG,CAAC5C,IAAI,CAACgD,QAAQ,GAAGxB,IAAI,CAACuB,IAAI,CAACC,QAAQ;UACxC;UACA,IAAI,CAACpC,UAAU,CAACgC,GAAG,CAAC5C,IAAI,CAAC;UACzB,OAAO4C,GAAG;QACZ,CAAC,CAAC,CACH;MACH;MACA,OAAO3D,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEAwB,cAAcA,CAACwC,MAAc;IAC3B,OAAO,IAAI,CAAC3D,IAAI,CAAC4D,GAAG,CAAC,GAAGvE,gBAAgB,CAACwE,YAAY,IAAIF,MAAM,KAAK,CAAC;EACvE;EAEAG,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,IAAI,CAACnD,WAAW,CAACoD,KAAK;IAClC,OAAOD,GAAG,GAAGA,GAAG,CAAC,IAAI,CAACvD,QAAQ,CAAC,GAAG,IAAI;EACxC;EAEA,IAAIyD,eAAeA,CAAA;IACjB,MAAMvD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACoD,KAAK;IACnC,IAAItD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEiD,QAAQ,EAAEQ,iBAAiB,EAAEnD,MAAM,EAAE;MAC1E,OAAOL,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACiD,QAAQ,CAACQ,iBAAiB,CAAC,CAAC,CAAC;IAChE;IACA,OAAO,EAAE;EACX;EAEA,IAAIC,UAAUA,CAAA;IACZ,MAAMzD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACoD,KAAK;IACnC,OAAOtD,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG,IAAI;EAChD;EAEA,IAAImC,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAAChC,WAAW,CAACoD,KAAK;EACjC;EAEA1C,UAAUA,CAACZ,IAAS;IAClB,MAAM0D,IAAI,GAAQ,IAAI,CAACzD,OAAO,EAAE;IAChC,IAAID,IAAI,EAAEa,WAAW,EAAE;MACrB6C,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,GAAG;QAC1B,GAAG2D,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC;QAC5B,GAAGC,IAAI,EAAEa;OACV;IACH;IACA,IAAIb,IAAI,EAAE+C,IAAI,EAAE;MACdW,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,CAACgD,IAAI,GAAG/C,IAAI,EAAE+C,IAAI;IAC7C;IACA,IAAI/C,IAAI,EAAEgD,QAAQ,EAAE;MAClBU,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,CAACiD,QAAQ,GAAGhD,IAAI,EAAEgD,QAAQ;IACrD;IACA,IAAI,CAACH,OAAO,CAACa,IAAI,CAAC,IAAI,CAAC5D,QAAQ,CAAC,EAAE4D,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,EAAE,IAAI,CAAC4D,oBAAoB,EAAE,CAAC;EAC3F;EAEAA,oBAAoBA,CAAA;IAClB,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC;EAC9C;EAEAiC,QAAQA,CAAA;IACN,IAAI,CAAC+B,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACxE,IAAI,CAAC4D,GAAG,CAAC,GAAGvE,gBAAgB,CAACwE,YAAY,IAAI,IAAI,CAACM,UAAU,CAACjD,UAAU,SAAS,CAAC,CAACe,SAAS,CAAC,MAAK;MACpG,IAAI,CAACwC,eAAe,EAAE;MACtB,CAAC,IAAI,CAAClE,eAAe,IACnB,IAAI,CAACF,cAAc,CAAC+B,WAAW,CAAC;QAC9BC,IAAI,EAAE;OACP,CAAC;MACJ,IAAI,CAACzB,WAAW,CAAC8D,IAAI,CAAC,IAAI,CAAC;MAC3BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAnE,OAAOA,CAAA;IACL,MAAMoE,SAAS,GAAQ,IAAI,CAACC,YAAY,EAAE;IAC1C,MAAMzD,WAAW,GAAQ,IAAI,CAAC0D,cAAc,EAAE;IAC9C,IAAIF,SAAS,IAAI,IAAI,CAACG,YAAY,CAAC3D,WAAW,CAAC,EAAE;MAC/C,OAAO;QACL,CAAC,IAAI,CAACd,cAAc,GAAG0E,IAAI,CAACC,KAAK,CAAC7D,WAAW,CAAC;QAC9C,CAAC,IAAI,CAACf,QAAQ,GAAG2E,IAAI,CAACC,KAAK,CAACL,SAAS;OACtC;IACH;IACA,OAAO,EAAE;EACX;EAEAxB,OAAOA,CAAC8B,KAAa,EAAE3E,IAAS,EAAEuC,UAAmB;IACnD,IAAIA,UAAU,EAAE;MACdqB,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC9E,QAAQ,EAAE2E,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC1Df,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC7E,cAAc,EAAE0E,IAAI,CAACI,SAAS,CAAC7E,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL8E,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC9E,QAAQ,EAAE2E,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC5DG,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC7E,cAAc,EAAE0E,IAAI,CAACI,SAAS,CAAC7E,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,CAACE,WAAW,CAAC8D,IAAI,CAAC;MACpB,CAAC,IAAI,CAACjE,cAAc,GAAGC,IAAI;MAC3B,CAAC,IAAI,CAACF,QAAQ,GAAG6E;KAClB,CAAC;EACJ;EAEAL,YAAYA,CAAA;IACV,OAAOV,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC,IAAIgF,cAAc,CAACjB,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC;EACrF;EAEAyE,cAAcA,CAAA;IACZ,OAAOX,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9D,cAAc,CAAC,IAAI+E,cAAc,CAACjB,OAAO,CAAC,IAAI,CAAC9D,cAAc,CAAC;EACjG;EAEAgF,YAAYA,CAAA;IACV,MAAMC,QAAQ,GAAGF,cAAc,CAACjB,OAAO,CAAC,UAAU,CAAC;IAEnD,IAAImB,QAAQ,EAAE;MACZ,IAAI;QACF,MAAMC,UAAU,GAAGR,IAAI,CAACC,KAAK,CAACM,QAAQ,CAAC;QACvC,OAAOC,UAAU,CAAClE,KAAK,IAAI,IAAI;MACjC,CAAC,CAAC,OAAOmE,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAEAnB,eAAeA,CAAA;IACbH,YAAY,CAACuB,UAAU,CAAC,IAAI,CAACrF,QAAQ,CAAC;IACtCgF,cAAc,CAACK,UAAU,CAAC,IAAI,CAACrF,QAAQ,CAAC;IACxC8D,YAAY,CAACuB,UAAU,CAAC,IAAI,CAACpF,cAAc,CAAC;IAC5C+E,cAAc,CAACK,UAAU,CAAC,IAAI,CAACpF,cAAc,CAAC;EAChD;EAEAyE,YAAYA,CAACY,GAAQ;IACnB,IAAI;MACFX,IAAI,CAACC,KAAK,CAACU,GAAG,CAAC;IACjB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEMC,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAM3E,WAAW,GAAG0E,KAAI,CAAC9B,UAAU;MACnC,aAAa1E,aAAa,CAACwG,KAAI,CAACE,oBAAoB,CAAC5E,WAAW,CAAC,CAAC;IAAC;EACrE;EAEA4E,oBAAoBA,CAAC5E,WAAgB;IACnC,OAAO,IAAI,CAACvB,IAAI,CACb4D,GAAG,CAAM,GAAGvE,gBAAgB,CAACwE,YAAY,IAAItC,WAAW,CAACL,UAAU,qBAAqB,CAAC,CACzFE,IAAI,CACH1B,GAAG,CAAE4D,GAAG,IAAI;MACV,IAAIA,GAAG,EAAEpB,IAAI,EAAEnB,MAAM,EAAE;QACrB,MAAMmB,IAAI,GAAIoB,GAAG,EAAEpB,IAAI,IAAI,EAAG;QAC9B,IAAI,CAAC/B,WAAW,CAACuE,IAAI,CAACxC,IAAI,CAAC;QAC3B,OAAOA,IAAI;MACb;MACA,OAAO,EAAE;IACX,CAAC,CAAC,CACH,CACAd,IAAI,CACH7B,UAAU,CAAEqG,KAAK,IAAI;MACnB,IAAI,CAACzF,WAAW,CAACuE,IAAI,CAAC,EAAE,CAAC;MACzB,OAAOkB,KAAK;IACd,CAAC,CAAC,CACH;EACL;EAEA,IAAIQ,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACjG,WAAW,EAAE6D,KAAK,IAAI,EAAE;EACtC;;;uBA5QWlE,WAAW,EAAAuG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAX7G,WAAW;MAAA8G,OAAA,EAAX9G,WAAW,CAAA+G,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}