{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProspectsComponent } from './prospects.component';\nimport { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';\nimport { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';\nimport { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';\nimport { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';\nimport { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';\nimport { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';\nimport { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';\nimport { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';\nimport { AddProspectComponent } from './add-prospect/add-prospect.component';\nimport { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProspectsComponent\n}, {\n  path: 'create',\n  component: AddProspectComponent\n}, {\n  path: ':id',\n  component: ProspectsDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ProspectsOverviewComponent\n  }, {\n    path: 'contacts',\n    component: ProspectsContactsComponent\n  }, {\n    path: 'sales-team',\n    component: ProspectsSalesTeamComponent\n  }, {\n    path: 'ai-insights',\n    component: ProspectsAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: ProspectsOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: ProspectsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ProspectsNotesComponent\n  }, {\n    path: 'activities',\n    component: ProspectActivitiesComponent\n  }, {\n    path: 'activities/detail/:id',\n    component: ActivitiesItemDetailComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let ProspectsRoutingModule = /*#__PURE__*/(() => {\n  class ProspectsRoutingModule {\n    static {\n      this.ɵfac = function ProspectsRoutingModule_Factory(t) {\n        return new (t || ProspectsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ProspectsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ProspectsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}