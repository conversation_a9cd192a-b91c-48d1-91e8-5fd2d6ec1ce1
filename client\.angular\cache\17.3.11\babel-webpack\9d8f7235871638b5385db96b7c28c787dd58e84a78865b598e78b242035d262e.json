{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ForgotPasswordService = /*#__PURE__*/(() => {\n  class ForgotPasswordService {\n    constructor(http) {\n      this.http = http;\n    }\n    forgotPassword(data) {\n      return this.http.post(CMS_APIContstant.RESET_PASSWORD_REQUEST, data);\n    }\n    static {\n      this.ɵfac = function ForgotPasswordService_Factory(t) {\n        return new (t || ForgotPasswordService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ForgotPasswordService,\n        factory: ForgotPasswordService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ForgotPasswordService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}