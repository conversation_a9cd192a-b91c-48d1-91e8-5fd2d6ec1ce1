{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-checkbox p-component\": true,\n  \"p-checkbox-checked\": a0,\n  \"p-checkbox-disabled\": a1,\n  \"p-checkbox-focused\": a2\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"p-highlight\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst _c3 = (a0, a1, a2) => ({\n  \"p-checkbox-label\": true,\n  \"p-checkbox-label-active\": a0,\n  \"p-disabled\": a1,\n  \"p-checkbox-label-focus\": a2\n});\nfunction Checkbox_ng_container_5_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.checkboxIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Checkbox_ng_container_5_ng_container_1_CheckIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Checkbox_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Checkbox_ng_container_5_ng_container_1_span_1_Template, 1, 2, \"span\", 8)(2, Checkbox_ng_container_5_ng_container_1_CheckIcon_2_Template, 1, 2, \"CheckIcon\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checkboxIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.checkboxIcon);\n  }\n}\nfunction Checkbox_ng_container_5_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Checkbox_ng_container_5_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Checkbox_ng_container_5_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Checkbox_ng_container_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Checkbox_ng_container_5_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.checkboxIconTemplate);\n  }\n}\nfunction Checkbox_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Checkbox_ng_container_5_ng_container_1_Template, 3, 2, \"ng-container\", 5)(2, Checkbox_ng_container_5_span_2_Template, 2, 2, \"span\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.checkboxIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checkboxIconTemplate);\n  }\n}\nfunction Checkbox_label_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 14);\n    i0.ɵɵlistener(\"click\", function Checkbox_label_6_Template_label_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.labelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c3, ctx_r1.checked(), ctx_r1.disabled, ctx_r1.focused));\n    i0.ɵɵattribute(\"for\", ctx_r1.inputId)(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.label, \"\");\n  }\n}\nconst CHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Checkbox),\n  multi: true\n};\n/**\n * Checkbox is an extension to standard checkbox element with theming.\n * @group Components\n */\nlet Checkbox = /*#__PURE__*/(() => {\n  class Checkbox {\n    cd;\n    /**\n     * Value of the checkbox.\n     * @group Props\n     */\n    value;\n    /**\n     * Name of the checkbox group.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Allows to select a boolean value instead of multiple values.\n     * @group Props\n     */\n    binary;\n    /**\n     * Label of the checkbox.\n     * @group Props\n     */\n    label;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the label.\n     * @group Props\n     */\n    labelStyleClass;\n    /**\n     * Form control value.\n     * @group Props\n     */\n    formControl;\n    /**\n     * Icon class of the checkbox icon.\n     * @group Props\n     */\n    checkboxIcon;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * When present, it specifies that checkbox must be checked before submitting the form.\n     * @group Props\n     */\n    required;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    trueValue = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    falseValue = false;\n    /**\n     * Callback to invoke on value change.\n     * @param {CheckboxChangeEvent} event - Custom value change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke when the receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    inputViewChild;\n    templates;\n    checkboxIconTemplate;\n    model;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    focused = false;\n    constructor(cd) {\n      this.cd = cd;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'icon':\n            this.checkboxIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onClick(event) {\n      if (!this.disabled && !this.readonly) {\n        this.inputViewChild.nativeElement.focus();\n        let newModelValue;\n        if (!this.binary) {\n          if (this.checked()) newModelValue = this.model.filter(val => !ObjectUtils.equals(val, this.value));else newModelValue = this.model ? [...this.model, this.value] : [this.value];\n          this.onModelChange(newModelValue);\n          this.model = newModelValue;\n          if (this.formControl) {\n            this.formControl.setValue(newModelValue);\n          }\n        } else {\n          newModelValue = this.checked() ? this.falseValue : this.trueValue;\n          this.model = newModelValue;\n          this.onModelChange(newModelValue);\n        }\n        this.onChange.emit({\n          checked: newModelValue,\n          originalEvent: event\n        });\n      }\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      this.onModelTouched();\n      this.onBlur.emit(event);\n    }\n    writeValue(model) {\n      this.model = model;\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    checked() {\n      return this.binary ? this.model === this.trueValue : ObjectUtils.contains(this.value, this.model);\n    }\n    static ɵfac = function Checkbox_Factory(t) {\n      return new (t || Checkbox)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Checkbox,\n      selectors: [[\"p-checkbox\"]],\n      contentQueries: function Checkbox_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Checkbox_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        value: \"value\",\n        name: \"name\",\n        disabled: \"disabled\",\n        binary: \"binary\",\n        label: \"label\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        ariaLabel: \"ariaLabel\",\n        tabindex: \"tabindex\",\n        inputId: \"inputId\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        labelStyleClass: \"labelStyleClass\",\n        formControl: \"formControl\",\n        checkboxIcon: \"checkboxIcon\",\n        readonly: \"readonly\",\n        required: \"required\",\n        trueValue: \"trueValue\",\n        falseValue: \"falseValue\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      features: [i0.ɵɵProvidersFeature([CHECKBOX_VALUE_ACCESSOR])],\n      decls: 7,\n      vars: 35,\n      consts: [[\"input\", \"\"], [3, \"click\", \"ngStyle\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"focus\", \"blur\", \"value\", \"checked\", \"disabled\", \"readonly\"], [1, \"p-checkbox-box\", 3, \"ngClass\"], [4, \"ngIf\"], [3, \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [1, \"p-checkbox-icon\", 3, \"ngClass\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\"], [3, \"click\", \"ngClass\"]],\n      template: function Checkbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function Checkbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClick($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3, 0);\n          i0.ɵɵlistener(\"focus\", function Checkbox_Template_input_focus_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"blur\", function Checkbox_Template_input_blur_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, Checkbox_ng_container_5_Template, 3, 2, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, Checkbox_label_6_Template, 2, 10, \"label\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction3(27, _c1, ctx.checked(), ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"data-pc-name\", \"checkbox\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"data-pc-section\", \"hiddenInputWrapper\")(\"data-p-hidden-accessible\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", ctx.value)(\"checked\", ctx.checked())(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"required\", ctx.required)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"aria-checked\", ctx.checked())(\"data-pc-section\", \"hiddenInput\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(31, _c2, ctx.checked(), ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"data-p-highlight\", ctx.checked())(\"data-p-disabled\", ctx.disabled)(\"data-p-focused\", ctx.focused)(\"data-pc-section\", \"input\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.checked());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.label);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, CheckIcon],\n      styles: [\"@layer primeng{.p-checkbox{display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;vertical-align:bottom;position:relative}.p-checkbox-disabled{cursor:default!important;pointer-events:none}.p-checkbox-box{display:flex;justify-content:center;align-items:center}p-checkbox{display:inline-flex;vertical-align:bottom;align-items:center}.p-checkbox-label{line-height:1}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Checkbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CheckboxModule = /*#__PURE__*/(() => {\n  class CheckboxModule {\n    static ɵfac = function CheckboxModule_Factory(t) {\n      return new (t || CheckboxModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CheckboxModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, CheckIcon, SharedModule]\n    });\n  }\n  return CheckboxModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CHECKBOX_VALUE_ACCESSOR, Checkbox, CheckboxModule };\n//# sourceMappingURL=primeng-checkbox.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}