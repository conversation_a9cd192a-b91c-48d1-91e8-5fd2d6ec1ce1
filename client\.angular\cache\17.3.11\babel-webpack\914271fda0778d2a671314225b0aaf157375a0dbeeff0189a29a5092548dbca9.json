{"ast": null, "code": "'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n  try {\n    $defineProperty({}, 'a', {\n      value: 1\n    });\n  } catch (e) {\n    // IE 8 has a broken defineProperty\n    $defineProperty = false;\n  }\n}\nmodule.exports = $defineProperty;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}