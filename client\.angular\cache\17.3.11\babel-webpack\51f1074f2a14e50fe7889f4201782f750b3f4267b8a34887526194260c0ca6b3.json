{"ast": null, "code": "import { HttpEventType, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, takeUntil, of, Subject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ImportService = /*#__PURE__*/(() => {\n  class ImportService {\n    constructor(http) {\n      this.http = http;\n      this.uploadSubject = new BehaviorSubject(null);\n      this.flexupload = this.uploadSubject.asObservable();\n      this.unsubscribe$ = new Subject();\n    }\n    save(apiurl, data) {\n      return this.http.post(apiurl, data, {\n        reportProgress: true,\n        observe: 'events'\n      }).pipe(map(event => {\n        switch (event.type) {\n          case HttpEventType.UploadProgress:\n            const progress = Math.round(event.loaded / (event.total || 1) * 100);\n            return {\n              status: 'PROGRESS',\n              progress\n            };\n          case HttpEventType.Response:\n            return {\n              status: 'COMPLETED',\n              body: event.body\n            };\n          default:\n            return {\n              status: 'UNKNOWN',\n              event\n            };\n        }\n      }));\n    }\n    getProgessStatus(apiurl, table_name) {\n      const params = new HttpParams().set('filters[table_name]', table_name).set('sort[0]', 'createdAt:desc').set('pagination[limit]', '1');\n      return this.http.get(apiurl, {\n        params\n      });\n    }\n    getFilelog(apiurl, table_name) {\n      const params = new HttpParams().set('sort[0]', 'createdAt:desc').set('filters[table_name]', table_name);\n      return this.http.get(apiurl, {\n        params\n      });\n    }\n    delete(deleteurl) {\n      return this.http.delete(deleteurl);\n    }\n    export(id, exporturl, tabname) {\n      return new Promise((resolve, reject) => {\n        this.http.get(exporturl, {\n          responseType: 'blob'\n        }).pipe(takeUntil(this.unsubscribe$), catchError(error => {\n          reject('Error fetching data: ' + error); // Reject the Promise on error\n          return of(null); // Handle error and return default value\n        })).subscribe(blob => {\n          if (blob) {\n            const file = new Blob([blob], {\n              type: 'application/zip'\n            });\n            const fileURL = URL.createObjectURL(file);\n            const a = document.createElement('a');\n            a.href = fileURL;\n            a.download = `${tabname}_${id}.zip`;\n            a.click();\n            URL.revokeObjectURL(fileURL);\n            resolve(blob);\n          }\n        });\n      });\n    }\n    static {\n      this.ɵfac = function ImportService_Factory(t) {\n        return new (t || ImportService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ImportService,\n        factory: ImportService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ImportService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}