{"ast": null, "code": "import stateList from './assets/state.json';\nimport { findEntryByCode, findStateByCodeAndCountryCode, compare } from './utils';\n// Get a list of all states.\nexport function getAllStates() {\n  return stateList;\n}\n// Get a list of states belonging to a specific country.\nexport function getStatesOfCountry(countryCode = '') {\n  if (!countryCode) return [];\n  // get data from file or cache\n  const states = stateList.filter(value => {\n    return value.countryCode === countryCode;\n  });\n  return states.sort(compare);\n}\nexport function getStateByCodeAndCountry(stateCode, countryCode) {\n  if (!stateCode) return undefined;\n  if (!countryCode) return undefined;\n  return findStateByCodeAndCountryCode(stateList, stateCode, countryCode);\n}\n// to be deprecate\nexport function getStateByCode(isoCode) {\n  // eslint-disable-next-line no-console\n  console.warn(`WARNING! 'getStateByCode' has been deprecated, please use the new 'getStateByCodeAndCountry' function instead!`);\n  if (!isoCode) return undefined;\n  return findEntryByCode(stateList, isoCode);\n}\nfunction sortByIsoCode(countries) {\n  return countries.sort((a, b) => {\n    return compare(a, b, entity => {\n      return `${entity.countryCode}-${entity.isoCode}`;\n    });\n  });\n}\nexport default {\n  getAllStates,\n  getStatesOfCountry,\n  getStateByCodeAndCountry,\n  getStateByCode,\n  sortByIsoCode\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}