import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import { ActivitiesService } from 'src/app/store/activities/activities.service';
import { MessageService } from 'primeng/api';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  startWith,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';

interface Column {
  field: string;
  header: string;
}

interface DropdownOption {
  label: string;
  value: string;
}

@Component({
  selector: 'app-activities-call-followup-form',
  templateUrl: './activities-call-followup-form.component.html',
  styleUrl: './activities-call-followup-form.component.scss',
})
export class ActivitiesCallFollowupFormComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @Input() visible: boolean = false;
  @Output() onClose = new EventEmitter<void>();
  public activity_id: string = '';
  public submitted = false;
  public saving = false;
  public position: string = 'right';
  public addDialogVisible: boolean = false;
  public existingDialogVisible: boolean = false;
  private defaultOptions: any = [];
  public accounts$?: Observable<any[]>;
  public accountLoading = false;
  public accountInput$ = new Subject<string>();
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  public existingcontacts$?: Observable<any[]>;
  public existingcontactLoading = false;
  public existingcontactInput$ = new Subject<string>();
  private owner_id: string | null = null;

  public FollowUpForm: FormGroup = this.formBuilder.group({
    document_type: ['', [Validators.required]],
    subject: ['', [Validators.required]],
    main_account_party_id: ['', [Validators.required]],
    main_contact_party_id: ['', [Validators.required]],
    phone_call_category: ['', [Validators.required]],
    disposition_code: [''],
    start_date: [''],
    end_date: [''],
    initiator_code: ['', [Validators.required]],
    activity_status: ['', [Validators.required]],
    notes: ['', [Validators.required]],
    contactexisting: [''],
    involved_parties: this.formBuilder.array([this.createContactFormGroup()]),
  });

  public dropdowns: Record<string, any[]> = {
    activityDocumentType: [],
    activityCategory: [],
    activityStatus: [],
    activitydisposition: [],
    activityInitiatorCode: [],
  };

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private activitiesservice: ActivitiesService,
    private messageservice: MessageService
  ) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [{ field: 'email', header: 'Email Address' }];

  sortField: string = '';
  sortOrder: number = 1;

  ngOnInit() {
    this.activity_id = this.route.parent?.snapshot.paramMap.get('id') || '';
    this.FollowUpForm.get('main_account_party_id')
      ?.valueChanges.pipe(
        takeUntil(this.unsubscribe$),
        tap((selectedBpId) => {
          if (selectedBpId) {
            this.loadAccountByContacts(selectedBpId);
          } else {
            this.contacts$ = of(this.defaultOptions);
          }
        }),
        catchError((err) => {
          console.error('Account selection error:', err);
          this.contacts$ = of(this.defaultOptions);
          return of();
        })
      )
      .subscribe();
    this.loadExistingContacts();
    this.getOwner().subscribe({
      next: (response: string | null) => {
        this.owner_id = response;
      },
      error: (err) => {
        console.error('Error fetching bp_id:', err);
      },
    });
    this.loadAccounts();

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }
  
  TableColumnReorder(event: any) {
    const draggedCol = this.cols[event.dragIndex];
    this.cols.splice(event.dragIndex, 1);
    this.cols.splice(event.dropIndex, 0, draggedCol);
  }

  private getOwner(): Observable<string | null> {
    return this.activitiesservice.getEmailwisePartner();
  }

  loadActivityDropDown(target: string, type: string): void {
    this.activitiesservice
      .getActivityDropdownOptions(type)
      .subscribe((res: any) => {
        const options: DropdownOption[] =
          res?.data?.map(
            (attr: { description: string; code: string }): DropdownOption => ({
              label: attr.description,
              value: attr.code,
            })
          ) ?? [];

        // Assign options to dropdown object
        this.dropdowns[target] = options;

        // Set 'Open' as default selected for activityStatus only
        if (target === 'activityStatus') {
          const openOption = options.find(
            (opt) => opt.label.toLowerCase() === 'open'
          );

          if (openOption) {
            const control = this.FollowUpForm?.get('activity_status');
            if (control) {
              control.setValue(openOption.value);
            }
          }
        }
      });
  }

  private loadAccounts(): void {
    this.accounts$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.accountInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.accountLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'FLCU01',
            'filters[roles][bp_role][$in][1]': 'FLCU00',
            'filters[roles][bp_role][$in][2]': 'PRO001',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Account fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.accountLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  private loadExistingContacts() {
    this.existingcontacts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.existingcontactInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.existingcontactLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq]`]: 'BUP001',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((data: any) => {
              return data || []; // Make sure to return correct data structure
            }),
            tap(() => (this.existingcontactLoading = false)),
            catchError((error) => {
              this.existingcontactLoading = false;
              return of([]);
            })
          );
        })
      )
    );
  }

  private loadAccountByContacts(bpId: string): void {
    this.contacts$ = this.contactInput$.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.contactLoading = true)),
      switchMap((term: string) => {
        const params: any = {
          'filters[bp_company_id][$eq]': bpId,
          'populate[business_partner_person][populate][addresses][populate]':
            '*',
        };

        if (term) {
          params[
            'filters[$or][0][business_partner_person][bp_id][$containsi]'
          ] = term;
          params[
            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'
          ] = term;
        }

        return this.activitiesservice.getPartnersContact(params).pipe(
          map((response: any[]) => response || []),
          tap((contacts: any[]) => {
            this.contactLoading = false;
          }),
          catchError((error) => {
            console.error('Contact loading failed:', error);
            this.contactLoading = false;
            return of([]);
          })
        );
      })
    );
  }

  selectExistingContact() {
    this.addExistingContact(this.FollowUpForm.value);
    this.existingDialogVisible = false; // Close dialog
  }

  addExistingContact(existing: any) {
    const contactForm = this.formBuilder.group({
      bp_full_name: [existing?.contactexisting?.bp_full_name || ''],
      email_address: [existing?.contactexisting?.email || ''],
      role_code: 'BUP001',
      party_id: existing?.contactexisting?.bp_id || '',
    });

    const firstGroup = this.involved_parties.at(0) as FormGroup;
    const bpName = firstGroup?.get('bp_full_name')?.value;

    if (!bpName && this.involved_parties.length === 1) {
      // Replace the default empty group
      this.involved_parties.setControl(0, contactForm);
    } else {
      // Otherwise, add a new contact
      this.involved_parties.push(contactForm);
    }

    this.existingDialogVisible = false; // Close dialog
  }

  deleteContact(index: number) {
    if (this.involved_parties.length > 1) {
      this.involved_parties.removeAt(index);
    }
  }

  createContactFormGroup(): FormGroup {
    return this.formBuilder.group({
      bp_full_name: [''],
      email_address: [''],
    });
  }

  async onSubmit() {
    this.submitted = true;

    if (this.FollowUpForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.FollowUpForm.value };

    const data = {
      document_type: value?.document_type,
      subject: value?.subject,
      main_account_party_id: value?.main_account_party_id,
      main_contact_party_id: value?.main_contact_party_id,
      phone_call_category: value?.phone_call_category,
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      disposition_code: value?.disposition_code,
      initiator_code: value?.initiator_code,
      owner_party_id: this.owner_id,
      activity_status: value?.activity_status,
      note: value?.notes,
      involved_parties: Array.isArray(value.involved_parties)
        ? [
            ...value.involved_parties,
            ...(value?.main_account_party_id
              ? [{ role_code: 'FLCU01', party_id: value.main_account_party_id }]
              : []),
            ...(Array.isArray(value.main_contact_party_id)
              ? value.main_contact_party_id.map((id: any) => ({
                  role_code: 'BUP001',
                  party_id: id,
                }))
              : []),
            ...(this.owner_id
              ? [{ role_code: 'BUP003', party_id: this.owner_id }]
              : []),
          ]
        : [],
      type_code: '0002',
      activity_id: this.activity_id,
      btd_role_code: '2',
    };

    this.activitiesservice
      .createFollowup(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.addDialogVisible = false;
          this.saving = false;
          this.visible = false;
          this.FollowUpForm.reset();
          this.messageservice.add({
            severity: 'success',
            detail: 'Follow Up Added Successfully!',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  get f(): any {
    return this.FollowUpForm.controls;
  }

  get involved_parties(): any {
    return this.FollowUpForm.get('involved_parties') as FormArray;
  }

  showExistingDialog(position: string) {
    this.position = position;
    this.existingDialogVisible = true;
  }

  showDialog(position: string) {
    this.position = position;
    this.visible = true;
    this.submitted = false;
    this.FollowUpForm.reset();
    setTimeout(() => {
      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');
      this.loadActivityDropDown(
        'activityDocumentType',
        'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'
      );
      this.loadActivityDropDown(
        'activityCategory',
        'CRM_ACTIVITY_PHONE_CALL_CATEGORY'
      );
      this.loadActivityDropDown(
        'activitydisposition',
        'CRM_ACTIVITY_DISPOSITION_CODE'
      );
      this.loadActivityDropDown(
        'activityInitiatorCode',
        'CRM_ACTIVITY_INITIATOR_CODE'
      );
    }, 50);
  }

  hideDialog(): void {
    this.onClose.emit();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
