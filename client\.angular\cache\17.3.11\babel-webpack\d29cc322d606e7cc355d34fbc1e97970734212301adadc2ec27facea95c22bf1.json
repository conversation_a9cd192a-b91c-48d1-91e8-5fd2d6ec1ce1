{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../prospects.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/tooltip\";\nimport * as i8 from \"primeng/editor\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ProspectsNotesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsNotesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction ProspectsNotesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsNotesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction ProspectsNotesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ProspectsNotesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 23)(5, ProspectsNotesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction ProspectsNotesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"note\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, ProspectsNotesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 23)(5, ProspectsNotesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProspectsNotesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 25);\n    i0.ɵɵelementStart(7, \"th\")(8, \"div\", 26);\n    i0.ɵɵtext(9, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r6 == null ? null : notes_r6.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r6 == null ? null : notes_r6.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ProspectsNotesComponent_ng_template_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 33);\n    i0.ɵɵtemplate(3, ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 34)(4, ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_4_Template, 3, 4, \"ng-container\", 34);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n  }\n}\nfunction ProspectsNotesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 30);\n    i0.ɵɵtemplate(2, ProspectsNotesComponent_ng_template_10_ng_container_2_Template, 5, 3, \"ng-container\", 25);\n    i0.ɵɵelementStart(3, \"td\")(4, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_ng_template_10_Template_button_click_4_listener() {\n      const notes_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNote(notes_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_ng_template_10_Template_button_click_5_listener($event) {\n      const notes_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(notes_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r6 == null ? null : notes_r6.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ProspectsNotesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsNotesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsNotesComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProspectsNotesComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProspectsNotesComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ProspectsNotesComponent_div_19_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport class ProspectsNotesComponent {\n  constructor(formBuilder, prospectsservice, messageservice, confirmationservice) {\n    this.formBuilder = formBuilder;\n    this.prospectsservice = prospectsservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.notedetails = [];\n    this.visible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.saving = false;\n    this.bp_id = '';\n    this.editid = '';\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'createdAt',\n      header: 'Last Updated On'\n    }, {\n      field: 'updatedAt',\n      header: 'Updated By'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.notedetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.prospectsservice.prospect.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.notedetails = response?.notes;\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  editNote(note) {\n    this.visible = true;\n    this.editid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.visible = true;\n      if (_this.NoteForm.invalid) {\n        _this.visible = true;\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        bp_id: _this.bp_id,\n        note: value?.note\n      };\n      if (_this.editid) {\n        _this.prospectsservice.updateNote(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.visible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.prospectsservice.getProspectByID(_this.bp_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: res => {\n            _this.saving = false;\n            _this.visible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.prospectsservice.createNote(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.visible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.prospectsservice.getProspectByID(_this.bp_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: res => {\n            _this.saving = false;\n            _this.visible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.prospectsservice.deleteNote(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.prospectsservice.getProspectByID(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  get f() {\n    return this.NoteForm.controls;\n  }\n  showDialog(position) {\n    this.position = position;\n    this.visible = true;\n    this.submitted = false;\n    this.NoteForm.reset();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ProspectsNotesComponent_Factory(t) {\n      return new (t || ProspectsNotesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProspectsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsNotesComponent,\n      selectors: [[\"app-prospects-notes\"]],\n      decls: 25,\n      vars: 26,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"rounded\", \"styleClass\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"border-round-left-lg\", 2, \"min-width\", \"70rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"font-medium\", \"border-round-left-lg\", \"note-text\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [4, \"ngIf\"]],\n      template: function ProspectsNotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_Template_p_button_click_5_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsNotesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function ProspectsNotesComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, ProspectsNotesComponent_ng_template_9_Template, 10, 3, \"ng-template\", 8)(10, ProspectsNotesComponent_ng_template_10_Template, 6, 2, \"ng-template\", 9)(11, ProspectsNotesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, ProspectsNotesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ProspectsNotesComponent_Template_p_dialog_visibleChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(14, ProspectsNotesComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n          i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"div\", 15);\n          i0.ɵɵelement(18, \"p-editor\", 16);\n          i0.ɵɵtemplate(19, ProspectsNotesComponent_div_19_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_Template_button_click_21_listener() {\n            return ctx.visible = false;\n          });\n          i0.ɵɵtext(22, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function ProspectsNotesComponent_Template_button_click_23_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(24, \"Save\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"font-semibold px-3\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c2, (ctx.submitted || ctx.f[\"note\"].touched) && ctx.f[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.submitted || ctx.f[\"note\"].touched) && ctx.f[\"note\"].errors);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i5.Table, i3.PrimeTemplate, i5.SortableColumn, i5.ReorderableColumn, i1.FormGroupDirective, i1.FormControlName, i6.ButtonDirective, i6.Button, i7.Tooltip, i8.Editor, i9.Dialog, i10.MultiSelect, i4.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL3Byb3NwZWN0cy1kZXRhaWxzL3Byb3NwZWN0cy1ub3Rlcy9wcm9zcGVjdHMtbm90ZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7RUFJSSxxQkFBQTtFQUNBLFdBQUE7QUFDSjs7QUFJUTtFQUNJLGtCQUFBO0FBRFo7QUFHWTtFQUNJLDRCQUFBO0VBQ0EsMkNBQUE7QUFEaEI7QUFHZ0I7RUFDSSxTQUFBO0FBRHBCO0FBS1k7RUFDSSw0QkFBQTtFQUNBLGlCQUFBO0FBSGhCIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gICAgY29sb3I6IHZhcigtLXJlZC01MDApO1xyXG4gICAgcmlnaHQ6IDEwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCB7XHJcbiAgICAubm90ZS1wb3B1cCB7XHJcbiAgICAgICAgLnAtZGlhbG9nIHtcclxuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1MHB4O1xyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXN1cmZhY2UtMTAwKTtcclxuXHJcbiAgICAgICAgICAgICAgICBoNCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS43MTRyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "ProspectsNotesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "ProspectsNotesComponent_ng_template_9_ng_container_6_i_4_Template", "ProspectsNotesComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "ProspectsNotesComponent_ng_template_9_Template_th_click_1_listener", "_r1", "ProspectsNotesComponent_ng_template_9_i_4_Template", "ProspectsNotesComponent_ng_template_9_i_5_Template", "ProspectsNotesComponent_ng_template_9_ng_container_6_Template", "selectedColumns", "ɵɵpipeBind2", "notes_r6", "createdAt", "updatedAt", "ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_3_Template", "ProspectsNotesComponent_ng_template_10_ng_container_2_ng_container_4_Template", "col_r7", "ProspectsNotesComponent_ng_template_10_ng_container_2_Template", "ProspectsNotesComponent_ng_template_10_Template_button_click_4_listener", "_r5", "editNote", "ProspectsNotesComponent_ng_template_10_Template_button_click_5_listener", "$event", "stopPropagation", "confirmRemove", "note", "ɵɵsanitizeHtml", "ProspectsNotesComponent_div_19_div_1_Template", "f", "errors", "ProspectsNotesComponent", "constructor", "formBuilder", "prospectsservice", "messageservice", "confirmationservice", "unsubscribe$", "notedetails", "visible", "position", "submitted", "saving", "bp_id", "editid", "NoteForm", "group", "required", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "prospect", "pipe", "subscribe", "response", "notes", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "documentId", "patchValue", "onSubmit", "_this", "_asyncToGenerator", "invalid", "value", "updateNote", "complete", "reset", "add", "severity", "detail", "getProspectByID", "error", "res", "createNote", "item", "confirm", "message", "icon", "accept", "remove", "deleteNote", "next", "stripHtml", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "controls", "showDialog", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProspectsService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "ProspectsNotesComponent_Template", "rf", "ctx", "ProspectsNotesComponent_Template_p_button_click_5_listener", "ɵɵtwoWayListener", "ProspectsNotesComponent_Template_p_multiSelect_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "ProspectsNotesComponent_Template_p_table_onColReorder_8_listener", "ProspectsNotesComponent_ng_template_9_Template", "ProspectsNotesComponent_ng_template_10_Template", "ProspectsNotesComponent_ng_template_11_Template", "ProspectsNotesComponent_ng_template_12_Template", "ProspectsNotesComponent_Template_p_dialog_visibleChange_13_listener", "ProspectsNotesComponent_ng_template_14_Template", "ProspectsNotesComponent_div_19_Template", "ProspectsNotesComponent_Template_button_click_21_listener", "ProspectsNotesComponent_Template_button_click_23_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1", "ɵɵpureFunction1", "_c2", "touched"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-notes\\prospects-notes.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-notes\\prospects-notes.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ProspectsService } from '../../prospects.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n@Component({\r\n  selector: 'app-prospects-notes',\r\n  templateUrl: './prospects-notes.component.html',\r\n  styleUrl: './prospects-notes.component.scss',\r\n})\r\nexport class ProspectsNotesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public notedetails: any[] = [];\r\n  public visible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public bp_id: string = '';\r\n  public editid: string = '';\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private prospectsservice: ProspectsService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'createdAt', header: 'Last Updated On' },\r\n    { field: 'updatedAt', header: 'Updated By' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.notedetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.prospectsservice.prospect\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.notedetails = response?.notes;\r\n        }\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.visible = true;\r\n    this.editid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      bp_id: this.bp_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.editid) {\r\n      this.prospectsservice\r\n        .updateNote(this.editid, data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.visible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.prospectsservice\r\n              .getProspectByID(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.saving = false;\r\n            this.visible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.prospectsservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.visible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.prospectsservice\r\n              .getProspectByID(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.saving = false;\r\n            this.visible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.prospectsservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.prospectsservice\r\n            .getProspectByID(this.bp_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n\r\n  get f(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.position = position;\r\n    this.visible = true;\r\n    this.submitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                [rounded]=\"true\" class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"notedetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th (click)=\"customSort('note')\" class=\"border-round-left-lg\" style=\"min-width: 70rem;\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Note\r\n                            <i *ngIf=\"sortField === 'note'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'note'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Actions\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes let-columns=\"columns\">\r\n                <tr>\r\n                    <td class=\"font-medium border-round-left-lg note-text\" [innerHTML]=\"notes?.note || '-'\">\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ notes?.createdAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td>\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': (submitted || f['note'].touched) && f['note'].errors }\"></p-editor>\r\n\r\n                <div *ngIf=\"(submitted || f['note'].touched) && f['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"flex justify-content-end gap-2 mt-3\">\r\n            <button pButton type=\"button\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\">Cancel</button>\r\n            <button pButton type=\"submit\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit();\">Save</button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICqBbC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA4D;;;;;IAOxDD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,kFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,iEAAA,gBACkF,IAAAC,iEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAhB7ChB,EADJ,CAAAM,cAAA,SAAI,aACwF;IAApFN,EAAA,CAAAO,UAAA,mBAAAmB,mEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAC5Bf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,aACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,kDAAA,gBACkF,IAAAC,kDAAA,gBAE1B;IAEhE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,6DAAA,2BAAkD;IAY9C9B,EADJ,CAAAM,cAAA,SAAI,cACqC;IACjCN,EAAA,CAAAiB,MAAA,gBACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAM,EACL,EACJ;;;;IAtBWrB,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAG1BzB,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAGRzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAgC,WAAA,OAAAC,QAAA,kBAAAA,QAAA,CAAAC,SAAA,8BACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAgC,WAAA,OAAAC,QAAA,kBAAAA,QAAA,CAAAE,SAAA,8BACJ;;;;;IAVZnC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAMjCL,EAJA,CAAAkB,UAAA,IAAAkB,6EAAA,2BAA0C,IAAAC,6EAAA,2BAIA;;IAKlDrC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAXarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAoC,MAAA,CAAAtB,KAAA,CAAsB;IAEjBhB,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;;;;;;IAZxDF,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAC,SAAA,aACK;IAELD,EAAA,CAAAkB,UAAA,IAAAqB,8DAAA,2BAAkD;IAgB9CvC,EADJ,CAAAM,cAAA,SAAI,iBAE8B;IAA1BN,EAAA,CAAAO,UAAA,mBAAAiC,wEAAA;MAAA,MAAAP,QAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAuC,QAAA,CAAAT,QAAA,CAAe;IAAA,EAAC;IAACjC,EAAA,CAAAqB,YAAA,EAAS;IACvCrB,EAAA,CAAAM,cAAA,iBAC8D;IAA1DN,EAAA,CAAAO,UAAA,mBAAAoC,wEAAAC,MAAA;MAAA,MAAAX,QAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAS+B,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA7C,EAAA,CAAAc,WAAA,CAAEX,MAAA,CAAA2C,aAAA,CAAAb,QAAA,CAAoB;IAAA,EAAE;IAErEjC,EAFsE,CAAAqB,YAAA,EAAS,EACtE,EACJ;;;;;IAxBsDrB,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAE,UAAA,eAAA+B,QAAA,kBAAAA,QAAA,CAAAc,IAAA,UAAA/C,EAAA,CAAAgD,cAAA,CAAgC;IAGzDhD,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA2BhD/B,EADJ,CAAAM,cAAA,SAAI,aACkD;IAAAN,EAAA,CAAAiB,MAAA,sBAAe;IACrEjB,EADqE,CAAAqB,YAAA,EAAK,EACrE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACkD;IAAAN,EAAA,CAAAiB,MAAA,yCAAkC;IACxFjB,EADwF,CAAAqB,YAAA,EAAK,EACxF;;;;;IAQbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,WAAI;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAWDrB,EAAA,CAAAM,cAAA,UAA0C;IAAAN,EAAA,CAAAiB,MAAA,wBAAiB;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAFrErB,EAAA,CAAAM,cAAA,cACmE;IAC/DN,EAAA,CAAAkB,UAAA,IAAA+B,6CAAA,kBAA0C;IAC9CjD,EAAA,CAAAqB,YAAA,EAAM;;;;IADIrB,EAAA,CAAAsB,SAAA,EAAkC;IAAlCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAA+C,CAAA,SAAAC,MAAA,aAAkC;;;AD7F5D,OAAM,MAAOC,uBAAuB;EAclCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAjBrB,KAAAC,YAAY,GAAG,IAAI5D,OAAO,EAAQ;IACnC,KAAA6D,WAAW,GAAU,EAAE;IACvB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,MAAM,GAAW,EAAE;IAEnB,KAAAC,QAAQ,GAAc,IAAI,CAACZ,WAAW,CAACa,KAAK,CAAC;MAClDpB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClD,UAAU,CAACuE,QAAQ,CAAC;KACjC,CAAC;IASM,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEtD,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EACjD;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAVjB;EAYJW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACuD,WAAW,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC7B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAExD,KAAK,CAAC;MAC9C,MAAM4D,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEzD,KAAK,CAAC;MAE9C,IAAI6D,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACxE,SAAS,GAAGyE,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE/D,KAAa;IACvC,IAAI,CAAC+D,IAAI,IAAI,CAAC/D,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACgE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC/D,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACiE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC9B,gBAAgB,CAAC+B,QAAQ,CAC3BC,IAAI,CAACxF,SAAS,CAAC,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACzB,KAAK,GAAGyB,QAAQ,EAAEzB,KAAK;QAC5B,IAAI,CAACL,WAAW,GAAG8B,QAAQ,EAAEC,KAAK;MACpC;IACF,CAAC,CAAC;IAEJ,IAAI,CAACrB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAIvC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACsC,gBAAgB;EAC9B;EAEA,IAAItC,eAAeA,CAAC4D,GAAU;IAC5B,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACsB,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAC5B,gBAAgB,CAAC2B,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAAC7B,gBAAgB,CAAC8B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC7B,gBAAgB,CAAC8B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAvD,QAAQA,CAACK,IAAS;IAChB,IAAI,CAACa,OAAO,GAAG,IAAI;IACnB,IAAI,CAACK,MAAM,GAAGlB,IAAI,EAAEsD,UAAU;IAC9B,IAAI,CAACnC,QAAQ,CAACoC,UAAU,CAACvD,IAAI,CAAC;EAChC;EAEMwD,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC1C,SAAS,GAAG,IAAI;MACrB0C,KAAI,CAAC5C,OAAO,GAAG,IAAI;MAEnB,IAAI4C,KAAI,CAACtC,QAAQ,CAACwC,OAAO,EAAE;QACzBF,KAAI,CAAC5C,OAAO,GAAG,IAAI;QACnB;MACF;MAEA4C,KAAI,CAACzC,MAAM,GAAG,IAAI;MAClB,MAAM4C,KAAK,GAAG;QAAE,GAAGH,KAAI,CAACtC,QAAQ,CAACyC;MAAK,CAAE;MAExC,MAAM5B,IAAI,GAAG;QACXf,KAAK,EAAEwC,KAAI,CAACxC,KAAK;QACjBjB,IAAI,EAAE4D,KAAK,EAAE5D;OACd;MAED,IAAIyD,KAAI,CAACvC,MAAM,EAAE;QACfuC,KAAI,CAACjD,gBAAgB,CAClBqD,UAAU,CAACJ,KAAI,CAACvC,MAAM,EAAEc,IAAI,CAAC,CAC7BQ,IAAI,CAACxF,SAAS,CAACyG,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;UACTqB,QAAQ,EAAEA,CAAA,KAAK;YACbL,KAAI,CAACzC,MAAM,GAAG,KAAK;YACnByC,KAAI,CAAC5C,OAAO,GAAG,KAAK;YACpB4C,KAAI,CAACtC,QAAQ,CAAC4C,KAAK,EAAE;YACrBN,KAAI,CAAChD,cAAc,CAACuD,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFT,KAAI,CAACjD,gBAAgB,CAClB2D,eAAe,CAACV,KAAI,CAACxC,KAAK,CAAC,CAC3BuB,IAAI,CAACxF,SAAS,CAACyG,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClC8B,SAAS,EAAE;UAChB,CAAC;UACD2B,KAAK,EAAGC,GAAQ,IAAI;YAClBZ,KAAI,CAACzC,MAAM,GAAG,KAAK;YACnByC,KAAI,CAAC5C,OAAO,GAAG,IAAI;YACnB4C,KAAI,CAAChD,cAAc,CAACuD,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACLT,KAAI,CAACjD,gBAAgB,CAClB8D,UAAU,CAACtC,IAAI,CAAC,CAChBQ,IAAI,CAACxF,SAAS,CAACyG,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;UACTqB,QAAQ,EAAEA,CAAA,KAAK;YACbL,KAAI,CAACzC,MAAM,GAAG,KAAK;YACnByC,KAAI,CAAC5C,OAAO,GAAG,KAAK;YACpB4C,KAAI,CAACtC,QAAQ,CAAC4C,KAAK,EAAE;YACrBN,KAAI,CAAChD,cAAc,CAACuD,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFT,KAAI,CAACjD,gBAAgB,CAClB2D,eAAe,CAACV,KAAI,CAACxC,KAAK,CAAC,CAC3BuB,IAAI,CAACxF,SAAS,CAACyG,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClC8B,SAAS,EAAE;UAChB,CAAC;UACD2B,KAAK,EAAGC,GAAQ,IAAI;YAClBZ,KAAI,CAACzC,MAAM,GAAG,KAAK;YACnByC,KAAI,CAAC5C,OAAO,GAAG,IAAI;YACnB4C,KAAI,CAAChD,cAAc,CAACuD,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEAnE,aAAaA,CAACwE,IAAS;IACrB,IAAI,CAAC7D,mBAAmB,CAAC8D,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEhG,MAAM,EAAE,SAAS;MACjBiG,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACL,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAK,MAAMA,CAACL,IAAS;IACd,IAAI,CAAC/D,gBAAgB,CAClBqE,UAAU,CAACN,IAAI,CAACjB,UAAU,CAAC,CAC3Bd,IAAI,CAACxF,SAAS,CAAC,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;MACTqC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACrE,cAAc,CAACuD,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC1D,gBAAgB,CAClB2D,eAAe,CAAC,IAAI,CAAClD,KAAK,CAAC,CAC3BuB,IAAI,CAACxF,SAAS,CAAC,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAClC8B,SAAS,EAAE;MAChB,CAAC;MACD2B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3D,cAAc,CAACuD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAa,SAASA,CAACC,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAGA,IAAInF,CAACA,CAAA;IACH,OAAO,IAAI,CAACgB,QAAQ,CAACoE,QAAQ;EAC/B;EAEAC,UAAUA,CAAC1E,QAAgB;IACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,QAAQ,CAAC4C,KAAK,EAAE;EACvB;EAEA0B,WAAWA,CAAA;IACT,IAAI,CAAC9E,YAAY,CAACmE,IAAI,EAAE;IACxB,IAAI,CAACnE,YAAY,CAACmD,QAAQ,EAAE;EAC9B;;;uBArOWzD,uBAAuB,EAAApD,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAvB5F,uBAAuB;MAAA6F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BvJ,EAFR,CAAAM,cAAA,aAAuD,aAC2C,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,YAAK;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAGrDrB,EADJ,CAAAM,cAAA,aAAmD,kBAE4B;UADrDN,EAAA,CAAAO,UAAA,mBAAAkJ,2DAAA;YAAA,OAASD,GAAA,CAAAjB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAAnDvI,EAAA,CAAAqB,YAAA,EAC2E;UAE3ErB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAA0J,gBAAA,2BAAAC,wEAAA/G,MAAA;YAAA5C,EAAA,CAAA4J,kBAAA,CAAAJ,GAAA,CAAAzH,eAAA,EAAAa,MAAA,MAAA4G,GAAA,CAAAzH,eAAA,GAAAa,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE5C,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAAsJ,iEAAAjH,MAAA;YAAA,OAAgB4G,GAAA,CAAAzD,eAAA,CAAAnD,MAAA,CAAuB;UAAA,EAAC;UAmExC5C,EAjEA,CAAAkB,UAAA,IAAA4I,8CAAA,0BAAgC,KAAAC,+CAAA,yBA8B8B,KAAAC,+CAAA,0BA8BxB,KAAAC,+CAAA,0BAKD;UAOjDjK,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAM,cAAA,oBACuB;UADEN,EAAA,CAAA0J,gBAAA,2BAAAQ,oEAAAtH,MAAA;YAAA5C,EAAA,CAAA4J,kBAAA,CAAAJ,GAAA,CAAA5F,OAAA,EAAAhB,MAAA,MAAA4G,GAAA,CAAA5F,OAAA,GAAAhB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAE1C5C,EAAA,CAAAkB,UAAA,KAAAiJ,+CAAA,yBAAgC;UAMxBnK,EAFR,CAAAM,cAAA,gBAAqE,eACZ,eACT;UACpCN,EAAA,CAAAC,SAAA,oBACkG;UAElGD,EAAA,CAAAkB,UAAA,KAAAkJ,uCAAA,kBACmE;UAK3EpK,EAFI,CAAAqB,YAAA,EAAM,EAEJ;UAEFrB,EADJ,CAAAM,cAAA,eAAiD,kBAGf;UAA1BN,EAAA,CAAAO,UAAA,mBAAA8J,0DAAA;YAAA,OAAAb,GAAA,CAAA5F,OAAA,GAAmB,KAAK;UAAA,EAAC;UAAC5D,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAS;UAC7CrB,EAAA,CAAAM,cAAA,kBAC0B;UAAtBN,EAAA,CAAAO,UAAA,mBAAA+J,0DAAA;YAAA,OAASd,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAE;UAACvG,EAAA,CAAAiB,MAAA,YAAI;UAI1CjB,EAJ0C,CAAAqB,YAAA,EAAS,EACrC,EACH,EAEA;;;UAnHKrB,EAAA,CAAAsB,SAAA,GAAgB;UAAiBtB,EAAjC,CAAAE,UAAA,iBAAgB,oCAAoD;UAEzDF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAsJ,GAAA,CAAAlF,IAAA,CAAgB;UAACtE,EAAA,CAAAuK,gBAAA,YAAAf,GAAA,CAAAzH,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAAqB;UAC6CtB,EADlE,CAAAE,UAAA,UAAAsJ,GAAA,CAAA7F,WAAA,CAAqB,YAAyB,mBAAmB,cAAc,oBACvC,4BAAqD;UA4E/D3D,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAwK,UAAA,CAAAxK,EAAA,CAAAyK,eAAA,KAAAC,GAAA,EAA4B;UAAjE1K,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAAuK,gBAAA,YAAAf,GAAA,CAAA5F,OAAA,CAAqB;UAAmD5D,EAArB,CAAAE,UAAA,qBAAoB,oBAAoB;UAM1GF,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAE,UAAA,cAAAsJ,GAAA,CAAAtF,QAAA,CAAsB;UAGkDlE,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAwK,UAAA,CAAAxK,EAAA,CAAAyK,eAAA,KAAAE,GAAA,EAA6B;UAC3F3K,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA4K,eAAA,KAAAC,GAAA,GAAArB,GAAA,CAAA1F,SAAA,IAAA0F,GAAA,CAAAtG,CAAA,SAAA4H,OAAA,KAAAtB,GAAA,CAAAtG,CAAA,SAAAC,MAAA,EAAkF;UAEhFnD,EAAA,CAAAsB,SAAA,EAA0D;UAA1DtB,EAAA,CAAAE,UAAA,UAAAsJ,GAAA,CAAA1F,SAAA,IAAA0F,GAAA,CAAAtG,CAAA,SAAA4H,OAAA,KAAAtB,GAAA,CAAAtG,CAAA,SAAAC,MAAA,CAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}