{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError, shareReplay, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/dialog\";\nconst _c0 = [\"partySelect\"];\nconst _c1 = () => ({\n  width: \"48rem\"\n});\nfunction SalesCallInvolvedPartiesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 24)(2, \"div\", 25);\n    i0.ɵɵtext(3, \" Role \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 27)(6, \"div\", 25);\n    i0.ɵɵtext(7, \" Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 29)(10, \"div\", 25);\n    i0.ɵɵtext(11, \" Mobile\");\n    i0.ɵɵelement(12, \"p-sortIcon\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 31)(14, \"div\", 25);\n    i0.ɵɵtext(15, \" Phone\");\n    i0.ɵɵelement(16, \"p-sortIcon\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 33)(18, \"div\", 25);\n    i0.ɵɵtext(19, \" E-Mail \");\n    i0.ɵɵelement(20, \"p-sortIcon\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 35);\n    i0.ɵɵtext(24, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 35)(14, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_8_Template_button_click_14_listener($event) {\n      const partie_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(partie_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partie_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.role_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.mobile) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.email_address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r2 == null ? null : partie_r2.address) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"No involved parties found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"Loading involved parties data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Involved Parties\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallInvolvedPartiesComponent_ng_template_31_span_2_Template, 2, 1, \"span\", 39)(3, SalesCallInvolvedPartiesComponent_ng_template_31_span_3_Template, 2, 1, \"span\", 39)(4, SalesCallInvolvedPartiesComponent_ng_template_31_span_4_Template, 2, 1, \"span\", 39)(5, SalesCallInvolvedPartiesComponent_ng_template_31_span_5_Template, 2, 1, \"span\", 39)(6, SalesCallInvolvedPartiesComponent_ng_template_31_span_6_Template, 2, 1, \"span\", 39);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email && item_r4.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email && !item_r4.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile && (item_r4.email || item_r4.bp_full_name));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile && !item_r4.email && !item_r4.bp_full_name);\n  }\n}\nexport class SalesCallInvolvedPartiesComponent {\n  constructor(activitiesservice, formBuilder, messageservice, confirmationservice) {\n    this.activitiesservice = activitiesservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.involvedpartiesdetails = null;\n    this.activity_id = '';\n    this.addDialogVisible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.saving = false;\n    this.partyDataLoading = false;\n    this.partyInput$ = new Subject();\n    this.InvolvedPartiesForm = this.formBuilder.group({\n      role_code: [''],\n      party_id: ['']\n    });\n    this.role = [{\n      label: 'Account',\n      value: 'FLCU01'\n    }, {\n      label: 'Contact',\n      value: 'BUP001'\n    }, {\n      label: 'Created By',\n      value: 'YC'\n    }, {\n      label: 'Inside Sales Rep',\n      value: 'YI'\n    }, {\n      label: 'Outside Sales Rep',\n      value: 'YO'\n    }];\n  }\n  ngOnInit() {\n    this.loadPartyDataOnRoleChange();\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response.activity_id;\n        const involvedParties = response?.involved_parties || [];\n        const roleMap = {\n          YI: 'Inside Sales Rep',\n          //YC: 'Created By',\n          YO: 'Outside Sales Rep'\n        };\n        const allowedPartnerFunctions = ['YI', 'YO'];\n        this.involvedpartiesdetails = involvedParties.map(party => {\n          const addresses = party?.business_partner?.addresses || [];\n          const address = addresses.find(addr => addr?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT'));\n          const processedAddress = address ? {\n            email_address: address?.emails?.[0]?.email_address || '-',\n            mobile: address?.phone_numbers?.find(item => item.phone_number_type === '3')?.phone_number || '-',\n            phone_number: address?.phone_numbers?.find(item => item.phone_number_type === '1')?.phone_number || '-',\n            address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', ')\n          } : {\n            email_address: '-',\n            mobile: '-',\n            phone_number: '-',\n            address: '-'\n          };\n          let roleMatch = '-';\n          if (party?.role_code === 'FLCU01' || party?.role_code === 'BUP001') {\n            roleMatch = this.role.find(r => r.value === party?.role_code || r.value === party?.bp_role)?.label || '-';\n          } else {\n            const partnerFn = party?.business_partner?.customer?.partner_functions?.find(p => allowedPartnerFunctions.includes(p?.partner_function));\n            if (!partnerFn && party?.role_code === 'BUP003' && !['YI', 'YO'].includes(party?.function_code)) {\n              roleMatch = 'Created By';\n            } else if (partnerFn) {\n              roleMatch = roleMap[partnerFn.partner_function] || '-';\n            }\n          }\n          return {\n            ...party,\n            bp_full_name: party?.business_partner?.bp_full_name || '-',\n            role_code: roleMatch,\n            ...processedAddress\n          };\n        });\n      }\n    });\n  }\n  loadPartyDataOnRoleChange() {\n    // Use valueChanges to watch for role_code changes\n    this.partyData$ = this.InvolvedPartiesForm.get('role_code').valueChanges.pipe(tap(() => {\n      this.clearPartyData(); // Extracted clearing logic\n    }), switchMap(role => {\n      if (!role) return of([]); // Return empty array if no role selected\n      return this.partyInput$.pipe(distinctUntilChanged(),\n      // Only trigger when input changes\n      debounceTime(300),\n      // Prevent rapid search calls\n      tap(() => this.partyDataLoading = true), switchMap(term => this.getPartnersByRoleAndSearch(role, term)));\n    }), shareReplay(1) // Ensure multiple subscribers get the same data\n    );\n  }\n  // Extracted method to clear party data\n  clearPartyData() {\n    this.InvolvedPartiesForm.get('party_id')?.reset();\n    this.partyInput$.next(''); // Clear the search term\n    if (this.partySelect) {\n      this.partySelect.clearModel(); // Clear the search input\n    }\n  }\n  // Method to get partners based on the role and search term\n  getPartnersByRoleAndSearch(role, term) {\n    const params = this.getParamsByRole(role, term);\n    if (!params) return of([]);\n    return this.activitiesservice.getPartners(params).pipe(map(res => res || []), catchError(error => {\n      console.error('Error fetching partners:', error); // Log error for debugging\n      this.partyDataLoading = false;\n      return of([]); // Return empty array in case of error\n    }), tap(() => this.partyDataLoading = false) // Reset loading flag after fetching\n    );\n  }\n  getParamsByRole(role, term) {\n    if (!role) return null;\n    const filters = {\n      'filters[$or][0][bp_full_name][$containsi]': term,\n      'filters[$or][1][bp_id][$containsi]': term,\n      'filters[$or][2][first_name][$containsi]': term,\n      'filters[$or][3][last_name][$containsi]': term\n    };\n    // Define roleFilters with string keys and object values\n    const roleFilters = {\n      FLCU01: {\n        'filters[roles][bp_role][$eq][0]': 'FLCU01',\n        'filters[roles][bp_role][$eq][1]': 'FLCU00'\n      },\n      BUP001: {\n        'filters[roles][bp_role][$eq]': 'BUP001'\n      },\n      YI: {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'filters[customer][partner_functions][partner_function][$eq]': 'YI'\n      },\n      YO: {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'filters[customer][partner_functions][partner_function][$eq]': 'YO'\n      },\n      YC: {\n        'filters[roles][bp_role][$eq]': 'BUP003'\n        //'filters[customer][partner_functions][partner_function][$eq]': 'YC',\n      }\n    };\n    // Use the roleFilters map to get the filters for the specific role\n    const roleSpecificFilters = roleFilters[role];\n    if (roleSpecificFilters) {\n      return {\n        ...roleSpecificFilters,\n        ...filters // Merge common filters\n      };\n    }\n    return null; // Return null if no filters are found for the given role\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.InvolvedPartiesForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.InvolvedPartiesForm.value\n      };\n      let role_code = value?.role_code;\n      if (['YI', 'YO', 'YC'].includes(role_code)) {\n        role_code = 'BUP003';\n      }\n      const data = {\n        activity_id: _this.activity_id,\n        role_code: role_code,\n        party_id: value?.party_id\n      };\n      _this.activitiesservice.createInvolvedParty(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.InvolvedPartiesForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Involved Party Added successFully!'\n          });\n          _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: res => {\n          _this.saving = false;\n          _this.addDialogVisible = true;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteInvolvedParty(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.InvolvedPartiesForm.reset();\n    if (this.role?.length > 0) {\n      this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallInvolvedPartiesComponent_Factory(t) {\n      return new (t || SalesCallInvolvedPartiesComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallInvolvedPartiesComponent,\n      selectors: [[\"app-sales-call-involved-parties\"]],\n      viewQuery: function SalesCallInvolvedPartiesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.partySelect = _t.first);\n        }\n      },\n      decls: 35,\n      vars: 21,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"party-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Role\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"role_code\", \"placeholder\", \"Select a Role\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Involved Party\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"party_id\", \"appendTo\", \"body\", 1, \"w-full\", \"h-3rem\", 3, \"search\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pSortableColumn\", \"role_code\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"role_code\"], [\"pSortableColumn\", \"bp_full_name\"], [\"field\", \"bp_full_name\"], [\"pSortableColumn\", \"mobile\"], [\"field\", \"mobile\"], [\"pSortableColumn\", \"phone_number\"], [\"field\", \"phone_number\"], [\"pSortableColumn\", \"email_address\"], [\"field\", \"email_address\"], [1, \"border-round-right-lg\"], [1, \"border-round-left-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"7\", 1, \"border-round-left-lg\"], [4, \"ngIf\"]],\n      template: function SalesCallInvolvedPartiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Involved Parties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_p_button_click_4_listener() {\n            return ctx.showNewDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, SalesCallInvolvedPartiesComponent_ng_template_7_Template, 25, 0, \"ng-template\", 6)(8, SalesCallInvolvedPartiesComponent_ng_template_8_Template, 15, 6, \"ng-template\", 7)(9, SalesCallInvolvedPartiesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, SalesCallInvolvedPartiesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"p-dialog\", 10);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallInvolvedPartiesComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(12, SalesCallInvolvedPartiesComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n          i0.ɵɵelementStart(13, \"form\", 11)(14, \"div\", 12)(15, \"label\", 13)(16, \"span\", 14);\n          i0.ɵɵtext(17, \"supervisor_account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \"Role \");\n          i0.ɵɵelementStart(19, \"span\", 15);\n          i0.ɵɵtext(20, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵelement(22, \"p-dropdown\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"label\", 18)(25, \"span\", 14);\n          i0.ɵɵtext(26, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Involved Party \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"ng-select\", 19);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵlistener(\"search\", function SalesCallInvolvedPartiesComponent_Template_ng_select_search_29_listener($event) {\n            return ctx.partyInput$.next($event.term);\n          });\n          i0.ɵɵtemplate(31, SalesCallInvolvedPartiesComponent_ng_template_31_Template, 7, 6, \"ng-template\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_33_listener() {\n            return ctx.addDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_34_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.involvedpartiesdetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(20, _c1));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.InvolvedPartiesForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.role);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(30, 18, ctx.partyData$))(\"hideSelected\", true)(\"loading\", ctx.partyDataLoading)(\"minTermLength\", 3)(\"typeahead\", ctx.partyInput$);\n        }\n      },\n      dependencies: [i4.NgIf, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Dialog, i4.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "map", "of", "distinctUntilChanged", "switchMap", "tap", "catchError", "shareReplay", "debounceTime", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "SalesCallInvolvedPartiesComponent_ng_template_8_Template_button_click_14_listener", "$event", "partie_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "stopPropagation", "ɵɵresetView", "confirmRemove", "ɵɵadvance", "ɵɵtextInterpolate1", "role_code", "bp_full_name", "mobile", "phone_number", "email_address", "address", "item_r4", "email", "ɵɵtemplate", "SalesCallInvolvedPartiesComponent_ng_template_31_span_2_Template", "SalesCallInvolvedPartiesComponent_ng_template_31_span_3_Template", "SalesCallInvolvedPartiesComponent_ng_template_31_span_4_Template", "SalesCallInvolvedPartiesComponent_ng_template_31_span_5_Template", "SalesCallInvolvedPartiesComponent_ng_template_31_span_6_Template", "ɵɵtextInterpolate", "bp_id", "ɵɵproperty", "SalesCallInvolvedPartiesComponent", "constructor", "activitiesservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "involvedpartiesdetails", "activity_id", "addDialogVisible", "position", "submitted", "saving", "partyDataLoading", "partyInput$", "InvolvedPartiesForm", "group", "party_id", "role", "label", "value", "ngOnInit", "loadPartyDataOnRoleChange", "activity", "pipe", "subscribe", "response", "involvedParties", "involved_parties", "roleMap", "YI", "YO", "allowedPartnerFunctions", "party", "addresses", "business_partner", "find", "addr", "address_usages", "some", "usage", "address_usage", "processedAddress", "emails", "phone_numbers", "item", "phone_number_type", "house_number", "street_name", "city_name", "region", "country", "postal_code", "filter", "Boolean", "join", "roleMatch", "r", "bp_role", "partnerFn", "customer", "partner_functions", "p", "includes", "partner_function", "function_code", "partyData$", "get", "valueChanges", "clearPartyData", "term", "getPartnersByRoleAndSearch", "reset", "next", "partySelect", "clearModel", "params", "getParamsByRole", "getPartners", "res", "error", "console", "filters", "roleFilters", "FLCU01", "BUP001", "YC", "roleSpecificFilters", "onSubmit", "_this", "_asyncToGenerator", "invalid", "data", "createInvolvedParty", "add", "severity", "detail", "getActivityByID", "confirm", "message", "header", "icon", "accept", "remove", "deleteInvolvedParty", "documentId", "showNewDialog", "length", "setValue", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "FormBuilder", "i3", "MessageService", "ConfirmationService", "selectors", "viewQuery", "SalesCallInvolvedPartiesComponent_Query", "rf", "ctx", "SalesCallInvolvedPartiesComponent_Template_p_button_click_4_listener", "SalesCallInvolvedPartiesComponent_ng_template_7_Template", "SalesCallInvolvedPartiesComponent_ng_template_8_Template", "SalesCallInvolvedPartiesComponent_ng_template_9_Template", "SalesCallInvolvedPartiesComponent_ng_template_10_Template", "ɵɵtwoWayListener", "SalesCallInvolvedPartiesComponent_Template_p_dialog_visibleChange_11_listener", "ɵɵtwoWayBindingSet", "SalesCallInvolvedPartiesComponent_ng_template_12_Template", "SalesCallInvolvedPartiesComponent_Template_ng_select_search_29_listener", "SalesCallInvolvedPartiesComponent_ng_template_31_Template", "SalesCallInvolvedPartiesComponent_Template_button_click_33_listener", "SalesCallInvolvedPartiesComponent_Template_button_click_34_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "ɵɵtwoWayProperty", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-involved-parties\\sales-call-involved-parties.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-involved-parties\\sales-call-involved-parties.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  shareReplay,\r\n  debounceTime,\r\n} from 'rxjs/operators';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { NgSelectComponent } from '@ng-select/ng-select';\r\n\r\n@Component({\r\n  selector: 'app-sales-call-involved-parties',\r\n  templateUrl: './sales-call-involved-parties.component.html',\r\n  styleUrl: './sales-call-involved-parties.component.scss',\r\n})\r\nexport class SalesCallInvolvedPartiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('partySelect') partySelect!: NgSelectComponent;\r\n  public involvedpartiesdetails: any = null;\r\n  public activity_id: string = '';\r\n  public addDialogVisible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public partyData$?: Observable<any[]>;\r\n  public partyDataLoading = false;\r\n  public partyInput$ = new Subject<string>();\r\n  public InvolvedPartiesForm: FormGroup = this.formBuilder.group({\r\n    role_code: [''],\r\n    party_id: [''],\r\n  });\r\n\r\n  public role = [\r\n    { label: 'Account', value: 'FLCU01' },\r\n    { label: 'Contact', value: 'BUP001' },\r\n    { label: 'Created By', value: 'YC' },\r\n    { label: 'Inside Sales Rep', value: 'YI' },\r\n    { label: 'Outside Sales Rep', value: 'YO' },\r\n  ];\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadPartyDataOnRoleChange();\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response.activity_id;\r\n          const involvedParties = response?.involved_parties || [];\r\n\r\n          const roleMap: any = {\r\n            YI: 'Inside Sales Rep',\r\n            //YC: 'Created By',\r\n            YO: 'Outside Sales Rep',\r\n          };\r\n\r\n          const allowedPartnerFunctions = ['YI', 'YO'];\r\n\r\n          this.involvedpartiesdetails = involvedParties.map((party: any) => {\r\n            const addresses = party?.business_partner?.addresses || [];\r\n\r\n            const address = addresses.find((addr: any) =>\r\n              addr?.address_usages?.some(\r\n                (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n              )\r\n            );\r\n\r\n            const processedAddress = address\r\n              ? {\r\n                  email_address: address?.emails?.[0]?.email_address || '-',\r\n                  mobile:\r\n                    address?.phone_numbers?.find(\r\n                      (item: any) => item.phone_number_type === '3'\r\n                    )?.phone_number || '-',\r\n                  phone_number:\r\n                    address?.phone_numbers?.find(\r\n                      (item: any) => item.phone_number_type === '1'\r\n                    )?.phone_number || '-',\r\n                  address: [\r\n                    address?.house_number,\r\n                    address?.street_name,\r\n                    address?.city_name,\r\n                    address?.region,\r\n                    address?.country,\r\n                    address?.postal_code,\r\n                  ]\r\n                    .filter(Boolean)\r\n                    .join(', '),\r\n                }\r\n              : {\r\n                  email_address: '-',\r\n                  mobile: '-',\r\n                  phone_number: '-',\r\n                  address: '-',\r\n                };\r\n\r\n            let roleMatch = '-';\r\n\r\n            if (\r\n              party?.role_code === 'FLCU01' ||\r\n              party?.role_code === 'BUP001'\r\n            ) {\r\n              roleMatch =\r\n                this.role.find(\r\n                  (r) =>\r\n                    r.value === party?.role_code || r.value === party?.bp_role\r\n                )?.label || '-';\r\n            } else {\r\n              const partnerFn =\r\n                party?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) =>\r\n                    allowedPartnerFunctions.includes(p?.partner_function)\r\n                );\r\n              if (\r\n                !partnerFn &&\r\n                party?.role_code === 'BUP003' &&\r\n                !['YI', 'YO'].includes(party?.function_code)\r\n              ) {\r\n                roleMatch = 'Created By';\r\n              } else if (partnerFn) {\r\n                roleMatch = roleMap[partnerFn.partner_function] || '-';\r\n              }\r\n            }\r\n\r\n            return {\r\n              ...party,\r\n              bp_full_name: party?.business_partner?.bp_full_name || '-',\r\n              role_code: roleMatch,\r\n              ...processedAddress,\r\n            };\r\n          });\r\n        }\r\n      });\r\n  }\r\n\r\n  loadPartyDataOnRoleChange(): void {\r\n    // Use valueChanges to watch for role_code changes\r\n    this.partyData$ = this.InvolvedPartiesForm.get(\r\n      'role_code'\r\n    )!.valueChanges.pipe(\r\n      tap(() => {\r\n        this.clearPartyData(); // Extracted clearing logic\r\n      }),\r\n      switchMap((role: string) => {\r\n        if (!role) return of([]); // Return empty array if no role selected\r\n\r\n        return this.partyInput$.pipe(\r\n          distinctUntilChanged(), // Only trigger when input changes\r\n          debounceTime(300), // Prevent rapid search calls\r\n          tap(() => (this.partyDataLoading = true)),\r\n          switchMap((term: string) =>\r\n            this.getPartnersByRoleAndSearch(role, term)\r\n          )\r\n        );\r\n      }),\r\n      shareReplay(1) // Ensure multiple subscribers get the same data\r\n    );\r\n  }\r\n\r\n  // Extracted method to clear party data\r\n  private clearPartyData(): void {\r\n    this.InvolvedPartiesForm.get('party_id')?.reset();\r\n    this.partyInput$.next(''); // Clear the search term\r\n    if (this.partySelect) {\r\n      this.partySelect.clearModel(); // Clear the search input\r\n    }\r\n  }\r\n\r\n  // Method to get partners based on the role and search term\r\n  private getPartnersByRoleAndSearch(role: string, term: string) {\r\n    const params = this.getParamsByRole(role, term);\r\n    if (!params) return of([]);\r\n\r\n    return this.activitiesservice.getPartners(params).pipe(\r\n      map((res: any) => res || []),\r\n      catchError((error) => {\r\n        console.error('Error fetching partners:', error); // Log error for debugging\r\n        this.partyDataLoading = false;\r\n        return of([]); // Return empty array in case of error\r\n      }),\r\n      tap(() => (this.partyDataLoading = false)) // Reset loading flag after fetching\r\n    );\r\n  }\r\n\r\n  private getParamsByRole(role: string, term: string): any | null {\r\n    if (!role) return null;\r\n\r\n    const filters: any = {\r\n      'filters[$or][0][bp_full_name][$containsi]': term,\r\n      'filters[$or][1][bp_id][$containsi]': term,\r\n      'filters[$or][2][first_name][$containsi]': term,\r\n      'filters[$or][3][last_name][$containsi]': term,\r\n    };\r\n\r\n    // Define roleFilters with string keys and object values\r\n    const roleFilters: Record<string, any> = {\r\n      FLCU01: {\r\n        'filters[roles][bp_role][$eq][0]': 'FLCU01',\r\n        'filters[roles][bp_role][$eq][1]': 'FLCU00',\r\n      },\r\n      BUP001: {\r\n        'filters[roles][bp_role][$eq]': 'BUP001',\r\n      },\r\n      YI: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        'filters[customer][partner_functions][partner_function][$eq]': 'YI',\r\n      },\r\n      YO: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        'filters[customer][partner_functions][partner_function][$eq]': 'YO',\r\n      },\r\n      YC: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        //'filters[customer][partner_functions][partner_function][$eq]': 'YC',\r\n      },\r\n    };\r\n\r\n    // Use the roleFilters map to get the filters for the specific role\r\n    const roleSpecificFilters = roleFilters[role];\r\n\r\n    if (roleSpecificFilters) {\r\n      return {\r\n        ...roleSpecificFilters,\r\n        ...filters, // Merge common filters\r\n      };\r\n    }\r\n\r\n    return null; // Return null if no filters are found for the given role\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.InvolvedPartiesForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.InvolvedPartiesForm.value };\r\n\r\n    let role_code = value?.role_code;\r\n    if (['YI', 'YO', 'YC'].includes(role_code)) {\r\n      role_code = 'BUP003';\r\n    }\r\n\r\n    const data = {\r\n      activity_id: this.activity_id,\r\n      role_code: role_code,\r\n      party_id: value?.party_id,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createInvolvedParty(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.InvolvedPartiesForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Involved Party Added successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteInvolvedParty(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.InvolvedPartiesForm.reset();\r\n    if (this.role?.length > 0) {\r\n      this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Involved Parties</h4>\r\n        <p-button label=\"Add\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"involvedpartiesdetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"role_code\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Role <p-sortIcon field=\"role_code\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_full_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Name <p-sortIcon field=\"bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"mobile\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Mobile<p-sortIcon field=\"mobile\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"phone_number\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Phone<p-sortIcon field=\"phone_number\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"email_address\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            E-Mail <p-sortIcon field=\"email_address\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Address</th>\r\n                    <th class=\"border-round-right-lg\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-partie>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ partie?.role_code || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ partie?.bp_full_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ partie?.mobile || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ partie?.phone_number || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ partie?.email_address || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ partie?.address || \"-\" }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(partie)\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">No involved parties found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">Loading involved parties data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '48rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"party-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Involved Parties</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"InvolvedPartiesForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Role\">\r\n                <span class=\"material-symbols-rounded\">supervisor_account</span>Role\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"role\" formControlName=\"role_code\" placeholder=\"Select a Role\" optionLabel=\"label\"\r\n                    optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Involved Party\">\r\n                <span class=\"material-symbols-rounded\">person</span>Involved Party\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select [items]=\"partyData$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\" [hideSelected]=\"true\"\r\n                    [loading]=\"partyDataLoading\" [minTermLength]=\"3\" [typeahead]=\"partyInput$\"\r\n                    formControlName=\"party_id\" appendTo=\"body\" class=\"w-full h-3rem\"\r\n                    (search)=\"partyInput$.next($event.term)\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        <span *ngIf=\"item.email && item.bp_full_name\">\r\n                            : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.email && !item.bp_full_name\">\r\n                            : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.mobile && (item.email || item.bp_full_name)\">\r\n                            : {{ item.mobile }}</span>\r\n                        <span *ngIf=\"item.mobile && !item.email && !item.bp_full_name\">\r\n                            : {{ item.mobile }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,EAAcC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAG9D,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,YAAY,QACP,gBAAgB;;;;;;;;;;;;;;;;;;ICECC,EAFR,CAAAC,cAAA,SAAI,aAC6D,cACd;IACvCD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,SAAA,qBAA2C;IAExDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAmC,cACY;IACvCD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,SAAA,qBAA8C;IAE3DH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAA6B,eACkB;IACvCD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,SAAA,sBAAwC;IAEtDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAmC,eACY;IACvCD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,SAAA,sBAA8C;IAE3DH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAoC,eACW;IACvCD,EAAA,CAAAE,MAAA,gBAAO;IAAAF,EAAA,CAAAG,SAAA,sBAA+C;IAE9DH,EADI,CAAAI,YAAA,EAAM,EACL;IACLJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC5CF,EAD4C,CAAAI,YAAA,EAAK,EAC5C;;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAEDJ,EADJ,CAAAC,cAAA,cAAkC,kBAEgC;IAA1DD,EAAA,CAAAK,UAAA,mBAAAC,kFAAAC,MAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAASN,MAAA,CAAAO,eAAA,EAAwB;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAEH,MAAA,CAAAI,aAAA,CAAAR,SAAA,CAAqB;IAAA,EAAC;IAErER,EAFsE,CAAAI,YAAA,EAAS,EACtE,EACJ;;;;IArBGJ,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAW,SAAA,cACJ;IAEInB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAY,YAAA,cACJ;IAEIpB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAa,MAAA,cACJ;IAEIrB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAc,YAAA,cACJ;IAEItB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAe,aAAA,cACJ;IAEIvB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,SAAA,kBAAAA,SAAA,CAAAgB,OAAA,cACJ;;;;;IASAxB,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAC3EF,EAD2E,CAAAI,YAAA,EAAK,EAC3E;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,kDAA2C;IAC5FF,EAD4F,CAAAI,YAAA,EAAK,EAC5F;;;;;IAQbJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAI,YAAA,EAAK;;;;;IA0BTJ,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAiB,SAAA,EAAyB;IAAzBjB,EAAA,CAAAkB,kBAAA,QAAAO,OAAA,CAAAL,YAAA,KAAyB;;;;;IAC1DpB,EAAA,CAAAC,cAAA,WAA8C;IAC1CD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAAzBJ,EAAA,CAAAiB,SAAA,EAAkB;IAAlBjB,EAAA,CAAAkB,kBAAA,QAAAO,OAAA,CAAAC,KAAA,KAAkB;;;;;IACtB1B,EAAA,CAAAC,cAAA,WAA+C;IAC3CD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAAzBJ,EAAA,CAAAiB,SAAA,EAAkB;IAAlBjB,EAAA,CAAAkB,kBAAA,QAAAO,OAAA,CAAAC,KAAA,KAAkB;;;;;IACtB1B,EAAA,CAAAC,cAAA,WAA+D;IAC3DD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAkB,kBAAA,QAAAO,OAAA,CAAAJ,MAAA,KAAmB;;;;;IACvBrB,EAAA,CAAAC,cAAA,WAA+D;IAC3DD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAkB,kBAAA,QAAAO,OAAA,CAAAJ,MAAA,KAAmB;;;;;IATvBrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAI,YAAA,EAAO;IAQ7BJ,EAPA,CAAA2B,UAAA,IAAAC,gEAAA,mBAAgC,IAAAC,gEAAA,mBACc,IAAAC,gEAAA,mBAEC,IAAAC,gEAAA,mBAEgB,IAAAC,gEAAA,mBAEA;;;;IARzDhC,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAS,KAAA,CAAgB;IACflC,EAAA,CAAAiB,SAAA,EAAuB;IAAvBjB,EAAA,CAAAmC,UAAA,SAAAV,OAAA,CAAAL,YAAA,CAAuB;IACvBpB,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAmC,UAAA,SAAAV,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAL,YAAA,CAAqC;IAErCpB,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAAmC,UAAA,SAAAV,OAAA,CAAAC,KAAA,KAAAD,OAAA,CAAAL,YAAA,CAAsC;IAEtCpB,EAAA,CAAAiB,SAAA,EAAsD;IAAtDjB,EAAA,CAAAmC,UAAA,SAAAV,OAAA,CAAAJ,MAAA,KAAAI,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAL,YAAA,EAAsD;IAEtDpB,EAAA,CAAAiB,SAAA,EAAsD;IAAtDjB,EAAA,CAAAmC,UAAA,SAAAV,OAAA,CAAAJ,MAAA,KAAAI,OAAA,CAAAC,KAAA,KAAAD,OAAA,CAAAL,YAAA,CAAsD;;;ADjGrF,OAAM,MAAOgB,iCAAiC;EAyB5CC,YACUC,iBAAoC,EACpCC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA5BrB,KAAAC,YAAY,GAAG,IAAIpD,OAAO,EAAQ;IAEnC,KAAAqD,sBAAsB,GAAQ,IAAI;IAClC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,IAAI5D,OAAO,EAAU;IACnC,KAAA6D,mBAAmB,GAAc,IAAI,CAACZ,WAAW,CAACa,KAAK,CAAC;MAC7DjC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfkC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;IAEK,KAAAC,IAAI,GAAG,CACZ;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACrC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACrC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAI,CAAE,EACpC;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC1C;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC5C;EAOE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACpB,iBAAiB,CAACqB,QAAQ,CAC5BC,IAAI,CAACrE,SAAS,CAAC,IAAI,CAACmD,YAAY,CAAC,CAAC,CAClCmB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAClB,WAAW,GAAGkB,QAAQ,CAAClB,WAAW;QACvC,MAAMmB,eAAe,GAAGD,QAAQ,EAAEE,gBAAgB,IAAI,EAAE;QAExD,MAAMC,OAAO,GAAQ;UACnBC,EAAE,EAAE,kBAAkB;UACtB;UACAC,EAAE,EAAE;SACL;QAED,MAAMC,uBAAuB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;QAE5C,IAAI,CAACzB,sBAAsB,GAAGoB,eAAe,CAACvE,GAAG,CAAE6E,KAAU,IAAI;UAC/D,MAAMC,SAAS,GAAGD,KAAK,EAAEE,gBAAgB,EAAED,SAAS,IAAI,EAAE;UAE1D,MAAM9C,OAAO,GAAG8C,SAAS,CAACE,IAAI,CAAEC,IAAS,IACvCA,IAAI,EAAEC,cAAc,EAAEC,IAAI,CACvBC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF;UAED,MAAMC,gBAAgB,GAAGtD,OAAO,GAC5B;YACED,aAAa,EAAEC,OAAO,EAAEuD,MAAM,GAAG,CAAC,CAAC,EAAExD,aAAa,IAAI,GAAG;YACzDF,MAAM,EACJG,OAAO,EAAEwD,aAAa,EAAER,IAAI,CACzBS,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAK,GAAG,CAC9C,EAAE5D,YAAY,IAAI,GAAG;YACxBA,YAAY,EACVE,OAAO,EAAEwD,aAAa,EAAER,IAAI,CACzBS,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAK,GAAG,CAC9C,EAAE5D,YAAY,IAAI,GAAG;YACxBE,OAAO,EAAE,CACPA,OAAO,EAAE2D,YAAY,EACrB3D,OAAO,EAAE4D,WAAW,EACpB5D,OAAO,EAAE6D,SAAS,EAClB7D,OAAO,EAAE8D,MAAM,EACf9D,OAAO,EAAE+D,OAAO,EAChB/D,OAAO,EAAEgE,WAAW,CACrB,CACEC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI;WACb,GACD;YACEpE,aAAa,EAAE,GAAG;YAClBF,MAAM,EAAE,GAAG;YACXC,YAAY,EAAE,GAAG;YACjBE,OAAO,EAAE;WACV;UAEL,IAAIoE,SAAS,GAAG,GAAG;UAEnB,IACEvB,KAAK,EAAElD,SAAS,KAAK,QAAQ,IAC7BkD,KAAK,EAAElD,SAAS,KAAK,QAAQ,EAC7B;YACAyE,SAAS,GACP,IAAI,CAACtC,IAAI,CAACkB,IAAI,CACXqB,CAAC,IACAA,CAAC,CAACrC,KAAK,KAAKa,KAAK,EAAElD,SAAS,IAAI0E,CAAC,CAACrC,KAAK,KAAKa,KAAK,EAAEyB,OAAO,CAC7D,EAAEvC,KAAK,IAAI,GAAG;UACnB,CAAC,MAAM;YACL,MAAMwC,SAAS,GACb1B,KAAK,EAAEE,gBAAgB,EAAEyB,QAAQ,EAAEC,iBAAiB,EAAEzB,IAAI,CACvD0B,CAAM,IACL9B,uBAAuB,CAAC+B,QAAQ,CAACD,CAAC,EAAEE,gBAAgB,CAAC,CACxD;YACH,IACE,CAACL,SAAS,IACV1B,KAAK,EAAElD,SAAS,KAAK,QAAQ,IAC7B,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAACgF,QAAQ,CAAC9B,KAAK,EAAEgC,aAAa,CAAC,EAC5C;cACAT,SAAS,GAAG,YAAY;YAC1B,CAAC,MAAM,IAAIG,SAAS,EAAE;cACpBH,SAAS,GAAG3B,OAAO,CAAC8B,SAAS,CAACK,gBAAgB,CAAC,IAAI,GAAG;YACxD;UACF;UAEA,OAAO;YACL,GAAG/B,KAAK;YACRjD,YAAY,EAAEiD,KAAK,EAAEE,gBAAgB,EAAEnD,YAAY,IAAI,GAAG;YAC1DD,SAAS,EAAEyE,SAAS;YACpB,GAAGd;WACJ;QACH,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACN;EAEApB,yBAAyBA,CAAA;IACvB;IACA,IAAI,CAAC4C,UAAU,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,GAAG,CAC5C,WAAW,CACX,CAACC,YAAY,CAAC5C,IAAI,CAClBhE,GAAG,CAAC,MAAK;MACP,IAAI,CAAC6G,cAAc,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC,EACF9G,SAAS,CAAE2D,IAAY,IAAI;MACzB,IAAI,CAACA,IAAI,EAAE,OAAO7D,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAE1B,OAAO,IAAI,CAACyD,WAAW,CAACU,IAAI,CAC1BlE,oBAAoB,EAAE;MAAE;MACxBK,YAAY,CAAC,GAAG,CAAC;MAAE;MACnBH,GAAG,CAAC,MAAO,IAAI,CAACqD,gBAAgB,GAAG,IAAK,CAAC,EACzCtD,SAAS,CAAE+G,IAAY,IACrB,IAAI,CAACC,0BAA0B,CAACrD,IAAI,EAAEoD,IAAI,CAAC,CAC5C,CACF;IACH,CAAC,CAAC,EACF5G,WAAW,CAAC,CAAC,CAAC,CAAC;KAChB;EACH;EAEA;EACQ2G,cAAcA,CAAA;IACpB,IAAI,CAACtD,mBAAmB,CAACoD,GAAG,CAAC,UAAU,CAAC,EAAEK,KAAK,EAAE;IACjD,IAAI,CAAC1D,WAAW,CAAC2D,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACC,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACC,UAAU,EAAE,CAAC,CAAC;IACjC;EACF;EAEA;EACQJ,0BAA0BA,CAACrD,IAAY,EAAEoD,IAAY;IAC3D,MAAMM,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC3D,IAAI,EAAEoD,IAAI,CAAC;IAC/C,IAAI,CAACM,MAAM,EAAE,OAAOvH,EAAE,CAAC,EAAE,CAAC;IAE1B,OAAO,IAAI,CAAC6C,iBAAiB,CAAC4E,WAAW,CAACF,MAAM,CAAC,CAACpD,IAAI,CACpDpE,GAAG,CAAE2H,GAAQ,IAAKA,GAAG,IAAI,EAAE,CAAC,EAC5BtH,UAAU,CAAEuH,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC,CAAC,CAAC;MAClD,IAAI,CAACnE,gBAAgB,GAAG,KAAK;MAC7B,OAAOxD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,EACFG,GAAG,CAAC,MAAO,IAAI,CAACqD,gBAAgB,GAAG,KAAM,CAAC,CAAC;KAC5C;EACH;EAEQgE,eAAeA,CAAC3D,IAAY,EAAEoD,IAAY;IAChD,IAAI,CAACpD,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMgE,OAAO,GAAQ;MACnB,2CAA2C,EAAEZ,IAAI;MACjD,oCAAoC,EAAEA,IAAI;MAC1C,yCAAyC,EAAEA,IAAI;MAC/C,wCAAwC,EAAEA;KAC3C;IAED;IACA,MAAMa,WAAW,GAAwB;MACvCC,MAAM,EAAE;QACN,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE;OACpC;MACDC,MAAM,EAAE;QACN,8BAA8B,EAAE;OACjC;MACDvD,EAAE,EAAE;QACF,8BAA8B,EAAE,QAAQ;QACxC,6DAA6D,EAAE;OAChE;MACDC,EAAE,EAAE;QACF,8BAA8B,EAAE,QAAQ;QACxC,6DAA6D,EAAE;OAChE;MACDuD,EAAE,EAAE;QACF,8BAA8B,EAAE;QAChC;;KAEH;IAED;IACA,MAAMC,mBAAmB,GAAGJ,WAAW,CAACjE,IAAI,CAAC;IAE7C,IAAIqE,mBAAmB,EAAE;MACvB,OAAO;QACL,GAAGA,mBAAmB;QACtB,GAAGL,OAAO,CAAE;OACb;IACH;IAEA,OAAO,IAAI,CAAC,CAAC;EACf;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC9E,SAAS,GAAG,IAAI;MAErB,IAAI8E,KAAI,CAAC1E,mBAAmB,CAAC4E,OAAO,EAAE;QACpC;MACF;MAEAF,KAAI,CAAC7E,MAAM,GAAG,IAAI;MAClB,MAAMQ,KAAK,GAAG;QAAE,GAAGqE,KAAI,CAAC1E,mBAAmB,CAACK;MAAK,CAAE;MAEnD,IAAIrC,SAAS,GAAGqC,KAAK,EAAErC,SAAS;MAChC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACgF,QAAQ,CAAChF,SAAS,CAAC,EAAE;QAC1CA,SAAS,GAAG,QAAQ;MACtB;MAEA,MAAM6G,IAAI,GAAG;QACXpF,WAAW,EAAEiF,KAAI,CAACjF,WAAW;QAC7BzB,SAAS,EAAEA,SAAS;QACpBkC,QAAQ,EAAEG,KAAK,EAAEH;OAClB;MAEDwE,KAAI,CAACvF,iBAAiB,CACnB2F,mBAAmB,CAACD,IAAI,CAAC,CACzBpE,IAAI,CAACrE,SAAS,CAACsI,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCmB,SAAS,CAAC;QACTgD,IAAI,EAAG/C,QAAa,IAAI;UACtB+D,KAAI,CAAC7E,MAAM,GAAG,KAAK;UACnB6E,KAAI,CAAChF,gBAAgB,GAAG,KAAK;UAC7BgF,KAAI,CAAC1E,mBAAmB,CAACyD,KAAK,EAAE;UAChCiB,KAAI,CAACrF,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFP,KAAI,CAACvF,iBAAiB,CACnB+F,eAAe,CAACR,KAAI,CAACjF,WAAW,CAAC,CACjCgB,IAAI,CAACrE,SAAS,CAACsI,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCmB,SAAS,EAAE;QAChB,CAAC;QACDuD,KAAK,EAAGD,GAAQ,IAAI;UAClBU,KAAI,CAAC7E,MAAM,GAAG,KAAK;UACnB6E,KAAI,CAAChF,gBAAgB,GAAG,IAAI;UAC5BgF,KAAI,CAACrF,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEApH,aAAaA,CAACiE,IAAS;IACrB,IAAI,CAACxC,mBAAmB,CAAC6F,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC1D,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA0D,MAAMA,CAAC1D,IAAS;IACd,IAAI,CAAC3C,iBAAiB,CACnBsG,mBAAmB,CAAC3D,IAAI,CAAC4D,UAAU,CAAC,CACpCjF,IAAI,CAACrE,SAAS,CAAC,IAAI,CAACmD,YAAY,CAAC,CAAC,CAClCmB,SAAS,CAAC;MACTgD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACrE,cAAc,CAAC0F,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC9F,iBAAiB,CACnB+F,eAAe,CAAC,IAAI,CAACzF,WAAW,CAAC,CACjCgB,IAAI,CAACrE,SAAS,CAAC,IAAI,CAACmD,YAAY,CAAC,CAAC,CAClCmB,SAAS,EAAE;MAChB,CAAC;MACDuD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC5E,cAAc,CAAC0F,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAU,aAAaA,CAAChG,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,mBAAmB,CAACyD,KAAK,EAAE;IAChC,IAAI,IAAI,CAACtD,IAAI,EAAEyF,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC5F,mBAAmB,CAACoD,GAAG,CAAC,WAAW,CAAC,EAAEyC,QAAQ,CAAC,IAAI,CAAC1F,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;IACzE;EACF;EAEAyF,WAAWA,CAAA;IACT,IAAI,CAACvG,YAAY,CAACmE,IAAI,EAAE;IACxB,IAAI,CAACnE,YAAY,CAACwG,QAAQ,EAAE;EAC9B;;;uBA9TW9G,iCAAiC,EAAApC,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAArJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvJ,EAAA,CAAAmJ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzJ,EAAA,CAAAmJ,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAjCtH,iCAAiC;MAAAuH,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UClBtC9J,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACpEJ,EAAA,CAAAC,cAAA,kBAC2D;UADrCD,EAAA,CAAAK,UAAA,mBAAA2J,qEAAA;YAAA,OAASD,GAAA,CAAAjB,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAE1D9I,EAFI,CAAAI,YAAA,EAC2D,EACzD;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAgE1BD,EA/DA,CAAA2B,UAAA,IAAAsI,wDAAA,0BAAgC,IAAAC,wDAAA,0BAgCS,IAAAC,wDAAA,yBA0BH,KAAAC,yDAAA,yBAKD;UAOjDpK,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UACNJ,EAAA,CAAAC,cAAA,oBAC4C;UADnBD,EAAA,CAAAqK,gBAAA,2BAAAC,8EAAA/J,MAAA;YAAAP,EAAA,CAAAuK,kBAAA,CAAAR,GAAA,CAAAlH,gBAAA,EAAAtC,MAAA,MAAAwJ,GAAA,CAAAlH,gBAAA,GAAAtC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAEnDP,EAAA,CAAA2B,UAAA,KAAA6I,yDAAA,yBAAgC;UAOpBxK,EAHZ,CAAAC,cAAA,gBAAgF,eACvB,iBAC2C,gBACjD;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,aAChE;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAG,SAAA,sBAEa;UAErBH,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAqD,iBACqD,gBAC3D;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,uBACxD;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UAEJJ,EADJ,CAAAC,cAAA,eAAwC,qBAIS;;UAAzCD,EAAA,CAAAK,UAAA,oBAAAoK,wEAAAlK,MAAA;YAAA,OAAUwJ,GAAA,CAAA7G,WAAA,CAAA2D,IAAA,CAAAtG,MAAA,CAAAmG,IAAA,CAA6B;UAAA,EAAC;UACxC1G,EAAA,CAAA2B,UAAA,KAAA+I,yDAAA,0BAA2C;UAcvD1K,EAFQ,CAAAI,YAAA,EAAY,EACV,EACJ;UAEFJ,EADJ,CAAAC,cAAA,eAAoD,kBAGT;UAAnCD,EAAA,CAAAK,UAAA,mBAAAsK,oEAAA;YAAA,OAAAZ,GAAA,CAAAlH,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAC7C,EAAA,CAAAI,YAAA,EAAS;UAChDJ,EAAA,CAAAC,cAAA,kBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAAuK,oEAAA;YAAA,OAASb,GAAA,CAAAnC,QAAA,EAAU;UAAA,EAAC;UAGpC5H,EAHqC,CAAAI,YAAA,EAAS,EAChC,EACH,EACA;;;UA/HCJ,EAAA,CAAAiB,SAAA,GAAmC;UAACjB,EAApC,CAAAmC,UAAA,oCAAmC,iBAAiB;UAI/CnC,EAAA,CAAAiB,SAAA,GAAgC;UAAwCjB,EAAxE,CAAAmC,UAAA,UAAA4H,GAAA,CAAApH,sBAAA,CAAgC,YAAyB,mBAAiC;UAyEnD3C,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAA6K,UAAA,CAAA7K,EAAA,CAAA8K,eAAA,KAAAC,GAAA,EAA4B;UAA1E/K,EAAA,CAAAmC,UAAA,eAAc;UAACnC,EAAA,CAAAgL,gBAAA,YAAAjB,GAAA,CAAAlH,gBAAA,CAA8B;UACnD7C,EADiF,CAAAmC,UAAA,qBAAoB,oBAClF;UAKbnC,EAAA,CAAAiB,SAAA,GAAiC;UAAjCjB,EAAA,CAAAmC,UAAA,cAAA4H,GAAA,CAAA5G,mBAAA,CAAiC;UAOfnD,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAmC,UAAA,YAAA4H,GAAA,CAAAzG,IAAA,CAAgB;UAUjBtD,EAAA,CAAAiB,SAAA,GAA4B;UACcjB,EAD1C,CAAAmC,UAAA,UAAAnC,EAAA,CAAAiL,WAAA,SAAAlB,GAAA,CAAAzD,UAAA,EAA4B,sBAAiE,YAAAyD,GAAA,CAAA9G,gBAAA,CACxE,oBAAoB,cAAA8G,GAAA,CAAA7G,WAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}