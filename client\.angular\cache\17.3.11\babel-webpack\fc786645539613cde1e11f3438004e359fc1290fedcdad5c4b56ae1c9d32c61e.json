{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i2 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputnumber p-component\": true,\n  \"p-inputnumber-buttons-stacked\": a0,\n  \"p-inputnumber-buttons-horizontal\": a1,\n  \"p-inputnumber-buttons-vertical\": a2\n});\nconst _c2 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-up\": true\n});\nconst _c3 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-down\": true\n});\nfunction InputNumber_ng_container_3_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 8);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_span_2_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 6)(2, InputNumber_ng_container_3_span_2_Template, 2, 2, \"span\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_span_4_ng_container_3_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_span_4_ng_container_6_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(2, InputNumber_span_4_span_2_Template, 1, 2, \"span\", 13)(3, InputNumber_span_4_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(5, InputNumber_span_4_span_5_Template, 1, 2, \"span\", 13)(6, InputNumber_span_4_ng_container_6_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"buttonGroup\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(17, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(18, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", ctx_r2.decrementbutton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nfunction InputNumber_button_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_5_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_5_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_button_5_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_5_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_5_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_5_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_5_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_5_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_5_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_5_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n  }\n}\nfunction InputNumber_button_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_6_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_6_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_button_6_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_6_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_6_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_6_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_6_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_6_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_6_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"decrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputNumber),\n  multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\nclass InputNumber {\n  document;\n  el;\n  cd;\n  injector;\n  /**\n   * Displays spinner buttons.\n   * @group Props\n   */\n  showButtons = false;\n  /**\n   * Whether to format the value.\n   * @group Props\n   */\n  format = true;\n  /**\n   * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n   * @group Props\n   */\n  buttonLayout = 'stacked';\n  /**\n   * Identifier of the focus input to match a label defined for the component.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Advisory information to display on input.\n   * @group Props\n   */\n  placeholder;\n  /**\n   * Size of the input field.\n   * @group Props\n   */\n  size;\n  /**\n   * Maximum number of character allows in the input field.\n   * @group Props\n   */\n  maxlength;\n  /**\n   * Specifies tab order of the element.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Title text of the input text.\n   * @group Props\n   */\n  title;\n  /**\n   * Specifies one or more IDs in the DOM that labels the input field.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Used to define a string that labels the input element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Used to indicate that user input is required on an element before a form can be submitted.\n   * @group Props\n   */\n  ariaRequired;\n  /**\n   * Name of the input field.\n   * @group Props\n   */\n  name;\n  /**\n   * Indicates that whether the input field is required.\n   * @group Props\n   */\n  required;\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  autocomplete;\n  /**\n   * Mininum boundary value.\n   * @group Props\n   */\n  min;\n  /**\n   * Maximum boundary value.\n   * @group Props\n   */\n  max;\n  /**\n   * Style class of the increment button.\n   * @group Props\n   */\n  incrementButtonClass;\n  /**\n   * Style class of the decrement button.\n   * @group Props\n   */\n  decrementButtonClass;\n  /**\n   * Style class of the increment button.\n   * @group Props\n   */\n  incrementButtonIcon;\n  /**\n   * Style class of the decrement button.\n   * @group Props\n   */\n  decrementButtonIcon;\n  /**\n   * When present, it specifies that an input field is read-only.\n   * @group Props\n   */\n  readonly = false;\n  /**\n   * Step factor to increment/decrement the value.\n   * @group Props\n   */\n  step = 1;\n  /**\n   * Determines whether the input field is empty.\n   * @group Props\n   */\n  allowEmpty = true;\n  /**\n   * Locale to be used in formatting.\n   * @group Props\n   */\n  locale;\n  /**\n   * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n   * @group Props\n   */\n  localeMatcher;\n  /**\n   * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n   * @group Props\n   */\n  mode = 'decimal';\n  /**\n   * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n   * @group Props\n   */\n  currency;\n  /**\n   * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n   * @group Props\n   */\n  currencyDisplay;\n  /**\n   * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n   * @group Props\n   */\n  useGrouping = true;\n  /**\n   * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n   * @group Props\n   */\n  minFractionDigits;\n  /**\n   * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n   * @group Props\n   */\n  maxFractionDigits;\n  /**\n   * Text to display before the value.\n   * @group Props\n   */\n  prefix;\n  /**\n   * Text to display after the value.\n   * @group Props\n   */\n  suffix;\n  /**\n   * Inline style of the input field.\n   * @group Props\n   */\n  inputStyle;\n  /**\n   * Style class of the input field.\n   * @group Props\n   */\n  inputStyleClass;\n  /**\n   * When enabled, a clear icon is displayed to clear the value.\n   * @group Props\n   */\n  showClear = false;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(disabled) {\n    if (disabled) this.focused = false;\n    this._disabled = disabled;\n    if (this.timer) this.clearTimer();\n  }\n  /**\n   * Callback to invoke on input.\n   * @param {InputNumberInputEvent} event - Custom input event.\n   * @group Emits\n   */\n  onInput = new EventEmitter();\n  /**\n   * Callback to invoke when the component receives focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to invoke when the component loses focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Callback to invoke on input key press.\n   * @param {KeyboardEvent} event - Keyboard event.\n   * @group Emits\n   */\n  onKeyDown = new EventEmitter();\n  /**\n   * Callback to invoke when clear token is clicked.\n   * @group Emits\n   */\n  onClear = new EventEmitter();\n  input;\n  templates;\n  clearIconTemplate;\n  incrementButtonIconTemplate;\n  decrementButtonIconTemplate;\n  value;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  focused;\n  initialized;\n  groupChar = '';\n  prefixChar = '';\n  suffixChar = '';\n  isSpecialChar;\n  timer;\n  lastValue;\n  _numeral;\n  numberFormat;\n  _decimal;\n  _group;\n  _minusSign;\n  _currency;\n  _prefix;\n  _suffix;\n  _index;\n  _disabled;\n  ngControl = null;\n  constructor(document, el, cd, injector) {\n    this.document = document;\n    this.el = el;\n    this.cd = cd;\n    this.injector = injector;\n  }\n  ngOnChanges(simpleChange) {\n    const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n    if (props.some(p => !!simpleChange[p])) {\n      this.updateConstructParser();\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'clearicon':\n          this.clearIconTemplate = item.template;\n          break;\n        case 'incrementbuttonicon':\n          this.incrementButtonIconTemplate = item.template;\n          break;\n        case 'decrementbuttonicon':\n          this.decrementButtonIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnInit() {\n    this.ngControl = this.injector.get(NgControl, null, {\n      optional: true\n    });\n    this.constructParser();\n    this.initialized = true;\n  }\n  getOptions() {\n    return {\n      localeMatcher: this.localeMatcher,\n      style: this.mode,\n      currency: this.currency,\n      currencyDisplay: this.currencyDisplay,\n      useGrouping: this.useGrouping,\n      minimumFractionDigits: this.minFractionDigits,\n      maximumFractionDigits: this.maxFractionDigits\n    };\n  }\n  constructParser() {\n    this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n    const numerals = [...new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    }).format(9876543210)].reverse();\n    const index = new Map(numerals.map((d, i) => [d, i]));\n    this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n    this._group = this.getGroupingExpression();\n    this._minusSign = this.getMinusSignExpression();\n    this._currency = this.getCurrencyExpression();\n    this._decimal = this.getDecimalExpression();\n    this._suffix = this.getSuffixExpression();\n    this._prefix = this.getPrefixExpression();\n    this._index = d => index.get(d);\n  }\n  updateConstructParser() {\n    if (this.initialized) {\n      this.constructParser();\n    }\n  }\n  escapeRegExp(text) {\n    return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n  }\n  getDecimalExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      ...this.getOptions(),\n      useGrouping: false\n    });\n    return new RegExp(`[${formatter.format(1.1).replace(this._currency, '').trim().replace(this._numeral, '')}]`, 'g');\n  }\n  getGroupingExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: true\n    });\n    this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n    return new RegExp(`[${this.groupChar}]`, 'g');\n  }\n  getMinusSignExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    });\n    return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n  }\n  getCurrencyExpression() {\n    if (this.currency) {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: 'currency',\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n    }\n    return new RegExp(`[]`, 'g');\n  }\n  getPrefixExpression() {\n    if (this.prefix) {\n      this.prefixChar = this.prefix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay\n      });\n      this.prefixChar = formatter.format(1).split('1')[0];\n    }\n    return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n  }\n  getSuffixExpression() {\n    if (this.suffix) {\n      this.suffixChar = this.suffix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      this.suffixChar = formatter.format(1).split('1')[1];\n    }\n    return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n  }\n  formatValue(value) {\n    if (value != null) {\n      if (value === '-') {\n        // Minus sign\n        return value;\n      }\n      if (this.format) {\n        let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n        let formattedValue = formatter.format(value);\n        if (this.prefix) {\n          formattedValue = this.prefix + formattedValue;\n        }\n        if (this.suffix) {\n          formattedValue = formattedValue + this.suffix;\n        }\n        return formattedValue;\n      }\n      return value.toString();\n    }\n    return '';\n  }\n  parseValue(text) {\n    let filteredText = text.replace(this._suffix, '').replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '').replace(this._group, '').replace(this._minusSign, '-').replace(this._decimal, '.').replace(this._numeral, this._index);\n    if (filteredText) {\n      if (filteredText === '-')\n        // Minus sign\n        return filteredText;\n      let parsedValue = +filteredText;\n      return isNaN(parsedValue) ? null : parsedValue;\n    }\n    return null;\n  }\n  repeat(event, interval, dir) {\n    if (this.readonly) {\n      return;\n    }\n    let i = interval || 500;\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(event, 40, dir);\n    }, i);\n    this.spin(event, dir);\n  }\n  spin(event, dir) {\n    let step = this.step * dir;\n    let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n    let newValue = this.validateValue(currentValue + step);\n    if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n      return;\n    }\n    this.updateInput(newValue, null, 'spin', null);\n    this.updateModel(event, newValue);\n    this.handleOnInput(event, currentValue, newValue);\n  }\n  clear() {\n    this.value = null;\n    this.onModelChange(this.value);\n    this.onClear.emit();\n  }\n  onUpButtonMouseDown(event) {\n    if (event.button === 2) {\n      this.clearTimer();\n      return;\n    }\n    if (!this.disabled) {\n      this.input?.nativeElement.focus();\n      this.repeat(event, null, 1);\n      event.preventDefault();\n    }\n  }\n  onUpButtonMouseUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onUpButtonMouseLeave() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onUpButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, 1);\n    }\n  }\n  onUpButtonKeyUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonMouseDown(event) {\n    if (event.button === 2) {\n      this.clearTimer();\n      return;\n    }\n    if (!this.disabled) {\n      this.input?.nativeElement.focus();\n      this.repeat(event, null, -1);\n      event.preventDefault();\n    }\n  }\n  onDownButtonMouseUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonMouseLeave() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonKeyUp() {\n    if (!this.disabled) {\n      this.clearTimer();\n    }\n  }\n  onDownButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, -1);\n    }\n  }\n  onUserInput(event) {\n    if (this.readonly) {\n      return;\n    }\n    if (this.isSpecialChar) {\n      event.target.value = this.lastValue;\n    }\n    this.isSpecialChar = false;\n  }\n  onInputKeyDown(event) {\n    if (this.readonly) {\n      return;\n    }\n    this.lastValue = event.target.value;\n    if (event.shiftKey || event.altKey) {\n      this.isSpecialChar = true;\n      return;\n    }\n    let selectionStart = event.target.selectionStart;\n    let selectionEnd = event.target.selectionEnd;\n    let inputValue = event.target.value;\n    let newValueStr = null;\n    if (event.altKey) {\n      event.preventDefault();\n    }\n    switch (event.code) {\n      case 'ArrowUp':\n        this.spin(event, 1);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        this.spin(event, -1);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        if (!this.isNumeralChar(inputValue.charAt(selectionStart - 1))) {\n          event.preventDefault();\n        }\n        break;\n      case 'ArrowRight':\n        if (!this.isNumeralChar(inputValue.charAt(selectionStart))) {\n          event.preventDefault();\n        }\n        break;\n      case 'Tab':\n      case 'Enter':\n        newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        this.input.nativeElement.value = this.formatValue(newValueStr);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n        this.updateModel(event, newValueStr);\n        break;\n      case 'Backspace':\n        {\n          event.preventDefault();\n          if (selectionStart === selectionEnd) {\n            const deleteChar = inputValue.charAt(selectionStart - 1);\n            const {\n              decimalCharIndex,\n              decimalCharIndexWithoutPrefix\n            } = this.getDecimalCharIndexes(inputValue);\n            if (this.isNumeralChar(deleteChar)) {\n              const decimalLength = this.getDecimalLength(inputValue);\n              if (this._group.test(deleteChar)) {\n                this._group.lastIndex = 0;\n                newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n              } else if (this._decimal.test(deleteChar)) {\n                this._decimal.lastIndex = 0;\n                if (decimalLength) {\n                  this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                } else {\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                }\n              } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n              } else if (decimalCharIndexWithoutPrefix === 1) {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n              }\n            } else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n              newValueStr = inputValue.slice(1);\n            }\n            this.updateValue(event, newValueStr, null, 'delete-single');\n          } else {\n            newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n            this.updateValue(event, newValueStr, null, 'delete-range');\n          }\n          break;\n        }\n      case 'Delete':\n        event.preventDefault();\n        if (selectionStart === selectionEnd) {\n          const deleteChar = inputValue.charAt(selectionStart);\n          const {\n            decimalCharIndex,\n            decimalCharIndexWithoutPrefix\n          } = this.getDecimalCharIndexes(inputValue);\n          if (this.isNumeralChar(deleteChar)) {\n            const decimalLength = this.getDecimalLength(inputValue);\n            if (this._group.test(deleteChar)) {\n              this._group.lastIndex = 0;\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n            } else if (this._decimal.test(deleteChar)) {\n              this._decimal.lastIndex = 0;\n              if (decimalLength) {\n                this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n              }\n            } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n              const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n              newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n            } else if (decimalCharIndexWithoutPrefix === 1) {\n              newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n              newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n            } else {\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n            }\n          }\n          this.updateValue(event, newValueStr, null, 'delete-back-single');\n        } else {\n          newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, null, 'delete-range');\n        }\n        break;\n      case 'Home':\n        if (this.min) {\n          this.updateModel(event, this.min);\n          event.preventDefault();\n        }\n        break;\n      case 'End':\n        if (this.max) {\n          this.updateModel(event, this.max);\n          event.preventDefault();\n        }\n        break;\n      default:\n        break;\n    }\n    this.onKeyDown.emit(event);\n  }\n  onInputKeyPress(event) {\n    if (this.readonly) {\n      return;\n    }\n    let code = event.which || event.keyCode;\n    let char = String.fromCharCode(code);\n    const isDecimalSign = this.isDecimalSign(char);\n    const isMinusSign = this.isMinusSign(char);\n    if (code != 13) {\n      event.preventDefault();\n    }\n    const newValue = this.parseValue(this.input.nativeElement.value + char);\n    const newValueStr = newValue != null ? newValue.toString() : '';\n    if (this.maxlength && newValueStr.length > this.maxlength) {\n      return;\n    }\n    if (48 <= code && code <= 57 || isMinusSign || isDecimalSign) {\n      this.insert(event, char, {\n        isDecimalSign,\n        isMinusSign\n      });\n    }\n  }\n  onPaste(event) {\n    if (!this.disabled && !this.readonly) {\n      event.preventDefault();\n      let data = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n      if (data) {\n        if (this.maxlength) {\n          data = data.toString().substring(0, this.maxlength);\n        }\n        let filteredData = this.parseValue(data);\n        if (filteredData != null) {\n          this.insert(event, filteredData.toString());\n        }\n      }\n    }\n  }\n  allowMinusSign() {\n    return this.min == null || this.min < 0;\n  }\n  isMinusSign(char) {\n    if (this._minusSign.test(char) || char === '-') {\n      this._minusSign.lastIndex = 0;\n      return true;\n    }\n    return false;\n  }\n  isDecimalSign(char) {\n    if (this._decimal.test(char)) {\n      this._decimal.lastIndex = 0;\n      return true;\n    }\n    return false;\n  }\n  isDecimalMode() {\n    return this.mode === 'decimal';\n  }\n  getDecimalCharIndexes(val) {\n    let decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const filteredVal = val.replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '');\n    const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      decimalCharIndexWithoutPrefix\n    };\n  }\n  getCharIndexes(val) {\n    const decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const minusCharIndex = val.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n    const suffixCharIndex = val.search(this._suffix);\n    this._suffix.lastIndex = 0;\n    const currencyCharIndex = val.search(this._currency);\n    this._currency.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    };\n  }\n  insert(event, text, sign = {\n    isDecimalSign: false,\n    isMinusSign: false\n  }) {\n    const minusCharIndexOnText = text.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n    if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n      return;\n    }\n    let selectionStart = this.input?.nativeElement.selectionStart;\n    let selectionEnd = this.input?.nativeElement.selectionEnd;\n    let inputValue = this.input?.nativeElement.value.trim();\n    const {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    } = this.getCharIndexes(inputValue);\n    let newValueStr;\n    if (sign.isMinusSign) {\n      if (selectionStart === 0) {\n        newValueStr = inputValue;\n        if (minusCharIndex === -1 || selectionEnd !== 0) {\n          newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n        }\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else if (sign.isDecimalSign) {\n      if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n        this.updateValue(event, inputValue, text, 'insert');\n      } else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      } else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else {\n      const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n      const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n      if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n        if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n          const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n          newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n          this.updateValue(event, newValueStr, text, operation);\n        }\n      } else {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, operation);\n      }\n    }\n  }\n  insertText(value, text, start, end) {\n    let textSplit = text === '.' ? text : text.split('.');\n    if (textSplit.length === 2) {\n      const decimalCharIndex = value.slice(start, end).search(this._decimal);\n      this._decimal.lastIndex = 0;\n      return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n    } else if (end - start === value.length) {\n      return this.formatValue(text);\n    } else if (start === 0) {\n      return text + value.slice(end);\n    } else if (end === value.length) {\n      return value.slice(0, start) + text;\n    } else {\n      return value.slice(0, start) + text + value.slice(end);\n    }\n  }\n  deleteRange(value, start, end) {\n    let newValueStr;\n    if (end - start === value.length) newValueStr = '';else if (start === 0) newValueStr = value.slice(end);else if (end === value.length) newValueStr = value.slice(0, start);else newValueStr = value.slice(0, start) + value.slice(end);\n    return newValueStr;\n  }\n  initCursor() {\n    let selectionStart = this.input?.nativeElement.selectionStart;\n    let inputValue = this.input?.nativeElement.value;\n    let valueLength = inputValue.length;\n    let index = null;\n    // remove prefix\n    let prefixLength = (this.prefixChar || '').length;\n    inputValue = inputValue.replace(this._prefix, '');\n    selectionStart = selectionStart - prefixLength;\n    let char = inputValue.charAt(selectionStart);\n    if (this.isNumeralChar(char)) {\n      return selectionStart + prefixLength;\n    }\n    //left\n    let i = selectionStart - 1;\n    while (i >= 0) {\n      char = inputValue.charAt(i);\n      if (this.isNumeralChar(char)) {\n        index = i + prefixLength;\n        break;\n      } else {\n        i--;\n      }\n    }\n    if (index !== null) {\n      this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n    } else {\n      i = selectionStart;\n      while (i < valueLength) {\n        char = inputValue.charAt(i);\n        if (this.isNumeralChar(char)) {\n          index = i + prefixLength;\n          break;\n        } else {\n          i++;\n        }\n      }\n      if (index !== null) {\n        this.input?.nativeElement.setSelectionRange(index, index);\n      }\n    }\n    return index || 0;\n  }\n  onInputClick() {\n    const currentValue = this.input?.nativeElement.value;\n    if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n      this.initCursor();\n    }\n  }\n  isNumeralChar(char) {\n    if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n      this.resetRegex();\n      return true;\n    }\n    return false;\n  }\n  resetRegex() {\n    this._numeral.lastIndex = 0;\n    this._decimal.lastIndex = 0;\n    this._group.lastIndex = 0;\n    this._minusSign.lastIndex = 0;\n  }\n  updateValue(event, valueStr, insertedValueStr, operation) {\n    let currentValue = this.input?.nativeElement.value;\n    let newValue = null;\n    if (valueStr != null) {\n      newValue = this.parseValue(valueStr);\n      newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n      this.updateInput(newValue, insertedValueStr, operation, valueStr);\n      this.handleOnInput(event, currentValue, newValue);\n    }\n  }\n  handleOnInput(event, currentValue, newValue) {\n    if (this.isValueChanged(currentValue, newValue)) {\n      this.input.nativeElement.value = this.formatValue(newValue);\n      this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n      this.updateModel(event, newValue);\n      this.onInput.emit({\n        originalEvent: event,\n        value: newValue,\n        formattedValue: currentValue\n      });\n    }\n  }\n  isValueChanged(currentValue, newValue) {\n    if (newValue === null && currentValue !== null) {\n      return true;\n    }\n    if (newValue != null) {\n      let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n      return newValue !== parsedCurrentValue;\n    }\n    return false;\n  }\n  validateValue(value) {\n    if (value === '-' || value == null) {\n      return null;\n    }\n    if (this.min != null && value < this.min) {\n      return this.min;\n    }\n    if (this.max != null && value > this.max) {\n      return this.max;\n    }\n    return value;\n  }\n  updateInput(value, insertedValueStr, operation, valueStr) {\n    insertedValueStr = insertedValueStr || '';\n    let inputValue = this.input?.nativeElement.value;\n    let newValue = this.formatValue(value);\n    let currentLength = inputValue.length;\n    if (newValue !== valueStr) {\n      newValue = this.concatValues(newValue, valueStr);\n    }\n    if (currentLength === 0) {\n      this.input.nativeElement.value = newValue;\n      this.input.nativeElement.setSelectionRange(0, 0);\n      const index = this.initCursor();\n      const selectionEnd = index + insertedValueStr.length;\n      this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n    } else {\n      let selectionStart = this.input.nativeElement.selectionStart;\n      let selectionEnd = this.input.nativeElement.selectionEnd;\n      if (this.maxlength && newValue.length > this.maxlength) {\n        newValue = newValue.slice(0, this.maxlength);\n        selectionStart = Math.min(selectionStart, this.maxlength);\n        selectionEnd = Math.min(selectionEnd, this.maxlength);\n      }\n      if (this.maxlength && this.maxlength < newValue.length) {\n        return;\n      }\n      this.input.nativeElement.value = newValue;\n      let newLength = newValue.length;\n      if (operation === 'range-insert') {\n        const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n        const startValueStr = startValue !== null ? startValue.toString() : '';\n        const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n        const sRegex = new RegExp(startExpr, 'g');\n        sRegex.test(newValue);\n        const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n        const tRegex = new RegExp(tExpr, 'g');\n        tRegex.test(newValue.slice(sRegex.lastIndex));\n        selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (newLength === currentLength) {\n        if (operation === 'insert' || operation === 'delete-back-single') this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);else if (operation === 'delete-single') this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);else if (operation === 'delete-range' || operation === 'spin') this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (operation === 'delete-back-single') {\n        let prevChar = inputValue.charAt(selectionEnd - 1);\n        let nextChar = inputValue.charAt(selectionEnd);\n        let diff = currentLength - newLength;\n        let isGroupChar = this._group.test(nextChar);\n        if (isGroupChar && diff === 1) {\n          selectionEnd += 1;\n        } else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n          selectionEnd += -1 * diff + 1;\n        }\n        this._group.lastIndex = 0;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (inputValue === '-' && operation === 'insert') {\n        this.input.nativeElement.setSelectionRange(0, 0);\n        const index = this.initCursor();\n        const selectionEnd = index + insertedValueStr.length + 1;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else {\n        selectionEnd = selectionEnd + (newLength - currentLength);\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      }\n    }\n    this.input.nativeElement.setAttribute('aria-valuenow', value);\n  }\n  concatValues(val1, val2) {\n    if (val1 && val2) {\n      let decimalCharIndex = val2.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      if (this.suffixChar) {\n        return val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n      } else {\n        return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n      }\n    }\n    return val1;\n  }\n  getDecimalLength(value) {\n    if (value) {\n      const valueSplit = value.split(this._decimal);\n      if (valueSplit.length === 2) {\n        return valueSplit[1].replace(this._suffix, '').trim().replace(/\\s/g, '').replace(this._currency, '').length;\n      }\n    }\n    return 0;\n  }\n  onInputFocus(event) {\n    this.focused = true;\n    this.onFocus.emit(event);\n  }\n  onInputBlur(event) {\n    this.focused = false;\n    let newValue = this.validateValue(this.parseValue(this.input.nativeElement.value));\n    this.onBlur.emit(event);\n    this.input.nativeElement.value = this.formatValue(newValue);\n    this.input.nativeElement.setAttribute('aria-valuenow', newValue);\n    this.updateModel(event, newValue);\n  }\n  formattedValue() {\n    const val = !this.value && !this.allowEmpty ? 0 : this.value;\n    return this.formatValue(val);\n  }\n  updateModel(event, value) {\n    const isBlurUpdateOnMode = this.ngControl?.control?.updateOn === 'blur';\n    if (this.value !== value) {\n      this.value = value;\n      if (!(isBlurUpdateOnMode && this.focused)) {\n        this.onModelChange(value);\n      }\n    } else if (isBlurUpdateOnMode) {\n      this.onModelChange(value);\n    }\n    this.onModelTouched();\n  }\n  writeValue(value) {\n    this.value = value;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n  get filled() {\n    return this.value != null && this.value.toString().length > 0;\n  }\n  clearTimer() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  }\n  getFormatter() {\n    return this.numberFormat;\n  }\n  static ɵfac = function InputNumber_Factory(t) {\n    return new (t || InputNumber)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputNumber,\n    selectors: [[\"p-inputNumber\"]],\n    contentQueries: function InputNumber_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function InputNumber_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n    hostVars: 6,\n    hostBindings: function InputNumber_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused)(\"p-inputnumber-clearable\", ctx.showClear && ctx.buttonLayout != \"vertical\");\n      }\n    },\n    inputs: {\n      showButtons: \"showButtons\",\n      format: \"format\",\n      buttonLayout: \"buttonLayout\",\n      inputId: \"inputId\",\n      styleClass: \"styleClass\",\n      style: \"style\",\n      placeholder: \"placeholder\",\n      size: \"size\",\n      maxlength: \"maxlength\",\n      tabindex: \"tabindex\",\n      title: \"title\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      ariaLabel: \"ariaLabel\",\n      ariaRequired: \"ariaRequired\",\n      name: \"name\",\n      required: \"required\",\n      autocomplete: \"autocomplete\",\n      min: \"min\",\n      max: \"max\",\n      incrementButtonClass: \"incrementButtonClass\",\n      decrementButtonClass: \"decrementButtonClass\",\n      incrementButtonIcon: \"incrementButtonIcon\",\n      decrementButtonIcon: \"decrementButtonIcon\",\n      readonly: \"readonly\",\n      step: \"step\",\n      allowEmpty: \"allowEmpty\",\n      locale: \"locale\",\n      localeMatcher: \"localeMatcher\",\n      mode: \"mode\",\n      currency: \"currency\",\n      currencyDisplay: \"currencyDisplay\",\n      useGrouping: \"useGrouping\",\n      minFractionDigits: \"minFractionDigits\",\n      maxFractionDigits: \"maxFractionDigits\",\n      prefix: \"prefix\",\n      suffix: \"suffix\",\n      inputStyle: \"inputStyle\",\n      inputStyleClass: \"inputStyleClass\",\n      showClear: \"showClear\",\n      disabled: \"disabled\"\n    },\n    outputs: {\n      onInput: \"onInput\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\",\n      onKeyDown: \"onKeyDown\",\n      onClear: \"onClear\"\n    },\n    features: [i0.ɵɵProvidersFeature([INPUTNUMBER_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 39,\n    consts: [[\"input\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"pInputText\", \"\", \"role\", \"spinbutton\", \"inputmode\", \"decimal\", 3, \"input\", \"keydown\", \"keypress\", \"paste\", \"click\", \"focus\", \"blur\", \"ngClass\", \"ngStyle\", \"value\", \"disabled\", \"readonly\"], [4, \"ngIf\"], [\"class\", \"p-inputnumber-button-group\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-button-icon-only\", \"tabindex\", \"-1\", 3, \"ngClass\", \"class\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", 4, \"ngIf\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-inputnumber-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"ngClass\"], [1, \"p-inputnumber-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [1, \"p-inputnumber-button-group\"], [\"type\", \"button\", \"pButton\", \"\", \"tabindex\", \"-1\", 1, \"p-button-icon-only\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", \"ngClass\", \"disabled\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function InputNumber_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n        i0.ɵɵlistener(\"input\", function InputNumber_Template_input_input_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onUserInput($event));\n        })(\"keydown\", function InputNumber_Template_input_keydown_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputKeyDown($event));\n        })(\"keypress\", function InputNumber_Template_input_keypress_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputKeyPress($event));\n        })(\"paste\", function InputNumber_Template_input_paste_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPaste($event));\n        })(\"click\", function InputNumber_Template_input_click_1_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputClick());\n        })(\"focus\", function InputNumber_Template_input_focus_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputFocus($event));\n        })(\"blur\", function InputNumber_Template_input_blur_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onInputBlur($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, InputNumber_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, InputNumber_span_4_Template, 7, 19, \"span\", 4)(5, InputNumber_button_5_Template, 3, 9, \"button\", 5)(6, InputNumber_button_6_Template, 3, 9, \"button\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(35, _c1, ctx.showButtons && ctx.buttonLayout === \"stacked\", ctx.showButtons && ctx.buttonLayout === \"horizontal\", ctx.showButtons && ctx.buttonLayout === \"vertical\"))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"inputnumber\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.inputStyleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-input\")(\"ngStyle\", ctx.inputStyle)(\"value\", ctx.formattedValue())(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly);\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-valuemin\", ctx.min)(\"aria-valuemax\", ctx.max)(\"aria-valuenow\", ctx.value)(\"placeholder\", ctx.placeholder)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"title\", ctx.title)(\"size\", ctx.size)(\"name\", ctx.name)(\"autocomplete\", ctx.autocomplete)(\"maxlength\", ctx.maxlength)(\"tabindex\", ctx.tabindex)(\"aria-required\", ctx.ariaRequired)(\"required\", ctx.required)(\"min\", ctx.min)(\"max\", ctx.max)(\"data-pc-section\", \"input\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.buttonLayout != \"vertical\" && ctx.showClear && ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout === \"stacked\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.InputText, i3.ButtonDirective, TimesIcon, AngleUpIcon, AngleDownIcon],\n    styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumber, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputNumber',\n      template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [INPUTNUMBER_VALUE_ACCESSOR],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n      },\n      styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Injector\n  }], {\n    showButtons: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    buttonLayout: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    maxlength: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaRequired: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    autocomplete: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    incrementButtonClass: [{\n      type: Input\n    }],\n    decrementButtonClass: [{\n      type: Input\n    }],\n    incrementButtonIcon: [{\n      type: Input\n    }],\n    decrementButtonIcon: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    allowEmpty: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    localeMatcher: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    currency: [{\n      type: Input\n    }],\n    currencyDisplay: [{\n      type: Input\n    }],\n    useGrouping: [{\n      type: Input\n    }],\n    minFractionDigits: [{\n      type: Input\n    }],\n    maxFractionDigits: [{\n      type: Input\n    }],\n    prefix: [{\n      type: Input\n    }],\n    suffix: [{\n      type: Input\n    }],\n    inputStyle: [{\n      type: Input\n    }],\n    inputStyleClass: [{\n      type: Input\n    }],\n    showClear: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    onInput: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onKeyDown: [{\n      type: Output\n    }],\n    onClear: [{\n      type: Output\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass InputNumberModule {\n  static ɵfac = function InputNumberModule_Factory(t) {\n    return new (t || InputNumberModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputNumberModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumberModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon],\n      exports: [InputNumber, SharedModule],\n      declarations: [InputNumber]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };", "map": {"version": 3, "names": ["i1", "DOCUMENT", "CommonModule", "i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "NG_VALUE_ACCESSOR", "NgControl", "PrimeTemplate", "SharedModule", "i3", "ButtonModule", "<PERSON><PERSON><PERSON><PERSON>", "AngleDownIcon", "AngleUpIcon", "TimesIcon", "i2", "InputTextModule", "_c0", "_c1", "a0", "a1", "a2", "_c2", "_c3", "InputNumber_ng_container_3_TimesIcon_1_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "InputNumber_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "clear", "ɵɵelementEnd", "ɵɵproperty", "ɵɵattribute", "InputNumber_ng_container_3_span_2_1_ng_template_0_Template", "InputNumber_ng_container_3_span_2_1_Template", "ɵɵtemplate", "InputNumber_ng_container_3_span_2_Template", "_r4", "InputNumber_ng_container_3_span_2_Template_span_click_0_listener", "ɵɵadvance", "clearIconTemplate", "InputNumber_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "InputNumber_span_4_span_2_Template", "ɵɵelement", "incrementButtonIcon", "InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template", "InputNumber_span_4_ng_container_3_2_ng_template_0_Template", "InputNumber_span_4_ng_container_3_2_Template", "InputNumber_span_4_ng_container_3_Template", "incrementButtonIconTemplate", "InputNumber_span_4_span_5_Template", "decrementButtonIcon", "InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template", "InputNumber_span_4_ng_container_6_2_ng_template_0_Template", "InputNumber_span_4_ng_container_6_2_Template", "InputNumber_span_4_ng_container_6_Template", "decrementButtonIconTemplate", "InputNumber_span_4_Template", "_r5", "InputNumber_span_4_Template_button_mousedown_1_listener", "$event", "onUpButtonMouseDown", "InputNumber_span_4_Template_button_mouseup_1_listener", "onUpButtonMouseUp", "InputNumber_span_4_Template_button_mouseleave_1_listener", "onUpButtonMouseLeave", "InputNumber_span_4_Template_button_keydown_1_listener", "onUpButtonKeyDown", "InputNumber_span_4_Template_button_keyup_1_listener", "onUpButtonKeyUp", "InputNumber_span_4_Template_button_mousedown_4_listener", "onDownButtonMouseDown", "InputNumber_span_4_Template_button_mouseup_4_listener", "onDownButtonMouseUp", "InputNumber_span_4_Template_button_mouseleave_4_listener", "onDownButtonMouseLeave", "InputNumber_span_4_Template_button_keydown_4_listener", "onDownButtonKeyDown", "InputNumber_span_4_Template_button_keyup_4_listener", "onDownButtonKeyUp", "ɵɵclassMap", "incrementButtonClass", "ɵɵpureFunction0", "disabled", "decrementButtonClass", "decrementbutton", "InputNumber_button_5_span_1_Template", "InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template", "InputNumber_button_5_ng_container_2_2_ng_template_0_Template", "InputNumber_button_5_ng_container_2_2_Template", "InputNumber_button_5_ng_container_2_Template", "InputNumber_button_5_Template", "_r6", "InputNumber_button_5_Template_button_mousedown_0_listener", "InputNumber_button_5_Template_button_mouseup_0_listener", "InputNumber_button_5_Template_button_mouseleave_0_listener", "InputNumber_button_5_Template_button_keydown_0_listener", "InputNumber_button_5_Template_button_keyup_0_listener", "InputNumber_button_6_span_1_Template", "InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template", "InputNumber_button_6_ng_container_2_2_ng_template_0_Template", "InputNumber_button_6_ng_container_2_2_Template", "InputNumber_button_6_ng_container_2_Template", "InputNumber_button_6_Template", "_r7", "InputNumber_button_6_Template_button_mousedown_0_listener", "InputNumber_button_6_Template_button_mouseup_0_listener", "InputNumber_button_6_Template_button_mouseleave_0_listener", "InputNumber_button_6_Template_button_keydown_0_listener", "InputNumber_button_6_Template_button_keyup_0_listener", "INPUTNUMBER_VALUE_ACCESSOR", "provide", "useExisting", "InputNumber", "multi", "document", "el", "cd", "injector", "showButtons", "format", "buttonLayout", "inputId", "styleClass", "style", "placeholder", "size", "maxlength", "tabindex", "title", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaRequired", "name", "required", "autocomplete", "min", "max", "readonly", "step", "allowEmpty", "locale", "localeMatcher", "mode", "currency", "currencyDisplay", "useGrouping", "minFractionDigits", "maxFractionDigits", "prefix", "suffix", "inputStyle", "inputStyleClass", "showClear", "_disabled", "focused", "timer", "clearTimer", "onInput", "onFocus", "onBlur", "onKeyDown", "onClear", "input", "templates", "value", "onModelChange", "onModelTouched", "initialized", "groupChar", "prefixChar", "suffixChar", "isSpecialChar", "lastValue", "_numeral", "numberFormat", "_decimal", "_group", "_minusSign", "_currency", "_prefix", "_suffix", "_index", "ngControl", "constructor", "ngOnChanges", "simpleChange", "props", "some", "p", "updateConstructParser", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "ngOnInit", "get", "optional", "<PERSON><PERSON><PERSON><PERSON>", "getOptions", "minimumFractionDigits", "maximumFractionDigits", "Intl", "NumberFormat", "numerals", "reverse", "index", "Map", "map", "d", "i", "RegExp", "join", "getGroupingExpression", "getMinusSignExpression", "getCurrencyExpression", "getDecimalExpression", "getSuffixExpression", "getPrefixExpression", "escapeRegExp", "text", "replace", "formatter", "trim", "char<PERSON>t", "split", "formatValue", "formattedValue", "toString", "parseValue", "filteredText", "parsedValue", "isNaN", "repeat", "event", "interval", "dir", "setTimeout", "spin", "currentValue", "nativeElement", "newValue", "validate<PERSON><PERSON>ue", "length", "updateInput", "updateModel", "handleOnInput", "emit", "button", "focus", "preventDefault", "keyCode", "onUserInput", "target", "onInputKeyDown", "shift<PERSON>ey", "altKey", "selectionStart", "selectionEnd", "inputValue", "newValueStr", "code", "isNumeralChar", "setAttribute", "deleteChar", "decimalCharIndex", "decimalCharIndexWithoutPrefix", "getDecimalCharIndexes", "decimalLength", "getDecimalLength", "test", "lastIndex", "slice", "setSelectionRange", "insertedText", "isDecimalMode", "search", "updateValue", "deleteRange", "onInputKeyPress", "which", "char", "String", "fromCharCode", "isDecimalSign", "isMinusSign", "insert", "onPaste", "data", "clipboardData", "defaultView", "getData", "substring", "filteredData", "allowMinusSign", "val", "filteredVal", "getCharIndexes", "minusCharIndex", "suffixCharIndex", "currencyCharIndex", "sign", "minusCharIndexOnText", "insertText", "resolvedOptions", "operation", "charIndex", "start", "end", "textSplit", "initCursor", "valueLength", "prefixLength", "onInputClick", "getSelection", "resetRegex", "valueStr", "insertedValueStr", "isValueChanged", "originalEvent", "parsedCurrentValue", "<PERSON><PERSON><PERSON><PERSON>", "concat<PERSON><PERSON><PERSON>", "Math", "<PERSON><PERSON><PERSON><PERSON>", "startValue", "startValueStr", "startExpr", "sRegex", "tExpr", "tRegex", "prevChar", "nextChar", "diff", "isGroupChar", "val1", "val2", "valueSplit", "onInputFocus", "onInputBlur", "isBlurUpdateOnMode", "control", "updateOn", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "filled", "clearInterval", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "InputNumber_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "Injector", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "InputNumber_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "InputNumber_Query", "ɵɵviewQuery", "first", "hostAttrs", "hostVars", "hostBindings", "InputNumber_HostBindings", "ɵɵclassProp", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "InputNumber_Template", "_r1", "InputNumber_Template_input_input_1_listener", "InputNumber_Template_input_keydown_1_listener", "InputNumber_Template_input_keypress_1_listener", "InputNumber_Template_input_paste_1_listener", "InputNumber_Template_input_click_1_listener", "InputNumber_Template_input_focus_1_listener", "InputNumber_Template_input_blur_1_listener", "ɵɵpureFunction3", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "InputText", "ButtonDirective", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "providers", "None", "host", "class", "Document", "decorators", "InputNumberModule", "InputNumberModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-inputnumber.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i2 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\n\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputNumber),\n    multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\nclass InputNumber {\n    document;\n    el;\n    cd;\n    injector;\n    /**\n     * Displays spinner buttons.\n     * @group Props\n     */\n    showButtons = false;\n    /**\n     * Whether to format the value.\n     * @group Props\n     */\n    format = true;\n    /**\n     * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n     * @group Props\n     */\n    buttonLayout = 'stacked';\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    size;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    maxlength;\n    /**\n     * Specifies tab order of the element.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Title text of the input text.\n     * @group Props\n     */\n    title;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Used to indicate that user input is required on an element before a form can be submitted.\n     * @group Props\n     */\n    ariaRequired;\n    /**\n     * Name of the input field.\n     * @group Props\n     */\n    name;\n    /**\n     * Indicates that whether the input field is required.\n     * @group Props\n     */\n    required;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    autocomplete;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    min;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    max;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonClass;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonClass;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonIcon;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonIcon;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    readonly = false;\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    step = 1;\n    /**\n     * Determines whether the input field is empty.\n     * @group Props\n     */\n    allowEmpty = true;\n    /**\n     * Locale to be used in formatting.\n     * @group Props\n     */\n    locale;\n    /**\n     * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n     * @group Props\n     */\n    localeMatcher;\n    /**\n     * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n     * @group Props\n     */\n    mode = 'decimal';\n    /**\n     * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n     * @group Props\n     */\n    currency;\n    /**\n     * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n     * @group Props\n     */\n    currencyDisplay;\n    /**\n     * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n     * @group Props\n     */\n    useGrouping = true;\n    /**\n     * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    minFractionDigits;\n    /**\n     * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    maxFractionDigits;\n    /**\n     * Text to display before the value.\n     * @group Props\n     */\n    prefix;\n    /**\n     * Text to display after the value.\n     * @group Props\n     */\n    suffix;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        if (disabled)\n            this.focused = false;\n        this._disabled = disabled;\n        if (this.timer)\n            this.clearTimer();\n    }\n    /**\n     * Callback to invoke on input.\n     * @param {InputNumberInputEvent} event - Custom input event.\n     * @group Emits\n     */\n    onInput = new EventEmitter();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke on input key press.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    onKeyDown = new EventEmitter();\n    /**\n     * Callback to invoke when clear token is clicked.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    input;\n    templates;\n    clearIconTemplate;\n    incrementButtonIconTemplate;\n    decrementButtonIconTemplate;\n    value;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    focused;\n    initialized;\n    groupChar = '';\n    prefixChar = '';\n    suffixChar = '';\n    isSpecialChar;\n    timer;\n    lastValue;\n    _numeral;\n    numberFormat;\n    _decimal;\n    _group;\n    _minusSign;\n    _currency;\n    _prefix;\n    _suffix;\n    _index;\n    _disabled;\n    ngControl = null;\n    constructor(document, el, cd, injector) {\n        this.document = document;\n        this.el = el;\n        this.cd = cd;\n        this.injector = injector;\n    }\n    ngOnChanges(simpleChange) {\n        const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n        if (props.some((p) => !!simpleChange[p])) {\n            this.updateConstructParser();\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n                case 'incrementbuttonicon':\n                    this.incrementButtonIconTemplate = item.template;\n                    break;\n                case 'decrementbuttonicon':\n                    this.decrementButtonIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnInit() {\n        this.ngControl = this.injector.get(NgControl, null, { optional: true });\n        this.constructParser();\n        this.initialized = true;\n    }\n    getOptions() {\n        return {\n            localeMatcher: this.localeMatcher,\n            style: this.mode,\n            currency: this.currency,\n            currencyDisplay: this.currencyDisplay,\n            useGrouping: this.useGrouping,\n            minimumFractionDigits: this.minFractionDigits,\n            maximumFractionDigits: this.maxFractionDigits\n        };\n    }\n    constructParser() {\n        this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n        const numerals = [...new Intl.NumberFormat(this.locale, { useGrouping: false }).format(9876543210)].reverse();\n        const index = new Map(numerals.map((d, i) => [d, i]));\n        this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n        this._group = this.getGroupingExpression();\n        this._minusSign = this.getMinusSignExpression();\n        this._currency = this.getCurrencyExpression();\n        this._decimal = this.getDecimalExpression();\n        this._suffix = this.getSuffixExpression();\n        this._prefix = this.getPrefixExpression();\n        this._index = (d) => index.get(d);\n    }\n    updateConstructParser() {\n        if (this.initialized) {\n            this.constructParser();\n        }\n    }\n    escapeRegExp(text) {\n        return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n    }\n    getDecimalExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { ...this.getOptions(), useGrouping: false });\n        return new RegExp(`[${formatter\n            .format(1.1)\n            .replace(this._currency, '')\n            .trim()\n            .replace(this._numeral, '')}]`, 'g');\n    }\n    getGroupingExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: true });\n        this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n        return new RegExp(`[${this.groupChar}]`, 'g');\n    }\n    getMinusSignExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: false });\n        return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getCurrencyExpression() {\n        if (this.currency) {\n            const formatter = new Intl.NumberFormat(this.locale, { style: 'currency', currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n        }\n        return new RegExp(`[]`, 'g');\n    }\n    getPrefixExpression() {\n        if (this.prefix) {\n            this.prefixChar = this.prefix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay });\n            this.prefixChar = formatter.format(1).split('1')[0];\n        }\n        return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n    }\n    getSuffixExpression() {\n        if (this.suffix) {\n            this.suffixChar = this.suffix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            this.suffixChar = formatter.format(1).split('1')[1];\n        }\n        return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n    }\n    formatValue(value) {\n        if (value != null) {\n            if (value === '-') {\n                // Minus sign\n                return value;\n            }\n            if (this.format) {\n                let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n                let formattedValue = formatter.format(value);\n                if (this.prefix) {\n                    formattedValue = this.prefix + formattedValue;\n                }\n                if (this.suffix) {\n                    formattedValue = formattedValue + this.suffix;\n                }\n                return formattedValue;\n            }\n            return value.toString();\n        }\n        return '';\n    }\n    parseValue(text) {\n        let filteredText = text\n            .replace(this._suffix, '')\n            .replace(this._prefix, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(this._currency, '')\n            .replace(this._group, '')\n            .replace(this._minusSign, '-')\n            .replace(this._decimal, '.')\n            .replace(this._numeral, this._index);\n        if (filteredText) {\n            if (filteredText === '-')\n                // Minus sign\n                return filteredText;\n            let parsedValue = +filteredText;\n            return isNaN(parsedValue) ? null : parsedValue;\n        }\n        return null;\n    }\n    repeat(event, interval, dir) {\n        if (this.readonly) {\n            return;\n        }\n        let i = interval || 500;\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, 40, dir);\n        }, i);\n        this.spin(event, dir);\n    }\n    spin(event, dir) {\n        let step = this.step * dir;\n        let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n        let newValue = this.validateValue(currentValue + step);\n        if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n            return;\n        }\n        this.updateInput(newValue, null, 'spin', null);\n        this.updateModel(event, newValue);\n        this.handleOnInput(event, currentValue, newValue);\n    }\n    clear() {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n    onUpButtonMouseDown(event) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, 1);\n            event.preventDefault();\n        }\n    }\n    onUpButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onUpButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onUpButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, 1);\n        }\n    }\n    onUpButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonMouseDown(event) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, -1);\n            event.preventDefault();\n        }\n    }\n    onDownButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n    onDownButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, -1);\n        }\n    }\n    onUserInput(event) {\n        if (this.readonly) {\n            return;\n        }\n        if (this.isSpecialChar) {\n            event.target.value = this.lastValue;\n        }\n        this.isSpecialChar = false;\n    }\n    onInputKeyDown(event) {\n        if (this.readonly) {\n            return;\n        }\n        this.lastValue = event.target.value;\n        if (event.shiftKey || event.altKey) {\n            this.isSpecialChar = true;\n            return;\n        }\n        let selectionStart = event.target.selectionStart;\n        let selectionEnd = event.target.selectionEnd;\n        let inputValue = event.target.value;\n        let newValueStr = null;\n        if (event.altKey) {\n            event.preventDefault();\n        }\n        switch (event.code) {\n            case 'ArrowUp':\n                this.spin(event, 1);\n                event.preventDefault();\n                break;\n            case 'ArrowDown':\n                this.spin(event, -1);\n                event.preventDefault();\n                break;\n            case 'ArrowLeft':\n                if (!this.isNumeralChar(inputValue.charAt(selectionStart - 1))) {\n                    event.preventDefault();\n                }\n                break;\n            case 'ArrowRight':\n                if (!this.isNumeralChar(inputValue.charAt(selectionStart))) {\n                    event.preventDefault();\n                }\n                break;\n            case 'Tab':\n            case 'Enter':\n                newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n                this.input.nativeElement.value = this.formatValue(newValueStr);\n                this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n                this.updateModel(event, newValueStr);\n                break;\n            case 'Backspace': {\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    const deleteChar = inputValue.charAt(selectionStart - 1);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                        }\n                    }\n                    else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n                        newValueStr = inputValue.slice(1);\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            }\n            case 'Delete':\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    const deleteChar = inputValue.charAt(selectionStart);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                        }\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-back-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            case 'Home':\n                if (this.min) {\n                    this.updateModel(event, this.min);\n                    event.preventDefault();\n                }\n                break;\n            case 'End':\n                if (this.max) {\n                    this.updateModel(event, this.max);\n                    event.preventDefault();\n                }\n                break;\n            default:\n                break;\n        }\n        this.onKeyDown.emit(event);\n    }\n    onInputKeyPress(event) {\n        if (this.readonly) {\n            return;\n        }\n        let code = event.which || event.keyCode;\n        let char = String.fromCharCode(code);\n        const isDecimalSign = this.isDecimalSign(char);\n        const isMinusSign = this.isMinusSign(char);\n        if (code != 13) {\n            event.preventDefault();\n        }\n        const newValue = this.parseValue(this.input.nativeElement.value + char);\n        const newValueStr = newValue != null ? newValue.toString() : '';\n        if (this.maxlength && newValueStr.length > this.maxlength) {\n            return;\n        }\n        if ((48 <= code && code <= 57) || isMinusSign || isDecimalSign) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n        }\n    }\n    onPaste(event) {\n        if (!this.disabled && !this.readonly) {\n            event.preventDefault();\n            let data = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n            if (data) {\n                if (this.maxlength) {\n                    data = data.toString().substring(0, this.maxlength);\n                }\n                let filteredData = this.parseValue(data);\n                if (filteredData != null) {\n                    this.insert(event, filteredData.toString());\n                }\n            }\n        }\n    }\n    allowMinusSign() {\n        return this.min == null || this.min < 0;\n    }\n    isMinusSign(char) {\n        if (this._minusSign.test(char) || char === '-') {\n            this._minusSign.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalSign(char) {\n        if (this._decimal.test(char)) {\n            this._decimal.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalMode() {\n        return this.mode === 'decimal';\n    }\n    getDecimalCharIndexes(val) {\n        let decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const filteredVal = val\n            .replace(this._prefix, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(this._currency, '');\n        const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        return { decimalCharIndex, decimalCharIndexWithoutPrefix };\n    }\n    getCharIndexes(val) {\n        const decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const minusCharIndex = val.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        const suffixCharIndex = val.search(this._suffix);\n        this._suffix.lastIndex = 0;\n        const currencyCharIndex = val.search(this._currency);\n        this._currency.lastIndex = 0;\n        return { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex };\n    }\n    insert(event, text, sign = { isDecimalSign: false, isMinusSign: false }) {\n        const minusCharIndexOnText = text.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n            return;\n        }\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let selectionEnd = this.input?.nativeElement.selectionEnd;\n        let inputValue = this.input?.nativeElement.value.trim();\n        const { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex } = this.getCharIndexes(inputValue);\n        let newValueStr;\n        if (sign.isMinusSign) {\n            if (selectionStart === 0) {\n                newValueStr = inputValue;\n                if (minusCharIndex === -1 || selectionEnd !== 0) {\n                    newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n                }\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else if (sign.isDecimalSign) {\n            if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n                this.updateValue(event, inputValue, text, 'insert');\n            }\n            else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n            else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else {\n            const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n            const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n            if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n                    const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n                    newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n                    this.updateValue(event, newValueStr, text, operation);\n                }\n            }\n            else {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, operation);\n            }\n        }\n    }\n    insertText(value, text, start, end) {\n        let textSplit = text === '.' ? text : text.split('.');\n        if (textSplit.length === 2) {\n            const decimalCharIndex = value.slice(start, end).search(this._decimal);\n            this._decimal.lastIndex = 0;\n            return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n        }\n        else if (end - start === value.length) {\n            return this.formatValue(text);\n        }\n        else if (start === 0) {\n            return text + value.slice(end);\n        }\n        else if (end === value.length) {\n            return value.slice(0, start) + text;\n        }\n        else {\n            return value.slice(0, start) + text + value.slice(end);\n        }\n    }\n    deleteRange(value, start, end) {\n        let newValueStr;\n        if (end - start === value.length)\n            newValueStr = '';\n        else if (start === 0)\n            newValueStr = value.slice(end);\n        else if (end === value.length)\n            newValueStr = value.slice(0, start);\n        else\n            newValueStr = value.slice(0, start) + value.slice(end);\n        return newValueStr;\n    }\n    initCursor() {\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let inputValue = this.input?.nativeElement.value;\n        let valueLength = inputValue.length;\n        let index = null;\n        // remove prefix\n        let prefixLength = (this.prefixChar || '').length;\n        inputValue = inputValue.replace(this._prefix, '');\n        selectionStart = selectionStart - prefixLength;\n        let char = inputValue.charAt(selectionStart);\n        if (this.isNumeralChar(char)) {\n            return selectionStart + prefixLength;\n        }\n        //left\n        let i = selectionStart - 1;\n        while (i >= 0) {\n            char = inputValue.charAt(i);\n            if (this.isNumeralChar(char)) {\n                index = i + prefixLength;\n                break;\n            }\n            else {\n                i--;\n            }\n        }\n        if (index !== null) {\n            this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n        }\n        else {\n            i = selectionStart;\n            while (i < valueLength) {\n                char = inputValue.charAt(i);\n                if (this.isNumeralChar(char)) {\n                    index = i + prefixLength;\n                    break;\n                }\n                else {\n                    i++;\n                }\n            }\n            if (index !== null) {\n                this.input?.nativeElement.setSelectionRange(index, index);\n            }\n        }\n        return index || 0;\n    }\n    onInputClick() {\n        const currentValue = this.input?.nativeElement.value;\n        if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n            this.initCursor();\n        }\n    }\n    isNumeralChar(char) {\n        if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n            this.resetRegex();\n            return true;\n        }\n        return false;\n    }\n    resetRegex() {\n        this._numeral.lastIndex = 0;\n        this._decimal.lastIndex = 0;\n        this._group.lastIndex = 0;\n        this._minusSign.lastIndex = 0;\n    }\n    updateValue(event, valueStr, insertedValueStr, operation) {\n        let currentValue = this.input?.nativeElement.value;\n        let newValue = null;\n        if (valueStr != null) {\n            newValue = this.parseValue(valueStr);\n            newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n            this.updateInput(newValue, insertedValueStr, operation, valueStr);\n            this.handleOnInput(event, currentValue, newValue);\n        }\n    }\n    handleOnInput(event, currentValue, newValue) {\n        if (this.isValueChanged(currentValue, newValue)) {\n            this.input.nativeElement.value = this.formatValue(newValue);\n            this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n            this.updateModel(event, newValue);\n            this.onInput.emit({ originalEvent: event, value: newValue, formattedValue: currentValue });\n        }\n    }\n    isValueChanged(currentValue, newValue) {\n        if (newValue === null && currentValue !== null) {\n            return true;\n        }\n        if (newValue != null) {\n            let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n            return newValue !== parsedCurrentValue;\n        }\n        return false;\n    }\n    validateValue(value) {\n        if (value === '-' || value == null) {\n            return null;\n        }\n        if (this.min != null && value < this.min) {\n            return this.min;\n        }\n        if (this.max != null && value > this.max) {\n            return this.max;\n        }\n        return value;\n    }\n    updateInput(value, insertedValueStr, operation, valueStr) {\n        insertedValueStr = insertedValueStr || '';\n        let inputValue = this.input?.nativeElement.value;\n        let newValue = this.formatValue(value);\n        let currentLength = inputValue.length;\n        if (newValue !== valueStr) {\n            newValue = this.concatValues(newValue, valueStr);\n        }\n        if (currentLength === 0) {\n            this.input.nativeElement.value = newValue;\n            this.input.nativeElement.setSelectionRange(0, 0);\n            const index = this.initCursor();\n            const selectionEnd = index + insertedValueStr.length;\n            this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        }\n        else {\n            let selectionStart = this.input.nativeElement.selectionStart;\n            let selectionEnd = this.input.nativeElement.selectionEnd;\n            if (this.maxlength && newValue.length > this.maxlength) {\n                newValue = newValue.slice(0, this.maxlength);\n                selectionStart = Math.min(selectionStart, this.maxlength);\n                selectionEnd = Math.min(selectionEnd, this.maxlength);\n            }\n            if (this.maxlength && this.maxlength < newValue.length) {\n                return;\n            }\n            this.input.nativeElement.value = newValue;\n            let newLength = newValue.length;\n            if (operation === 'range-insert') {\n                const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n                const startValueStr = startValue !== null ? startValue.toString() : '';\n                const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n                const sRegex = new RegExp(startExpr, 'g');\n                sRegex.test(newValue);\n                const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n                const tRegex = new RegExp(tExpr, 'g');\n                tRegex.test(newValue.slice(sRegex.lastIndex));\n                selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (newLength === currentLength) {\n                if (operation === 'insert' || operation === 'delete-back-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);\n                else if (operation === 'delete-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);\n                else if (operation === 'delete-range' || operation === 'spin')\n                    this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (operation === 'delete-back-single') {\n                let prevChar = inputValue.charAt(selectionEnd - 1);\n                let nextChar = inputValue.charAt(selectionEnd);\n                let diff = currentLength - newLength;\n                let isGroupChar = this._group.test(nextChar);\n                if (isGroupChar && diff === 1) {\n                    selectionEnd += 1;\n                }\n                else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n                    selectionEnd += -1 * diff + 1;\n                }\n                this._group.lastIndex = 0;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (inputValue === '-' && operation === 'insert') {\n                this.input.nativeElement.setSelectionRange(0, 0);\n                const index = this.initCursor();\n                const selectionEnd = index + insertedValueStr.length + 1;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else {\n                selectionEnd = selectionEnd + (newLength - currentLength);\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n        }\n        this.input.nativeElement.setAttribute('aria-valuenow', value);\n    }\n    concatValues(val1, val2) {\n        if (val1 && val2) {\n            let decimalCharIndex = val2.search(this._decimal);\n            this._decimal.lastIndex = 0;\n            if (this.suffixChar) {\n                return val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n            }\n            else {\n                return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n            }\n        }\n        return val1;\n    }\n    getDecimalLength(value) {\n        if (value) {\n            const valueSplit = value.split(this._decimal);\n            if (valueSplit.length === 2) {\n                return valueSplit[1]\n                    .replace(this._suffix, '')\n                    .trim()\n                    .replace(/\\s/g, '')\n                    .replace(this._currency, '').length;\n            }\n        }\n        return 0;\n    }\n    onInputFocus(event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n        this.focused = false;\n        let newValue = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        this.onBlur.emit(event);\n        this.input.nativeElement.value = this.formatValue(newValue);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValue);\n        this.updateModel(event, newValue);\n    }\n    formattedValue() {\n        const val = !this.value && !this.allowEmpty ? 0 : this.value;\n        return this.formatValue(val);\n    }\n    updateModel(event, value) {\n        const isBlurUpdateOnMode = this.ngControl?.control?.updateOn === 'blur';\n        if (this.value !== value) {\n            this.value = value;\n            if (!(isBlurUpdateOnMode && this.focused)) {\n                this.onModelChange(value);\n            }\n        }\n        else if (isBlurUpdateOnMode) {\n            this.onModelChange(value);\n        }\n        this.onModelTouched();\n    }\n    writeValue(value) {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    get filled() {\n        return this.value != null && this.value.toString().length > 0;\n    }\n    clearTimer() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n    getFormatter() {\n        return this.numberFormat;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumber, deps: [{ token: DOCUMENT }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: InputNumber, selector: \"p-inputNumber\", inputs: { showButtons: \"showButtons\", format: \"format\", buttonLayout: \"buttonLayout\", inputId: \"inputId\", styleClass: \"styleClass\", style: \"style\", placeholder: \"placeholder\", size: \"size\", maxlength: \"maxlength\", tabindex: \"tabindex\", title: \"title\", ariaLabelledBy: \"ariaLabelledBy\", ariaLabel: \"ariaLabel\", ariaRequired: \"ariaRequired\", name: \"name\", required: \"required\", autocomplete: \"autocomplete\", min: \"min\", max: \"max\", incrementButtonClass: \"incrementButtonClass\", decrementButtonClass: \"decrementButtonClass\", incrementButtonIcon: \"incrementButtonIcon\", decrementButtonIcon: \"decrementButtonIcon\", readonly: \"readonly\", step: \"step\", allowEmpty: \"allowEmpty\", locale: \"locale\", localeMatcher: \"localeMatcher\", mode: \"mode\", currency: \"currency\", currencyDisplay: \"currencyDisplay\", useGrouping: \"useGrouping\", minFractionDigits: \"minFractionDigits\", maxFractionDigits: \"maxFractionDigits\", prefix: \"prefix\", suffix: \"suffix\", inputStyle: \"inputStyle\", inputStyleClass: \"inputStyleClass\", showClear: \"showClear\", disabled: \"disabled\" }, outputs: { onInput: \"onInput\", onFocus: \"onFocus\", onBlur: \"onBlur\", onKeyDown: \"onKeyDown\", onClear: \"onClear\" }, host: { properties: { \"class.p-inputwrapper-filled\": \"filled\", \"class.p-inputwrapper-focus\": \"focused\", \"class.p-inputnumber-clearable\": \"showClear && buttonLayout != \\\"vertical\\\"\" }, classAttribute: \"p-element p-inputwrapper\" }, providers: [INPUTNUMBER_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `, isInline: true, styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.InputText), selector: \"[pInputText]\" }, { kind: \"directive\", type: i0.forwardRef(() => i3.ButtonDirective), selector: \"[pButton]\", inputs: [\"iconPos\", \"loadingIcon\", \"label\", \"icon\", \"loading\"] }, { kind: \"component\", type: i0.forwardRef(() => TimesIcon), selector: \"TimesIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleUpIcon), selector: \"AngleUpIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleDownIcon), selector: \"AngleDownIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumber, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-inputNumber', template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, providers: [INPUTNUMBER_VALUE_ACCESSOR], encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element p-inputwrapper',\n                        '[class.p-inputwrapper-filled]': 'filled',\n                        '[class.p-inputwrapper-focus]': 'focused',\n                        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n                    }, styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.Injector }], propDecorators: { showButtons: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], buttonLayout: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], maxlength: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], title: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaRequired: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], autocomplete: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], incrementButtonClass: [{\n                type: Input\n            }], decrementButtonClass: [{\n                type: Input\n            }], incrementButtonIcon: [{\n                type: Input\n            }], decrementButtonIcon: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], allowEmpty: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], localeMatcher: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], currency: [{\n                type: Input\n            }], currencyDisplay: [{\n                type: Input\n            }], useGrouping: [{\n                type: Input\n            }], minFractionDigits: [{\n                type: Input\n            }], maxFractionDigits: [{\n                type: Input\n            }], prefix: [{\n                type: Input\n            }], suffix: [{\n                type: Input\n            }], inputStyle: [{\n                type: Input\n            }], inputStyleClass: [{\n                type: Input\n            }], showClear: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], onInput: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onKeyDown: [{\n                type: Output\n            }], onClear: [{\n                type: Output\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass InputNumberModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumberModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumberModule, declarations: [InputNumber], imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon], exports: [InputNumber, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumberModule, imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputNumberModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon],\n                    exports: [InputNumber, SharedModule],\n                    declarations: [InputNumber]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC5K,SAASC,iBAAiB,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,iCAAAF,EAAA;EAAA,oCAAAC,EAAA;EAAA,kCAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAA;AAAA;AAAA,SAAAC,gDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgkCyClC,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,kBAoDsD,CAAC;IApDzDpC,EAAE,CAAAqC,UAAA,mBAAAC,2EAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAoDOF,MAAA,CAAAG,KAAA,CAAM,CAAC;IAAA,EAAC;IApDjB3C,EAAE,CAAA4C,YAAA,CAoDsD,CAAC;EAAA;EAAA,IAAAZ,EAAA;IApDzDhC,EAAE,CAAA6C,UAAA,sCAoDJ,CAAC;IApDC7C,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAC,2DAAAf,EAAA,EAAAC,GAAA;AAAA,SAAAe,6CAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAAiD,UAAA,IAAAF,0DAAA,qBAsDzB,CAAC;EAAA;AAAA;AAAA,SAAAG,2CAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GAtDsBnD,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,aAqDwC,CAAC;IArD3CpC,EAAE,CAAAqC,UAAA,mBAAAe,iEAAA;MAAFpD,EAAE,CAAAuC,aAAA,CAAAY,GAAA;MAAA,MAAAX,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAqDtCF,MAAA,CAAAG,KAAA,CAAM,CAAC;IAAA,EAAC;IArD4B3C,EAAE,CAAAiD,UAAA,IAAAD,4CAAA,gBAsDzB,CAAC;IAtDsBhD,EAAE,CAAA4C,YAAA,CAuDzE,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAvDsExC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA8C,WAAA;IAAF9C,EAAE,CAAAqD,SAAA,CAsD3B,CAAC;IAtDwBrD,EAAE,CAAA6C,UAAA,qBAAAL,MAAA,CAAAc,iBAsD3B,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtDwBhC,EAAE,CAAAwD,uBAAA,EAmDb,CAAC;IAnDUxD,EAAE,CAAAiD,UAAA,IAAAlB,+CAAA,sBAoDsD,CAAC,IAAAmB,0CAAA,iBACf,CAAC;IArD3ClD,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,SAAA,CAoD5C,CAAC;IApDyCrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAc,iBAoD5C,CAAC;IApDyCtD,EAAE,CAAAqD,SAAA,CAqDlD,CAAC;IArD+CrD,EAAE,CAAA6C,UAAA,SAAAL,MAAA,CAAAc,iBAqDlD,CAAC;EAAA;AAAA;AAAA,SAAAI,mCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArD+ChC,EAAE,CAAA2D,SAAA,cA0E4C,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GA1E/CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA6C,UAAA,YAAAL,MAAA,CAAAoB,mBA0EX,CAAC;IA1EQ5D,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAe,yDAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAA2D,SAAA,iBA4E2B,CAAC;EAAA;EAAA,IAAA3B,EAAA;IA5E9BhC,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAgB,2DAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,6CAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAAiD,UAAA,IAAAa,0DAAA,qBA6EX,CAAC;EAAA;AAAA;AAAA,SAAAE,2CAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7EQhC,EAAE,CAAAwD,uBAAA,EA2EjC,CAAC;IA3E8BxD,EAAE,CAAAiD,UAAA,IAAAY,wDAAA,wBA4E2B,CAAC,IAAAE,4CAAA,gBACvC,CAAC;IA7EQ/D,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,SAAA,CA4ExB,CAAC;IA5EqBrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAyB,2BA4ExB,CAAC;IA5EqBjE,EAAE,CAAAqD,SAAA,CA6Eb,CAAC;IA7EUrD,EAAE,CAAA6C,UAAA,qBAAAL,MAAA,CAAAyB,2BA6Eb,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7EUhC,EAAE,CAAA2D,SAAA,cAgG4C,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GAhG/CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA6C,UAAA,YAAAL,MAAA,CAAA2B,mBAgGX,CAAC;IAhGQnE,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAsB,2DAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAA2D,SAAA,mBAkG6B,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAlGhChC,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAuB,2DAAArC,EAAA,EAAAC,GAAA;AAAA,SAAAqC,6CAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAAiD,UAAA,IAAAoB,0DAAA,qBAmGX,CAAC;EAAA;AAAA;AAAA,SAAAE,2CAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnGQhC,EAAE,CAAAwD,uBAAA,EAiGjC,CAAC;IAjG8BxD,EAAE,CAAAiD,UAAA,IAAAmB,0DAAA,0BAkG6B,CAAC,IAAAE,4CAAA,gBACzC,CAAC;IAnGQtE,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,SAAA,CAkGtB,CAAC;IAlGmBrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAgC,2BAkGtB,CAAC;IAlGmBxE,EAAE,CAAAqD,SAAA,CAmGb,CAAC;IAnGUrD,EAAE,CAAA6C,UAAA,qBAAAL,MAAA,CAAAgC,2BAmGb,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0C,GAAA,GAnGU1E,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,cAyD8C,CAAC,gBAgB9H,CAAC;IAzE4EpC,EAAE,CAAAqC,UAAA,uBAAAsC,wDAAAC,MAAA;MAAF5E,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAkE9DF,MAAA,CAAAqC,mBAAA,CAAAD,MAA0B,CAAC;IAAA,EAAC,qBAAAE,sDAAA;MAlEgC9E,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAmEhEF,MAAA,CAAAuC,iBAAA,CAAkB,CAAC;IAAA,EAAC,wBAAAC,yDAAA;MAnE0ChF,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAoE7DF,MAAA,CAAAyC,oBAAA,CAAqB,CAAC;IAAA,EAAC,qBAAAC,sDAAAN,MAAA;MApEoC5E,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAqEhEF,MAAA,CAAA2C,iBAAA,CAAAP,MAAwB,CAAC;IAAA,EAAC,mBAAAQ,oDAAA;MArEoCpF,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAsElEF,MAAA,CAAA6C,eAAA,CAAgB,CAAC;IAAA,EAAC;IAtE8CrF,EAAE,CAAAiD,UAAA,IAAAS,kCAAA,kBA0EqC,CAAC,IAAAM,0CAAA,yBACvE,CAAC;IA3E8BhE,EAAE,CAAA4C,YAAA,CA+EvE,CAAC;IA/EoE5C,EAAE,CAAAoC,cAAA,gBA+F/E,CAAC;IA/F4EpC,EAAE,CAAAqC,UAAA,uBAAAiD,wDAAAV,MAAA;MAAF5E,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAyF9DF,MAAA,CAAA+C,qBAAA,CAAAX,MAA4B,CAAC;IAAA,EAAC,qBAAAY,sDAAA;MAzF8BxF,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA0FhEF,MAAA,CAAAiD,mBAAA,CAAoB,CAAC;IAAA,EAAC,wBAAAC,yDAAA;MA1FwC1F,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA2F7DF,MAAA,CAAAmD,sBAAA,CAAuB,CAAC;IAAA,EAAC,qBAAAC,sDAAAhB,MAAA;MA3FkC5E,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA4FhEF,MAAA,CAAAqD,mBAAA,CAAAjB,MAA0B,CAAC;IAAA,EAAC,mBAAAkB,oDAAA;MA5FkC9F,EAAE,CAAAuC,aAAA,CAAAmC,GAAA;MAAA,MAAAlC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA6FlEF,MAAA,CAAAuD,iBAAA,CAAkB,CAAC;IAAA,EAAC;IA7F4C/F,EAAE,CAAAiD,UAAA,IAAAiB,kCAAA,kBAgGqC,CAAC,IAAAK,0CAAA,yBACvE,CAAC;IAjG8BvE,EAAE,CAAA4C,YAAA,CAqGvE,CAAC,CACP,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAtG0ExC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA8C,WAAA;IAAF9C,EAAE,CAAAqD,SAAA,CA+D9C,CAAC;IA/D2CrD,EAAE,CAAAgG,UAAA,CAAAxD,MAAA,CAAAyD,oBA+D9C,CAAC;IA/D2CjG,EAAE,CAAA6C,UAAA,YAAF7C,EAAE,CAAAkG,eAAA,KAAArE,GAAA,CA6DR,CAAC,aAAAW,MAAA,CAAA2D,QAGhD,CAAC;IAhEoDnG,EAAE,CAAA8C,WAAA;IAAF9C,EAAE,CAAAqD,SAAA,CA0E5C,CAAC;IA1EyCrD,EAAE,CAAA6C,UAAA,SAAAL,MAAA,CAAAoB,mBA0E5C,CAAC;IA1EyC5D,EAAE,CAAAqD,SAAA,CA2EnC,CAAC;IA3EgCrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAoB,mBA2EnC,CAAC;IA3EgC5D,EAAE,CAAAqD,SAAA,CAqF9C,CAAC;IArF2CrD,EAAE,CAAAgG,UAAA,CAAAxD,MAAA,CAAA4D,oBAqF9C,CAAC;IArF2CpG,EAAE,CAAA6C,UAAA,YAAF7C,EAAE,CAAAkG,eAAA,KAAApE,GAAA,CAmFN,CAAC,aAAAU,MAAA,CAAA2D,QAGlD,CAAC;IAtFoDnG,EAAE,CAAA8C,WAAA,yCAAAN,MAAA,CAAA6D,eAAA;IAAFrG,EAAE,CAAAqD,SAAA,CAgG5C,CAAC;IAhGyCrD,EAAE,CAAA6C,UAAA,SAAAL,MAAA,CAAA2B,mBAgG5C,CAAC;IAhGyCnE,EAAE,CAAAqD,SAAA,CAiGnC,CAAC;IAjGgCrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAA2B,mBAiGnC,CAAC;EAAA;AAAA;AAAA,SAAAmC,qCAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjGgChC,EAAE,CAAA2D,SAAA,cAwHwC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GAxH3CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA6C,UAAA,YAAAL,MAAA,CAAAoB,mBAwHf,CAAC;IAxHY5D,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAyD,2DAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAA2D,SAAA,iBA0HuB,CAAC;EAAA;EAAA,IAAA3B,EAAA;IA1H1BhC,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAA0D,6DAAAxE,EAAA,EAAAC,GAAA;AAAA,SAAAwE,+CAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAAiD,UAAA,IAAAuD,4DAAA,qBA2Hf,CAAC;EAAA;AAAA;AAAA,SAAAE,6CAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3HYhC,EAAE,CAAAwD,uBAAA,EAyHrC,CAAC;IAzHkCxD,EAAE,CAAAiD,UAAA,IAAAsD,0DAAA,wBA0HuB,CAAC,IAAAE,8CAAA,gBACvC,CAAC;IA3HYzG,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,SAAA,CA0H5B,CAAC;IA1HyBrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAyB,2BA0H5B,CAAC;IA1HyBjE,EAAE,CAAAqD,SAAA,CA2HjB,CAAC;IA3HcrD,EAAE,CAAA6C,UAAA,qBAAAL,MAAA,CAAAyB,2BA2HjB,CAAC;EAAA;AAAA;AAAA,SAAA0C,8BAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4E,GAAA,GA3Hc5G,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,gBAuHnF,CAAC;IAvHgFpC,EAAE,CAAAqC,UAAA,uBAAAwE,0DAAAjC,MAAA;MAAF5E,EAAE,CAAAuC,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAiHlEF,MAAA,CAAAqC,mBAAA,CAAAD,MAA0B,CAAC;IAAA,EAAC,qBAAAkC,wDAAA;MAjHoC9G,EAAE,CAAAuC,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAkHpEF,MAAA,CAAAuC,iBAAA,CAAkB,CAAC;IAAA,EAAC,wBAAAgC,2DAAA;MAlH8C/G,EAAE,CAAAuC,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAmHjEF,MAAA,CAAAyC,oBAAA,CAAqB,CAAC;IAAA,EAAC,qBAAA+B,wDAAApC,MAAA;MAnHwC5E,EAAE,CAAAuC,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAoHpEF,MAAA,CAAA2C,iBAAA,CAAAP,MAAwB,CAAC;IAAA,EAAC,mBAAAqC,sDAAA;MApHwCjH,EAAE,CAAAuC,aAAA,CAAAqE,GAAA;MAAA,MAAApE,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAqHtEF,MAAA,CAAA6C,eAAA,CAAgB,CAAC;IAAA,EAAC;IArHkDrF,EAAE,CAAAiD,UAAA,IAAAqD,oCAAA,kBAwHiC,CAAC,IAAAI,4CAAA,yBACvE,CAAC;IAzHkC1G,EAAE,CAAA4C,YAAA,CA6H3E,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GA7HwExC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAgG,UAAA,CAAAxD,MAAA,CAAAyD,oBA4GlD,CAAC;IA5G+CjG,EAAE,CAAA6C,UAAA,YAAF7C,EAAE,CAAAkG,eAAA,IAAArE,GAAA,CA2GZ,CAAC,aAAAW,MAAA,CAAA2D,QAGhD,CAAC;IA9GwDnG,EAAE,CAAA8C,WAAA;IAAF9C,EAAE,CAAAqD,SAAA,CAwHhD,CAAC;IAxH6CrD,EAAE,CAAA6C,UAAA,SAAAL,MAAA,CAAAoB,mBAwHhD,CAAC;IAxH6C5D,EAAE,CAAAqD,SAAA,CAyHvC,CAAC;IAzHoCrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAoB,mBAyHvC,CAAC;EAAA;AAAA;AAAA,SAAAsD,qCAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzHoChC,EAAE,CAAA2D,SAAA,cA+IwC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAQ,MAAA,GA/I3CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA6C,UAAA,YAAAL,MAAA,CAAA2B,mBA+If,CAAC;IA/IYnE,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAqE,6DAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAA2D,SAAA,mBAiJyB,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAjJ5BhC,EAAE,CAAA8C,WAAA;EAAA;AAAA;AAAA,SAAAsE,6DAAApF,EAAA,EAAAC,GAAA;AAAA,SAAAoF,+CAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFhC,EAAE,CAAAiD,UAAA,IAAAmE,4DAAA,qBAkJf,CAAC;EAAA;AAAA;AAAA,SAAAE,6CAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlJYhC,EAAE,CAAAwD,uBAAA,EAgJrC,CAAC;IAhJkCxD,EAAE,CAAAiD,UAAA,IAAAkE,4DAAA,0BAiJyB,CAAC,IAAAE,8CAAA,gBACzC,CAAC;IAlJYrH,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAQ,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,SAAA,CAiJ1B,CAAC;IAjJuBrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAAgC,2BAiJ1B,CAAC;IAjJuBxE,EAAE,CAAAqD,SAAA,CAkJjB,CAAC;IAlJcrD,EAAE,CAAA6C,UAAA,qBAAAL,MAAA,CAAAgC,2BAkJjB,CAAC;EAAA;AAAA;AAAA,SAAA+C,8BAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwF,GAAA,GAlJcxH,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,gBA8InF,CAAC;IA9IgFpC,EAAE,CAAAqC,UAAA,uBAAAoF,0DAAA7C,MAAA;MAAF5E,EAAE,CAAAuC,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAwIlEF,MAAA,CAAA+C,qBAAA,CAAAX,MAA4B,CAAC;IAAA,EAAC,qBAAA8C,wDAAA;MAxIkC1H,EAAE,CAAAuC,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CAyIpEF,MAAA,CAAAiD,mBAAA,CAAoB,CAAC;IAAA,EAAC,wBAAAkC,2DAAA;MAzI4C3H,EAAE,CAAAuC,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA0IjEF,MAAA,CAAAmD,sBAAA,CAAuB,CAAC;IAAA,EAAC,qBAAAiC,wDAAAhD,MAAA;MA1IsC5E,EAAE,CAAAuC,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA2IpEF,MAAA,CAAAqD,mBAAA,CAAAjB,MAA0B,CAAC;IAAA,EAAC,mBAAAiD,sDAAA;MA3IsC7H,EAAE,CAAAuC,aAAA,CAAAiF,GAAA;MAAA,MAAAhF,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA4ItEF,MAAA,CAAAuD,iBAAA,CAAkB,CAAC;IAAA,EAAC;IA5IgD/F,EAAE,CAAAiD,UAAA,IAAAiE,oCAAA,kBA+IiC,CAAC,IAAAI,4CAAA,yBACvE,CAAC;IAhJkCtH,EAAE,CAAA4C,YAAA,CAoJ3E,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GApJwExC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAgG,UAAA,CAAAxD,MAAA,CAAA4D,oBAoIlD,CAAC;IApI+CpG,EAAE,CAAA6C,UAAA,YAAF7C,EAAE,CAAAkG,eAAA,IAAApE,GAAA,CAkIV,CAAC,aAAAU,MAAA,CAAA2D,QAGlD,CAAC;IArIwDnG,EAAE,CAAA8C,WAAA;IAAF9C,EAAE,CAAAqD,SAAA,CA+IhD,CAAC;IA/I6CrD,EAAE,CAAA6C,UAAA,SAAAL,MAAA,CAAA2B,mBA+IhD,CAAC;IA/I6CnE,EAAE,CAAAqD,SAAA,CAgJvC,CAAC;IAhJoCrD,EAAE,CAAA6C,UAAA,UAAAL,MAAA,CAAA2B,mBAgJvC,CAAC;EAAA;AAAA;AA9sCzD,MAAM2D,0BAA0B,GAAG;EAC/BC,OAAO,EAAEnH,iBAAiB;EAC1BoH,WAAW,EAAE/H,UAAU,CAAC,MAAMgI,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdE,QAAQ;EACRC,EAAE;EACFC,EAAE;EACFC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,WAAW,GAAG,KAAK;EACnB;AACJ;AACA;AACA;EACIC,MAAM,GAAG,IAAI;EACb;AACJ;AACA;AACA;EACIC,YAAY,GAAG,SAAS;EACxB;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,GAAG;EACH;AACJ;AACA;AACA;EACIC,GAAG;EACH;AACJ;AACA;AACA;EACIxD,oBAAoB;EACpB;AACJ;AACA;AACA;EACIG,oBAAoB;EACpB;AACJ;AACA;AACA;EACIxC,mBAAmB;EACnB;AACJ;AACA;AACA;EACIO,mBAAmB;EACnB;AACJ;AACA;AACA;EACIuF,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,IAAI,GAAG,CAAC;EACR;AACJ;AACA;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,IAAI,GAAG,SAAS;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACI,IAAItE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuE,SAAS;EACzB;EACA,IAAIvE,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAIA,QAAQ,EACR,IAAI,CAACwE,OAAO,GAAG,KAAK;IACxB,IAAI,CAACD,SAAS,GAAGvE,QAAQ;IACzB,IAAI,IAAI,CAACyE,KAAK,EACV,IAAI,CAACC,UAAU,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAO,GAAG,IAAI5K,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACI6K,OAAO,GAAG,IAAI7K,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACI8K,MAAM,GAAG,IAAI9K,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI+K,SAAS,GAAG,IAAI/K,YAAY,CAAC,CAAC;EAC9B;AACJ;AACA;AACA;EACIgL,OAAO,GAAG,IAAIhL,YAAY,CAAC,CAAC;EAC5BiL,KAAK;EACLC,SAAS;EACT9H,iBAAiB;EACjBW,2BAA2B;EAC3BO,2BAA2B;EAC3B6G,KAAK;EACLC,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC1BZ,OAAO;EACPa,WAAW;EACXC,SAAS,GAAG,EAAE;EACdC,UAAU,GAAG,EAAE;EACfC,UAAU,GAAG,EAAE;EACfC,aAAa;EACbhB,KAAK;EACLiB,SAAS;EACTC,QAAQ;EACRC,YAAY;EACZC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS;EACTC,OAAO;EACPC,OAAO;EACPC,MAAM;EACN5B,SAAS;EACT6B,SAAS,GAAG,IAAI;EAChBC,WAAWA,CAACrE,QAAQ,EAAEC,EAAE,EAAEC,EAAE,EAAEC,QAAQ,EAAE;IACpC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAmE,WAAWA,CAACC,YAAY,EAAE;IACtB,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC7J,IAAIA,KAAK,CAACC,IAAI,CAAEC,CAAC,IAAK,CAAC,CAACH,YAAY,CAACG,CAAC,CAAC,CAAC,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC3B,SAAS,CAAC4B,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,WAAW;UACZ,IAAI,CAAC5J,iBAAiB,GAAG2J,IAAI,CAACE,QAAQ;UACtC;QACJ,KAAK,qBAAqB;UACtB,IAAI,CAAClJ,2BAA2B,GAAGgJ,IAAI,CAACE,QAAQ;UAChD;QACJ,KAAK,qBAAqB;UACtB,IAAI,CAAC3I,2BAA2B,GAAGyI,IAAI,CAACE,QAAQ;UAChD;MACR;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,SAAS,GAAG,IAAI,CAACjE,QAAQ,CAAC+E,GAAG,CAACxM,SAAS,EAAE,IAAI,EAAE;MAAEyM,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC/B,WAAW,GAAG,IAAI;EAC3B;EACAgC,UAAUA,CAAA,EAAG;IACT,OAAO;MACH1D,aAAa,EAAE,IAAI,CAACA,aAAa;MACjClB,KAAK,EAAE,IAAI,CAACmB,IAAI;MAChBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BuD,qBAAqB,EAAE,IAAI,CAACtD,iBAAiB;MAC7CuD,qBAAqB,EAAE,IAAI,CAACtD;IAChC,CAAC;EACL;EACAmD,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxB,YAAY,GAAG,IAAI4B,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE,IAAI,CAAC2D,UAAU,CAAC,CAAC,CAAC;IACzE,MAAMK,QAAQ,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAM,CAAC,CAAC,CAAC1B,MAAM,CAAC,UAAU,CAAC,CAAC,CAACsF,OAAO,CAAC,CAAC;IAC7G,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAACrC,QAAQ,GAAG,IAAIsC,MAAM,CAAE,IAAGP,QAAQ,CAACQ,IAAI,CAAC,EAAE,CAAE,GAAE,EAAE,GAAG,CAAC;IACzD,IAAI,CAACpC,MAAM,GAAG,IAAI,CAACqC,qBAAqB,CAAC,CAAC;IAC1C,IAAI,CAACpC,UAAU,GAAG,IAAI,CAACqC,sBAAsB,CAAC,CAAC;IAC/C,IAAI,CAACpC,SAAS,GAAG,IAAI,CAACqC,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACxC,QAAQ,GAAG,IAAI,CAACyC,oBAAoB,CAAC,CAAC;IAC3C,IAAI,CAACpC,OAAO,GAAG,IAAI,CAACqC,mBAAmB,CAAC,CAAC;IACzC,IAAI,CAACtC,OAAO,GAAG,IAAI,CAACuC,mBAAmB,CAAC,CAAC;IACzC,IAAI,CAACrC,MAAM,GAAI4B,CAAC,IAAKH,KAAK,CAACV,GAAG,CAACa,CAAC,CAAC;EACrC;EACApB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACtB,WAAW,EAAE;MAClB,IAAI,CAAC+B,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAqB,YAAYA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;EAC3D;EACAL,oBAAoBA,CAAA,EAAG;IACnB,MAAMM,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;MAAE,GAAG,IAAI,CAAC2D,UAAU,CAAC,CAAC;MAAEtD,WAAW,EAAE;IAAM,CAAC,CAAC;IAClG,OAAO,IAAIkE,MAAM,CAAE,IAAGW,SAAS,CAC1BvG,MAAM,CAAC,GAAG,CAAC,CACXsG,OAAO,CAAC,IAAI,CAAC3C,SAAS,EAAE,EAAE,CAAC,CAC3B6C,IAAI,CAAC,CAAC,CACNF,OAAO,CAAC,IAAI,CAAChD,QAAQ,EAAE,EAAE,CAAE,GAAE,EAAE,GAAG,CAAC;EAC5C;EACAwC,qBAAqBA,CAAA,EAAG;IACpB,MAAMS,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAK,CAAC,CAAC;IAC3E,IAAI,CAACuB,SAAS,GAAGsD,SAAS,CAACvG,MAAM,CAAC,OAAO,CAAC,CAACwG,IAAI,CAAC,CAAC,CAACF,OAAO,CAAC,IAAI,CAAChD,QAAQ,EAAE,EAAE,CAAC,CAACmD,MAAM,CAAC,CAAC,CAAC;IACtF,OAAO,IAAIb,MAAM,CAAE,IAAG,IAAI,CAAC3C,SAAU,GAAE,EAAE,GAAG,CAAC;EACjD;EACA8C,sBAAsBA,CAAA,EAAG;IACrB,MAAMQ,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;MAAEK,WAAW,EAAE;IAAM,CAAC,CAAC;IAC5E,OAAO,IAAIkE,MAAM,CAAE,IAAGW,SAAS,CAACvG,MAAM,CAAC,CAAC,CAAC,CAAC,CAACwG,IAAI,CAAC,CAAC,CAACF,OAAO,CAAC,IAAI,CAAChD,QAAQ,EAAE,EAAE,CAAE,GAAE,EAAE,GAAG,CAAC;EACzF;EACA0C,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACxE,QAAQ,EAAE;MACf,MAAM+E,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;QAAEjB,KAAK,EAAE,UAAU;QAAEoB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;QAAEwD,qBAAqB,EAAE,CAAC;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC;MAC/L,OAAO,IAAIU,MAAM,CAAE,IAAGW,SAAS,CAACvG,MAAM,CAAC,CAAC,CAAC,CAACsG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,CAAChD,QAAQ,EAAE,EAAE,CAAC,CAACgD,OAAO,CAAC,IAAI,CAAC7C,MAAM,EAAE,EAAE,CAAE,GAAE,EAAE,GAAG,CAAC;IAC7H;IACA,OAAO,IAAImC,MAAM,CAAE,IAAG,EAAE,GAAG,CAAC;EAChC;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACtE,MAAM,EAAE;MACb,IAAI,CAACqB,UAAU,GAAG,IAAI,CAACrB,MAAM;IACjC,CAAC,MACI;MACD,MAAM0E,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;QAAEjB,KAAK,EAAE,IAAI,CAACmB,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA;MAAgB,CAAC,CAAC;MAC1I,IAAI,CAACyB,UAAU,GAAGqD,SAAS,CAACvG,MAAM,CAAC,CAAC,CAAC,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,IAAId,MAAM,CAAE,GAAE,IAAI,CAACQ,YAAY,CAAC,IAAI,CAAClD,UAAU,IAAI,EAAE,CAAE,EAAC,EAAE,GAAG,CAAC;EACzE;EACAgD,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACpE,MAAM,EAAE;MACb,IAAI,CAACqB,UAAU,GAAG,IAAI,CAACrB,MAAM;IACjC,CAAC,MACI;MACD,MAAMyE,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE;QAAEjB,KAAK,EAAE,IAAI,CAACmB,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;QAAEwD,qBAAqB,EAAE,CAAC;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC;MAC9L,IAAI,CAAC/B,UAAU,GAAGoD,SAAS,CAACvG,MAAM,CAAC,CAAC,CAAC,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,IAAId,MAAM,CAAE,GAAE,IAAI,CAACQ,YAAY,CAAC,IAAI,CAACjD,UAAU,IAAI,EAAE,CAAE,EAAC,EAAE,GAAG,CAAC;EACzE;EACAwD,WAAWA,CAAC9D,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,IAAIA,KAAK,KAAK,GAAG,EAAE;QACf;QACA,OAAOA,KAAK;MAChB;MACA,IAAI,IAAI,CAAC7C,MAAM,EAAE;QACb,IAAIuG,SAAS,GAAG,IAAIpB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE,IAAI,CAAC2D,UAAU,CAAC,CAAC,CAAC;QACrE,IAAI4B,cAAc,GAAGL,SAAS,CAACvG,MAAM,CAAC6C,KAAK,CAAC;QAC5C,IAAI,IAAI,CAAChB,MAAM,EAAE;UACb+E,cAAc,GAAG,IAAI,CAAC/E,MAAM,GAAG+E,cAAc;QACjD;QACA,IAAI,IAAI,CAAC9E,MAAM,EAAE;UACb8E,cAAc,GAAGA,cAAc,GAAG,IAAI,CAAC9E,MAAM;QACjD;QACA,OAAO8E,cAAc;MACzB;MACA,OAAO/D,KAAK,CAACgE,QAAQ,CAAC,CAAC;IAC3B;IACA,OAAO,EAAE;EACb;EACAC,UAAUA,CAACT,IAAI,EAAE;IACb,IAAIU,YAAY,GAAGV,IAAI,CAClBC,OAAO,CAAC,IAAI,CAACzC,OAAO,EAAE,EAAE,CAAC,CACzByC,OAAO,CAAC,IAAI,CAAC1C,OAAO,EAAE,EAAE,CAAC,CACzB4C,IAAI,CAAC,CAAC,CACNF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,IAAI,CAAC3C,SAAS,EAAE,EAAE,CAAC,CAC3B2C,OAAO,CAAC,IAAI,CAAC7C,MAAM,EAAE,EAAE,CAAC,CACxB6C,OAAO,CAAC,IAAI,CAAC5C,UAAU,EAAE,GAAG,CAAC,CAC7B4C,OAAO,CAAC,IAAI,CAAC9C,QAAQ,EAAE,GAAG,CAAC,CAC3B8C,OAAO,CAAC,IAAI,CAAChD,QAAQ,EAAE,IAAI,CAACQ,MAAM,CAAC;IACxC,IAAIiD,YAAY,EAAE;MACd,IAAIA,YAAY,KAAK,GAAG;QACpB;QACA,OAAOA,YAAY;MACvB,IAAIC,WAAW,GAAG,CAACD,YAAY;MAC/B,OAAOE,KAAK,CAACD,WAAW,CAAC,GAAG,IAAI,GAAGA,WAAW;IAClD;IACA,OAAO,IAAI;EACf;EACAE,MAAMA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IACzB,IAAI,IAAI,CAACnG,QAAQ,EAAE;MACf;IACJ;IACA,IAAIyE,CAAC,GAAGyB,QAAQ,IAAI,GAAG;IACvB,IAAI,CAAC/E,UAAU,CAAC,CAAC;IACjB,IAAI,CAACD,KAAK,GAAGkF,UAAU,CAAC,MAAM;MAC1B,IAAI,CAACJ,MAAM,CAACC,KAAK,EAAE,EAAE,EAAEE,GAAG,CAAC;IAC/B,CAAC,EAAE1B,CAAC,CAAC;IACL,IAAI,CAAC4B,IAAI,CAACJ,KAAK,EAAEE,GAAG,CAAC;EACzB;EACAE,IAAIA,CAACJ,KAAK,EAAEE,GAAG,EAAE;IACb,IAAIlG,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGkG,GAAG;IAC1B,IAAIG,YAAY,GAAG,IAAI,CAACV,UAAU,CAAC,IAAI,CAACnE,KAAK,EAAE8E,aAAa,CAAC5E,KAAK,CAAC,IAAI,CAAC;IACxE,IAAI6E,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACH,YAAY,GAAGrG,IAAI,CAAC;IACtD,IAAI,IAAI,CAACZ,SAAS,IAAI,IAAI,CAACA,SAAS,GAAG,IAAI,CAACoG,WAAW,CAACe,QAAQ,CAAC,CAACE,MAAM,EAAE;MACtE;IACJ;IACA,IAAI,CAACC,WAAW,CAACH,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACI,WAAW,CAACX,KAAK,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACK,aAAa,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,CAAC;EACrD;EACAvN,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC0I,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,aAAa,CAAC,IAAI,CAACD,KAAK,CAAC;IAC9B,IAAI,CAACH,OAAO,CAACsF,IAAI,CAAC,CAAC;EACvB;EACA3L,mBAAmBA,CAAC8K,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAAC5F,UAAU,CAAC,CAAC;MACjB;IACJ;IACA,IAAI,CAAC,IAAI,CAAC1E,QAAQ,EAAE;MAChB,IAAI,CAACgF,KAAK,EAAE8E,aAAa,CAACS,KAAK,CAAC,CAAC;MACjC,IAAI,CAAChB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;MAC3BA,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA5L,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACoB,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACA5F,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACkB,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACA1F,iBAAiBA,CAACwK,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACiB,OAAO,KAAK,EAAE,IAAIjB,KAAK,CAACiB,OAAO,KAAK,EAAE,EAAE;MAC9C,IAAI,CAAClB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/B;EACJ;EACAtK,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACc,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAtF,qBAAqBA,CAACoK,KAAK,EAAE;IACzB,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAAC5F,UAAU,CAAC,CAAC;MACjB;IACJ;IACA,IAAI,CAAC,IAAI,CAAC1E,QAAQ,EAAE;MAChB,IAAI,CAACgF,KAAK,EAAE8E,aAAa,CAACS,KAAK,CAAC,CAAC;MACjC,IAAI,CAAChB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5BA,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAlL,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACU,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAlF,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACQ,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACA9E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE;MAChB,IAAI,CAAC0E,UAAU,CAAC,CAAC;IACrB;EACJ;EACAhF,mBAAmBA,CAAC8J,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACiB,OAAO,KAAK,EAAE,IAAIjB,KAAK,CAACiB,OAAO,KAAK,EAAE,EAAE;MAC9C,IAAI,CAAClB,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChC;EACJ;EACAkB,WAAWA,CAAClB,KAAK,EAAE;IACf,IAAI,IAAI,CAACjG,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,IAAI,CAACkC,aAAa,EAAE;MACpB+D,KAAK,CAACmB,MAAM,CAACzF,KAAK,GAAG,IAAI,CAACQ,SAAS;IACvC;IACA,IAAI,CAACD,aAAa,GAAG,KAAK;EAC9B;EACAmF,cAAcA,CAACpB,KAAK,EAAE;IAClB,IAAI,IAAI,CAACjG,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACmC,SAAS,GAAG8D,KAAK,CAACmB,MAAM,CAACzF,KAAK;IACnC,IAAIsE,KAAK,CAACqB,QAAQ,IAAIrB,KAAK,CAACsB,MAAM,EAAE;MAChC,IAAI,CAACrF,aAAa,GAAG,IAAI;MACzB;IACJ;IACA,IAAIsF,cAAc,GAAGvB,KAAK,CAACmB,MAAM,CAACI,cAAc;IAChD,IAAIC,YAAY,GAAGxB,KAAK,CAACmB,MAAM,CAACK,YAAY;IAC5C,IAAIC,UAAU,GAAGzB,KAAK,CAACmB,MAAM,CAACzF,KAAK;IACnC,IAAIgG,WAAW,GAAG,IAAI;IACtB,IAAI1B,KAAK,CAACsB,MAAM,EAAE;MACdtB,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;IACA,QAAQhB,KAAK,CAAC2B,IAAI;MACd,KAAK,SAAS;QACV,IAAI,CAACvB,IAAI,CAACJ,KAAK,EAAE,CAAC,CAAC;QACnBA,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACZ,IAAI,CAACJ,KAAK,EAAE,CAAC,CAAC,CAAC;QACpBA,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC,IAAI,CAACY,aAAa,CAACH,UAAU,CAACnC,MAAM,CAACiC,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE;UAC5DvB,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ,KAAK,YAAY;QACb,IAAI,CAAC,IAAI,CAACY,aAAa,CAACH,UAAU,CAACnC,MAAM,CAACiC,cAAc,CAAC,CAAC,EAAE;UACxDvB,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ,KAAK,KAAK;MACV,KAAK,OAAO;QACRU,WAAW,GAAG,IAAI,CAAClB,aAAa,CAAC,IAAI,CAACb,UAAU,CAAC,IAAI,CAACnE,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,CAAC,CAAC;QACjF,IAAI,CAACF,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAG,IAAI,CAAC8D,WAAW,CAACkC,WAAW,CAAC;QAC9D,IAAI,CAAClG,KAAK,CAAC8E,aAAa,CAACuB,YAAY,CAAC,eAAe,EAAEH,WAAW,CAAC;QACnE,IAAI,CAACf,WAAW,CAACX,KAAK,EAAE0B,WAAW,CAAC;QACpC;MACJ,KAAK,WAAW;QAAE;UACd1B,KAAK,CAACgB,cAAc,CAAC,CAAC;UACtB,IAAIO,cAAc,KAAKC,YAAY,EAAE;YACjC,MAAMM,UAAU,GAAGL,UAAU,CAACnC,MAAM,CAACiC,cAAc,GAAG,CAAC,CAAC;YACxD,MAAM;cAAEQ,gBAAgB;cAAEC;YAA8B,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAACR,UAAU,CAAC;YAClG,IAAI,IAAI,CAACG,aAAa,CAACE,UAAU,CAAC,EAAE;cAChC,MAAMI,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACV,UAAU,CAAC;cACvD,IAAI,IAAI,CAACnF,MAAM,CAAC8F,IAAI,CAACN,UAAU,CAAC,EAAE;gBAC9B,IAAI,CAACxF,MAAM,CAAC+F,SAAS,GAAG,CAAC;gBACzBX,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;cAChG,CAAC,MACI,IAAI,IAAI,CAAClF,QAAQ,CAAC+F,IAAI,CAACN,UAAU,CAAC,EAAE;gBACrC,IAAI,CAACzF,QAAQ,CAACgG,SAAS,GAAG,CAAC;gBAC3B,IAAIH,aAAa,EAAE;kBACf,IAAI,CAAC1G,KAAK,EAAE8E,aAAa,CAACiC,iBAAiB,CAAChB,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;gBACvF,CAAC,MACI;kBACDG,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,CAAC;gBAC5F;cACJ,CAAC,MACI,IAAIQ,gBAAgB,GAAG,CAAC,IAAIR,cAAc,GAAGQ,gBAAgB,EAAE;gBAChE,MAAMS,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAACjI,iBAAiB,IAAI,CAAC,IAAI0H,aAAa,GAAG,EAAE,GAAG,GAAG;gBACrGR,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,GAAG,CAAC,CAAC,GAAGiB,YAAY,GAAGf,UAAU,CAACa,KAAK,CAACf,cAAc,CAAC;cAC3G,CAAC,MACI,IAAIS,6BAA6B,KAAK,CAAC,EAAE;gBAC1CN,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,CAAC;gBAC9FG,WAAW,GAAG,IAAI,CAAC/B,UAAU,CAAC+B,WAAW,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,EAAE;cACrE,CAAC,MACI;gBACDA,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,GAAG,CAAC,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,CAAC;cAC5F;YACJ,CAAC,MACI,IAAI,IAAI,CAACnH,IAAI,KAAK,UAAU,IAAI0H,UAAU,CAACY,MAAM,CAAC,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;cAC1EkF,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,CAAC;YACrC;YACA,IAAI,CAACK,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC;UAC/D,CAAC,MACI;YACDA,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACnB,UAAU,EAAEF,cAAc,EAAEC,YAAY,CAAC;YACxE,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC;UAC9D;UACA;QACJ;MACA,KAAK,QAAQ;QACT1B,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB,IAAIO,cAAc,KAAKC,YAAY,EAAE;UACjC,MAAMM,UAAU,GAAGL,UAAU,CAACnC,MAAM,CAACiC,cAAc,CAAC;UACpD,MAAM;YAAEQ,gBAAgB;YAAEC;UAA8B,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAACR,UAAU,CAAC;UAClG,IAAI,IAAI,CAACG,aAAa,CAACE,UAAU,CAAC,EAAE;YAChC,MAAMI,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACV,UAAU,CAAC;YACvD,IAAI,IAAI,CAACnF,MAAM,CAAC8F,IAAI,CAACN,UAAU,CAAC,EAAE;cAC9B,IAAI,CAACxF,MAAM,CAAC+F,SAAS,GAAG,CAAC;cACzBX,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;YAC5F,CAAC,MACI,IAAI,IAAI,CAAClF,QAAQ,CAAC+F,IAAI,CAACN,UAAU,CAAC,EAAE;cACrC,IAAI,CAACzF,QAAQ,CAACgG,SAAS,GAAG,CAAC;cAC3B,IAAIH,aAAa,EAAE;gBACf,IAAI,CAAC1G,KAAK,EAAE8E,aAAa,CAACiC,iBAAiB,CAAChB,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;cACvF,CAAC,MACI;gBACDG,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;cAC5F;YACJ,CAAC,MACI,IAAIQ,gBAAgB,GAAG,CAAC,IAAIR,cAAc,GAAGQ,gBAAgB,EAAE;cAChE,MAAMS,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAACjI,iBAAiB,IAAI,CAAC,IAAI0H,aAAa,GAAG,EAAE,GAAG,GAAG;cACrGR,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAGiB,YAAY,GAAGf,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;YAC3G,CAAC,MACI,IAAIS,6BAA6B,KAAK,CAAC,EAAE;cAC1CN,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAG,GAAG,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;cAC9FG,WAAW,GAAG,IAAI,CAAC/B,UAAU,CAAC+B,WAAW,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,EAAE;YACrE,CAAC,MACI;cACDA,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAGE,UAAU,CAACa,KAAK,CAACf,cAAc,GAAG,CAAC,CAAC;YAC5F;UACJ;UACA,IAAI,CAACoB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAE,IAAI,EAAE,oBAAoB,CAAC;QACpE,CAAC,MACI;UACDA,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACnB,UAAU,EAAEF,cAAc,EAAEC,YAAY,CAAC;UACxE,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC;QAC9D;QACA;MACJ,KAAK,MAAM;QACP,IAAI,IAAI,CAAC7H,GAAG,EAAE;UACV,IAAI,CAAC8G,WAAW,CAACX,KAAK,EAAE,IAAI,CAACnG,GAAG,CAAC;UACjCmG,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ,KAAK,KAAK;QACN,IAAI,IAAI,CAAClH,GAAG,EAAE;UACV,IAAI,CAAC6G,WAAW,CAACX,KAAK,EAAE,IAAI,CAAClG,GAAG,CAAC;UACjCkG,KAAK,CAACgB,cAAc,CAAC,CAAC;QAC1B;QACA;MACJ;QACI;IACR;IACA,IAAI,CAAC1F,SAAS,CAACuF,IAAI,CAACb,KAAK,CAAC;EAC9B;EACA6C,eAAeA,CAAC7C,KAAK,EAAE;IACnB,IAAI,IAAI,CAACjG,QAAQ,EAAE;MACf;IACJ;IACA,IAAI4H,IAAI,GAAG3B,KAAK,CAAC8C,KAAK,IAAI9C,KAAK,CAACiB,OAAO;IACvC,IAAI8B,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACtB,IAAI,CAAC;IACpC,MAAMuB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACH,IAAI,CAAC;IAC9C,MAAMI,WAAW,GAAG,IAAI,CAACA,WAAW,CAACJ,IAAI,CAAC;IAC1C,IAAIpB,IAAI,IAAI,EAAE,EAAE;MACZ3B,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC1B;IACA,MAAMT,QAAQ,GAAG,IAAI,CAACZ,UAAU,CAAC,IAAI,CAACnE,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAGqH,IAAI,CAAC;IACvE,MAAMrB,WAAW,GAAGnB,QAAQ,IAAI,IAAI,GAAGA,QAAQ,CAACb,QAAQ,CAAC,CAAC,GAAG,EAAE;IAC/D,IAAI,IAAI,CAACtG,SAAS,IAAIsI,WAAW,CAACjB,MAAM,GAAG,IAAI,CAACrH,SAAS,EAAE;MACvD;IACJ;IACA,IAAK,EAAE,IAAIuI,IAAI,IAAIA,IAAI,IAAI,EAAE,IAAKwB,WAAW,IAAID,aAAa,EAAE;MAC5D,IAAI,CAACE,MAAM,CAACpD,KAAK,EAAE+C,IAAI,EAAE;QAAEG,aAAa;QAAEC;MAAY,CAAC,CAAC;IAC5D;EACJ;EACAE,OAAOA,CAACrD,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACxJ,QAAQ,IAAI,CAAC,IAAI,CAACuD,QAAQ,EAAE;MAClCiG,KAAK,CAACgB,cAAc,CAAC,CAAC;MACtB,IAAIsC,IAAI,GAAG,CAACtD,KAAK,CAACuD,aAAa,IAAI,IAAI,CAAC/K,QAAQ,CAACgL,WAAW,CAAC,eAAe,CAAC,EAAEC,OAAO,CAAC,MAAM,CAAC;MAC9F,IAAIH,IAAI,EAAE;QACN,IAAI,IAAI,CAAClK,SAAS,EAAE;UAChBkK,IAAI,GAAGA,IAAI,CAAC5D,QAAQ,CAAC,CAAC,CAACgE,SAAS,CAAC,CAAC,EAAE,IAAI,CAACtK,SAAS,CAAC;QACvD;QACA,IAAIuK,YAAY,GAAG,IAAI,CAAChE,UAAU,CAAC2D,IAAI,CAAC;QACxC,IAAIK,YAAY,IAAI,IAAI,EAAE;UACtB,IAAI,CAACP,MAAM,CAACpD,KAAK,EAAE2D,YAAY,CAACjE,QAAQ,CAAC,CAAC,CAAC;QAC/C;MACJ;IACJ;EACJ;EACAkE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/J,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC;EAC3C;EACAsJ,WAAWA,CAACJ,IAAI,EAAE;IACd,IAAI,IAAI,CAACxG,UAAU,CAAC6F,IAAI,CAACW,IAAI,CAAC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAC5C,IAAI,CAACxG,UAAU,CAAC8F,SAAS,GAAG,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAa,aAAaA,CAACH,IAAI,EAAE;IAChB,IAAI,IAAI,CAAC1G,QAAQ,CAAC+F,IAAI,CAACW,IAAI,CAAC,EAAE;MAC1B,IAAI,CAAC1G,QAAQ,CAACgG,SAAS,GAAG,CAAC;MAC3B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAI,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrI,IAAI,KAAK,SAAS;EAClC;EACA6H,qBAAqBA,CAAC4B,GAAG,EAAE;IACvB,IAAI9B,gBAAgB,GAAG8B,GAAG,CAACnB,MAAM,CAAC,IAAI,CAACrG,QAAQ,CAAC;IAChD,IAAI,CAACA,QAAQ,CAACgG,SAAS,GAAG,CAAC;IAC3B,MAAMyB,WAAW,GAAGD,GAAG,CAClB1E,OAAO,CAAC,IAAI,CAAC1C,OAAO,EAAE,EAAE,CAAC,CACzB4C,IAAI,CAAC,CAAC,CACNF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,IAAI,CAAC3C,SAAS,EAAE,EAAE,CAAC;IAChC,MAAMwF,6BAA6B,GAAG8B,WAAW,CAACpB,MAAM,CAAC,IAAI,CAACrG,QAAQ,CAAC;IACvE,IAAI,CAACA,QAAQ,CAACgG,SAAS,GAAG,CAAC;IAC3B,OAAO;MAAEN,gBAAgB;MAAEC;IAA8B,CAAC;EAC9D;EACA+B,cAAcA,CAACF,GAAG,EAAE;IAChB,MAAM9B,gBAAgB,GAAG8B,GAAG,CAACnB,MAAM,CAAC,IAAI,CAACrG,QAAQ,CAAC;IAClD,IAAI,CAACA,QAAQ,CAACgG,SAAS,GAAG,CAAC;IAC3B,MAAM2B,cAAc,GAAGH,GAAG,CAACnB,MAAM,CAAC,IAAI,CAACnG,UAAU,CAAC;IAClD,IAAI,CAACA,UAAU,CAAC8F,SAAS,GAAG,CAAC;IAC7B,MAAM4B,eAAe,GAAGJ,GAAG,CAACnB,MAAM,CAAC,IAAI,CAAChG,OAAO,CAAC;IAChD,IAAI,CAACA,OAAO,CAAC2F,SAAS,GAAG,CAAC;IAC1B,MAAM6B,iBAAiB,GAAGL,GAAG,CAACnB,MAAM,CAAC,IAAI,CAAClG,SAAS,CAAC;IACpD,IAAI,CAACA,SAAS,CAAC6F,SAAS,GAAG,CAAC;IAC5B,OAAO;MAAEN,gBAAgB;MAAEiC,cAAc;MAAEC,eAAe;MAAEC;IAAkB,CAAC;EACnF;EACAd,MAAMA,CAACpD,KAAK,EAAEd,IAAI,EAAEiF,IAAI,GAAG;IAAEjB,aAAa,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAM,CAAC,EAAE;IACrE,MAAMiB,oBAAoB,GAAGlF,IAAI,CAACwD,MAAM,CAAC,IAAI,CAACnG,UAAU,CAAC;IACzD,IAAI,CAACA,UAAU,CAAC8F,SAAS,GAAG,CAAC;IAC7B,IAAI,CAAC,IAAI,CAACuB,cAAc,CAAC,CAAC,IAAIQ,oBAAoB,KAAK,CAAC,CAAC,EAAE;MACvD;IACJ;IACA,IAAI7C,cAAc,GAAG,IAAI,CAAC/F,KAAK,EAAE8E,aAAa,CAACiB,cAAc;IAC7D,IAAIC,YAAY,GAAG,IAAI,CAAChG,KAAK,EAAE8E,aAAa,CAACkB,YAAY;IACzD,IAAIC,UAAU,GAAG,IAAI,CAACjG,KAAK,EAAE8E,aAAa,CAAC5E,KAAK,CAAC2D,IAAI,CAAC,CAAC;IACvD,MAAM;MAAE0C,gBAAgB;MAAEiC,cAAc;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAG,IAAI,CAACH,cAAc,CAACtC,UAAU,CAAC;IAChH,IAAIC,WAAW;IACf,IAAIyC,IAAI,CAAChB,WAAW,EAAE;MAClB,IAAI5B,cAAc,KAAK,CAAC,EAAE;QACtBG,WAAW,GAAGD,UAAU;QACxB,IAAIuC,cAAc,KAAK,CAAC,CAAC,IAAIxC,YAAY,KAAK,CAAC,EAAE;UAC7CE,WAAW,GAAG,IAAI,CAAC2C,UAAU,CAAC5C,UAAU,EAAEvC,IAAI,EAAE,CAAC,EAAEsC,YAAY,CAAC;QACpE;QACA,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAExC,IAAI,EAAE,QAAQ,CAAC;MACxD;IACJ,CAAC,MACI,IAAIiF,IAAI,CAACjB,aAAa,EAAE;MACzB,IAAInB,gBAAgB,GAAG,CAAC,IAAIR,cAAc,KAAKQ,gBAAgB,EAAE;QAC7D,IAAI,CAACY,WAAW,CAAC3C,KAAK,EAAEyB,UAAU,EAAEvC,IAAI,EAAE,QAAQ,CAAC;MACvD,CAAC,MACI,IAAI6C,gBAAgB,GAAGR,cAAc,IAAIQ,gBAAgB,GAAGP,YAAY,EAAE;QAC3EE,WAAW,GAAG,IAAI,CAAC2C,UAAU,CAAC5C,UAAU,EAAEvC,IAAI,EAAEqC,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAExC,IAAI,EAAE,QAAQ,CAAC;MACxD,CAAC,MACI,IAAI6C,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAACtH,iBAAiB,EAAE;QACxDiH,WAAW,GAAG,IAAI,CAAC2C,UAAU,CAAC5C,UAAU,EAAEvC,IAAI,EAAEqC,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAExC,IAAI,EAAE,QAAQ,CAAC;MACxD;IACJ,CAAC,MACI;MACD,MAAMzE,iBAAiB,GAAG,IAAI,CAAC2B,YAAY,CAACkI,eAAe,CAAC,CAAC,CAACvG,qBAAqB;MACnF,MAAMwG,SAAS,GAAGhD,cAAc,KAAKC,YAAY,GAAG,cAAc,GAAG,QAAQ;MAC7E,IAAIO,gBAAgB,GAAG,CAAC,IAAIR,cAAc,GAAGQ,gBAAgB,EAAE;QAC3D,IAAIR,cAAc,GAAGrC,IAAI,CAACuB,MAAM,IAAIsB,gBAAgB,GAAG,CAAC,CAAC,IAAItH,iBAAiB,EAAE;UAC5E,MAAM+J,SAAS,GAAGN,iBAAiB,IAAI3C,cAAc,GAAG2C,iBAAiB,GAAG,CAAC,GAAGD,eAAe,IAAI1C,cAAc,GAAG0C,eAAe,GAAGxC,UAAU,CAAChB,MAAM;UACvJiB,WAAW,GAAGD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,GAAGrC,IAAI,GAAGuC,UAAU,CAACa,KAAK,CAACf,cAAc,GAAGrC,IAAI,CAACuB,MAAM,EAAE+D,SAAS,CAAC,GAAG/C,UAAU,CAACa,KAAK,CAACkC,SAAS,CAAC;UAClJ,IAAI,CAAC7B,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAExC,IAAI,EAAEqF,SAAS,CAAC;QACzD;MACJ,CAAC,MACI;QACD7C,WAAW,GAAG,IAAI,CAAC2C,UAAU,CAAC5C,UAAU,EAAEvC,IAAI,EAAEqC,cAAc,EAAEC,YAAY,CAAC;QAC7E,IAAI,CAACmB,WAAW,CAAC3C,KAAK,EAAE0B,WAAW,EAAExC,IAAI,EAAEqF,SAAS,CAAC;MACzD;IACJ;EACJ;EACAF,UAAUA,CAAC3I,KAAK,EAAEwD,IAAI,EAAEuF,KAAK,EAAEC,GAAG,EAAE;IAChC,IAAIC,SAAS,GAAGzF,IAAI,KAAK,GAAG,GAAGA,IAAI,GAAGA,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC;IACrD,IAAIoF,SAAS,CAAClE,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMsB,gBAAgB,GAAGrG,KAAK,CAAC4G,KAAK,CAACmC,KAAK,EAAEC,GAAG,CAAC,CAAChC,MAAM,CAAC,IAAI,CAACrG,QAAQ,CAAC;MACtE,IAAI,CAACA,QAAQ,CAACgG,SAAS,GAAG,CAAC;MAC3B,OAAON,gBAAgB,GAAG,CAAC,GAAGrG,KAAK,CAAC4G,KAAK,CAAC,CAAC,EAAEmC,KAAK,CAAC,GAAG,IAAI,CAACjF,WAAW,CAACN,IAAI,CAAC,GAAGxD,KAAK,CAAC4G,KAAK,CAACoC,GAAG,CAAC,GAAGhJ,KAAK,IAAI,IAAI,CAAC8D,WAAW,CAACN,IAAI,CAAC;IACrI,CAAC,MACI,IAAIwF,GAAG,GAAGD,KAAK,KAAK/I,KAAK,CAAC+E,MAAM,EAAE;MACnC,OAAO,IAAI,CAACjB,WAAW,CAACN,IAAI,CAAC;IACjC,CAAC,MACI,IAAIuF,KAAK,KAAK,CAAC,EAAE;MAClB,OAAOvF,IAAI,GAAGxD,KAAK,CAAC4G,KAAK,CAACoC,GAAG,CAAC;IAClC,CAAC,MACI,IAAIA,GAAG,KAAKhJ,KAAK,CAAC+E,MAAM,EAAE;MAC3B,OAAO/E,KAAK,CAAC4G,KAAK,CAAC,CAAC,EAAEmC,KAAK,CAAC,GAAGvF,IAAI;IACvC,CAAC,MACI;MACD,OAAOxD,KAAK,CAAC4G,KAAK,CAAC,CAAC,EAAEmC,KAAK,CAAC,GAAGvF,IAAI,GAAGxD,KAAK,CAAC4G,KAAK,CAACoC,GAAG,CAAC;IAC1D;EACJ;EACA9B,WAAWA,CAAClH,KAAK,EAAE+I,KAAK,EAAEC,GAAG,EAAE;IAC3B,IAAIhD,WAAW;IACf,IAAIgD,GAAG,GAAGD,KAAK,KAAK/I,KAAK,CAAC+E,MAAM,EAC5BiB,WAAW,GAAG,EAAE,CAAC,KAChB,IAAI+C,KAAK,KAAK,CAAC,EAChB/C,WAAW,GAAGhG,KAAK,CAAC4G,KAAK,CAACoC,GAAG,CAAC,CAAC,KAC9B,IAAIA,GAAG,KAAKhJ,KAAK,CAAC+E,MAAM,EACzBiB,WAAW,GAAGhG,KAAK,CAAC4G,KAAK,CAAC,CAAC,EAAEmC,KAAK,CAAC,CAAC,KAEpC/C,WAAW,GAAGhG,KAAK,CAAC4G,KAAK,CAAC,CAAC,EAAEmC,KAAK,CAAC,GAAG/I,KAAK,CAAC4G,KAAK,CAACoC,GAAG,CAAC;IAC1D,OAAOhD,WAAW;EACtB;EACAkD,UAAUA,CAAA,EAAG;IACT,IAAIrD,cAAc,GAAG,IAAI,CAAC/F,KAAK,EAAE8E,aAAa,CAACiB,cAAc;IAC7D,IAAIE,UAAU,GAAG,IAAI,CAACjG,KAAK,EAAE8E,aAAa,CAAC5E,KAAK;IAChD,IAAImJ,WAAW,GAAGpD,UAAU,CAAChB,MAAM;IACnC,IAAIrC,KAAK,GAAG,IAAI;IAChB;IACA,IAAI0G,YAAY,GAAG,CAAC,IAAI,CAAC/I,UAAU,IAAI,EAAE,EAAE0E,MAAM;IACjDgB,UAAU,GAAGA,UAAU,CAACtC,OAAO,CAAC,IAAI,CAAC1C,OAAO,EAAE,EAAE,CAAC;IACjD8E,cAAc,GAAGA,cAAc,GAAGuD,YAAY;IAC9C,IAAI/B,IAAI,GAAGtB,UAAU,CAACnC,MAAM,CAACiC,cAAc,CAAC;IAC5C,IAAI,IAAI,CAACK,aAAa,CAACmB,IAAI,CAAC,EAAE;MAC1B,OAAOxB,cAAc,GAAGuD,YAAY;IACxC;IACA;IACA,IAAItG,CAAC,GAAG+C,cAAc,GAAG,CAAC;IAC1B,OAAO/C,CAAC,IAAI,CAAC,EAAE;MACXuE,IAAI,GAAGtB,UAAU,CAACnC,MAAM,CAACd,CAAC,CAAC;MAC3B,IAAI,IAAI,CAACoD,aAAa,CAACmB,IAAI,CAAC,EAAE;QAC1B3E,KAAK,GAAGI,CAAC,GAAGsG,YAAY;QACxB;MACJ,CAAC,MACI;QACDtG,CAAC,EAAE;MACP;IACJ;IACA,IAAIJ,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAAC5C,KAAK,EAAE8E,aAAa,CAACiC,iBAAiB,CAACnE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;IACrE,CAAC,MACI;MACDI,CAAC,GAAG+C,cAAc;MAClB,OAAO/C,CAAC,GAAGqG,WAAW,EAAE;QACpB9B,IAAI,GAAGtB,UAAU,CAACnC,MAAM,CAACd,CAAC,CAAC;QAC3B,IAAI,IAAI,CAACoD,aAAa,CAACmB,IAAI,CAAC,EAAE;UAC1B3E,KAAK,GAAGI,CAAC,GAAGsG,YAAY;UACxB;QACJ,CAAC,MACI;UACDtG,CAAC,EAAE;QACP;MACJ;MACA,IAAIJ,KAAK,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC5C,KAAK,EAAE8E,aAAa,CAACiC,iBAAiB,CAACnE,KAAK,EAAEA,KAAK,CAAC;MAC7D;IACJ;IACA,OAAOA,KAAK,IAAI,CAAC;EACrB;EACA2G,YAAYA,CAAA,EAAG;IACX,MAAM1E,YAAY,GAAG,IAAI,CAAC7E,KAAK,EAAE8E,aAAa,CAAC5E,KAAK;IACpD,IAAI,CAAC,IAAI,CAAC3B,QAAQ,IAAIsG,YAAY,KAAK9O,UAAU,CAACyT,YAAY,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACJ,UAAU,CAAC,CAAC;IACrB;EACJ;EACAhD,aAAaA,CAACmB,IAAI,EAAE;IAChB,IAAIA,IAAI,CAACtC,MAAM,KAAK,CAAC,KAAK,IAAI,CAACtE,QAAQ,CAACiG,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAAC1G,QAAQ,CAAC+F,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAACzG,MAAM,CAAC8F,IAAI,CAACW,IAAI,CAAC,IAAI,IAAI,CAACxG,UAAU,CAAC6F,IAAI,CAACW,IAAI,CAAC,CAAC,EAAE;MACrI,IAAI,CAACkC,UAAU,CAAC,CAAC;MACjB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAA,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9I,QAAQ,CAACkG,SAAS,GAAG,CAAC;IAC3B,IAAI,CAAChG,QAAQ,CAACgG,SAAS,GAAG,CAAC;IAC3B,IAAI,CAAC/F,MAAM,CAAC+F,SAAS,GAAG,CAAC;IACzB,IAAI,CAAC9F,UAAU,CAAC8F,SAAS,GAAG,CAAC;EACjC;EACAM,WAAWA,CAAC3C,KAAK,EAAEkF,QAAQ,EAAEC,gBAAgB,EAAEZ,SAAS,EAAE;IACtD,IAAIlE,YAAY,GAAG,IAAI,CAAC7E,KAAK,EAAE8E,aAAa,CAAC5E,KAAK;IAClD,IAAI6E,QAAQ,GAAG,IAAI;IACnB,IAAI2E,QAAQ,IAAI,IAAI,EAAE;MAClB3E,QAAQ,GAAG,IAAI,CAACZ,UAAU,CAACuF,QAAQ,CAAC;MACpC3E,QAAQ,GAAG,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACtG,UAAU,GAAG,CAAC,GAAGsG,QAAQ;MACvD,IAAI,CAACG,WAAW,CAACH,QAAQ,EAAE4E,gBAAgB,EAAEZ,SAAS,EAAEW,QAAQ,CAAC;MACjE,IAAI,CAACtE,aAAa,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,CAAC;IACrD;EACJ;EACAK,aAAaA,CAACZ,KAAK,EAAEK,YAAY,EAAEE,QAAQ,EAAE;IACzC,IAAI,IAAI,CAAC6E,cAAc,CAAC/E,YAAY,EAAEE,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAAC/E,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAG,IAAI,CAAC8D,WAAW,CAACe,QAAQ,CAAC;MAC3D,IAAI,CAAC/E,KAAK,EAAE8E,aAAa,CAACuB,YAAY,CAAC,eAAe,EAAEtB,QAAQ,CAAC;MACjE,IAAI,CAACI,WAAW,CAACX,KAAK,EAAEO,QAAQ,CAAC;MACjC,IAAI,CAACpF,OAAO,CAAC0F,IAAI,CAAC;QAAEwE,aAAa,EAAErF,KAAK;QAAEtE,KAAK,EAAE6E,QAAQ;QAAEd,cAAc,EAAEY;MAAa,CAAC,CAAC;IAC9F;EACJ;EACA+E,cAAcA,CAAC/E,YAAY,EAAEE,QAAQ,EAAE;IACnC,IAAIA,QAAQ,KAAK,IAAI,IAAIF,YAAY,KAAK,IAAI,EAAE;MAC5C,OAAO,IAAI;IACf;IACA,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAI+E,kBAAkB,GAAG,OAAOjF,YAAY,KAAK,QAAQ,GAAG,IAAI,CAACV,UAAU,CAACU,YAAY,CAAC,GAAGA,YAAY;MACxG,OAAOE,QAAQ,KAAK+E,kBAAkB;IAC1C;IACA,OAAO,KAAK;EAChB;EACA9E,aAAaA,CAAC9E,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,IAAI,IAAI,EAAE;MAChC,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC7B,GAAG,IAAI,IAAI,IAAI6B,KAAK,GAAG,IAAI,CAAC7B,GAAG,EAAE;MACtC,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,IAAI,IAAI,CAACC,GAAG,IAAI,IAAI,IAAI4B,KAAK,GAAG,IAAI,CAAC5B,GAAG,EAAE;MACtC,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,OAAO4B,KAAK;EAChB;EACAgF,WAAWA,CAAChF,KAAK,EAAEyJ,gBAAgB,EAAEZ,SAAS,EAAEW,QAAQ,EAAE;IACtDC,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;IACzC,IAAI1D,UAAU,GAAG,IAAI,CAACjG,KAAK,EAAE8E,aAAa,CAAC5E,KAAK;IAChD,IAAI6E,QAAQ,GAAG,IAAI,CAACf,WAAW,CAAC9D,KAAK,CAAC;IACtC,IAAI6J,aAAa,GAAG9D,UAAU,CAAChB,MAAM;IACrC,IAAIF,QAAQ,KAAK2E,QAAQ,EAAE;MACvB3E,QAAQ,GAAG,IAAI,CAACiF,YAAY,CAACjF,QAAQ,EAAE2E,QAAQ,CAAC;IACpD;IACA,IAAIK,aAAa,KAAK,CAAC,EAAE;MACrB,IAAI,CAAC/J,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAG6E,QAAQ;MACzC,IAAI,CAAC/E,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAChD,MAAMnE,KAAK,GAAG,IAAI,CAACwG,UAAU,CAAC,CAAC;MAC/B,MAAMpD,YAAY,GAAGpD,KAAK,GAAG+G,gBAAgB,CAAC1E,MAAM;MACpD,IAAI,CAACjF,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;IAC1E,CAAC,MACI;MACD,IAAID,cAAc,GAAG,IAAI,CAAC/F,KAAK,CAAC8E,aAAa,CAACiB,cAAc;MAC5D,IAAIC,YAAY,GAAG,IAAI,CAAChG,KAAK,CAAC8E,aAAa,CAACkB,YAAY;MACxD,IAAI,IAAI,CAACpI,SAAS,IAAImH,QAAQ,CAACE,MAAM,GAAG,IAAI,CAACrH,SAAS,EAAE;QACpDmH,QAAQ,GAAGA,QAAQ,CAAC+B,KAAK,CAAC,CAAC,EAAE,IAAI,CAAClJ,SAAS,CAAC;QAC5CmI,cAAc,GAAGkE,IAAI,CAAC5L,GAAG,CAAC0H,cAAc,EAAE,IAAI,CAACnI,SAAS,CAAC;QACzDoI,YAAY,GAAGiE,IAAI,CAAC5L,GAAG,CAAC2H,YAAY,EAAE,IAAI,CAACpI,SAAS,CAAC;MACzD;MACA,IAAI,IAAI,CAACA,SAAS,IAAI,IAAI,CAACA,SAAS,GAAGmH,QAAQ,CAACE,MAAM,EAAE;QACpD;MACJ;MACA,IAAI,CAACjF,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAG6E,QAAQ;MACzC,IAAImF,SAAS,GAAGnF,QAAQ,CAACE,MAAM;MAC/B,IAAI8D,SAAS,KAAK,cAAc,EAAE;QAC9B,MAAMoB,UAAU,GAAG,IAAI,CAAChG,UAAU,CAAC,CAAC8B,UAAU,IAAI,EAAE,EAAEa,KAAK,CAAC,CAAC,EAAEf,cAAc,CAAC,CAAC;QAC/E,MAAMqE,aAAa,GAAGD,UAAU,KAAK,IAAI,GAAGA,UAAU,CAACjG,QAAQ,CAAC,CAAC,GAAG,EAAE;QACtE,MAAMmG,SAAS,GAAGD,aAAa,CAACrG,KAAK,CAAC,EAAE,CAAC,CAACb,IAAI,CAAE,IAAG,IAAI,CAAC5C,SAAU,IAAG,CAAC;QACtE,MAAMgK,MAAM,GAAG,IAAIrH,MAAM,CAACoH,SAAS,EAAE,GAAG,CAAC;QACzCC,MAAM,CAAC1D,IAAI,CAAC7B,QAAQ,CAAC;QACrB,MAAMwF,KAAK,GAAGZ,gBAAgB,CAAC5F,KAAK,CAAC,EAAE,CAAC,CAACb,IAAI,CAAE,IAAG,IAAI,CAAC5C,SAAU,IAAG,CAAC;QACrE,MAAMkK,MAAM,GAAG,IAAIvH,MAAM,CAACsH,KAAK,EAAE,GAAG,CAAC;QACrCC,MAAM,CAAC5D,IAAI,CAAC7B,QAAQ,CAAC+B,KAAK,CAACwD,MAAM,CAACzD,SAAS,CAAC,CAAC;QAC7Cb,YAAY,GAAGsE,MAAM,CAACzD,SAAS,GAAG2D,MAAM,CAAC3D,SAAS;QAClD,IAAI,CAAC7G,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI,IAAIkE,SAAS,KAAKH,aAAa,EAAE;QAClC,IAAIhB,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,oBAAoB,EAC5D,IAAI,CAAC/I,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC,KAC9E,IAAI+C,SAAS,KAAK,eAAe,EAClC,IAAI,CAAC/I,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC,KAC9E,IAAI+C,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,MAAM,EACzD,IAAI,CAAC/I,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;MAC9E,CAAC,MACI,IAAI+C,SAAS,KAAK,oBAAoB,EAAE;QACzC,IAAI0B,QAAQ,GAAGxE,UAAU,CAACnC,MAAM,CAACkC,YAAY,GAAG,CAAC,CAAC;QAClD,IAAI0E,QAAQ,GAAGzE,UAAU,CAACnC,MAAM,CAACkC,YAAY,CAAC;QAC9C,IAAI2E,IAAI,GAAGZ,aAAa,GAAGG,SAAS;QACpC,IAAIU,WAAW,GAAG,IAAI,CAAC9J,MAAM,CAAC8F,IAAI,CAAC8D,QAAQ,CAAC;QAC5C,IAAIE,WAAW,IAAID,IAAI,KAAK,CAAC,EAAE;UAC3B3E,YAAY,IAAI,CAAC;QACrB,CAAC,MACI,IAAI,CAAC4E,WAAW,IAAI,IAAI,CAACxE,aAAa,CAACqE,QAAQ,CAAC,EAAE;UACnDzE,YAAY,IAAI,CAAC,CAAC,GAAG2E,IAAI,GAAG,CAAC;QACjC;QACA,IAAI,CAAC7J,MAAM,CAAC+F,SAAS,GAAG,CAAC;QACzB,IAAI,CAAC7G,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI,IAAIC,UAAU,KAAK,GAAG,IAAI8C,SAAS,KAAK,QAAQ,EAAE;QACnD,IAAI,CAAC/I,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAChD,MAAMnE,KAAK,GAAG,IAAI,CAACwG,UAAU,CAAC,CAAC;QAC/B,MAAMpD,YAAY,GAAGpD,KAAK,GAAG+G,gBAAgB,CAAC1E,MAAM,GAAG,CAAC;QACxD,IAAI,CAACjF,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;MAC1E,CAAC,MACI;QACDA,YAAY,GAAGA,YAAY,IAAIkE,SAAS,GAAGH,aAAa,CAAC;QACzD,IAAI,CAAC/J,KAAK,CAAC8E,aAAa,CAACiC,iBAAiB,CAACf,YAAY,EAAEA,YAAY,CAAC;MAC1E;IACJ;IACA,IAAI,CAAChG,KAAK,CAAC8E,aAAa,CAACuB,YAAY,CAAC,eAAe,EAAEnG,KAAK,CAAC;EACjE;EACA8J,YAAYA,CAACa,IAAI,EAAEC,IAAI,EAAE;IACrB,IAAID,IAAI,IAAIC,IAAI,EAAE;MACd,IAAIvE,gBAAgB,GAAGuE,IAAI,CAAC5D,MAAM,CAAC,IAAI,CAACrG,QAAQ,CAAC;MACjD,IAAI,CAACA,QAAQ,CAACgG,SAAS,GAAG,CAAC;MAC3B,IAAI,IAAI,CAACrG,UAAU,EAAE;QACjB,OAAOqK,IAAI,CAAClH,OAAO,CAAC,IAAI,CAACnD,UAAU,EAAE,EAAE,CAAC,CAACuD,KAAK,CAAC,IAAI,CAAClD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGiK,IAAI,CAACnH,OAAO,CAAC,IAAI,CAACnD,UAAU,EAAE,EAAE,CAAC,CAACsG,KAAK,CAACP,gBAAgB,CAAC,GAAG,IAAI,CAAC/F,UAAU;MAClJ,CAAC,MACI;QACD,OAAO+F,gBAAgB,KAAK,CAAC,CAAC,GAAGsE,IAAI,CAAC9G,KAAK,CAAC,IAAI,CAAClD,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGiK,IAAI,CAAChE,KAAK,CAACP,gBAAgB,CAAC,GAAGsE,IAAI;MACvG;IACJ;IACA,OAAOA,IAAI;EACf;EACAlE,gBAAgBA,CAACzG,KAAK,EAAE;IACpB,IAAIA,KAAK,EAAE;MACP,MAAM6K,UAAU,GAAG7K,KAAK,CAAC6D,KAAK,CAAC,IAAI,CAAClD,QAAQ,CAAC;MAC7C,IAAIkK,UAAU,CAAC9F,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO8F,UAAU,CAAC,CAAC,CAAC,CACfpH,OAAO,CAAC,IAAI,CAACzC,OAAO,EAAE,EAAE,CAAC,CACzB2C,IAAI,CAAC,CAAC,CACNF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,IAAI,CAAC3C,SAAS,EAAE,EAAE,CAAC,CAACiE,MAAM;MAC3C;IACJ;IACA,OAAO,CAAC;EACZ;EACA+F,YAAYA,CAACxG,KAAK,EAAE;IAChB,IAAI,CAAChF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACI,OAAO,CAACyF,IAAI,CAACb,KAAK,CAAC;EAC5B;EACAyG,WAAWA,CAACzG,KAAK,EAAE;IACf,IAAI,CAAChF,OAAO,GAAG,KAAK;IACpB,IAAIuF,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACb,UAAU,CAAC,IAAI,CAACnE,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,CAAC,CAAC;IAClF,IAAI,CAACL,MAAM,CAACwF,IAAI,CAACb,KAAK,CAAC;IACvB,IAAI,CAACxE,KAAK,CAAC8E,aAAa,CAAC5E,KAAK,GAAG,IAAI,CAAC8D,WAAW,CAACe,QAAQ,CAAC;IAC3D,IAAI,CAAC/E,KAAK,CAAC8E,aAAa,CAACuB,YAAY,CAAC,eAAe,EAAEtB,QAAQ,CAAC;IAChE,IAAI,CAACI,WAAW,CAACX,KAAK,EAAEO,QAAQ,CAAC;EACrC;EACAd,cAAcA,CAAA,EAAG;IACb,MAAMoE,GAAG,GAAG,CAAC,IAAI,CAACnI,KAAK,IAAI,CAAC,IAAI,CAACzB,UAAU,GAAG,CAAC,GAAG,IAAI,CAACyB,KAAK;IAC5D,OAAO,IAAI,CAAC8D,WAAW,CAACqE,GAAG,CAAC;EAChC;EACAlD,WAAWA,CAACX,KAAK,EAAEtE,KAAK,EAAE;IACtB,MAAMgL,kBAAkB,GAAG,IAAI,CAAC9J,SAAS,EAAE+J,OAAO,EAAEC,QAAQ,KAAK,MAAM;IACvE,IAAI,IAAI,CAAClL,KAAK,KAAKA,KAAK,EAAE;MACtB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,EAAEgL,kBAAkB,IAAI,IAAI,CAAC1L,OAAO,CAAC,EAAE;QACvC,IAAI,CAACW,aAAa,CAACD,KAAK,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIgL,kBAAkB,EAAE;MACzB,IAAI,CAAC/K,aAAa,CAACD,KAAK,CAAC;IAC7B;IACA,IAAI,CAACE,cAAc,CAAC,CAAC;EACzB;EACAiL,UAAUA,CAACnL,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAChD,EAAE,CAACoO,YAAY,CAAC,CAAC;EAC1B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACrL,aAAa,GAAGqL,EAAE;EAC3B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACpL,cAAc,GAAGoL,EAAE;EAC5B;EACAE,gBAAgBA,CAACrD,GAAG,EAAE;IAClB,IAAI,CAACrN,QAAQ,GAAGqN,GAAG;IACnB,IAAI,CAACnL,EAAE,CAACoO,YAAY,CAAC,CAAC;EAC1B;EACA,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACzL,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,CAACgE,QAAQ,CAAC,CAAC,CAACe,MAAM,GAAG,CAAC;EACjE;EACAvF,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACD,KAAK,EAAE;MACZmM,aAAa,CAAC,IAAI,CAACnM,KAAK,CAAC;IAC7B;EACJ;EACAoM,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjL,YAAY;EAC5B;EACA,OAAOkL,IAAI,YAAAC,oBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFlP,WAAW,EAArBjI,EAAE,CAAAoX,iBAAA,CAAqCtX,QAAQ,GAA/CE,EAAE,CAAAoX,iBAAA,CAA0DpX,EAAE,CAACqX,UAAU,GAAzErX,EAAE,CAAAoX,iBAAA,CAAoFpX,EAAE,CAACsX,iBAAiB,GAA1GtX,EAAE,CAAAoX,iBAAA,CAAqHpX,EAAE,CAACuX,QAAQ;EAAA;EAC3N,OAAOC,IAAI,kBAD8ExX,EAAE,CAAAyX,iBAAA;IAAAC,IAAA,EACJzP,WAAW;IAAA0P,SAAA;IAAAC,cAAA,WAAAC,2BAAA7V,EAAA,EAAAC,GAAA,EAAA6V,QAAA;MAAA,IAAA9V,EAAA;QADThC,EAAE,CAAA+X,cAAA,CAAAD,QAAA,EACg/ChX,aAAa;MAAA;MAAA,IAAAkB,EAAA;QAAA,IAAAgW,EAAA;QAD//ChY,EAAE,CAAAiY,cAAA,CAAAD,EAAA,GAAFhY,EAAE,CAAAkY,WAAA,QAAAjW,GAAA,CAAAmJ,SAAA,GAAA4M,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,kBAAApW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhC,EAAE,CAAAqY,WAAA,CAAA7W,GAAA;MAAA;MAAA,IAAAQ,EAAA;QAAA,IAAAgW,EAAA;QAAFhY,EAAE,CAAAiY,cAAA,CAAAD,EAAA,GAAFhY,EAAE,CAAAkY,WAAA,QAAAjW,GAAA,CAAAkJ,KAAA,GAAA6M,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAA1W,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhC,EAAE,CAAA2Y,WAAA,0BAAA1W,GAAA,CAAA6U,MACM,CAAC,yBAAA7U,GAAA,CAAA0I,OAAD,CAAC,4BAAA1I,GAAA,CAAAwI,SAAA,IAAAxI,GAAA,CAAAwG,YAAA,IAAkB,UAAnB,CAAC;MAAA;IAAA;IAAAmQ,MAAA;MAAArQ,WAAA;MAAAC,MAAA;MAAAC,YAAA;MAAAC,OAAA;MAAAC,UAAA;MAAAC,KAAA;MAAAC,WAAA;MAAAC,IAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,cAAA;MAAAC,SAAA;MAAAC,YAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,YAAA;MAAAC,GAAA;MAAAC,GAAA;MAAAxD,oBAAA;MAAAG,oBAAA;MAAAxC,mBAAA;MAAAO,mBAAA;MAAAuF,QAAA;MAAAC,IAAA;MAAAC,UAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,eAAA;MAAAC,WAAA;MAAAC,iBAAA;MAAAC,iBAAA;MAAAC,MAAA;MAAAC,MAAA;MAAAC,UAAA;MAAAC,eAAA;MAAAC,SAAA;MAAAtE,QAAA;IAAA;IAAA0S,OAAA;MAAA/N,OAAA;MAAAC,OAAA;MAAAC,MAAA;MAAAC,SAAA;MAAAC,OAAA;IAAA;IAAA4N,QAAA,GADT9Y,EAAE,CAAA+Y,kBAAA,CACg6C,CAACjR,0BAA0B,CAAC,GAD97C9H,EAAE,CAAAgZ,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAhM,QAAA,WAAAiM,qBAAApX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAqX,GAAA,GAAFrZ,EAAE,CAAAmC,gBAAA;QAAFnC,EAAE,CAAAoC,cAAA,aAavF,CAAC,iBAqCI,CAAC;QAlD+EpC,EAAE,CAAAqC,UAAA,mBAAAiX,4CAAA1U,MAAA;UAAF5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA0CtET,GAAA,CAAA4O,WAAA,CAAAjM,MAAkB,CAAC;QAAA,EAAC,qBAAA2U,8CAAA3U,MAAA;UA1CgD5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA2CpET,GAAA,CAAA8O,cAAA,CAAAnM,MAAqB,CAAC;QAAA,EAAC,sBAAA4U,+CAAA5U,MAAA;UA3C2C5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA4CnET,GAAA,CAAAuQ,eAAA,CAAA5N,MAAsB,CAAC;QAAA,EAAC,mBAAA6U,4CAAA7U,MAAA;UA5CyC5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA6CtET,GAAA,CAAA+Q,OAAA,CAAApO,MAAc,CAAC;QAAA,EAAC,mBAAA8U,4CAAA;UA7CoD1Z,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA8CtET,GAAA,CAAAyS,YAAA,CAAa,CAAC;QAAA,EAAC,mBAAAiF,4CAAA/U,MAAA;UA9CqD5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CA+CtET,GAAA,CAAAkU,YAAA,CAAAvR,MAAmB,CAAC;QAAA,EAAC,kBAAAgV,2CAAAhV,MAAA;UA/C+C5E,EAAE,CAAAuC,aAAA,CAAA8W,GAAA;UAAA,OAAFrZ,EAAE,CAAA0C,WAAA,CAgDvET,GAAA,CAAAmU,WAAA,CAAAxR,MAAkB,CAAC;QAAA,EAAC;QAhDiD5E,EAAE,CAAA4C,YAAA,CAkDlF,CAAC;QAlD+E5C,EAAE,CAAAiD,UAAA,IAAAM,mCAAA,yBAmDb,CAAC,IAAAkB,2BAAA,kBAM0D,CAAC,IAAAkC,6BAAA,mBA8DlI,CAAC,IAAAY,6BAAA,mBAuBD,CAAC;QA9IgFvH,EAAE,CAAA4C,YAAA,CAqJjF,CAAC;MAAA;MAAA,IAAAZ,EAAA;QArJ8EhC,EAAE,CAAAgG,UAAA,CAAA/D,GAAA,CAAA0G,UAUhE,CAAC;QAV6D3I,EAAE,CAAA6C,UAAA,YAAF7C,EAAE,CAAA6Z,eAAA,KAAApY,GAAA,EAAAQ,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,gBAAAxG,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,mBAAAxG,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,gBAQlF,CAAC,YAAAxG,GAAA,CAAA2G,KACc,CAAC;QATgE5I,EAAE,CAAA8C,WAAA;QAAF9C,EAAE,CAAAqD,SAAA,CAqBvD,CAAC;QArBoDrD,EAAE,CAAAgG,UAAA,CAAA/D,GAAA,CAAAuI,eAqBvD,CAAC;QArBoDxK,EAAE,CAAA6C,UAAA,iCAmB/C,CAAC,YAAAZ,GAAA,CAAAsI,UACZ,CAAC,UAAAtI,GAAA,CAAAmN,cAAA,EAEG,CAAC,aAAAnN,GAAA,CAAAkE,QAIN,CAAC,aAAAlE,GAAA,CAAAyH,QACD,CAAC;QA3BwD1J,EAAE,CAAA8C,WAAA,OAAAb,GAAA,CAAAyG,OAAA,mBAAAzG,GAAA,CAAAuH,GAAA,mBAAAvH,GAAA,CAAAwH,GAAA,mBAAAxH,GAAA,CAAAoJ,KAAA,iBAAApJ,GAAA,CAAA4G,WAAA,gBAAA5G,GAAA,CAAAkH,SAAA,qBAAAlH,GAAA,CAAAiH,cAAA,WAAAjH,GAAA,CAAAgH,KAAA,UAAAhH,GAAA,CAAA6G,IAAA,UAAA7G,GAAA,CAAAoH,IAAA,kBAAApH,GAAA,CAAAsH,YAAA,eAAAtH,GAAA,CAAA8G,SAAA,cAAA9G,GAAA,CAAA+G,QAAA,mBAAA/G,GAAA,CAAAmH,YAAA,cAAAnH,GAAA,CAAAqH,QAAA,SAAArH,GAAA,CAAAuH,GAAA,SAAAvH,GAAA,CAAAwH,GAAA;QAAFzJ,EAAE,CAAAqD,SAAA,EAmDf,CAAC;QAnDYrD,EAAE,CAAA6C,UAAA,SAAAZ,GAAA,CAAAwG,YAAA,kBAAAxG,GAAA,CAAAwI,SAAA,IAAAxI,GAAA,CAAAoJ,KAmDf,CAAC;QAnDYrL,EAAE,CAAAqD,SAAA,CAyDK,CAAC;QAzDRrD,EAAE,CAAA6C,UAAA,SAAAZ,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,cAyDK,CAAC;QAzDRzI,EAAE,CAAAqD,SAAA,CAwGhC,CAAC;QAxG6BrD,EAAE,CAAA6C,UAAA,SAAAZ,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,cAwGhC,CAAC;QAxG6BzI,EAAE,CAAAqD,SAAA,CA+HhC,CAAC;QA/H6BrD,EAAE,CAAA6C,UAAA,SAAAZ,GAAA,CAAAsG,WAAA,IAAAtG,GAAA,CAAAwG,YAAA,cA+HhC,CAAC;MAAA;IAAA;IAAAqR,YAAA,EAAAA,CAAA,MAuBqjEja,EAAE,CAACka,OAAO,EAAyGla,EAAE,CAACma,IAAI,EAAkHna,EAAE,CAACoa,gBAAgB,EAAyKpa,EAAE,CAACqa,OAAO,EAAgG5Y,EAAE,CAAC6Y,SAAS,EAA8EnZ,EAAE,CAACoZ,eAAe,EAA2I/Y,SAAS,EAA2ED,WAAW,EAA6ED,aAAa;IAAAkZ,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACvjG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxJ6Fxa,EAAE,CAAAya,iBAAA,CAwJJxS,WAAW,EAAc,CAAC;IACzGyP,IAAI,EAAEvX,SAAS;IACfua,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAExN,QAAQ,EAAG;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEoN,eAAe,EAAEna,uBAAuB,CAACwa,MAAM;MAAEC,SAAS,EAAE,CAAC/S,0BAA0B,CAAC;MAAEwS,aAAa,EAAEja,iBAAiB,CAACya,IAAI;MAAEC,IAAI,EAAE;QACtHC,KAAK,EAAE,0BAA0B;QACjC,+BAA+B,EAAE,QAAQ;QACzC,8BAA8B,EAAE,SAAS;QACzC,iCAAiC,EAAE;MACvC,CAAC;MAAEX,MAAM,EAAE,CAAC,ohEAAohE;IAAE,CAAC;EAC/iE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3C,IAAI,EAAEuD,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9CxD,IAAI,EAAEpX,MAAM;MACZoa,IAAI,EAAE,CAAC5a,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE4X,IAAI,EAAE1X,EAAE,CAACqX;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAE1X,EAAE,CAACsX;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAE1X,EAAE,CAACuX;EAAS,CAAC,CAAC,EAAkB;IAAEhP,WAAW,EAAE,CAAC;MACvHmP,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEiI,MAAM,EAAE,CAAC;MACTkP,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEkI,YAAY,EAAE,CAAC;MACfiP,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEmI,OAAO,EAAE,CAAC;MACVgP,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEoI,UAAU,EAAE,CAAC;MACb+O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEqI,KAAK,EAAE,CAAC;MACR8O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEsI,WAAW,EAAE,CAAC;MACd6O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEuI,IAAI,EAAE,CAAC;MACP4O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEwI,SAAS,EAAE,CAAC;MACZ2O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEyI,QAAQ,EAAE,CAAC;MACX0O,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE0I,KAAK,EAAE,CAAC;MACRyO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE2I,cAAc,EAAE,CAAC;MACjBwO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE4I,SAAS,EAAE,CAAC;MACZuO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE6I,YAAY,EAAE,CAAC;MACfsO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE8I,IAAI,EAAE,CAAC;MACPqO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE+I,QAAQ,EAAE,CAAC;MACXoO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEgJ,YAAY,EAAE,CAAC;MACfmO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEiJ,GAAG,EAAE,CAAC;MACNkO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEkJ,GAAG,EAAE,CAAC;MACNiO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE0F,oBAAoB,EAAE,CAAC;MACvByR,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE6F,oBAAoB,EAAE,CAAC;MACvBsR,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEqD,mBAAmB,EAAE,CAAC;MACtB8T,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE4D,mBAAmB,EAAE,CAAC;MACtBuT,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEmJ,QAAQ,EAAE,CAAC;MACXgO,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEoJ,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEqJ,UAAU,EAAE,CAAC;MACb8N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACT6N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEuJ,aAAa,EAAE,CAAC;MAChB4N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEwJ,IAAI,EAAE,CAAC;MACP2N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEyJ,QAAQ,EAAE,CAAC;MACX0N,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE0J,eAAe,EAAE,CAAC;MAClByN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE2J,WAAW,EAAE,CAAC;MACdwN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE4J,iBAAiB,EAAE,CAAC;MACpBuN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE6J,iBAAiB,EAAE,CAAC;MACpBsN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE8J,MAAM,EAAE,CAAC;MACTqN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE+J,MAAM,EAAE,CAAC;MACToN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEgK,UAAU,EAAE,CAAC;MACbmN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEiK,eAAe,EAAE,CAAC;MAClBkN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEkK,SAAS,EAAE,CAAC;MACZiN,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAE4F,QAAQ,EAAE,CAAC;MACXuR,IAAI,EAAEnX;IACV,CAAC,CAAC;IAAEuK,OAAO,EAAE,CAAC;MACV4M,IAAI,EAAElX;IACV,CAAC,CAAC;IAAEuK,OAAO,EAAE,CAAC;MACV2M,IAAI,EAAElX;IACV,CAAC,CAAC;IAAEwK,MAAM,EAAE,CAAC;MACT0M,IAAI,EAAElX;IACV,CAAC,CAAC;IAAEyK,SAAS,EAAE,CAAC;MACZyM,IAAI,EAAElX;IACV,CAAC,CAAC;IAAE0K,OAAO,EAAE,CAAC;MACVwM,IAAI,EAAElX;IACV,CAAC,CAAC;IAAE2K,KAAK,EAAE,CAAC;MACRuM,IAAI,EAAEjX,SAAS;MACfia,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEtP,SAAS,EAAE,CAAC;MACZsM,IAAI,EAAEhX,eAAe;MACrBga,IAAI,EAAE,CAAC5Z,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMqa,iBAAiB,CAAC;EACpB,OAAOlE,IAAI,YAAAmE,0BAAAjE,CAAA;IAAA,YAAAA,CAAA,IAAwFgE,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBA3Z8Erb,EAAE,CAAAsb,gBAAA;IAAA5D,IAAA,EA2ZSyD;EAAiB;EACrH,OAAOI,IAAI,kBA5Z8Evb,EAAE,CAAAwb,gBAAA;IAAAC,OAAA,GA4ZsC1b,YAAY,EAAEwB,eAAe,EAAEN,YAAY,EAAEI,SAAS,EAAED,WAAW,EAAED,aAAa,EAAEJ,YAAY;EAAA;AACrO;AACA;EAAA,QAAAyZ,SAAA,oBAAAA,SAAA,KA9Z6Fxa,EAAE,CAAAya,iBAAA,CA8ZJU,iBAAiB,EAAc,CAAC;IAC/GzD,IAAI,EAAE/W,QAAQ;IACd+Z,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC1b,YAAY,EAAEwB,eAAe,EAAEN,YAAY,EAAEI,SAAS,EAAED,WAAW,EAAED,aAAa,CAAC;MAC7Fua,OAAO,EAAE,CAACzT,WAAW,EAAElH,YAAY,CAAC;MACpC4a,YAAY,EAAE,CAAC1T,WAAW;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,0BAA0B,EAAEG,WAAW,EAAEkT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}