<p-dialog (onHide)="hideDialog()" [modal]="true" [(visible)]="visible" [style]="{ width: '50rem' }" [position]="'right'"
    [draggable]="false" class="followup-popup">
    <ng-template pTemplate="header">
        <h4>Task</h4>
    </ng-template>

    <form [formGroup]="FollowUpForm" class="relative flex flex-column gap-1">
        <div class="field grid mt-0 text-base">
            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Transaction Type">
                    <span class="material-symbols-rounded text-2xl">description</span>Transaction Type
                    <span class="text-red-500">*</span>
                </label>
                <p-dropdown [options]="dropdowns['activityDocumentType']" formControlName="document_type"
                    placeholder="Select a Document Type" optionLabel="label" optionValue="value"
                    styleClass="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['document_type'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && f['document_type'].errors" class="p-error">
                    <div *ngIf="submitted && f['document_type'].errors['required']">
                        Document Type is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Subject">
                    <span class="material-symbols-rounded text-2xl">subject</span>Subject
                    <span class="text-red-500">*</span>
                </label>
                <input pInputText id="subject" type="text" formControlName="subject" placeholder="Subject"
                    class="p-inputtext p-component p-element h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['subject'].errors }" />
                <div *ngIf="submitted && f['subject'].errors" class="p-error">
                    <div *ngIf="submitted && f['subject'].errors['required']">
                        Subject is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Account">
                    <span class="material-symbols-rounded text-2xl">account_circle</span>Account
                    <span class="text-red-500">*</span>
                </label>
                <ng-select pInputText [items]="accounts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="accountLoading" [minTermLength]="0"
                    formControlName="main_account_party_id" [typeahead]="accountInput$" [maxSelectedItems]="10"
                    appendTo="body" [ngClass]="{ 'is-invalid': submitted && f['main_account_party_id'].errors }"
                    [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.bp_id }}</span>
                        <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                    </ng-template>
                </ng-select>
                <div *ngIf="submitted && f['main_account_party_id'].errors" class="p-error">
                    <div *ngIf="submitted && f['main_account_party_id'].errors['required']">
                        Account is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Contact">
                    <span class="material-symbols-rounded text-2xl">person</span>Contact
                    <span class="text-red-500">*</span>
                </label>
                <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="contactLoading" [minTermLength]="0"
                    formControlName="main_contact_party_id" [typeahead]="contactInput$" [maxSelectedItems]="10"
                    appendTo="body" [closeOnSelect]="false"
                    [ngClass]="{ 'is-invalid': submitted && f['main_contact_party_id'].errors }"
                    [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Select a Contact">
                    <ng-template ng-option-tmp let-item="item">
                        <div class="flex align-items-center gap-2">
                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                            <span *ngIf="item.email"> : {{ item.email }}</span>
                            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                        </div>
                    </ng-template>
                </ng-select>
                <div *ngIf="submitted && f['main_contact_party_id'].errors" class="p-error">
                    <div *ngIf="submitted && f['main_contact_party_id'].errors['required']">
                        Contact is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Category">
                    <span class="material-symbols-rounded text-2xl">category</span>Category
                    <span class="text-red-500">*</span>
                </label>
                <p-dropdown [options]="dropdowns['activityCategory']" formControlName="task_category"
                    placeholder="Select a Category" optionLabel="label" optionValue="value" styleClass="h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['task_category'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && f['task_category'].errors" class="p-error">
                    <div *ngIf="submitted && f['task_category'].errors['required']">
                        Category is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Processor">
                    <span class="material-symbols-rounded text-2xl">person</span>Processor
                </label>
                <ng-select pInputText [items]="employees$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="employeeLoading" [minTermLength]="0"
                    formControlName="processor_party_id" [typeahead]="employeeInput$" [maxSelectedItems]="10"
                    appendTo="body" [closeOnSelect]="false"
                    [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Search for a processor">
                    <ng-template ng-option-tmp let-item="item">
                        <div class="flex align-items-center gap-2">
                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                            <span *ngIf="item.email"> : {{ item.email }}</span>
                            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                        </div>
                    </ng-template>
                </ng-select>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Create Date">
                    <span class="material-symbols-rounded text-2xl">schedule</span>Create Date
                </label>
                <p-calendar formControlName="start_date" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                    [showIcon]="true" styleClass="h-3rem w-full" placeholder="Create Date" />
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Expected Decision Date">
                    <span class="material-symbols-rounded text-2xl">schedule</span>Expected Decision Date
                </label>
                <p-calendar formControlName="end_date" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                    [showIcon]="true" styleClass="h-3rem w-full" placeholder="Expected Decision Date" />
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Priority">
                    <span class="material-symbols-rounded text-2xl">flag</span>Priority
                </label>
                <p-dropdown [options]="dropdowns['activityPriority']" formControlName="priority"
                    placeholder="Select a Prioriry" optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
                </p-dropdown>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Status">
                    <span class="material-symbols-rounded text-2xl">check_circle</span>Status
                    <span class="text-red-500">*</span>
                </label>
                <p-dropdown [options]="dropdowns['activityStatus']" formControlName="activity_status"
                    placeholder="Select a Status" optionLabel="label" optionValue="value" styleClass="h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['activity_status'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && f['activity_status'].errors" class="p-error">
                    <div *ngIf="
                                submitted &&
                                f['activity_status'].errors &&
                                f['activity_status'].errors['required']
                              ">
                        Status is required.
                    </div>
                </div>
            </div>
            <div class="col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Notes">
                    <span class="material-symbols-rounded text-2xl">notes</span>Notes
                    <span class="text-red-500">*</span>
                </label>
                <p-editor formControlName="notes" placeholder="Enter your note here..." [style]="{ height: '125px'}"
                    [ngClass]="{ 'is-invalid': submitted && f['notes'].errors }" styleClass="w-full" />
                <div *ngIf="submitted && f['notes'].errors" class="p-error">
                    <div *ngIf="
                                    submitted &&
                                    f['notes'].errors &&
                                    f['notes'].errors['required']
                                  ">
                        Notes is required.
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50">
            <div class="flex justify-content-between align-items-center mb-3">
                <h4 class="m-0 flex align-items-center h-3rem">Additional Contacts Persons</h4>

                <div class="flex gap-3">
                    <p-button label="Existing Contact" (click)="showExistingDialog('right')" icon="pi pi-users"
                        iconPos="right" [styleClass]="'font-semibold px-3'" [rounded]="true"></p-button>
                </div>
            </div>

            <div class="table-sec">
                <p-table #dt [value]="involved_parties?.controls" [rows]="10" styleClass="w-full" [paginator]="true"
                    [scrollable]="true" [reorderableColumns]="true" (onColReorder)="TableColumnReorder($event)"
                    responsiveLayout="scroll" class="scrollable-table followup-add-table">


                    <ng-template pTemplate="header">
                        <tr>
                            <th class="border-round-left-lg">Name</th>
                            <ng-container *ngFor="let col of selectedColumns">
                                <th pReorderableColumn>
                                    <div class="flex align-items-center cursor-pointer">
                                        {{ col.header }}
                                    </div>
                                </th>
                            </ng-container>
                            <th class="border-round-right-lg">Actions</th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-contact let-columns="columns" let-i="rowIndex">
                        <tr [formGroup]="contact">
                            <td class="border-round-left-lg">
                                <input pInputText type="text" class="p-inputtext p-component p-element h-3rem w-full" formControlName="bp_full_name"
                                    placeholder="Enter a Name" readonly />
                            </td>
                            <ng-container *ngFor="let col of selectedColumns">
                                <td>
                                    <ng-container [ngSwitch]="col.field">
                                        <ng-container *ngSwitchCase="'email'">
                                            <input pInputText type="email" class="p-inputtext p-component p-element h-3rem w-full"
                                                formControlName="email_address" placeholder="Enter Email" readonly />
                                        </ng-container>
                                    </ng-container>
                                </td>
                            </ng-container>

                            <td class="border-round-right-lg">
                                <button pButton pRipple type="button" icon="pi pi-trash"
                                    class="p-button-rounded p-button-danger" (click)="deleteContact(i)" title="Delete"
                                    *ngIf="involved_parties.length > 1"></button>
                            </td>
                        </tr>
                    </ng-template>

                </p-table>
            </div>

        </div> -->
        <!-- <p-dialog [modal]="true" [(visible)]="existingDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
            [draggable]="false" class="prospect-popup">
            <ng-template pTemplate="header">
                <h4>Contact Information</h4>
            </ng-template>

            <form [formGroup]="FollowUpForm" class="relative flex flex-column gap-1">
                <div class="field flex align-items-center text-base">
                    <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Contacts">
                        <span class="material-symbols-rounded">person</span>Contacts
                    </label>
                    <div class="form-input flex-1 relative">
                        <ng-select pInputText [items]="existingcontacts$ | async" bindLabel="bp_full_name"
                            [hideSelected]="true" [loading]="existingcontactLoading" [minTermLength]="0"
                            formControlName="contactexisting" [typeahead]="existingcontactInput$"
                            [maxSelectedItems]="10" appendTo="body"
                            [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                            <ng-template ng-option-tmp let-item="item">
                                <span>{{ item.bp_id }}</span>
                                <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                                <span *ngIf="item.email"> : {{ item.email }}</span>
                                <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="flex justify-content-end gap-2 mt-3">
                    <button pButton type="button"
                        class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center font-medium w-9rem h-3rem"
                        (click)="existingDialogVisible = false">
                        Cancel
                    </button>
                    <button pButton type="button" class="p-button-rounded justify-content-center w-9rem h-3rem"
                        (click)="selectExistingContact()">
                        Save
                    </button>
                </div>
            </form>
        </p-dialog> -->
        <div class="flex align-items-center gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="visible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>

</p-dialog>