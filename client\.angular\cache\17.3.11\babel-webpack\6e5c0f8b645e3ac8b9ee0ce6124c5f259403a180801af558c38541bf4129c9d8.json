{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/table\";\nimport * as i4 from \"primeng/button\";\nfunction AccountInvoicesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10);\n    i0.ɵɵtext(2, \"Relationship Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Business Partner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Sales Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Created On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 11);\n    i0.ɵɵtext(10, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountInvoicesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 11)(10, \"button\", 13)(11, \"i\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const relation_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (relation_r1 == null ? null : relation_r1.RelationshipType) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (relation_r1 == null ? null : relation_r1.BusinessPartner) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((relation_r1 == null ? null : relation_r1.Shareholder) || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (relation_r1 == null ? null : relation_r1.Address) || \"-\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(relation_r1.Action);\n  }\n}\nfunction AccountInvoicesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2, \"No relationships found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountInvoicesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2, \"Loading relationships data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountInvoicesComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.relationshipdetails = null;\n    this.bp_id = '';\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.relationshipdetails = response?.contact_activity;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountInvoicesComponent_Factory(t) {\n      return new (t || AccountInvoicesComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountInvoicesComponent,\n      selectors: [[\"app-account-invoices\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function AccountInvoicesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Relationships\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, AccountInvoicesComponent_ng_template_7_Template, 11, 0, \"ng-template\", 6)(8, AccountInvoicesComponent_ng_template_8_Template, 13, 5, \"ng-template\", 7)(9, AccountInvoicesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, AccountInvoicesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.relationshipdetails)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i2.PrimeTemplate, i3.Table, i4.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "relation_r1", "RelationshipType", "BusinessPartner", "ɵɵtextInterpolate", "Shareholder", "Address", "Action", "AccountInvoicesComponent", "constructor", "accountservice", "unsubscribe$", "relationshipdetails", "bp_id", "ngOnInit", "account", "pipe", "subscribe", "response", "contact_activity", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountInvoicesComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AccountInvoicesComponent_ng_template_7_Template", "AccountInvoicesComponent_ng_template_8_Template", "AccountInvoicesComponent_ng_template_9_Template", "AccountInvoicesComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\n\r\n@Component({\r\n  selector: 'app-account-invoices',\r\n  templateUrl: './account-invoices.component.html',\r\n  styleUrl: './account-invoices.component.scss',\r\n})\r\nexport class AccountInvoicesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public relationshipdetails: any = null;\r\n  public bp_id: string = '';\r\n\r\n  constructor(private accountservice: AccountService) { }\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.relationshipdetails = response?.contact_activity;\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Relationships</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"relationshipdetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\">Relationship Type</th>\r\n                    <th>Business Partner</th>\r\n                    <th>Sales Organization</th>\r\n                    <th>Created On</th>\r\n                    <th class=\"border-round-right-lg text-center\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-relation>\r\n                <tr>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ relation?.RelationshipType || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ relation?.BusinessPartner || '-' }}\r\n                    </td>\r\n                    <td>{{ relation?.Shareholder || '-' }}</td>\r\n                    <td>\r\n                        {{ relation?.Address || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\">\r\n                            <i class=\"material-symbols-rounded text-red-500\">{{\r\n                                relation.Action\r\n                                }}</i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">No relationships found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">Loading relationships data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;ICWrBC,EADJ,CAAAC,cAAA,SAAI,aACiC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,aAA8C;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACxDF,EADwD,CAAAG,YAAA,EAAK,EACxD;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACsF;IAClFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIGH,EAHR,CAAAC,cAAA,aAA8C,kBAEwD,aAC7C;IAAAD,EAAA,CAAAE,MAAA,IAE3C;IAGlBF,EAHkB,CAAAG,YAAA,EAAI,EACL,EACR,EACJ;;;;IAjBGH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAC,gBAAA,cACJ;IAEIP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAE,eAAA,cACJ;IACIR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAS,iBAAA,EAAAH,WAAA,kBAAAA,WAAA,CAAAI,WAAA,SAAkC;IAElCV,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAK,OAAA,cACJ;IAIyDX,EAAA,CAAAI,SAAA,GAE3C;IAF2CJ,EAAA,CAAAS,iBAAA,CAAAH,WAAA,CAAAM,MAAA,CAE3C;;;;;IAOdZ,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC9EF,EAD8E,CAAAG,YAAA,EAAK,EAC9E;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC/FF,EAD+F,CAAAG,YAAA,EAAK,EAC/F;;;ADzCrB,OAAM,MAAOU,wBAAwB;EAKnCC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAJ1B,KAAAC,YAAY,GAAG,IAAIlB,OAAO,EAAQ;IACnC,KAAAmB,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,KAAK,GAAW,EAAE;EAE6B;EAEtDC,QAAQA,CAAA;IACN,IAAI,CAACJ,cAAc,CAACK,OAAO,CACxBC,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACiB,YAAY,CAAC,CAAC,CAClCM,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACL,KAAK,GAAGK,QAAQ,EAAEL,KAAK;QAC5B,IAAI,CAACD,mBAAmB,GAAGM,QAAQ,EAAEC,gBAAgB;MACvD;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,IAAI,EAAE;IACxB,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;EAC9B;;;uBArBWd,wBAAwB,EAAAb,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxBjB,wBAAwB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BrC,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjEH,EAAA,CAAAuC,SAAA,kBAC2D;UAC/DvC,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAsC1BD,EArCA,CAAAwC,UAAA,IAAAC,+CAAA,0BAAgC,IAAAC,+CAAA,0BAUW,IAAAC,+CAAA,yBAsBL,KAAAC,gDAAA,yBAKD;UAOjD5C,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAlDMH,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAA6C,UAAA,oCAAmC,iBAAiB;UAI/C7C,EAAA,CAAAI,SAAA,GAA6B;UAAuCJ,EAApE,CAAA6C,UAAA,UAAAP,GAAA,CAAArB,mBAAA,CAA6B,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}