{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../opportunities/opportunities.service\";\nimport * as i3 from \"../organizational.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/tabview\";\nimport * as i8 from \"primeng/toast\";\nimport * as i9 from \"primeng/confirmdialog\";\nimport * as i10 from \"primeng/breadcrumb\";\nimport * as i11 from \"../../../shared/initials.pipe\";\nfunction OrganizationDetailsComponent_p_tabPanel_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction OrganizationDetailsComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 30);\n    i0.ɵɵtemplate(1, OrganizationDetailsComponent_p_tabPanel_8_ng_template_1_Template, 2, 2, \"ng-template\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class OrganizationDetailsComponent {\n  constructor(router, route, opportunitiesservice, organizationalservice) {\n    this.router = router;\n    this.route = route;\n    this.opportunitiesservice = opportunitiesservice;\n    this.organizationalservice = organizationalservice;\n    this.unsubscribe$ = new Subject();\n    this.organizationDetails = null;\n    this.sidebarDetails = null;\n    this.items = [];\n    this.id = '';\n    this.partner_role = '';\n    this.breadcrumbitems = [];\n    this.activeItem = null;\n    this.isSidebarHidden = false;\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const opportunityId = params.get('id');\n      if (opportunityId) {\n        this.loadOpportunityData(opportunityId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const partner_role = response?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n      this.partner_role = partner_role?.bp_full_name || null;\n      this.organizationDetails = response || null;\n      this.sidebarDetails = this.formatSidebarDetails(response?.business_partner?.addresses || []);\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'General',\n      routerLink: `/store/organization/${id}/general`\n    }, {\n      label: 'Functions',\n      routerLink: `/store/organization/${id}/functions`\n    }, {\n      label: 'Employees',\n      routerLink: `/store/organization/${id}/employees`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'general';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'General');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Organization',\n      routerLink: ['/store/organization']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadOpportunityData(activityId) {\n    this.organizationalservice.getOrganizationByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.organizationDetails = response?.data[0] || null;\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/organization']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OrganizationDetailsComponent_Factory(t) {\n      return new (t || OrganizationDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.OrganizationalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrganizationDetailsComponent,\n      selectors: [[\"app-organization-details\"]],\n      decls: 76,\n      vars: 26,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\", \"sidebar-c-details\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function OrganizationDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"p-tabView\", 7);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function OrganizationDetailsComponent_Template_p_tabView_activeIndexChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function OrganizationDetailsComponent_Template_p_tabView_onChange_7_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(8, OrganizationDetailsComponent_p_tabPanel_8_Template, 2, 1, \"p-tabPanel\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"h5\", 16);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 17)(20, \"h5\", 18);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 19)(23, \"li\", 20)(24, \"span\", 21);\n          i0.ɵɵtext(25, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"li\", 20)(28, \"span\", 21);\n          i0.ɵɵtext(29, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 20)(32, \"span\", 21);\n          i0.ɵɵtext(33, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 22)(36, \"ul\", 23)(37, \"li\", 24)(38, \"span\", 25)(39, \"i\", 26);\n          i0.ɵɵtext(40, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 27);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 24)(45, \"span\", 25)(46, \"i\", 26);\n          i0.ɵɵtext(47, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 27);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 24)(52, \"span\", 25)(53, \"i\", 26);\n          i0.ɵɵtext(54, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 27);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 24)(59, \"span\", 25)(60, \"i\", 26);\n          i0.ɵɵtext(61, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 27);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"li\", 24)(66, \"span\", 25)(67, \"i\", 26);\n          i0.ɵɵtext(68, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 27);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(72, \"div\", 28)(73, \"p-button\", 29);\n          i0.ɵɵlistener(\"click\", function OrganizationDetailsComponent_Template_p_button_click_73_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(75, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 24, (ctx.organizationDetails == null ? null : ctx.organizationDetails.business_partner == null ? null : ctx.organizationDetails.business_partner.bp_full_name) || \"-\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.organizationDetails == null ? null : ctx.organizationDetails.business_partner == null ? null : ctx.organizationDetails.business_partner.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.organizationDetails == null ? null : ctx.organizationDetails.business_partner == null ? null : ctx.organizationDetails.business_partner.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.organizationDetails == null ? null : ctx.organizationDetails.business_partner == null ? null : ctx.organizationDetails.business_partner.contact_companies == null ? null : ctx.organizationDetails.business_partner.contact_companies[0] == null ? null : ctx.organizationDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.organizationDetails.business_partner.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.organizationDetails == null ? null : ctx.organizationDetails.business_partner == null ? null : ctx.organizationDetails.business_partner.contact_companies == null ? null : ctx.organizationDetails.business_partner.contact_companies[0] == null ? null : ctx.organizationDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.organizationDetails.business_partner.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i4.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i5.Button, i6.PrimeTemplate, i7.TabView, i7.TabPanel, i8.Toast, i9.ConfirmDialog, i10.Breadcrumb, i11.InitialsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "OrganizationDetailsComponent_p_tabPanel_8_ng_template_1_Template", "OrganizationDetailsComponent", "constructor", "router", "route", "opportunitiesservice", "organizationalservice", "unsubscribe$", "organizationDetails", "sidebarDetails", "items", "id", "partner_role", "breadcrumbitems", "activeItem", "isSidebarHidden", "activeIndex", "ngOnInit", "snapshot", "paramMap", "get", "home", "icon", "makeMenuItems", "length", "setActiveTabFromURL", "pipe", "subscribe", "params", "opportunityId", "loadOpportunityData", "events", "opportunity", "response", "business_partner", "customer", "partner_functions", "find", "p", "partner_function", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "activityId", "getOrganizationByID", "next", "data", "error", "console", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "OpportunitiesService", "i3", "OrganizationalService", "selectors", "decls", "vars", "consts", "template", "OrganizationDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "OrganizationDetailsComponent_Template_p_tabView_activeIndexChange_7_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "OrganizationDetailsComponent_Template_p_tabView_onChange_7_listener", "OrganizationDetailsComponent_p_tabPanel_8_Template", "OrganizationDetailsComponent_Template_p_button_click_73_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "bp_id", "contact_companies", "business_partner_person", "first_name", "last_name"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organization-details\\organization-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organization-details\\organization-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { OpportunitiesService } from '../../opportunities/opportunities.service';\r\nimport { OrganizationalService } from '../organizational.service';\r\n\r\n@Component({\r\n  selector: 'app-organization-details',\r\n  templateUrl: './organization-details.component.html',\r\n  styleUrl: './organization-details.component.scss',\r\n})\r\nexport class OrganizationDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public organizationDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public items: MenuItem[] = [];\r\n  public home: MenuItem | any;\r\n  public id: string = '';\r\n  public partner_role: string = '';\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public isSidebarHidden = false;\r\n  public activeIndex: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private organizationalservice:OrganizationalService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.makeMenuItems(this.id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const opportunityId = params.get('id');\r\n        if (opportunityId) {\r\n          this.loadOpportunityData(opportunityId);\r\n        }\r\n      });\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const partner_role =\r\n          response?.business_partner?.customer?.partner_functions?.find(\r\n            (p: any) => p.partner_function === 'YI'\r\n          );\r\n        this.partner_role = partner_role?.bp_full_name || null;\r\n        this.organizationDetails = response || null;\r\n        this.sidebarDetails = this.formatSidebarDetails(\r\n          response?.business_partner?.addresses || []\r\n        );\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'General',\r\n        routerLink: `/store/organization/${id}/general`,\r\n      },\r\n      {\r\n        label: 'Functions',\r\n        routerLink: `/store/organization/${id}/functions`,\r\n      },\r\n      {\r\n        label: 'Employees',\r\n        routerLink: `/store/organization/${id}/employees`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'general';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'General');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Organization', routerLink: ['/store/organization'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadOpportunityData(activityId: string): void {\r\n    this.organizationalservice\r\n      .getOrganizationByID(activityId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.organizationDetails = response?.data[0] || null;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/organization']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative flex-nowrap\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full sidebar-c-details\"\r\n                    [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">{{\r\n                                        organizationDetails?.business_partner?.bp_full_name || \"-\" | initials }}</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{organizationDetails?.business_partner?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">CRM ID</span> :\r\n                                            {{organizationDetails?.business_partner?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Account Owner </span> : {{partner_role || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Main Contact</span> : {{\r\n                                            (organizationDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (organizationDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICUjBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,gEAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADCnG,OAAM,MAAOQ,4BAA4B;EAavCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,qBAA2C;IAH3C,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAhBvB,KAAAC,YAAY,GAAG,IAAIpB,OAAO,EAAQ;IACnC,KAAAqB,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,KAAK,GAAe,EAAE;IAEtB,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,UAAU,GAAoB,IAAI;IAClC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAW,CAAC;EAO3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,EAAE,GAAG,IAAI,CAACP,KAAK,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE3B,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAAC4B,aAAa,CAAC,IAAI,CAACZ,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACD,KAAK,CAACc,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACV,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACe,mBAAmB,EAAE;IAE1B,IAAI,CAACrB,KAAK,CAACe,QAAQ,CAChBO,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACmB,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,aAAa,GAAGD,MAAM,CAACR,GAAG,CAAC,IAAI,CAAC;MACtC,IAAIS,aAAa,EAAE;QACjB,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC;MACzC;IACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC1B,MAAM,CAAC4B,MAAM,CAACL,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACmB,YAAY,CAAC,CAAC,CAACoB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACpB,oBAAoB,CAAC2B,WAAW,CAClCN,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACmB,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAEM,QAAa,IAAI;MAC3B,MAAMrB,YAAY,GAChBqB,QAAQ,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC1DC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;MACH,IAAI,CAAC3B,YAAY,GAAGA,YAAY,EAAE4B,YAAY,IAAI,IAAI;MACtD,IAAI,CAAChC,mBAAmB,GAAGyB,QAAQ,IAAI,IAAI;MAC3C,IAAI,CAACxB,cAAc,GAAG,IAAI,CAACgC,oBAAoB,CAC7CR,QAAQ,EAAEC,gBAAgB,EAAEQ,SAAS,IAAI,EAAE,CAC5C;IACH,CAAC,CAAC;EACN;EAEQD,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAvC,aAAaA,CAACZ,EAAU;IACtB,IAAI,CAACD,KAAK,GAAG,CACX;MACEZ,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE,uBAAuBgB,EAAE;KACtC,EACD;MACEb,KAAK,EAAE,WAAW;MAClBH,UAAU,EAAE,uBAAuBgB,EAAE;KACtC,EACD;MACEb,KAAK,EAAE,WAAW;MAClBH,UAAU,EAAE,uBAAuBgB,EAAE;KACtC,CACF;EACH;EAEAc,mBAAmBA,CAAA;IACjB,MAAMuC,QAAQ,GAAG,IAAI,CAAC7D,MAAM,CAAC8D,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,SAAS;IAEzD,IAAI,IAAI,CAAC1D,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAM6C,UAAU,GAAG,IAAI,CAAC3D,KAAK,CAAC4D,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAAC5E,UAAU,CAAC6E,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAAClD,WAAW,GAAGqD,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACvD,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,IAAI,CAACM,WAAW,CAAC,IAAI,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAAC+D,gBAAgB,CAAC,IAAI,CAAC3D,UAAU,EAAEhB,KAAK,IAAI,SAAS,CAAC;EAC5D;EAEA2E,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC7D,eAAe,GAAG,CACrB;MAAEf,KAAK,EAAE,cAAc;MAAEH,UAAU,EAAE,CAAC,qBAAqB;IAAC,CAAE,EAC9D;MAAEG,KAAK,EAAE4E,SAAS;MAAE/E,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAgF,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAAClE,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACR,WAAW,GAAG4D,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACpE,KAAK,CAAC,IAAI,CAACM,WAAW,CAAC;IAEhD,IAAI8D,WAAW,EAAEnF,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAAC4E,aAAa,CAACD,WAAW,CAACnF,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQmC,mBAAmBA,CAACkD,UAAkB;IAC5C,IAAI,CAAC1E,qBAAqB,CACvB2E,mBAAmB,CAACD,UAAU,CAAC,CAC/BtD,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACmB,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAC;MACTuD,IAAI,EAAGjD,QAAa,IAAI;QACtB,IAAI,CAACzB,mBAAmB,GAAGyB,QAAQ,EAAEkD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACtD,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACnF,MAAM,CAACoF,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACzE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA0E,WAAWA,CAAA;IACT,IAAI,CAAClF,YAAY,CAAC2E,IAAI,EAAE;IACxB,IAAI,CAAC3E,YAAY,CAACmF,QAAQ,EAAE;EAC9B;;;uBA/JWzF,4BAA4B,EAAAZ,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAzG,EAAA,CAAAsG,iBAAA,CAAAI,EAAA,CAAAC,oBAAA,GAAA3G,EAAA,CAAAsG,iBAAA,CAAAM,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAA5BjG,4BAA4B;MAAAkG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZzCpH,EAAA,CAAAsH,SAAA,iBAAuD;UAG/CtH,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UACtDD,EAAA,CAAAsH,SAAA,sBAA+F;UAEvGtH,EADI,CAAAG,YAAA,EAAM,EACJ;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAuH,gBAAA,+BAAAC,6EAAAC,MAAA;YAAAzH,EAAA,CAAA0H,kBAAA,CAAAL,GAAA,CAAA1F,WAAA,EAAA8F,MAAA,MAAAJ,GAAA,CAAA1F,WAAA,GAAA8F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACzH,EAAA,CAAA2H,UAAA,sBAAAC,oEAAAH,MAAA;YAAA,OAAYJ,GAAA,CAAA/B,WAAA,CAAAmC,MAAA,CAAmB;UAAA,EAAC;UACzFzH,EAAA,CAAAU,UAAA,IAAAmH,kDAAA,wBAAoF;UAQ5F7H,EADI,CAAAG,YAAA,EAAY,EACV;UAUsBH,EAT5B,CAAAC,cAAA,aAAqD,eACL,eAEG,eAC4B,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,IACiC;;UAChFF,EADgF,CAAAG,YAAA,EAAK,EAC/E;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACZ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAE5C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAKlD;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAKpFF,EALoF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA2H,UAAA,mBAAAG,iEAAA;YAAA,OAAST,GAAA,CAAAlB,aAAA,EAAe;UAAA,EAAC;UAH7BnG,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAsH,SAAA,qBAA+B;UAKnDtH,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAsH,SAAA,uBAAmC;;;UA1GJtH,EAAA,CAAAI,UAAA,cAAa;UAIlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAiH,GAAA,CAAA7F,eAAA,CAAyB,SAAA6F,GAAA,CAAArF,IAAA,CAAc,uCAAuC;UAMjFhC,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAA+H,gBAAA,gBAAAV,GAAA,CAAA1F,WAAA,CAA6B;UAC5B3B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAAhG,KAAA,CAAU;UAYlCrB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAgI,WAAA,iBAAAX,GAAA,CAAA3F,eAAA,CAAsC;UAMqB1B,EAAA,CAAAO,SAAA,GACiC;UADjCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAiI,WAAA,UAAAZ,GAAA,CAAAlG,mBAAA,kBAAAkG,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,kBAAAwE,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAM,YAAA,UACiC;UAIxEnD,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAkI,kBAAA,OAAAb,GAAA,CAAAlG,mBAAA,kBAAAkG,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,kBAAAwE,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAM,YAAA,cACJ;UAGgDnD,EAAA,CAAAO,SAAA,GAE5C;UAF4CP,EAAA,CAAAkI,kBAAA,SAAAb,GAAA,CAAAlG,mBAAA,kBAAAkG,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,kBAAAwE,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAsF,KAAA,cAE5C;UAKoDnI,EAAA,CAAAO,SAAA,GACpD;UADoDP,EAAA,CAAAkI,kBAAA,QAAAb,GAAA,CAAA9F,YAAA,aACpD;UAEkDvB,EAAA,CAAAO,SAAA,GAKlD;UALkDP,EAAA,CAAAkI,kBAAA,UAAAb,GAAA,CAAAlG,mBAAA,kBAAAkG,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,kBAAAwE,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,kBAAAf,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,qBAAAf,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAAjB,GAAA,CAAAlG,mBAAA,kBAAAkG,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,kBAAAwE,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,kBAAAf,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,qBAAAf,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAlG,mBAAA,CAAA0B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAKlD;UAWiBvI,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAjG,cAAA,kBAAAiG,GAAA,CAAAjG,cAAA,qBAAAiG,GAAA,CAAAjG,cAAA,IAAAmC,OAAA,SAAuC;UAMvCvD,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAjG,cAAA,kBAAAiG,GAAA,CAAAjG,cAAA,qBAAAiG,GAAA,CAAAjG,cAAA,IAAAmD,YAAA,SAA4C;UAO9CvE,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAjG,cAAA,kBAAAiG,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,kBAAAf,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,qBAAAf,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,kBAAAgE,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,qBAAAgE,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,kBAAA6C,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,qBAAA6C,GAAA,CAAAjG,cAAA,CAAAgH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,IAAAD,YAAA,SACP;UAKSvE,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAjG,cAAA,kBAAAiG,GAAA,CAAAjG,cAAA,qBAAAiG,GAAA,CAAAjG,cAAA,IAAAiD,aAAA,SAA6C;UAM7CrE,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAjG,cAAA,kBAAAiG,GAAA,CAAAjG,cAAA,qBAAAiG,GAAA,CAAAjG,cAAA,IAAAqD,WAAA,SAA2C;UAUlDzE,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAgI,WAAA,gBAAAX,GAAA,CAAA3F,eAAA,CAAqC;UAF/D1B,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}