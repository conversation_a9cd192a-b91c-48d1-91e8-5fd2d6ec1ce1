{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ViewEncapsulation, Input, Output, signal, computed, effect, ChangeDetectionStrategy, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, Footer, Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport * as i4 from 'primeng/overlay';\nimport { OverlayModule } from 'primeng/overlay';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i6 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport * as i5 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nconst _c0 = a0 => ({\n  height: a0\n});\nconst _c1 = (a0, a1, a2) => ({\n  \"p-multiselect-item\": true,\n  \"p-highlight\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst _c2 = a0 => ({\n  \"p-highlight\": a0\n});\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nfunction MultiSelectItem_ng_container_3_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelectItem_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelectItem_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelectItem_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelectItem_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtemplate(1, MultiSelectItem_ng_container_3_span_2_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.checkIconTemplate);\n  }\n}\nfunction MultiSelectItem_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelectItem_ng_container_3_CheckIcon_1_Template, 1, 2, \"CheckIcon\", 5)(2, MultiSelectItem_ng_container_3_span_2_Template, 2, 2, \"span\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.checkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.checkIconTemplate);\n  }\n}\nfunction MultiSelectItem_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r0.label) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"empty\");\n  }\n}\nfunction MultiSelectItem_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c4 = [\"container\"];\nconst _c5 = [\"overlay\"];\nconst _c6 = [\"filterInput\"];\nconst _c7 = [\"focusInput\"];\nconst _c8 = [\"items\"];\nconst _c9 = [\"scroller\"];\nconst _c10 = [\"lastHiddenFocusableEl\"];\nconst _c11 = [\"firstHiddenFocusableEl\"];\nconst _c12 = [\"headerCheckbox\"];\nconst _c13 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c14 = [\"p-header\", \"p-footer\"];\nconst _c15 = (a0, a1) => ({\n  $implicit: a0,\n  removeChip: a1\n});\nconst _c16 = a0 => ({\n  options: a0\n});\nconst _c17 = a0 => ({\n  \"p-checkbox-disabled\": a0\n});\nconst _c18 = (a0, a1, a2) => ({\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nconst _c19 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c20 = () => ({});\nfunction MultiSelect_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.label() || \"empty\");\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_TimesCircleIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesCircleIcon\", 30);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_TimesCircleIcon_1_Template_TimesCircleIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const item_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeOption(item_r4, ctx_r1.event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-multiselect-token-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_span_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeOption(item_r4, ctx_r1.event));\n    });\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_span_2_ng_container_1_Template, 1, 0, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\")(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.removeTokenIconTemplate);\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_TimesCircleIcon_1_Template, 1, 3, \"TimesCircleIcon\", 28)(2, MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_span_2_Template, 2, 3, \"span\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.removeTokenIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removeTokenIconTemplate);\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26, 3)(2, \"span\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, MultiSelect_ng_container_7_ng_container_2_div_1_ng_container_4_Template, 3, 2, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getLabelByValue(item_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.disabled);\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder || ctx_r1.defaultLabel || \"empty\");\n  }\n}\nfunction MultiSelect_ng_container_7_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_7_ng_container_2_div_1_Template, 5, 2, \"div\", 25)(2, MultiSelect_ng_container_7_ng_container_2_ng_container_2_Template, 2, 1, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.chipSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.modelValue() || ctx_r1.modelValue().length === 0);\n  }\n}\nfunction MultiSelect_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_7_ng_container_1_Template, 2, 1, \"ng-container\", 19)(2, MultiSelect_ng_container_7_ng_container_2_Template, 3, 2, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.display === \"comma\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.display === \"chip\");\n  }\n}\nfunction MultiSelect_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_container_9_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 30);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_container_9_TimesIcon_1_Template_TimesIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clear($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-multiselect-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelect_ng_container_9_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelect_ng_container_9_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_container_9_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelect_ng_container_9_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_container_9_span_2_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clear($event));\n    });\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_9_span_2_1_Template, 1, 0, null, 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearicon\")(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.clearIconTemplate);\n  }\n}\nfunction MultiSelect_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_9_TimesIcon_1_Template, 1, 3, \"TimesIcon\", 28)(2, MultiSelect_ng_container_9_span_2_Template, 2, 3, \"span\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.clearIconTemplate);\n  }\n}\nfunction MultiSelect_ng_container_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.dropdownIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"triggericon\")(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelect_ng_container_11_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 38);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-multiselect-trigger-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"triggericon\")(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelect_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_container_11_span_1_Template, 1, 3, \"span\", 35)(2, MultiSelect_ng_container_11_ChevronDownIcon_2_Template, 1, 3, \"ChevronDownIcon\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dropdownIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.dropdownIcon);\n  }\n}\nfunction MultiSelect_span_12_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelect_span_12_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_span_12_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelect_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 39);\n    i0.ɵɵtemplate(1, MultiSelect_span_12_1_Template, 1, 0, null, 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"triggericon\")(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.dropdownIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_div_3_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c16, ctx_r1.filterOptions));\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 38);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_1_Template, 1, 0, null, 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.checkIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c3, ctx_r1.allSelected()));\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_CheckIcon_1_Template, 1, 2, \"CheckIcon\", 36)(2, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_span_2_Template, 2, 5, \"span\", 55);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.checkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checkIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onToggleAll($event));\n    })(\"keydown\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHeaderCheckboxKeyDown($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"input\", 53, 8);\n    i0.ɵɵlistener(\"focus\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template_input_focus_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHeaderCheckboxFocus());\n    })(\"blur\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template_input_blur_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHeaderCheckboxBlur());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 54);\n    i0.ɵɵtemplate(5, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_ng_container_5_Template, 3, 2, \"ng-container\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c17, ctx_r1.disabled || ctx_r1.toggleAllDisabled));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"readonly\", ctx_r1.readonly)(\"disabled\", ctx_r1.disabled || ctx_r1.toggleAllDisabled);\n    i0.ɵɵattribute(\"checked\", ctx_r1.allSelected())(\"aria-label\", ctx_r1.toggleAllAriaLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c18, ctx_r1.allSelected(), ctx_r1.headerCheckboxFocus, ctx_r1.disabled || ctx_r1.toggleAllDisabled));\n    i0.ɵɵattribute(\"aria-checked\", ctx_r1.allSelected());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allSelected());\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_1_SearchIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SearchIcon\", 38);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-multiselect-filter-icon\");\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_1_Template, 1, 0, null, 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.filterIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"input\", 58, 9);\n    i0.ɵɵlistener(\"input\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFilterInputChange($event));\n    })(\"keydown\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFilterKeyDown($event));\n    })(\"click\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template_input_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onInputClick($event));\n    })(\"blur\", function MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template_input_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFilterBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MultiSelect_ng_template_15_div_3_ng_template_4_div_1_SearchIcon_3_Template, 1, 1, \"SearchIcon\", 36)(4, MultiSelect_ng_template_15_div_3_ng_template_4_div_1_span_4_Template, 2, 1, \"span\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1._filterValue() || \"\")(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"autocomplete\", ctx_r1.autocomplete)(\"placeholder\", ctx_r1.filterPlaceHolder)(\"aria-owns\", ctx_r1.id + \"_list\")(\"aria-activedescendant\", ctx_r1.focusedOptionId)(\"placeholder\", ctx_r1.filterPlaceHolder)(\"aria-label\", ctx_r1.ariaFilterLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.filterIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filterIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_TimesIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 38);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-multiselect-close-icon\");\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_span_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_span_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_div_3_ng_template_4_span_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_div_3_ng_template_4_span_4_1_Template, 1, 0, null, 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_div_3_ng_template_4_div_0_Template, 6, 15, \"div\", 48)(1, MultiSelect_ng_template_15_div_3_ng_template_4_div_1_Template, 5, 10, \"div\", 49);\n    i0.ɵɵelementStart(2, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function MultiSelect_ng_template_15_div_3_ng_template_4_Template_button_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    });\n    i0.ɵɵtemplate(3, MultiSelect_ng_template_15_div_3_ng_template_4_TimesIcon_3_Template, 1, 1, \"TimesIcon\", 36)(4, MultiSelect_ng_template_15_div_3_ng_template_4_span_4_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showToggleAll && !ctx_r1.selectionLimit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filter);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.closeAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, MultiSelect_ng_template_15_div_3_ng_container_2_Template, 1, 0, \"ng-container\", 32)(3, MultiSelect_ng_template_15_div_3_ng_container_3_Template, 2, 4, \"ng-container\", 47)(4, MultiSelect_ng_template_15_div_3_ng_template_4_Template, 5, 5, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const builtInFilterElement_r12 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filterTemplate)(\"ngIfElse\", builtInFilterElement_r12);\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_p_scroller_5_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const items_r14 = ctx.$implicit;\n    const scrollerOptions_r15 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const buildInItems_r16 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r16)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c19, items_r14, scrollerOptions_r15));\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_p_scroller_5_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r17 = ctx.options;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c16, scrollerOptions_r17));\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_p_scroller_5_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 63);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MultiSelect_ng_template_15_p_scroller_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 62, 10);\n    i0.ɵɵlistener(\"onLazyLoad\", function MultiSelect_ng_template_15_p_scroller_5_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, MultiSelect_ng_template_15_p_scroller_5_ng_template_2_Template, 1, 5, \"ng-template\", 24)(3, MultiSelect_ng_template_15_p_scroller_5_ng_container_3_Template, 2, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(9, _c0, ctx_r1.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r1.visibleOptions())(\"itemSize\", ctx_r1.virtualScrollItemSize || ctx_r1._itemSize)(\"autoSize\", true)(\"tabindex\", -1)(\"lazy\", ctx_r1.lazy)(\"options\", ctx_r1.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_ng_container_6_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const buildInItems_r16 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r16)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c19, ctx_r1.visibleOptions(), i0.ɵɵpureFunction0(2, _c20)));\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getOptionGroupLabel(option_r18.optionGroup));\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 67);\n    i0.ɵɵtemplate(2, MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_span_2_Template, 2, 1, \"span\", 19)(3, MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_ng_container_3_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    const option_r18 = ctx_r18.$implicit;\n    const i_r20 = ctx_r18.index;\n    const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c0, scrollerOptions_r21.itemSize + \"px\"));\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_\" + ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.groupTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c3, option_r18.optionGroup));\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-multiSelectItem\", 68);\n    i0.ɵɵlistener(\"onClick\", function MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_1_Template_p_multiSelectItem_onClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const i_r20 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionSelect($event, false, ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21)));\n    })(\"onMouseEnter\", function MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_1_Template_p_multiSelectItem_onMouseEnter_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const i_r20 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionMouseEnter($event, ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21)));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    const option_r18 = ctx_r18.$implicit;\n    const i_r20 = ctx_r18.index;\n    const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r1.id + \"_\" + ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21))(\"option\", option_r18)(\"selected\", ctx_r1.isSelected(option_r18))(\"label\", ctx_r1.getOptionLabel(option_r18))(\"disabled\", ctx_r1.isOptionDisabled(option_r18))(\"template\", ctx_r1.itemTemplate)(\"checkIconTemplate\", ctx_r1.checkIconTemplate)(\"itemSize\", scrollerOptions_r21.itemSize)(\"focused\", ctx_r1.focusedOptionIndex() === ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21))(\"ariaPosInset\", ctx_r1.getAriaPosInset(ctx_r1.getOptionIndex(i_r20, scrollerOptions_r21)))(\"ariaSetSize\", ctx_r1.ariaSetSize);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_0_Template, 4, 9, \"ng-container\", 19)(1, MultiSelect_ng_template_15_ng_template_7_ng_template_2_ng_container_1_Template, 2, 11, \"ng-container\", 19);\n  }\n  if (rf & 2) {\n    const option_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOptionGroup(option_r18));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isOptionGroup(option_r18));\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.emptyFilterMessageLabel, \" \");\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 12);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 69);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_ng_template_7_li_3_ng_container_1_Template, 2, 1, \"ng-container\", 47)(2, MultiSelect_ng_template_15_ng_template_7_li_3_ng_container_2_Template, 2, 0, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r21.itemSize + \"px\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.emptyFilterTemplate && !ctx_r1.emptyTemplate)(\"ngIfElse\", ctx_r1.emptyFilter);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.emptyFilterTemplate || ctx_r1.emptyTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.emptyMessageLabel, \" \");\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 13);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 69);\n    i0.ɵɵtemplate(1, MultiSelect_ng_template_15_ng_template_7_li_4_ng_container_1_Template, 2, 1, \"ng-container\", 47)(2, MultiSelect_ng_template_15_ng_template_7_li_4_ng_container_2_Template, 2, 0, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r21 = i0.ɵɵnextContext().options;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r21.itemSize + \"px\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.emptyTemplate)(\"ngIfElse\", ctx_r1.empty);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.emptyTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 64, 11);\n    i0.ɵɵtemplate(2, MultiSelect_ng_template_15_ng_template_7_ng_template_2_Template, 2, 2, \"ng-template\", 65)(3, MultiSelect_ng_template_15_ng_template_7_li_3_Template, 3, 6, \"li\", 66)(4, MultiSelect_ng_template_15_ng_template_7_li_4_Template, 3, 6, \"li\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const items_r23 = ctx.$implicit;\n    const scrollerOptions_r21 = ctx.options;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(scrollerOptions_r21.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r21.contentStyleClass);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", items_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFilter() && ctx_r1.isEmpty());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasFilter() && ctx_r1.isEmpty());\n  }\n}\nfunction MultiSelect_ng_template_15_div_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MultiSelect_ng_template_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, MultiSelect_ng_template_15_div_9_ng_container_2_Template, 1, 0, \"ng-container\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nfunction MultiSelect_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"span\", 41, 4);\n    i0.ɵɵlistener(\"focus\", function MultiSelect_ng_template_15_Template_span_focus_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFirstHiddenFocus($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MultiSelect_ng_template_15_div_3_Template, 6, 3, \"div\", 42);\n    i0.ɵɵelementStart(4, \"div\", 43);\n    i0.ɵɵtemplate(5, MultiSelect_ng_template_15_p_scroller_5_Template, 4, 11, \"p-scroller\", 44)(6, MultiSelect_ng_template_15_ng_container_6_Template, 2, 6, \"ng-container\", 19)(7, MultiSelect_ng_template_15_ng_template_7_Template, 5, 6, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MultiSelect_ng_template_15_div_9_Template, 3, 1, \"div\", 45);\n    i0.ɵɵelementStart(10, \"span\", 41, 6);\n    i0.ɵɵlistener(\"focus\", function MultiSelect_ng_template_15_Template_span_focus_10_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLastHiddenFocus($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-multiselect-panel p-component\")(\"ngStyle\", ctx_r1.panelStyle);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-hidden\", \"true\")(\"tabindex\", 0)(\"data-p-hidden-accessible\", true)(\"data-p-hidden-focusable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showHeader);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"max-height\", ctx_r1.virtualScroll ? \"auto\" : ctx_r1.scrollHeight || \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.virtualScroll);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.virtualScroll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.footerFacet || ctx_r1.footerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"tabindex\", 0)(\"data-p-hidden-accessible\", true)(\"data-p-hidden-focusable\", true);\n  }\n}\nconst MULTISELECT_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MultiSelect),\n  multi: true\n};\nlet MultiSelectItem = /*#__PURE__*/(() => {\n  class MultiSelectItem {\n    id;\n    option;\n    selected;\n    label;\n    disabled;\n    itemSize;\n    focused;\n    ariaPosInset;\n    ariaSetSize;\n    template;\n    checkIconTemplate;\n    onClick = new EventEmitter();\n    onMouseEnter = new EventEmitter();\n    onOptionClick(event) {\n      this.onClick.emit({\n        originalEvent: event,\n        option: this.option,\n        selected: this.selected\n      });\n      event.stopPropagation();\n    }\n    onOptionMouseEnter(event) {\n      this.onMouseEnter.emit({\n        originalEvent: event,\n        option: this.option,\n        selected: this.selected\n      });\n    }\n    static ɵfac = function MultiSelectItem_Factory(t) {\n      return new (t || MultiSelectItem)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MultiSelectItem,\n      selectors: [[\"p-multiSelectItem\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        id: \"id\",\n        option: \"option\",\n        selected: \"selected\",\n        label: \"label\",\n        disabled: \"disabled\",\n        itemSize: \"itemSize\",\n        focused: \"focused\",\n        ariaPosInset: \"ariaPosInset\",\n        ariaSetSize: \"ariaSetSize\",\n        template: \"template\",\n        checkIconTemplate: \"checkIconTemplate\"\n      },\n      outputs: {\n        onClick: \"onClick\",\n        onMouseEnter: \"onMouseEnter\"\n      },\n      decls: 6,\n      vars: 26,\n      consts: [[\"pRipple\", \"\", 1, \"p-multiselect-item\", 3, \"click\", \"mouseenter\", \"ngStyle\", \"ngClass\", \"id\"], [1, \"p-checkbox\", \"p-component\"], [1, \"p-checkbox-box\", 3, \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\"]],\n      template: function MultiSelectItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"li\", 0);\n          i0.ɵɵlistener(\"click\", function MultiSelectItem_Template_li_click_0_listener($event) {\n            return ctx.onOptionClick($event);\n          })(\"mouseenter\", function MultiSelectItem_Template_li_mouseenter_0_listener($event) {\n            return ctx.onOptionMouseEnter($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, MultiSelectItem_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, MultiSelectItem_span_4_Template, 2, 1, \"span\", 3)(5, MultiSelectItem_ng_container_5_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(16, _c0, ctx.itemSize + \"px\"))(\"ngClass\", i0.ɵɵpureFunction3(18, _c1, ctx.selected, ctx.disabled, ctx.focused))(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-label\", ctx.label)(\"aria-setsize\", ctx.ariaSetSize)(\"aria-posinset\", ctx.ariaPosInset)(\"aria-selected\", ctx.selected)(\"data-p-focused\", ctx.focused)(\"data-p-highlight\", ctx.selected)(\"data-p-disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, ctx.selected));\n          i0.ɵɵattribute(\"aria-checked\", ctx.selected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.template);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(24, _c3, ctx.option));\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, CheckIcon],\n      encapsulation: 2\n    });\n  }\n  return MultiSelectItem;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * MultiSelect is used to select multiple items from a collection.\n * @group Components\n */\nlet MultiSelect = /*#__PURE__*/(() => {\n  class MultiSelect {\n    el;\n    renderer;\n    cd;\n    zone;\n    filterService;\n    config;\n    overlayService;\n    /**\n     * Unique identifier of the component\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the overlay panel.\n     * @group Props\n     */\n    panelStyle;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    panelStyleClass;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    group;\n    /**\n     * When specified, displays an input field to filter the items on keyup.\n     * @group Props\n     */\n    filter = true;\n    /**\n     * Defines placeholder of the filter input.\n     * @group Props\n     */\n    filterPlaceHolder;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    filterLocale;\n    /**\n     * Specifies the visibility of the options panel.\n     * @group Props\n     */\n    overlayVisible;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Whether to show labels of selected item labels or use default label.\n     * @group Props\n     * @defaultValue true\n     */\n    set displaySelectedLabel(val) {\n      this._displaySelectedLabel = val;\n    }\n    get displaySelectedLabel() {\n      return this._displaySelectedLabel;\n    }\n    /**\n     * Decides how many selected item labels to show at most.\n     * @group Props\n     * @defaultValue 3\n     */\n    set maxSelectedLabels(val) {\n      this._maxSelectedLabels = val;\n    }\n    get maxSelectedLabels() {\n      return this._maxSelectedLabels;\n    }\n    /**\n     * Decides how many selected item labels to show at most.\n     * @group Props\n     */\n    selectionLimit;\n    /**\n     * Label to display after exceeding max selected labels e.g. ({0} items selected), defaults \"ellipsis\" keyword to indicate a text-overflow.\n     * @group Props\n     */\n    selectedItemsLabel = '{0} items selected';\n    /**\n     * Whether to show the checkbox at header to toggle all items at once.\n     * @group Props\n     */\n    showToggleAll = true;\n    /**\n     * Text to display when filtering does not return any results.\n     * @group Props\n     */\n    emptyFilterMessage = '';\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    emptyMessage = '';\n    /**\n     * Clears the filter value when hiding the dropdown.\n     * @group Props\n     */\n    resetFilterOnHide = false;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    dropdownIcon;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    optionLabel;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    optionValue;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    optionDisabled;\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    optionGroupLabel = 'label';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    optionGroupChildren = 'items';\n    /**\n     * Whether to show the header.\n     * @group Props\n     */\n    showHeader = true;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    filterBy;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    scrollHeight = '200px';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.\n     * @group Props\n     */\n    overlayOptions;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    ariaFilterLabel;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    filterMatchMode = 'contains';\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    tooltip = '';\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition = 'right';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    tooltipPositionStyle = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Applies focus to the filter element when the overlay is shown.\n     * @group Props\n     */\n    autofocusFilter = true;\n    /**\n     * Defines how the selected items are displayed.\n     * @group Props\n     */\n    display = 'comma';\n    /**\n     * Defines the autocomplete is active.\n     * @group Props\n     */\n    autocomplete = 'off';\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    get autoZIndex() {\n      return this._autoZIndex;\n    }\n    set autoZIndex(val) {\n      this._autoZIndex = val;\n      console.warn('The autoZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    get baseZIndex() {\n      return this._baseZIndex;\n    }\n    set baseZIndex(val) {\n      this._baseZIndex = val;\n      console.warn('The baseZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get showTransitionOptions() {\n      return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val) {\n      this._showTransitionOptions = val;\n      console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    get hideTransitionOptions() {\n      return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val) {\n      this._hideTransitionOptions = val;\n      console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Label to display when there are no selections.\n     * @group Props\n     * @deprecated Use placeholder instead.\n     */\n    set defaultLabel(val) {\n      this._defaultLabel = val;\n      console.warn('defaultLabel property is deprecated since 16.6.0, use placeholder instead');\n    }\n    get defaultLabel() {\n      return this._defaultLabel;\n    }\n    /**\n     * Label to display when there are no selections.\n     * @group Props\n     */\n    set placeholder(val) {\n      this._placeholder = val;\n    }\n    get placeholder() {\n      return this._placeholder;\n    }\n    /**\n     * An array of objects to display as the available options.\n     * @group Props\n     */\n    get options() {\n      const options = this._options();\n      return options;\n    }\n    set options(val) {\n      this._options.set(val);\n    }\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    get filterValue() {\n      return this._filterValue();\n    }\n    set filterValue(val) {\n      this._filterValue.set(val);\n    }\n    /**\n     * Item size of item to be virtual scrolled.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(val) {\n      this._itemSize = val;\n      console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * Whether all data is selected.\n     * @group Props\n     */\n    get selectAll() {\n      return this._selectAll;\n    }\n    set selectAll(value) {\n      this._selectAll = value;\n    }\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    focusOnHover = false;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    filterFields;\n    /**\n     * Determines if the option will be selected on focus.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    autoOptionFocus = true;\n    /**\n     * Callback to invoke when value changes.\n     * @param {MultiSelectChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {MultiSelectFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    onFilter = new EventEmitter();\n    /**\n     * Callback to invoke when multiselect receives focus.\n     * @param {MultiSelectFocusEvent} event - Custom focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when multiselect loses focus.\n     * @param {MultiSelectBlurEvent} event - Custom blur event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when component is clicked.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to invoke when input field is cleared.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    /**\n     * Callback to invoke when overlay panel becomes visible.\n     * @group Emits\n     */\n    onPanelShow = new EventEmitter();\n    /**\n     * Callback to invoke when overlay panel becomes hidden.\n     * @group Emits\n     */\n    onPanelHide = new EventEmitter();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {MultiSelectLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {MultiSelectRemoveEvent} event - Remove event.\n     * @group Emits\n     */\n    onRemove = new EventEmitter();\n    /**\n     * Callback to invoke when all data is selected.\n     * @param {MultiSelectSelectAllChangeEvent} event - Custom select event.\n     * @group Emits\n     */\n    onSelectAllChange = new EventEmitter();\n    containerViewChild;\n    overlayViewChild;\n    filterInputChild;\n    focusInputViewChild;\n    itemsViewChild;\n    scroller;\n    lastHiddenFocusableElementOnOverlay;\n    firstHiddenFocusableElementOnOverlay;\n    headerCheckboxViewChild;\n    footerFacet;\n    headerFacet;\n    templates;\n    searchValue;\n    searchTimeout;\n    _selectAll = null;\n    _autoZIndex;\n    _baseZIndex;\n    _showTransitionOptions;\n    _hideTransitionOptions;\n    _defaultLabel;\n    _placeholder;\n    _itemSize;\n    _selectionLimit;\n    value;\n    _filteredOptions;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    valuesAsString;\n    focus;\n    filtered;\n    itemTemplate;\n    groupTemplate;\n    loaderTemplate;\n    headerTemplate;\n    filterTemplate;\n    footerTemplate;\n    emptyFilterTemplate;\n    emptyTemplate;\n    selectedItemsTemplate;\n    checkIconTemplate;\n    filterIconTemplate;\n    removeTokenIconTemplate;\n    closeIconTemplate;\n    clearIconTemplate;\n    dropdownIconTemplate;\n    headerCheckboxFocus;\n    filterOptions;\n    preventModelTouched;\n    preventDocumentDefault;\n    focused = false;\n    itemsWrapper;\n    _displaySelectedLabel = true;\n    _maxSelectedLabels = 3;\n    modelValue = signal(null);\n    _filterValue = signal(null);\n    _options = signal(null);\n    startRangeIndex = signal(-1);\n    focusedOptionIndex = signal(-1);\n    selectedOptions;\n    get containerClass() {\n      return {\n        'p-multiselect p-component p-inputwrapper': true,\n        'p-disabled': this.disabled,\n        'p-multiselect-clearable': this.showClear && !this.disabled,\n        'p-multiselect-chip': this.display === 'chip',\n        'p-focus': this.focused\n      };\n    }\n    get inputClass() {\n      return {\n        'p-multiselect-label p-inputtext': true,\n        'p-placeholder': (this.placeholder || this.defaultLabel) && (this.label() === this.placeholder || this.label() === this.defaultLabel),\n        'p-multiselect-label-empty': !this.selectedItemsTemplate && (this.label() === 'p-emptylabel' || this.label().length === 0)\n      };\n    }\n    get panelClass() {\n      return {\n        'p-multiselect-panel p-component': true,\n        'p-input-filled': this.config.inputStyle === 'filled',\n        'p-ripple-disabled': this.config.ripple === false\n      };\n    }\n    get labelClass() {\n      return {\n        'p-multiselect-label': true,\n        'p-placeholder': this.label() === this.placeholder || this.label() === this.defaultLabel,\n        'p-multiselect-label-empty': !this.placeholder && !this.defaultLabel && (!this.modelValue() || this.modelValue().length === 0)\n      };\n    }\n    get emptyMessageLabel() {\n      return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n    get emptyFilterMessageLabel() {\n      return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n    }\n    get filled() {\n      if (typeof this.modelValue() === 'string') return !!this.modelValue();\n      return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n    get isVisibleClearIcon() {\n      return this.modelValue() != null && this.modelValue() !== '' && ObjectUtils.isNotEmpty(this.modelValue()) && this.showClear && !this.disabled && this.filled;\n    }\n    get toggleAllAriaLabel() {\n      return this.config.translation.aria ? this.config.translation.aria[this.allSelected() ? 'selectAll' : 'unselectAll'] : undefined;\n    }\n    get closeAriaLabel() {\n      return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n    visibleOptions = computed(() => {\n      const options = this.group ? this.flatOptions(this.options) : this.options || [];\n      if (this._filterValue()) {\n        const filteredOptions = this.filterService.filter(options, this.searchFields(), this._filterValue(), this.filterMatchMode, this.filterLocale);\n        if (this.group) {\n          const optionGroups = this.options || [];\n          const filtered = [];\n          optionGroups.forEach(group => {\n            const groupChildren = this.getOptionGroupChildren(group);\n            const filteredItems = groupChildren.filter(item => filteredOptions.includes(item));\n            if (filteredItems.length > 0) filtered.push({\n              ...group,\n              [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems]\n            });\n          });\n          return this.flatOptions(filtered);\n        }\n        return filteredOptions;\n      }\n      return options;\n    });\n    label = computed(() => {\n      let label;\n      const modelValue = this.modelValue();\n      if (modelValue && modelValue.length && this.displaySelectedLabel) {\n        if (ObjectUtils.isNotEmpty(this.maxSelectedLabels) && modelValue.length > this.maxSelectedLabels) {\n          return this.getSelectedItemsLabel();\n        } else {\n          label = '';\n          for (let i = 0; i < modelValue.length; i++) {\n            if (i !== 0) {\n              label += ', ';\n            }\n            label += this.getLabelByValue(modelValue[i]);\n          }\n        }\n      } else {\n        label = this.placeholder || this.defaultLabel || '';\n      }\n      return label;\n    });\n    chipSelectedItems = computed(() => {\n      return ObjectUtils.isNotEmpty(this.maxSelectedLabels) && this.modelValue() && this.modelValue().length > this.maxSelectedLabels ? this.modelValue().slice(0, this.maxSelectedLabels) : this.modelValue();\n    });\n    constructor(el, renderer, cd, zone, filterService, config, overlayService) {\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.zone = zone;\n      this.filterService = filterService;\n      this.config = config;\n      this.overlayService = overlayService;\n      effect(() => {\n        const modelValue = this.modelValue();\n        const visibleOptions = this.visibleOptions();\n        if (visibleOptions && ObjectUtils.isNotEmpty(visibleOptions) && modelValue) {\n          if (this.optionValue && this.optionLabel) {\n            this.selectedOptions = visibleOptions.filter(option => modelValue.includes(option[this.optionLabel]) || modelValue.includes(option[this.optionValue]));\n          } else {\n            this.selectedOptions = [...modelValue];\n          }\n        }\n      });\n    }\n    ngOnInit() {\n      this.id = this.id || UniqueComponentId();\n      this.autoUpdateModel();\n      if (this.filterBy) {\n        this.filterOptions = {\n          filter: value => this.onFilterInputChange(value),\n          reset: () => this.resetFilter()\n        };\n      }\n    }\n    maxSelectionLimitReached() {\n      return this.selectionLimit && this.modelValue() && this.modelValue().length === this.selectionLimit;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'group':\n            this.groupTemplate = item.template;\n            break;\n          case 'selectedItems':\n            this.selectedItemsTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'filter':\n            this.filterTemplate = item.template;\n            break;\n          case 'emptyfilter':\n            this.emptyFilterTemplate = item.template;\n            break;\n          case 'empty':\n            this.emptyTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'loader':\n            this.loaderTemplate = item.template;\n            break;\n          case 'checkicon':\n            this.checkIconTemplate = item.template;\n            break;\n          case 'filtericon':\n            this.filterIconTemplate = item.template;\n            break;\n          case 'removetokenicon':\n            this.removeTokenIconTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'dropdownicon':\n            this.dropdownIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (this.overlayVisible) {\n        this.show();\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.filtered) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => {\n            this.overlayViewChild?.alignOverlay();\n          }, 1);\n        });\n        this.filtered = false;\n      }\n    }\n    flatOptions(options) {\n      return (options || []).reduce((result, option, index) => {\n        result.push({\n          optionGroup: option,\n          group: true,\n          index\n        });\n        const optionGroupChildren = this.getOptionGroupChildren(option);\n        optionGroupChildren && optionGroupChildren.forEach(o => result.push(o));\n        return result;\n      }, []);\n    }\n    autoUpdateModel() {\n      if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption()) {\n        this.focusedOptionIndex.set(this.findFirstFocusedOptionIndex());\n        const value = this.getOptionValue(this.visibleOptions()[this.focusedOptionIndex()]);\n        this.onOptionSelect({\n          originalEvent: null,\n          option: [value]\n        });\n      }\n    }\n    /**\n     * Updates the model value.\n     * @group Method\n     */\n    updateModel(value, event) {\n      this.value = value;\n      this.onModelChange(value);\n      this.modelValue.set(value);\n    }\n    onInputClick(event) {\n      event.stopPropagation();\n      event.preventDefault();\n      this.focusedOptionIndex.set(-1);\n    }\n    onOptionSelect(event, isFocus = false, index = -1) {\n      const {\n        originalEvent,\n        option\n      } = event;\n      if (this.disabled || this.isOptionDisabled(option)) {\n        return;\n      }\n      let selected = this.isSelected(option);\n      let value = null;\n      if (selected) {\n        value = this.modelValue().filter(val => !ObjectUtils.equals(val, this.getOptionValue(option), this.equalityKey()));\n      } else {\n        value = [...(this.modelValue() || []), this.getOptionValue(option)];\n      }\n      this.updateModel(value, originalEvent);\n      index !== -1 && this.focusedOptionIndex.set(index);\n      isFocus && DomHandler.focus(this.focusInputViewChild?.nativeElement);\n      this.onChange.emit({\n        originalEvent: event,\n        value: value,\n        itemValue: option\n      });\n    }\n    findSelectedOptionIndex() {\n      return this.hasSelectedOption() ? this.visibleOptions().findIndex(option => this.isValidSelectedOption(option)) : -1;\n    }\n    onOptionSelectRange(event, start = -1, end = -1) {\n      start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n      end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n      if (start !== -1 && end !== -1) {\n        const rangeStart = Math.min(start, end);\n        const rangeEnd = Math.max(start, end);\n        const value = this.visibleOptions().slice(rangeStart, rangeEnd + 1).filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n        this.updateModel(value, event);\n      }\n    }\n    searchFields() {\n      return (this.filterBy || this.optionLabel || 'label').split(',');\n    }\n    findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n      let matchedOptionIndex = -1;\n      if (this.hasSelectedOption()) {\n        if (firstCheckUp) {\n          matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n          matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n        } else {\n          matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n          matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n        }\n      }\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    findPrevSelectedOptionIndex(index) {\n      const matchedOptionIndex = this.hasSelectedOption() && index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidSelectedOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n    }\n    findFirstFocusedOptionIndex() {\n      const selectedIndex = this.findFirstSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n    findFirstOptionIndex() {\n      return this.visibleOptions().findIndex(option => this.isValidOption(option));\n    }\n    findFirstSelectedOptionIndex() {\n      return this.hasSelectedOption() ? this.visibleOptions().findIndex(option => this.isValidSelectedOption(option)) : -1;\n    }\n    findNextSelectedOptionIndex(index) {\n      const matchedOptionIndex = this.hasSelectedOption() && index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidSelectedOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n    }\n    equalityKey() {\n      return this.optionValue ? null : this.dataKey;\n    }\n    hasSelectedOption() {\n      return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n    isValidSelectedOption(option) {\n      return this.isValidOption(option) && this.isSelected(option);\n    }\n    isOptionGroup(option) {\n      return (this.group || this.optionGroupLabel) && option.optionGroup && option.group;\n    }\n    isValidOption(option) {\n      return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n    isOptionDisabled(option) {\n      if (this.maxSelectionLimitReached() && !this.isSelected(option)) {\n        return true;\n      }\n      return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false;\n    }\n    isSelected(option) {\n      const optionValue = this.getOptionValue(option);\n      return (this.modelValue() || []).some(value => ObjectUtils.equals(value, optionValue, this.equalityKey()));\n    }\n    isOptionMatched(option) {\n      return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n    isEmpty() {\n      return !this._options() || this.visibleOptions() && this.visibleOptions().length === 0;\n    }\n    getOptionIndex(index, scrollerOptions) {\n      return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n    getAriaPosInset(index) {\n      return (this.optionGroupLabel ? index - this.visibleOptions().slice(0, index).filter(option => this.isOptionGroup(option)).length : index) + 1;\n    }\n    get ariaSetSize() {\n      return this.visibleOptions().filter(option => !this.isOptionGroup(option)).length;\n    }\n    getLabelByValue(value) {\n      const options = this.group ? this.flatOptions(this._options()) : this._options() || [];\n      const matchedOption = options.find(option => !this.isOptionGroup(option) && ObjectUtils.equals(this.getOptionValue(option), value, this.equalityKey()));\n      return matchedOption ? this.getOptionLabel(matchedOption) : null;\n    }\n    getSelectedItemsLabel() {\n      let pattern = /{(.*?)}/;\n      if (pattern.test(this.selectedItemsLabel)) {\n        return this.selectedItemsLabel.replace(this.selectedItemsLabel.match(pattern)[0], this.modelValue().length + '');\n      }\n      return this.selectedItemsLabel;\n    }\n    getOptionLabel(option) {\n      return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option && option.label != undefined ? option.label : option;\n    }\n    getOptionValue(option) {\n      return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n    getOptionGroupLabel(optionGroup) {\n      return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label != undefined ? optionGroup.label : optionGroup;\n    }\n    getOptionGroupChildren(optionGroup) {\n      return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    onKeyDown(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      const metaKey = event.metaKey || event.ctrlKey;\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'PageDown':\n          this.onPageDownKey(event);\n          break;\n        case 'PageUp':\n          this.onPageUpKey(event);\n          break;\n        case 'Enter':\n        case 'Space':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event);\n          break;\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          this.onShiftKey();\n          break;\n        default:\n          if (event.code === 'KeyA' && metaKey) {\n            const value = this.visibleOptions().filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n            this.updateModel(value, event);\n            event.preventDefault();\n            break;\n          }\n          if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n            !this.overlayVisible && this.show();\n            this.searchOptions(event, event.key);\n            event.preventDefault();\n          }\n          break;\n      }\n    }\n    onFilterKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event, true);\n          break;\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          this.onArrowLeftKey(event, true);\n          break;\n        case 'Home':\n          this.onHomeKey(event, true);\n          break;\n        case 'End':\n          this.onEndKey(event, true);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event, true);\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowLeftKey(event, pressedInInputText = false) {\n      pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n    onArrowDownKey(event) {\n      const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n      if (event.shiftKey) {\n        this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n      }\n      this.changeFocusedOptionIndex(event, optionIndex);\n      !this.overlayVisible && this.show();\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    onArrowUpKey(event, pressedInInputText = false) {\n      if (event.altKey && !pressedInInputText) {\n        if (this.focusedOptionIndex() !== -1) {\n          this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n        this.overlayVisible && this.hide();\n        event.preventDefault();\n      } else {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n        if (event.shiftKey) {\n          this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n        }\n        this.changeFocusedOptionIndex(event, optionIndex);\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n      }\n      event.stopPropagation();\n    }\n    onHomeKey(event, pressedInInputText = false) {\n      const {\n        currentTarget\n      } = event;\n      if (pressedInInputText) {\n        const len = currentTarget.value.length;\n        currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        let metaKey = event.metaKey || event.ctrlKey;\n        let optionIndex = this.findFirstOptionIndex();\n        if (event.shiftKey && metaKey) {\n          this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n        }\n        this.changeFocusedOptionIndex(event, optionIndex);\n        !this.overlayVisible && this.show();\n      }\n      event.preventDefault();\n    }\n    onEndKey(event, pressedInInputText = false) {\n      const {\n        currentTarget\n      } = event;\n      if (pressedInInputText) {\n        const len = currentTarget.value.length;\n        currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        let metaKey = event.metaKey || event.ctrlKey;\n        let optionIndex = this.findLastFocusedOptionIndex();\n        if (event.shiftKey && metaKey) {\n          this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n        }\n        this.changeFocusedOptionIndex(event, optionIndex);\n        !this.overlayVisible && this.show();\n      }\n      event.preventDefault();\n    }\n    onPageDownKey(event) {\n      this.scrollInView(this.visibleOptions().length - 1);\n      event.preventDefault();\n    }\n    onPageUpKey(event) {\n      this.scrollInView(0);\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      if (!this.overlayVisible) {\n        this.onArrowDownKey(event);\n      } else {\n        if (this.focusedOptionIndex() !== -1) {\n          if (event.shiftKey) {\n            this.onOptionSelectRange(event, this.focusedOptionIndex());\n          } else {\n            this.onOptionSelect({\n              originalEvent: event,\n              option: this.visibleOptions()[this.focusedOptionIndex()]\n            });\n          }\n        }\n      }\n      event.preventDefault();\n    }\n    onEscapeKey(event) {\n      this.overlayVisible && this.hide(true);\n      event.preventDefault();\n    }\n    onDeleteKey(event) {\n      if (this.showClear) {\n        this.clear(event);\n        event.preventDefault();\n      }\n    }\n    onTabKey(event, pressedInInputText = false) {\n      if (!pressedInInputText) {\n        if (this.overlayVisible && this.hasFocusableElements()) {\n          DomHandler.focus(event.shiftKey ? this.lastHiddenFocusableElementOnOverlay.nativeElement : this.firstHiddenFocusableElementOnOverlay.nativeElement);\n          event.preventDefault();\n        } else {\n          if (this.focusedOptionIndex() !== -1) {\n            this.onOptionSelect({\n              originalEvent: event,\n              option: this.visibleOptions()[this.focusedOptionIndex()]\n            });\n          }\n          this.overlayVisible && this.hide(this.filter);\n        }\n      }\n    }\n    onShiftKey() {\n      this.startRangeIndex.set(this.focusedOptionIndex());\n    }\n    onContainerClick(event) {\n      if (this.disabled || this.readonly || event.target.isSameNode(this.focusInputViewChild?.nativeElement)) {\n        return;\n      }\n      if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n        event.preventDefault();\n        return;\n      } else if (!this.overlayViewChild || !this.overlayViewChild.el.nativeElement.contains(event.target)) {\n        this.overlayVisible ? this.hide(true) : this.show(true);\n      }\n      this.focusInputViewChild?.nativeElement.focus({\n        preventScroll: true\n      });\n      this.onClick.emit(event);\n      this.cd.detectChanges();\n    }\n    onFirstHiddenFocus(event) {\n      const focusableEl = event.relatedTarget === this.focusInputViewChild?.nativeElement ? DomHandler.getFirstFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])') : this.focusInputViewChild?.nativeElement;\n      DomHandler.focus(focusableEl);\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n      this.onFocus.emit({\n        originalEvent: event\n      });\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      this.onBlur.emit({\n        originalEvent: event\n      });\n      if (!this.preventModelTouched) {\n        this.onModelTouched();\n      }\n      this.preventModelTouched = false;\n    }\n    onFilterInputChange(event) {\n      let value = event.target.value?.trim();\n      this._filterValue.set(value);\n      this.focusedOptionIndex.set(-1);\n      this.onFilter.emit({\n        originalEvent: event,\n        filter: this._filterValue()\n      });\n      !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n    }\n    onLastHiddenFocus(event) {\n      const focusableEl = event.relatedTarget === this.focusInputViewChild?.nativeElement ? DomHandler.getLastFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])') : this.focusInputViewChild?.nativeElement;\n      DomHandler.focus(focusableEl);\n    }\n    onOptionMouseEnter(event, index) {\n      if (this.focusOnHover) {\n        this.changeFocusedOptionIndex(event, index);\n      }\n    }\n    onHeaderCheckboxKeyDown(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      switch (event.code) {\n        case 'Space':\n          this.onToggleAll(event);\n          break;\n        case 'Enter':\n          this.onToggleAll(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onFilterBlur(event) {\n      this.focusedOptionIndex.set(-1);\n    }\n    onHeaderCheckboxFocus() {\n      this.headerCheckboxFocus = true;\n    }\n    onHeaderCheckboxBlur() {\n      this.headerCheckboxFocus = false;\n    }\n    onToggleAll(event) {\n      if (this.disabled || this.readonly) {\n        return;\n      }\n      if (this.selectAll != null) {\n        this.onSelectAllChange.emit({\n          originalEvent: event,\n          checked: !this.allSelected()\n        });\n      } else {\n        const value = this.allSelected() ? [] : this.visibleOptions().filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n        this.updateModel(value, event);\n      }\n      DomHandler.focus(this.headerCheckboxViewChild.nativeElement);\n      this.headerCheckboxFocus = true;\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    changeFocusedOptionIndex(event, index) {\n      if (this.focusedOptionIndex() !== index) {\n        this.focusedOptionIndex.set(index);\n        this.scrollInView();\n      }\n    }\n    get virtualScrollerDisabled() {\n      return !this.virtualScroll;\n    }\n    scrollInView(index = -1) {\n      const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n      if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n        const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n          element.scrollIntoView && element.scrollIntoView({\n            block: 'nearest',\n            inline: 'nearest'\n          });\n        } else if (!this.virtualScrollerDisabled) {\n          setTimeout(() => {\n            this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n          }, 0);\n        }\n      }\n    }\n    get focusedOptionId() {\n      return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n    writeValue(value) {\n      this.value = value;\n      this.modelValue.set(this.value);\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    allSelected() {\n      return this.selectAll !== null ? this.selectAll : ObjectUtils.isNotEmpty(this.visibleOptions()) && this.visibleOptions().every(option => this.isOptionGroup(option) || this.isOptionDisabled(option) || this.isSelected(option));\n    }\n    /**\n     * Displays the panel.\n     * @group Method\n     */\n    show(isFocus) {\n      this.overlayVisible = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      if (isFocus) {\n        DomHandler.focus(this.focusInputViewChild?.nativeElement);\n      }\n      this.cd.markForCheck();\n    }\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    hide(isFocus) {\n      this.overlayVisible = false;\n      this.focusedOptionIndex.set(-1);\n      if (this.filter && this.resetFilterOnHide) {\n        this.resetFilter();\n      }\n      isFocus && DomHandler.focus(this.focusInputViewChild?.nativeElement);\n      this.onPanelHide.emit();\n      this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-multiselect-items-wrapper');\n          this.virtualScroll && this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n          if (this._options() && this._options().length) {\n            if (this.virtualScroll) {\n              const selectedIndex = ObjectUtils.isNotEmpty(this.modelValue()) ? this.focusedOptionIndex() : -1;\n              if (selectedIndex !== -1) {\n                this.scroller?.scrollToIndex(selectedIndex);\n              }\n            } else {\n              let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-multiselect-item.p-highlight');\n              if (selectedListItem) {\n                selectedListItem.scrollIntoView({\n                  block: 'nearest',\n                  inline: 'center'\n                });\n              }\n            }\n          }\n          if (this.filterInputChild && this.filterInputChild.nativeElement) {\n            this.preventModelTouched = true;\n            if (this.autofocusFilter) {\n              this.filterInputChild.nativeElement.focus();\n            }\n          }\n          this.onPanelShow.emit();\n        case 'void':\n          this.itemsWrapper = null;\n          this.onModelTouched();\n          break;\n      }\n    }\n    resetFilter() {\n      if (this.filterInputChild && this.filterInputChild.nativeElement) {\n        this.filterInputChild.nativeElement.value = '';\n      }\n      this._filterValue.set(null);\n      this._filteredOptions = null;\n    }\n    close(event) {\n      this.hide();\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    clear(event) {\n      this.value = null;\n      this.updateModel(null, event);\n      this.selectedOptions = null;\n      this.onClear.emit();\n      event.stopPropagation();\n    }\n    removeOption(optionValue, event) {\n      let value = this.modelValue().filter(val => !ObjectUtils.equals(val, optionValue, this.equalityKey()));\n      this.updateModel(value, event);\n      this.onChange.emit({\n        originalEvent: event,\n        value: value,\n        itemValue: optionValue\n      });\n      event && event.stopPropagation();\n    }\n    findNextItem(item) {\n      let nextItem = item.nextElementSibling;\n      if (nextItem) return DomHandler.hasClass(nextItem.children[0], 'p-disabled') || DomHandler.isHidden(nextItem.children[0]) || DomHandler.hasClass(nextItem, 'p-multiselect-item-group') ? this.findNextItem(nextItem) : nextItem.children[0];else return null;\n    }\n    findPrevItem(item) {\n      let prevItem = item.previousElementSibling;\n      if (prevItem) return DomHandler.hasClass(prevItem.children[0], 'p-disabled') || DomHandler.isHidden(prevItem.children[0]) || DomHandler.hasClass(prevItem, 'p-multiselect-item-group') ? this.findPrevItem(prevItem) : prevItem.children[0];else return null;\n    }\n    findNextOptionIndex(index) {\n      const matchedOptionIndex = index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n    findPrevOptionIndex(index) {\n      const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    findLastSelectedOptionIndex() {\n      return this.hasSelectedOption() ? ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidSelectedOption(option)) : -1;\n    }\n    findLastFocusedOptionIndex() {\n      const selectedIndex = this.findLastSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n    findLastOptionIndex() {\n      return ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidOption(option));\n    }\n    searchOptions(event, char) {\n      this.searchValue = (this.searchValue || '') + char;\n      let optionIndex = -1;\n      let matched = false;\n      if (this.focusedOptionIndex() !== -1) {\n        optionIndex = this.visibleOptions().slice(this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option));\n        optionIndex = optionIndex === -1 ? this.visibleOptions().slice(0, this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option)) : optionIndex + this.focusedOptionIndex();\n      } else {\n        optionIndex = this.visibleOptions().findIndex(option => this.isOptionMatched(option));\n      }\n      if (optionIndex !== -1) {\n        matched = true;\n      }\n      if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n        optionIndex = this.findFirstFocusedOptionIndex();\n      }\n      if (optionIndex !== -1) {\n        this.changeFocusedOptionIndex(event, optionIndex);\n      }\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.searchValue = '';\n        this.searchTimeout = null;\n      }, 500);\n      return matched;\n    }\n    activateFilter() {\n      if (this.hasFilter() && this._options) {\n        if (this.group) {\n          let filteredGroups = [];\n          for (let optgroup of this.options) {\n            let filteredSubOptions = this.filterService.filter(this.getOptionGroupChildren(optgroup), this.searchFields(), this.filterValue, this.filterMatchMode, this.filterLocale);\n            if (filteredSubOptions && filteredSubOptions.length) {\n              filteredGroups.push({\n                ...optgroup,\n                ...{\n                  [this.optionGroupChildren]: filteredSubOptions\n                }\n              });\n            }\n          }\n          this._filteredOptions = filteredGroups;\n        } else {\n          this._filteredOptions = this.filterService.filter(this.options, this.searchFields(), this.filterValue, this.filterMatchMode, this.filterLocale);\n        }\n      } else {\n        this._filteredOptions = null;\n      }\n    }\n    hasFocusableElements() {\n      return DomHandler.getFocusableElements(this.overlayViewChild.overlayViewChild.nativeElement, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n    }\n    hasFilter() {\n      return this._filterValue() && this._filterValue().trim().length > 0;\n    }\n    static ɵfac = function MultiSelect_Factory(t) {\n      return new (t || MultiSelect)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.FilterService), i0.ɵɵdirectiveInject(i3.PrimeNGConfig), i0.ɵɵdirectiveInject(i3.OverlayService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MultiSelect,\n      selectors: [[\"p-multiSelect\"]],\n      contentQueries: function MultiSelect_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function MultiSelect_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n          i0.ɵɵviewQuery(_c8, 5);\n          i0.ɵɵviewQuery(_c9, 5);\n          i0.ɵɵviewQuery(_c10, 5);\n          i0.ɵɵviewQuery(_c11, 5);\n          i0.ɵɵviewQuery(_c12, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterInputChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.focusInputViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lastHiddenFocusableElementOnOverlay = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.firstHiddenFocusableElementOnOverlay = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCheckboxViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 4,\n      hostBindings: function MultiSelect_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-focus\", ctx.focused || ctx.overlayVisible)(\"p-inputwrapper-filled\", ctx.filled);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        ariaLabel: \"ariaLabel\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        panelStyle: \"panelStyle\",\n        panelStyleClass: \"panelStyleClass\",\n        inputId: \"inputId\",\n        disabled: \"disabled\",\n        readonly: \"readonly\",\n        group: \"group\",\n        filter: \"filter\",\n        filterPlaceHolder: \"filterPlaceHolder\",\n        filterLocale: \"filterLocale\",\n        overlayVisible: \"overlayVisible\",\n        tabindex: \"tabindex\",\n        appendTo: \"appendTo\",\n        dataKey: \"dataKey\",\n        name: \"name\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        displaySelectedLabel: \"displaySelectedLabel\",\n        maxSelectedLabels: \"maxSelectedLabels\",\n        selectionLimit: \"selectionLimit\",\n        selectedItemsLabel: \"selectedItemsLabel\",\n        showToggleAll: \"showToggleAll\",\n        emptyFilterMessage: \"emptyFilterMessage\",\n        emptyMessage: \"emptyMessage\",\n        resetFilterOnHide: \"resetFilterOnHide\",\n        dropdownIcon: \"dropdownIcon\",\n        optionLabel: \"optionLabel\",\n        optionValue: \"optionValue\",\n        optionDisabled: \"optionDisabled\",\n        optionGroupLabel: \"optionGroupLabel\",\n        optionGroupChildren: \"optionGroupChildren\",\n        showHeader: \"showHeader\",\n        filterBy: \"filterBy\",\n        scrollHeight: \"scrollHeight\",\n        lazy: \"lazy\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        overlayOptions: \"overlayOptions\",\n        ariaFilterLabel: \"ariaFilterLabel\",\n        filterMatchMode: \"filterMatchMode\",\n        tooltip: \"tooltip\",\n        tooltipPosition: \"tooltipPosition\",\n        tooltipPositionStyle: \"tooltipPositionStyle\",\n        tooltipStyleClass: \"tooltipStyleClass\",\n        autofocusFilter: \"autofocusFilter\",\n        display: \"display\",\n        autocomplete: \"autocomplete\",\n        showClear: \"showClear\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        defaultLabel: \"defaultLabel\",\n        placeholder: \"placeholder\",\n        options: \"options\",\n        filterValue: \"filterValue\",\n        itemSize: \"itemSize\",\n        selectAll: \"selectAll\",\n        focusOnHover: \"focusOnHover\",\n        filterFields: \"filterFields\",\n        selectOnFocus: \"selectOnFocus\",\n        autoOptionFocus: \"autoOptionFocus\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onFilter: \"onFilter\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onClick: \"onClick\",\n        onClear: \"onClear\",\n        onPanelShow: \"onPanelShow\",\n        onPanelHide: \"onPanelHide\",\n        onLazyLoad: \"onLazyLoad\",\n        onRemove: \"onRemove\",\n        onSelectAllChange: \"onSelectAllChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([MULTISELECT_VALUE_ACCESSOR])],\n      ngContentSelectors: _c14,\n      decls: 16,\n      vars: 41,\n      consts: [[\"container\", \"\"], [\"focusInput\", \"\"], [\"overlay\", \"\"], [\"token\", \"\"], [\"firstHiddenFocusableEl\", \"\"], [\"buildInItems\", \"\"], [\"lastHiddenFocusableEl\", \"\"], [\"builtInFilterElement\", \"\"], [\"headerCheckbox\", \"\"], [\"filterInput\", \"\"], [\"scroller\", \"\"], [\"items\", \"\"], [\"emptyFilter\", \"\"], [\"empty\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [1, \"p-hidden-accessible\"], [\"role\", \"combobox\", 3, \"focus\", \"blur\", \"keydown\", \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\"], [1, \"p-multiselect-label-container\", 3, \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\"], [3, \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-multiselect-trigger\"], [\"class\", \"p-multiselect-trigger-icon\", 4, \"ngIf\"], [3, \"visibleChange\", \"onAnimationStart\", \"onHide\", \"visible\", \"options\", \"target\", \"appendTo\", \"autoZIndex\", \"baseZIndex\", \"showTransitionOptions\", \"hideTransitionOptions\"], [\"pTemplate\", \"content\"], [\"class\", \"p-multiselect-token\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-multiselect-token\"], [1, \"p-multiselect-token-label\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-multiselect-token-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"styleClass\"], [1, \"p-multiselect-token-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-multiselect-clear-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"p-multiselect-clear-icon\", 3, \"click\"], [\"class\", \"p-multiselect-trigger-icon\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [1, \"p-multiselect-trigger-icon\", 3, \"ngClass\"], [3, \"styleClass\"], [1, \"p-multiselect-trigger-icon\"], [3, \"ngClass\", \"ngStyle\"], [\"role\", \"presentation\", 1, \"p-hidden-accessible\", \"p-hidden-focusable\", 3, \"focus\"], [\"class\", \"p-multiselect-header\", 4, \"ngIf\"], [1, \"p-multiselect-items-wrapper\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"tabindex\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [\"class\", \"p-multiselect-footer\", 4, \"ngIf\"], [1, \"p-multiselect-header\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-checkbox p-component\", 3, \"ngClass\", \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-multiselect-filter-container\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-multiselect-close\", \"p-link\", \"p-button-icon-only\", 3, \"click\"], [\"class\", \"p-multiselect-close-icon\", 4, \"ngIf\"], [1, \"p-checkbox\", \"p-component\", 3, \"click\", \"keydown\", \"ngClass\"], [\"type\", \"checkbox\", 3, \"focus\", \"blur\", \"readonly\", \"disabled\"], [\"role\", \"checkbox\", 1, \"p-checkbox-box\", 3, \"ngClass\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [1, \"p-checkbox-icon\"], [1, \"p-multiselect-filter-container\"], [\"type\", \"text\", \"role\", \"searchbox\", 1, \"p-multiselect-filter\", \"p-inputtext\", \"p-component\", 3, \"input\", \"keydown\", \"click\", \"blur\", \"value\", \"disabled\"], [\"class\", \"p-multiselect-filter-icon\", 4, \"ngIf\"], [1, \"p-multiselect-filter-icon\"], [1, \"p-multiselect-close-icon\"], [3, \"onLazyLoad\", \"items\", \"itemSize\", \"autoSize\", \"tabindex\", \"lazy\", \"options\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", \"aria-multiselectable\", \"true\", 1, \"p-multiselect-items\", \"p-component\", 3, \"ngClass\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-multiselect-empty-message\", 3, \"ngStyle\", 4, \"ngIf\"], [\"role\", \"option\", 1, \"p-multiselect-item-group\", 3, \"ngStyle\"], [3, \"onClick\", \"onMouseEnter\", \"id\", \"option\", \"selected\", \"label\", \"disabled\", \"template\", \"checkIconTemplate\", \"itemSize\", \"focused\", \"ariaPosInset\", \"ariaSetSize\"], [1, \"p-multiselect-empty-message\", 3, \"ngStyle\"], [1, \"p-multiselect-footer\"]],\n      template: function MultiSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c13);\n          i0.ɵɵelementStart(0, \"div\", 14, 0);\n          i0.ɵɵlistener(\"click\", function MultiSelect_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onContainerClick($event));\n          });\n          i0.ɵɵelementStart(2, \"div\", 15)(3, \"input\", 16, 1);\n          i0.ɵɵlistener(\"focus\", function MultiSelect_Template_input_focus_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"blur\", function MultiSelect_Template_input_blur_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          })(\"keydown\", function MultiSelect_Template_input_keydown_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyDown($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 17)(6, \"div\", 18);\n          i0.ɵɵtemplate(7, MultiSelect_ng_container_7_Template, 3, 2, \"ng-container\", 19)(8, MultiSelect_ng_container_8_Template, 1, 0, \"ng-container\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, MultiSelect_ng_container_9_Template, 3, 2, \"ng-container\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 21);\n          i0.ɵɵtemplate(11, MultiSelect_ng_container_11_Template, 3, 2, \"ng-container\", 19)(12, MultiSelect_span_12_Template, 2, 3, \"span\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-overlay\", 23, 2);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function MultiSelect_Template_p_overlay_visibleChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.overlayVisible, $event) || (ctx.overlayVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onAnimationStart\", function MultiSelect_Template_p_overlay_onAnimationStart_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOverlayAnimationStart($event));\n          })(\"onHide\", function MultiSelect_Template_p_overlay_onHide_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hide());\n          });\n          i0.ɵɵtemplate(15, MultiSelect_ng_template_15_Template, 12, 18, \"ng-template\", 24);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"pTooltip\", ctx.tooltip)(\"tooltipPosition\", ctx.tooltipPosition)(\"positionStyle\", ctx.tooltipPositionStyle)(\"tooltipStyleClass\", ctx.tooltipStyleClass);\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled)(\"id\", ctx.inputId)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-haspopup\", \"listbox\")(\"aria-expanded\", ctx.overlayVisible)(\"aria-controls\", ctx.id + \"_list\")(\"tabindex\", !ctx.disabled ? ctx.tabindex : -1)(\"aria-activedescendant\", ctx.focused ? ctx.focusedOptionId : undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tooltip)(\"tooltipPosition\", ctx.tooltipPosition)(\"positionStyle\", ctx.tooltipPositionStyle)(\"tooltipStyleClass\", ctx.tooltipStyleClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.labelClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedItemsTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.selectedItemsTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(38, _c15, ctx.selectedOptions, ctx.removeOption.bind(ctx)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisibleClearIcon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.dropdownIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dropdownIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.overlayVisible);\n          i0.ɵɵproperty(\"options\", ctx.overlayOptions)(\"target\", \"@parent\")(\"appendTo\", ctx.appendTo)(\"autoZIndex\", ctx.autoZIndex)(\"baseZIndex\", ctx.baseZIndex)(\"showTransitionOptions\", ctx.showTransitionOptions)(\"hideTransitionOptions\", ctx.hideTransitionOptions);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i4.Overlay, i3.PrimeTemplate, i5.Tooltip, i2.Ripple, i6.Scroller, CheckIcon, SearchIcon, TimesCircleIcon, TimesIcon, ChevronDownIcon, MultiSelectItem],\n      styles: [\"@layer primeng{.p-multiselect{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-multiselect-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-multiselect-label-container{overflow:hidden;flex:1 1 auto;cursor:pointer;display:flex}.p-multiselect-label{display:block;white-space:nowrap;cursor:pointer;overflow:hidden;text-overflow:ellipsis}.p-multiselect-label-empty{overflow:hidden;visibility:hidden}.p-multiselect-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-multiselect-token-icon{cursor:pointer}.p-multiselect-token-label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:100px}.p-multiselect-items-wrapper{overflow:auto}.p-multiselect-items{margin:0;padding:0;list-style-type:none}.p-multiselect-item{cursor:pointer;display:flex;align-items:center;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-multiselect-header{display:flex;align-items:center;justify-content:space-between}.p-multiselect-filter-container{position:relative;flex:1 1 auto}.p-multiselect-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-multiselect-filter-container .p-inputtext{width:100%}.p-multiselect-close{display:flex;align-items:center;justify-content:center;flex-shrink:0;overflow:hidden;position:relative}.p-fluid .p-multiselect{display:flex}.p-multiselect-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-multiselect-clearable{position:relative}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MultiSelect;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MultiSelectModule = /*#__PURE__*/(() => {\n  class MultiSelectModule {\n    static ɵfac = function MultiSelectModule_Factory(t) {\n      return new (t || MultiSelectModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MultiSelectModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, OverlayModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, CheckIcon, SearchIcon, TimesCircleIcon, TimesIcon, ChevronDownIcon, CheckIcon, OverlayModule, SharedModule, ScrollerModule]\n    });\n  }\n  return MultiSelectModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MULTISELECT_VALUE_ACCESSOR, MultiSelect, MultiSelectItem, MultiSelectModule };\n//# sourceMappingURL=primeng-multiselect.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}