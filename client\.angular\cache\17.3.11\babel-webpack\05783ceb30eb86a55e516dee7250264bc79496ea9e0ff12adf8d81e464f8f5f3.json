{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../account.service\";\nimport * as i4 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i5 from \"src/app/store/activities/activities.service\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/calendar\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"@ng-select/ng-select\";\nimport * as i14 from \"primeng/inputtext\";\nimport * as i15 from \"primeng/dialog\";\nimport * as i16 from \"primeng/editor\";\nimport * as i17 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nfunction AccountOpportunitiesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 54);\n    i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 49)(5, AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 47);\n    i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 48);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 49)(5, AccountOpportunitiesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.business_partner_owner == null ? null : opportunity_r5.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"opportunityStatus\", opportunity_r5 == null ? null : opportunity_r5.life_cycle_status_code) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r5 == null ? null : opportunity_r5.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.last_changed_by) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r5 == null ? null : opportunity_r5.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.opportunity_id) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 60);\n    i0.ɵɵtemplate(3, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_3_Template, 2, 1, \"ng-container\", 61)(4, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_4_Template, 2, 1, \"ng-container\", 61)(5, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_5_Template, 3, 4, \"ng-container\", 61)(6, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_6_Template, 2, 1, \"ng-container\", 61)(7, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_7_Template, 3, 4, \"ng-container\", 61)(8, AccountOpportunitiesComponent_ng_template_10_ng_container_6_ng_container_8_Template, 2, 1, \"ng-container\", 61);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"life_cycle_status_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"last_changed_by\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"opportunity_id\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 55)(1, \"td\", 56)(2, \"div\", 57)(3, \"div\", 58)(4, \"a\", 59);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(6, AccountOpportunitiesComponent_ng_template_10_ng_container_6_Template, 9, 7, \"ng-container\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"href\", \"/#/store/opportunities/\" + (opportunity_r5 == null ? null : opportunity_r5.opportunity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r5 == null ? null : opportunity_r5.name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 62);\n    i0.ɵɵtext(2, \" No opportunities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 62);\n    i0.ɵɵtext(2, \" Loading opportunities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Opportunities\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_25_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r7.email, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r7.mobile, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountOpportunitiesComponent_ng_template_41_span_3_Template, 2, 1, \"span\", 64)(4, AccountOpportunitiesComponent_ng_template_41_span_4_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r7.bp_id, \": \", item_r7.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.mobile);\n  }\n}\nfunction AccountOpportunitiesComponent_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_42_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_57_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"expected_revenue_amount\"].errors && ctx_r1.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_78_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_78_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_103_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"note\"].errors && ctx_r1.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport let AccountOpportunitiesComponent = /*#__PURE__*/(() => {\n  class AccountOpportunitiesComponent {\n    constructor(router, route, formBuilder, accountservice, opportunitiesservice, activitiesservice, messageservice) {\n      this.router = router;\n      this.route = route;\n      this.formBuilder = formBuilder;\n      this.accountservice = accountservice;\n      this.opportunitiesservice = opportunitiesservice;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.unsubscribe$ = new Subject();\n      this.opportunitiesdetails = [];\n      this.bp_id = '';\n      this.account_id = '';\n      this.documentId = '';\n      this.position = 'right';\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.visible = false;\n      this.owner_id = null;\n      this.dropdowns = {\n        opportunityCategory: [],\n        opportunityStatus: [],\n        opportunitySource: []\n      };\n      this.OpportunityForm = this.formBuilder.group({\n        name: ['', [Validators.required]],\n        primary_contact_party_id: ['', [Validators.required]],\n        origin_type_code: [''],\n        expected_revenue_amount: ['', [Validators.required]],\n        expected_revenue_start_date: [''],\n        expected_revenue_end_date: [''],\n        life_cycle_status_code: ['', [Validators.required]],\n        probability_percent: [''],\n        group_code: [''],\n        note: ['', [Validators.required]]\n      });\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'business_partner_owner.bp_full_name',\n        header: 'Owner'\n      }, {\n        field: 'life_cycle_status_code',\n        header: 'Status'\n      }, {\n        field: 'expected_revenue_end_date',\n        header: 'Last Updated Date'\n      }, {\n        field: 'last_changed_by',\n        header: 'Last Updated By'\n      }, {\n        field: 'expected_revenue_end_date',\n        header: 'Close Date'\n      }, {\n        field: 'opportunity_id',\n        header: 'Opportunity ID'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.opportunitiesdetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.bp_id = response.bp_id;\n          this.documentId = response?.documentId;\n          this.account_id = response.bp_id;\n          this.loadAccountByContacts(this.account_id);\n          if (this.bp_id) {\n            this.opportunitiesservice.getOpportunity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n              next: opportunityResponse => {\n                if (Array.isArray(opportunityResponse.data)) {\n                  this.opportunitiesdetails = opportunityResponse.data.slice(0, 10);\n                } else {\n                  this.opportunitiesdetails = [];\n                }\n              },\n              error: error => {\n                console.error('Error fetching Opportunities:', error);\n              }\n            });\n          }\n        }\n      });\n      this.getOwner().subscribe({\n        next: response => {\n          this.owner_id = response;\n        },\n        error: err => {\n          console.error('Error fetching bp_id:', err);\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    getOwner() {\n      return this.activitiesservice.getEmailwisePartner();\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        const options = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n        // Assign options to dropdown object\n        this.dropdowns[target] = options;\n        // Set 'Open' as default selected for activityStatus only\n        if (target === 'opportunityStatus') {\n          const openOption = options.find(opt => opt.label.toLowerCase() === 'discover');\n          if (openOption) {\n            this.OpportunityForm.get('life_cycle_status_code')?.setValue(openOption.value);\n          }\n        }\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.OpportunityForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.OpportunityForm.value\n        };\n        const data = {\n          name: value?.name,\n          prospect_party_id: _this.account_id,\n          primary_contact_party_id: value?.primary_contact_party_id,\n          origin_type_code: value?.origin_type_code,\n          expected_revenue_amount: value?.expected_revenue_amount,\n          expected_revenue_start_date: value?.expected_revenue_start_date ? _this.formatDate(value.expected_revenue_start_date) : null,\n          expected_revenue_end_date: value?.expected_revenue_end_date ? _this.formatDate(value.expected_revenue_end_date) : null,\n          life_cycle_status_code: value?.life_cycle_status_code,\n          probability_percent: value?.probability_percent,\n          group_code: value?.group_code,\n          main_employee_responsible_party_id: _this.owner_id,\n          note: value?.note\n        };\n        _this.opportunitiesservice.createOpportunity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.saving = false;\n            _this.visible = false;\n            _this.OpportunityForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Opportunities Added successfully!.'\n            });\n            _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: res => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.OpportunityForm.controls;\n    }\n    showDialog(position) {\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.OpportunityForm.reset();\n      setTimeout(() => {\n        this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n        this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n        this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n      }, 0);\n    }\n    navigateToOpportunityDetail(item) {\n      this.router.navigate(['detail', item?.opportunity_id], {\n        relativeTo: this.route,\n        state: {\n          opportunitydata: item,\n          moduleId: this.documentId\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountOpportunitiesComponent_Factory(t) {\n        return new (t || AccountOpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.OpportunitiesService), i0.ɵɵdirectiveInject(i5.ActivitiesService), i0.ɵɵdirectiveInject(i6.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountOpportunitiesComponent,\n        selectors: [[\"app-account-opportunities\"]],\n        decls: 107,\n        vars: 61,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"account-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Name\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"readonly\", \"\", \"pTooltip\", \"Account ID\", 1, \"h-3rem\", \"w-full\", 3, \"value\"], [\"for\", \"Primary Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Source\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select a Source\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Expected Value\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"expected_revenue_amount\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Create Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Expected Decision Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Probability\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter your note here...\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"note-text\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n        template: function AccountOpportunitiesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Opportunities\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_p_button_click_5_listener() {\n              return ctx.showDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountOpportunitiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function AccountOpportunitiesComponent_Template_p_table_onColReorder_8_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(9, AccountOpportunitiesComponent_ng_template_9_Template, 7, 3, \"ng-template\", 8)(10, AccountOpportunitiesComponent_ng_template_10_Template, 7, 3, \"ng-template\", 9)(11, AccountOpportunitiesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, AccountOpportunitiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"p-dialog\", 12);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AccountOpportunitiesComponent_Template_p_dialog_visibleChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(14, AccountOpportunitiesComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n            i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"div\", 15)(18, \"label\", 16)(19, \"span\", 17);\n            i0.ɵɵtext(20, \"badge\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \"Name \");\n            i0.ɵɵelementStart(22, \"span\", 18);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(24, \"input\", 19);\n            i0.ɵɵtemplate(25, AccountOpportunitiesComponent_div_25_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 15)(27, \"label\", 21)(28, \"span\", 17);\n            i0.ɵɵtext(29, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \"Account \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"input\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 15)(33, \"label\", 23)(34, \"span\", 17);\n            i0.ɵɵtext(35, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(36, \"Primary Contact \");\n            i0.ɵɵelementStart(37, \"span\", 18);\n            i0.ɵɵtext(38, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"ng-select\", 24);\n            i0.ɵɵpipe(40, \"async\");\n            i0.ɵɵtemplate(41, AccountOpportunitiesComponent_ng_template_41_Template, 5, 4, \"ng-template\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(42, AccountOpportunitiesComponent_div_42_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 26)(44, \"label\", 27)(45, \"span\", 17);\n            i0.ɵɵtext(46, \"source\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(47, \"Source \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"p-dropdown\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 26)(50, \"label\", 29)(51, \"span\", 17);\n            i0.ɵɵtext(52, \"show_chart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(53, \"Expected Value \");\n            i0.ɵɵelementStart(54, \"span\", 18);\n            i0.ɵɵtext(55, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(56, \"input\", 30);\n            i0.ɵɵtemplate(57, AccountOpportunitiesComponent_div_57_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 26)(59, \"label\", 31)(60, \"span\", 17);\n            i0.ɵɵtext(61, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(62, \"Create Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(63, \"p-calendar\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"div\", 26)(65, \"label\", 33)(66, \"span\", 17);\n            i0.ɵɵtext(67, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(68, \"Expected Decision Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(69, \"p-calendar\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"div\", 26)(71, \"label\", 35)(72, \"span\", 17);\n            i0.ɵɵtext(73, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(74, \"Status \");\n            i0.ɵɵelementStart(75, \"span\", 18);\n            i0.ɵɵtext(76, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(77, \"p-dropdown\", 36);\n            i0.ɵɵtemplate(78, AccountOpportunitiesComponent_div_78_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"div\", 26)(80, \"label\", 37)(81, \"span\", 17);\n            i0.ɵɵtext(82, \"percent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(83, \"Probability \");\n            i0.ɵɵelementStart(84, \"span\", 18);\n            i0.ɵɵtext(85, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(86, \"input\", 38);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"div\", 26)(88, \"label\", 39)(89, \"span\", 17);\n            i0.ɵɵtext(90, \"category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(91, \"Category \");\n            i0.ɵɵelementStart(92, \"span\", 18);\n            i0.ɵɵtext(93, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(94, \"p-dropdown\", 40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"div\", 41)(96, \"label\", 42)(97, \"span\", 17);\n            i0.ɵɵtext(98, \"notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(99, \"Notes \");\n            i0.ɵɵelementStart(100, \"span\", 18);\n            i0.ɵɵtext(101, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(102, \"p-editor\", 43);\n            i0.ɵɵtemplate(103, AccountOpportunitiesComponent_div_103_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(104, \"div\", 44)(105, \"button\", 45);\n            i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_105_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"button\", 46);\n            i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_106_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.opportunitiesdetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(49, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.OpportunityForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c1, ctx.submitted && ctx.f[\"name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"name\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"value\", ctx.account_id);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 47, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(52, _c1, ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunitySource\"]);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c1, ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(56, _c1, ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityCategory\"]);\n            i0.ɵɵadvance(8);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(58, _c2));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c1, ctx.submitted && ctx.f[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i7.NgSwitch, i7.NgSwitchCase, i8.Tooltip, i6.PrimeTemplate, i9.Dropdown, i10.Table, i10.SortableColumn, i10.FrozenColumn, i10.ReorderableColumn, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i11.Calendar, i12.ButtonDirective, i12.Button, i13.NgSelectComponent, i13.NgOptionTemplateDirective, i14.InputText, i15.Dialog, i16.Editor, i17.MultiSelect, i7.AsyncPipe, i7.DatePipe],\n        styles: [\".account-popup .p-dialog.p-component.p-dialog-resizable{width:calc(100vw - 510px)!important}  .account-popup .field{grid-template-columns:repeat(auto-fill,minmax(360px,1fr))}\"]\n      });\n    }\n  }\n  return AccountOpportunitiesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}