{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function single(predicate) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let singleValue;\n    let seenValue = false;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      seenValue = true;\n      if (!predicate || predicate(value, index++, source)) {\n        hasValue && subscriber.error(new SequenceError('Too many matching values'));\n        hasValue = true;\n        singleValue = value;\n      }\n    }, () => {\n      if (hasValue) {\n        subscriber.next(singleValue);\n        subscriber.complete();\n      } else {\n        subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SequenceError", "NotFoundError", "operate", "createOperatorSubscriber", "single", "predicate", "source", "subscriber", "hasValue", "singleValue", "seenValue", "index", "subscribe", "value", "error", "next", "complete"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/rxjs/dist/esm/internal/operators/single.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function single(predicate) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let singleValue;\n        let seenValue = false;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            seenValue = true;\n            if (!predicate || predicate(value, index++, source)) {\n                hasValue && subscriber.error(new SequenceError('Too many matching values'));\n                hasValue = true;\n                singleValue = value;\n            }\n        }, () => {\n            if (hasValue) {\n                subscriber.next(singleValue);\n                subscriber.complete();\n            }\n            else {\n                subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,MAAMA,CAACC,SAAS,EAAE;EAC9B,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,WAAW;IACf,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,KAAK,GAAG,CAAC;IACbL,MAAM,CAACM,SAAS,CAACT,wBAAwB,CAACI,UAAU,EAAGM,KAAK,IAAK;MAC7DH,SAAS,GAAG,IAAI;MAChB,IAAI,CAACL,SAAS,IAAIA,SAAS,CAACQ,KAAK,EAAEF,KAAK,EAAE,EAAEL,MAAM,CAAC,EAAE;QACjDE,QAAQ,IAAID,UAAU,CAACO,KAAK,CAAC,IAAId,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAC3EQ,QAAQ,GAAG,IAAI;QACfC,WAAW,GAAGI,KAAK;MACvB;IACJ,CAAC,EAAE,MAAM;MACL,IAAIL,QAAQ,EAAE;QACVD,UAAU,CAACQ,IAAI,CAACN,WAAW,CAAC;QAC5BF,UAAU,CAACS,QAAQ,CAAC,CAAC;MACzB,CAAC,MACI;QACDT,UAAU,CAACO,KAAK,CAACJ,SAAS,GAAG,IAAIT,aAAa,CAAC,oBAAoB,CAAC,GAAG,IAAIF,UAAU,CAAC,CAAC,CAAC;MAC5F;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}