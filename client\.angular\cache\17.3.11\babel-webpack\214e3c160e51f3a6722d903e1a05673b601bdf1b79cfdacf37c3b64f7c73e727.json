{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ActivitiesComponent } from './activities.component';\nimport { ActivitiesDetailsComponent } from './activities-details/activities-details.component';\nimport { ActivitiesOverviewComponent } from './activities-details/activities-overview/activities-overview.component';\nimport { ActivitiesContactsComponent } from './activities-details/activities-contacts/activities-contacts.component';\nimport { ActivitiesSalesTeamComponent } from './activities-details/activities-sales-team/activities-sales-team.component';\nimport { ActivitiesAiInsightsComponent } from './activities-details/activities-ai-insights/activities-ai-insights.component';\nimport { ActivitiesOrganizationDataComponent } from './activities-details/activities-organization-data/activities-organization-data.component';\nimport { ActivitiesAttachmentsComponent } from './activities-details/activities-attachments/activities-attachments.component';\nimport { ActivitiesNotesComponent } from './activities-details/activities-notes/activities-notes.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ActivitiesComponent\n}, {\n  path: '',\n  component: ActivitiesDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ActivitiesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: ActivitiesContactsComponent\n  }, {\n    path: 'sales-team',\n    component: ActivitiesSalesTeamComponent\n  }, {\n    path: 'ai-insights',\n    component: ActivitiesAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: ActivitiesOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: ActivitiesAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ActivitiesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class ActivitiesRoutingModule {\n  static {\n    this.ɵfac = function ActivitiesRoutingModule_Factory(t) {\n      return new (t || ActivitiesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ActivitiesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ActivitiesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ActivitiesComponent", "ActivitiesDetailsComponent", "ActivitiesOverviewComponent", "ActivitiesContactsComponent", "ActivitiesSalesTeamComponent", "ActivitiesAiInsightsComponent", "ActivitiesOrganizationDataComponent", "ActivitiesAttachmentsComponent", "ActivitiesNotesComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ActivitiesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ActivitiesComponent } from './activities.component';\r\nimport { ActivitiesDetailsComponent } from './activities-details/activities-details.component';\r\nimport { ActivitiesOverviewComponent } from './activities-details/activities-overview/activities-overview.component';\r\nimport { ActivitiesContactsComponent } from './activities-details/activities-contacts/activities-contacts.component';\r\nimport { ActivitiesSalesTeamComponent } from './activities-details/activities-sales-team/activities-sales-team.component';\r\nimport { ActivitiesAiInsightsComponent } from './activities-details/activities-ai-insights/activities-ai-insights.component';\r\nimport { ActivitiesOrganizationDataComponent } from './activities-details/activities-organization-data/activities-organization-data.component';\r\nimport { ActivitiesAttachmentsComponent } from './activities-details/activities-attachments/activities-attachments.component';\r\nimport { ActivitiesNotesComponent } from './activities-details/activities-notes/activities-notes.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ActivitiesComponent },\r\n  {\r\n    path: '',\r\n    component: ActivitiesDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: ActivitiesOverviewComponent },\r\n      { path: 'contacts', component: ActivitiesContactsComponent },\r\n      { path: 'sales-team', component: ActivitiesSalesTeamComponent },\r\n      { path: 'ai-insights', component: ActivitiesAiInsightsComponent },\r\n      { path: 'organization-data', component: ActivitiesOrganizationDataComponent },\r\n      { path: 'attachments', component: ActivitiesAttachmentsComponent },\r\n      { path: 'notes', component: ActivitiesNotesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ActivitiesRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,4BAA4B,QAAQ,4EAA4E;AACzH,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,mCAAmC,QAAQ,0FAA0F;AAC9I,SAASC,8BAA8B,QAAQ,8EAA8E;AAC7H,SAASC,wBAAwB,QAAQ,kEAAkE;;;AAE3G,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEX;AAAmB,CAAE,EAC5C;EACEU,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEV,0BAA0B;EACrCW,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAA2B,CAAE,EAC5D;IAAEQ,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAER;EAA2B,CAAE,EAC5D;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA4B,CAAE,EAC/D;IAAEM,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEN;EAA6B,CAAE,EACjE;IAAEK,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEL;EAAmC,CAAE,EAC7E;IAAEI,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEJ;EAA8B,CAAE,EAClE;IAAEG,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEH;EAAwB,CAAE,EACtD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBhB,YAAY,CAACiB,QAAQ,CAACP,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXgB,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAnB,YAAA;IAAAoB,OAAA,GAFxBpB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}