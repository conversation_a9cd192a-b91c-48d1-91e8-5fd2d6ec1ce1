{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createFind } from './find';\nexport function findIndex(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'index'));\n}", "map": {"version": 3, "names": ["operate", "createFind", "findIndex", "predicate", "thisArg"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/rxjs/dist/esm/internal/operators/findIndex.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createFind } from './find';\nexport function findIndex(predicate, thisArg) {\n    return operate(createFind(predicate, thisArg, 'index'));\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,UAAU,QAAQ,QAAQ;AACnC,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC1C,OAAOJ,OAAO,CAACC,UAAU,CAACE,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}