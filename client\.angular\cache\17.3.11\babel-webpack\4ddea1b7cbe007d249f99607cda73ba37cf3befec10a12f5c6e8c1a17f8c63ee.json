{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nconst _c0 = [\"element\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"*\"];\nconst _c3 = (a0, a1, a2) => ({\n  \"p-scroller\": true,\n  \"p-scroller-inline\": a0,\n  \"p-both-scroll\": a1,\n  \"p-horizontal-scroll\": a2\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c5 = a0 => ({\n  \"p-scroller-loading\": a0\n});\nconst _c6 = a0 => ({\n  \"p-component-overlay\": a0\n});\nconst _c7 = a0 => ({\n  numCols: a0\n});\nconst _c8 = a0 => ({\n  options: a0\n});\nconst _c9 = () => ({\n  styleClass: \"p-scroller-loading-icon\"\n});\nconst _c10 = (a0, a1) => ({\n  rows: a0,\n  columns: a1\n});\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r1.loadedItems, ctx_r1.getContentOptions()));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const index_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, item_r3, ctx_r1.getOptions(index_r4)));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c5, ctx_r1.d_loading))(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loadedItems)(\"ngForTrackBy\", ctx_r1._trackBy || ctx_r1.index);\n  }\n}\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.spacerStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"spacer\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c8, ctx_r1.getLoaderOptions(index_r5, ctx_r1.both && i0.ɵɵpureFunction1(2, _c7, ctx_r1._numItemsInViewport.cols))));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loaderArr);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c8, i0.ɵɵpureFunction0(2, _c9)));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-scroller-loading-icon pi-spin\");\n    i0.ɵɵattribute(\"data-pc-section\", \"loadingIcon\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 6)(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const buildInLoaderIcon_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderIconTemplate)(\"ngIfElse\", buildInLoaderIcon_r6);\n  }\n}\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 6)(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildInLoader_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c6, !ctx_r1.loaderTemplate));\n    i0.ɵɵattribute(\"data-pc-section\", \"loader\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderTemplate)(\"ngIfElse\", buildInLoader_r7);\n  }\n}\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7, 1);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 6)(4, Scroller_ng_container_0_ng_template_4_Template, 3, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, Scroller_ng_container_0_div_6_Template, 1, 2, \"div\", 8)(7, Scroller_ng_container_0_div_7_Template, 4, 6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const buildInContent_r8 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._style)(\"ngClass\", i0.ɵɵpureFunction3(12, _c3, ctx_r1.inline, ctx_r1.both, ctx_r1.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r1._id)(\"tabindex\", ctx_r1.tabindex)(\"data-pc-name\", \"scroller\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate)(\"ngIfElse\", buildInContent_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._showSpacer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loaderDisabled && ctx_r1._showLoader && ctx_r1.d_loading);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c4, ctx_r1.items, i0.ɵɵpureFunction2(2, _c10, ctx_r1._items, ctx_r1.loadedColumns)));\n  }\n}\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate);\n  }\n}\nlet Scroller = /*#__PURE__*/(() => {\n  class Scroller {\n    document;\n    platformId;\n    renderer;\n    cd;\n    zone;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    get id() {\n      return this._id;\n    }\n    set id(val) {\n      this._id = val;\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n      return this._style;\n    }\n    set style(val) {\n      this._style = val;\n    }\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    get styleClass() {\n      return this._styleClass;\n    }\n    set styleClass(val) {\n      this._styleClass = val;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    get tabindex() {\n      return this._tabindex;\n    }\n    set tabindex(val) {\n      this._tabindex = val;\n    }\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    get items() {\n      return this._items;\n    }\n    set items(val) {\n      this._items = val;\n    }\n    /**\n     * The height/width of item according to orientation.\n     * @group Props\n     */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(val) {\n      this._itemSize = val;\n    }\n    /**\n     * Height of the scroll viewport.\n     * @group Props\n     */\n    get scrollHeight() {\n      return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n      this._scrollHeight = val;\n    }\n    /**\n     * Width of the scroll viewport.\n     * @group Props\n     */\n    get scrollWidth() {\n      return this._scrollWidth;\n    }\n    set scrollWidth(val) {\n      this._scrollWidth = val;\n    }\n    /**\n     * The orientation of scrollbar.\n     * @group Props\n     */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(val) {\n      this._orientation = val;\n    }\n    /**\n     * Used to specify how many items to load in each load method in lazy mode.\n     * @group Props\n     */\n    get step() {\n      return this._step;\n    }\n    set step(val) {\n      this._step = val;\n    }\n    /**\n     * Delay in scroll before new data is loaded.\n     * @group Props\n     */\n    get delay() {\n      return this._delay;\n    }\n    set delay(val) {\n      this._delay = val;\n    }\n    /**\n     * Delay after window's resize finishes.\n     * @group Props\n     */\n    get resizeDelay() {\n      return this._resizeDelay;\n    }\n    set resizeDelay(val) {\n      this._resizeDelay = val;\n    }\n    /**\n     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n     * @group Props\n     */\n    get appendOnly() {\n      return this._appendOnly;\n    }\n    set appendOnly(val) {\n      this._appendOnly = val;\n    }\n    /**\n     * Specifies whether the scroller should be displayed inline or not.\n     * @group Props\n     */\n    get inline() {\n      return this._inline;\n    }\n    set inline(val) {\n      this._inline = val;\n    }\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    get lazy() {\n      return this._lazy;\n    }\n    set lazy(val) {\n      this._lazy = val;\n    }\n    /**\n     * If disabled, the scroller feature is eliminated and the content is displayed directly.\n     * @group Props\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(val) {\n      this._disabled = val;\n    }\n    /**\n     * Used to implement a custom loader instead of using the loader feature in the scroller.\n     * @group Props\n     */\n    get loaderDisabled() {\n      return this._loaderDisabled;\n    }\n    set loaderDisabled(val) {\n      this._loaderDisabled = val;\n    }\n    /**\n     * Columns to display.\n     * @group Props\n     */\n    get columns() {\n      return this._columns;\n    }\n    set columns(val) {\n      this._columns = val;\n    }\n    /**\n     * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n     * @group Props\n     */\n    get showSpacer() {\n      return this._showSpacer;\n    }\n    set showSpacer(val) {\n      this._showSpacer = val;\n    }\n    /**\n     * Defines whether to show loader.\n     * @group Props\n     */\n    get showLoader() {\n      return this._showLoader;\n    }\n    set showLoader(val) {\n      this._showLoader = val;\n    }\n    /**\n     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n     * @group Props\n     */\n    get numToleratedItems() {\n      return this._numToleratedItems;\n    }\n    set numToleratedItems(val) {\n      this._numToleratedItems = val;\n    }\n    /**\n     * Defines whether the data is loaded.\n     * @group Props\n     */\n    get loading() {\n      return this._loading;\n    }\n    set loading(val) {\n      this._loading = val;\n    }\n    /**\n     * Defines whether to dynamically change the height or width of scrollable container.\n     * @group Props\n     */\n    get autoSize() {\n      return this._autoSize;\n    }\n    set autoSize(val) {\n      this._autoSize = val;\n    }\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n     * @group Props\n     */\n    get trackBy() {\n      return this._trackBy;\n    }\n    set trackBy(val) {\n      this._trackBy = val;\n    }\n    /**\n     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    get options() {\n      return this._options;\n    }\n    set options(val) {\n      this._options = val;\n      if (val && typeof val === 'object') {\n        //@ts-ignore\n        Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n      }\n    }\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    onScroll = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position and item's range in view changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n     * @group Emits\n     */\n    onScrollIndexChange = new EventEmitter();\n    elementViewChild;\n    contentViewChild;\n    templates;\n    _id;\n    _style;\n    _styleClass;\n    _tabindex = 0;\n    _items;\n    _itemSize = 0;\n    _scrollHeight;\n    _scrollWidth;\n    _orientation = 'vertical';\n    _step = 0;\n    _delay = 0;\n    _resizeDelay = 10;\n    _appendOnly = false;\n    _inline = false;\n    _lazy = false;\n    _disabled = false;\n    _loaderDisabled = false;\n    _columns;\n    _showSpacer = true;\n    _showLoader = false;\n    _numToleratedItems;\n    _loading;\n    _autoSize = false;\n    _trackBy;\n    _options;\n    d_loading = false;\n    d_numToleratedItems;\n    contentEl;\n    contentTemplate;\n    itemTemplate;\n    loaderTemplate;\n    loaderIconTemplate;\n    first = 0;\n    last = 0;\n    page = 0;\n    isRangeChanged = false;\n    numItemsInViewport = 0;\n    lastScrollPos = 0;\n    lazyLoadState = {};\n    loaderArr = [];\n    spacerStyle = {};\n    contentStyle = {};\n    scrollTimeout;\n    resizeTimeout;\n    initialized = false;\n    windowResizeListener;\n    defaultWidth;\n    defaultHeight;\n    defaultContentWidth;\n    defaultContentHeight;\n    get vertical() {\n      return this._orientation === 'vertical';\n    }\n    get horizontal() {\n      return this._orientation === 'horizontal';\n    }\n    get both() {\n      return this._orientation === 'both';\n    }\n    get loadedItems() {\n      if (this._items && !this.d_loading) {\n        if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n      }\n      return [];\n    }\n    get loadedRows() {\n      return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n    }\n    get loadedColumns() {\n      if (this._columns && (this.both || this.horizontal)) {\n        return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n      }\n      return this._columns;\n    }\n    get isPageChanged() {\n      return this._step ? this.page !== this.getPageByFirst() : true;\n    }\n    constructor(document, platformId, renderer, cd, zone) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.zone = zone;\n    }\n    ngOnInit() {\n      this.setInitialState();\n    }\n    ngOnChanges(simpleChanges) {\n      let isLoadingChanged = false;\n      if (simpleChanges.loading) {\n        const {\n          previousValue,\n          currentValue\n        } = simpleChanges.loading;\n        if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n          this.d_loading = currentValue;\n          isLoadingChanged = true;\n        }\n      }\n      if (simpleChanges.orientation) {\n        this.lastScrollPos = this.both ? {\n          top: 0,\n          left: 0\n        } : 0;\n      }\n      if (simpleChanges.numToleratedItems) {\n        const {\n          previousValue,\n          currentValue\n        } = simpleChanges.numToleratedItems;\n        if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n          this.d_numToleratedItems = currentValue;\n        }\n      }\n      if (simpleChanges.options) {\n        const {\n          previousValue,\n          currentValue\n        } = simpleChanges.options;\n        if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n          this.d_loading = currentValue.loading;\n          isLoadingChanged = true;\n        }\n        if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n          this.d_numToleratedItems = currentValue.numToleratedItems;\n        }\n      }\n      if (this.initialized) {\n        const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n        if (isChanged) {\n          this.init();\n          this.calculateAutoSize();\n        }\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'loader':\n            this.loaderTemplate = item.template;\n            break;\n          case 'loadericon':\n            this.loaderIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      Promise.resolve().then(() => {\n        this.viewInit();\n      });\n    }\n    ngAfterViewChecked() {\n      if (!this.initialized) {\n        this.viewInit();\n      }\n    }\n    ngOnDestroy() {\n      this.unbindResizeListener();\n      this.contentEl = null;\n      this.initialized = false;\n    }\n    viewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n          this.setInitialState();\n          this.setContentEl(this.contentEl);\n          this.init();\n          this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n          this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n          this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n          this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n          this.initialized = true;\n        }\n      }\n    }\n    init() {\n      if (!this._disabled) {\n        this.setSize();\n        this.calculateOptions();\n        this.setSpacerSize();\n        this.bindResizeListener();\n        this.cd.detectChanges();\n      }\n    }\n    setContentEl(el) {\n      this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n    }\n    setInitialState() {\n      this.first = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      this.last = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      this.numItemsInViewport = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n      this.d_loading = this._loading || false;\n      this.d_numToleratedItems = this._numToleratedItems;\n      this.loaderArr = [];\n      this.spacerStyle = {};\n      this.contentStyle = {};\n    }\n    getElementRef() {\n      return this.elementViewChild;\n    }\n    getPageByFirst() {\n      return Math.floor((this.first + this.d_numToleratedItems * 4) / (this._step || 1));\n    }\n    scrollTo(options) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n      this.elementViewChild?.nativeElement?.scrollTo(options);\n    }\n    scrollToIndex(index, behavior = 'auto') {\n      const {\n        numToleratedItems\n      } = this.calculateNumItems();\n      const contentPos = this.getContentPosition();\n      const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n      const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      let newFirst = 0;\n      if (this.both) {\n        newFirst = {\n          rows: calculateFirst(index[0], numToleratedItems[0]),\n          cols: calculateFirst(index[1], numToleratedItems[1])\n        };\n        scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n      } else {\n        newFirst = calculateFirst(index, numToleratedItems);\n        this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n      }\n      this.isRangeChanged = this.first !== newFirst;\n      this.first = newFirst;\n    }\n    scrollInView(index, to, behavior = 'auto') {\n      if (to) {\n        const {\n          first,\n          viewport\n        } = this.getRenderedRange();\n        const scrollTo = (left = 0, top = 0) => this.scrollTo({\n          left,\n          top,\n          behavior\n        });\n        const isToStart = to === 'to-start';\n        const isToEnd = to === 'to-end';\n        if (isToStart) {\n          if (this.both) {\n            if (viewport.first.rows - first.rows > index[0]) {\n              scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n            } else if (viewport.first.cols - first.cols > index[1]) {\n              scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n            }\n          } else {\n            if (viewport.first - first > index) {\n              const pos = (viewport.first - 1) * this._itemSize;\n              this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n            }\n          }\n        } else if (isToEnd) {\n          if (this.both) {\n            if (viewport.last.rows - first.rows <= index[0] + 1) {\n              scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n              scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n            }\n          } else {\n            if (viewport.last - first <= index + 1) {\n              const pos = (viewport.first + 1) * this._itemSize;\n              this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n            }\n          }\n        }\n      } else {\n        this.scrollToIndex(index, behavior);\n      }\n    }\n    getRenderedRange() {\n      const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n      let firstInViewport = this.first;\n      let lastInViewport = 0;\n      if (this.elementViewChild?.nativeElement) {\n        const {\n          scrollTop,\n          scrollLeft\n        } = this.elementViewChild.nativeElement;\n        if (this.both) {\n          firstInViewport = {\n            rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n            cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n          };\n          lastInViewport = {\n            rows: firstInViewport.rows + this.numItemsInViewport.rows,\n            cols: firstInViewport.cols + this.numItemsInViewport.cols\n          };\n        } else {\n          const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n          firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n          lastInViewport = firstInViewport + this.numItemsInViewport;\n        }\n      }\n      return {\n        first: this.first,\n        last: this.last,\n        viewport: {\n          first: firstInViewport,\n          last: lastInViewport\n        }\n      };\n    }\n    calculateNumItems() {\n      const contentPos = this.getContentPosition();\n      const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n      const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n      const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n      const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n      const numItemsInViewport = this.both ? {\n        rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n        cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n      } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n      const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n      return {\n        numItemsInViewport,\n        numToleratedItems\n      };\n    }\n    calculateOptions() {\n      const {\n        numItemsInViewport,\n        numToleratedItems\n      } = this.calculateNumItems();\n      const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n      const first = this.first;\n      const last = this.both ? {\n        rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n        cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n      } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n      this.last = last;\n      this.numItemsInViewport = numItemsInViewport;\n      this.d_numToleratedItems = numToleratedItems;\n      if (this.showLoader) {\n        this.loaderArr = this.both ? Array.from({\n          length: numItemsInViewport.rows\n        }).map(() => Array.from({\n          length: numItemsInViewport.cols\n        })) : Array.from({\n          length: numItemsInViewport\n        });\n      }\n      if (this._lazy) {\n        Promise.resolve().then(() => {\n          this.lazyLoadState = {\n            first: this._step ? this.both ? {\n              rows: 0,\n              cols: first.cols\n            } : 0 : first,\n            last: Math.min(this._step ? this._step : this.last, this.items.length)\n          };\n          this.handleEvents('onLazyLoad', this.lazyLoadState);\n        });\n      }\n    }\n    calculateAutoSize() {\n      if (this._autoSize && !this.d_loading) {\n        Promise.resolve().then(() => {\n          if (this.contentEl) {\n            this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n            this.contentEl.style.position = 'relative';\n            this.elementViewChild.nativeElement.style.contain = 'none';\n            const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n            contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n            contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n            const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n            (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n            (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n            this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n            this.contentEl.style.position = '';\n            this.elementViewChild.nativeElement.style.contain = '';\n          }\n        });\n      }\n    }\n    getLast(last = 0, isCols = false) {\n      return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n    }\n    getContentPosition() {\n      if (this.contentEl) {\n        const style = getComputedStyle(this.contentEl);\n        const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n        const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n        const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n        const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n        return {\n          left,\n          right,\n          top,\n          bottom,\n          x: left + right,\n          y: top + bottom\n        };\n      }\n      return {\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0,\n        x: 0,\n        y: 0\n      };\n    }\n    setSize() {\n      if (this.elementViewChild?.nativeElement) {\n        const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n        const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n        const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n        const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n        if (this.both || this.horizontal) {\n          setProp('height', height);\n          setProp('width', width);\n        } else {\n          setProp('height', height);\n        }\n      }\n    }\n    setSpacerSize() {\n      if (this._items) {\n        const contentPos = this.getContentPosition();\n        const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = {\n          ...this.spacerStyle,\n          ...{\n            [`${_name}`]: (_value || []).length * _size + _cpos + 'px'\n          }\n        };\n        if (this.both) {\n          setProp('height', this._items, this._itemSize[0], contentPos.y);\n          setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n        } else {\n          this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n        }\n      }\n    }\n    setContentPosition(pos) {\n      if (this.contentEl && !this._appendOnly) {\n        const first = pos ? pos.first : this.first;\n        const calculateTranslateVal = (_first, _size) => _first * _size;\n        const setTransform = (_x = 0, _y = 0) => this.contentStyle = {\n          ...this.contentStyle,\n          ...{\n            transform: `translate3d(${_x}px, ${_y}px, 0)`\n          }\n        };\n        if (this.both) {\n          setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n        } else {\n          const translateVal = calculateTranslateVal(first, this._itemSize);\n          this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n        }\n      }\n    }\n    onScrollPositionChange(event) {\n      const target = event.target;\n      const contentPos = this.getContentPosition();\n      const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n      const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n      const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n        return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n      };\n      const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n        if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n      };\n      const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n        let lastValue = _first + _num + 2 * _numT;\n        if (_currentIndex >= _numT) {\n          lastValue += _numT + 1;\n        }\n        return this.getLast(lastValue, _isCols);\n      };\n      const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n      const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n      let newFirst = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      let newLast = this.last;\n      let isRangeChanged = false;\n      let newScrollPos = this.lastScrollPos;\n      if (this.both) {\n        const isScrollDown = this.lastScrollPos.top <= scrollTop;\n        const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n        if (!this._appendOnly || this._appendOnly && (isScrollDown || isScrollRight)) {\n          const currentIndex = {\n            rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n            cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n          };\n          const triggerIndex = {\n            rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n            cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n          };\n          newFirst = {\n            rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n            cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n          };\n          newLast = {\n            rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n            cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n          };\n          isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n          newScrollPos = {\n            top: scrollTop,\n            left: scrollLeft\n          };\n        }\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n        if (!this._appendOnly || this._appendOnly && isScrollDownOrRight) {\n          const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n          const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n          newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n          newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n          isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n          newScrollPos = scrollPos;\n        }\n      }\n      return {\n        first: newFirst,\n        last: newLast,\n        isRangeChanged,\n        scrollPos: newScrollPos\n      };\n    }\n    onScrollChange(event) {\n      const {\n        first,\n        last,\n        isRangeChanged,\n        scrollPos\n      } = this.onScrollPositionChange(event);\n      if (isRangeChanged) {\n        const newState = {\n          first,\n          last\n        };\n        this.setContentPosition(newState);\n        this.first = first;\n        this.last = last;\n        this.lastScrollPos = scrollPos;\n        this.handleEvents('onScrollIndexChange', newState);\n        if (this._lazy && this.isPageChanged) {\n          const lazyLoadState = {\n            first: this._step ? Math.min(this.getPageByFirst() * this._step, this.items.length - this._step) : first,\n            last: Math.min(this._step ? (this.getPageByFirst() + 1) * this._step : last, this.items.length)\n          };\n          const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n          isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n          this.lazyLoadState = lazyLoadState;\n        }\n      }\n    }\n    onContainerScroll(event) {\n      this.handleEvents('onScroll', {\n        originalEvent: event\n      });\n      if (this._delay && this.isPageChanged) {\n        if (this.scrollTimeout) {\n          clearTimeout(this.scrollTimeout);\n        }\n        if (!this.d_loading && this.showLoader) {\n          const {\n            isRangeChanged\n          } = this.onScrollPositionChange(event);\n          const changed = isRangeChanged || (this._step ? this.isPageChanged : false);\n          if (changed) {\n            this.d_loading = true;\n            this.cd.detectChanges();\n          }\n        }\n        this.scrollTimeout = setTimeout(() => {\n          this.onScrollChange(event);\n          if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n            this.d_loading = false;\n            this.page = this.getPageByFirst();\n            this.cd.detectChanges();\n          }\n        }, this._delay);\n      } else {\n        !this.d_loading && this.onScrollChange(event);\n      }\n    }\n    bindResizeListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.windowResizeListener) {\n          this.zone.runOutsideAngular(() => {\n            const window = this.document.defaultView;\n            const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n            this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n          });\n        }\n      }\n    }\n    unbindResizeListener() {\n      if (this.windowResizeListener) {\n        this.windowResizeListener();\n        this.windowResizeListener = null;\n      }\n    }\n    onWindowResize() {\n      if (this.resizeTimeout) {\n        clearTimeout(this.resizeTimeout);\n      }\n      this.resizeTimeout = setTimeout(() => {\n        if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n          const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n          const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n          const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n          reinit && this.zone.run(() => {\n            this.d_numToleratedItems = this._numToleratedItems;\n            this.defaultWidth = width;\n            this.defaultHeight = height;\n            this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n            this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n            this.init();\n          });\n        }\n      }, this._resizeDelay);\n    }\n    handleEvents(name, params) {\n      //@ts-ignore\n      return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n    }\n    getContentOptions() {\n      return {\n        contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n        items: this.loadedItems,\n        getItemOptions: index => this.getOptions(index),\n        loading: this.d_loading,\n        getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n        itemSize: this._itemSize,\n        rows: this.loadedRows,\n        columns: this.loadedColumns,\n        spacerStyle: this.spacerStyle,\n        contentStyle: this.contentStyle,\n        vertical: this.vertical,\n        horizontal: this.horizontal,\n        both: this.both\n      };\n    }\n    getOptions(renderedIndex) {\n      const count = (this._items || []).length;\n      const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n      return {\n        index,\n        count,\n        first: index === 0,\n        last: index === count - 1,\n        even: index % 2 === 0,\n        odd: index % 2 !== 0\n      };\n    }\n    getLoaderOptions(index, extOptions) {\n      const count = this.loaderArr.length;\n      return {\n        index,\n        count,\n        first: index === 0,\n        last: index === count - 1,\n        even: index % 2 === 0,\n        odd: index % 2 !== 0,\n        ...extOptions\n      };\n    }\n    static ɵfac = function Scroller_Factory(t) {\n      return new (t || Scroller)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Scroller,\n      selectors: [[\"p-scroller\"]],\n      contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Scroller_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-scroller-viewport\", \"p-element\"],\n      inputs: {\n        id: \"id\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        tabindex: \"tabindex\",\n        items: \"items\",\n        itemSize: \"itemSize\",\n        scrollHeight: \"scrollHeight\",\n        scrollWidth: \"scrollWidth\",\n        orientation: \"orientation\",\n        step: \"step\",\n        delay: \"delay\",\n        resizeDelay: \"resizeDelay\",\n        appendOnly: \"appendOnly\",\n        inline: \"inline\",\n        lazy: \"lazy\",\n        disabled: \"disabled\",\n        loaderDisabled: \"loaderDisabled\",\n        columns: \"columns\",\n        showSpacer: \"showSpacer\",\n        showLoader: \"showLoader\",\n        numToleratedItems: \"numToleratedItems\",\n        loading: \"loading\",\n        autoSize: \"autoSize\",\n        trackBy: \"trackBy\",\n        options: \"options\"\n      },\n      outputs: {\n        onLazyLoad: \"onLazyLoad\",\n        onScroll: \"onScroll\",\n        onScrollIndexChange: \"onScrollIndexChange\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c2,\n      decls: 3,\n      vars: 2,\n      consts: [[\"disabledContainer\", \"\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"content\", \"\"], [\"buildInLoader\", \"\"], [\"buildInLoaderIcon\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"scroll\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-scroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-scroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-scroller-content\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-scroller-spacer\", 3, \"ngStyle\"], [1, \"p-scroller-loader\", 3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [3, \"styleClass\"], [4, \"ngIf\"]],\n      template: function Scroller_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 16, \"ng-container\", 6)(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const disabledContainer_r9 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", disabledContainer_r9);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SpinnerIcon],\n      styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return Scroller;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ScrollerModule = /*#__PURE__*/(() => {\n  class ScrollerModule {\n    static ɵfac = function ScrollerModule_Factory(t) {\n      return new (t || ScrollerModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ScrollerModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule]\n    });\n  }\n  return ScrollerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };\n//# sourceMappingURL=primeng-scroller.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}