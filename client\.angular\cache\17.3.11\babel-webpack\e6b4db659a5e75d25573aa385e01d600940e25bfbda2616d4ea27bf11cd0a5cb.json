{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/tabview\";\nimport * as i7 from \"primeng/inputtext\";\nfunction ServiceTicketsComponent_p_tabPanel_106_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.RouterLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ServiceTicketsComponent_p_tabPanel_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 34);\n    i0.ɵɵtemplate(1, ServiceTicketsComponent_p_tabPanel_106_ng_template_1_Template, 2, 2, \"ng-template\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ServiceTicketsComponent {\n  constructor(renderer, route) {\n    this.renderer = renderer;\n    this.route = route;\n    this.bodyClass = 'service-ticket-body';\n    this.items = [{\n      label: 'Service Ticket',\n      routerLink: ['/store/service-tickets']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.id = '';\n    this.activeIndex = 0;\n    this.scrollableTabs = [{\n      label: 'Overview',\n      RouterLink: '/store/service-tickets/overview'\n    }, {\n      label: 'Contacts',\n      RouterLink: '/store/service-tickets/contacts'\n    }, {\n      label: 'AI Insights',\n      RouterLink: '/store/service-tickets/ai-insights'\n    }, {\n      label: 'Attachments',\n      RouterLink: '/store/service-tickets/attachments'\n    }, {\n      label: 'Notes',\n      RouterLink: '/store/service-tickets/notes'\n    }, {\n      label: 'Activities',\n      RouterLink: '/store/service-tickets/activities'\n    }, {\n      label: 'Relationships',\n      RouterLink: '/store/service-tickets/relationships'\n    }, {\n      label: 'Tickets',\n      RouterLink: '/store/service-tickets/tickets'\n    }, {\n      label: 'Sales Quotes',\n      RouterLink: '/store/service-tickets/sales-quotes'\n    }, {\n      label: 'Sales Orders',\n      RouterLink: '/store/service-tickets/sales-orders'\n    }, {\n      label: 'Returns',\n      RouterLink: '/store/service-tickets/returns'\n    }, {\n      label: 'Invoices',\n      RouterLink: '/store/service-tickets/invoices'\n    }];\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.renderer.addClass(document.body, this.bodyClass);\n  }\n  static {\n    this.ɵfac = function ServiceTicketsComponent_Factory(t) {\n      return new (t || ServiceTicketsComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ServiceTicketsComponent,\n      selectors: [[\"app-service-tickets\"]],\n      decls: 109,\n      vars: 11,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-3\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\", \"border-none\", \"border-bottom-2\", \"border-solid\", \"border-gray-50\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"m-0\", \"p-0\", \"ml-auto\", \"uppercase\", \"font-medium\", \"text-primary\"], [1, \"account-sec\", \"w-full\", \"border-none\", \"border-bottom-2\", \"border-solid\", \"border-gray-50\"], [1, \"acc-title\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"header-title\", \"m-0\", \"pl-3\", \"relative\"], [\"severity\", \"success\", 3, \"outlined\", \"styleClass\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [3, \"outlined\", \"styleClass\"], [1, \"account-p-tabs\", \"relative\", \"flex\", \"gap-3\", \"flex-column\"], [1, \"acc-tab-list\", \"relative\", \"flex\", \"gap-1\", \"bg-primary\", \"p-1\", \"border-round\"], [1, \"p-button\", \"p-component\", \"p-ripple\", \"p-element\", \"flex-1\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"text-900\", \"surface-0\", \"uppercase\", \"text-sm\", \"font-semibold\"], [1, \"p-button\", \"p-component\", \"p-ripple\", \"p-element\", \"flex-1\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"uppercase\", \"text-sm\", \"font-semibold\"], [1, \"acc-tab-info\", \"pb-2\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-2\", \"md:col-2\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"24715\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"0541BC220\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"Marriott\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"Team 1\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"Open\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-red-500\"], [1, \"col-12\", \"lg:col-2\", \"md:col-2\", \"pt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"pt-0\"], [1, \"details-tabs-sec\", \"mt-4\"], [1, \"details-tabs-list\"], [3, \"scrollable\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ServiceTicketsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"h4\", 5)(6, \"span\");\n          i0.ɵɵtext(7, \"Account ID:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" 24715 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7)(11, \"h5\", 8);\n          i0.ɵɵtext(12, \"Service Ticket\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"p-button\", 9)(15, \"i\", 10);\n          i0.ɵɵtext(16, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" Save \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p-button\", 11)(19, \"i\", 10);\n          i0.ɵɵtext(20, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Submit \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p-button\", 11)(23, \"i\", 10);\n          i0.ɵɵtext(24, \"ads_click \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25, \" Action \");\n          i0.ɵɵelementStart(26, \"i\", 10);\n          i0.ɵɵtext(27, \"keyboard_arrow_down \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"div\", 13)(30, \"button\", 14)(31, \"i\", 10);\n          i0.ɵɵtext(32, \"grade\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" New \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 15)(35, \"i\", 10);\n          i0.ɵɵtext(36, \"assignment_turned_in\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \" Assigned \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 15)(39, \"i\", 10);\n          i0.ɵɵtext(40, \"motion_photos_paused\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" In Progress \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 15)(43, \"i\", 10);\n          i0.ɵɵtext(44, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" Completed \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 15)(47, \"i\", 10);\n          i0.ɵɵtext(48, \"deployed_code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" On Hold \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 15)(51, \"i\", 10);\n          i0.ɵɵtext(52, \"cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Cancelled \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 16)(55, \"div\", 17)(56, \"div\", 18)(57, \"div\", 19)(58, \"label\", 20);\n          i0.ɵɵtext(59, \"Ticket ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 18)(62, \"div\", 19)(63, \"label\", 20);\n          i0.ɵɵtext(64, \"Account ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 18)(67, \"div\", 19)(68, \"label\", 20);\n          i0.ɵɵtext(69, \"Account Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 18)(72, \"div\", 19)(73, \"label\", 20);\n          i0.ɵɵtext(74, \"Support Team\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(75, \"input\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 18)(77, \"div\", 19)(78, \"label\", 20);\n          i0.ɵɵtext(79, \"Assigned To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 18)(82, \"div\", 19)(83, \"label\", 20);\n          i0.ɵɵtext(84, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(85, \"input\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 27)(87, \"div\", 19)(88, \"label\", 20);\n          i0.ɵɵtext(89, \"Priority\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(90, \"input\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 28)(92, \"div\", 19)(93, \"label\", 20);\n          i0.ɵɵtext(94, \"Subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"textarea\", 25);\n          i0.ɵɵtext(96, \"                            \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(97, \"div\", 28)(98, \"div\", 19)(99, \"label\", 20);\n          i0.ɵɵtext(100, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"textarea\", 25);\n          i0.ɵɵtext(102, \"                            \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(103, \"div\", 29)(104, \"div\", 30)(105, \"p-tabView\", 31);\n          i0.ɵɵtemplate(106, ServiceTicketsComponent_p_tabPanel_106_Template, 2, 1, \"p-tabPanel\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 33);\n          i0.ɵɵelement(108, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-6rem flex align-items-center justify-content-center gap-1\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-6rem flex align-items-center justify-content-center gap-1 text-color-secondary\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-8rem flex align-items-center justify-content-center gap-1 text-orange-600\");\n          i0.ɵɵadvance(83);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.scrollableTabs);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.Breadcrumb, i4.PrimeTemplate, i5.Button, i6.TabView, i6.TabPanel, i7.InputText],\n      styles: [\".service-ticket-body .topbar-start h1 {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvc2VydmljZS10aWNrZXRzL3NlcnZpY2UtdGlja2V0cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNJLGFBQUE7QUFBUiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCB7XHJcbiAgICAuc2VydmljZS10aWNrZXQtYm9keSAudG9wYmFyLXN0YXJ0IGgxIHtcclxuICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "RouterLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ServiceTicketsComponent_p_tabPanel_106_ng_template_1_Template", "ServiceTicketsComponent", "constructor", "renderer", "route", "bodyClass", "items", "routerLink", "home", "icon", "id", "activeIndex", "scrollableTabs", "ngOnInit", "snapshot", "paramMap", "get", "addClass", "document", "body", "ɵɵdirectiveInject", "Renderer2", "i1", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "ServiceTicketsComponent_Template", "rf", "ctx", "ɵɵelement", "ServiceTicketsComponent_p_tabPanel_106_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-service-tickets',\r\n  templateUrl: './service-tickets.component.html',\r\n  styleUrl: './service-tickets.component.scss'\r\n})\r\nexport class ServiceTicketsComponent {\r\n\r\n  private bodyClass = 'service-ticket-body';\r\n\r\n  items: MenuItem[] | any = [\r\n    { label: 'Service Ticket', routerLink: ['/store/service-tickets'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n\r\n  id: string = '';\r\n\r\n  constructor(private renderer: Renderer2, private route: ActivatedRoute) { }\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.renderer.addClass(document.body, this.bodyClass);\r\n  }\r\n\r\n  activeIndex: number = 0;\r\n  scrollableTabs: any[] = [\r\n    {\r\n      label: 'Overview',\r\n      RouterLink: '/store/service-tickets/overview',\r\n    },\r\n    {\r\n      label: 'Contacts',\r\n      RouterLink: '/store/service-tickets/contacts'\r\n    },\r\n    {\r\n      label: 'AI Insights',\r\n      RouterLink: '/store/service-tickets/ai-insights'\r\n    },\r\n    {\r\n      label: 'Attachments',\r\n      RouterLink: '/store/service-tickets/attachments'\r\n    },\r\n    {\r\n      label: 'Notes',\r\n      RouterLink: '/store/service-tickets/notes'\r\n    },\r\n    {\r\n      label: 'Activities',\r\n      RouterLink: '/store/service-tickets/activities'\r\n    },\r\n    {\r\n      label: 'Relationships',\r\n      RouterLink: '/store/service-tickets/relationships'\r\n    },\r\n    {\r\n      label: 'Tickets',\r\n      RouterLink: '/store/service-tickets/tickets'\r\n    },\r\n    {\r\n      label: 'Sales Quotes',\r\n      RouterLink: '/store/service-tickets/sales-quotes'\r\n    },\r\n    {\r\n      label: 'Sales Orders',\r\n      RouterLink: '/store/service-tickets/sales-orders'\r\n    },\r\n    {\r\n      label: 'Returns',\r\n      RouterLink: '/store/service-tickets/returns'\r\n    },\r\n    {\r\n      label: 'Invoices',\r\n      RouterLink: '/store/service-tickets/invoices'\r\n    },\r\n  ];\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div\r\n        class=\"filter-sec my-3 pb-3 flex align-items-center justify-content-between border-none border-bottom-2 border-solid border-gray-50\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <h4 class=\"m-0 p-0 ml-auto uppercase font-medium text-primary\">\r\n                <span>Account ID:</span> 24715\r\n            </h4>\r\n        </div>\r\n    </div>\r\n    <div class=\"account-sec w-full border-none border-bottom-2 border-solid border-gray-50\">\r\n        <div class=\"acc-title mb-3 flex align-items-center justify-content-between\">\r\n            <h5 class=\"header-title m-0 pl-3 relative\">Service Ticket</h5>\r\n            <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\" severity=\"success\"\r\n                    [styleClass]=\"'w-6rem flex align-items-center justify-content-center gap-1'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">save</i> Save\r\n                </p-button>\r\n                <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-6rem flex align-items-center justify-content-center gap-1 text-color-secondary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">check_circle</i> Submit\r\n                </p-button>\r\n                <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-8rem flex align-items-center justify-content-center gap-1 text-orange-600'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">ads_click </i> Action <i\r\n                        class=\"material-symbols-rounded text-2xl\">keyboard_arrow_down </i>\r\n                </p-button>\r\n            </div>\r\n        </div>\r\n        <div class=\"account-p-tabs relative flex gap-3 flex-column\">\r\n            <div class=\"acc-tab-list relative flex gap-1 bg-primary p-1 border-round\">\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 text-900 surface-0 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">grade</i> New\r\n                </button>\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">assignment_turned_in</i> Assigned\r\n                </button>\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">motion_photos_paused</i> In Progress\r\n                </button>\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">check_circle</i> Completed\r\n                </button>\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">deployed_code</i> On Hold\r\n                </button>\r\n                <button\r\n                    class=\"p-button p-component p-ripple p-element flex-1 w-full flex align-items-center justify-content-center gap-1 uppercase text-sm font-semibold\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">cancel</i> Cancelled\r\n                </button>\r\n            </div>\r\n            <div class=\"acc-tab-info pb-2\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Ticket ID</label>\r\n                            <input pInputText id=\"username\" value=\"24715\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Account ID</label>\r\n                            <input pInputText id=\"username\" value=\"0541BC220\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Account Name</label>\r\n                            <input pInputText id=\"username\" value=\"Marriott\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Support Team</label>\r\n                            <input pInputText id=\"username\" value=\"Team 1\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Assigned To</label>\r\n                            <input pInputText id=\"username\" value=\"\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Status</label>\r\n                            <input pInputText id=\"username\" value=\"Open\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-red-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-2 md:col-2 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Priority</label>\r\n                            <input pInputText id=\"username\" value=\"\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Subject</label>\r\n                            <textarea pInputText id=\"username\" value=\"\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\">\r\n                            </textarea>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Description</label>\r\n                            <textarea pInputText id=\"username\" value=\"\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\">\r\n                            </textarea>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"details-tabs-sec mt-4\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\">\r\n                <p-tabPanel *ngFor=\"let tab of scrollableTabs\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.RouterLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;ICsIwBA,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAA8E;IAC1ED,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANkCH,EAAA,CAAAI,UAAA,+BAA8B;;;AD3H7F,OAAM,MAAOQ,uBAAuB;EAWlCC,YAAoBC,QAAmB,EAAUC,KAAqB;IAAlD,KAAAD,QAAQ,GAARA,QAAQ;IAAqB,KAAAC,KAAK,GAALA,KAAK;IAT9C,KAAAC,SAAS,GAAG,qBAAqB;IAEzC,KAAAC,KAAK,GAAqB,CACxB;MAAER,KAAK,EAAE,gBAAgB;MAAES,UAAU,EAAE,CAAC,wBAAwB;IAAC,CAAE,CACpE;IACD,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAEhE,KAAAG,EAAE,GAAW,EAAE;IASf,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,cAAc,GAAU,CACtB;MACEd,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,CACF;EAzDyE;EAE1EkB,QAAQA,CAAA;IACN,IAAI,CAACH,EAAE,GAAG,IAAI,CAACN,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACb,QAAQ,CAACc,QAAQ,CAACC,QAAQ,CAACC,IAAI,EAAE,IAAI,CAACd,SAAS,CAAC;EACvD;;;uBAhBWJ,uBAAuB,EAAAZ,EAAA,CAAA+B,iBAAA,CAAA/B,EAAA,CAAAgC,SAAA,GAAAhC,EAAA,CAAA+B,iBAAA,CAAAE,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAvBtB,uBAAuB;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCN5BzC,EAHR,CAAAC,cAAA,aAA8D,aAE+E,aACzG;UACxBD,EAAA,CAAA2C,SAAA,sBAAqF;UACzF3C,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAA2C,YACwB,WACrD;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cAC7B;UAERF,EAFQ,CAAAG,YAAA,EAAK,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,aAAwF,cACR,aAC7B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAItDH,EAHR,CAAAC,cAAA,cAA2C,mBAE0C,aAChC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAC1D;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGPH,EAFJ,CAAAC,cAAA,oBACsG,aACrD;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAClE;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGPH,EAFJ,CAAAC,cAAA,oBACiG,aAChD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAC,cAAA,aACrB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAG9EF,EAH8E,CAAAG,YAAA,EAAI,EAC/D,EACT,EACJ;UAKMH,EAJZ,CAAAC,cAAA,eAA4D,eACkB,kBAEoG,aACzH;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,aAC3D;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGLH,EAFJ,CAAAC,cAAA,kBACuJ,aACtG;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,kBAC1E;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGLH,EAFJ,CAAAC,cAAA,kBACuJ,aACtG;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAC1E;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGLH,EAFJ,CAAAC,cAAA,kBACuJ,aACtG;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,mBAClE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGLH,EAFJ,CAAAC,cAAA,kBACuJ,aACtG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,iBACnE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGLH,EAFJ,CAAAC,cAAA,kBACuJ,aACtG;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,mBAC5D;UACJF,EADI,CAAAG,YAAA,EAAS,EACP;UAKUH,EAJhB,CAAAC,cAAA,eAA+B,eACJ,eACmB,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAA2C,SAAA,iBACmE;UAE3E3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAA2C,SAAA,iBAC+D;UAEvE3C,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAC,cAAA,oBAC6D;UAC7DD,EAAA,CAAAE,MAAA;UAERF,EAFQ,CAAAG,YAAA,EAAW,EACT,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAC,cAAA,qBAC6D;UAC7DD,EAAA,CAAAE,MAAA;UAMxBF,EANwB,CAAAG,YAAA,EAAW,EACT,EACJ,EACJ,EACJ,EACJ,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,gBACA,sBACI;UAC3BD,EAAA,CAAAU,UAAA,MAAAkC,+CAAA,yBAA8E;UAQtF5C,EADI,CAAAG,YAAA,EAAY,EACV;UACNH,EAAA,CAAAC,cAAA,gBAAqD;UACjDD,EAAA,CAAA2C,SAAA,sBAA+B;UAG3C3C,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;UA7IoBH,EAAA,CAAAO,SAAA,GAAe;UAAeP,EAA9B,CAAAI,UAAA,UAAAsC,GAAA,CAAAzB,KAAA,CAAe,SAAAyB,GAAA,CAAAvB,IAAA,CAAc,uCAAuC;UAYpEnB,EAAA,CAAAO,SAAA,IAAiB;UACvBP,EADM,CAAAI,UAAA,kBAAiB,6EACqD;UAGtEJ,EAAA,CAAAO,SAAA,GAAiB;UACvBP,EADM,CAAAI,UAAA,kBAAiB,kGAC0E;UAG3FJ,EAAA,CAAAO,SAAA,GAAiB;UACvBP,EADM,CAAAI,UAAA,kBAAiB,6FACqE;UA0GzFJ,EAAA,CAAAO,SAAA,IAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UACEJ,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAsC,GAAA,CAAAnB,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}