{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MenuService {\n  constructor() {\n    this.menuSource = new Subject();\n    this.resetSource = new Subject();\n    this.menuSource$ = this.menuSource.asObservable();\n    this.resetSource$ = this.resetSource.asObservable();\n  }\n  onMenuStateChange(event) {\n    this.menuSource.next(event);\n  }\n  reset() {\n    this.resetSource.next(true);\n  }\n  static {\n    this.ɵfac = function MenuService_Factory(t) {\n      return new (t || MenuService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MenuService,\n      factory: MenuService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "MenuService", "constructor", "menuSource", "resetSource", "menuSource$", "asObservable", "resetSource$", "onMenuStateChange", "event", "next", "reset", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\layout\\app.menu.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\nimport { MenuChangeEvent } from './api/menuchangeevent';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class MenuService {\r\n\r\n    private menuSource = new Subject<MenuChangeEvent>();\r\n    private resetSource = new Subject();\r\n\r\n    menuSource$ = this.menuSource.asObservable();\r\n    resetSource$ = this.resetSource.asObservable();\r\n\r\n    onMenuStateChange(event: MenuChangeEvent) {\r\n        this.menuSource.next(event);\r\n    }\r\n\r\n    reset() {\r\n        this.resetSource.next(true);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;;AAM9B,OAAM,MAAOC,WAAW;EAHxBC,YAAA;IAKY,KAAAC,UAAU,GAAG,IAAIH,OAAO,EAAmB;IAC3C,KAAAI,WAAW,GAAG,IAAIJ,OAAO,EAAE;IAEnC,KAAAK,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;;EAE9CE,iBAAiBA,CAACC,KAAsB;IACpC,IAAI,CAACN,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;EAC/B;EAEAE,KAAKA,CAAA;IACD,IAAI,CAACP,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC;EAC/B;;;uBAdST,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAW,OAAA,EAAXX,WAAW,CAAAY,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}