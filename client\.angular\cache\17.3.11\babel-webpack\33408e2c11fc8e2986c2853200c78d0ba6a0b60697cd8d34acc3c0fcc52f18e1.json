{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { NgSelectModule } from \"@ng-select/ng-select\";\nimport { MessageService, ConfirmationService } from \"primeng/api\";\nimport { AutoCompleteModule } from \"primeng/autocomplete\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { ButtonModule } from \"primeng/button\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { CheckboxModule } from \"primeng/checkbox\";\nimport { ConfirmDialogModule } from \"primeng/confirmdialog\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { EditorModule } from \"primeng/editor\";\nimport { InputTextModule } from \"primeng/inputtext\";\nimport { ProgressSpinnerModule } from \"primeng/progressspinner\";\nimport { SidebarModule } from \"primeng/sidebar\";\nimport { TableModule } from \"primeng/table\";\nimport { TabViewModule } from \"primeng/tabview\";\nimport { ToastModule } from \"primeng/toast\";\nimport { SharedModule } from \"src/app/shared/shared.module\";\nimport { CommonFormModule } from \"../common-form/common-form.module\";\nimport { MultiSelectModule } from \"primeng/multiselect\";\nimport * as i0 from \"@angular/core\";\nexport let AccountSharedModule = /*#__PURE__*/(() => {\n  class AccountSharedModule {\n    static {\n      this.ɵfac = function AccountSharedModule_Factory(t) {\n        return new (t || AccountSharedModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AccountSharedModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, NgSelectModule, ButtonModule, TabViewModule, ToastModule, CheckboxModule, ConfirmDialogModule, AutoCompleteModule, InputTextModule, ProgressSpinnerModule, SidebarModule, DialogModule, EditorModule, SharedModule, CommonFormModule, MultiSelectModule]\n      });\n    }\n  }\n  return AccountSharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}