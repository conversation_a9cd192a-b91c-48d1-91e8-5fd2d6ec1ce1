{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InitialsPipe } from './initials.pipe';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { TableModule } from 'primeng/table';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, DropdownModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [InitialsPipe],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, DropdownModule, CalendarModule],\n    exports: [InitialsPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "InitialsPipe", "FormsModule", "ReactiveFormsModule", "DialogModule", "TableModule", "NgSelectModule", "ButtonModule", "DropdownModule", "CalendarModule", "SharedModule", "declarations", "imports", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { InitialsPipe } from './initials.pipe';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { TableModule } from 'primeng/table';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CalendarModule } from 'primeng/calendar';\r\n\r\n@NgModule({\r\n  declarations: [InitialsPipe],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DialogModule,\r\n    TableModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    CalendarModule,\r\n  ],\r\n  exports: [InitialsPipe],\r\n})\r\nexport class SharedModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;;AAiBjD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAZrBV,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc;IAAA;EAAA;;;2EAILC,YAAY;IAAAC,YAAA,GAdRV,YAAY;IAAAW,OAAA,GAEzBZ,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc;IAAAI,OAAA,GAENZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}