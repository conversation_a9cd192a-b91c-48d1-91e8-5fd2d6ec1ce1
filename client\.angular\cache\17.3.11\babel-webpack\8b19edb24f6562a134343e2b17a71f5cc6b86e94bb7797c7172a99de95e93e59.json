{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let CustomerInfoComponent = /*#__PURE__*/(() => {\n  class CustomerInfoComponent {\n    constructor() {\n      this.customerDetails = null;\n    }\n    static {\n      this.ɵfac = function CustomerInfoComponent_Factory(t) {\n        return new (t || CustomerInfoComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerInfoComponent,\n        selectors: [[\"app-customer-info\"]],\n        inputs: {\n          customerDetails: \"customerDetails\"\n        },\n        decls: 66,\n        vars: 13,\n        consts: [[1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"]],\n        template: function CustomerInfoComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n            i0.ɵɵtext(3, \"Partner Id\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"span\", 3);\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 1)(7, \"span\", 2);\n            i0.ɵɵtext(8, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"span\", 3);\n            i0.ɵɵtext(10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 1)(12, \"span\", 2);\n            i0.ɵɵtext(13, \"Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\", 3);\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 1)(17, \"span\", 2);\n            i0.ɵɵtext(18, \"Company\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"span\", 3);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 1)(22, \"span\", 2);\n            i0.ɵɵtext(23, \"Partner Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"span\", 3);\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 1)(27, \"span\", 2);\n            i0.ɵɵtext(28, \"Partner Full Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"span\", 3);\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 1)(32, \"span\", 2);\n            i0.ɵɵtext(33, \"Partner Grouping \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"span\", 3);\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 1)(37, \"span\", 2);\n            i0.ɵɵtext(38, \"Partner Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"span\", 3);\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 1)(42, \"span\", 2);\n            i0.ɵɵtext(43, \"Partner UUID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\", 3);\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 1)(47, \"span\", 2);\n            i0.ɵɵtext(48, \"Partner Name 1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\", 3);\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\", 1)(52, \"span\", 2);\n            i0.ɵɵtext(53, \"Partner Name 2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"span\", 3);\n            i0.ɵɵtext(55);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"div\", 1)(57, \"span\", 2);\n            i0.ɵɵtext(58, \"Partner Name 3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"span\", 3);\n            i0.ɵɵtext(60);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(61, \"div\", 1)(62, \"span\", 2);\n            i0.ɵɵtext(63, \"Partner Name 4\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\", 3);\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.email) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.phone) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_category) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_grouping) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_type) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_uuid) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.org_bp_name1) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.org_bp_name2) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.org_bp_name3) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.org_bp_name4) || \"-\");\n          }\n        }\n      });\n    }\n  }\n  return CustomerInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}