{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UsersService {\n  constructor(http) {\n    this.http = http;\n    this.userSubject = new BehaviorSubject(null);\n    this.user = this.userSubject.asObservable();\n  }\n  createUser(userData) {\n    return this.http.post(`${CMS_APIContstant.USER_DETAILS}`, userData);\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][email][$containsi]', searchTerm).set('filters[$or][1][username][$containsi]', searchTerm).set('filters[$or][2][firstname][$containsi]', searchTerm).set('filters[$or][3][lastname][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?populate=*`, {\n      params\n    });\n  }\n  getUserRoles() {\n    return this.http.get(`${CMS_APIContstant.USER_ROLES}`);\n  }\n  getUserForallRoles(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate=*`);\n  }\n  getUserByIDName(data) {\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`);\n  }\n  getAllCustomers() {\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`);\n  }\n  getUserByCustomer(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('populate[business_partner][populate]', 'addresses').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', searchTerm).set('filters[$or][1][customer_name][$containsi]', searchTerm).set('filters[$or][2][business_partner][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}/customers`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  updateAdminUser(userId, updatedData) {\n    return this.http.put(`${ApiConstant.ADMIN_USERS}/${userId}`, updatedData);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  unlinkCustomers(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/unlink-all-customer`);\n  }\n  linkCustomers(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/link-all-customer`);\n  }\n  static {\n    this.ɵfac = function UsersService_Factory(t) {\n      return new (t || UsersService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UsersService,\n      factory: UsersService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "ApiConstant", "CMS_APIContstant", "UsersService", "constructor", "http", "userSubject", "user", "asObservable", "createUser", "userData", "post", "USER_DETAILS", "getUsers", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "getUserRoles", "USER_ROLES", "getUserForallRoles", "userId", "getUserByID", "id", "getUserByIDName", "data", "CUSTOMERS", "getAllCustomers", "getUserByCustomer", "updateUser", "updatedData", "put", "updateAdminUser", "ADMIN_USERS", "getCustomers", "appendAll", "unlinkCustomers", "linkCustomers", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\profile\\users.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class UsersService {\r\n  public userSubject = new BehaviorSubject<any>(null);\r\n  public user = this.userSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createUser(userData: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.USER_DETAILS}`, userData);\r\n  }\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][email][$containsi]', searchTerm)\r\n        .set('filters[$or][1][username][$containsi]', searchTerm)\r\n        .set('filters[$or][2][firstname][$containsi]', searchTerm)\r\n        .set('filters[$or][3][lastname][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/list?populate=*`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserRoles() {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.USER_ROLES}`);\r\n  }\r\n\r\n  getUserForallRoles(userId: string): Observable<any> {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate=*`\r\n    );\r\n  }\r\n\r\n  getUserByIDName(data: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`\r\n    );\r\n  }\r\n\r\n  getAllCustomers() {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`);\r\n  }\r\n\r\n  getUserByCustomer(\r\n    id: any,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('populate[business_partner][populate]', 'addresses')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][customer_name][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][2][business_partner][phone][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}/customers`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n\r\n  updateAdminUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(`${ApiConstant.ADMIN_USERS}/${userId}`, updatedData);\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  unlinkCustomers(userId: string) {\r\n    return this.http.get(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}/unlink-all-customer`\r\n    );\r\n  }\r\n\r\n  linkCustomers(userId: string) {\r\n    return this.http.get(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}/link-all-customer`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAyB,MAAM;AACvD,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;;;AAK/E,OAAM,MAAOC,YAAY;EAIvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,WAAW,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC5C,KAAAO,IAAI,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EAEN;EAEvCC,UAAUA,CAACC,QAAa;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,YAAY,EAAE,EAAEF,QAAQ,CAAC;EACrE;EAEAG,QAAQA,CACNC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIpB,UAAU,EAAE,CAC1BqB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC;IAC7D;IACA,OAAO,IAAI,CAACb,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,kBAAkB,EAClD;MACEO;KACD,CACF;EACH;EAEAM,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpB,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAACwB,UAAU,EAAE,CAAC;EAC/D;EAEAC,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAACvB,IAAI,CAACmB,GAAG,CAAC,GAAGtB,gBAAgB,CAACU,YAAY,IAAIgB,MAAM,KAAK,CAAC;EACvE;EAEAC,WAAWA,CAACC,EAAO;IACjB,OAAO,IAAI,CAACzB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIkB,EAAE,aAAa,CACpD;EACH;EAEAC,eAAeA,CAACC,IAAS;IACvB,OAAO,IAAI,CAAC3B,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAAC+B,SAAS,wBAAwBD,IAAI,EAAE,CAC5D;EACH;EAEAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC7B,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAAC+B,SAAS,EAAE,CAAC;EAC9D;EAEAE,iBAAiBA,CACfL,EAAO,EACPhB,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIpB,UAAU,EAAE,CAC1BqB,GAAG,CAAC,sCAAsC,EAAE,WAAW,CAAC,CACxDA,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CACF,sDAAsD,EACtDF,UAAU,CACX;IACL;IAEA,OAAO,IAAI,CAACb,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIkB,EAAE,YAAY,EAClD;MACEX;KACD,CACF;EACH;EAEAiB,UAAUA,CAACR,MAAc,EAAES,WAAgB;IACzC,OAAO,IAAI,CAAChC,IAAI,CAACiC,GAAG,CAClB,GAAGpC,gBAAgB,CAACU,YAAY,IAAIgB,MAAM,EAAE,EAC5CS,WAAW,CACZ;EACH;EAEAE,eAAeA,CAACX,MAAc,EAAES,WAAgB;IAC9C,OAAO,IAAI,CAAChC,IAAI,CAACiC,GAAG,CAAC,GAAGrC,WAAW,CAACuC,WAAW,IAAIZ,MAAM,EAAE,EAAES,WAAW,CAAC;EAC3E;EAEAI,YAAYA,CAACT,IAAS;IACpB,MAAMb,MAAM,GAAG,IAAIpB,UAAU,EAAE,CAAC2C,SAAS,CAAC;MAAE,GAAGV;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAAC3B,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAAC+B,SAAS,EAAE,EAAE;MAC3Dd;KACD,CAAC;EACJ;EAEAwB,eAAeA,CAACf,MAAc;IAC5B,OAAO,IAAI,CAACvB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIgB,MAAM,sBAAsB,CACjE;EACH;EAEAgB,aAAaA,CAAChB,MAAc;IAC1B,OAAO,IAAI,CAACvB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIgB,MAAM,oBAAoB,CAC/D;EACH;;;uBA7HWzB,YAAY,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZ7C,YAAY;MAAA8C,OAAA,EAAZ9C,YAAY,CAAA+C,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}