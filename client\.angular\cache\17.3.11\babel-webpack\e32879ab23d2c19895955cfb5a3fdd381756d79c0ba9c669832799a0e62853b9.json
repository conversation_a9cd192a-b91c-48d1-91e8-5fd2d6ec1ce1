{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HomeRoutingModule } from './home-routing.module';\nimport { GalleriaModule } from 'primeng/galleria';\nimport * as i0 from \"@angular/core\";\nexport let HomeModule = /*#__PURE__*/(() => {\n  class HomeModule {\n    static {\n      this.ɵfac = function HomeModule_Factory(t) {\n        return new (t || HomeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: HomeModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, HomeRoutingModule, GalleriaModule]\n      });\n    }\n  }\n  return HomeModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}