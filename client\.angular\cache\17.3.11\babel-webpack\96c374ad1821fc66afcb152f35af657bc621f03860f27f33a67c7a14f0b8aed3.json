{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../opportunities.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/toast\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tabview\";\nimport * as i10 from \"primeng/breadcrumb\";\nimport * as i11 from \"primeng/confirmdialog\";\nimport * as i12 from \"../../../shared/initials.pipe\";\nfunction OpportunitiesDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction OpportunitiesDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 32);\n    i0.ɵɵtemplate(1, OpportunitiesDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class OpportunitiesDetailsComponent {\n  constructor(router, route, opportunitiesservice) {\n    this.router = router;\n    this.route = route;\n    this.opportunitiesservice = opportunitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.opportunityDetails = null;\n    this.sidebarDetails = null;\n    this.items = [];\n    this.id = '';\n    this.partner_role = '';\n    this.breadcrumbitems = [];\n    this.activeItem = null;\n    this.isSidebarHidden = false;\n    this.Actions = [];\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'Copy',\n      code: 'CO'\n    }, {\n      name: 'Preview',\n      code: 'PW'\n    }];\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const opportunityId = params.get('id');\n      if (opportunityId) {\n        this.loadOpportunityData(opportunityId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const partner_role = response?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n      this.partner_role = partner_role?.bp_full_name || null;\n      this.opportunityDetails = response || null;\n      this.sidebarDetails = this.formatSidebarDetails(response?.business_partner?.addresses || []);\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/opportunities/${id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/opportunities/${id}/contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/opportunities/${id}/sales-team`\n    }, {\n      label: 'Follow Up',\n      routerLink: `/store/opportunities/${id}/follow-up`\n    },\n    // {\n    //   label: 'Hierarchy',\n    //   routerLink: `/store/opportunities/${id}/hierarchy`,\n    // },\n    // {\n    //   label: 'Document Flow',\n    //   routerLink: `/store/opportunities/${id}/document-flow`,\n    // },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/opportunities/${id}/ai-insights`,\n    // },\n    // {\n    //   label: 'Organization Data',\n    //   routerLink: `/store/opportunities/${id}/organization-data`,\n    // },\n    {\n      label: 'Attachments',\n      routerLink: `/store/opportunities/${id}/attachments`\n    }\n    // {\n    //   label: 'Notes',\n    //   routerLink: `/store/opportunities/${id}/notes`,\n    // },\n    ];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Opportunities',\n      routerLink: ['/store/opportunities']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadOpportunityData(activityId) {\n    this.opportunitiesservice.getOpportunityByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.opportunityDetails = response?.data[0] || null;\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/opportunities']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesDetailsComponent_Factory(t) {\n      return new (t || OpportunitiesDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesDetailsComponent,\n      selectors: [[\"app-opportunities-details\"]],\n      decls: 77,\n      vars: 29,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\", \"sidebar-c-details\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function OpportunitiesDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function OpportunitiesDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function OpportunitiesDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, OpportunitiesDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n          i0.ɵɵtext(26, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"li\", 21)(29, \"span\", 22);\n          i0.ɵɵtext(30, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"li\", 21)(33, \"span\", 22);\n          i0.ɵɵtext(34, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"ul\", 24)(38, \"li\", 25)(39, \"span\", 26)(40, \"i\", 27);\n          i0.ɵɵtext(41, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\", 28);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 25)(46, \"span\", 26)(47, \"i\", 27);\n          i0.ɵɵtext(48, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 28);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"li\", 25)(53, \"span\", 26)(54, \"i\", 27);\n          i0.ɵɵtext(55, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"span\", 28);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"li\", 25)(60, \"span\", 26)(61, \"i\", 27);\n          i0.ɵɵtext(62, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 28);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"li\", 25)(67, \"span\", 26)(68, \"i\", 27);\n          i0.ɵɵtext(69, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 28);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(73, \"div\", 29)(74, \"p-button\", 30);\n          i0.ɵɵlistener(\"click\", function OpportunitiesDetailsComponent_Template_p_button_click_74_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(75, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(76, \"p-confirmDialog\", 31);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 27, (ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_full_name) || \"-\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.contact_companies == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0] == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.contact_companies == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0] == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i3.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.NgControlStatus, i4.NgModel, i5.PrimeTemplate, i6.Toast, i7.Button, i8.Dropdown, i9.TabView, i9.TabPanel, i10.Breadcrumb, i11.ConfirmDialog, i12.InitialsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "OpportunitiesDetailsComponent_p_tabPanel_9_ng_template_1_Template", "OpportunitiesDetailsComponent", "constructor", "router", "route", "opportunitiesservice", "unsubscribe$", "opportunityDetails", "sidebarDetails", "items", "id", "partner_role", "breadcrumbitems", "activeItem", "isSidebarHidden", "Actions", "activeIndex", "ngOnInit", "snapshot", "paramMap", "get", "home", "icon", "name", "code", "makeMenuItems", "length", "setActiveTabFromURL", "pipe", "subscribe", "params", "opportunityId", "loadOpportunityData", "events", "opportunity", "response", "business_partner", "customer", "partner_functions", "find", "p", "partner_function", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "activityId", "getOpportunityByID", "next", "data", "error", "console", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "OpportunitiesService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "OpportunitiesDetailsComponent_Template_p_dropdown_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "OpportunitiesDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵlistener", "OpportunitiesDetailsComponent_Template_p_tabView_onChange_8_listener", "OpportunitiesDetailsComponent_p_tabPanel_9_Template", "OpportunitiesDetailsComponent_Template_p_button_click_74_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "bp_id", "contact_companies", "business_partner_person", "first_name", "last_name"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { OpportunitiesService } from '../opportunities.service';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-opportunities-details',\r\n  templateUrl: './opportunities-details.component.html',\r\n  styleUrl: './opportunities-details.component.scss',\r\n})\r\nexport class OpportunitiesDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public opportunityDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public items: MenuItem[] = [];\r\n  public home: MenuItem | any;\r\n  public id: string = '';\r\n  public partner_role: string = '';\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public isSidebarHidden = false;\r\n  public Actions: Actions[] = [];\r\n  public selectedActions: Actions | undefined;\r\n  public activeIndex: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private opportunitiesservice: OpportunitiesService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'Copy', code: 'CO' },\r\n      { name: 'Preview', code: 'PW' },\r\n    ];\r\n\r\n    this.makeMenuItems(this.id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const opportunityId = params.get('id');\r\n        if (opportunityId) {\r\n          this.loadOpportunityData(opportunityId);\r\n        }\r\n      });\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const partner_role =\r\n          response?.business_partner?.customer?.partner_functions?.find(\r\n            (p: any) => p.partner_function === 'YI'\r\n          );\r\n        this.partner_role = partner_role?.bp_full_name || null;\r\n        this.opportunityDetails = response || null;\r\n        this.sidebarDetails = this.formatSidebarDetails(\r\n          response?.business_partner?.addresses || []\r\n        );\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/opportunities/${id}/overview`,\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        routerLink: `/store/opportunities/${id}/contacts`,\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        routerLink: `/store/opportunities/${id}/sales-team`,\r\n      },\r\n      {\r\n        label: 'Follow Up',\r\n        routerLink: `/store/opportunities/${id}/follow-up`,\r\n      },\r\n      // {\r\n      //   label: 'Hierarchy',\r\n      //   routerLink: `/store/opportunities/${id}/hierarchy`,\r\n      // },\r\n      // {\r\n      //   label: 'Document Flow',\r\n      //   routerLink: `/store/opportunities/${id}/document-flow`,\r\n      // },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/opportunities/${id}/ai-insights`,\r\n      // },\r\n      // {\r\n      //   label: 'Organization Data',\r\n      //   routerLink: `/store/opportunities/${id}/organization-data`,\r\n      // },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/opportunities/${id}/attachments`,\r\n      },\r\n      // {\r\n      //   label: 'Notes',\r\n      //   routerLink: `/store/opportunities/${id}/notes`,\r\n      // },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Opportunities', routerLink: ['/store/opportunities'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadOpportunityData(activityId: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityByID(activityId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.opportunityDetails = response?.data[0] || null;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/opportunities']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative flex-nowrap\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full sidebar-c-details\"\r\n                    [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">{{\r\n                                        (opportunityDetails?.business_partner?.bp_full_name || \"-\") | initials }}</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{opportunityDetails?.business_partner?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">CRM ID</span> :\r\n                                            {{opportunityDetails?.business_partner?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Account Owner </span> : {{partner_role || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Main Contact</span> : {{\r\n                                            (opportunityDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (opportunityDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog class=\"confirm-popup\"></p-confirmDialog>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;ICcjBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,iEAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADCnG,OAAM,MAAOQ,6BAA6B;EAexCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C;IAF1C,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IAjBtB,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;IACnC,KAAAoB,kBAAkB,GAAQ,IAAI;IAC9B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,KAAK,GAAe,EAAE;IAEtB,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,UAAU,GAAoB,IAAI;IAClC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAc,EAAE;IAEvB,KAAAC,WAAW,GAAW,CAAC;EAM3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACP,EAAE,GAAG,IAAI,CAACN,KAAK,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE3B,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACoB,OAAO,GAAG,CACb;MAAEQ,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,CAChC;IAED,IAAI,CAACC,aAAa,CAAC,IAAI,CAACf,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACD,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACb,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACkB,mBAAmB,EAAE;IAE1B,IAAI,CAACvB,KAAK,CAACe,QAAQ,CAChBS,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,aAAa,GAAGD,MAAM,CAACV,GAAG,CAAC,IAAI,CAAC;MACtC,IAAIW,aAAa,EAAE;QACjB,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC;MACzC;IACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC5B,MAAM,CAAC8B,MAAM,CAACL,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAACuB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACtB,oBAAoB,CAAC6B,WAAW,CAClCN,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAEM,QAAa,IAAI;MAC3B,MAAMxB,YAAY,GAChBwB,QAAQ,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC1DC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;MACH,IAAI,CAAC9B,YAAY,GAAGA,YAAY,EAAE+B,YAAY,IAAI,IAAI;MACtD,IAAI,CAACnC,kBAAkB,GAAG4B,QAAQ,IAAI,IAAI;MAC1C,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACmC,oBAAoB,CAC7CR,QAAQ,EAAEC,gBAAgB,EAAEQ,SAAS,IAAI,EAAE,CAC5C;IACH,CAAC,CAAC;EACN;EAEQD,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAvC,aAAaA,CAACf,EAAU;IACtB,IAAI,CAACD,KAAK,GAAG,CACX;MACEX,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,wBAAwBe,EAAE;KACvC,EACD;MACEZ,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,wBAAwBe,EAAE;KACvC,EACD;MACEZ,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,wBAAwBe,EAAE;KACvC,EACD;MACEZ,KAAK,EAAE,WAAW;MAClBH,UAAU,EAAE,wBAAwBe,EAAE;KACvC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEZ,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,wBAAwBe,EAAE;;IAExC;IACA;IACA;IACA;IAAA,CACD;EACH;EAEAiB,mBAAmBA,CAAA;IACjB,MAAMuC,QAAQ,GAAG,IAAI,CAAC/D,MAAM,CAACgE,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAAC7D,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAM6C,UAAU,GAAG,IAAI,CAAC9D,KAAK,CAAC+D,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAAC9E,UAAU,CAAC+E,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAACpD,WAAW,GAAGuD,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAAC1D,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,IAAI,CAACO,WAAW,CAAC,IAAI,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAAC9D,UAAU,EAAEf,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA6E,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAChE,eAAe,GAAG,CACrB;MAAEd,KAAK,EAAE,eAAe;MAAEH,UAAU,EAAE,CAAC,sBAAsB;IAAC,CAAE,EAChE;MAAEG,KAAK,EAAE8E,SAAS;MAAEjF,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAkF,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACrE,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACV,WAAW,GAAG8D,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACvE,KAAK,CAAC,IAAI,CAACO,WAAW,CAAC;IAEhD,IAAIgE,WAAW,EAAErF,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAAC8E,aAAa,CAACD,WAAW,CAACrF,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQqC,mBAAmBA,CAACkD,UAAkB;IAC5C,IAAI,CAAC7E,oBAAoB,CACtB8E,kBAAkB,CAACD,UAAU,CAAC,CAC9BtD,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAC;MACTuD,IAAI,EAAGjD,QAAa,IAAI;QACtB,IAAI,CAAC5B,kBAAkB,GAAG4B,QAAQ,EAAEkD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACrD,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACrF,MAAM,CAACsF,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAChD;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC5E,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA6E,WAAWA,CAAA;IACT,IAAI,CAACrF,YAAY,CAAC8E,IAAI,EAAE;IACxB,IAAI,CAAC9E,YAAY,CAACsF,QAAQ,EAAE;EAC9B;;;uBAhMW3F,6BAA6B,EAAAZ,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1G,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA3G,EAAA,CAAAwG,iBAAA,CAAAI,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA7BjG,6BAA6B;MAAAkG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB1CpH,EAAA,CAAAsH,SAAA,iBAAuD;UAG/CtH,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAsH,SAAA,sBAA+F;UACnGtH,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBACyG;UADzED,EAAA,CAAAuH,gBAAA,2BAAAC,2EAAAC,MAAA;YAAAzH,EAAA,CAAA0H,kBAAA,CAAAL,GAAA,CAAAM,eAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAM,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAEjEzH,EAFI,CAAAG,YAAA,EACyG,EACvG;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAuH,gBAAA,+BAAAK,8EAAAH,MAAA;YAAAzH,EAAA,CAAA0H,kBAAA,CAAAL,GAAA,CAAA1F,WAAA,EAAA8F,MAAA,MAAAJ,GAAA,CAAA1F,WAAA,GAAA8F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACzH,EAAA,CAAA6H,UAAA,sBAAAC,qEAAAL,MAAA;YAAA,OAAYJ,GAAA,CAAA7B,WAAA,CAAAiC,MAAA,CAAmB;UAAA,EAAC;UACzFzH,EAAA,CAAAU,UAAA,IAAAqH,mDAAA,wBAAoF;UAQ5F/H,EADI,CAAAG,YAAA,EAAY,EACV;UAUsBH,EAT5B,CAAAC,cAAA,eAAqD,eACL,eAEG,eAC4B,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,IACkC;;UACjFF,EADiF,CAAAG,YAAA,EAAK,EAChF;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACZ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAE5C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAKlD;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAKpFF,EALoF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA6H,UAAA,mBAAAG,kEAAA;YAAA,OAASX,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAH7BrG,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAsH,SAAA,qBAA+B;UAKnDtH,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAsH,SAAA,2BAAyD;;;UA9G1BtH,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAiH,GAAA,CAAA9F,eAAA,CAAyB,SAAA8F,GAAA,CAAArF,IAAA,CAAc,uCAAuC;UAEpFhC,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAA3F,OAAA,CAAmB;UAAC1B,EAAA,CAAAiI,gBAAA,YAAAZ,GAAA,CAAAM,eAAA,CAA6B;UACzD3H,EAAA,CAAAI,UAAA,mGAAkG;UAKvFJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAiI,gBAAA,gBAAAZ,GAAA,CAAA1F,WAAA,CAA6B;UAC5B3B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAiH,GAAA,CAAAjG,KAAA,CAAU;UAYlCpB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAkI,WAAA,iBAAAb,GAAA,CAAA5F,eAAA,CAAsC;UAMqBzB,EAAA,CAAAO,SAAA,GACkC;UADlCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAmI,WAAA,UAAAd,GAAA,CAAAnG,kBAAA,kBAAAmG,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,kBAAAsE,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAM,YAAA,UACkC;UAIzErD,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAoI,kBAAA,OAAAf,GAAA,CAAAnG,kBAAA,kBAAAmG,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,kBAAAsE,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAM,YAAA,cACJ;UAGgDrD,EAAA,CAAAO,SAAA,GAE5C;UAF4CP,EAAA,CAAAoI,kBAAA,SAAAf,GAAA,CAAAnG,kBAAA,kBAAAmG,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,kBAAAsE,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAsF,KAAA,cAE5C;UAKoDrI,EAAA,CAAAO,SAAA,GACpD;UADoDP,EAAA,CAAAoI,kBAAA,QAAAf,GAAA,CAAA/F,YAAA,aACpD;UAEkDtB,EAAA,CAAAO,SAAA,GAKlD;UALkDP,EAAA,CAAAoI,kBAAA,UAAAf,GAAA,CAAAnG,kBAAA,kBAAAmG,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,kBAAAsE,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,kBAAAjB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,qBAAAjB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,kBAAAlB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAAnB,GAAA,CAAAnG,kBAAA,kBAAAmG,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,kBAAAsE,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,kBAAAjB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,qBAAAjB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,kBAAAlB,GAAA,CAAAnG,kBAAA,CAAA6B,gBAAA,CAAAuF,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAKlD;UAWiBzI,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAlG,cAAA,kBAAAkG,GAAA,CAAAlG,cAAA,qBAAAkG,GAAA,CAAAlG,cAAA,IAAAsC,OAAA,SAAuC;UAMvCzD,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAlG,cAAA,kBAAAkG,GAAA,CAAAlG,cAAA,qBAAAkG,GAAA,CAAAlG,cAAA,IAAAsD,YAAA,SAA4C;UAO9CzE,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAlG,cAAA,kBAAAkG,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,kBAAAjB,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,qBAAAjB,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,kBAAAlB,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,kBAAA8D,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,qBAAA8D,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,kBAAA2C,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,qBAAA2C,GAAA,CAAAlG,cAAA,CAAAmH,iBAAA,IAAAC,uBAAA,CAAAhF,SAAA,IAAAmB,aAAA,IAAAD,YAAA,SACP;UAKSzE,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAlG,cAAA,kBAAAkG,GAAA,CAAAlG,cAAA,qBAAAkG,GAAA,CAAAlG,cAAA,IAAAoD,aAAA,SAA6C;UAM7CvE,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,iBAAA,EAAA6G,GAAA,CAAAlG,cAAA,kBAAAkG,GAAA,CAAAlG,cAAA,qBAAAkG,GAAA,CAAAlG,cAAA,IAAAwD,WAAA,SAA2C;UAUlD3E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAkI,WAAA,gBAAAb,GAAA,CAAA5F,eAAA,CAAqC;UAF/DzB,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}