{"ast": null, "code": "import { map } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ContentVendorService = /*#__PURE__*/(() => {\n  class ContentVendorService {\n    constructor(http) {\n      this.http = http;\n    }\n    getContentBySlug(slug) {\n      const language = localStorage.getItem('lang') || 'en';\n      return this.http.get(`${CMS_APIContstant.CONTENT_CRM}?locale=${language}&filters[slug]=${slug}&populate=*&pLevel=5`).pipe(map(response => {\n        const data = response?.data || [];\n        if (Array.isArray(data) && data.length > 0) {\n          return data[0];\n        } else {\n          return null;\n        }\n      }));\n    }\n    getDataByComponentName(body, componentName) {\n      const data = body.filter(item => item.__component === componentName);\n      return !data?.length ? null : data;\n    }\n    static {\n      this.ɵfac = function ContentVendorService_Factory(t) {\n        return new (t || ContentVendorService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ContentVendorService,\n        factory: ContentVendorService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ContentVendorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}