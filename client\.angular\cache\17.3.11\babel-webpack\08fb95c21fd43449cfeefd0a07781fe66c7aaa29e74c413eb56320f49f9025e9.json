{"ast": null, "code": "import { MessageService, ConfirmationService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./customer.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtext\";\nconst _c0 = [\"filter\"];\nconst _c1 = () => [\"bp_id\", \"bp_full_name\", \"bp_category\", \"email\", \"phone\"];\nfunction CustomerComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function CustomerComponent_ng_template_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dt1_r4 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r2.clear(dt1_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵelement(3, \"i\", 14);\n    i0.ɵɵelementStart(4, \"input\", 15, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerComponent_ng_template_7_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.globalSearchTerm, $event) || (ctx_r2.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CustomerComponent_ng_template_7_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dt1_r4 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r2.onGlobalFilter(dt1_r4, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.globalSearchTerm);\n  }\n}\nfunction CustomerComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 16)(2, \"div\", 17);\n    i0.ɵɵtext(3, \" ID \");\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵelement(5, \"p-sortIcon\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\", 20)(7, \"div\", 17);\n    i0.ɵɵtext(8, \" Name \");\n    i0.ɵɵelementStart(9, \"div\", 18);\n    i0.ɵɵelement(10, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"th\", 22)(12, \"div\", 17);\n    i0.ɵɵtext(13, \" Category \");\n    i0.ɵɵelementStart(14, \"div\", 18);\n    i0.ɵɵelement(15, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"th\", 24)(17, \"div\", 17);\n    i0.ɵɵtext(18, \" Email \");\n    i0.ɵɵelementStart(19, \"div\", 18);\n    i0.ɵɵelement(20, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"th\", 26)(22, \"div\", 17);\n    i0.ɵɵtext(23, \" Phone \");\n    i0.ɵɵelementStart(24, \"div\", 18);\n    i0.ɵɵelement(25, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction CustomerComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 28)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/backoffice/customer/\" + customer_r5.bp_id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.bp_id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.bp_full_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.bp_category, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.email, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.phone, \" \");\n  }\n}\nfunction CustomerComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"No customers found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"Loading customers data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CustomerComponent = /*#__PURE__*/(() => {\n  class CustomerComponent {\n    constructor(customerService) {\n      this.customerService = customerService;\n      this.customers = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n    }\n    ngOnInit() {\n      this.loadCustomers({\n        first: 0,\n        rows: 10\n      });\n    }\n    loadCustomers(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      this.customerService.getCustomers(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n        next: response => {\n          this.customers = response?.data || [];\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching customers', error);\n          this.loading = false;\n        }\n      });\n    }\n    onGlobalFilter(table, event) {\n      this.loadCustomers({\n        first: 0,\n        rows: 10\n      });\n    }\n    clear(table) {\n      this.globalSearchTerm = '';\n      this.filter.nativeElement.value = '';\n      this.loadCustomers({\n        first: 0,\n        rows: 10\n      });\n    }\n    static {\n      this.ɵfac = function CustomerComponent_Factory(t) {\n        return new (t || CustomerComponent)(i0.ɵɵdirectiveInject(i1.CustomerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerComponent,\n        selectors: [[\"app-customer\"]],\n        viewQuery: function CustomerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService])],\n        decls: 12,\n        vars: 9,\n        consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"rowHover\", \"paginator\", \"globalFilterFields\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\"], [\"pButton\", \"\", \"label\", \"Clear\", \"icon\", \"pi pi-filter-slash\", 1, \"p-button-outlined\", \"mb-2\", 3, \"click\"], [1, \"p-input-icon-left\", \"mb-2\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Keyword\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"pSortableColumn\", \"bp_id\", 2, \"min-width\", \"12rem\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"bp_id\"], [\"pSortableColumn\", \"bp_full_name\", 2, \"min-width\", \"12rem\"], [\"field\", \"bp_full_name\"], [\"pSortableColumn\", \"bp_category\", 2, \"min-width\", \"12rem\"], [\"field\", \"bp_category\"], [\"pSortableColumn\", \"email\", 2, \"min-width\", \"10rem\"], [\"field\", \"email\"], [\"pSortableColumn\", \"phone\", 2, \"min-width\", \"12rem\"], [\"field\", \"phone\"], [1, \"cursor-pointer\", 3, \"routerLink\"], [\"colspan\", \"8\"]],\n        template: function CustomerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"h5\");\n            i0.ɵɵtext(4, \"Filter Menu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p-table\", 5, 0);\n            i0.ɵɵlistener(\"onLazyLoad\", function CustomerComponent_Template_p_table_onLazyLoad_5_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadCustomers($event));\n            });\n            i0.ɵɵtemplate(7, CustomerComponent_ng_template_7_Template, 6, 1, \"ng-template\", 6)(8, CustomerComponent_ng_template_8_Template, 26, 0, \"ng-template\", 7)(9, CustomerComponent_ng_template_9_Template, 11, 6, \"ng-template\", 8)(10, CustomerComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9)(11, CustomerComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"value\", ctx.customers)(\"rows\", 10)(\"loading\", ctx.loading)(\"rowHover\", true)(\"paginator\", true)(\"globalFilterFields\", i0.ɵɵpureFunction0(8, _c1))(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n          }\n        },\n        dependencies: [i2.RouterLink, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.Table, i5.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i6.ButtonDirective, i7.InputText]\n      });\n    }\n  }\n  return CustomerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}