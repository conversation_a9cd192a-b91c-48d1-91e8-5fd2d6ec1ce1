{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Inject, Input, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * Badge Directive is directive usage of badge component.\n * @group Components\n */\nfunction Badge_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass())(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.value);\n  }\n}\nlet BadgeDirective = /*#__PURE__*/(() => {\n  class BadgeDirective {\n    document;\n    el;\n    renderer;\n    /**\n     * Icon position of the component.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(val) {\n      this._disabled = val;\n    }\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    get size() {\n      return this._size;\n    }\n    set size(val) {\n      this._size = val;\n      if (this.initialized) {\n        this.setSizeClasses();\n      }\n    }\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    get value() {\n      return this._value;\n    }\n    set value(val) {\n      if (val !== this._value) {\n        this._value = val;\n        if (this.initialized) {\n          let badge = document.getElementById(this.id);\n          if (this._value) {\n            if (DomHandler.hasClass(badge, 'p-badge-dot')) DomHandler.removeClass(badge, 'p-badge-dot');\n            if (String(this._value).length === 1) {\n              DomHandler.addClass(badge, 'p-badge-no-gutter');\n            } else {\n              DomHandler.removeClass(badge, 'p-badge-no-gutter');\n            }\n          } else if (!this._value && !DomHandler.hasClass(badge, 'p-badge-dot')) {\n            DomHandler.addClass(badge, 'p-badge-dot');\n          }\n          badge.innerHTML = '';\n          this.renderer.appendChild(badge, document.createTextNode(this._value));\n        }\n      }\n    }\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    severity;\n    _value;\n    initialized = false;\n    id;\n    _disabled = false;\n    _size;\n    constructor(document, el, renderer) {\n      this.document = document;\n      this.el = el;\n      this.renderer = renderer;\n    }\n    ngAfterViewInit() {\n      this.id = UniqueComponentId() + '_badge';\n      let el = this.el.nativeElement.nodeName.indexOf('-') != -1 ? this.el.nativeElement.firstChild : this.el.nativeElement;\n      if (this._disabled) {\n        return null;\n      }\n      let badge = this.document.createElement('span');\n      badge.id = this.id;\n      badge.className = 'p-badge p-component';\n      if (this.severity) {\n        DomHandler.addClass(badge, 'p-badge-' + this.severity);\n      }\n      this.setSizeClasses(badge);\n      if (this.value != null) {\n        this.renderer.appendChild(badge, this.document.createTextNode(this.value));\n        if (String(this.value).length === 1) {\n          DomHandler.addClass(badge, 'p-badge-no-gutter');\n        }\n      } else {\n        DomHandler.addClass(badge, 'p-badge-dot');\n      }\n      DomHandler.addClass(el, 'p-overlay-badge');\n      this.renderer.appendChild(el, badge);\n      this.initialized = true;\n    }\n    setSizeClasses(element) {\n      const badge = element ?? this.document.getElementById(this.id);\n      if (!badge) {\n        return;\n      }\n      if (this._size) {\n        if (this._size === 'large') {\n          DomHandler.addClass(badge, 'p-badge-lg');\n          DomHandler.removeClass(badge, 'p-badge-xl');\n        }\n        if (this._size === 'xlarge') {\n          DomHandler.addClass(badge, 'p-badge-xl');\n          DomHandler.removeClass(badge, 'p-badge-lg');\n        }\n      } else {\n        DomHandler.removeClass(badge, 'p-badge-lg');\n        DomHandler.removeClass(badge, 'p-badge-xl');\n      }\n    }\n    ngOnDestroy() {\n      this.initialized = false;\n    }\n    static ɵfac = function BadgeDirective_Factory(t) {\n      return new (t || BadgeDirective)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BadgeDirective,\n      selectors: [[\"\", \"pBadge\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        iconPos: \"iconPos\",\n        disabled: [i0.ɵɵInputFlags.None, \"badgeDisabled\", \"disabled\"],\n        size: \"size\",\n        value: \"value\",\n        severity: \"severity\"\n      }\n    });\n  }\n  return BadgeDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Badge is a small status indicator for another element.\n * @group Components\n */\nlet Badge = /*#__PURE__*/(() => {\n  class Badge {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    size;\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    severity;\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    value;\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    badgeDisabled = false;\n    containerClass() {\n      return {\n        'p-badge p-component': true,\n        'p-badge-no-gutter': this.value != undefined && String(this.value).length === 1,\n        'p-badge-lg': this.size === 'large',\n        'p-badge-xl': this.size === 'xlarge',\n        'p-badge-info': this.severity === 'info',\n        'p-badge-success': this.severity === 'success',\n        'p-badge-warning': this.severity === 'warning',\n        'p-badge-danger': this.severity === 'danger'\n      };\n    }\n    static ɵfac = function Badge_Factory(t) {\n      return new (t || Badge)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Badge,\n      selectors: [[\"p-badge\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        styleClass: \"styleClass\",\n        style: \"style\",\n        size: \"size\",\n        severity: \"severity\",\n        value: \"value\",\n        badgeDisabled: \"badgeDisabled\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"]],\n      template: function Badge_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, Badge_span_0_Template, 2, 5, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.badgeDisabled);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n      styles: [\"@layer primeng{.p-badge{display:inline-block;border-radius:10px;text-align:center;padding:0 .5rem}.p-overlay-badge{position:relative}.p-overlay-badge .p-badge{position:absolute;top:0;right:0;transform:translate(50%,-50%);transform-origin:100% 0;margin:0}.p-badge-dot{width:.5rem;min-width:.5rem;height:.5rem;border-radius:50%;padding:0}.p-badge-no-gutter{padding:0;border-radius:50%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Badge;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BadgeModule = /*#__PURE__*/(() => {\n  class BadgeModule {\n    static ɵfac = function BadgeModule_Factory(t) {\n      return new (t || BadgeModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BadgeModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return BadgeModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Badge, BadgeDirective, BadgeModule };\n//# sourceMappingURL=primeng-badge.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}