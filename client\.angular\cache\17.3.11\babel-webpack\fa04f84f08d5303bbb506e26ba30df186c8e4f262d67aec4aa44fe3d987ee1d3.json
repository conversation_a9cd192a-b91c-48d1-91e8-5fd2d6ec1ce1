{"ast": null, "code": "import { style, state, animate, transition, trigger } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nconst _c0 = [\"*\", [[\"p-header\"]]];\nconst _c1 = [\"*\", \"p-header\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nconst _c3 = a0 => ({\n  transitionParams: a0\n});\nconst _c4 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c5 = a0 => ({\n  value: \"hidden\",\n  params: a0\n});\nfunction AccordionTab_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template, 1, 2, \"ChevronDownIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.collapseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.collapseIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_2_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template, 1, 2, \"ChevronRightIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.expandIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.expandIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, AccordionTab_ng_container_3_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selected);\n  }\n}\nfunction AccordionTab_4_ng_template_0_Template(rf, ctx) {}\nfunction AccordionTab_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccordionTab_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AccordionTab_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.header, \" \");\n  }\n}\nfunction AccordionTab_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_content_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\nfunction AccordionTab_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_11_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n  }\n}\nconst _c6 = [\"*\"];\nclass AccordionTab {\n  el;\n  changeDetector;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Used to define the header of the tab.\n   * @group Props\n   */\n  header;\n  /**\n   * Inline style of the tab header.\n   * @group Props\n   */\n  headerStyle;\n  /**\n   * Inline style of the tab.\n   * @group Props\n   */\n  tabStyle;\n  /**\n   * Inline style of the tab content.\n   * @group Props\n   */\n  contentStyle;\n  /**\n   * Style class of the tab.\n   * @group Props\n   */\n  tabStyleClass;\n  /**\n   * Style class of the tab header.\n   * @group Props\n   */\n  headerStyleClass;\n  /**\n   * Style class of the tab content.\n   * @group Props\n   */\n  contentStyleClass;\n  /**\n   * Whether the tab is disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n   * @group Props\n   */\n  cache = true;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Position of the icon.\n   * @group Props\n   */\n  iconPos = 'start';\n  /**\n   * The value that returns the selection.\n   * @group Props\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(val) {\n    this._selected = val;\n    if (!this.loaded) {\n      if (this._selected && this.cache) {\n        this.loaded = true;\n      }\n      this.changeDetector.detectChanges();\n    }\n  }\n  /**\n   * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n   * @group Props\n   */\n  headerAriaLevel = 2;\n  /**\n   * Event triggered by changing the choice.\n   * @param {boolean} value - Boolean value indicates that the option is changed.\n   * @group Emits\n   */\n  selectedChange = new EventEmitter();\n  headerFacet;\n  templates;\n  _selected = false;\n  get iconClass() {\n    if (this.iconPos === 'end') {\n      return 'p-accordion-toggle-icon-end';\n    } else {\n      return 'p-accordion-toggle-icon';\n    }\n  }\n  contentTemplate;\n  headerTemplate;\n  iconTemplate;\n  loaded = false;\n  accordion;\n  constructor(accordion, el, changeDetector) {\n    this.el = el;\n    this.changeDetector = changeDetector;\n    this.accordion = accordion;\n    this.id = UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'icon':\n          this.iconTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  toggle(event) {\n    if (this.disabled) {\n      return false;\n    }\n    let index = this.findTabIndex();\n    if (this.selected) {\n      this.selected = false;\n      this.accordion.onClose.emit({\n        originalEvent: event,\n        index: index\n      });\n    } else {\n      if (!this.accordion.multiple) {\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n          if (this.accordion.tabs[i].selected) {\n            this.accordion.tabs[i].selected = false;\n            this.accordion.tabs[i].selectedChange.emit(false);\n            this.accordion.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n      this.selected = true;\n      this.loaded = true;\n      this.accordion.onOpen.emit({\n        originalEvent: event,\n        index: index\n      });\n    }\n    this.selectedChange.emit(this.selected);\n    this.accordion.updateActiveIndex();\n    this.changeDetector.markForCheck();\n    event?.preventDefault();\n  }\n  findTabIndex() {\n    let index = -1;\n    for (var i = 0; i < this.accordion.tabs.length; i++) {\n      if (this.accordion.tabs[i] == this) {\n        index = i;\n        break;\n      }\n    }\n    return index;\n  }\n  get hasHeaderFacet() {\n    return this.headerFacet && this.headerFacet.length > 0;\n  }\n  onKeydown(event) {\n    switch (event.code) {\n      case 'Enter':\n      case 'Space':\n        this.toggle(event);\n        event.preventDefault(); // ???\n        break;\n      default:\n        break;\n    }\n  }\n  getTabHeaderActionId(tabId) {\n    return `${tabId}_header_action`;\n  }\n  getTabContentId(tabId) {\n    return `${tabId}_content`;\n  }\n  ngOnDestroy() {\n    this.accordion.tabs.splice(this.findTabIndex(), 1);\n  }\n  static ɵfac = function AccordionTab_Factory(t) {\n    return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AccordionTab,\n    selectors: [[\"p-accordionTab\"]],\n    contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      id: \"id\",\n      header: \"header\",\n      headerStyle: \"headerStyle\",\n      tabStyle: \"tabStyle\",\n      contentStyle: \"contentStyle\",\n      tabStyleClass: \"tabStyleClass\",\n      headerStyleClass: \"headerStyleClass\",\n      contentStyleClass: \"contentStyleClass\",\n      disabled: \"disabled\",\n      cache: \"cache\",\n      transitionOptions: \"transitionOptions\",\n      iconPos: \"iconPos\",\n      selected: \"selected\",\n      headerAriaLevel: \"headerAriaLevel\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\"\n    },\n    ngContentSelectors: _c1,\n    decls: 12,\n    vars: 45,\n    consts: [[1, \"p-accordion-tab\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"heading\", 1, \"p-accordion-header\"], [\"role\", \"button\", 1, \"p-accordion-header-link\", 3, \"click\", \"keydown\", \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\", 3, \"ngClass\", \"ngStyle\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-accordion-header-text\"]],\n    template: function AccordionTab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n        i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n          return ctx.toggle($event);\n        })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n          return ctx.onKeydown($event);\n        });\n        i0.ɵɵtemplate(3, AccordionTab_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, AccordionTab_4_Template, 1, 0, null, 4)(5, AccordionTab_span_5_Template, 2, 1, \"span\", 5)(6, AccordionTab_ng_container_6_Template, 1, 0, \"ng-container\", 6)(7, AccordionTab_ng_content_7_Template, 1, 0, \"ng-content\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n        i0.ɵɵprojection(10);\n        i0.ɵɵtemplate(11, AccordionTab_ng_container_11_Template, 2, 1, \"ng-container\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-accordion-tab-active\", ctx.selected);\n        i0.ɵɵproperty(\"ngClass\", ctx.tabStyleClass)(\"ngStyle\", ctx.tabStyle);\n        i0.ɵɵattribute(\"data-pc-name\", \"accordiontab\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"p-highlight\", ctx.selected)(\"p-disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-level\", ctx.headerAriaLevel)(\"data-p-disabled\", ctx.disabled)(\"data-pc-section\", \"header\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleMap(ctx.headerStyle);\n        i0.ɵɵproperty(\"ngClass\", ctx.headerStyleClass);\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.getTabHeaderActionId(ctx.id))(\"aria-controls\", ctx.getTabContentId(ctx.id))(\"aria-expanded\", ctx.selected)(\"aria-disabled\", ctx.disabled)(\"data-pc-section\", \"headeraction\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.iconTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(35, _c2, ctx.selected));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(39, _c4, i0.ɵɵpureFunction1(37, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(43, _c5, i0.ɵɵpureFunction1(41, _c3, ctx.transitionOptions)));\n        i0.ɵɵattribute(\"id\", ctx.getTabContentId(ctx.id))(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.getTabHeaderActionId(ctx.id))(\"data-pc-section\", \"toggleablecontent\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.contentStyleClass)(\"ngStyle\", ctx.contentStyle);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, ChevronRightIcon, ChevronDownIcon],\n    styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('tabContent', [state('hidden', style({\n        height: '0',\n        visibility: 'hidden'\n      })), state('visible', style({\n        height: '*',\n        visibility: 'visible'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionTab, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordionTab',\n      template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('tabContent', [state('hidden', style({\n        height: '0',\n        visibility: 'hidden'\n      })), state('visible', style({\n        height: '*',\n        visibility: 'visible'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"]\n    }]\n  }], () => [{\n    type: Accordion,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => Accordion)]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    id: [{\n      type: Input\n    }],\n    header: [{\n      type: Input\n    }],\n    headerStyle: [{\n      type: Input\n    }],\n    tabStyle: [{\n      type: Input\n    }],\n    contentStyle: [{\n      type: Input\n    }],\n    tabStyleClass: [{\n      type: Input\n    }],\n    headerStyleClass: [{\n      type: Input\n    }],\n    contentStyleClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    cache: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    iconPos: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    headerAriaLevel: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    headerFacet: [{\n      type: ContentChildren,\n      args: [Header]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nclass Accordion {\n  el;\n  changeDetector;\n  /**\n   * When enabled, multiple tabs can be activated at the same time.\n   * @group Props\n   */\n  multiple = false;\n  /**\n   * Inline style of the tab header and content.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Icon of a collapsed tab.\n   * @group Props\n   */\n  expandIcon;\n  /**\n   * Icon of an expanded tab.\n   * @group Props\n   */\n  collapseIcon;\n  /**\n   * Index of the active tab or an array of indexes in multiple mode.\n   * @group Props\n   */\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(val) {\n    this._activeIndex = val;\n    if (this.preventActiveIndexPropagation) {\n      this.preventActiveIndexPropagation = false;\n      return;\n    }\n    this.updateSelectionState();\n  }\n  /**\n   * When enabled, the focused tab is activated.\n   * @group Props\n   */\n  selectOnFocus = false;\n  /**\n   * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n   * @group Props\n   */\n  get headerAriaLevel() {\n    return this._headerAriaLevel;\n  }\n  set headerAriaLevel(val) {\n    if (typeof val === 'number' && val > 0) {\n      this._headerAriaLevel = val;\n    } else if (this._headerAriaLevel !== 2) {\n      this._headerAriaLevel = 2;\n    }\n  }\n  /**\n   * Callback to invoke when an active tab is collapsed by clicking on the header.\n   * @param {AccordionTabCloseEvent} event - Custom tab close event.\n   * @group Emits\n   */\n  onClose = new EventEmitter();\n  /**\n   * Callback to invoke when a tab gets expanded.\n   * @param {AccordionTabOpenEvent} event - Custom tab open event.\n   * @group Emits\n   */\n  onOpen = new EventEmitter();\n  /**\n   * Returns the active index.\n   * @param {number | number[]} value - New index.\n   * @group Emits\n   */\n  activeIndexChange = new EventEmitter();\n  tabList;\n  tabListSubscription = null;\n  _activeIndex;\n  _headerAriaLevel = 2;\n  preventActiveIndexPropagation = false;\n  tabs = [];\n  constructor(el, changeDetector) {\n    this.el = el;\n    this.changeDetector = changeDetector;\n  }\n  onKeydown(event) {\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onTabArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onTabArrowUpKey(event);\n        break;\n      case 'Home':\n        if (!event.shiftKey) {\n          this.onTabHomeKey(event);\n        }\n        break;\n      case 'End':\n        if (!event.shiftKey) {\n          this.onTabEndKey(event);\n        }\n        break;\n    }\n  }\n  onTabArrowDownKey(event) {\n    const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n    nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n    event.preventDefault();\n  }\n  onTabArrowUpKey(event) {\n    const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n    prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n    event.preventDefault();\n  }\n  onTabHomeKey(event) {\n    const firstHeaderAction = this.findFirstHeaderAction();\n    this.changeFocusedTab(firstHeaderAction);\n    event.preventDefault();\n  }\n  changeFocusedTab(element) {\n    if (element) {\n      DomHandler.focus(element);\n      if (this.selectOnFocus) {\n        this.tabs.forEach((tab, i) => {\n          let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n          if (this.multiple) {\n            if (!this._activeIndex) {\n              this._activeIndex = [];\n            }\n            if (tab.id == element.id) {\n              tab.selected = !tab.selected;\n              if (!this._activeIndex.includes(i)) {\n                this._activeIndex.push(i);\n              } else {\n                this._activeIndex = this._activeIndex.filter(ind => ind !== i);\n              }\n            }\n          } else {\n            if (tab.id == element.id) {\n              tab.selected = !tab.selected;\n              this._activeIndex = i;\n            } else {\n              tab.selected = false;\n            }\n          }\n          tab.selectedChange.emit(selected);\n          this.activeIndexChange.emit(this._activeIndex);\n          tab.changeDetector.markForCheck();\n        });\n      }\n    }\n  }\n  findNextHeaderAction(tabElement, selfCheck = false) {\n    const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n    const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  }\n  findPrevHeaderAction(tabElement, selfCheck = false) {\n    const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n    const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  }\n  findFirstHeaderAction() {\n    const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n    return this.findNextHeaderAction(firstEl, true);\n  }\n  findLastHeaderAction() {\n    const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n    const lastEl = childNodes[childNodes.length - 1];\n    return this.findPrevHeaderAction(lastEl, true);\n  }\n  onTabEndKey(event) {\n    const lastHeaderAction = this.findLastHeaderAction();\n    this.changeFocusedTab(lastHeaderAction);\n    event.preventDefault();\n  }\n  ngAfterContentInit() {\n    this.initTabs();\n    this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n      this.initTabs();\n    });\n  }\n  initTabs() {\n    this.tabs = this.tabList.toArray();\n    this.tabs.forEach(tab => {\n      tab.headerAriaLevel = this._headerAriaLevel;\n    });\n    this.updateSelectionState();\n    this.changeDetector.markForCheck();\n  }\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n  updateSelectionState() {\n    if (this.tabs && this.tabs.length && this._activeIndex != null) {\n      for (let i = 0; i < this.tabs.length; i++) {\n        let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n        let changed = selected !== this.tabs[i].selected;\n        if (changed) {\n          this.tabs[i].selected = selected;\n          this.tabs[i].selectedChange.emit(selected);\n          this.tabs[i].changeDetector.markForCheck();\n        }\n      }\n    }\n  }\n  isTabActive(index) {\n    return this.multiple ? this._activeIndex && this._activeIndex.includes(index) : this._activeIndex === index;\n  }\n  getTabProp(tab, name) {\n    return tab.props ? tab.props[name] : undefined;\n  }\n  updateActiveIndex() {\n    let index = this.multiple ? [] : null;\n    this.tabs.forEach((tab, i) => {\n      if (tab.selected) {\n        if (this.multiple) {\n          index.push(i);\n        } else {\n          index = i;\n          return;\n        }\n      }\n    });\n    this.preventActiveIndexPropagation = true;\n    this.activeIndexChange.emit(index);\n  }\n  ngOnDestroy() {\n    if (this.tabListSubscription) {\n      this.tabListSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function Accordion_Factory(t) {\n    return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Accordion,\n    selectors: [[\"p-accordion\"]],\n    contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, AccordionTab, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    hostBindings: function Accordion_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function Accordion_keydown_HostBindingHandler($event) {\n          return ctx.onKeydown($event);\n        });\n      }\n    },\n    inputs: {\n      multiple: \"multiple\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      expandIcon: \"expandIcon\",\n      collapseIcon: \"collapseIcon\",\n      activeIndex: \"activeIndex\",\n      selectOnFocus: \"selectOnFocus\",\n      headerAriaLevel: \"headerAriaLevel\"\n    },\n    outputs: {\n      onClose: \"onClose\",\n      onOpen: \"onOpen\",\n      activeIndexChange: \"activeIndexChange\"\n    },\n    ngContentSelectors: _c6,\n    decls: 2,\n    vars: 4,\n    consts: [[3, \"ngClass\", \"ngStyle\"]],\n    template: function Accordion_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Accordion, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordion',\n      template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    multiple: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    expandIcon: [{\n      type: Input\n    }],\n    collapseIcon: [{\n      type: Input\n    }],\n    activeIndex: [{\n      type: Input\n    }],\n    selectOnFocus: [{\n      type: Input\n    }],\n    headerAriaLevel: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    onOpen: [{\n      type: Output\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    tabList: [{\n      type: ContentChildren,\n      args: [AccordionTab, {\n        descendants: true\n      }]\n    }],\n    onKeydown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass AccordionModule {\n  static ɵfac = function AccordionModule_Factory(t) {\n    return new (t || AccordionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AccordionModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n      exports: [Accordion, AccordionTab, SharedModule],\n      declarations: [Accordion, AccordionTab]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };", "map": {"version": 3, "names": ["style", "state", "animate", "transition", "trigger", "i1", "CommonModule", "i0", "EventEmitter", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "HostListener", "NgModule", "Header", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDownIcon", "ChevronRightIcon", "UniqueComponentId", "_c0", "_c1", "_c2", "a0", "$implicit", "_c3", "transitionParams", "_c4", "value", "params", "_c5", "AccordionTab_ng_container_3_ng_container_1_span_1_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵclassMap", "accordion", "collapseIcon", "ɵɵproperty", "iconClass", "ɵɵattribute", "AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template", "AccordionTab_ng_container_3_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ɵɵadvance", "AccordionTab_ng_container_3_ng_container_2_span_1_Template", "expandIcon", "AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template", "AccordionTab_ng_container_3_ng_container_2_Template", "AccordionTab_ng_container_3_Template", "selected", "AccordionTab_4_ng_template_0_Template", "AccordionTab_4_Template", "AccordionTab_span_5_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtextInterpolate1", "header", "AccordionTab_ng_container_6_Template", "ɵɵelementContainer", "AccordionTab_ng_content_7_Template", "ɵɵprojection", "AccordionTab_ng_container_11_ng_container_1_Template", "AccordionTab_ng_container_11_Template", "contentTemplate", "_c6", "AccordionTab", "el", "changeDetector", "id", "headerStyle", "tabStyle", "contentStyle", "tabStyleClass", "headerStyleClass", "contentStyleClass", "disabled", "cache", "transitionOptions", "iconPos", "_selected", "val", "loaded", "detectChanges", "headerAriaLevel", "<PERSON><PERSON><PERSON><PERSON>", "headerFacet", "templates", "headerTemplate", "iconTemplate", "constructor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "toggle", "event", "index", "findTabIndex", "onClose", "emit", "originalEvent", "multiple", "i", "tabs", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOpen", "updateActiveIndex", "preventDefault", "hasHeaderFacet", "onKeydown", "code", "getTabHeaderActionId", "tabId", "getTabContentId", "ngOnDestroy", "splice", "ɵfac", "AccordionTab_Factory", "t", "ɵɵdirectiveInject", "Accordion", "ElementRef", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "AccordionTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "outputs", "ngContentSelectors", "decls", "vars", "consts", "AccordionTab_Template", "ɵɵprojectionDef", "ɵɵlistener", "AccordionTab_Template_a_click_2_listener", "$event", "AccordionTab_Template_a_keydown_2_listener", "ɵɵclassProp", "ɵɵstyleMap", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "data", "animation", "height", "visibility", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "host", "class", "decorators", "styleClass", "activeIndex", "_activeIndex", "preventActiveIndexPropagation", "updateSelectionState", "selectOnFocus", "_headerAriaLevel", "activeIndexChange", "tabList", "tabListSubscription", "onTabArrowDownKey", "onTabArrowUpKey", "shift<PERSON>ey", "onTabHomeKey", "onTabEndKey", "nextHeaderAction", "findNextHeaderAction", "target", "parentElement", "changeFocusedTab", "prevHeaderAction", "findPrevHeaderAction", "firstHeaderAction", "findFirstHeaderAction", "element", "focus", "tab", "includes", "push", "filter", "ind", "tabElement", "<PERSON><PERSON><PERSON><PERSON>", "nextTabElement", "nextElement<PERSON><PERSON>ling", "headerElement", "findSingle", "getAttribute", "prevTabElement", "previousElementSibling", "firstEl", "nativeElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "childNodes", "findLastHeaderAction", "lastEl", "lastHeaderAction", "initTabs", "changes", "subscribe", "_", "toArray", "getBlockableElement", "children", "changed", "isTabActive", "getTabProp", "name", "props", "undefined", "unsubscribe", "Accordion_Factory", "Accordion_ContentQueries", "hostBindings", "Accordion_HostBindings", "Accordion_keydown_HostBindingHandler", "Accordion_Template", "descendants", "AccordionModule", "AccordionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-accordion.mjs"], "sourcesContent": ["import { style, state, animate, transition, trigger } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nclass AccordionTab {\n    el;\n    changeDetector;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    header;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    headerStyle;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    tabStyle;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    tabStyleClass;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    headerStyleClass;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'start';\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(val) {\n        this._selected = val;\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n            this.changeDetector.detectChanges();\n        }\n    }\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    headerAriaLevel = 2;\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    selectedChange = new EventEmitter();\n    headerFacet;\n    templates;\n    _selected = false;\n    get iconClass() {\n        if (this.iconPos === 'end') {\n            return 'p-accordion-toggle-icon-end';\n        }\n        else {\n            return 'p-accordion-toggle-icon';\n        }\n    }\n    contentTemplate;\n    headerTemplate;\n    iconTemplate;\n    loaded = false;\n    accordion;\n    constructor(accordion, el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n        this.accordion = accordion;\n        this.id = UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.disabled) {\n            return false;\n        }\n        let index = this.findTabIndex();\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        }\n        else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n        event?.preventDefault();\n    }\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n    get hasHeaderFacet() {\n        return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.toggle(event);\n                event.preventDefault(); // ???\n                break;\n            default:\n                break;\n        }\n    }\n    getTabHeaderActionId(tabId) {\n        return `${tabId}_header_action`;\n    }\n    getTabContentId(tabId) {\n        return `${tabId}_content`;\n    }\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionTab, deps: [{ token: forwardRef(() => Accordion) }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: AccordionTab, selector: \"p-accordionTab\", inputs: { id: \"id\", header: \"header\", headerStyle: \"headerStyle\", tabStyle: \"tabStyle\", contentStyle: \"contentStyle\", tabStyleClass: \"tabStyleClass\", headerStyleClass: \"headerStyleClass\", contentStyleClass: \"contentStyleClass\", disabled: \"disabled\", cache: \"cache\", transitionOptions: \"transitionOptions\", iconPos: \"iconPos\", selected: \"selected\", headerAriaLevel: \"headerAriaLevel\" }, outputs: { selectedChange: \"selectedChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", predicate: Header }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronDownIcon), selector: \"ChevronDownIcon\" }], animations: [\n            trigger('tabContent', [\n                state('hidden', style({\n                    height: '0',\n                    visibility: 'hidden'\n                })),\n                state('visible', style({\n                    height: '*',\n                    visibility: 'visible'\n                })),\n                transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                transition('void => *', animate(0))\n            ])\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-accordionTab', template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('tabContent', [\n                            state('hidden', style({\n                                height: '0',\n                                visibility: 'hidden'\n                            })),\n                            state('visible', style({\n                                height: '*',\n                                visibility: 'visible'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Accordion, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => Accordion)]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { id: [{\n                type: Input\n            }], header: [{\n                type: Input\n            }], headerStyle: [{\n                type: Input\n            }], tabStyle: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], tabStyleClass: [{\n                type: Input\n            }], headerStyleClass: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], cache: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], iconPos: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], headerAriaLevel: [{\n                type: Input\n            }], selectedChange: [{\n                type: Output\n            }], headerFacet: [{\n                type: ContentChildren,\n                args: [Header]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nclass Accordion {\n    el;\n    changeDetector;\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    expandIcon;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    collapseIcon;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(val) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n        this.updateSelectionState();\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    get headerAriaLevel() {\n        return this._headerAriaLevel;\n    }\n    set headerAriaLevel(val) {\n        if (typeof val === 'number' && val > 0) {\n            this._headerAriaLevel = val;\n        }\n        else if (this._headerAriaLevel !== 2) {\n            this._headerAriaLevel = 2;\n        }\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    onOpen = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    tabList;\n    tabListSubscription = null;\n    _activeIndex;\n    _headerAriaLevel = 2;\n    preventActiveIndexPropagation = false;\n    tabs = [];\n    constructor(el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n    }\n    onKeydown(event) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onTabArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onTabArrowUpKey(event);\n                break;\n            case 'Home':\n                if (!event.shiftKey) {\n                    this.onTabHomeKey(event);\n                }\n                break;\n            case 'End':\n                if (!event.shiftKey) {\n                    this.onTabEndKey(event);\n                }\n                break;\n        }\n    }\n    onTabArrowDownKey(event) {\n        const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n        nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n        event.preventDefault();\n    }\n    onTabArrowUpKey(event) {\n        const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n        prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n        event.preventDefault();\n    }\n    onTabHomeKey(event) {\n        const firstHeaderAction = this.findFirstHeaderAction();\n        this.changeFocusedTab(firstHeaderAction);\n        event.preventDefault();\n    }\n    changeFocusedTab(element) {\n        if (element) {\n            DomHandler.focus(element);\n            if (this.selectOnFocus) {\n                this.tabs.forEach((tab, i) => {\n                    let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                    if (this.multiple) {\n                        if (!this._activeIndex) {\n                            this._activeIndex = [];\n                        }\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            if (!this._activeIndex.includes(i)) {\n                                this._activeIndex.push(i);\n                            }\n                            else {\n                                this._activeIndex = this._activeIndex.filter((ind) => ind !== i);\n                            }\n                        }\n                    }\n                    else {\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            this._activeIndex = i;\n                        }\n                        else {\n                            tab.selected = false;\n                        }\n                    }\n                    tab.selectedChange.emit(selected);\n                    this.activeIndexChange.emit(this._activeIndex);\n                    tab.changeDetector.markForCheck();\n                });\n            }\n        }\n    }\n    findNextHeaderAction(tabElement, selfCheck = false) {\n        const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n        const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n        const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n        const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n    findFirstHeaderAction() {\n        const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n        return this.findNextHeaderAction(firstEl, true);\n    }\n    findLastHeaderAction() {\n        const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n        const lastEl = childNodes[childNodes.length - 1];\n        return this.findPrevHeaderAction(lastEl, true);\n    }\n    onTabEndKey(event) {\n        const lastHeaderAction = this.findLastHeaderAction();\n        this.changeFocusedTab(lastHeaderAction);\n        event.preventDefault();\n    }\n    ngAfterContentInit() {\n        this.initTabs();\n        this.tabListSubscription = this.tabList.changes.subscribe((_) => {\n            this.initTabs();\n        });\n    }\n    initTabs() {\n        this.tabs = this.tabList.toArray();\n        this.tabs.forEach((tab) => {\n            tab.headerAriaLevel = this._headerAriaLevel;\n        });\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                let changed = selected !== this.tabs[i].selected;\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n    isTabActive(index) {\n        return this.multiple ? this._activeIndex && this._activeIndex.includes(index) : this._activeIndex === index;\n    }\n    getTabProp(tab, name) {\n        return tab.props ? tab.props[name] : undefined;\n    }\n    updateActiveIndex() {\n        let index = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    index.push(i);\n                }\n                else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Accordion, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Accordion, selector: \"p-accordion\", inputs: { multiple: \"multiple\", style: \"style\", styleClass: \"styleClass\", expandIcon: \"expandIcon\", collapseIcon: \"collapseIcon\", activeIndex: \"activeIndex\", selectOnFocus: \"selectOnFocus\", headerAriaLevel: \"headerAriaLevel\" }, outputs: { onClose: \"onClose\", onOpen: \"onOpen\", activeIndexChange: \"activeIndexChange\" }, host: { listeners: { \"keydown\": \"onKeydown($event)\" }, classAttribute: \"p-element\" }, queries: [{ propertyName: \"tabList\", predicate: AccordionTab, descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Accordion, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordion',\n                    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { multiple: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], expandIcon: [{\n                type: Input\n            }], collapseIcon: [{\n                type: Input\n            }], activeIndex: [{\n                type: Input\n            }], selectOnFocus: [{\n                type: Input\n            }], headerAriaLevel: [{\n                type: Input\n            }], onClose: [{\n                type: Output\n            }], onOpen: [{\n                type: Output\n            }], activeIndexChange: [{\n                type: Output\n            }], tabList: [{\n                type: ContentChildren,\n                args: [AccordionTab, { descendants: true }]\n            }], onKeydown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\nclass AccordionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionModule, declarations: [Accordion, AccordionTab], imports: [CommonModule, ChevronRightIcon, ChevronDownIcon], exports: [Accordion, AccordionTab, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionModule, imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: AccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n                    exports: [Accordion, AccordionTab, SharedModule],\n                    declarations: [Accordion, AccordionTab]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC/K,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACjE,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,iBAAiB,QAAQ,eAAe;;AAEjD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,MAAAE,GAAA,GAAAF,EAAA;EAAAG,gBAAA,EAAAH;AAAA;AAAA,MAAAI,GAAA,GAAAJ,EAAA;EAAAK,KAAA;EAAAC,MAAA,EAAAN;AAAA;AAAA,MAAAO,GAAA,GAAAP,EAAA;EAAAK,KAAA;EAAAC,MAAA,EAAAN;AAAA;AAAA,SAAAQ,2DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAmM6F/B,EAAE,CAAAiC,SAAA,cAoByD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApB5DlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAoC,UAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAC,YAoBC,CAAC;IApBJtC,EAAE,CAAAuC,UAAA,YAAAL,MAAA,CAAAM,SAoBuB,CAAC;IApB1BxC,EAAE,CAAAyC,WAAA;EAAA;AAAA;AAAA,SAAAC,sEAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF/B,EAAE,CAAAiC,SAAA,yBAqB+B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GArBlClC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAuC,UAAA,YAAAL,MAAA,CAAAM,SAqBE,CAAC;IArBLxC,EAAE,CAAAyC,WAAA;EAAA;AAAA;AAAA,SAAAE,oDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF/B,EAAE,CAAA4C,uBAAA,EAmBzC,CAAC;IAnBsC5C,EAAE,CAAA6C,UAAA,IAAAf,0DAAA,iBAoBkD,CAAC,IAAAY,qEAAA,6BACpB,CAAC;IArBlC1C,EAAE,CAAA8C,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA+C,SAAA,CAoBjC,CAAC;IApB8B/C,EAAE,CAAAuC,UAAA,SAAAL,MAAA,CAAAG,SAAA,CAAAC,YAoBjC,CAAC;IApB8BtC,EAAE,CAAA+C,SAAA,CAqBrB,CAAC;IArBkB/C,EAAE,CAAAuC,UAAA,UAAAL,MAAA,CAAAG,SAAA,CAAAC,YAqBrB,CAAC;EAAA;AAAA;AAAA,SAAAU,2DAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArBkB/B,EAAE,CAAAiC,SAAA,cAwBqD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAxBxDlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAoC,UAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAY,UAwBH,CAAC;IAxBAjD,EAAE,CAAAuC,UAAA,YAAAL,MAAA,CAAAM,SAwBmB,CAAC;IAxBtBxC,EAAE,CAAAyC,WAAA;EAAA;AAAA;AAAA,SAAAS,uEAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF/B,EAAE,CAAAiC,SAAA,0BAyB8B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAzBjClC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAuC,UAAA,YAAAL,MAAA,CAAAM,SAyBC,CAAC;IAzBJxC,EAAE,CAAAyC,WAAA;EAAA;AAAA;AAAA,SAAAU,oDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF/B,EAAE,CAAA4C,uBAAA,EAuBxC,CAAC;IAvBqC5C,EAAE,CAAA6C,UAAA,IAAAG,0DAAA,iBAwB8C,CAAC,IAAAE,sEAAA,8BACjB,CAAC;IAzBjClD,EAAE,CAAA8C,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA+C,SAAA,CAwBnC,CAAC;IAxBgC/C,EAAE,CAAAuC,UAAA,SAAAL,MAAA,CAAAG,SAAA,CAAAY,UAwBnC,CAAC;IAxBgCjD,EAAE,CAAA+C,SAAA,CAyBtB,CAAC;IAzBmB/C,EAAE,CAAAuC,UAAA,UAAAL,MAAA,CAAAG,SAAA,CAAAY,UAyBtB,CAAC;EAAA;AAAA;AAAA,SAAAG,qCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBmB/B,EAAE,CAAA4C,uBAAA,EAkBxC,CAAC;IAlBqC5C,EAAE,CAAA6C,UAAA,IAAAF,mDAAA,yBAmBzC,CAAC,IAAAQ,mDAAA,yBAIA,CAAC;IAvBqCnD,EAAE,CAAA8C,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA+C,SAAA,CAmB3C,CAAC;IAnBwC/C,EAAE,CAAAuC,UAAA,SAAAL,MAAA,CAAAmB,QAmB3C,CAAC;IAnBwCrD,EAAE,CAAA+C,SAAA,CAuB1C,CAAC;IAvBuC/C,EAAE,CAAAuC,UAAA,UAAAL,MAAA,CAAAmB,QAuB1C,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAvB,EAAA,EAAAC,GAAA;AAAA,SAAAuB,wBAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBuC/B,EAAE,CAAA6C,UAAA,IAAAS,qCAAA,qBA4BI,CAAC;EAAA;AAAA;AAAA,SAAAE,6BAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5BP/B,EAAE,CAAAyD,cAAA,cA6Bd,CAAC;IA7BWzD,EAAE,CAAA0D,MAAA,EA+B5E,CAAC;IA/ByE1D,EAAE,CAAA2D,YAAA,CA+BrE,CAAC;EAAA;EAAA,IAAA5B,EAAA;IAAA,MAAAG,MAAA,GA/BkElC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA+C,SAAA,CA+B5E,CAAC;IA/ByE/C,EAAE,CAAA4D,kBAAA,MAAA1B,MAAA,CAAA2B,MAAA,KA+B5E,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/ByE/B,EAAE,CAAA+D,kBAAA,EAgCZ,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhCS/B,EAAE,CAAAiE,YAAA,kCAiCV,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCO/B,EAAE,CAAA+D,kBAAA,EAgDP,CAAC;EAAA;AAAA;AAAA,SAAAI,sCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhDI/B,EAAE,CAAA4C,uBAAA,EA+CP,CAAC;IA/CI5C,EAAE,CAAA6C,UAAA,IAAAqB,oDAAA,yBAgDtB,CAAC;IAhDmBlE,EAAE,CAAA8C,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA+C,SAAA,CAgDxB,CAAC;IAhDqB/C,EAAE,CAAAuC,UAAA,qBAAAL,MAAA,CAAAkC,eAgDxB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AA/OxE,MAAMC,YAAY,CAAC;EACfC,EAAE;EACFC,cAAc;EACd;AACJ;AACA;AACA;EACIC,EAAE;EACF;AACJ;AACA;AACA;EACIZ,MAAM;EACN;AACJ;AACA;AACA;EACIa,WAAW;EACX;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,KAAK,GAAG,IAAI;EACZ;AACJ;AACA;AACA;EACIC,iBAAiB,GAAG,sCAAsC;EAC1D;AACJ;AACA;AACA;EACIC,OAAO,GAAG,OAAO;EACjB;AACJ;AACA;AACA;EACI,IAAI9B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+B,SAAS;EACzB;EACA,IAAI/B,QAAQA,CAACgC,GAAG,EAAE;IACd,IAAI,CAACD,SAAS,GAAGC,GAAG;IACpB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd,IAAI,IAAI,CAACF,SAAS,IAAI,IAAI,CAACH,KAAK,EAAE;QAC9B,IAAI,CAACK,MAAM,GAAG,IAAI;MACtB;MACA,IAAI,CAACd,cAAc,CAACe,aAAa,CAAC,CAAC;IACvC;EACJ;EACA;AACJ;AACA;AACA;EACIC,eAAe,GAAG,CAAC;EACnB;AACJ;AACA;AACA;AACA;EACIC,cAAc,GAAG,IAAIxF,YAAY,CAAC,CAAC;EACnCyF,WAAW;EACXC,SAAS;EACTP,SAAS,GAAG,KAAK;EACjB,IAAI5C,SAASA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC2C,OAAO,KAAK,KAAK,EAAE;MACxB,OAAO,6BAA6B;IACxC,CAAC,MACI;MACD,OAAO,yBAAyB;IACpC;EACJ;EACAf,eAAe;EACfwB,cAAc;EACdC,YAAY;EACZP,MAAM,GAAG,KAAK;EACdjD,SAAS;EACTyD,WAAWA,CAACzD,SAAS,EAAEkC,EAAE,EAAEC,cAAc,EAAE;IACvC,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACnC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoC,EAAE,GAAGvD,iBAAiB,CAAC,CAAC;EACjC;EACA6E,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACJ,SAAS,CAACK,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAAC9B,eAAe,GAAG6B,IAAI,CAACE,QAAQ;UACpC;QACJ,KAAK,QAAQ;UACT,IAAI,CAACP,cAAc,GAAGK,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,MAAM;UACP,IAAI,CAACN,YAAY,GAAGI,IAAI,CAACE,QAAQ;UACjC;QACJ;UACI,IAAI,CAAC/B,eAAe,GAAG6B,IAAI,CAACE,QAAQ;UACpC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAIsB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,IAAI,CAAClD,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrB,IAAI,CAAChB,SAAS,CAACmE,OAAO,CAACC,IAAI,CAAC;QAAEC,aAAa,EAAEL,KAAK;QAAEC,KAAK,EAAEA;MAAM,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAACjE,SAAS,CAACsE,QAAQ,EAAE;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UACjD,IAAI,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,EAAE;YACjC,IAAI,CAAChB,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,GAAG,KAAK;YACvC,IAAI,CAAChB,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACnB,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;YACjD,IAAI,CAACpE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACpC,cAAc,CAACuC,YAAY,CAAC,CAAC;UACxD;QACJ;MACJ;MACA,IAAI,CAAC1D,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACiC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACjD,SAAS,CAAC2E,MAAM,CAACP,IAAI,CAAC;QAAEC,aAAa,EAAEL,KAAK;QAAEC,KAAK,EAAEA;MAAM,CAAC,CAAC;IACtE;IACA,IAAI,CAACb,cAAc,CAACgB,IAAI,CAAC,IAAI,CAACpD,QAAQ,CAAC;IACvC,IAAI,CAAChB,SAAS,CAAC4E,iBAAiB,CAAC,CAAC;IAClC,IAAI,CAACzC,cAAc,CAACuC,YAAY,CAAC,CAAC;IAClCV,KAAK,EAAEa,cAAc,CAAC,CAAC;EAC3B;EACAX,YAAYA,CAAA,EAAG;IACX,IAAID,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,IAAI,IAAI,EAAE;QAChCN,KAAK,GAAGM,CAAC;QACT;MACJ;IACJ;IACA,OAAON,KAAK;EAChB;EACA,IAAIa,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACzB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACoB,MAAM,GAAG,CAAC;EAC1D;EACAM,SAASA,CAACf,KAAK,EAAE;IACb,QAAQA,KAAK,CAACgB,IAAI;MACd,KAAK,OAAO;MACZ,KAAK,OAAO;QACR,IAAI,CAACjB,MAAM,CAACC,KAAK,CAAC;QAClBA,KAAK,CAACa,cAAc,CAAC,CAAC,CAAC,CAAC;QACxB;MACJ;QACI;IACR;EACJ;EACAI,oBAAoBA,CAACC,KAAK,EAAE;IACxB,OAAQ,GAAEA,KAAM,gBAAe;EACnC;EACAC,eAAeA,CAACD,KAAK,EAAE;IACnB,OAAQ,GAAEA,KAAM,UAAS;EAC7B;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpF,SAAS,CAACwE,IAAI,CAACa,MAAM,CAAC,IAAI,CAACnB,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EACtD;EACA,OAAOoB,IAAI,YAAAC,qBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFvD,YAAY,EAAtBtE,EAAE,CAAA8H,iBAAA,CAAsC5H,UAAU,CAAC,MAAM6H,SAAS,CAAC,GAAnE/H,EAAE,CAAA8H,iBAAA,CAA8E9H,EAAE,CAACgI,UAAU,GAA7FhI,EAAE,CAAA8H,iBAAA,CAAwG9H,EAAE,CAACiI,iBAAiB;EAAA;EACvN,OAAOC,IAAI,kBAD8ElI,EAAE,CAAAmI,iBAAA;IAAAC,IAAA,EACJ9D,YAAY;IAAA+D,SAAA;IAAAC,cAAA,WAAAC,4BAAAxG,EAAA,EAAAC,GAAA,EAAAwG,QAAA;MAAA,IAAAzG,EAAA;QADV/B,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EACkjB5H,MAAM;QAD1jBZ,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EAComB3H,aAAa;MAAA;MAAA,IAAAkB,EAAA;QAAA,IAAA2G,EAAA;QADnnB1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA5G,GAAA,CAAA0D,WAAA,GAAAgD,EAAA;QAAF1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA5G,GAAA,CAAA2D,SAAA,GAAA+C,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAC,MAAA;MAAArE,EAAA;MAAAZ,MAAA;MAAAa,WAAA;MAAAC,QAAA;MAAAC,YAAA;MAAAC,aAAA;MAAAC,gBAAA;MAAAC,iBAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,iBAAA;MAAAC,OAAA;MAAA9B,QAAA;MAAAmC,eAAA;IAAA;IAAAuD,OAAA;MAAAtD,cAAA;IAAA;IAAAuD,kBAAA,EAAA5H,GAAA;IAAA6H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAhD,QAAA,WAAAiD,sBAAArH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF/B,EAAE,CAAAqJ,eAAA,CAAAlI,GAAA;QAAFnB,EAAE,CAAAyD,cAAA,YAEmE,CAAC,YAC4D,CAAC,UAchN,CAAC;QAjB4EzD,EAAE,CAAAsJ,UAAA,mBAAAC,yCAAAC,MAAA;UAAA,OASlExH,GAAA,CAAAoE,MAAA,CAAAoD,MAAa,CAAC;QAAA,EAAC,qBAAAC,2CAAAD,MAAA;UAAA,OACbxH,GAAA,CAAAoF,SAAA,CAAAoC,MAAgB,CAAC;QAAA,EAAC;QAV4CxJ,EAAE,CAAA6C,UAAA,IAAAO,oCAAA,yBAkBxC,CAAC,IAAAG,uBAAA,eAU2C,CAAC,IAAAC,4BAAA,iBACnB,CAAC,IAAAM,oCAAA,yBAGd,CAAC,IAAAE,kCAAA,uBACG,CAAC;QAjCoBhE,EAAE,CAAA2D,YAAA,CAkC5E,CAAC,CACH,CAAC;QAnC2E3D,EAAE,CAAAyD,cAAA,YA4CnF,CAAC,YAC0F,CAAC;QA7CXzD,EAAE,CAAAiE,YAAA,GA8CnD,CAAC;QA9CgDjE,EAAE,CAAA6C,UAAA,KAAAsB,qCAAA,yBA+CP,CAAC;QA/CInE,EAAE,CAAA2D,YAAA,CAkD1E,CAAC,CACL,CAAC,CACL,CAAC;MAAA;MAAA,IAAA5B,EAAA;QApD+E/B,EAAE,CAAA0J,WAAA,2BAAA1H,GAAA,CAAAqB,QAElB,CAAC;QAFerD,EAAE,CAAAuC,UAAA,YAAAP,GAAA,CAAA6C,aAEQ,CAAC,YAAA7C,GAAA,CAAA2C,QAAoB,CAAC;QAFhC3E,EAAE,CAAAyC,WAAA;QAAFzC,EAAE,CAAA+C,SAAA,CAG6B,CAAC;QAHhC/C,EAAE,CAAA0J,WAAA,gBAAA1H,GAAA,CAAAqB,QAG6B,CAAC,eAAArB,GAAA,CAAAgD,QAA6B,CAAC;QAH9DhF,EAAE,CAAAyC,WAAA,eAAAT,GAAA,CAAAwD,eAAA,qBAAAxD,GAAA,CAAAgD,QAAA;QAAFhF,EAAE,CAAA+C,SAAA,CAMvD,CAAC;QANoD/C,EAAE,CAAA2J,UAAA,CAAA3H,GAAA,CAAA0C,WAMvD,CAAC;QANoD1E,EAAE,CAAAuC,UAAA,YAAAP,GAAA,CAAA8C,gBAKhD,CAAC;QAL6C9E,EAAE,CAAAyC,WAAA,aAAAT,GAAA,CAAAgD,QAAA,mBAAAhD,GAAA,CAAAsF,oBAAA,CAAAtF,GAAA,CAAAyC,EAAA,oBAAAzC,GAAA,CAAAwF,eAAA,CAAAxF,GAAA,CAAAyC,EAAA,oBAAAzC,GAAA,CAAAqB,QAAA,mBAAArB,GAAA,CAAAgD,QAAA;QAAFhF,EAAE,CAAA+C,SAAA,CAkB1C,CAAC;QAlBuC/C,EAAE,CAAAuC,UAAA,UAAAP,GAAA,CAAA6D,YAkB1C,CAAC;QAlBuC7F,EAAE,CAAA+C,SAAA,CA4B9B,CAAC;QA5B2B/C,EAAE,CAAAuC,UAAA,qBAAAP,GAAA,CAAA6D,YA4B9B,CAAC,4BA5B2B7F,EAAE,CAAA4J,eAAA,KAAAvI,GAAA,EAAAW,GAAA,CAAAqB,QAAA,CA4BE,CAAC;QA5BLrD,EAAE,CAAA+C,SAAA,CA6BhB,CAAC;QA7Ba/C,EAAE,CAAAuC,UAAA,UAAAP,GAAA,CAAAmF,cA6BhB,CAAC;QA7BanH,EAAE,CAAA+C,SAAA,CAgC7B,CAAC;QAhC0B/C,EAAE,CAAAuC,UAAA,qBAAAP,GAAA,CAAA4D,cAgC7B,CAAC;QAhC0B5F,EAAE,CAAA+C,SAAA,CAiCzB,CAAC;QAjCsB/C,EAAE,CAAAuC,UAAA,SAAAP,GAAA,CAAAmF,cAiCzB,CAAC;QAjCsBnH,EAAE,CAAA+C,SAAA,CAuCuF,CAAC;QAvC1F/C,EAAE,CAAAuC,UAAA,gBAAAP,GAAA,CAAAqB,QAAA,GAAFrD,EAAE,CAAA4J,eAAA,KAAAlI,GAAA,EAAF1B,EAAE,CAAA4J,eAAA,KAAApI,GAAA,EAAAQ,GAAA,CAAAkD,iBAAA,KAAFlF,EAAE,CAAA4J,eAAA,KAAA/H,GAAA,EAAF7B,EAAE,CAAA4J,eAAA,KAAApI,GAAA,EAAAQ,GAAA,CAAAkD,iBAAA,EAuCuF,CAAC;QAvC1FlF,EAAE,CAAAyC,WAAA,OAAAT,GAAA,CAAAwF,eAAA,CAAAxF,GAAA,CAAAyC,EAAA,mBAAAzC,GAAA,CAAAqB,QAAA,qBAAArB,GAAA,CAAAsF,oBAAA,CAAAtF,GAAA,CAAAyC,EAAA;QAAFzE,EAAE,CAAA+C,SAAA,CA6ClB,CAAC;QA7Ce/C,EAAE,CAAAuC,UAAA,YAAAP,GAAA,CAAA+C,iBA6ClB,CAAC,YAAA/C,GAAA,CAAA4C,YAAwB,CAAC;QA7CV5E,EAAE,CAAA+C,SAAA,EA+CT,CAAC;QA/CM/C,EAAE,CAAAuC,UAAA,SAAAP,GAAA,CAAAoC,eAAA,KAAApC,GAAA,CAAAiD,KAAA,GAAAjD,GAAA,CAAAsD,MAAA,GAAAtD,GAAA,CAAAqB,QAAA,CA+CT,CAAC;MAAA;IAAA;IAAAwG,YAAA,EAAAA,CAAA,MAMgf/J,EAAE,CAACgK,OAAO,EAAyGhK,EAAE,CAACiK,IAAI,EAAkHjK,EAAE,CAACkK,gBAAgB,EAAyKlK,EAAE,CAACmK,OAAO,EAAgGhJ,gBAAgB,EAAkFD,eAAe;IAAAkJ,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAA+C,CAC7uCxK,OAAO,CAAC,YAAY,EAAE,CAClBH,KAAK,CAAC,QAAQ,EAAED,KAAK,CAAC;QAClB6K,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;MAChB,CAAC,CAAC,CAAC,EACH7K,KAAK,CAAC,SAAS,EAAED,KAAK,CAAC;QACnB6K,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;MAChB,CAAC,CAAC,CAAC,EACH3K,UAAU,CAAC,oBAAoB,EAAE,CAACD,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,EACnEC,UAAU,CAAC,WAAW,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;IACL;IAAA6K,eAAA;EAAA;AACT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApE6FzK,EAAE,CAAA0K,iBAAA,CAoEJpG,YAAY,EAAc,CAAC;IAC1G8D,IAAI,EAAEjI,SAAS;IACfwK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAEzE,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE0E,UAAU,EAAE,CACKhL,OAAO,CAAC,YAAY,EAAE,CAClBH,KAAK,CAAC,QAAQ,EAAED,KAAK,CAAC;QAClB6K,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;MAChB,CAAC,CAAC,CAAC,EACH7K,KAAK,CAAC,SAAS,EAAED,KAAK,CAAC;QACnB6K,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;MAChB,CAAC,CAAC,CAAC,EACH3K,UAAU,CAAC,oBAAoB,EAAE,CAACD,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,EACnEC,UAAU,CAAC,WAAW,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC,CACL;MAAE6K,eAAe,EAAEpK,uBAAuB,CAAC0K,MAAM;MAAEX,aAAa,EAAE9J,iBAAiB,CAAC0K,IAAI;MAAEC,IAAI,EAAE;QAC7FC,KAAK,EAAE;MACX,CAAC;MAAEf,MAAM,EAAE,CAAC,seAAse;IAAE,CAAC;EACjgB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9B,IAAI,EAAEL,SAAS;IAAEmD,UAAU,EAAE,CAAC;MAC/C9C,IAAI,EAAE9H,MAAM;MACZqK,IAAI,EAAE,CAACzK,UAAU,CAAC,MAAM6H,SAAS,CAAC;IACtC,CAAC;EAAE,CAAC,EAAE;IAAEK,IAAI,EAAEpI,EAAE,CAACgI;EAAW,CAAC,EAAE;IAAEI,IAAI,EAAEpI,EAAE,CAACiI;EAAkB,CAAC,CAAC,EAAkB;IAAExD,EAAE,EAAE,CAAC;MACvF2D,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEsD,MAAM,EAAE,CAAC;MACTuE,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEmE,WAAW,EAAE,CAAC;MACd0D,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEoE,QAAQ,EAAE,CAAC;MACXyD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEqE,YAAY,EAAE,CAAC;MACfwD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEsE,aAAa,EAAE,CAAC;MAChBuD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEuE,gBAAgB,EAAE,CAAC;MACnBsD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEwE,iBAAiB,EAAE,CAAC;MACpBqD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEyE,QAAQ,EAAE,CAAC;MACXoD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE0E,KAAK,EAAE,CAAC;MACRmD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE2E,iBAAiB,EAAE,CAAC;MACpBkD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE4E,OAAO,EAAE,CAAC;MACViD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE8C,QAAQ,EAAE,CAAC;MACX+E,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiF,eAAe,EAAE,CAAC;MAClB4C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEkF,cAAc,EAAE,CAAC;MACjB2C,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEkF,WAAW,EAAE,CAAC;MACd0C,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAAC/J,MAAM;IACjB,CAAC,CAAC;IAAE+E,SAAS,EAAE,CAAC;MACZyC,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAAC9J,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMkH,SAAS,CAAC;EACZxD,EAAE;EACFC,cAAc;EACd;AACJ;AACA;AACA;EACImC,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIlH,KAAK;EACL;AACJ;AACA;AACA;EACI0L,UAAU;EACV;AACJ;AACA;AACA;EACIlI,UAAU;EACV;AACJ;AACA;AACA;EACIX,YAAY;EACZ;AACJ;AACA;AACA;EACI,IAAI8I,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAC/F,GAAG,EAAE;IACjB,IAAI,CAACgG,YAAY,GAAGhG,GAAG;IACvB,IAAI,IAAI,CAACiG,6BAA6B,EAAE;MACpC,IAAI,CAACA,6BAA6B,GAAG,KAAK;MAC1C;IACJ;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIC,aAAa,GAAG,KAAK;EACrB;AACJ;AACA;AACA;EACI,IAAIhG,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACiG,gBAAgB;EAChC;EACA,IAAIjG,eAAeA,CAACH,GAAG,EAAE;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,EAAE;MACpC,IAAI,CAACoG,gBAAgB,GAAGpG,GAAG;IAC/B,CAAC,MACI,IAAI,IAAI,CAACoG,gBAAgB,KAAK,CAAC,EAAE;MAClC,IAAI,CAACA,gBAAgB,GAAG,CAAC;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIjF,OAAO,GAAG,IAAIvG,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACI+G,MAAM,GAAG,IAAI/G,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACIyL,iBAAiB,GAAG,IAAIzL,YAAY,CAAC,CAAC;EACtC0L,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BP,YAAY;EACZI,gBAAgB,GAAG,CAAC;EACpBH,6BAA6B,GAAG,KAAK;EACrCzE,IAAI,GAAG,EAAE;EACTf,WAAWA,CAACvB,EAAE,EAAEC,cAAc,EAAE;IAC5B,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACA4C,SAASA,CAACf,KAAK,EAAE;IACb,QAAQA,KAAK,CAACgB,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAACwE,iBAAiB,CAACxF,KAAK,CAAC;QAC7B;MACJ,KAAK,SAAS;QACV,IAAI,CAACyF,eAAe,CAACzF,KAAK,CAAC;QAC3B;MACJ,KAAK,MAAM;QACP,IAAI,CAACA,KAAK,CAAC0F,QAAQ,EAAE;UACjB,IAAI,CAACC,YAAY,CAAC3F,KAAK,CAAC;QAC5B;QACA;MACJ,KAAK,KAAK;QACN,IAAI,CAACA,KAAK,CAAC0F,QAAQ,EAAE;UACjB,IAAI,CAACE,WAAW,CAAC5F,KAAK,CAAC;QAC3B;QACA;IACR;EACJ;EACAwF,iBAAiBA,CAACxF,KAAK,EAAE;IACrB,MAAM6F,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC9F,KAAK,CAAC+F,MAAM,CAACC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;IAC1GH,gBAAgB,GAAG,IAAI,CAACI,gBAAgB,CAACJ,gBAAgB,CAAC,GAAG,IAAI,CAACF,YAAY,CAAC3F,KAAK,CAAC;IACrFA,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACA4E,eAAeA,CAACzF,KAAK,EAAE;IACnB,MAAMkG,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACnG,KAAK,CAAC+F,MAAM,CAACC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;IAC1GE,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,CAACC,gBAAgB,CAAC,GAAG,IAAI,CAACN,WAAW,CAAC5F,KAAK,CAAC;IACpFA,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACA8E,YAAYA,CAAC3F,KAAK,EAAE;IAChB,MAAMoG,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACJ,gBAAgB,CAACG,iBAAiB,CAAC;IACxCpG,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACAoF,gBAAgBA,CAACK,OAAO,EAAE;IACtB,IAAIA,OAAO,EAAE;MACT5L,UAAU,CAAC6L,KAAK,CAACD,OAAO,CAAC;MACzB,IAAI,IAAI,CAACnB,aAAa,EAAE;QACpB,IAAI,CAAC3E,IAAI,CAACb,OAAO,CAAC,CAAC6G,GAAG,EAAEjG,CAAC,KAAK;UAC1B,IAAIvD,QAAQ,GAAG,IAAI,CAACsD,QAAQ,GAAG,IAAI,CAAC0E,YAAY,CAACyB,QAAQ,CAAClG,CAAC,CAAC,GAAGA,CAAC,KAAK,IAAI,CAACyE,YAAY;UACtF,IAAI,IAAI,CAAC1E,QAAQ,EAAE;YACf,IAAI,CAAC,IAAI,CAAC0E,YAAY,EAAE;cACpB,IAAI,CAACA,YAAY,GAAG,EAAE;YAC1B;YACA,IAAIwB,GAAG,CAACpI,EAAE,IAAIkI,OAAO,CAAClI,EAAE,EAAE;cACtBoI,GAAG,CAACxJ,QAAQ,GAAG,CAACwJ,GAAG,CAACxJ,QAAQ;cAC5B,IAAI,CAAC,IAAI,CAACgI,YAAY,CAACyB,QAAQ,CAAClG,CAAC,CAAC,EAAE;gBAChC,IAAI,CAACyE,YAAY,CAAC0B,IAAI,CAACnG,CAAC,CAAC;cAC7B,CAAC,MACI;gBACD,IAAI,CAACyE,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC2B,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAKrG,CAAC,CAAC;cACpE;YACJ;UACJ,CAAC,MACI;YACD,IAAIiG,GAAG,CAACpI,EAAE,IAAIkI,OAAO,CAAClI,EAAE,EAAE;cACtBoI,GAAG,CAACxJ,QAAQ,GAAG,CAACwJ,GAAG,CAACxJ,QAAQ;cAC5B,IAAI,CAACgI,YAAY,GAAGzE,CAAC;YACzB,CAAC,MACI;cACDiG,GAAG,CAACxJ,QAAQ,GAAG,KAAK;YACxB;UACJ;UACAwJ,GAAG,CAACpH,cAAc,CAACgB,IAAI,CAACpD,QAAQ,CAAC;UACjC,IAAI,CAACqI,iBAAiB,CAACjF,IAAI,CAAC,IAAI,CAAC4E,YAAY,CAAC;UAC9CwB,GAAG,CAACrI,cAAc,CAACuC,YAAY,CAAC,CAAC;QACrC,CAAC,CAAC;MACN;IACJ;EACJ;EACAoF,oBAAoBA,CAACe,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAE;IAChD,MAAMC,cAAc,GAAGD,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACG,kBAAkB;IAC7E,MAAMC,aAAa,GAAGvM,UAAU,CAACwM,UAAU,CAACH,cAAc,EAAE,4BAA4B,CAAC;IACzF,OAAOE,aAAa,GAAIvM,UAAU,CAACyM,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACnB,oBAAoB,CAACmB,aAAa,CAACjB,aAAa,CAACA,aAAa,CAAC,GAAGtL,UAAU,CAACwM,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAI,IAAI;EAC/O;EACAd,oBAAoBA,CAACU,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAE;IAChD,MAAMM,cAAc,GAAGN,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACQ,sBAAsB;IACjF,MAAMJ,aAAa,GAAGvM,UAAU,CAACwM,UAAU,CAACE,cAAc,EAAE,4BAA4B,CAAC;IACzF,OAAOH,aAAa,GAAIvM,UAAU,CAACyM,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACd,oBAAoB,CAACc,aAAa,CAACjB,aAAa,CAACA,aAAa,CAAC,GAAGtL,UAAU,CAACwM,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAI,IAAI;EAC/O;EACAZ,qBAAqBA,CAAA,EAAG;IACpB,MAAMiB,OAAO,GAAG,IAAI,CAACpJ,EAAE,CAACqJ,aAAa,CAACC,iBAAiB,CAACC,UAAU,CAAC,CAAC,CAAC;IACrE,OAAO,IAAI,CAAC3B,oBAAoB,CAACwB,OAAO,EAAE,IAAI,CAAC;EACnD;EACAI,oBAAoBA,CAAA,EAAG;IACnB,MAAMD,UAAU,GAAG,IAAI,CAACvJ,EAAE,CAACqJ,aAAa,CAACC,iBAAiB,CAACC,UAAU;IACrE,MAAME,MAAM,GAAGF,UAAU,CAACA,UAAU,CAAChH,MAAM,GAAG,CAAC,CAAC;IAChD,OAAO,IAAI,CAAC0F,oBAAoB,CAACwB,MAAM,EAAE,IAAI,CAAC;EAClD;EACA/B,WAAWA,CAAC5F,KAAK,EAAE;IACf,MAAM4H,gBAAgB,GAAG,IAAI,CAACF,oBAAoB,CAAC,CAAC;IACpD,IAAI,CAACzB,gBAAgB,CAAC2B,gBAAgB,CAAC;IACvC5H,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACAnB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACmI,QAAQ,CAAC,CAAC;IACf,IAAI,CAACtC,mBAAmB,GAAG,IAAI,CAACD,OAAO,CAACwC,OAAO,CAACC,SAAS,CAAEC,CAAC,IAAK;MAC7D,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAA,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrH,IAAI,GAAG,IAAI,CAAC8E,OAAO,CAAC2C,OAAO,CAAC,CAAC;IAClC,IAAI,CAACzH,IAAI,CAACb,OAAO,CAAE6G,GAAG,IAAK;MACvBA,GAAG,CAACrH,eAAe,GAAG,IAAI,CAACiG,gBAAgB;IAC/C,CAAC,CAAC;IACF,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC/G,cAAc,CAACuC,YAAY,CAAC,CAAC;EACtC;EACAwH,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChK,EAAE,CAACqJ,aAAa,CAACY,QAAQ,CAAC,CAAC,CAAC;EAC5C;EACAjD,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC1E,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,IAAI,IAAI,CAACuE,YAAY,IAAI,IAAI,EAAE;MAC5D,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACvC,IAAIvD,QAAQ,GAAG,IAAI,CAACsD,QAAQ,GAAG,IAAI,CAAC0E,YAAY,CAACyB,QAAQ,CAAClG,CAAC,CAAC,GAAGA,CAAC,KAAK,IAAI,CAACyE,YAAY;QACtF,IAAIoD,OAAO,GAAGpL,QAAQ,KAAK,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ;QAChD,IAAIoL,OAAO,EAAE;UACT,IAAI,CAAC5H,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,GAAGA,QAAQ;UAChC,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACnB,cAAc,CAACgB,IAAI,CAACpD,QAAQ,CAAC;UAC1C,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACpC,cAAc,CAACuC,YAAY,CAAC,CAAC;QAC9C;MACJ;IACJ;EACJ;EACA2H,WAAWA,CAACpI,KAAK,EAAE;IACf,OAAO,IAAI,CAACK,QAAQ,GAAG,IAAI,CAAC0E,YAAY,IAAI,IAAI,CAACA,YAAY,CAACyB,QAAQ,CAACxG,KAAK,CAAC,GAAG,IAAI,CAAC+E,YAAY,KAAK/E,KAAK;EAC/G;EACAqI,UAAUA,CAAC9B,GAAG,EAAE+B,IAAI,EAAE;IAClB,OAAO/B,GAAG,CAACgC,KAAK,GAAGhC,GAAG,CAACgC,KAAK,CAACD,IAAI,CAAC,GAAGE,SAAS;EAClD;EACA7H,iBAAiBA,CAAA,EAAG;IAChB,IAAIX,KAAK,GAAG,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,IAAI;IACrC,IAAI,CAACE,IAAI,CAACb,OAAO,CAAC,CAAC6G,GAAG,EAAEjG,CAAC,KAAK;MAC1B,IAAIiG,GAAG,CAACxJ,QAAQ,EAAE;QACd,IAAI,IAAI,CAACsD,QAAQ,EAAE;UACfL,KAAK,CAACyG,IAAI,CAACnG,CAAC,CAAC;QACjB,CAAC,MACI;UACDN,KAAK,GAAGM,CAAC;UACT;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAAC0E,6BAA6B,GAAG,IAAI;IACzC,IAAI,CAACI,iBAAiB,CAACjF,IAAI,CAACH,KAAK,CAAC;EACtC;EACAmB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACmE,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACmD,WAAW,CAAC,CAAC;IAC1C;EACJ;EACA,OAAOpH,IAAI,YAAAqH,kBAAAnH,CAAA;IAAA,YAAAA,CAAA,IAAwFE,SAAS,EAzanB/H,EAAE,CAAA8H,iBAAA,CAyamC9H,EAAE,CAACgI,UAAU,GAzalDhI,EAAE,CAAA8H,iBAAA,CAya6D9H,EAAE,CAACiI,iBAAiB;EAAA;EAC5K,OAAOC,IAAI,kBA1a8ElI,EAAE,CAAAmI,iBAAA;IAAAC,IAAA,EA0aJL,SAAS;IAAAM,SAAA;IAAAC,cAAA,WAAA2G,yBAAAlN,EAAA,EAAAC,GAAA,EAAAwG,QAAA;MAAA,IAAAzG,EAAA;QA1aP/B,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EA0aqelE,YAAY;MAAA;MAAA,IAAAvC,EAAA;QAAA,IAAA2G,EAAA;QA1anf1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA5G,GAAA,CAAA2J,OAAA,GAAAjD,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAqG,YAAA,WAAAC,uBAAApN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF/B,EAAE,CAAAsJ,UAAA,qBAAA8F,qCAAA5F,MAAA;UAAA,OA0aJxH,GAAA,CAAAoF,SAAA,CAAAoC,MAAgB,CAAC;QAAA,CAAT,CAAC;MAAA;IAAA;IAAAV,MAAA;MAAAnC,QAAA;MAAAlH,KAAA;MAAA0L,UAAA;MAAAlI,UAAA;MAAAX,YAAA;MAAA8I,WAAA;MAAAI,aAAA;MAAAhG,eAAA;IAAA;IAAAuD,OAAA;MAAAvC,OAAA;MAAAQ,MAAA;MAAA0E,iBAAA;IAAA;IAAA1C,kBAAA,EAAA3E,GAAA;IAAA4E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAhD,QAAA,WAAAkJ,mBAAAtN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1aP/B,EAAE,CAAAqJ,eAAA;QAAFrJ,EAAE,CAAAyD,cAAA,YA2aN,CAAC;QA3aGzD,EAAE,CAAAiE,YAAA,EA4a3D,CAAC;QA5awDjE,EAAE,CAAA2D,YAAA,CA6alF,CAAC;MAAA;MAAA,IAAA5B,EAAA;QA7a+E/B,EAAE,CAAAoC,UAAA,CAAAJ,GAAA,CAAAmJ,UA2aP,CAAC;QA3aInL,EAAE,CAAAuC,UAAA,qCA2a9C,CAAC,YAAAP,GAAA,CAAAvC,KAAiB,CAAC;MAAA;IAAA;IAAAoK,YAAA,GAGH/J,EAAE,CAACgK,OAAO,EAAoFhK,EAAE,CAACmK,OAAO;IAAAE,aAAA;IAAAK,eAAA;EAAA;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhb6FzK,EAAE,CAAA0K,iBAAA,CAgbJ3C,SAAS,EAAc,CAAC;IACvGK,IAAI,EAAEjI,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBzE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KAAK;MACeqE,eAAe,EAAEpK,uBAAuB,CAAC0K,MAAM;MAC/CE,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7C,IAAI,EAAEpI,EAAE,CAACgI;EAAW,CAAC,EAAE;IAAEI,IAAI,EAAEpI,EAAE,CAACiI;EAAkB,CAAC,CAAC,EAAkB;IAAEtB,QAAQ,EAAE,CAAC;MAC1GyB,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEd,KAAK,EAAE,CAAC;MACR2I,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE4K,UAAU,EAAE,CAAC;MACb/C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE0C,UAAU,EAAE,CAAC;MACbmF,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE+B,YAAY,EAAE,CAAC;MACf8F,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE6K,WAAW,EAAE,CAAC;MACdhD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiL,aAAa,EAAE,CAAC;MAChBpD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiF,eAAe,EAAE,CAAC;MAClB4C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiG,OAAO,EAAE,CAAC;MACV4B,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEwG,MAAM,EAAE,CAAC;MACToB,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEkL,iBAAiB,EAAE,CAAC;MACpBtD,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEmL,OAAO,EAAE,CAAC;MACVvD,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAACrG,YAAY,EAAE;QAAEgL,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAElI,SAAS,EAAE,CAAC;MACZgB,IAAI,EAAE1H,YAAY;MAClBiK,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM4E,eAAe,CAAC;EAClB,OAAO5H,IAAI,YAAA6H,wBAAA3H,CAAA;IAAA,YAAAA,CAAA,IAAwF0H,eAAe;EAAA;EAClH,OAAOE,IAAI,kBA7d8EzP,EAAE,CAAA0P,gBAAA;IAAAtH,IAAA,EA6dSmH;EAAe;EACnH,OAAOI,IAAI,kBA9d8E3P,EAAE,CAAA4P,gBAAA;IAAAC,OAAA,GA8doC9P,YAAY,EAAEkB,gBAAgB,EAAED,eAAe,EAAEF,YAAY;EAAA;AAChM;AACA;EAAA,QAAA2J,SAAA,oBAAAA,SAAA,KAhe6FzK,EAAE,CAAA0K,iBAAA,CAgeJ6E,eAAe,EAAc,CAAC;IAC7GnH,IAAI,EAAEzH,QAAQ;IACdgK,IAAI,EAAE,CAAC;MACCkF,OAAO,EAAE,CAAC9P,YAAY,EAAEkB,gBAAgB,EAAED,eAAe,CAAC;MAC1D8O,OAAO,EAAE,CAAC/H,SAAS,EAAEzD,YAAY,EAAExD,YAAY,CAAC;MAChDiP,YAAY,EAAE,CAAChI,SAAS,EAAEzD,YAAY;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASyD,SAAS,EAAEwH,eAAe,EAAEjL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}