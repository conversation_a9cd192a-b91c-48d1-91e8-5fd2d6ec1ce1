{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { OpportunitiesComponent } from './opportunities.component';\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\nimport { OpportunitiesFollowUpComponent } from './opportunities-details/opportunities-follow-up/opportunities-follow-up.component';\nimport { OpportunitiesHierarchyComponent } from './opportunities-details/opportunities-hierarchy/opportunities-hierarchy.component';\nimport { OpportunitiesDocumentFlowComponent } from './opportunities-details/opportunities-document-flow/opportunities-document-flow.component';\nimport { SalesCallFollowItemDetailComponent } from './opportunities-details/opportunities-follow-up/sales-call-follow-item-detail/sales-call-follow-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: OpportunitiesComponent\n}, {\n  path: 'create',\n  component: AddOpportunitieComponent\n}, {\n  path: ':id',\n  component: OpportunitiesDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: OpportunitiesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: OpportunitiesContactsComponent\n  }, {\n    path: 'sales-team',\n    component: OpportunitiesSalesTeamComponent\n  }, {\n    path: 'follow-up',\n    component: OpportunitiesFollowUpComponent\n  }, {\n    path: 'follow-up/:id',\n    component: SalesCallFollowItemDetailComponent\n  }, {\n    path: 'hierarchy',\n    component: OpportunitiesHierarchyComponent\n  }, {\n    path: 'document-flow',\n    component: OpportunitiesDocumentFlowComponent\n  }, {\n    path: 'ai-insights',\n    component: OpportunitiesAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: OpportunitiesOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: OpportunitiesAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: OpportunitiesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let OpportunitiesRoutingModule = /*#__PURE__*/(() => {\n  class OpportunitiesRoutingModule {\n    static {\n      this.ɵfac = function OpportunitiesRoutingModule_Factory(t) {\n        return new (t || OpportunitiesRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: OpportunitiesRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return OpportunitiesRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}