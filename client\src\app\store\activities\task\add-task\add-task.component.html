<p-toast position="top-right" [life]="3000"></p-toast>
<form [formGroup]="TaskForm">
    <div class="card shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50">
        <h3 class="mb-2 flex align-items-center h-3rem">Create Task</h3>
        <div class="p-fluid p-formgrid grid mt-0">
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">description</span>
                        Transaction Type <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="dropdowns['activityDocumentTypes']" formControlName="document_type"
                        placeholder="Select a Document Type" optionLabel="label" optionValue="value"
                        styleClass="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['document_type'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['document_type'].errors" class="p-error">
                        <div *ngIf="
                            submitted &&
                            f['document_type'].errors &&
                            f['document_type'].errors['required']
                          ">
                            Document Type is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">subject</span>
                        Subject <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="subject" type="text" formControlName="subject" placeholder="Subject"
                        class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['subject'].errors }" />
                    <div *ngIf="submitted && f['subject'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['subject'].errors &&
                                f['subject'].errors['required']
                              ">
                            Subject is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">account_circle</span>
                        Account <span class="text-red-500">*</span>
                    </label>
                    <ng-select pInputText [items]="accounts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="accountLoading" [minTermLength]="0"
                        formControlName="main_account_party_id" [typeahead]="accountInput$" [maxSelectedItems]="10"
                        appendTo="body" [ngClass]="{ 'is-invalid': submitted && f['main_account_party_id'].errors }"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Search for an account">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.bp_id }}</span>
                            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                        </ng-template>
                    </ng-select>
                    <div *ngIf="submitted && f['main_account_party_id'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['main_account_party_id'].errors &&
                                f['main_account_party_id'].errors['required']
                              ">
                            Account is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">person</span>
                        Contact <span class="text-red-500">*</span>
                    </label>
                    <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="contactLoading" [minTermLength]="0"
                        formControlName="main_contact_party_id" [typeahead]="contactInput$" [maxSelectedItems]="10"
                        appendTo="body" [closeOnSelect]="false"
                        [ngClass]="{ 'is-invalid': submitted && f['main_contact_party_id'].errors }"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Select a Contact">
                        <ng-template ng-option-tmp let-item="item">
                            <div class="flex align-items-center gap-2">
                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                                <span *ngIf="item.email"> : {{ item.email }}</span>
                                <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                    <div *ngIf="submitted && f['main_contact_party_id'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['main_contact_party_id'].errors &&
                                f['main_contact_party_id'].errors['required']
                              ">
                            Contact is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">category</span>
                        Category <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="dropdowns['activityCategory']" formControlName="task_category"
                        placeholder="Select a Category" optionLabel="label" optionValue="value"
                        styleClass="h-3rem w-full"
                        [ngClass]="{ 'is-invalid': submitted && f['task_category'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['task_category'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['task_category'].errors &&
                                f['task_category'].errors['required']
                              ">
                            Category is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">person</span>
                        Processor
                    </label>
                    <ng-select pInputText [items]="employees$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="employeeLoading" [minTermLength]="0"
                        formControlName="processor_party_id" [typeahead]="employeeInput$" [maxSelectedItems]="10"
                        appendTo="body" [closeOnSelect]="false"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Search for a processor">
                        <ng-template ng-option-tmp let-item="item">
                            <div class="flex align-items-center gap-2">
                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                                <span *ngIf="item.email"> : {{ item.email }}</span>
                                <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">schedule</span>
                        Create Date
                    </label>
                    <p-calendar formControlName="start_date" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                        [showIcon]="true" styleClass="h-3rem w-full" placeholder="Create Date" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">schedule</span>
                        Expected Decision Date
                    </label>
                    <p-calendar formControlName="end_date" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                        [showIcon]="true" styleClass="h-3rem w-full" placeholder="Expected Decision Date" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">flag</span>
                        Priority
                    </label>
                    <p-dropdown [options]="dropdowns['activityPriority']" formControlName="priority"
                        placeholder="Select a Prioriry" optionLabel="label" optionValue="value"
                        styleClass="h-3rem w-full">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">check_circle</span>
                        Status <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="dropdowns['activityStatus']" formControlName="activity_status"
                        placeholder="Select a Status" optionLabel="label" optionValue="value" styleClass="h-3rem w-full"
                        [ngClass]="{ 'is-invalid': submitted && f['activity_status'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['activity_status'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['activity_status'].errors &&
                                f['activity_status'].errors['required']
                              ">
                            Status is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-8 md:col-8 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">notes</span>
                        Notes <span class="text-red-500">*</span>
                    </label>
                    <p-editor formControlName="notes" placeholder="Enter your note here..."
                        [style]="{ height: '125px' }" [ngClass]="{ 'is-invalid': submitted && f['notes'].errors }"
                        [ngClass]="{ 'is-invalid': submitted && f['notes'].errors }" />
                    <div *ngIf="submitted && f['notes'].errors" class="p-error">
                        <div *ngIf="
                                    submitted &&
                                    f['notes'].errors &&
                                    f['notes'].errors['required']
                                  ">
                            Notes is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="border-top-2 border-gray-400 my-5"></div>
        </div>
    </div>
    <div class="flex align-items-center gap-3 mt-4 ml-auto">
        <button pButton type="button" label="Cancel"
            class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
            (click)="onCancel()"></button>
        <button pButton type="submit" label="Create" class="p-button-rounded justify-content-center w-9rem h-3rem"
            (click)="onSubmit()"></button>
    </div>
</form>