{"ast": null, "code": "import countryList from './assets/country.json';\nimport { compare, findEntryByCode } from './utils';\n// Get a country by isoCode.\nfunction getCountryByCode(isoCode) {\n  if (!isoCode) return undefined;\n  return findEntryByCode(countryList, isoCode);\n}\n// Get a list of all countries.\nfunction getAllCountries() {\n  return countryList;\n}\nfunction sortByIsoCode(countries) {\n  return countries.sort((a, b) => {\n    return compare(a, b, entity => {\n      return entity.isoCode;\n    });\n  });\n}\nexport default {\n  getCountryByCode,\n  getAllCountries,\n  sortByIsoCode\n};", "map": {"version": 3, "names": ["countryList", "compare", "findEntryByCode", "getCountryByCode", "isoCode", "undefined", "getAllCountries", "sortByIsoCode", "countries", "sort", "a", "b", "entity"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/country-state-city/lib/country.js"], "sourcesContent": ["import countryList from './assets/country.json';\nimport { compare, findEntryByCode } from './utils';\n// Get a country by isoCode.\nfunction getCountryByCode(isoCode) {\n    if (!isoCode)\n        return undefined;\n    return findEntryByCode(countryList, isoCode);\n}\n// Get a list of all countries.\nfunction getAllCountries() {\n    return countryList;\n}\nfunction sortByIsoCode(countries) {\n    return countries.sort((a, b) => {\n        return compare(a, b, (entity) => {\n            return entity.isoCode;\n        });\n    });\n}\nexport default {\n    getCountryByCode,\n    getAllCountries,\n    sortByIsoCode,\n};\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,uBAAuB;AAC/C,SAASC,OAAO,EAAEC,eAAe,QAAQ,SAAS;AAClD;AACA,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EACR,OAAOC,SAAS;EACpB,OAAOH,eAAe,CAACF,WAAW,EAAEI,OAAO,CAAC;AAChD;AACA;AACA,SAASE,eAAeA,CAAA,EAAG;EACvB,OAAON,WAAW;AACtB;AACA,SAASO,aAAaA,CAACC,SAAS,EAAE;EAC9B,OAAOA,SAAS,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5B,OAAOV,OAAO,CAACS,CAAC,EAAEC,CAAC,EAAGC,MAAM,IAAK;MAC7B,OAAOA,MAAM,CAACR,OAAO;IACzB,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,eAAe;EACXD,gBAAgB;EAChBG,eAAe;EACfC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}