{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TicketStorageService {\n  constructor() {\n    this.STORAGE_PREFIX = 'ticket_form_data_';\n  }\n  /**\n   * Save ticket form data to local storage using ticket ID as key\n   * @param ticketId - The unique ticket ID to use as storage key\n   * @param formData - The form data containing invoice_no, order_no, and credit_memo_no\n   */\n  saveTicketFormData(ticketId, formData) {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to save form data');\n      return;\n    }\n    const ticketFormData = {\n      invoice_no: formData.invoice_no || '',\n      order_no: formData.order_no || '',\n      credit_memo_no: formData.credit_memo_no || '',\n      timestamp: new Date().toISOString()\n    };\n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      localStorage.setItem(storageKey, JSON.stringify(ticketFormData));\n    } catch (error) {\n      console.error('Error saving ticket form data to local storage:', error);\n    }\n  }\n  /**\n   * Retrieve ticket form data from local storage by ticket ID\n   * @param ticketId - The unique ticket ID used as storage key\n   * @returns The stored ticket form data or null if not found\n   */\n  getTicketFormData(ticketId) {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to retrieve form data');\n      return null;\n    }\n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      const storedData = localStorage.getItem(storageKey);\n      return storedData ? JSON.parse(storedData) : null;\n    } catch (error) {\n      console.error('Error retrieving ticket form data from local storage:', error);\n      return null;\n    }\n  }\n  /**\n   * Clear ticket form data from local storage by ticket ID\n   * @param ticketId - The unique ticket ID used as storage key\n   */\n  clearTicketFormData(ticketId) {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to clear form data');\n      return;\n    }\n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      localStorage.removeItem(storageKey);\n    } catch (error) {\n      console.error('Error clearing ticket form data from local storage:', error);\n    }\n  }\n  /**\n   * Get all ticket form data keys from local storage\n   * @returns Array of ticket IDs that have stored form data\n   */\n  getAllTicketFormDataKeys() {\n    try {\n      const keys = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          keys.push(key.replace(this.STORAGE_PREFIX, ''));\n        }\n      }\n      return keys;\n    } catch (error) {\n      console.error('Error getting ticket form data keys from local storage:', error);\n      return [];\n    }\n  }\n  /**\n   * Clear all ticket form data from local storage\n   * Useful for cleanup operations\n   */\n  clearAllTicketFormData() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          keysToRemove.push(key);\n        }\n      }\n      keysToRemove.forEach(key => {\n        localStorage.removeItem(key);\n      });\n    } catch (error) {\n      console.error('Error clearing all ticket form data from local storage:', error);\n    }\n  }\n  /**\n   * Clear old ticket form data (older than specified days)\n   * @param daysOld - Number of days after which data should be considered old\n   */\n  clearOldTicketFormData(daysOld = 30) {\n    try {\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - daysOld);\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          const storedData = localStorage.getItem(key);\n          if (storedData) {\n            try {\n              const parsedData = JSON.parse(storedData);\n              const dataDate = new Date(parsedData.timestamp);\n              if (dataDate < cutoffDate) {\n                keysToRemove.push(key);\n              }\n            } catch (parseError) {\n              // If we can't parse the data, consider it old and remove it\n              keysToRemove.push(key);\n            }\n          }\n        }\n      }\n      keysToRemove.forEach(key => {\n        localStorage.removeItem(key);\n      });\n      if (keysToRemove.length > 0) {\n        console.log(`Cleared ${keysToRemove.length} old ticket form data entries`);\n      }\n    } catch (error) {\n      console.error('Error clearing old ticket form data from local storage:', error);\n    }\n  }\n  static {\n    this.ɵfac = function TicketStorageService_Factory(t) {\n      return new (t || TicketStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TicketStorageService,\n      factory: TicketStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["TicketStorageService", "constructor", "STORAGE_PREFIX", "saveTicketFormData", "ticketId", "formData", "console", "warn", "ticketFormData", "invoice_no", "order_no", "credit_memo_no", "timestamp", "Date", "toISOString", "storageKey", "localStorage", "setItem", "JSON", "stringify", "error", "getTicketFormData", "storedData", "getItem", "parse", "clearTicketFormData", "removeItem", "getAllTicketFormDataKeys", "keys", "i", "length", "key", "startsWith", "push", "replace", "clearAllTicketFormData", "keysToRemove", "for<PERSON>ach", "clearOldTicketFormData", "daysOld", "cutoffDate", "setDate", "getDate", "parsedData", "dataDate", "parseError", "log", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\ticket-storage.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\nexport interface TicketFormData {\n  invoice_no: string;\n  order_no: string;\n  credit_memo_no: string;\n  timestamp: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TicketStorageService {\n\n  private readonly STORAGE_PREFIX = 'ticket_form_data_';\n\n  constructor() { }\n\n  /**\n   * Save ticket form data to local storage using ticket ID as key\n   * @param ticketId - The unique ticket ID to use as storage key\n   * @param formData - The form data containing invoice_no, order_no, and credit_memo_no\n   */\n  saveTicketFormData(ticketId: string, formData: Partial<TicketFormData>): void {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to save form data');\n      return;\n    }\n    \n    const ticketFormData: TicketFormData = {\n      invoice_no: formData.invoice_no || '',\n      order_no: formData.order_no || '',\n      credit_memo_no: formData.credit_memo_no || '',\n      timestamp: new Date().toISOString()\n    };\n    \n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      localStorage.setItem(storageKey, JSON.stringify(ticketFormData));\n    } catch (error) {\n      console.error('Error saving ticket form data to local storage:', error);\n    }\n  }\n\n  /**\n   * Retrieve ticket form data from local storage by ticket ID\n   * @param ticketId - The unique ticket ID used as storage key\n   * @returns The stored ticket form data or null if not found\n   */\n  getTicketFormData(ticketId: string): TicketFormData | null {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to retrieve form data');\n      return null;\n    }\n    \n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      const storedData = localStorage.getItem(storageKey);\n      return storedData ? JSON.parse(storedData) : null;\n    } catch (error) {\n      console.error('Error retrieving ticket form data from local storage:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear ticket form data from local storage by ticket ID\n   * @param ticketId - The unique ticket ID used as storage key\n   */\n  clearTicketFormData(ticketId: string): void {\n    if (!ticketId) {\n      console.warn('Ticket ID is required to clear form data');\n      return;\n    }\n    \n    try {\n      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n      localStorage.removeItem(storageKey);\n    } catch (error) {\n      console.error('Error clearing ticket form data from local storage:', error);\n    }\n  }\n\n  /**\n   * Get all ticket form data keys from local storage\n   * @returns Array of ticket IDs that have stored form data\n   */\n  getAllTicketFormDataKeys(): string[] {\n    try {\n      const keys: string[] = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          keys.push(key.replace(this.STORAGE_PREFIX, ''));\n        }\n      }\n      return keys;\n    } catch (error) {\n      console.error('Error getting ticket form data keys from local storage:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Clear all ticket form data from local storage\n   * Useful for cleanup operations\n   */\n  clearAllTicketFormData(): void {\n    try {\n      const keysToRemove: string[] = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          keysToRemove.push(key);\n        }\n      }\n      \n      keysToRemove.forEach(key => {\n        localStorage.removeItem(key);\n      });\n    } catch (error) {\n      console.error('Error clearing all ticket form data from local storage:', error);\n    }\n  }\n\n  /**\n   * Clear old ticket form data (older than specified days)\n   * @param daysOld - Number of days after which data should be considered old\n   */\n  clearOldTicketFormData(daysOld: number = 30): void {\n    try {\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - daysOld);\n      \n      const keysToRemove: string[] = [];\n      \n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.STORAGE_PREFIX)) {\n          const storedData = localStorage.getItem(key);\n          if (storedData) {\n            try {\n              const parsedData: TicketFormData = JSON.parse(storedData);\n              const dataDate = new Date(parsedData.timestamp);\n              if (dataDate < cutoffDate) {\n                keysToRemove.push(key);\n              }\n            } catch (parseError) {\n              // If we can't parse the data, consider it old and remove it\n              keysToRemove.push(key);\n            }\n          }\n        }\n      }\n      \n      keysToRemove.forEach(key => {\n        localStorage.removeItem(key);\n      });\n      \n      if (keysToRemove.length > 0) {\n        console.log(`Cleared ${keysToRemove.length} old ticket form data entries`);\n      }\n    } catch (error) {\n      console.error('Error clearing old ticket form data from local storage:', error);\n    }\n  }\n}\n"], "mappings": ";AAYA,OAAM,MAAOA,oBAAoB;EAI/BC,YAAA;IAFiB,KAAAC,cAAc,GAAG,mBAAmB;EAErC;EAEhB;;;;;EAKAC,kBAAkBA,CAACC,QAAgB,EAAEC,QAAiC;IACpE,IAAI,CAACD,QAAQ,EAAE;MACbE,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAC;MACvD;IACF;IAEA,MAAMC,cAAc,GAAmB;MACrCC,UAAU,EAAEJ,QAAQ,CAACI,UAAU,IAAI,EAAE;MACrCC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,IAAI,EAAE;MACjCC,cAAc,EAAEN,QAAQ,CAACM,cAAc,IAAI,EAAE;MAC7CC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC;IAED,IAAI;MACF,MAAMC,UAAU,GAAG,GAAG,IAAI,CAACb,cAAc,GAAGE,QAAQ,EAAE;MACtDY,YAAY,CAACC,OAAO,CAACF,UAAU,EAAEG,IAAI,CAACC,SAAS,CAACX,cAAc,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE;EACF;EAEA;;;;;EAKAC,iBAAiBA,CAACjB,QAAgB;IAChC,IAAI,CAACA,QAAQ,EAAE;MACbE,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;MAC3D,OAAO,IAAI;IACb;IAEA,IAAI;MACF,MAAMQ,UAAU,GAAG,GAAG,IAAI,CAACb,cAAc,GAAGE,QAAQ,EAAE;MACtD,MAAMkB,UAAU,GAAGN,YAAY,CAACO,OAAO,CAACR,UAAU,CAAC;MACnD,OAAOO,UAAU,GAAGJ,IAAI,CAACM,KAAK,CAACF,UAAU,CAAC,GAAG,IAAI;IACnD,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,OAAO,IAAI;IACb;EACF;EAEA;;;;EAIAK,mBAAmBA,CAACrB,QAAgB;IAClC,IAAI,CAACA,QAAQ,EAAE;MACbE,OAAO,CAACC,IAAI,CAAC,0CAA0C,CAAC;MACxD;IACF;IAEA,IAAI;MACF,MAAMQ,UAAU,GAAG,GAAG,IAAI,CAACb,cAAc,GAAGE,QAAQ,EAAE;MACtDY,YAAY,CAACU,UAAU,CAACX,UAAU,CAAC;IACrC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;IAC7E;EACF;EAEA;;;;EAIAO,wBAAwBA,CAAA;IACtB,IAAI;MACF,MAAMC,IAAI,GAAa,EAAE;MACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAME,GAAG,GAAGf,YAAY,CAACe,GAAG,CAACF,CAAC,CAAC;QAC/B,IAAIE,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC9B,cAAc,CAAC,EAAE;UAC9C0B,IAAI,CAACK,IAAI,CAACF,GAAG,CAACG,OAAO,CAAC,IAAI,CAAChC,cAAc,EAAE,EAAE,CAAC,CAAC;QACjD;MACF;MACA,OAAO0B,IAAI;IACb,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E,OAAO,EAAE;IACX;EACF;EAEA;;;;EAIAe,sBAAsBA,CAAA;IACpB,IAAI;MACF,MAAMC,YAAY,GAAa,EAAE;MACjC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAME,GAAG,GAAGf,YAAY,CAACe,GAAG,CAACF,CAAC,CAAC;QAC/B,IAAIE,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC9B,cAAc,CAAC,EAAE;UAC9CkC,YAAY,CAACH,IAAI,CAACF,GAAG,CAAC;QACxB;MACF;MAEAK,YAAY,CAACC,OAAO,CAACN,GAAG,IAAG;QACzBf,YAAY,CAACU,UAAU,CAACK,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;IACjF;EACF;EAEA;;;;EAIAkB,sBAAsBA,CAACC,OAAA,GAAkB,EAAE;IACzC,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI3B,IAAI,EAAE;MAC7B2B,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,EAAE,GAAGH,OAAO,CAAC;MAElD,MAAMH,YAAY,GAAa,EAAE;MAEjC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAME,GAAG,GAAGf,YAAY,CAACe,GAAG,CAACF,CAAC,CAAC;QAC/B,IAAIE,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC9B,cAAc,CAAC,EAAE;UAC9C,MAAMoB,UAAU,GAAGN,YAAY,CAACO,OAAO,CAACQ,GAAG,CAAC;UAC5C,IAAIT,UAAU,EAAE;YACd,IAAI;cACF,MAAMqB,UAAU,GAAmBzB,IAAI,CAACM,KAAK,CAACF,UAAU,CAAC;cACzD,MAAMsB,QAAQ,GAAG,IAAI/B,IAAI,CAAC8B,UAAU,CAAC/B,SAAS,CAAC;cAC/C,IAAIgC,QAAQ,GAAGJ,UAAU,EAAE;gBACzBJ,YAAY,CAACH,IAAI,CAACF,GAAG,CAAC;cACxB;YACF,CAAC,CAAC,OAAOc,UAAU,EAAE;cACnB;cACAT,YAAY,CAACH,IAAI,CAACF,GAAG,CAAC;YACxB;UACF;QACF;MACF;MAEAK,YAAY,CAACC,OAAO,CAACN,GAAG,IAAG;QACzBf,YAAY,CAACU,UAAU,CAACK,GAAG,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAIK,YAAY,CAACN,MAAM,GAAG,CAAC,EAAE;QAC3BxB,OAAO,CAACwC,GAAG,CAAC,WAAWV,YAAY,CAACN,MAAM,+BAA+B,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;IACjF;EACF;;;uBAzJWpB,oBAAoB;IAAA;EAAA;;;aAApBA,oBAAoB;MAAA+C,OAAA,EAApB/C,oBAAoB,CAAAgD,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}