{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/breadcrumb\";\nconst _c0 = () => [\"/auth/signup\"];\nfunction ActivitiesComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 15);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"External ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Delivery\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Requested Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 16);\n    i0.ɵɵtext(18, \"Expected Value\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActivitiesComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", tableinfo_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ExternalID, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Account, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Description, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Delivery, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.RequestedDate, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ExpectedValue, \" \");\n  }\n}\nexport class ActivitiesComponent {\n  constructor() {\n    this.tableData = [];\n  }\n  ngOnInit() {\n    this.items = [{\n      label: 'Activities',\n      routerLink: ['/store/activities']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.AdminM = [{\n      name: 'ALL(0)',\n      code: 'all'\n    }, {\n      name: 'My Orders (20)',\n      code: 'my-orders'\n    }, {\n      name: 'My Teams Orders (20)',\n      code: 'team-o'\n    }, {\n      name: 'Orders My Territories (20)',\n      code: 'territories-o'\n    }];\n    this.tableData = [{\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }, {\n      Id: '052',\n      ExternalID: 'BG1001',\n      Account: 'Hilton',\n      Description: 'Identity opp...',\n      Status: 'OPEN',\n      Delivery: 'Adam Smith',\n      RequestedDate: '04/11/2024',\n      ExpectedValue: '55000.00 USD'\n    }];\n  }\n  static {\n    this.ɵfac = function ActivitiesComponent_Factory(t) {\n      return new (t || ActivitiesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesComponent,\n      selectors: [[\"app-activities\"]],\n      decls: 17,\n      vars: 8,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round\", \"border-1\", \"surface-border\"], [1, \"pi\", \"pi-search\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [1, \"border-round-right-lg\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"]],\n      template: function ActivitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"span\", 6);\n          i0.ɵɵelement(7, \"input\", 7)(8, \"i\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"button\", 9)(10, \"span\", 10);\n          i0.ɵɵtext(11, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"p-table\", 12);\n          i0.ɵɵtemplate(15, ActivitiesComponent_ng_template_15_Template, 19, 0, \"ng-template\", 13)(16, ActivitiesComponent_ng_template_16_Template, 19, 10, \"ng-template\", 14);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 14)(\"paginator\", true);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.Table, i3.PrimeTemplate, i2.TableCheckbox, i2.TableHeaderCheckbox, i4.Breadcrumb],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "tableinfo_r1", "ɵɵtextInterpolate1", "Id", "ExternalID", "Account", "Description", "Status", "Delivery", "RequestedDate", "Expected<PERSON><PERSON><PERSON>", "ActivitiesComponent", "constructor", "tableData", "ngOnInit", "items", "label", "routerLink", "home", "icon", "AdminM", "name", "code", "selectors", "decls", "vars", "consts", "template", "ActivitiesComponent_Template", "rf", "ctx", "ɵɵtemplate", "ActivitiesComponent_ng_template_15_Template", "ActivitiesComponent_ng_template_16_Template", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\n\r\ninterface AdminM {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\ninterface AccountTableData {\r\n  Id?: string;\r\n  ExternalID?: string;\r\n  Account?: string;\r\n  Description?: string;\r\n  Status?: string;\r\n  Delivery?: string;\r\n  RequestedDate?: string;\r\n  ExpectedValue?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-activities',\r\n  templateUrl: './activities.component.html',\r\n  styleUrl: './activities.component.scss'\r\n})\r\nexport class ActivitiesComponent {\r\n\r\n  items: MenuItem[] | any;\r\n  home: MenuItem | any;\r\n  AdminM: AdminM[] | undefined;\r\n  SelectedAdminM: AdminM | undefined;\r\n\r\n  tableData: AccountTableData[] = [];\r\n\r\n  ngOnInit() {\r\n    this.items = [\r\n      { label: 'Activities', routerLink: ['/store/activities'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.AdminM = [\r\n      { name: 'ALL(0)', code: 'all' },\r\n      { name: 'My Orders (20)', code: 'my-orders' },\r\n      { name: 'My Teams Orders (20)', code: 'team-o' },\r\n      { name: 'Orders My Territories (20)', code: 'territories-o' },\r\n    ];\r\n\r\n    this.tableData = [\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n      {\r\n        Id: '052',\r\n        ExternalID: 'BG1001',\r\n        Account: 'Hilton',\r\n        Description: 'Identity opp...',\r\n        Status: 'OPEN',\r\n        Delivery: 'Adam Smith',\r\n        RequestedDate: '04/11/2024',\r\n        ExpectedValue: '55000.00 USD',\r\n      },\r\n\r\n    ];\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" placeholder=\"Search\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round border-1 surface-border\">\r\n                    <i class=\"pi pi-search\"></i>\r\n                </span>\r\n            </div>\r\n            <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n                class=\"h-3rem p-element p-ripple p-button-outlined p-button p-component w-8rem justify-content-center gap-2 font-semibold\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"tableData\" dataKey=\"id\" [rows]=\"14\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\" >\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox/>\r\n                    </th>\r\n                    <th>ID</th>\r\n                    <th>External ID</th>\r\n                    <th>Account</th>\r\n                    <th>Description</th>\r\n                    <th>Status</th>\r\n                    <th>Delivery</th>\r\n                    <th>Requested Date</th>\r\n                    <th class=\"border-round-right-lg\">Expected Value</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-tableinfo>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"tableinfo\" />\r\n                    </td>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline\" [routerLink]=\"'/store/activities/overview'\">\r\n                        {{ tableinfo.Id }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.ExternalID }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.Account }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.Description }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.Status }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.Delivery }}\r\n                    </td>\r\n                    <td>\r\n                        {{ tableinfo.RequestedDate }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        {{ tableinfo.ExpectedValue }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;IC2BoBA,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAwB;IAC5BF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,SAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACpDJ,EADoD,CAAAG,YAAA,EAAK,EACpD;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,0BAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA6G;IACzGD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA1BoBH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,UAAAC,YAAA,CAAmB;IAEyBP,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAM,UAAA,4CAA2C;IACxGN,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAE,EAAA,MACJ;IAEIT,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAG,UAAA,MACJ;IAEIV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAI,OAAA,MACJ;IAEIX,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAK,WAAA,MACJ;IAEIZ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAM,MAAA,MACJ;IAEIb,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAO,QAAA,MACJ;IAEId,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAQ,aAAA,MACJ;IAEIf,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAQ,kBAAA,MAAAD,YAAA,CAAAS,aAAA,MACJ;;;AD7CpB,OAAM,MAAOC,mBAAmB;EALhCC,YAAA;IAYE,KAAAC,SAAS,GAAuB,EAAE;;EAElCC,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAG,CACX;MAAEC,KAAK,EAAE,YAAY;MAAEC,UAAU,EAAE,CAAC,mBAAmB;IAAC,CAAE,CAC3D;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACG,MAAM,GAAG,CACZ;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAED,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAW,CAAE,EAC7C;MAAED,IAAI,EAAE,sBAAsB;MAAEC,IAAI,EAAE;IAAQ,CAAE,EAChD;MAAED,IAAI,EAAE,4BAA4B;MAAEC,IAAI,EAAE;IAAe,CAAE,CAC9D;IAED,IAAI,CAACT,SAAS,GAAG,CACf;MACEV,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,EACD;MACEP,EAAE,EAAE,KAAK;MACTC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,iBAAiB;MAC9BC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,YAAY;MAC3BC,aAAa,EAAE;KAChB,CAEF;EACH;;;uBA5OWC,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBxBnC,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAAqF;UACzFF,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAHR,CAAAC,cAAA,aAA2C,aAEb,cACW;UAG7BD,EAFA,CAAAE,SAAA,eACuG,WAC3E;UAEpCF,EADI,CAAAG,YAAA,EAAO,EACL;UAGFH,EAFJ,CAAAC,cAAA,gBAC+H,gBAC3E;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,mBAEY;UAkB3BD,EAhBA,CAAAqC,UAAA,KAAAC,2CAAA,2BAAgC,KAAAC,2CAAA,4BAgBY;UAiCxDvC,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAvEoBH,EAAA,CAAAK,SAAA,GAAe;UAAeL,EAA9B,CAAAM,UAAA,UAAA8B,GAAA,CAAAf,KAAA,CAAe,SAAAe,GAAA,CAAAZ,IAAA,CAAc,uCAAuC;UAW5DxB,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAA+B;UAQhDzC,EAAA,CAAAK,SAAA,GAAmB;UAAwCL,EAA3D,CAAAM,UAAA,UAAA8B,GAAA,CAAAjB,SAAA,CAAmB,YAAyB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}