{"ast": null, "code": "import { map, of } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SettingsService {\n  constructor(http) {\n    this.http = http;\n  }\n  getCurrencies() {\n    return ['USD'];\n  }\n  getCountries() {\n    return ['USA'];\n  }\n  getSettings() {\n    if (this.settings) {\n      return of(this.settings);\n    }\n    return this.http.get(`${CMS_APIContstant.SETTINGS}`, {}).pipe(map(res => {\n      this.settings = res.data;\n      return res.data;\n    }));\n  }\n  saveSettings(data) {\n    return this.http.post(`${CMS_APIContstant.SETTINGS}`, data).pipe(map(res => {\n      this.settings = res.data;\n      return res.data;\n    }));\n  }\n  static {\n    this.ɵfac = function SettingsService_Factory(t) {\n      return new (t || SettingsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SettingsService,\n      factory: SettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "of", "CMS_APIContstant", "SettingsService", "constructor", "http", "getCurrencies", "getCountries", "getSettings", "settings", "get", "SETTINGS", "pipe", "res", "data", "saveSettings", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\setting.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { map, of } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SettingsService {\r\n  private settings!: any;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getCurrencies() {\r\n    return ['USD'];\r\n  }\r\n\r\n  getCountries() {\r\n    return ['USA'];\r\n  }\r\n\r\n  getSettings() {\r\n    if (this.settings) {\r\n      return of(this.settings);\r\n    }\r\n    return this.http.get<any>(`${CMS_APIContstant.SETTINGS}`, {}).pipe(\r\n      map((res) => {\r\n        this.settings = res.data;\r\n        return res.data;\r\n      })\r\n    );\r\n  }\r\n\r\n  saveSettings(data: any) {\r\n    return this.http.post<any>(`${CMS_APIContstant.SETTINGS}`, data).pipe(\r\n      map((res) => {\r\n        this.settings = res.data;\r\n        return res.data;\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,aAAaA,CAAA;IACX,OAAO,CAAC,KAAK,CAAC;EAChB;EAEAC,YAAYA,CAAA;IACV,OAAO,CAAC,KAAK,CAAC;EAChB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB,OAAOR,EAAE,CAAC,IAAI,CAACQ,QAAQ,CAAC;IAC1B;IACA,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAM,GAAGR,gBAAgB,CAACS,QAAQ,EAAE,EAAE,EAAE,CAAC,CAACC,IAAI,CAChEZ,GAAG,CAAEa,GAAG,IAAI;MACV,IAAI,CAACJ,QAAQ,GAAGI,GAAG,CAACC,IAAI;MACxB,OAAOD,GAAG,CAACC,IAAI;IACjB,CAAC,CAAC,CACH;EACH;EAEAC,YAAYA,CAACD,IAAS;IACpB,OAAO,IAAI,CAACT,IAAI,CAACW,IAAI,CAAM,GAAGd,gBAAgB,CAACS,QAAQ,EAAE,EAAEG,IAAI,CAAC,CAACF,IAAI,CACnEZ,GAAG,CAAEa,GAAG,IAAI;MACV,IAAI,CAACJ,QAAQ,GAAGI,GAAG,CAACC,IAAI;MACxB,OAAOD,GAAG,CAACC,IAAI;IACjB,CAAC,CAAC,CACH;EACH;;;uBAhCWX,eAAe,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfjB,eAAe;MAAAkB,OAAA,EAAflB,eAAe,CAAAmB,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}