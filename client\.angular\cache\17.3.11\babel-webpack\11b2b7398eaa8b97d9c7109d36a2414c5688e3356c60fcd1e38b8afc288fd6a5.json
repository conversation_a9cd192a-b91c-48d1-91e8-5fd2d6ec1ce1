{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { State } from 'country-state-city';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../activities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction TaskComponent_ng_template_19_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskComponent_ng_template_19_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction TaskComponent_ng_template_19_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskComponent_ng_template_19_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction TaskComponent_ng_template_19_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function TaskComponent_ng_template_19_ng_container_8_Template_th_click_1_listener() {\n      const col_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskComponent_ng_template_19_ng_container_8_i_4_Template, 1, 1, \"i\", 24)(5, TaskComponent_ng_template_19_ng_container_8_i_5_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== col_r6.field);\n  }\n}\nfunction TaskComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function TaskComponent_ng_template_19_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"main_account_party_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" Account ID \");\n    i0.ɵɵtemplate(6, TaskComponent_ng_template_19_i_6_Template, 1, 1, \"i\", 24)(7, TaskComponent_ng_template_19_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, TaskComponent_ng_template_19_ng_container_8_Template, 6, 4, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === \"main_account_party_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== \"main_account_party_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/tasks/\" + task_r7.activity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.subject) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.business_partner == null ? null : task_r7.business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.business_partner_contact == null ? null : task_r7.business_partner_contact.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityStatus\", task_r7 == null ? null : task_r7.activity_status) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r3.stripHtml(task_r7 == null ? null : task_r7.globalNote == null ? null : task_r7.globalNote.note));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.globalNote == null ? null : task_r7.globalNote.note) ? i0.ɵɵpipeBind3(3, 2, ctx_r3.stripHtml(task_r7.globalNote.note), 0, 80) + (task_r7.globalNote.note.length > 80 ? \"...\" : \"\") : \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.start_date) ? i0.ɵɵpipeBind2(2, 1, task_r7 == null ? null : task_r7.start_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.end_date) ? i0.ɵɵpipeBind2(2, 1, task_r7 == null ? null : task_r7.end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityPriority\", task_r7 == null ? null : task_r7.priority) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.business_partner_processor == null ? null : task_r7.business_partner_processor.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityCategory\", task_r7 == null ? null : task_r7.task_category) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.business_partner_owner == null ? null : task_r7.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = i0.ɵɵnextContext().$implicit;\n    const task_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", task_r7[col_r8.field] || \"-\", \" \");\n  }\n}\nfunction TaskComponent_ng_template_20_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, TaskComponent_ng_template_20_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 35)(4, TaskComponent_ng_template_20_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 35)(5, TaskComponent_ng_template_20_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 35)(6, TaskComponent_ng_template_20_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 35)(7, TaskComponent_ng_template_20_ng_container_5_ng_container_7_Template, 4, 6, \"ng-container\", 35)(8, TaskComponent_ng_template_20_ng_container_5_ng_container_8_Template, 3, 4, \"ng-container\", 35)(9, TaskComponent_ng_template_20_ng_container_5_ng_container_9_Template, 3, 4, \"ng-container\", 35)(10, TaskComponent_ng_template_20_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 35)(11, TaskComponent_ng_template_20_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 35)(12, TaskComponent_ng_template_20_ng_container_5_ng_container_12_Template, 2, 1, \"ng-container\", 35)(13, TaskComponent_ng_template_20_ng_container_5_ng_container_13_Template, 2, 1, \"ng-container\", 35)(14, TaskComponent_ng_template_20_ng_container_5_ng_container_14_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_contact.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"activity_status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"notes.note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"priority\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_processor.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"task_category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n  }\n}\nfunction TaskComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TaskComponent_ng_template_20_ng_container_5_Template, 15, 12, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", task_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/tasks/\" + task_r7.activity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r7 == null ? null : task_r7.main_account_party_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction TaskComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"No tasks found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"Loading tasks data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TaskComponent {\n  constructor(activitiesservice, router) {\n    this.activitiesservice = activitiesservice;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.tasks = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.dropdowns = {\n      activityCategory: [],\n      activityStatus: [],\n      activityPriority: []\n    };\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'subject',\n      header: 'Subject'\n    }, {\n      field: 'business_partner.bp_full_name',\n      header: 'Account'\n    }, {\n      field: 'business_partner_contact.bp_full_name',\n      header: 'Primary Contact'\n    }, {\n      field: 'activity_status',\n      header: 'Status'\n    }, {\n      field: 'notes.note',\n      header: 'Notes'\n    }, {\n      field: 'start_date',\n      header: 'Start Date/Time'\n    }, {\n      field: 'end_date',\n      header: 'Due Date/Time'\n    }, {\n      field: 'priority',\n      header: 'Priority'\n    }, {\n      field: 'business_partner_processor.bp_full_name',\n      header: 'Processor'\n    }, {\n      field: 'task_category',\n      header: 'Category'\n    }, {\n      field: 'business_partner_owner.bp_full_name',\n      header: 'Owner'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.tasks.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n    this.breadcrumbitems = [{\n      label: 'Tasks',\n      routerLink: ['/store/activities/tasks']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Tasks',\n      code: 'MT'\n    }, {\n      name: 'My Open Tasks',\n      code: 'MOT'\n    }, {\n      name: 'My Completed Tasks',\n      code: 'MCT'\n    }, {\n      name: 'My Team`s Tasks',\n      code: 'MTST'\n    }, {\n      name: 'My Tasks Today',\n      code: 'MTT'\n    }, {\n      name: 'My Tasks This Week',\n      code: 'MTTW'\n    }, {\n      name: 'My Tasks This Month',\n      code: 'MTTM'\n    }];\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    // optional: preserve user reorder logic instead\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadTasks(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    const filter = this.selectedActions?.code;\n    this.activitiesservice.getTasks(page, pageSize, sortField, sortOrder, this.globalSearchTerm, filter).subscribe({\n      next: response => {\n        this.tasks = (response?.data || []).map(call => {\n          // 🔍 Find the global note from the call.notes array\n          const globalNote = call.notes?.find(n => n.is_global_note === true);\n          return {\n            ...call,\n            globalNote: globalNote || null // 📝 Attach the global note (if found)\n          };\n        });\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching tasks', error);\n        this.loading = false;\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 15\n    };\n    this.loadTasks(dt1State);\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  getStateNameByCode(stateCode, countryCode) {\n    const states = State.getStatesOfCountry(countryCode);\n    const match = states.find(state => state.isoCode === stateCode);\n    return match ? match.name : '-';\n  }\n  signup() {\n    this.router.navigate(['/store/activities/tasks/create']);\n  }\n  onGlobalFilter(table, event) {\n    this.loadTasks({\n      first: 0,\n      rows: 14\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function TaskComponent_Factory(t) {\n      return new (t || TaskComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskComponent,\n      selectors: [[\"app-tasks\"]],\n      viewQuery: function TaskComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 23,\n      vars: 18,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Task\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", \"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [1, \"text-blue-600\", \"font-medium\", \"underline\", \"cursor-pointer\", 3, \"routerLink\"], [\"tooltipPosition\", \"top\", \"tooltipStyleClass\", \"multi-line-tooltip\", 3, \"pTooltip\"], [\"colspan\", \"14\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function TaskComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function TaskComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(18);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function TaskComponent_Template_p_dropdown_onChange_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function TaskComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.signup());\n          });\n          i0.ɵɵelementStart(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-multiSelect\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskComponent_Template_p_multiSelect_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 15)(17, \"p-table\", 16, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function TaskComponent_Template_p_table_onLazyLoad_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadTasks($event));\n          })(\"onColReorder\", function TaskComponent_Template_p_table_onColReorder_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(19, TaskComponent_ng_template_19_Template, 9, 3, \"ng-template\", 17)(20, TaskComponent_ng_template_20_Template, 6, 4, \"ng-template\", 18)(21, TaskComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19)(22, TaskComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.tasks)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i2.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.Tooltip, i10.MultiSelect, i3.SlicePipe, i3.DatePipe],\n      styles: [\".custom-sort-icon {\\n  font-size: 0.85rem;\\n  color: #888;\\n  transition: transform 0.2s ease;\\n}\\n  th:hover .custom-sort-icon {\\n  color: #000;\\n}\\n  .multi-line-tooltip {\\n  white-space: normal !important;\\n  max-width: 300px;\\n  word-wrap: break-word;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy90YXNrL3Rhc2suY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDSSxrQkFBQTtFQUNBLFdBQUE7RUFDQSwrQkFBQTtBQUFSO0FBR0k7RUFDSSxXQUFBO0FBRFI7QUFJSTtFQUNJLDhCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtBQUZSIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIHtcclxuICAgIC5jdXN0b20tc29ydC1pY29uIHtcclxuICAgICAgICBmb250LXNpemU6IDAuODVyZW07XHJcbiAgICAgICAgY29sb3I6ICM4ODg7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuICAgIH1cclxuXHJcbiAgICB0aDpob3ZlciAuY3VzdG9tLXNvcnQtaWNvbiB7XHJcbiAgICAgICAgY29sb3I6ICMwMDA7XHJcbiAgICB9XHJcblxyXG4gICAgLm11bHRpLWxpbmUtdG9vbHRpcCB7XHJcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vcm1hbCAhaW1wb3J0YW50O1xyXG4gICAgICAgIG1heC13aWR0aDogMzAwcHg7XHJcbiAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkO1xyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "State", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r3", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "TaskComponent_ng_template_19_ng_container_8_Template_th_click_1_listener", "col_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "TaskComponent_ng_template_19_ng_container_8_i_4_Template", "TaskComponent_ng_template_19_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "TaskComponent_ng_template_19_Template_th_click_3_listener", "_r3", "TaskComponent_ng_template_19_i_6_Template", "TaskComponent_ng_template_19_i_7_Template", "TaskComponent_ng_template_19_ng_container_8_Template", "selectedColumns", "task_r7", "activity_id", "subject", "business_partner", "bp_full_name", "business_partner_contact", "getLabelFromDropdown", "activity_status", "ɵɵpropertyInterpolate", "stripHtml", "globalNote", "note", "ɵɵpipeBind3", "length", "start_date", "ɵɵpipeBind2", "end_date", "priority", "business_partner_processor", "task_category", "business_partner_owner", "col_r8", "TaskComponent_ng_template_20_ng_container_5_ng_container_3_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_4_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_5_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_6_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_7_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_8_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_9_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_10_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_11_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_12_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_13_Template", "TaskComponent_ng_template_20_ng_container_5_ng_container_14_Template", "TaskComponent_ng_template_20_ng_container_5_Template", "main_account_party_id", "TaskComponent", "constructor", "activitiesservice", "router", "unsubscribe$", "tasks", "totalRecords", "loading", "globalSearchTerm", "dropdowns", "activityCategory", "activityStatus", "activityPriority", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "loadActivityDropDown", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "target", "type", "getActivityDropdownOptions", "subscribe", "res", "map", "attr", "description", "value", "dropdownKey", "item", "find", "opt", "loadTasks", "page", "first", "rows", "pageSize", "selectedActions", "getTasks", "next", "response", "call", "notes", "n", "is_global_note", "meta", "pagination", "total", "error", "console", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "getStateNameByCode", "stateCode", "countryCode", "states", "getStatesOfCountry", "match", "state", "isoCode", "signup", "navigate", "onGlobalFilter", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "Router", "selectors", "viewQuery", "TaskComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "TaskComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "TaskComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "TaskComponent_Template_p_dropdown_ngModelChange_10_listener", "TaskComponent_Template_p_dropdown_onChange_10_listener", "TaskComponent_Template_button_click_11_listener", "TaskComponent_Template_p_multiSelect_ngModelChange_15_listener", "TaskComponent_Template_p_table_onLazyLoad_17_listener", "TaskComponent_Template_p_table_onColReorder_17_listener", "TaskComponent_ng_template_19_Template", "TaskComponent_ng_template_20_Template", "TaskComponent_ng_template_21_Template", "TaskComponent_ng_template_22_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject } from 'rxjs';\r\nimport { ActivitiesService } from '../activities.service';\r\nimport { Table } from 'primeng/table';\r\nimport { Router } from '@angular/router';\r\nimport { State } from 'country-state-city';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-tasks',\r\n  templateUrl: './task.component.html',\r\n  styleUrl: './task.component.scss',\r\n})\r\nexport class TaskComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('dt1') dt1!: Table;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public tasks: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activityPriority: [],\r\n  };\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'subject', header: 'Subject' },\r\n    { field: 'business_partner.bp_full_name', header: 'Account' },\r\n    {\r\n      field: 'business_partner_contact.bp_full_name',\r\n      header: 'Primary Contact',\r\n    },\r\n    { field: 'activity_status', header: 'Status' },\r\n    { field: 'notes.note', header: 'Notes' },\r\n    { field: 'start_date', header: 'Start Date/Time' },\r\n    { field: 'end_date', header: 'Due Date/Time' },\r\n    { field: 'priority', header: 'Priority' },\r\n    { field: 'business_partner_processor.bp_full_name', header: 'Processor' },\r\n    { field: 'task_category', header: 'Category' },\r\n    { field: 'business_partner_owner.bp_full_name', header: 'Owner' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.tasks.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\r\n    this.breadcrumbitems = [\r\n      { label: 'Tasks', routerLink: ['/store/activities/tasks'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Tasks', code: 'MT' },\r\n      { name: 'My Open Tasks', code: 'MOT' },\r\n      { name: 'My Completed Tasks', code: 'MCT' },\r\n      { name: 'My Team`s Tasks', code: 'MTST' },\r\n      { name: 'My Tasks Today', code: 'MTT' },\r\n      { name: 'My Tasks This Week', code: 'MTTW' },\r\n      { name: 'My Tasks This Month', code: 'MTTM' },\r\n    ];\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    // optional: preserve user reorder logic instead\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  loadTasks(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    const filter = this.selectedActions?.code;\r\n\r\n    this.activitiesservice\r\n      .getTasks(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm,\r\n        filter\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.tasks = (response?.data || []).map((call: any) => {\r\n            // 🔍 Find the global note from the call.notes array\r\n            const globalNote = call.notes?.find(\r\n              (n: any) => n.is_global_note === true\r\n            );\r\n            return {\r\n              ...call,\r\n              globalNote: globalNote || null, // 📝 Attach the global note (if found)\r\n            };\r\n          });\r\n\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching tasks', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 15,\r\n    };\r\n    this.loadTasks(dt1State);\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  getStateNameByCode(stateCode: string, countryCode: string): string {\r\n    const states = State.getStatesOfCountry(countryCode);\r\n    const match = states.find((state) => state.isoCode === stateCode);\r\n    return match ? match.name : '-';\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/activities/tasks/create']);\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadTasks({ first: 0, rows: 14 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        placeholder=\"Search Task\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange()\"\r\n                optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n            <button type=\"button\" type=\"button\" (click)=\"signup()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"tasks\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadTasks($event)\" [loading]=\"loading\"\r\n            [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\" [scrollable]=\"true\"\r\n            class=\"scrollable-table\" [reorderableColumns]=\"true\" (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('main_account_party_id')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Account ID\r\n                            <i *ngIf=\"sortField === 'main_account_party_id'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'main_account_party_id'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-task let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"task\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline\"\r\n                        [routerLink]=\"'/store/activities/tasks/' + task.activity_id\">\r\n                        {{ task?.main_account_party_id || '-'}}\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <!-- Use ngSwitch or conditional logic to handle special fields -->\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'subject'\">\r\n                                    <span class=\"text-blue-600 font-medium underline cursor-pointer\"\r\n                                        [routerLink]=\"'/store/activities/tasks/' + task.activity_id\">\r\n                                        {{ task?.subject || '-' }}\r\n                                    </span>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner.bp_full_name'\">\r\n                                    {{ task?.business_partner?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_contact.bp_full_name'\">\r\n                                    {{ task?.business_partner_contact?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'activity_status'\">\r\n                                    {{ getLabelFromDropdown('activityStatus', task?.activity_status) || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'notes.note'\">\r\n                                    <span pTooltip=\"{{ stripHtml(task?.globalNote?.note) }}\" tooltipPosition=\"top\"\r\n                                        tooltipStyleClass=\"multi-line-tooltip\">\r\n                                        {{\r\n                                        task?.globalNote?.note\r\n                                        ? (stripHtml(task.globalNote.note) | slice: 0:80) + (task.globalNote.note.length\r\n                                        > 80 ? '...' : '')\r\n                                        : '-'\r\n                                        }}\r\n                                    </span>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'start_date'\">\r\n                                    {{ task?.start_date ? (task?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'end_date'\">\r\n                                    {{ task?.end_date ? (task?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'priority'\">\r\n                                    {{ getLabelFromDropdown('activityPriority', task?.priority) || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_processor.bp_full_name'\">\r\n                                    {{ task?.business_partner_processor?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'task_category'\">\r\n                                    {{ getLabelFromDropdown('activityCategory', task?.task_category) || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_owner.bp_full_name'\">\r\n                                    {{ task?.business_partner_owner?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchDefault>\r\n                                    {{ task[col.field] || '-' }}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"14\" class=\"border-round-left-lg pl-3\">No tasks found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"14\" class=\"border-round-left-lg pl-3\">Loading tasks data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;AAI9B,SAASC,KAAK,QAAQ,oBAAoB;;;;;;;;;;;;;;;ICqCdC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA6E;;;;;IAOzED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,yEAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,wDAAA,gBACkF,IAAAC,wDAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAgE;IAA9CN,EAAA,CAAAO,UAAA,mBAAAmB,0DAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,uBAAuB,CAAC;IAAA,EAAC;IAC3Df,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,mBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,yCAAA,gBACkF,IAAAC,yCAAA,gBAET;IAEjF7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,oDAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAjBWrB,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,6BAA2C;IAG3CzB,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,6BAA2C;IAGzBzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAK,uBAAA,GAAwC;IACpCL,EAAA,CAAAM,cAAA,eACiE;IAC7DN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;;IAFHrB,EAAA,CAAAsB,SAAA,EAA4D;IAA5DtB,EAAA,CAAAE,UAAA,4CAAA8B,OAAA,CAAAC,WAAA,CAA4D;IAC5DjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAE,OAAA,cACJ;;;;;IAGJlC,EAAA,CAAAK,uBAAA,GAA8D;IAC1DL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAG,gBAAA,kBAAAH,OAAA,CAAAG,gBAAA,CAAAC,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAAsE;IAClEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAK,wBAAA,kBAAAL,OAAA,CAAAK,wBAAA,CAAAD,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAmC,oBAAA,mBAAAN,OAAA,kBAAAA,OAAA,CAAAO,eAAA,cACJ;;;;;IAEAvC,EAAA,CAAAK,uBAAA,GAA2C;IACvCL,EAAA,CAAAM,cAAA,eAC2C;IACvCN,EAAA,CAAAiB,MAAA,GAMJ;;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAAkD;IAAlDtB,EAAA,CAAAwC,qBAAA,aAAArC,MAAA,CAAAsC,SAAA,CAAAT,OAAA,kBAAAA,OAAA,CAAAU,UAAA,kBAAAV,OAAA,CAAAU,UAAA,CAAAC,IAAA,EAAkD;IAEpD3C,EAAA,CAAAsB,SAAA,EAMJ;IANItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAU,UAAA,kBAAAV,OAAA,CAAAU,UAAA,CAAAC,IAAA,IAAA3C,EAAA,CAAA4C,WAAA,OAAAzC,MAAA,CAAAsC,SAAA,CAAAT,OAAA,CAAAU,UAAA,CAAAC,IAAA,aAAAX,OAAA,CAAAU,UAAA,CAAAC,IAAA,CAAAE,MAAA,+BAMJ;;;;;IAGJ7C,EAAA,CAAAK,uBAAA,GAA2C;IACvCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAc,UAAA,IAAA9C,EAAA,CAAA+C,WAAA,OAAAf,OAAA,kBAAAA,OAAA,CAAAc,UAAA,mCACJ;;;;;IAEA9C,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAgB,QAAA,IAAAhD,EAAA,CAAA+C,WAAA,OAAAf,OAAA,kBAAAA,OAAA,CAAAgB,QAAA,mCACJ;;;;;IAEAhD,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAmC,oBAAA,qBAAAN,OAAA,kBAAAA,OAAA,CAAAiB,QAAA,cACJ;;;;;IAEAjD,EAAA,CAAAK,uBAAA,GAAwE;IACpEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAkB,0BAAA,kBAAAlB,OAAA,CAAAkB,0BAAA,CAAAd,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAmC,oBAAA,qBAAAN,OAAA,kBAAAA,OAAA,CAAAmB,aAAA,cACJ;;;;;IAEAnD,EAAA,CAAAK,uBAAA,GAAoE;IAChEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAoB,sBAAA,kBAAApB,OAAA,CAAAoB,sBAAA,CAAAhB,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA+B;IAC3BL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,OAAA,CAAAqB,MAAA,CAAArC,KAAA,cACJ;;;;;IA9DZhB,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IAEAN,EAAA,CAAAK,uBAAA,OAAqC;IAyDjCL,EAvDA,CAAAkB,UAAA,IAAAoC,mEAAA,2BAAwC,IAAAC,mEAAA,2BAOsB,IAAAC,mEAAA,2BAIQ,IAAAC,mEAAA,2BAItB,IAAAC,mEAAA,2BAIL,IAAAC,mEAAA,2BAYA,IAAAC,mEAAA,2BAIF,KAAAC,oEAAA,2BAIA,KAAAC,oEAAA,2BAI+B,KAAAC,oEAAA,2BAI1B,KAAAC,oEAAA,2BAIsB,KAAAC,oEAAA,2BAIrC;;IAIvCjE,EAAA,CAAAqB,YAAA,EAAK;;;;;IA7DarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAmD,MAAA,CAAArC,KAAA,CAAsB;IAEjBhB,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,2BAAuB;IAOvBF,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAsB,SAAA,EAAqD;IAArDtB,EAAA,CAAAE,UAAA,yDAAqD;IAIrDF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAI/BF,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,8BAA0B;IAY1BF,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,8BAA0B;IAI1BF,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAIxBF,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAIxBF,EAAA,CAAAsB,SAAA,EAAuD;IAAvDtB,EAAA,CAAAE,UAAA,2DAAuD;IAIvDF,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,iCAA6B;IAI7BF,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,uDAAmD;;;;;IA/D9EF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAkC;IACtCD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aACiE;IAC7DN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAkB,UAAA,IAAAgD,oDAAA,6BAAkD;IAkEtDlE,EAAA,CAAAqB,YAAA,EAAK;;;;;IAxEoBrB,EAAA,CAAAsB,SAAA,GAAc;IAAdtB,EAAA,CAAAE,UAAA,UAAA8B,OAAA,CAAc;IAG/BhC,EAAA,CAAAsB,SAAA,EAA4D;IAA5DtB,EAAA,CAAAE,UAAA,4CAAA8B,OAAA,CAAAC,WAAA,CAA4D;IAC5DjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAmC,qBAAA,cACJ;IAC8BnE,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAuEhD/B,EADJ,CAAAM,cAAA,SAAI,aACmD;IAAAN,EAAA,CAAAiB,MAAA,sBAAe;IACtEjB,EADsE,CAAAqB,YAAA,EAAK,EACtE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACmD;IAAAN,EAAA,CAAAiB,MAAA,uCAAgC;IACvFjB,EADuF,CAAAqB,YAAA,EAAK,EACvF;;;AD/HrB,OAAM,MAAO+C,aAAa;EAkBxBC,YACUC,iBAAoC,EACpCC,MAAc;IADd,KAAAD,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IAnBR,KAAAC,YAAY,GAAG,IAAI1E,OAAO,EAAQ;IAInC,KAAA2E,KAAK,GAAU,EAAE;IACjB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAI7B,KAAAC,SAAS,GAA0B;MACxCC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE;KACnB;IAOO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAElE,KAAK,EAAE,SAAS;MAAEQ,MAAM,EAAE;IAAS,CAAE,EACvC;MAAER,KAAK,EAAE,+BAA+B;MAAEQ,MAAM,EAAE;IAAS,CAAE,EAC7D;MACER,KAAK,EAAE,uCAAuC;MAC9CQ,MAAM,EAAE;KACT,EACD;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EAC9C;MAAER,KAAK,EAAE,YAAY;MAAEQ,MAAM,EAAE;IAAO,CAAE,EACxC;MAAER,KAAK,EAAE,YAAY;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EAClD;MAAER,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAe,CAAE,EAC9C;MAAER,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAU,CAAE,EACzC;MAAER,KAAK,EAAE,yCAAyC;MAAEQ,MAAM,EAAE;IAAW,CAAE,EACzE;MAAER,KAAK,EAAE,eAAe;MAAEQ,MAAM,EAAE;IAAU,CAAE,EAC9C;MAAER,KAAK,EAAE,qCAAqC;MAAEQ,MAAM,EAAE;IAAO,CAAE,CAClE;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAtBlB;EAwBHW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACqE,KAAK,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACvB,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEpE,KAAK,CAAC;MAC9C,MAAMwE,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAErE,KAAK,CAAC;MAE9C,IAAIyE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAACpF,SAAS,GAAGqF,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE3E,KAAa;IACvC,IAAI,CAAC2E,IAAI,IAAI,CAAC3E,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC4E,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC3E,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,CAAC,kBAAkB,EAAE,4BAA4B,CAAC;IAC3E,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CAAC,kBAAkB,EAAE,uBAAuB,CAAC;IACtE,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,OAAO;MAAEC,UAAU,EAAE,CAAC,yBAAyB;IAAC,CAAE,CAC5D;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAK,CAAE,EACtC;MAAED,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC3C;MAAED,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAM,CAAE,EACzC;MAAED,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAK,CAAE,EACvC;MAAED,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAM,CAAE,EAC5C;MAAED,IAAI,EAAE,qBAAqB;MAAEC,IAAI,EAAE;IAAM,CAAE,CAC9C;IAED,IAAI,CAACzB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAInD,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACkD,gBAAgB;EAC9B;EAEA,IAAIlD,eAAeA,CAAC4E,GAAU;IAC5B;IACA,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC0B,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAChC,gBAAgB,CAAC+B,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACjC,gBAAgB,CAACkC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACjC,gBAAgB,CAACkC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAf,oBAAoBA,CAACmB,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAChD,iBAAiB,CACnBiD,0BAA0B,CAACD,IAAI,CAAC,CAChCE,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAAC5C,SAAS,CAACwC,MAAM,CAAC,GACpBI,GAAG,EAAE9B,IAAI,EAAE+B,GAAG,CAAEC,IAAS,KAAM;QAC7BvB,KAAK,EAAEuB,IAAI,CAACC,WAAW;QACvBC,KAAK,EAAEF,IAAI,CAACjB;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEApE,oBAAoBA,CAACwF,WAAmB,EAAED,KAAa;IACrD,MAAME,IAAI,GAAG,IAAI,CAAClD,SAAS,CAACiD,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACJ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOE,IAAI,EAAE3B,KAAK,IAAIyB,KAAK;EAC7B;EAEAK,SAASA,CAAClB,KAAU;IAClB,IAAI,CAACrC,OAAO,GAAG,IAAI;IACnB,MAAMwD,IAAI,GAAGnB,KAAK,CAACoB,KAAK,GAAGpB,KAAK,CAACqB,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGtB,KAAK,CAACqB,IAAI;IAC3B,MAAM5G,SAAS,GAAGuF,KAAK,CAACvF,SAAS;IACjC,MAAMrB,SAAS,GAAG4G,KAAK,CAAC5G,SAAS;IACjC,MAAMwG,MAAM,GAAG,IAAI,CAAC2B,eAAe,EAAE7B,IAAI;IAEzC,IAAI,CAACpC,iBAAiB,CACnBkE,QAAQ,CACPL,IAAI,EACJG,QAAQ,EACR7G,SAAS,EACTrB,SAAS,EACT,IAAI,CAACwE,gBAAgB,EACrBgC,MAAM,CACP,CACAY,SAAS,CAAC;MACTiB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACjE,KAAK,GAAG,CAACiE,QAAQ,EAAE/C,IAAI,IAAI,EAAE,EAAE+B,GAAG,CAAEiB,IAAS,IAAI;UACpD;UACA,MAAMjG,UAAU,GAAGiG,IAAI,CAACC,KAAK,EAAEZ,IAAI,CAChCa,CAAM,IAAKA,CAAC,CAACC,cAAc,KAAK,IAAI,CACtC;UACD,OAAO;YACL,GAAGH,IAAI;YACPjG,UAAU,EAAEA,UAAU,IAAI,IAAI,CAAE;WACjC;QACH,CAAC,CAAC;QAEF,IAAI,CAACgC,YAAY,GAAGgE,QAAQ,EAAEK,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAACtE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDuE,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACvE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAyE,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvDnB,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACH,SAAS,CAACmB,QAAQ,CAAC;EAC1B;EAEA5G,SAASA,CAAC+G,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAC,kBAAkBA,CAACC,SAAiB,EAAEC,WAAmB;IACvD,MAAMC,MAAM,GAAGnK,KAAK,CAACoK,kBAAkB,CAACF,WAAW,CAAC;IACpD,MAAMG,KAAK,GAAGF,MAAM,CAAClC,IAAI,CAAEqC,KAAK,IAAKA,KAAK,CAACC,OAAO,KAAKN,SAAS,CAAC;IACjE,OAAOI,KAAK,GAAGA,KAAK,CAAC3D,IAAI,GAAG,GAAG;EACjC;EAEA8D,MAAMA,CAAA;IACJ,IAAI,CAAChG,MAAM,CAACiG,QAAQ,CAAC,CAAC,gCAAgC,CAAC,CAAC;EAC1D;EAEAC,cAAcA,CAACC,KAAY,EAAE1D,KAAY;IACvC,IAAI,CAACkB,SAAS,CAAC;MAAEE,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EACxC;EAEAsC,WAAWA,CAAA;IACT,IAAI,CAACnG,YAAY,CAACiE,IAAI,EAAE;IACxB,IAAI,CAACjE,YAAY,CAACoG,QAAQ,EAAE;EAC9B;;;uBAhNWxG,aAAa,EAAApE,EAAA,CAAA6K,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA/K,EAAA,CAAA6K,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAb7G,aAAa;MAAA8G,SAAA;MAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCpBlBrL,EAFR,CAAAM,cAAA,aAA8D,aACmB,aAC7C;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAM,cAAA,aAA2C,aAEb,cACW,kBAG8E;UAFhFN,EAAA,CAAAuL,gBAAA,2BAAAC,sDAAAC,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA1L,EAAA,CAAA2L,kBAAA,CAAAL,GAAA,CAAA1G,gBAAA,EAAA6G,MAAA,MAAAH,GAAA,CAAA1G,gBAAA,GAAA6G,MAAA;YAAA,OAAAzL,EAAA,CAAAc,WAAA,CAAA2K,MAAA;UAAA,EAA8B;UAACzL,EAAA,CAAAO,UAAA,mBAAAqL,8CAAAH,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA,MAAAG,MAAA,GAAA7L,EAAA,CAAA8L,WAAA;YAAA,OAAA9L,EAAA,CAAAc,WAAA,CAASwK,GAAA,CAAAb,cAAA,CAAAoB,MAAA,EAAAJ,MAAA,CAA2B;UAAA,EAAC;UAA/FzL,EAAA,CAAAqB,YAAA,EAE2G;UAC3GrB,EAAA,CAAAC,SAAA,YAAiD;UAEzDD,EADI,CAAAqB,YAAA,EAAO,EACL;UACNrB,EAAA,CAAAM,cAAA,sBAEyG;UAFzEN,EAAA,CAAAuL,gBAAA,2BAAAQ,4DAAAN,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA1L,EAAA,CAAA2L,kBAAA,CAAAL,GAAA,CAAA/C,eAAA,EAAAkD,MAAA,MAAAH,GAAA,CAAA/C,eAAA,GAAAkD,MAAA;YAAA,OAAAzL,EAAA,CAAAc,WAAA,CAAA2K,MAAA;UAAA,EAA6B;UAACzL,EAAA,CAAAO,UAAA,sBAAAyL,uDAAA;YAAAhM,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA,OAAA1L,EAAA,CAAAc,WAAA,CAAYwK,GAAA,CAAAlC,cAAA,EAAgB;UAAA,EAAC;UAA3FpJ,EAAA,CAAAqB,YAAA,EAEyG;UACzGrB,EAAA,CAAAM,cAAA,kBAC0I;UADtGN,EAAA,CAAAO,UAAA,mBAAA0L,gDAAA;YAAAjM,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA,OAAA1L,EAAA,CAAAc,WAAA,CAASwK,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAElDvK,EAAA,CAAAM,cAAA,gBAAgD;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAACrB,EAAA,CAAAiB,MAAA,gBACpE;UAAAjB,EAAA,CAAAqB,YAAA,EAAS;UAETrB,EAAA,CAAAM,cAAA,yBAE+I;UAF/GN,EAAA,CAAAuL,gBAAA,2BAAAW,+DAAAT,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA1L,EAAA,CAAA2L,kBAAA,CAAAL,GAAA,CAAAvJ,eAAA,EAAA0J,MAAA,MAAAH,GAAA,CAAAvJ,eAAA,GAAA0J,MAAA;YAAA,OAAAzL,EAAA,CAAAc,WAAA,CAAA2K,MAAA;UAAA,EAA6B;UAKrEzL,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,eAAuB,sBAG+E;UAAzCN,EAFF,CAAAO,UAAA,wBAAA4L,sDAAAV,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA,OAAA1L,EAAA,CAAAc,WAAA,CAAcwK,GAAA,CAAApD,SAAA,CAAAuD,MAAA,CAAiB;UAAA,EAAC,0BAAAW,wDAAAX,MAAA;YAAAzL,EAAA,CAAAU,aAAA,CAAAgL,GAAA;YAAA,OAAA1L,EAAA,CAAAc,WAAA,CAEdwK,GAAA,CAAAvE,eAAA,CAAA0E,MAAA,CAAuB;UAAA,EAAC;UAiH7FzL,EA/GA,CAAAkB,UAAA,KAAAmL,qCAAA,0BAAgC,KAAAC,qCAAA,0BA4B6B,KAAAC,qCAAA,0BA8EvB,KAAAC,qCAAA,0BAKD;UAQjDxM,EAHQ,CAAAqB,YAAA,EAAU,EACR,EAEJ;;;UAvJoBrB,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAAoL,GAAA,CAAAnF,eAAA,CAAyB,SAAAmF,GAAA,CAAAhF,IAAA,CAAc,uCAAuC;UAMzDtG,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAyM,gBAAA,YAAAnB,GAAA,CAAA1G,gBAAA,CAA8B;UAMrD5E,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAE,UAAA,YAAAoL,GAAA,CAAA9E,OAAA,CAAmB;UAACxG,EAAA,CAAAyM,gBAAA,YAAAnB,GAAA,CAAA/C,eAAA,CAA6B;UAEzDvI,EAAA,CAAAE,UAAA,mGAAkG;UAMvFF,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAoL,GAAA,CAAApG,IAAA,CAAgB;UAAClF,EAAA,CAAAyM,gBAAA,YAAAnB,GAAA,CAAAvJ,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMpIF,EAAA,CAAAsB,SAAA,GAAe;UAEAtB,EAFf,CAAAE,UAAA,UAAAoL,GAAA,CAAA7G,KAAA,CAAe,YAAyB,YAAA6G,GAAA,CAAA3G,OAAA,CAAqD,mBACrF,iBAAA2G,GAAA,CAAA5G,YAAA,CAA8B,cAAc,oBAA8C,4BACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}