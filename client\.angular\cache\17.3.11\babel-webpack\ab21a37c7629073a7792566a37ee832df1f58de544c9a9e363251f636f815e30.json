{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./sales-quotes.service\";\nimport * as i3 from \"../services/setting.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/breadcrumb\";\nimport * as i11 from \"primeng/calendar\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/paginator\";\nimport * as i14 from \"primeng/progressspinner\";\nimport * as i15 from \"primeng/multiselect\";\nconst _c0 = a0 => [a0];\nfunction SalesQuotesComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 35);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 35);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 36);\n    i0.ɵɵlistener(\"click\", function SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_4_Template, 1, 1, \"i\", 31)(5, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_i_5_Template, 1, 0, \"i\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function SalesQuotesComponent_p_table_41_ng_template_1_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"SD_DOC\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5, \" Quote # \");\n    i0.ɵɵtemplate(6, SalesQuotesComponent_p_table_41_ng_template_1_i_6_Template, 1, 1, \"i\", 31)(7, SalesQuotesComponent_p_table_41_ng_template_1_i_7_Template, 1, 0, \"i\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SalesQuotesComponent_p_table_41_ng_template_1_ng_container_8_Template, 6, 4, \"ng-container\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_NAME, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_STATUS, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_DATE, \" \");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 41);\n    i0.ɵɵtemplate(3, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 42)(4, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 42)(5, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 42);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_NAME\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_STATUS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_DATE\");\n  }\n}\nfunction SalesQuotesComponent_p_table_41_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 37)(1, \"td\", 38);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SalesQuotesComponent_p_table_41_ng_template_2_ng_container_5_Template, 6, 4, \"ng-container\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", tableinfo_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, \"/store/sales-quotes/\" + tableinfo_r6.SD_DOC));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.SD_DOC, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesQuotesComponent_p_table_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 25);\n    i0.ɵɵlistener(\"onColReorder\", function SalesQuotesComponent_p_table_41_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(1, SalesQuotesComponent_p_table_41_ng_template_1_Template, 9, 3, \"ng-template\", 26)(2, SalesQuotesComponent_p_table_41_ng_template_2_Template, 6, 6, \"ng-template\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.tableData)(\"rows\", 14)(\"totalRecords\", ctx_r1.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction SalesQuotesComponent_p_paginator_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 43);\n    i0.ɵɵlistener(\"onPageChange\", function SalesQuotesComponent_p_paginator_42_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"first\", ctx_r1.first)(\"rows\", ctx_r1.rows)(\"totalRecords\", ctx_r1.totalRecords);\n  }\n}\nexport let SalesQuotesComponent = /*#__PURE__*/(() => {\n  class SalesQuotesComponent {\n    constructor(fb, SalesQuotesService, settingManager) {\n      this.fb = fb;\n      this.SalesQuotesService = SalesQuotesService;\n      this.settingManager = settingManager;\n      this.unsubscribe$ = new Subject();\n      this.items = [];\n      this.home = {};\n      this.allData = [];\n      this.tableData = [];\n      this.totalRecords = 1000;\n      this.loading = false;\n      this.first = 0;\n      this.rows = 10;\n      this.channels = ['Web Order', 'S4 Order'];\n      this.QuoteStatus = ['All'];\n      this.orderStatusesValue = {};\n      this.orderValue = {};\n      this.orderType = '';\n      this.currentPage = 1;\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'DOC_NAME',\n        header: 'Quote Name'\n      }, {\n        field: 'DOC_STATUS',\n        header: 'Quote Status'\n      }, {\n        field: 'DOC_DATE',\n        header: 'Date Placed'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n      this.filterForm = this.fb.group({\n        dateFrom: [''],\n        dateTo: [''],\n        QuoteStatus: ['All'],\n        Quote: ['']\n      });\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.tableData.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.items = [{\n        label: 'Sales Quotes',\n        routerLink: ['/store/sales-quotes']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.loading = true;\n      this.SalesQuotesService.fetchOrderStatuses({\n        'filters[type][$eq]': 'QUOTE_STATUS'\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.data.length) {\n            this.QuoteStatus = response?.data.map(val => {\n              this.orderStatusesValue[val.description] = val.code;\n              this.orderValue[val.code] = val.description;\n              this.orderStatusesValue['All'] = this.orderStatusesValue['All'] ? `${this.orderStatusesValue['All']};${val.code}` : val.code;\n              return val.description;\n            });\n            this.QuoteStatus = ['All', ...this.QuoteStatus];\n          }\n        },\n        error: error => {\n          console.error('Error fetching avatars:', error);\n        }\n      });\n      this.settingManager.getSettings().pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          console.log('f-f-f-f-', response, response?.data);\n          if (response && response[0]) {\n            this.orderType = response[0].sales_quote_type_code;\n          }\n          this.onPageChange({\n            first: this.first,\n            rows: this.rows\n          });\n        },\n        error: error => {\n          this.onPageChange({\n            first: this.first,\n            rows: this.rows\n          });\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    fetchOrders(count) {\n      this.loading = true;\n      const filterValues = this.filterForm.value;\n      const rawParams = {\n        // SOLDTO: this.sellerDetails.customer_id,\n        // SOLDTO: '00830VGB',\n        // VKORG: 1000,\n        COUNT: count,\n        SD_DOC: filterValues.Quote,\n        DOCUMENT_DATE: filterValues.dateFrom ? new Date(filterValues.dateFrom).toISOString().slice(0, 10) : '',\n        DOCUMENT_DATE_TO: filterValues.dateTo ? new Date(filterValues.dateTo).toISOString().slice(0, 10) : '',\n        DOC_STATUS: this.orderStatusesValue[filterValues.QuoteStatus] || 'A;C',\n        DOC_TYPE: this.orderType\n      };\n      // Remove empty or undefined values from params\n      const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n      this.SalesQuotesService.fetchSalesquoteOrders(params).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.SALESQUOTES) {\n            this.tableData = response.SALESQUOTES.map(record => ({\n              SD_DOC: record?.SD_DOC || '-',\n              DOC_NAME: record?.DOC_NAME || '-',\n              DOC_TYPE: record?.DOC_TYPE || '-',\n              // DOC_STATUS: record?.DOC_STATUS || '-',\n              DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n              DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-'\n            }));\n            const newRecords = response.SALESQUOTES.length;\n            const totalFetched = this.allData.length + newRecords;\n            const skipCount = totalFetched - newRecords;\n            this.allData.push(...this.tableData.slice(skipCount));\n            this.totalRecords = this.allData.length;\n            this.paginateData();\n          } else {\n            this.allData = [];\n            this.totalRecords = 0;\n            this.paginateData();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching avatars:', error);\n          this.loading = false;\n        }\n      });\n    }\n    onPageChange(event) {\n      this.first = event.first;\n      this.rows = event.rows;\n      this.currentPage = this.first / this.rows + 1;\n      if (this.first + this.rows >= this.allData.length && this.allData.length % 100 == 0) {\n        this.fetchOrders(this.allData.length + 1000);\n      }\n      this.paginateData();\n    }\n    paginateData() {\n      this.tableData = this.allData.slice(this.first, this.first + this.rows);\n    }\n    onSearch() {\n      this.allData = [];\n      this.totalRecords = 1000;\n      this.fetchOrders(this.totalRecords);\n    }\n    onClear() {\n      this.allData = [];\n      this.totalRecords = 1000;\n      this.filterForm.reset({\n        dateFrom: '',\n        dateTo: '',\n        QuoteStatus: 'All',\n        Quote: ''\n      });\n      this.fetchOrders(this.totalRecords);\n    }\n    createQuote() {\n      const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2`;\n      window.open(url, '_blank');\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesQuotesComponent_Factory(t) {\n        return new (t || SalesQuotesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SalesQuotesService), i0.ɵɵdirectiveInject(i3.SettingsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesQuotesComponent,\n        selectors: [[\"app-sales-quotes\"]],\n        decls: 43,\n        vars: 14,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\", 3, \"ngSubmit\", \"formGroup\"], [1, \"filter-sec\", \"mb-5\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"formControlName\", \"dateFrom\", \"placeholder\", \"Date From\", \"styleClass\", \"h-3rem w-full\", 3, \"showIcon\"], [\"formControlName\", \"dateTo\", \"placeholder\", \"Date To\", \"styleClass\", \"h-3rem w-full\", 3, \"showIcon\"], [\"formControlName\", \"QuoteStatus\", \"placeholder\", \"Quote Status\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"formControlName\", \"Quote\", \"placeholder\", \"Quote #\", 1, \"p-inputtext\", \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Clear\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Search\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\", \"onColReorder\", 4, \"ngIf\"], [3, \"first\", \"rows\", \"totalRecords\", \"onPageChange\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"onPageChange\", \"first\", \"rows\", \"totalRecords\"]],\n        template: function SalesQuotesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function SalesQuotesComponent_Template_button_click_5_listener() {\n              return ctx.createQuote();\n            });\n            i0.ɵɵelementStart(6, \"span\", 6);\n            i0.ɵɵtext(7, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(8, \" Create \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p-multiSelect\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesQuotesComponent_Template_p_multiSelect_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function SalesQuotesComponent_Template_form_ngSubmit_10_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11)(14, \"span\", 12);\n            i0.ɵɵtext(15, \"calendar_month\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(16, \" Date From \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"p-calendar\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 10)(19, \"label\", 11)(20, \"span\", 12);\n            i0.ɵɵtext(21, \"calendar_month\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(22, \" Date To \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(23, \"p-calendar\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 10)(25, \"label\", 11)(26, \"span\", 12);\n            i0.ɵɵtext(27, \"price_change\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(28, \" Quote Status \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"p-dropdown\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 10)(31, \"label\", 11)(32, \"span\", 12);\n            i0.ɵɵtext(33, \"request_quote\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" Quote # \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SalesQuotesComponent_Template_button_click_37_listener() {\n              return ctx.onClear();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"button\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 20);\n            i0.ɵɵtemplate(40, SalesQuotesComponent_div_40_Template, 2, 0, \"div\", 21)(41, SalesQuotesComponent_p_table_41_Template, 3, 6, \"p-table\", 22)(42, SalesQuotesComponent_p_paginator_42_Template, 1, 3, \"p-paginator\", 23);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showIcon\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showIcon\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.QuoteStatus)(\"styleClass\", \"h-3rem w-full flex align-items-center\");\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i6.Table, i7.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i6.TableCheckbox, i6.TableHeaderCheckbox, i8.ButtonDirective, i9.Dropdown, i10.Breadcrumb, i11.Calendar, i12.InputText, i13.Paginator, i1.FormGroupDirective, i1.FormControlName, i14.ProgressSpinner, i15.MultiSelect],\n        styles: [\".filter-sec[_ngcontent-%COMP%]{width:100%!important;display:flex;align-items:center;justify-content:space-between;gap:18px}.filter-sec[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%]{width:100%}.filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%]   p-dropdown[_ngcontent-%COMP%]{width:100%}.customer-info[_ngcontent-%COMP%]{border:1px solid #ccc;background-color:#e7ecf2;padding:15px;display:flex;margin-bottom:30px;justify-content:space-between;gap:18px!important;border-radius:15px;box-shadow:5px 5px 10px #0003}.form-info[_ngcontent-%COMP%]{border:1px solid #ccc;margin-bottom:50px;padding:20px 10px 10px;border-radius:15px;box-shadow:5px 5px 10px #0003}\"]\n      });\n    }\n  }\n  return SalesQuotesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}