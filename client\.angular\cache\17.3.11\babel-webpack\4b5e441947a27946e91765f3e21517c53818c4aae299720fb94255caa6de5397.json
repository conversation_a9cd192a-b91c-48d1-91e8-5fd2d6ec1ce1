{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./account.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = () => [\"/auth/signup\"];\nconst _c1 = a0 => ({\n  \"text-orange-600 cursor-pointer font-medium\": true,\n  underline: a0\n});\nfunction AccountComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 21)(4, \"div\", 22);\n    i0.ɵɵtext(5, \" Account ID \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 24)(8, \"div\", 22);\n    i0.ɵɵtext(9, \" Name \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\")(12, \"div\", 22);\n    i0.ɵɵtext(13, \" Address \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"th\")(15, \"div\", 22);\n    i0.ɵɵtext(16, \" City \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\")(18, \"div\", 22);\n    i0.ɵɵtext(19, \" State \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\")(21, \"div\", 22);\n    i0.ɵɵtext(22, \" Size \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"th\")(24, \"div\", 22);\n    i0.ɵɵtext(25, \" Role \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 20);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16, \" Customer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const account_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/store/account/\" + account_r3.documentId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", account_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c1, account_r3 == null ? null : account_r3.bp_id));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, account_r3 == null ? null : account_r3.bp_full_name));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.city_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.region) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r3 == null ? null : account_r3.size) || \"-\", \" \");\n  }\n}\nfunction AccountComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"No accounts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"Loading accounts data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.accounts = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Account',\n      routerLink: ['/store/account']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Accounts',\n      code: 'MA'\n    }, {\n      name: 'Obsolete Accounts',\n      code: 'OA'\n    }];\n  }\n  loadAccounts(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.accountservice.getAccounts(page, pageSize, sortField, sortOrder, this.globalSearchTerm).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.accounts = response?.data.map(account => {\n          const defaultAddress = account.address_usages?.find(usage => usage.address_usage === 'XXDEFAULT')?.business_partner_address;\n          return {\n            ...account,\n            address: [defaultAddress?.house_number, defaultAddress?.street_name, defaultAddress?.city_name, defaultAddress?.region, defaultAddress?.country, defaultAddress?.postal_code].filter(Boolean).join(', '),\n            city_name: defaultAddress?.city_name || '-',\n            region: defaultAddress?.region || '-'\n          };\n        }) || [];\n        this.totalRecords = response?.meta?.pagination?.total || 0;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching accounts', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadAccounts({\n      first: 0,\n      rows: 15\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountComponent_Factory(t) {\n      return new (t || AccountComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountComponent,\n      selectors: [[\"app-account\"]],\n      decls: 22,\n      vars: 15,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Account\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"bg-orange-700\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"bp_id\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"bp_id\"], [\"pSortableColumn\", \"bp_full_name\"], [\"field\", \"bp_full_name\"], [1, \"cursor-pointer\", 3, \"routerLink\"], [3, \"value\"], [3, \"ngClass\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function AccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function AccountComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(17);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12)(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"p-table\", 15, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function AccountComponent_Template_p_table_onLazyLoad_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadAccounts($event));\n          });\n          i0.ɵɵtemplate(18, AccountComponent_ng_template_18_Template, 26, 0, \"ng-template\", 16)(19, AccountComponent_ng_template_19_Template, 17, 14, \"ng-template\", 17)(20, AccountComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18)(21, AccountComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.accounts)(\"rows\", 15)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i3.RouterLink, i4.Breadcrumb, i5.PrimeTemplate, i6.Dropdown, i7.Table, i7.SortableColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "account_r3", "documentId", "ɵɵadvance", "ɵɵpureFunction1", "_c1", "bp_id", "ɵɵtextInterpolate1", "bp_full_name", "address", "city_name", "region", "size", "AccountComponent", "constructor", "accountservice", "unsubscribe$", "accounts", "totalRecords", "loading", "globalSearchTerm", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "loadAccounts", "event", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getAccounts", "pipe", "subscribe", "next", "response", "data", "map", "account", "defaultAddress", "address_usages", "find", "usage", "address_usage", "business_partner_address", "house_number", "street_name", "country", "postal_code", "filter", "Boolean", "join", "meta", "pagination", "total", "error", "console", "onGlobalFilter", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "AccountComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "AccountComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "AccountComponent_Template_p_dropdown_ngModelChange_10_listener", "selectedActions", "AccountComponent_Template_p_table_onLazyLoad_16_listener", "ɵɵtemplate", "AccountComponent_ng_template_18_Template", "AccountComponent_ng_template_19_Template", "AccountComponent_ng_template_20_Template", "AccountComponent_ng_template_21_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { AccountService } from './account.service';\r\nimport { Table } from 'primeng/table';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n@Component({\r\n  selector: 'app-account',\r\n  templateUrl: './account.component.html',\r\n  styleUrl: './account.component.scss',\r\n})\r\nexport class AccountComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n  public accounts: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n\r\n  constructor(private accountservice: AccountService) {}\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Account', routerLink: ['/store/account'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Accounts', code: 'MA' },\r\n      { name: 'Obsolete Accounts', code: 'OA' },\r\n    ];\r\n  }\r\n\r\n  loadAccounts(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.accountservice\r\n      .getAccounts(page, pageSize, sortField, sortOrder, this.globalSearchTerm)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.accounts =\r\n            response?.data.map((account: any) => {\r\n              const defaultAddress = account.address_usages?.find(\r\n                (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n              )?.business_partner_address;\r\n\r\n              return {\r\n                ...account,\r\n                address: [\r\n                  defaultAddress?.house_number,\r\n                  defaultAddress?.street_name,\r\n                  defaultAddress?.city_name,\r\n                  defaultAddress?.region,\r\n                  defaultAddress?.country,\r\n                  defaultAddress?.postal_code,\r\n                ]\r\n                  .filter(Boolean)\r\n                  .join(', '),\r\n                city_name: defaultAddress?.city_name || '-',\r\n                region: defaultAddress?.region || '-',\r\n              };\r\n            }) || [];\r\n\r\n          this.totalRecords = response?.meta?.pagination?.total || 0;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching accounts', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadAccounts({ first: 0, rows: 15 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        placeholder=\"Search Account\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold'\" />\r\n            <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component bg-orange-700 w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"accounts\" dataKey=\"id\" [rows]=\"15\" (onLazyLoad)=\"loadAccounts($event)\"\r\n            [loading]=\"loading\" styleClass=\"\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Account ID\r\n                            <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_full_name\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Name\r\n                            <p-sortIcon field=\"bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Address\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            City\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            State\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Size\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Role\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-account>\r\n                <tr class=\"cursor-pointer\" [routerLink]=\"'/store/account/' + account.documentId\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"account\" />\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true,\r\n                        underline: account?.bp_id\r\n                        }\">\r\n                        {{ account?.bp_id || \"-\" }}\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true,\r\n                        underline: account?.bp_full_name\r\n                        }\">\r\n                        {{ account?.bp_full_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.address || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.city_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.region || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.size || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        Customer\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">No accounts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">Loading accounts data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;IC0BrBC,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAA4B,cACa;IACjCD,EAAA,CAAAI,MAAA,mBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAAuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAmC,cACM;IACjCD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,iBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,cACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,eACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,cACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,cACJ;IAERJ,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;IAKDH,EADJ,CAAAC,cAAA,aAAiF,aACpB;IACrDD,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGO;IACHD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGO;IACHD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,kBACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA/BsBH,EAAA,CAAAK,UAAA,mCAAAC,UAAA,CAAAC,UAAA,CAAqD;IAEvDP,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAK,UAAA,UAAAC,UAAA,CAAiB;IAElCN,EAAA,CAAAQ,SAAA,EAGE;IAHFR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAJ,UAAA,kBAAAA,UAAA,CAAAK,KAAA,EAGE;IACFX,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAK,KAAA,cACJ;IACIX,EAAA,CAAAQ,SAAA,EAGE;IAHFR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAJ,UAAA,kBAAAA,UAAA,CAAAO,YAAA,EAGE;IACFb,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAO,YAAA,cACJ;IAEIb,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAQ,OAAA,cACJ;IAEId,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAS,SAAA,cACJ;IAEIf,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAU,MAAA,cACJ;IAEIhB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAW,IAAA,cACJ;;;;;IAQAjB,EADJ,CAAAC,cAAA,SAAI,aACkD;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACxEJ,EADwE,CAAAG,YAAA,EAAK,EACxE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACkD;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IACzFJ,EADyF,CAAAG,YAAA,EAAK,EACzF;;;ADpGrB,OAAM,MAAOe,gBAAgB;EAW3BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAV1B,KAAAC,YAAY,GAAG,IAAIvB,OAAO,EAAQ;IAKnC,KAAAwB,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;EAEiB;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,SAAS;MAAEC,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,CACrD;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IACrD,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAI,CAAE,EACnC;MAAED,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC1C;EACH;EAEAC,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAMa,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,MAAME,SAAS,GAAGL,KAAK,CAACK,SAAS;IACjC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAEjC,IAAI,CAACtB,cAAc,CAChBuB,WAAW,CAACN,IAAI,EAAEG,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAE,IAAI,CAACjB,gBAAgB,CAAC,CACxEmB,IAAI,CAAC7C,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClCwB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACzB,QAAQ,GACXyB,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAAEC,OAAY,IAAI;UAClC,MAAMC,cAAc,GAAGD,OAAO,CAACE,cAAc,EAAEC,IAAI,CAChDC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,EAAEC,wBAAwB;UAE3B,OAAO;YACL,GAAGN,OAAO;YACVpC,OAAO,EAAE,CACPqC,cAAc,EAAEM,YAAY,EAC5BN,cAAc,EAAEO,WAAW,EAC3BP,cAAc,EAAEpC,SAAS,EACzBoC,cAAc,EAAEnC,MAAM,EACtBmC,cAAc,EAAEQ,OAAO,EACvBR,cAAc,EAAES,WAAW,CAC5B,CACEC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;YACbhD,SAAS,EAAEoC,cAAc,EAAEpC,SAAS,IAAI,GAAG;YAC3CC,MAAM,EAAEmC,cAAc,EAAEnC,MAAM,IAAI;WACnC;QACH,CAAC,CAAC,IAAI,EAAE;QAEV,IAAI,CAACO,YAAY,GAAGwB,QAAQ,EAAEiB,IAAI,EAAEC,UAAU,EAAEC,KAAK,IAAI,CAAC;QAC1D,IAAI,CAAC1C,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2C,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC3C,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA6C,cAAcA,CAACC,KAAY,EAAElC,KAAY;IACvC,IAAI,CAACD,YAAY,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC3C;EAEAgC,WAAWA,CAAA;IACT,IAAI,CAAClD,YAAY,CAACyB,IAAI,EAAE;IACxB,IAAI,CAACzB,YAAY,CAACmD,QAAQ,EAAE;EAC9B;;;uBA9EWtD,gBAAgB,EAAAlB,EAAA,CAAAyE,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhBzD,gBAAgB;MAAA0D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCbrBlF,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAG4E;UAF9ED,EAAA,CAAAoF,gBAAA,2BAAAC,yDAAAC,MAAA;YAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;YAAAxF,EAAA,CAAAyF,kBAAA,CAAAN,GAAA,CAAA1D,gBAAA,EAAA6D,MAAA,MAAAH,GAAA,CAAA1D,gBAAA,GAAA6D,MAAA;YAAA,OAAAtF,EAAA,CAAA0F,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UAACtF,EAAA,CAAA2F,UAAA,mBAAAC,iDAAAN,MAAA;YAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;YAAA,MAAAK,MAAA,GAAA7F,EAAA,CAAA8F,WAAA;YAAA,OAAA9F,EAAA,CAAA0F,WAAA,CAASP,GAAA,CAAAd,cAAA,CAAAwB,MAAA,EAAAP,MAAA,CAA2B;UAAA,EAAC;UAA/FtF,EAAA,CAAAG,YAAA,EAEyG;UACzGH,EAAA,CAAAE,SAAA,YAA4B;UAEpCF,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAC,cAAA,sBACwF;UADxDD,EAAA,CAAAoF,gBAAA,2BAAAW,+DAAAT,MAAA;YAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;YAAAxF,EAAA,CAAAyF,kBAAA,CAAAN,GAAA,CAAAa,eAAA,EAAAV,MAAA,MAAAH,GAAA,CAAAa,eAAA,GAAAV,MAAA;YAAA,OAAAtF,EAAA,CAAA0F,WAAA,CAAAJ,MAAA;UAAA,EAA6B;UAA7DtF,EAAA,CAAAG,YAAA,EACwF;UAGpFH,EAFJ,CAAAC,cAAA,kBACuI,gBACnF;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAGW;UAF4BD,EAAA,CAAA2F,UAAA,wBAAAM,yDAAAX,MAAA;YAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;YAAA,OAAAxF,EAAA,CAAA0F,WAAA,CAAcP,GAAA,CAAAhD,YAAA,CAAAmD,MAAA,CAAoB;UAAA,EAAC;UAuFzFtF,EApFA,CAAAkG,UAAA,KAAAC,wCAAA,2BAAgC,KAAAC,wCAAA,4BA6CU,KAAAC,wCAAA,0BAkCJ,KAAAC,wCAAA,0BAKD;UAOjDtG,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UApHoBH,EAAA,CAAAQ,SAAA,GAAyB;UAAeR,EAAxC,CAAAK,UAAA,UAAA8E,GAAA,CAAAxD,eAAA,CAAyB,SAAAwD,GAAA,CAAArD,IAAA,CAAc,uCAAuC;UAMzD9B,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAA1D,gBAAA,CAA8B;UAMrDzB,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAK,UAAA,YAAA8E,GAAA,CAAAnD,OAAA,CAAmB;UAAChC,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAa,eAAA,CAA6B;UACzDhG,EAAA,CAAAK,UAAA,kFAAiF;UAC/DL,EAAA,CAAAQ,SAAA,EAA+B;UAA/BR,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAwG,eAAA,KAAAC,GAAA,EAA+B;UAQ3CzG,EAAA,CAAAQ,SAAA,GAAkB;UACuDR,EADzE,CAAAK,UAAA,UAAA8E,GAAA,CAAA7D,QAAA,CAAkB,YAAyB,YAAA6D,GAAA,CAAA3D,OAAA,CAClC,mBAAiC,iBAAA2D,GAAA,CAAA5D,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}