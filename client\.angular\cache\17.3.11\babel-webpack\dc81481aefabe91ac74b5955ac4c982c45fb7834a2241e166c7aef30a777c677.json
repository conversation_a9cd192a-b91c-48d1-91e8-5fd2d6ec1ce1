{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AccordionModule } from 'primeng/accordion';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ExportRoutingModule } from './export-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let ExportModule = /*#__PURE__*/(() => {\n  class ExportModule {\n    static {\n      this.ɵfac = function ExportModule_Factory(t) {\n        return new (t || ExportModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ExportModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, ExportRoutingModule, BreadcrumbModule, DropdownModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule, AccordionModule, RadioButtonModule]\n      });\n    }\n  }\n  return ExportModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}