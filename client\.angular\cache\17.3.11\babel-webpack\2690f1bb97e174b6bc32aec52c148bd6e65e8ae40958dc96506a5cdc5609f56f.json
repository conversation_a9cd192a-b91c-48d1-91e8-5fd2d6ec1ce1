{"ast": null, "code": "import cityList from './assets/city.json';\nimport { compare, convertArrayToObject } from './utils';\nconst KEYS = [\"name\", \"countryCode\", \"stateCode\", \"latitude\", \"longitude\"];\nlet convertedCityList = [];\n// Get a list of all cities.\nfunction getAllCities(keys = KEYS) {\n  if (convertedCityList.length) {\n    return convertedCityList;\n  }\n  const cityJSON = cityList;\n  convertedCityList = convertArrayToObject(keys !== null && keys !== void 0 ? keys : KEYS, cityJSON);\n  return convertedCityList;\n}\n// Get a list of cities belonging to a specific state and country.\nfunction getCitiesOfState(countryCode, stateCode) {\n  if (!stateCode) return [];\n  if (!countryCode) return [];\n  const cityList = getAllCities();\n  const cities = cityList.filter(value => {\n    return value.countryCode === countryCode && value.stateCode === stateCode;\n  });\n  return cities.sort(compare);\n}\n// Get a list of cities belonging to a specific country.\nfunction getCitiesOfCountry(countryCode) {\n  if (!countryCode) return [];\n  const cityList = getAllCities();\n  const cities = cityList.filter(value => {\n    return value.countryCode === countryCode;\n  });\n  return cities.sort(compare);\n}\nfunction sortByStateAndName(cities) {\n  return cities.sort((a, b) => {\n    const result = compare(a, b, entity => {\n      return `${entity.countryCode}-${entity.stateCode}`;\n    });\n    if (result !== 0) return result;\n    return compare(a, b);\n  });\n}\nexport default {\n  getAllCities,\n  getCitiesOfState,\n  getCitiesOfCountry,\n  sortByStateAndName\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}