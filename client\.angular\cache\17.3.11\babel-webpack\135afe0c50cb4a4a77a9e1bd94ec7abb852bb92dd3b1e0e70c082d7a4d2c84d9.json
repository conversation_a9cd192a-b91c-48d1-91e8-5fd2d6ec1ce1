{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}];\nexport let HomeRoutingModule = /*#__PURE__*/(() => {\n  class HomeRoutingModule {\n    static {\n      this.ɵfac = function HomeRoutingModule_Factory(t) {\n        return new (t || HomeRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: HomeRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return HomeRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}