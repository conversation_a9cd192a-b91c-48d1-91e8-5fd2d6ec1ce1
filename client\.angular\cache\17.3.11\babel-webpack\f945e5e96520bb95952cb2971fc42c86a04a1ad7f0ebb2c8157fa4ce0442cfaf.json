{"ast": null, "code": "import { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class SalesOrdersService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n  }\n  fetchOrders(params) {\n    // const headers = new HttpHeaders().set('Authorization', `Bearer ${this.authService.getToken()}`);\n    return this.http.get(ApiConstant.SALES_ORDER, {\n      params\n      // headers,\n    });\n  }\n  fetchOrderStatuses(headers) {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n      params: headers\n    });\n  }\n  getPartnerFunction(custId) {\n    console.log(custId, \"CustId-->\");\n    return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}`).pipe(map(res => res.data));\n    // return this.http.get<any>(`https://chssnjyaapidev.cfapps.us10-001.hana.ondemand.com/api/customer-partner-functions?filters[customer_id][$eq]=${custId}`)\n    // ?filters[customer_id][$eq]=${custId}`)\n    // .pipe(map(res => res.data));\n  }\n  // getPartnerFunction(custId: string) {\n  //   return this.http.get<CustomerSearchList>(${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}).pipe(map(res => res.data));\n  // }\n  fetchOrderById(orderId) {\n    return this.http.get(`${ApiConstant.SALES_ORDER}/${orderId}`);\n  }\n  getImages(productId) {\n    console.log(this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), \"image res\");\n    return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\n  }\n  static {\n    this.ɵfac = function SalesOrdersService_Factory(t) {\n      return new (t || SalesOrdersService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SalesOrdersService,\n      factory: SalesOrdersService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConstant", "CMS_APIContstant", "map", "SalesOrdersService", "constructor", "http", "authService", "fetchOrders", "params", "get", "SALES_ORDER", "fetchOrderStatuses", "headers", "CONFIG_DATA", "getPartnerFunction", "custId", "console", "log", "CUSTOMER_PARTNER_FUNCTION", "pipe", "res", "data", "fetchOrderById", "orderId", "getImages", "productId", "PRODUCT_MDEIA", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { map } from \"rxjs\";\r\ninterface AccountTableData {\r\n  PURCH_NO?: string;\r\n  SD_DOC?: string;\r\n  CHANNEL?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_STATUS?: string;\r\n  TXN_CURRENCY?: string;\r\n  DOC_DATE?: string;\r\n  TOTAL_NET_AMOUNT?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SalesOrdersService {\r\n  constructor(private http: HttpClient, private authService: AuthService) { }\r\n\r\n  fetchOrders(params: any): Observable<{ resultData: AccountTableData[] }> {\r\n    // const headers = new HttpHeaders().set('Authorization', `Bearer ${this.authService.getToken()}`);\r\n    return this.http.get<{ resultData: AccountTableData[] }>(ApiConstant.SALES_ORDER, {\r\n      params,\r\n      // headers,\r\n    });\r\n  }\r\n\r\n  fetchOrderStatuses(headers: any): Observable<string[]> {\r\n    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA, {\r\n      params: headers\r\n    });\r\n  }\r\n\r\n  getPartnerFunction(custId: string) {\r\n    console.log(custId, \"CustId-->\")\r\n    return this.http.get<any>(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}`).pipe(map(res => res.data));\r\n    // return this.http.get<any>(`https://chssnjyaapidev.cfapps.us10-001.hana.ondemand.com/api/customer-partner-functions?filters[customer_id][$eq]=${custId}`)\r\n    // ?filters[customer_id][$eq]=${custId}`)\r\n    // .pipe(map(res => res.data));\r\n  }\r\n  // getPartnerFunction(custId: string) {\r\n  //   return this.http.get<CustomerSearchList>(${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}).pipe(map(res => res.data));\r\n  // }\r\n  fetchOrderById(orderId: string) {\r\n    return this.http.get<any>(`${ApiConstant.SALES_ORDER}/${orderId}`);\r\n  }\r\n  getImages(productId: string) {\r\n    console.log(this.http.get<any>(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), \"image res\")\r\n    return this.http.get<any>(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\r\n  }\r\n}\r\n"], "mappings": "AAIA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SAASC,GAAG,QAAQ,MAAM;;;;AAe1B,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB,EAAUC,WAAwB;IAAlD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,WAAW,GAAXA,WAAW;EAAiB;EAE1EC,WAAWA,CAACC,MAAW;IACrB;IACA,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAqCT,WAAW,CAACU,WAAW,EAAE;MAChFF;MACA;KACD,CAAC;EACJ;EAEAG,kBAAkBA,CAACC,OAAY;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACI,GAAG,CAAMR,gBAAgB,CAACY,WAAW,EAAE;MACtDL,MAAM,EAAEI;KACT,CAAC;EACJ;EAEAE,kBAAkBA,CAACC,MAAc;IAC/BC,OAAO,CAACC,GAAG,CAACF,MAAM,EAAE,WAAW,CAAC;IAChC,OAAO,IAAI,CAACV,IAAI,CAACI,GAAG,CAAM,GAAGR,gBAAgB,CAACiB,yBAAyB,8BAA8BH,MAAM,EAAE,CAAC,CAACI,IAAI,CAACjB,GAAG,CAACkB,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC;IACzI;IACA;IACA;EACF;EACA;EACA;EACA;EACAC,cAAcA,CAACC,OAAe;IAC5B,OAAO,IAAI,CAAClB,IAAI,CAACI,GAAG,CAAM,GAAGT,WAAW,CAACU,WAAW,IAAIa,OAAO,EAAE,CAAC;EACpE;EACAC,SAASA,CAACC,SAAiB;IACzBT,OAAO,CAACC,GAAG,CAAC,IAAI,CAACZ,IAAI,CAACI,GAAG,CAAM,GAAGR,gBAAgB,CAACyB,aAAa,6BAA6BD,SAAS,EAAE,CAAC,EAAE,WAAW,CAAC;IACvH,OAAO,IAAI,CAACpB,IAAI,CAACI,GAAG,CAAM,GAAGR,gBAAgB,CAACyB,aAAa,6BAA6BD,SAAS,EAAE,CAAC;EACtG;;;uBAjCWtB,kBAAkB,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAlB7B,kBAAkB;MAAA8B,OAAA,EAAlB9B,kBAAkB,CAAA+B,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}