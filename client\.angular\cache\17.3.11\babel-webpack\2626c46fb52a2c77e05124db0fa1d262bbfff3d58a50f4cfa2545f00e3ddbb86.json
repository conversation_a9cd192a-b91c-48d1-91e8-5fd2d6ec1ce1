{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputswitch p-component\": true,\n  \"p-inputswitch-checked\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst INPUTSWITCH_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputSwitch),\n  multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\nclass InputSwitch {\n  cd;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Identifier of the input element.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Name of the input element.\n   * @group Props\n   */\n  name;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * When present, it specifies that the component cannot be edited.\n   * @group Props\n   */\n  readonly;\n  /**\n   * Value in checked state.\n   * @group Props\n   */\n  trueValue = true;\n  /**\n   * Value in unchecked state.\n   * @group Props\n   */\n  falseValue = false;\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Callback to invoke when the on value change.\n   * @param {InputSwitchChangeEvent} event - Custom change event.\n   * @group Emits\n   */\n  onChange = new EventEmitter();\n  input;\n  modelValue = false;\n  focused = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  constructor(cd) {\n    this.cd = cd;\n  }\n  onClick(event) {\n    if (!this.disabled && !this.readonly) {\n      this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n      this.onModelChange(this.modelValue);\n      this.onChange.emit({\n        originalEvent: event,\n        checked: this.modelValue\n      });\n      event.preventDefault();\n      this.input.nativeElement.focus();\n    }\n  }\n  onFocus() {\n    this.focused = true;\n  }\n  onBlur() {\n    this.focused = false;\n    this.onModelTouched();\n  }\n  writeValue(value) {\n    this.modelValue = value;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n  checked() {\n    return this.modelValue === this.trueValue;\n  }\n  static ɵfac = function InputSwitch_Factory(t) {\n    return new (t || InputSwitch)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputSwitch,\n    selectors: [[\"p-inputSwitch\"]],\n    viewQuery: function InputSwitch_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: \"tabindex\",\n      inputId: \"inputId\",\n      name: \"name\",\n      disabled: \"disabled\",\n      readonly: \"readonly\",\n      trueValue: \"trueValue\",\n      falseValue: \"falseValue\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      onChange: \"onChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([INPUTSWITCH_VALUE_ACCESSOR])],\n    decls: 5,\n    vars: 22,\n    consts: [[\"input\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"role\", \"switch\", 3, \"focus\", \"blur\", \"checked\", \"disabled\"], [1, \"p-inputswitch-slider\"]],\n    template: function InputSwitch_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function InputSwitch_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onClick($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3, 0);\n        i0.ɵɵlistener(\"focus\", function InputSwitch_Template_input_focus_2_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus());\n        })(\"blur\", function InputSwitch_Template_input_blur_2_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(4, \"span\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(18, _c1, ctx.checked(), ctx.disabled, ctx.focused))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"inputswitch\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"hiddenInputWrapper\")(\"data-p-hidden-accessible\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"checked\", ctx.checked())(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-checked\", ctx.checked())(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"data-pc-section\", \"hiddenInput\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"data-pc-section\", \"slider\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputSwitch, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputSwitch',\n      template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `,\n      providers: [INPUTSWITCH_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    trueValue: [{\n      type: Input\n    }],\n    falseValue: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    onChange: [{\n      type: Output\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }]\n  });\n})();\nclass InputSwitchModule {\n  static ɵfac = function InputSwitchModule_Factory(t) {\n    return new (t || InputSwitchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputSwitchModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputSwitchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [InputSwitch],\n      declarations: [InputSwitch]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTSWITCH_VALUE_ACCESSOR, InputSwitch, InputSwitchModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "_c0", "_c1", "a0", "a1", "a2", "INPUTSWITCH_VALUE_ACCESSOR", "provide", "useExisting", "InputSwitch", "multi", "cd", "style", "styleClass", "tabindex", "inputId", "name", "disabled", "readonly", "trueValue", "falseValue", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "onChange", "input", "modelValue", "focused", "onModelChange", "onModelTouched", "constructor", "onClick", "event", "checked", "emit", "originalEvent", "preventDefault", "nativeElement", "focus", "onFocus", "onBlur", "writeValue", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "val", "ɵfac", "InputSwitch_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "InputSwitch_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "InputSwitch_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "InputSwitch_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "InputSwitch_Template_input_focus_2_listener", "InputSwitch_Template_input_blur_2_listener", "ɵɵelementEnd", "ɵɵelement", "ɵɵclassMap", "ɵɵproperty", "ɵɵpureFunction3", "ɵɵattribute", "ɵɵadvance", "dependencies", "Ng<PERSON><PERSON>", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "OnPush", "None", "host", "class", "InputSwitchModule", "InputSwitchModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-inputswitch.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\n\nconst INPUTSWITCH_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputSwitch),\n    multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\nclass InputSwitch {\n    cd;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the input element.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    trueValue = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    falseValue = false;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Callback to invoke when the on value change.\n     * @param {InputSwitchChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    input;\n    modelValue = false;\n    focused = false;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    constructor(cd) {\n        this.cd = cd;\n    }\n    onClick(event) {\n        if (!this.disabled && !this.readonly) {\n            this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n            this.onModelChange(this.modelValue);\n            this.onChange.emit({\n                originalEvent: event,\n                checked: this.modelValue\n            });\n            event.preventDefault();\n            this.input.nativeElement.focus();\n        }\n    }\n    onFocus() {\n        this.focused = true;\n    }\n    onBlur() {\n        this.focused = false;\n        this.onModelTouched();\n    }\n    writeValue(value) {\n        this.modelValue = value;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    checked() {\n        return this.modelValue === this.trueValue;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitch, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: InputSwitch, selector: \"p-inputSwitch\", inputs: { style: \"style\", styleClass: \"styleClass\", tabindex: \"tabindex\", inputId: \"inputId\", name: \"name\", disabled: \"disabled\", readonly: \"readonly\", trueValue: \"trueValue\", falseValue: \"falseValue\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\" }, outputs: { onChange: \"onChange\" }, host: { classAttribute: \"p-element\" }, providers: [INPUTSWITCH_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], ngImport: i0, template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitch, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-inputSwitch', template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `, providers: [INPUTSWITCH_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], trueValue: [{\n                type: Input\n            }], falseValue: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], onChange: [{\n                type: Output\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }] } });\nclass InputSwitchModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitchModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitchModule, declarations: [InputSwitch], imports: [CommonModule], exports: [InputSwitch] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitchModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputSwitchModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [InputSwitch],\n                    declarations: [InputSwitch]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTSWITCH_VALUE_ACCESSOR, InputSwitch, InputSwitchModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnJ,SAASC,iBAAiB,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,yBAAAF,EAAA;EAAA,cAAAC,EAAA;EAAA,WAAAC;AAAA;AAEnD,MAAMC,0BAA0B,GAAG;EAC/BC,OAAO,EAAEP,iBAAiB;EAC1BQ,WAAW,EAAEjB,UAAU,CAAC,MAAMkB,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdE,EAAE;EACF;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,QAAQ,GAAG,IAAI/B,YAAY,CAAC,CAAC;EAC7BgC,KAAK;EACLC,UAAU,GAAG,KAAK;EAClBC,OAAO,GAAG,KAAK;EACfC,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC1BC,WAAWA,CAAClB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;EAChB;EACAmB,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACd,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACO,UAAU,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC,GAAG,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACD,SAAS;MACnE,IAAI,CAACQ,aAAa,CAAC,IAAI,CAACF,UAAU,CAAC;MACnC,IAAI,CAACF,QAAQ,CAACU,IAAI,CAAC;QACfC,aAAa,EAAEH,KAAK;QACpBC,OAAO,EAAE,IAAI,CAACP;MAClB,CAAC,CAAC;MACFM,KAAK,CAACI,cAAc,CAAC,CAAC;MACtB,IAAI,CAACX,KAAK,CAACY,aAAa,CAACC,KAAK,CAAC,CAAC;IACpC;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACZ,OAAO,GAAG,IAAI;EACvB;EACAa,MAAMA,CAAA,EAAG;IACL,IAAI,CAACb,OAAO,GAAG,KAAK;IACpB,IAAI,CAACE,cAAc,CAAC,CAAC;EACzB;EACAY,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,CAAChB,UAAU,GAAGgB,KAAK;IACvB,IAAI,CAAC9B,EAAE,CAAC+B,YAAY,CAAC,CAAC;EAC1B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACjB,aAAa,GAAGiB,EAAE;EAC3B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAChB,cAAc,GAAGgB,EAAE;EAC5B;EACAE,gBAAgBA,CAACC,GAAG,EAAE;IAClB,IAAI,CAAC9B,QAAQ,GAAG8B,GAAG;IACnB,IAAI,CAACpC,EAAE,CAAC+B,YAAY,CAAC,CAAC;EAC1B;EACAV,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACP,UAAU,KAAK,IAAI,CAACN,SAAS;EAC7C;EACA,OAAO6B,IAAI,YAAAC,oBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFzC,WAAW,EAArBnB,EAAE,CAAA6D,iBAAA,CAAqC7D,EAAE,CAAC8D,iBAAiB;EAAA;EACpJ,OAAOC,IAAI,kBAD8E/D,EAAE,CAAAgE,iBAAA;IAAAC,IAAA,EACJ9C,WAAW;IAAA+C,SAAA;IAAAC,SAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADTrE,EAAE,CAAAuE,WAAA,CAAA5D,GAAA;MAAA;MAAA,IAAA0D,EAAA;QAAA,IAAAG,EAAA;QAAFxE,EAAE,CAAAyE,cAAA,CAAAD,EAAA,GAAFxE,EAAE,CAAA0E,WAAA,QAAAJ,GAAA,CAAApC,KAAA,GAAAsC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAvD,KAAA;MAAAC,UAAA;MAAAC,QAAA;MAAAC,OAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,QAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,SAAA;MAAAC,cAAA;IAAA;IAAA8C,OAAA;MAAA7C,QAAA;IAAA;IAAA8C,QAAA,GAAF/E,EAAE,CAAAgF,kBAAA,CAC+X,CAAChE,0BAA0B,CAAC;IAAAiE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAAhB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAiB,GAAA,GAD7ZtF,EAAE,CAAAuF,gBAAA;QAAFvF,EAAE,CAAAwF,cAAA,YASvF,CAAC;QAToFxF,EAAE,CAAAyF,UAAA,mBAAAC,0CAAAC,MAAA;UAAF3F,EAAE,CAAA4F,aAAA,CAAAN,GAAA;UAAA,OAAFtF,EAAE,CAAA6F,WAAA,CAM1EvB,GAAA,CAAA9B,OAAA,CAAAmD,MAAc,CAAC;QAAA,EAAC;QANwD3F,EAAE,CAAAwF,cAAA,YAUkC,CAAC,iBAgBjH,CAAC;QA1B2ExF,EAAE,CAAAyF,UAAA,mBAAAK,4CAAA;UAAF9F,EAAE,CAAA4F,aAAA,CAAAN,GAAA;UAAA,OAAFtF,EAAE,CAAA6F,WAAA,CAuBlEvB,GAAA,CAAAtB,OAAA,CAAQ,CAAC;QAAA,EAAC,kBAAA+C,2CAAA;UAvBsD/F,EAAE,CAAA4F,aAAA,CAAAN,GAAA;UAAA,OAAFtF,EAAE,CAAA6F,WAAA,CAwBnEvB,GAAA,CAAArB,MAAA,CAAO,CAAC;QAAA,EAAC;QAxBwDjD,EAAE,CAAAgG,YAAA,CA0B9E,CAAC,CACD,CAAC;QA3B2EhG,EAAE,CAAAiG,SAAA,aA4BR,CAAC;QA5BKjG,EAAE,CAAAgG,YAAA,CA6BlF,CAAC;MAAA;MAAA,IAAA3B,EAAA;QA7B+ErE,EAAE,CAAAkG,UAAA,CAAA5B,GAAA,CAAA/C,UAKhE,CAAC;QAL6DvB,EAAE,CAAAmG,UAAA,YAAFnG,EAAE,CAAAoG,eAAA,KAAAxF,GAAA,EAAA0D,GAAA,CAAA5B,OAAA,IAAA4B,GAAA,CAAA3C,QAAA,EAAA2C,GAAA,CAAAlC,OAAA,CAG6C,CAAC,YAAAkC,GAAA,CAAAhD,KACjH,CAAC;QAJgEtB,EAAE,CAAAqG,WAAA;QAAFrG,EAAE,CAAAsG,SAAA,CAUN,CAAC;QAVGtG,EAAE,CAAAqG,WAAA;QAAFrG,EAAE,CAAAsG,SAAA,CAgBvD,CAAC;QAhBoDtG,EAAE,CAAAmG,UAAA,YAAA7B,GAAA,CAAA5B,OAAA,EAgBvD,CAAC,aAAA4B,GAAA,CAAA3C,QACD,CAAC;QAjBoD3B,EAAE,CAAAqG,WAAA,OAAA/B,GAAA,CAAA7C,OAAA,kBAAA6C,GAAA,CAAA5B,OAAA,uBAAA4B,GAAA,CAAAtC,cAAA,gBAAAsC,GAAA,CAAAvC,SAAA,UAAAuC,GAAA,CAAA5C,IAAA,cAAA4C,GAAA,CAAA9C,QAAA;QAAFxB,EAAE,CAAAsG,SAAA,EA4BhB,CAAC;QA5BatG,EAAE,CAAAqG,WAAA;MAAA;IAAA;IAAAE,YAAA,GA8BkQzG,EAAE,CAAC0G,OAAO,EAAoF1G,EAAE,CAAC2G,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACzc;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhC6F7G,EAAE,CAAA8G,iBAAA,CAgCJ3F,WAAW,EAAc,CAAC;IACzG8C,IAAI,EAAE9D,SAAS;IACf4G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAE5B,QAAQ,EAAG;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE6B,SAAS,EAAE,CAACjG,0BAA0B,CAAC;MAAE4F,eAAe,EAAExG,uBAAuB,CAAC8G,MAAM;MAAEP,aAAa,EAAEtG,iBAAiB,CAAC8G,IAAI;MAAEC,IAAI,EAAE;QACtHC,KAAK,EAAE;MACX,CAAC;MAAEX,MAAM,EAAE,CAAC,oRAAoR;IAAE,CAAC;EAC/S,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzC,IAAI,EAAEjE,EAAE,CAAC8D;EAAkB,CAAC,CAAC,EAAkB;IAAExC,KAAK,EAAE,CAAC;MAC9E2C,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEiB,UAAU,EAAE,CAAC;MACb0C,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEkB,QAAQ,EAAE,CAAC;MACXyC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEmB,OAAO,EAAE,CAAC;MACVwC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEoB,IAAI,EAAE,CAAC;MACPuC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEqB,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEsB,QAAQ,EAAE,CAAC;MACXqC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEuB,SAAS,EAAE,CAAC;MACZoC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEwB,UAAU,EAAE,CAAC;MACbmC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEyB,SAAS,EAAE,CAAC;MACZkC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE0B,cAAc,EAAE,CAAC;MACjBiC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE2B,QAAQ,EAAE,CAAC;MACXgC,IAAI,EAAE1D;IACV,CAAC,CAAC;IAAE2B,KAAK,EAAE,CAAC;MACR+B,IAAI,EAAEzD,SAAS;MACfuG,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMO,iBAAiB,CAAC;EACpB,OAAO5D,IAAI,YAAA6D,0BAAA3D,CAAA;IAAA,YAAAA,CAAA,IAAwF0D,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBAhG8ExH,EAAE,CAAAyH,gBAAA;IAAAxD,IAAA,EAgGSqD;EAAiB;EACrH,OAAOI,IAAI,kBAjG8E1H,EAAE,CAAA2H,gBAAA;IAAAC,OAAA,GAiGsC7H,YAAY;EAAA;AACjJ;AACA;EAAA,QAAA8G,SAAA,oBAAAA,SAAA,KAnG6F7G,EAAE,CAAA8G,iBAAA,CAmGJQ,iBAAiB,EAAc,CAAC;IAC/GrD,IAAI,EAAExD,QAAQ;IACdsG,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAAC7H,YAAY,CAAC;MACvB8H,OAAO,EAAE,CAAC1G,WAAW,CAAC;MACtB2G,YAAY,EAAE,CAAC3G,WAAW;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,0BAA0B,EAAEG,WAAW,EAAEmG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}