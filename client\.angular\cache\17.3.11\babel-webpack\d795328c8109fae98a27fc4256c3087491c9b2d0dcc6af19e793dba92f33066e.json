{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ResetPasswordService = /*#__PURE__*/(() => {\n  class ResetPasswordService {\n    constructor(http) {\n      this.http = http;\n    }\n    resetPassword(data) {\n      return this.http.post(CMS_APIContstant.RESET_PASSWORD, data);\n    }\n    static {\n      this.ɵfac = function ResetPasswordService_Factory(t) {\n        return new (t || ResetPasswordService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ResetPasswordService,\n        factory: ResetPasswordService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ResetPasswordService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}