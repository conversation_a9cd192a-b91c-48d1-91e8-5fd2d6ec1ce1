{"ast": null, "code": "/*!\n * Quill Editor v1.3.7\n * https://quilljs.com/\n * Copyright (c) 2014, <PERSON>\n * Copyright (c) 2013, salesforce.com\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define([], factory);else if (typeof exports === 'object') exports[\"Quill\"] = factory();else root[\"Quill\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function () {\n  return /******/function (modules) {\n    // webpackBootstrap\n    /******/ // The module cache\n    /******/\n    var installedModules = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/\n      /******/ // Check if module is in cache\n      /******/if (installedModules[moduleId]) {\n        /******/return installedModules[moduleId].exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = installedModules[moduleId] = {\n        /******/i: moduleId,\n        /******/l: false,\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Flag the module as loaded\n      /******/\n      module.l = true;\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /******/\n    /******/ // expose the modules object (__webpack_modules__)\n    /******/\n    __webpack_require__.m = modules;\n    /******/\n    /******/ // expose the module cache\n    /******/\n    __webpack_require__.c = installedModules;\n    /******/\n    /******/ // define getter function for harmony exports\n    /******/\n    __webpack_require__.d = function (exports, name, getter) {\n      /******/if (!__webpack_require__.o(exports, name)) {\n        /******/Object.defineProperty(exports, name, {\n          /******/configurable: false,\n          /******/enumerable: true,\n          /******/get: getter\n          /******/\n        });\n        /******/\n      }\n      /******/\n    };\n    /******/\n    /******/ // getDefaultExport function for compatibility with non-harmony modules\n    /******/\n    __webpack_require__.n = function (module) {\n      /******/var getter = module && module.__esModule ? /******/function getDefault() {\n        return module['default'];\n      } : /******/function getModuleExports() {\n        return module;\n      };\n      /******/\n      __webpack_require__.d(getter, 'a', getter);\n      /******/\n      return getter;\n      /******/\n    };\n    /******/\n    /******/ // Object.prototype.hasOwnProperty.call\n    /******/\n    __webpack_require__.o = function (object, property) {\n      return Object.prototype.hasOwnProperty.call(object, property);\n    };\n    /******/\n    /******/ // __webpack_public_path__\n    /******/\n    __webpack_require__.p = \"\";\n    /******/\n    /******/ // Load entry module and return exports\n    /******/\n    return __webpack_require__(__webpack_require__.s = 109);\n    /******/\n  }\n  /************************************************************************/\n  /******/([( /* 0 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var container_1 = __webpack_require__(17);\n    var format_1 = __webpack_require__(18);\n    var leaf_1 = __webpack_require__(19);\n    var scroll_1 = __webpack_require__(45);\n    var inline_1 = __webpack_require__(46);\n    var block_1 = __webpack_require__(47);\n    var embed_1 = __webpack_require__(48);\n    var text_1 = __webpack_require__(49);\n    var attributor_1 = __webpack_require__(12);\n    var class_1 = __webpack_require__(32);\n    var style_1 = __webpack_require__(33);\n    var store_1 = __webpack_require__(31);\n    var Registry = __webpack_require__(1);\n    var Parchment = {\n      Scope: Registry.Scope,\n      create: Registry.create,\n      find: Registry.find,\n      query: Registry.query,\n      register: Registry.register,\n      Container: container_1.default,\n      Format: format_1.default,\n      Leaf: leaf_1.default,\n      Embed: embed_1.default,\n      Scroll: scroll_1.default,\n      Block: block_1.default,\n      Inline: inline_1.default,\n      Text: text_1.default,\n      Attributor: {\n        Attribute: attributor_1.default,\n        Class: class_1.default,\n        Style: style_1.default,\n        Store: store_1.default\n      }\n    };\n    exports.default = Parchment;\n\n    /***/\n  }), ( /* 1 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var ParchmentError = /** @class */function (_super) {\n      __extends(ParchmentError, _super);\n      function ParchmentError(message) {\n        var _this = this;\n        message = '[Parchment] ' + message;\n        _this = _super.call(this, message) || this;\n        _this.message = message;\n        _this.name = _this.constructor.name;\n        return _this;\n      }\n      return ParchmentError;\n    }(Error);\n    exports.ParchmentError = ParchmentError;\n    var attributes = {};\n    var classes = {};\n    var tags = {};\n    var types = {};\n    exports.DATA_KEY = '__blot';\n    var Scope;\n    (function (Scope) {\n      Scope[Scope[\"TYPE\"] = 3] = \"TYPE\";\n      Scope[Scope[\"LEVEL\"] = 12] = \"LEVEL\";\n      Scope[Scope[\"ATTRIBUTE\"] = 13] = \"ATTRIBUTE\";\n      Scope[Scope[\"BLOT\"] = 14] = \"BLOT\";\n      Scope[Scope[\"INLINE\"] = 7] = \"INLINE\";\n      Scope[Scope[\"BLOCK\"] = 11] = \"BLOCK\";\n      Scope[Scope[\"BLOCK_BLOT\"] = 10] = \"BLOCK_BLOT\";\n      Scope[Scope[\"INLINE_BLOT\"] = 6] = \"INLINE_BLOT\";\n      Scope[Scope[\"BLOCK_ATTRIBUTE\"] = 9] = \"BLOCK_ATTRIBUTE\";\n      Scope[Scope[\"INLINE_ATTRIBUTE\"] = 5] = \"INLINE_ATTRIBUTE\";\n      Scope[Scope[\"ANY\"] = 15] = \"ANY\";\n    })(Scope = exports.Scope || (exports.Scope = {}));\n    function create(input, value) {\n      var match = query(input);\n      if (match == null) {\n        throw new ParchmentError(\"Unable to create \" + input + \" blot\");\n      }\n      var BlotClass = match;\n      var node =\n      // @ts-ignore\n      input instanceof Node || input['nodeType'] === Node.TEXT_NODE ? input : BlotClass.create(value);\n      return new BlotClass(node, value);\n    }\n    exports.create = create;\n    function find(node, bubble) {\n      if (bubble === void 0) {\n        bubble = false;\n      }\n      if (node == null) return null;\n      // @ts-ignore\n      if (node[exports.DATA_KEY] != null) return node[exports.DATA_KEY].blot;\n      if (bubble) return find(node.parentNode, bubble);\n      return null;\n    }\n    exports.find = find;\n    function query(query, scope) {\n      if (scope === void 0) {\n        scope = Scope.ANY;\n      }\n      var match;\n      if (typeof query === 'string') {\n        match = types[query] || attributes[query];\n        // @ts-ignore\n      } else if (query instanceof Text || query['nodeType'] === Node.TEXT_NODE) {\n        match = types['text'];\n      } else if (typeof query === 'number') {\n        if (query & Scope.LEVEL & Scope.BLOCK) {\n          match = types['block'];\n        } else if (query & Scope.LEVEL & Scope.INLINE) {\n          match = types['inline'];\n        }\n      } else if (query instanceof HTMLElement) {\n        var names = (query.getAttribute('class') || '').split(/\\s+/);\n        for (var i in names) {\n          match = classes[names[i]];\n          if (match) break;\n        }\n        match = match || tags[query.tagName];\n      }\n      if (match == null) return null;\n      // @ts-ignore\n      if (scope & Scope.LEVEL & match.scope && scope & Scope.TYPE & match.scope) return match;\n      return null;\n    }\n    exports.query = query;\n    function register() {\n      var Definitions = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        Definitions[_i] = arguments[_i];\n      }\n      if (Definitions.length > 1) {\n        return Definitions.map(function (d) {\n          return register(d);\n        });\n      }\n      var Definition = Definitions[0];\n      if (typeof Definition.blotName !== 'string' && typeof Definition.attrName !== 'string') {\n        throw new ParchmentError('Invalid definition');\n      } else if (Definition.blotName === 'abstract') {\n        throw new ParchmentError('Cannot register abstract class');\n      }\n      types[Definition.blotName || Definition.attrName] = Definition;\n      if (typeof Definition.keyName === 'string') {\n        attributes[Definition.keyName] = Definition;\n      } else {\n        if (Definition.className != null) {\n          classes[Definition.className] = Definition;\n        }\n        if (Definition.tagName != null) {\n          if (Array.isArray(Definition.tagName)) {\n            Definition.tagName = Definition.tagName.map(function (tagName) {\n              return tagName.toUpperCase();\n            });\n          } else {\n            Definition.tagName = Definition.tagName.toUpperCase();\n          }\n          var tagNames = Array.isArray(Definition.tagName) ? Definition.tagName : [Definition.tagName];\n          tagNames.forEach(function (tag) {\n            if (tags[tag] == null || Definition.className == null) {\n              tags[tag] = Definition;\n            }\n          });\n        }\n      }\n      return Definition;\n    }\n    exports.register = register;\n\n    /***/\n  }), ( /* 2 */\n  /***/function (module, exports, __webpack_require__) {\n    var diff = __webpack_require__(51);\n    var equal = __webpack_require__(11);\n    var extend = __webpack_require__(3);\n    var op = __webpack_require__(20);\n    var NULL_CHARACTER = String.fromCharCode(0); // Placeholder char for embed in diff()\n\n    var Delta = function (ops) {\n      // Assume we are given a well formed ops\n      if (Array.isArray(ops)) {\n        this.ops = ops;\n      } else if (ops != null && Array.isArray(ops.ops)) {\n        this.ops = ops.ops;\n      } else {\n        this.ops = [];\n      }\n    };\n    Delta.prototype.insert = function (text, attributes) {\n      var newOp = {};\n      if (text.length === 0) return this;\n      newOp.insert = text;\n      if (attributes != null && typeof attributes === 'object' && Object.keys(attributes).length > 0) {\n        newOp.attributes = attributes;\n      }\n      return this.push(newOp);\n    };\n    Delta.prototype['delete'] = function (length) {\n      if (length <= 0) return this;\n      return this.push({\n        'delete': length\n      });\n    };\n    Delta.prototype.retain = function (length, attributes) {\n      if (length <= 0) return this;\n      var newOp = {\n        retain: length\n      };\n      if (attributes != null && typeof attributes === 'object' && Object.keys(attributes).length > 0) {\n        newOp.attributes = attributes;\n      }\n      return this.push(newOp);\n    };\n    Delta.prototype.push = function (newOp) {\n      var index = this.ops.length;\n      var lastOp = this.ops[index - 1];\n      newOp = extend(true, {}, newOp);\n      if (typeof lastOp === 'object') {\n        if (typeof newOp['delete'] === 'number' && typeof lastOp['delete'] === 'number') {\n          this.ops[index - 1] = {\n            'delete': lastOp['delete'] + newOp['delete']\n          };\n          return this;\n        }\n        // Since it does not matter if we insert before or after deleting at the same index,\n        // always prefer to insert first\n        if (typeof lastOp['delete'] === 'number' && newOp.insert != null) {\n          index -= 1;\n          lastOp = this.ops[index - 1];\n          if (typeof lastOp !== 'object') {\n            this.ops.unshift(newOp);\n            return this;\n          }\n        }\n        if (equal(newOp.attributes, lastOp.attributes)) {\n          if (typeof newOp.insert === 'string' && typeof lastOp.insert === 'string') {\n            this.ops[index - 1] = {\n              insert: lastOp.insert + newOp.insert\n            };\n            if (typeof newOp.attributes === 'object') this.ops[index - 1].attributes = newOp.attributes;\n            return this;\n          } else if (typeof newOp.retain === 'number' && typeof lastOp.retain === 'number') {\n            this.ops[index - 1] = {\n              retain: lastOp.retain + newOp.retain\n            };\n            if (typeof newOp.attributes === 'object') this.ops[index - 1].attributes = newOp.attributes;\n            return this;\n          }\n        }\n      }\n      if (index === this.ops.length) {\n        this.ops.push(newOp);\n      } else {\n        this.ops.splice(index, 0, newOp);\n      }\n      return this;\n    };\n    Delta.prototype.chop = function () {\n      var lastOp = this.ops[this.ops.length - 1];\n      if (lastOp && lastOp.retain && !lastOp.attributes) {\n        this.ops.pop();\n      }\n      return this;\n    };\n    Delta.prototype.filter = function (predicate) {\n      return this.ops.filter(predicate);\n    };\n    Delta.prototype.forEach = function (predicate) {\n      this.ops.forEach(predicate);\n    };\n    Delta.prototype.map = function (predicate) {\n      return this.ops.map(predicate);\n    };\n    Delta.prototype.partition = function (predicate) {\n      var passed = [],\n        failed = [];\n      this.forEach(function (op) {\n        var target = predicate(op) ? passed : failed;\n        target.push(op);\n      });\n      return [passed, failed];\n    };\n    Delta.prototype.reduce = function (predicate, initial) {\n      return this.ops.reduce(predicate, initial);\n    };\n    Delta.prototype.changeLength = function () {\n      return this.reduce(function (length, elem) {\n        if (elem.insert) {\n          return length + op.length(elem);\n        } else if (elem.delete) {\n          return length - elem.delete;\n        }\n        return length;\n      }, 0);\n    };\n    Delta.prototype.length = function () {\n      return this.reduce(function (length, elem) {\n        return length + op.length(elem);\n      }, 0);\n    };\n    Delta.prototype.slice = function (start, end) {\n      start = start || 0;\n      if (typeof end !== 'number') end = Infinity;\n      var ops = [];\n      var iter = op.iterator(this.ops);\n      var index = 0;\n      while (index < end && iter.hasNext()) {\n        var nextOp;\n        if (index < start) {\n          nextOp = iter.next(start - index);\n        } else {\n          nextOp = iter.next(end - index);\n          ops.push(nextOp);\n        }\n        index += op.length(nextOp);\n      }\n      return new Delta(ops);\n    };\n    Delta.prototype.compose = function (other) {\n      var thisIter = op.iterator(this.ops);\n      var otherIter = op.iterator(other.ops);\n      var ops = [];\n      var firstOther = otherIter.peek();\n      if (firstOther != null && typeof firstOther.retain === 'number' && firstOther.attributes == null) {\n        var firstLeft = firstOther.retain;\n        while (thisIter.peekType() === 'insert' && thisIter.peekLength() <= firstLeft) {\n          firstLeft -= thisIter.peekLength();\n          ops.push(thisIter.next());\n        }\n        if (firstOther.retain - firstLeft > 0) {\n          otherIter.next(firstOther.retain - firstLeft);\n        }\n      }\n      var delta = new Delta(ops);\n      while (thisIter.hasNext() || otherIter.hasNext()) {\n        if (otherIter.peekType() === 'insert') {\n          delta.push(otherIter.next());\n        } else if (thisIter.peekType() === 'delete') {\n          delta.push(thisIter.next());\n        } else {\n          var length = Math.min(thisIter.peekLength(), otherIter.peekLength());\n          var thisOp = thisIter.next(length);\n          var otherOp = otherIter.next(length);\n          if (typeof otherOp.retain === 'number') {\n            var newOp = {};\n            if (typeof thisOp.retain === 'number') {\n              newOp.retain = length;\n            } else {\n              newOp.insert = thisOp.insert;\n            }\n            // Preserve null when composing with a retain, otherwise remove it for inserts\n            var attributes = op.attributes.compose(thisOp.attributes, otherOp.attributes, typeof thisOp.retain === 'number');\n            if (attributes) newOp.attributes = attributes;\n            delta.push(newOp);\n\n            // Optimization if rest of other is just retain\n            if (!otherIter.hasNext() && equal(delta.ops[delta.ops.length - 1], newOp)) {\n              var rest = new Delta(thisIter.rest());\n              return delta.concat(rest).chop();\n            }\n\n            // Other op should be delete, we could be an insert or retain\n            // Insert + delete cancels out\n          } else if (typeof otherOp['delete'] === 'number' && typeof thisOp.retain === 'number') {\n            delta.push(otherOp);\n          }\n        }\n      }\n      return delta.chop();\n    };\n    Delta.prototype.concat = function (other) {\n      var delta = new Delta(this.ops.slice());\n      if (other.ops.length > 0) {\n        delta.push(other.ops[0]);\n        delta.ops = delta.ops.concat(other.ops.slice(1));\n      }\n      return delta;\n    };\n    Delta.prototype.diff = function (other, index) {\n      if (this.ops === other.ops) {\n        return new Delta();\n      }\n      var strings = [this, other].map(function (delta) {\n        return delta.map(function (op) {\n          if (op.insert != null) {\n            return typeof op.insert === 'string' ? op.insert : NULL_CHARACTER;\n          }\n          var prep = delta === other ? 'on' : 'with';\n          throw new Error('diff() called ' + prep + ' non-document');\n        }).join('');\n      });\n      var delta = new Delta();\n      var diffResult = diff(strings[0], strings[1], index);\n      var thisIter = op.iterator(this.ops);\n      var otherIter = op.iterator(other.ops);\n      diffResult.forEach(function (component) {\n        var length = component[1].length;\n        while (length > 0) {\n          var opLength = 0;\n          switch (component[0]) {\n            case diff.INSERT:\n              opLength = Math.min(otherIter.peekLength(), length);\n              delta.push(otherIter.next(opLength));\n              break;\n            case diff.DELETE:\n              opLength = Math.min(length, thisIter.peekLength());\n              thisIter.next(opLength);\n              delta['delete'](opLength);\n              break;\n            case diff.EQUAL:\n              opLength = Math.min(thisIter.peekLength(), otherIter.peekLength(), length);\n              var thisOp = thisIter.next(opLength);\n              var otherOp = otherIter.next(opLength);\n              if (equal(thisOp.insert, otherOp.insert)) {\n                delta.retain(opLength, op.attributes.diff(thisOp.attributes, otherOp.attributes));\n              } else {\n                delta.push(otherOp)['delete'](opLength);\n              }\n              break;\n          }\n          length -= opLength;\n        }\n      });\n      return delta.chop();\n    };\n    Delta.prototype.eachLine = function (predicate, newline) {\n      newline = newline || '\\n';\n      var iter = op.iterator(this.ops);\n      var line = new Delta();\n      var i = 0;\n      while (iter.hasNext()) {\n        if (iter.peekType() !== 'insert') return;\n        var thisOp = iter.peek();\n        var start = op.length(thisOp) - iter.peekLength();\n        var index = typeof thisOp.insert === 'string' ? thisOp.insert.indexOf(newline, start) - start : -1;\n        if (index < 0) {\n          line.push(iter.next());\n        } else if (index > 0) {\n          line.push(iter.next(index));\n        } else {\n          if (predicate(line, iter.next(1).attributes || {}, i) === false) {\n            return;\n          }\n          i += 1;\n          line = new Delta();\n        }\n      }\n      if (line.length() > 0) {\n        predicate(line, {}, i);\n      }\n    };\n    Delta.prototype.transform = function (other, priority) {\n      priority = !!priority;\n      if (typeof other === 'number') {\n        return this.transformPosition(other, priority);\n      }\n      var thisIter = op.iterator(this.ops);\n      var otherIter = op.iterator(other.ops);\n      var delta = new Delta();\n      while (thisIter.hasNext() || otherIter.hasNext()) {\n        if (thisIter.peekType() === 'insert' && (priority || otherIter.peekType() !== 'insert')) {\n          delta.retain(op.length(thisIter.next()));\n        } else if (otherIter.peekType() === 'insert') {\n          delta.push(otherIter.next());\n        } else {\n          var length = Math.min(thisIter.peekLength(), otherIter.peekLength());\n          var thisOp = thisIter.next(length);\n          var otherOp = otherIter.next(length);\n          if (thisOp['delete']) {\n            // Our delete either makes their delete redundant or removes their retain\n            continue;\n          } else if (otherOp['delete']) {\n            delta.push(otherOp);\n          } else {\n            // We retain either their retain or insert\n            delta.retain(length, op.attributes.transform(thisOp.attributes, otherOp.attributes, priority));\n          }\n        }\n      }\n      return delta.chop();\n    };\n    Delta.prototype.transformPosition = function (index, priority) {\n      priority = !!priority;\n      var thisIter = op.iterator(this.ops);\n      var offset = 0;\n      while (thisIter.hasNext() && offset <= index) {\n        var length = thisIter.peekLength();\n        var nextType = thisIter.peekType();\n        thisIter.next();\n        if (nextType === 'delete') {\n          index -= Math.min(length, index - offset);\n          continue;\n        } else if (nextType === 'insert' && (offset < index || !priority)) {\n          index += length;\n        }\n        offset += length;\n      }\n      return index;\n    };\n    module.exports = Delta;\n\n    /***/\n  }), ( /* 3 */\n  /***/function (module, exports) {\n    'use strict';\n\n    var hasOwn = Object.prototype.hasOwnProperty;\n    var toStr = Object.prototype.toString;\n    var defineProperty = Object.defineProperty;\n    var gOPD = Object.getOwnPropertyDescriptor;\n    var isArray = function isArray(arr) {\n      if (typeof Array.isArray === 'function') {\n        return Array.isArray(arr);\n      }\n      return toStr.call(arr) === '[object Array]';\n    };\n    var isPlainObject = function isPlainObject(obj) {\n      if (!obj || toStr.call(obj) !== '[object Object]') {\n        return false;\n      }\n      var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n      var hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n      // Not own constructor property must be Object\n      if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n        return false;\n      }\n\n      // Own properties are enumerated firstly, so to speed up,\n      // if last one is own, then all properties are own.\n      var key;\n      for (key in obj) {/**/}\n      return typeof key === 'undefined' || hasOwn.call(obj, key);\n    };\n\n    // If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\n    var setProperty = function setProperty(target, options) {\n      if (defineProperty && options.name === '__proto__') {\n        defineProperty(target, options.name, {\n          enumerable: true,\n          configurable: true,\n          value: options.newValue,\n          writable: true\n        });\n      } else {\n        target[options.name] = options.newValue;\n      }\n    };\n\n    // Return undefined instead of __proto__ if '__proto__' is not an own property\n    var getProperty = function getProperty(obj, name) {\n      if (name === '__proto__') {\n        if (!hasOwn.call(obj, name)) {\n          return void 0;\n        } else if (gOPD) {\n          // In early versions of node, obj['__proto__'] is buggy when obj has\n          // __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n          return gOPD(obj, name).value;\n        }\n      }\n      return obj[name];\n    };\n    module.exports = function extend() {\n      var options, name, src, copy, copyIsArray, clone;\n      var target = arguments[0];\n      var i = 1;\n      var length = arguments.length;\n      var deep = false;\n\n      // Handle a deep copy situation\n      if (typeof target === 'boolean') {\n        deep = target;\n        target = arguments[1] || {};\n        // skip the boolean and the target\n        i = 2;\n      }\n      if (target == null || typeof target !== 'object' && typeof target !== 'function') {\n        target = {};\n      }\n      for (; i < length; ++i) {\n        options = arguments[i];\n        // Only deal with non-null/undefined values\n        if (options != null) {\n          // Extend the base object\n          for (name in options) {\n            src = getProperty(target, name);\n            copy = getProperty(options, name);\n\n            // Prevent never-ending loop\n            if (target !== copy) {\n              // Recurse if we're merging plain objects or arrays\n              if (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n                if (copyIsArray) {\n                  copyIsArray = false;\n                  clone = src && isArray(src) ? src : [];\n                } else {\n                  clone = src && isPlainObject(src) ? src : {};\n                }\n\n                // Never move original objects, clone them\n                setProperty(target, {\n                  name: name,\n                  newValue: extend(deep, clone, copy)\n                });\n\n                // Don't bring in undefined values\n              } else if (typeof copy !== 'undefined') {\n                setProperty(target, {\n                  name: name,\n                  newValue: copy\n                });\n              }\n            }\n          }\n        }\n      }\n\n      // Return the modified object\n      return target;\n    };\n\n    /***/\n  }), ( /* 4 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.BlockEmbed = exports.bubbleFormats = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _break = __webpack_require__(16);\n    var _break2 = _interopRequireDefault(_break);\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var NEWLINE_LENGTH = 1;\n    var BlockEmbed = function (_Parchment$Embed) {\n      _inherits(BlockEmbed, _Parchment$Embed);\n      function BlockEmbed() {\n        _classCallCheck(this, BlockEmbed);\n        return _possibleConstructorReturn(this, (BlockEmbed.__proto__ || Object.getPrototypeOf(BlockEmbed)).apply(this, arguments));\n      }\n      _createClass(BlockEmbed, [{\n        key: 'attach',\n        value: function attach() {\n          _get(BlockEmbed.prototype.__proto__ || Object.getPrototypeOf(BlockEmbed.prototype), 'attach', this).call(this);\n          this.attributes = new _parchment2.default.Attributor.Store(this.domNode);\n        }\n      }, {\n        key: 'delta',\n        value: function delta() {\n          return new _quillDelta2.default().insert(this.value(), (0, _extend2.default)(this.formats(), this.attributes.values()));\n        }\n      }, {\n        key: 'format',\n        value: function format(name, value) {\n          var attribute = _parchment2.default.query(name, _parchment2.default.Scope.BLOCK_ATTRIBUTE);\n          if (attribute != null) {\n            this.attributes.attribute(attribute, value);\n          }\n        }\n      }, {\n        key: 'formatAt',\n        value: function formatAt(index, length, name, value) {\n          this.format(name, value);\n        }\n      }, {\n        key: 'insertAt',\n        value: function insertAt(index, value, def) {\n          if (typeof value === 'string' && value.endsWith('\\n')) {\n            var block = _parchment2.default.create(Block.blotName);\n            this.parent.insertBefore(block, index === 0 ? this : this.next);\n            block.insertAt(0, value.slice(0, -1));\n          } else {\n            _get(BlockEmbed.prototype.__proto__ || Object.getPrototypeOf(BlockEmbed.prototype), 'insertAt', this).call(this, index, value, def);\n          }\n        }\n      }]);\n      return BlockEmbed;\n    }(_parchment2.default.Embed);\n    BlockEmbed.scope = _parchment2.default.Scope.BLOCK_BLOT;\n    // It is important for cursor behavior BlockEmbeds use tags that are block level elements\n\n    var Block = function (_Parchment$Block) {\n      _inherits(Block, _Parchment$Block);\n      function Block(domNode) {\n        _classCallCheck(this, Block);\n        var _this2 = _possibleConstructorReturn(this, (Block.__proto__ || Object.getPrototypeOf(Block)).call(this, domNode));\n        _this2.cache = {};\n        return _this2;\n      }\n      _createClass(Block, [{\n        key: 'delta',\n        value: function delta() {\n          if (this.cache.delta == null) {\n            this.cache.delta = this.descendants(_parchment2.default.Leaf).reduce(function (delta, leaf) {\n              if (leaf.length() === 0) {\n                return delta;\n              } else {\n                return delta.insert(leaf.value(), bubbleFormats(leaf));\n              }\n            }, new _quillDelta2.default()).insert('\\n', bubbleFormats(this));\n          }\n          return this.cache.delta;\n        }\n      }, {\n        key: 'deleteAt',\n        value: function deleteAt(index, length) {\n          _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'deleteAt', this).call(this, index, length);\n          this.cache = {};\n        }\n      }, {\n        key: 'formatAt',\n        value: function formatAt(index, length, name, value) {\n          if (length <= 0) return;\n          if (_parchment2.default.query(name, _parchment2.default.Scope.BLOCK)) {\n            if (index + length === this.length()) {\n              this.format(name, value);\n            }\n          } else {\n            _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'formatAt', this).call(this, index, Math.min(length, this.length() - index - 1), name, value);\n          }\n          this.cache = {};\n        }\n      }, {\n        key: 'insertAt',\n        value: function insertAt(index, value, def) {\n          if (def != null) return _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'insertAt', this).call(this, index, value, def);\n          if (value.length === 0) return;\n          var lines = value.split('\\n');\n          var text = lines.shift();\n          if (text.length > 0) {\n            if (index < this.length() - 1 || this.children.tail == null) {\n              _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'insertAt', this).call(this, Math.min(index, this.length() - 1), text);\n            } else {\n              this.children.tail.insertAt(this.children.tail.length(), text);\n            }\n            this.cache = {};\n          }\n          var block = this;\n          lines.reduce(function (index, line) {\n            block = block.split(index, true);\n            block.insertAt(0, line);\n            return line.length;\n          }, index + text.length);\n        }\n      }, {\n        key: 'insertBefore',\n        value: function insertBefore(blot, ref) {\n          var head = this.children.head;\n          _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'insertBefore', this).call(this, blot, ref);\n          if (head instanceof _break2.default) {\n            head.remove();\n          }\n          this.cache = {};\n        }\n      }, {\n        key: 'length',\n        value: function length() {\n          if (this.cache.length == null) {\n            this.cache.length = _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'length', this).call(this) + NEWLINE_LENGTH;\n          }\n          return this.cache.length;\n        }\n      }, {\n        key: 'moveChildren',\n        value: function moveChildren(target, ref) {\n          _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'moveChildren', this).call(this, target, ref);\n          this.cache = {};\n        }\n      }, {\n        key: 'optimize',\n        value: function optimize(context) {\n          _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'optimize', this).call(this, context);\n          this.cache = {};\n        }\n      }, {\n        key: 'path',\n        value: function path(index) {\n          return _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'path', this).call(this, index, true);\n        }\n      }, {\n        key: 'removeChild',\n        value: function removeChild(child) {\n          _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'removeChild', this).call(this, child);\n          this.cache = {};\n        }\n      }, {\n        key: 'split',\n        value: function split(index) {\n          var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n          if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {\n            var clone = this.clone();\n            if (index === 0) {\n              this.parent.insertBefore(clone, this);\n              return this;\n            } else {\n              this.parent.insertBefore(clone, this.next);\n              return clone;\n            }\n          } else {\n            var next = _get(Block.prototype.__proto__ || Object.getPrototypeOf(Block.prototype), 'split', this).call(this, index, force);\n            this.cache = {};\n            return next;\n          }\n        }\n      }]);\n      return Block;\n    }(_parchment2.default.Block);\n    Block.blotName = 'block';\n    Block.tagName = 'P';\n    Block.defaultChild = 'break';\n    Block.allowedChildren = [_inline2.default, _parchment2.default.Embed, _text2.default];\n    function bubbleFormats(blot) {\n      var formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (blot == null) return formats;\n      if (typeof blot.formats === 'function') {\n        formats = (0, _extend2.default)(formats, blot.formats());\n      }\n      if (blot.parent == null || blot.parent.blotName == 'scroll' || blot.parent.statics.scope !== blot.statics.scope) {\n        return formats;\n      }\n      return bubbleFormats(blot.parent, formats);\n    }\n    exports.bubbleFormats = bubbleFormats;\n    exports.BlockEmbed = BlockEmbed;\n    exports.default = Block;\n\n    /***/\n  }), ( /* 5 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.overload = exports.expandConfig = undefined;\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    __webpack_require__(50);\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _editor = __webpack_require__(14);\n    var _editor2 = _interopRequireDefault(_editor);\n    var _emitter3 = __webpack_require__(8);\n    var _emitter4 = _interopRequireDefault(_emitter3);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _selection = __webpack_require__(15);\n    var _selection2 = _interopRequireDefault(_selection);\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    var _theme = __webpack_require__(34);\n    var _theme2 = _interopRequireDefault(_theme);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var debug = (0, _logger2.default)('quill');\n    var Quill = function () {\n      _createClass(Quill, null, [{\n        key: 'debug',\n        value: function debug(limit) {\n          if (limit === true) {\n            limit = 'log';\n          }\n          _logger2.default.level(limit);\n        }\n      }, {\n        key: 'find',\n        value: function find(node) {\n          return node.__quill || _parchment2.default.find(node);\n        }\n      }, {\n        key: 'import',\n        value: function _import(name) {\n          if (this.imports[name] == null) {\n            debug.error('Cannot import ' + name + '. Are you sure it was registered?');\n          }\n          return this.imports[name];\n        }\n      }, {\n        key: 'register',\n        value: function register(path, target) {\n          var _this = this;\n          var overwrite = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n          if (typeof path !== 'string') {\n            var name = path.attrName || path.blotName;\n            if (typeof name === 'string') {\n              // register(Blot | Attributor, overwrite)\n              this.register('formats/' + name, path, target);\n            } else {\n              Object.keys(path).forEach(function (key) {\n                _this.register(key, path[key], target);\n              });\n            }\n          } else {\n            if (this.imports[path] != null && !overwrite) {\n              debug.warn('Overwriting ' + path + ' with', target);\n            }\n            this.imports[path] = target;\n            if ((path.startsWith('blots/') || path.startsWith('formats/')) && target.blotName !== 'abstract') {\n              _parchment2.default.register(target);\n            } else if (path.startsWith('modules') && typeof target.register === 'function') {\n              target.register();\n            }\n          }\n        }\n      }]);\n      function Quill(container) {\n        var _this2 = this;\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        _classCallCheck(this, Quill);\n        this.options = expandConfig(container, options);\n        this.container = this.options.container;\n        if (this.container == null) {\n          return debug.error('Invalid Quill container', container);\n        }\n        if (this.options.debug) {\n          Quill.debug(this.options.debug);\n        }\n        var html = this.container.innerHTML.trim();\n        this.container.classList.add('ql-container');\n        this.container.innerHTML = '';\n        this.container.__quill = this;\n        this.root = this.addContainer('ql-editor');\n        this.root.classList.add('ql-blank');\n        this.root.setAttribute('data-gramm', false);\n        this.scrollingContainer = this.options.scrollingContainer || this.root;\n        this.emitter = new _emitter4.default();\n        this.scroll = _parchment2.default.create(this.root, {\n          emitter: this.emitter,\n          whitelist: this.options.formats\n        });\n        this.editor = new _editor2.default(this.scroll);\n        this.selection = new _selection2.default(this.scroll, this.emitter);\n        this.theme = new this.options.theme(this, this.options);\n        this.keyboard = this.theme.addModule('keyboard');\n        this.clipboard = this.theme.addModule('clipboard');\n        this.history = this.theme.addModule('history');\n        this.theme.init();\n        this.emitter.on(_emitter4.default.events.EDITOR_CHANGE, function (type) {\n          if (type === _emitter4.default.events.TEXT_CHANGE) {\n            _this2.root.classList.toggle('ql-blank', _this2.editor.isBlank());\n          }\n        });\n        this.emitter.on(_emitter4.default.events.SCROLL_UPDATE, function (source, mutations) {\n          var range = _this2.selection.lastRange;\n          var index = range && range.length === 0 ? range.index : undefined;\n          modify.call(_this2, function () {\n            return _this2.editor.update(null, mutations, index);\n          }, source);\n        });\n        var contents = this.clipboard.convert('<div class=\\'ql-editor\\' style=\"white-space: normal;\">' + html + '<p><br></p></div>');\n        this.setContents(contents);\n        this.history.clear();\n        if (this.options.placeholder) {\n          this.root.setAttribute('data-placeholder', this.options.placeholder);\n        }\n        if (this.options.readOnly) {\n          this.disable();\n        }\n      }\n      _createClass(Quill, [{\n        key: 'addContainer',\n        value: function addContainer(container) {\n          var refNode = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n          if (typeof container === 'string') {\n            var className = container;\n            container = document.createElement('div');\n            container.classList.add(className);\n          }\n          this.container.insertBefore(container, refNode);\n          return container;\n        }\n      }, {\n        key: 'blur',\n        value: function blur() {\n          this.selection.setRange(null);\n        }\n      }, {\n        key: 'deleteText',\n        value: function deleteText(index, length, source) {\n          var _this3 = this;\n          var _overload = overload(index, length, source);\n          var _overload2 = _slicedToArray(_overload, 4);\n          index = _overload2[0];\n          length = _overload2[1];\n          source = _overload2[3];\n          return modify.call(this, function () {\n            return _this3.editor.deleteText(index, length);\n          }, source, index, -1 * length);\n        }\n      }, {\n        key: 'disable',\n        value: function disable() {\n          this.enable(false);\n        }\n      }, {\n        key: 'enable',\n        value: function enable() {\n          var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n          this.scroll.enable(enabled);\n          this.container.classList.toggle('ql-disabled', !enabled);\n        }\n      }, {\n        key: 'focus',\n        value: function focus() {\n          var scrollTop = this.scrollingContainer.scrollTop;\n          this.selection.focus();\n          this.scrollingContainer.scrollTop = scrollTop;\n          this.scrollIntoView();\n        }\n      }, {\n        key: 'format',\n        value: function format(name, value) {\n          var _this4 = this;\n          var source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _emitter4.default.sources.API;\n          return modify.call(this, function () {\n            var range = _this4.getSelection(true);\n            var change = new _quillDelta2.default();\n            if (range == null) {\n              return change;\n            } else if (_parchment2.default.query(name, _parchment2.default.Scope.BLOCK)) {\n              change = _this4.editor.formatLine(range.index, range.length, _defineProperty({}, name, value));\n            } else if (range.length === 0) {\n              _this4.selection.format(name, value);\n              return change;\n            } else {\n              change = _this4.editor.formatText(range.index, range.length, _defineProperty({}, name, value));\n            }\n            _this4.setSelection(range, _emitter4.default.sources.SILENT);\n            return change;\n          }, source);\n        }\n      }, {\n        key: 'formatLine',\n        value: function formatLine(index, length, name, value, source) {\n          var _this5 = this;\n          var formats = void 0;\n          var _overload3 = overload(index, length, name, value, source);\n          var _overload4 = _slicedToArray(_overload3, 4);\n          index = _overload4[0];\n          length = _overload4[1];\n          formats = _overload4[2];\n          source = _overload4[3];\n          return modify.call(this, function () {\n            return _this5.editor.formatLine(index, length, formats);\n          }, source, index, 0);\n        }\n      }, {\n        key: 'formatText',\n        value: function formatText(index, length, name, value, source) {\n          var _this6 = this;\n          var formats = void 0;\n          var _overload5 = overload(index, length, name, value, source);\n          var _overload6 = _slicedToArray(_overload5, 4);\n          index = _overload6[0];\n          length = _overload6[1];\n          formats = _overload6[2];\n          source = _overload6[3];\n          return modify.call(this, function () {\n            return _this6.editor.formatText(index, length, formats);\n          }, source, index, 0);\n        }\n      }, {\n        key: 'getBounds',\n        value: function getBounds(index) {\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          var bounds = void 0;\n          if (typeof index === 'number') {\n            bounds = this.selection.getBounds(index, length);\n          } else {\n            bounds = this.selection.getBounds(index.index, index.length);\n          }\n          var containerBounds = this.container.getBoundingClientRect();\n          return {\n            bottom: bounds.bottom - containerBounds.top,\n            height: bounds.height,\n            left: bounds.left - containerBounds.left,\n            right: bounds.right - containerBounds.left,\n            top: bounds.top - containerBounds.top,\n            width: bounds.width\n          };\n        }\n      }, {\n        key: 'getContents',\n        value: function getContents() {\n          var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.getLength() - index;\n          var _overload7 = overload(index, length);\n          var _overload8 = _slicedToArray(_overload7, 2);\n          index = _overload8[0];\n          length = _overload8[1];\n          return this.editor.getContents(index, length);\n        }\n      }, {\n        key: 'getFormat',\n        value: function getFormat() {\n          var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.getSelection(true);\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          if (typeof index === 'number') {\n            return this.editor.getFormat(index, length);\n          } else {\n            return this.editor.getFormat(index.index, index.length);\n          }\n        }\n      }, {\n        key: 'getIndex',\n        value: function getIndex(blot) {\n          return blot.offset(this.scroll);\n        }\n      }, {\n        key: 'getLength',\n        value: function getLength() {\n          return this.scroll.length();\n        }\n      }, {\n        key: 'getLeaf',\n        value: function getLeaf(index) {\n          return this.scroll.leaf(index);\n        }\n      }, {\n        key: 'getLine',\n        value: function getLine(index) {\n          return this.scroll.line(index);\n        }\n      }, {\n        key: 'getLines',\n        value: function getLines() {\n          var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MAX_VALUE;\n          if (typeof index !== 'number') {\n            return this.scroll.lines(index.index, index.length);\n          } else {\n            return this.scroll.lines(index, length);\n          }\n        }\n      }, {\n        key: 'getModule',\n        value: function getModule(name) {\n          return this.theme.modules[name];\n        }\n      }, {\n        key: 'getSelection',\n        value: function getSelection() {\n          var focus = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n          if (focus) this.focus();\n          this.update(); // Make sure we access getRange with editor in consistent state\n          return this.selection.getRange()[0];\n        }\n      }, {\n        key: 'getText',\n        value: function getText() {\n          var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.getLength() - index;\n          var _overload9 = overload(index, length);\n          var _overload10 = _slicedToArray(_overload9, 2);\n          index = _overload10[0];\n          length = _overload10[1];\n          return this.editor.getText(index, length);\n        }\n      }, {\n        key: 'hasFocus',\n        value: function hasFocus() {\n          return this.selection.hasFocus();\n        }\n      }, {\n        key: 'insertEmbed',\n        value: function insertEmbed(index, embed, value) {\n          var _this7 = this;\n          var source = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Quill.sources.API;\n          return modify.call(this, function () {\n            return _this7.editor.insertEmbed(index, embed, value);\n          }, source, index);\n        }\n      }, {\n        key: 'insertText',\n        value: function insertText(index, text, name, value, source) {\n          var _this8 = this;\n          var formats = void 0;\n          var _overload11 = overload(index, 0, name, value, source);\n          var _overload12 = _slicedToArray(_overload11, 4);\n          index = _overload12[0];\n          formats = _overload12[2];\n          source = _overload12[3];\n          return modify.call(this, function () {\n            return _this8.editor.insertText(index, text, formats);\n          }, source, index, text.length);\n        }\n      }, {\n        key: 'isEnabled',\n        value: function isEnabled() {\n          return !this.container.classList.contains('ql-disabled');\n        }\n      }, {\n        key: 'off',\n        value: function off() {\n          return this.emitter.off.apply(this.emitter, arguments);\n        }\n      }, {\n        key: 'on',\n        value: function on() {\n          return this.emitter.on.apply(this.emitter, arguments);\n        }\n      }, {\n        key: 'once',\n        value: function once() {\n          return this.emitter.once.apply(this.emitter, arguments);\n        }\n      }, {\n        key: 'pasteHTML',\n        value: function pasteHTML(index, html, source) {\n          this.clipboard.dangerouslyPasteHTML(index, html, source);\n        }\n      }, {\n        key: 'removeFormat',\n        value: function removeFormat(index, length, source) {\n          var _this9 = this;\n          var _overload13 = overload(index, length, source);\n          var _overload14 = _slicedToArray(_overload13, 4);\n          index = _overload14[0];\n          length = _overload14[1];\n          source = _overload14[3];\n          return modify.call(this, function () {\n            return _this9.editor.removeFormat(index, length);\n          }, source, index);\n        }\n      }, {\n        key: 'scrollIntoView',\n        value: function scrollIntoView() {\n          this.selection.scrollIntoView(this.scrollingContainer);\n        }\n      }, {\n        key: 'setContents',\n        value: function setContents(delta) {\n          var _this10 = this;\n          var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _emitter4.default.sources.API;\n          return modify.call(this, function () {\n            delta = new _quillDelta2.default(delta);\n            var length = _this10.getLength();\n            var deleted = _this10.editor.deleteText(0, length);\n            var applied = _this10.editor.applyDelta(delta);\n            var lastOp = applied.ops[applied.ops.length - 1];\n            if (lastOp != null && typeof lastOp.insert === 'string' && lastOp.insert[lastOp.insert.length - 1] === '\\n') {\n              _this10.editor.deleteText(_this10.getLength() - 1, 1);\n              applied.delete(1);\n            }\n            var ret = deleted.compose(applied);\n            return ret;\n          }, source);\n        }\n      }, {\n        key: 'setSelection',\n        value: function setSelection(index, length, source) {\n          if (index == null) {\n            this.selection.setRange(null, length || Quill.sources.API);\n          } else {\n            var _overload15 = overload(index, length, source);\n            var _overload16 = _slicedToArray(_overload15, 4);\n            index = _overload16[0];\n            length = _overload16[1];\n            source = _overload16[3];\n            this.selection.setRange(new _selection.Range(index, length), source);\n            if (source !== _emitter4.default.sources.SILENT) {\n              this.selection.scrollIntoView(this.scrollingContainer);\n            }\n          }\n        }\n      }, {\n        key: 'setText',\n        value: function setText(text) {\n          var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _emitter4.default.sources.API;\n          var delta = new _quillDelta2.default().insert(text);\n          return this.setContents(delta, source);\n        }\n      }, {\n        key: 'update',\n        value: function update() {\n          var source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _emitter4.default.sources.USER;\n          var change = this.scroll.update(source); // Will update selection before selection.update() does if text changes\n          this.selection.update(source);\n          return change;\n        }\n      }, {\n        key: 'updateContents',\n        value: function updateContents(delta) {\n          var _this11 = this;\n          var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _emitter4.default.sources.API;\n          return modify.call(this, function () {\n            delta = new _quillDelta2.default(delta);\n            return _this11.editor.applyDelta(delta, source);\n          }, source, true);\n        }\n      }]);\n      return Quill;\n    }();\n    Quill.DEFAULTS = {\n      bounds: null,\n      formats: null,\n      modules: {},\n      placeholder: '',\n      readOnly: false,\n      scrollingContainer: null,\n      strict: true,\n      theme: 'default'\n    };\n    Quill.events = _emitter4.default.events;\n    Quill.sources = _emitter4.default.sources;\n    // eslint-disable-next-line no-undef\n    Quill.version = false ? 'dev' : \"1.3.7\";\n    Quill.imports = {\n      'delta': _quillDelta2.default,\n      'parchment': _parchment2.default,\n      'core/module': _module2.default,\n      'core/theme': _theme2.default\n    };\n    function expandConfig(container, userConfig) {\n      userConfig = (0, _extend2.default)(true, {\n        container: container,\n        modules: {\n          clipboard: true,\n          keyboard: true,\n          history: true\n        }\n      }, userConfig);\n      if (!userConfig.theme || userConfig.theme === Quill.DEFAULTS.theme) {\n        userConfig.theme = _theme2.default;\n      } else {\n        userConfig.theme = Quill.import('themes/' + userConfig.theme);\n        if (userConfig.theme == null) {\n          throw new Error('Invalid theme ' + userConfig.theme + '. Did you register it?');\n        }\n      }\n      var themeConfig = (0, _extend2.default)(true, {}, userConfig.theme.DEFAULTS);\n      [themeConfig, userConfig].forEach(function (config) {\n        config.modules = config.modules || {};\n        Object.keys(config.modules).forEach(function (module) {\n          if (config.modules[module] === true) {\n            config.modules[module] = {};\n          }\n        });\n      });\n      var moduleNames = Object.keys(themeConfig.modules).concat(Object.keys(userConfig.modules));\n      var moduleConfig = moduleNames.reduce(function (config, name) {\n        var moduleClass = Quill.import('modules/' + name);\n        if (moduleClass == null) {\n          debug.error('Cannot load ' + name + ' module. Are you sure you registered it?');\n        } else {\n          config[name] = moduleClass.DEFAULTS || {};\n        }\n        return config;\n      }, {});\n      // Special case toolbar shorthand\n      if (userConfig.modules != null && userConfig.modules.toolbar && userConfig.modules.toolbar.constructor !== Object) {\n        userConfig.modules.toolbar = {\n          container: userConfig.modules.toolbar\n        };\n      }\n      userConfig = (0, _extend2.default)(true, {}, Quill.DEFAULTS, {\n        modules: moduleConfig\n      }, themeConfig, userConfig);\n      ['bounds', 'container', 'scrollingContainer'].forEach(function (key) {\n        if (typeof userConfig[key] === 'string') {\n          userConfig[key] = document.querySelector(userConfig[key]);\n        }\n      });\n      userConfig.modules = Object.keys(userConfig.modules).reduce(function (config, name) {\n        if (userConfig.modules[name]) {\n          config[name] = userConfig.modules[name];\n        }\n        return config;\n      }, {});\n      return userConfig;\n    }\n\n    // Handle selection preservation and TEXT_CHANGE emission\n    // common to modification APIs\n    function modify(modifier, source, index, shift) {\n      if (this.options.strict && !this.isEnabled() && source === _emitter4.default.sources.USER) {\n        return new _quillDelta2.default();\n      }\n      var range = index == null ? null : this.getSelection();\n      var oldDelta = this.editor.delta;\n      var change = modifier();\n      if (range != null) {\n        if (index === true) index = range.index;\n        if (shift == null) {\n          range = shiftRange(range, change, source);\n        } else if (shift !== 0) {\n          range = shiftRange(range, index, shift, source);\n        }\n        this.setSelection(range, _emitter4.default.sources.SILENT);\n      }\n      if (change.length() > 0) {\n        var _emitter;\n        var args = [_emitter4.default.events.TEXT_CHANGE, change, oldDelta, source];\n        (_emitter = this.emitter).emit.apply(_emitter, [_emitter4.default.events.EDITOR_CHANGE].concat(args));\n        if (source !== _emitter4.default.sources.SILENT) {\n          var _emitter2;\n          (_emitter2 = this.emitter).emit.apply(_emitter2, args);\n        }\n      }\n      return change;\n    }\n    function overload(index, length, name, value, source) {\n      var formats = {};\n      if (typeof index.index === 'number' && typeof index.length === 'number') {\n        // Allow for throwaway end (used by insertText/insertEmbed)\n        if (typeof length !== 'number') {\n          source = value, value = name, name = length, length = index.length, index = index.index;\n        } else {\n          length = index.length, index = index.index;\n        }\n      } else if (typeof length !== 'number') {\n        source = value, value = name, name = length, length = 0;\n      }\n      // Handle format being object, two format name/value strings or excluded\n      if ((typeof name === 'undefined' ? 'undefined' : _typeof(name)) === 'object') {\n        formats = name;\n        source = value;\n      } else if (typeof name === 'string') {\n        if (value != null) {\n          formats[name] = value;\n        } else {\n          source = name;\n        }\n      }\n      // Handle optional source\n      source = source || _emitter4.default.sources.API;\n      return [index, length, formats, source];\n    }\n    function shiftRange(range, index, length, source) {\n      if (range == null) return null;\n      var start = void 0,\n        end = void 0;\n      if (index instanceof _quillDelta2.default) {\n        var _map = [range.index, range.index + range.length].map(function (pos) {\n          return index.transformPosition(pos, source !== _emitter4.default.sources.USER);\n        });\n        var _map2 = _slicedToArray(_map, 2);\n        start = _map2[0];\n        end = _map2[1];\n      } else {\n        var _map3 = [range.index, range.index + range.length].map(function (pos) {\n          if (pos < index || pos === index && source === _emitter4.default.sources.USER) return pos;\n          if (length >= 0) {\n            return pos + length;\n          } else {\n            return Math.max(index, pos + length);\n          }\n        });\n        var _map4 = _slicedToArray(_map3, 2);\n        start = _map4[0];\n        end = _map4[1];\n      }\n      return new _selection.Range(start, end - start);\n    }\n    exports.expandConfig = expandConfig;\n    exports.overload = overload;\n    exports.default = Quill;\n\n    /***/\n  }), ( /* 6 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Inline = function (_Parchment$Inline) {\n      _inherits(Inline, _Parchment$Inline);\n      function Inline() {\n        _classCallCheck(this, Inline);\n        return _possibleConstructorReturn(this, (Inline.__proto__ || Object.getPrototypeOf(Inline)).apply(this, arguments));\n      }\n      _createClass(Inline, [{\n        key: 'formatAt',\n        value: function formatAt(index, length, name, value) {\n          if (Inline.compare(this.statics.blotName, name) < 0 && _parchment2.default.query(name, _parchment2.default.Scope.BLOT)) {\n            var blot = this.isolate(index, length);\n            if (value) {\n              blot.wrap(name, value);\n            }\n          } else {\n            _get(Inline.prototype.__proto__ || Object.getPrototypeOf(Inline.prototype), 'formatAt', this).call(this, index, length, name, value);\n          }\n        }\n      }, {\n        key: 'optimize',\n        value: function optimize(context) {\n          _get(Inline.prototype.__proto__ || Object.getPrototypeOf(Inline.prototype), 'optimize', this).call(this, context);\n          if (this.parent instanceof Inline && Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0) {\n            var parent = this.parent.isolate(this.offset(), this.length());\n            this.moveChildren(parent);\n            parent.wrap(this);\n          }\n        }\n      }], [{\n        key: 'compare',\n        value: function compare(self, other) {\n          var selfIndex = Inline.order.indexOf(self);\n          var otherIndex = Inline.order.indexOf(other);\n          if (selfIndex >= 0 || otherIndex >= 0) {\n            return selfIndex - otherIndex;\n          } else if (self === other) {\n            return 0;\n          } else if (self < other) {\n            return -1;\n          } else {\n            return 1;\n          }\n        }\n      }]);\n      return Inline;\n    }(_parchment2.default.Inline);\n    Inline.allowedChildren = [Inline, _parchment2.default.Embed, _text2.default];\n    // Lower index means deeper in the DOM tree, since not found (-1) is for embeds\n    Inline.order = ['cursor', 'inline',\n    // Must be lower\n    'underline', 'strike', 'italic', 'bold', 'script', 'link', 'code' // Must be higher\n    ];\n    exports.default = Inline;\n\n    /***/\n  }), ( /* 7 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var TextBlot = function (_Parchment$Text) {\n      _inherits(TextBlot, _Parchment$Text);\n      function TextBlot() {\n        _classCallCheck(this, TextBlot);\n        return _possibleConstructorReturn(this, (TextBlot.__proto__ || Object.getPrototypeOf(TextBlot)).apply(this, arguments));\n      }\n      return TextBlot;\n    }(_parchment2.default.Text);\n    exports.default = TextBlot;\n\n    /***/\n  }), ( /* 8 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _eventemitter = __webpack_require__(54);\n    var _eventemitter2 = _interopRequireDefault(_eventemitter);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var debug = (0, _logger2.default)('quill:events');\n    var EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\n    EVENTS.forEach(function (eventName) {\n      document.addEventListener(eventName, function () {\n        for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        [].slice.call(document.querySelectorAll('.ql-container')).forEach(function (node) {\n          // TODO use WeakMap\n          if (node.__quill && node.__quill.emitter) {\n            var _node$__quill$emitter;\n            (_node$__quill$emitter = node.__quill.emitter).handleDOM.apply(_node$__quill$emitter, args);\n          }\n        });\n      });\n    });\n    var Emitter = function (_EventEmitter) {\n      _inherits(Emitter, _EventEmitter);\n      function Emitter() {\n        _classCallCheck(this, Emitter);\n        var _this = _possibleConstructorReturn(this, (Emitter.__proto__ || Object.getPrototypeOf(Emitter)).call(this));\n        _this.listeners = {};\n        _this.on('error', debug.error);\n        return _this;\n      }\n      _createClass(Emitter, [{\n        key: 'emit',\n        value: function emit() {\n          debug.log.apply(debug, arguments);\n          _get(Emitter.prototype.__proto__ || Object.getPrototypeOf(Emitter.prototype), 'emit', this).apply(this, arguments);\n        }\n      }, {\n        key: 'handleDOM',\n        value: function handleDOM(event) {\n          for (var _len2 = arguments.length, args = Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n          (this.listeners[event.type] || []).forEach(function (_ref) {\n            var node = _ref.node,\n              handler = _ref.handler;\n            if (event.target === node || node.contains(event.target)) {\n              handler.apply(undefined, [event].concat(args));\n            }\n          });\n        }\n      }, {\n        key: 'listenDOM',\n        value: function listenDOM(eventName, node, handler) {\n          if (!this.listeners[eventName]) {\n            this.listeners[eventName] = [];\n          }\n          this.listeners[eventName].push({\n            node: node,\n            handler: handler\n          });\n        }\n      }]);\n      return Emitter;\n    }(_eventemitter2.default);\n    Emitter.events = {\n      EDITOR_CHANGE: 'editor-change',\n      SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n      SCROLL_OPTIMIZE: 'scroll-optimize',\n      SCROLL_UPDATE: 'scroll-update',\n      SELECTION_CHANGE: 'selection-change',\n      TEXT_CHANGE: 'text-change'\n    };\n    Emitter.sources = {\n      API: 'api',\n      SILENT: 'silent',\n      USER: 'user'\n    };\n    exports.default = Emitter;\n\n    /***/\n  }), ( /* 9 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var Module = function Module(quill) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      _classCallCheck(this, Module);\n      this.quill = quill;\n      this.options = options;\n    };\n    Module.DEFAULTS = {};\n    exports.default = Module;\n\n    /***/\n  }), ( /* 10 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var levels = ['error', 'warn', 'log', 'info'];\n    var level = 'warn';\n    function debug(method) {\n      if (levels.indexOf(method) <= levels.indexOf(level)) {\n        var _console;\n        for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        (_console = console)[method].apply(_console, args); // eslint-disable-line no-console\n      }\n    }\n    function namespace(ns) {\n      return levels.reduce(function (logger, method) {\n        logger[method] = debug.bind(console, method, ns);\n        return logger;\n      }, {});\n    }\n    debug.level = namespace.level = function (newLevel) {\n      level = newLevel;\n    };\n    exports.default = namespace;\n\n    /***/\n  }), ( /* 11 */\n  /***/function (module, exports, __webpack_require__) {\n    var pSlice = Array.prototype.slice;\n    var objectKeys = __webpack_require__(52);\n    var isArguments = __webpack_require__(53);\n    var deepEqual = module.exports = function (actual, expected, opts) {\n      if (!opts) opts = {};\n      // 7.1. All identical values are equivalent, as determined by ===.\n      if (actual === expected) {\n        return true;\n      } else if (actual instanceof Date && expected instanceof Date) {\n        return actual.getTime() === expected.getTime();\n\n        // 7.3. Other pairs that do not both pass typeof value == 'object',\n        // equivalence is determined by ==.\n      } else if (!actual || !expected || typeof actual != 'object' && typeof expected != 'object') {\n        return opts.strict ? actual === expected : actual == expected;\n\n        // 7.4. For all other Object pairs, including Array objects, equivalence is\n        // determined by having the same number of owned properties (as verified\n        // with Object.prototype.hasOwnProperty.call), the same set of keys\n        // (although not necessarily the same order), equivalent values for every\n        // corresponding key, and an identical 'prototype' property. Note: this\n        // accounts for both named and indexed properties on Arrays.\n      } else {\n        return objEquiv(actual, expected, opts);\n      }\n    };\n    function isUndefinedOrNull(value) {\n      return value === null || value === undefined;\n    }\n    function isBuffer(x) {\n      if (!x || typeof x !== 'object' || typeof x.length !== 'number') return false;\n      if (typeof x.copy !== 'function' || typeof x.slice !== 'function') {\n        return false;\n      }\n      if (x.length > 0 && typeof x[0] !== 'number') return false;\n      return true;\n    }\n    function objEquiv(a, b, opts) {\n      var i, key;\n      if (isUndefinedOrNull(a) || isUndefinedOrNull(b)) return false;\n      // an identical 'prototype' property.\n      if (a.prototype !== b.prototype) return false;\n      //~~~I've managed to break Object.keys through screwy arguments passing.\n      //   Converting to array solves the problem.\n      if (isArguments(a)) {\n        if (!isArguments(b)) {\n          return false;\n        }\n        a = pSlice.call(a);\n        b = pSlice.call(b);\n        return deepEqual(a, b, opts);\n      }\n      if (isBuffer(a)) {\n        if (!isBuffer(b)) {\n          return false;\n        }\n        if (a.length !== b.length) return false;\n        for (i = 0; i < a.length; i++) {\n          if (a[i] !== b[i]) return false;\n        }\n        return true;\n      }\n      try {\n        var ka = objectKeys(a),\n          kb = objectKeys(b);\n      } catch (e) {\n        //happens when one is a string literal and the other isn't\n        return false;\n      }\n      // having the same number of owned properties (keys incorporates\n      // hasOwnProperty)\n      if (ka.length != kb.length) return false;\n      //the same set of keys (although not necessarily the same order),\n      ka.sort();\n      kb.sort();\n      //~~~cheap key test\n      for (i = ka.length - 1; i >= 0; i--) {\n        if (ka[i] != kb[i]) return false;\n      }\n      //equivalent values for every corresponding key, and\n      //~~~possibly expensive deep test\n      for (i = ka.length - 1; i >= 0; i--) {\n        key = ka[i];\n        if (!deepEqual(a[key], b[key], opts)) return false;\n      }\n      return typeof a === typeof b;\n    }\n\n    /***/\n  }), ( /* 12 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var Registry = __webpack_require__(1);\n    var Attributor = /** @class */function () {\n      function Attributor(attrName, keyName, options) {\n        if (options === void 0) {\n          options = {};\n        }\n        this.attrName = attrName;\n        this.keyName = keyName;\n        var attributeBit = Registry.Scope.TYPE & Registry.Scope.ATTRIBUTE;\n        if (options.scope != null) {\n          // Ignore type bits, force attribute bit\n          this.scope = options.scope & Registry.Scope.LEVEL | attributeBit;\n        } else {\n          this.scope = Registry.Scope.ATTRIBUTE;\n        }\n        if (options.whitelist != null) this.whitelist = options.whitelist;\n      }\n      Attributor.keys = function (node) {\n        return [].map.call(node.attributes, function (item) {\n          return item.name;\n        });\n      };\n      Attributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value)) return false;\n        node.setAttribute(this.keyName, value);\n        return true;\n      };\n      Attributor.prototype.canAdd = function (node, value) {\n        var match = Registry.query(node, Registry.Scope.BLOT & (this.scope | Registry.Scope.TYPE));\n        if (match == null) return false;\n        if (this.whitelist == null) return true;\n        if (typeof value === 'string') {\n          return this.whitelist.indexOf(value.replace(/[\"']/g, '')) > -1;\n        } else {\n          return this.whitelist.indexOf(value) > -1;\n        }\n      };\n      Attributor.prototype.remove = function (node) {\n        node.removeAttribute(this.keyName);\n      };\n      Attributor.prototype.value = function (node) {\n        var value = node.getAttribute(this.keyName);\n        if (this.canAdd(node, value) && value) {\n          return value;\n        }\n        return '';\n      };\n      return Attributor;\n    }();\n    exports.default = Attributor;\n\n    /***/\n  }), ( /* 13 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.Code = undefined;\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Code = function (_Inline) {\n      _inherits(Code, _Inline);\n      function Code() {\n        _classCallCheck(this, Code);\n        return _possibleConstructorReturn(this, (Code.__proto__ || Object.getPrototypeOf(Code)).apply(this, arguments));\n      }\n      return Code;\n    }(_inline2.default);\n    Code.blotName = 'code';\n    Code.tagName = 'CODE';\n    var CodeBlock = function (_Block) {\n      _inherits(CodeBlock, _Block);\n      function CodeBlock() {\n        _classCallCheck(this, CodeBlock);\n        return _possibleConstructorReturn(this, (CodeBlock.__proto__ || Object.getPrototypeOf(CodeBlock)).apply(this, arguments));\n      }\n      _createClass(CodeBlock, [{\n        key: 'delta',\n        value: function delta() {\n          var _this3 = this;\n          var text = this.domNode.textContent;\n          if (text.endsWith('\\n')) {\n            // Should always be true\n            text = text.slice(0, -1);\n          }\n          return text.split('\\n').reduce(function (delta, frag) {\n            return delta.insert(frag).insert('\\n', _this3.formats());\n          }, new _quillDelta2.default());\n        }\n      }, {\n        key: 'format',\n        value: function format(name, value) {\n          if (name === this.statics.blotName && value) return;\n          var _descendant = this.descendant(_text2.default, this.length() - 1),\n            _descendant2 = _slicedToArray(_descendant, 1),\n            text = _descendant2[0];\n          if (text != null) {\n            text.deleteAt(text.length() - 1, 1);\n          }\n          _get(CodeBlock.prototype.__proto__ || Object.getPrototypeOf(CodeBlock.prototype), 'format', this).call(this, name, value);\n        }\n      }, {\n        key: 'formatAt',\n        value: function formatAt(index, length, name, value) {\n          if (length === 0) return;\n          if (_parchment2.default.query(name, _parchment2.default.Scope.BLOCK) == null || name === this.statics.blotName && value === this.statics.formats(this.domNode)) {\n            return;\n          }\n          var nextNewline = this.newlineIndex(index);\n          if (nextNewline < 0 || nextNewline >= index + length) return;\n          var prevNewline = this.newlineIndex(index, true) + 1;\n          var isolateLength = nextNewline - prevNewline + 1;\n          var blot = this.isolate(prevNewline, isolateLength);\n          var next = blot.next;\n          blot.format(name, value);\n          if (next instanceof CodeBlock) {\n            next.formatAt(0, index - prevNewline + length - isolateLength, name, value);\n          }\n        }\n      }, {\n        key: 'insertAt',\n        value: function insertAt(index, value, def) {\n          if (def != null) return;\n          var _descendant3 = this.descendant(_text2.default, index),\n            _descendant4 = _slicedToArray(_descendant3, 2),\n            text = _descendant4[0],\n            offset = _descendant4[1];\n          text.insertAt(offset, value);\n        }\n      }, {\n        key: 'length',\n        value: function length() {\n          var length = this.domNode.textContent.length;\n          if (!this.domNode.textContent.endsWith('\\n')) {\n            return length + 1;\n          }\n          return length;\n        }\n      }, {\n        key: 'newlineIndex',\n        value: function newlineIndex(searchIndex) {\n          var reverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n          if (!reverse) {\n            var offset = this.domNode.textContent.slice(searchIndex).indexOf('\\n');\n            return offset > -1 ? searchIndex + offset : -1;\n          } else {\n            return this.domNode.textContent.slice(0, searchIndex).lastIndexOf('\\n');\n          }\n        }\n      }, {\n        key: 'optimize',\n        value: function optimize(context) {\n          if (!this.domNode.textContent.endsWith('\\n')) {\n            this.appendChild(_parchment2.default.create('text', '\\n'));\n          }\n          _get(CodeBlock.prototype.__proto__ || Object.getPrototypeOf(CodeBlock.prototype), 'optimize', this).call(this, context);\n          var next = this.next;\n          if (next != null && next.prev === this && next.statics.blotName === this.statics.blotName && this.statics.formats(this.domNode) === next.statics.formats(next.domNode)) {\n            next.optimize(context);\n            next.moveChildren(this);\n            next.remove();\n          }\n        }\n      }, {\n        key: 'replace',\n        value: function replace(target) {\n          _get(CodeBlock.prototype.__proto__ || Object.getPrototypeOf(CodeBlock.prototype), 'replace', this).call(this, target);\n          [].slice.call(this.domNode.querySelectorAll('*')).forEach(function (node) {\n            var blot = _parchment2.default.find(node);\n            if (blot == null) {\n              node.parentNode.removeChild(node);\n            } else if (blot instanceof _parchment2.default.Embed) {\n              blot.remove();\n            } else {\n              blot.unwrap();\n            }\n          });\n        }\n      }], [{\n        key: 'create',\n        value: function create(value) {\n          var domNode = _get(CodeBlock.__proto__ || Object.getPrototypeOf(CodeBlock), 'create', this).call(this, value);\n          domNode.setAttribute('spellcheck', false);\n          return domNode;\n        }\n      }, {\n        key: 'formats',\n        value: function formats() {\n          return true;\n        }\n      }]);\n      return CodeBlock;\n    }(_block2.default);\n    CodeBlock.blotName = 'code-block';\n    CodeBlock.tagName = 'PRE';\n    CodeBlock.TAB = '  ';\n    exports.Code = Code;\n    exports.default = CodeBlock;\n\n    /***/\n  }), ( /* 14 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _op = __webpack_require__(20);\n    var _op2 = _interopRequireDefault(_op);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _code = __webpack_require__(13);\n    var _code2 = _interopRequireDefault(_code);\n    var _cursor = __webpack_require__(24);\n    var _cursor2 = _interopRequireDefault(_cursor);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    var _break = __webpack_require__(16);\n    var _break2 = _interopRequireDefault(_break);\n    var _clone = __webpack_require__(21);\n    var _clone2 = _interopRequireDefault(_clone);\n    var _deepEqual = __webpack_require__(11);\n    var _deepEqual2 = _interopRequireDefault(_deepEqual);\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var ASCII = /^[ -~]*$/;\n    var Editor = function () {\n      function Editor(scroll) {\n        _classCallCheck(this, Editor);\n        this.scroll = scroll;\n        this.delta = this.getDelta();\n      }\n      _createClass(Editor, [{\n        key: 'applyDelta',\n        value: function applyDelta(delta) {\n          var _this = this;\n          var consumeNextNewline = false;\n          this.scroll.update();\n          var scrollLength = this.scroll.length();\n          this.scroll.batchStart();\n          delta = normalizeDelta(delta);\n          delta.reduce(function (index, op) {\n            var length = op.retain || op.delete || op.insert.length || 1;\n            var attributes = op.attributes || {};\n            if (op.insert != null) {\n              if (typeof op.insert === 'string') {\n                var text = op.insert;\n                if (text.endsWith('\\n') && consumeNextNewline) {\n                  consumeNextNewline = false;\n                  text = text.slice(0, -1);\n                }\n                if (index >= scrollLength && !text.endsWith('\\n')) {\n                  consumeNextNewline = true;\n                }\n                _this.scroll.insertAt(index, text);\n                var _scroll$line = _this.scroll.line(index),\n                  _scroll$line2 = _slicedToArray(_scroll$line, 2),\n                  line = _scroll$line2[0],\n                  offset = _scroll$line2[1];\n                var formats = (0, _extend2.default)({}, (0, _block.bubbleFormats)(line));\n                if (line instanceof _block2.default) {\n                  var _line$descendant = line.descendant(_parchment2.default.Leaf, offset),\n                    _line$descendant2 = _slicedToArray(_line$descendant, 1),\n                    leaf = _line$descendant2[0];\n                  formats = (0, _extend2.default)(formats, (0, _block.bubbleFormats)(leaf));\n                }\n                attributes = _op2.default.attributes.diff(formats, attributes) || {};\n              } else if (_typeof(op.insert) === 'object') {\n                var key = Object.keys(op.insert)[0]; // There should only be one key\n                if (key == null) return index;\n                _this.scroll.insertAt(index, key, op.insert[key]);\n              }\n              scrollLength += length;\n            }\n            Object.keys(attributes).forEach(function (name) {\n              _this.scroll.formatAt(index, length, name, attributes[name]);\n            });\n            return index + length;\n          }, 0);\n          delta.reduce(function (index, op) {\n            if (typeof op.delete === 'number') {\n              _this.scroll.deleteAt(index, op.delete);\n              return index;\n            }\n            return index + (op.retain || op.insert.length || 1);\n          }, 0);\n          this.scroll.batchEnd();\n          return this.update(delta);\n        }\n      }, {\n        key: 'deleteText',\n        value: function deleteText(index, length) {\n          this.scroll.deleteAt(index, length);\n          return this.update(new _quillDelta2.default().retain(index).delete(length));\n        }\n      }, {\n        key: 'formatLine',\n        value: function formatLine(index, length) {\n          var _this2 = this;\n          var formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n          this.scroll.update();\n          Object.keys(formats).forEach(function (format) {\n            if (_this2.scroll.whitelist != null && !_this2.scroll.whitelist[format]) return;\n            var lines = _this2.scroll.lines(index, Math.max(length, 1));\n            var lengthRemaining = length;\n            lines.forEach(function (line) {\n              var lineLength = line.length();\n              if (!(line instanceof _code2.default)) {\n                line.format(format, formats[format]);\n              } else {\n                var codeIndex = index - line.offset(_this2.scroll);\n                var codeLength = line.newlineIndex(codeIndex + lengthRemaining) - codeIndex + 1;\n                line.formatAt(codeIndex, codeLength, format, formats[format]);\n              }\n              lengthRemaining -= lineLength;\n            });\n          });\n          this.scroll.optimize();\n          return this.update(new _quillDelta2.default().retain(index).retain(length, (0, _clone2.default)(formats)));\n        }\n      }, {\n        key: 'formatText',\n        value: function formatText(index, length) {\n          var _this3 = this;\n          var formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n          Object.keys(formats).forEach(function (format) {\n            _this3.scroll.formatAt(index, length, format, formats[format]);\n          });\n          return this.update(new _quillDelta2.default().retain(index).retain(length, (0, _clone2.default)(formats)));\n        }\n      }, {\n        key: 'getContents',\n        value: function getContents(index, length) {\n          return this.delta.slice(index, index + length);\n        }\n      }, {\n        key: 'getDelta',\n        value: function getDelta() {\n          return this.scroll.lines().reduce(function (delta, line) {\n            return delta.concat(line.delta());\n          }, new _quillDelta2.default());\n        }\n      }, {\n        key: 'getFormat',\n        value: function getFormat(index) {\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          var lines = [],\n            leaves = [];\n          if (length === 0) {\n            this.scroll.path(index).forEach(function (path) {\n              var _path = _slicedToArray(path, 1),\n                blot = _path[0];\n              if (blot instanceof _block2.default) {\n                lines.push(blot);\n              } else if (blot instanceof _parchment2.default.Leaf) {\n                leaves.push(blot);\n              }\n            });\n          } else {\n            lines = this.scroll.lines(index, length);\n            leaves = this.scroll.descendants(_parchment2.default.Leaf, index, length);\n          }\n          var formatsArr = [lines, leaves].map(function (blots) {\n            if (blots.length === 0) return {};\n            var formats = (0, _block.bubbleFormats)(blots.shift());\n            while (Object.keys(formats).length > 0) {\n              var blot = blots.shift();\n              if (blot == null) return formats;\n              formats = combineFormats((0, _block.bubbleFormats)(blot), formats);\n            }\n            return formats;\n          });\n          return _extend2.default.apply(_extend2.default, formatsArr);\n        }\n      }, {\n        key: 'getText',\n        value: function getText(index, length) {\n          return this.getContents(index, length).filter(function (op) {\n            return typeof op.insert === 'string';\n          }).map(function (op) {\n            return op.insert;\n          }).join('');\n        }\n      }, {\n        key: 'insertEmbed',\n        value: function insertEmbed(index, embed, value) {\n          this.scroll.insertAt(index, embed, value);\n          return this.update(new _quillDelta2.default().retain(index).insert(_defineProperty({}, embed, value)));\n        }\n      }, {\n        key: 'insertText',\n        value: function insertText(index, text) {\n          var _this4 = this;\n          var formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n          text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n          this.scroll.insertAt(index, text);\n          Object.keys(formats).forEach(function (format) {\n            _this4.scroll.formatAt(index, text.length, format, formats[format]);\n          });\n          return this.update(new _quillDelta2.default().retain(index).insert(text, (0, _clone2.default)(formats)));\n        }\n      }, {\n        key: 'isBlank',\n        value: function isBlank() {\n          if (this.scroll.children.length == 0) return true;\n          if (this.scroll.children.length > 1) return false;\n          var block = this.scroll.children.head;\n          if (block.statics.blotName !== _block2.default.blotName) return false;\n          if (block.children.length > 1) return false;\n          return block.children.head instanceof _break2.default;\n        }\n      }, {\n        key: 'removeFormat',\n        value: function removeFormat(index, length) {\n          var text = this.getText(index, length);\n          var _scroll$line3 = this.scroll.line(index + length),\n            _scroll$line4 = _slicedToArray(_scroll$line3, 2),\n            line = _scroll$line4[0],\n            offset = _scroll$line4[1];\n          var suffixLength = 0,\n            suffix = new _quillDelta2.default();\n          if (line != null) {\n            if (!(line instanceof _code2.default)) {\n              suffixLength = line.length() - offset;\n            } else {\n              suffixLength = line.newlineIndex(offset) - offset + 1;\n            }\n            suffix = line.delta().slice(offset, offset + suffixLength - 1).insert('\\n');\n          }\n          var contents = this.getContents(index, length + suffixLength);\n          var diff = contents.diff(new _quillDelta2.default().insert(text).concat(suffix));\n          var delta = new _quillDelta2.default().retain(index).concat(diff);\n          return this.applyDelta(delta);\n        }\n      }, {\n        key: 'update',\n        value: function update(change) {\n          var mutations = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n          var cursorIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;\n          var oldDelta = this.delta;\n          if (mutations.length === 1 && mutations[0].type === 'characterData' && mutations[0].target.data.match(ASCII) && _parchment2.default.find(mutations[0].target)) {\n            // Optimization for character changes\n            var textBlot = _parchment2.default.find(mutations[0].target);\n            var formats = (0, _block.bubbleFormats)(textBlot);\n            var index = textBlot.offset(this.scroll);\n            var oldValue = mutations[0].oldValue.replace(_cursor2.default.CONTENTS, '');\n            var oldText = new _quillDelta2.default().insert(oldValue);\n            var newText = new _quillDelta2.default().insert(textBlot.value());\n            var diffDelta = new _quillDelta2.default().retain(index).concat(oldText.diff(newText, cursorIndex));\n            change = diffDelta.reduce(function (delta, op) {\n              if (op.insert) {\n                return delta.insert(op.insert, formats);\n              } else {\n                return delta.push(op);\n              }\n            }, new _quillDelta2.default());\n            this.delta = oldDelta.compose(change);\n          } else {\n            this.delta = this.getDelta();\n            if (!change || !(0, _deepEqual2.default)(oldDelta.compose(change), this.delta)) {\n              change = oldDelta.diff(this.delta, cursorIndex);\n            }\n          }\n          return change;\n        }\n      }]);\n      return Editor;\n    }();\n    function combineFormats(formats, combined) {\n      return Object.keys(combined).reduce(function (merged, name) {\n        if (formats[name] == null) return merged;\n        if (combined[name] === formats[name]) {\n          merged[name] = combined[name];\n        } else if (Array.isArray(combined[name])) {\n          if (combined[name].indexOf(formats[name]) < 0) {\n            merged[name] = combined[name].concat([formats[name]]);\n          }\n        } else {\n          merged[name] = [combined[name], formats[name]];\n        }\n        return merged;\n      }, {});\n    }\n    function normalizeDelta(delta) {\n      return delta.reduce(function (delta, op) {\n        if (op.insert === 1) {\n          var attributes = (0, _clone2.default)(op.attributes);\n          delete attributes['image'];\n          return delta.insert({\n            image: op.attributes.image\n          }, attributes);\n        }\n        if (op.attributes != null && (op.attributes.list === true || op.attributes.bullet === true)) {\n          op = (0, _clone2.default)(op);\n          if (op.attributes.list) {\n            op.attributes.list = 'ordered';\n          } else {\n            op.attributes.list = 'bullet';\n            delete op.attributes.bullet;\n          }\n        }\n        if (typeof op.insert === 'string') {\n          var text = op.insert.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n          return delta.insert(text, op.attributes);\n        }\n        return delta.push(op);\n      }, new _quillDelta2.default());\n    }\n    exports.default = Editor;\n\n    /***/\n  }), ( /* 15 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.Range = undefined;\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _clone = __webpack_require__(21);\n    var _clone2 = _interopRequireDefault(_clone);\n    var _deepEqual = __webpack_require__(11);\n    var _deepEqual2 = _interopRequireDefault(_deepEqual);\n    var _emitter3 = __webpack_require__(8);\n    var _emitter4 = _interopRequireDefault(_emitter3);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _toConsumableArray(arr) {\n      if (Array.isArray(arr)) {\n        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      } else {\n        return Array.from(arr);\n      }\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var debug = (0, _logger2.default)('quill:selection');\n    var Range = function Range(index) {\n      var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      _classCallCheck(this, Range);\n      this.index = index;\n      this.length = length;\n    };\n    var Selection = function () {\n      function Selection(scroll, emitter) {\n        var _this = this;\n        _classCallCheck(this, Selection);\n        this.emitter = emitter;\n        this.scroll = scroll;\n        this.composing = false;\n        this.mouseDown = false;\n        this.root = this.scroll.domNode;\n        this.cursor = _parchment2.default.create('cursor', this);\n        // savedRange is last non-null range\n        this.lastRange = this.savedRange = new Range(0, 0);\n        this.handleComposition();\n        this.handleDragging();\n        this.emitter.listenDOM('selectionchange', document, function () {\n          if (!_this.mouseDown) {\n            setTimeout(_this.update.bind(_this, _emitter4.default.sources.USER), 1);\n          }\n        });\n        this.emitter.on(_emitter4.default.events.EDITOR_CHANGE, function (type, delta) {\n          if (type === _emitter4.default.events.TEXT_CHANGE && delta.length() > 0) {\n            _this.update(_emitter4.default.sources.SILENT);\n          }\n        });\n        this.emitter.on(_emitter4.default.events.SCROLL_BEFORE_UPDATE, function () {\n          if (!_this.hasFocus()) return;\n          var native = _this.getNativeRange();\n          if (native == null) return;\n          if (native.start.node === _this.cursor.textNode) return; // cursor.restore() will handle\n          // TODO unclear if this has negative side effects\n          _this.emitter.once(_emitter4.default.events.SCROLL_UPDATE, function () {\n            try {\n              _this.setNativeRange(native.start.node, native.start.offset, native.end.node, native.end.offset);\n            } catch (ignored) {}\n          });\n        });\n        this.emitter.on(_emitter4.default.events.SCROLL_OPTIMIZE, function (mutations, context) {\n          if (context.range) {\n            var _context$range = context.range,\n              startNode = _context$range.startNode,\n              startOffset = _context$range.startOffset,\n              endNode = _context$range.endNode,\n              endOffset = _context$range.endOffset;\n            _this.setNativeRange(startNode, startOffset, endNode, endOffset);\n          }\n        });\n        this.update(_emitter4.default.sources.SILENT);\n      }\n      _createClass(Selection, [{\n        key: 'handleComposition',\n        value: function handleComposition() {\n          var _this2 = this;\n          this.root.addEventListener('compositionstart', function () {\n            _this2.composing = true;\n          });\n          this.root.addEventListener('compositionend', function () {\n            _this2.composing = false;\n            if (_this2.cursor.parent) {\n              var range = _this2.cursor.restore();\n              if (!range) return;\n              setTimeout(function () {\n                _this2.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);\n              }, 1);\n            }\n          });\n        }\n      }, {\n        key: 'handleDragging',\n        value: function handleDragging() {\n          var _this3 = this;\n          this.emitter.listenDOM('mousedown', document.body, function () {\n            _this3.mouseDown = true;\n          });\n          this.emitter.listenDOM('mouseup', document.body, function () {\n            _this3.mouseDown = false;\n            _this3.update(_emitter4.default.sources.USER);\n          });\n        }\n      }, {\n        key: 'focus',\n        value: function focus() {\n          if (this.hasFocus()) return;\n          this.root.focus();\n          this.setRange(this.savedRange);\n        }\n      }, {\n        key: 'format',\n        value: function format(_format, value) {\n          if (this.scroll.whitelist != null && !this.scroll.whitelist[_format]) return;\n          this.scroll.update();\n          var nativeRange = this.getNativeRange();\n          if (nativeRange == null || !nativeRange.native.collapsed || _parchment2.default.query(_format, _parchment2.default.Scope.BLOCK)) return;\n          if (nativeRange.start.node !== this.cursor.textNode) {\n            var blot = _parchment2.default.find(nativeRange.start.node, false);\n            if (blot == null) return;\n            // TODO Give blot ability to not split\n            if (blot instanceof _parchment2.default.Leaf) {\n              var after = blot.split(nativeRange.start.offset);\n              blot.parent.insertBefore(this.cursor, after);\n            } else {\n              blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n            }\n            this.cursor.attach();\n          }\n          this.cursor.format(_format, value);\n          this.scroll.optimize();\n          this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n          this.update();\n        }\n      }, {\n        key: 'getBounds',\n        value: function getBounds(index) {\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          var scrollLength = this.scroll.length();\n          index = Math.min(index, scrollLength - 1);\n          length = Math.min(index + length, scrollLength - 1) - index;\n          var node = void 0,\n            _scroll$leaf = this.scroll.leaf(index),\n            _scroll$leaf2 = _slicedToArray(_scroll$leaf, 2),\n            leaf = _scroll$leaf2[0],\n            offset = _scroll$leaf2[1];\n          if (leaf == null) return null;\n          var _leaf$position = leaf.position(offset, true);\n          var _leaf$position2 = _slicedToArray(_leaf$position, 2);\n          node = _leaf$position2[0];\n          offset = _leaf$position2[1];\n          var range = document.createRange();\n          if (length > 0) {\n            range.setStart(node, offset);\n            var _scroll$leaf3 = this.scroll.leaf(index + length);\n            var _scroll$leaf4 = _slicedToArray(_scroll$leaf3, 2);\n            leaf = _scroll$leaf4[0];\n            offset = _scroll$leaf4[1];\n            if (leaf == null) return null;\n            var _leaf$position3 = leaf.position(offset, true);\n            var _leaf$position4 = _slicedToArray(_leaf$position3, 2);\n            node = _leaf$position4[0];\n            offset = _leaf$position4[1];\n            range.setEnd(node, offset);\n            return range.getBoundingClientRect();\n          } else {\n            var side = 'left';\n            var rect = void 0;\n            if (node instanceof Text) {\n              if (offset < node.data.length) {\n                range.setStart(node, offset);\n                range.setEnd(node, offset + 1);\n              } else {\n                range.setStart(node, offset - 1);\n                range.setEnd(node, offset);\n                side = 'right';\n              }\n              rect = range.getBoundingClientRect();\n            } else {\n              rect = leaf.domNode.getBoundingClientRect();\n              if (offset > 0) side = 'right';\n            }\n            return {\n              bottom: rect.top + rect.height,\n              height: rect.height,\n              left: rect[side],\n              right: rect[side],\n              top: rect.top,\n              width: 0\n            };\n          }\n        }\n      }, {\n        key: 'getNativeRange',\n        value: function getNativeRange() {\n          var selection = document.getSelection();\n          if (selection == null || selection.rangeCount <= 0) return null;\n          var nativeRange = selection.getRangeAt(0);\n          if (nativeRange == null) return null;\n          var range = this.normalizeNative(nativeRange);\n          debug.info('getNativeRange', range);\n          return range;\n        }\n      }, {\n        key: 'getRange',\n        value: function getRange() {\n          var normalized = this.getNativeRange();\n          if (normalized == null) return [null, null];\n          var range = this.normalizedToRange(normalized);\n          return [range, normalized];\n        }\n      }, {\n        key: 'hasFocus',\n        value: function hasFocus() {\n          return document.activeElement === this.root;\n        }\n      }, {\n        key: 'normalizedToRange',\n        value: function normalizedToRange(range) {\n          var _this4 = this;\n          var positions = [[range.start.node, range.start.offset]];\n          if (!range.native.collapsed) {\n            positions.push([range.end.node, range.end.offset]);\n          }\n          var indexes = positions.map(function (position) {\n            var _position = _slicedToArray(position, 2),\n              node = _position[0],\n              offset = _position[1];\n            var blot = _parchment2.default.find(node, true);\n            var index = blot.offset(_this4.scroll);\n            if (offset === 0) {\n              return index;\n            } else if (blot instanceof _parchment2.default.Container) {\n              return index + blot.length();\n            } else {\n              return index + blot.index(node, offset);\n            }\n          });\n          var end = Math.min(Math.max.apply(Math, _toConsumableArray(indexes)), this.scroll.length() - 1);\n          var start = Math.min.apply(Math, [end].concat(_toConsumableArray(indexes)));\n          return new Range(start, end - start);\n        }\n      }, {\n        key: 'normalizeNative',\n        value: function normalizeNative(nativeRange) {\n          if (!contains(this.root, nativeRange.startContainer) || !nativeRange.collapsed && !contains(this.root, nativeRange.endContainer)) {\n            return null;\n          }\n          var range = {\n            start: {\n              node: nativeRange.startContainer,\n              offset: nativeRange.startOffset\n            },\n            end: {\n              node: nativeRange.endContainer,\n              offset: nativeRange.endOffset\n            },\n            native: nativeRange\n          };\n          [range.start, range.end].forEach(function (position) {\n            var node = position.node,\n              offset = position.offset;\n            while (!(node instanceof Text) && node.childNodes.length > 0) {\n              if (node.childNodes.length > offset) {\n                node = node.childNodes[offset];\n                offset = 0;\n              } else if (node.childNodes.length === offset) {\n                node = node.lastChild;\n                offset = node instanceof Text ? node.data.length : node.childNodes.length + 1;\n              } else {\n                break;\n              }\n            }\n            position.node = node, position.offset = offset;\n          });\n          return range;\n        }\n      }, {\n        key: 'rangeToNative',\n        value: function rangeToNative(range) {\n          var _this5 = this;\n          var indexes = range.collapsed ? [range.index] : [range.index, range.index + range.length];\n          var args = [];\n          var scrollLength = this.scroll.length();\n          indexes.forEach(function (index, i) {\n            index = Math.min(scrollLength - 1, index);\n            var node = void 0,\n              _scroll$leaf5 = _this5.scroll.leaf(index),\n              _scroll$leaf6 = _slicedToArray(_scroll$leaf5, 2),\n              leaf = _scroll$leaf6[0],\n              offset = _scroll$leaf6[1];\n            var _leaf$position5 = leaf.position(offset, i !== 0);\n            var _leaf$position6 = _slicedToArray(_leaf$position5, 2);\n            node = _leaf$position6[0];\n            offset = _leaf$position6[1];\n            args.push(node, offset);\n          });\n          if (args.length < 2) {\n            args = args.concat(args);\n          }\n          return args;\n        }\n      }, {\n        key: 'scrollIntoView',\n        value: function scrollIntoView(scrollingContainer) {\n          var range = this.lastRange;\n          if (range == null) return;\n          var bounds = this.getBounds(range.index, range.length);\n          if (bounds == null) return;\n          var limit = this.scroll.length() - 1;\n          var _scroll$line = this.scroll.line(Math.min(range.index, limit)),\n            _scroll$line2 = _slicedToArray(_scroll$line, 1),\n            first = _scroll$line2[0];\n          var last = first;\n          if (range.length > 0) {\n            var _scroll$line3 = this.scroll.line(Math.min(range.index + range.length, limit));\n            var _scroll$line4 = _slicedToArray(_scroll$line3, 1);\n            last = _scroll$line4[0];\n          }\n          if (first == null || last == null) return;\n          var scrollBounds = scrollingContainer.getBoundingClientRect();\n          if (bounds.top < scrollBounds.top) {\n            scrollingContainer.scrollTop -= scrollBounds.top - bounds.top;\n          } else if (bounds.bottom > scrollBounds.bottom) {\n            scrollingContainer.scrollTop += bounds.bottom - scrollBounds.bottom;\n          }\n        }\n      }, {\n        key: 'setNativeRange',\n        value: function setNativeRange(startNode, startOffset) {\n          var endNode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : startNode;\n          var endOffset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : startOffset;\n          var force = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n          debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n          if (startNode != null && (this.root.parentNode == null || startNode.parentNode == null || endNode.parentNode == null)) {\n            return;\n          }\n          var selection = document.getSelection();\n          if (selection == null) return;\n          if (startNode != null) {\n            if (!this.hasFocus()) this.root.focus();\n            var native = (this.getNativeRange() || {}).native;\n            if (native == null || force || startNode !== native.startContainer || startOffset !== native.startOffset || endNode !== native.endContainer || endOffset !== native.endOffset) {\n              if (startNode.tagName == \"BR\") {\n                startOffset = [].indexOf.call(startNode.parentNode.childNodes, startNode);\n                startNode = startNode.parentNode;\n              }\n              if (endNode.tagName == \"BR\") {\n                endOffset = [].indexOf.call(endNode.parentNode.childNodes, endNode);\n                endNode = endNode.parentNode;\n              }\n              var range = document.createRange();\n              range.setStart(startNode, startOffset);\n              range.setEnd(endNode, endOffset);\n              selection.removeAllRanges();\n              selection.addRange(range);\n            }\n          } else {\n            selection.removeAllRanges();\n            this.root.blur();\n            document.body.focus(); // root.blur() not enough on IE11+Travis+SauceLabs (but not local VMs)\n          }\n        }\n      }, {\n        key: 'setRange',\n        value: function setRange(range) {\n          var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n          var source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _emitter4.default.sources.API;\n          if (typeof force === 'string') {\n            source = force;\n            force = false;\n          }\n          debug.info('setRange', range);\n          if (range != null) {\n            var args = this.rangeToNative(range);\n            this.setNativeRange.apply(this, _toConsumableArray(args).concat([force]));\n          } else {\n            this.setNativeRange(null);\n          }\n          this.update(source);\n        }\n      }, {\n        key: 'update',\n        value: function update() {\n          var source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _emitter4.default.sources.USER;\n          var oldRange = this.lastRange;\n          var _getRange = this.getRange(),\n            _getRange2 = _slicedToArray(_getRange, 2),\n            lastRange = _getRange2[0],\n            nativeRange = _getRange2[1];\n          this.lastRange = lastRange;\n          if (this.lastRange != null) {\n            this.savedRange = this.lastRange;\n          }\n          if (!(0, _deepEqual2.default)(oldRange, this.lastRange)) {\n            var _emitter;\n            if (!this.composing && nativeRange != null && nativeRange.native.collapsed && nativeRange.start.node !== this.cursor.textNode) {\n              this.cursor.restore();\n            }\n            var args = [_emitter4.default.events.SELECTION_CHANGE, (0, _clone2.default)(this.lastRange), (0, _clone2.default)(oldRange), source];\n            (_emitter = this.emitter).emit.apply(_emitter, [_emitter4.default.events.EDITOR_CHANGE].concat(args));\n            if (source !== _emitter4.default.sources.SILENT) {\n              var _emitter2;\n              (_emitter2 = this.emitter).emit.apply(_emitter2, args);\n            }\n          }\n        }\n      }]);\n      return Selection;\n    }();\n    function contains(parent, descendant) {\n      try {\n        // Firefox inserts inaccessible nodes around video elements\n        descendant.parentNode;\n      } catch (e) {\n        return false;\n      }\n      // IE11 has bug with Text nodes\n      // https://connect.microsoft.com/IE/feedback/details/780874/node-contains-is-incorrect\n      if (descendant instanceof Text) {\n        descendant = descendant.parentNode;\n      }\n      return parent.contains(descendant);\n    }\n    exports.Range = Range;\n    exports.default = Selection;\n\n    /***/\n  }), ( /* 16 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Break = function (_Parchment$Embed) {\n      _inherits(Break, _Parchment$Embed);\n      function Break() {\n        _classCallCheck(this, Break);\n        return _possibleConstructorReturn(this, (Break.__proto__ || Object.getPrototypeOf(Break)).apply(this, arguments));\n      }\n      _createClass(Break, [{\n        key: 'insertInto',\n        value: function insertInto(parent, ref) {\n          if (parent.children.length === 0) {\n            _get(Break.prototype.__proto__ || Object.getPrototypeOf(Break.prototype), 'insertInto', this).call(this, parent, ref);\n          } else {\n            this.remove();\n          }\n        }\n      }, {\n        key: 'length',\n        value: function length() {\n          return 0;\n        }\n      }, {\n        key: 'value',\n        value: function value() {\n          return '';\n        }\n      }], [{\n        key: 'value',\n        value: function value() {\n          return undefined;\n        }\n      }]);\n      return Break;\n    }(_parchment2.default.Embed);\n    Break.blotName = 'break';\n    Break.tagName = 'BR';\n    exports.default = Break;\n\n    /***/\n  }), ( /* 17 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var linked_list_1 = __webpack_require__(44);\n    var shadow_1 = __webpack_require__(30);\n    var Registry = __webpack_require__(1);\n    var ContainerBlot = /** @class */function (_super) {\n      __extends(ContainerBlot, _super);\n      function ContainerBlot(domNode) {\n        var _this = _super.call(this, domNode) || this;\n        _this.build();\n        return _this;\n      }\n      ContainerBlot.prototype.appendChild = function (other) {\n        this.insertBefore(other);\n      };\n      ContainerBlot.prototype.attach = function () {\n        _super.prototype.attach.call(this);\n        this.children.forEach(function (child) {\n          child.attach();\n        });\n      };\n      ContainerBlot.prototype.build = function () {\n        var _this = this;\n        this.children = new linked_list_1.default();\n        // Need to be reversed for if DOM nodes already in order\n        [].slice.call(this.domNode.childNodes).reverse().forEach(function (node) {\n          try {\n            var child = makeBlot(node);\n            _this.insertBefore(child, _this.children.head || undefined);\n          } catch (err) {\n            if (err instanceof Registry.ParchmentError) return;else throw err;\n          }\n        });\n      };\n      ContainerBlot.prototype.deleteAt = function (index, length) {\n        if (index === 0 && length === this.length()) {\n          return this.remove();\n        }\n        this.children.forEachAt(index, length, function (child, offset, length) {\n          child.deleteAt(offset, length);\n        });\n      };\n      ContainerBlot.prototype.descendant = function (criteria, index) {\n        var _a = this.children.find(index),\n          child = _a[0],\n          offset = _a[1];\n        if (criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria) {\n          return [child, offset];\n        } else if (child instanceof ContainerBlot) {\n          return child.descendant(criteria, offset);\n        } else {\n          return [null, -1];\n        }\n      };\n      ContainerBlot.prototype.descendants = function (criteria, index, length) {\n        if (index === void 0) {\n          index = 0;\n        }\n        if (length === void 0) {\n          length = Number.MAX_VALUE;\n        }\n        var descendants = [];\n        var lengthLeft = length;\n        this.children.forEachAt(index, length, function (child, index, length) {\n          if (criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria) {\n            descendants.push(child);\n          }\n          if (child instanceof ContainerBlot) {\n            descendants = descendants.concat(child.descendants(criteria, index, lengthLeft));\n          }\n          lengthLeft -= length;\n        });\n        return descendants;\n      };\n      ContainerBlot.prototype.detach = function () {\n        this.children.forEach(function (child) {\n          child.detach();\n        });\n        _super.prototype.detach.call(this);\n      };\n      ContainerBlot.prototype.formatAt = function (index, length, name, value) {\n        this.children.forEachAt(index, length, function (child, offset, length) {\n          child.formatAt(offset, length, name, value);\n        });\n      };\n      ContainerBlot.prototype.insertAt = function (index, value, def) {\n        var _a = this.children.find(index),\n          child = _a[0],\n          offset = _a[1];\n        if (child) {\n          child.insertAt(offset, value, def);\n        } else {\n          var blot = def == null ? Registry.create('text', value) : Registry.create(value, def);\n          this.appendChild(blot);\n        }\n      };\n      ContainerBlot.prototype.insertBefore = function (childBlot, refBlot) {\n        if (this.statics.allowedChildren != null && !this.statics.allowedChildren.some(function (child) {\n          return childBlot instanceof child;\n        })) {\n          throw new Registry.ParchmentError(\"Cannot insert \" + childBlot.statics.blotName + \" into \" + this.statics.blotName);\n        }\n        childBlot.insertInto(this, refBlot);\n      };\n      ContainerBlot.prototype.length = function () {\n        return this.children.reduce(function (memo, child) {\n          return memo + child.length();\n        }, 0);\n      };\n      ContainerBlot.prototype.moveChildren = function (targetParent, refNode) {\n        this.children.forEach(function (child) {\n          targetParent.insertBefore(child, refNode);\n        });\n      };\n      ContainerBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        if (this.children.length === 0) {\n          if (this.statics.defaultChild != null) {\n            var child = Registry.create(this.statics.defaultChild);\n            this.appendChild(child);\n            child.optimize(context);\n          } else {\n            this.remove();\n          }\n        }\n      };\n      ContainerBlot.prototype.path = function (index, inclusive) {\n        if (inclusive === void 0) {\n          inclusive = false;\n        }\n        var _a = this.children.find(index, inclusive),\n          child = _a[0],\n          offset = _a[1];\n        var position = [[this, index]];\n        if (child instanceof ContainerBlot) {\n          return position.concat(child.path(offset, inclusive));\n        } else if (child != null) {\n          position.push([child, offset]);\n        }\n        return position;\n      };\n      ContainerBlot.prototype.removeChild = function (child) {\n        this.children.remove(child);\n      };\n      ContainerBlot.prototype.replace = function (target) {\n        if (target instanceof ContainerBlot) {\n          target.moveChildren(this);\n        }\n        _super.prototype.replace.call(this, target);\n      };\n      ContainerBlot.prototype.split = function (index, force) {\n        if (force === void 0) {\n          force = false;\n        }\n        if (!force) {\n          if (index === 0) return this;\n          if (index === this.length()) return this.next;\n        }\n        var after = this.clone();\n        this.parent.insertBefore(after, this.next);\n        this.children.forEachAt(index, this.length(), function (child, offset, length) {\n          child = child.split(offset, force);\n          after.appendChild(child);\n        });\n        return after;\n      };\n      ContainerBlot.prototype.unwrap = function () {\n        this.moveChildren(this.parent, this.next);\n        this.remove();\n      };\n      ContainerBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        var addedNodes = [];\n        var removedNodes = [];\n        mutations.forEach(function (mutation) {\n          if (mutation.target === _this.domNode && mutation.type === 'childList') {\n            addedNodes.push.apply(addedNodes, mutation.addedNodes);\n            removedNodes.push.apply(removedNodes, mutation.removedNodes);\n          }\n        });\n        removedNodes.forEach(function (node) {\n          // Check node has actually been removed\n          // One exception is Chrome does not immediately remove IFRAMEs\n          // from DOM but MutationRecord is correct in its reported removal\n          if (node.parentNode != null &&\n          // @ts-ignore\n          node.tagName !== 'IFRAME' && document.body.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n            return;\n          }\n          var blot = Registry.find(node);\n          if (blot == null) return;\n          if (blot.domNode.parentNode == null || blot.domNode.parentNode === _this.domNode) {\n            blot.detach();\n          }\n        });\n        addedNodes.filter(function (node) {\n          return node.parentNode == _this.domNode;\n        }).sort(function (a, b) {\n          if (a === b) return 0;\n          if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {\n            return 1;\n          }\n          return -1;\n        }).forEach(function (node) {\n          var refBlot = null;\n          if (node.nextSibling != null) {\n            refBlot = Registry.find(node.nextSibling);\n          }\n          var blot = makeBlot(node);\n          if (blot.next != refBlot || blot.next == null) {\n            if (blot.parent != null) {\n              blot.parent.removeChild(_this);\n            }\n            _this.insertBefore(blot, refBlot || undefined);\n          }\n        });\n      };\n      return ContainerBlot;\n    }(shadow_1.default);\n    function makeBlot(node) {\n      var blot = Registry.find(node);\n      if (blot == null) {\n        try {\n          blot = Registry.create(node);\n        } catch (e) {\n          blot = Registry.create(Registry.Scope.INLINE);\n          [].slice.call(node.childNodes).forEach(function (child) {\n            // @ts-ignore\n            blot.domNode.appendChild(child);\n          });\n          if (node.parentNode) {\n            node.parentNode.replaceChild(blot.domNode, node);\n          }\n          blot.attach();\n        }\n      }\n      return blot;\n    }\n    exports.default = ContainerBlot;\n\n    /***/\n  }), ( /* 18 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var attributor_1 = __webpack_require__(12);\n    var store_1 = __webpack_require__(31);\n    var container_1 = __webpack_require__(17);\n    var Registry = __webpack_require__(1);\n    var FormatBlot = /** @class */function (_super) {\n      __extends(FormatBlot, _super);\n      function FormatBlot(domNode) {\n        var _this = _super.call(this, domNode) || this;\n        _this.attributes = new store_1.default(_this.domNode);\n        return _this;\n      }\n      FormatBlot.formats = function (domNode) {\n        if (typeof this.tagName === 'string') {\n          return true;\n        } else if (Array.isArray(this.tagName)) {\n          return domNode.tagName.toLowerCase();\n        }\n        return undefined;\n      };\n      FormatBlot.prototype.format = function (name, value) {\n        var format = Registry.query(name);\n        if (format instanceof attributor_1.default) {\n          this.attributes.attribute(format, value);\n        } else if (value) {\n          if (format != null && (name !== this.statics.blotName || this.formats()[name] !== value)) {\n            this.replaceWith(name, value);\n          }\n        }\n      };\n      FormatBlot.prototype.formats = function () {\n        var formats = this.attributes.values();\n        var format = this.statics.formats(this.domNode);\n        if (format != null) {\n          formats[this.statics.blotName] = format;\n        }\n        return formats;\n      };\n      FormatBlot.prototype.replaceWith = function (name, value) {\n        var replacement = _super.prototype.replaceWith.call(this, name, value);\n        this.attributes.copy(replacement);\n        return replacement;\n      };\n      FormatBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        _super.prototype.update.call(this, mutations, context);\n        if (mutations.some(function (mutation) {\n          return mutation.target === _this.domNode && mutation.type === 'attributes';\n        })) {\n          this.attributes.build();\n        }\n      };\n      FormatBlot.prototype.wrap = function (name, value) {\n        var wrapper = _super.prototype.wrap.call(this, name, value);\n        if (wrapper instanceof FormatBlot && wrapper.statics.scope === this.statics.scope) {\n          this.attributes.move(wrapper);\n        }\n        return wrapper;\n      };\n      return FormatBlot;\n    }(container_1.default);\n    exports.default = FormatBlot;\n\n    /***/\n  }), ( /* 19 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var shadow_1 = __webpack_require__(30);\n    var Registry = __webpack_require__(1);\n    var LeafBlot = /** @class */function (_super) {\n      __extends(LeafBlot, _super);\n      function LeafBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      LeafBlot.value = function (domNode) {\n        return true;\n      };\n      LeafBlot.prototype.index = function (node, offset) {\n        if (this.domNode === node || this.domNode.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n          return Math.min(offset, 1);\n        }\n        return -1;\n      };\n      LeafBlot.prototype.position = function (index, inclusive) {\n        var offset = [].indexOf.call(this.parent.domNode.childNodes, this.domNode);\n        if (index > 0) offset += 1;\n        return [this.parent.domNode, offset];\n      };\n      LeafBlot.prototype.value = function () {\n        var _a;\n        return _a = {}, _a[this.statics.blotName] = this.statics.value(this.domNode) || true, _a;\n      };\n      LeafBlot.scope = Registry.Scope.INLINE_BLOT;\n      return LeafBlot;\n    }(shadow_1.default);\n    exports.default = LeafBlot;\n\n    /***/\n  }), ( /* 20 */\n  /***/function (module, exports, __webpack_require__) {\n    var equal = __webpack_require__(11);\n    var extend = __webpack_require__(3);\n    var lib = {\n      attributes: {\n        compose: function (a, b, keepNull) {\n          if (typeof a !== 'object') a = {};\n          if (typeof b !== 'object') b = {};\n          var attributes = extend(true, {}, b);\n          if (!keepNull) {\n            attributes = Object.keys(attributes).reduce(function (copy, key) {\n              if (attributes[key] != null) {\n                copy[key] = attributes[key];\n              }\n              return copy;\n            }, {});\n          }\n          for (var key in a) {\n            if (a[key] !== undefined && b[key] === undefined) {\n              attributes[key] = a[key];\n            }\n          }\n          return Object.keys(attributes).length > 0 ? attributes : undefined;\n        },\n        diff: function (a, b) {\n          if (typeof a !== 'object') a = {};\n          if (typeof b !== 'object') b = {};\n          var attributes = Object.keys(a).concat(Object.keys(b)).reduce(function (attributes, key) {\n            if (!equal(a[key], b[key])) {\n              attributes[key] = b[key] === undefined ? null : b[key];\n            }\n            return attributes;\n          }, {});\n          return Object.keys(attributes).length > 0 ? attributes : undefined;\n        },\n        transform: function (a, b, priority) {\n          if (typeof a !== 'object') return b;\n          if (typeof b !== 'object') return undefined;\n          if (!priority) return b; // b simply overwrites us without priority\n          var attributes = Object.keys(b).reduce(function (attributes, key) {\n            if (a[key] === undefined) attributes[key] = b[key]; // null is a valid value\n            return attributes;\n          }, {});\n          return Object.keys(attributes).length > 0 ? attributes : undefined;\n        }\n      },\n      iterator: function (ops) {\n        return new Iterator(ops);\n      },\n      length: function (op) {\n        if (typeof op['delete'] === 'number') {\n          return op['delete'];\n        } else if (typeof op.retain === 'number') {\n          return op.retain;\n        } else {\n          return typeof op.insert === 'string' ? op.insert.length : 1;\n        }\n      }\n    };\n    function Iterator(ops) {\n      this.ops = ops;\n      this.index = 0;\n      this.offset = 0;\n    }\n    ;\n    Iterator.prototype.hasNext = function () {\n      return this.peekLength() < Infinity;\n    };\n    Iterator.prototype.next = function (length) {\n      if (!length) length = Infinity;\n      var nextOp = this.ops[this.index];\n      if (nextOp) {\n        var offset = this.offset;\n        var opLength = lib.length(nextOp);\n        if (length >= opLength - offset) {\n          length = opLength - offset;\n          this.index += 1;\n          this.offset = 0;\n        } else {\n          this.offset += length;\n        }\n        if (typeof nextOp['delete'] === 'number') {\n          return {\n            'delete': length\n          };\n        } else {\n          var retOp = {};\n          if (nextOp.attributes) {\n            retOp.attributes = nextOp.attributes;\n          }\n          if (typeof nextOp.retain === 'number') {\n            retOp.retain = length;\n          } else if (typeof nextOp.insert === 'string') {\n            retOp.insert = nextOp.insert.substr(offset, length);\n          } else {\n            // offset should === 0, length should === 1\n            retOp.insert = nextOp.insert;\n          }\n          return retOp;\n        }\n      } else {\n        return {\n          retain: Infinity\n        };\n      }\n    };\n    Iterator.prototype.peek = function () {\n      return this.ops[this.index];\n    };\n    Iterator.prototype.peekLength = function () {\n      if (this.ops[this.index]) {\n        // Should never return 0 if our index is being managed correctly\n        return lib.length(this.ops[this.index]) - this.offset;\n      } else {\n        return Infinity;\n      }\n    };\n    Iterator.prototype.peekType = function () {\n      if (this.ops[this.index]) {\n        if (typeof this.ops[this.index]['delete'] === 'number') {\n          return 'delete';\n        } else if (typeof this.ops[this.index].retain === 'number') {\n          return 'retain';\n        } else {\n          return 'insert';\n        }\n      }\n      return 'retain';\n    };\n    Iterator.prototype.rest = function () {\n      if (!this.hasNext()) {\n        return [];\n      } else if (this.offset === 0) {\n        return this.ops.slice(this.index);\n      } else {\n        var offset = this.offset;\n        var index = this.index;\n        var next = this.next();\n        var rest = this.ops.slice(this.index);\n        this.offset = offset;\n        this.index = index;\n        return [next].concat(rest);\n      }\n    };\n    module.exports = lib;\n\n    /***/\n  }), ( /* 21 */\n  /***/function (module, exports) {\n    var clone = function () {\n      'use strict';\n\n      function _instanceof(obj, type) {\n        return type != null && obj instanceof type;\n      }\n      var nativeMap;\n      try {\n        nativeMap = Map;\n      } catch (_) {\n        // maybe a reference error because no `Map`. Give it a dummy value that no\n        // value will ever be an instanceof.\n        nativeMap = function () {};\n      }\n      var nativeSet;\n      try {\n        nativeSet = Set;\n      } catch (_) {\n        nativeSet = function () {};\n      }\n      var nativePromise;\n      try {\n        nativePromise = Promise;\n      } catch (_) {\n        nativePromise = function () {};\n      }\n\n      /**\n       * Clones (copies) an Object using deep copying.\n       *\n       * This function supports circular references by default, but if you are certain\n       * there are no circular references in your object, you can save some CPU time\n       * by calling clone(obj, false).\n       *\n       * Caution: if `circular` is false and `parent` contains circular references,\n       * your program may enter an infinite loop and crash.\n       *\n       * @param `parent` - the object to be cloned\n       * @param `circular` - set to true if the object to be cloned may contain\n       *    circular references. (optional - true by default)\n       * @param `depth` - set to a number if the object is only to be cloned to\n       *    a particular depth. (optional - defaults to Infinity)\n       * @param `prototype` - sets the prototype to be used when cloning an object.\n       *    (optional - defaults to parent prototype).\n       * @param `includeNonEnumerable` - set to true if the non-enumerable properties\n       *    should be cloned as well. Non-enumerable properties on the prototype\n       *    chain will be ignored. (optional - false by default)\n      */\n      function clone(parent, circular, depth, prototype, includeNonEnumerable) {\n        if (typeof circular === 'object') {\n          depth = circular.depth;\n          prototype = circular.prototype;\n          includeNonEnumerable = circular.includeNonEnumerable;\n          circular = circular.circular;\n        }\n        // maintain two arrays for circular references, where corresponding parents\n        // and children have the same index\n        var allParents = [];\n        var allChildren = [];\n        var useBuffer = typeof Buffer != 'undefined';\n        if (typeof circular == 'undefined') circular = true;\n        if (typeof depth == 'undefined') depth = Infinity;\n\n        // recurse this function so we don't reset allParents and allChildren\n        function _clone(parent, depth) {\n          // cloning null always returns null\n          if (parent === null) return null;\n          if (depth === 0) return parent;\n          var child;\n          var proto;\n          if (typeof parent != 'object') {\n            return parent;\n          }\n          if (_instanceof(parent, nativeMap)) {\n            child = new nativeMap();\n          } else if (_instanceof(parent, nativeSet)) {\n            child = new nativeSet();\n          } else if (_instanceof(parent, nativePromise)) {\n            child = new nativePromise(function (resolve, reject) {\n              parent.then(function (value) {\n                resolve(_clone(value, depth - 1));\n              }, function (err) {\n                reject(_clone(err, depth - 1));\n              });\n            });\n          } else if (clone.__isArray(parent)) {\n            child = [];\n          } else if (clone.__isRegExp(parent)) {\n            child = new RegExp(parent.source, __getRegExpFlags(parent));\n            if (parent.lastIndex) child.lastIndex = parent.lastIndex;\n          } else if (clone.__isDate(parent)) {\n            child = new Date(parent.getTime());\n          } else if (useBuffer && Buffer.isBuffer(parent)) {\n            if (Buffer.allocUnsafe) {\n              // Node.js >= 4.5.0\n              child = Buffer.allocUnsafe(parent.length);\n            } else {\n              // Older Node.js versions\n              child = new Buffer(parent.length);\n            }\n            parent.copy(child);\n            return child;\n          } else if (_instanceof(parent, Error)) {\n            child = Object.create(parent);\n          } else {\n            if (typeof prototype == 'undefined') {\n              proto = Object.getPrototypeOf(parent);\n              child = Object.create(proto);\n            } else {\n              child = Object.create(prototype);\n              proto = prototype;\n            }\n          }\n          if (circular) {\n            var index = allParents.indexOf(parent);\n            if (index != -1) {\n              return allChildren[index];\n            }\n            allParents.push(parent);\n            allChildren.push(child);\n          }\n          if (_instanceof(parent, nativeMap)) {\n            parent.forEach(function (value, key) {\n              var keyChild = _clone(key, depth - 1);\n              var valueChild = _clone(value, depth - 1);\n              child.set(keyChild, valueChild);\n            });\n          }\n          if (_instanceof(parent, nativeSet)) {\n            parent.forEach(function (value) {\n              var entryChild = _clone(value, depth - 1);\n              child.add(entryChild);\n            });\n          }\n          for (var i in parent) {\n            var attrs;\n            if (proto) {\n              attrs = Object.getOwnPropertyDescriptor(proto, i);\n            }\n            if (attrs && attrs.set == null) {\n              continue;\n            }\n            child[i] = _clone(parent[i], depth - 1);\n          }\n          if (Object.getOwnPropertySymbols) {\n            var symbols = Object.getOwnPropertySymbols(parent);\n            for (var i = 0; i < symbols.length; i++) {\n              // Don't need to worry about cloning a symbol because it is a primitive,\n              // like a number or string.\n              var symbol = symbols[i];\n              var descriptor = Object.getOwnPropertyDescriptor(parent, symbol);\n              if (descriptor && !descriptor.enumerable && !includeNonEnumerable) {\n                continue;\n              }\n              child[symbol] = _clone(parent[symbol], depth - 1);\n              if (!descriptor.enumerable) {\n                Object.defineProperty(child, symbol, {\n                  enumerable: false\n                });\n              }\n            }\n          }\n          if (includeNonEnumerable) {\n            var allPropertyNames = Object.getOwnPropertyNames(parent);\n            for (var i = 0; i < allPropertyNames.length; i++) {\n              var propertyName = allPropertyNames[i];\n              var descriptor = Object.getOwnPropertyDescriptor(parent, propertyName);\n              if (descriptor && descriptor.enumerable) {\n                continue;\n              }\n              child[propertyName] = _clone(parent[propertyName], depth - 1);\n              Object.defineProperty(child, propertyName, {\n                enumerable: false\n              });\n            }\n          }\n          return child;\n        }\n        return _clone(parent, depth);\n      }\n\n      /**\n       * Simple flat clone using prototype, accepts only objects, usefull for property\n       * override on FLAT configuration object (no nested props).\n       *\n       * USE WITH CAUTION! This may not behave as you wish if you do not know how this\n       * works.\n       */\n      clone.clonePrototype = function clonePrototype(parent) {\n        if (parent === null) return null;\n        var c = function () {};\n        c.prototype = parent;\n        return new c();\n      };\n\n      // private utility functions\n\n      function __objToStr(o) {\n        return Object.prototype.toString.call(o);\n      }\n      clone.__objToStr = __objToStr;\n      function __isDate(o) {\n        return typeof o === 'object' && __objToStr(o) === '[object Date]';\n      }\n      clone.__isDate = __isDate;\n      function __isArray(o) {\n        return typeof o === 'object' && __objToStr(o) === '[object Array]';\n      }\n      clone.__isArray = __isArray;\n      function __isRegExp(o) {\n        return typeof o === 'object' && __objToStr(o) === '[object RegExp]';\n      }\n      clone.__isRegExp = __isRegExp;\n      function __getRegExpFlags(re) {\n        var flags = '';\n        if (re.global) flags += 'g';\n        if (re.ignoreCase) flags += 'i';\n        if (re.multiline) flags += 'm';\n        return flags;\n      }\n      clone.__getRegExpFlags = __getRegExpFlags;\n      return clone;\n    }();\n    if (typeof module === 'object' && module.exports) {\n      module.exports = clone;\n    }\n\n    /***/\n  }), ( /* 22 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _emitter = __webpack_require__(8);\n    var _emitter2 = _interopRequireDefault(_emitter);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    var _break = __webpack_require__(16);\n    var _break2 = _interopRequireDefault(_break);\n    var _code = __webpack_require__(13);\n    var _code2 = _interopRequireDefault(_code);\n    var _container = __webpack_require__(25);\n    var _container2 = _interopRequireDefault(_container);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    function isLine(blot) {\n      return blot instanceof _block2.default || blot instanceof _block.BlockEmbed;\n    }\n    var Scroll = function (_Parchment$Scroll) {\n      _inherits(Scroll, _Parchment$Scroll);\n      function Scroll(domNode, config) {\n        _classCallCheck(this, Scroll);\n        var _this = _possibleConstructorReturn(this, (Scroll.__proto__ || Object.getPrototypeOf(Scroll)).call(this, domNode));\n        _this.emitter = config.emitter;\n        if (Array.isArray(config.whitelist)) {\n          _this.whitelist = config.whitelist.reduce(function (whitelist, format) {\n            whitelist[format] = true;\n            return whitelist;\n          }, {});\n        }\n        // Some reason fixes composition issues with character languages in Windows/Chrome, Safari\n        _this.domNode.addEventListener('DOMNodeInserted', function () {});\n        _this.optimize();\n        _this.enable();\n        return _this;\n      }\n      _createClass(Scroll, [{\n        key: 'batchStart',\n        value: function batchStart() {\n          this.batch = true;\n        }\n      }, {\n        key: 'batchEnd',\n        value: function batchEnd() {\n          this.batch = false;\n          this.optimize();\n        }\n      }, {\n        key: 'deleteAt',\n        value: function deleteAt(index, length) {\n          var _line = this.line(index),\n            _line2 = _slicedToArray(_line, 2),\n            first = _line2[0],\n            offset = _line2[1];\n          var _line3 = this.line(index + length),\n            _line4 = _slicedToArray(_line3, 1),\n            last = _line4[0];\n          _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'deleteAt', this).call(this, index, length);\n          if (last != null && first !== last && offset > 0) {\n            if (first instanceof _block.BlockEmbed || last instanceof _block.BlockEmbed) {\n              this.optimize();\n              return;\n            }\n            if (first instanceof _code2.default) {\n              var newlineIndex = first.newlineIndex(first.length(), true);\n              if (newlineIndex > -1) {\n                first = first.split(newlineIndex + 1);\n                if (first === last) {\n                  this.optimize();\n                  return;\n                }\n              }\n            } else if (last instanceof _code2.default) {\n              var _newlineIndex = last.newlineIndex(0);\n              if (_newlineIndex > -1) {\n                last.split(_newlineIndex + 1);\n              }\n            }\n            var ref = last.children.head instanceof _break2.default ? null : last.children.head;\n            first.moveChildren(last, ref);\n            first.remove();\n          }\n          this.optimize();\n        }\n      }, {\n        key: 'enable',\n        value: function enable() {\n          var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n          this.domNode.setAttribute('contenteditable', enabled);\n        }\n      }, {\n        key: 'formatAt',\n        value: function formatAt(index, length, format, value) {\n          if (this.whitelist != null && !this.whitelist[format]) return;\n          _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'formatAt', this).call(this, index, length, format, value);\n          this.optimize();\n        }\n      }, {\n        key: 'insertAt',\n        value: function insertAt(index, value, def) {\n          if (def != null && this.whitelist != null && !this.whitelist[value]) return;\n          if (index >= this.length()) {\n            if (def == null || _parchment2.default.query(value, _parchment2.default.Scope.BLOCK) == null) {\n              var blot = _parchment2.default.create(this.statics.defaultChild);\n              this.appendChild(blot);\n              if (def == null && value.endsWith('\\n')) {\n                value = value.slice(0, -1);\n              }\n              blot.insertAt(0, value, def);\n            } else {\n              var embed = _parchment2.default.create(value, def);\n              this.appendChild(embed);\n            }\n          } else {\n            _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'insertAt', this).call(this, index, value, def);\n          }\n          this.optimize();\n        }\n      }, {\n        key: 'insertBefore',\n        value: function insertBefore(blot, ref) {\n          if (blot.statics.scope === _parchment2.default.Scope.INLINE_BLOT) {\n            var wrapper = _parchment2.default.create(this.statics.defaultChild);\n            wrapper.appendChild(blot);\n            blot = wrapper;\n          }\n          _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'insertBefore', this).call(this, blot, ref);\n        }\n      }, {\n        key: 'leaf',\n        value: function leaf(index) {\n          return this.path(index).pop() || [null, -1];\n        }\n      }, {\n        key: 'line',\n        value: function line(index) {\n          if (index === this.length()) {\n            return this.line(index - 1);\n          }\n          return this.descendant(isLine, index);\n        }\n      }, {\n        key: 'lines',\n        value: function lines() {\n          var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MAX_VALUE;\n          var getLines = function getLines(blot, index, length) {\n            var lines = [],\n              lengthLeft = length;\n            blot.children.forEachAt(index, length, function (child, index, length) {\n              if (isLine(child)) {\n                lines.push(child);\n              } else if (child instanceof _parchment2.default.Container) {\n                lines = lines.concat(getLines(child, index, lengthLeft));\n              }\n              lengthLeft -= length;\n            });\n            return lines;\n          };\n          return getLines(this, index, length);\n        }\n      }, {\n        key: 'optimize',\n        value: function optimize() {\n          var mutations = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n          var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n          if (this.batch === true) return;\n          _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'optimize', this).call(this, mutations, context);\n          if (mutations.length > 0) {\n            this.emitter.emit(_emitter2.default.events.SCROLL_OPTIMIZE, mutations, context);\n          }\n        }\n      }, {\n        key: 'path',\n        value: function path(index) {\n          return _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'path', this).call(this, index).slice(1); // Exclude self\n        }\n      }, {\n        key: 'update',\n        value: function update(mutations) {\n          if (this.batch === true) return;\n          var source = _emitter2.default.sources.USER;\n          if (typeof mutations === 'string') {\n            source = mutations;\n          }\n          if (!Array.isArray(mutations)) {\n            mutations = this.observer.takeRecords();\n          }\n          if (mutations.length > 0) {\n            this.emitter.emit(_emitter2.default.events.SCROLL_BEFORE_UPDATE, source, mutations);\n          }\n          _get(Scroll.prototype.__proto__ || Object.getPrototypeOf(Scroll.prototype), 'update', this).call(this, mutations.concat([])); // pass copy\n          if (mutations.length > 0) {\n            this.emitter.emit(_emitter2.default.events.SCROLL_UPDATE, source, mutations);\n          }\n        }\n      }]);\n      return Scroll;\n    }(_parchment2.default.Scroll);\n    Scroll.blotName = 'scroll';\n    Scroll.className = 'ql-editor';\n    Scroll.tagName = 'DIV';\n    Scroll.defaultChild = 'block';\n    Scroll.allowedChildren = [_block2.default, _block.BlockEmbed, _container2.default];\n    exports.default = Scroll;\n\n    /***/\n  }), ( /* 23 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.SHORTKEY = exports.default = undefined;\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _clone = __webpack_require__(21);\n    var _clone2 = _interopRequireDefault(_clone);\n    var _deepEqual = __webpack_require__(11);\n    var _deepEqual2 = _interopRequireDefault(_deepEqual);\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _op = __webpack_require__(20);\n    var _op2 = _interopRequireDefault(_op);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var debug = (0, _logger2.default)('quill:keyboard');\n    var SHORTKEY = /Mac/i.test(navigator.platform) ? 'metaKey' : 'ctrlKey';\n    var Keyboard = function (_Module) {\n      _inherits(Keyboard, _Module);\n      _createClass(Keyboard, null, [{\n        key: 'match',\n        value: function match(evt, binding) {\n          binding = normalize(binding);\n          if (['altKey', 'ctrlKey', 'metaKey', 'shiftKey'].some(function (key) {\n            return !!binding[key] !== evt[key] && binding[key] !== null;\n          })) {\n            return false;\n          }\n          return binding.key === (evt.which || evt.keyCode);\n        }\n      }]);\n      function Keyboard(quill, options) {\n        _classCallCheck(this, Keyboard);\n        var _this = _possibleConstructorReturn(this, (Keyboard.__proto__ || Object.getPrototypeOf(Keyboard)).call(this, quill, options));\n        _this.bindings = {};\n        Object.keys(_this.options.bindings).forEach(function (name) {\n          if (name === 'list autofill' && quill.scroll.whitelist != null && !quill.scroll.whitelist['list']) {\n            return;\n          }\n          if (_this.options.bindings[name]) {\n            _this.addBinding(_this.options.bindings[name]);\n          }\n        });\n        _this.addBinding({\n          key: Keyboard.keys.ENTER,\n          shiftKey: null\n        }, handleEnter);\n        _this.addBinding({\n          key: Keyboard.keys.ENTER,\n          metaKey: null,\n          ctrlKey: null,\n          altKey: null\n        }, function () {});\n        if (/Firefox/i.test(navigator.userAgent)) {\n          // Need to handle delete and backspace for Firefox in the general case #1171\n          _this.addBinding({\n            key: Keyboard.keys.BACKSPACE\n          }, {\n            collapsed: true\n          }, handleBackspace);\n          _this.addBinding({\n            key: Keyboard.keys.DELETE\n          }, {\n            collapsed: true\n          }, handleDelete);\n        } else {\n          _this.addBinding({\n            key: Keyboard.keys.BACKSPACE\n          }, {\n            collapsed: true,\n            prefix: /^.?$/\n          }, handleBackspace);\n          _this.addBinding({\n            key: Keyboard.keys.DELETE\n          }, {\n            collapsed: true,\n            suffix: /^.?$/\n          }, handleDelete);\n        }\n        _this.addBinding({\n          key: Keyboard.keys.BACKSPACE\n        }, {\n          collapsed: false\n        }, handleDeleteRange);\n        _this.addBinding({\n          key: Keyboard.keys.DELETE\n        }, {\n          collapsed: false\n        }, handleDeleteRange);\n        _this.addBinding({\n          key: Keyboard.keys.BACKSPACE,\n          altKey: null,\n          ctrlKey: null,\n          metaKey: null,\n          shiftKey: null\n        }, {\n          collapsed: true,\n          offset: 0\n        }, handleBackspace);\n        _this.listen();\n        return _this;\n      }\n      _createClass(Keyboard, [{\n        key: 'addBinding',\n        value: function addBinding(key) {\n          var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n          var handler = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n          var binding = normalize(key);\n          if (binding == null || binding.key == null) {\n            return debug.warn('Attempted to add invalid keyboard binding', binding);\n          }\n          if (typeof context === 'function') {\n            context = {\n              handler: context\n            };\n          }\n          if (typeof handler === 'function') {\n            handler = {\n              handler: handler\n            };\n          }\n          binding = (0, _extend2.default)(binding, context, handler);\n          this.bindings[binding.key] = this.bindings[binding.key] || [];\n          this.bindings[binding.key].push(binding);\n        }\n      }, {\n        key: 'listen',\n        value: function listen() {\n          var _this2 = this;\n          this.quill.root.addEventListener('keydown', function (evt) {\n            if (evt.defaultPrevented) return;\n            var which = evt.which || evt.keyCode;\n            var bindings = (_this2.bindings[which] || []).filter(function (binding) {\n              return Keyboard.match(evt, binding);\n            });\n            if (bindings.length === 0) return;\n            var range = _this2.quill.getSelection();\n            if (range == null || !_this2.quill.hasFocus()) return;\n            var _quill$getLine = _this2.quill.getLine(range.index),\n              _quill$getLine2 = _slicedToArray(_quill$getLine, 2),\n              line = _quill$getLine2[0],\n              offset = _quill$getLine2[1];\n            var _quill$getLeaf = _this2.quill.getLeaf(range.index),\n              _quill$getLeaf2 = _slicedToArray(_quill$getLeaf, 2),\n              leafStart = _quill$getLeaf2[0],\n              offsetStart = _quill$getLeaf2[1];\n            var _ref = range.length === 0 ? [leafStart, offsetStart] : _this2.quill.getLeaf(range.index + range.length),\n              _ref2 = _slicedToArray(_ref, 2),\n              leafEnd = _ref2[0],\n              offsetEnd = _ref2[1];\n            var prefixText = leafStart instanceof _parchment2.default.Text ? leafStart.value().slice(0, offsetStart) : '';\n            var suffixText = leafEnd instanceof _parchment2.default.Text ? leafEnd.value().slice(offsetEnd) : '';\n            var curContext = {\n              collapsed: range.length === 0,\n              empty: range.length === 0 && line.length() <= 1,\n              format: _this2.quill.getFormat(range),\n              offset: offset,\n              prefix: prefixText,\n              suffix: suffixText\n            };\n            var prevented = bindings.some(function (binding) {\n              if (binding.collapsed != null && binding.collapsed !== curContext.collapsed) return false;\n              if (binding.empty != null && binding.empty !== curContext.empty) return false;\n              if (binding.offset != null && binding.offset !== curContext.offset) return false;\n              if (Array.isArray(binding.format)) {\n                // any format is present\n                if (binding.format.every(function (name) {\n                  return curContext.format[name] == null;\n                })) {\n                  return false;\n                }\n              } else if (_typeof(binding.format) === 'object') {\n                // all formats must match\n                if (!Object.keys(binding.format).every(function (name) {\n                  if (binding.format[name] === true) return curContext.format[name] != null;\n                  if (binding.format[name] === false) return curContext.format[name] == null;\n                  return (0, _deepEqual2.default)(binding.format[name], curContext.format[name]);\n                })) {\n                  return false;\n                }\n              }\n              if (binding.prefix != null && !binding.prefix.test(curContext.prefix)) return false;\n              if (binding.suffix != null && !binding.suffix.test(curContext.suffix)) return false;\n              return binding.handler.call(_this2, range, curContext) !== true;\n            });\n            if (prevented) {\n              evt.preventDefault();\n            }\n          });\n        }\n      }]);\n      return Keyboard;\n    }(_module2.default);\n    Keyboard.keys = {\n      BACKSPACE: 8,\n      TAB: 9,\n      ENTER: 13,\n      ESCAPE: 27,\n      LEFT: 37,\n      UP: 38,\n      RIGHT: 39,\n      DOWN: 40,\n      DELETE: 46\n    };\n    Keyboard.DEFAULTS = {\n      bindings: {\n        'bold': makeFormatHandler('bold'),\n        'italic': makeFormatHandler('italic'),\n        'underline': makeFormatHandler('underline'),\n        'indent': {\n          // highlight tab or tab at beginning of list, indent or blockquote\n          key: Keyboard.keys.TAB,\n          format: ['blockquote', 'indent', 'list'],\n          handler: function handler(range, context) {\n            if (context.collapsed && context.offset !== 0) return true;\n            this.quill.format('indent', '+1', _quill2.default.sources.USER);\n          }\n        },\n        'outdent': {\n          key: Keyboard.keys.TAB,\n          shiftKey: true,\n          format: ['blockquote', 'indent', 'list'],\n          // highlight tab or tab at beginning of list, indent or blockquote\n          handler: function handler(range, context) {\n            if (context.collapsed && context.offset !== 0) return true;\n            this.quill.format('indent', '-1', _quill2.default.sources.USER);\n          }\n        },\n        'outdent backspace': {\n          key: Keyboard.keys.BACKSPACE,\n          collapsed: true,\n          shiftKey: null,\n          metaKey: null,\n          ctrlKey: null,\n          altKey: null,\n          format: ['indent', 'list'],\n          offset: 0,\n          handler: function handler(range, context) {\n            if (context.format.indent != null) {\n              this.quill.format('indent', '-1', _quill2.default.sources.USER);\n            } else if (context.format.list != null) {\n              this.quill.format('list', false, _quill2.default.sources.USER);\n            }\n          }\n        },\n        'indent code-block': makeCodeBlockHandler(true),\n        'outdent code-block': makeCodeBlockHandler(false),\n        'remove tab': {\n          key: Keyboard.keys.TAB,\n          shiftKey: true,\n          collapsed: true,\n          prefix: /\\t$/,\n          handler: function handler(range) {\n            this.quill.deleteText(range.index - 1, 1, _quill2.default.sources.USER);\n          }\n        },\n        'tab': {\n          key: Keyboard.keys.TAB,\n          handler: function handler(range) {\n            this.quill.history.cutoff();\n            var delta = new _quillDelta2.default().retain(range.index).delete(range.length).insert('\\t');\n            this.quill.updateContents(delta, _quill2.default.sources.USER);\n            this.quill.history.cutoff();\n            this.quill.setSelection(range.index + 1, _quill2.default.sources.SILENT);\n          }\n        },\n        'list empty enter': {\n          key: Keyboard.keys.ENTER,\n          collapsed: true,\n          format: ['list'],\n          empty: true,\n          handler: function handler(range, context) {\n            this.quill.format('list', false, _quill2.default.sources.USER);\n            if (context.format.indent) {\n              this.quill.format('indent', false, _quill2.default.sources.USER);\n            }\n          }\n        },\n        'checklist enter': {\n          key: Keyboard.keys.ENTER,\n          collapsed: true,\n          format: {\n            list: 'checked'\n          },\n          handler: function handler(range) {\n            var _quill$getLine3 = this.quill.getLine(range.index),\n              _quill$getLine4 = _slicedToArray(_quill$getLine3, 2),\n              line = _quill$getLine4[0],\n              offset = _quill$getLine4[1];\n            var formats = (0, _extend2.default)({}, line.formats(), {\n              list: 'checked'\n            });\n            var delta = new _quillDelta2.default().retain(range.index).insert('\\n', formats).retain(line.length() - offset - 1).retain(1, {\n              list: 'unchecked'\n            });\n            this.quill.updateContents(delta, _quill2.default.sources.USER);\n            this.quill.setSelection(range.index + 1, _quill2.default.sources.SILENT);\n            this.quill.scrollIntoView();\n          }\n        },\n        'header enter': {\n          key: Keyboard.keys.ENTER,\n          collapsed: true,\n          format: ['header'],\n          suffix: /^$/,\n          handler: function handler(range, context) {\n            var _quill$getLine5 = this.quill.getLine(range.index),\n              _quill$getLine6 = _slicedToArray(_quill$getLine5, 2),\n              line = _quill$getLine6[0],\n              offset = _quill$getLine6[1];\n            var delta = new _quillDelta2.default().retain(range.index).insert('\\n', context.format).retain(line.length() - offset - 1).retain(1, {\n              header: null\n            });\n            this.quill.updateContents(delta, _quill2.default.sources.USER);\n            this.quill.setSelection(range.index + 1, _quill2.default.sources.SILENT);\n            this.quill.scrollIntoView();\n          }\n        },\n        'list autofill': {\n          key: ' ',\n          collapsed: true,\n          format: {\n            list: false\n          },\n          prefix: /^\\s*?(\\d+\\.|-|\\*|\\[ ?\\]|\\[x\\])$/,\n          handler: function handler(range, context) {\n            var length = context.prefix.length;\n            var _quill$getLine7 = this.quill.getLine(range.index),\n              _quill$getLine8 = _slicedToArray(_quill$getLine7, 2),\n              line = _quill$getLine8[0],\n              offset = _quill$getLine8[1];\n            if (offset > length) return true;\n            var value = void 0;\n            switch (context.prefix.trim()) {\n              case '[]':\n              case '[ ]':\n                value = 'unchecked';\n                break;\n              case '[x]':\n                value = 'checked';\n                break;\n              case '-':\n              case '*':\n                value = 'bullet';\n                break;\n              default:\n                value = 'ordered';\n            }\n            this.quill.insertText(range.index, ' ', _quill2.default.sources.USER);\n            this.quill.history.cutoff();\n            var delta = new _quillDelta2.default().retain(range.index - offset).delete(length + 1).retain(line.length() - 2 - offset).retain(1, {\n              list: value\n            });\n            this.quill.updateContents(delta, _quill2.default.sources.USER);\n            this.quill.history.cutoff();\n            this.quill.setSelection(range.index - length, _quill2.default.sources.SILENT);\n          }\n        },\n        'code exit': {\n          key: Keyboard.keys.ENTER,\n          collapsed: true,\n          format: ['code-block'],\n          prefix: /\\n\\n$/,\n          suffix: /^\\s+$/,\n          handler: function handler(range) {\n            var _quill$getLine9 = this.quill.getLine(range.index),\n              _quill$getLine10 = _slicedToArray(_quill$getLine9, 2),\n              line = _quill$getLine10[0],\n              offset = _quill$getLine10[1];\n            var delta = new _quillDelta2.default().retain(range.index + line.length() - offset - 2).retain(1, {\n              'code-block': null\n            }).delete(1);\n            this.quill.updateContents(delta, _quill2.default.sources.USER);\n          }\n        },\n        'embed left': makeEmbedArrowHandler(Keyboard.keys.LEFT, false),\n        'embed left shift': makeEmbedArrowHandler(Keyboard.keys.LEFT, true),\n        'embed right': makeEmbedArrowHandler(Keyboard.keys.RIGHT, false),\n        'embed right shift': makeEmbedArrowHandler(Keyboard.keys.RIGHT, true)\n      }\n    };\n    function makeEmbedArrowHandler(key, shiftKey) {\n      var _ref3;\n      var where = key === Keyboard.keys.LEFT ? 'prefix' : 'suffix';\n      return _ref3 = {\n        key: key,\n        shiftKey: shiftKey,\n        altKey: null\n      }, _defineProperty(_ref3, where, /^$/), _defineProperty(_ref3, 'handler', function handler(range) {\n        var index = range.index;\n        if (key === Keyboard.keys.RIGHT) {\n          index += range.length + 1;\n        }\n        var _quill$getLeaf3 = this.quill.getLeaf(index),\n          _quill$getLeaf4 = _slicedToArray(_quill$getLeaf3, 1),\n          leaf = _quill$getLeaf4[0];\n        if (!(leaf instanceof _parchment2.default.Embed)) return true;\n        if (key === Keyboard.keys.LEFT) {\n          if (shiftKey) {\n            this.quill.setSelection(range.index - 1, range.length + 1, _quill2.default.sources.USER);\n          } else {\n            this.quill.setSelection(range.index - 1, _quill2.default.sources.USER);\n          }\n        } else {\n          if (shiftKey) {\n            this.quill.setSelection(range.index, range.length + 1, _quill2.default.sources.USER);\n          } else {\n            this.quill.setSelection(range.index + range.length + 1, _quill2.default.sources.USER);\n          }\n        }\n        return false;\n      }), _ref3;\n    }\n    function handleBackspace(range, context) {\n      if (range.index === 0 || this.quill.getLength() <= 1) return;\n      var _quill$getLine11 = this.quill.getLine(range.index),\n        _quill$getLine12 = _slicedToArray(_quill$getLine11, 1),\n        line = _quill$getLine12[0];\n      var formats = {};\n      if (context.offset === 0) {\n        var _quill$getLine13 = this.quill.getLine(range.index - 1),\n          _quill$getLine14 = _slicedToArray(_quill$getLine13, 1),\n          prev = _quill$getLine14[0];\n        if (prev != null && prev.length() > 1) {\n          var curFormats = line.formats();\n          var prevFormats = this.quill.getFormat(range.index - 1, 1);\n          formats = _op2.default.attributes.diff(curFormats, prevFormats) || {};\n        }\n      }\n      // Check for astral symbols\n      var length = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]$/.test(context.prefix) ? 2 : 1;\n      this.quill.deleteText(range.index - length, length, _quill2.default.sources.USER);\n      if (Object.keys(formats).length > 0) {\n        this.quill.formatLine(range.index - length, length, formats, _quill2.default.sources.USER);\n      }\n      this.quill.focus();\n    }\n    function handleDelete(range, context) {\n      // Check for astral symbols\n      var length = /^[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/.test(context.suffix) ? 2 : 1;\n      if (range.index >= this.quill.getLength() - length) return;\n      var formats = {},\n        nextLength = 0;\n      var _quill$getLine15 = this.quill.getLine(range.index),\n        _quill$getLine16 = _slicedToArray(_quill$getLine15, 1),\n        line = _quill$getLine16[0];\n      if (context.offset >= line.length() - 1) {\n        var _quill$getLine17 = this.quill.getLine(range.index + 1),\n          _quill$getLine18 = _slicedToArray(_quill$getLine17, 1),\n          next = _quill$getLine18[0];\n        if (next) {\n          var curFormats = line.formats();\n          var nextFormats = this.quill.getFormat(range.index, 1);\n          formats = _op2.default.attributes.diff(curFormats, nextFormats) || {};\n          nextLength = next.length();\n        }\n      }\n      this.quill.deleteText(range.index, length, _quill2.default.sources.USER);\n      if (Object.keys(formats).length > 0) {\n        this.quill.formatLine(range.index + nextLength - 1, length, formats, _quill2.default.sources.USER);\n      }\n    }\n    function handleDeleteRange(range) {\n      var lines = this.quill.getLines(range);\n      var formats = {};\n      if (lines.length > 1) {\n        var firstFormats = lines[0].formats();\n        var lastFormats = lines[lines.length - 1].formats();\n        formats = _op2.default.attributes.diff(lastFormats, firstFormats) || {};\n      }\n      this.quill.deleteText(range, _quill2.default.sources.USER);\n      if (Object.keys(formats).length > 0) {\n        this.quill.formatLine(range.index, 1, formats, _quill2.default.sources.USER);\n      }\n      this.quill.setSelection(range.index, _quill2.default.sources.SILENT);\n      this.quill.focus();\n    }\n    function handleEnter(range, context) {\n      var _this3 = this;\n      if (range.length > 0) {\n        this.quill.scroll.deleteAt(range.index, range.length); // So we do not trigger text-change\n      }\n      var lineFormats = Object.keys(context.format).reduce(function (lineFormats, format) {\n        if (_parchment2.default.query(format, _parchment2.default.Scope.BLOCK) && !Array.isArray(context.format[format])) {\n          lineFormats[format] = context.format[format];\n        }\n        return lineFormats;\n      }, {});\n      this.quill.insertText(range.index, '\\n', lineFormats, _quill2.default.sources.USER);\n      // Earlier scroll.deleteAt might have messed up our selection,\n      // so insertText's built in selection preservation is not reliable\n      this.quill.setSelection(range.index + 1, _quill2.default.sources.SILENT);\n      this.quill.focus();\n      Object.keys(context.format).forEach(function (name) {\n        if (lineFormats[name] != null) return;\n        if (Array.isArray(context.format[name])) return;\n        if (name === 'link') return;\n        _this3.quill.format(name, context.format[name], _quill2.default.sources.USER);\n      });\n    }\n    function makeCodeBlockHandler(indent) {\n      return {\n        key: Keyboard.keys.TAB,\n        shiftKey: !indent,\n        format: {\n          'code-block': true\n        },\n        handler: function handler(range) {\n          var CodeBlock = _parchment2.default.query('code-block');\n          var index = range.index,\n            length = range.length;\n          var _quill$scroll$descend = this.quill.scroll.descendant(CodeBlock, index),\n            _quill$scroll$descend2 = _slicedToArray(_quill$scroll$descend, 2),\n            block = _quill$scroll$descend2[0],\n            offset = _quill$scroll$descend2[1];\n          if (block == null) return;\n          var scrollIndex = this.quill.getIndex(block);\n          var start = block.newlineIndex(offset, true) + 1;\n          var end = block.newlineIndex(scrollIndex + offset + length);\n          var lines = block.domNode.textContent.slice(start, end).split('\\n');\n          offset = 0;\n          lines.forEach(function (line, i) {\n            if (indent) {\n              block.insertAt(start + offset, CodeBlock.TAB);\n              offset += CodeBlock.TAB.length;\n              if (i === 0) {\n                index += CodeBlock.TAB.length;\n              } else {\n                length += CodeBlock.TAB.length;\n              }\n            } else if (line.startsWith(CodeBlock.TAB)) {\n              block.deleteAt(start + offset, CodeBlock.TAB.length);\n              offset -= CodeBlock.TAB.length;\n              if (i === 0) {\n                index -= CodeBlock.TAB.length;\n              } else {\n                length -= CodeBlock.TAB.length;\n              }\n            }\n            offset += line.length + 1;\n          });\n          this.quill.update(_quill2.default.sources.USER);\n          this.quill.setSelection(index, length, _quill2.default.sources.SILENT);\n        }\n      };\n    }\n    function makeFormatHandler(format) {\n      return {\n        key: format[0].toUpperCase(),\n        shortKey: true,\n        handler: function handler(range, context) {\n          this.quill.format(format, !context.format[format], _quill2.default.sources.USER);\n        }\n      };\n    }\n    function normalize(binding) {\n      if (typeof binding === 'string' || typeof binding === 'number') {\n        return normalize({\n          key: binding\n        });\n      }\n      if ((typeof binding === 'undefined' ? 'undefined' : _typeof(binding)) === 'object') {\n        binding = (0, _clone2.default)(binding, false);\n      }\n      if (typeof binding.key === 'string') {\n        if (Keyboard.keys[binding.key.toUpperCase()] != null) {\n          binding.key = Keyboard.keys[binding.key.toUpperCase()];\n        } else if (binding.key.length === 1) {\n          binding.key = binding.key.toUpperCase().charCodeAt(0);\n        } else {\n          return null;\n        }\n      }\n      if (binding.shortKey) {\n        binding[SHORTKEY] = binding.shortKey;\n        delete binding.shortKey;\n      }\n      return binding;\n    }\n    exports.default = Keyboard;\n    exports.SHORTKEY = SHORTKEY;\n\n    /***/\n  }), ( /* 24 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Cursor = function (_Parchment$Embed) {\n      _inherits(Cursor, _Parchment$Embed);\n      _createClass(Cursor, null, [{\n        key: 'value',\n        value: function value() {\n          return undefined;\n        }\n      }]);\n      function Cursor(domNode, selection) {\n        _classCallCheck(this, Cursor);\n        var _this = _possibleConstructorReturn(this, (Cursor.__proto__ || Object.getPrototypeOf(Cursor)).call(this, domNode));\n        _this.selection = selection;\n        _this.textNode = document.createTextNode(Cursor.CONTENTS);\n        _this.domNode.appendChild(_this.textNode);\n        _this._length = 0;\n        return _this;\n      }\n      _createClass(Cursor, [{\n        key: 'detach',\n        value: function detach() {\n          // super.detach() will also clear domNode.__blot\n          if (this.parent != null) this.parent.removeChild(this);\n        }\n      }, {\n        key: 'format',\n        value: function format(name, value) {\n          if (this._length !== 0) {\n            return _get(Cursor.prototype.__proto__ || Object.getPrototypeOf(Cursor.prototype), 'format', this).call(this, name, value);\n          }\n          var target = this,\n            index = 0;\n          while (target != null && target.statics.scope !== _parchment2.default.Scope.BLOCK_BLOT) {\n            index += target.offset(target.parent);\n            target = target.parent;\n          }\n          if (target != null) {\n            this._length = Cursor.CONTENTS.length;\n            target.optimize();\n            target.formatAt(index, Cursor.CONTENTS.length, name, value);\n            this._length = 0;\n          }\n        }\n      }, {\n        key: 'index',\n        value: function index(node, offset) {\n          if (node === this.textNode) return 0;\n          return _get(Cursor.prototype.__proto__ || Object.getPrototypeOf(Cursor.prototype), 'index', this).call(this, node, offset);\n        }\n      }, {\n        key: 'length',\n        value: function length() {\n          return this._length;\n        }\n      }, {\n        key: 'position',\n        value: function position() {\n          return [this.textNode, this.textNode.data.length];\n        }\n      }, {\n        key: 'remove',\n        value: function remove() {\n          _get(Cursor.prototype.__proto__ || Object.getPrototypeOf(Cursor.prototype), 'remove', this).call(this);\n          this.parent = null;\n        }\n      }, {\n        key: 'restore',\n        value: function restore() {\n          if (this.selection.composing || this.parent == null) return;\n          var textNode = this.textNode;\n          var range = this.selection.getNativeRange();\n          var restoreText = void 0,\n            start = void 0,\n            end = void 0;\n          if (range != null && range.start.node === textNode && range.end.node === textNode) {\n            var _ref = [textNode, range.start.offset, range.end.offset];\n            restoreText = _ref[0];\n            start = _ref[1];\n            end = _ref[2];\n          }\n          // Link format will insert text outside of anchor tag\n          while (this.domNode.lastChild != null && this.domNode.lastChild !== this.textNode) {\n            this.domNode.parentNode.insertBefore(this.domNode.lastChild, this.domNode);\n          }\n          if (this.textNode.data !== Cursor.CONTENTS) {\n            var text = this.textNode.data.split(Cursor.CONTENTS).join('');\n            if (this.next instanceof _text2.default) {\n              restoreText = this.next.domNode;\n              this.next.insertAt(0, text);\n              this.textNode.data = Cursor.CONTENTS;\n            } else {\n              this.textNode.data = text;\n              this.parent.insertBefore(_parchment2.default.create(this.textNode), this);\n              this.textNode = document.createTextNode(Cursor.CONTENTS);\n              this.domNode.appendChild(this.textNode);\n            }\n          }\n          this.remove();\n          if (start != null) {\n            var _map = [start, end].map(function (offset) {\n              return Math.max(0, Math.min(restoreText.data.length, offset - 1));\n            });\n            var _map2 = _slicedToArray(_map, 2);\n            start = _map2[0];\n            end = _map2[1];\n            return {\n              startNode: restoreText,\n              startOffset: start,\n              endNode: restoreText,\n              endOffset: end\n            };\n          }\n        }\n      }, {\n        key: 'update',\n        value: function update(mutations, context) {\n          var _this2 = this;\n          if (mutations.some(function (mutation) {\n            return mutation.type === 'characterData' && mutation.target === _this2.textNode;\n          })) {\n            var range = this.restore();\n            if (range) context.range = range;\n          }\n        }\n      }, {\n        key: 'value',\n        value: function value() {\n          return '';\n        }\n      }]);\n      return Cursor;\n    }(_parchment2.default.Embed);\n    Cursor.blotName = 'cursor';\n    Cursor.className = 'ql-cursor';\n    Cursor.tagName = 'span';\n    Cursor.CONTENTS = '\\uFEFF'; // Zero width no break space\n\n    exports.default = Cursor;\n\n    /***/\n  }), ( /* 25 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Container = function (_Parchment$Container) {\n      _inherits(Container, _Parchment$Container);\n      function Container() {\n        _classCallCheck(this, Container);\n        return _possibleConstructorReturn(this, (Container.__proto__ || Object.getPrototypeOf(Container)).apply(this, arguments));\n      }\n      return Container;\n    }(_parchment2.default.Container);\n    Container.allowedChildren = [_block2.default, _block.BlockEmbed, Container];\n    exports.default = Container;\n\n    /***/\n  }), ( /* 26 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.ColorStyle = exports.ColorClass = exports.ColorAttributor = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ColorAttributor = function (_Parchment$Attributor) {\n      _inherits(ColorAttributor, _Parchment$Attributor);\n      function ColorAttributor() {\n        _classCallCheck(this, ColorAttributor);\n        return _possibleConstructorReturn(this, (ColorAttributor.__proto__ || Object.getPrototypeOf(ColorAttributor)).apply(this, arguments));\n      }\n      _createClass(ColorAttributor, [{\n        key: 'value',\n        value: function value(domNode) {\n          var value = _get(ColorAttributor.prototype.__proto__ || Object.getPrototypeOf(ColorAttributor.prototype), 'value', this).call(this, domNode);\n          if (!value.startsWith('rgb(')) return value;\n          value = value.replace(/^[^\\d]+/, '').replace(/[^\\d]+$/, '');\n          return '#' + value.split(',').map(function (component) {\n            return ('00' + parseInt(component).toString(16)).slice(-2);\n          }).join('');\n        }\n      }]);\n      return ColorAttributor;\n    }(_parchment2.default.Attributor.Style);\n    var ColorClass = new _parchment2.default.Attributor.Class('color', 'ql-color', {\n      scope: _parchment2.default.Scope.INLINE\n    });\n    var ColorStyle = new ColorAttributor('color', 'color', {\n      scope: _parchment2.default.Scope.INLINE\n    });\n    exports.ColorAttributor = ColorAttributor;\n    exports.ColorClass = ColorClass;\n    exports.ColorStyle = ColorStyle;\n\n    /***/\n  }), ( /* 27 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.sanitize = exports.default = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Link = function (_Inline) {\n      _inherits(Link, _Inline);\n      function Link() {\n        _classCallCheck(this, Link);\n        return _possibleConstructorReturn(this, (Link.__proto__ || Object.getPrototypeOf(Link)).apply(this, arguments));\n      }\n      _createClass(Link, [{\n        key: 'format',\n        value: function format(name, value) {\n          if (name !== this.statics.blotName || !value) return _get(Link.prototype.__proto__ || Object.getPrototypeOf(Link.prototype), 'format', this).call(this, name, value);\n          value = this.constructor.sanitize(value);\n          this.domNode.setAttribute('href', value);\n        }\n      }], [{\n        key: 'create',\n        value: function create(value) {\n          var node = _get(Link.__proto__ || Object.getPrototypeOf(Link), 'create', this).call(this, value);\n          value = this.sanitize(value);\n          node.setAttribute('href', value);\n          node.setAttribute('rel', 'noopener noreferrer');\n          node.setAttribute('target', '_blank');\n          return node;\n        }\n      }, {\n        key: 'formats',\n        value: function formats(domNode) {\n          return domNode.getAttribute('href');\n        }\n      }, {\n        key: 'sanitize',\n        value: function sanitize(url) {\n          return _sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n        }\n      }]);\n      return Link;\n    }(_inline2.default);\n    Link.blotName = 'link';\n    Link.tagName = 'A';\n    Link.SANITIZED_URL = 'about:blank';\n    Link.PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel'];\n    function _sanitize(url, protocols) {\n      var anchor = document.createElement('a');\n      anchor.href = url;\n      var protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n      return protocols.indexOf(protocol) > -1;\n    }\n    exports.default = Link;\n    exports.sanitize = _sanitize;\n\n    /***/\n  }), ( /* 28 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _keyboard = __webpack_require__(23);\n    var _keyboard2 = _interopRequireDefault(_keyboard);\n    var _dropdown = __webpack_require__(107);\n    var _dropdown2 = _interopRequireDefault(_dropdown);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var optionsCounter = 0;\n    function toggleAriaAttribute(element, attribute) {\n      element.setAttribute(attribute, !(element.getAttribute(attribute) === 'true'));\n    }\n    var Picker = function () {\n      function Picker(select) {\n        var _this = this;\n        _classCallCheck(this, Picker);\n        this.select = select;\n        this.container = document.createElement('span');\n        this.buildPicker();\n        this.select.style.display = 'none';\n        this.select.parentNode.insertBefore(this.container, this.select);\n        this.label.addEventListener('mousedown', function () {\n          _this.togglePicker();\n        });\n        this.label.addEventListener('keydown', function (event) {\n          switch (event.keyCode) {\n            // Allows the \"Enter\" key to open the picker\n            case _keyboard2.default.keys.ENTER:\n              _this.togglePicker();\n              break;\n\n            // Allows the \"Escape\" key to close the picker\n            case _keyboard2.default.keys.ESCAPE:\n              _this.escape();\n              event.preventDefault();\n              break;\n            default:\n          }\n        });\n        this.select.addEventListener('change', this.update.bind(this));\n      }\n      _createClass(Picker, [{\n        key: 'togglePicker',\n        value: function togglePicker() {\n          this.container.classList.toggle('ql-expanded');\n          // Toggle aria-expanded and aria-hidden to make the picker accessible\n          toggleAriaAttribute(this.label, 'aria-expanded');\n          toggleAriaAttribute(this.options, 'aria-hidden');\n        }\n      }, {\n        key: 'buildItem',\n        value: function buildItem(option) {\n          var _this2 = this;\n          var item = document.createElement('span');\n          item.tabIndex = '0';\n          item.setAttribute('role', 'button');\n          item.classList.add('ql-picker-item');\n          if (option.hasAttribute('value')) {\n            item.setAttribute('data-value', option.getAttribute('value'));\n          }\n          if (option.textContent) {\n            item.setAttribute('data-label', option.textContent);\n          }\n          item.addEventListener('click', function () {\n            _this2.selectItem(item, true);\n          });\n          item.addEventListener('keydown', function (event) {\n            switch (event.keyCode) {\n              // Allows the \"Enter\" key to select an item\n              case _keyboard2.default.keys.ENTER:\n                _this2.selectItem(item, true);\n                event.preventDefault();\n                break;\n\n              // Allows the \"Escape\" key to close the picker\n              case _keyboard2.default.keys.ESCAPE:\n                _this2.escape();\n                event.preventDefault();\n                break;\n              default:\n            }\n          });\n          return item;\n        }\n      }, {\n        key: 'buildLabel',\n        value: function buildLabel() {\n          var label = document.createElement('span');\n          label.classList.add('ql-picker-label');\n          label.innerHTML = _dropdown2.default;\n          label.tabIndex = '0';\n          label.setAttribute('role', 'button');\n          label.setAttribute('aria-expanded', 'false');\n          this.container.appendChild(label);\n          return label;\n        }\n      }, {\n        key: 'buildOptions',\n        value: function buildOptions() {\n          var _this3 = this;\n          var options = document.createElement('span');\n          options.classList.add('ql-picker-options');\n\n          // Don't want screen readers to read this until options are visible\n          options.setAttribute('aria-hidden', 'true');\n          options.tabIndex = '-1';\n\n          // Need a unique id for aria-controls\n          options.id = 'ql-picker-options-' + optionsCounter;\n          optionsCounter += 1;\n          this.label.setAttribute('aria-controls', options.id);\n          this.options = options;\n          [].slice.call(this.select.options).forEach(function (option) {\n            var item = _this3.buildItem(option);\n            options.appendChild(item);\n            if (option.selected === true) {\n              _this3.selectItem(item);\n            }\n          });\n          this.container.appendChild(options);\n        }\n      }, {\n        key: 'buildPicker',\n        value: function buildPicker() {\n          var _this4 = this;\n          [].slice.call(this.select.attributes).forEach(function (item) {\n            _this4.container.setAttribute(item.name, item.value);\n          });\n          this.container.classList.add('ql-picker');\n          this.label = this.buildLabel();\n          this.buildOptions();\n        }\n      }, {\n        key: 'escape',\n        value: function escape() {\n          var _this5 = this;\n\n          // Close menu and return focus to trigger label\n          this.close();\n          // Need setTimeout for accessibility to ensure that the browser executes\n          // focus on the next process thread and after any DOM content changes\n          setTimeout(function () {\n            return _this5.label.focus();\n          }, 1);\n        }\n      }, {\n        key: 'close',\n        value: function close() {\n          this.container.classList.remove('ql-expanded');\n          this.label.setAttribute('aria-expanded', 'false');\n          this.options.setAttribute('aria-hidden', 'true');\n        }\n      }, {\n        key: 'selectItem',\n        value: function selectItem(item) {\n          var trigger = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n          var selected = this.container.querySelector('.ql-selected');\n          if (item === selected) return;\n          if (selected != null) {\n            selected.classList.remove('ql-selected');\n          }\n          if (item == null) return;\n          item.classList.add('ql-selected');\n          this.select.selectedIndex = [].indexOf.call(item.parentNode.children, item);\n          if (item.hasAttribute('data-value')) {\n            this.label.setAttribute('data-value', item.getAttribute('data-value'));\n          } else {\n            this.label.removeAttribute('data-value');\n          }\n          if (item.hasAttribute('data-label')) {\n            this.label.setAttribute('data-label', item.getAttribute('data-label'));\n          } else {\n            this.label.removeAttribute('data-label');\n          }\n          if (trigger) {\n            if (typeof Event === 'function') {\n              this.select.dispatchEvent(new Event('change'));\n            } else if ((typeof Event === 'undefined' ? 'undefined' : _typeof(Event)) === 'object') {\n              // IE11\n              var event = document.createEvent('Event');\n              event.initEvent('change', true, true);\n              this.select.dispatchEvent(event);\n            }\n            this.close();\n          }\n        }\n      }, {\n        key: 'update',\n        value: function update() {\n          var option = void 0;\n          if (this.select.selectedIndex > -1) {\n            var item = this.container.querySelector('.ql-picker-options').children[this.select.selectedIndex];\n            option = this.select.options[this.select.selectedIndex];\n            this.selectItem(item);\n          } else {\n            this.selectItem(null);\n          }\n          var isActive = option != null && option !== this.select.querySelector('option[selected]');\n          this.label.classList.toggle('ql-active', isActive);\n        }\n      }]);\n      return Picker;\n    }();\n    exports.default = Picker;\n\n    /***/\n  }), ( /* 29 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    var _break = __webpack_require__(16);\n    var _break2 = _interopRequireDefault(_break);\n    var _container = __webpack_require__(25);\n    var _container2 = _interopRequireDefault(_container);\n    var _cursor = __webpack_require__(24);\n    var _cursor2 = _interopRequireDefault(_cursor);\n    var _embed = __webpack_require__(35);\n    var _embed2 = _interopRequireDefault(_embed);\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    var _scroll = __webpack_require__(22);\n    var _scroll2 = _interopRequireDefault(_scroll);\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    var _clipboard = __webpack_require__(55);\n    var _clipboard2 = _interopRequireDefault(_clipboard);\n    var _history = __webpack_require__(42);\n    var _history2 = _interopRequireDefault(_history);\n    var _keyboard = __webpack_require__(23);\n    var _keyboard2 = _interopRequireDefault(_keyboard);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    _quill2.default.register({\n      'blots/block': _block2.default,\n      'blots/block/embed': _block.BlockEmbed,\n      'blots/break': _break2.default,\n      'blots/container': _container2.default,\n      'blots/cursor': _cursor2.default,\n      'blots/embed': _embed2.default,\n      'blots/inline': _inline2.default,\n      'blots/scroll': _scroll2.default,\n      'blots/text': _text2.default,\n      'modules/clipboard': _clipboard2.default,\n      'modules/history': _history2.default,\n      'modules/keyboard': _keyboard2.default\n    });\n    _parchment2.default.register(_block2.default, _break2.default, _cursor2.default, _inline2.default, _scroll2.default, _text2.default);\n    exports.default = _quill2.default;\n\n    /***/\n  }), ( /* 30 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var Registry = __webpack_require__(1);\n    var ShadowBlot = /** @class */function () {\n      function ShadowBlot(domNode) {\n        this.domNode = domNode;\n        // @ts-ignore\n        this.domNode[Registry.DATA_KEY] = {\n          blot: this\n        };\n      }\n      Object.defineProperty(ShadowBlot.prototype, \"statics\", {\n        // Hack for accessing inherited static methods\n        get: function () {\n          return this.constructor;\n        },\n        enumerable: true,\n        configurable: true\n      });\n      ShadowBlot.create = function (value) {\n        if (this.tagName == null) {\n          throw new Registry.ParchmentError('Blot definition missing tagName');\n        }\n        var node;\n        if (Array.isArray(this.tagName)) {\n          if (typeof value === 'string') {\n            value = value.toUpperCase();\n            if (parseInt(value).toString() === value) {\n              value = parseInt(value);\n            }\n          }\n          if (typeof value === 'number') {\n            node = document.createElement(this.tagName[value - 1]);\n          } else if (this.tagName.indexOf(value) > -1) {\n            node = document.createElement(value);\n          } else {\n            node = document.createElement(this.tagName[0]);\n          }\n        } else {\n          node = document.createElement(this.tagName);\n        }\n        if (this.className) {\n          node.classList.add(this.className);\n        }\n        return node;\n      };\n      ShadowBlot.prototype.attach = function () {\n        if (this.parent != null) {\n          this.scroll = this.parent.scroll;\n        }\n      };\n      ShadowBlot.prototype.clone = function () {\n        var domNode = this.domNode.cloneNode(false);\n        return Registry.create(domNode);\n      };\n      ShadowBlot.prototype.detach = function () {\n        if (this.parent != null) this.parent.removeChild(this);\n        // @ts-ignore\n        delete this.domNode[Registry.DATA_KEY];\n      };\n      ShadowBlot.prototype.deleteAt = function (index, length) {\n        var blot = this.isolate(index, length);\n        blot.remove();\n      };\n      ShadowBlot.prototype.formatAt = function (index, length, name, value) {\n        var blot = this.isolate(index, length);\n        if (Registry.query(name, Registry.Scope.BLOT) != null && value) {\n          blot.wrap(name, value);\n        } else if (Registry.query(name, Registry.Scope.ATTRIBUTE) != null) {\n          var parent = Registry.create(this.statics.scope);\n          blot.wrap(parent);\n          parent.format(name, value);\n        }\n      };\n      ShadowBlot.prototype.insertAt = function (index, value, def) {\n        var blot = def == null ? Registry.create('text', value) : Registry.create(value, def);\n        var ref = this.split(index);\n        this.parent.insertBefore(blot, ref);\n      };\n      ShadowBlot.prototype.insertInto = function (parentBlot, refBlot) {\n        if (refBlot === void 0) {\n          refBlot = null;\n        }\n        if (this.parent != null) {\n          this.parent.children.remove(this);\n        }\n        var refDomNode = null;\n        parentBlot.children.insertBefore(this, refBlot);\n        if (refBlot != null) {\n          refDomNode = refBlot.domNode;\n        }\n        if (this.domNode.parentNode != parentBlot.domNode || this.domNode.nextSibling != refDomNode) {\n          parentBlot.domNode.insertBefore(this.domNode, refDomNode);\n        }\n        this.parent = parentBlot;\n        this.attach();\n      };\n      ShadowBlot.prototype.isolate = function (index, length) {\n        var target = this.split(index);\n        target.split(length);\n        return target;\n      };\n      ShadowBlot.prototype.length = function () {\n        return 1;\n      };\n      ShadowBlot.prototype.offset = function (root) {\n        if (root === void 0) {\n          root = this.parent;\n        }\n        if (this.parent == null || this == root) return 0;\n        return this.parent.children.offset(this) + this.parent.offset(root);\n      };\n      ShadowBlot.prototype.optimize = function (context) {\n        // TODO clean up once we use WeakMap\n        // @ts-ignore\n        if (this.domNode[Registry.DATA_KEY] != null) {\n          // @ts-ignore\n          delete this.domNode[Registry.DATA_KEY].mutations;\n        }\n      };\n      ShadowBlot.prototype.remove = function () {\n        if (this.domNode.parentNode != null) {\n          this.domNode.parentNode.removeChild(this.domNode);\n        }\n        this.detach();\n      };\n      ShadowBlot.prototype.replace = function (target) {\n        if (target.parent == null) return;\n        target.parent.insertBefore(this, target.next);\n        target.remove();\n      };\n      ShadowBlot.prototype.replaceWith = function (name, value) {\n        var replacement = typeof name === 'string' ? Registry.create(name, value) : name;\n        replacement.replace(this);\n        return replacement;\n      };\n      ShadowBlot.prototype.split = function (index, force) {\n        return index === 0 ? this : this.next;\n      };\n      ShadowBlot.prototype.update = function (mutations, context) {\n        // Nothing to do by default\n      };\n      ShadowBlot.prototype.wrap = function (name, value) {\n        var wrapper = typeof name === 'string' ? Registry.create(name, value) : name;\n        if (this.parent != null) {\n          this.parent.insertBefore(wrapper, this.next);\n        }\n        wrapper.appendChild(this);\n        return wrapper;\n      };\n      ShadowBlot.blotName = 'abstract';\n      return ShadowBlot;\n    }();\n    exports.default = ShadowBlot;\n\n    /***/\n  }), ( /* 31 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var attributor_1 = __webpack_require__(12);\n    var class_1 = __webpack_require__(32);\n    var style_1 = __webpack_require__(33);\n    var Registry = __webpack_require__(1);\n    var AttributorStore = /** @class */function () {\n      function AttributorStore(domNode) {\n        this.attributes = {};\n        this.domNode = domNode;\n        this.build();\n      }\n      AttributorStore.prototype.attribute = function (attribute, value) {\n        // verb\n        if (value) {\n          if (attribute.add(this.domNode, value)) {\n            if (attribute.value(this.domNode) != null) {\n              this.attributes[attribute.attrName] = attribute;\n            } else {\n              delete this.attributes[attribute.attrName];\n            }\n          }\n        } else {\n          attribute.remove(this.domNode);\n          delete this.attributes[attribute.attrName];\n        }\n      };\n      AttributorStore.prototype.build = function () {\n        var _this = this;\n        this.attributes = {};\n        var attributes = attributor_1.default.keys(this.domNode);\n        var classes = class_1.default.keys(this.domNode);\n        var styles = style_1.default.keys(this.domNode);\n        attributes.concat(classes).concat(styles).forEach(function (name) {\n          var attr = Registry.query(name, Registry.Scope.ATTRIBUTE);\n          if (attr instanceof attributor_1.default) {\n            _this.attributes[attr.attrName] = attr;\n          }\n        });\n      };\n      AttributorStore.prototype.copy = function (target) {\n        var _this = this;\n        Object.keys(this.attributes).forEach(function (key) {\n          var value = _this.attributes[key].value(_this.domNode);\n          target.format(key, value);\n        });\n      };\n      AttributorStore.prototype.move = function (target) {\n        var _this = this;\n        this.copy(target);\n        Object.keys(this.attributes).forEach(function (key) {\n          _this.attributes[key].remove(_this.domNode);\n        });\n        this.attributes = {};\n      };\n      AttributorStore.prototype.values = function () {\n        var _this = this;\n        return Object.keys(this.attributes).reduce(function (attributes, name) {\n          attributes[name] = _this.attributes[name].value(_this.domNode);\n          return attributes;\n        }, {});\n      };\n      return AttributorStore;\n    }();\n    exports.default = AttributorStore;\n\n    /***/\n  }), ( /* 32 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var attributor_1 = __webpack_require__(12);\n    function match(node, prefix) {\n      var className = node.getAttribute('class') || '';\n      return className.split(/\\s+/).filter(function (name) {\n        return name.indexOf(prefix + \"-\") === 0;\n      });\n    }\n    var ClassAttributor = /** @class */function (_super) {\n      __extends(ClassAttributor, _super);\n      function ClassAttributor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      ClassAttributor.keys = function (node) {\n        return (node.getAttribute('class') || '').split(/\\s+/).map(function (name) {\n          return name.split('-').slice(0, -1).join('-');\n        });\n      };\n      ClassAttributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value)) return false;\n        this.remove(node);\n        node.classList.add(this.keyName + \"-\" + value);\n        return true;\n      };\n      ClassAttributor.prototype.remove = function (node) {\n        var matches = match(node, this.keyName);\n        matches.forEach(function (name) {\n          node.classList.remove(name);\n        });\n        if (node.classList.length === 0) {\n          node.removeAttribute('class');\n        }\n      };\n      ClassAttributor.prototype.value = function (node) {\n        var result = match(node, this.keyName)[0] || '';\n        var value = result.slice(this.keyName.length + 1); // +1 for hyphen\n        return this.canAdd(node, value) ? value : '';\n      };\n      return ClassAttributor;\n    }(attributor_1.default);\n    exports.default = ClassAttributor;\n\n    /***/\n  }), ( /* 33 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var attributor_1 = __webpack_require__(12);\n    function camelize(name) {\n      var parts = name.split('-');\n      var rest = parts.slice(1).map(function (part) {\n        return part[0].toUpperCase() + part.slice(1);\n      }).join('');\n      return parts[0] + rest;\n    }\n    var StyleAttributor = /** @class */function (_super) {\n      __extends(StyleAttributor, _super);\n      function StyleAttributor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      StyleAttributor.keys = function (node) {\n        return (node.getAttribute('style') || '').split(';').map(function (value) {\n          var arr = value.split(':');\n          return arr[0].trim();\n        });\n      };\n      StyleAttributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value)) return false;\n        // @ts-ignore\n        node.style[camelize(this.keyName)] = value;\n        return true;\n      };\n      StyleAttributor.prototype.remove = function (node) {\n        // @ts-ignore\n        node.style[camelize(this.keyName)] = '';\n        if (!node.getAttribute('style')) {\n          node.removeAttribute('style');\n        }\n      };\n      StyleAttributor.prototype.value = function (node) {\n        // @ts-ignore\n        var value = node.style[camelize(this.keyName)];\n        return this.canAdd(node, value) ? value : '';\n      };\n      return StyleAttributor;\n    }(attributor_1.default);\n    exports.default = StyleAttributor;\n\n    /***/\n  }), ( /* 34 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var Theme = function () {\n      function Theme(quill, options) {\n        _classCallCheck(this, Theme);\n        this.quill = quill;\n        this.options = options;\n        this.modules = {};\n      }\n      _createClass(Theme, [{\n        key: 'init',\n        value: function init() {\n          var _this = this;\n          Object.keys(this.options.modules).forEach(function (name) {\n            if (_this.modules[name] == null) {\n              _this.addModule(name);\n            }\n          });\n        }\n      }, {\n        key: 'addModule',\n        value: function addModule(name) {\n          var moduleClass = this.quill.constructor.import('modules/' + name);\n          this.modules[name] = new moduleClass(this.quill, this.options.modules[name] || {});\n          return this.modules[name];\n        }\n      }]);\n      return Theme;\n    }();\n    Theme.DEFAULTS = {\n      modules: {}\n    };\n    Theme.themes = {\n      'default': Theme\n    };\n    exports.default = Theme;\n\n    /***/\n  }), ( /* 35 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _text = __webpack_require__(7);\n    var _text2 = _interopRequireDefault(_text);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var GUARD_TEXT = '\\uFEFF';\n    var Embed = function (_Parchment$Embed) {\n      _inherits(Embed, _Parchment$Embed);\n      function Embed(node) {\n        _classCallCheck(this, Embed);\n        var _this = _possibleConstructorReturn(this, (Embed.__proto__ || Object.getPrototypeOf(Embed)).call(this, node));\n        _this.contentNode = document.createElement('span');\n        _this.contentNode.setAttribute('contenteditable', false);\n        [].slice.call(_this.domNode.childNodes).forEach(function (childNode) {\n          _this.contentNode.appendChild(childNode);\n        });\n        _this.leftGuard = document.createTextNode(GUARD_TEXT);\n        _this.rightGuard = document.createTextNode(GUARD_TEXT);\n        _this.domNode.appendChild(_this.leftGuard);\n        _this.domNode.appendChild(_this.contentNode);\n        _this.domNode.appendChild(_this.rightGuard);\n        return _this;\n      }\n      _createClass(Embed, [{\n        key: 'index',\n        value: function index(node, offset) {\n          if (node === this.leftGuard) return 0;\n          if (node === this.rightGuard) return 1;\n          return _get(Embed.prototype.__proto__ || Object.getPrototypeOf(Embed.prototype), 'index', this).call(this, node, offset);\n        }\n      }, {\n        key: 'restore',\n        value: function restore(node) {\n          var range = void 0,\n            textNode = void 0;\n          var text = node.data.split(GUARD_TEXT).join('');\n          if (node === this.leftGuard) {\n            if (this.prev instanceof _text2.default) {\n              var prevLength = this.prev.length();\n              this.prev.insertAt(prevLength, text);\n              range = {\n                startNode: this.prev.domNode,\n                startOffset: prevLength + text.length\n              };\n            } else {\n              textNode = document.createTextNode(text);\n              this.parent.insertBefore(_parchment2.default.create(textNode), this);\n              range = {\n                startNode: textNode,\n                startOffset: text.length\n              };\n            }\n          } else if (node === this.rightGuard) {\n            if (this.next instanceof _text2.default) {\n              this.next.insertAt(0, text);\n              range = {\n                startNode: this.next.domNode,\n                startOffset: text.length\n              };\n            } else {\n              textNode = document.createTextNode(text);\n              this.parent.insertBefore(_parchment2.default.create(textNode), this.next);\n              range = {\n                startNode: textNode,\n                startOffset: text.length\n              };\n            }\n          }\n          node.data = GUARD_TEXT;\n          return range;\n        }\n      }, {\n        key: 'update',\n        value: function update(mutations, context) {\n          var _this2 = this;\n          mutations.forEach(function (mutation) {\n            if (mutation.type === 'characterData' && (mutation.target === _this2.leftGuard || mutation.target === _this2.rightGuard)) {\n              var range = _this2.restore(mutation.target);\n              if (range) context.range = range;\n            }\n          });\n        }\n      }]);\n      return Embed;\n    }(_parchment2.default.Embed);\n    exports.default = Embed;\n\n    /***/\n  }), ( /* 36 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.AlignStyle = exports.AlignClass = exports.AlignAttribute = undefined;\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    var config = {\n      scope: _parchment2.default.Scope.BLOCK,\n      whitelist: ['right', 'center', 'justify']\n    };\n    var AlignAttribute = new _parchment2.default.Attributor.Attribute('align', 'align', config);\n    var AlignClass = new _parchment2.default.Attributor.Class('align', 'ql-align', config);\n    var AlignStyle = new _parchment2.default.Attributor.Style('align', 'text-align', config);\n    exports.AlignAttribute = AlignAttribute;\n    exports.AlignClass = AlignClass;\n    exports.AlignStyle = AlignStyle;\n\n    /***/\n  }), ( /* 37 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.BackgroundStyle = exports.BackgroundClass = undefined;\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _color = __webpack_require__(26);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    var BackgroundClass = new _parchment2.default.Attributor.Class('background', 'ql-bg', {\n      scope: _parchment2.default.Scope.INLINE\n    });\n    var BackgroundStyle = new _color.ColorAttributor('background', 'background-color', {\n      scope: _parchment2.default.Scope.INLINE\n    });\n    exports.BackgroundClass = BackgroundClass;\n    exports.BackgroundStyle = BackgroundStyle;\n\n    /***/\n  }), ( /* 38 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.DirectionStyle = exports.DirectionClass = exports.DirectionAttribute = undefined;\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    var config = {\n      scope: _parchment2.default.Scope.BLOCK,\n      whitelist: ['rtl']\n    };\n    var DirectionAttribute = new _parchment2.default.Attributor.Attribute('direction', 'dir', config);\n    var DirectionClass = new _parchment2.default.Attributor.Class('direction', 'ql-direction', config);\n    var DirectionStyle = new _parchment2.default.Attributor.Style('direction', 'direction', config);\n    exports.DirectionAttribute = DirectionAttribute;\n    exports.DirectionClass = DirectionClass;\n    exports.DirectionStyle = DirectionStyle;\n\n    /***/\n  }), ( /* 39 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.FontClass = exports.FontStyle = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var config = {\n      scope: _parchment2.default.Scope.INLINE,\n      whitelist: ['serif', 'monospace']\n    };\n    var FontClass = new _parchment2.default.Attributor.Class('font', 'ql-font', config);\n    var FontStyleAttributor = function (_Parchment$Attributor) {\n      _inherits(FontStyleAttributor, _Parchment$Attributor);\n      function FontStyleAttributor() {\n        _classCallCheck(this, FontStyleAttributor);\n        return _possibleConstructorReturn(this, (FontStyleAttributor.__proto__ || Object.getPrototypeOf(FontStyleAttributor)).apply(this, arguments));\n      }\n      _createClass(FontStyleAttributor, [{\n        key: 'value',\n        value: function value(node) {\n          return _get(FontStyleAttributor.prototype.__proto__ || Object.getPrototypeOf(FontStyleAttributor.prototype), 'value', this).call(this, node).replace(/[\"']/g, '');\n        }\n      }]);\n      return FontStyleAttributor;\n    }(_parchment2.default.Attributor.Style);\n    var FontStyle = new FontStyleAttributor('font', 'font-family', config);\n    exports.FontStyle = FontStyle;\n    exports.FontClass = FontClass;\n\n    /***/\n  }), ( /* 40 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.SizeStyle = exports.SizeClass = undefined;\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    var SizeClass = new _parchment2.default.Attributor.Class('size', 'ql-size', {\n      scope: _parchment2.default.Scope.INLINE,\n      whitelist: ['small', 'large', 'huge']\n    });\n    var SizeStyle = new _parchment2.default.Attributor.Style('size', 'font-size', {\n      scope: _parchment2.default.Scope.INLINE,\n      whitelist: ['10px', '18px', '32px']\n    });\n    exports.SizeClass = SizeClass;\n    exports.SizeStyle = SizeStyle;\n\n    /***/\n  }), ( /* 41 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    module.exports = {\n      'align': {\n        '': __webpack_require__(76),\n        'center': __webpack_require__(77),\n        'right': __webpack_require__(78),\n        'justify': __webpack_require__(79)\n      },\n      'background': __webpack_require__(80),\n      'blockquote': __webpack_require__(81),\n      'bold': __webpack_require__(82),\n      'clean': __webpack_require__(83),\n      'code': __webpack_require__(58),\n      'code-block': __webpack_require__(58),\n      'color': __webpack_require__(84),\n      'direction': {\n        '': __webpack_require__(85),\n        'rtl': __webpack_require__(86)\n      },\n      'float': {\n        'center': __webpack_require__(87),\n        'full': __webpack_require__(88),\n        'left': __webpack_require__(89),\n        'right': __webpack_require__(90)\n      },\n      'formula': __webpack_require__(91),\n      'header': {\n        '1': __webpack_require__(92),\n        '2': __webpack_require__(93)\n      },\n      'italic': __webpack_require__(94),\n      'image': __webpack_require__(95),\n      'indent': {\n        '+1': __webpack_require__(96),\n        '-1': __webpack_require__(97)\n      },\n      'link': __webpack_require__(98),\n      'list': {\n        'ordered': __webpack_require__(99),\n        'bullet': __webpack_require__(100),\n        'check': __webpack_require__(101)\n      },\n      'script': {\n        'sub': __webpack_require__(102),\n        'super': __webpack_require__(103)\n      },\n      'strike': __webpack_require__(104),\n      'underline': __webpack_require__(105),\n      'video': __webpack_require__(106)\n    };\n\n    /***/\n  }), ( /* 42 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.getLastChangeIndex = exports.default = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var History = function (_Module) {\n      _inherits(History, _Module);\n      function History(quill, options) {\n        _classCallCheck(this, History);\n        var _this = _possibleConstructorReturn(this, (History.__proto__ || Object.getPrototypeOf(History)).call(this, quill, options));\n        _this.lastRecorded = 0;\n        _this.ignoreChange = false;\n        _this.clear();\n        _this.quill.on(_quill2.default.events.EDITOR_CHANGE, function (eventName, delta, oldDelta, source) {\n          if (eventName !== _quill2.default.events.TEXT_CHANGE || _this.ignoreChange) return;\n          if (!_this.options.userOnly || source === _quill2.default.sources.USER) {\n            _this.record(delta, oldDelta);\n          } else {\n            _this.transform(delta);\n          }\n        });\n        _this.quill.keyboard.addBinding({\n          key: 'Z',\n          shortKey: true\n        }, _this.undo.bind(_this));\n        _this.quill.keyboard.addBinding({\n          key: 'Z',\n          shortKey: true,\n          shiftKey: true\n        }, _this.redo.bind(_this));\n        if (/Win/i.test(navigator.platform)) {\n          _this.quill.keyboard.addBinding({\n            key: 'Y',\n            shortKey: true\n          }, _this.redo.bind(_this));\n        }\n        return _this;\n      }\n      _createClass(History, [{\n        key: 'change',\n        value: function change(source, dest) {\n          if (this.stack[source].length === 0) return;\n          var delta = this.stack[source].pop();\n          this.stack[dest].push(delta);\n          this.lastRecorded = 0;\n          this.ignoreChange = true;\n          this.quill.updateContents(delta[source], _quill2.default.sources.USER);\n          this.ignoreChange = false;\n          var index = getLastChangeIndex(delta[source]);\n          this.quill.setSelection(index);\n        }\n      }, {\n        key: 'clear',\n        value: function clear() {\n          this.stack = {\n            undo: [],\n            redo: []\n          };\n        }\n      }, {\n        key: 'cutoff',\n        value: function cutoff() {\n          this.lastRecorded = 0;\n        }\n      }, {\n        key: 'record',\n        value: function record(changeDelta, oldDelta) {\n          if (changeDelta.ops.length === 0) return;\n          this.stack.redo = [];\n          var undoDelta = this.quill.getContents().diff(oldDelta);\n          var timestamp = Date.now();\n          if (this.lastRecorded + this.options.delay > timestamp && this.stack.undo.length > 0) {\n            var delta = this.stack.undo.pop();\n            undoDelta = undoDelta.compose(delta.undo);\n            changeDelta = delta.redo.compose(changeDelta);\n          } else {\n            this.lastRecorded = timestamp;\n          }\n          this.stack.undo.push({\n            redo: changeDelta,\n            undo: undoDelta\n          });\n          if (this.stack.undo.length > this.options.maxStack) {\n            this.stack.undo.shift();\n          }\n        }\n      }, {\n        key: 'redo',\n        value: function redo() {\n          this.change('redo', 'undo');\n        }\n      }, {\n        key: 'transform',\n        value: function transform(delta) {\n          this.stack.undo.forEach(function (change) {\n            change.undo = delta.transform(change.undo, true);\n            change.redo = delta.transform(change.redo, true);\n          });\n          this.stack.redo.forEach(function (change) {\n            change.undo = delta.transform(change.undo, true);\n            change.redo = delta.transform(change.redo, true);\n          });\n        }\n      }, {\n        key: 'undo',\n        value: function undo() {\n          this.change('undo', 'redo');\n        }\n      }]);\n      return History;\n    }(_module2.default);\n    History.DEFAULTS = {\n      delay: 1000,\n      maxStack: 100,\n      userOnly: false\n    };\n    function endsWithNewlineChange(delta) {\n      var lastOp = delta.ops[delta.ops.length - 1];\n      if (lastOp == null) return false;\n      if (lastOp.insert != null) {\n        return typeof lastOp.insert === 'string' && lastOp.insert.endsWith('\\n');\n      }\n      if (lastOp.attributes != null) {\n        return Object.keys(lastOp.attributes).some(function (attr) {\n          return _parchment2.default.query(attr, _parchment2.default.Scope.BLOCK) != null;\n        });\n      }\n      return false;\n    }\n    function getLastChangeIndex(delta) {\n      var deleteLength = delta.reduce(function (length, op) {\n        length += op.delete || 0;\n        return length;\n      }, 0);\n      var changeIndex = delta.length() - deleteLength;\n      if (endsWithNewlineChange(delta)) {\n        changeIndex -= 1;\n      }\n      return changeIndex;\n    }\n    exports.default = History;\n    exports.getLastChangeIndex = getLastChangeIndex;\n\n    /***/\n  }), ( /* 43 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.BaseTooltip = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _emitter = __webpack_require__(8);\n    var _emitter2 = _interopRequireDefault(_emitter);\n    var _keyboard = __webpack_require__(23);\n    var _keyboard2 = _interopRequireDefault(_keyboard);\n    var _theme = __webpack_require__(34);\n    var _theme2 = _interopRequireDefault(_theme);\n    var _colorPicker = __webpack_require__(59);\n    var _colorPicker2 = _interopRequireDefault(_colorPicker);\n    var _iconPicker = __webpack_require__(60);\n    var _iconPicker2 = _interopRequireDefault(_iconPicker);\n    var _picker = __webpack_require__(28);\n    var _picker2 = _interopRequireDefault(_picker);\n    var _tooltip = __webpack_require__(61);\n    var _tooltip2 = _interopRequireDefault(_tooltip);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ALIGNS = [false, 'center', 'right', 'justify'];\n    var COLORS = [\"#000000\", \"#e60000\", \"#ff9900\", \"#ffff00\", \"#008a00\", \"#0066cc\", \"#9933ff\", \"#ffffff\", \"#facccc\", \"#ffebcc\", \"#ffffcc\", \"#cce8cc\", \"#cce0f5\", \"#ebd6ff\", \"#bbbbbb\", \"#f06666\", \"#ffc266\", \"#ffff66\", \"#66b966\", \"#66a3e0\", \"#c285ff\", \"#888888\", \"#a10000\", \"#b26b00\", \"#b2b200\", \"#006100\", \"#0047b2\", \"#6b24b2\", \"#444444\", \"#5c0000\", \"#663d00\", \"#666600\", \"#003700\", \"#002966\", \"#3d1466\"];\n    var FONTS = [false, 'serif', 'monospace'];\n    var HEADERS = ['1', '2', '3', false];\n    var SIZES = ['small', false, 'large', 'huge'];\n    var BaseTheme = function (_Theme) {\n      _inherits(BaseTheme, _Theme);\n      function BaseTheme(quill, options) {\n        _classCallCheck(this, BaseTheme);\n        var _this = _possibleConstructorReturn(this, (BaseTheme.__proto__ || Object.getPrototypeOf(BaseTheme)).call(this, quill, options));\n        var listener = function listener(e) {\n          if (!document.body.contains(quill.root)) {\n            return document.body.removeEventListener('click', listener);\n          }\n          if (_this.tooltip != null && !_this.tooltip.root.contains(e.target) && document.activeElement !== _this.tooltip.textbox && !_this.quill.hasFocus()) {\n            _this.tooltip.hide();\n          }\n          if (_this.pickers != null) {\n            _this.pickers.forEach(function (picker) {\n              if (!picker.container.contains(e.target)) {\n                picker.close();\n              }\n            });\n          }\n        };\n        quill.emitter.listenDOM('click', document.body, listener);\n        return _this;\n      }\n      _createClass(BaseTheme, [{\n        key: 'addModule',\n        value: function addModule(name) {\n          var module = _get(BaseTheme.prototype.__proto__ || Object.getPrototypeOf(BaseTheme.prototype), 'addModule', this).call(this, name);\n          if (name === 'toolbar') {\n            this.extendToolbar(module);\n          }\n          return module;\n        }\n      }, {\n        key: 'buildButtons',\n        value: function buildButtons(buttons, icons) {\n          buttons.forEach(function (button) {\n            var className = button.getAttribute('class') || '';\n            className.split(/\\s+/).forEach(function (name) {\n              if (!name.startsWith('ql-')) return;\n              name = name.slice('ql-'.length);\n              if (icons[name] == null) return;\n              if (name === 'direction') {\n                button.innerHTML = icons[name][''] + icons[name]['rtl'];\n              } else if (typeof icons[name] === 'string') {\n                button.innerHTML = icons[name];\n              } else {\n                var value = button.value || '';\n                if (value != null && icons[name][value]) {\n                  button.innerHTML = icons[name][value];\n                }\n              }\n            });\n          });\n        }\n      }, {\n        key: 'buildPickers',\n        value: function buildPickers(selects, icons) {\n          var _this2 = this;\n          this.pickers = selects.map(function (select) {\n            if (select.classList.contains('ql-align')) {\n              if (select.querySelector('option') == null) {\n                fillSelect(select, ALIGNS);\n              }\n              return new _iconPicker2.default(select, icons.align);\n            } else if (select.classList.contains('ql-background') || select.classList.contains('ql-color')) {\n              var format = select.classList.contains('ql-background') ? 'background' : 'color';\n              if (select.querySelector('option') == null) {\n                fillSelect(select, COLORS, format === 'background' ? '#ffffff' : '#000000');\n              }\n              return new _colorPicker2.default(select, icons[format]);\n            } else {\n              if (select.querySelector('option') == null) {\n                if (select.classList.contains('ql-font')) {\n                  fillSelect(select, FONTS);\n                } else if (select.classList.contains('ql-header')) {\n                  fillSelect(select, HEADERS);\n                } else if (select.classList.contains('ql-size')) {\n                  fillSelect(select, SIZES);\n                }\n              }\n              return new _picker2.default(select);\n            }\n          });\n          var update = function update() {\n            _this2.pickers.forEach(function (picker) {\n              picker.update();\n            });\n          };\n          this.quill.on(_emitter2.default.events.EDITOR_CHANGE, update);\n        }\n      }]);\n      return BaseTheme;\n    }(_theme2.default);\n    BaseTheme.DEFAULTS = (0, _extend2.default)(true, {}, _theme2.default.DEFAULTS, {\n      modules: {\n        toolbar: {\n          handlers: {\n            formula: function formula() {\n              this.quill.theme.tooltip.edit('formula');\n            },\n            image: function image() {\n              var _this3 = this;\n              var fileInput = this.container.querySelector('input.ql-image[type=file]');\n              if (fileInput == null) {\n                fileInput = document.createElement('input');\n                fileInput.setAttribute('type', 'file');\n                fileInput.setAttribute('accept', 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon');\n                fileInput.classList.add('ql-image');\n                fileInput.addEventListener('change', function () {\n                  if (fileInput.files != null && fileInput.files[0] != null) {\n                    var reader = new FileReader();\n                    reader.onload = function (e) {\n                      var range = _this3.quill.getSelection(true);\n                      _this3.quill.updateContents(new _quillDelta2.default().retain(range.index).delete(range.length).insert({\n                        image: e.target.result\n                      }), _emitter2.default.sources.USER);\n                      _this3.quill.setSelection(range.index + 1, _emitter2.default.sources.SILENT);\n                      fileInput.value = \"\";\n                    };\n                    reader.readAsDataURL(fileInput.files[0]);\n                  }\n                });\n                this.container.appendChild(fileInput);\n              }\n              fileInput.click();\n            },\n            video: function video() {\n              this.quill.theme.tooltip.edit('video');\n            }\n          }\n        }\n      }\n    });\n    var BaseTooltip = function (_Tooltip) {\n      _inherits(BaseTooltip, _Tooltip);\n      function BaseTooltip(quill, boundsContainer) {\n        _classCallCheck(this, BaseTooltip);\n        var _this4 = _possibleConstructorReturn(this, (BaseTooltip.__proto__ || Object.getPrototypeOf(BaseTooltip)).call(this, quill, boundsContainer));\n        _this4.textbox = _this4.root.querySelector('input[type=\"text\"]');\n        _this4.listen();\n        return _this4;\n      }\n      _createClass(BaseTooltip, [{\n        key: 'listen',\n        value: function listen() {\n          var _this5 = this;\n          this.textbox.addEventListener('keydown', function (event) {\n            if (_keyboard2.default.match(event, 'enter')) {\n              _this5.save();\n              event.preventDefault();\n            } else if (_keyboard2.default.match(event, 'escape')) {\n              _this5.cancel();\n              event.preventDefault();\n            }\n          });\n        }\n      }, {\n        key: 'cancel',\n        value: function cancel() {\n          this.hide();\n        }\n      }, {\n        key: 'edit',\n        value: function edit() {\n          var mode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'link';\n          var preview = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n          this.root.classList.remove('ql-hidden');\n          this.root.classList.add('ql-editing');\n          if (preview != null) {\n            this.textbox.value = preview;\n          } else if (mode !== this.root.getAttribute('data-mode')) {\n            this.textbox.value = '';\n          }\n          this.position(this.quill.getBounds(this.quill.selection.savedRange));\n          this.textbox.select();\n          this.textbox.setAttribute('placeholder', this.textbox.getAttribute('data-' + mode) || '');\n          this.root.setAttribute('data-mode', mode);\n        }\n      }, {\n        key: 'restoreFocus',\n        value: function restoreFocus() {\n          var scrollTop = this.quill.scrollingContainer.scrollTop;\n          this.quill.focus();\n          this.quill.scrollingContainer.scrollTop = scrollTop;\n        }\n      }, {\n        key: 'save',\n        value: function save() {\n          var value = this.textbox.value;\n          switch (this.root.getAttribute('data-mode')) {\n            case 'link':\n              {\n                var scrollTop = this.quill.root.scrollTop;\n                if (this.linkRange) {\n                  this.quill.formatText(this.linkRange, 'link', value, _emitter2.default.sources.USER);\n                  delete this.linkRange;\n                } else {\n                  this.restoreFocus();\n                  this.quill.format('link', value, _emitter2.default.sources.USER);\n                }\n                this.quill.root.scrollTop = scrollTop;\n                break;\n              }\n            case 'video':\n              {\n                value = extractVideoUrl(value);\n              }\n            // eslint-disable-next-line no-fallthrough\n            case 'formula':\n              {\n                if (!value) break;\n                var range = this.quill.getSelection(true);\n                if (range != null) {\n                  var index = range.index + range.length;\n                  this.quill.insertEmbed(index, this.root.getAttribute('data-mode'), value, _emitter2.default.sources.USER);\n                  if (this.root.getAttribute('data-mode') === 'formula') {\n                    this.quill.insertText(index + 1, ' ', _emitter2.default.sources.USER);\n                  }\n                  this.quill.setSelection(index + 2, _emitter2.default.sources.USER);\n                }\n                break;\n              }\n            default:\n          }\n          this.textbox.value = '';\n          this.hide();\n        }\n      }]);\n      return BaseTooltip;\n    }(_tooltip2.default);\n    function extractVideoUrl(url) {\n      var match = url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/) || url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n      if (match) {\n        return (match[1] || 'https') + '://www.youtube.com/embed/' + match[2] + '?showinfo=0';\n      }\n      if (match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/)) {\n        // eslint-disable-line no-cond-assign\n        return (match[1] || 'https') + '://player.vimeo.com/video/' + match[2] + '/';\n      }\n      return url;\n    }\n    function fillSelect(select, values) {\n      var defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      values.forEach(function (value) {\n        var option = document.createElement('option');\n        if (value === defaultValue) {\n          option.setAttribute('selected', 'selected');\n        } else {\n          option.setAttribute('value', value);\n        }\n        select.appendChild(option);\n      });\n    }\n    exports.BaseTooltip = BaseTooltip;\n    exports.default = BaseTheme;\n\n    /***/\n  }), ( /* 44 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var LinkedList = /** @class */function () {\n      function LinkedList() {\n        this.head = this.tail = null;\n        this.length = 0;\n      }\n      LinkedList.prototype.append = function () {\n        var nodes = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          nodes[_i] = arguments[_i];\n        }\n        this.insertBefore(nodes[0], null);\n        if (nodes.length > 1) {\n          this.append.apply(this, nodes.slice(1));\n        }\n      };\n      LinkedList.prototype.contains = function (node) {\n        var cur,\n          next = this.iterator();\n        while (cur = next()) {\n          if (cur === node) return true;\n        }\n        return false;\n      };\n      LinkedList.prototype.insertBefore = function (node, refNode) {\n        if (!node) return;\n        node.next = refNode;\n        if (refNode != null) {\n          node.prev = refNode.prev;\n          if (refNode.prev != null) {\n            refNode.prev.next = node;\n          }\n          refNode.prev = node;\n          if (refNode === this.head) {\n            this.head = node;\n          }\n        } else if (this.tail != null) {\n          this.tail.next = node;\n          node.prev = this.tail;\n          this.tail = node;\n        } else {\n          node.prev = null;\n          this.head = this.tail = node;\n        }\n        this.length += 1;\n      };\n      LinkedList.prototype.offset = function (target) {\n        var index = 0,\n          cur = this.head;\n        while (cur != null) {\n          if (cur === target) return index;\n          index += cur.length();\n          cur = cur.next;\n        }\n        return -1;\n      };\n      LinkedList.prototype.remove = function (node) {\n        if (!this.contains(node)) return;\n        if (node.prev != null) node.prev.next = node.next;\n        if (node.next != null) node.next.prev = node.prev;\n        if (node === this.head) this.head = node.next;\n        if (node === this.tail) this.tail = node.prev;\n        this.length -= 1;\n      };\n      LinkedList.prototype.iterator = function (curNode) {\n        if (curNode === void 0) {\n          curNode = this.head;\n        }\n        // TODO use yield when we can\n        return function () {\n          var ret = curNode;\n          if (curNode != null) curNode = curNode.next;\n          return ret;\n        };\n      };\n      LinkedList.prototype.find = function (index, inclusive) {\n        if (inclusive === void 0) {\n          inclusive = false;\n        }\n        var cur,\n          next = this.iterator();\n        while (cur = next()) {\n          var length = cur.length();\n          if (index < length || inclusive && index === length && (cur.next == null || cur.next.length() !== 0)) {\n            return [cur, index];\n          }\n          index -= length;\n        }\n        return [null, 0];\n      };\n      LinkedList.prototype.forEach = function (callback) {\n        var cur,\n          next = this.iterator();\n        while (cur = next()) {\n          callback(cur);\n        }\n      };\n      LinkedList.prototype.forEachAt = function (index, length, callback) {\n        if (length <= 0) return;\n        var _a = this.find(index),\n          startNode = _a[0],\n          offset = _a[1];\n        var cur,\n          curIndex = index - offset,\n          next = this.iterator(startNode);\n        while ((cur = next()) && curIndex < index + length) {\n          var curLength = cur.length();\n          if (index > curIndex) {\n            callback(cur, index - curIndex, Math.min(length, curIndex + curLength - index));\n          } else {\n            callback(cur, 0, Math.min(curLength, index + length - curIndex));\n          }\n          curIndex += curLength;\n        }\n      };\n      LinkedList.prototype.map = function (callback) {\n        return this.reduce(function (memo, cur) {\n          memo.push(callback(cur));\n          return memo;\n        }, []);\n      };\n      LinkedList.prototype.reduce = function (callback, memo) {\n        var cur,\n          next = this.iterator();\n        while (cur = next()) {\n          memo = callback(memo, cur);\n        }\n        return memo;\n      };\n      return LinkedList;\n    }();\n    exports.default = LinkedList;\n\n    /***/\n  }), ( /* 45 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var container_1 = __webpack_require__(17);\n    var Registry = __webpack_require__(1);\n    var OBSERVER_CONFIG = {\n      attributes: true,\n      characterData: true,\n      characterDataOldValue: true,\n      childList: true,\n      subtree: true\n    };\n    var MAX_OPTIMIZE_ITERATIONS = 100;\n    var ScrollBlot = /** @class */function (_super) {\n      __extends(ScrollBlot, _super);\n      function ScrollBlot(node) {\n        var _this = _super.call(this, node) || this;\n        _this.scroll = _this;\n        _this.observer = new MutationObserver(function (mutations) {\n          _this.update(mutations);\n        });\n        _this.observer.observe(_this.domNode, OBSERVER_CONFIG);\n        _this.attach();\n        return _this;\n      }\n      ScrollBlot.prototype.detach = function () {\n        _super.prototype.detach.call(this);\n        this.observer.disconnect();\n      };\n      ScrollBlot.prototype.deleteAt = function (index, length) {\n        this.update();\n        if (index === 0 && length === this.length()) {\n          this.children.forEach(function (child) {\n            child.remove();\n          });\n        } else {\n          _super.prototype.deleteAt.call(this, index, length);\n        }\n      };\n      ScrollBlot.prototype.formatAt = function (index, length, name, value) {\n        this.update();\n        _super.prototype.formatAt.call(this, index, length, name, value);\n      };\n      ScrollBlot.prototype.insertAt = function (index, value, def) {\n        this.update();\n        _super.prototype.insertAt.call(this, index, value, def);\n      };\n      ScrollBlot.prototype.optimize = function (mutations, context) {\n        var _this = this;\n        if (mutations === void 0) {\n          mutations = [];\n        }\n        if (context === void 0) {\n          context = {};\n        }\n        _super.prototype.optimize.call(this, context);\n        // We must modify mutations directly, cannot make copy and then modify\n        var records = [].slice.call(this.observer.takeRecords());\n        // Array.push currently seems to be implemented by a non-tail recursive function\n        // so we cannot just mutations.push.apply(mutations, this.observer.takeRecords());\n        while (records.length > 0) mutations.push(records.pop());\n        // TODO use WeakMap\n        var mark = function (blot, markParent) {\n          if (markParent === void 0) {\n            markParent = true;\n          }\n          if (blot == null || blot === _this) return;\n          if (blot.domNode.parentNode == null) return;\n          // @ts-ignore\n          if (blot.domNode[Registry.DATA_KEY].mutations == null) {\n            // @ts-ignore\n            blot.domNode[Registry.DATA_KEY].mutations = [];\n          }\n          if (markParent) mark(blot.parent);\n        };\n        var optimize = function (blot) {\n          // Post-order traversal\n          if (\n          // @ts-ignore\n          blot.domNode[Registry.DATA_KEY] == null ||\n          // @ts-ignore\n          blot.domNode[Registry.DATA_KEY].mutations == null) {\n            return;\n          }\n          if (blot instanceof container_1.default) {\n            blot.children.forEach(optimize);\n          }\n          blot.optimize(context);\n        };\n        var remaining = mutations;\n        for (var i = 0; remaining.length > 0; i += 1) {\n          if (i >= MAX_OPTIMIZE_ITERATIONS) {\n            throw new Error('[Parchment] Maximum optimize iterations reached');\n          }\n          remaining.forEach(function (mutation) {\n            var blot = Registry.find(mutation.target, true);\n            if (blot == null) return;\n            if (blot.domNode === mutation.target) {\n              if (mutation.type === 'childList') {\n                mark(Registry.find(mutation.previousSibling, false));\n                [].forEach.call(mutation.addedNodes, function (node) {\n                  var child = Registry.find(node, false);\n                  mark(child, false);\n                  if (child instanceof container_1.default) {\n                    child.children.forEach(function (grandChild) {\n                      mark(grandChild, false);\n                    });\n                  }\n                });\n              } else if (mutation.type === 'attributes') {\n                mark(blot.prev);\n              }\n            }\n            mark(blot);\n          });\n          this.children.forEach(optimize);\n          remaining = [].slice.call(this.observer.takeRecords());\n          records = remaining.slice();\n          while (records.length > 0) mutations.push(records.pop());\n        }\n      };\n      ScrollBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        if (context === void 0) {\n          context = {};\n        }\n        mutations = mutations || this.observer.takeRecords();\n        // TODO use WeakMap\n        mutations.map(function (mutation) {\n          var blot = Registry.find(mutation.target, true);\n          if (blot == null) return null;\n          // @ts-ignore\n          if (blot.domNode[Registry.DATA_KEY].mutations == null) {\n            // @ts-ignore\n            blot.domNode[Registry.DATA_KEY].mutations = [mutation];\n            return blot;\n          } else {\n            // @ts-ignore\n            blot.domNode[Registry.DATA_KEY].mutations.push(mutation);\n            return null;\n          }\n        }).forEach(function (blot) {\n          if (blot == null || blot === _this ||\n          //@ts-ignore\n          blot.domNode[Registry.DATA_KEY] == null) return;\n          // @ts-ignore\n          blot.update(blot.domNode[Registry.DATA_KEY].mutations || [], context);\n        });\n        // @ts-ignore\n        if (this.domNode[Registry.DATA_KEY].mutations != null) {\n          // @ts-ignore\n          _super.prototype.update.call(this, this.domNode[Registry.DATA_KEY].mutations, context);\n        }\n        this.optimize(mutations, context);\n      };\n      ScrollBlot.blotName = 'scroll';\n      ScrollBlot.defaultChild = 'block';\n      ScrollBlot.scope = Registry.Scope.BLOCK_BLOT;\n      ScrollBlot.tagName = 'DIV';\n      return ScrollBlot;\n    }(container_1.default);\n    exports.default = ScrollBlot;\n\n    /***/\n  }), ( /* 46 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var format_1 = __webpack_require__(18);\n    var Registry = __webpack_require__(1);\n    // Shallow object comparison\n    function isEqual(obj1, obj2) {\n      if (Object.keys(obj1).length !== Object.keys(obj2).length) return false;\n      // @ts-ignore\n      for (var prop in obj1) {\n        // @ts-ignore\n        if (obj1[prop] !== obj2[prop]) return false;\n      }\n      return true;\n    }\n    var InlineBlot = /** @class */function (_super) {\n      __extends(InlineBlot, _super);\n      function InlineBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      InlineBlot.formats = function (domNode) {\n        if (domNode.tagName === InlineBlot.tagName) return undefined;\n        return _super.formats.call(this, domNode);\n      };\n      InlineBlot.prototype.format = function (name, value) {\n        var _this = this;\n        if (name === this.statics.blotName && !value) {\n          this.children.forEach(function (child) {\n            if (!(child instanceof format_1.default)) {\n              child = child.wrap(InlineBlot.blotName, true);\n            }\n            _this.attributes.copy(child);\n          });\n          this.unwrap();\n        } else {\n          _super.prototype.format.call(this, name, value);\n        }\n      };\n      InlineBlot.prototype.formatAt = function (index, length, name, value) {\n        if (this.formats()[name] != null || Registry.query(name, Registry.Scope.ATTRIBUTE)) {\n          var blot = this.isolate(index, length);\n          blot.format(name, value);\n        } else {\n          _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n      };\n      InlineBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        var formats = this.formats();\n        if (Object.keys(formats).length === 0) {\n          return this.unwrap(); // unformatted span\n        }\n        var next = this.next;\n        if (next instanceof InlineBlot && next.prev === this && isEqual(formats, next.formats())) {\n          next.moveChildren(this);\n          next.remove();\n        }\n      };\n      InlineBlot.blotName = 'inline';\n      InlineBlot.scope = Registry.Scope.INLINE_BLOT;\n      InlineBlot.tagName = 'SPAN';\n      return InlineBlot;\n    }(format_1.default);\n    exports.default = InlineBlot;\n\n    /***/\n  }), ( /* 47 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var format_1 = __webpack_require__(18);\n    var Registry = __webpack_require__(1);\n    var BlockBlot = /** @class */function (_super) {\n      __extends(BlockBlot, _super);\n      function BlockBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      BlockBlot.formats = function (domNode) {\n        var tagName = Registry.query(BlockBlot.blotName).tagName;\n        if (domNode.tagName === tagName) return undefined;\n        return _super.formats.call(this, domNode);\n      };\n      BlockBlot.prototype.format = function (name, value) {\n        if (Registry.query(name, Registry.Scope.BLOCK) == null) {\n          return;\n        } else if (name === this.statics.blotName && !value) {\n          this.replaceWith(BlockBlot.blotName);\n        } else {\n          _super.prototype.format.call(this, name, value);\n        }\n      };\n      BlockBlot.prototype.formatAt = function (index, length, name, value) {\n        if (Registry.query(name, Registry.Scope.BLOCK) != null) {\n          this.format(name, value);\n        } else {\n          _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n      };\n      BlockBlot.prototype.insertAt = function (index, value, def) {\n        if (def == null || Registry.query(value, Registry.Scope.INLINE) != null) {\n          // Insert text or inline\n          _super.prototype.insertAt.call(this, index, value, def);\n        } else {\n          var after = this.split(index);\n          var blot = Registry.create(value, def);\n          after.parent.insertBefore(blot, after);\n        }\n      };\n      BlockBlot.prototype.update = function (mutations, context) {\n        if (navigator.userAgent.match(/Trident/)) {\n          this.build();\n        } else {\n          _super.prototype.update.call(this, mutations, context);\n        }\n      };\n      BlockBlot.blotName = 'block';\n      BlockBlot.scope = Registry.Scope.BLOCK_BLOT;\n      BlockBlot.tagName = 'P';\n      return BlockBlot;\n    }(format_1.default);\n    exports.default = BlockBlot;\n\n    /***/\n  }), ( /* 48 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var leaf_1 = __webpack_require__(19);\n    var EmbedBlot = /** @class */function (_super) {\n      __extends(EmbedBlot, _super);\n      function EmbedBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n      }\n      EmbedBlot.formats = function (domNode) {\n        return undefined;\n      };\n      EmbedBlot.prototype.format = function (name, value) {\n        // super.formatAt wraps, which is what we want in general,\n        // but this allows subclasses to overwrite for formats\n        // that just apply to particular embeds\n        _super.prototype.formatAt.call(this, 0, this.length(), name, value);\n      };\n      EmbedBlot.prototype.formatAt = function (index, length, name, value) {\n        if (index === 0 && length === this.length()) {\n          this.format(name, value);\n        } else {\n          _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n      };\n      EmbedBlot.prototype.formats = function () {\n        return this.statics.formats(this.domNode);\n      };\n      return EmbedBlot;\n    }(leaf_1.default);\n    exports.default = EmbedBlot;\n\n    /***/\n  }), ( /* 49 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var __extends = this && this.__extends || function () {\n      var extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (d, b) {\n        d.__proto__ = b;\n      } || function (d, b) {\n        for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      };\n      return function (d, b) {\n        extendStatics(d, b);\n        function __() {\n          this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n      };\n    }();\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var leaf_1 = __webpack_require__(19);\n    var Registry = __webpack_require__(1);\n    var TextBlot = /** @class */function (_super) {\n      __extends(TextBlot, _super);\n      function TextBlot(node) {\n        var _this = _super.call(this, node) || this;\n        _this.text = _this.statics.value(_this.domNode);\n        return _this;\n      }\n      TextBlot.create = function (value) {\n        return document.createTextNode(value);\n      };\n      TextBlot.value = function (domNode) {\n        var text = domNode.data;\n        // @ts-ignore\n        if (text['normalize']) text = text['normalize']();\n        return text;\n      };\n      TextBlot.prototype.deleteAt = function (index, length) {\n        this.domNode.data = this.text = this.text.slice(0, index) + this.text.slice(index + length);\n      };\n      TextBlot.prototype.index = function (node, offset) {\n        if (this.domNode === node) {\n          return offset;\n        }\n        return -1;\n      };\n      TextBlot.prototype.insertAt = function (index, value, def) {\n        if (def == null) {\n          this.text = this.text.slice(0, index) + value + this.text.slice(index);\n          this.domNode.data = this.text;\n        } else {\n          _super.prototype.insertAt.call(this, index, value, def);\n        }\n      };\n      TextBlot.prototype.length = function () {\n        return this.text.length;\n      };\n      TextBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        this.text = this.statics.value(this.domNode);\n        if (this.text.length === 0) {\n          this.remove();\n        } else if (this.next instanceof TextBlot && this.next.prev === this) {\n          this.insertAt(this.length(), this.next.value());\n          this.next.remove();\n        }\n      };\n      TextBlot.prototype.position = function (index, inclusive) {\n        if (inclusive === void 0) {\n          inclusive = false;\n        }\n        return [this.domNode, index];\n      };\n      TextBlot.prototype.split = function (index, force) {\n        if (force === void 0) {\n          force = false;\n        }\n        if (!force) {\n          if (index === 0) return this;\n          if (index === this.length()) return this.next;\n        }\n        var after = Registry.create(this.domNode.splitText(index));\n        this.parent.insertBefore(after, this.next);\n        this.text = this.statics.value(this.domNode);\n        return after;\n      };\n      TextBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        if (mutations.some(function (mutation) {\n          return mutation.type === 'characterData' && mutation.target === _this.domNode;\n        })) {\n          this.text = this.statics.value(this.domNode);\n        }\n      };\n      TextBlot.prototype.value = function () {\n        return this.text;\n      };\n      TextBlot.blotName = 'text';\n      TextBlot.scope = Registry.Scope.INLINE_BLOT;\n      return TextBlot;\n    }(leaf_1.default);\n    exports.default = TextBlot;\n\n    /***/\n  }), ( /* 50 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var elem = document.createElement('div');\n    elem.classList.toggle('test-class', false);\n    if (elem.classList.contains('test-class')) {\n      var _toggle = DOMTokenList.prototype.toggle;\n      DOMTokenList.prototype.toggle = function (token, force) {\n        if (arguments.length > 1 && !this.contains(token) === !force) {\n          return force;\n        } else {\n          return _toggle.call(this, token);\n        }\n      };\n    }\n    if (!String.prototype.startsWith) {\n      String.prototype.startsWith = function (searchString, position) {\n        position = position || 0;\n        return this.substr(position, searchString.length) === searchString;\n      };\n    }\n    if (!String.prototype.endsWith) {\n      String.prototype.endsWith = function (searchString, position) {\n        var subjectString = this.toString();\n        if (typeof position !== 'number' || !isFinite(position) || Math.floor(position) !== position || position > subjectString.length) {\n          position = subjectString.length;\n        }\n        position -= searchString.length;\n        var lastIndex = subjectString.indexOf(searchString, position);\n        return lastIndex !== -1 && lastIndex === position;\n      };\n    }\n    if (!Array.prototype.find) {\n      Object.defineProperty(Array.prototype, \"find\", {\n        value: function value(predicate) {\n          if (this === null) {\n            throw new TypeError('Array.prototype.find called on null or undefined');\n          }\n          if (typeof predicate !== 'function') {\n            throw new TypeError('predicate must be a function');\n          }\n          var list = Object(this);\n          var length = list.length >>> 0;\n          var thisArg = arguments[1];\n          var value;\n          for (var i = 0; i < length; i++) {\n            value = list[i];\n            if (predicate.call(thisArg, value, i, list)) {\n              return value;\n            }\n          }\n          return undefined;\n        }\n      });\n    }\n    document.addEventListener(\"DOMContentLoaded\", function () {\n      // Disable resizing in Firefox\n      document.execCommand(\"enableObjectResizing\", false, false);\n      // Disable automatic linkifying in IE11\n      document.execCommand(\"autoUrlDetect\", false, false);\n    });\n\n    /***/\n  }), ( /* 51 */\n  /***/function (module, exports) {\n    /**\n     * This library modifies the diff-patch-match library by Neil Fraser\n     * by removing the patch and match functionality and certain advanced\n     * options in the diff function. The original license is as follows:\n     *\n     * ===\n     *\n     * Diff Match and Patch\n     *\n     * Copyright 2006 Google Inc.\n     * http://code.google.com/p/google-diff-match-patch/\n     *\n     * Licensed under the Apache License, Version 2.0 (the \"License\");\n     * you may not use this file except in compliance with the License.\n     * You may obtain a copy of the License at\n     *\n     *   http://www.apache.org/licenses/LICENSE-2.0\n     *\n     * Unless required by applicable law or agreed to in writing, software\n     * distributed under the License is distributed on an \"AS IS\" BASIS,\n     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n     * See the License for the specific language governing permissions and\n     * limitations under the License.\n     */\n\n    /**\n     * The data structure representing a diff is an array of tuples:\n     * [[DIFF_DELETE, 'Hello'], [DIFF_INSERT, 'Goodbye'], [DIFF_EQUAL, ' world.']]\n     * which means: delete 'Hello', add 'Goodbye' and keep ' world.'\n     */\n    var DIFF_DELETE = -1;\n    var DIFF_INSERT = 1;\n    var DIFF_EQUAL = 0;\n\n    /**\n     * Find the differences between two texts.  Simplifies the problem by stripping\n     * any common prefix or suffix off the texts before diffing.\n     * @param {string} text1 Old string to be diffed.\n     * @param {string} text2 New string to be diffed.\n     * @param {Int} cursor_pos Expected edit position in text1 (optional)\n     * @return {Array} Array of diff tuples.\n     */\n    function diff_main(text1, text2, cursor_pos) {\n      // Check for equality (speedup).\n      if (text1 == text2) {\n        if (text1) {\n          return [[DIFF_EQUAL, text1]];\n        }\n        return [];\n      }\n\n      // Check cursor_pos within bounds\n      if (cursor_pos < 0 || text1.length < cursor_pos) {\n        cursor_pos = null;\n      }\n\n      // Trim off common prefix (speedup).\n      var commonlength = diff_commonPrefix(text1, text2);\n      var commonprefix = text1.substring(0, commonlength);\n      text1 = text1.substring(commonlength);\n      text2 = text2.substring(commonlength);\n\n      // Trim off common suffix (speedup).\n      commonlength = diff_commonSuffix(text1, text2);\n      var commonsuffix = text1.substring(text1.length - commonlength);\n      text1 = text1.substring(0, text1.length - commonlength);\n      text2 = text2.substring(0, text2.length - commonlength);\n\n      // Compute the diff on the middle block.\n      var diffs = diff_compute_(text1, text2);\n\n      // Restore the prefix and suffix.\n      if (commonprefix) {\n        diffs.unshift([DIFF_EQUAL, commonprefix]);\n      }\n      if (commonsuffix) {\n        diffs.push([DIFF_EQUAL, commonsuffix]);\n      }\n      diff_cleanupMerge(diffs);\n      if (cursor_pos != null) {\n        diffs = fix_cursor(diffs, cursor_pos);\n      }\n      diffs = fix_emoji(diffs);\n      return diffs;\n    }\n    ;\n\n    /**\n     * Find the differences between two texts.  Assumes that the texts do not\n     * have any common prefix or suffix.\n     * @param {string} text1 Old string to be diffed.\n     * @param {string} text2 New string to be diffed.\n     * @return {Array} Array of diff tuples.\n     */\n    function diff_compute_(text1, text2) {\n      var diffs;\n      if (!text1) {\n        // Just add some text (speedup).\n        return [[DIFF_INSERT, text2]];\n      }\n      if (!text2) {\n        // Just delete some text (speedup).\n        return [[DIFF_DELETE, text1]];\n      }\n      var longtext = text1.length > text2.length ? text1 : text2;\n      var shorttext = text1.length > text2.length ? text2 : text1;\n      var i = longtext.indexOf(shorttext);\n      if (i != -1) {\n        // Shorter text is inside the longer text (speedup).\n        diffs = [[DIFF_INSERT, longtext.substring(0, i)], [DIFF_EQUAL, shorttext], [DIFF_INSERT, longtext.substring(i + shorttext.length)]];\n        // Swap insertions for deletions if diff is reversed.\n        if (text1.length > text2.length) {\n          diffs[0][0] = diffs[2][0] = DIFF_DELETE;\n        }\n        return diffs;\n      }\n      if (shorttext.length == 1) {\n        // Single character string.\n        // After the previous speedup, the character can't be an equality.\n        return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];\n      }\n\n      // Check to see if the problem can be split in two.\n      var hm = diff_halfMatch_(text1, text2);\n      if (hm) {\n        // A half-match was found, sort out the return data.\n        var text1_a = hm[0];\n        var text1_b = hm[1];\n        var text2_a = hm[2];\n        var text2_b = hm[3];\n        var mid_common = hm[4];\n        // Send both pairs off for separate processing.\n        var diffs_a = diff_main(text1_a, text2_a);\n        var diffs_b = diff_main(text1_b, text2_b);\n        // Merge the results.\n        return diffs_a.concat([[DIFF_EQUAL, mid_common]], diffs_b);\n      }\n      return diff_bisect_(text1, text2);\n    }\n    ;\n\n    /**\n     * Find the 'middle snake' of a diff, split the problem in two\n     * and return the recursively constructed diff.\n     * See Myers 1986 paper: An O(ND) Difference Algorithm and Its Variations.\n     * @param {string} text1 Old string to be diffed.\n     * @param {string} text2 New string to be diffed.\n     * @return {Array} Array of diff tuples.\n     * @private\n     */\n    function diff_bisect_(text1, text2) {\n      // Cache the text lengths to prevent multiple calls.\n      var text1_length = text1.length;\n      var text2_length = text2.length;\n      var max_d = Math.ceil((text1_length + text2_length) / 2);\n      var v_offset = max_d;\n      var v_length = 2 * max_d;\n      var v1 = new Array(v_length);\n      var v2 = new Array(v_length);\n      // Setting all elements to -1 is faster in Chrome & Firefox than mixing\n      // integers and undefined.\n      for (var x = 0; x < v_length; x++) {\n        v1[x] = -1;\n        v2[x] = -1;\n      }\n      v1[v_offset + 1] = 0;\n      v2[v_offset + 1] = 0;\n      var delta = text1_length - text2_length;\n      // If the total number of characters is odd, then the front path will collide\n      // with the reverse path.\n      var front = delta % 2 != 0;\n      // Offsets for start and end of k loop.\n      // Prevents mapping of space beyond the grid.\n      var k1start = 0;\n      var k1end = 0;\n      var k2start = 0;\n      var k2end = 0;\n      for (var d = 0; d < max_d; d++) {\n        // Walk the front path one step.\n        for (var k1 = -d + k1start; k1 <= d - k1end; k1 += 2) {\n          var k1_offset = v_offset + k1;\n          var x1;\n          if (k1 == -d || k1 != d && v1[k1_offset - 1] < v1[k1_offset + 1]) {\n            x1 = v1[k1_offset + 1];\n          } else {\n            x1 = v1[k1_offset - 1] + 1;\n          }\n          var y1 = x1 - k1;\n          while (x1 < text1_length && y1 < text2_length && text1.charAt(x1) == text2.charAt(y1)) {\n            x1++;\n            y1++;\n          }\n          v1[k1_offset] = x1;\n          if (x1 > text1_length) {\n            // Ran off the right of the graph.\n            k1end += 2;\n          } else if (y1 > text2_length) {\n            // Ran off the bottom of the graph.\n            k1start += 2;\n          } else if (front) {\n            var k2_offset = v_offset + delta - k1;\n            if (k2_offset >= 0 && k2_offset < v_length && v2[k2_offset] != -1) {\n              // Mirror x2 onto top-left coordinate system.\n              var x2 = text1_length - v2[k2_offset];\n              if (x1 >= x2) {\n                // Overlap detected.\n                return diff_bisectSplit_(text1, text2, x1, y1);\n              }\n            }\n          }\n        }\n\n        // Walk the reverse path one step.\n        for (var k2 = -d + k2start; k2 <= d - k2end; k2 += 2) {\n          var k2_offset = v_offset + k2;\n          var x2;\n          if (k2 == -d || k2 != d && v2[k2_offset - 1] < v2[k2_offset + 1]) {\n            x2 = v2[k2_offset + 1];\n          } else {\n            x2 = v2[k2_offset - 1] + 1;\n          }\n          var y2 = x2 - k2;\n          while (x2 < text1_length && y2 < text2_length && text1.charAt(text1_length - x2 - 1) == text2.charAt(text2_length - y2 - 1)) {\n            x2++;\n            y2++;\n          }\n          v2[k2_offset] = x2;\n          if (x2 > text1_length) {\n            // Ran off the left of the graph.\n            k2end += 2;\n          } else if (y2 > text2_length) {\n            // Ran off the top of the graph.\n            k2start += 2;\n          } else if (!front) {\n            var k1_offset = v_offset + delta - k2;\n            if (k1_offset >= 0 && k1_offset < v_length && v1[k1_offset] != -1) {\n              var x1 = v1[k1_offset];\n              var y1 = v_offset + x1 - k1_offset;\n              // Mirror x2 onto top-left coordinate system.\n              x2 = text1_length - x2;\n              if (x1 >= x2) {\n                // Overlap detected.\n                return diff_bisectSplit_(text1, text2, x1, y1);\n              }\n            }\n          }\n        }\n      }\n      // Diff took too long and hit the deadline or\n      // number of diffs equals number of characters, no commonality at all.\n      return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];\n    }\n    ;\n\n    /**\n     * Given the location of the 'middle snake', split the diff in two parts\n     * and recurse.\n     * @param {string} text1 Old string to be diffed.\n     * @param {string} text2 New string to be diffed.\n     * @param {number} x Index of split point in text1.\n     * @param {number} y Index of split point in text2.\n     * @return {Array} Array of diff tuples.\n     */\n    function diff_bisectSplit_(text1, text2, x, y) {\n      var text1a = text1.substring(0, x);\n      var text2a = text2.substring(0, y);\n      var text1b = text1.substring(x);\n      var text2b = text2.substring(y);\n\n      // Compute both diffs serially.\n      var diffs = diff_main(text1a, text2a);\n      var diffsb = diff_main(text1b, text2b);\n      return diffs.concat(diffsb);\n    }\n    ;\n\n    /**\n     * Determine the common prefix of two strings.\n     * @param {string} text1 First string.\n     * @param {string} text2 Second string.\n     * @return {number} The number of characters common to the start of each\n     *     string.\n     */\n    function diff_commonPrefix(text1, text2) {\n      // Quick check for common null cases.\n      if (!text1 || !text2 || text1.charAt(0) != text2.charAt(0)) {\n        return 0;\n      }\n      // Binary search.\n      // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n      var pointermin = 0;\n      var pointermax = Math.min(text1.length, text2.length);\n      var pointermid = pointermax;\n      var pointerstart = 0;\n      while (pointermin < pointermid) {\n        if (text1.substring(pointerstart, pointermid) == text2.substring(pointerstart, pointermid)) {\n          pointermin = pointermid;\n          pointerstart = pointermin;\n        } else {\n          pointermax = pointermid;\n        }\n        pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n      }\n      return pointermid;\n    }\n    ;\n\n    /**\n     * Determine the common suffix of two strings.\n     * @param {string} text1 First string.\n     * @param {string} text2 Second string.\n     * @return {number} The number of characters common to the end of each string.\n     */\n    function diff_commonSuffix(text1, text2) {\n      // Quick check for common null cases.\n      if (!text1 || !text2 || text1.charAt(text1.length - 1) != text2.charAt(text2.length - 1)) {\n        return 0;\n      }\n      // Binary search.\n      // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n      var pointermin = 0;\n      var pointermax = Math.min(text1.length, text2.length);\n      var pointermid = pointermax;\n      var pointerend = 0;\n      while (pointermin < pointermid) {\n        if (text1.substring(text1.length - pointermid, text1.length - pointerend) == text2.substring(text2.length - pointermid, text2.length - pointerend)) {\n          pointermin = pointermid;\n          pointerend = pointermin;\n        } else {\n          pointermax = pointermid;\n        }\n        pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n      }\n      return pointermid;\n    }\n    ;\n\n    /**\n     * Do the two texts share a substring which is at least half the length of the\n     * longer text?\n     * This speedup can produce non-minimal diffs.\n     * @param {string} text1 First string.\n     * @param {string} text2 Second string.\n     * @return {Array.<string>} Five element Array, containing the prefix of\n     *     text1, the suffix of text1, the prefix of text2, the suffix of\n     *     text2 and the common middle.  Or null if there was no match.\n     */\n    function diff_halfMatch_(text1, text2) {\n      var longtext = text1.length > text2.length ? text1 : text2;\n      var shorttext = text1.length > text2.length ? text2 : text1;\n      if (longtext.length < 4 || shorttext.length * 2 < longtext.length) {\n        return null; // Pointless.\n      }\n\n      /**\n       * Does a substring of shorttext exist within longtext such that the substring\n       * is at least half the length of longtext?\n       * Closure, but does not reference any external variables.\n       * @param {string} longtext Longer string.\n       * @param {string} shorttext Shorter string.\n       * @param {number} i Start index of quarter length substring within longtext.\n       * @return {Array.<string>} Five element Array, containing the prefix of\n       *     longtext, the suffix of longtext, the prefix of shorttext, the suffix\n       *     of shorttext and the common middle.  Or null if there was no match.\n       * @private\n       */\n      function diff_halfMatchI_(longtext, shorttext, i) {\n        // Start with a 1/4 length substring at position i as a seed.\n        var seed = longtext.substring(i, i + Math.floor(longtext.length / 4));\n        var j = -1;\n        var best_common = '';\n        var best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b;\n        while ((j = shorttext.indexOf(seed, j + 1)) != -1) {\n          var prefixLength = diff_commonPrefix(longtext.substring(i), shorttext.substring(j));\n          var suffixLength = diff_commonSuffix(longtext.substring(0, i), shorttext.substring(0, j));\n          if (best_common.length < suffixLength + prefixLength) {\n            best_common = shorttext.substring(j - suffixLength, j) + shorttext.substring(j, j + prefixLength);\n            best_longtext_a = longtext.substring(0, i - suffixLength);\n            best_longtext_b = longtext.substring(i + prefixLength);\n            best_shorttext_a = shorttext.substring(0, j - suffixLength);\n            best_shorttext_b = shorttext.substring(j + prefixLength);\n          }\n        }\n        if (best_common.length * 2 >= longtext.length) {\n          return [best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b, best_common];\n        } else {\n          return null;\n        }\n      }\n\n      // First check if the second quarter is the seed for a half-match.\n      var hm1 = diff_halfMatchI_(longtext, shorttext, Math.ceil(longtext.length / 4));\n      // Check again based on the third quarter.\n      var hm2 = diff_halfMatchI_(longtext, shorttext, Math.ceil(longtext.length / 2));\n      var hm;\n      if (!hm1 && !hm2) {\n        return null;\n      } else if (!hm2) {\n        hm = hm1;\n      } else if (!hm1) {\n        hm = hm2;\n      } else {\n        // Both matched.  Select the longest.\n        hm = hm1[4].length > hm2[4].length ? hm1 : hm2;\n      }\n\n      // A half-match was found, sort out the return data.\n      var text1_a, text1_b, text2_a, text2_b;\n      if (text1.length > text2.length) {\n        text1_a = hm[0];\n        text1_b = hm[1];\n        text2_a = hm[2];\n        text2_b = hm[3];\n      } else {\n        text2_a = hm[0];\n        text2_b = hm[1];\n        text1_a = hm[2];\n        text1_b = hm[3];\n      }\n      var mid_common = hm[4];\n      return [text1_a, text1_b, text2_a, text2_b, mid_common];\n    }\n    ;\n\n    /**\n     * Reorder and merge like edit sections.  Merge equalities.\n     * Any edit section can move as long as it doesn't cross an equality.\n     * @param {Array} diffs Array of diff tuples.\n     */\n    function diff_cleanupMerge(diffs) {\n      diffs.push([DIFF_EQUAL, '']); // Add a dummy entry at the end.\n      var pointer = 0;\n      var count_delete = 0;\n      var count_insert = 0;\n      var text_delete = '';\n      var text_insert = '';\n      var commonlength;\n      while (pointer < diffs.length) {\n        switch (diffs[pointer][0]) {\n          case DIFF_INSERT:\n            count_insert++;\n            text_insert += diffs[pointer][1];\n            pointer++;\n            break;\n          case DIFF_DELETE:\n            count_delete++;\n            text_delete += diffs[pointer][1];\n            pointer++;\n            break;\n          case DIFF_EQUAL:\n            // Upon reaching an equality, check for prior redundancies.\n            if (count_delete + count_insert > 1) {\n              if (count_delete !== 0 && count_insert !== 0) {\n                // Factor out any common prefixies.\n                commonlength = diff_commonPrefix(text_insert, text_delete);\n                if (commonlength !== 0) {\n                  if (pointer - count_delete - count_insert > 0 && diffs[pointer - count_delete - count_insert - 1][0] == DIFF_EQUAL) {\n                    diffs[pointer - count_delete - count_insert - 1][1] += text_insert.substring(0, commonlength);\n                  } else {\n                    diffs.splice(0, 0, [DIFF_EQUAL, text_insert.substring(0, commonlength)]);\n                    pointer++;\n                  }\n                  text_insert = text_insert.substring(commonlength);\n                  text_delete = text_delete.substring(commonlength);\n                }\n                // Factor out any common suffixies.\n                commonlength = diff_commonSuffix(text_insert, text_delete);\n                if (commonlength !== 0) {\n                  diffs[pointer][1] = text_insert.substring(text_insert.length - commonlength) + diffs[pointer][1];\n                  text_insert = text_insert.substring(0, text_insert.length - commonlength);\n                  text_delete = text_delete.substring(0, text_delete.length - commonlength);\n                }\n              }\n              // Delete the offending records and add the merged ones.\n              if (count_delete === 0) {\n                diffs.splice(pointer - count_insert, count_delete + count_insert, [DIFF_INSERT, text_insert]);\n              } else if (count_insert === 0) {\n                diffs.splice(pointer - count_delete, count_delete + count_insert, [DIFF_DELETE, text_delete]);\n              } else {\n                diffs.splice(pointer - count_delete - count_insert, count_delete + count_insert, [DIFF_DELETE, text_delete], [DIFF_INSERT, text_insert]);\n              }\n              pointer = pointer - count_delete - count_insert + (count_delete ? 1 : 0) + (count_insert ? 1 : 0) + 1;\n            } else if (pointer !== 0 && diffs[pointer - 1][0] == DIFF_EQUAL) {\n              // Merge this equality with the previous one.\n              diffs[pointer - 1][1] += diffs[pointer][1];\n              diffs.splice(pointer, 1);\n            } else {\n              pointer++;\n            }\n            count_insert = 0;\n            count_delete = 0;\n            text_delete = '';\n            text_insert = '';\n            break;\n        }\n      }\n      if (diffs[diffs.length - 1][1] === '') {\n        diffs.pop(); // Remove the dummy entry at the end.\n      }\n\n      // Second pass: look for single edits surrounded on both sides by equalities\n      // which can be shifted sideways to eliminate an equality.\n      // e.g: A<ins>BA</ins>C -> <ins>AB</ins>AC\n      var changes = false;\n      pointer = 1;\n      // Intentionally ignore the first and last element (don't need checking).\n      while (pointer < diffs.length - 1) {\n        if (diffs[pointer - 1][0] == DIFF_EQUAL && diffs[pointer + 1][0] == DIFF_EQUAL) {\n          // This is a single edit surrounded by equalities.\n          if (diffs[pointer][1].substring(diffs[pointer][1].length - diffs[pointer - 1][1].length) == diffs[pointer - 1][1]) {\n            // Shift the edit over the previous equality.\n            diffs[pointer][1] = diffs[pointer - 1][1] + diffs[pointer][1].substring(0, diffs[pointer][1].length - diffs[pointer - 1][1].length);\n            diffs[pointer + 1][1] = diffs[pointer - 1][1] + diffs[pointer + 1][1];\n            diffs.splice(pointer - 1, 1);\n            changes = true;\n          } else if (diffs[pointer][1].substring(0, diffs[pointer + 1][1].length) == diffs[pointer + 1][1]) {\n            // Shift the edit over the next equality.\n            diffs[pointer - 1][1] += diffs[pointer + 1][1];\n            diffs[pointer][1] = diffs[pointer][1].substring(diffs[pointer + 1][1].length) + diffs[pointer + 1][1];\n            diffs.splice(pointer + 1, 1);\n            changes = true;\n          }\n        }\n        pointer++;\n      }\n      // If shifts were made, the diff needs reordering and another shift sweep.\n      if (changes) {\n        diff_cleanupMerge(diffs);\n      }\n    }\n    ;\n    var diff = diff_main;\n    diff.INSERT = DIFF_INSERT;\n    diff.DELETE = DIFF_DELETE;\n    diff.EQUAL = DIFF_EQUAL;\n    module.exports = diff;\n\n    /*\n     * Modify a diff such that the cursor position points to the start of a change:\n     * E.g.\n     *   cursor_normalize_diff([[DIFF_EQUAL, 'abc']], 1)\n     *     => [1, [[DIFF_EQUAL, 'a'], [DIFF_EQUAL, 'bc']]]\n     *   cursor_normalize_diff([[DIFF_INSERT, 'new'], [DIFF_DELETE, 'xyz']], 2)\n     *     => [2, [[DIFF_INSERT, 'new'], [DIFF_DELETE, 'xy'], [DIFF_DELETE, 'z']]]\n     *\n     * @param {Array} diffs Array of diff tuples\n     * @param {Int} cursor_pos Suggested edit position. Must not be out of bounds!\n     * @return {Array} A tuple [cursor location in the modified diff, modified diff]\n     */\n    function cursor_normalize_diff(diffs, cursor_pos) {\n      if (cursor_pos === 0) {\n        return [DIFF_EQUAL, diffs];\n      }\n      for (var current_pos = 0, i = 0; i < diffs.length; i++) {\n        var d = diffs[i];\n        if (d[0] === DIFF_DELETE || d[0] === DIFF_EQUAL) {\n          var next_pos = current_pos + d[1].length;\n          if (cursor_pos === next_pos) {\n            return [i + 1, diffs];\n          } else if (cursor_pos < next_pos) {\n            // copy to prevent side effects\n            diffs = diffs.slice();\n            // split d into two diff changes\n            var split_pos = cursor_pos - current_pos;\n            var d_left = [d[0], d[1].slice(0, split_pos)];\n            var d_right = [d[0], d[1].slice(split_pos)];\n            diffs.splice(i, 1, d_left, d_right);\n            return [i + 1, diffs];\n          } else {\n            current_pos = next_pos;\n          }\n        }\n      }\n      throw new Error('cursor_pos is out of bounds!');\n    }\n\n    /*\n     * Modify a diff such that the edit position is \"shifted\" to the proposed edit location (cursor_position).\n     *\n     * Case 1)\n     *   Check if a naive shift is possible:\n     *     [0, X], [ 1, Y] -> [ 1, Y], [0, X]    (if X + Y === Y + X)\n     *     [0, X], [-1, Y] -> [-1, Y], [0, X]    (if X + Y === Y + X) - holds same result\n     * Case 2)\n     *   Check if the following shifts are possible:\n     *     [0, 'pre'], [ 1, 'prefix'] -> [ 1, 'pre'], [0, 'pre'], [ 1, 'fix']\n     *     [0, 'pre'], [-1, 'prefix'] -> [-1, 'pre'], [0, 'pre'], [-1, 'fix']\n     *         ^            ^\n     *         d          d_next\n     *\n     * @param {Array} diffs Array of diff tuples\n     * @param {Int} cursor_pos Suggested edit position. Must not be out of bounds!\n     * @return {Array} Array of diff tuples\n     */\n    function fix_cursor(diffs, cursor_pos) {\n      var norm = cursor_normalize_diff(diffs, cursor_pos);\n      var ndiffs = norm[1];\n      var cursor_pointer = norm[0];\n      var d = ndiffs[cursor_pointer];\n      var d_next = ndiffs[cursor_pointer + 1];\n      if (d == null) {\n        // Text was deleted from end of original string,\n        // cursor is now out of bounds in new string\n        return diffs;\n      } else if (d[0] !== DIFF_EQUAL) {\n        // A modification happened at the cursor location.\n        // This is the expected outcome, so we can return the original diff.\n        return diffs;\n      } else {\n        if (d_next != null && d[1] + d_next[1] === d_next[1] + d[1]) {\n          // Case 1)\n          // It is possible to perform a naive shift\n          ndiffs.splice(cursor_pointer, 2, d_next, d);\n          return merge_tuples(ndiffs, cursor_pointer, 2);\n        } else if (d_next != null && d_next[1].indexOf(d[1]) === 0) {\n          // Case 2)\n          // d[1] is a prefix of d_next[1]\n          // We can assume that d_next[0] !== 0, since d[0] === 0\n          // Shift edit locations..\n          ndiffs.splice(cursor_pointer, 2, [d_next[0], d[1]], [0, d[1]]);\n          var suffix = d_next[1].slice(d[1].length);\n          if (suffix.length > 0) {\n            ndiffs.splice(cursor_pointer + 2, 0, [d_next[0], suffix]);\n          }\n          return merge_tuples(ndiffs, cursor_pointer, 3);\n        } else {\n          // Not possible to perform any modification\n          return diffs;\n        }\n      }\n    }\n\n    /*\n     * Check diff did not split surrogate pairs.\n     * Ex. [0, '\\uD83D'], [-1, '\\uDC36'], [1, '\\uDC2F'] -> [-1, '\\uD83D\\uDC36'], [1, '\\uD83D\\uDC2F']\n     *     '\\uD83D\\uDC36' === '🐶', '\\uD83D\\uDC2F' === '🐯'\n     *\n     * @param {Array} diffs Array of diff tuples\n     * @return {Array} Array of diff tuples\n     */\n    function fix_emoji(diffs) {\n      var compact = false;\n      var starts_with_pair_end = function (str) {\n        return str.charCodeAt(0) >= 0xDC00 && str.charCodeAt(0) <= 0xDFFF;\n      };\n      var ends_with_pair_start = function (str) {\n        return str.charCodeAt(str.length - 1) >= 0xD800 && str.charCodeAt(str.length - 1) <= 0xDBFF;\n      };\n      for (var i = 2; i < diffs.length; i += 1) {\n        if (diffs[i - 2][0] === DIFF_EQUAL && ends_with_pair_start(diffs[i - 2][1]) && diffs[i - 1][0] === DIFF_DELETE && starts_with_pair_end(diffs[i - 1][1]) && diffs[i][0] === DIFF_INSERT && starts_with_pair_end(diffs[i][1])) {\n          compact = true;\n          diffs[i - 1][1] = diffs[i - 2][1].slice(-1) + diffs[i - 1][1];\n          diffs[i][1] = diffs[i - 2][1].slice(-1) + diffs[i][1];\n          diffs[i - 2][1] = diffs[i - 2][1].slice(0, -1);\n        }\n      }\n      if (!compact) {\n        return diffs;\n      }\n      var fixed_diffs = [];\n      for (var i = 0; i < diffs.length; i += 1) {\n        if (diffs[i][1].length > 0) {\n          fixed_diffs.push(diffs[i]);\n        }\n      }\n      return fixed_diffs;\n    }\n\n    /*\n     * Try to merge tuples with their neigbors in a given range.\n     * E.g. [0, 'a'], [0, 'b'] -> [0, 'ab']\n     *\n     * @param {Array} diffs Array of diff tuples.\n     * @param {Int} start Position of the first element to merge (diffs[start] is also merged with diffs[start - 1]).\n     * @param {Int} length Number of consecutive elements to check.\n     * @return {Array} Array of merged diff tuples.\n     */\n    function merge_tuples(diffs, start, length) {\n      // Check from (start-1) to (start+length).\n      for (var i = start + length - 1; i >= 0 && i >= start - 1; i--) {\n        if (i + 1 < diffs.length) {\n          var left_d = diffs[i];\n          var right_d = diffs[i + 1];\n          if (left_d[0] === right_d[1]) {\n            diffs.splice(i, 2, [left_d[0], left_d[1] + right_d[1]]);\n          }\n        }\n      }\n      return diffs;\n    }\n\n    /***/\n  }), ( /* 52 */\n  /***/function (module, exports) {\n    exports = module.exports = typeof Object.keys === 'function' ? Object.keys : shim;\n    exports.shim = shim;\n    function shim(obj) {\n      var keys = [];\n      for (var key in obj) keys.push(key);\n      return keys;\n    }\n\n    /***/\n  }), ( /* 53 */\n  /***/function (module, exports) {\n    var supportsArgumentsClass = function () {\n      return Object.prototype.toString.call(arguments);\n    }() == '[object Arguments]';\n    exports = module.exports = supportsArgumentsClass ? supported : unsupported;\n    exports.supported = supported;\n    function supported(object) {\n      return Object.prototype.toString.call(object) == '[object Arguments]';\n    }\n    ;\n    exports.unsupported = unsupported;\n    function unsupported(object) {\n      return object && typeof object == 'object' && typeof object.length == 'number' && Object.prototype.hasOwnProperty.call(object, 'callee') && !Object.prototype.propertyIsEnumerable.call(object, 'callee') || false;\n    }\n    ;\n\n    /***/\n  }), ( /* 54 */\n  /***/function (module, exports) {\n    'use strict';\n\n    var has = Object.prototype.hasOwnProperty,\n      prefix = '~';\n\n    /**\n     * Constructor to create a storage for our `EE` objects.\n     * An `Events` instance is a plain object whose properties are event names.\n     *\n     * @constructor\n     * @api private\n     */\n    function Events() {}\n\n    //\n    // We try to not inherit from `Object.prototype`. In some engines creating an\n    // instance in this way is faster than calling `Object.create(null)` directly.\n    // If `Object.create(null)` is not supported we prefix the event names with a\n    // character to make sure that the built-in object properties are not\n    // overridden or used as an attack vector.\n    //\n    if (Object.create) {\n      Events.prototype = Object.create(null);\n\n      //\n      // This hack is needed because the `__proto__` property is still inherited in\n      // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n      //\n      if (!new Events().__proto__) prefix = false;\n    }\n\n    /**\n     * Representation of a single event listener.\n     *\n     * @param {Function} fn The listener function.\n     * @param {Mixed} context The context to invoke the listener with.\n     * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n     * @constructor\n     * @api private\n     */\n    function EE(fn, context, once) {\n      this.fn = fn;\n      this.context = context;\n      this.once = once || false;\n    }\n\n    /**\n     * Minimal `EventEmitter` interface that is molded against the Node.js\n     * `EventEmitter` interface.\n     *\n     * @constructor\n     * @api public\n     */\n    function EventEmitter() {\n      this._events = new Events();\n      this._eventsCount = 0;\n    }\n\n    /**\n     * Return an array listing the events for which the emitter has registered\n     * listeners.\n     *\n     * @returns {Array}\n     * @api public\n     */\n    EventEmitter.prototype.eventNames = function eventNames() {\n      var names = [],\n        events,\n        name;\n      if (this._eventsCount === 0) return names;\n      for (name in events = this._events) {\n        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n      }\n      if (Object.getOwnPropertySymbols) {\n        return names.concat(Object.getOwnPropertySymbols(events));\n      }\n      return names;\n    };\n\n    /**\n     * Return the listeners registered for a given event.\n     *\n     * @param {String|Symbol} event The event name.\n     * @param {Boolean} exists Only check if there are listeners.\n     * @returns {Array|Boolean}\n     * @api public\n     */\n    EventEmitter.prototype.listeners = function listeners(event, exists) {\n      var evt = prefix ? prefix + event : event,\n        available = this._events[evt];\n      if (exists) return !!available;\n      if (!available) return [];\n      if (available.fn) return [available.fn];\n      for (var i = 0, l = available.length, ee = new Array(l); i < l; i++) {\n        ee[i] = available[i].fn;\n      }\n      return ee;\n    };\n\n    /**\n     * Calls each of the listeners registered for a given event.\n     *\n     * @param {String|Symbol} event The event name.\n     * @returns {Boolean} `true` if the event had listeners, else `false`.\n     * @api public\n     */\n    EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n      var evt = prefix ? prefix + event : event;\n      if (!this._events[evt]) return false;\n      var listeners = this._events[evt],\n        len = arguments.length,\n        args,\n        i;\n      if (listeners.fn) {\n        if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n        switch (len) {\n          case 1:\n            return listeners.fn.call(listeners.context), true;\n          case 2:\n            return listeners.fn.call(listeners.context, a1), true;\n          case 3:\n            return listeners.fn.call(listeners.context, a1, a2), true;\n          case 4:\n            return listeners.fn.call(listeners.context, a1, a2, a3), true;\n          case 5:\n            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n          case 6:\n            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n        }\n        for (i = 1, args = new Array(len - 1); i < len; i++) {\n          args[i - 1] = arguments[i];\n        }\n        listeners.fn.apply(listeners.context, args);\n      } else {\n        var length = listeners.length,\n          j;\n        for (i = 0; i < length; i++) {\n          if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n          switch (len) {\n            case 1:\n              listeners[i].fn.call(listeners[i].context);\n              break;\n            case 2:\n              listeners[i].fn.call(listeners[i].context, a1);\n              break;\n            case 3:\n              listeners[i].fn.call(listeners[i].context, a1, a2);\n              break;\n            case 4:\n              listeners[i].fn.call(listeners[i].context, a1, a2, a3);\n              break;\n            default:\n              if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {\n                args[j - 1] = arguments[j];\n              }\n              listeners[i].fn.apply(listeners[i].context, args);\n          }\n        }\n      }\n      return true;\n    };\n\n    /**\n     * Add a listener for a given event.\n     *\n     * @param {String|Symbol} event The event name.\n     * @param {Function} fn The listener function.\n     * @param {Mixed} [context=this] The context to invoke the listener with.\n     * @returns {EventEmitter} `this`.\n     * @api public\n     */\n    EventEmitter.prototype.on = function on(event, fn, context) {\n      var listener = new EE(fn, context || this),\n        evt = prefix ? prefix + event : event;\n      if (!this._events[evt]) this._events[evt] = listener, this._eventsCount++;else if (!this._events[evt].fn) this._events[evt].push(listener);else this._events[evt] = [this._events[evt], listener];\n      return this;\n    };\n\n    /**\n     * Add a one-time listener for a given event.\n     *\n     * @param {String|Symbol} event The event name.\n     * @param {Function} fn The listener function.\n     * @param {Mixed} [context=this] The context to invoke the listener with.\n     * @returns {EventEmitter} `this`.\n     * @api public\n     */\n    EventEmitter.prototype.once = function once(event, fn, context) {\n      var listener = new EE(fn, context || this, true),\n        evt = prefix ? prefix + event : event;\n      if (!this._events[evt]) this._events[evt] = listener, this._eventsCount++;else if (!this._events[evt].fn) this._events[evt].push(listener);else this._events[evt] = [this._events[evt], listener];\n      return this;\n    };\n\n    /**\n     * Remove the listeners of a given event.\n     *\n     * @param {String|Symbol} event The event name.\n     * @param {Function} fn Only remove the listeners that match this function.\n     * @param {Mixed} context Only remove the listeners that have this context.\n     * @param {Boolean} once Only remove one-time listeners.\n     * @returns {EventEmitter} `this`.\n     * @api public\n     */\n    EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n      var evt = prefix ? prefix + event : event;\n      if (!this._events[evt]) return this;\n      if (!fn) {\n        if (--this._eventsCount === 0) this._events = new Events();else delete this._events[evt];\n        return this;\n      }\n      var listeners = this._events[evt];\n      if (listeners.fn) {\n        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {\n          if (--this._eventsCount === 0) this._events = new Events();else delete this._events[evt];\n        }\n      } else {\n        for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {\n            events.push(listeners[i]);\n          }\n        }\n\n        //\n        // Reset the array, or remove it completely if we have no more listeners.\n        //\n        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;else if (--this._eventsCount === 0) this._events = new Events();else delete this._events[evt];\n      }\n      return this;\n    };\n\n    /**\n     * Remove all listeners, or those of the specified event.\n     *\n     * @param {String|Symbol} [event] The event name.\n     * @returns {EventEmitter} `this`.\n     * @api public\n     */\n    EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n      var evt;\n      if (event) {\n        evt = prefix ? prefix + event : event;\n        if (this._events[evt]) {\n          if (--this._eventsCount === 0) this._events = new Events();else delete this._events[evt];\n        }\n      } else {\n        this._events = new Events();\n        this._eventsCount = 0;\n      }\n      return this;\n    };\n\n    //\n    // Alias methods names because people roll like that.\n    //\n    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n    EventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n    //\n    // This function doesn't apply anymore.\n    //\n    EventEmitter.prototype.setMaxListeners = function setMaxListeners() {\n      return this;\n    };\n\n    //\n    // Expose the prefix.\n    //\n    EventEmitter.prefixed = prefix;\n\n    //\n    // Allow `EventEmitter` to be imported as module namespace.\n    //\n    EventEmitter.EventEmitter = EventEmitter;\n\n    //\n    // Expose the module.\n    //\n    if ('undefined' !== typeof module) {\n      module.exports = EventEmitter;\n    }\n\n    /***/\n  }), ( /* 55 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.matchText = exports.matchSpacing = exports.matchNewline = exports.matchBlot = exports.matchAttributor = exports.default = undefined;\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _extend2 = __webpack_require__(3);\n    var _extend3 = _interopRequireDefault(_extend2);\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    var _align = __webpack_require__(36);\n    var _background = __webpack_require__(37);\n    var _code = __webpack_require__(13);\n    var _code2 = _interopRequireDefault(_code);\n    var _color = __webpack_require__(26);\n    var _direction = __webpack_require__(38);\n    var _font = __webpack_require__(39);\n    var _size = __webpack_require__(40);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var debug = (0, _logger2.default)('quill:clipboard');\n    var DOM_KEY = '__ql-matcher';\n    var CLIPBOARD_CONFIG = [[Node.TEXT_NODE, matchText], [Node.TEXT_NODE, matchNewline], ['br', matchBreak], [Node.ELEMENT_NODE, matchNewline], [Node.ELEMENT_NODE, matchBlot], [Node.ELEMENT_NODE, matchSpacing], [Node.ELEMENT_NODE, matchAttributor], [Node.ELEMENT_NODE, matchStyles], ['li', matchIndent], ['b', matchAlias.bind(matchAlias, 'bold')], ['i', matchAlias.bind(matchAlias, 'italic')], ['style', matchIgnore]];\n    var ATTRIBUTE_ATTRIBUTORS = [_align.AlignAttribute, _direction.DirectionAttribute].reduce(function (memo, attr) {\n      memo[attr.keyName] = attr;\n      return memo;\n    }, {});\n    var STYLE_ATTRIBUTORS = [_align.AlignStyle, _background.BackgroundStyle, _color.ColorStyle, _direction.DirectionStyle, _font.FontStyle, _size.SizeStyle].reduce(function (memo, attr) {\n      memo[attr.keyName] = attr;\n      return memo;\n    }, {});\n    var Clipboard = function (_Module) {\n      _inherits(Clipboard, _Module);\n      function Clipboard(quill, options) {\n        _classCallCheck(this, Clipboard);\n        var _this = _possibleConstructorReturn(this, (Clipboard.__proto__ || Object.getPrototypeOf(Clipboard)).call(this, quill, options));\n        _this.quill.root.addEventListener('paste', _this.onPaste.bind(_this));\n        _this.container = _this.quill.addContainer('ql-clipboard');\n        _this.container.setAttribute('contenteditable', true);\n        _this.container.setAttribute('tabindex', -1);\n        _this.matchers = [];\n        CLIPBOARD_CONFIG.concat(_this.options.matchers).forEach(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            selector = _ref2[0],\n            matcher = _ref2[1];\n          if (!options.matchVisual && matcher === matchSpacing) return;\n          _this.addMatcher(selector, matcher);\n        });\n        return _this;\n      }\n      _createClass(Clipboard, [{\n        key: 'addMatcher',\n        value: function addMatcher(selector, matcher) {\n          this.matchers.push([selector, matcher]);\n        }\n      }, {\n        key: 'convert',\n        value: function convert(html) {\n          if (typeof html === 'string') {\n            this.container.innerHTML = html.replace(/\\>\\r?\\n +\\</g, '><'); // Remove spaces between tags\n            return this.convert();\n          }\n          var formats = this.quill.getFormat(this.quill.selection.savedRange.index);\n          if (formats[_code2.default.blotName]) {\n            var text = this.container.innerText;\n            this.container.innerHTML = '';\n            return new _quillDelta2.default().insert(text, _defineProperty({}, _code2.default.blotName, formats[_code2.default.blotName]));\n          }\n          var _prepareMatching = this.prepareMatching(),\n            _prepareMatching2 = _slicedToArray(_prepareMatching, 2),\n            elementMatchers = _prepareMatching2[0],\n            textMatchers = _prepareMatching2[1];\n          var delta = traverse(this.container, elementMatchers, textMatchers);\n          // Remove trailing newline\n          if (deltaEndsWith(delta, '\\n') && delta.ops[delta.ops.length - 1].attributes == null) {\n            delta = delta.compose(new _quillDelta2.default().retain(delta.length() - 1).delete(1));\n          }\n          debug.log('convert', this.container.innerHTML, delta);\n          this.container.innerHTML = '';\n          return delta;\n        }\n      }, {\n        key: 'dangerouslyPasteHTML',\n        value: function dangerouslyPasteHTML(index, html) {\n          var source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _quill2.default.sources.API;\n          if (typeof index === 'string') {\n            this.quill.setContents(this.convert(index), html);\n            this.quill.setSelection(0, _quill2.default.sources.SILENT);\n          } else {\n            var paste = this.convert(html);\n            this.quill.updateContents(new _quillDelta2.default().retain(index).concat(paste), source);\n            this.quill.setSelection(index + paste.length(), _quill2.default.sources.SILENT);\n          }\n        }\n      }, {\n        key: 'onPaste',\n        value: function onPaste(e) {\n          var _this2 = this;\n          if (e.defaultPrevented || !this.quill.isEnabled()) return;\n          var range = this.quill.getSelection();\n          var delta = new _quillDelta2.default().retain(range.index);\n          var scrollTop = this.quill.scrollingContainer.scrollTop;\n          this.container.focus();\n          this.quill.selection.update(_quill2.default.sources.SILENT);\n          setTimeout(function () {\n            delta = delta.concat(_this2.convert()).delete(range.length);\n            _this2.quill.updateContents(delta, _quill2.default.sources.USER);\n            // range.length contributes to delta.length()\n            _this2.quill.setSelection(delta.length() - range.length, _quill2.default.sources.SILENT);\n            _this2.quill.scrollingContainer.scrollTop = scrollTop;\n            _this2.quill.focus();\n          }, 1);\n        }\n      }, {\n        key: 'prepareMatching',\n        value: function prepareMatching() {\n          var _this3 = this;\n          var elementMatchers = [],\n            textMatchers = [];\n          this.matchers.forEach(function (pair) {\n            var _pair = _slicedToArray(pair, 2),\n              selector = _pair[0],\n              matcher = _pair[1];\n            switch (selector) {\n              case Node.TEXT_NODE:\n                textMatchers.push(matcher);\n                break;\n              case Node.ELEMENT_NODE:\n                elementMatchers.push(matcher);\n                break;\n              default:\n                [].forEach.call(_this3.container.querySelectorAll(selector), function (node) {\n                  // TODO use weakmap\n                  node[DOM_KEY] = node[DOM_KEY] || [];\n                  node[DOM_KEY].push(matcher);\n                });\n                break;\n            }\n          });\n          return [elementMatchers, textMatchers];\n        }\n      }]);\n      return Clipboard;\n    }(_module2.default);\n    Clipboard.DEFAULTS = {\n      matchers: [],\n      matchVisual: true\n    };\n    function applyFormat(delta, format, value) {\n      if ((typeof format === 'undefined' ? 'undefined' : _typeof(format)) === 'object') {\n        return Object.keys(format).reduce(function (delta, key) {\n          return applyFormat(delta, key, format[key]);\n        }, delta);\n      } else {\n        return delta.reduce(function (delta, op) {\n          if (op.attributes && op.attributes[format]) {\n            return delta.push(op);\n          } else {\n            return delta.insert(op.insert, (0, _extend3.default)({}, _defineProperty({}, format, value), op.attributes));\n          }\n        }, new _quillDelta2.default());\n      }\n    }\n    function computeStyle(node) {\n      if (node.nodeType !== Node.ELEMENT_NODE) return {};\n      var DOM_KEY = '__ql-computed-style';\n      return node[DOM_KEY] || (node[DOM_KEY] = window.getComputedStyle(node));\n    }\n    function deltaEndsWith(delta, text) {\n      var endText = \"\";\n      for (var i = delta.ops.length - 1; i >= 0 && endText.length < text.length; --i) {\n        var op = delta.ops[i];\n        if (typeof op.insert !== 'string') break;\n        endText = op.insert + endText;\n      }\n      return endText.slice(-1 * text.length) === text;\n    }\n    function isLine(node) {\n      if (node.childNodes.length === 0) return false; // Exclude embed blocks\n      var style = computeStyle(node);\n      return ['block', 'list-item'].indexOf(style.display) > -1;\n    }\n    function traverse(node, elementMatchers, textMatchers) {\n      // Post-order\n      if (node.nodeType === node.TEXT_NODE) {\n        return textMatchers.reduce(function (delta, matcher) {\n          return matcher(node, delta);\n        }, new _quillDelta2.default());\n      } else if (node.nodeType === node.ELEMENT_NODE) {\n        return [].reduce.call(node.childNodes || [], function (delta, childNode) {\n          var childrenDelta = traverse(childNode, elementMatchers, textMatchers);\n          if (childNode.nodeType === node.ELEMENT_NODE) {\n            childrenDelta = elementMatchers.reduce(function (childrenDelta, matcher) {\n              return matcher(childNode, childrenDelta);\n            }, childrenDelta);\n            childrenDelta = (childNode[DOM_KEY] || []).reduce(function (childrenDelta, matcher) {\n              return matcher(childNode, childrenDelta);\n            }, childrenDelta);\n          }\n          return delta.concat(childrenDelta);\n        }, new _quillDelta2.default());\n      } else {\n        return new _quillDelta2.default();\n      }\n    }\n    function matchAlias(format, node, delta) {\n      return applyFormat(delta, format, true);\n    }\n    function matchAttributor(node, delta) {\n      var attributes = _parchment2.default.Attributor.Attribute.keys(node);\n      var classes = _parchment2.default.Attributor.Class.keys(node);\n      var styles = _parchment2.default.Attributor.Style.keys(node);\n      var formats = {};\n      attributes.concat(classes).concat(styles).forEach(function (name) {\n        var attr = _parchment2.default.query(name, _parchment2.default.Scope.ATTRIBUTE);\n        if (attr != null) {\n          formats[attr.attrName] = attr.value(node);\n          if (formats[attr.attrName]) return;\n        }\n        attr = ATTRIBUTE_ATTRIBUTORS[name];\n        if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n          formats[attr.attrName] = attr.value(node) || undefined;\n        }\n        attr = STYLE_ATTRIBUTORS[name];\n        if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n          attr = STYLE_ATTRIBUTORS[name];\n          formats[attr.attrName] = attr.value(node) || undefined;\n        }\n      });\n      if (Object.keys(formats).length > 0) {\n        delta = applyFormat(delta, formats);\n      }\n      return delta;\n    }\n    function matchBlot(node, delta) {\n      var match = _parchment2.default.query(node);\n      if (match == null) return delta;\n      if (match.prototype instanceof _parchment2.default.Embed) {\n        var embed = {};\n        var value = match.value(node);\n        if (value != null) {\n          embed[match.blotName] = value;\n          delta = new _quillDelta2.default().insert(embed, match.formats(node));\n        }\n      } else if (typeof match.formats === 'function') {\n        delta = applyFormat(delta, match.blotName, match.formats(node));\n      }\n      return delta;\n    }\n    function matchBreak(node, delta) {\n      if (!deltaEndsWith(delta, '\\n')) {\n        delta.insert('\\n');\n      }\n      return delta;\n    }\n    function matchIgnore() {\n      return new _quillDelta2.default();\n    }\n    function matchIndent(node, delta) {\n      var match = _parchment2.default.query(node);\n      if (match == null || match.blotName !== 'list-item' || !deltaEndsWith(delta, '\\n')) {\n        return delta;\n      }\n      var indent = -1,\n        parent = node.parentNode;\n      while (!parent.classList.contains('ql-clipboard')) {\n        if ((_parchment2.default.query(parent) || {}).blotName === 'list') {\n          indent += 1;\n        }\n        parent = parent.parentNode;\n      }\n      if (indent <= 0) return delta;\n      return delta.compose(new _quillDelta2.default().retain(delta.length() - 1).retain(1, {\n        indent: indent\n      }));\n    }\n    function matchNewline(node, delta) {\n      if (!deltaEndsWith(delta, '\\n')) {\n        if (isLine(node) || delta.length() > 0 && node.nextSibling && isLine(node.nextSibling)) {\n          delta.insert('\\n');\n        }\n      }\n      return delta;\n    }\n    function matchSpacing(node, delta) {\n      if (isLine(node) && node.nextElementSibling != null && !deltaEndsWith(delta, '\\n\\n')) {\n        var nodeHeight = node.offsetHeight + parseFloat(computeStyle(node).marginTop) + parseFloat(computeStyle(node).marginBottom);\n        if (node.nextElementSibling.offsetTop > node.offsetTop + nodeHeight * 1.5) {\n          delta.insert('\\n');\n        }\n      }\n      return delta;\n    }\n    function matchStyles(node, delta) {\n      var formats = {};\n      var style = node.style || {};\n      if (style.fontStyle && computeStyle(node).fontStyle === 'italic') {\n        formats.italic = true;\n      }\n      if (style.fontWeight && (computeStyle(node).fontWeight.startsWith('bold') || parseInt(computeStyle(node).fontWeight) >= 700)) {\n        formats.bold = true;\n      }\n      if (Object.keys(formats).length > 0) {\n        delta = applyFormat(delta, formats);\n      }\n      if (parseFloat(style.textIndent || 0) > 0) {\n        // Could be 0.5in\n        delta = new _quillDelta2.default().insert('\\t').concat(delta);\n      }\n      return delta;\n    }\n    function matchText(node, delta) {\n      var text = node.data;\n      // Word represents empty line with <o:p>&nbsp;</o:p>\n      if (node.parentNode.tagName === 'O:P') {\n        return delta.insert(text.trim());\n      }\n      if (text.trim().length === 0 && node.parentNode.classList.contains('ql-clipboard')) {\n        return delta;\n      }\n      if (!computeStyle(node.parentNode).whiteSpace.startsWith('pre')) {\n        // eslint-disable-next-line func-style\n        var replacer = function replacer(collapse, match) {\n          match = match.replace(/[^\\u00a0]/g, ''); // \\u00a0 is nbsp;\n          return match.length < 1 && collapse ? ' ' : match;\n        };\n        text = text.replace(/\\r\\n/g, ' ').replace(/\\n/g, ' ');\n        text = text.replace(/\\s\\s+/g, replacer.bind(replacer, true)); // collapse whitespace\n        if (node.previousSibling == null && isLine(node.parentNode) || node.previousSibling != null && isLine(node.previousSibling)) {\n          text = text.replace(/^\\s+/, replacer.bind(replacer, false));\n        }\n        if (node.nextSibling == null && isLine(node.parentNode) || node.nextSibling != null && isLine(node.nextSibling)) {\n          text = text.replace(/\\s+$/, replacer.bind(replacer, false));\n        }\n      }\n      return delta.insert(text);\n    }\n    exports.default = Clipboard;\n    exports.matchAttributor = matchAttributor;\n    exports.matchBlot = matchBlot;\n    exports.matchNewline = matchNewline;\n    exports.matchSpacing = matchSpacing;\n    exports.matchText = matchText;\n\n    /***/\n  }), ( /* 56 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Bold = function (_Inline) {\n      _inherits(Bold, _Inline);\n      function Bold() {\n        _classCallCheck(this, Bold);\n        return _possibleConstructorReturn(this, (Bold.__proto__ || Object.getPrototypeOf(Bold)).apply(this, arguments));\n      }\n      _createClass(Bold, [{\n        key: 'optimize',\n        value: function optimize(context) {\n          _get(Bold.prototype.__proto__ || Object.getPrototypeOf(Bold.prototype), 'optimize', this).call(this, context);\n          if (this.domNode.tagName !== this.statics.tagName[0]) {\n            this.replaceWith(this.statics.blotName);\n          }\n        }\n      }], [{\n        key: 'create',\n        value: function create() {\n          return _get(Bold.__proto__ || Object.getPrototypeOf(Bold), 'create', this).call(this);\n        }\n      }, {\n        key: 'formats',\n        value: function formats() {\n          return true;\n        }\n      }]);\n      return Bold;\n    }(_inline2.default);\n    Bold.blotName = 'bold';\n    Bold.tagName = ['STRONG', 'B'];\n    exports.default = Bold;\n\n    /***/\n  }), ( /* 57 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.addControls = exports.default = undefined;\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _quillDelta = __webpack_require__(2);\n    var _quillDelta2 = _interopRequireDefault(_quillDelta);\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _logger = __webpack_require__(10);\n    var _logger2 = _interopRequireDefault(_logger);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var debug = (0, _logger2.default)('quill:toolbar');\n    var Toolbar = function (_Module) {\n      _inherits(Toolbar, _Module);\n      function Toolbar(quill, options) {\n        _classCallCheck(this, Toolbar);\n        var _this = _possibleConstructorReturn(this, (Toolbar.__proto__ || Object.getPrototypeOf(Toolbar)).call(this, quill, options));\n        if (Array.isArray(_this.options.container)) {\n          var container = document.createElement('div');\n          addControls(container, _this.options.container);\n          quill.container.parentNode.insertBefore(container, quill.container);\n          _this.container = container;\n        } else if (typeof _this.options.container === 'string') {\n          _this.container = document.querySelector(_this.options.container);\n        } else {\n          _this.container = _this.options.container;\n        }\n        if (!(_this.container instanceof HTMLElement)) {\n          var _ret;\n          return _ret = debug.error('Container required for toolbar', _this.options), _possibleConstructorReturn(_this, _ret);\n        }\n        _this.container.classList.add('ql-toolbar');\n        _this.controls = [];\n        _this.handlers = {};\n        Object.keys(_this.options.handlers).forEach(function (format) {\n          _this.addHandler(format, _this.options.handlers[format]);\n        });\n        [].forEach.call(_this.container.querySelectorAll('button, select'), function (input) {\n          _this.attach(input);\n        });\n        _this.quill.on(_quill2.default.events.EDITOR_CHANGE, function (type, range) {\n          if (type === _quill2.default.events.SELECTION_CHANGE) {\n            _this.update(range);\n          }\n        });\n        _this.quill.on(_quill2.default.events.SCROLL_OPTIMIZE, function () {\n          var _this$quill$selection = _this.quill.selection.getRange(),\n            _this$quill$selection2 = _slicedToArray(_this$quill$selection, 1),\n            range = _this$quill$selection2[0]; // quill.getSelection triggers update\n\n          _this.update(range);\n        });\n        return _this;\n      }\n      _createClass(Toolbar, [{\n        key: 'addHandler',\n        value: function addHandler(format, handler) {\n          this.handlers[format] = handler;\n        }\n      }, {\n        key: 'attach',\n        value: function attach(input) {\n          var _this2 = this;\n          var format = [].find.call(input.classList, function (className) {\n            return className.indexOf('ql-') === 0;\n          });\n          if (!format) return;\n          format = format.slice('ql-'.length);\n          if (input.tagName === 'BUTTON') {\n            input.setAttribute('type', 'button');\n          }\n          if (this.handlers[format] == null) {\n            if (this.quill.scroll.whitelist != null && this.quill.scroll.whitelist[format] == null) {\n              debug.warn('ignoring attaching to disabled format', format, input);\n              return;\n            }\n            if (_parchment2.default.query(format) == null) {\n              debug.warn('ignoring attaching to nonexistent format', format, input);\n              return;\n            }\n          }\n          var eventName = input.tagName === 'SELECT' ? 'change' : 'click';\n          input.addEventListener(eventName, function (e) {\n            var value = void 0;\n            if (input.tagName === 'SELECT') {\n              if (input.selectedIndex < 0) return;\n              var selected = input.options[input.selectedIndex];\n              if (selected.hasAttribute('selected')) {\n                value = false;\n              } else {\n                value = selected.value || false;\n              }\n            } else {\n              if (input.classList.contains('ql-active')) {\n                value = false;\n              } else {\n                value = input.value || !input.hasAttribute('value');\n              }\n              e.preventDefault();\n            }\n            _this2.quill.focus();\n            var _quill$selection$getR = _this2.quill.selection.getRange(),\n              _quill$selection$getR2 = _slicedToArray(_quill$selection$getR, 1),\n              range = _quill$selection$getR2[0];\n            if (_this2.handlers[format] != null) {\n              _this2.handlers[format].call(_this2, value);\n            } else if (_parchment2.default.query(format).prototype instanceof _parchment2.default.Embed) {\n              value = prompt('Enter ' + format);\n              if (!value) return;\n              _this2.quill.updateContents(new _quillDelta2.default().retain(range.index).delete(range.length).insert(_defineProperty({}, format, value)), _quill2.default.sources.USER);\n            } else {\n              _this2.quill.format(format, value, _quill2.default.sources.USER);\n            }\n            _this2.update(range);\n          });\n          // TODO use weakmap\n          this.controls.push([format, input]);\n        }\n      }, {\n        key: 'update',\n        value: function update(range) {\n          var formats = range == null ? {} : this.quill.getFormat(range);\n          this.controls.forEach(function (pair) {\n            var _pair = _slicedToArray(pair, 2),\n              format = _pair[0],\n              input = _pair[1];\n            if (input.tagName === 'SELECT') {\n              var option = void 0;\n              if (range == null) {\n                option = null;\n              } else if (formats[format] == null) {\n                option = input.querySelector('option[selected]');\n              } else if (!Array.isArray(formats[format])) {\n                var value = formats[format];\n                if (typeof value === 'string') {\n                  value = value.replace(/\\\"/g, '\\\\\"');\n                }\n                option = input.querySelector('option[value=\"' + value + '\"]');\n              }\n              if (option == null) {\n                input.value = ''; // TODO make configurable?\n                input.selectedIndex = -1;\n              } else {\n                option.selected = true;\n              }\n            } else {\n              if (range == null) {\n                input.classList.remove('ql-active');\n              } else if (input.hasAttribute('value')) {\n                // both being null should match (default values)\n                // '1' should match with 1 (headers)\n                var isActive = formats[format] === input.getAttribute('value') || formats[format] != null && formats[format].toString() === input.getAttribute('value') || formats[format] == null && !input.getAttribute('value');\n                input.classList.toggle('ql-active', isActive);\n              } else {\n                input.classList.toggle('ql-active', formats[format] != null);\n              }\n            }\n          });\n        }\n      }]);\n      return Toolbar;\n    }(_module2.default);\n    Toolbar.DEFAULTS = {};\n    function addButton(container, format, value) {\n      var input = document.createElement('button');\n      input.setAttribute('type', 'button');\n      input.classList.add('ql-' + format);\n      if (value != null) {\n        input.value = value;\n      }\n      container.appendChild(input);\n    }\n    function addControls(container, groups) {\n      if (!Array.isArray(groups[0])) {\n        groups = [groups];\n      }\n      groups.forEach(function (controls) {\n        var group = document.createElement('span');\n        group.classList.add('ql-formats');\n        controls.forEach(function (control) {\n          if (typeof control === 'string') {\n            addButton(group, control);\n          } else {\n            var format = Object.keys(control)[0];\n            var value = control[format];\n            if (Array.isArray(value)) {\n              addSelect(group, format, value);\n            } else {\n              addButton(group, format, value);\n            }\n          }\n        });\n        container.appendChild(group);\n      });\n    }\n    function addSelect(container, format, values) {\n      var input = document.createElement('select');\n      input.classList.add('ql-' + format);\n      values.forEach(function (value) {\n        var option = document.createElement('option');\n        if (value !== false) {\n          option.setAttribute('value', value);\n        } else {\n          option.setAttribute('selected', 'selected');\n        }\n        input.appendChild(option);\n      });\n      container.appendChild(input);\n    }\n    Toolbar.DEFAULTS = {\n      container: null,\n      handlers: {\n        clean: function clean() {\n          var _this3 = this;\n          var range = this.quill.getSelection();\n          if (range == null) return;\n          if (range.length == 0) {\n            var formats = this.quill.getFormat();\n            Object.keys(formats).forEach(function (name) {\n              // Clean functionality in existing apps only clean inline formats\n              if (_parchment2.default.query(name, _parchment2.default.Scope.INLINE) != null) {\n                _this3.quill.format(name, false);\n              }\n            });\n          } else {\n            this.quill.removeFormat(range, _quill2.default.sources.USER);\n          }\n        },\n        direction: function direction(value) {\n          var align = this.quill.getFormat()['align'];\n          if (value === 'rtl' && align == null) {\n            this.quill.format('align', 'right', _quill2.default.sources.USER);\n          } else if (!value && align === 'right') {\n            this.quill.format('align', false, _quill2.default.sources.USER);\n          }\n          this.quill.format('direction', value, _quill2.default.sources.USER);\n        },\n        indent: function indent(value) {\n          var range = this.quill.getSelection();\n          var formats = this.quill.getFormat(range);\n          var indent = parseInt(formats.indent || 0);\n          if (value === '+1' || value === '-1') {\n            var modifier = value === '+1' ? 1 : -1;\n            if (formats.direction === 'rtl') modifier *= -1;\n            this.quill.format('indent', indent + modifier, _quill2.default.sources.USER);\n          }\n        },\n        link: function link(value) {\n          if (value === true) {\n            value = prompt('Enter link URL:');\n          }\n          this.quill.format('link', value, _quill2.default.sources.USER);\n        },\n        list: function list(value) {\n          var range = this.quill.getSelection();\n          var formats = this.quill.getFormat(range);\n          if (value === 'check') {\n            if (formats['list'] === 'checked' || formats['list'] === 'unchecked') {\n              this.quill.format('list', false, _quill2.default.sources.USER);\n            } else {\n              this.quill.format('list', 'unchecked', _quill2.default.sources.USER);\n            }\n          } else {\n            this.quill.format('list', value, _quill2.default.sources.USER);\n          }\n        }\n      }\n    };\n    exports.default = Toolbar;\n    exports.addControls = addControls;\n\n    /***/\n  }), ( /* 58 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <polyline class=\\\"ql-even ql-stroke\\\" points=\\\"5 7 3 9 5 11\\\"></polyline> <polyline class=\\\"ql-even ql-stroke\\\" points=\\\"13 7 15 9 13 11\\\"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>\";\n\n    /***/\n  }), ( /* 59 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _picker = __webpack_require__(28);\n    var _picker2 = _interopRequireDefault(_picker);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ColorPicker = function (_Picker) {\n      _inherits(ColorPicker, _Picker);\n      function ColorPicker(select, label) {\n        _classCallCheck(this, ColorPicker);\n        var _this = _possibleConstructorReturn(this, (ColorPicker.__proto__ || Object.getPrototypeOf(ColorPicker)).call(this, select));\n        _this.label.innerHTML = label;\n        _this.container.classList.add('ql-color-picker');\n        [].slice.call(_this.container.querySelectorAll('.ql-picker-item'), 0, 7).forEach(function (item) {\n          item.classList.add('ql-primary');\n        });\n        return _this;\n      }\n      _createClass(ColorPicker, [{\n        key: 'buildItem',\n        value: function buildItem(option) {\n          var item = _get(ColorPicker.prototype.__proto__ || Object.getPrototypeOf(ColorPicker.prototype), 'buildItem', this).call(this, option);\n          item.style.backgroundColor = option.getAttribute('value') || '';\n          return item;\n        }\n      }, {\n        key: 'selectItem',\n        value: function selectItem(item, trigger) {\n          _get(ColorPicker.prototype.__proto__ || Object.getPrototypeOf(ColorPicker.prototype), 'selectItem', this).call(this, item, trigger);\n          var colorLabel = this.label.querySelector('.ql-color-label');\n          var value = item ? item.getAttribute('data-value') || '' : '';\n          if (colorLabel) {\n            if (colorLabel.tagName === 'line') {\n              colorLabel.style.stroke = value;\n            } else {\n              colorLabel.style.fill = value;\n            }\n          }\n        }\n      }]);\n      return ColorPicker;\n    }(_picker2.default);\n    exports.default = ColorPicker;\n\n    /***/\n  }), ( /* 60 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _picker = __webpack_require__(28);\n    var _picker2 = _interopRequireDefault(_picker);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var IconPicker = function (_Picker) {\n      _inherits(IconPicker, _Picker);\n      function IconPicker(select, icons) {\n        _classCallCheck(this, IconPicker);\n        var _this = _possibleConstructorReturn(this, (IconPicker.__proto__ || Object.getPrototypeOf(IconPicker)).call(this, select));\n        _this.container.classList.add('ql-icon-picker');\n        [].forEach.call(_this.container.querySelectorAll('.ql-picker-item'), function (item) {\n          item.innerHTML = icons[item.getAttribute('data-value') || ''];\n        });\n        _this.defaultItem = _this.container.querySelector('.ql-selected');\n        _this.selectItem(_this.defaultItem);\n        return _this;\n      }\n      _createClass(IconPicker, [{\n        key: 'selectItem',\n        value: function selectItem(item, trigger) {\n          _get(IconPicker.prototype.__proto__ || Object.getPrototypeOf(IconPicker.prototype), 'selectItem', this).call(this, item, trigger);\n          item = item || this.defaultItem;\n          this.label.innerHTML = item.innerHTML;\n        }\n      }]);\n      return IconPicker;\n    }(_picker2.default);\n    exports.default = IconPicker;\n\n    /***/\n  }), ( /* 61 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var Tooltip = function () {\n      function Tooltip(quill, boundsContainer) {\n        var _this = this;\n        _classCallCheck(this, Tooltip);\n        this.quill = quill;\n        this.boundsContainer = boundsContainer || document.body;\n        this.root = quill.addContainer('ql-tooltip');\n        this.root.innerHTML = this.constructor.TEMPLATE;\n        if (this.quill.root === this.quill.scrollingContainer) {\n          this.quill.root.addEventListener('scroll', function () {\n            _this.root.style.marginTop = -1 * _this.quill.root.scrollTop + 'px';\n          });\n        }\n        this.hide();\n      }\n      _createClass(Tooltip, [{\n        key: 'hide',\n        value: function hide() {\n          this.root.classList.add('ql-hidden');\n        }\n      }, {\n        key: 'position',\n        value: function position(reference) {\n          var left = reference.left + reference.width / 2 - this.root.offsetWidth / 2;\n          // root.scrollTop should be 0 if scrollContainer !== root\n          var top = reference.bottom + this.quill.root.scrollTop;\n          this.root.style.left = left + 'px';\n          this.root.style.top = top + 'px';\n          this.root.classList.remove('ql-flip');\n          var containerBounds = this.boundsContainer.getBoundingClientRect();\n          var rootBounds = this.root.getBoundingClientRect();\n          var shift = 0;\n          if (rootBounds.right > containerBounds.right) {\n            shift = containerBounds.right - rootBounds.right;\n            this.root.style.left = left + shift + 'px';\n          }\n          if (rootBounds.left < containerBounds.left) {\n            shift = containerBounds.left - rootBounds.left;\n            this.root.style.left = left + shift + 'px';\n          }\n          if (rootBounds.bottom > containerBounds.bottom) {\n            var height = rootBounds.bottom - rootBounds.top;\n            var verticalShift = reference.bottom - reference.top + height;\n            this.root.style.top = top - verticalShift + 'px';\n            this.root.classList.add('ql-flip');\n          }\n          return shift;\n        }\n      }, {\n        key: 'show',\n        value: function show() {\n          this.root.classList.remove('ql-editing');\n          this.root.classList.remove('ql-hidden');\n        }\n      }]);\n      return Tooltip;\n    }();\n    exports.default = Tooltip;\n\n    /***/\n  }), ( /* 62 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _slicedToArray = function () {\n      function sliceIterator(arr, i) {\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _e = undefined;\n        try {\n          for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"]) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      return function (arr, i) {\n        if (Array.isArray(arr)) {\n          return arr;\n        } else if (Symbol.iterator in Object(arr)) {\n          return sliceIterator(arr, i);\n        } else {\n          throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n        }\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _emitter = __webpack_require__(8);\n    var _emitter2 = _interopRequireDefault(_emitter);\n    var _base = __webpack_require__(43);\n    var _base2 = _interopRequireDefault(_base);\n    var _link = __webpack_require__(27);\n    var _link2 = _interopRequireDefault(_link);\n    var _selection = __webpack_require__(15);\n    var _icons = __webpack_require__(41);\n    var _icons2 = _interopRequireDefault(_icons);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var TOOLBAR_CONFIG = [[{\n      header: ['1', '2', '3', false]\n    }], ['bold', 'italic', 'underline', 'link'], [{\n      list: 'ordered'\n    }, {\n      list: 'bullet'\n    }], ['clean']];\n    var SnowTheme = function (_BaseTheme) {\n      _inherits(SnowTheme, _BaseTheme);\n      function SnowTheme(quill, options) {\n        _classCallCheck(this, SnowTheme);\n        if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n          options.modules.toolbar.container = TOOLBAR_CONFIG;\n        }\n        var _this = _possibleConstructorReturn(this, (SnowTheme.__proto__ || Object.getPrototypeOf(SnowTheme)).call(this, quill, options));\n        _this.quill.container.classList.add('ql-snow');\n        return _this;\n      }\n      _createClass(SnowTheme, [{\n        key: 'extendToolbar',\n        value: function extendToolbar(toolbar) {\n          toolbar.container.classList.add('ql-snow');\n          this.buildButtons([].slice.call(toolbar.container.querySelectorAll('button')), _icons2.default);\n          this.buildPickers([].slice.call(toolbar.container.querySelectorAll('select')), _icons2.default);\n          this.tooltip = new SnowTooltip(this.quill, this.options.bounds);\n          if (toolbar.container.querySelector('.ql-link')) {\n            this.quill.keyboard.addBinding({\n              key: 'K',\n              shortKey: true\n            }, function (range, context) {\n              toolbar.handlers['link'].call(toolbar, !context.format.link);\n            });\n          }\n        }\n      }]);\n      return SnowTheme;\n    }(_base2.default);\n    SnowTheme.DEFAULTS = (0, _extend2.default)(true, {}, _base2.default.DEFAULTS, {\n      modules: {\n        toolbar: {\n          handlers: {\n            link: function link(value) {\n              if (value) {\n                var range = this.quill.getSelection();\n                if (range == null || range.length == 0) return;\n                var preview = this.quill.getText(range);\n                if (/^\\S+@\\S+\\.\\S+$/.test(preview) && preview.indexOf('mailto:') !== 0) {\n                  preview = 'mailto:' + preview;\n                }\n                var tooltip = this.quill.theme.tooltip;\n                tooltip.edit('link', preview);\n              } else {\n                this.quill.format('link', false);\n              }\n            }\n          }\n        }\n      }\n    });\n    var SnowTooltip = function (_BaseTooltip) {\n      _inherits(SnowTooltip, _BaseTooltip);\n      function SnowTooltip(quill, bounds) {\n        _classCallCheck(this, SnowTooltip);\n        var _this2 = _possibleConstructorReturn(this, (SnowTooltip.__proto__ || Object.getPrototypeOf(SnowTooltip)).call(this, quill, bounds));\n        _this2.preview = _this2.root.querySelector('a.ql-preview');\n        return _this2;\n      }\n      _createClass(SnowTooltip, [{\n        key: 'listen',\n        value: function listen() {\n          var _this3 = this;\n          _get(SnowTooltip.prototype.__proto__ || Object.getPrototypeOf(SnowTooltip.prototype), 'listen', this).call(this);\n          this.root.querySelector('a.ql-action').addEventListener('click', function (event) {\n            if (_this3.root.classList.contains('ql-editing')) {\n              _this3.save();\n            } else {\n              _this3.edit('link', _this3.preview.textContent);\n            }\n            event.preventDefault();\n          });\n          this.root.querySelector('a.ql-remove').addEventListener('click', function (event) {\n            if (_this3.linkRange != null) {\n              var range = _this3.linkRange;\n              _this3.restoreFocus();\n              _this3.quill.formatText(range, 'link', false, _emitter2.default.sources.USER);\n              delete _this3.linkRange;\n            }\n            event.preventDefault();\n            _this3.hide();\n          });\n          this.quill.on(_emitter2.default.events.SELECTION_CHANGE, function (range, oldRange, source) {\n            if (range == null) return;\n            if (range.length === 0 && source === _emitter2.default.sources.USER) {\n              var _quill$scroll$descend = _this3.quill.scroll.descendant(_link2.default, range.index),\n                _quill$scroll$descend2 = _slicedToArray(_quill$scroll$descend, 2),\n                link = _quill$scroll$descend2[0],\n                offset = _quill$scroll$descend2[1];\n              if (link != null) {\n                _this3.linkRange = new _selection.Range(range.index - offset, link.length());\n                var preview = _link2.default.formats(link.domNode);\n                _this3.preview.textContent = preview;\n                _this3.preview.setAttribute('href', preview);\n                _this3.show();\n                _this3.position(_this3.quill.getBounds(_this3.linkRange));\n                return;\n              }\n            } else {\n              delete _this3.linkRange;\n            }\n            _this3.hide();\n          });\n        }\n      }, {\n        key: 'show',\n        value: function show() {\n          _get(SnowTooltip.prototype.__proto__ || Object.getPrototypeOf(SnowTooltip.prototype), 'show', this).call(this);\n          this.root.removeAttribute('data-mode');\n        }\n      }]);\n      return SnowTooltip;\n    }(_base.BaseTooltip);\n    SnowTooltip.TEMPLATE = ['<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-action\"></a>', '<a class=\"ql-remove\"></a>'].join('');\n    exports.default = SnowTheme;\n\n    /***/\n  }), ( /* 63 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _core = __webpack_require__(29);\n    var _core2 = _interopRequireDefault(_core);\n    var _align = __webpack_require__(36);\n    var _direction = __webpack_require__(38);\n    var _indent = __webpack_require__(64);\n    var _blockquote = __webpack_require__(65);\n    var _blockquote2 = _interopRequireDefault(_blockquote);\n    var _header = __webpack_require__(66);\n    var _header2 = _interopRequireDefault(_header);\n    var _list = __webpack_require__(67);\n    var _list2 = _interopRequireDefault(_list);\n    var _background = __webpack_require__(37);\n    var _color = __webpack_require__(26);\n    var _font = __webpack_require__(39);\n    var _size = __webpack_require__(40);\n    var _bold = __webpack_require__(56);\n    var _bold2 = _interopRequireDefault(_bold);\n    var _italic = __webpack_require__(68);\n    var _italic2 = _interopRequireDefault(_italic);\n    var _link = __webpack_require__(27);\n    var _link2 = _interopRequireDefault(_link);\n    var _script = __webpack_require__(69);\n    var _script2 = _interopRequireDefault(_script);\n    var _strike = __webpack_require__(70);\n    var _strike2 = _interopRequireDefault(_strike);\n    var _underline = __webpack_require__(71);\n    var _underline2 = _interopRequireDefault(_underline);\n    var _image = __webpack_require__(72);\n    var _image2 = _interopRequireDefault(_image);\n    var _video = __webpack_require__(73);\n    var _video2 = _interopRequireDefault(_video);\n    var _code = __webpack_require__(13);\n    var _code2 = _interopRequireDefault(_code);\n    var _formula = __webpack_require__(74);\n    var _formula2 = _interopRequireDefault(_formula);\n    var _syntax = __webpack_require__(75);\n    var _syntax2 = _interopRequireDefault(_syntax);\n    var _toolbar = __webpack_require__(57);\n    var _toolbar2 = _interopRequireDefault(_toolbar);\n    var _icons = __webpack_require__(41);\n    var _icons2 = _interopRequireDefault(_icons);\n    var _picker = __webpack_require__(28);\n    var _picker2 = _interopRequireDefault(_picker);\n    var _colorPicker = __webpack_require__(59);\n    var _colorPicker2 = _interopRequireDefault(_colorPicker);\n    var _iconPicker = __webpack_require__(60);\n    var _iconPicker2 = _interopRequireDefault(_iconPicker);\n    var _tooltip = __webpack_require__(61);\n    var _tooltip2 = _interopRequireDefault(_tooltip);\n    var _bubble = __webpack_require__(108);\n    var _bubble2 = _interopRequireDefault(_bubble);\n    var _snow = __webpack_require__(62);\n    var _snow2 = _interopRequireDefault(_snow);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    _core2.default.register({\n      'attributors/attribute/direction': _direction.DirectionAttribute,\n      'attributors/class/align': _align.AlignClass,\n      'attributors/class/background': _background.BackgroundClass,\n      'attributors/class/color': _color.ColorClass,\n      'attributors/class/direction': _direction.DirectionClass,\n      'attributors/class/font': _font.FontClass,\n      'attributors/class/size': _size.SizeClass,\n      'attributors/style/align': _align.AlignStyle,\n      'attributors/style/background': _background.BackgroundStyle,\n      'attributors/style/color': _color.ColorStyle,\n      'attributors/style/direction': _direction.DirectionStyle,\n      'attributors/style/font': _font.FontStyle,\n      'attributors/style/size': _size.SizeStyle\n    }, true);\n    _core2.default.register({\n      'formats/align': _align.AlignClass,\n      'formats/direction': _direction.DirectionClass,\n      'formats/indent': _indent.IndentClass,\n      'formats/background': _background.BackgroundStyle,\n      'formats/color': _color.ColorStyle,\n      'formats/font': _font.FontClass,\n      'formats/size': _size.SizeClass,\n      'formats/blockquote': _blockquote2.default,\n      'formats/code-block': _code2.default,\n      'formats/header': _header2.default,\n      'formats/list': _list2.default,\n      'formats/bold': _bold2.default,\n      'formats/code': _code.Code,\n      'formats/italic': _italic2.default,\n      'formats/link': _link2.default,\n      'formats/script': _script2.default,\n      'formats/strike': _strike2.default,\n      'formats/underline': _underline2.default,\n      'formats/image': _image2.default,\n      'formats/video': _video2.default,\n      'formats/list/item': _list.ListItem,\n      'modules/formula': _formula2.default,\n      'modules/syntax': _syntax2.default,\n      'modules/toolbar': _toolbar2.default,\n      'themes/bubble': _bubble2.default,\n      'themes/snow': _snow2.default,\n      'ui/icons': _icons2.default,\n      'ui/picker': _picker2.default,\n      'ui/icon-picker': _iconPicker2.default,\n      'ui/color-picker': _colorPicker2.default,\n      'ui/tooltip': _tooltip2.default\n    }, true);\n    exports.default = _core2.default;\n\n    /***/\n  }), ( /* 64 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.IndentClass = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var IdentAttributor = function (_Parchment$Attributor) {\n      _inherits(IdentAttributor, _Parchment$Attributor);\n      function IdentAttributor() {\n        _classCallCheck(this, IdentAttributor);\n        return _possibleConstructorReturn(this, (IdentAttributor.__proto__ || Object.getPrototypeOf(IdentAttributor)).apply(this, arguments));\n      }\n      _createClass(IdentAttributor, [{\n        key: 'add',\n        value: function add(node, value) {\n          if (value === '+1' || value === '-1') {\n            var indent = this.value(node) || 0;\n            value = value === '+1' ? indent + 1 : indent - 1;\n          }\n          if (value === 0) {\n            this.remove(node);\n            return true;\n          } else {\n            return _get(IdentAttributor.prototype.__proto__ || Object.getPrototypeOf(IdentAttributor.prototype), 'add', this).call(this, node, value);\n          }\n        }\n      }, {\n        key: 'canAdd',\n        value: function canAdd(node, value) {\n          return _get(IdentAttributor.prototype.__proto__ || Object.getPrototypeOf(IdentAttributor.prototype), 'canAdd', this).call(this, node, value) || _get(IdentAttributor.prototype.__proto__ || Object.getPrototypeOf(IdentAttributor.prototype), 'canAdd', this).call(this, node, parseInt(value));\n        }\n      }, {\n        key: 'value',\n        value: function value(node) {\n          return parseInt(_get(IdentAttributor.prototype.__proto__ || Object.getPrototypeOf(IdentAttributor.prototype), 'value', this).call(this, node)) || undefined; // Don't return NaN\n        }\n      }]);\n      return IdentAttributor;\n    }(_parchment2.default.Attributor.Class);\n    var IndentClass = new IdentAttributor('indent', 'ql-indent', {\n      scope: _parchment2.default.Scope.BLOCK,\n      whitelist: [1, 2, 3, 4, 5, 6, 7, 8]\n    });\n    exports.IndentClass = IndentClass;\n\n    /***/\n  }), ( /* 65 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Blockquote = function (_Block) {\n      _inherits(Blockquote, _Block);\n      function Blockquote() {\n        _classCallCheck(this, Blockquote);\n        return _possibleConstructorReturn(this, (Blockquote.__proto__ || Object.getPrototypeOf(Blockquote)).apply(this, arguments));\n      }\n      return Blockquote;\n    }(_block2.default);\n    Blockquote.blotName = 'blockquote';\n    Blockquote.tagName = 'blockquote';\n    exports.default = Blockquote;\n\n    /***/\n  }), ( /* 66 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Header = function (_Block) {\n      _inherits(Header, _Block);\n      function Header() {\n        _classCallCheck(this, Header);\n        return _possibleConstructorReturn(this, (Header.__proto__ || Object.getPrototypeOf(Header)).apply(this, arguments));\n      }\n      _createClass(Header, null, [{\n        key: 'formats',\n        value: function formats(domNode) {\n          return this.tagName.indexOf(domNode.tagName) + 1;\n        }\n      }]);\n      return Header;\n    }(_block2.default);\n    Header.blotName = 'header';\n    Header.tagName = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'];\n    exports.default = Header;\n\n    /***/\n  }), ( /* 67 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.ListItem = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _block = __webpack_require__(4);\n    var _block2 = _interopRequireDefault(_block);\n    var _container = __webpack_require__(25);\n    var _container2 = _interopRequireDefault(_container);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _defineProperty(obj, key, value) {\n      if (key in obj) {\n        Object.defineProperty(obj, key, {\n          value: value,\n          enumerable: true,\n          configurable: true,\n          writable: true\n        });\n      } else {\n        obj[key] = value;\n      }\n      return obj;\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ListItem = function (_Block) {\n      _inherits(ListItem, _Block);\n      function ListItem() {\n        _classCallCheck(this, ListItem);\n        return _possibleConstructorReturn(this, (ListItem.__proto__ || Object.getPrototypeOf(ListItem)).apply(this, arguments));\n      }\n      _createClass(ListItem, [{\n        key: 'format',\n        value: function format(name, value) {\n          if (name === List.blotName && !value) {\n            this.replaceWith(_parchment2.default.create(this.statics.scope));\n          } else {\n            _get(ListItem.prototype.__proto__ || Object.getPrototypeOf(ListItem.prototype), 'format', this).call(this, name, value);\n          }\n        }\n      }, {\n        key: 'remove',\n        value: function remove() {\n          if (this.prev == null && this.next == null) {\n            this.parent.remove();\n          } else {\n            _get(ListItem.prototype.__proto__ || Object.getPrototypeOf(ListItem.prototype), 'remove', this).call(this);\n          }\n        }\n      }, {\n        key: 'replaceWith',\n        value: function replaceWith(name, value) {\n          this.parent.isolate(this.offset(this.parent), this.length());\n          if (name === this.parent.statics.blotName) {\n            this.parent.replaceWith(name, value);\n            return this;\n          } else {\n            this.parent.unwrap();\n            return _get(ListItem.prototype.__proto__ || Object.getPrototypeOf(ListItem.prototype), 'replaceWith', this).call(this, name, value);\n          }\n        }\n      }], [{\n        key: 'formats',\n        value: function formats(domNode) {\n          return domNode.tagName === this.tagName ? undefined : _get(ListItem.__proto__ || Object.getPrototypeOf(ListItem), 'formats', this).call(this, domNode);\n        }\n      }]);\n      return ListItem;\n    }(_block2.default);\n    ListItem.blotName = 'list-item';\n    ListItem.tagName = 'LI';\n    var List = function (_Container) {\n      _inherits(List, _Container);\n      _createClass(List, null, [{\n        key: 'create',\n        value: function create(value) {\n          var tagName = value === 'ordered' ? 'OL' : 'UL';\n          var node = _get(List.__proto__ || Object.getPrototypeOf(List), 'create', this).call(this, tagName);\n          if (value === 'checked' || value === 'unchecked') {\n            node.setAttribute('data-checked', value === 'checked');\n          }\n          return node;\n        }\n      }, {\n        key: 'formats',\n        value: function formats(domNode) {\n          if (domNode.tagName === 'OL') return 'ordered';\n          if (domNode.tagName === 'UL') {\n            if (domNode.hasAttribute('data-checked')) {\n              return domNode.getAttribute('data-checked') === 'true' ? 'checked' : 'unchecked';\n            } else {\n              return 'bullet';\n            }\n          }\n          return undefined;\n        }\n      }]);\n      function List(domNode) {\n        _classCallCheck(this, List);\n        var _this2 = _possibleConstructorReturn(this, (List.__proto__ || Object.getPrototypeOf(List)).call(this, domNode));\n        var listEventHandler = function listEventHandler(e) {\n          if (e.target.parentNode !== domNode) return;\n          var format = _this2.statics.formats(domNode);\n          var blot = _parchment2.default.find(e.target);\n          if (format === 'checked') {\n            blot.format('list', 'unchecked');\n          } else if (format === 'unchecked') {\n            blot.format('list', 'checked');\n          }\n        };\n        domNode.addEventListener('touchstart', listEventHandler);\n        domNode.addEventListener('mousedown', listEventHandler);\n        return _this2;\n      }\n      _createClass(List, [{\n        key: 'format',\n        value: function format(name, value) {\n          if (this.children.length > 0) {\n            this.children.tail.format(name, value);\n          }\n        }\n      }, {\n        key: 'formats',\n        value: function formats() {\n          // We don't inherit from FormatBlot\n          return _defineProperty({}, this.statics.blotName, this.statics.formats(this.domNode));\n        }\n      }, {\n        key: 'insertBefore',\n        value: function insertBefore(blot, ref) {\n          if (blot instanceof ListItem) {\n            _get(List.prototype.__proto__ || Object.getPrototypeOf(List.prototype), 'insertBefore', this).call(this, blot, ref);\n          } else {\n            var index = ref == null ? this.length() : ref.offset(this);\n            var after = this.split(index);\n            after.parent.insertBefore(blot, after);\n          }\n        }\n      }, {\n        key: 'optimize',\n        value: function optimize(context) {\n          _get(List.prototype.__proto__ || Object.getPrototypeOf(List.prototype), 'optimize', this).call(this, context);\n          var next = this.next;\n          if (next != null && next.prev === this && next.statics.blotName === this.statics.blotName && next.domNode.tagName === this.domNode.tagName && next.domNode.getAttribute('data-checked') === this.domNode.getAttribute('data-checked')) {\n            next.moveChildren(this);\n            next.remove();\n          }\n        }\n      }, {\n        key: 'replace',\n        value: function replace(target) {\n          if (target.statics.blotName !== this.statics.blotName) {\n            var item = _parchment2.default.create(this.statics.defaultChild);\n            target.moveChildren(item);\n            this.appendChild(item);\n          }\n          _get(List.prototype.__proto__ || Object.getPrototypeOf(List.prototype), 'replace', this).call(this, target);\n        }\n      }]);\n      return List;\n    }(_container2.default);\n    List.blotName = 'list';\n    List.scope = _parchment2.default.Scope.BLOCK_BLOT;\n    List.tagName = ['OL', 'UL'];\n    List.defaultChild = 'list-item';\n    List.allowedChildren = [ListItem];\n    exports.ListItem = ListItem;\n    exports.default = List;\n\n    /***/\n  }), ( /* 68 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _bold = __webpack_require__(56);\n    var _bold2 = _interopRequireDefault(_bold);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Italic = function (_Bold) {\n      _inherits(Italic, _Bold);\n      function Italic() {\n        _classCallCheck(this, Italic);\n        return _possibleConstructorReturn(this, (Italic.__proto__ || Object.getPrototypeOf(Italic)).apply(this, arguments));\n      }\n      return Italic;\n    }(_bold2.default);\n    Italic.blotName = 'italic';\n    Italic.tagName = ['EM', 'I'];\n    exports.default = Italic;\n\n    /***/\n  }), ( /* 69 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Script = function (_Inline) {\n      _inherits(Script, _Inline);\n      function Script() {\n        _classCallCheck(this, Script);\n        return _possibleConstructorReturn(this, (Script.__proto__ || Object.getPrototypeOf(Script)).apply(this, arguments));\n      }\n      _createClass(Script, null, [{\n        key: 'create',\n        value: function create(value) {\n          if (value === 'super') {\n            return document.createElement('sup');\n          } else if (value === 'sub') {\n            return document.createElement('sub');\n          } else {\n            return _get(Script.__proto__ || Object.getPrototypeOf(Script), 'create', this).call(this, value);\n          }\n        }\n      }, {\n        key: 'formats',\n        value: function formats(domNode) {\n          if (domNode.tagName === 'SUB') return 'sub';\n          if (domNode.tagName === 'SUP') return 'super';\n          return undefined;\n        }\n      }]);\n      return Script;\n    }(_inline2.default);\n    Script.blotName = 'script';\n    Script.tagName = ['SUB', 'SUP'];\n    exports.default = Script;\n\n    /***/\n  }), ( /* 70 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Strike = function (_Inline) {\n      _inherits(Strike, _Inline);\n      function Strike() {\n        _classCallCheck(this, Strike);\n        return _possibleConstructorReturn(this, (Strike.__proto__ || Object.getPrototypeOf(Strike)).apply(this, arguments));\n      }\n      return Strike;\n    }(_inline2.default);\n    Strike.blotName = 'strike';\n    Strike.tagName = 'S';\n    exports.default = Strike;\n\n    /***/\n  }), ( /* 71 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _inline = __webpack_require__(6);\n    var _inline2 = _interopRequireDefault(_inline);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var Underline = function (_Inline) {\n      _inherits(Underline, _Inline);\n      function Underline() {\n        _classCallCheck(this, Underline);\n        return _possibleConstructorReturn(this, (Underline.__proto__ || Object.getPrototypeOf(Underline)).apply(this, arguments));\n      }\n      return Underline;\n    }(_inline2.default);\n    Underline.blotName = 'underline';\n    Underline.tagName = 'U';\n    exports.default = Underline;\n\n    /***/\n  }), ( /* 72 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _link = __webpack_require__(27);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ATTRIBUTES = ['alt', 'height', 'width'];\n    var Image = function (_Parchment$Embed) {\n      _inherits(Image, _Parchment$Embed);\n      function Image() {\n        _classCallCheck(this, Image);\n        return _possibleConstructorReturn(this, (Image.__proto__ || Object.getPrototypeOf(Image)).apply(this, arguments));\n      }\n      _createClass(Image, [{\n        key: 'format',\n        value: function format(name, value) {\n          if (ATTRIBUTES.indexOf(name) > -1) {\n            if (value) {\n              this.domNode.setAttribute(name, value);\n            } else {\n              this.domNode.removeAttribute(name);\n            }\n          } else {\n            _get(Image.prototype.__proto__ || Object.getPrototypeOf(Image.prototype), 'format', this).call(this, name, value);\n          }\n        }\n      }], [{\n        key: 'create',\n        value: function create(value) {\n          var node = _get(Image.__proto__ || Object.getPrototypeOf(Image), 'create', this).call(this, value);\n          if (typeof value === 'string') {\n            node.setAttribute('src', this.sanitize(value));\n          }\n          return node;\n        }\n      }, {\n        key: 'formats',\n        value: function formats(domNode) {\n          return ATTRIBUTES.reduce(function (formats, attribute) {\n            if (domNode.hasAttribute(attribute)) {\n              formats[attribute] = domNode.getAttribute(attribute);\n            }\n            return formats;\n          }, {});\n        }\n      }, {\n        key: 'match',\n        value: function match(url) {\n          return /\\.(jpe?g|gif|png)$/.test(url) || /^data:image\\/.+;base64/.test(url);\n        }\n      }, {\n        key: 'sanitize',\n        value: function sanitize(url) {\n          return (0, _link.sanitize)(url, ['http', 'https', 'data']) ? url : '//:0';\n        }\n      }, {\n        key: 'value',\n        value: function value(domNode) {\n          return domNode.getAttribute('src');\n        }\n      }]);\n      return Image;\n    }(_parchment2.default.Embed);\n    Image.blotName = 'image';\n    Image.tagName = 'IMG';\n    exports.default = Image;\n\n    /***/\n  }), ( /* 73 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _block = __webpack_require__(4);\n    var _link = __webpack_require__(27);\n    var _link2 = _interopRequireDefault(_link);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var ATTRIBUTES = ['height', 'width'];\n    var Video = function (_BlockEmbed) {\n      _inherits(Video, _BlockEmbed);\n      function Video() {\n        _classCallCheck(this, Video);\n        return _possibleConstructorReturn(this, (Video.__proto__ || Object.getPrototypeOf(Video)).apply(this, arguments));\n      }\n      _createClass(Video, [{\n        key: 'format',\n        value: function format(name, value) {\n          if (ATTRIBUTES.indexOf(name) > -1) {\n            if (value) {\n              this.domNode.setAttribute(name, value);\n            } else {\n              this.domNode.removeAttribute(name);\n            }\n          } else {\n            _get(Video.prototype.__proto__ || Object.getPrototypeOf(Video.prototype), 'format', this).call(this, name, value);\n          }\n        }\n      }], [{\n        key: 'create',\n        value: function create(value) {\n          var node = _get(Video.__proto__ || Object.getPrototypeOf(Video), 'create', this).call(this, value);\n          node.setAttribute('frameborder', '0');\n          node.setAttribute('allowfullscreen', true);\n          node.setAttribute('src', this.sanitize(value));\n          return node;\n        }\n      }, {\n        key: 'formats',\n        value: function formats(domNode) {\n          return ATTRIBUTES.reduce(function (formats, attribute) {\n            if (domNode.hasAttribute(attribute)) {\n              formats[attribute] = domNode.getAttribute(attribute);\n            }\n            return formats;\n          }, {});\n        }\n      }, {\n        key: 'sanitize',\n        value: function sanitize(url) {\n          return _link2.default.sanitize(url);\n        }\n      }, {\n        key: 'value',\n        value: function value(domNode) {\n          return domNode.getAttribute('src');\n        }\n      }]);\n      return Video;\n    }(_block.BlockEmbed);\n    Video.blotName = 'video';\n    Video.className = 'ql-video';\n    Video.tagName = 'IFRAME';\n    exports.default = Video;\n\n    /***/\n  }), ( /* 74 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.FormulaBlot = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _embed = __webpack_require__(35);\n    var _embed2 = _interopRequireDefault(_embed);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var FormulaBlot = function (_Embed) {\n      _inherits(FormulaBlot, _Embed);\n      function FormulaBlot() {\n        _classCallCheck(this, FormulaBlot);\n        return _possibleConstructorReturn(this, (FormulaBlot.__proto__ || Object.getPrototypeOf(FormulaBlot)).apply(this, arguments));\n      }\n      _createClass(FormulaBlot, null, [{\n        key: 'create',\n        value: function create(value) {\n          var node = _get(FormulaBlot.__proto__ || Object.getPrototypeOf(FormulaBlot), 'create', this).call(this, value);\n          if (typeof value === 'string') {\n            window.katex.render(value, node, {\n              throwOnError: false,\n              errorColor: '#f00'\n            });\n            node.setAttribute('data-value', value);\n          }\n          return node;\n        }\n      }, {\n        key: 'value',\n        value: function value(domNode) {\n          return domNode.getAttribute('data-value');\n        }\n      }]);\n      return FormulaBlot;\n    }(_embed2.default);\n    FormulaBlot.blotName = 'formula';\n    FormulaBlot.className = 'ql-formula';\n    FormulaBlot.tagName = 'SPAN';\n    var Formula = function (_Module) {\n      _inherits(Formula, _Module);\n      _createClass(Formula, null, [{\n        key: 'register',\n        value: function register() {\n          _quill2.default.register(FormulaBlot, true);\n        }\n      }]);\n      function Formula() {\n        _classCallCheck(this, Formula);\n        var _this2 = _possibleConstructorReturn(this, (Formula.__proto__ || Object.getPrototypeOf(Formula)).call(this));\n        if (window.katex == null) {\n          throw new Error('Formula module requires KaTeX.');\n        }\n        return _this2;\n      }\n      return Formula;\n    }(_module2.default);\n    exports.FormulaBlot = FormulaBlot;\n    exports.default = Formula;\n\n    /***/\n  }), ( /* 75 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.CodeToken = exports.CodeBlock = undefined;\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _parchment = __webpack_require__(0);\n    var _parchment2 = _interopRequireDefault(_parchment);\n    var _quill = __webpack_require__(5);\n    var _quill2 = _interopRequireDefault(_quill);\n    var _module = __webpack_require__(9);\n    var _module2 = _interopRequireDefault(_module);\n    var _code = __webpack_require__(13);\n    var _code2 = _interopRequireDefault(_code);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var SyntaxCodeBlock = function (_CodeBlock) {\n      _inherits(SyntaxCodeBlock, _CodeBlock);\n      function SyntaxCodeBlock() {\n        _classCallCheck(this, SyntaxCodeBlock);\n        return _possibleConstructorReturn(this, (SyntaxCodeBlock.__proto__ || Object.getPrototypeOf(SyntaxCodeBlock)).apply(this, arguments));\n      }\n      _createClass(SyntaxCodeBlock, [{\n        key: 'replaceWith',\n        value: function replaceWith(block) {\n          this.domNode.textContent = this.domNode.textContent;\n          this.attach();\n          _get(SyntaxCodeBlock.prototype.__proto__ || Object.getPrototypeOf(SyntaxCodeBlock.prototype), 'replaceWith', this).call(this, block);\n        }\n      }, {\n        key: 'highlight',\n        value: function highlight(_highlight) {\n          var text = this.domNode.textContent;\n          if (this.cachedText !== text) {\n            if (text.trim().length > 0 || this.cachedText == null) {\n              this.domNode.innerHTML = _highlight(text);\n              this.domNode.normalize();\n              this.attach();\n            }\n            this.cachedText = text;\n          }\n        }\n      }]);\n      return SyntaxCodeBlock;\n    }(_code2.default);\n    SyntaxCodeBlock.className = 'ql-syntax';\n    var CodeToken = new _parchment2.default.Attributor.Class('token', 'hljs', {\n      scope: _parchment2.default.Scope.INLINE\n    });\n    var Syntax = function (_Module) {\n      _inherits(Syntax, _Module);\n      _createClass(Syntax, null, [{\n        key: 'register',\n        value: function register() {\n          _quill2.default.register(CodeToken, true);\n          _quill2.default.register(SyntaxCodeBlock, true);\n        }\n      }]);\n      function Syntax(quill, options) {\n        _classCallCheck(this, Syntax);\n        var _this2 = _possibleConstructorReturn(this, (Syntax.__proto__ || Object.getPrototypeOf(Syntax)).call(this, quill, options));\n        if (typeof _this2.options.highlight !== 'function') {\n          throw new Error('Syntax module requires highlight.js. Please include the library on the page before Quill.');\n        }\n        var timer = null;\n        _this2.quill.on(_quill2.default.events.SCROLL_OPTIMIZE, function () {\n          clearTimeout(timer);\n          timer = setTimeout(function () {\n            _this2.highlight();\n            timer = null;\n          }, _this2.options.interval);\n        });\n        _this2.highlight();\n        return _this2;\n      }\n      _createClass(Syntax, [{\n        key: 'highlight',\n        value: function highlight() {\n          var _this3 = this;\n          if (this.quill.selection.composing) return;\n          this.quill.update(_quill2.default.sources.USER);\n          var range = this.quill.getSelection();\n          this.quill.scroll.descendants(SyntaxCodeBlock).forEach(function (code) {\n            code.highlight(_this3.options.highlight);\n          });\n          this.quill.update(_quill2.default.sources.SILENT);\n          if (range != null) {\n            this.quill.setSelection(range, _quill2.default.sources.SILENT);\n          }\n        }\n      }]);\n      return Syntax;\n    }(_module2.default);\n    Syntax.DEFAULTS = {\n      highlight: function () {\n        if (window.hljs == null) return null;\n        return function (text) {\n          var result = window.hljs.highlightAuto(text);\n          return result.value;\n        };\n      }(),\n      interval: 1000\n    };\n    exports.CodeBlock = SyntaxCodeBlock;\n    exports.CodeToken = CodeToken;\n    exports.default = Syntax;\n\n    /***/\n  }), ( /* 76 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>\";\n\n    /***/\n  }), ( /* 77 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>\";\n\n    /***/\n  }), ( /* 78 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>\";\n\n    /***/\n  }), ( /* 79 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>\";\n\n    /***/\n  }), ( /* 80 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <g class=\\\"ql-fill ql-color-label\\\"> <polygon points=\\\"6 6.868 6 6 5 6 5 7 5.942 7 6 6.868\\\"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points=\\\"6.817 5 6 5 6 6 6.38 6 6.817 5\\\"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points=\\\"4 11.439 4 11 3 11 3 12 3.755 12 4 11.439\\\"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points=\\\"4.63 10 4 10 4 11 4.192 11 4.63 10\\\"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points=\\\"13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174\\\"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points=\\\"12 6.868 12 6 11.62 6 12 6.868\\\"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points=\\\"12.933 9 13 9 13 8 12.495 8 12.933 9\\\"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points=\\\"5.5 13 9 5 12.5 13\\\"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>\";\n\n    /***/\n  }), ( /* 81 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <rect class=\\\"ql-fill ql-stroke\\\" height=3 width=3 x=4 y=5></rect> <rect class=\\\"ql-fill ql-stroke\\\" height=3 width=3 x=11 y=5></rect> <path class=\\\"ql-even ql-fill ql-stroke\\\" d=M7,8c0,4.031-3,5-3,5></path> <path class=\\\"ql-even ql-fill ql-stroke\\\" d=M14,8c0,4.031-3,5-3,5></path> </svg>\";\n\n    /***/\n  }), ( /* 82 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>\";\n\n    /***/\n  }), ( /* 83 */\n  /***/function (module, exports) {\n    module.exports = \"<svg class=\\\"\\\" viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>\";\n\n    /***/\n  }), ( /* 84 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=\\\"ql-color-label ql-stroke ql-transparent\\\" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points=\\\"5.5 11 9 3 12.5 11\\\"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>\";\n\n    /***/\n  }), ( /* 85 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <polygon class=\\\"ql-stroke ql-fill\\\" points=\\\"3 11 5 9 3 7 3 11\\\"></polygon> <line class=\\\"ql-stroke ql-fill\\\" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>\";\n\n    /***/\n  }), ( /* 86 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <polygon class=\\\"ql-stroke ql-fill\\\" points=\\\"15 12 13 10 15 8 15 12\\\"></polygon> <line class=\\\"ql-stroke ql-fill\\\" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>\";\n\n    /***/\n  }), ( /* 87 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>\";\n\n    /***/\n  }), ( /* 88 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>\";\n\n    /***/\n  }), ( /* 89 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>\";\n\n    /***/\n  }), ( /* 90 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform=\\\"translate(24 18) rotate(-180)\\\"/> </svg>\";\n\n    /***/\n  }), ( /* 91 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>\";\n\n    /***/\n  }), ( /* 92 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewBox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>\";\n\n    /***/\n  }), ( /* 93 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewBox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>\";\n\n    /***/\n  }), ( /* 94 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>\";\n\n    /***/\n  }), ( /* 95 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class=\\\"ql-even ql-fill\\\" points=\\\"5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12\\\"></polyline> </svg>\";\n\n    /***/\n  }), ( /* 96 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=\\\"ql-fill ql-stroke\\\" points=\\\"3 7 3 11 5 9 3 7\\\"></polyline> </svg>\";\n\n    /***/\n  }), ( /* 97 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points=\\\"5 7 5 11 3 9 5 7\\\"></polyline> </svg>\";\n\n    /***/\n  }), ( /* 98 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class=\\\"ql-even ql-stroke\\\" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class=\\\"ql-even ql-stroke\\\" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>\";\n\n    /***/\n  }), ( /* 99 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class=\\\"ql-stroke ql-thin\\\" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class=\\\"ql-stroke ql-thin\\\" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class=\\\"ql-stroke ql-thin\\\" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>\";\n\n    /***/\n  }), ( /* 100 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>\";\n\n    /***/\n  }), ( /* 101 */\n  /***/function (module, exports) {\n    module.exports = \"<svg class=\\\"\\\" viewbox=\\\"0 0 18 18\\\"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points=\\\"3 4 4 5 6 3\\\"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points=\\\"3 14 4 15 6 13\\\"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points=\\\"3 9 4 10 6 8\\\"></polyline> </svg>\";\n\n    /***/\n  }), ( /* 102 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>\";\n\n    /***/\n  }), ( /* 103 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>\";\n\n    /***/\n  }), ( /* 104 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <line class=\\\"ql-stroke ql-thin\\\" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>\";\n\n    /***/\n  }), ( /* 105 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>\";\n\n    /***/\n  }), ( /* 106 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>\";\n\n    /***/\n  }), ( /* 107 */\n  /***/function (module, exports) {\n    module.exports = \"<svg viewbox=\\\"0 0 18 18\\\"> <polygon class=ql-stroke points=\\\"7 11 9 13 11 11 7 11\\\"></polygon> <polygon class=ql-stroke points=\\\"7 7 9 5 11 7 7 7\\\"></polygon> </svg>\";\n\n    /***/\n  }), ( /* 108 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    Object.defineProperty(exports, \"__esModule\", {\n      value: true\n    });\n    exports.default = exports.BubbleTooltip = undefined;\n    var _get = function get(object, property, receiver) {\n      if (object === null) object = Function.prototype;\n      var desc = Object.getOwnPropertyDescriptor(object, property);\n      if (desc === undefined) {\n        var parent = Object.getPrototypeOf(object);\n        if (parent === null) {\n          return undefined;\n        } else {\n          return get(parent, property, receiver);\n        }\n      } else if (\"value\" in desc) {\n        return desc.value;\n      } else {\n        var getter = desc.get;\n        if (getter === undefined) {\n          return undefined;\n        }\n        return getter.call(receiver);\n      }\n    };\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    var _extend = __webpack_require__(3);\n    var _extend2 = _interopRequireDefault(_extend);\n    var _emitter = __webpack_require__(8);\n    var _emitter2 = _interopRequireDefault(_emitter);\n    var _base = __webpack_require__(43);\n    var _base2 = _interopRequireDefault(_base);\n    var _selection = __webpack_require__(15);\n    var _icons = __webpack_require__(41);\n    var _icons2 = _interopRequireDefault(_icons);\n    function _interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : {\n        default: obj\n      };\n    }\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    function _possibleConstructorReturn(self, call) {\n      if (!self) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n      }\n      return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n    function _inherits(subClass, superClass) {\n      if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n      }\n      subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n          value: subClass,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n    var TOOLBAR_CONFIG = [['bold', 'italic', 'link'], [{\n      header: 1\n    }, {\n      header: 2\n    }, 'blockquote']];\n    var BubbleTheme = function (_BaseTheme) {\n      _inherits(BubbleTheme, _BaseTheme);\n      function BubbleTheme(quill, options) {\n        _classCallCheck(this, BubbleTheme);\n        if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n          options.modules.toolbar.container = TOOLBAR_CONFIG;\n        }\n        var _this = _possibleConstructorReturn(this, (BubbleTheme.__proto__ || Object.getPrototypeOf(BubbleTheme)).call(this, quill, options));\n        _this.quill.container.classList.add('ql-bubble');\n        return _this;\n      }\n      _createClass(BubbleTheme, [{\n        key: 'extendToolbar',\n        value: function extendToolbar(toolbar) {\n          this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);\n          this.tooltip.root.appendChild(toolbar.container);\n          this.buildButtons([].slice.call(toolbar.container.querySelectorAll('button')), _icons2.default);\n          this.buildPickers([].slice.call(toolbar.container.querySelectorAll('select')), _icons2.default);\n        }\n      }]);\n      return BubbleTheme;\n    }(_base2.default);\n    BubbleTheme.DEFAULTS = (0, _extend2.default)(true, {}, _base2.default.DEFAULTS, {\n      modules: {\n        toolbar: {\n          handlers: {\n            link: function link(value) {\n              if (!value) {\n                this.quill.format('link', false);\n              } else {\n                this.quill.theme.tooltip.edit();\n              }\n            }\n          }\n        }\n      }\n    });\n    var BubbleTooltip = function (_BaseTooltip) {\n      _inherits(BubbleTooltip, _BaseTooltip);\n      function BubbleTooltip(quill, bounds) {\n        _classCallCheck(this, BubbleTooltip);\n        var _this2 = _possibleConstructorReturn(this, (BubbleTooltip.__proto__ || Object.getPrototypeOf(BubbleTooltip)).call(this, quill, bounds));\n        _this2.quill.on(_emitter2.default.events.EDITOR_CHANGE, function (type, range, oldRange, source) {\n          if (type !== _emitter2.default.events.SELECTION_CHANGE) return;\n          if (range != null && range.length > 0 && source === _emitter2.default.sources.USER) {\n            _this2.show();\n            // Lock our width so we will expand beyond our offsetParent boundaries\n            _this2.root.style.left = '0px';\n            _this2.root.style.width = '';\n            _this2.root.style.width = _this2.root.offsetWidth + 'px';\n            var lines = _this2.quill.getLines(range.index, range.length);\n            if (lines.length === 1) {\n              _this2.position(_this2.quill.getBounds(range));\n            } else {\n              var lastLine = lines[lines.length - 1];\n              var index = _this2.quill.getIndex(lastLine);\n              var length = Math.min(lastLine.length() - 1, range.index + range.length - index);\n              var _bounds = _this2.quill.getBounds(new _selection.Range(index, length));\n              _this2.position(_bounds);\n            }\n          } else if (document.activeElement !== _this2.textbox && _this2.quill.hasFocus()) {\n            _this2.hide();\n          }\n        });\n        return _this2;\n      }\n      _createClass(BubbleTooltip, [{\n        key: 'listen',\n        value: function listen() {\n          var _this3 = this;\n          _get(BubbleTooltip.prototype.__proto__ || Object.getPrototypeOf(BubbleTooltip.prototype), 'listen', this).call(this);\n          this.root.querySelector('.ql-close').addEventListener('click', function () {\n            _this3.root.classList.remove('ql-editing');\n          });\n          this.quill.on(_emitter2.default.events.SCROLL_OPTIMIZE, function () {\n            // Let selection be restored by toolbar handlers before repositioning\n            setTimeout(function () {\n              if (_this3.root.classList.contains('ql-hidden')) return;\n              var range = _this3.quill.getSelection();\n              if (range != null) {\n                _this3.position(_this3.quill.getBounds(range));\n              }\n            }, 1);\n          });\n        }\n      }, {\n        key: 'cancel',\n        value: function cancel() {\n          this.show();\n        }\n      }, {\n        key: 'position',\n        value: function position(reference) {\n          var shift = _get(BubbleTooltip.prototype.__proto__ || Object.getPrototypeOf(BubbleTooltip.prototype), 'position', this).call(this, reference);\n          var arrow = this.root.querySelector('.ql-tooltip-arrow');\n          arrow.style.marginLeft = '';\n          if (shift === 0) return shift;\n          arrow.style.marginLeft = -1 * shift - arrow.offsetWidth / 2 + 'px';\n        }\n      }]);\n      return BubbleTooltip;\n    }(_base.BaseTooltip);\n    BubbleTooltip.TEMPLATE = ['<span class=\"ql-tooltip-arrow\"></span>', '<div class=\"ql-tooltip-editor\">', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-close\"></a>', '</div>'].join('');\n    exports.BubbleTooltip = BubbleTooltip;\n    exports.default = BubbleTheme;\n\n    /***/\n  }), ( /* 109 */\n  /***/function (module, exports, __webpack_require__) {\n    module.exports = __webpack_require__(63);\n\n    /***/\n  }\n  /******/)])[\"default\"];\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}