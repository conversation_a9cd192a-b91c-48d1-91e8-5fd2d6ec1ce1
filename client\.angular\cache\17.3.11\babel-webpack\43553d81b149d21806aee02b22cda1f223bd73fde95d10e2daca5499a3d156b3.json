{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Swedish [sv]\n//! author : <PERSON><PERSON> : https://github.com/ulmus\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var sv = moment.defineLocale('sv', {\n    months: 'januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n    weekdays: 'söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag'.split('_'),\n    weekdaysShort: 'sön_mån_tis_ons_tor_fre_lör'.split('_'),\n    weekdaysMin: 'sö_må_ti_on_to_fr_lö'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [kl.] HH:mm',\n      LLLL: 'dddd D MMMM YYYY [kl.] HH:mm',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Idag] LT',\n      nextDay: '[Imorgon] LT',\n      lastDay: '[Igår] LT',\n      nextWeek: '[På] dddd LT',\n      lastWeek: '[I] dddd[s] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: 'för %s sedan',\n      s: 'några sekunder',\n      ss: '%d sekunder',\n      m: 'en minut',\n      mm: '%d minuter',\n      h: 'en timme',\n      hh: '%d timmar',\n      d: 'en dag',\n      dd: '%d dagar',\n      M: 'en månad',\n      MM: '%d månader',\n      y: 'ett år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(\\:e|\\:a)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? ':e' : b === 1 ? ':a' : b === 2 ? ':a' : b === 3 ? ':e' : ':e';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sv;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "sv", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "lll", "llll", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "b", "output", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/sv.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Swedish [sv]\n//! author : <PERSON><PERSON> : https://github.com/ulmus\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var sv = moment.defineLocale('sv', {\n        months: 'januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n        weekdays: 'söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag'.split('_'),\n        weekdaysShort: 'sön_mån_tis_ons_tor_fre_lör'.split('_'),\n        weekdaysMin: 'sö_må_ti_on_to_fr_lö'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY [kl.] HH:mm',\n            LLLL: 'dddd D MMMM YYYY [kl.] HH:mm',\n            lll: 'D MMM YYYY HH:mm',\n            llll: 'ddd D MMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Idag] LT',\n            nextDay: '[Imorgon] LT',\n            lastDay: '[Igår] LT',\n            nextWeek: '[På] dddd LT',\n            lastWeek: '[I] dddd[s] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'om %s',\n            past: 'för %s sedan',\n            s: 'några sekunder',\n            ss: '%d sekunder',\n            m: 'en minut',\n            mm: '%d minuter',\n            h: 'en timme',\n            hh: '%d timmar',\n            d: 'en dag',\n            dd: '%d dagar',\n            M: 'en månad',\n            MM: '%d månader',\n            y: 'ett år',\n            yy: '%d år',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(\\:e|\\:a)/,\n        ordinal: function (number) {\n            var b = number % 10,\n                output =\n                    ~~((number % 100) / 10) === 1\n                        ? ':e'\n                        : b === 1\n                          ? ':a'\n                          : b === 2\n                            ? ':a'\n                            : b === 3\n                              ? ':e'\n                              : ':e';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return sv;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,uFAAuF,CAACC,KAAK,CACjG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,mDAAmD,CAACF,KAAK,CAAC,GAAG,CAAC;IACxEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE,8BAA8B;MACpCC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,cAAc;MACxBC,QAAQ,EAAE,gBAAgB;MAC1BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,cAAc;MACpBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,kBAAkB;IAC1CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,MAAM,GACF,CAAC,EAAGF,MAAM,GAAG,GAAG,GAAI,EAAE,CAAC,KAAK,CAAC,GACvB,IAAI,GACJC,CAAC,KAAK,CAAC,GACL,IAAI,GACJA,CAAC,KAAK,CAAC,GACL,IAAI,GACJA,CAAC,KAAK,CAAC,GACL,IAAI,GACJ,IAAI;MACxB,OAAOD,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}