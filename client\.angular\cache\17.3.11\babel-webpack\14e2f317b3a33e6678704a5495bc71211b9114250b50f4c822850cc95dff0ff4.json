{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':ticket-id',\n  component: ServiceTicketsComponent\n  // children: [\n  //   {\n  //     path: ':id'\n  //   }\n  // ]\n}];\nexport class ServiceTicketsRoutingModule {\n  static {\n    this.ɵfac = function ServiceTicketsRoutingModule_Factory(t) {\n      return new (t || ServiceTicketsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceTicketsComponent", "routes", "path", "component", "ServiceTicketsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: ':ticket-id',\r\n    component: ServiceTicketsComponent,\r\n    // children: [\r\n    //   {\r\n    //     path: ':id'\r\n    //   }\r\n    // ]\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceTicketsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;;;AAErE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEH;EACX;EACA;EACA;EACA;EACA;CACD,CACF;AAMD,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF5BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}