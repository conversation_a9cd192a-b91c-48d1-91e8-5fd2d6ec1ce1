{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SalesOrdersComponent } from './sales-orders.component';\nimport { SalesOrdersDetailsComponent } from './sales-orders-details/sales-orders-details.component';\nimport { SalesOrdersOverviewComponent } from './sales-orders-details/sales-orders-overview/sales-orders-overview.component';\nimport { SalesOrdersContactsComponent } from './sales-orders-details/sales-orders-contacts/sales-orders-contacts.component';\nimport { SalesOrdersSalesTeamComponent } from './sales-orders-details/sales-orders-sales-team/sales-orders-sales-team.component';\nimport { SalesOrdersAiInsightsComponent } from './sales-orders-details/sales-orders-ai-insights/sales-orders-ai-insights.component';\nimport { SalesOrdersOrganizationDataComponent } from './sales-orders-details/sales-orders-organization-data/sales-orders-organization-data.component';\nimport { SalesOrdersAttachmentsComponent } from './sales-orders-details/sales-orders-attachments/sales-orders-attachments.component';\nimport { SalesOrdersNotesComponent } from './sales-orders-details/sales-orders-notes/sales-orders-notes.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SalesOrdersComponent\n}, {\n  path: ':id',\n  component: SalesOrdersDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: SalesOrdersOverviewComponent\n  }, {\n    path: 'contacts',\n    component: SalesOrdersContactsComponent\n  }, {\n    path: 'sales-team',\n    component: SalesOrdersSalesTeamComponent\n  }, {\n    path: 'ai-insight',\n    component: SalesOrdersAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: SalesOrdersOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: SalesOrdersAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: SalesOrdersNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let SalesOrdersRoutingModule = /*#__PURE__*/(() => {\n  class SalesOrdersRoutingModule {\n    static {\n      this.ɵfac = function SalesOrdersRoutingModule_Factory(t) {\n        return new (t || SalesOrdersRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SalesOrdersRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return SalesOrdersRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}