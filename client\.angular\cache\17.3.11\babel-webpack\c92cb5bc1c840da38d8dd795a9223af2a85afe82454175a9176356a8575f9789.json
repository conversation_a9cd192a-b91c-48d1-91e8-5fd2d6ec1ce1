{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SalesQuotesService {\n  constructor(http) {\n    this.http = http;\n  }\n  fetchSalesquoteOrders(params) {\n    return this.http.get(ApiConstant.SALES_QUOTE_GENERIC, {\n      params\n      // headers,\n    });\n  }\n  getPartnerFunction(custId) {\n    console.log(custId, 'CustId-->');\n    return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`).pipe(map(res => res.data));\n  }\n  fetchOrderStatuses(headers) {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n      params: headers\n    });\n  }\n  getImages(productId) {\n    console.log(this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), 'image res');\n    return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\n  }\n  getQuoteDetails(data) {\n    const params = new HttpParams().append('DOC_TYPE', data.DOC_TYPE);\n    return this.http.get(`${ApiConstant.SALES_QUOTE}/${data.SD_DOC}`, {\n      params\n    });\n  }\n  getAllStatuses() {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=QUOTE_STATUS');\n  }\n  static {\n    this.ɵfac = function SalesQuotesService_Factory(t) {\n      return new (t || SalesQuotesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SalesQuotesService,\n      factory: SalesQuotesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "ApiConstant", "CMS_APIContstant", "map", "SalesQuotesService", "constructor", "http", "fetchSalesquoteOrders", "params", "get", "SALES_QUOTE_GENERIC", "getPartnerFunction", "custId", "console", "log", "CUSTOMER_PARTNER_FUNCTION", "pipe", "res", "data", "fetchOrderStatuses", "headers", "CONFIG_DATA", "getImages", "productId", "PRODUCT_MDEIA", "getQuoteDetails", "append", "DOC_TYPE", "SALES_QUOTE", "SD_DOC", "getAllStatuses", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { map } from 'rxjs';\r\n\r\ninterface SalesQuoteData {\r\n  SD_DOC?: string;\r\n  DOC_NAME?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_DATE?: string;\r\n  DOC_STATUS?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SalesQuotesService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  fetchSalesquoteOrders(\r\n    params: any\r\n  ): Observable<{ SALESQUOTES: SalesQuoteData[] }> {\r\n    return this.http.get<{ SALESQUOTES: SalesQuoteData[] }>(\r\n      ApiConstant.SALES_QUOTE_GENERIC,\r\n      {\r\n        params,\r\n        // headers,\r\n      }\r\n    );\r\n  }\r\n  getPartnerFunction(custId: string) {\r\n    console.log(custId, 'CustId-->');\r\n    return this.http\r\n      .get<any>(\r\n        `${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`\r\n      )\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  fetchOrderStatuses(headers: any): Observable<string[]> {\r\n    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA, {\r\n      params: headers,\r\n    });\r\n  }\r\n\r\n  getImages(productId: string) {\r\n    console.log(\r\n      this.http.get<any>(\r\n        `${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`\r\n      ),\r\n      'image res'\r\n    );\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`\r\n    );\r\n  }\r\n\r\n  getQuoteDetails(data: any) {\r\n    const params = new HttpParams().append('DOC_TYPE', data.DOC_TYPE);\r\n    return this.http.get<any>(`${ApiConstant.SALES_QUOTE}/${data.SD_DOC}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getAllStatuses() {\r\n    return this.http.get<any>(\r\n      CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=QUOTE_STATUS'\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAkCA,UAAU,QAAQ,sBAAsB;AAG1E,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SAASC,GAAG,QAAQ,MAAM;;;AAa1B,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,qBAAqBA,CACnBC,MAAW;IAEX,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAClBR,WAAW,CAACS,mBAAmB,EAC/B;MACEF;MACA;KACD,CACF;EACH;EACAG,kBAAkBA,CAACC,MAAc;IAC/BC,OAAO,CAACC,GAAG,CAACF,MAAM,EAAE,WAAW,CAAC;IAChC,OAAO,IAAI,CAACN,IAAI,CACbG,GAAG,CACF,GAAGP,gBAAgB,CAACa,yBAAyB,8BAA8BH,MAAM,qBAAqB,CACvG,CACAI,IAAI,CAACb,GAAG,CAAEc,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC;EACjC;EAEAC,kBAAkBA,CAACC,OAAY;IAC7B,OAAO,IAAI,CAACd,IAAI,CAACG,GAAG,CAAMP,gBAAgB,CAACmB,WAAW,EAAE;MACtDb,MAAM,EAAEY;KACT,CAAC;EACJ;EAEAE,SAASA,CAACC,SAAiB;IACzBV,OAAO,CAACC,GAAG,CACT,IAAI,CAACR,IAAI,CAACG,GAAG,CACX,GAAGP,gBAAgB,CAACsB,aAAa,6BAA6BD,SAAS,EAAE,CAC1E,EACD,WAAW,CACZ;IACD,OAAO,IAAI,CAACjB,IAAI,CAACG,GAAG,CAClB,GAAGP,gBAAgB,CAACsB,aAAa,6BAA6BD,SAAS,EAAE,CAC1E;EACH;EAEAE,eAAeA,CAACP,IAAS;IACvB,MAAMV,MAAM,GAAG,IAAIR,UAAU,EAAE,CAAC0B,MAAM,CAAC,UAAU,EAAER,IAAI,CAACS,QAAQ,CAAC;IACjE,OAAO,IAAI,CAACrB,IAAI,CAACG,GAAG,CAAM,GAAGR,WAAW,CAAC2B,WAAW,IAAIV,IAAI,CAACW,MAAM,EAAE,EAAE;MACrErB;KACD,CAAC;EACJ;EAEAsB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACxB,IAAI,CAACG,GAAG,CAClBP,gBAAgB,CAACmB,WAAW,GAAG,kCAAkC,CAClE;EACH;;;uBApDWjB,kBAAkB,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB9B,kBAAkB;MAAA+B,OAAA,EAAlB/B,kBAAkB,CAAAgC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}