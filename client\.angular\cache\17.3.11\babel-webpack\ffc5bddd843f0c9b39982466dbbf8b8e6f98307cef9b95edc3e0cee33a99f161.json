{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ResetPasswordComponent } from './reset-password.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ResetPasswordModule {\n  static {\n    this.ɵfac = function ResetPasswordModule_Factory(t) {\n      return new (t || ResetPasswordModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResetPasswordModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule.forChild([{\n        path: '',\n        component: ResetPasswordComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResetPasswordModule, {\n    declarations: [ResetPasswordComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ResetPasswordComponent", "RouterModule", "FormsModule", "ReactiveFormsModule", "ResetPasswordModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\session\\reset-password\\reset-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ResetPasswordComponent } from './reset-password.component';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ResetPasswordComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    RouterModule.forChild([{ path: '', component: ResetPasswordComponent }]),\r\n  ]\r\n})\r\nexport class ResetPasswordModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;;AAcjE,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAN5BL,YAAY,EACZG,WAAW,EACXC,mBAAmB,EACnBF,YAAY,CAACI,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEP;MAAsB,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAG/DI,mBAAmB;IAAAI,YAAA,GAT5BR,sBAAsB;IAAAS,OAAA,GAGtBV,YAAY,EACZG,WAAW,EACXC,mBAAmB,EAAAO,EAAA,CAAAT,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}