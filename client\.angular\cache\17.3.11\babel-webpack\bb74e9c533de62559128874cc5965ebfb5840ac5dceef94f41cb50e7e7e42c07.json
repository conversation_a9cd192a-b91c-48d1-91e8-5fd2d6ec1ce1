{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n  constructor(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._finalizers = null;\n  }\n  unsubscribe() {\n    let errors;\n    if (!this.closed) {\n      this.closed = true;\n      const {\n        _parentage\n      } = this;\n      if (_parentage) {\n        this._parentage = null;\n        if (Array.isArray(_parentage)) {\n          for (const parent of _parentage) {\n            parent.remove(this);\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n      const {\n        initialTeardown: initialFinalizer\n      } = this;\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n      const {\n        _finalizers\n      } = this;\n      if (_finalizers) {\n        this._finalizers = null;\n        for (const finalizer of _finalizers) {\n          try {\n            execFinalizer(finalizer);\n          } catch (err) {\n            errors = errors !== null && errors !== void 0 ? errors : [];\n            if (err instanceof UnsubscriptionError) {\n              errors = [...errors, ...err.errors];\n            } else {\n              errors.push(err);\n            }\n          }\n        }\n      }\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  }\n  add(teardown) {\n    var _a;\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n          teardown._addParent(this);\n        }\n        (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  }\n  _hasParent(parent) {\n    const {\n      _parentage\n    } = this;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  }\n  _addParent(parent) {\n    const {\n      _parentage\n    } = this;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  }\n  _removeParent(parent) {\n    const {\n      _parentage\n    } = this;\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  }\n  remove(teardown) {\n    const {\n      _finalizers\n    } = this;\n    _finalizers && arrRemove(_finalizers, teardown);\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  }\n}\nSubscription.EMPTY = (() => {\n  const empty = new Subscription();\n  empty.closed = true;\n  return empty;\n})();\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\nfunction execFinalizer(finalizer) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["isFunction", "UnsubscriptionError", "arr<PERSON><PERSON><PERSON>", "Subscription", "constructor", "initialTeardown", "closed", "_parentage", "_finalizers", "unsubscribe", "errors", "Array", "isArray", "parent", "remove", "initialFinalizer", "e", "finalizer", "execFinalizer", "err", "push", "add", "teardown", "_a", "_hasParent", "_addParent", "includes", "_removeParent", "EMPTY", "empty", "EMPTY_SUBSCRIPTION", "isSubscription", "value"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/rxjs/dist/esm/internal/Subscription.js"], "sourcesContent": ["import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n    constructor(initialTeardown) {\n        this.initialTeardown = initialTeardown;\n        this.closed = false;\n        this._parentage = null;\n        this._finalizers = null;\n    }\n    unsubscribe() {\n        let errors;\n        if (!this.closed) {\n            this.closed = true;\n            const { _parentage } = this;\n            if (_parentage) {\n                this._parentage = null;\n                if (Array.isArray(_parentage)) {\n                    for (const parent of _parentage) {\n                        parent.remove(this);\n                    }\n                }\n                else {\n                    _parentage.remove(this);\n                }\n            }\n            const { initialTeardown: initialFinalizer } = this;\n            if (isFunction(initialFinalizer)) {\n                try {\n                    initialFinalizer();\n                }\n                catch (e) {\n                    errors = e instanceof UnsubscriptionError ? e.errors : [e];\n                }\n            }\n            const { _finalizers } = this;\n            if (_finalizers) {\n                this._finalizers = null;\n                for (const finalizer of _finalizers) {\n                    try {\n                        execFinalizer(finalizer);\n                    }\n                    catch (err) {\n                        errors = errors !== null && errors !== void 0 ? errors : [];\n                        if (err instanceof UnsubscriptionError) {\n                            errors = [...errors, ...err.errors];\n                        }\n                        else {\n                            errors.push(err);\n                        }\n                    }\n                }\n            }\n            if (errors) {\n                throw new UnsubscriptionError(errors);\n            }\n        }\n    }\n    add(teardown) {\n        var _a;\n        if (teardown && teardown !== this) {\n            if (this.closed) {\n                execFinalizer(teardown);\n            }\n            else {\n                if (teardown instanceof Subscription) {\n                    if (teardown.closed || teardown._hasParent(this)) {\n                        return;\n                    }\n                    teardown._addParent(this);\n                }\n                (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n            }\n        }\n    }\n    _hasParent(parent) {\n        const { _parentage } = this;\n        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));\n    }\n    _addParent(parent) {\n        const { _parentage } = this;\n        this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n    }\n    _removeParent(parent) {\n        const { _parentage } = this;\n        if (_parentage === parent) {\n            this._parentage = null;\n        }\n        else if (Array.isArray(_parentage)) {\n            arrRemove(_parentage, parent);\n        }\n    }\n    remove(teardown) {\n        const { _finalizers } = this;\n        _finalizers && arrRemove(_finalizers, teardown);\n        if (teardown instanceof Subscription) {\n            teardown._removeParent(this);\n        }\n    }\n}\nSubscription.EMPTY = (() => {\n    const empty = new Subscription();\n    empty.closed = true;\n    return empty;\n})();\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n    return (value instanceof Subscription ||\n        (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe)));\n}\nfunction execFinalizer(finalizer) {\n    if (isFunction(finalizer)) {\n        finalizer();\n    }\n    else {\n        finalizer.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAIC,MAAM;IACV,IAAI,CAAC,IAAI,CAACJ,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,MAAM;QAAEC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAII,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;UAC3B,KAAK,MAAMM,MAAM,IAAIN,UAAU,EAAE;YAC7BM,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;UACvB;QACJ,CAAC,MACI;UACDP,UAAU,CAACO,MAAM,CAAC,IAAI,CAAC;QAC3B;MACJ;MACA,MAAM;QAAET,eAAe,EAAEU;MAAiB,CAAC,GAAG,IAAI;MAClD,IAAIf,UAAU,CAACe,gBAAgB,CAAC,EAAE;QAC9B,IAAI;UACAA,gBAAgB,CAAC,CAAC;QACtB,CAAC,CACD,OAAOC,CAAC,EAAE;UACNN,MAAM,GAAGM,CAAC,YAAYf,mBAAmB,GAAGe,CAAC,CAACN,MAAM,GAAG,CAACM,CAAC,CAAC;QAC9D;MACJ;MACA,MAAM;QAAER;MAAY,CAAC,GAAG,IAAI;MAC5B,IAAIA,WAAW,EAAE;QACb,IAAI,CAACA,WAAW,GAAG,IAAI;QACvB,KAAK,MAAMS,SAAS,IAAIT,WAAW,EAAE;UACjC,IAAI;YACAU,aAAa,CAACD,SAAS,CAAC;UAC5B,CAAC,CACD,OAAOE,GAAG,EAAE;YACRT,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAE;YAC3D,IAAIS,GAAG,YAAYlB,mBAAmB,EAAE;cACpCS,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAE,GAAGS,GAAG,CAACT,MAAM,CAAC;YACvC,CAAC,MACI;cACDA,MAAM,CAACU,IAAI,CAACD,GAAG,CAAC;YACpB;UACJ;QACJ;MACJ;MACA,IAAIT,MAAM,EAAE;QACR,MAAM,IAAIT,mBAAmB,CAACS,MAAM,CAAC;MACzC;IACJ;EACJ;EACAW,GAAGA,CAACC,QAAQ,EAAE;IACV,IAAIC,EAAE;IACN,IAAID,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MAC/B,IAAI,IAAI,CAAChB,MAAM,EAAE;QACbY,aAAa,CAACI,QAAQ,CAAC;MAC3B,CAAC,MACI;QACD,IAAIA,QAAQ,YAAYnB,YAAY,EAAE;UAClC,IAAImB,QAAQ,CAAChB,MAAM,IAAIgB,QAAQ,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;YAC9C;UACJ;UACAF,QAAQ,CAACG,UAAU,CAAC,IAAI,CAAC;QAC7B;QACA,CAAC,IAAI,CAACjB,WAAW,GAAG,CAACe,EAAE,GAAG,IAAI,CAACf,WAAW,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEH,IAAI,CAACE,QAAQ,CAAC;MACnG;IACJ;EACJ;EACAE,UAAUA,CAACX,MAAM,EAAE;IACf,MAAM;MAAEN;IAAW,CAAC,GAAG,IAAI;IAC3B,OAAOA,UAAU,KAAKM,MAAM,IAAKF,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,IAAIA,UAAU,CAACmB,QAAQ,CAACb,MAAM,CAAE;EAC9F;EACAY,UAAUA,CAACZ,MAAM,EAAE;IACf,MAAM;MAAEN;IAAW,CAAC,GAAG,IAAI;IAC3B,IAAI,CAACA,UAAU,GAAGI,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,IAAIA,UAAU,CAACa,IAAI,CAACP,MAAM,CAAC,EAAEN,UAAU,IAAIA,UAAU,GAAG,CAACA,UAAU,EAAEM,MAAM,CAAC,GAAGA,MAAM;EACpI;EACAc,aAAaA,CAACd,MAAM,EAAE;IAClB,MAAM;MAAEN;IAAW,CAAC,GAAG,IAAI;IAC3B,IAAIA,UAAU,KAAKM,MAAM,EAAE;MACvB,IAAI,CAACN,UAAU,GAAG,IAAI;IAC1B,CAAC,MACI,IAAII,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;MAChCL,SAAS,CAACK,UAAU,EAAEM,MAAM,CAAC;IACjC;EACJ;EACAC,MAAMA,CAACQ,QAAQ,EAAE;IACb,MAAM;MAAEd;IAAY,CAAC,GAAG,IAAI;IAC5BA,WAAW,IAAIN,SAAS,CAACM,WAAW,EAAEc,QAAQ,CAAC;IAC/C,IAAIA,QAAQ,YAAYnB,YAAY,EAAE;MAClCmB,QAAQ,CAACK,aAAa,CAAC,IAAI,CAAC;IAChC;EACJ;AACJ;AACAxB,YAAY,CAACyB,KAAK,GAAG,CAAC,MAAM;EACxB,MAAMC,KAAK,GAAG,IAAI1B,YAAY,CAAC,CAAC;EAChC0B,KAAK,CAACvB,MAAM,GAAG,IAAI;EACnB,OAAOuB,KAAK;AAChB,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMC,kBAAkB,GAAG3B,YAAY,CAACyB,KAAK;AACpD,OAAO,SAASG,cAAcA,CAACC,KAAK,EAAE;EAClC,OAAQA,KAAK,YAAY7B,YAAY,IAChC6B,KAAK,IAAI,QAAQ,IAAIA,KAAK,IAAIhC,UAAU,CAACgC,KAAK,CAAClB,MAAM,CAAC,IAAId,UAAU,CAACgC,KAAK,CAACX,GAAG,CAAC,IAAIrB,UAAU,CAACgC,KAAK,CAACvB,WAAW,CAAE;AAC1H;AACA,SAASS,aAAaA,CAACD,SAAS,EAAE;EAC9B,IAAIjB,UAAU,CAACiB,SAAS,CAAC,EAAE;IACvBA,SAAS,CAAC,CAAC;EACf,CAAC,MACI;IACDA,SAAS,CAACR,WAAW,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}