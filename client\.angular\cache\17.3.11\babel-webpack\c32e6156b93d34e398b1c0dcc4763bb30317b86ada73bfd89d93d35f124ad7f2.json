{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../account.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/progressspinner\";\nfunction AccountDetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 38);\n    i0.ɵɵtemplate(1, AccountDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class AccountDetailsComponent {\n  constructor(route, router, formBuilder, accountservice, messageservice, confirmationservice) {\n    this.route = route;\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.accountservice = accountservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.accountDetails = null;\n    this.sidebarDetails = null;\n    this.NoteDetails = null;\n    this.activeItem = {};\n    this.bp_id = '';\n    this.bp_status = '';\n    this.bp_extension_docId = '';\n    this.partner_id = '';\n    this.partner_role = '';\n    this.loading = false;\n    this.activeIndex = 0;\n    this.isSidebarHidden = false;\n    this.submitted = false;\n    this.saving = false;\n    this.Actions = [];\n    this.GlobalNoteForm = this.formBuilder.group({\n      note: ['']\n    });\n  }\n  ngOnInit() {\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      this.partner_id = response?.bp_id;\n      if (this.partner_id) {\n        this.accountservice.getGlobalNote(this.partner_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n          next: noteResponse => {\n            this.NoteDetails = noteResponse?.data[0] || [];\n            this.GlobalNoteForm.patchValue({\n              note: noteResponse?.data[0]?.note\n            });\n          },\n          error: error => {\n            console.error('Error fetching global note:', error);\n          }\n        });\n      }\n    });\n    this.makeMenuItems(this.bp_id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const accountId = params.get('id');\n      if (accountId) {\n        this.loadAccountData(accountId);\n      }\n    });\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(bp_id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/account/${bp_id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/account/${bp_id}/contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/account/${bp_id}/sales-team`\n    }, {\n      label: 'Relationships',\n      routerLink: `/store/account/${bp_id}/relationships`\n    },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/account/${bp_id}/ai-insights`,\n    // },\n    {\n      label: 'Organization Data',\n      routerLink: `/store/account/${bp_id}/organization-data`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/account/${bp_id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/account/${bp_id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/account/${bp_id}/activities`\n    }, {\n      label: 'Opportunities',\n      routerLink: `/store/account/${bp_id}/opportunities`\n    }, {\n      label: 'Tickets',\n      routerLink: `/store/account/${bp_id}/tickets`\n    }, {\n      label: 'Sales Quotes',\n      routerLink: `/store/account/${bp_id}/sales-quotes`\n    }, {\n      label: 'Sales Orders',\n      routerLink: `/store/account/${bp_id}/sales-orders`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Accounts',\n      routerLink: ['/store/account']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadAccountData(accountId) {\n    this.loading = true;\n    this.accountservice.getAccountByID(accountId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response) {\n          this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\n          this.bp_extension_docId = response?.data?.[0]?.bp_extension?.documentId;\n          this.Actions = [{\n            name: this.bp_status === 'OBSOLETE' ? 'Set As Active' : 'Set As Obsolete',\n            code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO'\n          }];\n          const partner_role = response?.data?.[0]?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n          this.partner_role = partner_role?.bp_identification?.business_partner?.bp_full_name || null;\n          this.accountDetails = response?.data?.[0] || null;\n          this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.addresses || []);\n          this.loading = false;\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      SAA: () => this.UpdateStatus(this.bp_extension_docId, 'ACTIVE'),\n      SAO: () => this.UpdateStatus(this.bp_extension_docId, 'OBSOLETE')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      bp_status: status,\n      bp_id: this.partner_id\n    };\n    const apiCall = docid ? this.accountservice.updateBpExtension(docid, data) // Update if exists\n    : this.accountservice.createBpExtension(data); // Create if not exists\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.GlobalNoteForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.GlobalNoteForm.value\n      };\n      const data = {\n        note: value?.note,\n        bp_id: _this?.partner_id,\n        ...(!_this.NoteDetails.documentId ? {\n          is_global_note: true\n        } : {})\n      };\n      const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.accountservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n      : _this.accountservice.createNote(data); // Create if not exists\n      apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Account Note Updated successFully!'\n          });\n          _this.accountservice.getAccountByID(_this.partner_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  goToBack() {\n    this.router.navigate(['/store/account']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountDetailsComponent_Factory(t) {\n      return new (t || AccountDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountDetailsComponent,\n      selectors: [[\"app-account-details\"]],\n      decls: 83,\n      vars: 25,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"onChange\", \"activeIndexChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function AccountDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵtemplate(1, AccountDetailsComponent_div_1_Template, 2, 0, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵlistener(\"onChange\", function AccountDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function AccountDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(9, AccountDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"h5\", 19);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 20)(23, \"li\", 21)(24, \"span\", 22);\n          i0.ɵɵtext(25, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"li\", 21)(28, \"span\", 22);\n          i0.ɵɵtext(29, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21)(32, \"span\", 22);\n          i0.ɵɵtext(33, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"ul\", 24)(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n          i0.ɵɵtext(40, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 28);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n          i0.ɵɵtext(47, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 28);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n          i0.ɵɵtext(54, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 28);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 25)(59, \"span\", 26)(60, \"i\", 27);\n          i0.ɵɵtext(61, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 28);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"li\", 25)(66, \"span\", 26)(67, \"i\", 27);\n          i0.ɵɵtext(68, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 28);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(72, \"form\", 29)(73, \"div\", 30)(74, \"h4\", 31);\n          i0.ɵɵtext(75, \"Global Note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 32);\n          i0.ɵɵelement(77, \"textarea\", 33);\n          i0.ɵɵelementStart(78, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_button_click_78_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(79, \"div\", 35)(80, \"p-button\", 36);\n          i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_p_button_click_80_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(82, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.contact_person_addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.contact_person_addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i6.Breadcrumb, i4.PrimeTemplate, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i7.ButtonDirective, i7.Button, i8.TabView, i8.TabPanel, i9.Toast, i10.ConfirmDialog, i11.ProgressSpinner],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "AccountDetailsComponent_p_tabPanel_9_ng_template_1_Template", "AccountDetailsComponent", "constructor", "route", "router", "formBuilder", "accountservice", "messageservice", "confirmationservice", "unsubscribe$", "accountDetails", "sidebarDetails", "NoteDetails", "activeItem", "bp_id", "bp_status", "bp_extension_docId", "partner_id", "partner_role", "loading", "activeIndex", "isSidebarHidden", "submitted", "saving", "Actions", "GlobalNoteForm", "group", "note", "ngOnInit", "home", "icon", "snapshot", "paramMap", "get", "account", "pipe", "subscribe", "response", "getGlobalNote", "next", "noteResponse", "data", "patchValue", "error", "console", "makeMenuItems", "items", "length", "setActiveTabFromURL", "params", "accountId", "loadAccountData", "events", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "breadcrumbitems", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getAccountByID", "bp_extension", "documentId", "name", "code", "customer", "partner_functions", "find", "p", "partner_function", "bp_identification", "business_partner", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "onActionChange", "actionCode", "value", "actionsMap", "SAA", "UpdateStatus", "SAO", "action", "confirm", "message", "header", "accept", "docid", "status", "apiCall", "updateBpExtension", "createBpExtension", "add", "severity", "detail", "setTimeout", "window", "location", "reload", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "is_global_note", "updateNote", "createNote", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "FormBuilder", "i3", "AccountService", "i4", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "AccountDetailsComponent_Template", "rf", "ctx", "AccountDetailsComponent_div_1_Template", "ɵɵlistener", "AccountDetailsComponent_Template_p_tabView_onChange_8_listener", "$event", "ɵɵtwoWayListener", "AccountDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵtwoWayBindingSet", "AccountDetailsComponent_p_tabPanel_9_Template", "AccountDetailsComponent_Template_button_click_78_listener", "AccountDetailsComponent_Template_p_button_click_80_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵtextInterpolate1", "contact_companies", "business_partner_person", "first_name", "last_name", "contact_person_addresses"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AccountService } from '../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n@Component({\r\n  selector: 'app-account-details',\r\n  templateUrl: './account-details.component.html',\r\n  styleUrl: './account-details.component.scss',\r\n})\r\nexport class AccountDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public accountDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public NoteDetails: any = null;\r\n  public items: MenuItem[] | any;\r\n  public activeItem: MenuItem = {};\r\n  public home: MenuItem | any;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public bp_id: string = '';\r\n  public bp_status: string = '';\r\n  public bp_extension_docId: string = '';\r\n  public partner_id: string = '';\r\n  public partner_role: string = '';\r\n  public loading: boolean = false;\r\n  public activeIndex: number = 0;\r\n  public isSidebarHidden = false;\r\n  public submitted = false;\r\n  public saving = false;\r\n  public Actions: Actions[] = [];\r\n\r\n  public GlobalNoteForm: FormGroup = this.formBuilder.group({\r\n    note: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private formBuilder: FormBuilder,\r\n    private accountservice: AccountService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        this.partner_id = response?.bp_id;\r\n\r\n        if (this.partner_id) {\r\n          this.accountservice\r\n            .getGlobalNote(this.partner_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe({\r\n              next: (noteResponse) => {\r\n                this.NoteDetails = noteResponse?.data[0] || [];\r\n\r\n                this.GlobalNoteForm.patchValue({\r\n                  note: noteResponse?.data[0]?.note,\r\n                });\r\n              },\r\n              error: (error) => {\r\n                console.error('Error fetching global note:', error);\r\n              },\r\n            });\r\n        }\r\n      });\r\n\r\n    this.makeMenuItems(this.bp_id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const accountId = params.get('id');\r\n        if (accountId) {\r\n          this.loadAccountData(accountId);\r\n        }\r\n      });\r\n\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(bp_id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/account/${bp_id}/overview`,\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        routerLink: `/store/account/${bp_id}/contacts`,\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        routerLink: `/store/account/${bp_id}/sales-team`,\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        routerLink: `/store/account/${bp_id}/relationships`,\r\n      },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/account/${bp_id}/ai-insights`,\r\n      // },\r\n      {\r\n        label: 'Organization Data',\r\n        routerLink: `/store/account/${bp_id}/organization-data`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/account/${bp_id}/attachments`,\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        routerLink: `/store/account/${bp_id}/notes`,\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        routerLink: `/store/account/${bp_id}/activities`,\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        routerLink: `/store/account/${bp_id}/opportunities`,\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        routerLink: `/store/account/${bp_id}/tickets`,\r\n      },\r\n      {\r\n        label: 'Sales Quotes',\r\n        routerLink: `/store/account/${bp_id}/sales-quotes`,\r\n      },\r\n      {\r\n        label: 'Sales Orders',\r\n        routerLink: `/store/account/${bp_id}/sales-orders`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Accounts', routerLink: ['/store/account'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadAccountData(accountId: string): void {\r\n    this.loading = true;\r\n    this.accountservice\r\n      .getAccountByID(accountId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response) {\r\n            this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\r\n            this.bp_extension_docId =\r\n              response?.data?.[0]?.bp_extension?.documentId;\r\n            this.Actions = [\r\n              {\r\n                name:\r\n                  this.bp_status === 'OBSOLETE'\r\n                    ? 'Set As Active'\r\n                    : 'Set As Obsolete',\r\n                code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO',\r\n              },\r\n            ];\r\n            const partner_role =\r\n              response?.data?.[0]?.customer?.partner_functions?.find(\r\n                (p: any) => p.partner_function === 'YI'\r\n              );\r\n            this.partner_role =\r\n              partner_role?.bp_identification?.business_partner?.bp_full_name ||\r\n              null;\r\n            this.accountDetails = response?.data?.[0] || null;\r\n            this.sidebarDetails = this.formatSidebarDetails(\r\n              response?.data[0]?.addresses || []\r\n            );\r\n            this.loading = false;\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      SAA: () => this.UpdateStatus(this.bp_extension_docId, 'ACTIVE'),\r\n      SAO: () => this.UpdateStatus(this.bp_extension_docId, 'OBSOLETE'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      bp_status: status,\r\n      bp_id: this.partner_id,\r\n    };\r\n    const apiCall = docid\r\n      ? this.accountservice.updateBpExtension(docid, data) // Update if exists\r\n      : this.accountservice.createBpExtension(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Action Updated Successfully!',\r\n        });\r\n\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 1000);\r\n      },\r\n      error: () => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.GlobalNoteForm.invalid) {\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    const value = { ...this.GlobalNoteForm.value };\r\n\r\n    const data = {\r\n      note: value?.note,\r\n      bp_id: this?.partner_id,\r\n      ...(!this.NoteDetails.documentId ? { is_global_note: true } : {}),\r\n    };\r\n\r\n    const apiCall =\r\n      this.NoteDetails && this.NoteDetails.documentId\r\n        ? this.accountservice.updateNote(this.NoteDetails.documentId, data) // Update if exists\r\n        : this.accountservice.createNote(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Account Note Updated successFully!',\r\n        });\r\n        this.accountservice\r\n          .getAccountByID(this.partner_id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/account']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"loading\">\r\n    <p-progressSpinner></p-progressSpinner>\r\n</div>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <!-- <p-dropdown [options]=\"Actions\" (onChange)=\"onActionChange($event)\" optionLabel=\"name\" placeholder=\"Action\"\r\n        [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" /> -->\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" (onChange)=\"onTabChange($event)\" [(activeIndex)]=\"activeIndex\">\r\n                <p-tabPanel *ngFor=\"let tab of items;let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative flex-nowrap\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{accountDetails?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">CRM ID</span> : {{accountDetails?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Account Owner </span> :\r\n                                            {{partner_role || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Main Contact</span> :\r\n                                            {{\r\n                                            (accountDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (accountDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.contact_person_addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                    <form [formGroup]=\"GlobalNoteForm\">\r\n                        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden mt-5 p-3\">\r\n                            <h4 class=\"mb-3 font-semibold text-color\">Global Note</h4>\r\n                            <div class=\"flex flex-column gap-3\">\r\n                                <textarea formControlName=\"note\" rows=\"4\"\r\n                                    class=\"w-full h-8rem p-2 border-1 border-round\"\r\n                                    placeholder=\"Enter your note here...\"></textarea>\r\n                                <button pButton type=\"button\" (click)=\"onNoteSubmit()\" label=\"Save Note\"\r\n                                    class=\"p-button-rounded justify-content-center w-9rem h-3rem\"></button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": ";AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICHzCC,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAE,SAAA,wBAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBkBH,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAI,MAAA,GACvE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAK,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDP,EAAA,CAAAQ,SAAA,EACvE;IADuER,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBV,EAAA,CAAAC,cAAA,qBAAmF;IAC/ED,EAAA,CAAAW,UAAA,IAAAC,2DAAA,0BAAgC;IAKpCZ,EAAA,CAAAG,YAAA,EAAa;;;IANuCH,EAAA,CAAAK,UAAA,+BAA8B;;;ADDlG,OAAM,MAAOQ,uBAAuB;EAyBlCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,cAA8B,EAC9BC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA9BrB,KAAAC,YAAY,GAAG,IAAIvB,OAAO,EAAQ;IACnC,KAAAwB,cAAc,GAAQ,IAAI;IAC1B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAAC,UAAU,GAAa,EAAE;IAGzB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,OAAO,GAAc,EAAE;IAEvB,KAAAC,cAAc,GAAc,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACxDC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEnC,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACmB,KAAK,GAAG,IAAI,CAACX,KAAK,CAAC4B,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAAC3B,cAAc,CAAC4B,OAAO,CACxBC,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAI,CAACpB,UAAU,GAAGoB,QAAQ,EAAEvB,KAAK;MAEjC,IAAI,IAAI,CAACG,UAAU,EAAE;QACnB,IAAI,CAACX,cAAc,CAChBgC,aAAa,CAAC,IAAI,CAACrB,UAAU,CAAC,CAC9BkB,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAC;UACTG,IAAI,EAAGC,YAAY,IAAI;YACrB,IAAI,CAAC5B,WAAW,GAAG4B,YAAY,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YAE9C,IAAI,CAAChB,cAAc,CAACiB,UAAU,CAAC;cAC7Bf,IAAI,EAAEa,YAAY,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEd;aAC9B,CAAC;UACJ,CAAC;UACDgB,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACrD;SACD,CAAC;MACN;IACF,CAAC,CAAC;IAEJ,IAAI,CAACE,aAAa,CAAC,IAAI,CAAC/B,KAAK,CAAC;IAC9B,IAAI,IAAI,CAACgC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACiC,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACE,mBAAmB,EAAE;IAC1B,IAAI,CAAC7C,KAAK,CAAC6B,QAAQ,CAChBG,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAEa,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAAChB,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIiB,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;IAEJ,IAAI,CAAC9C,MAAM,CAACgD,MAAM,CAACjB,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAAC2B,SAAS,CAAC,MAAK;MACnE,IAAI,CAACY,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAH,aAAaA,CAAC/B,KAAa;IACzB,IAAI,CAACgC,KAAK,GAAG,CACX;MACEhD,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC;IACD;IACA;IACA;IACA;IACA;MACEhB,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,EACD;MACEhB,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE,kBAAkBmB,KAAK;KACpC,CACF;EACH;EAEAkC,mBAAmBA,CAAA;IACjB,MAAMK,QAAQ,GAAG,IAAI,CAACjD,MAAM,CAACkD,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAACX,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMW,UAAU,GAAG,IAAI,CAACZ,KAAK,CAACa,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAACjE,UAAU,CAACkE,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAACnC,WAAW,GAAGsC,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAAC7C,UAAU,GAAG,IAAI,CAACiC,KAAK,CAAC,IAAI,CAAC1B,WAAW,CAAC,IAAI,IAAI,CAAC0B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACgB,gBAAgB,CAAC,IAAI,CAACjD,UAAU,EAAEf,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEAgE,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAACC,eAAe,GAAG,CACrB;MAAElE,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,EACrD;MAAEG,KAAK,EAAEiE,SAAS;MAAEpE,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAsE,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACpB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAAC3B,WAAW,GAAG8C,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC1B,WAAW,CAAC;IAEhD,IAAIgD,WAAW,EAAEzE,UAAU,EAAE;MAC3B,IAAI,CAACS,MAAM,CAACiE,aAAa,CAACD,WAAW,CAACzE,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQwD,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAAC/B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACb,cAAc,CAChBgE,cAAc,CAACpB,SAAS,CAAC,CACzBf,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAC;MACTG,IAAI,EAAGF,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACtB,SAAS,GAAGsB,QAAQ,EAAEI,IAAI,GAAG,CAAC,CAAC,EAAE8B,YAAY,EAAExD,SAAS;UAC7D,IAAI,CAACC,kBAAkB,GACrBqB,QAAQ,EAAEI,IAAI,GAAG,CAAC,CAAC,EAAE8B,YAAY,EAAEC,UAAU;UAC/C,IAAI,CAAChD,OAAO,GAAG,CACb;YACEiD,IAAI,EACF,IAAI,CAAC1D,SAAS,KAAK,UAAU,GACzB,eAAe,GACf,iBAAiB;YACvB2D,IAAI,EAAE,IAAI,CAAC3D,SAAS,KAAK,UAAU,GAAG,KAAK,GAAG;WAC/C,CACF;UACD,MAAMG,YAAY,GAChBmB,QAAQ,EAAEI,IAAI,GAAG,CAAC,CAAC,EAAEkC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CACnDC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;UACH,IAAI,CAAC7D,YAAY,GACfA,YAAY,EAAE8D,iBAAiB,EAAEC,gBAAgB,EAAEC,YAAY,IAC/D,IAAI;UACN,IAAI,CAACxE,cAAc,GAAG2B,QAAQ,EAAEI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI;UACjD,IAAI,CAAC9B,cAAc,GAAG,IAAI,CAACwE,oBAAoB,CAC7C9C,QAAQ,EAAEI,IAAI,CAAC,CAAC,CAAC,EAAE2C,SAAS,IAAI,EAAE,CACnC;UACD,IAAI,CAACjE,OAAO,GAAG,KAAK;QACtB;MACF,CAAC;MACDwB,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACxB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEQgE,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,cAAcA,CAACxC,KAAU;IACvB,MAAMyC,UAAU,GAAGzC,KAAK,CAAC0C,KAAK,EAAElC,IAAI;IAEpC,MAAMmC,UAAU,GAAkC;MAChDC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC/F,kBAAkB,EAAE,QAAQ,CAAC;MAC/DgG,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC/F,kBAAkB,EAAE,UAAU;KACjE;IAED,MAAMiG,MAAM,GAAGJ,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIM,MAAM,EAAE;MACV,IAAI,CAACzG,mBAAmB,CAAC0G,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjBtF,IAAI,EAAE,4BAA4B;QAClCuF,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAF,YAAYA,CAACO,KAAU,EAAEC,MAAW;IAClC,MAAM9E,IAAI,GAAG;MACX1B,SAAS,EAAEwG,MAAM;MACjBzG,KAAK,EAAE,IAAI,CAACG;KACb;IACD,MAAMuG,OAAO,GAAGF,KAAK,GACjB,IAAI,CAAChH,cAAc,CAACmH,iBAAiB,CAACH,KAAK,EAAE7E,IAAI,CAAC,CAAC;IAAA,EACnD,IAAI,CAACnC,cAAc,CAACoH,iBAAiB,CAACjF,IAAI,CAAC,CAAC,CAAC;IACjD+E,OAAO,CAACrF,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAAC2B,SAAS,CAAC;MACnDG,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChC,cAAc,CAACoH,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEFC,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDtF,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpC,cAAc,CAACoH,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAEMK,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC7G,SAAS,GAAG,IAAI;MAErB,IAAI6G,KAAI,CAAC1G,cAAc,CAAC4G,OAAO,EAAE;QAC/B;MACF;MACAF,KAAI,CAAC5G,MAAM,GAAG,IAAI;MAClB,MAAMqF,KAAK,GAAG;QAAE,GAAGuB,KAAI,CAAC1G,cAAc,CAACmF;MAAK,CAAE;MAE9C,MAAMnE,IAAI,GAAG;QACXd,IAAI,EAAEiF,KAAK,EAAEjF,IAAI;QACjBb,KAAK,EAAEqH,KAAI,EAAElH,UAAU;QACvB,IAAI,CAACkH,KAAI,CAACvH,WAAW,CAAC4D,UAAU,GAAG;UAAE8D,cAAc,EAAE;QAAI,CAAE,GAAG,EAAE;OACjE;MAED,MAAMd,OAAO,GACXW,KAAI,CAACvH,WAAW,IAAIuH,KAAI,CAACvH,WAAW,CAAC4D,UAAU,GAC3C2D,KAAI,CAAC7H,cAAc,CAACiI,UAAU,CAACJ,KAAI,CAACvH,WAAW,CAAC4D,UAAU,EAAE/B,IAAI,CAAC,CAAC;MAAA,EAClE0F,KAAI,CAAC7H,cAAc,CAACkI,UAAU,CAAC/F,IAAI,CAAC,CAAC,CAAC;MAC5C+E,OAAO,CAACrF,IAAI,CAAChD,SAAS,CAACgJ,KAAI,CAAC1H,YAAY,CAAC,CAAC,CAAC2B,SAAS,CAAC;QACnDG,IAAI,EAAEA,CAAA,KAAK;UACT4F,KAAI,CAAC5H,cAAc,CAACoH,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFM,KAAI,CAAC7H,cAAc,CAChBgE,cAAc,CAAC6D,KAAI,CAAClH,UAAU,CAAC,CAC/BkB,IAAI,CAAChD,SAAS,CAACgJ,KAAI,CAAC1H,YAAY,CAAC,CAAC,CAClC2B,SAAS,EAAE;QAChB,CAAC;QACDO,KAAK,EAAEA,CAAA,KAAK;UACVwF,KAAI,CAAC5G,MAAM,GAAG,KAAK;UACnB4G,KAAI,CAAC5H,cAAc,CAACoH,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEAY,QAAQA,CAAA;IACN,IAAI,CAACrI,MAAM,CAACsI,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACtH,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAuH,WAAWA,CAAA;IACT,IAAI,CAACnI,YAAY,CAAC8B,IAAI,EAAE;IACxB,IAAI,CAAC9B,YAAY,CAACoI,QAAQ,EAAE;EAC9B;;;uBAhVW5I,uBAAuB,EAAAb,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7J,EAAA,CAAA0J,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA/J,EAAA,CAAA0J,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAA0J,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAnK,EAAA,CAAA0J,iBAAA,CAAAQ,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAvBvJ,uBAAuB;MAAAwJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBpC3K,EAAA,CAAAE,SAAA,iBAAuD;UACvDF,EAAA,CAAAW,UAAA,IAAAkK,sCAAA,iBAAwF;UAKhF7K,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAE,SAAA,sBAA+F;UAIvGF,EAHI,CAAAG,YAAA,EAAM,EAGJ;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAA8K,UAAA,sBAAAC,+DAAAC,MAAA;YAAA,OAAYJ,GAAA,CAAA/F,WAAA,CAAAmG,MAAA,CAAmB;UAAA,EAAC;UAAChL,EAAA,CAAAiL,gBAAA,+BAAAC,wEAAAF,MAAA;YAAAhL,EAAA,CAAAmL,kBAAA,CAAAP,GAAA,CAAA5I,WAAA,EAAAgJ,MAAA,MAAAJ,GAAA,CAAA5I,WAAA,GAAAgJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UACzFhL,EAAA,CAAAW,UAAA,IAAAyK,6CAAA,wBAAmF;UAQ3FpL,EADI,CAAAG,YAAA,EAAY,EACV;UASsBH,EAR5B,CAAAC,cAAA,eAAqD,eACL,eACmD,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAI,MAAA,UAAE;UACjDJ,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAI,MAAA,IACJ;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACE;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,IAC1D;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAI,MAAA,sBAAc;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,IAElE;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,IAMhE;UAIhBJ,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAI,MAAA,gBAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAI,MAAA,IAAuC;UAChEJ,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAI,MAAA,cAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAI,MAAA,IAA4C;UACrEJ,EADqE,CAAAG,YAAA,EAAO,EACvE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,qBAChD;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAI,MAAA,IACP;UAChBJ,EADgB,CAAAG,YAAA,EAAO,EAClB;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,cAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAI,MAAA,IAA6C;UACtEJ,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAI,MAAA,gBAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAI,MAAA,IAA2C;UAIhFJ,EAJgF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,eAC6C,cAC9B;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAC,cAAA,eAAoC;UAChCD,EAAA,CAAAE,SAAA,oBAEqD;UACrDF,EAAA,CAAAC,cAAA,kBACkE;UADpCD,EAAA,CAAA8K,UAAA,mBAAAO,0DAAA;YAAA,OAAST,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAKtE9I,EAJkF,CAAAG,YAAA,EAAS,EACzE,EACJ,EACH,EACL;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA8K,UAAA,mBAAAQ,4DAAA;YAAA,OAASV,GAAA,CAAArB,aAAA,EAAe;UAAA,EAAC;UAH7BvJ,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAE,SAAA,qBAA+B;UAKnDF,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAE,SAAA,uBAAmC;;;UA5HJF,EAAA,CAAAK,UAAA,cAAa;UAC6BL,EAAA,CAAAQ,SAAA,EAAa;UAAbR,EAAA,CAAAK,UAAA,SAAAuK,GAAA,CAAA7I,OAAA,CAAa;UAQ5D/B,EAAA,CAAAQ,SAAA,GAAyB;UAAeR,EAAxC,CAAAK,UAAA,UAAAuK,GAAA,CAAAhG,eAAA,CAAyB,SAAAgG,GAAA,CAAAnI,IAAA,CAAc,uCAAuC;UAQjFzC,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAK,UAAA,oBAAmB;UAAkCL,EAAA,CAAAuL,gBAAA,gBAAAX,GAAA,CAAA5I,WAAA,CAA6B;UAC7DhC,EAAA,CAAAQ,SAAA,EAAS;UAATR,EAAA,CAAAK,UAAA,YAAAuK,GAAA,CAAAlH,KAAA,CAAS;UAWe1D,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAwL,WAAA,iBAAAZ,GAAA,CAAA3I,eAAA,CAAsC;UAUlEjC,EAAA,CAAAQ,SAAA,GACJ;UADIR,EAAA,CAAAyL,kBAAA,OAAAb,GAAA,CAAAtJ,cAAA,kBAAAsJ,GAAA,CAAAtJ,cAAA,CAAAwE,YAAA,cACJ;UAG8D9F,EAAA,CAAAQ,SAAA,GAC1D;UAD0DR,EAAA,CAAAyL,kBAAA,SAAAb,GAAA,CAAAtJ,cAAA,kBAAAsJ,GAAA,CAAAtJ,cAAA,CAAAI,KAAA,cAC1D;UAKkE1B,EAAA,CAAAQ,SAAA,GAElE;UAFkER,EAAA,CAAAyL,kBAAA,QAAAb,GAAA,CAAA9I,YAAA,aAElE;UAEgE9B,EAAA,CAAAQ,SAAA,GAMhE;UANgER,EAAA,CAAAyL,kBAAA,UAAAb,GAAA,CAAAtJ,cAAA,kBAAAsJ,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,kBAAAd,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,qBAAAd,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,IAAAC,uBAAA,kBAAAf,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAAhB,GAAA,CAAAtJ,cAAA,kBAAAsJ,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,kBAAAd,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,qBAAAd,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,IAAAC,uBAAA,kBAAAf,GAAA,CAAAtJ,cAAA,CAAAoK,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAMhE;UAWiB7L,EAAA,CAAAQ,SAAA,GAAuC;UAAvCR,EAAA,CAAAS,iBAAA,EAAAmK,GAAA,CAAArJ,cAAA,kBAAAqJ,GAAA,CAAArJ,cAAA,qBAAAqJ,GAAA,CAAArJ,cAAA,IAAA2E,OAAA,SAAuC;UAMvClG,EAAA,CAAAQ,SAAA,GAA4C;UAA5CR,EAAA,CAAAS,iBAAA,EAAAmK,GAAA,CAAArJ,cAAA,kBAAAqJ,GAAA,CAAArJ,cAAA,qBAAAqJ,GAAA,CAAArJ,cAAA,IAAA2F,YAAA,SAA4C;UAO9ClH,EAAA,CAAAQ,SAAA,GACP;UADOR,EAAA,CAAAS,iBAAA,EAAAmK,GAAA,CAAArJ,cAAA,kBAAAqJ,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,kBAAAd,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,qBAAAd,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,kBAAAf,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,kBAAAlB,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,qBAAAlB,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3E,aAAA,kBAAAyD,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3E,aAAA,qBAAAyD,GAAA,CAAArJ,cAAA,CAAAmK,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3E,aAAA,IAAAD,YAAA,SACP;UAKSlH,EAAA,CAAAQ,SAAA,GAA6C;UAA7CR,EAAA,CAAAS,iBAAA,EAAAmK,GAAA,CAAArJ,cAAA,kBAAAqJ,GAAA,CAAArJ,cAAA,qBAAAqJ,GAAA,CAAArJ,cAAA,IAAAyF,aAAA,SAA6C;UAM7ChH,EAAA,CAAAQ,SAAA,GAA2C;UAA3CR,EAAA,CAAAS,iBAAA,EAAAmK,GAAA,CAAArJ,cAAA,kBAAAqJ,GAAA,CAAArJ,cAAA,qBAAAqJ,GAAA,CAAArJ,cAAA,IAAA6F,WAAA,SAA2C;UAK1EpH,EAAA,CAAAQ,SAAA,EAA4B;UAA5BR,EAAA,CAAAK,UAAA,cAAAuK,GAAA,CAAAvI,cAAA,CAA4B;UAiBJrC,EAAA,CAAAQ,SAAA,GAAqC;UAArCR,EAAA,CAAAwL,WAAA,gBAAAZ,GAAA,CAAA3I,eAAA,CAAqC;UAF/DjC,EAD8B,CAAAK,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}