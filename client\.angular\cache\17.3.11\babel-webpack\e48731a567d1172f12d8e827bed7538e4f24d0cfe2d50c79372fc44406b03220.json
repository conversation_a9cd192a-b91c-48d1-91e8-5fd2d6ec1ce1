{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { jwtDecode } from 'jwt-decode';\nimport { AuthService } from '../authentication/auth.service';\nexport function initializeApp(service) {\n  const urlParams = new URLSearchParams(window.location.search);\n  if (urlParams.has('code')) {\n    const token = urlParams.get('code');\n    const decoded = jwtDecode(token);\n    const user = {\n      id: decoded.id,\n      documentId: decoded.documentId,\n      isAdmin: true\n    };\n    service.setAuth(token, user, false);\n  }\n  return () => service.checkAdminUser();\n}\nexport const appInitializerProviders = [{\n  provide: APP_INITIALIZER,\n  useFactory: initializeApp,\n  deps: [AuthService],\n  multi: true\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}