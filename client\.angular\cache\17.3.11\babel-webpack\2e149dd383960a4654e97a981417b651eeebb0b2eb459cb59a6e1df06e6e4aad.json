{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../opportunities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesHierarchyComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 23)(2, \"div\", 24);\n    i0.ɵɵtext(3, \" Opportunity ID \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \" Parent Opportunity ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 26);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 27)(6, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function OpportunitiesHierarchyComponent_ng_template_8_Template_button_click_6_listener($event) {\n      const hierarchy_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(hierarchy_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const hierarchy_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (hierarchy_r2 == null ? null : hierarchy_r2.opportunity_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (hierarchy_r2 == null ? null : hierarchy_r2.parent_object_id) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"No Hierarchy found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \"Loading Hierarchy data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Add Child Opportunity\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_24_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r4.name, \"\");\n  }\n}\nfunction OpportunitiesHierarchyComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesHierarchyComponent_ng_template_24_span_2_Template, 2, 1, \"span\", 30);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.opportunity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.name);\n  }\n}\nfunction OpportunitiesHierarchyComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Opportunity is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesHierarchyComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, OpportunitiesHierarchyComponent_div_25_div_1_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"opportunity_id\"].errors[\"required\"]);\n  }\n}\nexport let OpportunitiesHierarchyComponent = /*#__PURE__*/(() => {\n  class OpportunitiesHierarchyComponent {\n    constructor(route, formBuilder, opportunitiesservice, messageservice, confirmationservice) {\n      this.route = route;\n      this.formBuilder = formBuilder;\n      this.opportunitiesservice = opportunitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.hierarchydetails = null;\n      this.visible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.opportunity_id = '';\n      this.editid = '';\n      this.opportunitiesLoading = false;\n      this.opportunitiesInput$ = new Subject();\n      this.defaultOptions = [];\n      this.selectedDialogType = null;\n      this.selectedParentId = null;\n      this.HierarchyForm = this.formBuilder.group({\n        opportunity_id: ['', [Validators.required]]\n      });\n    }\n    ngOnInit() {\n      this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n      this.loadOpportunity();\n      this.opportunitiesservice.getHierarchy(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.data) {\n            this.hierarchydetails = response.data;\n          }\n        },\n        error: err => {\n          console.error('Failed to load hierarchy:', err);\n        }\n      });\n    }\n    loadOpportunity() {\n      this.opportunities$ = concat(of(this.defaultOptions), this.opportunitiesInput$.pipe(distinctUntilChanged(), tap(() => this.opportunitiesLoading = true), switchMap(term => {\n        const params = {\n          [`fields[0]`]: 'opportunity_id',\n          [`fields[1]`]: 'name'\n        };\n        if (term) {\n          params[`filters[$or][0][opportunity_id][$containsi]`] = term;\n          params[`filters[$or][1][name][$containsi]`] = term;\n          return this.opportunitiesservice.getHierarchyOpportunity(params).pipe(map(data => {\n            return data;\n          }), tap(() => this.opportunitiesLoading = false));\n        }\n        return of([]).pipe(tap(() => this.opportunitiesLoading = false));\n      })));\n    }\n    refreshHierarchyWithMessage(successMessage) {\n      this.opportunitiesservice.getHierarchy(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.data) {\n            this.hierarchydetails = response.data;\n          }\n          this.messageservice.add({\n            severity: 'success',\n            detail: successMessage\n          });\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Failed to refresh hierarchy data.'\n          });\n        }\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        _this.visible = true;\n        if (_this.HierarchyForm.invalid) {\n          _this.visible = true;\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.HierarchyForm.value\n        };\n        const data = {\n          parent_object_id: _this.position === 'parent' ? _this.selectedParentId : _this.opportunity_id,\n          opportunity_id: value?.opportunity_id,\n          business_transaction_document_relationship_role_code: '14'\n        };\n        _this.opportunitiesservice.createHierarchy(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.visible = false;\n            _this.HierarchyForm.reset();\n            _this.refreshHierarchyWithMessage('Hierarchy Created Successfully!.');\n          },\n          error: () => {\n            _this.saving = false;\n            _this.visible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.opportunitiesservice.deleteHierarchy(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.refreshHierarchyWithMessage('Record Deleted Successfully!');\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    get f() {\n      return this.HierarchyForm.controls;\n    }\n    showDialog(position, parentid) {\n      if (position === 'parent' && parentid) {\n        this.selectedParentId = parentid;\n      }\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.HierarchyForm.reset();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OpportunitiesHierarchyComponent_Factory(t) {\n        return new (t || OpportunitiesHierarchyComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.OpportunitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunitiesHierarchyComponent,\n        selectors: [[\"app-opportunities-hierarchy\"]],\n        decls: 31,\n        vars: 25,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"hierarchy-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Opportunity\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"name\", \"bindValue\", \"opportunity_id\", \"formControlName\", \"opportunity_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"class\", \"invalid-feedback top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pSortableColumn\", \"opportunity_id\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"opportunity_id\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"], [4, \"ngIf\"], [1, \"invalid-feedback\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n        template: function OpportunitiesHierarchyComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Hierarchy\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 3);\n            i0.ɵɵlistener(\"click\", function OpportunitiesHierarchyComponent_Template_p_button_click_4_listener() {\n              return ctx.showDialog(\"child\", \"\");\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n            i0.ɵɵtemplate(7, OpportunitiesHierarchyComponent_ng_template_7_Template, 9, 0, \"ng-template\", 6)(8, OpportunitiesHierarchyComponent_ng_template_8_Template, 7, 2, \"ng-template\", 7)(9, OpportunitiesHierarchyComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, OpportunitiesHierarchyComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"p-dialog\", 10);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesHierarchyComponent_Template_p_dialog_visibleChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(12, OpportunitiesHierarchyComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n            i0.ɵɵelementStart(13, \"form\", 11)(14, \"div\", 12)(15, \"label\", 13)(16, \"span\", 14);\n            i0.ɵɵtext(17, \"lightbulb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(18, \"Opportunity\");\n            i0.ɵɵelementStart(19, \"span\", 15);\n            i0.ɵɵtext(20, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 16)(22, \"ng-select\", 17);\n            i0.ɵɵpipe(23, \"async\");\n            i0.ɵɵtemplate(24, OpportunitiesHierarchyComponent_ng_template_24_Template, 3, 2, \"ng-template\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(25, OpportunitiesHierarchyComponent_div_25_Template, 2, 1, \"div\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 20)(27, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function OpportunitiesHierarchyComponent_Template_button_click_27_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵtext(28, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function OpportunitiesHierarchyComponent_Template_button_click_29_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(30, \" Save \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.hierarchydetails)(\"rows\", 10)(\"paginator\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.HierarchyForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(23, 20, ctx.opportunities$))(\"hideSelected\", true)(\"loading\", ctx.opportunitiesLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.opportunitiesInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(23, _c1, ctx.submitted && ctx.f[\"opportunity_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"opportunity_id\"].errors);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i6.Table, i4.PrimeTemplate, i6.SortableColumn, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dialog, i9.NgSelectComponent, i9.NgOptionTemplateDirective, i10.Tooltip, i11.InputText, i5.AsyncPipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .hierarchy-popup .p-dialog{margin-right:50px}  .hierarchy-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .hierarchy-popup .p-dialog .p-dialog-header h4{margin:0}  .hierarchy-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return OpportunitiesHierarchyComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}