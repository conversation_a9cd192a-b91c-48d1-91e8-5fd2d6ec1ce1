{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Talossan [tzl]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n//! author : <PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n\n  // After the year there should be a slash and the amount of years since December 26, 1979 in Roman numerals.\n  // This is currently too difficult (maybe even impossible) to add.\n  var tzl = moment.defineLocale('tzl', {\n    months: 'Januar_Fevraglh_Març_Avrïu_Mai_Gün_<PERSON>_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar'.split('_'),\n    monthsShort: 'Jan_Fev_Mar_Avr_<PERSON>_<PERSON>ü<PERSON>_Jul_Gus_Set_Lis_Noe_Zec'.split('_'),\n    weekdays: 'Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi'.split('_'),\n    weekdaysShort: 'Súl_Lún_Mai_Már_Xhú_Vié_Sát'.split('_'),\n    weekdaysMin: 'Sú_Lú_Ma_Má_Xh_Vi_Sá'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM [dallas] YYYY',\n      LLL: 'D. MMMM [dallas] YYYY HH.mm',\n      LLLL: 'dddd, [li] D. MMMM [dallas] YYYY HH.mm'\n    },\n    meridiemParse: /d\\'o|d\\'a/i,\n    isPM: function (input) {\n      return \"d'o\" === input.toLowerCase();\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? \"d'o\" : \"D'O\";\n      } else {\n        return isLower ? \"d'a\" : \"D'A\";\n      }\n    },\n    calendar: {\n      sameDay: '[oxhi à] LT',\n      nextDay: '[demà à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[ieiri à] LT',\n      lastWeek: '[sür el] dddd [lasteu à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'osprei %s',\n      past: 'ja%s',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['viensas secunds', \"'iensas secunds\"],\n      ss: [number + ' secunds', '' + number + ' secunds'],\n      m: [\"'n míut\", \"'iens míut\"],\n      mm: [number + ' míuts', '' + number + ' míuts'],\n      h: [\"'n þora\", \"'iensa þora\"],\n      hh: [number + ' þoras', '' + number + ' þoras'],\n      d: [\"'n ziua\", \"'iensa ziua\"],\n      dd: [number + ' ziuas', '' + number + ' ziuas'],\n      M: [\"'n mes\", \"'iens mes\"],\n      MM: [number + ' mesen', '' + number + ' mesen'],\n      y: [\"'n ar\", \"'iens ar\"],\n      yy: [number + ' ars', '' + number + ' ars']\n    };\n    return isFuture ? format[key][0] : withoutSuffix ? format[key][0] : format[key][1];\n  }\n  return tzl;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}