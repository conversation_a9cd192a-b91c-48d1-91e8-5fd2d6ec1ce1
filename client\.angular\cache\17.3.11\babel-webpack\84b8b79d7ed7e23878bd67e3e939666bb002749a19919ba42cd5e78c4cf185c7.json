{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/activities/activities.service\";\nimport * as i2 from \"../../../opportunities.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nexport let SalesCallFollowItemDetailComponent = /*#__PURE__*/(() => {\n  class SalesCallFollowItemDetailComponent {\n    constructor(activitiesservice, opportunitiesservice, router) {\n      this.activitiesservice = activitiesservice;\n      this.opportunitiesservice = opportunitiesservice;\n      this.router = router;\n      this.followupDetail = null;\n      this.unsubscribe$ = new Subject();\n      this.dropdowns = {\n        activityDocumentType: [],\n        activityCategory: [],\n        activityStatus: [],\n        activitydisposition: [],\n        activityInitiatorCode: []\n      };\n    }\n    ngOnInit() {\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n      this.followupdata = history.state.followupdata;\n      this.activitiesservice.getActivityByID(this.followupdata?.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (!response?.data?.length) return;\n        this.followupDetail = response.data[0];\n      });\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    goToBack() {\n      this.opportunitiesservice.getOpportunityByID(this.followupdata?.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.router.navigate(['/store/opportunities', this.followupdata.opportunity_id, 'follow-up']);\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallFollowItemDetailComponent_Factory(t) {\n        return new (t || SalesCallFollowItemDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallFollowItemDetailComponent,\n        selectors: [[\"app-sales-call-follow-item-detail\"]],\n        decls: 173,\n        vars: 25,\n        consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"pt-0\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"filter-sec\", \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"pt-0\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mb-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"]],\n        template: function SalesCallFollowItemDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n            i0.ɵɵtext(5, \"Follow Up Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function SalesCallFollowItemDetailComponent_Template_button_click_7_listener() {\n              return ctx.goToBack();\n            });\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9, \"arrow_back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Back \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 2)(13, \"div\", 9)(14, \"h4\", 4);\n            i0.ɵɵtext(15, \"Follow Up Information\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"ul\", 11)(18, \"li\", 12)(19, \"div\", 13)(20, \"i\", 14);\n            i0.ɵɵtext(21, \"badge\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 15)(23, \"h6\", 16);\n            i0.ɵɵtext(24, \"ID #\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\", 17);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"li\", 12)(28, \"div\", 13)(29, \"i\", 14);\n            i0.ɵɵtext(30, \"subject\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 15)(32, \"h6\", 16);\n            i0.ɵɵtext(33, \"Subject\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"p\", 17);\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"li\", 12)(37, \"div\", 13)(38, \"i\", 14);\n            i0.ɵɵtext(39, \"description\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 15)(41, \"h6\", 16);\n            i0.ɵɵtext(42, \"Transaction Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"p\", 17);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"li\", 12)(46, \"div\", 13)(47, \"i\", 14);\n            i0.ɵɵtext(48, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 15)(50, \"h6\", 16);\n            i0.ɵɵtext(51, \"Account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"p\", 17);\n            i0.ɵɵtext(53);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"li\", 12)(55, \"div\", 13)(56, \"i\", 14);\n            i0.ɵɵtext(57, \"person\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"div\", 15)(59, \"h6\", 16);\n            i0.ɵɵtext(60, \"Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"p\", 17);\n            i0.ɵɵtext(62);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(63, \"li\", 12)(64, \"div\", 13)(65, \"i\", 14);\n            i0.ɵɵtext(66, \"category\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 15)(68, \"h6\", 16);\n            i0.ɵɵtext(69, \"Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"p\", 17);\n            i0.ɵɵtext(71);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(72, \"li\", 12)(73, \"div\", 13)(74, \"i\", 14);\n            i0.ɵɵtext(75, \"code\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"div\", 15)(77, \"h6\", 16);\n            i0.ɵɵtext(78, \"Disposition Code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"p\", 17);\n            i0.ɵɵtext(80);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(81, \"li\", 12)(82, \"div\", 13)(83, \"i\", 14);\n            i0.ɵɵtext(84, \"info\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(85, \"div\", 15)(86, \"h6\", 16);\n            i0.ɵɵtext(87, \"Reason\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"p\", 17);\n            i0.ɵɵtext(89);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"li\", 12)(91, \"div\", 13)(92, \"i\", 14);\n            i0.ɵɵtext(93, \"schedule\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(94, \"div\", 15)(95, \"h6\", 16);\n            i0.ɵɵtext(96, \"Call Date/Time\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"p\", 17);\n            i0.ɵɵtext(98);\n            i0.ɵɵpipe(99, \"date\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(100, \"li\", 12)(101, \"div\", 13)(102, \"i\", 14);\n            i0.ɵɵtext(103, \"schedule\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(104, \"div\", 15)(105, \"h6\", 16);\n            i0.ɵɵtext(106, \"End Date/Time\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"p\", 17);\n            i0.ɵɵtext(108);\n            i0.ɵɵpipe(109, \"date\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(110, \"li\", 12)(111, \"div\", 13)(112, \"i\", 14);\n            i0.ɵɵtext(113, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(114, \"div\", 15)(115, \"h6\", 16);\n            i0.ɵɵtext(116, \"Owner\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"p\", 17);\n            i0.ɵɵtext(118);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(119, \"li\", 12)(120, \"div\", 13)(121, \"i\", 14);\n            i0.ɵɵtext(122, \"branding_watermark\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(123, \"div\", 15)(124, \"h6\", 16);\n            i0.ɵɵtext(125, \"Brand\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(126, \"p\", 17);\n            i0.ɵɵtext(127);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(128, \"li\", 12)(129, \"div\", 13)(130, \"i\", 14);\n            i0.ɵɵtext(131, \"group\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(132, \"div\", 15)(133, \"h6\", 16);\n            i0.ɵɵtext(134, \"Customer Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(135, \"p\", 17);\n            i0.ɵɵtext(136);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(137, \"li\", 12)(138, \"div\", 13)(139, \"i\", 14);\n            i0.ɵɵtext(140, \"emoji_events\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(141, \"div\", 15)(142, \"h6\", 16);\n            i0.ɵɵtext(143, \"Ranking\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(144, \"p\", 17);\n            i0.ɵɵtext(145);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(146, \"li\", 12)(147, \"div\", 13)(148, \"i\", 14);\n            i0.ɵɵtext(149, \"check_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(150, \"div\", 15)(151, \"h6\", 16);\n            i0.ɵɵtext(152, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(153, \"p\", 17);\n            i0.ɵɵtext(154);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(155, \"li\", 12)(156, \"div\", 13)(157, \"i\", 14);\n            i0.ɵɵtext(158, \"label\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(159, \"div\", 15)(160, \"h6\", 16);\n            i0.ɵɵtext(161, \"Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(162, \"p\", 17);\n            i0.ɵɵtext(163);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(164, \"li\", 12)(165, \"div\", 13)(166, \"i\", 14);\n            i0.ɵɵtext(167, \"access_time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(168, \"div\", 15)(169, \"h6\", 16);\n            i0.ɵɵtext(170, \"Customer TimeZone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(171, \"p\", 17);\n            i0.ɵɵtext(172);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(26);\n            i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.activity_id) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.subject) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(ctx.getLabelFromDropdown(\"activityDocumentType\", ctx.followupDetail == null ? null : ctx.followupDetail.document_type) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.business_partner == null ? null : ctx.followupDetail.business_partner.bp_full_name) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.business_partner_contact == null ? null : ctx.followupDetail.business_partner_contact.bp_full_name) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"activityCategory\", ctx.followupDetail == null ? null : ctx.followupDetail.phone_call_category) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"activitydisposition\", ctx.followupDetail == null ? null : ctx.followupDetail.disposition_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.reason) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.start_date) ? i0.ɵɵpipeBind3(99, 17, ctx.followupDetail == null ? null : ctx.followupDetail.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \"\");\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.end_date) ? i0.ɵɵpipeBind3(109, 21, ctx.followupDetail == null ? null : ctx.followupDetail.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \"\");\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.business_partner_owner == null ? null : ctx.followupDetail.business_partner_owner.bp_full_name) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.brand) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.customer_group) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.ranking) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"activityStatus\", ctx.followupDetail == null ? null : ctx.followupDetail.activity_status) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"activityInitiatorCode\", ctx.followupDetail == null ? null : ctx.followupDetail.initiator_code) || \"-\", \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.customer_timezone) || \"-\", \"\");\n          }\n        },\n        dependencies: [i4.DatePipe]\n      });\n    }\n  }\n  return SalesCallFollowItemDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}