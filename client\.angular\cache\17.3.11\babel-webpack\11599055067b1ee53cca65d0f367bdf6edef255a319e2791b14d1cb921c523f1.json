{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { ApiConstant } from 'src/app/constants/api.constants';\nimport { map, tap } from 'rxjs/operators';\nimport { stringify } from 'qs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport let AccountService = /*#__PURE__*/(() => {\n  class AccountService {\n    constructor(http, authservice) {\n      this.http = http;\n      this.authservice = authservice;\n      this.accountSubject = new BehaviorSubject(null);\n      this.account = this.accountSubject.asObservable();\n      this.contactSubject = new BehaviorSubject(null);\n      this.contact = this.contactSubject.asObservable();\n    }\n    createContact(data) {\n      return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\n    }\n    createMarketing(data) {\n      return this.http.post(`${CMS_APIContstant.MARKETING_ATTRIBUTES}`, {\n        data\n      });\n    }\n    createExistingContact(data) {\n      return this.http.post(`${CMS_APIContstant.EXISTING_CONTACT}`, data);\n    }\n    createNote(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n        data\n      });\n    }\n    updateMarketing(Id, data) {\n      return this.http.put(`${CMS_APIContstant.MARKETING_ATTRIBUTES}/${Id}`, {\n        data\n      });\n    }\n    updateContact(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n    }\n    updateReactivate(contactdata) {\n      const data = {\n        validity_end_date: '9999-12-29'\n      };\n      return this.http.put(`${CMS_APIContstant.PARTNERS_CONTACTS}/${contactdata.documentId}`, {\n        data\n      });\n    }\n    updateBpStatus(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, {\n        data\n      });\n    }\n    updateNote(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n        data\n      });\n    }\n    deleteContact(id) {\n      return this.http.delete(`${CMS_APIContstant.ACCOUNT_CONTACT}/${id}`);\n    }\n    getAccountDetailsByCreditInvoiceOrder(data) {\n      return this.http.post(ApiConstant.PARTNERS + '/search', data);\n    }\n    getAccounts(page, pageSize, sortField, sortOrder, searchTerm, obsolete, myaccount) {\n      let params = new HttpParams().set('pagination[page]', Math.max(page, 1).toString()).set('pagination[pageSize]', Math.max(pageSize, 1).toString()).set('fields', 'bp_id,bp_full_name,is_marked_for_archiving').set('filters[roles][bp_role][$in][0]', 'FLCU01').set('filters[roles][bp_role][$in][1]', 'FLCU00').set('populate[addresses][fields][0]', 'house_number').set('populate[addresses][fields][1]', 'street_name').set('populate[addresses][fields][2]', 'city_name').set('populate[addresses][fields][3]', 'region').set('populate[addresses][fields][4]', 'country').set('populate[addresses][fields][5]', 'postal_code').set('populate[addresses][populate][address_usages][fields][0]', 'address_usage');\n      if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm && searchTerm.trim() !== '') {\n        params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][addresses][house_number][$containsi]', searchTerm).set('filters[$or][3][addresses][city_name][$containsi]', searchTerm).set('filters[$or][4][addresses][region][$containsi]', searchTerm).set('filters[$or][5][addresses][postal_code][$containsi]', searchTerm);\n      }\n      // Combine obsolete and myaccount filters carefully to avoid index collision\n      if (obsolete || myaccount) {\n        let andIndex = 0;\n        if (obsolete) {\n          params = params.set(`filters[$and][${andIndex}][is_marked_for_archiving][$eq]`, 'true');\n          andIndex++;\n        }\n        if (myaccount) {\n          const email = this.authservice.getUserEmail();\n          if (email) {\n            params = params.set(`filters[$and][${andIndex}][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][${andIndex + 1}][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n          } else {\n            // Handle the case where email is null or undefined, if necessary\n            console.warn('No email found for the logged-in user');\n          }\n        }\n      }\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      });\n    }\n    getContacts(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]=*`, {\n        params\n      }).pipe(tap(response => console.log('Contacts API Data:', response)), map(response => (response?.data || []).map(item => {\n        const contact = item?.addresses?.[0];\n        const email = contact?.emails?.[0]?.email_address || '';\n        const phone = contact?.phone_numbers?.[0]?.phone_number || '';\n        return {\n          bp_id: item?.bp_id || '',\n          bp_full_name: (item?.first_name ? item.first_name : '') + (item?.last_name ? ' ' + item.last_name : ''),\n          email: email,\n          phone: phone\n        };\n      })));\n    }\n    getCPFunction() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getCPDepartment() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    fetchOrders(params) {\n      return this.http.get(ApiConstant.SALES_ORDER, {\n        params\n      });\n    }\n    fetchSalesquoteOrders(params) {\n      return this.http.get(ApiConstant.SALES_QUOTE, {\n        params\n      });\n    }\n    fetchPartnerById(bp_Id) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?filters[bp_id][$eq]=${bp_Id}&populate=address_usages.business_partner_address`);\n    }\n    fetchOrderById(orderId) {\n      return this.http.get(`${ApiConstant.SALES_ORDER}/${orderId}`);\n    }\n    getQuoteDetails(data) {\n      const params = new HttpParams().append('DOC_TYPE', data.DOC_TYPE);\n      return this.http.get(`${ApiConstant.SALES_QUOTE}/${data.SD_DOC}`, {\n        params\n      });\n    }\n    fetchOrderStatuses(headers) {\n      return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n        params: headers\n      });\n    }\n    getPartnerFunction(custId) {\n      return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`).pipe(map(res => res.data));\n    }\n    getGlobalNote(id) {\n      let params = new HttpParams().set('filters[is_global_note][$eq]', 'true').set('filters[bp_id][$eq]', id);\n      return this.http.get(`${CMS_APIContstant.CRM_NOTE}`, {\n        params\n      });\n    }\n    getDropdownOptions(type) {\n      const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getCRMPartner() {\n      let params = new HttpParams().set('filters[usage][$eq]', 'CRM').set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      }).pipe(map(response => {\n        let data = response.data || [];\n        return data.map(item => ({\n          label: item.description,\n          // Display text\n          value: item.code // Stored value\n        }));\n      }));\n    }\n    getAccountByID(documentId, isAccountId = false) {\n      const params = new HttpParams().set(isAccountId ? 'filters[bp_id][$eq]' : 'filters[documentId][$eq]', documentId).set('populate[customer][populate][partner_functions][populate][business_partner][populate][addresses][populate]', '*').set('populate[notes][populate]', '*').set('populate[bp_extension][populate]', '*').set('populate[marketing_attributes][populate]', '*').set('populate[contact_companies][populate][person_func_and_dept][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][addresses][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][bp_extension][populate]', '*').set('populate[addresses][populate]', '*').set('populate[address_usages][populate]', '*').set('populate[roles][fields][0]', 'bp_role');\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      }).pipe(map(response => {\n        const accountDetails = response?.data[0] || null;\n        this.accountSubject.next(accountDetails);\n        return response;\n      }));\n    }\n    getContactByID(contactId) {\n      const params = stringify({\n        filters: {\n          $and: [{\n            bp_id: {\n              $eq: contactId\n            }\n          }]\n        },\n        populate: {\n          addresses: {\n            populate: {\n              emails: {\n                fields: ['email_address']\n              }\n            }\n          }\n        }\n      });\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?${params}`).pipe(map(response => {\n        const contactDetails = response?.data[0] || null;\n        this.contactSubject.next(contactDetails);\n        return response;\n      }));\n    }\n    deleteNote(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n    }\n    search(query) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?${query}`).pipe(map(response => {\n        return response?.data || [];\n      }));\n    }\n    getAccountDetailsByContact(query) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}?${query}`).pipe(map(response => {\n        return response?.data || [];\n      }));\n    }\n    getInvoices(data) {\n      const params = new HttpParams().appendAll(data);\n      return this.http.get(ApiConstant.INVOICE, {\n        params\n      });\n    }\n    getMemos(data) {\n      const params = new HttpParams().appendAll(data);\n      return this.http.get(ApiConstant.INVOICE, {\n        params\n      });\n    }\n    getReturns(data) {\n      const params = new HttpParams().appendAll(data);\n      return this.http.get(ApiConstant.RETURN_ORDER, {\n        params\n      });\n    }\n    invoicePdf(url) {\n      return this.http.get(url, {\n        observe: 'response',\n        responseType: 'blob'\n      });\n    }\n    sendInvoicesByEmail(payload) {\n      return this.http.post(ApiConstant.INVOICE + '/email', payload);\n    }\n    static {\n      this.ɵfac = function AccountService_Factory(t) {\n        return new (t || AccountService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AccountService,\n        factory: AccountService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AccountService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}