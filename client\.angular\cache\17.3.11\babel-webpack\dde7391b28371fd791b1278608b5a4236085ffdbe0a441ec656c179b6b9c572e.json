{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/editor\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"primeng/inputswitch\";\nimport * as i14 from \"primeng/tooltip\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"primeng/inputtext\";\nimport * as i17 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29)(4, \"span\", 30);\n    i0.ɵɵtext(5, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Opportunity ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"label\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Expected Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 31);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 28)(36, \"label\", 29)(37, \"span\", 30);\n    i0.ɵɵtext(38, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 32)(41, \"a\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 29)(46, \"span\", 30);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Primary Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 32)(50, \"a\", 33);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"div\", 27)(53, \"div\", 28)(54, \"label\", 29)(55, \"span\", 30);\n    i0.ɵɵtext(56, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(57, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 31);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 27)(61, \"div\", 28)(62, \"label\", 29)(63, \"span\", 30);\n    i0.ɵɵtext(64, \"account_tree\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \" Parent Opportunity \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 31);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 27)(69, \"div\", 28)(70, \"label\", 29)(71, \"span\", 30);\n    i0.ɵɵtext(72, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(73, \" Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 31);\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 27)(77, \"div\", 28)(78, \"label\", 29)(79, \"span\", 30);\n    i0.ɵɵtext(80, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 31);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(84, \"div\", 27)(85, \"div\", 28)(86, \"label\", 29)(87, \"span\", 30);\n    i0.ɵɵtext(88, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 31);\n    i0.ɵɵtext(91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(92, \"div\", 27)(93, \"div\", 28)(94, \"label\", 29)(95, \"span\", 30);\n    i0.ɵɵtext(96, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Reason for Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 31);\n    i0.ɵɵtext(99);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(100, \"div\", 27)(101, \"div\", 28)(102, \"label\", 29)(103, \"span\", 30);\n    i0.ɵɵtext(104, \"track_changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \" Days in Sales Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"div\", 31);\n    i0.ɵɵtext(107);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(108, \"div\", 27)(109, \"div\", 28)(110, \"label\", 29)(111, \"span\", 30);\n    i0.ɵɵtext(112, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(113, \" Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 31);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(116, \"div\", 27)(117, \"div\", 28)(118, \"label\", 29)(119, \"span\", 30);\n    i0.ɵɵtext(120, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"div\", 31);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(124, \"div\", 27)(125, \"div\", 28)(126, \"label\", 29)(127, \"span\", 30);\n    i0.ɵɵtext(128, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(129, \" Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 31);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(132, \"div\", 27)(133, \"div\", 28)(134, \"label\", 29)(135, \"span\", 30);\n    i0.ɵɵtext(136, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(137, \" Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"div\", 31);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(140, \"div\", 27)(141, \"div\", 28)(142, \"label\", 29)(143, \"span\", 30);\n    i0.ɵɵtext(144, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(145, \" Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(146, \"div\", 31);\n    i0.ɵɵtext(147);\n    i0.ɵɵpipe(148, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(149, \"div\", 27)(150, \"div\", 28)(151, \"label\", 29)(152, \"span\", 30);\n    i0.ɵɵtext(153, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(154, \" Last Updated Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(155, \"div\", 31);\n    i0.ɵɵtext(156);\n    i0.ɵɵpipe(157, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(158, \"div\", 27)(159, \"div\", 28)(160, \"label\", 29)(161, \"span\", 30);\n    i0.ɵɵtext(162, \"update\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(163, \" Last Updated By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(164, \"div\", 31);\n    i0.ɵɵtext(165);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(166, \"div\", 27)(167, \"div\", 28)(168, \"label\", 29)(169, \"span\", 30);\n    i0.ɵɵtext(170, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(171, \" Progress \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(172, \"div\", 31);\n    i0.ɵɵtext(173);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(174, \"div\", 27)(175, \"div\", 28)(176, \"label\", 29)(177, \"span\", 30);\n    i0.ɵɵtext(178, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(179, \" Need Help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(180, \"div\", 31);\n    i0.ɵɵtext(181);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.opportunity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_end_date) ? i0.ɵɵpipeBind2(33, 24, ctx_r0.overviewDetails.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0] == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"opportunityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.group_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.parent_opportunity) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunitySource\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.origin_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.weighted_expected_net_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.life_cycle_status_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.result_reason_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.days_in_sales_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.probability_percent) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_organisation_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_unit_party_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_start_date) ? i0.ɵɵpipeBind2(148, 27, ctx_r0.overviewDetails.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.updatedAt) ? i0.ɵɵpipeBind2(157, 30, ctx_r0.overviewDetails.updatedAt, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_changed_by) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.progress) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.need_help) ? \"Yes\" : \"No\", \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_11_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_23_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template, 2, 1, \"span\", 60);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_42_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, OpportunitiesOverviewComponent_form_6_ng_template_53_span_3_Template, 2, 1, \"span\", 60)(4, OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bp_id, \": \", item_r4.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_54_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_87_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\", 35)(5, \"span\", 36);\n    i0.ɵɵtext(6, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Name \");\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"input\", 38);\n    i0.ɵɵtemplate(11, OpportunitiesOverviewComponent_form_6_div_11_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 28)(14, \"label\", 35)(15, \"span\", 36);\n    i0.ɵɵtext(16, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \"Expected Value \");\n    i0.ɵɵelementStart(18, \"span\", 37);\n    i0.ɵɵtext(19, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 41);\n    i0.ɵɵelement(21, \"input\", 42)(22, \"p-dropdown\", 43);\n    i0.ɵɵtemplate(23, OpportunitiesOverviewComponent_form_6_div_23_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 27)(25, \"div\", 28)(26, \"label\", 35)(27, \"span\", 36);\n    i0.ɵɵtext(28, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \"Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p-calendar\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"div\", 28)(33, \"label\", 35)(34, \"span\", 36);\n    i0.ɵɵtext(35, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \"Account \");\n    i0.ɵɵelementStart(37, \"span\", 37);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"ng-select\", 45);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵtemplate(41, OpportunitiesOverviewComponent_form_6_ng_template_41_Template, 3, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpportunitiesOverviewComponent_form_6_div_42_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 35)(46, \"span\", 36);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \"Primary Contact \");\n    i0.ɵɵelementStart(49, \"span\", 37);\n    i0.ɵɵtext(50, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"ng-select\", 47);\n    i0.ɵɵpipe(52, \"async\");\n    i0.ɵɵtemplate(53, OpportunitiesOverviewComponent_form_6_ng_template_53_Template, 5, 4, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpportunitiesOverviewComponent_form_6_div_54_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 27)(56, \"div\", 28)(57, \"label\", 35)(58, \"span\", 36);\n    i0.ɵɵtext(59, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-dropdown\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 27)(63, \"div\", 28)(64, \"label\", 35)(65, \"span\", 36);\n    i0.ɵɵtext(66, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-dropdown\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 40)(70, \"div\", 28)(71, \"label\", 35)(72, \"span\", 36);\n    i0.ɵɵtext(73, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 41);\n    i0.ɵɵelement(76, \"input\", 50)(77, \"p-dropdown\", 51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"div\", 27)(79, \"div\", 28)(80, \"label\", 35)(81, \"span\", 36);\n    i0.ɵɵtext(82, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \"Status \");\n    i0.ɵɵelementStart(84, \"span\", 37);\n    i0.ɵɵtext(85, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(86, \"p-dropdown\", 52);\n    i0.ɵɵtemplate(87, OpportunitiesOverviewComponent_form_6_div_87_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 27)(89, \"div\", 28)(90, \"label\", 35)(91, \"span\", 36);\n    i0.ɵɵtext(92, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 27)(96, \"div\", 28)(97, \"label\", 35)(98, \"span\", 36);\n    i0.ɵɵtext(99, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(101, \"input\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 27)(103, \"div\", 28)(104, \"label\", 35)(105, \"span\", 36);\n    i0.ɵɵtext(106, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(107, \"Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(108, \"input\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"div\", 27)(110, \"div\", 28)(111, \"label\", 35)(112, \"span\", 36);\n    i0.ɵɵtext(113, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(114, \"Created Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(115, \"p-calendar\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 27)(117, \"div\", 28)(118, \"label\", 35)(119, \"span\", 36);\n    i0.ɵɵtext(120, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \"Need help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(122, \"p-inputSwitch\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(123, \"div\", 58)(124, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_form_6_Template_button_click_124_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.OpportunityOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(48, _c2, ctx_r0.submitted && ctx_r0.f[\"name\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c2, ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies)(\"disabled\", ctx_r0.currencies.length === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 44, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(52, _c2, ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(52, 46, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(54, _c2, ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityCategory\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunitySource\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies)(\"disabled\", ctx_r0.currencies.length === 1);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityStatus\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(56, _c2, ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNote === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 69);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNote === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 69);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener() {\n      const col_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r7.field, ctx_r0.notedetails, \"notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_4_Template, 1, 1, \"i\", 64)(5, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_5_Template, 1, 0, \"i\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r7.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r7.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote === col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote !== col_r7.field);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 62);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_16_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.customSort(\"note\", ctx_r0.notedetails, \"notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 63);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_ng_template_16_i_4_Template, 1, 1, \"i\", 64)(5, OpportunitiesOverviewComponent_ng_template_16_i_5_Template, 1, 0, \"i\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template, 6, 4, \"ng-container\", 66);\n    i0.ɵɵelementStart(7, \"th\", 67);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNoteColumns);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r9 == null ? null : notes_r9.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (notes_r9 == null ? null : notes_r9.updatedBy) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 75);\n    i0.ɵɵtemplate(3, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 76)(4, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template, 2, 1, \"ng-container\", 76);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r10.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedBy\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 71);\n    i0.ɵɵelement(1, \"td\", 72);\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_Template, 5, 3, \"ng-container\", 66);\n    i0.ɵɵelementStart(3, \"td\", 67)(4, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_17_Template_button_click_4_listener() {\n      const notes_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_17_Template_button_click_5_listener($event) {\n      const notes_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r9));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r9 == null ? null : notes_r9.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNoteColumns);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 77);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 77);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_div_26_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class OpportunitiesOverviewComponent {\n  constructor(formBuilder, opportunitiesservice, activitiesservice, messageservice, confirmationservice, router) {\n    this.formBuilder = formBuilder;\n    this.opportunitiesservice = opportunitiesservice;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.router = router;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.notedetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.opportunity_id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.currencies = [{\n      label: 'USD',\n      value: 'USD'\n    }];\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.OpportunityOverviewForm = this.formBuilder.group({\n      name: ['', [Validators.required]],\n      prospect_party_id: ['', [Validators.required]],\n      primary_contact_party_id: ['', [Validators.required]],\n      origin_type_code: [''],\n      expected_revenue_amount: ['', [Validators.required]],\n      expected_revenue_amount_currency_code: [''],\n      weighted_expected_net_amount: [''],\n      weighted_expected_net_amount_currency_code: [''],\n      expected_revenue_start_date: [''],\n      expected_revenue_end_date: [''],\n      life_cycle_status_code: ['', [Validators.required]],\n      probability_percent: [''],\n      group_code: [''],\n      sales_organisation_id: [''],\n      sales_unit_party_id: [''],\n      need_help: ['']\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n    this._selectedNoteColumns = [];\n    this.NoteCols = [{\n      field: 'createdAt',\n      header: 'Created At'\n    }, {\n      field: 'updatedBy',\n      header: 'Updated At'\n    }];\n    this.sortFieldNote = '';\n    this.sortOrderNote = 1;\n    this.compareById = (a, b) => a === b;\n  }\n  ngOnInit() {\n    // Opportunities successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('opportunitiesMessage');\n      }\n    }, 100);\n    this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    this.OpportunityOverviewForm.get('prospect_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.loadAccounts();\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.opportunity_id = response?.opportunity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n    this._selectedNoteColumns = this.NoteCols;\n  }\n  get selectedNoteColumns() {\n    return this._selectedNoteColumns;\n  }\n  set selectedNoteColumns(val) {\n    this._selectedNoteColumns = this.NoteCols.filter(col => val.includes(col));\n  }\n  onNoteColumnReorder(event) {\n    const draggedCol = this.NoteCols[event.dragIndex];\n    this.NoteCols.splice(event.dragIndex, 1);\n    this.NoteCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'notes') {\n      this.sortFieldNote = field;\n      this.sortOrderNote = this.sortOrderNote === 1 ? -1 : 1;\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = null;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrderNote * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(opportunity) {\n    const mainAccount = opportunity?.prospect_party_id ? {\n      bp_id: opportunity.prospect_party_id,\n      bp_full_name: opportunity.business_partner?.bp_full_name || ''\n    } : null;\n    const mainContact = opportunity?.primary_contact_party_id ? {\n      bp_id: opportunity.primary_contact_party_id,\n      bp_full_name: opportunity.business_partner_contact?.bp_full_name || ''\n    } : null;\n    this.existingopportunity = {\n      opportunity_id: opportunity?.opportunity_id,\n      name: opportunity?.name,\n      group_code: opportunity?.group_code,\n      origin_type_code: opportunity?.origin_type_code,\n      probability_percent: opportunity?.probability_percent,\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\n      expected_revenue_amount_currency_code: opportunity?.expected_revenue_amount ? 'USD' : '',\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\n      prospect_party_id: mainAccount,\n      primary_contact_party_id: mainContact,\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\n      weighted_expected_net_amount_currency_code: opportunity?.weighted_expected_net_amount ? 'USD' : '',\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\n      main_employee_responsible_party_id: opportunity?.main_employee_responsible_party_id,\n      sales_organisation_id: opportunity?.sales_organisation_id,\n      sales_unit_party_id: opportunity?.sales_unit_party_id,\n      need_help: opportunity?.need_help ? true : false\n    };\n    this.editid = opportunity.documentId;\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        opportunity_id: _this.opportunity_id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.opportunitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.opportunitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onSubmit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.OpportunityOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.OpportunityOverviewForm.value\n      };\n      const data = {\n        name: value?.name,\n        expected_revenue_amount: value?.expected_revenue_amount,\n        expected_revenue_amount_currency_code: value?.expected_revenue_amount ? 'USD' : '',\n        expected_revenue_end_date: value?.expected_revenue_end_date ? _this2.formatDate(value.expected_revenue_end_date) : null,\n        prospect_party_id: value?.prospect_party_id?.bp_id ?? value?.prospect_party_id,\n        primary_contact_party_id: value?.primary_contact_party_id?.bp_id ?? value?.primary_contact_party_id,\n        group_code: value?.group_code,\n        origin_type_code: value?.origin_type_code,\n        weighted_expected_net_amount: value?.weighted_expected_net_amount,\n        weighted_expected_net_amount_currency_code: value?.weighted_expected_net_amount ? 'USD' : '',\n        life_cycle_status_code: value?.life_cycle_status_code,\n        probability_percent: value?.probability_percent,\n        main_employee_responsible_party_id: value?.main_employee_responsible_party_id,\n        sales_organisation_id: value?.sales_organisation_id,\n        sales_unit_party_id: value?.sales_unit_party_id,\n        expected_revenue_start_date: value?.expected_revenue_start_date ? _this2.formatDate(value.expected_revenue_start_date) : null,\n        need_help: value?.need_help\n      };\n      _this2.opportunitiesservice.updateOpportunity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Opportunity Updated successFully!'\n          });\n          _this2.opportunitiesservice.getOpportunityByID(_this2.opportunity_id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const d = date instanceof Date ? date : new Date(date);\n    if (isNaN(d.getTime())) return ''; // handle invalid date\n    const yyyy = d.getFullYear();\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\n    const dd = String(d.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  get f() {\n    return this.OpportunityOverviewForm.controls;\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  onReset() {\n    this.submitted = false;\n    this.OpportunityOverviewForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesOverviewComponent_Factory(t) {\n      return new (t || OpportunitiesOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesOverviewComponent,\n      selectors: [[\"app-opportunities-overview\"]],\n      decls: 30,\n      vars: 31,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"font-medium\", \"text-700\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [1, \"flex\", \"align-items-center\", \"w-full\", \"gap-2\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"expected_revenue_amount_currency_code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"Currency\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"disabled\"], [\"formControlName\", \"expected_revenue_end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Expected Decision Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select Category\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select Source\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"weighted_expected_net_amount\", \"placeholder\", \"Weighted Value\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"weighted_expected_net_amount_currency_code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"Currency\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"disabled\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select Status\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_organisation_id\", \"type\", \"text\", \"formControlName\", \"sales_organisation_id\", \"placeholder\", \"Sales Organization'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_unit_party_id\", \"type\", \"text\", \"formControlName\", \"sales_unit_party_id\", \"placeholder\", \"Sales Unit\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"expected_revenue_start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Created Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"need_help\", 1, \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"border-round-left-lg\", 2, \"min-width\", \"70rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [1, \"border-round-left-lg\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function OpportunitiesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, OpportunitiesOverviewComponent_div_5_Template, 182, 33, \"div\", 4)(6, OpportunitiesOverviewComponent_form_6_Template, 125, 58, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"p-button\", 9);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_12_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-multiSelect\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedNoteColumns, $event) || (ctx.selectedNoteColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-table\", 12);\n          i0.ɵɵlistener(\"onColReorder\", function OpportunitiesOverviewComponent_Template_p_table_onColReorder_15_listener($event) {\n            return ctx.onNoteColumnReorder($event);\n          });\n          i0.ɵɵtemplate(16, OpportunitiesOverviewComponent_ng_template_16_Template, 9, 3, \"ng-template\", 13)(17, OpportunitiesOverviewComponent_ng_template_17_Template, 6, 2, \"ng-template\", 14)(18, OpportunitiesOverviewComponent_ng_template_18_Template, 3, 0, \"ng-template\", 15)(19, OpportunitiesOverviewComponent_ng_template_19_Template, 3, 0, \"ng-template\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"p-dialog\", 17);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(21, OpportunitiesOverviewComponent_ng_template_21_Template, 2, 0, \"ng-template\", 13);\n          i0.ɵɵelementStart(22, \"form\", 18)(23, \"div\", 19)(24, \"div\", 20);\n          i0.ɵɵelement(25, \"p-editor\", 21);\n          i0.ɵɵtemplate(26, OpportunitiesOverviewComponent_div_26_Template, 2, 1, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 23)(28, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_28_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_29_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.NoteCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedNoteColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(27, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(28, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i7.Table, i4.PrimeTemplate, i7.SortableColumn, i7.ReorderableColumn, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.Editor, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.InputSwitch, i14.Tooltip, i15.Calendar, i16.InputText, i17.MultiSelect, i6.AsyncPipe, i6.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1vdmVydmlldy9vcHBvcnR1bml0aWVzLW92ZXJ2aWV3LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUkscUJBQUE7RUFDQSxXQUFBO0FBQ0o7O0FBSVE7RUFDSSxrQkFBQTtBQURaO0FBR1k7RUFDSSw0QkFBQTtFQUNBLDJDQUFBO0FBRGhCO0FBR2dCO0VBQ0ksU0FBQTtBQURwQjtBQUtZO0VBQ0ksNEJBQUE7RUFDQSxpQkFBQTtBQUhoQiIsInNvdXJjZXNDb250ZW50IjpbIi5pbnZhbGlkLWZlZWRiYWNrLFxyXG4ucC1pbnB1dHRleHQ6aW52YWxpZCxcclxuLmlzLWNoZWNrYm94LWludmFsaWQsXHJcbi5wLWlucHV0dGV4dC5pcy1pbnZhbGlkIHtcclxuICAgIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICAgIHJpZ2h0OiAxMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAge1xyXG4gICAgLm5vdGUtcG9wdXAge1xyXG4gICAgICAgIC5wLWRpYWxvZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNTBweDtcclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "opportunity_id", "name", "ɵɵtextInterpolate1", "expected_revenue_amount", "expected_revenue_end_date", "ɵɵpipeBind2", "ɵɵproperty", "business_partner", "documentId", "ɵɵsanitizeUrl", "bp_full_name", "business_partner_contact", "contact_persons", "getLabelFromDropdown", "group_code", "parent_opportunity", "origin_type_code", "weighted_expected_net_amount", "life_cycle_status_code", "result_reason_code", "days_in_sales_status", "probability_percent", "business_partner_owner", "sales_organisation_id", "sales_unit_party_id", "expected_revenue_start_date", "updatedAt", "last_changed_by", "progress", "need_help", "ɵɵtemplate", "OpportunitiesOverviewComponent_form_6_div_11_div_1_Template", "submitted", "f", "errors", "OpportunitiesOverviewComponent_form_6_div_23_div_1_Template", "item_r3", "OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template", "bp_id", "OpportunitiesOverviewComponent_form_6_div_42_div_1_Template", "item_r4", "email", "mobile", "OpportunitiesOverviewComponent_form_6_ng_template_53_span_3_Template", "OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template", "ɵɵtextInterpolate2", "OpportunitiesOverviewComponent_form_6_div_54_div_1_Template", "OpportunitiesOverviewComponent_form_6_div_87_div_1_Template", "ɵɵelement", "OpportunitiesOverviewComponent_form_6_div_11_Template", "OpportunitiesOverviewComponent_form_6_div_23_Template", "OpportunitiesOverviewComponent_form_6_ng_template_41_Template", "OpportunitiesOverviewComponent_form_6_div_42_Template", "OpportunitiesOverviewComponent_form_6_ng_template_53_Template", "OpportunitiesOverviewComponent_form_6_div_54_Template", "OpportunitiesOverviewComponent_form_6_div_87_Template", "ɵɵlistener", "OpportunitiesOverviewComponent_form_6_Template_button_click_124_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "OpportunityOverviewForm", "ɵɵpureFunction1", "_c2", "currencies", "length", "ɵɵclassMap", "ɵɵpipeBind1", "accounts$", "accountLoading", "compareById", "accountInput$", "contacts$", "contactLoading", "contactInput$", "dropdowns", "sortOrderNote", "ɵɵelementContainerStart", "OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener", "col_r7", "_r6", "$implicit", "customSort", "field", "notedetails", "OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_4_Template", "OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_5_Template", "header", "sortFieldNote", "OpportunitiesOverviewComponent_ng_template_16_Template_th_click_1_listener", "_r5", "OpportunitiesOverviewComponent_ng_template_16_i_4_Template", "OpportunitiesOverviewComponent_ng_template_16_i_5_Template", "OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template", "selectedNoteColumns", "notes_r9", "updatedBy", "OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template", "OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template", "col_r10", "OpportunitiesOverviewComponent_ng_template_17_ng_container_2_Template", "OpportunitiesOverviewComponent_ng_template_17_Template_button_click_4_listener", "_r8", "editNote", "OpportunitiesOverviewComponent_ng_template_17_Template_button_click_5_listener", "$event", "stopPropagation", "confirmRemove", "note", "ɵɵsanitizeHtml", "OpportunitiesOverviewComponent_div_26_div_1_Template", "fNote", "OpportunitiesOverviewComponent", "constructor", "formBuilder", "opportunitiesservice", "activitiesservice", "messageservice", "confirmationservice", "router", "ngUnsubscribe", "defaultOptions", "saving", "editid", "isEditMode", "label", "value", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "group", "required", "prospect_party_id", "primary_contact_party_id", "expected_revenue_amount_currency_code", "weighted_expected_net_amount_currency_code", "NoteForm", "opportunityCategory", "opportunityStatus", "opportunitySource", "_selectedNoteColumns", "NoteCols", "a", "b", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadOpportunityDropDown", "get", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "err", "console", "error", "subscribe", "loadAccounts", "opportunity", "response", "notes", "fetchOverviewData", "val", "filter", "col", "includes", "onNoteColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "data", "type", "sort", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "i", "target", "getOpportunityDropdownOptions", "res", "attr", "description", "code", "dropdownKey", "item", "find", "opt", "term", "params", "getPartners", "bpId", "getPartnersContact", "contacts", "patchValue", "mainAccount", "mainContact", "existingopportunity", "main_employee_responsible_party_id", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getOpportunityByID", "createNote", "_this2", "formatDate", "updateOpportunity", "next", "date", "d", "Date", "isNaN", "getTime", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "confirm", "message", "icon", "accept", "remove", "deleteNote", "stripHtml", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "showDialog", "position", "controls", "toggleEdit", "onReset", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "OpportunitiesService", "i3", "ActivitiesService", "i4", "MessageService", "ConfirmationService", "i5", "Router", "selectors", "decls", "vars", "consts", "template", "OpportunitiesOverviewComponent_Template", "rf", "ctx", "OpportunitiesOverviewComponent_Template_p_button_click_4_listener", "OpportunitiesOverviewComponent_div_5_Template", "OpportunitiesOverviewComponent_form_6_Template", "OpportunitiesOverviewComponent_Template_p_button_click_12_listener", "ɵɵtwoWayListener", "OpportunitiesOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener", "ɵɵtwoWayBindingSet", "OpportunitiesOverviewComponent_Template_p_table_onColReorder_15_listener", "OpportunitiesOverviewComponent_ng_template_16_Template", "OpportunitiesOverviewComponent_ng_template_17_Template", "OpportunitiesOverviewComponent_ng_template_18_Template", "OpportunitiesOverviewComponent_ng_template_19_Template", "OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_20_listener", "OpportunitiesOverviewComponent_ng_template_21_Template", "OpportunitiesOverviewComponent_div_26_Template", "OpportunitiesOverviewComponent_Template_button_click_28_listener", "OpportunitiesOverviewComponent_Template_button_click_29_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\n\r\ninterface NoteColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-opportunities-overview',\r\n  templateUrl: './opportunities-overview.component.html',\r\n  styleUrl: './opportunities-overview.component.scss',\r\n})\r\nexport class OpportunitiesOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public notedetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingopportunity: any;\r\n  public opportunity_id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public currencies = [{ label: 'USD', value: 'USD' }];\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n\r\n  public OpportunityOverviewForm: FormGroup = this.formBuilder.group({\r\n    name: ['', [Validators.required]],\r\n    prospect_party_id: ['', [Validators.required]],\r\n    primary_contact_party_id: ['', [Validators.required]],\r\n    origin_type_code: [''],\r\n    expected_revenue_amount: ['', [Validators.required]],\r\n    expected_revenue_amount_currency_code: [''],\r\n    weighted_expected_net_amount: [''],\r\n    weighted_expected_net_amount_currency_code: [''],\r\n    expected_revenue_start_date: [''],\r\n    expected_revenue_end_date: [''],\r\n    life_cycle_status_code: ['', [Validators.required]],\r\n    probability_percent: [''],\r\n    group_code: [''],\r\n    sales_organisation_id: [''],\r\n    sales_unit_party_id: [''],\r\n    need_help: [''],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  private _selectedNoteColumns: NoteColumn[] = [];\r\n\r\n  public NoteCols: NoteColumn[] = [\r\n    { field: 'createdAt', header: 'Created At' },\r\n    { field: 'updatedBy', header: 'Updated At' },\r\n  ];\r\n\r\n  sortFieldNote: string = '';\r\n  sortOrderNote: number = 1;\r\n\r\n  ngOnInit(): void {\r\n    // Opportunities successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('opportunitiesMessage');\r\n      }\r\n    }, 100);\r\n    this.loadOpportunityDropDown(\r\n      'opportunityCategory',\r\n      'CRM_OPPORTUNITY_GROUP'\r\n    );\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.loadOpportunityDropDown(\r\n      'opportunitySource',\r\n      'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n    );\r\n    this.OpportunityOverviewForm.get('prospect_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.ngUnsubscribe),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.loadAccounts();\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.opportunity_id = response?.opportunity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n\r\n    this._selectedNoteColumns = this.NoteCols;\r\n  }\r\n\r\n  get selectedNoteColumns(): any[] {\r\n    return this._selectedNoteColumns;\r\n  }\r\n\r\n  set selectedNoteColumns(val: any[]) {\r\n    this._selectedNoteColumns = this.NoteCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  onNoteColumnReorder(event: any) {\r\n    const draggedCol = this.NoteCols[event.dragIndex];\r\n    this.NoteCols.splice(event.dragIndex, 1);\r\n    this.NoteCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(field: string, data: any[], type: 'notes') {\r\n    if (type === 'notes') {\r\n      this.sortFieldNote = field;\r\n      this.sortOrderNote = this.sortOrderNote === 1 ? -1 : 1;\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = null;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrderNote * result;\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(opportunity: any) {\r\n    const mainAccount = opportunity?.prospect_party_id\r\n      ? {\r\n          bp_id: opportunity.prospect_party_id,\r\n          bp_full_name: opportunity.business_partner?.bp_full_name || '',\r\n        }\r\n      : null;\r\n\r\n    const mainContact = opportunity?.primary_contact_party_id\r\n      ? {\r\n          bp_id: opportunity.primary_contact_party_id,\r\n          bp_full_name:\r\n            opportunity.business_partner_contact?.bp_full_name || '',\r\n        }\r\n      : null;\r\n    this.existingopportunity = {\r\n      opportunity_id: opportunity?.opportunity_id,\r\n      name: opportunity?.name,\r\n      group_code: opportunity?.group_code,\r\n      origin_type_code: opportunity?.origin_type_code,\r\n      probability_percent: opportunity?.probability_percent,\r\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\r\n      expected_revenue_amount_currency_code:\r\n        opportunity?.expected_revenue_amount ? 'USD' : '',\r\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\r\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\r\n      prospect_party_id: mainAccount,\r\n      primary_contact_party_id: mainContact,\r\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\r\n      weighted_expected_net_amount_currency_code:\r\n        opportunity?.weighted_expected_net_amount ? 'USD' : '',\r\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\r\n      main_employee_responsible_party_id:\r\n        opportunity?.main_employee_responsible_party_id,\r\n      sales_organisation_id: opportunity?.sales_organisation_id,\r\n      sales_unit_party_id: opportunity?.sales_unit_party_id,\r\n      need_help: opportunity?.need_help ? true : false,\r\n    };\r\n    this.editid = opportunity.documentId;\r\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      opportunity_id: this.opportunity_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.opportunitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.opportunitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.OpportunityOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.OpportunityOverviewForm.value };\r\n\r\n    const data = {\r\n      name: value?.name,\r\n      expected_revenue_amount: value?.expected_revenue_amount,\r\n      expected_revenue_amount_currency_code: value?.expected_revenue_amount\r\n        ? 'USD'\r\n        : '',\r\n      expected_revenue_end_date: value?.expected_revenue_end_date\r\n        ? this.formatDate(value.expected_revenue_end_date)\r\n        : null,\r\n      prospect_party_id:\r\n        value?.prospect_party_id?.bp_id ?? value?.prospect_party_id,\r\n      primary_contact_party_id:\r\n        value?.primary_contact_party_id?.bp_id ??\r\n        value?.primary_contact_party_id,\r\n      group_code: value?.group_code,\r\n      origin_type_code: value?.origin_type_code,\r\n      weighted_expected_net_amount: value?.weighted_expected_net_amount,\r\n      weighted_expected_net_amount_currency_code:\r\n        value?.weighted_expected_net_amount ? 'USD' : '',\r\n      life_cycle_status_code: value?.life_cycle_status_code,\r\n      probability_percent: value?.probability_percent,\r\n      main_employee_responsible_party_id:\r\n        value?.main_employee_responsible_party_id,\r\n      sales_organisation_id: value?.sales_organisation_id,\r\n      sales_unit_party_id: value?.sales_unit_party_id,\r\n      expected_revenue_start_date: value?.expected_revenue_start_date\r\n        ? this.formatDate(value.expected_revenue_start_date)\r\n        : null,\r\n      need_help: value?.need_help,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .updateOpportunity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Opportunity Updated successFully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    if (!date) return '';\r\n    const d = date instanceof Date ? date : new Date(date);\r\n    if (isNaN(d.getTime())) return ''; // handle invalid date\r\n    const yyyy = d.getFullYear();\r\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(d.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.OpportunityOverviewForm.controls;\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.OpportunityOverviewForm.reset();\r\n  }\r\n\r\n  compareById = (a: any, b: any) => a === b;\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil' : ''\" iconPos=\"right\"\r\n            class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold px-3'\" (click)=\"toggleEdit()\" [rounded]=\"true\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">tag</span> Opportunity ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.opportunity_id || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> Name\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.name || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">attach_money</span> Expected Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.expected_revenue_amount || '-'}}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Expected Decision\r\n                    Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_end_date ?\r\n                    (overviewDetails.expected_revenue_end_date | date: 'MM/dd/yyyy') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/account/' + overviewDetails?.business_partner?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">contact_phone</span> Primary Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/contacts/' + overviewDetails?.business_partner_contact?.contact_persons?.[0]?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner_contact?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityCategory',\r\n                    overviewDetails?.group_code) || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_tree</span> Parent Opportunity\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.parent_opportunity || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">source</span> Source\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunitySource',\r\n                    overviewDetails?.origin_type_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">scale</span> Weighted Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.weighted_expected_net_amount ||\r\n                    '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">lens</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityStatus',\r\n                    overviewDetails?.life_cycle_status_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">fact_check</span> Reason for Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.result_reason_code || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">track_changes</span> Days in Sales\r\n                    Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.days_in_sales_status || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Probability\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.probability_percent || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Owner\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">business</span> Sales Organization\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_organisation_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">sell</span> Sales Unit\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_unit_party_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Create Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_start_date ?\r\n                    (overviewDetails.expected_revenue_start_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Last Updated Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.updatedAt ?\r\n                    (overviewDetails.updatedAt | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">update</span> Last Updated By\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.last_changed_by || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Progress\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.progress || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">help</span> Need Help\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.need_help ? 'Yes' : 'No' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"OpportunityOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">badge</span> Name\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"name\" type=\"text\" formControlName=\"name\" placeholder=\"Name\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['name'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['name'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['name'].errors &&\r\n                                f['name'].errors['required']\r\n                              \">\r\n                            Name is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">attach_money</span>Expected Value\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"expected_revenue_amount\"\r\n                            placeholder=\"Expected Value\" class=\"h-3rem w-full\"\r\n                            [ngClass]=\"{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"expected_revenue_amount_currency_code\"\r\n                            optionLabel=\"label\" optionValue=\"value\" placeholder=\"Currency\" styleClass=\"h-3rem w-full\"\r\n                            [disabled]=\"currencies.length === 1\"></p-dropdown>\r\n                        <div *ngIf=\"submitted && f['expected_revenue_amount'].errors\" class=\"p-error\">\r\n                            <div *ngIf=\"\r\n                                submitted &&\r\n                                f['expected_revenue_amount'].errors &&\r\n                                f['expected_revenue_amount'].errors['required']\r\n                              \">\r\n                                Expected Value is required.\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Expected Decision Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Expected Decision Date\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"prospect_party_id\" [compareWith]=\"compareById\" [typeahead]=\"accountInput$\"\r\n                        [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['prospect_party_id'].errors }\"\r\n                        [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['prospect_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['prospect_party_id'].errors &&\r\n                                f['prospect_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">contact_phone</span>Primary Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"primary_contact_party_id\" [compareWith]=\"compareById\"\r\n                        [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\" appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }\"\r\n                        [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </div>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['primary_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['primary_contact_party_id'].errors &&\r\n                                f['primary_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityCategory']\" formControlName=\"group_code\"\r\n                        placeholder=\"Select Category\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">source</span>Source\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunitySource']\" formControlName=\"origin_type_code\"\r\n                        placeholder=\"Select Source\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">scale</span>Weighted Value\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"weighted_expected_net_amount\"\r\n                            placeholder=\"Weighted Value\" class=\"h-3rem w-full\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"weighted_expected_net_amount_currency_code\"\r\n                            optionLabel=\"label\" optionValue=\"value\" placeholder=\"Currency\" styleClass=\"h-3rem w-full\"\r\n                            [disabled]=\"currencies.length === 1\"></p-dropdown>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">lens</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityStatus']\" formControlName=\"life_cycle_status_code\"\r\n                        placeholder=\"Select Status\" [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['life_cycle_status_code'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['life_cycle_status_code'].errors &&\r\n                                f['life_cycle_status_code'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">trending_up</span>Probability\r\n                    </label>\r\n                    <input pInputText id=\"probability_percent\" type=\"text\" formControlName=\"probability_percent\"\r\n                        placeholder=\"Probability'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">business</span>Sales Organization\r\n                    </label>\r\n                    <input pInputText id=\"sales_organisation_id\" type=\"text\" formControlName=\"sales_organisation_id\"\r\n                        placeholder=\"Sales Organization'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">sell</span>Sales Unit\r\n                    </label>\r\n                    <input pInputText id=\"sales_unit_party_id\" type=\"text\" formControlName=\"sales_unit_party_id\"\r\n                        placeholder=\"Sales Unit\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Created Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_start_date\" [showButtonBar]=\"true\"\r\n                        dateFormat=\"yy-mm-dd\" placeholder=\"Created Date\" [showIcon]=\"true\"\r\n                        styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">help</span>Need help\r\n                    </label>\r\n                    <p-inputSwitch formControlName=\"need_help\" class=\"h-3rem w-full\"></p-inputSwitch>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n\r\n<div class=\"p-3 w-full bg-white border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n            <p-multiSelect [options]=\"NoteCols\" [(ngModel)]=\"selectedNoteColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"notedetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onNoteColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\" style=\"min-width: 70rem;\"\r\n                        (click)=\"customSort('note', notedetails, 'notes')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Note\r\n                            <i *ngIf=\"sortFieldNote === 'note'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderNote === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldNote !== 'note'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedNoteColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, notedetails, 'notes')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldNote === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderNote === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldNote !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td class=\"border-round-left-lg\" [innerHTML]=\"notes?.note || '-'\">\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedNoteColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedBy'\">\r\n                                    {{ notes?.updatedBy || '-'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AACtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICCHC,EALhB,CAAAC,cAAA,cAA6D,cAEV,cACnB,gBACmD,eACN;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAEvGF,EAFuG,CAAAG,YAAA,EAAM,EACnG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,cAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gCAE9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,yBACtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACX;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAC9C,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,2BACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,+BAEtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACc;IAE3EF,EAF2E,CAAAG,YAAA,EAAM,EACvE,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,6BACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,0BAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAA6C;IAE1GF,EAF0G,CAAAG,YAAA,EAAM,EACtG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAAsC;IAEnGF,EAFmG,CAAAG,YAAA,EAAM,EAC/F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,oBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAGZF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IAhN2DH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,cAAA,SAA0C;IAQ1CR,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,IAAA,SAC/C;IAQ+CT,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,uBAAA,cACrD;IASqDX,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAK,yBAAA,IAAAZ,EAAA,CAAAa,WAAA,SAAAP,MAAA,CAAAC,eAAA,CAAAK,yBAAA,sBAE/C;IASCZ,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAc,UAAA,gCAAAR,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAQ,gBAAA,kBAAAT,MAAA,CAAAC,eAAA,CAAAQ,gBAAA,CAAAC,UAAA,iBAAAhB,EAAA,CAAAiB,aAAA,CAA0F;IAEzFjB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAU,kBAAA,OAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAQ,gBAAA,kBAAAT,MAAA,CAAAC,eAAA,CAAAQ,gBAAA,CAAAG,YAAA,cACJ;IAUGlB,EAAA,CAAAI,SAAA,GAAyH;IAAzHJ,EAAA,CAAAc,UAAA,iCAAAR,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAY,wBAAA,kBAAAb,MAAA,CAAAC,eAAA,CAAAY,wBAAA,CAAAC,eAAA,kBAAAd,MAAA,CAAAC,eAAA,CAAAY,wBAAA,CAAAC,eAAA,qBAAAd,MAAA,CAAAC,eAAA,CAAAY,wBAAA,CAAAC,eAAA,IAAAJ,UAAA,iBAAAhB,EAAA,CAAAiB,aAAA,CAAyH;IAExHjB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAU,kBAAA,OAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAY,wBAAA,kBAAAb,MAAA,CAAAC,eAAA,CAAAY,wBAAA,CAAAD,YAAA,cACJ;IASiDlB,EAAA,CAAAI,SAAA,GACX;IADWJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAe,oBAAA,wBAAAf,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,UAAA,SACX;IAQWtB,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAgB,kBAAA,SAC/C;IAQ+CvB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAe,oBAAA,sBAAAf,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,gBAAA,cAErD;IAQqDxB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,4BAAA,cAErD;IAQqDzB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAe,oBAAA,sBAAAf,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,sBAAA,cAErD;IAQqD1B,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAoB,kBAAA,cAGrD;IASqD3B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAqB,oBAAA,cACrD;IAQqD5B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAsB,mBAAA,cACrD;IAQqD7B,EAAA,CAAAI,SAAA,GACc;IADdJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAuB,sBAAA,kBAAAxB,MAAA,CAAAC,eAAA,CAAAuB,sBAAA,CAAAZ,YAAA,SACc;IAQdlB,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAwB,qBAAA,cACrD;IAQqD/B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAyB,mBAAA,cACrD;IAQqDhC,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA0B,2BAAA,IAAAjC,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAA0B,2BAAA,8BAE/C;IAQ+CjC,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA2B,SAAA,IAAAlC,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAA2B,SAAA,8BAE/C;IAQ+ClC,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA4B,eAAA,SAA6C;IAQ7CnC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA6B,QAAA,SAAsC;IAQtCpC,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA8B,SAAA,sBACrD;;;;;IAeQrC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAsC,UAAA,IAAAC,2DAAA,kBAIQ;IAGZvC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,SAAAC,MAAA,IAAApC,MAAA,CAAAmC,CAAA,SAAAC,MAAA,aAID;;;;;IAsBD1C,EAAA,CAAAC,cAAA,UAII;IACAD,EAAA,CAAAE,MAAA,oCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8E;IAC1ED,EAAA,CAAAsC,UAAA,IAAAK,2DAAA,kBAII;IAGR3C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIL;IAJKJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,4BAAAC,MAAA,IAAApC,MAAA,CAAAmC,CAAA,4BAAAC,MAAA,aAIL;;;;;IAgCD1C,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,QAAAkC,OAAA,CAAA1B,YAAA,KAAyB;;;;;IAD1DlB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAsC,UAAA,IAAAO,oEAAA,mBAAgC;;;;IAD1B7C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAuC,OAAA,CAAAE,KAAA,CAAgB;IACf9C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAc,UAAA,SAAA8B,OAAA,CAAA1B,YAAA,CAAuB;;;;;IAIlClB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAwE;IACpED,EAAA,CAAAsC,UAAA,IAAAS,2DAAA,kBAIQ;IAGZ/C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,sBAAAC,MAAA,IAAApC,MAAA,CAAAmC,CAAA,sBAAAC,MAAA,aAID;;;;;IAqBG1C,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAU,kBAAA,QAAAsC,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5CjD,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAU,kBAAA,QAAAsC,OAAA,CAAAE,MAAA,KAAmB;;;;;IAF9ClD,EADJ,CAAAC,cAAA,cAA2C,WACjC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAsC,UAAA,IAAAa,oEAAA,mBAAyB,IAAAC,oEAAA,mBACC;IAC9BpD,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAqD,kBAAA,KAAAL,OAAA,CAAAF,KAAA,QAAAE,OAAA,CAAA9B,YAAA,KAAyC;IACxClB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAc,UAAA,SAAAkC,OAAA,CAAAC,KAAA,CAAgB;IAChBjD,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAc,UAAA,SAAAkC,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhClD,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAsC,UAAA,IAAAgB,2DAAA,kBAIQ;IAGZtD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,6BAAAC,MAAA,IAAApC,MAAA,CAAAmC,CAAA,6BAAAC,MAAA,aAID;;;;;IAqDL1C,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA6E;IACzED,EAAA,CAAAsC,UAAA,IAAAiB,2DAAA,kBAIQ;IAGZvD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,2BAAAC,MAAA,IAAApC,MAAA,CAAAmC,CAAA,2BAAAC,MAAA,aAID;;;;;;IApKL1C,EALpB,CAAAC,cAAA,eAA+D,cAClB,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,aACtE;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAwD,SAAA,iBACwF;IACxFxD,EAAA,CAAAsC,UAAA,KAAAmB,qDAAA,kBAA2D;IAUnEzD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAK9CD,EAJA,CAAAwD,SAAA,iBAEqF,sBAI/B;IACtDxD,EAAA,CAAAsC,UAAA,KAAAoB,qDAAA,kBAA8E;IAW1F1D,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,+BACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,sBACmG;IAE3GxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAC9E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAKuE;;IACnED,EAAA,CAAAsC,UAAA,KAAAqB,6DAAA,0BAA2C;IAI/C3D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAsC,UAAA,KAAAsB,qDAAA,kBAAwE;IAUhF5D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,wBAC7E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAKuE;;IACnED,EAAA,CAAAsC,UAAA,KAAAuB,6DAAA,0BAA2C;IAO/C7D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAsC,UAAA,KAAAwB,qDAAA,kBAA+E;IAUvF9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,sBAEa;IAErBxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC1E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,sBAEa;IAErBxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAI9CD,EAHA,CAAAwD,SAAA,iBACyD,sBAIH;IAGlExD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACpE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAwD,SAAA,sBAGa;IACbxD,EAAA,CAAAsC,UAAA,KAAAyB,qDAAA,kBAA6E;IAUrF/D,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,iBACuD;IAE/DxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,4BAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,kBAC8D;IAEtExD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,kBACqD;IAE7DxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,sBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,uBAE4C;IAEpDxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,mBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAwD,SAAA,0BAAiF;IAG7FxD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAEvB;IAArBD,EAAA,CAAAgE,UAAA,mBAAAC,yEAAA;MAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAoE,aAAA;MAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAS/D,MAAA,CAAAgE,QAAA,EAAU;IAAA,EAAC;IAEhCtE,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IAlOkBH,EAAA,CAAAc,UAAA,cAAAR,MAAA,CAAAiE,uBAAA,CAAqC;IASpBvE,EAAA,CAAAI,SAAA,IAA2D;IAA3DJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,SAAAC,MAAA,EAA2D;IAC/E1C,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,SAAAC,MAAA,CAAmC;IAqBjC1C,EAAA,CAAAI,SAAA,IAA8E;IAA9EJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,4BAAAC,MAAA,EAA8E;IAEtE1C,EAAA,CAAAI,SAAA,EAAsB;IAE9BJ,EAFQ,CAAAc,UAAA,YAAAR,MAAA,CAAAoE,UAAA,CAAsB,aAAApE,MAAA,CAAAoE,UAAA,CAAAC,MAAA,OAEM;IAClC3E,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,4BAAAC,MAAA,CAAsD;IAkBR1C,EAAA,CAAAI,SAAA,GAAsB;IACrCJ,EADe,CAAAc,UAAA,uBAAsB,kBACpB;IAetDd,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAA4E,UAAA,0DAAkE;IADlE5E,EAJkB,CAAAc,UAAA,UAAAd,EAAA,CAAA6E,WAAA,SAAAvE,MAAA,CAAAwE,SAAA,EAA2B,sBACxB,YAAAxE,MAAA,CAAAyE,cAAA,CAA2B,oBAAoB,gBAAAzE,MAAA,CAAA0E,WAAA,CACL,cAAA1E,MAAA,CAAA2E,aAAA,CAA4B,wBACpE,YAAAjF,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,sBAAAC,MAAA,EACiD;IAOtE1C,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,sBAAAC,MAAA,CAAgD;IAsBlD1C,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAA4E,UAAA,0DAAkE;IADlE5E,EAJkB,CAAAc,UAAA,UAAAd,EAAA,CAAA6E,WAAA,SAAAvE,MAAA,CAAA4E,SAAA,EAA2B,sBACxB,YAAA5E,MAAA,CAAA6E,cAAA,CAA2B,oBAAoB,gBAAA7E,MAAA,CAAA0E,WAAA,CACE,cAAA1E,MAAA,CAAA8E,aAAA,CAC3C,wBAAwB,wBAAwC,YAAApF,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,6BAAAC,MAAA,EACZ;IAU7E1C,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,6BAAAC,MAAA,CAAuD;IAgBjD1C,EAAA,CAAAI,SAAA,GAA4C;IACtBJ,EADtB,CAAAc,UAAA,YAAAR,MAAA,CAAA+E,SAAA,wBAA4C,+BACQ;IASpDrF,EAAA,CAAAI,SAAA,GAA0C;IACtBJ,EADpB,CAAAc,UAAA,YAAAR,MAAA,CAAA+E,SAAA,sBAA0C,+BACQ;IAc9CrF,EAAA,CAAAI,SAAA,GAAsB;IAE9BJ,EAFQ,CAAAc,UAAA,YAAAR,MAAA,CAAAoE,UAAA,CAAsB,aAAApE,MAAA,CAAAoE,UAAA,CAAAC,MAAA,OAEM;IAUhC3E,EAAA,CAAAI,SAAA,GAA0C;IAElDJ,EAFQ,CAAAc,UAAA,YAAAR,MAAA,CAAA+E,SAAA,sBAA0C,+BACQ,YAAArF,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,2BAAAC,MAAA,EACmB;IAE3E1C,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAkC,SAAA,IAAAlC,MAAA,CAAAmC,CAAA,2BAAAC,MAAA,CAAqD;IA4CD1C,EAAA,CAAAI,SAAA,IAAsB;IAC3BJ,EADK,CAAAc,UAAA,uBAAsB,kBACV;;;;;IA6C9Dd,EAAA,CAAAwD,SAAA,YAC0F;;;;IAAtFxD,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAgF,aAAA,yDAAiF;;;;;IACrFtF,EAAA,CAAAwD,SAAA,YAAgE;;;;;IAS5DxD,EAAA,CAAAwD,SAAA,YAC0F;;;;IAAtFxD,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAgF,aAAA,yDAAiF;;;;;IACrFtF,EAAA,CAAAwD,SAAA,YAAmE;;;;;;IAP/ExD,EAAA,CAAAuF,uBAAA,GAAsD;IAClDvF,EAAA,CAAAC,cAAA,aAC0D;IAAtDD,EAAA,CAAAgE,UAAA,mBAAAwB,0FAAA;MAAA,MAAAC,MAAA,GAAAzF,EAAA,CAAAkE,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAoE,aAAA;MAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAS/D,MAAA,CAAAsF,UAAA,CAAAH,MAAA,CAAAI,KAAA,EAAAvF,MAAA,CAAAwF,WAAA,EAAmC,OAAO,CAAC;IAAA,EAAC;IACrD9F,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,GACA;IAEAF,EAFA,CAAAsC,UAAA,IAAAyD,yEAAA,gBACsF,IAAAC,yEAAA,gBACvB;IAEvEhG,EADI,CAAAG,YAAA,EAAM,EACL;;;;;;IARDH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAc,UAAA,oBAAA2E,MAAA,CAAAI,KAAA,CAA6B;IAGzB7F,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAU,kBAAA,MAAA+E,MAAA,CAAAQ,MAAA,MACA;IAAIjG,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA4F,aAAA,KAAAT,MAAA,CAAAI,KAAA,CAAiC;IAEjC7F,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA4F,aAAA,KAAAT,MAAA,CAAAI,KAAA,CAAiC;;;;;;IAjBjD7F,EADJ,CAAAC,cAAA,SAAI,aAEuD;IAAnDD,EAAA,CAAAgE,UAAA,mBAAAmC,2EAAA;MAAAnG,EAAA,CAAAkE,aAAA,CAAAkC,GAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAoE,aAAA;MAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAS/D,MAAA,CAAAsF,UAAA,CAAW,MAAM,EAAAtF,MAAA,CAAAwF,WAAA,EAAe,OAAO,CAAC;IAAA,EAAC;IAClD9F,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,aACA;IAEAF,EAFA,CAAAsC,UAAA,IAAA+D,0DAAA,gBACsF,IAAAC,0DAAA,gBAC1B;IAEpEtG,EADI,CAAAG,YAAA,EAAM,EACL;IAELH,EAAA,CAAAsC,UAAA,IAAAiE,qEAAA,2BAAsD;IAWtDvG,EAAA,CAAAC,cAAA,aAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAC7CF,EAD6C,CAAAG,YAAA,EAAK,EAC7C;;;;IAlBWH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA4F,aAAA,YAA8B;IAE9BlG,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA4F,aAAA,YAA8B;IAIZlG,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAkG,mBAAA,CAAsB;;;;;IAsBxCxG,EAAA,CAAAuF,uBAAA,GAA0C;IACtCvF,EAAA,CAAAE,MAAA,GACJ;;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAa,WAAA,OAAA4F,QAAA,kBAAAA,QAAA,CAAAvE,SAAA,8BACJ;;;;;IAEAlC,EAAA,CAAAuF,uBAAA,GAA0C;IACtCvF,EAAA,CAAAE,MAAA,GACJ;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAU,kBAAA,OAAA+F,QAAA,kBAAAA,QAAA,CAAAC,SAAA,cACJ;;;;;IATZ1G,EAAA,CAAAuF,uBAAA,GAAsD;IAClDvF,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAuF,uBAAA,OAAqC;IAKjCvF,EAJA,CAAAsC,UAAA,IAAAqE,oFAAA,2BAA0C,IAAAC,oFAAA,2BAIA;;IAKlD5G,EAAA,CAAAG,YAAA,EAAK;;;;;IAVaH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAc,UAAA,aAAA+F,OAAA,CAAAhB,KAAA,CAAsB;IACjB7F,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAc,UAAA,6BAAyB;IAIzBd,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAc,UAAA,6BAAyB;;;;;;IAVxDd,EAAA,CAAAC,cAAA,aAA2B;IACvBD,EAAA,CAAAwD,SAAA,aACK;IACLxD,EAAA,CAAAsC,UAAA,IAAAwE,qEAAA,2BAAsD;IAelD9G,EADJ,CAAAC,cAAA,aAAkC,iBAEA;IAA1BD,EAAA,CAAAgE,UAAA,mBAAA+C,+EAAA;MAAA,MAAAN,QAAA,GAAAzG,EAAA,CAAAkE,aAAA,CAAA8C,GAAA,EAAArB,SAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAoE,aAAA;MAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAS/D,MAAA,CAAA2G,QAAA,CAAAR,QAAA,CAAe;IAAA,EAAC;IAACzG,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,iBAC8D;IAA1DD,EAAA,CAAAgE,UAAA,mBAAAkD,+EAAAC,MAAA;MAAA,MAAAV,QAAA,GAAAzG,EAAA,CAAAkE,aAAA,CAAA8C,GAAA,EAAArB,SAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAoE,aAAA;MAAS+C,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAApH,EAAA,CAAAqE,WAAA,CAAE/D,MAAA,CAAA+G,aAAA,CAAAZ,QAAA,CAAoB;IAAA,EAAE;IAErEzG,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAtBgCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAc,UAAA,eAAA2F,QAAA,kBAAAA,QAAA,CAAAa,IAAA,UAAAtH,EAAA,CAAAuH,cAAA,CAAgC;IAEnCvH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAkG,mBAAA,CAAsB;;;;;IAyBpDxG,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACnCF,EADmC,CAAAG,YAAA,EAAK,EACnC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACtDF,EADsD,CAAAG,YAAA,EAAK,EACtD;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAAsC,UAAA,IAAAkF,oDAAA,kBAA8C;IAClDxH,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAmH,KAAA,SAAA/E,MAAA,aAAsC;;;AD5gBhE,OAAM,MAAOgF,8BAA8B;EAqDzCC,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAwC,EACxCC,MAAc;IALd,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IA1DR,KAAAC,aAAa,GAAG,IAAI9I,OAAO,EAAQ;IACpC,KAAAmB,eAAe,GAAQ,IAAI;IAC3B,KAAAuF,WAAW,GAAQ,IAAI;IAEvB,KAAAf,cAAc,GAAG,KAAK;IACtB,KAAAE,aAAa,GAAG,IAAI7F,OAAO,EAAU;IAErC,KAAA+F,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIhG,OAAO,EAAU;IACpC,KAAA+I,cAAc,GAAQ,EAAE;IACzB,KAAA3F,SAAS,GAAG,KAAK;IACjB,KAAA4F,MAAM,GAAG,KAAK;IAEd,KAAA5H,cAAc,GAAW,EAAE;IAC3B,KAAA6H,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAA5D,UAAU,GAAG,CAAC;MAAE6D,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAAC;IAC7C,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAAtE,uBAAuB,GAAc,IAAI,CAACqD,WAAW,CAACkB,KAAK,CAAC;MACjErI,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAAC4J,QAAQ,CAAC,CAAC;MACjCC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC7J,UAAU,CAAC4J,QAAQ,CAAC,CAAC;MAC9CE,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAAC9J,UAAU,CAAC4J,QAAQ,CAAC,CAAC;MACrDvH,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBb,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAAC4J,QAAQ,CAAC,CAAC;MACpDG,qCAAqC,EAAE,CAAC,EAAE,CAAC;MAC3CzH,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClC0H,0CAA0C,EAAE,CAAC,EAAE,CAAC;MAChDlH,2BAA2B,EAAE,CAAC,EAAE,CAAC;MACjCrB,yBAAyB,EAAE,CAAC,EAAE,CAAC;MAC/Bc,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAAC4J,QAAQ,CAAC,CAAC;MACnDlH,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBP,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBS,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBK,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;IAEK,KAAA+G,QAAQ,GAAc,IAAI,CAACxB,WAAW,CAACkB,KAAK,CAAC;MAClDxB,IAAI,EAAE,CAAC,EAAE,EAAE,CAACnI,UAAU,CAAC4J,QAAQ,CAAC;KACjC,CAAC;IAEK,KAAA1D,SAAS,GAA0B;MACxCgE,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;IAWO,KAAAC,oBAAoB,GAAiB,EAAE;IAExC,KAAAC,QAAQ,GAAiB,CAC9B;MAAE5D,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAY,CAAE,EAC5C;MAAEJ,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAZ,aAAa,GAAW,CAAC;IAidzB,KAAAN,WAAW,GAAG,CAAC0E,CAAM,EAAEC,CAAM,KAAKD,CAAC,KAAKC,CAAC;EA3dtC;EAYHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,sBAAsB,CAAC;MACrE,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC/B,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,sBAAsB,CAAC;MACnD;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;IACD,IAAI,CAACA,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACD,IAAI,CAAC9F,uBAAuB,CAAC+F,GAAG,CAAC,mBAAmB,CAAC,EACjDC,YAAY,CAACC,IAAI,CACjBnL,SAAS,CAAC,IAAI,CAAC6I,aAAa,CAAC,EAC7BvI,GAAG,CAAE8K,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACvF,SAAS,GAAG1F,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACFtI,UAAU,CAAE8K,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACzF,SAAS,GAAG1F,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;MACxC,OAAO3I,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACAsL,SAAS,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAAClD,oBAAoB,CAACmD,WAAW,CAClCR,IAAI,CAACnL,SAAS,CAAC,IAAI,CAAC6I,aAAa,CAAC,CAAC,CACnC4C,SAAS,CAAEG,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAACzK,cAAc,GAAGyK,QAAQ,EAAEzK,cAAc;MAC9C,IAAI,CAACD,eAAe,GAAG0K,QAAQ;MAC/B,IAAI,CAACnF,WAAW,GAAGmF,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAAC3K,eAAe,EAAE;QACxB,IAAI,CAAC4K,iBAAiB,CAAC,IAAI,CAAC5K,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;IAEJ,IAAI,CAACiJ,oBAAoB,GAAG,IAAI,CAACC,QAAQ;EAC3C;EAEA,IAAIjD,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACgD,oBAAoB;EAClC;EAEA,IAAIhD,mBAAmBA,CAAC4E,GAAU;IAChC,IAAI,CAAC5B,oBAAoB,GAAG,IAAI,CAACC,QAAQ,CAAC4B,MAAM,CAAEC,GAAG,IACnDF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEAE,mBAAmBA,CAACC,KAAU;IAC5B,MAAMC,UAAU,GAAG,IAAI,CAACjC,QAAQ,CAACgC,KAAK,CAACE,SAAS,CAAC;IACjD,IAAI,CAAClC,QAAQ,CAACmC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACxC,IAAI,CAAClC,QAAQ,CAACmC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACtD;EAEA9F,UAAUA,CAACC,KAAa,EAAEiG,IAAW,EAAEC,IAAa;IAClD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAAC7F,aAAa,GAAGL,KAAK;MAC1B,IAAI,CAACP,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IAEAwG,IAAI,CAACE,IAAI,CAAC,CAACtC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMsC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACxC,CAAC,EAAE7D,KAAK,CAAC;MAC9C,MAAMsG,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACvC,CAAC,EAAE9D,KAAK,CAAC;MAE9C,IAAIuG,MAAM,GAAG,IAAI;MAEjB,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAAC7G,aAAa,GAAG8G,MAAM;IACpC,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAACJ,IAAS,EAAEjG,KAAa;IACvC,IAAI,CAACiG,IAAI,IAAI,CAACjG,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAACyG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOR,IAAI,CAACjG,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAI0G,MAAM,GAAG1G,KAAK,CAAC2G,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIhE,KAAK,GAAGsD,IAAI;MAChB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAC5H,MAAM,EAAE8H,CAAC,EAAE,EAAE;QACtC,IAAIjE,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAAC+D,MAAM,CAACE,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOjE,KAAK;IACd;EACF;EAEA6B,uBAAuBA,CAACqC,MAAc,EAAEX,IAAY;IAClD,IAAI,CAAClE,oBAAoB,CACtB8E,6BAA6B,CAACZ,IAAI,CAAC,CACnCjB,SAAS,CAAE8B,GAAQ,IAAI;MACtB,IAAI,CAACvH,SAAS,CAACqH,MAAM,CAAC,GACpBE,GAAG,EAAEd,IAAI,EAAEvM,GAAG,CAAEsN,IAAS,KAAM;QAC7BtE,KAAK,EAAEsE,IAAI,CAACC,WAAW;QACvBtE,KAAK,EAAEqE,IAAI,CAACE;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEA1L,oBAAoBA,CAAC2L,WAAmB,EAAExE,KAAa;IACrD,MAAMyE,IAAI,GAAG,IAAI,CAAC5H,SAAS,CAAC2H,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAAC3E,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOyE,IAAI,EAAE1E,KAAK,IAAIC,KAAK;EAC7B;EAEQuC,YAAYA,CAAA;IAClB,IAAI,CAACjG,SAAS,GAAGxF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;IAAE;IACzB,IAAI,CAAClD,aAAa,CAACuF,IAAI,CACrB1K,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACoF,cAAc,GAAG,IAAK,CAAC,EACvCrF,SAAS,CAAE0N,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACvF,oBAAoB,CAACyF,WAAW,CAACD,MAAM,CAAC,CAAC7C,IAAI,CACvDjL,GAAG,CAAE0L,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxCpL,UAAU,CAAEgL,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAOrL,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFO,QAAQ,CAAC,MAAO,IAAI,CAACgF,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQ2F,qBAAqBA,CAAC6C,IAAY;IACxC,IAAI,CAACrI,SAAS,GAAG,IAAI,CAACE,aAAa,CAACoF,IAAI,CACtC5K,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACwF,cAAc,GAAG,IAAK,CAAC,EACvCzF,SAAS,CAAE0N,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEE,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIH,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAACtF,iBAAiB,CAAC0F,kBAAkB,CAACH,MAAM,CAAC,CAAC7C,IAAI,CAC3DjL,GAAG,CAAE0L,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxCtL,GAAG,CAAE8N,QAAe,IAAI;QACtB,IAAI,CAACtI,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACFtF,UAAU,CAAEgL,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC1F,cAAc,GAAG,KAAK;QAC3B,OAAO3F,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEAyH,QAAQA,CAACK,IAAS;IAChB,IAAI,CAACmB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGvB,IAAI,EAAEtG,UAAU;IAClC,IAAI,CAACoI,QAAQ,CAACsE,UAAU,CAACpG,IAAI,CAAC;EAChC;EAEA6D,iBAAiBA,CAACH,WAAgB;IAChC,MAAM2C,WAAW,GAAG3C,WAAW,EAAEhC,iBAAiB,GAC9C;MACElG,KAAK,EAAEkI,WAAW,CAAChC,iBAAiB;MACpC9H,YAAY,EAAE8J,WAAW,CAACjK,gBAAgB,EAAEG,YAAY,IAAI;KAC7D,GACD,IAAI;IAER,MAAM0M,WAAW,GAAG5C,WAAW,EAAE/B,wBAAwB,GACrD;MACEnG,KAAK,EAAEkI,WAAW,CAAC/B,wBAAwB;MAC3C/H,YAAY,EACV8J,WAAW,CAAC7J,wBAAwB,EAAED,YAAY,IAAI;KACzD,GACD,IAAI;IACR,IAAI,CAAC2M,mBAAmB,GAAG;MACzBrN,cAAc,EAAEwK,WAAW,EAAExK,cAAc;MAC3CC,IAAI,EAAEuK,WAAW,EAAEvK,IAAI;MACvBa,UAAU,EAAE0J,WAAW,EAAE1J,UAAU;MACnCE,gBAAgB,EAAEwJ,WAAW,EAAExJ,gBAAgB;MAC/CK,mBAAmB,EAAEmJ,WAAW,EAAEnJ,mBAAmB;MACrDlB,uBAAuB,EAAEqK,WAAW,EAAErK,uBAAuB;MAC7DuI,qCAAqC,EACnC8B,WAAW,EAAErK,uBAAuB,GAAG,KAAK,GAAG,EAAE;MACnDsB,2BAA2B,EAAE+I,WAAW,EAAE/I,2BAA2B;MACrErB,yBAAyB,EAAEoK,WAAW,EAAEpK,yBAAyB;MACjEoI,iBAAiB,EAAE2E,WAAW;MAC9B1E,wBAAwB,EAAE2E,WAAW;MACrCnM,4BAA4B,EAAEuJ,WAAW,EAAEvJ,4BAA4B;MACvE0H,0CAA0C,EACxC6B,WAAW,EAAEvJ,4BAA4B,GAAG,KAAK,GAAG,EAAE;MACxDC,sBAAsB,EAAEsJ,WAAW,EAAEtJ,sBAAsB;MAC3DoM,kCAAkC,EAChC9C,WAAW,EAAE8C,kCAAkC;MACjD/L,qBAAqB,EAAEiJ,WAAW,EAAEjJ,qBAAqB;MACzDC,mBAAmB,EAAEgJ,WAAW,EAAEhJ,mBAAmB;MACrDK,SAAS,EAAE2I,WAAW,EAAE3I,SAAS,GAAG,IAAI,GAAG;KAC5C;IACD,IAAI,CAACgG,MAAM,GAAG2C,WAAW,CAAChK,UAAU;IACpC,IAAI,CAACuD,uBAAuB,CAACmJ,UAAU,CAAC,IAAI,CAACG,mBAAmB,CAAC;EACnE;EAEME,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACrF,aAAa,GAAG,IAAI;MACzBqF,KAAI,CAACvF,WAAW,GAAG,IAAI;MAEvB,IAAIuF,KAAI,CAAC5E,QAAQ,CAAC8E,OAAO,EAAE;QACzBF,KAAI,CAACvF,WAAW,GAAG,IAAI;QACvB;MACF;MAEAuF,KAAI,CAACpF,UAAU,GAAG,IAAI;MACtB,MAAMJ,KAAK,GAAG;QAAE,GAAGwF,KAAI,CAAC5E,QAAQ,CAACZ;MAAK,CAAE;MAExC,MAAMsD,IAAI,GAAG;QACXtL,cAAc,EAAEwN,KAAI,CAACxN,cAAc;QACnC8G,IAAI,EAAEkB,KAAK,EAAElB;OACd;MAED,IAAI0G,KAAI,CAACnF,UAAU,EAAE;QACnBmF,KAAI,CAACnG,oBAAoB,CACtBsG,UAAU,CAACH,KAAI,CAACnF,UAAU,EAAEiD,IAAI,CAAC,CACjCtB,IAAI,CAACnL,SAAS,CAAC2O,KAAI,CAAC9F,aAAa,CAAC,CAAC,CACnC4C,SAAS,CAAC;UACTsD,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACpF,UAAU,GAAG,KAAK;YACvBoF,KAAI,CAACvF,WAAW,GAAG,KAAK;YACxBuF,KAAI,CAAC5E,QAAQ,CAACiF,KAAK,EAAE;YACrBL,KAAI,CAACjG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF6D,KAAI,CAACnG,oBAAoB,CACtByG,kBAAkB,CAACN,KAAI,CAACxN,cAAc,CAAC,CACvCgK,IAAI,CAACnL,SAAS,CAAC2O,KAAI,CAAC9F,aAAa,CAAC,CAAC,CACnC4C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAG+B,GAAQ,IAAI;YAClBoB,KAAI,CAACpF,UAAU,GAAG,KAAK;YACvBoF,KAAI,CAACvF,WAAW,GAAG,IAAI;YACvBuF,KAAI,CAACjG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACL6D,KAAI,CAACnG,oBAAoB,CACtB0G,UAAU,CAACzC,IAAI,CAAC,CAChBtB,IAAI,CAACnL,SAAS,CAAC2O,KAAI,CAAC9F,aAAa,CAAC,CAAC,CACnC4C,SAAS,CAAC;UACTsD,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACpF,UAAU,GAAG,KAAK;YACvBoF,KAAI,CAACvF,WAAW,GAAG,KAAK;YACxBuF,KAAI,CAAC5E,QAAQ,CAACiF,KAAK,EAAE;YACrBL,KAAI,CAACjG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF6D,KAAI,CAACnG,oBAAoB,CACtByG,kBAAkB,CAACN,KAAI,CAACxN,cAAc,CAAC,CACvCgK,IAAI,CAACnL,SAAS,CAAC2O,KAAI,CAAC9F,aAAa,CAAC,CAAC,CACnC4C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAG+B,GAAQ,IAAI;YAClBoB,KAAI,CAACpF,UAAU,GAAG,KAAK;YACvBoF,KAAI,CAACvF,WAAW,GAAG,IAAI;YACvBuF,KAAI,CAACjG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEM7F,QAAQA,CAAA;IAAA,IAAAkK,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAAChM,SAAS,GAAG,IAAI;MAErB,IAAIgM,MAAI,CAACjK,uBAAuB,CAAC2J,OAAO,EAAE;QACxC;MACF;MAEAM,MAAI,CAACpG,MAAM,GAAG,IAAI;MAClB,MAAMI,KAAK,GAAG;QAAE,GAAGgG,MAAI,CAACjK,uBAAuB,CAACiE;MAAK,CAAE;MAEvD,MAAMsD,IAAI,GAAG;QACXrL,IAAI,EAAE+H,KAAK,EAAE/H,IAAI;QACjBE,uBAAuB,EAAE6H,KAAK,EAAE7H,uBAAuB;QACvDuI,qCAAqC,EAAEV,KAAK,EAAE7H,uBAAuB,GACjE,KAAK,GACL,EAAE;QACNC,yBAAyB,EAAE4H,KAAK,EAAE5H,yBAAyB,GACvD4N,MAAI,CAACC,UAAU,CAACjG,KAAK,CAAC5H,yBAAyB,CAAC,GAChD,IAAI;QACRoI,iBAAiB,EACfR,KAAK,EAAEQ,iBAAiB,EAAElG,KAAK,IAAI0F,KAAK,EAAEQ,iBAAiB;QAC7DC,wBAAwB,EACtBT,KAAK,EAAES,wBAAwB,EAAEnG,KAAK,IACtC0F,KAAK,EAAES,wBAAwB;QACjC3H,UAAU,EAAEkH,KAAK,EAAElH,UAAU;QAC7BE,gBAAgB,EAAEgH,KAAK,EAAEhH,gBAAgB;QACzCC,4BAA4B,EAAE+G,KAAK,EAAE/G,4BAA4B;QACjE0H,0CAA0C,EACxCX,KAAK,EAAE/G,4BAA4B,GAAG,KAAK,GAAG,EAAE;QAClDC,sBAAsB,EAAE8G,KAAK,EAAE9G,sBAAsB;QACrDG,mBAAmB,EAAE2G,KAAK,EAAE3G,mBAAmB;QAC/CiM,kCAAkC,EAChCtF,KAAK,EAAEsF,kCAAkC;QAC3C/L,qBAAqB,EAAEyG,KAAK,EAAEzG,qBAAqB;QACnDC,mBAAmB,EAAEwG,KAAK,EAAExG,mBAAmB;QAC/CC,2BAA2B,EAAEuG,KAAK,EAAEvG,2BAA2B,GAC3DuM,MAAI,CAACC,UAAU,CAACjG,KAAK,CAACvG,2BAA2B,CAAC,GAClD,IAAI;QACRI,SAAS,EAAEmG,KAAK,EAAEnG;OACnB;MAEDmM,MAAI,CAAC3G,oBAAoB,CACtB6G,iBAAiB,CAACF,MAAI,CAACnG,MAAM,EAAEyD,IAAI,CAAC,CACpCtB,IAAI,CAACnL,SAAS,CAACmP,MAAI,CAACtG,aAAa,CAAC,CAAC,CACnC4C,SAAS,CAAC;QACT6D,IAAI,EAAG1D,QAAa,IAAI;UACtBuD,MAAI,CAACzG,cAAc,CAACkC,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFqE,MAAI,CAAC3G,oBAAoB,CACtByG,kBAAkB,CAACE,MAAI,CAAChO,cAAc,CAAC,CACvCgK,IAAI,CAACnL,SAAS,CAACmP,MAAI,CAACtG,aAAa,CAAC,CAAC,CACnC4C,SAAS,EAAE;UACd0D,MAAI,CAAClG,UAAU,GAAG,KAAK;QACzB,CAAC;QACDuC,KAAK,EAAG+B,GAAQ,IAAI;UAClB4B,MAAI,CAACpG,MAAM,GAAG,KAAK;UACnBoG,MAAI,CAACzG,cAAc,CAACkC,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAsE,UAAUA,CAACG,IAAS;IAClB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,CAAC,GAAGD,IAAI,YAAYE,IAAI,GAAGF,IAAI,GAAG,IAAIE,IAAI,CAACF,IAAI,CAAC;IACtD,IAAIG,KAAK,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGJ,CAAC,CAACK,WAAW,EAAE;IAC5B,MAAMC,EAAE,GAAGC,MAAM,CAACP,CAAC,CAACQ,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMC,EAAE,GAAGH,MAAM,CAACP,CAAC,CAACW,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/C,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEAlI,aAAaA,CAAC4F,IAAS;IACrB,IAAI,CAACjF,mBAAmB,CAACyH,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEzJ,MAAM,EAAE,SAAS;MACjB0J,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC5C,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA4C,MAAMA,CAAC5C,IAAS;IACd,IAAI,CAACpF,oBAAoB,CACtBiI,UAAU,CAAC7C,IAAI,CAACjM,UAAU,CAAC,CAC3BwJ,IAAI,CAACnL,SAAS,CAAC,IAAI,CAAC6I,aAAa,CAAC,CAAC,CACnC4C,SAAS,CAAC;MACT6D,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC5G,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACtC,oBAAoB,CACtByG,kBAAkB,CAAC,IAAI,CAAC9N,cAAc,CAAC,CACvCgK,IAAI,CAACnL,SAAS,CAAC,IAAI,CAAC6I,aAAa,CAAC,CAAC,CACnC4C,SAAS,EAAE;MAChB,CAAC;MACDD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9C,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA4F,SAASA,CAACC,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAC9H,YAAY,GAAG8H,QAAQ;IAC5B,IAAI,CAAC/H,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACS,QAAQ,CAACiF,KAAK,EAAE;EACvB;EAEA,IAAI5G,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC2B,QAAQ,CAACqH,QAAQ;EAC/B;EAEA,IAAIhO,CAACA,CAAA;IACH,OAAO,IAAI,CAAC8B,uBAAuB,CAACkM,QAAQ;EAC9C;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACpI,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAqI,OAAOA,CAAA;IACL,IAAI,CAACnO,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+B,uBAAuB,CAAC8J,KAAK,EAAE;EACtC;EAIAuC,WAAWA,CAAA;IACT,IAAI,CAAC1I,aAAa,CAACyG,IAAI,EAAE;IACzB,IAAI,CAACzG,aAAa,CAACkG,QAAQ,EAAE;EAC/B;;;uBA5hBW1G,8BAA8B,EAAA1H,EAAA,CAAA6Q,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/Q,EAAA,CAAA6Q,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAjR,EAAA,CAAA6Q,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAnR,EAAA,CAAA6Q,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArR,EAAA,CAAA6Q,iBAAA,CAAAO,EAAA,CAAAE,mBAAA,GAAAtR,EAAA,CAAA6Q,iBAAA,CAAAU,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9B9J,8BAA8B;MAAA+J,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBnC/R,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5DH,EAAA,CAAAC,cAAA,kBACyG;UAA1CD,EAAA,CAAAgE,UAAA,mBAAAiO,kEAAA;YAAA,OAASD,GAAA,CAAAtB,UAAA,EAAY;UAAA,EAAC;UACzF1Q,EAFI,CAAAG,YAAA,EACyG,EACvG;UAyNNH,EAxNA,CAAAsC,UAAA,IAAA4P,6CAAA,oBAA6D,IAAAC,8CAAA,qBAwNE;UAmOnEnS,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,aAA4D,aACwC,YAC7C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAErDH,EADJ,CAAAC,cAAA,cAA2C,mBAEoB;UADrCD,EAAA,CAAAgE,UAAA,mBAAAoO,mEAAA;YAAA,OAASJ,GAAA,CAAAzB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAAnDvQ,EAAA,CAAAG,YAAA,EAC2D;UAC3DH,EAAA,CAAAC,cAAA,yBAE+I;UAF3GD,EAAA,CAAAqS,gBAAA,2BAAAC,gFAAAnL,MAAA;YAAAnH,EAAA,CAAAuS,kBAAA,CAAAP,GAAA,CAAAxL,mBAAA,EAAAW,MAAA,MAAA6K,GAAA,CAAAxL,mBAAA,GAAAW,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAK7EnH,EAFQ,CAAAG,YAAA,EAAgB,EACd,EACJ;UAIFH,EAFJ,CAAAC,cAAA,eAAuB,mBAIoC;UADHD,EAAA,CAAAgE,UAAA,0BAAAwO,yEAAArL,MAAA;YAAA,OAAgB6K,GAAA,CAAAxG,mBAAA,CAAArE,MAAA,CAA2B;UAAA,EAAC;UA8D5FnH,EA3DA,CAAAsC,UAAA,KAAAmQ,sDAAA,0BAAgC,KAAAC,sDAAA,0BA2BQ,KAAAC,sDAAA,0BA2BF,KAAAC,sDAAA,0BAKD;UAOjD5S,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAAqS,gBAAA,2BAAAQ,2EAAA1L,MAAA;YAAAnH,EAAA,CAAAuS,kBAAA,CAAAP,GAAA,CAAAvJ,WAAA,EAAAtB,MAAA,MAAA6K,GAAA,CAAAvJ,WAAA,GAAAtB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9CnH,EAAA,CAAAsC,UAAA,KAAAwQ,sDAAA,0BAAgC;UAMxB9S,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAAwD,SAAA,oBAC0E;UAC1ExD,EAAA,CAAAsC,UAAA,KAAAyQ,8CAAA,kBACmE;UAI3E/S,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAAgE,UAAA,mBAAAgP,iEAAA;YAAA,OAAAhB,GAAA,CAAAvJ,WAAA,GAAuB,KAAK;UAAA,EAAC;UAACzI,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAAgE,UAAA,mBAAAiP,iEAAA;YAAA,OAASjB,GAAA,CAAAjE,YAAA,EAAc;UAAA,EAAC;UAIxC/N,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UAhjBOH,EAAA,CAAAI,SAAA,GAAuC;UACqCJ,EAD5E,CAAAc,UAAA,UAAAkR,GAAA,CAAA1J,UAAA,oBAAuC,UAAA0J,GAAA,CAAA1J,UAAA,uBAA2C,2CAC9B,iBAAwC;UAEpGtI,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAc,UAAA,UAAAkR,GAAA,CAAA1J,UAAA,CAAiB;UAwNhBtI,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAc,UAAA,SAAAkR,GAAA,CAAA1J,UAAA,CAAgB;UA0OXtI,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAc,UAAA,oCAAmC,iBAAiB;UACzCd,EAAA,CAAAI,SAAA,EAAoB;UAApBJ,EAAA,CAAAc,UAAA,YAAAkR,GAAA,CAAAvI,QAAA,CAAoB;UAACzJ,EAAA,CAAAkT,gBAAA,YAAAlB,GAAA,CAAAxL,mBAAA,CAAiC;UAEjExG,EAAA,CAAAc,UAAA,2IAA0I;UAOzId,EAAA,CAAAI,SAAA,GAAqB;UACNJ,EADf,CAAAc,UAAA,UAAAkR,GAAA,CAAAlM,WAAA,CAAqB,YAA4B,mBAAuC,oBAC1E,4BAA4B;UAsER9F,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmT,UAAA,CAAAnT,EAAA,CAAAoT,eAAA,KAAAC,GAAA,EAA4B;UAArErT,EAAA,CAAAc,UAAA,eAAc;UAACd,EAAA,CAAAkT,gBAAA,YAAAlB,GAAA,CAAAvJ,WAAA,CAAyB;UAAmDzI,EAArB,CAAAc,UAAA,qBAAoB,oBAAoB;UAM9Gd,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAc,UAAA,cAAAkR,GAAA,CAAA5I,QAAA,CAAsB;UAGkDpJ,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAmT,UAAA,CAAAnT,EAAA,CAAAoT,eAAA,KAAAE,GAAA,EAA6B;UAC3FtT,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAuN,GAAA,CAAArJ,aAAA,IAAAqJ,GAAA,CAAAvK,KAAA,SAAA/E,MAAA,EAAmE;UACjE1C,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAc,UAAA,SAAAkR,GAAA,CAAArJ,aAAA,IAAAqJ,GAAA,CAAAvK,KAAA,SAAA/E,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}