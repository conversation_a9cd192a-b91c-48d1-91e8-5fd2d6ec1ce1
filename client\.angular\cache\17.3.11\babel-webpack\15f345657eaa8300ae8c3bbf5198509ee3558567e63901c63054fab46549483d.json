{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../account.service\";\nimport * as i3 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/tooltip\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/table\";\nimport * as i9 from \"primeng/calendar\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dialog\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction AccountOpportunitiesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 45)(2, \"div\", 46);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 48)(6, \"div\", 46);\n    i0.ɵɵtext(7, \" Owner \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 50)(10, \"div\", 46);\n    i0.ɵɵtext(11, \" Status \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 52)(14, \"div\", 46);\n    i0.ɵɵtext(15, \" Last Updated Date \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 54)(18, \"div\", 46);\n    i0.ɵɵtext(19, \" Last Updated By \");\n    i0.ɵɵelement(20, \"p-sortIcon\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 52)(22, \"div\", 46);\n    i0.ɵɵtext(23, \" Close Date \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\", 56)(26, \"div\", 46);\n    i0.ɵɵtext(27, \" Opportunity ID \");\n    i0.ɵɵelement(28, \"p-sortIcon\", 57);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const opportunity_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", opportunity_r1.name || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.business_partner_owner == null ? null : opportunity_r1.business_partner_owner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"opportunityStatus\", opportunity_r1 == null ? null : opportunity_r1.life_cycle_status_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.expected_revenue_end_date) ? i0.ɵɵpipeBind2(9, 7, opportunity_r1 == null ? null : opportunity_r1.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.last_changed_by) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.expected_revenue_end_date) ? i0.ɵɵpipeBind2(14, 10, opportunity_r1 == null ? null : opportunity_r1.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.opportunity_id) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 58);\n    i0.ɵɵtext(2, \" No opportunities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 58);\n    i0.ɵɵtext(2, \" Loading opportunities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Opportunities\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_23_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_39_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AccountOpportunitiesComponent_ng_template_39_span_2_Template, 2, 1, \"span\", 60);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction AccountOpportunitiesComponent_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_40_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_55_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_55_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"expected_revenue_amount\"].errors && ctx_r1.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_76_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_76_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_102_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AccountOpportunitiesComponent_ng_template_102_span_2_Template, 2, 1, \"span\", 60);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Owner is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_103_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_employee_responsible_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_112_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_112_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport class AccountOpportunitiesComponent {\n  constructor(formBuilder, accountservice, opportunitiesservice, messageservice) {\n    this.formBuilder = formBuilder;\n    this.accountservice = accountservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.opportunitiesdetails = null;\n    this.bp_id = '';\n    this.account_id = '';\n    this.documentId = '';\n    this.position = 'right';\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.visible = false;\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n    this.OpportunityForm = this.formBuilder.group({\n      name: ['', [Validators.required]],\n      primary_contact_party_id: ['', [Validators.required]],\n      origin_type_code: [''],\n      expected_revenue_amount: ['', [Validators.required]],\n      expected_revenue_start_date: [''],\n      expected_revenue_end_date: [''],\n      life_cycle_status_code: ['', [Validators.required]],\n      probability_percent: [''],\n      group_code: [''],\n      main_employee_responsible_party_id: ['', [Validators.required]],\n      note: ['', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.loadContacts();\n    this.loadEmployees();\n    this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response.bp_id;\n        this.documentId = response?.documentId;\n        this.account_id = response.bp_id;\n        if (this.bp_id) {\n          this.opportunitiesservice.getOpportunity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n            next: opportunityResponse => {\n              if (Array.isArray(opportunityResponse.data)) {\n                this.opportunitiesdetails = opportunityResponse.data.slice(0, 10);\n              } else {\n                this.opportunitiesdetails = [];\n              }\n            },\n            error: error => {\n              console.error('Error fetching Opportunities:', error);\n            }\n          });\n        }\n      }\n    });\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Emit default options first\n    this.contactInput$.pipe(debounceTime(300),\n    // Prevent rapid requests on fast typing\n    distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$eq]': 'BUP001',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response || []), tap(() => this.contactLoading = false), catchError(error => {\n        console.error('Contact loading failed', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadEmployees() {\n    this.employees$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.employeeInput$.pipe(debounceTime(300),\n    // Debounce user input to avoid spamming API\n    distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response || []), tap(() => this.employeeLoading = false), catchError(error => {\n        console.error('Employee loading failed', error);\n        this.employeeLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.OpportunityForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.OpportunityForm.value\n      };\n      const data = {\n        name: value?.name,\n        prospect_party_id: _this.account_id,\n        primary_contact_party_id: value?.primary_contact_party_id,\n        origin_type_code: value?.origin_type_code,\n        expected_revenue_amount: value?.expected_revenue_amount,\n        expected_revenue_start_date: value?.expected_revenue_start_date ? _this.formatDate(value.expected_revenue_start_date) : null,\n        expected_revenue_end_date: value?.expected_revenue_end_date ? _this.formatDate(value.expected_revenue_end_date) : null,\n        life_cycle_status_code: value?.life_cycle_status_code,\n        probability_percent: value?.probability_percent,\n        group_code: value?.group_code,\n        main_employee_responsible_party_id: value?.main_employee_responsible_party_id,\n        note: value?.note\n      };\n      _this.opportunitiesservice.createOpportunity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.saving = false;\n          _this.visible = false;\n          _this.OpportunityForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Opportunities Added successfully!.'\n          });\n          _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: res => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.OpportunityForm.controls;\n  }\n  showDialog(position) {\n    this.position = position;\n    this.visible = true;\n    this.submitted = false;\n    this.OpportunityForm.reset();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountOpportunitiesComponent_Factory(t) {\n      return new (t || AccountOpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountService), i0.ɵɵdirectiveInject(i3.OpportunitiesService), i0.ɵɵdirectiveInject(i4.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountOpportunitiesComponent,\n      selectors: [[\"app-account-opportunities\"]],\n      decls: 116,\n      vars: 61,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"d-grid\", \"gap-3\", \"text-base\"], [1, \"flex-1\"], [\"for\", \"Name\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"readonly\", \"\", \"pTooltip\", \"Account ID\", 1, \"h-3rem\", \"w-full\", 3, \"value\"], [\"for\", \"Primary Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"for\", \"Source\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select a Source\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Expected Value\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"expected_revenue_amount\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Create Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Expected Decision Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Probability\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Owner\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_employee_responsible_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"p-2\", \"border-1\", \"border-round\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pSortableColumn\", \"name\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"name\"], [\"pSortableColumn\", \"business_partner_owner.bp_full_name\"], [\"field\", \"business_partner_owner.bp_full_name\"], [\"pSortableColumn\", \"life_cycle_status_code\"], [\"field\", \"life_cycle_status_code\"], [\"pSortableColumn\", \"expected_revenue_end_date\"], [\"field\", \"expected_revenue_end_date\"], [\"pSortableColumn\", \"last_changed_by\"], [\"field\", \"last_changed_by\"], [\"pSortableColumn\", \"opportunity_id\"], [\"field\", \"opportunity_id\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"p-error\"], [4, \"ngIf\"]],\n      template: function AccountOpportunitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Opportunities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_p_button_click_4_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, AccountOpportunitiesComponent_ng_template_7_Template, 29, 0, \"ng-template\", 6)(8, AccountOpportunitiesComponent_ng_template_8_Template, 17, 13, \"ng-template\", 7)(9, AccountOpportunitiesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, AccountOpportunitiesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"p-dialog\", 10);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AccountOpportunitiesComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(12, AccountOpportunitiesComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n          i0.ɵɵelementStart(13, \"form\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"label\", 14)(17, \"span\", 15);\n          i0.ɵɵtext(18, \"badge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \"Name \");\n          i0.ɵɵelementStart(20, \"span\", 16);\n          i0.ɵɵtext(21, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(22, \"input\", 17);\n          i0.ɵɵtemplate(23, AccountOpportunitiesComponent_div_23_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 19)(26, \"span\", 15);\n          i0.ɵɵtext(27, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \"Account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 13)(31, \"label\", 21)(32, \"span\", 15);\n          i0.ɵɵtext(33, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \"Primary Contact \");\n          i0.ɵɵelementStart(35, \"span\", 16);\n          i0.ɵɵtext(36, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"ng-select\", 22);\n          i0.ɵɵpipe(38, \"async\");\n          i0.ɵɵtemplate(39, AccountOpportunitiesComponent_ng_template_39_Template, 3, 2, \"ng-template\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, AccountOpportunitiesComponent_div_40_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 13)(42, \"label\", 24)(43, \"span\", 15);\n          i0.ɵɵtext(44, \"source\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \"Source \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"p-dropdown\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 13)(48, \"label\", 26)(49, \"span\", 15);\n          i0.ɵɵtext(50, \"show_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(51, \"Expected Value \");\n          i0.ɵɵelementStart(52, \"span\", 16);\n          i0.ɵɵtext(53, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(54, \"input\", 27);\n          i0.ɵɵtemplate(55, AccountOpportunitiesComponent_div_55_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 13)(57, \"label\", 28)(58, \"span\", 15);\n          i0.ɵɵtext(59, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \"Create Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(61, \"p-calendar\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 13)(63, \"label\", 30)(64, \"span\", 15);\n          i0.ɵɵtext(65, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \"Expected Decision Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(67, \"p-calendar\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 13)(69, \"label\", 32)(70, \"span\", 15);\n          i0.ɵɵtext(71, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(72, \"Status \");\n          i0.ɵɵelementStart(73, \"span\", 16);\n          i0.ɵɵtext(74, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(75, \"p-dropdown\", 33);\n          i0.ɵɵtemplate(76, AccountOpportunitiesComponent_div_76_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 13)(78, \"label\", 34)(79, \"span\", 15);\n          i0.ɵɵtext(80, \"percent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(81, \"Probability \");\n          i0.ɵɵelementStart(82, \"span\", 16);\n          i0.ɵɵtext(83, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(84, \"input\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"div\", 13)(86, \"label\", 36)(87, \"span\", 15);\n          i0.ɵɵtext(88, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(89, \"Category \");\n          i0.ɵɵelementStart(90, \"span\", 16);\n          i0.ɵɵtext(91, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(92, \"p-dropdown\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 13)(94, \"label\", 38)(95, \"span\", 15);\n          i0.ɵɵtext(96, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(97, \"Owner \");\n          i0.ɵɵelementStart(98, \"span\", 16);\n          i0.ɵɵtext(99, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"ng-select\", 39);\n          i0.ɵɵpipe(101, \"async\");\n          i0.ɵɵtemplate(102, AccountOpportunitiesComponent_ng_template_102_Template, 3, 2, \"ng-template\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(103, AccountOpportunitiesComponent_div_103_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 13)(105, \"label\", 40)(106, \"span\", 15);\n          i0.ɵɵtext(107, \"notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(108, \"Notes \");\n          i0.ɵɵelementStart(109, \"span\", 16);\n          i0.ɵɵtext(110, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(111, \"textarea\", 41);\n          i0.ɵɵtemplate(112, AccountOpportunitiesComponent_div_112_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 42)(114, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_114_listener() {\n            return ctx.visible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_115_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.opportunitiesdetails)(\"rows\", 8)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(48, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.OpportunityForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c1, ctx.submitted && ctx.f[\"name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"name\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.account_id);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(38, 44, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(51, _c1, ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunitySource\"]);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c1, ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(55, _c1, ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityCategory\"]);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(101, 46, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(57, _c1, ctx.submitted && ctx.f[\"main_employee_responsible_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_employee_responsible_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c1, ctx.submitted && ctx.f[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i6.Tooltip, i4.PrimeTemplate, i7.Dropdown, i8.Table, i8.SortableColumn, i8.SortIcon, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i9.Calendar, i10.ButtonDirective, i10.Button, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.InputText, i13.Dialog, i5.AsyncPipe, i5.DatePipe],\n      styles: [\".opportunity-popup .p-dialog.p-component.p-dialog-resizable {\\n  width: calc(100vw - 490px) !important;\\n}\\n  .opportunity-popup .field {\\n  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWNjb3VudC9hY2NvdW50LWRldGFpbHMvYWNjb3VudC1vcHBvcnR1bml0aWVzL2FjY291bnQtb3Bwb3J0dW5pdGllcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFUTtFQUNJLHFDQUFBO0FBRFo7QUFJUTtFQUNJLDREQUFBO0FBRloiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLm9wcG9ydHVuaXR5LXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cucC1jb21wb25lbnQucC1kaWFsb2ctcmVzaXphYmxlIHtcclxuICAgICAgICAgICAgd2lkdGg6IGNhbGMoMTAwdncgLSA0OTBweCkgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5maWVsZCB7XHJcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDM2MHB4LCAxZnIpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "concat", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "catchError", "debounceTime", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "opportunity_r1", "name", "business_partner_owner", "bp_full_name", "ctx_r1", "getLabelFromDropdown", "life_cycle_status_code", "expected_revenue_end_date", "ɵɵpipeBind2", "last_changed_by", "opportunity_id", "ɵɵtemplate", "AccountOpportunitiesComponent_div_23_div_1_Template", "ɵɵproperty", "submitted", "f", "errors", "item_r3", "AccountOpportunitiesComponent_ng_template_39_span_2_Template", "ɵɵtextInterpolate", "bp_id", "AccountOpportunitiesComponent_div_40_div_1_Template", "AccountOpportunitiesComponent_div_55_div_1_Template", "AccountOpportunitiesComponent_div_76_div_1_Template", "item_r4", "AccountOpportunitiesComponent_ng_template_102_span_2_Template", "AccountOpportunitiesComponent_div_103_div_1_Template", "AccountOpportunitiesComponent_div_112_div_1_Template", "AccountOpportunitiesComponent", "constructor", "formBuilder", "accountservice", "opportunitiesservice", "messageservice", "unsubscribe$", "opportunitiesdetails", "account_id", "documentId", "position", "contactLoading", "contactInput$", "employeeLoading", "employeeInput$", "defaultOptions", "saving", "visible", "dropdowns", "opportunityCategory", "opportunityStatus", "opportunitySource", "OpportunityForm", "group", "required", "primary_contact_party_id", "origin_type_code", "expected_revenue_amount", "expected_revenue_start_date", "probability_percent", "group_code", "main_employee_responsible_party_id", "note", "ngOnInit", "loadContacts", "loadEmployees", "loadOpportunityDropDown", "account", "pipe", "subscribe", "response", "getOpportunity", "next", "opportunityResponse", "Array", "isArray", "data", "slice", "error", "console", "target", "type", "getOpportunityDropdownOptions", "res", "attr", "label", "description", "value", "code", "dropdownKey", "item", "find", "opt", "contacts$", "term", "params", "getPartners", "employees$", "onSubmit", "_this", "_asyncToGenerator", "invalid", "prospect_party_id", "formatDate", "createOpportunity", "reset", "add", "severity", "detail", "getAccountByID", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showDialog", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AccountService", "i3", "OpportunitiesService", "i4", "MessageService", "selectors", "decls", "vars", "consts", "template", "AccountOpportunitiesComponent_Template", "rf", "ctx", "ɵɵlistener", "AccountOpportunitiesComponent_Template_p_button_click_4_listener", "AccountOpportunitiesComponent_ng_template_7_Template", "AccountOpportunitiesComponent_ng_template_8_Template", "AccountOpportunitiesComponent_ng_template_9_Template", "AccountOpportunitiesComponent_ng_template_10_Template", "ɵɵtwoWayListener", "AccountOpportunitiesComponent_Template_p_dialog_visibleChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "AccountOpportunitiesComponent_ng_template_12_Template", "AccountOpportunitiesComponent_div_23_Template", "AccountOpportunitiesComponent_ng_template_39_Template", "AccountOpportunitiesComponent_div_40_Template", "AccountOpportunitiesComponent_div_55_Template", "AccountOpportunitiesComponent_div_76_Template", "AccountOpportunitiesComponent_ng_template_102_Template", "AccountOpportunitiesComponent_div_103_Template", "AccountOpportunitiesComponent_div_112_Template", "AccountOpportunitiesComponent_Template_button_click_114_listener", "AccountOpportunitiesComponent_Template_button_click_115_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c1", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\nimport { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  debounceTime,\r\n} from 'rxjs/operators';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-account-opportunities',\r\n  templateUrl: './account-opportunities.component.html',\r\n  styleUrl: './account-opportunities.component.scss',\r\n})\r\nexport class AccountOpportunitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public opportunitiesdetails: any = null;\r\n  public bp_id: string = '';\r\n  public account_id: string = '';\r\n  public documentId: string = '';\r\n  public position: string = 'right';\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public visible: boolean = false;\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  public OpportunityForm: FormGroup = this.formBuilder.group({\r\n    name: ['', [Validators.required]],\r\n    primary_contact_party_id: ['', [Validators.required]],\r\n    origin_type_code: [''],\r\n    expected_revenue_amount: ['', [Validators.required]],\r\n    expected_revenue_start_date: [''],\r\n    expected_revenue_end_date: [''],\r\n    life_cycle_status_code: ['', [Validators.required]],\r\n    probability_percent: [''],\r\n    group_code: [''],\r\n    main_employee_responsible_party_id: ['', [Validators.required]],\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private accountservice: AccountService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadContacts();\r\n    this.loadEmployees();\r\n    this.loadOpportunityDropDown(\r\n      'opportunityCategory',\r\n      'CRM_OPPORTUNITY_GROUP'\r\n    );\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.loadOpportunityDropDown(\r\n      'opportunitySource',\r\n      'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n    );\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response.bp_id;\r\n          this.documentId = response?.documentId;\r\n          this.account_id = response.bp_id;\r\n          if (this.bp_id) {\r\n            this.opportunitiesservice\r\n              .getOpportunity(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe({\r\n                next: (opportunityResponse: any) => {\r\n                  if (Array.isArray(opportunityResponse.data)) {\r\n                    this.opportunitiesdetails = opportunityResponse.data.slice(\r\n                      0,\r\n                      10\r\n                    );\r\n                  } else {\r\n                    this.opportunitiesdetails = [];\r\n                  }\r\n                },\r\n                error: (error: any) => {\r\n                  console.error('Error fetching Opportunities:', error);\r\n                },\r\n              });\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  private loadContacts(): void {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Emit default options first\r\n      this.contactInput$.pipe(\r\n        debounceTime(300), // Prevent rapid requests on fast typing\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$eq]': 'BUP001',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response || []),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              console.error('Contact loading failed', error);\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadEmployees(): void {\r\n    this.employees$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.employeeInput$.pipe(\r\n        debounceTime(300), // Debounce user input to avoid spamming API\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$eq]': 'BUP003',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response || []),\r\n            tap(() => (this.employeeLoading = false)),\r\n            catchError((error) => {\r\n              console.error('Employee loading failed', error);\r\n              this.employeeLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.OpportunityForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.OpportunityForm.value };\r\n\r\n    const data = {\r\n      name: value?.name,\r\n      prospect_party_id: this.account_id,\r\n      primary_contact_party_id: value?.primary_contact_party_id,\r\n      origin_type_code: value?.origin_type_code,\r\n      expected_revenue_amount: value?.expected_revenue_amount,\r\n      expected_revenue_start_date: value?.expected_revenue_start_date\r\n        ? this.formatDate(value.expected_revenue_start_date)\r\n        : null,\r\n      expected_revenue_end_date: value?.expected_revenue_end_date\r\n        ? this.formatDate(value.expected_revenue_end_date)\r\n        : null,\r\n      life_cycle_status_code: value?.life_cycle_status_code,\r\n      probability_percent: value?.probability_percent,\r\n      group_code: value?.group_code,\r\n      main_employee_responsible_party_id:\r\n        value?.main_employee_responsible_party_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .createOpportunity(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.saving = false;\r\n          this.visible = false;\r\n          this.OpportunityForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Opportunities Added successfully!.',\r\n          });\r\n          this.accountservice\r\n            .getAccountByID(this.documentId)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.OpportunityForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.position = position;\r\n    this.visible = true;\r\n    this.submitted = false;\r\n    this.OpportunityForm.reset();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Opportunities</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" (click)=\"showDialog('right')\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"opportunitiesdetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Name\r\n                            <p-sortIcon field=\"name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"business_partner_owner.bp_full_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Owner\r\n                            <p-sortIcon field=\"business_partner_owner.bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"life_cycle_status_code\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Status\r\n                            <p-sortIcon field=\"life_cycle_status_code\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"expected_revenue_end_date\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Last Updated Date\r\n                            <p-sortIcon field=\"expected_revenue_end_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"last_changed_by\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Last Updated By\r\n                            <p-sortIcon field=\"last_changed_by\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"expected_revenue_end_date\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Close Date\r\n                            <p-sortIcon field=\"expected_revenue_end_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"opportunity_id\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Opportunity ID\r\n                            <p-sortIcon field=\"opportunity_id\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-opportunity>\r\n                <tr>\r\n                    <td>\r\n                        {{ opportunity.name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.business_partner_owner?.bp_full_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('opportunityStatus',\r\n                        opportunity?.life_cycle_status_code) || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.expected_revenue_end_date ? (opportunity?.expected_revenue_end_date | date:\r\n                        'MM-dd-yyyy hh:mm a')\r\n                        : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.last_changed_by || '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.expected_revenue_end_date ? (opportunity?.expected_revenue_end_date | date:\r\n                        'MM-dd-yyyy hh:mm a')\r\n                        : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.opportunity_id || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg pl-3\">\r\n                        No opportunities found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg pl-3\">\r\n                        Loading opportunities data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"opportunity-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Opportunities</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"OpportunityForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field d-grid gap-3 text-base\">\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Name\">\r\n                    <span class=\"material-symbols-rounded\">badge</span>Name\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"name\" type=\"text\" formControlName=\"name\" placeholder=\"Name\" class=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['name'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['name'].errors['required']\">\r\n                        Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Account\">\r\n                    <span class=\"material-symbols-rounded\">account_circle</span>Account\r\n                </label>\r\n                <input pInputText type=\"text\" [value]=\"account_id\" class=\"h-3rem w-full\" readonly\r\n                    pTooltip=\"Account ID\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Primary Contact\">\r\n                    <span class=\"material-symbols-rounded\">person</span>Primary Contact\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"primary_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['primary_contact_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['primary_contact_party_id'].errors['required']\">\r\n                        Contact is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Source\">\r\n                    <span class=\"material-symbols-rounded\">source</span>Source\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunitySource']\" formControlName=\"origin_type_code\"\r\n                    placeholder=\"Select a Source\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Expected Value\">\r\n                    <span class=\"material-symbols-rounded\">show_chart</span>Expected Value\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"expected_revenue_amount\" type=\"text\" formControlName=\"expected_revenue_amount\"\r\n                    placeholder=\"Expected Value\" class=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }\" />\r\n                <div *ngIf=\"submitted && f['expected_revenue_amount'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                submitted &&\r\n                                f['expected_revenue_amount'].errors &&\r\n                                f['expected_revenue_amount'].errors['required']\r\n                              \">\r\n                        Expected Value is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Create Date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>Create Date\r\n                </label>\r\n                <p-calendar formControlName=\"expected_revenue_start_date\" inputId=\"calendar-12h\" [showTime]=\"true\"\r\n                    hourFormat=\"12\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\" appendTo=\"body\"\r\n                    placeholder=\"Create Date\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Expected Decision Date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>Expected Decision Date\r\n                </label>\r\n                <p-calendar formControlName=\"expected_revenue_end_date\" inputId=\"calendar-12h\" [showTime]=\"true\"\r\n                    hourFormat=\"12\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\" appendTo=\"body\"\r\n                    placeholder=\"Expected Decision Date\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Status\">\r\n                    <span class=\"material-symbols-rounded\">check_circle</span>Status\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunityStatus']\" formControlName=\"life_cycle_status_code\"\r\n                    placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['life_cycle_status_code'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['life_cycle_status_code'].errors['required']\">\r\n                        Status is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Probability\">\r\n                    <span class=\"material-symbols-rounded\">percent</span>Probability\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"probability_percent\" type=\"text\" formControlName=\"probability_percent\"\r\n                    placeholder=\"Probability\" class=\"h-3rem w-full\" />\r\n            </div>\r\n\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Category\">\r\n                    <span class=\"material-symbols-rounded\">category</span>Category\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunityCategory']\" formControlName=\"group_code\"\r\n                    placeholder=\"Select a Category\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Owner\">\r\n                    <span class=\"material-symbols-rounded\">account_circle</span>Owner\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"employees$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"employeeLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_employee_responsible_party_id\" [typeahead]=\"employeeInput$\"\r\n                    [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['main_employee_responsible_party_id'].errors }\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_employee_responsible_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_employee_responsible_party_id'].errors['required']\">\r\n                        Owner is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Notes\">\r\n                    <span class=\"material-symbols-rounded\">notes</span>Notes\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <textarea formControlName=\"note\" rows=\"4\" class=\"w-full p-2 border-1 border-round\"\r\n                    placeholder=\"Enter your note here...\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['note'].errors }\"></textarea>\r\n                <div *ngIf=\"submitted && f['note'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['note'].errors['required']\">\r\n                        Notes is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAGtE,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,YAAY,QACP,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;ICECC,EAFR,CAAAC,cAAA,SAAI,aAC2B,cACoB;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAG,SAAA,qBAAsC;IAE9CH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAA0D,cACX;IACvCD,EAAA,CAAAE,MAAA,cACA;IAAAF,EAAA,CAAAG,SAAA,qBAAqE;IAE7EH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAA6C,eACE;IACvCD,EAAA,CAAAE,MAAA,gBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAwD;IAEhEH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAgD,eACD;IACvCD,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2D;IAEnEH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAsC,eACS;IACvCD,EAAA,CAAAE,MAAA,yBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAiD;IAEzDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAgD,eACD;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2D;IAEnEH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAqC,eACU;IACvCD,EAAA,CAAAE,MAAA,wBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAgD;IAI5DH,EAHQ,CAAAI,YAAA,EAAM,EACL,EAEJ;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,SACI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAIJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAIJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IA5BGJ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,cAAA,CAAAC,IAAA,aACJ;IAEIR,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAE,sBAAA,kBAAAF,cAAA,CAAAE,sBAAA,CAAAC,YAAA,cACJ;IAEIV,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,MAAAK,MAAA,CAAAC,oBAAA,sBAAAL,cAAA,kBAAAA,cAAA,CAAAM,sBAAA,cAEJ;IAEIb,EAAA,CAAAK,SAAA,GAIJ;IAJIL,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAO,yBAAA,IAAAd,EAAA,CAAAe,WAAA,OAAAR,cAAA,kBAAAA,cAAA,CAAAO,yBAAA,mCAIJ;IAEId,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAS,eAAA,cAEJ;IAEIhB,EAAA,CAAAK,SAAA,GAIJ;IAJIL,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAO,yBAAA,IAAAd,EAAA,CAAAe,WAAA,SAAAR,cAAA,kBAAAA,cAAA,CAAAO,yBAAA,mCAIJ;IAEId,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAU,cAAA,cACJ;;;;;IAKAjB,EADJ,CAAAC,cAAA,SAAI,aACmD;IAC/CD,EAAA,CAAAE,MAAA,gCACJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACmD;IAC/CD,EAAA,CAAAE,MAAA,mDACJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IAQbJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAI,YAAA,EAAK;;;;;IAaVJ,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHVJ,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAkB,UAAA,IAAAC,mDAAA,kBAAuD;IAG3DnB,EAAA,CAAAI,YAAA,EAAM;;;;IAHIJ,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,SAAAC,MAAA,aAA+C;;;;;IAwBjDvB,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAM,kBAAA,QAAAkB,OAAA,CAAAd,YAAA,KAAyB;;;;;IAD1DV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAI,YAAA,EAAO;IAC7BJ,EAAA,CAAAkB,UAAA,IAAAO,4DAAA,mBAAgC;;;;IAD1BzB,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAA0B,iBAAA,CAAAF,OAAA,CAAAG,KAAA,CAAgB;IACf3B,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAoB,UAAA,SAAAI,OAAA,CAAAd,YAAA,CAAuB;;;;;IAIlCV,EAAA,CAAAC,cAAA,UAA2E;IACvED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHVJ,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAkB,UAAA,IAAAU,mDAAA,kBAA2E;IAG/E5B,EAAA,CAAAI,YAAA,EAAM;;;;IAHIJ,EAAA,CAAAK,SAAA,EAAmE;IAAnEL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,6BAAAC,MAAA,aAAmE;;;;;IAsBzEvB,EAAA,CAAAC,cAAA,UAIY;IACRD,EAAA,CAAAE,MAAA,oCACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAPVJ,EAAA,CAAAC,cAAA,cAA8E;IAC1ED,EAAA,CAAAkB,UAAA,IAAAW,mDAAA,kBAIY;IAGhB7B,EAAA,CAAAI,YAAA,EAAM;;;;IAPIJ,EAAA,CAAAK,SAAA,EAIG;IAJHL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,4BAAAC,MAAA,IAAAZ,MAAA,CAAAW,CAAA,4BAAAC,MAAA,aAIG;;;;;IA+BTvB,EAAA,CAAAC,cAAA,UAAyE;IACrED,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHVJ,EAAA,CAAAC,cAAA,cAA6E;IACzED,EAAA,CAAAkB,UAAA,IAAAY,mDAAA,kBAAyE;IAG7E9B,EAAA,CAAAI,YAAA,EAAM;;;;IAHIJ,EAAA,CAAAK,SAAA,EAAiE;IAAjEL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,2BAAAC,MAAA,aAAiE;;;;;IAmCnEvB,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAM,kBAAA,QAAAyB,OAAA,CAAArB,YAAA,KAAyB;;;;;IAD1DV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAI,YAAA,EAAO;IAC7BJ,EAAA,CAAAkB,UAAA,IAAAc,6DAAA,mBAAgC;;;;IAD1BhC,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAA0B,iBAAA,CAAAK,OAAA,CAAAJ,KAAA,CAAgB;IACf3B,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAoB,UAAA,SAAAW,OAAA,CAAArB,YAAA,CAAuB;;;;;IAIlCV,EAAA,CAAAC,cAAA,UAAqF;IACjFD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHVJ,EAAA,CAAAC,cAAA,cAAyF;IACrFD,EAAA,CAAAkB,UAAA,IAAAe,oDAAA,kBAAqF;IAGzFjC,EAAA,CAAAI,YAAA,EAAM;;;;IAHIJ,EAAA,CAAAK,SAAA,EAA6E;IAA7EL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,uCAAAC,MAAA,aAA6E;;;;;IAcnFvB,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHVJ,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAkB,UAAA,IAAAgB,oDAAA,kBAAuD;IAG3DlC,EAAA,CAAAI,YAAA,EAAM;;;;IAHIJ,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAoB,UAAA,SAAAT,MAAA,CAAAU,SAAA,IAAAV,MAAA,CAAAW,CAAA,SAAAC,MAAA,aAA+C;;;ADnPzE,OAAM,MAAOY,6BAA6B;EAsCxCC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,oBAA0C,EAC1CC,cAA8B;IAH9B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IAzChB,KAAAC,YAAY,GAAG,IAAIpD,OAAO,EAAQ;IACnC,KAAAqD,oBAAoB,GAAQ,IAAI;IAChC,KAAAf,KAAK,GAAW,EAAE;IAClB,KAAAgB,UAAU,GAAW,EAAE;IACvB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,QAAQ,GAAW,OAAO;IAE1B,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI1D,OAAO,EAAU;IAErC,KAAA2D,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAI5D,OAAO,EAAU;IACrC,KAAA6D,cAAc,GAAQ,EAAE;IACzB,KAAA7B,SAAS,GAAG,KAAK;IACjB,KAAA8B,MAAM,GAAG,KAAK;IACd,KAAAC,OAAO,GAAY,KAAK;IAExB,KAAAC,SAAS,GAA0B;MACxCC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;IAEM,KAAAC,eAAe,GAAc,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACzDlD,IAAI,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACiE,QAAQ,CAAC,CAAC;MACjCC,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAACiE,QAAQ,CAAC,CAAC;MACrDE,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAACiE,QAAQ,CAAC,CAAC;MACpDI,2BAA2B,EAAE,CAAC,EAAE,CAAC;MACjCjD,yBAAyB,EAAE,CAAC,EAAE,CAAC;MAC/BD,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACiE,QAAQ,CAAC,CAAC;MACnDK,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,kCAAkC,EAAE,CAAC,EAAE,EAAE,CAACxE,UAAU,CAACiE,QAAQ,CAAC,CAAC;MAC/DQ,IAAI,EAAE,CAAC,EAAE,EAAE,CAACzE,UAAU,CAACiE,QAAQ,CAAC;KACjC,CAAC;EAOC;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;IACD,IAAI,CAACA,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACD,IAAI,CAACjC,cAAc,CAACkC,OAAO,CACxBC,IAAI,CAACnF,SAAS,CAAC,IAAI,CAACmD,YAAY,CAAC,CAAC,CAClCiC,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAChD,KAAK,GAAGgD,QAAQ,CAAChD,KAAK;QAC3B,IAAI,CAACiB,UAAU,GAAG+B,QAAQ,EAAE/B,UAAU;QACtC,IAAI,CAACD,UAAU,GAAGgC,QAAQ,CAAChD,KAAK;QAChC,IAAI,IAAI,CAACA,KAAK,EAAE;UACd,IAAI,CAACY,oBAAoB,CACtBqC,cAAc,CAAC,IAAI,CAACjD,KAAK,CAAC,CAC1B8C,IAAI,CAACnF,SAAS,CAAC,IAAI,CAACmD,YAAY,CAAC,CAAC,CAClCiC,SAAS,CAAC;YACTG,IAAI,EAAGC,mBAAwB,IAAI;cACjC,IAAIC,KAAK,CAACC,OAAO,CAACF,mBAAmB,CAACG,IAAI,CAAC,EAAE;gBAC3C,IAAI,CAACvC,oBAAoB,GAAGoC,mBAAmB,CAACG,IAAI,CAACC,KAAK,CACxD,CAAC,EACD,EAAE,CACH;cACH,CAAC,MAAM;gBACL,IAAI,CAACxC,oBAAoB,GAAG,EAAE;cAChC;YACF,CAAC;YACDyC,KAAK,EAAGA,KAAU,IAAI;cACpBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACvD;WACD,CAAC;QACN;MACF;IACF,CAAC,CAAC;EACN;EAEAZ,uBAAuBA,CAACc,MAAc,EAAEC,IAAY;IAClD,IAAI,CAAC/C,oBAAoB,CACtBgD,6BAA6B,CAACD,IAAI,CAAC,CACnCZ,SAAS,CAAEc,GAAQ,IAAI;MACtB,IAAI,CAACnC,SAAS,CAACgC,MAAM,CAAC,GACpBG,GAAG,EAAEP,IAAI,EAAEzF,GAAG,CAAEiG,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAjF,oBAAoBA,CAACkF,WAAmB,EAAEF,KAAa;IACrD,MAAMG,IAAI,GAAG,IAAI,CAAC1C,SAAS,CAACyC,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACL,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOG,IAAI,EAAEL,KAAK,IAAIE,KAAK;EAC7B;EAEQvB,YAAYA,CAAA;IAClB,IAAI,CAAC6B,SAAS,GAAG3G,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;IAAE;IACzB,IAAI,CAACH,aAAa,CAAC0B,IAAI,CACrB1E,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACiD,cAAc,GAAG,IAAK,CAAC,EACvClD,SAAS,CAAEuG,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,8BAA8B,EAAE,QAAQ;QACxC,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC5D,oBAAoB,CAAC8D,WAAW,CAACD,MAAM,CAAC,CAAC3B,IAAI,CACvDjF,GAAG,CAAEmF,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACtC9E,GAAG,CAAC,MAAO,IAAI,CAACiD,cAAc,GAAG,KAAM,CAAC,EACxChD,UAAU,CAAEqF,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACrC,cAAc,GAAG,KAAK;QAC3B,OAAOrD,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQ6E,aAAaA,CAAA;IACnB,IAAI,CAACgC,UAAU,GAAG/G,MAAM,CACtBE,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;IAAE;IACzB,IAAI,CAACD,cAAc,CAACwB,IAAI,CACtB1E,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACmD,eAAe,GAAG,IAAK,CAAC,EACxCpD,SAAS,CAAEuG,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,8BAA8B,EAAE,QAAQ;QACxC,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC5D,oBAAoB,CAAC8D,WAAW,CAACD,MAAM,CAAC,CAAC3B,IAAI,CACvDjF,GAAG,CAAEmF,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACtC9E,GAAG,CAAC,MAAO,IAAI,CAACmD,eAAe,GAAG,KAAM,CAAC,EACzClD,UAAU,CAAEqF,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACnC,eAAe,GAAG,KAAK;QAC5B,OAAOvD,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEM8G,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACnF,SAAS,GAAG,IAAI;MAErB,IAAImF,KAAI,CAAC/C,eAAe,CAACiD,OAAO,EAAE;QAChC;MACF;MAEAF,KAAI,CAACrD,MAAM,GAAG,IAAI;MAClB,MAAMyC,KAAK,GAAG;QAAE,GAAGY,KAAI,CAAC/C,eAAe,CAACmC;MAAK,CAAE;MAE/C,MAAMX,IAAI,GAAG;QACXzE,IAAI,EAAEoF,KAAK,EAAEpF,IAAI;QACjBmG,iBAAiB,EAAEH,KAAI,CAAC7D,UAAU;QAClCiB,wBAAwB,EAAEgC,KAAK,EAAEhC,wBAAwB;QACzDC,gBAAgB,EAAE+B,KAAK,EAAE/B,gBAAgB;QACzCC,uBAAuB,EAAE8B,KAAK,EAAE9B,uBAAuB;QACvDC,2BAA2B,EAAE6B,KAAK,EAAE7B,2BAA2B,GAC3DyC,KAAI,CAACI,UAAU,CAAChB,KAAK,CAAC7B,2BAA2B,CAAC,GAClD,IAAI;QACRjD,yBAAyB,EAAE8E,KAAK,EAAE9E,yBAAyB,GACvD0F,KAAI,CAACI,UAAU,CAAChB,KAAK,CAAC9E,yBAAyB,CAAC,GAChD,IAAI;QACRD,sBAAsB,EAAE+E,KAAK,EAAE/E,sBAAsB;QACrDmD,mBAAmB,EAAE4B,KAAK,EAAE5B,mBAAmB;QAC/CC,UAAU,EAAE2B,KAAK,EAAE3B,UAAU;QAC7BC,kCAAkC,EAChC0B,KAAK,EAAE1B,kCAAkC;QAC3CC,IAAI,EAAEyB,KAAK,EAAEzB;OACd;MAEDqC,KAAI,CAACjE,oBAAoB,CACtBsE,iBAAiB,CAAC5B,IAAI,CAAC,CACvBR,IAAI,CAACnF,SAAS,CAACkH,KAAI,CAAC/D,YAAY,CAAC,CAAC,CAClCiC,SAAS,CAAC;QACTG,IAAI,EAAEA,CAAA,KAAK;UACT2B,KAAI,CAACrD,MAAM,GAAG,KAAK;UACnBqD,KAAI,CAACpD,OAAO,GAAG,KAAK;UACpBoD,KAAI,CAAC/C,eAAe,CAACqD,KAAK,EAAE;UAC5BN,KAAI,CAAChE,cAAc,CAACuE,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFT,KAAI,CAAClE,cAAc,CAChB4E,cAAc,CAACV,KAAI,CAAC5D,UAAU,CAAC,CAC/B6B,IAAI,CAACnF,SAAS,CAACkH,KAAI,CAAC/D,YAAY,CAAC,CAAC,CAClCiC,SAAS,EAAE;QAChB,CAAC;QACDS,KAAK,EAAGK,GAAQ,IAAI;UAClBgB,KAAI,CAACrD,MAAM,GAAG,KAAK;UACnBqD,KAAI,CAAChE,cAAc,CAACuE,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAL,UAAUA,CAACO,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAIpG,CAACA,CAAA;IACH,OAAO,IAAI,CAACmC,eAAe,CAACmE,QAAQ;EACtC;EAEAC,UAAUA,CAAChF,QAAgB;IACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACO,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACoC,eAAe,CAACqD,KAAK,EAAE;EAC9B;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACrF,YAAY,CAACoC,IAAI,EAAE;IACxB,IAAI,CAACpC,YAAY,CAACsF,QAAQ,EAAE;EAC9B;;;uBAhQW5F,6BAA6B,EAAAnC,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,oBAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA7BrG,6BAA6B;MAAAsG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBlC/I,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACjEJ,EAAA,CAAAC,cAAA,kBACyF;UAAhCD,EAAA,CAAAiJ,UAAA,mBAAAC,iEAAA;YAAA,OAASF,GAAA,CAAAnB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAC1F7H,EAFI,CAAAI,YAAA,EACyF,EACvF;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAyF1BD,EAxFA,CAAAkB,UAAA,IAAAiI,oDAAA,0BAAgC,IAAAC,oDAAA,2BAgDc,IAAAC,oDAAA,yBAiCR,KAAAC,qDAAA,yBAOD;UASjDtJ,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UACNJ,EAAA,CAAAC,cAAA,oBAC8B;UADLD,EAAA,CAAAuJ,gBAAA,2BAAAC,0EAAAC,MAAA;YAAAzJ,EAAA,CAAA0J,kBAAA,CAAAV,GAAA,CAAA5F,OAAA,EAAAqG,MAAA,MAAAT,GAAA,CAAA5F,OAAA,GAAAqG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAE1CzJ,EAAA,CAAAkB,UAAA,KAAAyI,qDAAA,yBAAgC;UAQhB3J,EAJhB,CAAAC,cAAA,gBAA4E,eAC9B,eAClB,iBAC2D,gBAChC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,aACnD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,iBACkE;UAClEH,EAAA,CAAAkB,UAAA,KAAA0I,6CAAA,kBAA2D;UAK/D5J,EAAA,CAAAI,YAAA,EAAM;UAIEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC8D,gBACnC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,gBAChE;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAG,SAAA,iBAC4B;UAChCH,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBACsE,gBAC3C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,wBACpD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAC,cAAA,qBAGoG;;UAChGD,EAAA,CAAAkB,UAAA,KAAA2I,qDAAA,0BAA2C;UAI/C7J,EAAA,CAAAI,YAAA,EAAY;UACZJ,EAAA,CAAAkB,UAAA,KAAA4I,6CAAA,kBAA+E;UAKnF9J,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC6D,gBAClC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,eACxD;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAG,SAAA,sBAEa;UACjBH,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBACqE,gBAC1C;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,uBACxD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,iBAEqF;UACrFH,EAAA,CAAAkB,UAAA,KAAA6I,6CAAA,kBAA8E;UASlF/J,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBACkE,gBACvC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,oBAC1D;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAG,SAAA,sBAEgC;UACpCH,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC6E,gBAClD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,+BAC1D;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAG,SAAA,sBAE2C;UAC/CH,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC6D,gBAClC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,eAC1D;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,sBAGa;UACbH,EAAA,CAAAkB,UAAA,KAAA8I,6CAAA,kBAA6E;UAKjFhK,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBACkE,gBACvC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,oBACrD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,iBACsD;UAC1DH,EAAA,CAAAI,YAAA,EAAM;UAIEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC+D,gBACpC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,iBACtD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,sBAEa;UACjBH,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,eAAoB,iBAC4D,gBACjC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,cAC5D;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAC,cAAA,sBAI8F;;UAC1FD,EAAA,CAAAkB,UAAA,MAAA+I,sDAAA,0BAA2C;UAI/CjK,EAAA,CAAAI,YAAA,EAAY;UACZJ,EAAA,CAAAkB,UAAA,MAAAgJ,8CAAA,kBAAyF;UAK7FlK,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,gBAAoB,kBAC4D,iBACjC;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,eACnD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAG,SAAA,qBAE2E;UAC3EH,EAAA,CAAAkB,UAAA,MAAAiJ,8CAAA,kBAA2D;UAMnEnK,EADI,CAAAI,YAAA,EAAM,EACJ;UAEFJ,EADJ,CAAAC,cAAA,gBAAgD,mBAGd;UAA1BD,EAAA,CAAAiJ,UAAA,mBAAAmB,iEAAA;YAAA,OAAApB,GAAA,CAAA5F,OAAA,GAAmB,KAAK;UAAA,EAAC;UAACpD,EAAA,CAAAI,YAAA,EAAS;UACvCJ,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAiJ,UAAA,mBAAAoB,iEAAA;YAAA,OAASrB,GAAA,CAAAzC,QAAA,EAAU;UAAA,EAAC;UAIpCvG,EAJqC,CAAAI,YAAA,EAAS,EAChC,EACH,EAEA;;;UAjRCJ,EAAA,CAAAK,SAAA,GAAmC;UAACL,EAApC,CAAAoB,UAAA,oCAAmC,iBAAiB;UAI/CpB,EAAA,CAAAK,SAAA,GAA8B;UAAuCL,EAArE,CAAAoB,UAAA,UAAA4H,GAAA,CAAAtG,oBAAA,CAA8B,WAAwB,mBAAiC;UAoGzD1C,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAsK,UAAA,CAAAtK,EAAA,CAAAuK,eAAA,KAAAC,GAAA,EAA4B;UAAjExK,EAAA,CAAAoB,UAAA,eAAc;UAACpB,EAAA,CAAAyK,gBAAA,YAAAzB,GAAA,CAAA5F,OAAA,CAAqB;UAAmDpD,EAArB,CAAAoB,UAAA,qBAAoB,oBAAoB;UAM1GpB,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAoB,UAAA,cAAA4H,GAAA,CAAAvF,eAAA,CAA6B;UAQnBzD,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,SAAAC,MAAA,EAA2D;UACzDvB,EAAA,CAAAK,SAAA,EAAmC;UAAnCL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,SAAAC,MAAA,CAAmC;UAWXvB,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAoB,UAAA,UAAA4H,GAAA,CAAArG,UAAA,CAAoB;UAQ5B3C,EAAA,CAAAK,SAAA,GAA2B;UAG7BL,EAHE,CAAAoB,UAAA,UAAApB,EAAA,CAAA4K,WAAA,SAAA5B,GAAA,CAAA9C,SAAA,EAA2B,sBACxB,YAAA8C,GAAA,CAAAlG,cAAA,CAA2B,oBAAoB,cAAAkG,GAAA,CAAAjG,aAAA,CACE,wBAAwB,YAAA/C,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,6BAAAC,MAAA,EACC;UAM7FvB,EAAA,CAAAK,SAAA,GAAuD;UAAvDL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,6BAAAC,MAAA,CAAuD;UAUjDvB,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAoB,UAAA,YAAA4H,GAAA,CAAA3F,SAAA,sBAA0C;UAWlDrD,EAAA,CAAAK,SAAA,GAA8E;UAA9EL,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,4BAAAC,MAAA,EAA8E;UAC5EvB,EAAA,CAAAK,SAAA,EAAsD;UAAtDL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,4BAAAC,MAAA,CAAsD;UAcqBvB,EAAA,CAAAK,SAAA,GAAiB;UAC9EL,EAD6D,CAAAoB,UAAA,kBAAiB,kBAC7D;UAO0CpB,EAAA,CAAAK,SAAA,GAAiB;UAC5EL,EAD2D,CAAAoB,UAAA,kBAAiB,kBAC3D;UAQzBpB,EAAA,CAAAK,SAAA,GAA0C;UAElDL,EAFQ,CAAAoB,UAAA,YAAA4H,GAAA,CAAA3F,SAAA,sBAA0C,YAAArD,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,2BAAAC,MAAA,EAE2B;UAE3EvB,EAAA,CAAAK,SAAA,EAAqD;UAArDL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,2BAAAC,MAAA,CAAqD;UAoB/CvB,EAAA,CAAAK,SAAA,IAA4C;UAA5CL,EAAA,CAAAoB,UAAA,YAAA4H,GAAA,CAAA3F,SAAA,wBAA4C;UASlCrD,EAAA,CAAAK,SAAA,GAA4B;UAI9CL,EAJkB,CAAAoB,UAAA,UAAApB,EAAA,CAAA4K,WAAA,UAAA5B,GAAA,CAAA1C,UAAA,EAA4B,sBACzB,YAAA0C,GAAA,CAAAhG,eAAA,CAA4B,oBAAoB,cAAAgG,GAAA,CAAA/F,cAAA,CACY,wBAC1D,YAAAjD,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,uCAAAC,MAAA,EACkE;UAMvFvB,EAAA,CAAAK,SAAA,GAAiE;UAAjEL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,uCAAAC,MAAA,CAAiE;UAanEvB,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,SAAAC,MAAA,EAA2D;UACzDvB,EAAA,CAAAK,SAAA,EAAmC;UAAnCL,EAAA,CAAAoB,UAAA,SAAA4H,GAAA,CAAA3H,SAAA,IAAA2H,GAAA,CAAA1H,CAAA,SAAAC,MAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}