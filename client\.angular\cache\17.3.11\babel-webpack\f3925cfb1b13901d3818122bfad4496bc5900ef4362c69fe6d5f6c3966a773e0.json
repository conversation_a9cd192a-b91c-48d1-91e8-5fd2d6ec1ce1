{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ApiConstant, AppConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, ngZone) {\n      this.http = http;\n      this.ngZone = ngZone;\n      this.permissions = new BehaviorSubject([]);\n      this.cmsTokenVal = new BehaviorSubject(\"\");\n      this.sessionChannel = new BroadcastChannel(\"session\");\n      this.logoutTriggered = false;\n      this.TokenKey = 'jwtToken';\n      this.UserDetailsKey = 'userInfo';\n      const user = this.getAuth();\n      this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : \"\");\n      this.bindUserActivityEvents();\n    }\n    checkAdminUser() {\n      const user = this.getAuth();\n      if (user && user[this.UserDetailsKey]?.documentId) {\n        return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(tap(cartres => {\n          if (cartres) {\n            this.updateAuth({\n              userDetails: {\n                address: cartres.address,\n                email: cartres.email,\n                firstname: cartres.firstname,\n                lastname: cartres.lastname,\n                username: cartres.username\n              }\n            });\n          }\n        }));\n      } else {\n        return of(null);\n      }\n    }\n    bindUserActivityEvents() {\n      const events = [\"click\", \"keydown\"];\n      for (let i = 0; i < events.length; i++) {\n        const element = events[i];\n        fromEvent(document, element).subscribe(() => {\n          this.setInavtivityTimer();\n          this.sessionChannel.postMessage({\n            type: \"activityFound\"\n          });\n        });\n      }\n      this.sessionChannel.onmessage = event => {\n        if (event?.data?.type == \"activityFound\") {\n          this.ngZone.run(() => {\n            this.setInavtivityTimer();\n          });\n        }\n        if (event?.data?.type == \"logout\") {\n          this.logoutTriggered = true;\n          this.doLogout();\n        }\n      };\n      this.setInavtivityTimer();\n      this.sessionChannel.postMessage({\n        type: \"activityFound\"\n      });\n    }\n    setInavtivityTimer() {\n      clearTimeout(this.timer);\n      if (!this.isLoggedIn) {\n        return;\n      }\n      this.timer = setTimeout(() => {\n        this.doLogout();\n      }, AppConstant.SESSION_TIMEOUT);\n    }\n    login(username, password, rememberMe) {\n      return this.http.post(CMS_APIContstant.SINGIN, {\n        identifier: (username || \"\").toLowerCase(),\n        password\n      }).pipe(tap(res => {\n        if (res) {\n          this.setAuth(res.jwt, res.user, rememberMe);\n        }\n        return res;\n      }), switchMap(res => {\n        if (res?.user) {\n          return this.getCartDetails(res.user.documentId).pipe(map(data => {\n            if (data?.cart) {\n              res.user.cart = data.cart;\n              res.user.customer = data.cart.customer;\n            }\n            this.updateAuth(res.user);\n            return res;\n          }));\n        }\n        return of(null);\n      }));\n    }\n    getCartDetails(userId) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n    }\n    getToken() {\n      const val = this.userSubject.value;\n      return val ? val[this.TokenKey] : null;\n    }\n    get partnerFunction() {\n      const user = this.userSubject.value;\n      if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n        return user[this.UserDetailsKey].customer.partner_functions[0];\n      }\n      return {};\n    }\n    get userDetail() {\n      const user = this.userSubject.value;\n      return user ? user[this.UserDetailsKey] : null;\n    }\n    get isLoggedIn() {\n      return !!this.userSubject.value;\n    }\n    updateAuth(user) {\n      const auth = this.getAuth();\n      if (user?.userDetails) {\n        auth[this.UserDetailsKey] = {\n          ...auth[this.UserDetailsKey],\n          ...user?.userDetails\n        };\n      }\n      if (user?.cart) {\n        auth[this.UserDetailsKey].cart = user?.cart;\n      }\n      if (user?.customer) {\n        auth[this.UserDetailsKey].customer = user?.customer;\n      }\n      this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n    }\n    isRememberMeSelected() {\n      return !!localStorage.getItem(this.TokenKey);\n    }\n    doLogout() {\n      this.resetAuth();\n    }\n    resetAuth() {\n      this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\n        this.removeAuthToken();\n        !this.logoutTriggered && this.sessionChannel.postMessage({\n          type: \"logout\"\n        });\n        this.userSubject.next(null);\n        window.location.href = \"#/auth/login\";\n        window.location.reload();\n      });\n    }\n    getAuth() {\n      const authtoken = this.getAuthToken();\n      const userDetails = this.getUserDetails();\n      if (authtoken && this.isJsonString(userDetails)) {\n        return {\n          [this.UserDetailsKey]: JSON.parse(userDetails),\n          [this.TokenKey]: JSON.parse(authtoken)\n        };\n      }\n      return {};\n    }\n    setAuth(token, user, rememberMe) {\n      if (rememberMe) {\n        localStorage.setItem(this.TokenKey, JSON.stringify(token));\n        localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n      } else {\n        sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\n        sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n      }\n      this.userSubject.next({\n        [this.UserDetailsKey]: user,\n        [this.TokenKey]: token\n      });\n    }\n    getAuthToken() {\n      return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n    }\n    getUserDetails() {\n      return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n    }\n    getUserEmail() {\n      const userData = sessionStorage.getItem('userInfo');\n      if (userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          return parsedUser.email || null;\n        } catch (error) {\n          return null;\n        }\n      }\n      return null;\n    }\n    removeAuthToken() {\n      localStorage.removeItem(this.TokenKey);\n      sessionStorage.removeItem(this.TokenKey);\n      localStorage.removeItem(this.UserDetailsKey);\n      sessionStorage.removeItem(this.UserDetailsKey);\n    }\n    isJsonString(str) {\n      try {\n        JSON.parse(str);\n      } catch (e) {\n        return false;\n      }\n      return true;\n    }\n    getUserPermissions() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const userDetails = _this.userDetail;\n        return yield lastValueFrom(_this.getUserPermissionsDB(userDetails));\n      })();\n    }\n    getUserPermissionsDB(userDetails) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`).pipe(map(res => {\n        if (res?.data?.length) {\n          const data = res?.data || [];\n          this.permissions.next(data);\n          return data;\n        }\n        return [];\n      })).pipe(catchError(error => {\n        this.permissions.next([]);\n        return error;\n      }));\n    }\n    get getPermissions() {\n      return this.permissions?.value || [];\n    }\n    getCMSToken() {\n      return this.http.get(ApiConstant.FETCH_TOKEN).pipe(map(response => {\n        this.cmsTokenVal.next(response.token);\n        return response.token;\n      }));\n    }\n    get cmsToken() {\n      return of(this.cmsTokenVal?.value || \"\");\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}