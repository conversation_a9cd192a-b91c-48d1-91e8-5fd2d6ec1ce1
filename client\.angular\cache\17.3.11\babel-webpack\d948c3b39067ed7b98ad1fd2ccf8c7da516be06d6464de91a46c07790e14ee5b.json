{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ProspectsRoutingModule } from './prospects-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EditorModule } from 'primeng/editor';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { TableModule } from 'primeng/table';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DialogModule } from 'primeng/dialog';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { CommonFormModule } from '../common-form/common-form.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let ProspectsModule = /*#__PURE__*/(() => {\n  class ProspectsModule {\n    static {\n      this.ɵfac = function ProspectsModule_Factory(t) {\n        return new (t || ProspectsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ProspectsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, NgSelectModule, ProspectsRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, ToastModule, EditorModule, InputTextModule, ConfirmDialogModule, DialogModule, CheckboxModule, SharedModule, CommonFormModule, MultiSelectModule]\n      });\n    }\n  }\n  return ProspectsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}