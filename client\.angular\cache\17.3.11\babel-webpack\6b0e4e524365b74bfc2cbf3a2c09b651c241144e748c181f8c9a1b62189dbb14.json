{"ast": null, "code": "import { forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Observable, Subject, concat, of } from 'rxjs';\nimport { map, switchMap, tap, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../prospects.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ng-select/ng-select\";\nfunction EmployeeSelectComponent_ng_template_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r1.bp_full_name, \"\");\n  }\n}\nfunction EmployeeSelectComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EmployeeSelectComponent_ng_template_2_span_2_Template, 2, 1, \"span\", 2);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.bp_full_name);\n  }\n}\nexport let EmployeeSelectComponent = /*#__PURE__*/(() => {\n  class EmployeeSelectComponent {\n    constructor(prospectsService) {\n      this.prospectsService = prospectsService;\n      this.employees$ = new Observable();\n      this.employeeInput$ = new Subject();\n      this.employeeLoading = false;\n      this.onChange = value => {};\n      this.onTouched = () => {};\n    }\n    ngOnInit() {\n      this.loadEmployees();\n    }\n    loadEmployees() {\n      this.employees$ = concat(this.employeeInput$.pipe(distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP003',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n          return this.prospectsService.getEmployee(params).pipe(map(data => data), tap(() => this.employeeLoading = false));\n        }\n        return of([]).pipe(tap(() => this.employeeLoading = false));\n      })));\n    }\n    writeValue(value) {\n      this._value = value;\n    }\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {}\n    selectEmployee(value) {\n      const bp_id = value?.bp_id || null;\n      this._value = bp_id;\n      this.onChange(bp_id);\n    }\n    static {\n      this.ɵfac = function EmployeeSelectComponent_Factory(t) {\n        return new (t || EmployeeSelectComponent)(i0.ɵɵdirectiveInject(i1.ProspectsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EmployeeSelectComponent,\n        selectors: [[\"app-employee-select\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: NG_VALUE_ACCESSOR,\n          useExisting: forwardRef(() => EmployeeSelectComponent),\n          multi: true\n        }])],\n        decls: 3,\n        vars: 10,\n        consts: [[\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"appendTo\", \"body\", 3, \"change\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [4, \"ngIf\"]],\n        template: function EmployeeSelectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"ng-select\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵlistener(\"change\", function EmployeeSelectComponent_Template_ng_select_change_0_listener($event) {\n              return ctx.selectEmployee($event);\n            });\n            i0.ɵɵtemplate(2, EmployeeSelectComponent_ng_template_2_Template, 3, 2, \"ng-template\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(1, 8, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 10);\n          }\n        },\n        dependencies: [i2.NgIf, i3.NgSelectComponent, i3.NgOptionTemplateDirective, i2.AsyncPipe]\n      });\n    }\n  }\n  return EmployeeSelectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}