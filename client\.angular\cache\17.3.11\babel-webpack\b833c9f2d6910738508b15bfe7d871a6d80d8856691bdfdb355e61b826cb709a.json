{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/button\";\nfunction AccountOpportunitiesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10);\n    i0.ɵɵtext(2, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Owner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Last Updated Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Last Updated By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Close Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Opportunity ID\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const opportunity_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/sales-quotes/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.owner) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.status) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.last_updated) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.last_updated_by) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.close_date) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r1 == null ? null : opportunity_r1.opportunity_id) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 12);\n    i0.ɵɵtext(2, \"No Opportunities found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 12);\n    i0.ɵɵtext(2, \"Loading Opportunities data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountOpportunitiesComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.opportunitiesdetails = null;\n    this.bp_id = '';\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.opportunitiesdetails = response?.account_opportunity;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountOpportunitiesComponent_Factory(t) {\n      return new (t || AccountOpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountOpportunitiesComponent,\n      selectors: [[\"app-account-opportunities\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\", 3, \"routerLink\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function AccountOpportunitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Opportunities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, AccountOpportunitiesComponent_ng_template_7_Template, 15, 0, \"ng-template\", 6)(8, AccountOpportunitiesComponent_ng_template_8_Template, 15, 8, \"ng-template\", 7)(9, AccountOpportunitiesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, AccountOpportunitiesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.opportunitiesdetails)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i2.RouterLink, i3.PrimeTemplate, i4.Table, i5.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "opportunity_r1", "name", "owner", "status", "last_updated", "last_updated_by", "close_date", "opportunity_id", "AccountOpportunitiesComponent", "constructor", "accountservice", "unsubscribe$", "opportunitiesdetails", "bp_id", "ngOnInit", "account", "pipe", "subscribe", "response", "account_opportunity", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountOpportunitiesComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AccountOpportunitiesComponent_ng_template_7_Template", "AccountOpportunitiesComponent_ng_template_8_Template", "AccountOpportunitiesComponent_ng_template_9_Template", "AccountOpportunitiesComponent_ng_template_10_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\n\r\n@Component({\r\n  selector: 'app-account-opportunities',\r\n  templateUrl: './account-opportunities.component.html',\r\n  styleUrl: './account-opportunities.component.scss',\r\n})\r\nexport class AccountOpportunitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public opportunitiesdetails: any = null;\r\n  public bp_id: string = '';\r\n\r\n  constructor(private accountservice: AccountService) {}\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.opportunitiesdetails = response?.account_opportunity;\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Opportunities</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\"\r\n            [styleClass]=\"'w-5rem font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"opportunitiesdetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\">Name</th>\r\n                    <th>Owner</th>\r\n                    <th>Status</th>\r\n                    <th>Last Updated Date</th>\r\n                    <th>Last Updated By</th>\r\n                    <th>Close Date</th>\r\n                    <th>Opportunity ID</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-opportunity>\r\n                <tr>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\"\r\n                        [routerLink]=\"'/store/sales-quotes/overview'\">\r\n                        {{ opportunity?.name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.owner || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.status || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.last_updated || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.last_updated_by || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.close_date || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ opportunity?.opportunity_id || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">No Opportunities found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">Loading Opportunities data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;ICWrBC,EADJ,CAAAC,cAAA,SAAI,aACiC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IACtBF,EADsB,CAAAG,YAAA,EAAK,EACtB;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aAEkD;IAC9CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IArBGH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAK,UAAA,8CAA6C;IAC7CL,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAC,IAAA,cACJ;IAEIR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAE,KAAA,cACJ;IAEIT,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAG,MAAA,cACJ;IAEIV,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAI,YAAA,cACJ;IAEIX,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAK,eAAA,cACJ;IAEIZ,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAM,UAAA,cACJ;IAEIb,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAO,cAAA,cACJ;;;;;IAKAd,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC9EF,EAD8E,CAAAG,YAAA,EAAK,EAC9E;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC/FF,EAD+F,CAAAG,YAAA,EAAK,EAC/F;;;AD/CrB,OAAM,MAAOY,6BAA6B;EAKxCC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAJ1B,KAAAC,YAAY,GAAG,IAAIpB,OAAO,EAAQ;IACnC,KAAAqB,oBAAoB,GAAQ,IAAI;IAChC,KAAAC,KAAK,GAAW,EAAE;EAE4B;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACJ,cAAc,CAACK,OAAO,CACxBC,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACmB,YAAY,CAAC,CAAC,CAClCM,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACL,KAAK,GAAGK,QAAQ,EAAEL,KAAK;QAC5B,IAAI,CAACD,oBAAoB,GAAGM,QAAQ,EAAEC,mBAAmB;MAC3D;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,IAAI,EAAE;IACxB,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;EAC9B;;;uBArBWd,6BAA6B,EAAAf,EAAA,CAAA8B,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA7BjB,6BAA6B;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlCvC,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjEH,EAAA,CAAAyC,SAAA,kBAC4C;UAChDzC,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UA4C1BD,EA3CA,CAAA0C,UAAA,IAAAC,oDAAA,0BAAgC,IAAAC,oDAAA,0BAYc,IAAAC,oDAAA,yBA0BR,KAAAC,qDAAA,yBAKD;UAOjD9C,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAzDiEH,EAAA,CAAAI,SAAA,GAAiB;UAC5EJ,EAD2D,CAAAK,UAAA,kBAAiB,sCACvC;UAIhCL,EAAA,CAAAI,SAAA,GAA8B;UAAuCJ,EAArE,CAAAK,UAAA,UAAAmC,GAAA,CAAArB,oBAAA,CAA8B,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}