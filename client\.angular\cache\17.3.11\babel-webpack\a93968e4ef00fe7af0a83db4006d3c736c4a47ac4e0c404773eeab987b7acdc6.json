{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AiInsightsRoutingModule } from './ai-insights-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport let AiInsightsModule = /*#__PURE__*/(() => {\n  class AiInsightsModule {\n    static {\n      this.ɵfac = function AiInsightsModule_Factory(t) {\n        return new (t || AiInsightsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AiInsightsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, AiInsightsRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n      });\n    }\n  }\n  return AiInsightsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}