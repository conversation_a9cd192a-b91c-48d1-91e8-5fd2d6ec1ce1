{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/editor\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/inputswitch\";\nimport * as i13 from \"primeng/tooltip\";\nimport * as i14 from \"primeng/calendar\";\nimport * as i15 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"label\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Opportunity ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 25)(10, \"div\", 26)(11, \"label\", 27)(12, \"span\", 28);\n    i0.ɵɵtext(13, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"div\", 26)(19, \"label\", 27)(20, \"span\", 28);\n    i0.ɵɵtext(21, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Expected Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 29);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"label\", 27)(28, \"span\", 28);\n    i0.ɵɵtext(29, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 29);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 25)(35, \"div\", 26)(36, \"label\", 27)(37, \"span\", 28);\n    i0.ɵɵtext(38, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 29);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 25)(43, \"div\", 26)(44, \"label\", 27)(45, \"span\", 28);\n    i0.ɵɵtext(46, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47, \" Primary Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 29);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 25)(51, \"div\", 26)(52, \"label\", 27)(53, \"span\", 28);\n    i0.ɵɵtext(54, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 29);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 25)(59, \"div\", 26)(60, \"label\", 27)(61, \"span\", 28);\n    i0.ɵɵtext(62, \"account_tree\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(63, \" Parent Opportunity \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 29);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 25)(67, \"div\", 26)(68, \"label\", 27)(69, \"span\", 28);\n    i0.ɵɵtext(70, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(71, \" Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 29);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26)(76, \"label\", 27)(77, \"span\", 28);\n    i0.ɵɵtext(78, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 29);\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"div\", 25)(83, \"div\", 26)(84, \"label\", 27)(85, \"span\", 28);\n    i0.ɵɵtext(86, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"div\", 29);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 25)(91, \"div\", 26)(92, \"label\", 27)(93, \"span\", 28);\n    i0.ɵɵtext(94, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(95, \" Reason for Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 29);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(98, \"div\", 25)(99, \"div\", 26)(100, \"label\", 27)(101, \"span\", 28);\n    i0.ɵɵtext(102, \"track_changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(103, \" Days in Sales Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"div\", 29);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 25)(107, \"div\", 26)(108, \"label\", 27)(109, \"span\", 28);\n    i0.ɵɵtext(110, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(111, \" Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"div\", 29);\n    i0.ɵɵtext(113);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(114, \"div\", 25)(115, \"div\", 26)(116, \"label\", 27)(117, \"span\", 28);\n    i0.ɵɵtext(118, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(119, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"div\", 29);\n    i0.ɵɵtext(121);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(122, \"div\", 25)(123, \"div\", 26)(124, \"label\", 27)(125, \"span\", 28);\n    i0.ɵɵtext(126, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(127, \" Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(128, \"div\", 29);\n    i0.ɵɵtext(129);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(130, \"div\", 25)(131, \"div\", 26)(132, \"label\", 27)(133, \"span\", 28);\n    i0.ɵɵtext(134, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(135, \" Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"div\", 29);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(138, \"div\", 25)(139, \"div\", 26)(140, \"label\", 27)(141, \"span\", 28);\n    i0.ɵɵtext(142, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(143, \" Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(144, \"div\", 29);\n    i0.ɵɵtext(145);\n    i0.ɵɵpipe(146, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(147, \"div\", 25)(148, \"div\", 26)(149, \"label\", 27)(150, \"span\", 28);\n    i0.ɵɵtext(151, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(152, \" Last Updated Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(153, \"div\", 29);\n    i0.ɵɵtext(154);\n    i0.ɵɵpipe(155, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(156, \"div\", 25)(157, \"div\", 26)(158, \"label\", 27)(159, \"span\", 28);\n    i0.ɵɵtext(160, \"update\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(161, \" Last Updated By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(162, \"div\", 29);\n    i0.ɵɵtext(163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(164, \"div\", 25)(165, \"div\", 26)(166, \"label\", 27)(167, \"span\", 28);\n    i0.ɵɵtext(168, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(169, \" Progress \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"div\", 29);\n    i0.ɵɵtext(171);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(172, \"div\", 25)(173, \"div\", 26)(174, \"label\", 27)(175, \"span\", 28);\n    i0.ɵɵtext(176, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(177, \" Need Help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"div\", 29);\n    i0.ɵɵtext(179);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.opportunity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_end_date) ? i0.ɵɵpipeBind2(33, 22, ctx_r0.overviewDetails.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"opportunityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.group_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.parent_opportunity) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunitySource\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.origin_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.weighted_expected_net_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.life_cycle_status_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.result_reason_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.days_in_sales_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.probability_percent) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_organisation_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_unit_party_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_start_date) ? i0.ɵɵpipeBind2(146, 25, ctx_r0.overviewDetails.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_change_date) ? i0.ɵɵpipeBind2(155, 28, ctx_r0.overviewDetails.last_change_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_changed_by) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.progress) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.need_help) || \"-\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_11_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_23_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template, 2, 1, \"span\", 56);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_42_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_53_span_2_Template, 2, 1, \"span\", 56);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_54_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_87_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_105_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_105_span_2_Template, 2, 1, \"span\", 56);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_106_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Owner is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_106_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_employee_responsible_party_id\"].errors && ctx_r0.f[\"main_employee_responsible_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 30)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"label\", 31)(5, \"span\", 32);\n    i0.ɵɵtext(6, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Name \");\n    i0.ɵɵelementStart(8, \"span\", 33);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"input\", 34);\n    i0.ɵɵtemplate(11, OpportunitiesOverviewComponent_form_6_div_11_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"div\", 26)(14, \"label\", 31)(15, \"span\", 32);\n    i0.ɵɵtext(16, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \"Expected Value \");\n    i0.ɵɵelementStart(18, \"span\", 33);\n    i0.ɵɵtext(19, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 37);\n    i0.ɵɵelement(21, \"input\", 38)(22, \"p-dropdown\", 39);\n    i0.ɵɵtemplate(23, OpportunitiesOverviewComponent_form_6_div_23_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"div\", 26)(26, \"label\", 31)(27, \"span\", 32);\n    i0.ɵɵtext(28, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \"Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p-calendar\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 25)(32, \"div\", 26)(33, \"label\", 31)(34, \"span\", 32);\n    i0.ɵɵtext(35, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \"Account \");\n    i0.ɵɵelementStart(37, \"span\", 33);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"ng-select\", 41);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵtemplate(41, OpportunitiesOverviewComponent_form_6_ng_template_41_Template, 3, 2, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpportunitiesOverviewComponent_form_6_div_42_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 25)(44, \"div\", 26)(45, \"label\", 31)(46, \"span\", 32);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \"Primary Contact \");\n    i0.ɵɵelementStart(49, \"span\", 33);\n    i0.ɵɵtext(50, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"ng-select\", 43);\n    i0.ɵɵpipe(52, \"async\");\n    i0.ɵɵtemplate(53, OpportunitiesOverviewComponent_form_6_ng_template_53_Template, 3, 2, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpportunitiesOverviewComponent_form_6_div_54_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 25)(56, \"div\", 26)(57, \"label\", 31)(58, \"span\", 32);\n    i0.ɵɵtext(59, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-dropdown\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 25)(63, \"div\", 26)(64, \"label\", 31)(65, \"span\", 32);\n    i0.ɵɵtext(66, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-dropdown\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 36)(70, \"div\", 26)(71, \"label\", 31)(72, \"span\", 32);\n    i0.ɵɵtext(73, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 37);\n    i0.ɵɵelement(76, \"input\", 46)(77, \"p-dropdown\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"div\", 25)(79, \"div\", 26)(80, \"label\", 31)(81, \"span\", 32);\n    i0.ɵɵtext(82, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \"Status \");\n    i0.ɵɵelementStart(84, \"span\", 33);\n    i0.ɵɵtext(85, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(86, \"p-dropdown\", 47);\n    i0.ɵɵtemplate(87, OpportunitiesOverviewComponent_form_6_div_87_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 25)(89, \"div\", 26)(90, \"label\", 31)(91, \"span\", 32);\n    i0.ɵɵtext(92, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 25)(96, \"div\", 26)(97, \"label\", 31)(98, \"span\", 32);\n    i0.ɵɵtext(99, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Owner \");\n    i0.ɵɵelementStart(101, \"span\", 33);\n    i0.ɵɵtext(102, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(103, \"ng-select\", 49);\n    i0.ɵɵpipe(104, \"async\");\n    i0.ɵɵtemplate(105, OpportunitiesOverviewComponent_form_6_ng_template_105_Template, 3, 2, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(106, OpportunitiesOverviewComponent_form_6_div_106_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"div\", 25)(108, \"div\", 26)(109, \"label\", 31)(110, \"span\", 32);\n    i0.ɵɵtext(111, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(112, \"Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(113, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(114, \"div\", 25)(115, \"div\", 26)(116, \"label\", 31)(117, \"span\", 32);\n    i0.ɵɵtext(118, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(119, \"Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(120, \"input\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"div\", 25)(122, \"div\", 26)(123, \"label\", 31)(124, \"span\", 32);\n    i0.ɵɵtext(125, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(126, \"Created Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(127, \"p-calendar\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"div\", 25)(129, \"div\", 26)(130, \"label\", 31)(131, \"span\", 32);\n    i0.ɵɵtext(132, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(133, \"Need help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(134, \"p-inputSwitch\", 53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(135, \"div\", 54)(136, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_form_6_Template_button_click_136_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.OpportunityOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c2, ctx_r0.submitted && ctx_r0.f[\"name\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c2, ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 43, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(53, _c2, ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(52, 45, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(55, _c2, ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityCategory\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunitySource\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityStatus\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(57, _c2, ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(104, 47, ctx_r0.employees$))(\"hideSelected\", true)(\"loading\", ctx_r0.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.employeeInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(59, _c2, ctx_r0.submitted && ctx_r0.f[\"main_employee_responsible_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_employee_responsible_party_id\"].errors);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 57)(2, \"div\", 58);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 60)(6, \"div\", 58);\n    i0.ɵɵtext(7, \" Created At \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 62)(10, \"div\", 58);\n    i0.ɵɵtext(11, \" Updated At \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 64);\n    i0.ɵɵtext(14, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 66)(10, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_15_Template_button_click_10_listener() {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_15_Template_button_click_11_listener($event) {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.stripHtml(notes_r7 == null ? null : notes_r7.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 3, notes_r7 == null ? null : notes_r7.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 6, notes_r7 == null ? null : notes_r7.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 69);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 69);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_div_24_div_1_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class OpportunitiesOverviewComponent {\n  constructor(formBuilder, opportunitiesservice, messageservice, confirmationservice, router) {\n    this.formBuilder = formBuilder;\n    this.opportunitiesservice = opportunitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.router = router;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.notedetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.opportunity_id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.currencies = [{\n      label: 'USD',\n      value: 'USD'\n    }];\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.OpportunityOverviewForm = this.formBuilder.group({\n      name: ['', [Validators.required]],\n      prospect_party_id: ['', [Validators.required]],\n      primary_contact_party_id: ['', [Validators.required]],\n      origin_type_code: [''],\n      expected_revenue_amount: ['', [Validators.required]],\n      weighted_expected_net_amount: [''],\n      expected_revenue_start_date: [''],\n      expected_revenue_end_date: [''],\n      life_cycle_status_code: ['', [Validators.required]],\n      probability_percent: [''],\n      group_code: [''],\n      main_employee_responsible_party_id: ['', [Validators.required]],\n      sales_organisation_id: [''],\n      sales_unit_party_id: [''],\n      currency: ['USD'],\n      need_help: ['']\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n  }\n  ngOnInit() {\n    // Opportunities successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('opportunitiesMessage');\n      }\n    }, 100);\n    this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    this.loadAccounts();\n    this.loadContacts();\n    this.loadEmployees();\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.opportunity_id = response?.opportunity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Emit default options first\n    this.contactInput$.pipe(debounceTime(300),\n    // Prevent rapid requests on fast typing\n    distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$eq]': 'BUP001',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response || []), tap(() => this.contactLoading = false), catchError(error => {\n        console.error('Contact loading failed', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadEmployees() {\n    this.employees$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.employeeInput$.pipe(debounceTime(300),\n    // Debounce user input to avoid spamming API\n    distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response || []), tap(() => this.employeeLoading = false), catchError(error => {\n        console.error('Employee loading failed', error);\n        this.employeeLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(opportunity) {\n    this.existingopportunity = {\n      opportunity_id: opportunity?.opportunity_id,\n      name: opportunity?.name,\n      group_code: opportunity?.group_code,\n      origin_type_code: opportunity?.origin_type_code,\n      probability_percent: opportunity?.probability_percent,\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\n      prospect_party_id: opportunity?.prospect_party_id,\n      primary_contact_party_id: opportunity?.primary_contact_party_id,\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\n      main_employee_responsible_party_id: opportunity?.main_employee_responsible_party_id,\n      sales_organisation_id: opportunity?.sales_organisation_id,\n      sales_unit_party_id: opportunity?.sales_unit_party_id\n    };\n    this.editid = opportunity.documentId;\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        opportunity_id: _this.opportunity_id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.opportunitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.opportunitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onSubmit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.OpportunityOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.OpportunityOverviewForm.value\n      };\n      const data = {\n        name: value?.name,\n        expected_revenue_amount: value?.expected_revenue_amount,\n        expected_revenue_end_date: value?.expected_revenue_end_date ? _this2.formatDate(value.expected_revenue_end_date) : null,\n        prospect_party_id: value?.prospect_party_id,\n        primary_contact_party_id: value?.primary_contact_party_id,\n        group_code: value?.group_code,\n        origin_type_code: value?.origin_type_code,\n        weighted_expected_net_amount: value?.weighted_expected_net_amount,\n        life_cycle_status_code: value?.life_cycle_status_code,\n        probability_percent: value?.probability_percent,\n        main_employee_responsible_party_id: value?.main_employee_responsible_party_id,\n        sales_organisation_id: value?.sales_organisation_id,\n        sales_unit_party_id: value?.sales_unit_party_id,\n        expected_revenue_start_date: value?.expected_revenue_start_date ? _this2.formatDate(value.expected_revenue_start_date) : null\n      };\n      _this2.opportunitiesservice.updateOpportunity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Opportunity Updated successFully!'\n          });\n          _this2.opportunitiesservice.getOpportunityByID(_this2.opportunity_id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const d = date instanceof Date ? date : new Date(date);\n    if (isNaN(d.getTime())) return ''; // handle invalid date\n    const yyyy = d.getFullYear();\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\n    const dd = String(d.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  get f() {\n    return this.OpportunityOverviewForm.controls;\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  onReset() {\n    this.submitted = false;\n    this.OpportunityOverviewForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesOverviewComponent_Factory(t) {\n      return new (t || OpportunitiesOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesOverviewComponent,\n      selectors: [[\"app-opportunities-overview\"]],\n      decls: 28,\n      vars: 26,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"outlined\", \"styleClass\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"font-medium\", \"text-700\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [1, \"flex\", \"align-items-center\", \"w-full\", \"gap-2\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"currency\", \"optionLabel\", \"label\", \"placeholder\", \"Currency\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"expected_revenue_end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Expected Decision Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select Category\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select Source\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"weighted_expected_net_amount\", \"placeholder\", \"Weighted Value\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select Status\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_employee_responsible_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"sales_organisation_id\", \"type\", \"text\", \"formControlName\", \"sales_organisation_id\", \"placeholder\", \"Sales Organization'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_unit_party_id\", \"type\", \"text\", \"formControlName\", \"sales_unit_party_id\", \"placeholder\", \"Sales Unit\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"expected_revenue_start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Created Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"need_help\", 1, \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [1, \"p-error\"], [4, \"ngIf\"], [\"pSortableColumn\", \"note\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"note\"], [\"pSortableColumn\", \"createdAt\", 2, \"width\", \"10rem\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"updatedAt\", 2, \"width\", \"10rem\"], [\"field\", \"updatedAt\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function OpportunitiesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, OpportunitiesOverviewComponent_div_5_Template, 180, 31, \"div\", 4)(6, OpportunitiesOverviewComponent_form_6_Template, 137, 61, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-button\", 8);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_11_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"p-table\", 10);\n          i0.ɵɵtemplate(14, OpportunitiesOverviewComponent_ng_template_14_Template, 15, 0, \"ng-template\", 11)(15, OpportunitiesOverviewComponent_ng_template_15_Template, 12, 9, \"ng-template\", 12)(16, OpportunitiesOverviewComponent_ng_template_16_Template, 3, 0, \"ng-template\", 13)(17, OpportunitiesOverviewComponent_ng_template_17_Template, 3, 0, \"ng-template\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"p-dialog\", 15);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(19, OpportunitiesOverviewComponent_ng_template_19_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementStart(20, \"form\", 16)(21, \"div\", 17)(22, \"div\", 18);\n          i0.ɵɵelement(23, \"p-editor\", 19);\n          i0.ɵɵtemplate(24, OpportunitiesOverviewComponent_div_24_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_26_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_27_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Dialog, i10.Editor, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.InputSwitch, i13.Tooltip, i14.Calendar, i15.InputText, i5.AsyncPipe, i5.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1vdmVydmlldy9vcHBvcnR1bml0aWVzLW92ZXJ2aWV3LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUkscUJBQUE7RUFDQSxXQUFBO0FBQ0o7O0FBSVE7RUFDSSxrQkFBQTtBQURaO0FBR1k7RUFDSSw0QkFBQTtFQUNBLDJDQUFBO0FBRGhCO0FBR2dCO0VBQ0ksU0FBQTtBQURwQjtBQUtZO0VBQ0ksNEJBQUE7RUFDQSxpQkFBQTtBQUhoQiIsInNvdXJjZXNDb250ZW50IjpbIi5pbnZhbGlkLWZlZWRiYWNrLFxyXG4ucC1pbnB1dHRleHQ6aW52YWxpZCxcclxuLmlzLWNoZWNrYm94LWludmFsaWQsXHJcbi5wLWlucHV0dGV4dC5pcy1pbnZhbGlkIHtcclxuICAgIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICAgIHJpZ2h0OiAxMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAge1xyXG4gICAgLm5vdGUtcG9wdXAge1xyXG4gICAgICAgIC5wLWRpYWxvZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNTBweDtcclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "opportunity_id", "name", "ɵɵtextInterpolate1", "expected_revenue_amount", "expected_revenue_end_date", "ɵɵpipeBind2", "business_partner", "bp_full_name", "business_partner_owner", "getLabelFromDropdown", "group_code", "parent_opportunity", "origin_type_code", "weighted_expected_net_amount", "life_cycle_status_code", "result_reason_code", "days_in_sales_status", "probability_percent", "sales_organisation_id", "sales_unit_party_id", "expected_revenue_start_date", "last_change_date", "last_changed_by", "progress", "need_help", "ɵɵtemplate", "OpportunitiesOverviewComponent_form_6_div_11_div_1_Template", "ɵɵproperty", "submitted", "f", "errors", "OpportunitiesOverviewComponent_form_6_div_23_div_1_Template", "item_r3", "OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template", "bp_id", "OpportunitiesOverviewComponent_form_6_div_42_div_1_Template", "item_r4", "OpportunitiesOverviewComponent_form_6_ng_template_53_span_2_Template", "OpportunitiesOverviewComponent_form_6_div_54_div_1_Template", "OpportunitiesOverviewComponent_form_6_div_87_div_1_Template", "item_r5", "OpportunitiesOverviewComponent_form_6_ng_template_105_span_2_Template", "OpportunitiesOverviewComponent_form_6_div_106_div_1_Template", "ɵɵelement", "OpportunitiesOverviewComponent_form_6_div_11_Template", "OpportunitiesOverviewComponent_form_6_div_23_Template", "OpportunitiesOverviewComponent_form_6_ng_template_41_Template", "OpportunitiesOverviewComponent_form_6_div_42_Template", "OpportunitiesOverviewComponent_form_6_ng_template_53_Template", "OpportunitiesOverviewComponent_form_6_div_54_Template", "OpportunitiesOverviewComponent_form_6_div_87_Template", "OpportunitiesOverviewComponent_form_6_ng_template_105_Template", "OpportunitiesOverviewComponent_form_6_div_106_Template", "ɵɵlistener", "OpportunitiesOverviewComponent_form_6_Template_button_click_136_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "OpportunityOverviewForm", "ɵɵpureFunction1", "_c2", "currencies", "ɵɵpipeBind1", "accounts$", "accountLoading", "accountInput$", "contacts$", "contactLoading", "contactInput$", "dropdowns", "employees$", "employeeLoading", "employeeInput$", "OpportunitiesOverviewComponent_ng_template_15_Template_button_click_10_listener", "notes_r7", "_r6", "$implicit", "editNote", "OpportunitiesOverviewComponent_ng_template_15_Template_button_click_11_listener", "$event", "stopPropagation", "confirmRemove", "stripHtml", "note", "createdAt", "updatedAt", "OpportunitiesOverviewComponent_div_24_div_1_Template", "fNote", "OpportunitiesOverviewComponent", "constructor", "formBuilder", "opportunitiesservice", "messageservice", "confirmationservice", "router", "ngUnsubscribe", "notedetails", "defaultOptions", "saving", "editid", "isEditMode", "label", "value", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "group", "required", "prospect_party_id", "primary_contact_party_id", "main_employee_responsible_party_id", "currency", "NoteForm", "opportunityCategory", "opportunityStatus", "opportunitySource", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadOpportunityDropDown", "loadAccounts", "loadContacts", "loadEmployees", "opportunity", "pipe", "subscribe", "response", "notes", "fetchOverviewData", "target", "type", "getOpportunityDropdownOptions", "res", "data", "attr", "description", "code", "dropdownKey", "item", "find", "opt", "term", "params", "getPartners", "error", "console", "documentId", "patchValue", "existingopportunity", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getOpportunityByID", "createNote", "_this2", "formatDate", "updateOpportunity", "next", "date", "d", "Date", "isNaN", "getTime", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "confirm", "message", "header", "icon", "accept", "remove", "deleteNote", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "showDialog", "position", "controls", "toggleEdit", "onReset", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "OpportunitiesService", "i3", "MessageService", "ConfirmationService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "OpportunitiesOverviewComponent_Template", "rf", "ctx", "OpportunitiesOverviewComponent_Template_p_button_click_4_listener", "OpportunitiesOverviewComponent_div_5_Template", "OpportunitiesOverviewComponent_form_6_Template", "OpportunitiesOverviewComponent_Template_p_button_click_11_listener", "OpportunitiesOverviewComponent_ng_template_14_Template", "OpportunitiesOverviewComponent_ng_template_15_Template", "OpportunitiesOverviewComponent_ng_template_16_Template", "OpportunitiesOverviewComponent_ng_template_17_Template", "ɵɵtwoWayListener", "OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_18_listener", "ɵɵtwoWayBindingSet", "OpportunitiesOverviewComponent_ng_template_19_Template", "OpportunitiesOverviewComponent_div_24_Template", "OpportunitiesOverviewComponent_Template_button_click_26_listener", "OpportunitiesOverviewComponent_Template_button_click_27_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-overview',\r\n  templateUrl: './opportunities-overview.component.html',\r\n  styleUrl: './opportunities-overview.component.scss',\r\n})\r\nexport class OpportunitiesOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public notedetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingopportunity: any;\r\n  public opportunity_id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public currencies = [{ label: 'USD', value: 'USD' }];\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n\r\n  public OpportunityOverviewForm: FormGroup = this.formBuilder.group({\r\n    name: ['', [Validators.required]],\r\n    prospect_party_id: ['', [Validators.required]],\r\n    primary_contact_party_id: ['', [Validators.required]],\r\n    origin_type_code: [''],\r\n    expected_revenue_amount: ['', [Validators.required]],\r\n    weighted_expected_net_amount: [''],\r\n    expected_revenue_start_date: [''],\r\n    expected_revenue_end_date: [''],\r\n    life_cycle_status_code: ['', [Validators.required]],\r\n    probability_percent: [''],\r\n    group_code: [''],\r\n    main_employee_responsible_party_id: ['', [Validators.required]],\r\n    sales_organisation_id: [''],\r\n    sales_unit_party_id: [''],\r\n    currency: ['USD'],\r\n    need_help: [''],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Opportunities successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('opportunitiesMessage');\r\n      }\r\n    }, 100);\r\n    this.loadOpportunityDropDown(\r\n      'opportunityCategory',\r\n      'CRM_OPPORTUNITY_GROUP'\r\n    );\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.loadOpportunityDropDown(\r\n      'opportunitySource',\r\n      'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n    );\r\n    this.loadAccounts();\r\n    this.loadContacts();\r\n    this.loadEmployees();\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.opportunity_id = response?.opportunity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadContacts(): void {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Emit default options first\r\n      this.contactInput$.pipe(\r\n        debounceTime(300), // Prevent rapid requests on fast typing\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$eq]': 'BUP001',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response || []),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              console.error('Contact loading failed', error);\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadEmployees(): void {\r\n    this.employees$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.employeeInput$.pipe(\r\n        debounceTime(300), // Debounce user input to avoid spamming API\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$eq]': 'BUP003',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response || []),\r\n            tap(() => (this.employeeLoading = false)),\r\n            catchError((error) => {\r\n              console.error('Employee loading failed', error);\r\n              this.employeeLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(opportunity: any) {\r\n    this.existingopportunity = {\r\n      opportunity_id: opportunity?.opportunity_id,\r\n      name: opportunity?.name,\r\n      group_code: opportunity?.group_code,\r\n      origin_type_code: opportunity?.origin_type_code,\r\n      probability_percent: opportunity?.probability_percent,\r\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\r\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\r\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\r\n      prospect_party_id: opportunity?.prospect_party_id,\r\n      primary_contact_party_id: opportunity?.primary_contact_party_id,\r\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\r\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\r\n      main_employee_responsible_party_id:\r\n        opportunity?.main_employee_responsible_party_id,\r\n      sales_organisation_id: opportunity?.sales_organisation_id,\r\n      sales_unit_party_id: opportunity?.sales_unit_party_id,\r\n    };\r\n    this.editid = opportunity.documentId;\r\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      opportunity_id: this.opportunity_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.opportunitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.opportunitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.OpportunityOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.OpportunityOverviewForm.value };\r\n\r\n    const data = {\r\n      name: value?.name,\r\n      expected_revenue_amount: value?.expected_revenue_amount,\r\n      expected_revenue_end_date: value?.expected_revenue_end_date\r\n        ? this.formatDate(value.expected_revenue_end_date)\r\n        : null,\r\n      prospect_party_id: value?.prospect_party_id,\r\n      primary_contact_party_id: value?.primary_contact_party_id,\r\n      group_code: value?.group_code,\r\n      origin_type_code: value?.origin_type_code,\r\n      weighted_expected_net_amount: value?.weighted_expected_net_amount,\r\n      life_cycle_status_code: value?.life_cycle_status_code,\r\n      probability_percent: value?.probability_percent,\r\n      main_employee_responsible_party_id:\r\n        value?.main_employee_responsible_party_id,\r\n      sales_organisation_id: value?.sales_organisation_id,\r\n      sales_unit_party_id: value?.sales_unit_party_id,\r\n      expected_revenue_start_date: value?.expected_revenue_start_date\r\n        ? this.formatDate(value.expected_revenue_start_date)\r\n        : null,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .updateOpportunity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Opportunity Updated successFully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    if (!date) return '';\r\n    const d = date instanceof Date ? date : new Date(date);\r\n    if (isNaN(d.getTime())) return ''; // handle invalid date\r\n    const yyyy = d.getFullYear();\r\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(d.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.OpportunityOverviewForm.controls;\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.OpportunityOverviewForm.reset();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil':''\" iconPos=\"right\"\r\n            [outlined]=\"true\" class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold'\" (click)=\"toggleEdit()\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">tag</span> Opportunity ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.opportunity_id || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> Name\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.name || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">attach_money</span> Expected Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.expected_revenue_amount || '-'}}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Expected Decision\r\n                    Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_end_date ?\r\n                    (overviewDetails.expected_revenue_end_date | date: 'MM/dd/yyyy') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.business_partner?.bp_full_name\r\n                    || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">contact_phone</span> Primary Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name\r\n                    || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityCategory',\r\n                    overviewDetails?.group_code) || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_tree</span> Parent Opportunity\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.parent_opportunity || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">source</span> Source\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunitySource',\r\n                    overviewDetails?.origin_type_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">scale</span> Weighted Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.weighted_expected_net_amount ||\r\n                    '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">lens</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityStatus',\r\n                    overviewDetails?.life_cycle_status_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">fact_check</span> Reason for Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.result_reason_code || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">track_changes</span> Days in Sales\r\n                    Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.days_in_sales_status || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Probability\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.probability_percent || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Owner\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">business</span> Sales Organization\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_organisation_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">sell</span> Sales Unit\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_unit_party_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Create Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_start_date ?\r\n                    (overviewDetails.expected_revenue_start_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Last Updated Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.last_change_date ?\r\n                    (overviewDetails.last_change_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">update</span> Last Updated By\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.last_changed_by || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Progress\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.progress || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">help</span> Need Help\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.need_help || '-' }}</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"OpportunityOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">badge</span> Name\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"name\" type=\"text\" formControlName=\"name\" placeholder=\"Name\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['name'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['name'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['name'].errors &&\r\n                                f['name'].errors['required']\r\n                              \">\r\n                            Name is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">attach_money</span>Expected Value\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"expected_revenue_amount\"\r\n                            placeholder=\"Expected Value\" class=\"h-3rem w-full\"\r\n                            [ngClass]=\"{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"currency\" optionLabel=\"label\"\r\n                            placeholder=\"Currency\" styleClass=\"h-3rem w-full\"></p-dropdown>\r\n                        <div *ngIf=\"submitted && f['expected_revenue_amount'].errors\" class=\"p-error\">\r\n                            <div *ngIf=\"\r\n                                submitted &&\r\n                                f['expected_revenue_amount'].errors &&\r\n                                f['expected_revenue_amount'].errors['required']\r\n                              \">\r\n                                Expected Value is required.\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Expected Decision Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Expected Decision Date\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"prospect_party_id\" [typeahead]=\"accountInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['prospect_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['prospect_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['prospect_party_id'].errors &&\r\n                                f['prospect_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">contact_phone</span>Primary Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"primary_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['primary_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['primary_contact_party_id'].errors &&\r\n                                f['primary_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityCategory']\" formControlName=\"group_code\"\r\n                        placeholder=\"Select Category\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">source</span>Source\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunitySource']\" formControlName=\"origin_type_code\"\r\n                        placeholder=\"Select Source\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">scale</span>Weighted Value\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"weighted_expected_net_amount\"\r\n                            placeholder=\"Weighted Value\" class=\"h-3rem w-full\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"currency\" optionLabel=\"label\"\r\n                            placeholder=\"Currency\" styleClass=\"h-3rem w-full\"></p-dropdown>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">lens</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityStatus']\" formControlName=\"life_cycle_status_code\"\r\n                        placeholder=\"Select Status\" [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['life_cycle_status_code'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['life_cycle_status_code'].errors &&\r\n                                f['life_cycle_status_code'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">trending_up</span>Probability\r\n                    </label>\r\n                    <input pInputText id=\"probability_percent\" type=\"text\" formControlName=\"probability_percent\"\r\n                        placeholder=\"Probability'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">person</span>Owner\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"employees$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"employeeLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_employee_responsible_party_id\" [typeahead]=\"employeeInput$\"\r\n                        [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_employee_responsible_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_employee_responsible_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_employee_responsible_party_id'].errors &&\r\n                                f['main_employee_responsible_party_id'].errors['required']\r\n                              \">\r\n                            Owner is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">business</span>Sales Organization\r\n                    </label>\r\n                    <input pInputText id=\"sales_organisation_id\" type=\"text\" formControlName=\"sales_organisation_id\"\r\n                        placeholder=\"Sales Organization'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">sell</span>Sales Unit\r\n                    </label>\r\n                    <input pInputText id=\"sales_unit_party_id\" type=\"text\" formControlName=\"sales_unit_party_id\"\r\n                        placeholder=\"Sales Unit\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Created Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_start_date\" [showButtonBar]=\"true\"\r\n                        dateFormat=\"yy-mm-dd\" placeholder=\"Created Date\" [showIcon]=\"true\"\r\n                        styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">help</span>Need help\r\n                    </label>\r\n                    <p-inputSwitch formControlName=\"need_help\" class=\"h-3rem w-full\"></p-inputSwitch>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n<div class=\"p-3 w-full bg-white border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n        <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"notedetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"note\" style=\"width: 50rem;\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Note\r\n                            <p-sortIcon field=\"note\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Created At\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"updatedAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Updated At\r\n                            <p-sortIcon field=\"updatedAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg\" style=\"width: 7rem;\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ stripHtml(notes?.note) || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.createdAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AACtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICCHC,EALhB,CAAAC,cAAA,cAA6D,cAEV,cACnB,gBACmD,eACN;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAEvGF,EAFuG,CAAAG,YAAA,EAAM,EACnG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,cAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gCAE9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,yBACtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAG/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACX;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAC9C,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,2BACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,+BAEtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACc;IAE3EF,EAF2E,CAAAG,YAAA,EAAM,EACvE,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,6BACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,0BAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAA6C;IAE1GF,EAF0G,CAAAG,YAAA,EAAM,EACtG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAAsC;IAEnGF,EAFmG,CAAAG,YAAA,EAAM,EAC/F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,oBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAAuC;IAGxGF,EAHwG,CAAAG,YAAA,EAAM,EAChG,EACJ,EACJ;;;;IA1M2DH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,cAAA,SAA0C;IAQ1CR,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,IAAA,SAC/C;IAQ+CT,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,uBAAA,cACrD;IASqDX,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAK,yBAAA,IAAAZ,EAAA,CAAAa,WAAA,SAAAP,MAAA,CAAAC,eAAA,CAAAK,yBAAA,sBAE/C;IAQ+CZ,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAO,gBAAA,kBAAAR,MAAA,CAAAC,eAAA,CAAAO,gBAAA,CAAAC,YAAA,SAE/C;IAQ+Cf,EAAA,CAAAI,SAAA,GAG/C;IAH+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAS,sBAAA,kBAAAV,MAAA,CAAAC,eAAA,CAAAS,sBAAA,CAAAD,YAAA,SAG/C;IAQ+Cf,EAAA,CAAAI,SAAA,GACX;IADWJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAW,oBAAA,wBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,UAAA,SACX;IAQWlB,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAY,kBAAA,SAC/C;IAQ+CnB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAW,oBAAA,sBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAa,gBAAA,cAErD;IAQqDpB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAc,4BAAA,cAErD;IAQqDrB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAW,oBAAA,sBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,sBAAA,cAErD;IAQqDtB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAgB,kBAAA,cAGrD;IASqDvB,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,oBAAA,cACrD;IAQqDxB,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,mBAAA,cACrD;IAQqDzB,EAAA,CAAAI,SAAA,GACc;IADdJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAS,sBAAA,kBAAAV,MAAA,CAAAC,eAAA,CAAAS,sBAAA,CAAAD,YAAA,SACc;IAQdf,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,qBAAA,cACrD;IAQqD1B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAoB,mBAAA,cACrD;IAQqD3B,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAqB,2BAAA,IAAA5B,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAAqB,2BAAA,8BAE/C;IAQ+C5B,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAsB,gBAAA,IAAA7B,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAAsB,gBAAA,8BAE/C;IAQ+C7B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAuB,eAAA,SAA6C;IAQ7C9B,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAwB,QAAA,SAAsC;IAQtC/B,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAyB,SAAA,SAAuC;;;;;IAepFhC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAiC,UAAA,IAAAC,2DAAA,kBAIQ;IAGZlC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,SAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,SAAAC,MAAA,aAID;;;;;IAqBDtC,EAAA,CAAAC,cAAA,UAII;IACAD,EAAA,CAAAE,MAAA,oCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8E;IAC1ED,EAAA,CAAAiC,UAAA,IAAAM,2DAAA,kBAII;IAGRvC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIL;IAJKJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,4BAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,4BAAAC,MAAA,aAIL;;;;;IA8BDtC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,QAAA8B,OAAA,CAAAzB,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAQ,oEAAA,mBAAgC;;;;IAD1BzC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAmC,OAAA,CAAAE,KAAA,CAAgB;IACf1C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAmC,UAAA,SAAAK,OAAA,CAAAzB,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAwE;IACpED,EAAA,CAAAiC,UAAA,IAAAU,2DAAA,kBAIQ;IAGZ3C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,sBAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,sBAAAC,MAAA,aAID;;;;;IAkBDtC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,QAAAkC,OAAA,CAAA7B,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAY,oEAAA,mBAAgC;;;;IAD1B7C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAuC,OAAA,CAAAF,KAAA,CAAgB;IACf1C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAmC,UAAA,SAAAS,OAAA,CAAA7B,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAiC,UAAA,IAAAa,2DAAA,kBAIQ;IAGZ9C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,6BAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,6BAAAC,MAAA,aAID;;;;;IAoDLtC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA6E;IACzED,EAAA,CAAAiC,UAAA,IAAAc,2DAAA,kBAIQ;IAGZ/C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,2BAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,2BAAAC,MAAA,aAID;;;;;IA6BDtC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,QAAAsC,OAAA,CAAAjC,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAgB,qEAAA,mBAAgC;;;;IAD1BjD,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA2C,OAAA,CAAAN,KAAA,CAAgB;IACf1C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAmC,UAAA,SAAAa,OAAA,CAAAjC,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAyF;IACrFD,EAAA,CAAAiC,UAAA,IAAAiB,4DAAA,kBAIQ;IAGZlD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,uCAAAC,MAAA,IAAAhC,MAAA,CAAA+B,CAAA,uCAAAC,MAAA,aAID;;;;;;IAhMLtC,EALpB,CAAAC,cAAA,eAA+D,cAClB,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,aACtE;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAmD,SAAA,iBACwF;IACxFnD,EAAA,CAAAiC,UAAA,KAAAmB,qDAAA,kBAA2D;IAUnEpD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAK9CD,EAJA,CAAAmD,SAAA,iBAEqF,sBAGlB;IACnEnD,EAAA,CAAAiC,UAAA,KAAAoB,qDAAA,kBAA8E;IAW1FrD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,+BACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,sBACmG;IAE3GnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAC9E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAG6F;;IACzFD,EAAA,CAAAiC,UAAA,KAAAqB,6DAAA,0BAA2C;IAI/CtD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,KAAAsB,qDAAA,kBAAwE;IAUhFvD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,wBAC7E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAGoG;;IAChGD,EAAA,CAAAiC,UAAA,KAAAuB,6DAAA,0BAA2C;IAI/CxD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,KAAAwB,qDAAA,kBAA+E;IAUvFzD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,sBAEa;IAErBnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC1E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,sBAEa;IAErBnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAI9CD,EAHA,CAAAmD,SAAA,iBACyD,sBAGU;IAG/EnD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACpE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAmD,SAAA,sBAGa;IACbnD,EAAA,CAAAiC,UAAA,KAAAyB,qDAAA,kBAA6E;IAUrF1D,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,iBACuD;IAE/DnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACtE;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,sBAI8F;;IAC1FD,EAAA,CAAAiC,UAAA,MAAA0B,8DAAA,0BAA2C;IAI/C3D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,MAAA2B,sDAAA,kBAAyF;IAUjG5D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,4BAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,kBAC8D;IAEtEnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,kBACqD;IAE7DnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,sBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,uBAE4C;IAEpDnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,mBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAmD,SAAA,0BAAiF;IAG7FnD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAEvB;IAArBD,EAAA,CAAA6D,UAAA,mBAAAC,yEAAA;MAAA9D,EAAA,CAAA+D,aAAA,CAAAC,GAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAAA,OAAAjE,EAAA,CAAAkE,WAAA,CAAS5D,MAAA,CAAA6D,QAAA,EAAU;IAAA,EAAC;IAEhCnE,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IApPkBH,EAAA,CAAAmC,UAAA,cAAA7B,MAAA,CAAA8D,uBAAA,CAAqC;IASpBpE,EAAA,CAAAI,SAAA,IAA2D;IAA3DJ,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,SAAAC,MAAA,EAA2D;IAC/EtC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,SAAAC,MAAA,CAAmC;IAqBjCtC,EAAA,CAAAI,SAAA,IAA8E;IAA9EJ,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,4BAAAC,MAAA,EAA8E;IAEtEtC,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAiE,UAAA,CAAsB;IAE5BvE,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,4BAAAC,MAAA,CAAsD;IAkBRtC,EAAA,CAAAI,SAAA,GAAsB;IACrCJ,EADe,CAAAmC,UAAA,uBAAsB,kBACpB;IAUpCnC,EAAA,CAAAI,SAAA,GAA2B;IAG7BJ,EAHE,CAAAmC,UAAA,UAAAnC,EAAA,CAAAwE,WAAA,SAAAlE,MAAA,CAAAmE,SAAA,EAA2B,sBACxB,YAAAnE,MAAA,CAAAoE,cAAA,CAA2B,oBAAoB,cAAApE,MAAA,CAAAqE,aAAA,CACL,wBAAwB,YAAA3E,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,sBAAAC,MAAA,EACC;IAMtFtC,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,sBAAAC,MAAA,CAAgD;IAiBhCtC,EAAA,CAAAI,SAAA,GAA2B;IAG7BJ,EAHE,CAAAmC,UAAA,UAAAnC,EAAA,CAAAwE,WAAA,SAAAlE,MAAA,CAAAsE,SAAA,EAA2B,sBACxB,YAAAtE,MAAA,CAAAuE,cAAA,CAA2B,oBAAoB,cAAAvE,MAAA,CAAAwE,aAAA,CACE,wBAAwB,YAAA9E,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,6BAAAC,MAAA,EACC;IAM7FtC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,6BAAAC,MAAA,CAAuD;IAgBjDtC,EAAA,CAAAI,SAAA,GAA4C;IACtBJ,EADtB,CAAAmC,UAAA,YAAA7B,MAAA,CAAAyE,SAAA,wBAA4C,+BACQ;IASpD/E,EAAA,CAAAI,SAAA,GAA0C;IACtBJ,EADpB,CAAAmC,UAAA,YAAA7B,MAAA,CAAAyE,SAAA,sBAA0C,+BACQ;IAc9C/E,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAiE,UAAA,CAAsB;IAW1BvE,EAAA,CAAAI,SAAA,GAA0C;IAElDJ,EAFQ,CAAAmC,UAAA,YAAA7B,MAAA,CAAAyE,SAAA,sBAA0C,+BACQ,YAAA/E,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,2BAAAC,MAAA,EACmB;IAE3EtC,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,2BAAAC,MAAA,CAAqD;IA2BrCtC,EAAA,CAAAI,SAAA,IAA4B;IAI9CJ,EAJkB,CAAAmC,UAAA,UAAAnC,EAAA,CAAAwE,WAAA,UAAAlE,MAAA,CAAA0E,UAAA,EAA4B,sBACzB,YAAA1E,MAAA,CAAA2E,eAAA,CAA4B,oBAAoB,cAAA3E,MAAA,CAAA4E,cAAA,CACY,wBAC1D,YAAAlF,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAhE,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,uCAAAC,MAAA,EACkE;IAMvFtC,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,SAAA,IAAA9B,MAAA,CAAA+B,CAAA,uCAAAC,MAAA,CAAiE;IAkCbtC,EAAA,CAAAI,SAAA,IAAsB;IAC3BJ,EADK,CAAAmC,UAAA,uBAAsB,kBACV;;;;;IAiClEnC,EAFR,CAAAC,cAAA,SAAI,aAC8E,cAC/B;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAmD,SAAA,qBAAsC;IAE9CnD,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,cACP;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAmD,SAAA,qBAA2C;IAEnDnD,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,eACP;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAmD,SAAA,sBAA2C;IAEnDnD,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAClEF,EADkE,CAAAG,YAAA,EAAK,EAClE;;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAkC,kBAEA;IAA1BD,EAAA,CAAA6D,UAAA,mBAAAsB,gFAAA;MAAA,MAAAC,QAAA,GAAApF,EAAA,CAAA+D,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAAA,OAAAjE,EAAA,CAAAkE,WAAA,CAAS5D,MAAA,CAAAiF,QAAA,CAAAH,QAAA,CAAe;IAAA,EAAC;IAACpF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,kBAC8D;IAA1DD,EAAA,CAAA6D,UAAA,mBAAA2B,gFAAAC,MAAA;MAAA,MAAAL,QAAA,GAAApF,EAAA,CAAA+D,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAASwB,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA1F,EAAA,CAAAkE,WAAA,CAAE5D,MAAA,CAAAqF,aAAA,CAAAP,QAAA,CAAoB;IAAA,EAAE;IAErEpF,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAdGH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAsF,SAAA,CAAAR,QAAA,kBAAAA,QAAA,CAAAS,IAAA,cACJ;IAEI7F,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAa,WAAA,OAAAuE,QAAA,kBAAAA,QAAA,CAAAU,SAAA,8BACJ;IAEI9F,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAa,WAAA,OAAAuE,QAAA,kBAAAA,QAAA,CAAAW,SAAA,8BACJ;;;;;IAWA/F,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACnCF,EADmC,CAAAG,YAAA,EAAK,EACnC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACtDF,EADsD,CAAAG,YAAA,EAAK,EACtD;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAAiC,UAAA,IAAA+D,oDAAA,kBAA8C;IAClDhG,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA2F,KAAA,SAAA3D,MAAA,aAAsC;;;AD1gBhE,OAAM,MAAO4D,8BAA8B;EAwDzCC,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,mBAAwC,EACxCC,MAAc;IAJd,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IA5DR,KAAAC,aAAa,GAAG,IAAIpH,OAAO,EAAQ;IACpC,KAAAkB,eAAe,GAAQ,IAAI;IAC3B,KAAAmG,WAAW,GAAQ,IAAI;IAEvB,KAAAhC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAItF,OAAO,EAAU;IAErC,KAAAwF,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIzF,OAAO,EAAU;IAErC,KAAA4F,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAI7F,OAAO,EAAU;IACrC,KAAAsH,cAAc,GAAQ,EAAE;IACzB,KAAAvE,SAAS,GAAG,KAAK;IACjB,KAAAwE,MAAM,GAAG,KAAK;IAEd,KAAApG,cAAc,GAAW,EAAE;IAC3B,KAAAqG,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAvC,UAAU,GAAG,CAAC;MAAEwC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAAC;IAC7C,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAAjD,uBAAuB,GAAc,IAAI,CAACgC,WAAW,CAACkB,KAAK,CAAC;MACjE7G,IAAI,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACmI,QAAQ,CAAC,CAAC;MACjCC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACpI,UAAU,CAACmI,QAAQ,CAAC,CAAC;MAC9CE,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAACrI,UAAU,CAACmI,QAAQ,CAAC,CAAC;MACrDnG,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBT,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACmI,QAAQ,CAAC,CAAC;MACpDlG,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClCO,2BAA2B,EAAE,CAAC,EAAE,CAAC;MACjChB,yBAAyB,EAAE,CAAC,EAAE,CAAC;MAC/BU,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmI,QAAQ,CAAC,CAAC;MACnD9F,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBP,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBwG,kCAAkC,EAAE,CAAC,EAAE,EAAE,CAACtI,UAAU,CAACmI,QAAQ,CAAC,CAAC;MAC/D7F,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBgG,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB3F,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;IAEK,KAAA4F,QAAQ,GAAc,IAAI,CAACxB,WAAW,CAACkB,KAAK,CAAC;MAClDzB,IAAI,EAAE,CAAC,EAAE,EAAE,CAACzG,UAAU,CAACmI,QAAQ,CAAC;KACjC,CAAC;IAEK,KAAAxC,SAAS,GAA0B;MACxC8C,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;EAQE;EAEHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,sBAAsB,CAAC;MACrE,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC5B,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,sBAAsB,CAAC;MACnD;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;IACD,IAAI,CAACA,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACD,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACvC,oBAAoB,CAACwC,WAAW,CAClCC,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACmH,aAAa,CAAC,CAAC,CACnCsC,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAACxI,cAAc,GAAGwI,QAAQ,EAAExI,cAAc;MAC9C,IAAI,CAACD,eAAe,GAAGyI,QAAQ;MAC/B,IAAI,CAACtC,WAAW,GAAGsC,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAAC1I,eAAe,EAAE;QACxB,IAAI,CAAC2I,iBAAiB,CAAC,IAAI,CAAC3I,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEAkI,uBAAuBA,CAACU,MAAc,EAAEC,IAAY;IAClD,IAAI,CAAC/C,oBAAoB,CACtBgD,6BAA6B,CAACD,IAAI,CAAC,CACnCL,SAAS,CAAEO,GAAQ,IAAI;MACtB,IAAI,CAACvE,SAAS,CAACoE,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAE/J,GAAG,CAAEgK,IAAS,KAAM;QAC7BzC,KAAK,EAAEyC,IAAI,CAACC,WAAW;QACvBzC,KAAK,EAAEwC,IAAI,CAACE;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAzI,oBAAoBA,CAAC0I,WAAmB,EAAE3C,KAAa;IACrD,MAAM4C,IAAI,GAAG,IAAI,CAAC7E,SAAS,CAAC4E,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAAC9C,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAO4C,IAAI,EAAE7C,KAAK,IAAIC,KAAK;EAC7B;EAEQ0B,YAAYA,CAAA;IAClB,IAAI,CAACjE,SAAS,GAAGlF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACkH,cAAc,CAAC;IAAE;IACzB,IAAI,CAAChC,aAAa,CAACmE,IAAI,CACrBhJ,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC8E,cAAc,GAAG,IAAK,CAAC,EACvC/E,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC1D,oBAAoB,CAAC4D,WAAW,CAACD,MAAM,CAAC,CAAClB,IAAI,CACvDtJ,GAAG,CAAEwJ,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxCnJ,UAAU,CAAEqK,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAOzK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFM,QAAQ,CAAC,MAAO,IAAI,CAAC2E,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQiE,YAAYA,CAAA;IAClB,IAAI,CAAC/D,SAAS,GAAGrF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACkH,cAAc,CAAC;IAAE;IACzB,IAAI,CAAC7B,aAAa,CAACgE,IAAI,CACrBhJ,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACiF,cAAc,GAAG,IAAK,CAAC,EACvClF,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,8BAA8B,EAAE,QAAQ;QACxC,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC1D,oBAAoB,CAAC4D,WAAW,CAACD,MAAM,CAAC,CAAClB,IAAI,CACvDtJ,GAAG,CAAEwJ,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACtCpJ,GAAG,CAAC,MAAO,IAAI,CAACiF,cAAc,GAAG,KAAM,CAAC,EACxChF,UAAU,CAAEqK,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACrF,cAAc,GAAG,KAAK;QAC3B,OAAOpF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQmJ,aAAaA,CAAA;IACnB,IAAI,CAAC5D,UAAU,GAAGzF,MAAM,CACtBE,EAAE,CAAC,IAAI,CAACkH,cAAc,CAAC;IAAE;IACzB,IAAI,CAACzB,cAAc,CAAC4D,IAAI,CACtBhJ,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACqF,eAAe,GAAG,IAAK,CAAC,EACxCtF,SAAS,CAAEoK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,8BAA8B,EAAE,QAAQ;QACxC,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC1D,oBAAoB,CAAC4D,WAAW,CAACD,MAAM,CAAC,CAAClB,IAAI,CACvDtJ,GAAG,CAAEwJ,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACtCpJ,GAAG,CAAC,MAAO,IAAI,CAACqF,eAAe,GAAG,KAAM,CAAC,EACzCpF,UAAU,CAAEqK,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjF,eAAe,GAAG,KAAK;QAC5B,OAAOxF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEA8F,QAAQA,CAACM,IAAS;IAChB,IAAI,CAACoB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGxB,IAAI,EAAEuE,UAAU;IAClC,IAAI,CAACxC,QAAQ,CAACyC,UAAU,CAACxE,IAAI,CAAC;EAChC;EAEAqD,iBAAiBA,CAACL,WAAgB;IAChC,IAAI,CAACyB,mBAAmB,GAAG;MACzB9J,cAAc,EAAEqI,WAAW,EAAErI,cAAc;MAC3CC,IAAI,EAAEoI,WAAW,EAAEpI,IAAI;MACvBS,UAAU,EAAE2H,WAAW,EAAE3H,UAAU;MACnCE,gBAAgB,EAAEyH,WAAW,EAAEzH,gBAAgB;MAC/CK,mBAAmB,EAAEoH,WAAW,EAAEpH,mBAAmB;MACrDd,uBAAuB,EAAEkI,WAAW,EAAElI,uBAAuB;MAC7DiB,2BAA2B,EAAEiH,WAAW,EAAEjH,2BAA2B;MACrEhB,yBAAyB,EAAEiI,WAAW,EAAEjI,yBAAyB;MACjE4G,iBAAiB,EAAEqB,WAAW,EAAErB,iBAAiB;MACjDC,wBAAwB,EAAEoB,WAAW,EAAEpB,wBAAwB;MAC/DpG,4BAA4B,EAAEwH,WAAW,EAAExH,4BAA4B;MACvEC,sBAAsB,EAAEuH,WAAW,EAAEvH,sBAAsB;MAC3DoG,kCAAkC,EAChCmB,WAAW,EAAEnB,kCAAkC;MACjDhG,qBAAqB,EAAEmH,WAAW,EAAEnH,qBAAqB;MACzDC,mBAAmB,EAAEkH,WAAW,EAAElH;KACnC;IACD,IAAI,CAACkF,MAAM,GAAGgC,WAAW,CAACuB,UAAU;IACpC,IAAI,CAAChG,uBAAuB,CAACiG,UAAU,CAAC,IAAI,CAACC,mBAAmB,CAAC;EACnE;EAEMC,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACrD,aAAa,GAAG,IAAI;MACzBqD,KAAI,CAACvD,WAAW,GAAG,IAAI;MAEvB,IAAIuD,KAAI,CAAC5C,QAAQ,CAAC8C,OAAO,EAAE;QACzBF,KAAI,CAACvD,WAAW,GAAG,IAAI;QACvB;MACF;MAEAuD,KAAI,CAACpD,UAAU,GAAG,IAAI;MACtB,MAAMJ,KAAK,GAAG;QAAE,GAAGwD,KAAI,CAAC5C,QAAQ,CAACZ;MAAK,CAAE;MAExC,MAAMuC,IAAI,GAAG;QACX/I,cAAc,EAAEgK,KAAI,CAAChK,cAAc;QACnCqF,IAAI,EAAEmB,KAAK,EAAEnB;OACd;MAED,IAAI2E,KAAI,CAACnD,UAAU,EAAE;QACnBmD,KAAI,CAACnE,oBAAoB,CACtBsE,UAAU,CAACH,KAAI,CAACnD,UAAU,EAAEkC,IAAI,CAAC,CACjCT,IAAI,CAACxJ,SAAS,CAACkL,KAAI,CAAC/D,aAAa,CAAC,CAAC,CACnCsC,SAAS,CAAC;UACT6B,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACpD,UAAU,GAAG,KAAK;YACvBoD,KAAI,CAACvD,WAAW,GAAG,KAAK;YACxBuD,KAAI,CAAC5C,QAAQ,CAACiD,KAAK,EAAE;YACrBL,KAAI,CAAClE,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFiC,KAAI,CAACnE,oBAAoB,CACtByE,kBAAkB,CAACN,KAAI,CAAChK,cAAc,CAAC,CACvCsI,IAAI,CAACxJ,SAAS,CAACkL,KAAI,CAAC/D,aAAa,CAAC,CAAC,CACnCsC,SAAS,EAAE;UAChB,CAAC;UACDmB,KAAK,EAAGZ,GAAQ,IAAI;YAClBkB,KAAI,CAACpD,UAAU,GAAG,KAAK;YACvBoD,KAAI,CAACvD,WAAW,GAAG,IAAI;YACvBuD,KAAI,CAAClE,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACLiC,KAAI,CAACnE,oBAAoB,CACtB0E,UAAU,CAACxB,IAAI,CAAC,CAChBT,IAAI,CAACxJ,SAAS,CAACkL,KAAI,CAAC/D,aAAa,CAAC,CAAC,CACnCsC,SAAS,CAAC;UACT6B,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACpD,UAAU,GAAG,KAAK;YACvBoD,KAAI,CAACvD,WAAW,GAAG,KAAK;YACxBuD,KAAI,CAAC5C,QAAQ,CAACiD,KAAK,EAAE;YACrBL,KAAI,CAAClE,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFiC,KAAI,CAACnE,oBAAoB,CACtByE,kBAAkB,CAACN,KAAI,CAAChK,cAAc,CAAC,CACvCsI,IAAI,CAACxJ,SAAS,CAACkL,KAAI,CAAC/D,aAAa,CAAC,CAAC,CACnCsC,SAAS,EAAE;UAChB,CAAC;UACDmB,KAAK,EAAGZ,GAAQ,IAAI;YAClBkB,KAAI,CAACpD,UAAU,GAAG,KAAK;YACvBoD,KAAI,CAACvD,WAAW,GAAG,IAAI;YACvBuD,KAAI,CAAClE,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEMpE,QAAQA,CAAA;IAAA,IAAA6G,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAAC5I,SAAS,GAAG,IAAI;MAErB,IAAI4I,MAAI,CAAC5G,uBAAuB,CAACsG,OAAO,EAAE;QACxC;MACF;MAEAM,MAAI,CAACpE,MAAM,GAAG,IAAI;MAClB,MAAMI,KAAK,GAAG;QAAE,GAAGgE,MAAI,CAAC5G,uBAAuB,CAAC4C;MAAK,CAAE;MAEvD,MAAMuC,IAAI,GAAG;QACX9I,IAAI,EAAEuG,KAAK,EAAEvG,IAAI;QACjBE,uBAAuB,EAAEqG,KAAK,EAAErG,uBAAuB;QACvDC,yBAAyB,EAAEoG,KAAK,EAAEpG,yBAAyB,GACvDoK,MAAI,CAACC,UAAU,CAACjE,KAAK,CAACpG,yBAAyB,CAAC,GAChD,IAAI;QACR4G,iBAAiB,EAAER,KAAK,EAAEQ,iBAAiB;QAC3CC,wBAAwB,EAAET,KAAK,EAAES,wBAAwB;QACzDvG,UAAU,EAAE8F,KAAK,EAAE9F,UAAU;QAC7BE,gBAAgB,EAAE4F,KAAK,EAAE5F,gBAAgB;QACzCC,4BAA4B,EAAE2F,KAAK,EAAE3F,4BAA4B;QACjEC,sBAAsB,EAAE0F,KAAK,EAAE1F,sBAAsB;QACrDG,mBAAmB,EAAEuF,KAAK,EAAEvF,mBAAmB;QAC/CiG,kCAAkC,EAChCV,KAAK,EAAEU,kCAAkC;QAC3ChG,qBAAqB,EAAEsF,KAAK,EAAEtF,qBAAqB;QACnDC,mBAAmB,EAAEqF,KAAK,EAAErF,mBAAmB;QAC/CC,2BAA2B,EAAEoF,KAAK,EAAEpF,2BAA2B,GAC3DoJ,MAAI,CAACC,UAAU,CAACjE,KAAK,CAACpF,2BAA2B,CAAC,GAClD;OACL;MAEDoJ,MAAI,CAAC3E,oBAAoB,CACtB6E,iBAAiB,CAACF,MAAI,CAACnE,MAAM,EAAE0C,IAAI,CAAC,CACpCT,IAAI,CAACxJ,SAAS,CAAC0L,MAAI,CAACvE,aAAa,CAAC,CAAC,CACnCsC,SAAS,CAAC;QACToC,IAAI,EAAGnC,QAAa,IAAI;UACtBgC,MAAI,CAAC1E,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFyC,MAAI,CAAC3E,oBAAoB,CACtByE,kBAAkB,CAACE,MAAI,CAACxK,cAAc,CAAC,CACvCsI,IAAI,CAACxJ,SAAS,CAAC0L,MAAI,CAACvE,aAAa,CAAC,CAAC,CACnCsC,SAAS,EAAE;UACdiC,MAAI,CAAClE,UAAU,GAAG,KAAK;QACzB,CAAC;QACDoD,KAAK,EAAGZ,GAAQ,IAAI;UAClB0B,MAAI,CAACpE,MAAM,GAAG,KAAK;UACnBoE,MAAI,CAAC1E,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEA0C,UAAUA,CAACG,IAAS;IAClB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,CAAC,GAAGD,IAAI,YAAYE,IAAI,GAAGF,IAAI,GAAG,IAAIE,IAAI,CAACF,IAAI,CAAC;IACtD,IAAIG,KAAK,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGJ,CAAC,CAACK,WAAW,EAAE;IAC5B,MAAMC,EAAE,GAAGC,MAAM,CAACP,CAAC,CAACQ,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMC,EAAE,GAAGH,MAAM,CAACP,CAAC,CAACW,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/C,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEApG,aAAaA,CAACiE,IAAS;IACrB,IAAI,CAACrD,mBAAmB,CAAC0F,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC1C,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA0C,MAAMA,CAAC1C,IAAS;IACd,IAAI,CAACvD,oBAAoB,CACtBkG,UAAU,CAAC3C,IAAI,CAACQ,UAAU,CAAC,CAC3BtB,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACmH,aAAa,CAAC,CAAC,CACnCsC,SAAS,CAAC;MACToC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7E,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClC,oBAAoB,CACtByE,kBAAkB,CAAC,IAAI,CAACtK,cAAc,CAAC,CACvCsI,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACmH,aAAa,CAAC,CAAC,CACnCsC,SAAS,EAAE;MAChB,CAAC;MACDmB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC5D,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA3C,SAASA,CAAC4G,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAC9F,YAAY,GAAG8F,QAAQ;IAC5B,IAAI,CAAC/F,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACS,QAAQ,CAACiD,KAAK,EAAE;EACvB;EAEA,IAAI5E,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC2B,QAAQ,CAACqF,QAAQ;EAC/B;EAEA,IAAI5K,CAACA,CAAA;IACH,OAAO,IAAI,CAAC+B,uBAAuB,CAAC6I,QAAQ;EAC9C;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACpG,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAqG,OAAOA,CAAA;IACL,IAAI,CAAC/K,SAAS,GAAG,KAAK;IACtB,IAAI,CAACgC,uBAAuB,CAACyG,KAAK,EAAE;EACtC;EAEAuC,WAAWA,CAAA;IACT,IAAI,CAAC3G,aAAa,CAAC0E,IAAI,EAAE;IACzB,IAAI,CAAC1E,aAAa,CAACmE,QAAQ,EAAE;EAC/B;;;uBA/cW1E,8BAA8B,EAAAlG,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvN,EAAA,CAAAqN,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAzN,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3N,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAAE,mBAAA,GAAA5N,EAAA,CAAAqN,iBAAA,CAAAQ,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9B5H,8BAA8B;MAAA6H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBnCrO,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,kBACqG;UAAzBD,EAAA,CAAA6D,UAAA,mBAAA0K,kEAAA;YAAA,OAASD,GAAA,CAAApB,UAAA,EAAY;UAAA,EAAC;UACtGlN,EAFI,CAAAG,YAAA,EACqG,EACnG;UAmNNH,EAlNA,CAAAiC,UAAA,IAAAuM,6CAAA,oBAA6D,IAAAC,8CAAA,qBAkNE;UAqPnEzO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAA4D,aAC2B,YAChC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,mBAC2D;UADrCD,EAAA,CAAA6D,UAAA,mBAAA6K,mEAAA;YAAA,OAASJ,GAAA,CAAAvB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAEvD/M,EAFI,CAAAG,YAAA,EAC2D,EACzD;UAGFH,EADJ,CAAAC,cAAA,cAAuB,mBAEW;UAkD1BD,EAhDA,CAAAiC,UAAA,KAAA0M,sDAAA,2BAAgC,KAAAC,sDAAA,2BAwBQ,KAAAC,sDAAA,0BAmBF,KAAAC,sDAAA,0BAKD;UAOjD9O,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAA+O,gBAAA,2BAAAC,2EAAAvJ,MAAA;YAAAzF,EAAA,CAAAiP,kBAAA,CAAAX,GAAA,CAAArH,WAAA,EAAAxB,MAAA,MAAA6I,GAAA,CAAArH,WAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9CzF,EAAA,CAAAiC,UAAA,KAAAiN,sDAAA,0BAAgC;UAMxBlP,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAAmD,SAAA,oBAC0E;UAC1EnD,EAAA,CAAAiC,UAAA,KAAAkN,8CAAA,kBACmE;UAI3EnP,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAA6D,UAAA,mBAAAuL,iEAAA;YAAA,OAAAd,GAAA,CAAArH,WAAA,GAAuB,KAAK;UAAA,EAAC;UAACjH,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAA6D,UAAA,mBAAAwL,iEAAA;YAAA,OAASf,GAAA,CAAA/D,YAAA,EAAc;UAAA,EAAC;UAIxCvK,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UAxiBOH,EAAA,CAAAI,SAAA,GAAuC;UACXJ,EAD5B,CAAAmC,UAAA,UAAAmM,GAAA,CAAAxH,UAAA,oBAAuC,UAAAwH,GAAA,CAAAxH,UAAA,uBAAyC,kBACrE,sCAAsD;UAEzE9G,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAmC,UAAA,UAAAmM,GAAA,CAAAxH,UAAA,CAAiB;UAkNhB9G,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAmC,UAAA,SAAAmM,GAAA,CAAAxH,UAAA,CAAgB;UA0Pf9G,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAmC,UAAA,oCAAmC,iBAAiB;UAI/CnC,EAAA,CAAAI,SAAA,GAAqB;UAAwCJ,EAA7D,CAAAmC,UAAA,UAAAmM,GAAA,CAAA5H,WAAA,CAAqB,YAAyB,mBAAiC;UA2D7C1G,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAsP,UAAA,CAAAtP,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAA4B;UAArExP,EAAA,CAAAmC,UAAA,eAAc;UAACnC,EAAA,CAAAyP,gBAAA,YAAAnB,GAAA,CAAArH,WAAA,CAAyB;UAAmDjH,EAArB,CAAAmC,UAAA,qBAAoB,oBAAoB;UAM9GnC,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAmC,UAAA,cAAAmM,GAAA,CAAA1G,QAAA,CAAsB;UAGkD5H,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAsP,UAAA,CAAAtP,EAAA,CAAAuP,eAAA,KAAAG,GAAA,EAA6B;UAC3F1P,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAgK,GAAA,CAAAnH,aAAA,IAAAmH,GAAA,CAAArI,KAAA,SAAA3D,MAAA,EAAmE;UACjEtC,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAmC,UAAA,SAAAmM,GAAA,CAAAnH,aAAA,IAAAmH,GAAA,CAAArI,KAAA,SAAA3D,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}