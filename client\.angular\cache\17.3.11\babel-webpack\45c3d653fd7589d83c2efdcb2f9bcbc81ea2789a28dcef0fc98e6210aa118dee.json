{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction SalesCallOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"label\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 25)(10, \"div\", 26)(11, \"label\", 27)(12, \"span\", 28);\n    i0.ɵɵtext(13, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Subject \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"div\", 26)(19, \"label\", 27)(20, \"span\", 28);\n    i0.ɵɵtext(21, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \"Transaction Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 29);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"label\", 27)(28, \"span\", 28);\n    i0.ɵɵtext(29, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 30)(32, \"a\", 31);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 25)(35, \"div\", 26)(36, \"label\", 27)(37, \"span\", 28);\n    i0.ɵɵtext(38, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 29);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 25)(43, \"div\", 26)(44, \"label\", 27)(45, \"span\", 28);\n    i0.ɵɵtext(46, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 29);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 25)(51, \"div\", 26)(52, \"label\", 27)(53, \"span\", 28);\n    i0.ɵɵtext(54, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 29);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 25)(59, \"div\", 26)(60, \"label\", 27)(61, \"span\", 28);\n    i0.ɵɵtext(62, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(63, \" Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 29);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 25)(67, \"div\", 26)(68, \"label\", 27)(69, \"span\", 28);\n    i0.ɵɵtext(70, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(71, \" Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 29);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(75, \"div\", 25)(76, \"div\", 26)(77, \"label\", 27)(78, \"span\", 28);\n    i0.ɵɵtext(79, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(80, \" End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 29);\n    i0.ɵɵtext(82);\n    i0.ɵɵpipe(83, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(84, \"div\", 25)(85, \"div\", 26)(86, \"label\", 27)(87, \"span\", 28);\n    i0.ɵɵtext(88, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 29);\n    i0.ɵɵtext(91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(92, \"div\", 25)(93, \"div\", 26)(94, \"label\", 27)(95, \"span\", 28);\n    i0.ɵɵtext(96, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 29);\n    i0.ɵɵtext(99);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(100, \"div\", 25)(101, \"div\", 26)(102, \"label\", 27)(103, \"span\", 28);\n    i0.ɵɵtext(104, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \" Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"div\", 29);\n    i0.ɵɵtext(107);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(108, \"div\", 25)(109, \"div\", 26)(110, \"label\", 27)(111, \"span\", 28);\n    i0.ɵɵtext(112, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(113, \" Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 29);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(116, \"div\", 25)(117, \"div\", 26)(118, \"label\", 27)(119, \"span\", 28);\n    i0.ɵɵtext(120, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"div\", 29);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(124, \"div\", 25)(125, \"div\", 26)(126, \"label\", 27)(127, \"span\", 28);\n    i0.ɵɵtext(128, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(129, \" Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 29);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(132, \"div\", 25)(133, \"div\", 26)(134, \"label\", 27)(135, \"span\", 28);\n    i0.ɵɵtext(136, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(137, \" Customer TimeZone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"div\", 29);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.subject) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityDocumentType\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.document_type) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.phone_call_category) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activitydisposition\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.disposition_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.reason) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date) ? i0.ɵɵpipeBind3(74, 18, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date) ? i0.ɵɵpipeBind3(83, 22, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.brand) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_group) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.ranking) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityInitiatorCode\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.initiator_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_timezone) || \"-\", \" \");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallOverviewComponent_form_6_ng_template_12_span_2_Template, 2, 1, \"span\", 53);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_13_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors && ctx_r0.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_24_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallOverviewComponent_form_6_ng_template_24_span_2_Template, 2, 1, \"span\", 53);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_25_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors && ctx_r0.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_35_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors && ctx_r0.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_45_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors && ctx_r0.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_84_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.bp_full_name, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallOverviewComponent_form_6_ng_template_84_span_2_Template, 2, 1, \"span\", 53);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.bp_full_name);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_85_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Owner is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_85_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"owner_party_id\"].errors && ctx_r0.f[\"owner_party_id\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_116_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_116_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors && ctx_r0.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_126_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_126_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors && ctx_r0.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 32)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"label\", 33)(5, \"span\", 34);\n    i0.ɵɵtext(6, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \"Account \");\n    i0.ɵɵelementStart(8, \"span\", 35);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ng-select\", 36);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, SalesCallOverviewComponent_form_6_ng_template_12_Template, 3, 2, \"ng-template\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SalesCallOverviewComponent_form_6_div_13_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 25)(15, \"div\", 26)(16, \"label\", 33)(17, \"span\", 34);\n    i0.ɵɵtext(18, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \"Contact \");\n    i0.ɵɵelementStart(20, \"span\", 35);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"ng-select\", 39);\n    i0.ɵɵpipe(23, \"async\");\n    i0.ɵɵtemplate(24, SalesCallOverviewComponent_form_6_ng_template_24_Template, 3, 2, \"ng-template\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, SalesCallOverviewComponent_form_6_div_25_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 25)(27, \"div\", 26)(28, \"label\", 33)(29, \"span\", 34);\n    i0.ɵɵtext(30, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Subject \");\n    i0.ɵɵelementStart(32, \"span\", 35);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"input\", 40);\n    i0.ɵɵtemplate(35, SalesCallOverviewComponent_form_6_div_35_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 25)(37, \"div\", 26)(38, \"label\", 33)(39, \"span\", 34);\n    i0.ɵɵtext(40, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \"Category \");\n    i0.ɵɵelementStart(42, \"span\", 35);\n    i0.ɵɵtext(43, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(44, \"p-dropdown\", 41);\n    i0.ɵɵtemplate(45, SalesCallOverviewComponent_form_6_div_45_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 25)(47, \"div\", 26)(48, \"label\", 33)(49, \"span\", 34);\n    i0.ɵɵtext(50, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \"Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"p-dropdown\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 25)(54, \"div\", 26)(55, \"label\", 33)(56, \"span\", 34);\n    i0.ɵɵtext(57, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \"Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 25)(61, \"div\", 26)(62, \"label\", 33)(63, \"span\", 34);\n    i0.ɵɵtext(64, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \"Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"p-calendar\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 25)(68, \"div\", 26)(69, \"label\", 33)(70, \"span\", 34);\n    i0.ɵɵtext(71, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \"End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"p-calendar\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26)(76, \"label\", 33)(77, \"span\", 34);\n    i0.ɵɵtext(78, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \"Owner \");\n    i0.ɵɵelementStart(80, \"span\", 35);\n    i0.ɵɵtext(81, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"ng-select\", 46);\n    i0.ɵɵpipe(83, \"async\");\n    i0.ɵɵtemplate(84, SalesCallOverviewComponent_form_6_ng_template_84_Template, 3, 2, \"ng-template\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(85, SalesCallOverviewComponent_form_6_div_85_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 25)(87, \"div\", 26)(88, \"label\", 33)(89, \"span\", 34);\n    i0.ɵɵtext(90, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(91, \"Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(92, \"input\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(93, \"div\", 25)(94, \"div\", 26)(95, \"label\", 33)(96, \"span\", 34);\n    i0.ɵɵtext(97, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \"Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(99, \"input\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(100, \"div\", 25)(101, \"div\", 26)(102, \"label\", 33)(103, \"span\", 34);\n    i0.ɵɵtext(104, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \"Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(106, \"input\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"div\", 25)(108, \"div\", 26)(109, \"label\", 33)(110, \"span\", 34);\n    i0.ɵɵtext(111, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(112, \"Status \");\n    i0.ɵɵelementStart(113, \"span\", 35);\n    i0.ɵɵtext(114, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(115, \"p-dropdown\", 50);\n    i0.ɵɵtemplate(116, SalesCallOverviewComponent_form_6_div_116_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(117, \"div\", 25)(118, \"div\", 26)(119, \"label\", 33)(120, \"span\", 34);\n    i0.ɵɵtext(121, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \"Type \");\n    i0.ɵɵelementStart(123, \"span\", 35);\n    i0.ɵɵtext(124, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(125, \"p-dropdown\", 51);\n    i0.ɵɵtemplate(126, SalesCallOverviewComponent_form_6_div_126_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(127, \"div\", 52)(128, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_form_6_Template_button_click_128_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.SalesCallOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(11, 44, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(50, _c2, ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(23, 46, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(52, _c2, ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c2, ctx_r0.submitted && ctx_r0.f[\"subject\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityCategory\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(56, _c2, ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activitydisposition\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(83, 48, ctx_r0.employees$))(\"hideSelected\", true)(\"loading\", ctx_r0.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.employeeInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(58, _c2, ctx_r0.submitted && ctx_r0.f[\"owner_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"owner_party_id\"].errors);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(60, _c2, ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(62, _c2, ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 55)(2, \"div\", 56);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 58)(6, \"div\", 56);\n    i0.ɵɵtext(7, \" Created At \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 60)(10, \"div\", 56);\n    i0.ɵɵtext(11, \" Updated At \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 62);\n    i0.ɵɵtext(14, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 64)(10, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_15_Template_button_click_10_listener() {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_15_Template_button_click_11_listener($event) {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.stripHtml(notes_r7 == null ? null : notes_r7.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 3, notes_r7 == null ? null : notes_r7.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 6, notes_r7 == null ? null : notes_r7.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 67);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 67);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_div_24_div_1_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class SalesCallOverviewComponent {\n  constructor(formBuilder, activitiesservice, messageservice, confirmationservice) {\n    this.formBuilder = formBuilder;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.notedetails = null;\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityStatus: [],\n      activityCategory: [],\n      activitydisposition: [],\n      activityInitiatorCode: []\n    };\n    this.SalesCallOverviewForm = this.formBuilder.group({\n      main_account_party_id: ['', [Validators.required]],\n      main_contact_party_id: ['', [Validators.required]],\n      subject: ['', [Validators.required]],\n      phone_call_category: ['', [Validators.required]],\n      disposition_code: [''],\n      reason: [''],\n      start_date: [''],\n      end_date: [''],\n      owner_party_id: ['', [Validators.required]],\n      brand: [''],\n      ranking: [''],\n      customer_group: [''],\n      initiator_code: ['', [Validators.required]],\n      activity_status: ['', [Validators.required]]\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this.compareById = (a, b) => a === b;\n  }\n  ngOnInit() {\n    // sales call successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('salescallMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('salescallMessage');\n      }\n    }, 100);\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n    this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n    this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n    this.loadAccounts();\n    this.loadContacts();\n    this.loadEmployees();\n    this.activitiesservice.activity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.id = response?.activity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(activity) {\n    this.existingActivity = {\n      main_account_party_id: activity?.main_account_party_id,\n      main_contact_party_id: activity?.main_contact_party_id,\n      subject: activity?.subject,\n      phone_call_category: activity?.phone_call_category,\n      disposition_code: activity?.disposition_code,\n      reason: activity?.reason,\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\n      owner_party_id: activity?.owner_party_id,\n      brand: activity?.brand,\n      customer_group: activity?.customer_group,\n      ranking: activity?.ranking,\n      initiator_code: activity?.initiator_code,\n      activity_status: activity?.activity_status\n    };\n    this.editid = activity.documentId;\n    this.SalesCallOverviewForm.patchValue(this.existingActivity);\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.accountInput$.pipe(distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\n        [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.accountLoading = false), catchError(error => {\n        this.accountLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.contactLoading = false), catchError(error => {\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadEmployees() {\n    this.employees$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.employeeInput$.pipe(distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP003',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.employeeLoading = false), catchError(error => {\n        this.employeeLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        activity_id: _this.id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.activitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.activitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onSubmit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.SalesCallOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.SalesCallOverviewForm.value\n      };\n      const data = {\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        phone_call_category: value?.phone_call_category,\n        start_date: value?.start_date ? _this2.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this2.formatDate(value.end_date) : null,\n        disposition_code: value?.disposition_code,\n        initiator_code: value?.initiator_code,\n        owner_party_id: value?.owner_party_id,\n        activity_status: value?.activity_status,\n        reason: value?.reason,\n        brand: value?.brand,\n        customer_group: value?.customer_group,\n        ranking: value?.ranking\n      };\n      _this2.activitiesservice.updateActivity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Sales Call Updated successFully!'\n          });\n          _this2.activitiesservice.getActivityByID(_this2.id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.isEditMode = true;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.SalesCallOverviewForm.controls;\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  onReset() {\n    this.submitted = false;\n    this.SalesCallOverviewForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallOverviewComponent_Factory(t) {\n      return new (t || SalesCallOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallOverviewComponent,\n      selectors: [[\"app-sales-call-overview\"]],\n      decls: 28,\n      vars: 26,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-blue-600\", \"cursor-pointer\"], [2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"formControlName\", \"disposition_code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"Select Disposition Code\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"reason\", \"type\", \"text\", \"formControlName\", \"reason\", \"placeholder\", \"Reason\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Call Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"End Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"owner_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"brand\", \"type\", \"text\", \"formControlName\", \"brand\", \"placeholder\", \"Brand'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"customer_group\", \"type\", \"text\", \"formControlName\", \"customer_group\", \"placeholder\", \"Customer Group'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"ranking\", \"type\", \"text\", \"formControlName\", \"ranking\", \"placeholder\", \"Ranking'\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [4, \"ngIf\"], [1, \"p-error\"], [\"pSortableColumn\", \"note\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"note\"], [\"pSortableColumn\", \"createdAt\", 2, \"width\", \"10rem\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"updatedAt\", 2, \"width\", \"10rem\"], [\"field\", \"updatedAt\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function SalesCallOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, SalesCallOverviewComponent_div_5_Template, 140, 26, \"div\", 4)(6, SalesCallOverviewComponent_form_6_Template, 129, 64, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-button\", 8);\n          i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_p_button_click_11_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"p-table\", 10);\n          i0.ɵɵtemplate(14, SalesCallOverviewComponent_ng_template_14_Template, 15, 0, \"ng-template\", 11)(15, SalesCallOverviewComponent_ng_template_15_Template, 12, 9, \"ng-template\", 12)(16, SalesCallOverviewComponent_ng_template_16_Template, 3, 0, \"ng-template\", 13)(17, SalesCallOverviewComponent_ng_template_17_Template, 3, 0, \"ng-template\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"p-dialog\", 15);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallOverviewComponent_Template_p_dialog_visibleChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(19, SalesCallOverviewComponent_ng_template_19_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementStart(20, \"form\", 16)(21, \"div\", 17)(22, \"div\", 18);\n          i0.ɵɵelement(23, \"p-editor\", 19);\n          i0.ɵɵtemplate(24, SalesCallOverviewComponent_div_24_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_button_click_26_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_button_click_27_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i4.AsyncPipe, i4.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy9zYWxlcy1jYWxsL3NhbGVzLWNhbGwtZGV0YWlscy9zYWxlcy1jYWxsLW92ZXJ2aWV3L3NhbGVzLWNhbGwtb3ZlcnZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7RUFJSSxxQkFBQTtFQUNBLFdBQUE7QUFDSjs7QUFJUTtFQUNJLGtCQUFBO0FBRFo7QUFHWTtFQUNJLDRCQUFBO0VBQ0EsMkNBQUE7QUFEaEI7QUFHZ0I7RUFDSSxTQUFBO0FBRHBCO0FBS1k7RUFDSSw0QkFBQTtFQUNBLGlCQUFBO0FBSGhCIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gICAgY29sb3I6IHZhcigtLXJlZC01MDApO1xyXG4gICAgcmlnaHQ6IDEwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCB7XHJcbiAgICAubm90ZS1wb3B1cCB7XHJcbiAgICAgICAgLnAtZGlhbG9nIHtcclxuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1MHB4O1xyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXN1cmZhY2UtMTAwKTtcclxuXHJcbiAgICAgICAgICAgICAgICBoNCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS43MTRyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "catchError", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "activity_id", "subject", "getLabelFromDropdown", "document_type", "ɵɵproperty", "business_partner", "documentId", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "bp_full_name", "business_partner_contact", "phone_call_category", "disposition_code", "reason", "start_date", "ɵɵpipeBind3", "end_date", "business_partner_owner", "brand", "customer_group", "ranking", "activity_status", "initiator_code", "customer_timezone", "item_r3", "ɵɵtemplate", "SalesCallOverviewComponent_form_6_ng_template_12_span_2_Template", "bp_id", "SalesCallOverviewComponent_form_6_div_13_div_1_Template", "submitted", "f", "errors", "item_r4", "SalesCallOverviewComponent_form_6_ng_template_24_span_2_Template", "SalesCallOverviewComponent_form_6_div_25_div_1_Template", "SalesCallOverviewComponent_form_6_div_35_div_1_Template", "SalesCallOverviewComponent_form_6_div_45_div_1_Template", "item_r5", "SalesCallOverviewComponent_form_6_ng_template_84_span_2_Template", "SalesCallOverviewComponent_form_6_div_85_div_1_Template", "SalesCallOverviewComponent_form_6_div_116_div_1_Template", "SalesCallOverviewComponent_form_6_div_126_div_1_Template", "SalesCallOverviewComponent_form_6_ng_template_12_Template", "SalesCallOverviewComponent_form_6_div_13_Template", "SalesCallOverviewComponent_form_6_ng_template_24_Template", "SalesCallOverviewComponent_form_6_div_25_Template", "ɵɵelement", "SalesCallOverviewComponent_form_6_div_35_Template", "SalesCallOverviewComponent_form_6_div_45_Template", "SalesCallOverviewComponent_form_6_ng_template_84_Template", "SalesCallOverviewComponent_form_6_div_85_Template", "SalesCallOverviewComponent_form_6_div_116_Template", "SalesCallOverviewComponent_form_6_div_126_Template", "ɵɵlistener", "SalesCallOverviewComponent_form_6_Template_button_click_128_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "SalesCallOverviewForm", "ɵɵpipeBind1", "accounts$", "accountLoading", "compareById", "accountInput$", "ɵɵpureFunction1", "_c2", "contacts$", "contactLoading", "contactInput$", "dropdowns", "employees$", "employeeLoading", "employeeInput$", "SalesCallOverviewComponent_ng_template_15_Template_button_click_10_listener", "notes_r7", "_r6", "$implicit", "editNote", "SalesCallOverviewComponent_ng_template_15_Template_button_click_11_listener", "$event", "stopPropagation", "confirmRemove", "stripHtml", "note", "ɵɵpipeBind2", "createdAt", "updatedAt", "SalesCallOverviewComponent_div_24_div_1_Template", "fNote", "SalesCallOverviewComponent", "constructor", "formBuilder", "activitiesservice", "messageservice", "confirmationservice", "ngUnsubscribe", "defaultOptions", "saving", "id", "editid", "isEditMode", "notedetails", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "activityDocumentType", "activityStatus", "activityCategory", "activitydisposition", "activityInitiatorCode", "group", "main_account_party_id", "required", "main_contact_party_id", "owner_party_id", "NoteForm", "a", "b", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadActivityDropDown", "loadAccounts", "loadContacts", "loadEmployees", "activity", "pipe", "subscribe", "response", "notes", "fetchOverviewData", "target", "type", "getActivityDropdownOptions", "res", "data", "attr", "label", "description", "value", "code", "dropdownKey", "item", "find", "opt", "patchValue", "existingActivity", "Date", "term", "params", "getPartners", "error", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getActivityByID", "createNote", "_this2", "formatDate", "updateActivity", "next", "confirm", "message", "header", "icon", "accept", "remove", "deleteNote", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showDialog", "position", "toggleEdit", "onReset", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivitiesService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "SalesCallOverviewComponent_Template", "rf", "ctx", "SalesCallOverviewComponent_Template_p_button_click_4_listener", "SalesCallOverviewComponent_div_5_Template", "SalesCallOverviewComponent_form_6_Template", "SalesCallOverviewComponent_Template_p_button_click_11_listener", "SalesCallOverviewComponent_ng_template_14_Template", "SalesCallOverviewComponent_ng_template_15_Template", "SalesCallOverviewComponent_ng_template_16_Template", "SalesCallOverviewComponent_ng_template_17_Template", "ɵɵtwoWayListener", "SalesCallOverviewComponent_Template_p_dialog_visibleChange_18_listener", "ɵɵtwoWayBindingSet", "SalesCallOverviewComponent_ng_template_19_Template", "SalesCallOverviewComponent_div_24_Template", "SalesCallOverviewComponent_Template_button_click_26_listener", "SalesCallOverviewComponent_Template_button_click_27_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-overview\\sales-call-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-overview\\sales-call-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { MessageService,ConfirmationService } from 'primeng/api';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n} from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-sales-call-overview',\r\n  templateUrl: './sales-call-overview.component.html',\r\n  styleUrl: './sales-call-overview.component.scss',\r\n})\r\nexport class SalesCallOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingActivity: any;\r\n  public id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public notedetails: any = null;\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityStatus: [],\r\n    activityCategory: [],\r\n    activitydisposition: [],\r\n    activityInitiatorCode: [],\r\n  };\r\n\r\n  public SalesCallOverviewForm: FormGroup = this.formBuilder.group({\r\n    main_account_party_id: ['', [Validators.required]],\r\n    main_contact_party_id: ['', [Validators.required]],\r\n    subject: ['', [Validators.required]],\r\n    phone_call_category: ['', [Validators.required]],\r\n    disposition_code: [''],\r\n    reason: [''],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    owner_party_id: ['', [Validators.required]],\r\n    brand: [''],\r\n    ranking: [''],\r\n    customer_group: [''],\r\n    initiator_code: ['', [Validators.required]],\r\n    activity_status: ['', [Validators.required]],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice:ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // sales call successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('salescallMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('salescallMessage');\r\n      }\r\n    }, 100);\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activityCategory',\r\n      'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activitydisposition',\r\n      'CRM_ACTIVITY_DISPOSITION_CODE'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activityInitiatorCode',\r\n      'CRM_ACTIVITY_INITIATOR_CODE'\r\n    );\r\n    this.loadAccounts();\r\n    this.loadContacts();\r\n    this.loadEmployees();\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.id = response?.activity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(activity: any) {\r\n    this.existingActivity = {\r\n      main_account_party_id: activity?.main_account_party_id,\r\n      main_contact_party_id: activity?.main_contact_party_id,\r\n      subject: activity?.subject,\r\n      phone_call_category: activity?.phone_call_category,\r\n      disposition_code: activity?.disposition_code,\r\n      reason: activity?.reason,\r\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\r\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\r\n      owner_party_id: activity?.owner_party_id,\r\n      brand: activity?.brand,\r\n      customer_group: activity?.customer_group,\r\n      ranking: activity?.ranking,\r\n      initiator_code: activity?.initiator_code,\r\n      activity_status: activity?.activity_status,\r\n    };\r\n\r\n    this.editid = activity.documentId;\r\n    this.SalesCallOverviewForm.patchValue(this.existingActivity);\r\n  }\r\n\r\n  private loadAccounts() {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.accountInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\r\n            [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.accountLoading = false)),\r\n            catchError((error) => {\r\n              this.accountLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadContacts() {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.contactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadEmployees() {\r\n    this.employees$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.employeeInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP003',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.employeeLoading = false)),\r\n            catchError((error) => {\r\n              this.employeeLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      activity_id: this.id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.activitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.activitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.SalesCallOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.SalesCallOverviewForm.value };\r\n\r\n    const data = {\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      phone_call_category: value?.phone_call_category,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      disposition_code: value?.disposition_code,\r\n      initiator_code: value?.initiator_code,\r\n      owner_party_id: value?.owner_party_id,\r\n      activity_status: value?.activity_status,\r\n      reason: value?.reason,\r\n      brand: value?.brand,\r\n      customer_group: value?.customer_group,\r\n      ranking: value?.ranking,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .updateActivity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Sales Call Updated successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.isEditMode = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.SalesCallOverviewForm.controls;\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.SalesCallOverviewForm.reset();\r\n  }\r\n\r\n  compareById = (a: any, b: any) => a === b;\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil':''\" iconPos=\"right\"\r\n            class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold px-3'\" (click)=\"toggleEdit()\" [rounded]=\"true\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.activity_id || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">subject</span> Subject\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.subject || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">description</span>Transaction Type\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityDocumentType',\r\n                    overviewDetails?.document_type) || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-blue-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/account/' + overviewDetails?.business_partner?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\">\r\n                        {{ overviewDetails?.business_partner?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_contact?.bp_full_name || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    getLabelFromDropdown('activityCategory',overviewDetails?.phone_call_category)\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">code</span> Disposition Code\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    getLabelFromDropdown('activitydisposition',overviewDetails?.disposition_code)\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">info</span> Reason\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.reason || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> Call Date/Time\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.start_date ?\r\n                    (overviewDetails?.start_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> End Date/Time\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.end_date ?\r\n                    (overviewDetails?.end_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Owner\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">branding_watermark</span> Brand\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.brand || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">group</span> Customer Group\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.customer_group || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">emoji_events</span> Ranking\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.ranking || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">check_circle</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityStatus',\r\n                    overviewDetails?.activity_status) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">label</span> Type\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityInitiatorCode',\r\n                    overviewDetails?.initiator_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">access_time</span> Customer TimeZone\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.customer_timezone || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"SalesCallOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_account_party_id\" [compareWith]=\"compareById\" [typeahead]=\"accountInput$\"\r\n                        [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_account_party_id'].errors &&\r\n                                f['main_account_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">person</span>Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_contact_party_id'].errors &&\r\n                                f['main_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">subject</span> Subject\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['subject'].errors &&\r\n                                    f['subject'].errors['required']\r\n                                  \">\r\n                            Subject is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"phone_call_category\"\r\n                        placeholder=\"Select Category\" optionLabel=\"label\" optionValue=\"value\"\r\n                        [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['phone_call_category'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['phone_call_category'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['phone_call_category'].errors &&\r\n                                f['phone_call_category'].errors['required']\r\n                              \">\r\n                            Category is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">code</span>Disposition Code\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activitydisposition']\" formControlName=\"disposition_code\"\r\n                        optionLabel=\"label\" optionValue=\"value\" placeholder=\"Select Disposition Code\"\r\n                        [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">info</span>Reason\r\n                    </label>\r\n                    <input pInputText id=\"reason\" type=\"text\" formControlName=\"reason\" placeholder=\"Reason\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>Call Date/Time\r\n                    </label>\r\n                    <p-calendar formControlName=\"start_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Call Date/Time\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>End Date/Time\r\n                    </label>\r\n                    <p-calendar formControlName=\"end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"End Date/Time\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Owner\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"employees$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"employeeLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"owner_party_id\" [typeahead]=\"employeeInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['owner_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['owner_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['owner_party_id'].errors &&\r\n                                f['owner_party_id'].errors['required']\r\n                              \">\r\n                            Owner is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">branding_watermark</span>Brand\r\n                    </label>\r\n                    <input pInputText id=\"brand\" type=\"text\" formControlName=\"brand\" placeholder=\"Brand'\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">group</span>Customer Group\r\n                    </label>\r\n                    <input pInputText id=\"customer_group\" type=\"text\" formControlName=\"customer_group\"\r\n                        placeholder=\"Customer Group'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">emoji_events</span>Ranking\r\n                    </label>\r\n                    <input pInputText id=\"ranking\" type=\"text\" formControlName=\"ranking\" placeholder=\"Ranking'\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">check_circle</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                        placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['activity_status'].errors &&\r\n                                f['activity_status'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">label</span>Type\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityInitiatorCode']\" formControlName=\"initiator_code\"\r\n                        placeholder=\"Select a Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['initiator_code'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['initiator_code'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['initiator_code'].errors &&\r\n                                f['initiator_code'].errors['required']\r\n                              \">\r\n                            Type is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n        <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"notedetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"note\" style=\"width: 50rem;\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Note\r\n                            <p-sortIcon field=\"note\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Created At\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"updatedAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Updated At\r\n                            <p-sortIcon field=\"updatedAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg\" style=\"width: 7rem;\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ stripHtml(notes?.note) || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.createdAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAGtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,QACL,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;ICAHC,EAJhB,CAAAC,cAAA,cAA6D,cACV,cACnB,gBACqD,eACR;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,WAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAEhGF,EAFgG,CAAAG,YAAA,EAAM,EAC5F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,yBACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAAyE,aAElB;IAC/CD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,0BAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,uBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAC3F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,yBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAGZF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IAtK2DH,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,WAAA,SAC/C;IAQ+CR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,OAAA,SAAmC;IAQnCT,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,yBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,aAAA,SAE/C;IASCX,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAY,UAAA,gCAAAN,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAC,UAAA,iBAAAd,EAAA,CAAAe,aAAA,CAA0F;IAEzFf,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAI,YAAA,cACJ;IAUiDjB,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,wBAAA,kBAAAZ,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAD,YAAA,SAE/C;IAQ+CjB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,qBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAY,mBAAA,SAEzC;IAQyCnB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,wBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAa,gBAAA,SAEzC;IAQyCpB,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAc,MAAA,SAC/C;IAQ+CrB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,UAAA,IAAAtB,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,UAAA,mDAGrD;IAQqDtB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,QAAA,IAAAxB,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,QAAA,mDAGrD;IAQqDxB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,sBAAA,kBAAAnB,MAAA,CAAAC,eAAA,CAAAkB,sBAAA,CAAAR,YAAA,cAGrD;IAQqDjB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,KAAA,cAGrD;IAQqD1B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAoB,cAAA,cACrD;IAQqD3B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAqB,OAAA,cACrD;IAQqD5B,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,mBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAsB,eAAA,cAErD;IAQqD7B,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,0BAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAuB,cAAA,cAErD;IAQqD9B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAwB,iBAAA,cACrD;;;;;IAmBY/B,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAgB,kBAAA,QAAAgB,OAAA,CAAAf,YAAA,KAAyB;;;;;IAD1DjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAC,gEAAA,mBAAgC;;;;IAD1BlC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA2B,OAAA,CAAAG,KAAA,CAAgB;IACfnC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAAoB,OAAA,CAAAf,YAAA,CAAuB;;;;;IAIlCjB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAiC,UAAA,IAAAG,uDAAA,kBAIQ;IAGZpC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,aAID;;;;;IAkBDvC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAgB,kBAAA,QAAAwB,OAAA,CAAAvB,YAAA,KAAyB;;;;;IAD1DjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAQ,gEAAA,mBAAgC;;;;IAD1BzC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAmC,OAAA,CAAAL,KAAA,CAAgB;IACfnC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAA4B,OAAA,CAAAvB,YAAA,CAAuB;;;;;IAIlCjB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAiC,UAAA,IAAAS,uDAAA,kBAIQ;IAGZ1C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,aAID;;;;;IAeLvC,EAAA,CAAAC,cAAA,UAIY;IACRD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAiC,UAAA,IAAAU,uDAAA,kBAIY;IAGhB3C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIG;IAJHJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,YAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,YAAAC,MAAA,aAIG;;;;;IAkBTvC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAiC,UAAA,IAAAW,uDAAA,kBAIQ;IAGZ5C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,wBAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,wBAAAC,MAAA,aAID;;;;;IAyDDvC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAgB,kBAAA,QAAA6B,OAAA,CAAA5B,YAAA,KAAyB;;;;;IAD1DjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAiC,UAAA,IAAAa,gEAAA,mBAAgC;;;;IAD1B9C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAwC,OAAA,CAAAV,KAAA,CAAgB;IACfnC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAAiC,OAAA,CAAA5B,YAAA,CAAuB;;;;;IAIlCjB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAiC,UAAA,IAAAc,uDAAA,kBAIQ;IAGZ/C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,aAID;;;;;IA4CLvC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAiC,UAAA,IAAAe,wDAAA,kBAIQ;IAGZhD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,oBAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,oBAAAC,MAAA,aAID;;;;;IAiBLvC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAiC,UAAA,IAAAgB,wDAAA,kBAIQ;IAGZjD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,aAID;;;;;;IA3NLvC,EALpB,CAAAC,cAAA,eAA6D,cAChB,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC9E;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIiF;;IAC7ED,EAAA,CAAAiC,UAAA,KAAAiB,yDAAA,0BAA2C;IAI/ClD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,KAAAkB,iDAAA,kBAA4E;IAUpFnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBACtE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAGiG;;IAC7FD,EAAA,CAAAiC,UAAA,KAAAmB,yDAAA,0BAA2C;IAI/CpD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,KAAAoB,iDAAA,kBAA4E;IAUpFrD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAsD,SAAA,iBAC2F;IAC3FtD,EAAA,CAAAiC,UAAA,KAAAsB,iDAAA,kBAA8D;IAUtEvD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAsD,SAAA,sBAIa;IACbtD,EAAA,CAAAiC,UAAA,KAAAuB,iDAAA,kBAA0E;IAUlFxD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,yBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,sBAGa;IAErBtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,iBAC4B;IAEpCtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,sBAC2F;IAEnGtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,sBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,sBAC0F;IAElGtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,cAC9E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAG0F;;IACtFD,EAAA,CAAAiC,UAAA,KAAAwB,yDAAA,0BAA2C;IAI/CzD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAiC,UAAA,KAAAyB,iDAAA,kBAAqE;IAU7E1D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,cACtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,iBAC4B;IAEpCtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,iBAC0D;IAElEtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAsD,SAAA,kBAC4B;IAEpCtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAC5E;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAsD,SAAA,uBAGa;IACbtD,EAAA,CAAAiC,UAAA,MAAA0B,kDAAA,kBAAsE;IAU9E3D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,cACrE;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAsD,SAAA,uBAGa;IACbtD,EAAA,CAAAiC,UAAA,MAAA2B,kDAAA,kBAAqE;IAWjF5D,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAEvB;IAArBD,EAAA,CAAA6D,UAAA,mBAAAC,qEAAA;MAAA9D,EAAA,CAAA+D,aAAA,CAAAC,GAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAAA,OAAAjE,EAAA,CAAAkE,WAAA,CAAS5D,MAAA,CAAA6D,QAAA,EAAU;IAAA,EAAC;IAEhCnE,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IA3OkBH,EAAA,CAAAY,UAAA,cAAAN,MAAA,CAAA8D,qBAAA,CAAmC;IAQtBpE,EAAA,CAAAI,SAAA,IAA2B;IAI7CJ,EAJkB,CAAAY,UAAA,UAAAZ,EAAA,CAAAqE,WAAA,SAAA/D,MAAA,CAAAgE,SAAA,EAA2B,sBACxB,YAAAhE,MAAA,CAAAiE,cAAA,CAA2B,oBAAoB,gBAAAjE,MAAA,CAAAkE,WAAA,CACD,cAAAlE,MAAA,CAAAmE,aAAA,CAA4B,wBACxE,YAAAzE,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,EACqD;IAM1EvC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,CAAoD;IAiBpCvC,EAAA,CAAAI,SAAA,GAA2B;IAG7BJ,EAHE,CAAAY,UAAA,UAAAZ,EAAA,CAAAqE,WAAA,SAAA/D,MAAA,CAAAsE,SAAA,EAA2B,sBACxB,YAAAtE,MAAA,CAAAuE,cAAA,CAA2B,oBAAoB,cAAAvE,MAAA,CAAAwE,aAAA,CACD,wBAAwB,YAAA9E,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,EACC;IAM1FvC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,0BAAAC,MAAA,CAAoD;IAkBhCvC,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,YAAAC,MAAA,EAA8D;IAClFvC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,YAAAC,MAAA,CAAsC;IAiBhCvC,EAAA,CAAAI,SAAA,GAAyC;IAGjDJ,EAHQ,CAAAY,UAAA,YAAAN,MAAA,CAAAyE,SAAA,qBAAyC,+BAEnB,YAAA/E,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,wBAAAC,MAAA,EAC4C;IAExEvC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,wBAAAC,MAAA,CAAkD;IAgB5CvC,EAAA,CAAAI,SAAA,GAA4C;IAEpDJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAAyE,SAAA,wBAA4C,+BAEtB;IAmBO/E,EAAA,CAAAI,SAAA,IAAsB;IAC9BJ,EADQ,CAAAY,UAAA,uBAAsB,kBACb;IAQXZ,EAAA,CAAAI,SAAA,GAAsB;IAC7BJ,EADO,CAAAY,UAAA,uBAAsB,kBACZ;IAS3BZ,EAAA,CAAAI,SAAA,GAA4B;IAG9BJ,EAHE,CAAAY,UAAA,UAAAZ,EAAA,CAAAqE,WAAA,SAAA/D,MAAA,CAAA0E,UAAA,EAA4B,sBACzB,YAAA1E,MAAA,CAAA2E,eAAA,CAA4B,oBAAoB,cAAA3E,MAAA,CAAA4E,cAAA,CACR,wBAAwB,YAAAlF,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,EACA;IAMnFvC,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,CAA6C;IA4CvCvC,EAAA,CAAAI,SAAA,IAAuC;IAE/CJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAAyE,SAAA,mBAAuC,YAAA/E,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,oBAAAC,MAAA,EAEuB;IAEpEvC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,oBAAAC,MAAA,CAA8C;IAiBxCvC,EAAA,CAAAI,SAAA,GAA8C;IAEtDJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAAyE,SAAA,0BAA8C,YAAA/E,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,EAEe;IAEnEvC,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,mBAAAC,MAAA,CAA6C;;;;;IAiC/CvC,EAFR,CAAAC,cAAA,SAAI,aAC8E,cAC/B;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAsD,SAAA,qBAAsC;IAE9CtD,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,cACP;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAsD,SAAA,qBAA2C;IAEnDtD,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,eACP;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAsD,SAAA,sBAA2C;IAEnDtD,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAClEF,EADkE,CAAAG,YAAA,EAAK,EAClE;;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAkC,kBAEA;IAA1BD,EAAA,CAAA6D,UAAA,mBAAAsB,4EAAA;MAAA,MAAAC,QAAA,GAAApF,EAAA,CAAA+D,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAAA,OAAAjE,EAAA,CAAAkE,WAAA,CAAS5D,MAAA,CAAAiF,QAAA,CAAAH,QAAA,CAAe;IAAA,EAAC;IAACpF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,kBAC8D;IAA1DD,EAAA,CAAA6D,UAAA,mBAAA2B,4EAAAC,MAAA;MAAA,MAAAL,QAAA,GAAApF,EAAA,CAAA+D,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAiE,aAAA;MAASwB,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA1F,EAAA,CAAAkE,WAAA,CAAE5D,MAAA,CAAAqF,aAAA,CAAAP,QAAA,CAAoB;IAAA,EAAE;IAErEpF,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAdGH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAsF,SAAA,CAAAR,QAAA,kBAAAA,QAAA,CAAAS,IAAA,cACJ;IAEI7F,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAA8F,WAAA,OAAAV,QAAA,kBAAAA,QAAA,CAAAW,SAAA,8BACJ;IAEI/F,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAA8F,WAAA,OAAAV,QAAA,kBAAAA,QAAA,CAAAY,SAAA,8BACJ;;;;;IAWAhG,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACnCF,EADmC,CAAAG,YAAA,EAAK,EACnC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACtDF,EADsD,CAAAG,YAAA,EAAK,EACtD;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAAiC,UAAA,IAAAgE,gDAAA,kBAA8C;IAClDjG,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA4F,KAAA,SAAA3D,MAAA,aAAsC;;;ADhehE,OAAM,MAAO4D,0BAA0B;EAuDrCC,YACUC,WAAwB,EACxBC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAuC;IAHvC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA1DrB,KAAAC,aAAa,GAAG,IAAIlH,OAAO,EAAQ;IACpC,KAAAgB,eAAe,GAAQ,IAAI;IAE3B,KAAAgE,cAAc,GAAG,KAAK;IACtB,KAAAE,aAAa,GAAG,IAAIlF,OAAO,EAAU;IAErC,KAAAsF,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIvF,OAAO,EAAU;IAErC,KAAA0F,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAI3F,OAAO,EAAU;IACrC,KAAAmH,cAAc,GAAQ,EAAE;IACzB,KAAArE,SAAS,GAAG,KAAK;IACjB,KAAAsE,MAAM,GAAG,KAAK;IAEd,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAArC,SAAS,GAA0B;MACxCsC,oBAAoB,EAAE,EAAE;MACxBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE;KACxB;IAEM,KAAArD,qBAAqB,GAAc,IAAI,CAACiC,WAAW,CAACqB,KAAK,CAAC;MAC/DC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACrI,UAAU,CAACsI,QAAQ,CAAC,CAAC;MAClDC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACvI,UAAU,CAACsI,QAAQ,CAAC,CAAC;MAClDnH,OAAO,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACsI,QAAQ,CAAC,CAAC;MACpCzG,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACsI,QAAQ,CAAC,CAAC;MAChDxG,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdsG,cAAc,EAAE,CAAC,EAAE,EAAE,CAACxI,UAAU,CAACsI,QAAQ,CAAC,CAAC;MAC3ClG,KAAK,EAAE,CAAC,EAAE,CAAC;MACXE,OAAO,EAAE,CAAC,EAAE,CAAC;MACbD,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBG,cAAc,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACsI,QAAQ,CAAC,CAAC;MAC3C/F,eAAe,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACsI,QAAQ,CAAC;KAC5C,CAAC;IAEK,KAAAG,QAAQ,GAAc,IAAI,CAAC1B,WAAW,CAACqB,KAAK,CAAC;MAClD7B,IAAI,EAAE,CAAC,EAAE,EAAE,CAACvG,UAAU,CAACsI,QAAQ,CAAC;KACjC,CAAC;IAoZF,KAAApD,WAAW,GAAG,CAACwD,CAAM,EAAEC,CAAM,KAAKD,CAAC,KAAKC,CAAC;EA7YtC;EAEHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC;MACjE,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC7B,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,kBAAkB,CAAC;MAC/C;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CACvB,qBAAqB,EACrB,+BAA+B,CAChC;IACD,IAAI,CAACA,oBAAoB,CACvB,uBAAuB,EACvB,6BAA6B,CAC9B;IACD,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACxC,iBAAiB,CAACyC,QAAQ,CAC5BC,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACiH,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAACtC,EAAE,GAAGsC,QAAQ,EAAE1I,WAAW;MAC/B,IAAI,CAACD,eAAe,GAAG2I,QAAQ;MAC/B,IAAI,CAACnC,WAAW,GAAGmC,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAAC5I,eAAe,EAAE;QACxB,IAAI,CAAC6I,iBAAiB,CAAC,IAAI,CAAC7I,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEAoI,oBAAoBA,CAACU,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAChD,iBAAiB,CACnBiD,0BAA0B,CAACD,IAAI,CAAC,CAChCL,SAAS,CAAEO,GAAQ,IAAI;MACtB,IAAI,CAACzE,SAAS,CAACsE,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAE/J,GAAG,CAAEgK,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEApJ,oBAAoBA,CAACqJ,WAAmB,EAAEF,KAAa;IACrD,MAAMG,IAAI,GAAG,IAAI,CAACjF,SAAS,CAACgF,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACL,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOG,IAAI,EAAEL,KAAK,IAAIE,KAAK;EAC7B;EAEAtE,QAAQA,CAACM,IAAS;IAChB,IAAI,CAACmB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGvB,IAAI,EAAE/E,UAAU;IAClC,IAAI,CAACiH,QAAQ,CAACoC,UAAU,CAACtE,IAAI,CAAC;EAChC;EAEAuD,iBAAiBA,CAACL,QAAa;IAC7B,IAAI,CAACqB,gBAAgB,GAAG;MACtBzC,qBAAqB,EAAEoB,QAAQ,EAAEpB,qBAAqB;MACtDE,qBAAqB,EAAEkB,QAAQ,EAAElB,qBAAqB;MACtDpH,OAAO,EAAEsI,QAAQ,EAAEtI,OAAO;MAC1BU,mBAAmB,EAAE4H,QAAQ,EAAE5H,mBAAmB;MAClDC,gBAAgB,EAAE2H,QAAQ,EAAE3H,gBAAgB;MAC5CC,MAAM,EAAE0H,QAAQ,EAAE1H,MAAM;MACxBC,UAAU,EAAEyH,QAAQ,EAAEzH,UAAU,GAAG,IAAI+I,IAAI,CAACtB,QAAQ,EAAEzH,UAAU,CAAC,GAAG,IAAI;MACxEE,QAAQ,EAAEuH,QAAQ,EAAEvH,QAAQ,GAAG,IAAI6I,IAAI,CAACtB,QAAQ,EAAEvH,QAAQ,CAAC,GAAG,IAAI;MAClEsG,cAAc,EAAEiB,QAAQ,EAAEjB,cAAc;MACxCpG,KAAK,EAAEqH,QAAQ,EAAErH,KAAK;MACtBC,cAAc,EAAEoH,QAAQ,EAAEpH,cAAc;MACxCC,OAAO,EAAEmH,QAAQ,EAAEnH,OAAO;MAC1BE,cAAc,EAAEiH,QAAQ,EAAEjH,cAAc;MACxCD,eAAe,EAAEkH,QAAQ,EAAElH;KAC5B;IAED,IAAI,CAACgF,MAAM,GAAGkC,QAAQ,CAACjI,UAAU;IACjC,IAAI,CAACsD,qBAAqB,CAAC+F,UAAU,CAAC,IAAI,CAACC,gBAAgB,CAAC;EAC9D;EAEQxB,YAAYA,CAAA;IAClB,IAAI,CAACtE,SAAS,GAAG7E,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC+G,cAAc,CAAC;IAAE;IACzB,IAAI,CAACjC,aAAa,CAACuE,IAAI,CACrBpJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACyE,cAAc,GAAG,IAAK,CAAC,EACvC1E,SAAS,CAAEyK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MACA,OAAO,IAAI,CAAChE,iBAAiB,CAACkE,WAAW,CAACD,MAAM,CAAC,CAACvB,IAAI,CACpDtJ,GAAG,CAAE+J,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF3J,GAAG,CAAC,MAAO,IAAI,CAACyE,cAAc,GAAG,KAAM,CAAC,EACxCxE,UAAU,CAAE0K,KAAK,IAAI;QACnB,IAAI,CAAClG,cAAc,GAAG,KAAK;QAC3B,OAAO5E,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQkJ,YAAYA,CAAA;IAClB,IAAI,CAACjE,SAAS,GAAGnF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC+G,cAAc,CAAC;IAAE;IACzB,IAAI,CAAC5B,aAAa,CAACkE,IAAI,CACrBpJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC+E,cAAc,GAAG,IAAK,CAAC,EACvChF,SAAS,CAAEyK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MACA,OAAO,IAAI,CAAChE,iBAAiB,CAACkE,WAAW,CAACD,MAAM,CAAC,CAACvB,IAAI,CACpDtJ,GAAG,CAAE+J,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF3J,GAAG,CAAC,MAAO,IAAI,CAAC+E,cAAc,GAAG,KAAM,CAAC,EACxC9E,UAAU,CAAE0K,KAAK,IAAI;QACnB,IAAI,CAAC5F,cAAc,GAAG,KAAK;QAC3B,OAAOlF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQmJ,aAAaA,CAAA;IACnB,IAAI,CAAC9D,UAAU,GAAGvF,MAAM,CACtBE,EAAE,CAAC,IAAI,CAAC+G,cAAc,CAAC;IAAE;IACzB,IAAI,CAACxB,cAAc,CAAC8D,IAAI,CACtBpJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACmF,eAAe,GAAG,IAAK,CAAC,EACxCpF,SAAS,CAAEyK,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MACA,OAAO,IAAI,CAAChE,iBAAiB,CAACkE,WAAW,CAACD,MAAM,CAAC,CAACvB,IAAI,CACpDtJ,GAAG,CAAE+J,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF3J,GAAG,CAAC,MAAO,IAAI,CAACmF,eAAe,GAAG,KAAM,CAAC,EACzClF,UAAU,CAAE0K,KAAK,IAAI;QACnB,IAAI,CAACxF,eAAe,GAAG,KAAK;QAC5B,OAAOtF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEM+K,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACzD,aAAa,GAAG,IAAI;MACzByD,KAAI,CAAC3D,WAAW,GAAG,IAAI;MAEvB,IAAI2D,KAAI,CAAC5C,QAAQ,CAAC8C,OAAO,EAAE;QACzBF,KAAI,CAAC3D,WAAW,GAAG,IAAI;QACvB;MACF;MAEA2D,KAAI,CAACxD,UAAU,GAAG,IAAI;MACtB,MAAM0C,KAAK,GAAG;QAAE,GAAGc,KAAI,CAAC5C,QAAQ,CAAC8B;MAAK,CAAE;MAExC,MAAMJ,IAAI,GAAG;QACXjJ,WAAW,EAAEmK,KAAI,CAAC/D,EAAE;QACpBf,IAAI,EAAEgE,KAAK,EAAEhE;OACd;MAED,IAAI8E,KAAI,CAACvD,UAAU,EAAE;QACnBuD,KAAI,CAACrE,iBAAiB,CACnBwE,UAAU,CAACH,KAAI,CAACvD,UAAU,EAAEqC,IAAI,CAAC,CACjCT,IAAI,CAACxJ,SAAS,CAACmL,KAAI,CAAClE,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAC;UACT8B,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACxD,UAAU,GAAG,KAAK;YACvBwD,KAAI,CAAC3D,WAAW,GAAG,KAAK;YACxB2D,KAAI,CAAC5C,QAAQ,CAACiD,KAAK,EAAE;YACrBL,KAAI,CAACpE,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFkC,KAAI,CAACrE,iBAAiB,CACnB2E,eAAe,CAACN,KAAI,CAAC/D,EAAE,CAAC,CACxBoC,IAAI,CAACxJ,SAAS,CAACmL,KAAI,CAAClE,aAAa,CAAC,CAAC,CACnCwC,SAAS,EAAE;UAChB,CAAC;UACDwB,KAAK,EAAGjB,GAAQ,IAAI;YAClBmB,KAAI,CAACxD,UAAU,GAAG,KAAK;YACvBwD,KAAI,CAAC3D,WAAW,GAAG,IAAI;YACvB2D,KAAI,CAACpE,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACLkC,KAAI,CAACrE,iBAAiB,CACnB4E,UAAU,CAACzB,IAAI,CAAC,CAChBT,IAAI,CAACxJ,SAAS,CAACmL,KAAI,CAAClE,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAC;UACT8B,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACxD,UAAU,GAAG,KAAK;YACvBwD,KAAI,CAAC3D,WAAW,GAAG,KAAK;YACxB2D,KAAI,CAAC5C,QAAQ,CAACiD,KAAK,EAAE;YACrBL,KAAI,CAACpE,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFkC,KAAI,CAACrE,iBAAiB,CACnB2E,eAAe,CAACN,KAAI,CAAC/D,EAAE,CAAC,CACxBoC,IAAI,CAACxJ,SAAS,CAACmL,KAAI,CAAClE,aAAa,CAAC,CAAC,CACnCwC,SAAS,EAAE;UAChB,CAAC;UACDwB,KAAK,EAAGjB,GAAQ,IAAI;YAClBmB,KAAI,CAACxD,UAAU,GAAG,KAAK;YACvBwD,KAAI,CAAC3D,WAAW,GAAG,IAAI;YACvB2D,KAAI,CAACpE,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEMtE,QAAQA,CAAA;IAAA,IAAAgH,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAAC9I,SAAS,GAAG,IAAI;MAErB,IAAI8I,MAAI,CAAC/G,qBAAqB,CAACyG,OAAO,EAAE;QACtC;MACF;MAEAM,MAAI,CAACxE,MAAM,GAAG,IAAI;MAClB,MAAMkD,KAAK,GAAG;QAAE,GAAGsB,MAAI,CAAC/G,qBAAqB,CAACyF;MAAK,CAAE;MAErD,MAAMJ,IAAI,GAAG;QACXhJ,OAAO,EAAEoJ,KAAK,EAAEpJ,OAAO;QACvBkH,qBAAqB,EAAEkC,KAAK,EAAElC,qBAAqB;QACnDE,qBAAqB,EAAEgC,KAAK,EAAEhC,qBAAqB;QACnD1G,mBAAmB,EAAE0I,KAAK,EAAE1I,mBAAmB;QAC/CG,UAAU,EAAEuI,KAAK,EAAEvI,UAAU,GAAG6J,MAAI,CAACC,UAAU,CAACvB,KAAK,CAACvI,UAAU,CAAC,GAAG,IAAI;QACxEE,QAAQ,EAAEqI,KAAK,EAAErI,QAAQ,GAAG2J,MAAI,CAACC,UAAU,CAACvB,KAAK,CAACrI,QAAQ,CAAC,GAAG,IAAI;QAClEJ,gBAAgB,EAAEyI,KAAK,EAAEzI,gBAAgB;QACzCU,cAAc,EAAE+H,KAAK,EAAE/H,cAAc;QACrCgG,cAAc,EAAE+B,KAAK,EAAE/B,cAAc;QACrCjG,eAAe,EAAEgI,KAAK,EAAEhI,eAAe;QACvCR,MAAM,EAAEwI,KAAK,EAAExI,MAAM;QACrBK,KAAK,EAAEmI,KAAK,EAAEnI,KAAK;QACnBC,cAAc,EAAEkI,KAAK,EAAElI,cAAc;QACrCC,OAAO,EAAEiI,KAAK,EAAEjI;OACjB;MAEDuJ,MAAI,CAAC7E,iBAAiB,CACnB+E,cAAc,CAACF,MAAI,CAACtE,MAAM,EAAE4C,IAAI,CAAC,CACjCT,IAAI,CAACxJ,SAAS,CAAC2L,MAAI,CAAC1E,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAC;QACTqC,IAAI,EAAGpC,QAAa,IAAI;UACtBiC,MAAI,CAAC5E,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACF0C,MAAI,CAAC7E,iBAAiB,CACnB2E,eAAe,CAACE,MAAI,CAACvE,EAAE,CAAC,CACxBoC,IAAI,CAACxJ,SAAS,CAAC2L,MAAI,CAAC1E,aAAa,CAAC,CAAC,CACnCwC,SAAS,EAAE;UACdkC,MAAI,CAACrE,UAAU,GAAG,KAAK;QACzB,CAAC;QACD2D,KAAK,EAAGjB,GAAQ,IAAI;UAClB2B,MAAI,CAACxE,MAAM,GAAG,KAAK;UACnBwE,MAAI,CAACrE,UAAU,GAAG,IAAI;UACtBqE,MAAI,CAAC5E,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEA9C,aAAaA,CAACqE,IAAS;IACrB,IAAI,CAACxD,mBAAmB,CAAC+E,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC5B,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA4B,MAAMA,CAAC5B,IAAS;IACd,IAAI,CAAC1D,iBAAiB,CACnBuF,UAAU,CAAC7B,IAAI,CAAClJ,UAAU,CAAC,CAC3BkI,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACiH,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAC;MACTqC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/E,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACnC,iBAAiB,CACnB2E,eAAe,CAAC,IAAI,CAACrE,EAAE,CAAC,CACxBoC,IAAI,CAACxJ,SAAS,CAAC,IAAI,CAACiH,aAAa,CAAC,CAAC,CACnCwC,SAAS,EAAE;MAChB,CAAC;MACDwB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAClE,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA7C,SAASA,CAACkG,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAhB,UAAUA,CAACiB,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAItK,CAACA,CAAA;IACH,OAAO,IAAI,CAAC8B,qBAAqB,CAAC0I,QAAQ;EAC5C;EAEA,IAAI5G,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC6B,QAAQ,CAAC+E,QAAQ;EAC/B;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAC/F,YAAY,GAAG+F,QAAQ;IAC5B,IAAI,CAAChG,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACa,QAAQ,CAACiD,KAAK,EAAE;EACvB;EAEAiC,UAAUA,CAAA;IACR,IAAI,CAACnG,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAoG,OAAOA,CAAA;IACL,IAAI,CAAC7K,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+B,qBAAqB,CAAC4G,KAAK,EAAE;EACpC;EAIAmC,WAAWA,CAAA;IACT,IAAI,CAAC1G,aAAa,CAAC6E,IAAI,EAAE;IACzB,IAAI,CAAC7E,aAAa,CAACsE,QAAQ,EAAE;EAC/B;;;uBA9cW5E,0BAA0B,EAAAnG,EAAA,CAAAoN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtN,EAAA,CAAAoN,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAxN,EAAA,CAAAoN,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1N,EAAA,CAAAoN,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAA1BxH,0BAA0B;MAAAyH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf/BlO,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,kBACyG;UAA1CD,EAAA,CAAA6D,UAAA,mBAAAuK,8DAAA;YAAA,OAASD,GAAA,CAAAlB,UAAA,EAAY;UAAA,EAAC;UACzFjN,EAFI,CAAAG,YAAA,EACyG,EACvG;UA8KNH,EA7KA,CAAAiC,UAAA,IAAAoM,yCAAA,oBAA6D,IAAAC,0CAAA,qBA6KA;UA4OjEtO,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,aAAgE,aACuB,YAChC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,mBAC2D;UADrCD,EAAA,CAAA6D,UAAA,mBAAA0K,+DAAA;YAAA,OAASJ,GAAA,CAAApB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAEvD/M,EAFI,CAAAG,YAAA,EAC2D,EACzD;UAGFH,EADJ,CAAAC,cAAA,cAAuB,mBAEW;UAkD1BD,EAhDA,CAAAiC,UAAA,KAAAuM,kDAAA,2BAAgC,KAAAC,kDAAA,2BAwBQ,KAAAC,kDAAA,0BAmBF,KAAAC,kDAAA,0BAKD;UAOjD3O,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAA4O,gBAAA,2BAAAC,uEAAApJ,MAAA;YAAAzF,EAAA,CAAA8O,kBAAA,CAAAX,GAAA,CAAAnH,WAAA,EAAAvB,MAAA,MAAA0I,GAAA,CAAAnH,WAAA,GAAAvB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9CzF,EAAA,CAAAiC,UAAA,KAAA8M,kDAAA,0BAAgC;UAMxB/O,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAAsD,SAAA,oBAC0E;UAC1EtD,EAAA,CAAAiC,UAAA,KAAA+M,0CAAA,kBACmE;UAI3EhP,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAA6D,UAAA,mBAAAoL,6DAAA;YAAA,OAAAd,GAAA,CAAAnH,WAAA,GAAuB,KAAK;UAAA,EAAC;UAAChH,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAA6D,UAAA,mBAAAqL,6DAAA;YAAA,OAASf,GAAA,CAAAzD,YAAA,EAAc;UAAA,EAAC;UAIxC1K,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UA3fOH,EAAA,CAAAI,SAAA,GAAuC;UACqCJ,EAD5E,CAAAY,UAAA,UAAAuN,GAAA,CAAArH,UAAA,oBAAuC,UAAAqH,GAAA,CAAArH,UAAA,uBAAyC,2CAC5B,iBAAwC;UAEpG9G,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAY,UAAA,UAAAuN,GAAA,CAAArH,UAAA,CAAiB;UA6KhB9G,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAY,UAAA,SAAAuN,GAAA,CAAArH,UAAA,CAAgB;UAkPf9G,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAY,UAAA,oCAAmC,iBAAiB;UAI/CZ,EAAA,CAAAI,SAAA,GAAqB;UAAwCJ,EAA7D,CAAAY,UAAA,UAAAuN,GAAA,CAAApH,WAAA,CAAqB,YAAyB,mBAAiC;UA2D7C/G,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmP,UAAA,CAAAnP,EAAA,CAAAoP,eAAA,KAAAC,GAAA,EAA4B;UAArErP,EAAA,CAAAY,UAAA,eAAc;UAACZ,EAAA,CAAAsP,gBAAA,YAAAnB,GAAA,CAAAnH,WAAA,CAAyB;UAAmDhH,EAArB,CAAAY,UAAA,qBAAoB,oBAAoB;UAM9GZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAY,UAAA,cAAAuN,GAAA,CAAApG,QAAA,CAAsB;UAGkD/H,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAmP,UAAA,CAAAnP,EAAA,CAAAoP,eAAA,KAAAG,GAAA,EAA6B;UAC3FvP,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAAwJ,GAAA,CAAAjH,aAAA,IAAAiH,GAAA,CAAAjI,KAAA,SAAA3D,MAAA,EAAmE;UACjEvC,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAY,UAAA,SAAAuN,GAAA,CAAAjH,aAAA,IAAAiH,GAAA,CAAAjI,KAAA,SAAA3D,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}