{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/calendar\";\nimport * as i9 from \"primeng/inputtext\";\nfunction TasksOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"label\", 9)(4, \"span\", 10);\n    i0.ɵɵtext(5, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"label\", 9)(12, \"span\", 10);\n    i0.ɵɵtext(13, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 11);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"label\", 9)(20, \"span\", 10);\n    i0.ɵɵtext(21, \"article\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Subject \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 11);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 7)(26, \"div\", 8)(27, \"label\", 9)(28, \"span\", 10);\n    i0.ɵɵtext(29, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 11);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 7)(34, \"div\", 8)(35, \"label\", 9)(36, \"span\", 10);\n    i0.ɵɵtext(37, \"widgets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 11);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 7)(42, \"div\", 8)(43, \"label\", 9)(44, \"span\", 10);\n    i0.ɵɵtext(45, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 11);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 7)(50, \"div\", 8)(51, \"label\", 9)(52, \"span\", 10);\n    i0.ɵɵtext(53, \"supervisor_account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(54, \" Organizer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 11);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 7)(58, \"div\", 8)(59, \"label\", 9)(60, \"span\", 10);\n    i0.ɵɵtext(61, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Location \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 11);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 7)(66, \"div\", 8)(67, \"label\", 9)(68, \"span\", 10);\n    i0.ɵɵtext(69, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(70, \" End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 11);\n    i0.ɵɵtext(72);\n    i0.ɵɵpipe(73, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 7)(75, \"div\", 8)(76, \"label\", 9)(77, \"span\", 10);\n    i0.ɵɵtext(78, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" Start Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 11);\n    i0.ɵɵtext(81);\n    i0.ɵɵpipe(82, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(83, \"div\", 7)(84, \"div\", 8)(85, \"label\", 9)(86, \"span\", 10);\n    i0.ɵɵtext(87, \"priority_high\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(88, \" Priority \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"div\", 11);\n    i0.ɵɵtext(90);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(91, \"div\", 7)(92, \"div\", 8)(93, \"label\", 9)(94, \"span\", 10);\n    i0.ɵɵtext(95, \"link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(96, \" External ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(97, \"div\", 11);\n    i0.ɵɵtext(98);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(99, \"div\", 7)(100, \"div\", 8)(101, \"label\", 9)(102, \"span\", 10);\n    i0.ɵɵtext(103, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(104, \" Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\", 11);\n    i0.ɵɵtext(106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.bp_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_status) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.description) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.account) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.category) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.disposition_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.organizer) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.location) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date) ? i0.ɵɵpipeBind3(73, 13, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date) ? i0.ɵɵpipeBind3(82, 17, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.priority) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.external_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales) || \"-\");\n  }\n}\nfunction TasksOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 12)(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8)(4, \"label\", 13)(5, \"span\", 14);\n    i0.ɵɵtext(6, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Subject \");\n    i0.ɵɵelementStart(8, \"span\", 15);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"input\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"label\", 13)(14, \"span\", 14);\n    i0.ɵɵtext(15, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \"Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8)(20, \"label\", 13)(21, \"span\", 14);\n    i0.ɵɵtext(22, \"widgets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \"Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"p-dropdown\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 7)(26, \"div\", 8)(27, \"label\", 13)(28, \"span\", 14);\n    i0.ɵɵtext(29, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \"Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"p-dropdown\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 7)(33, \"div\", 8)(34, \"label\", 13)(35, \"span\", 14);\n    i0.ɵɵtext(36, \"supervisor_account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \"Organizer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 7)(40, \"div\", 8)(41, \"label\", 13)(42, \"span\", 14);\n    i0.ɵɵtext(43, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44, \" Location \");\n    i0.ɵɵelementStart(45, \"span\", 15);\n    i0.ɵɵtext(46, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(47, \"input\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 7)(49, \"div\", 8)(50, \"label\", 13)(51, \"span\", 14);\n    i0.ɵɵtext(52, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(53, \"End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"p-calendar\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 7)(56, \"div\", 8)(57, \"label\", 13)(58, \"span\", 14);\n    i0.ɵɵtext(59, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Start Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-calendar\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 7)(63, \"div\", 8)(64, \"label\", 13)(65, \"span\", 14);\n    i0.ɵɵtext(66, \"priority_high\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Priority \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-dropdown\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 7)(70, \"div\", 8)(71, \"label\", 13)(72, \"span\", 14);\n    i0.ɵɵtext(73, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(75, \"input\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 26)(77, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TasksOverviewComponent_form_6_Template_button_click_77_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.ActivityOverviewForm);\n    i0.ɵɵadvance(24);\n    i0.ɵɵproperty(\"options\", ctx_r0.categoryOptions)(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dispositionOptions)(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.priorityOptions)(\"styleClass\", \"h-3rem w-full\");\n  }\n}\nexport let TasksOverviewComponent = /*#__PURE__*/(() => {\n  class TasksOverviewComponent {\n    constructor(formBuilder, activitiesservice, messageservice, router) {\n      this.formBuilder = formBuilder;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.router = router;\n      this.ngUnsubscribe = new Subject();\n      this.overviewDetails = null;\n      this.ActivityOverviewForm = this.formBuilder.group({\n        description: [''],\n        activity_status: [''],\n        account: [''],\n        category: [''],\n        disposition_code: [''],\n        organizer: [''],\n        location: [''],\n        start_date: [''],\n        end_date: [''],\n        priority: [''],\n        external_id: [''],\n        sales_organization: ['']\n      });\n      this.submitted = false;\n      this.saving = false;\n      this.bp_id = '';\n      this.editid = '';\n      this.isEditMode = false;\n      this.categoryOptions = [{\n        label: 'Call Campaign',\n        value: 'CALL_CAMPAIGN'\n      }, {\n        label: 'Contact Deactivation',\n        value: 'CONTACT_DEACTIVATION'\n      }, {\n        label: 'Customer Service',\n        value: 'CUSTOMER_SERVICE'\n      }, {\n        label: 'Left Message',\n        value: 'LEFT_MESSAGE'\n      }, {\n        label: 'Marketing Drop Off',\n        value: 'MARKETING_DROP_OFF'\n      }, {\n        label: 'Product Service Presentation',\n        value: 'PRODUCT_SERVICE_PRESENTATION'\n      }, {\n        label: 'Sales Quote',\n        value: 'SALES_QUOTE'\n      }];\n      this.dispositionOptions = [{\n        label: 'Spiff',\n        value: 'SPIFF'\n      }, {\n        label: 'Team Campaign',\n        value: 'TEAM_CAMPAIGN'\n      }, {\n        label: 'Intro',\n        value: 'INTRO'\n      }, {\n        label: 'Luxury Call',\n        value: 'LUXURY_CALL'\n      }, {\n        label: 'Luxury Appointment',\n        value: 'LUXURY_APPOINTMENT'\n      }, {\n        label: 'Not Approved Supplier',\n        value: 'NOT_APPROVED_SUPPLIER'\n      }];\n      this.priorityOptions = [{\n        label: 'Immediate',\n        value: 'IMMEDIATE'\n      }, {\n        label: 'Low',\n        value: 'LOW'\n      }, {\n        label: 'Normal',\n        value: 'NORMAL'\n      }, {\n        label: 'Urgent',\n        value: 'URGENT'\n      }];\n    }\n    ngOnInit() {\n      this.activitiesservice.activity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n        if (!response) return;\n        this.bp_id = response?.bp_id;\n        this.overviewDetails = response;\n        if (this.overviewDetails) {\n          this.fetchOverviewData(this.overviewDetails);\n        }\n      });\n    }\n    fetchOverviewData(activity) {\n      this.existingActivity = {\n        description: activity?.description,\n        category: activity?.category,\n        start_date: activity?.start_date,\n        end_date: activity?.end_date,\n        priority: activity?.priority\n      };\n      this.editid = activity.updated_id;\n      this.ActivityOverviewForm.patchValue(this.existingActivity);\n    }\n    onSubmit() {\n      return _asyncToGenerator(function* () {})();\n    }\n    get f() {\n      return this.ActivityOverviewForm.controls;\n    }\n    toggleEdit() {\n      this.isEditMode = !this.isEditMode;\n    }\n    onReset() {\n      this.submitted = false;\n      this.ActivityOverviewForm.reset();\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function TasksOverviewComponent_Factory(t) {\n        return new (t || TasksOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TasksOverviewComponent,\n        selectors: [[\"app-tasks-overview\"]],\n        decls: 7,\n        vars: 6,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"outlined\", \"styleClass\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"font-medium\", \"text-700\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"description\", \"type\", \"text\", \"formControlName\", \"description\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"account\", \"type\", \"text\", \"formControlName\", \"account\", \"placeholder\", \"Account\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"category\", \"placeholder\", \"Select Category\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"disposition_code\", \"placeholder\", \"Select Code\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"organizer\", \"type\", \"text\", \"formControlName\", \"organizer\", \"placeholder\", \"Organizer\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"location\", \"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Use format 'address/postal code/country'\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"End Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Start Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"priority\", \"placeholder\", \"Select Priority\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"sales_organization\", \"type\", \"text\", \"formControlName\", \"sales_organization\", \"placeholder\", \"Sales Organization'\", 1, \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"]],\n        template: function TasksOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 3);\n            i0.ɵɵlistener(\"click\", function TasksOverviewComponent_Template_p_button_click_4_listener() {\n              return ctx.toggleEdit();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(5, TasksOverviewComponent_div_5_Template, 107, 21, \"div\", 4)(6, TasksOverviewComponent_form_6_Template, 78, 11, \"form\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          }\n        },\n        dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.ButtonDirective, i6.Button, i7.Dropdown, i8.Calendar, i9.InputText, i5.DatePipe]\n      });\n    }\n  }\n  return TasksOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}