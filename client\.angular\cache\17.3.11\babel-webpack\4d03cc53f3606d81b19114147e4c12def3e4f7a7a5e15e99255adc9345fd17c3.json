{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AccordionModule } from 'primeng/accordion';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { IdentifyAccountRoutingModule } from './identify-account-routing.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let IdentifyAccountModule = /*#__PURE__*/(() => {\n  class IdentifyAccountModule {\n    static {\n      this.ɵfac = function IdentifyAccountModule_Factory(t) {\n        return new (t || IdentifyAccountModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: IdentifyAccountModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, ReactiveFormsModule, IdentifyAccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule, AccordionModule, RadioButtonModule, MultiSelectModule]\n      });\n    }\n  }\n  return IdentifyAccountModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}