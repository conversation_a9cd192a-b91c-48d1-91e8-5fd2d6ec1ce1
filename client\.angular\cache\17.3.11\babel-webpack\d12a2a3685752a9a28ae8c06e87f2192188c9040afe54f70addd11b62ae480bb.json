{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { OpportunitiesRoutingModule } from './opportunities-routing.module';\nimport { OpportunitiesComponent } from './opportunities.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\nimport * as i0 from \"@angular/core\";\nexport class OpportunitiesModule {\n  static {\n    this.ɵfac = function OpportunitiesModule_Factory(t) {\n      return new (t || OpportunitiesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OpportunitiesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, OpportunitiesRoutingModule, FormsModule, ReactiveFormsModule, TableModule, ButtonModule, DropdownModule, InputSwitchModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OpportunitiesModule, {\n    declarations: [OpportunitiesComponent, OpportunitiesDetailsComponent, OpportunitiesOverviewComponent, OpportunitiesContactsComponent, OpportunitiesSalesTeamComponent, OpportunitiesAiInsightsComponent, OpportunitiesOrganizationDataComponent, OpportunitiesAttachmentsComponent, OpportunitiesNotesComponent],\n    imports: [CommonModule, OpportunitiesRoutingModule, FormsModule, ReactiveFormsModule, TableModule, ButtonModule, DropdownModule, InputSwitchModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "OpportunitiesRoutingModule", "OpportunitiesComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "InputSwitchModule", "MessageService", "ConfirmationService", "OpportunitiesDetailsComponent", "OpportunitiesOverviewComponent", "OpportunitiesContactsComponent", "OpportunitiesSalesTeamComponent", "OpportunitiesAiInsightsComponent", "OpportunitiesOrganizationDataComponent", "OpportunitiesAttachmentsComponent", "OpportunitiesNotesComponent", "OpportunitiesModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { OpportunitiesRoutingModule } from './opportunities-routing.module';\r\nimport { OpportunitiesComponent } from './opportunities.component';\r\nimport { FormsModule,ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\r\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\r\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\r\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\r\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\r\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\r\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\r\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    OpportunitiesComponent,\r\n    OpportunitiesDetailsComponent,\r\n    OpportunitiesOverviewComponent,\r\n    OpportunitiesContactsComponent,\r\n    OpportunitiesSalesTeamComponent,\r\n    OpportunitiesAiInsightsComponent,\r\n    OpportunitiesOrganizationDataComponent,\r\n    OpportunitiesAttachmentsComponent,\r\n    OpportunitiesNotesComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    OpportunitiesRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    InputSwitchModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class OpportunitiesModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,WAAW,EAACC,mBAAmB,QAAQ,gBAAgB;AAChE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,6BAA6B,QAAQ,yDAAyD;AACvG,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,gCAAgC,QAAQ,uFAAuF;AACxI,SAASC,sCAAsC,QAAQ,mGAAmG;AAC1J,SAASC,iCAAiC,QAAQ,uFAAuF;AACzI,SAASC,2BAA2B,QAAQ,2EAA2E;;AA+BvH,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAFnB,CAACV,cAAc,EAAEC,mBAAmB,CAAC;MAAAU,OAAA,GAd9CzB,YAAY,EACZC,0BAA0B,EAC1BE,WAAW,EACXC,mBAAmB,EACnBO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdI,iBAAiB,EACjBD,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;IAAA;EAAA;;;2EAINc,mBAAmB;IAAAE,YAAA,GA3B5BxB,sBAAsB,EACtBc,6BAA6B,EAC7BC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,gCAAgC,EAChCC,sCAAsC,EACtCC,iCAAiC,EACjCC,2BAA2B;IAAAE,OAAA,GAG3BzB,YAAY,EACZC,0BAA0B,EAC1BE,WAAW,EACXC,mBAAmB,EACnBO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdI,iBAAiB,EACjBD,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}