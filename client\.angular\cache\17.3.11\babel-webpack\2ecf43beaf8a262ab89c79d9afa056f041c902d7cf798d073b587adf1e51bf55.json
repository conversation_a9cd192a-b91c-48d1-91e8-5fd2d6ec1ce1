{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport Quill from 'quill';\nconst _c0 = [[[\"p-header\"]]];\nconst _c1 = [\"p-header\"];\nfunction Editor_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Editor_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Editor_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\nfunction Editor_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 5)(2, \"select\", 6)(3, \"option\", 7);\n    i0.ɵɵtext(4, \"Heading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 8);\n    i0.ɵɵtext(6, \"Subheading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 9);\n    i0.ɵɵtext(8, \"Normal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"select\", 10)(10, \"option\", 9);\n    i0.ɵɵtext(11, \"Sans Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 11);\n    i0.ɵɵtext(13, \"Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Monospace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"span\", 5);\n    i0.ɵɵelement(17, \"button\", 13)(18, \"button\", 14)(19, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 5);\n    i0.ɵɵelement(21, \"select\", 16)(22, \"select\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 5);\n    i0.ɵɵelement(24, \"button\", 18)(25, \"button\", 19);\n    i0.ɵɵelementStart(26, \"select\", 20);\n    i0.ɵɵelement(27, \"option\", 9);\n    i0.ɵɵelementStart(28, \"option\", 21);\n    i0.ɵɵtext(29, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 22);\n    i0.ɵɵtext(31, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 23);\n    i0.ɵɵtext(33, \"justify\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"span\", 5);\n    i0.ɵɵelement(35, \"button\", 24)(36, \"button\", 25)(37, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 5);\n    i0.ɵɵelement(39, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst EDITOR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Editor),\n  multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nlet Editor = /*#__PURE__*/(() => {\n  class Editor {\n    platformId;\n    el;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Placeholder text to show when editor is empty.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Whitelist of formats to display, see here for available options.\n     * @group Props\n     */\n    formats;\n    /**\n     * Modules configuration of Editor, see here for available options.\n     * @group Props\n     */\n    modules;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n     * @group Props\n     */\n    bounds;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n     * @group Props\n     */\n    scrollingContainer;\n    /**\n     * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n     * @group Props\n     */\n    debug;\n    /**\n     * Whether to instantiate the editor to read-only mode.\n     * @group Props\n     */\n    get readonly() {\n      return this._readonly;\n    }\n    set readonly(val) {\n      this._readonly = val;\n      // if (this.quill) {\n      //     if (this._readonly) this.quill.disable();\n      //     else this.quill.enable();\n      // }\n    }\n    /**\n     * Callback to invoke when the quill modules are loaded.\n     * @param {EditorInitEvent} event - custom event.\n     * @group Emits\n     */\n    onInit = new EventEmitter();\n    /**\n     * Callback to invoke when text of editor changes.\n     * @param {EditorTextChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onTextChange = new EventEmitter();\n    /**\n     * Callback to invoke when selection of the text changes.\n     * @param {EditorSelectionChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onSelectionChange = new EventEmitter();\n    templates;\n    toolbar;\n    value;\n    delayedCommand = null;\n    _readonly = false;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    quill;\n    headerTemplate;\n    get isAttachedQuillEditorToDOM() {\n      return this.quillElements?.editorElement?.isConnected;\n    }\n    quillElements;\n    constructor(platformId, el) {\n      this.platformId = platformId;\n      this.el = el;\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.initQuillElements();\n        if (this.isAttachedQuillEditorToDOM) {\n          this.initQuillEditor();\n        }\n      }\n    }\n    ngAfterViewChecked() {\n      if (isPlatformBrowser(this.platformId)) {\n        // The problem is inside the `quill` library, we need to wait for a new release.\n        // Function `isLine` - used `getComputedStyle`, it was rewritten in the next release.\n        // (We need to wait for a release higher than 1.3.7).\n        // These checks and code can be removed.\n        if (!this.quill && this.isAttachedQuillEditorToDOM) {\n          this.initQuillEditor();\n        }\n        // Can also be deleted after updating `quill`.\n        if (this.delayedCommand && this.isAttachedQuillEditorToDOM) {\n          this.delayedCommand();\n          this.delayedCommand = null;\n        }\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n        }\n      });\n    }\n    writeValue(value) {\n      this.value = value;\n      if (this.quill) {\n        if (value) {\n          const command = () => {\n            this.quill.setContents(this.quill.clipboard.convert(this.value));\n          };\n          if (this.isAttachedQuillEditorToDOM) {\n            command();\n          } else {\n            this.delayedCommand = command;\n          }\n        } else {\n          const command = () => {\n            this.quill.setText('');\n          };\n          if (this.isAttachedQuillEditorToDOM) {\n            command();\n          } else {\n            this.delayedCommand = command;\n          }\n        }\n      }\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    getQuill() {\n      return this.quill;\n    }\n    initQuillEditor() {\n      this.initQuillElements();\n      const {\n        toolbarElement,\n        editorElement\n      } = this.quillElements;\n      let defaultModule = {\n        toolbar: toolbarElement\n      };\n      let modules = this.modules ? {\n        ...defaultModule,\n        ...this.modules\n      } : defaultModule;\n      this.quill = new Quill(editorElement, {\n        modules: modules,\n        placeholder: this.placeholder,\n        readOnly: this.readonly,\n        theme: 'snow',\n        formats: this.formats,\n        bounds: this.bounds,\n        debug: this.debug,\n        scrollingContainer: this.scrollingContainer\n      });\n      if (this.value) {\n        this.quill.setContents(this.quill.clipboard.convert(this.value));\n      }\n      this.quill.on('text-change', (delta, oldContents, source) => {\n        if (source === 'user') {\n          let html = DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n          let text = this.quill.getText().trim();\n          if (html === '<p><br></p>') {\n            html = null;\n          }\n          this.onTextChange.emit({\n            htmlValue: html,\n            textValue: text,\n            delta: delta,\n            source: source\n          });\n          this.onModelChange(html);\n          this.onModelTouched();\n        }\n      });\n      this.quill.on('selection-change', (range, oldRange, source) => {\n        this.onSelectionChange.emit({\n          range: range,\n          oldRange: oldRange,\n          source: source\n        });\n      });\n      this.onInit.emit({\n        editor: this.quill\n      });\n    }\n    initQuillElements() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.quillElements) {\n          this.quillElements = {\n            editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n            toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n          };\n        }\n      }\n    }\n    static ɵfac = function Editor_Factory(t) {\n      return new (t || Editor)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Editor,\n      selectors: [[\"p-editor\"]],\n      contentQueries: function Editor_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.toolbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        placeholder: \"placeholder\",\n        formats: \"formats\",\n        modules: \"modules\",\n        bounds: \"bounds\",\n        scrollingContainer: \"scrollingContainer\",\n        debug: \"debug\",\n        readonly: \"readonly\"\n      },\n      outputs: {\n        onInit: \"onInit\",\n        onTextChange: \"onTextChange\",\n        onSelectionChange: \"onSelectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([EDITOR_VALUE_ACCESSOR])],\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 6,\n      consts: [[3, \"ngClass\"], [\"class\", \"p-editor-toolbar\", 4, \"ngIf\"], [1, \"p-editor-content\", 3, \"ngStyle\"], [1, \"p-editor-toolbar\"], [4, \"ngTemplateOutlet\"], [1, \"ql-formats\"], [1, \"ql-header\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"selected\", \"\"], [1, \"ql-font\"], [\"value\", \"serif\"], [\"value\", \"monospace\"], [\"aria-label\", \"Bold\", \"type\", \"button\", 1, \"ql-bold\"], [\"aria-label\", \"Italic\", \"type\", \"button\", 1, \"ql-italic\"], [\"aria-label\", \"Underline\", \"type\", \"button\", 1, \"ql-underline\"], [1, \"ql-color\"], [1, \"ql-background\"], [\"value\", \"ordered\", \"aria-label\", \"Ordered List\", \"type\", \"button\", 1, \"ql-list\"], [\"value\", \"bullet\", \"aria-label\", \"Unordered List\", \"type\", \"button\", 1, \"ql-list\"], [1, \"ql-align\"], [\"value\", \"center\"], [\"value\", \"right\"], [\"value\", \"justify\"], [\"aria-label\", \"Insert Link\", \"type\", \"button\", 1, \"ql-link\"], [\"aria-label\", \"Insert Image\", \"type\", \"button\", 1, \"ql-image\"], [\"aria-label\", \"Insert Code Block\", \"type\", \"button\", 1, \"ql-code-block\"], [\"aria-label\", \"Remove Styles\", \"type\", \"button\", 1, \"ql-clean\"]],\n      template: function Editor_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, Editor_div_1_Template, 3, 1, \"div\", 1)(2, Editor_div_2_Template, 40, 0, \"div\", 1);\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-editor-container\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.toolbar || ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.toolbar && !ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.style);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n      styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Editor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet EditorModule = /*#__PURE__*/(() => {\n  class EditorModule {\n    static ɵfac = function EditorModule_Factory(t) {\n      return new (t || EditorModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EditorModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return EditorModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };\n//# sourceMappingURL=primeng-editor.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}