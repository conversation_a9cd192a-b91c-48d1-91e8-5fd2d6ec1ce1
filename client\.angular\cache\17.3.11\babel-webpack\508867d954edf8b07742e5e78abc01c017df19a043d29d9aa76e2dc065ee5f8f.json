{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Azerbaijani [az]\n//! author : topchiyev : https://github.com/topchiyev\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: '-inci',\n    5: '-inci',\n    8: '-inci',\n    70: '-inci',\n    80: '-inci',\n    2: '-nci',\n    7: '-nci',\n    20: '-nci',\n    50: '-nci',\n    3: '-üncü',\n    4: '-üncü',\n    100: '-üncü',\n    6: '-ncı',\n    9: '-uncu',\n    10: '-uncu',\n    30: '-uncu',\n    60: '-ıncı',\n    90: '-ıncı'\n  };\n  var az = moment.defineLocale('az', {\n    months: 'yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr'.split('_'),\n    monthsShort: 'yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek'.split('_'),\n    weekdays: 'Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə'.split('_'),\n    weekdaysShort: 'Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən'.split('_'),\n    weekdaysMin: 'Bz_BE_ÇA_Çə_CA_Cü_Şə'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün saat] LT',\n      nextDay: '[sabah saat] LT',\n      nextWeek: '[gələn həftə] dddd [saat] LT',\n      lastDay: '[dünən] LT',\n      lastWeek: '[keçən həftə] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s sonra',\n      past: '%s əvvəl',\n      s: 'bir neçə saniyə',\n      ss: '%d saniyə',\n      m: 'bir dəqiqə',\n      mm: '%d dəqiqə',\n      h: 'bir saat',\n      hh: '%d saat',\n      d: 'bir gün',\n      dd: '%d gün',\n      M: 'bir ay',\n      MM: '%d ay',\n      y: 'bir il',\n      yy: '%d il'\n    },\n    meridiemParse: /gecə|səhər|gündüz|axşam/,\n    isPM: function (input) {\n      return /^(gündüz|axşam)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'gecə';\n      } else if (hour < 12) {\n        return 'səhər';\n      } else if (hour < 17) {\n        return 'gündüz';\n      } else {\n        return 'axşam';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,\n    ordinal: function (number) {\n      if (number === 0) {\n        // special case for zero\n        return number + '-ıncı';\n      }\n      var a = number % 10,\n        b = number % 100 - a,\n        c = number >= 100 ? 100 : null;\n      return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return az;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}