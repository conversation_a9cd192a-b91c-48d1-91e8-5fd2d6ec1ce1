{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { IdentifyAccountComponent } from './identify-account.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: IdentifyAccountComponent\n}];\nexport class IdentifyAccountRoutingModule {\n  static {\n    this.ɵfac = function IdentifyAccountRoutingModule_Factory(t) {\n      return new (t || IdentifyAccountRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IdentifyAccountRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IdentifyAccountRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "IdentifyAccountComponent", "routes", "path", "component", "IdentifyAccountRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { IdentifyAccountComponent } from './identify-account.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: IdentifyAccountComponent,\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class IdentifyAccountRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,wBAAwB,QAAQ,8BAA8B;;;AAEvE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,4BAA4B;;;uBAA5BA,4BAA4B;IAAA;EAAA;;;YAA5BA;IAA4B;EAAA;;;gBAH7BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,4BAA4B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF7BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}