{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nclass ProgressSpinner {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Width of the circle stroke.\n   * @group Props\n   */\n  strokeWidth = '2';\n  /**\n   * Color for the background of the circle.\n   * @group Props\n   */\n  fill = 'none';\n  /**\n   * Duration of the rotate animation.\n   * @group Props\n   */\n  animationDuration = '2s';\n  static ɵfac = function ProgressSpinner_Factory(t) {\n    return new (t || ProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ProgressSpinner,\n    selectors: [[\"p-progressSpinner\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      strokeWidth: \"strokeWidth\",\n      fill: \"fill\",\n      animationDuration: \"animationDuration\"\n    },\n    decls: 3,\n    vars: 10,\n    consts: [[\"role\", \"progressbar\", 1, \"p-progress-spinner\", 3, \"ngStyle\", \"ngClass\"], [\"viewBox\", \"25 25 50 50\", 1, \"p-progress-spinner-svg\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\", 1, \"p-progress-spinner-circle\"]],\n    template: function ProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(1, \"svg\", 1);\n        i0.ɵɵelement(2, \"circle\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.styleClass);\n        i0.ɵɵattribute(\"aria-busy\", true)(\"data-pc-name\", \"progressspinner\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'p-progressSpinner',\n      template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    fill: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }]\n  });\n})();\nclass ProgressSpinnerModule {\n  static ɵfac = function ProgressSpinnerModule_Factory(t) {\n    return new (t || ProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ProgressSpinnerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ProgressSpinner],\n      declarations: [ProgressSpinner]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "ProgressSpinner", "styleClass", "style", "strokeWidth", "fill", "animationDuration", "ɵfac", "ProgressSpinner_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "decls", "vars", "consts", "template", "ProgressSpinner_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵattribute", "ɵɵadvance", "ɵɵstyleProp", "dependencies", "Ng<PERSON><PERSON>", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "ProgressSpinnerModule", "ProgressSpinnerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-progressspinner.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nclass ProgressSpinner {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Width of the circle stroke.\n     * @group Props\n     */\n    strokeWidth = '2';\n    /**\n     * Color for the background of the circle.\n     * @group Props\n     */\n    fill = 'none';\n    /**\n     * Duration of the rotate animation.\n     * @group Props\n     */\n    animationDuration = '2s';\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinner, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: ProgressSpinner, selector: \"p-progressSpinner\", inputs: { styleClass: \"styleClass\", style: \"style\", strokeWidth: \"strokeWidth\", fill: \"fill\", animationDuration: \"animationDuration\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinner, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-progressSpinner', template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"] }]\n        }], propDecorators: { styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], strokeWidth: [{\n                type: Input\n            }], fill: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }] } });\nclass ProgressSpinnerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinnerModule, declarations: [ProgressSpinner], imports: [CommonModule], exports: [ProgressSpinner] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinnerModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ProgressSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [ProgressSpinner],\n                    declarations: [ProgressSpinner]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;;AAEtG;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,WAAW,GAAG,GAAG;EACjB;AACJ;AACA;AACA;EACIC,IAAI,GAAG,MAAM;EACb;AACJ;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB,OAAOC,IAAI,YAAAC,wBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFR,eAAe;EAAA;EAClH,OAAOS,IAAI,kBAD8Ef,EAAE,CAAAgB,iBAAA;IAAAC,IAAA,EACJX,eAAe;IAAAY,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAb,UAAA;MAAAC,KAAA;MAAAC,WAAA;MAAAC,IAAA;MAAAC,iBAAA;IAAA;IAAAU,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADb1B,EAAE,CAAA4B,cAAA,YAEoG,CAAC;QAFvG5B,EAAE,CAAA6B,cAAA;QAAF7B,EAAE,CAAA4B,cAAA,YAGqD,CAAC;QAHxD5B,EAAE,CAAA8B,SAAA,eAI+D,CAAC;QAJlE9B,EAAE,CAAA+B,YAAA,CAK9E,CAAC,CACL,CAAC;MAAA;MAAA,IAAAL,EAAA;QAN+E1B,EAAE,CAAAgC,UAAA,YAAAL,GAAA,CAAAnB,KAEvC,CAAC,YAAAmB,GAAA,CAAApB,UAAsB,CAAC;QAFaP,EAAE,CAAAiC,WAAA;QAAFjC,EAAE,CAAAkC,SAAA,CAGoB,CAAC;QAHvBlC,EAAE,CAAAmC,WAAA,uBAAAR,GAAA,CAAAhB,iBAGoB,CAAC;QAHvBX,EAAE,CAAAiC,WAAA;QAAFjC,EAAE,CAAAkC,SAAA,CAIG,CAAC;QAJNlC,EAAE,CAAAiC,WAAA,SAAAN,GAAA,CAAAjB,IAAA,kBAAAiB,GAAA,CAAAlB,WAAA;MAAA;IAAA;IAAA2B,YAAA,GAOi6BtC,EAAE,CAACuC,OAAO,EAAoFvC,EAAE,CAACwC,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACxmC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAT6F1C,EAAE,CAAA2C,iBAAA,CASJrC,eAAe,EAAc,CAAC;IAC7GW,IAAI,EAAEhB,SAAS;IACf2C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAErB,QAAQ,EAAG;AAC/D;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEiB,eAAe,EAAEvC,uBAAuB,CAAC4C,MAAM;MAAEN,aAAa,EAAErC,iBAAiB,CAAC4C,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,m7BAAm7B;IAAE,CAAC;EAC98B,CAAC,CAAC,QAAkB;IAAEhC,UAAU,EAAE,CAAC;MAC3BU,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEI,KAAK,EAAE,CAAC;MACRS,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEK,WAAW,EAAE,CAAC;MACdQ,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEM,IAAI,EAAE,CAAC;MACPO,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEO,iBAAiB,EAAE,CAAC;MACpBM,IAAI,EAAEb;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8C,qBAAqB,CAAC;EACxB,OAAOtC,IAAI,YAAAuC,8BAAArC,CAAA;IAAA,YAAAA,CAAA,IAAwFoC,qBAAqB;EAAA;EACxH,OAAOE,IAAI,kBAjC8EpD,EAAE,CAAAqD,gBAAA;IAAApC,IAAA,EAiCSiC;EAAqB;EACzH,OAAOI,IAAI,kBAlC8EtD,EAAE,CAAAuD,gBAAA;IAAAC,OAAA,GAkC0CzD,YAAY;EAAA;AACrJ;AACA;EAAA,QAAA2C,SAAA,oBAAAA,SAAA,KApC6F1C,EAAE,CAAA2C,iBAAA,CAoCJO,qBAAqB,EAAc,CAAC;IACnHjC,IAAI,EAAEZ,QAAQ;IACduC,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAACzD,YAAY,CAAC;MACvB0D,OAAO,EAAE,CAACnD,eAAe,CAAC;MAC1BoD,YAAY,EAAE,CAACpD,eAAe;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,eAAe,EAAE4C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}