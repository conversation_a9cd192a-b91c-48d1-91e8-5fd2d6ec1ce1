{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { AccountActivitiesComponent } from '../account/account-details/account-activities/account-activities.component';\nimport { AccountAiInsightsComponent } from '../account/account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountAttachmentsComponent } from '../account/account-details/account-attachments/account-attachments.component';\nimport { AccountContactsComponent } from '../account/account-details/account-contacts/account-contacts.component';\nimport { AccountDetailsComponent } from '../account/account-details/account-details.component';\nimport { AccountNotesComponent } from '../account/account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from '../account/account-details/account-opportunities/account-opportunities.component';\nimport { AccountOrganizationDataComponent } from '../account/account-details/account-organization-data/account-organization-data.component';\nimport { AccountOverviewComponent } from '../account/account-details/account-overview/account-overview.component';\nimport { AccountRelationshipsComponent } from '../account/account-details/account-relationships/account-relationships.component';\nimport { AccountSalesOrderDetailsComponent } from '../account/account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { AccountSalesOrdersComponent } from '../account/account-details/account-sales-orders/account-sales-orders.component';\nimport { AccountSalesQuoteDetailsComponent } from '../account/account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesQuotesComponent } from '../account/account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesTeamComponent } from '../account/account-details/account-sales-team/account-sales-team.component';\nimport { AccountTicketsComponent } from '../account/account-details/account-tickets/account-tickets.component';\nimport { AccountInvoicesComponent } from '../account/account-details/account-invoices/account-invoices.component';\nimport { AccountReturnsComponent } from '../account/account-details/account-returns/account-returns.component';\nimport { ReturnOrderDetailsComponent } from '../account/account-details/account-returns/return-order-details/return-order-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':ticket-id',\n  component: ServiceTicketsComponent,\n  children: [{\n    path: ':id',\n    component: AccountDetailsComponent,\n    data: {\n      hideBreadCrumbs: true\n    },\n    children: [{\n      path: 'overview',\n      component: AccountOverviewComponent\n    }, {\n      path: 'contacts',\n      component: AccountContactsComponent\n    }, {\n      path: 'sales-team',\n      component: AccountSalesTeamComponent\n    }, {\n      path: 'opportunities',\n      component: AccountOpportunitiesComponent\n    }, {\n      path: 'ai-insights',\n      component: AccountAiInsightsComponent\n    }, {\n      path: 'organization-data',\n      component: AccountOrganizationDataComponent\n    }, {\n      path: 'attachments',\n      component: AccountAttachmentsComponent\n    }, {\n      path: 'notes',\n      component: AccountNotesComponent\n    }, {\n      path: 'activities',\n      component: AccountActivitiesComponent\n    }, {\n      path: 'relationships',\n      component: AccountRelationshipsComponent\n    }, {\n      path: 'tickets',\n      component: AccountTicketsComponent\n    }, {\n      path: 'sales-quotes',\n      component: AccountSalesQuotesComponent\n    }, {\n      path: 'sales-quotes/:id',\n      component: AccountSalesQuoteDetailsComponent\n    }, {\n      path: 'sales-orders',\n      component: AccountSalesOrdersComponent\n    }, {\n      path: 'sales-orders/:id',\n      component: AccountSalesOrderDetailsComponent\n    }, {\n      path: 'invoices',\n      component: AccountInvoicesComponent\n    }, {\n      path: 'returns',\n      component: AccountReturnsComponent\n    }, {\n      path: \"return-order/:returnOrderId/:refDocId\",\n      component: ReturnOrderDetailsComponent\n    }, {\n      path: '',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }, {\n      path: '**',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }]\n  }]\n}];\nexport class ServiceTicketsRoutingModule {\n  static {\n    this.ɵfac = function ServiceTicketsRoutingModule_Factory(t) {\n      return new (t || ServiceTicketsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceTicketsComponent", "AccountActivitiesComponent", "AccountAiInsightsComponent", "AccountAttachmentsComponent", "AccountContactsComponent", "AccountDetailsComponent", "AccountNotesComponent", "AccountOpportunitiesComponent", "AccountOrganizationDataComponent", "AccountOverviewComponent", "AccountRelationshipsComponent", "AccountSalesOrderDetailsComponent", "AccountSalesOrdersComponent", "AccountSalesQuoteDetailsComponent", "AccountSalesQuotesComponent", "AccountSalesTeamComponent", "AccountTicketsComponent", "AccountInvoicesComponent", "AccountReturnsComponent", "ReturnOrderDetailsComponent", "routes", "path", "component", "children", "data", "hideBreadCrumbs", "redirectTo", "pathMatch", "ServiceTicketsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\nimport { AccountActivitiesComponent } from '../account/account-details/account-activities/account-activities.component';\r\nimport { AccountAiInsightsComponent } from '../account/account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountAttachmentsComponent } from '../account/account-details/account-attachments/account-attachments.component';\r\nimport { AccountContactsComponent } from '../account/account-details/account-contacts/account-contacts.component';\r\nimport { AccountDetailsComponent } from '../account/account-details/account-details.component';\r\nimport { AccountNotesComponent } from '../account/account-details/account-notes/account-notes.component';\r\nimport { AccountOpportunitiesComponent } from '../account/account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountOrganizationDataComponent } from '../account/account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountOverviewComponent } from '../account/account-details/account-overview/account-overview.component';\r\nimport { AccountRelationshipsComponent } from '../account/account-details/account-relationships/account-relationships.component';\r\nimport { AccountSalesOrderDetailsComponent } from '../account/account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\r\nimport { AccountSalesOrdersComponent } from '../account/account-details/account-sales-orders/account-sales-orders.component';\r\nimport { AccountSalesQuoteDetailsComponent } from '../account/account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\r\nimport { AccountSalesQuotesComponent } from '../account/account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesTeamComponent } from '../account/account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountTicketsComponent } from '../account/account-details/account-tickets/account-tickets.component';\r\nimport { AccountInvoicesComponent } from '../account/account-details/account-invoices/account-invoices.component';\r\nimport { AccountReturnsComponent } from '../account/account-details/account-returns/account-returns.component';\r\nimport { ReturnOrderDetailsComponent } from '../account/account-details/account-returns/return-order-details/return-order-details.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: ':ticket-id',\r\n    component: ServiceTicketsComponent,\r\n    children: [\r\n      {\r\n        path: ':id',\r\n        component: AccountDetailsComponent,\r\n        data: {\r\n          hideBreadCrumbs: true,\r\n        },\r\n        children: [\r\n          { path: 'overview', component: AccountOverviewComponent },\r\n          { path: 'contacts', component: AccountContactsComponent },\r\n          { path: 'sales-team', component: AccountSalesTeamComponent },\r\n          { path: 'opportunities', component: AccountOpportunitiesComponent },\r\n          { path: 'ai-insights', component: AccountAiInsightsComponent },\r\n          {\r\n            path: 'organization-data',\r\n            component: AccountOrganizationDataComponent,\r\n          },\r\n          { path: 'attachments', component: AccountAttachmentsComponent },\r\n          { path: 'notes', component: AccountNotesComponent },\r\n          { path: 'activities', component: AccountActivitiesComponent },\r\n          { path: 'relationships', component: AccountRelationshipsComponent },\r\n          { path: 'tickets', component: AccountTicketsComponent },\r\n          { path: 'sales-quotes', component: AccountSalesQuotesComponent },\r\n          {\r\n            path: 'sales-quotes/:id',\r\n            component: AccountSalesQuoteDetailsComponent,\r\n          },\r\n          { path: 'sales-orders', component: AccountSalesOrdersComponent },\r\n          {\r\n            path: 'sales-orders/:id',\r\n            component: AccountSalesOrderDetailsComponent,\r\n          },\r\n          {\r\n            path: 'invoices',\r\n            component: AccountInvoicesComponent,\r\n          },\r\n          {\r\n            path: 'returns',\r\n            component: AccountReturnsComponent,\r\n          },\r\n          {\r\n            path: \"return-order/:returnOrderId/:refDocId\",\r\n            component: ReturnOrderDetailsComponent\r\n          },\r\n          { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n          { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n        ],\r\n      },\r\n    ]\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceTicketsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,0BAA0B,QAAQ,4EAA4E;AACvH,SAASC,0BAA0B,QAAQ,8EAA8E;AACzH,SAASC,2BAA2B,QAAQ,8EAA8E;AAC1H,SAASC,wBAAwB,QAAQ,wEAAwE;AACjH,SAASC,uBAAuB,QAAQ,sDAAsD;AAC9F,SAASC,qBAAqB,QAAQ,kEAAkE;AACxG,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,gCAAgC,QAAQ,0FAA0F;AAC3I,SAASC,wBAAwB,QAAQ,wEAAwE;AACjH,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,iCAAiC,QAAQ,mHAAmH;AACrK,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,iCAAiC,QAAQ,mHAAmH;AACrK,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,yBAAyB,QAAQ,4EAA4E;AACtH,SAASC,uBAAuB,QAAQ,sEAAsE;AAC9G,SAASC,wBAAwB,QAAQ,wEAAwE;AACjH,SAASC,uBAAuB,QAAQ,sEAAsE;AAC9G,SAASC,2BAA2B,QAAQ,gGAAgG;;;AAE5I,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEtB,uBAAuB;EAClCuB,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEjB,uBAAuB;IAClCmB,IAAI,EAAE;MACJC,eAAe,EAAE;KAClB;IACDF,QAAQ,EAAE,CACR;MAAEF,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAEb;IAAwB,CAAE,EACzD;MAAEY,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAElB;IAAwB,CAAE,EACzD;MAAEiB,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAEP;IAAyB,CAAE,EAC5D;MAAEM,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAEf;IAA6B,CAAE,EACnE;MAAEc,IAAI,EAAE,aAAa;MAAEC,SAAS,EAAEpB;IAA0B,CAAE,EAC9D;MACEmB,IAAI,EAAE,mBAAmB;MACzBC,SAAS,EAAEd;KACZ,EACD;MAAEa,IAAI,EAAE,aAAa;MAAEC,SAAS,EAAEnB;IAA2B,CAAE,EAC/D;MAAEkB,IAAI,EAAE,OAAO;MAAEC,SAAS,EAAEhB;IAAqB,CAAE,EACnD;MAAEe,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAErB;IAA0B,CAAE,EAC7D;MAAEoB,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAEZ;IAA6B,CAAE,EACnE;MAAEW,IAAI,EAAE,SAAS;MAAEC,SAAS,EAAEN;IAAuB,CAAE,EACvD;MAAEK,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAER;IAA2B,CAAE,EAChE;MACEO,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAET;KACZ,EACD;MAAEQ,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAEV;IAA2B,CAAE,EAChE;MACES,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAEX;KACZ,EACD;MACEU,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAEL;KACZ,EACD;MACEI,IAAI,EAAE,SAAS;MACfC,SAAS,EAAEJ;KACZ,EACD;MACEG,IAAI,EAAE,uCAAuC;MAC7CC,SAAS,EAAEH;KACZ,EACD;MAAEE,IAAI,EAAE,EAAE;MAAEK,UAAU,EAAE,UAAU;MAAEC,SAAS,EAAE;IAAM,CAAE,EACvD;MAAEN,IAAI,EAAE,IAAI;MAAEK,UAAU,EAAE,UAAU;MAAEC,SAAS,EAAE;IAAM,CAAE;GAE5D;CAEJ,CACF;AAMD,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5B7B,YAAY,CAAC8B,QAAQ,CAACT,MAAM,CAAC,EAC7BrB,YAAY;IAAA;EAAA;;;2EAEX6B,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAhC,YAAA;IAAAiC,OAAA,GAF5BjC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}