{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Uyghur (China) [ug-cn]\n//! author: boyaq : https://github.com/boyaq\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ugCn = moment.defineLocale('ug-cn', {\n    months: 'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split('_'),\n    monthsShort: 'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split('_'),\n    weekdays: 'يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە'.split('_'),\n    weekdaysShort: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n    weekdaysMin: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY-يىلىM-ئاينىڭD-كۈنى',\n      LLL: 'YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm',\n      LLLL: 'dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm'\n    },\n    meridiemParse: /يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'يېرىم كېچە' || meridiem === 'سەھەر' || meridiem === 'چۈشتىن بۇرۇن') {\n        return hour;\n      } else if (meridiem === 'چۈشتىن كېيىن' || meridiem === 'كەچ') {\n        return hour + 12;\n      } else {\n        return hour >= 11 ? hour : hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      var hm = hour * 100 + minute;\n      if (hm < 600) {\n        return 'يېرىم كېچە';\n      } else if (hm < 900) {\n        return 'سەھەر';\n      } else if (hm < 1130) {\n        return 'چۈشتىن بۇرۇن';\n      } else if (hm < 1230) {\n        return 'چۈش';\n      } else if (hm < 1800) {\n        return 'چۈشتىن كېيىن';\n      } else {\n        return 'كەچ';\n      }\n    },\n    calendar: {\n      sameDay: '[بۈگۈن سائەت] LT',\n      nextDay: '[ئەتە سائەت] LT',\n      nextWeek: '[كېلەركى] dddd [سائەت] LT',\n      lastDay: '[تۆنۈگۈن] LT',\n      lastWeek: '[ئالدىنقى] dddd [سائەت] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s كېيىن',\n      past: '%s بۇرۇن',\n      s: 'نەچچە سېكونت',\n      ss: '%d سېكونت',\n      m: 'بىر مىنۇت',\n      mm: '%d مىنۇت',\n      h: 'بىر سائەت',\n      hh: '%d سائەت',\n      d: 'بىر كۈن',\n      dd: '%d كۈن',\n      M: 'بىر ئاي',\n      MM: '%d ئاي',\n      y: 'بىر يىل',\n      yy: '%d يىل'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '-كۈنى';\n        case 'w':\n        case 'W':\n          return number + '-ھەپتە';\n        default:\n          return number;\n      }\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1st is the first week of the year.\n    }\n  });\n  return ugCn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ugCn", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "hm", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "preparse", "string", "replace", "postformat", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/ug-cn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Uyghur (China) [ug-cn]\n//! author: boyaq : https://github.com/boyaq\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ugCn = moment.defineLocale('ug-cn', {\n        months: 'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split(\n            '_'\n        ),\n        monthsShort:\n            'يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر'.split(\n                '_'\n            ),\n        weekdays: 'يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە'.split(\n            '_'\n        ),\n        weekdaysShort: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n        weekdaysMin: 'يە_دۈ_سە_چا_پە_جۈ_شە'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: 'YYYY-يىلىM-ئاينىڭD-كۈنى',\n            LLL: 'YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm',\n            LLLL: 'dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm',\n        },\n        meridiemParse: /يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (\n                meridiem === 'يېرىم كېچە' ||\n                meridiem === 'سەھەر' ||\n                meridiem === 'چۈشتىن بۇرۇن'\n            ) {\n                return hour;\n            } else if (meridiem === 'چۈشتىن كېيىن' || meridiem === 'كەچ') {\n                return hour + 12;\n            } else {\n                return hour >= 11 ? hour : hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            var hm = hour * 100 + minute;\n            if (hm < 600) {\n                return 'يېرىم كېچە';\n            } else if (hm < 900) {\n                return 'سەھەر';\n            } else if (hm < 1130) {\n                return 'چۈشتىن بۇرۇن';\n            } else if (hm < 1230) {\n                return 'چۈش';\n            } else if (hm < 1800) {\n                return 'چۈشتىن كېيىن';\n            } else {\n                return 'كەچ';\n            }\n        },\n        calendar: {\n            sameDay: '[بۈگۈن سائەت] LT',\n            nextDay: '[ئەتە سائەت] LT',\n            nextWeek: '[كېلەركى] dddd [سائەت] LT',\n            lastDay: '[تۆنۈگۈن] LT',\n            lastWeek: '[ئالدىنقى] dddd [سائەت] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s كېيىن',\n            past: '%s بۇرۇن',\n            s: 'نەچچە سېكونت',\n            ss: '%d سېكونت',\n            m: 'بىر مىنۇت',\n            mm: '%d مىنۇت',\n            h: 'بىر سائەت',\n            hh: '%d سائەت',\n            d: 'بىر كۈن',\n            dd: '%d كۈن',\n            M: 'بىر ئاي',\n            MM: '%d ئاي',\n            y: 'بىر يىل',\n            yy: '%d يىل',\n        },\n\n        dayOfMonthOrdinalParse: /\\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'DDD':\n                    return number + '-كۈنى';\n                case 'w':\n                case 'W':\n                    return number + '-ھەپتە';\n                default:\n                    return number;\n            }\n        },\n        preparse: function (string) {\n            return string.replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string.replace(/,/g, '،');\n        },\n        week: {\n            // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 1st is the first week of the year.\n        },\n    });\n\n    return ugCn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,qFAAqF,CAACC,KAAK,CAC/F,GACJ,CAAC;IACDC,WAAW,EACP,qFAAqF,CAACD,KAAK,CACvF,GACJ,CAAC;IACLE,QAAQ,EAAE,wDAAwD,CAACF,KAAK,CACpE,GACJ,CAAC;IACDG,aAAa,EAAE,sBAAsB,CAACH,KAAK,CAAC,GAAG,CAAC;IAChDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,yBAAyB;MAC7BC,GAAG,EAAE,gCAAgC;MACrCC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,oDAAoD;IACnEC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IACIC,QAAQ,KAAK,YAAY,IACzBA,QAAQ,KAAK,OAAO,IACpBA,QAAQ,KAAK,cAAc,EAC7B;QACE,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,cAAc,IAAIA,QAAQ,KAAK,KAAK,EAAE;QAC1D,OAAOD,IAAI,GAAG,EAAE;MACpB,CAAC,MAAM;QACH,OAAOA,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIC,EAAE,GAAGJ,IAAI,GAAG,GAAG,GAAGE,MAAM;MAC5B,IAAIE,EAAE,GAAG,GAAG,EAAE;QACV,OAAO,YAAY;MACvB,CAAC,MAAM,IAAIA,EAAE,GAAG,GAAG,EAAE;QACjB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,cAAc;MACzB,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,cAAc;MACzB,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,2BAA2B;MACrCC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,4BAA4B;MACtCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IAEDC,sBAAsB,EAAE,4BAA4B;IACpDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;UACN,OAAOD,MAAM,GAAG,OAAO;QAC3B,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,QAAQ;QAC5B;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUF,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDE,IAAI,EAAE;MACF;MACAC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtD,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}