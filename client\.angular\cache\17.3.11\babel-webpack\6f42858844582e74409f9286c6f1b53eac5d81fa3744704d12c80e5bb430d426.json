{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/content-vendor.service\";\nimport * as i3 from \"primeng/galleria\";\nimport * as i4 from \"primeng/api\";\nconst _c0 = () => [\"/store/dashboard\"];\nconst _c1 = () => [\"/store/prospects\"];\nconst _c2 = () => [\"/store/account\"];\nconst _c3 = () => [\"/store/contacts\"];\nconst _c4 = () => [\"/store/activities/calls\"];\nconst _c5 = () => [\"/store/opportunities\"];\nconst _c6 = () => [\"/store/ai-insights\"];\nconst _c7 = () => [\"/store/sales-quotes\"];\nconst _c8 = () => [\"/store/sales-orders\"];\nfunction HomeComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"img\", 10);\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h1\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r1.url, i0.ɵɵsanitizeUrl)(\"alt\", item_r1.alt);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.content == null ? null : ctx_r1.content.i18n == null ? null : ctx_r1.content.i18n[\"label.title\"]) || \"\", \" \");\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor(route, CMSservice) {\n      this.route = route;\n      this.CMSservice = CMSservice;\n      this.images = [];\n      this.transitionInterval = 2000;\n    }\n    ngOnInit() {\n      this.content = this.route.snapshot.data['content'];\n      const mediaComponent = this.CMSservice.getDataByComponentName(this.content.body, \"crm.media\");\n      if (mediaComponent?.length) {\n        this.images = mediaComponent[0].Images;\n        this.transitionInterval = mediaComponent[0].timer || 2000;\n      }\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentVendorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        decls: 58,\n        vars: 24,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\", \"surface-card\"], [3, \"value\", \"circular\", \"showItemNavigators\", \"showThumbnails\", \"autoPlay\", \"transitionInterval\"], [\"pTemplate\", \"item\"], [1, \"home-box-list\", \"relative\", \"flex\", \"justify-content-center\", \"mt-8\", \"w-full\", \"gap-4\", \"mx-auto\", \"flex-wrap\", 3, \"routerLink\"], [1, \"home-box\", \"flex\", \"flex-column\", \"h-10rem\", \"w-10rem\", \"border-round\", \"cursor-pointer\", \"shadow-1\", \"border-1\", \"border-solid\", \"border-bluegray-100\", \"hover:border-primary\"], [1, \"home-icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mx-auto\", \"w-full\", \"h-6rem\", \"text-primary\", \"border-round\", \"bg-blue-100\"], [1, \"material-symbols-rounded\", \"text-5xl\"], [1, \"m-0\", \"mt-3\", \"relative\", \"block\", \"text-center\", \"text-md\", \"text-color\"], [1, \"home-box\", \"flex\", \"flex-column\", \"h-10rem\", \"w-10rem\", \"border-round\", \"cursor-pointer\", \"shadow-1\", \"border-1\", \"border-solid\", \"border-bluegray-100\", \"hover:border-primary\", 3, \"routerLink\"], [1, \"overview-banner\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-center\", \"h-32rem\", \"w-full\", \"border-round-lg\", \"overflow-hidden\"], [1, \"max-w-full\", \"w-full\", \"h-full\", 3, \"src\", \"alt\"], [1, \"banner-overlay\", \"absolute\", \"flex\", \"align-items-end\", \"justify-content-center\", \"w-full\", \"h-full\", \"top-0\", \"left-0\", \"p-8\"], [1, \"m-0\", \"p-0\", \"relative\", \"text-4xl\", \"font-semibold\", \"text-white\", \"text-center\", \"max-w-1200\", \"text-shadow-l-black\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"p-galleria\", 1);\n            i0.ɵɵtemplate(2, HomeComponent_ng_template_2_Template, 5, 3, \"ng-template\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"a\", 4)(5, \"div\", 5)(6, \"span\", 6);\n            i0.ɵɵtext(7, \"space_dashboard\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"h5\", 7);\n            i0.ɵɵtext(9, \"Dashboard\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 5)(12, \"span\", 6);\n            i0.ɵɵtext(13, \"person_search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"h5\", 7);\n            i0.ɵɵtext(15, \"Prospects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 5)(18, \"span\", 6);\n            i0.ɵɵtext(19, \"person\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"h5\", 7);\n            i0.ɵɵtext(21, \"Account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 5)(24, \"span\", 6);\n            i0.ɵɵtext(25, \"contact_phone\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"h5\", 7);\n            i0.ɵɵtext(27, \"Contacts\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 8)(29, \"div\", 5)(30, \"span\", 6);\n            i0.ɵɵtext(31, \"diversity_2\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"h5\", 7);\n            i0.ɵɵtext(33, \"Activities\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"div\", 8)(35, \"div\", 5)(36, \"span\", 6);\n            i0.ɵɵtext(37, \"wb_incandescent\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"h5\", 7);\n            i0.ɵɵtext(39, \"Opportunities\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 8)(41, \"div\", 5)(42, \"span\", 6);\n            i0.ɵɵtext(43, \"analytics\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"h5\", 7);\n            i0.ɵɵtext(45, \"Ai Insights\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 8)(47, \"div\", 5)(48, \"span\", 6);\n            i0.ɵɵtext(49, \"request_quote\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"h5\", 7);\n            i0.ɵɵtext(51, \"Sales Quotes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 8)(53, \"div\", 5)(54, \"span\", 6);\n            i0.ɵɵtext(55, \"list_alt\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"h5\", 7);\n            i0.ɵɵtext(57, \"Sales Orders\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", ctx.images)(\"circular\", true)(\"showItemNavigators\", true)(\"showThumbnails\", false)(\"autoPlay\", true)(\"transitionInterval\", ctx.transitionInterval);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(15, _c0));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(16, _c1));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c2));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c3));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c4));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c5));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(21, _c6));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(22, _c7));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(23, _c8));\n          }\n        },\n        dependencies: [i1.RouterLink, i3.Galleria, i4.PrimeTemplate],\n        styles: [\".surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{object-fit:cover}.surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";left:0;top:0;width:100%;height:100%;background:linear-gradient(0deg,rgba(0,0,0,.4392156863),transparent);mix-blend-mode:multiply}.home-box-list[_ngcontent-%COMP%]{max-width:1600px}.text-md[_ngcontent-%COMP%]{font-size:1.1rem!important}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}