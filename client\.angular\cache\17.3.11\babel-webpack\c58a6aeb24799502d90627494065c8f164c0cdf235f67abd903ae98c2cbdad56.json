{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OpportunitiesService {\n  constructor(http) {\n    this.http = http;\n    this.opportunitySubject = new BehaviorSubject(null);\n    this.opportunity = this.opportunitySubject.asObservable();\n  }\n  getOpportunities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'opportunity_id,bp_id,name,expected_value,probability,opportunity_status,expected_decision_date,createdAt,updatedAt');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][opportunity_id][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    });\n  }\n  getOpportunityByID(opportunityId) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', opportunityId);\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    }).pipe(map(response => {\n      const opportunityDetails = response?.data[0] || null;\n      this.opportunitySubject.next(opportunityDetails);\n      return response;\n    }));\n  }\n  static {\n    this.ɵfac = function OpportunitiesService_Factory(t) {\n      return new (t || OpportunitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OpportunitiesService,\n      factory: OpportunitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "OpportunitiesService", "constructor", "http", "opportunitySubject", "opportunity", "asObservable", "getOpportunities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "CRM_OPPORTUNITY", "getOpportunityByID", "opportunityId", "pipe", "response", "opportunityDetails", "data", "next", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OpportunitiesService {\r\n  public opportunitySubject = new BehaviorSubject<any>(null);\r\n  public opportunity = this.opportunitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getOpportunities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'opportunity_id,bp_id,name,expected_value,probability,opportunity_status,expected_decision_date,createdAt,updatedAt'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][2][opportunity_id][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getOpportunityByID(opportunityId: string) {\r\n    const params = new HttpParams().set('filters[bp_id][$eq]', opportunityId);\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const opportunityDetails = response?.data[0] || null;\r\n          this.opportunitySubject.next(opportunityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,kBAAkB,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IACnD,KAAAO,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAEpB;EAEvCC,gBAAgBA,CACdC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CACF,QAAQ,EACR,oHAAoH,CACrH;IAEH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;MACrEC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,mCAAmC,EAAEF,UAAU,CAAC;MACpEC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6CAA6C,EAC7CF,UAAU,CACX;IACH;IAEA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,eAAe,EAAE,EAAE;MACjEN;KACD,CAAC;EACJ;EAEAO,kBAAkBA,CAACC,aAAqB;IACtC,MAAMR,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAACiB,GAAG,CAAC,qBAAqB,EAAEO,aAAa,CAAC;IAEzE,OAAO,IAAI,CAAClB,IAAI,CACbe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,eAAe,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC,CAC7DS,IAAI,CACHvB,GAAG,CAAEwB,QAAa,IAAI;MACpB,MAAMC,kBAAkB,GAAGD,QAAQ,EAAEE,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACpD,IAAI,CAACrB,kBAAkB,CAACsB,IAAI,CAACF,kBAAkB,CAAC;MAChD,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;;;uBApDWtB,oBAAoB,EAAA0B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApB7B,oBAAoB;MAAA8B,OAAA,EAApB9B,oBAAoB,CAAA+B,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}