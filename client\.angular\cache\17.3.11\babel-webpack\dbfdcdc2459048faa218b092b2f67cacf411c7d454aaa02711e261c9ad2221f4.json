{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Gujarati [gu]\n//! author : <PERSON><PERSON><PERSON> Thank<PERSON> : https://github.com/Kaushik1987\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '૧',\n      2: '૨',\n      3: '૩',\n      4: '૪',\n      5: '૫',\n      6: '૬',\n      7: '૭',\n      8: '૮',\n      9: '૯',\n      0: '૦'\n    },\n    numberMap = {\n      '૧': '1',\n      '૨': '2',\n      '૩': '3',\n      '૪': '4',\n      '૫': '5',\n      '૬': '6',\n      '૭': '7',\n      '૮': '8',\n      '૯': '9',\n      '૦': '0'\n    };\n  var gu = moment.defineLocale('gu', {\n    months: 'જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર'.split('_'),\n    monthsShort: 'જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર'.split('_'),\n    weekdaysShort: 'રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ'.split('_'),\n    weekdaysMin: 'ર_સો_મં_બુ_ગુ_શુ_શ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm વાગ્યે',\n      LTS: 'A h:mm:ss વાગ્યે',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm વાગ્યે',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm વાગ્યે'\n    },\n    calendar: {\n      sameDay: '[આજ] LT',\n      nextDay: '[કાલે] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[ગઇકાલે] LT',\n      lastWeek: '[પાછલા] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s મા',\n      past: '%s પહેલા',\n      s: 'અમુક પળો',\n      ss: '%d સેકંડ',\n      m: 'એક મિનિટ',\n      mm: '%d મિનિટ',\n      h: 'એક કલાક',\n      hh: '%d કલાક',\n      d: 'એક દિવસ',\n      dd: '%d દિવસ',\n      M: 'એક મહિનો',\n      MM: '%d મહિનો',\n      y: 'એક વર્ષ',\n      yy: '%d વર્ષ'\n    },\n    preparse: function (string) {\n      return string.replace(/[૧૨૩૪૫૬૭૮૯૦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // Gujarati notation for meridiems are quite fuzzy in practice. While there exists\n    // a rigid notion of a 'Pahar' it is not used as rigidly in modern Gujarati.\n    meridiemParse: /રાત|બપોર|સવાર|સાંજ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'રાત') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'સવાર') {\n        return hour;\n      } else if (meridiem === 'બપોર') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'સાંજ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'રાત';\n      } else if (hour < 10) {\n        return 'સવાર';\n      } else if (hour < 17) {\n        return 'બપોર';\n      } else if (hour < 20) {\n        return 'સાંજ';\n      } else {\n        return 'રાત';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return gu;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}