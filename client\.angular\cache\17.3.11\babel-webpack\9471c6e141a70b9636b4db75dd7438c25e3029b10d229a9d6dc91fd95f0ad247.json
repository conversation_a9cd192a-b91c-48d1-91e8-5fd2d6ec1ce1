{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { SignupComponent } from './signup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SignupModule {\n  static {\n    this.ɵfac = function SignupModule_Factory(t) {\n      return new (t || SignupModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SignupModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, RouterModule.forChild([{\n        path: '',\n        component: SignupComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SignupModule, {\n    imports: [CommonModule, FormsModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "RouterModule", "SignupComponent", "SignupModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\session\\signup\\signup.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { FormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\nimport { SignupComponent } from './signup.component';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    RouterModule.forChild([{ path: '', component: SignupComponent }]),\r\n  ]\r\n})\r\nexport class SignupModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,oBAAoB;;;AAYpD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBALrBJ,YAAY,EACZC,WAAW,EACXC,YAAY,CAACG,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAe,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAGxDC,YAAY;IAAAI,OAAA,GALrBR,YAAY,EACZC,WAAW,EAAAQ,EAAA,CAAAP,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}