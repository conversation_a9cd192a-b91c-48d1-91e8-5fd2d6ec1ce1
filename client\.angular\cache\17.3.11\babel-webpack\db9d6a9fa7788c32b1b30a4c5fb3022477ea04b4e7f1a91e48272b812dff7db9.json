{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/editor\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"primeng/inputswitch\";\nimport * as i14 from \"primeng/tooltip\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"primeng/inputtext\";\nimport * as i17 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29)(4, \"span\", 30);\n    i0.ɵɵtext(5, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Opportunity ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"label\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Expected Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 31);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 28)(36, \"label\", 29)(37, \"span\", 30);\n    i0.ɵɵtext(38, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 32)(41, \"a\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 29)(46, \"span\", 30);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Primary Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 32)(50, \"a\", 33);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"div\", 27)(53, \"div\", 28)(54, \"label\", 29)(55, \"span\", 30);\n    i0.ɵɵtext(56, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(57, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 31);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 27)(61, \"div\", 28)(62, \"label\", 29)(63, \"span\", 30);\n    i0.ɵɵtext(64, \"account_tree\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \" Parent Opportunity \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 31);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 27)(69, \"div\", 28)(70, \"label\", 29)(71, \"span\", 30);\n    i0.ɵɵtext(72, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(73, \" Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 31);\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 27)(77, \"div\", 28)(78, \"label\", 29)(79, \"span\", 30);\n    i0.ɵɵtext(80, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 31);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(84, \"div\", 27)(85, \"div\", 28)(86, \"label\", 29)(87, \"span\", 30);\n    i0.ɵɵtext(88, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 31);\n    i0.ɵɵtext(91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(92, \"div\", 27)(93, \"div\", 28)(94, \"label\", 29)(95, \"span\", 30);\n    i0.ɵɵtext(96, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Reason for Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 31);\n    i0.ɵɵtext(99);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(100, \"div\", 27)(101, \"div\", 28)(102, \"label\", 29)(103, \"span\", 30);\n    i0.ɵɵtext(104, \"track_changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \" Days in Sales Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"div\", 31);\n    i0.ɵɵtext(107);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(108, \"div\", 27)(109, \"div\", 28)(110, \"label\", 29)(111, \"span\", 30);\n    i0.ɵɵtext(112, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(113, \" Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 31);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(116, \"div\", 27)(117, \"div\", 28)(118, \"label\", 29)(119, \"span\", 30);\n    i0.ɵɵtext(120, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"div\", 31);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(124, \"div\", 27)(125, \"div\", 28)(126, \"label\", 29)(127, \"span\", 30);\n    i0.ɵɵtext(128, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(129, \" Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 31);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(132, \"div\", 27)(133, \"div\", 28)(134, \"label\", 29)(135, \"span\", 30);\n    i0.ɵɵtext(136, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(137, \" Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"div\", 31);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(140, \"div\", 27)(141, \"div\", 28)(142, \"label\", 29)(143, \"span\", 30);\n    i0.ɵɵtext(144, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(145, \" Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(146, \"div\", 31);\n    i0.ɵɵtext(147);\n    i0.ɵɵpipe(148, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(149, \"div\", 27)(150, \"div\", 28)(151, \"label\", 29)(152, \"span\", 30);\n    i0.ɵɵtext(153, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(154, \" Last Updated Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(155, \"div\", 31);\n    i0.ɵɵtext(156);\n    i0.ɵɵpipe(157, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(158, \"div\", 27)(159, \"div\", 28)(160, \"label\", 29)(161, \"span\", 30);\n    i0.ɵɵtext(162, \"update\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(163, \" Last Updated By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(164, \"div\", 31);\n    i0.ɵɵtext(165);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(166, \"div\", 27)(167, \"div\", 28)(168, \"label\", 29)(169, \"span\", 30);\n    i0.ɵɵtext(170, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(171, \" Progress \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(172, \"div\", 31);\n    i0.ɵɵtext(173);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(174, \"div\", 27)(175, \"div\", 28)(176, \"label\", 29)(177, \"span\", 30);\n    i0.ɵɵtext(178, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(179, \" Need Help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(180, \"div\", 31);\n    i0.ɵɵtext(181);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.opportunity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_end_date) ? i0.ɵɵpipeBind2(33, 24, ctx_r0.overviewDetails.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0] == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"opportunityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.group_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.parent_opportunity) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunitySource\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.origin_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.weighted_expected_net_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.life_cycle_status_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.result_reason_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.days_in_sales_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.probability_percent) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_organisation_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_unit_party_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_start_date) ? i0.ɵɵpipeBind2(148, 27, ctx_r0.overviewDetails.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_change_date) ? i0.ɵɵpipeBind2(157, 30, ctx_r0.overviewDetails.last_change_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_changed_by) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.progress) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.need_help) || \"-\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_11_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_23_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template, 2, 1, \"span\", 59);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_42_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"input\", 61);\n    i0.ɵɵlistener(\"change\", function OpportunitiesOverviewComponent_form_6_ng_template_53_Template_input_change_1_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).item;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleSelection(item_r5.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template, 2, 1, \"span\", 59)(5, OpportunitiesOverviewComponent_form_6_ng_template_53_span_5_Template, 2, 1, \"span\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.isSelected(item_r5.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_54_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_87_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\", 35)(5, \"span\", 36);\n    i0.ɵɵtext(6, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Name \");\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"input\", 38);\n    i0.ɵɵtemplate(11, OpportunitiesOverviewComponent_form_6_div_11_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 28)(14, \"label\", 35)(15, \"span\", 36);\n    i0.ɵɵtext(16, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \"Expected Value \");\n    i0.ɵɵelementStart(18, \"span\", 37);\n    i0.ɵɵtext(19, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 41);\n    i0.ɵɵelement(21, \"input\", 42)(22, \"p-dropdown\", 43);\n    i0.ɵɵtemplate(23, OpportunitiesOverviewComponent_form_6_div_23_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 27)(25, \"div\", 28)(26, \"label\", 35)(27, \"span\", 36);\n    i0.ɵɵtext(28, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \"Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p-calendar\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"div\", 28)(33, \"label\", 35)(34, \"span\", 36);\n    i0.ɵɵtext(35, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \"Account \");\n    i0.ɵɵelementStart(37, \"span\", 37);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"ng-select\", 45);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵtemplate(41, OpportunitiesOverviewComponent_form_6_ng_template_41_Template, 3, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpportunitiesOverviewComponent_form_6_div_42_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 35)(46, \"span\", 36);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \"Primary Contact \");\n    i0.ɵɵelementStart(49, \"span\", 37);\n    i0.ɵɵtext(50, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"ng-select\", 47);\n    i0.ɵɵpipe(52, \"async\");\n    i0.ɵɵtemplate(53, OpportunitiesOverviewComponent_form_6_ng_template_53_Template, 6, 5, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpportunitiesOverviewComponent_form_6_div_54_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 27)(56, \"div\", 28)(57, \"label\", 35)(58, \"span\", 36);\n    i0.ɵɵtext(59, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-dropdown\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 27)(63, \"div\", 28)(64, \"label\", 35)(65, \"span\", 36);\n    i0.ɵɵtext(66, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-dropdown\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 40)(70, \"div\", 28)(71, \"label\", 35)(72, \"span\", 36);\n    i0.ɵɵtext(73, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 41);\n    i0.ɵɵelement(76, \"input\", 50)(77, \"p-dropdown\", 43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"div\", 27)(79, \"div\", 28)(80, \"label\", 35)(81, \"span\", 36);\n    i0.ɵɵtext(82, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \"Status \");\n    i0.ɵɵelementStart(84, \"span\", 37);\n    i0.ɵɵtext(85, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(86, \"p-dropdown\", 51);\n    i0.ɵɵtemplate(87, OpportunitiesOverviewComponent_form_6_div_87_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 27)(89, \"div\", 28)(90, \"label\", 35)(91, \"span\", 36);\n    i0.ɵɵtext(92, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 27)(96, \"div\", 28)(97, \"label\", 35)(98, \"span\", 36);\n    i0.ɵɵtext(99, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(101, \"input\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 27)(103, \"div\", 28)(104, \"label\", 35)(105, \"span\", 36);\n    i0.ɵɵtext(106, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(107, \"Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(108, \"input\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"div\", 27)(110, \"div\", 28)(111, \"label\", 35)(112, \"span\", 36);\n    i0.ɵɵtext(113, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(114, \"Created Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(115, \"p-calendar\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 27)(117, \"div\", 28)(118, \"label\", 35)(119, \"span\", 36);\n    i0.ɵɵtext(120, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \"Need help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(122, \"p-inputSwitch\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(123, \"div\", 57)(124, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_form_6_Template_button_click_124_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.OpportunityOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c2, ctx_r0.submitted && ctx_r0.f[\"name\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c2, ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 37, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(45, _c2, ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(52, 39, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(47, _c2, ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityCategory\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunitySource\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityStatus\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(49, _c2, ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNote === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 69);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNote === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 69);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 70);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener() {\n      const col_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r8.field, ctx_r0.notedetails, \"notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_4_Template, 1, 1, \"i\", 64)(5, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_i_5_Template, 1, 0, \"i\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r8.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r8.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote === col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote !== col_r8.field);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 62);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_16_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.customSort(\"note\", ctx_r0.notedetails, \"notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 63);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_ng_template_16_i_4_Template, 1, 1, \"i\", 64)(5, OpportunitiesOverviewComponent_ng_template_16_i_5_Template, 1, 0, \"i\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, OpportunitiesOverviewComponent_ng_template_16_ng_container_6_Template, 6, 4, \"ng-container\", 66);\n    i0.ɵɵelementStart(7, \"th\", 67);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNote !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNoteColumns);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r10 == null ? null : notes_r10.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (notes_r10 == null ? null : notes_r10.updatedBy) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 75);\n    i0.ɵɵtemplate(3, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 76)(4, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template, 2, 1, \"ng-container\", 76);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r11.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedBy\");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 71);\n    i0.ɵɵelement(1, \"td\", 72);\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_ng_template_17_ng_container_2_Template, 5, 3, \"ng-container\", 66);\n    i0.ɵɵelementStart(3, \"td\", 67)(4, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_17_Template_button_click_4_listener() {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_17_Template_button_click_5_listener($event) {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r10));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r10 == null ? null : notes_r10.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNoteColumns);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 77);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 77);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_div_26_div_1_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport let OpportunitiesOverviewComponent = /*#__PURE__*/(() => {\n  class OpportunitiesOverviewComponent {\n    constructor(formBuilder, opportunitiesservice, activitiesservice, messageservice, confirmationservice, router) {\n      this.formBuilder = formBuilder;\n      this.opportunitiesservice = opportunitiesservice;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.router = router;\n      this.ngUnsubscribe = new Subject();\n      this.overviewDetails = null;\n      this.notedetails = null;\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.opportunity_id = '';\n      this.editid = '';\n      this.isEditMode = false;\n      this.currencies = [{\n        label: 'USD',\n        value: 'USD'\n      }];\n      this.notevisible = false;\n      this.noteposition = 'right';\n      this.notesubmitted = false;\n      this.notesaving = false;\n      this.noteeditid = '';\n      this.OpportunityOverviewForm = this.formBuilder.group({\n        name: ['', [Validators.required]],\n        prospect_party_id: ['', [Validators.required]],\n        primary_contact_party_id: ['', [Validators.required]],\n        origin_type_code: [''],\n        expected_revenue_amount: ['', [Validators.required]],\n        weighted_expected_net_amount: [''],\n        expected_revenue_start_date: [''],\n        expected_revenue_end_date: [''],\n        life_cycle_status_code: ['', [Validators.required]],\n        probability_percent: [''],\n        group_code: [''],\n        sales_organisation_id: [''],\n        sales_unit_party_id: [''],\n        currency: ['USD'],\n        need_help: ['']\n      });\n      this.NoteForm = this.formBuilder.group({\n        note: ['', [Validators.required]]\n      });\n      this.dropdowns = {\n        opportunityCategory: [],\n        opportunityStatus: [],\n        opportunitySource: []\n      };\n      this._selectedNoteColumns = [];\n      this.NoteCols = [{\n        field: 'createdAt',\n        header: 'Created At'\n      }, {\n        field: 'updatedBy',\n        header: 'Updated At'\n      }];\n      this.sortFieldNote = '';\n      this.sortOrderNote = 1;\n    }\n    ngOnInit() {\n      // Opportunities successfully added message.\n      setTimeout(() => {\n        const successMessage = sessionStorage.getItem('opportunitiesMessage');\n        if (successMessage) {\n          this.messageservice.add({\n            severity: 'success',\n            detail: successMessage\n          });\n          sessionStorage.removeItem('opportunitiesMessage');\n        }\n      }, 100);\n      this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n      this.OpportunityOverviewForm.get('prospect_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n        if (selectedBpId) {\n          this.loadAccountByContacts(selectedBpId);\n        } else {\n          this.contacts$ = of(this.defaultOptions);\n        }\n      }), catchError(err => {\n        console.error('Account selection error:', err);\n        this.contacts$ = of(this.defaultOptions);\n        return of();\n      })).subscribe();\n      this.loadAccounts();\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n        if (!response) return;\n        this.opportunity_id = response?.opportunity_id;\n        this.overviewDetails = response;\n        this.notedetails = response?.notes;\n        if (this.overviewDetails) {\n          this.fetchOverviewData(this.overviewDetails);\n        }\n      });\n      this._selectedNoteColumns = this.NoteCols;\n    }\n    get selectedNoteColumns() {\n      return this._selectedNoteColumns;\n    }\n    set selectedNoteColumns(val) {\n      this._selectedNoteColumns = this.NoteCols.filter(col => val.includes(col));\n    }\n    onNoteColumnReorder(event) {\n      const draggedCol = this.NoteCols[event.dragIndex];\n      this.NoteCols.splice(event.dragIndex, 1);\n      this.NoteCols.splice(event.dropIndex, 0, draggedCol);\n    }\n    customSort(field, data, type) {\n      if (type === 'notes') {\n        this.sortFieldNote = field;\n        this.sortOrderNote = this.sortOrderNote === 1 ? -1 : 1;\n      }\n      data.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrderNote * result;\n      });\n    }\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        let fields = field.split('.');\n        let value = data;\n        for (let i = 0; i < fields.length; i++) {\n          if (value == null) return null;\n          value = value[fields[i]];\n        }\n        return value;\n      }\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Emit default empty options first\n      this.accountInput$.pipe(debounceTime(300),\n      // Add debounce to reduce API calls\n      distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          'filters[roles][bp_role][$in][0]': 'FLCU01',\n          'filters[roles][bp_role][$in][1]': 'FLCU00',\n          'fields[0]': 'bp_id',\n          'fields[1]': 'first_name',\n          'fields[2]': 'last_name',\n          'fields[3]': 'bp_full_name'\n        };\n        if (term) {\n          params['filters[$or][0][bp_id][$containsi]'] = term;\n          params['filters[$or][1][bp_full_name][$containsi]'] = term;\n        }\n        return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n        // Ensure non-null\n        catchError(error => {\n          console.error('Account fetch error:', error);\n          return of([]); // Return empty list on error\n        }), finalize(() => this.accountLoading = false) // Always turn off loading\n        );\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    toggleSelection(id) {\n      const control = this.OpportunityOverviewForm.get('primary_contact_party_id');\n      let currentValue = control?.value || [];\n      if (currentValue.includes(id)) {\n        currentValue = currentValue.filter(v => v !== id);\n      } else {\n        currentValue = [...currentValue, id];\n      }\n      control?.setValue(currentValue);\n    }\n    isSelected(id) {\n      return this.OpportunityOverviewForm.get('primary_contact_party_id')?.value?.includes(id);\n    }\n    editNote(note) {\n      this.notevisible = true;\n      this.noteeditid = note?.documentId;\n      this.NoteForm.patchValue(note);\n    }\n    fetchOverviewData(opportunity) {\n      this.existingopportunity = {\n        opportunity_id: opportunity?.opportunity_id,\n        name: opportunity?.name,\n        group_code: opportunity?.group_code,\n        origin_type_code: opportunity?.origin_type_code,\n        probability_percent: opportunity?.probability_percent,\n        expected_revenue_amount: opportunity?.expected_revenue_amount,\n        expected_revenue_start_date: opportunity?.expected_revenue_start_date,\n        expected_revenue_end_date: opportunity?.expected_revenue_end_date,\n        prospect_party_id: opportunity?.prospect_party_id,\n        primary_contact_party_id: opportunity?.primary_contact_party_id,\n        weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\n        life_cycle_status_code: opportunity?.life_cycle_status_code,\n        main_employee_responsible_party_id: opportunity?.main_employee_responsible_party_id,\n        sales_organisation_id: opportunity?.sales_organisation_id,\n        sales_unit_party_id: opportunity?.sales_unit_party_id\n      };\n      this.editid = opportunity.documentId;\n      this.OpportunityOverviewForm.patchValue(this.existingopportunity);\n    }\n    onNoteSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.notesubmitted = true;\n        _this.notevisible = true;\n        if (_this.NoteForm.invalid) {\n          _this.notevisible = true;\n          return;\n        }\n        _this.notesaving = true;\n        const value = {\n          ..._this.NoteForm.value\n        };\n        const data = {\n          opportunity_id: _this.opportunity_id,\n          note: value?.note\n        };\n        if (_this.noteeditid) {\n          _this.opportunitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n            complete: () => {\n              _this.notesaving = false;\n              _this.notevisible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Updated Successfully!.'\n              });\n              _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n            },\n            error: res => {\n              _this.notesaving = false;\n              _this.notevisible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.opportunitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n            complete: () => {\n              _this.notesaving = false;\n              _this.notevisible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Created Successfully!.'\n              });\n              _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n            },\n            error: res => {\n              _this.notesaving = false;\n              _this.notevisible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    onSubmit() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.submitted = true;\n        if (_this2.OpportunityOverviewForm.invalid) {\n          return;\n        }\n        _this2.saving = true;\n        const value = {\n          ..._this2.OpportunityOverviewForm.value\n        };\n        const data = {\n          name: value?.name,\n          expected_revenue_amount: value?.expected_revenue_amount,\n          expected_revenue_end_date: value?.expected_revenue_end_date ? _this2.formatDate(value.expected_revenue_end_date) : null,\n          prospect_party_id: value?.prospect_party_id,\n          primary_contact_party_id: value?.primary_contact_party_id,\n          group_code: value?.group_code,\n          origin_type_code: value?.origin_type_code,\n          weighted_expected_net_amount: value?.weighted_expected_net_amount,\n          life_cycle_status_code: value?.life_cycle_status_code,\n          probability_percent: value?.probability_percent,\n          main_employee_responsible_party_id: value?.main_employee_responsible_party_id,\n          sales_organisation_id: value?.sales_organisation_id,\n          sales_unit_party_id: value?.sales_unit_party_id,\n          expected_revenue_start_date: value?.expected_revenue_start_date ? _this2.formatDate(value.expected_revenue_start_date) : null\n        };\n        _this2.opportunitiesservice.updateOpportunity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n          next: response => {\n            _this2.messageservice.add({\n              severity: 'success',\n              detail: 'Opportunity Updated successFully!'\n            });\n            _this2.opportunitiesservice.getOpportunityByID(_this2.opportunity_id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n            _this2.isEditMode = false;\n          },\n          error: res => {\n            _this2.saving = false;\n            _this2.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const d = date instanceof Date ? date : new Date(date);\n      if (isNaN(d.getTime())) return ''; // handle invalid date\n      const yyyy = d.getFullYear();\n      const mm = String(d.getMonth() + 1).padStart(2, '0');\n      const dd = String(d.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.opportunitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    showDialog(position) {\n      this.noteposition = position;\n      this.notevisible = true;\n      this.notesubmitted = false;\n      this.NoteForm.reset();\n    }\n    get fNote() {\n      return this.NoteForm.controls;\n    }\n    get f() {\n      return this.OpportunityOverviewForm.controls;\n    }\n    toggleEdit() {\n      this.isEditMode = !this.isEditMode;\n    }\n    onReset() {\n      this.submitted = false;\n      this.OpportunityOverviewForm.reset();\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function OpportunitiesOverviewComponent_Factory(t) {\n        return new (t || OpportunitiesOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunitiesOverviewComponent,\n        selectors: [[\"app-opportunities-overview\"]],\n        decls: 30,\n        vars: 31,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"font-medium\", \"text-700\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [1, \"flex\", \"align-items-center\", \"w-full\", \"gap-2\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"currency\", \"optionLabel\", \"label\", \"placeholder\", \"Currency\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"expected_revenue_end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Expected Decision Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select Category\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select Source\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"weighted_expected_net_amount\", \"placeholder\", \"Weighted Value\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select Status\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_organisation_id\", \"type\", \"text\", \"formControlName\", \"sales_organisation_id\", \"placeholder\", \"Sales Organization'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_unit_party_id\", \"type\", \"text\", \"formControlName\", \"sales_unit_party_id\", \"placeholder\", \"Sales Unit\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"expected_revenue_start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Created Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"need_help\", 1, \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n        template: function OpportunitiesOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 3);\n            i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_4_listener() {\n              return ctx.toggleEdit();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(5, OpportunitiesOverviewComponent_div_5_Template, 182, 33, \"div\", 4)(6, OpportunitiesOverviewComponent_form_6_Template, 125, 51, \"form\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n            i0.ɵɵtext(10, \"Notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"p-button\", 9);\n            i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_12_listener() {\n              return ctx.showDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p-multiSelect\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedNoteColumns, $event) || (ctx.selectedNoteColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-table\", 12);\n            i0.ɵɵlistener(\"onColReorder\", function OpportunitiesOverviewComponent_Template_p_table_onColReorder_15_listener($event) {\n              return ctx.onNoteColumnReorder($event);\n            });\n            i0.ɵɵtemplate(16, OpportunitiesOverviewComponent_ng_template_16_Template, 9, 3, \"ng-template\", 13)(17, OpportunitiesOverviewComponent_ng_template_17_Template, 6, 2, \"ng-template\", 14)(18, OpportunitiesOverviewComponent_ng_template_18_Template, 3, 0, \"ng-template\", 15)(19, OpportunitiesOverviewComponent_ng_template_19_Template, 3, 0, \"ng-template\", 16);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"p-dialog\", 17);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_20_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(21, OpportunitiesOverviewComponent_ng_template_21_Template, 2, 0, \"ng-template\", 13);\n            i0.ɵɵelementStart(22, \"form\", 18)(23, \"div\", 19)(24, \"div\", 20);\n            i0.ɵɵelement(25, \"p-editor\", 21);\n            i0.ɵɵtemplate(26, OpportunitiesOverviewComponent_div_26_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 23)(28, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_28_listener() {\n              return ctx.notevisible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_29_listener() {\n              return ctx.onNoteSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.NoteCols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedNoteColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(27, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(28, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i7.Table, i4.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.Editor, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.InputSwitch, i14.Tooltip, i15.Calendar, i16.InputText, i17.MultiSelect, i6.AsyncPipe, i6.DatePipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .note-popup .p-dialog{margin-right:50px}  .note-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .note-popup .p-dialog .p-dialog-header h4{margin:0}  .note-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return OpportunitiesOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}