{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"../opportunities.service\";\nimport * as i5 from \"../../activities/activities.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/toast\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/editor\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/calendar\";\nimport * as i13 from \"primeng/inputtext\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => ({\n  height: \"125px\"\n});\nfunction AddOpportunitieComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_15_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction AddOpportunitieComponent_ng_template_26_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r2.bp_full_name, \"\");\n  }\n}\nfunction AddOpportunitieComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddOpportunitieComponent_ng_template_26_span_2_Template, 2, 1, \"span\", 28);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.bp_full_name);\n  }\n}\nfunction AddOpportunitieComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_27_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AddOpportunitieComponent_ng_template_38_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.email, \"\");\n  }\n}\nfunction AddOpportunitieComponent_ng_template_38_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.mobile, \"\");\n  }\n}\nfunction AddOpportunitieComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddOpportunitieComponent_ng_template_38_span_3_Template, 2, 1, \"span\", 28)(4, AddOpportunitieComponent_ng_template_38_span_4_Template, 2, 1, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r3.bp_id, \": \", item_r3.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.mobile);\n  }\n}\nfunction AddOpportunitieComponent_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_39_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AddOpportunitieComponent_div_56_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_56_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction AddOpportunitieComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_80_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction AddOpportunitieComponent_div_104_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddOpportunitieComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddOpportunitieComponent_div_104_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"note\"].errors && ctx_r0.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport let AddOpportunitieComponent = /*#__PURE__*/(() => {\n  class AddOpportunitieComponent {\n    constructor(formBuilder, router, messageservice, opportunitiesservice, activitiesservice) {\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.opportunitiesservice = opportunitiesservice;\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.owner_id = null;\n      this.OpportunityForm = this.formBuilder.group({\n        name: ['', [Validators.required]],\n        prospect_party_id: ['', [Validators.required]],\n        primary_contact_party_id: ['', [Validators.required]],\n        origin_type_code: [''],\n        expected_revenue_amount: ['', [Validators.required]],\n        expected_revenue_start_date: [''],\n        expected_revenue_end_date: [''],\n        life_cycle_status_code: ['', [Validators.required]],\n        probability_percent: [''],\n        group_code: [''],\n        note: ['', [Validators.required]]\n      });\n      this.dropdowns = {\n        opportunityCategory: [],\n        opportunityStatus: [],\n        opportunitySource: []\n      };\n    }\n    ngOnInit() {\n      this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n      this.OpportunityForm.get('prospect_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n        if (selectedBpId) {\n          this.loadAccountByContacts(selectedBpId);\n        } else {\n          this.contacts$ = of(this.defaultOptions);\n        }\n      }), catchError(err => {\n        console.error('Account selection error:', err);\n        this.contacts$ = of(this.defaultOptions);\n        return of();\n      })).subscribe();\n      this.getOwner().subscribe({\n        next: response => {\n          this.owner_id = response;\n        },\n        error: err => {\n          console.error('Error fetching bp_id:', err);\n        }\n      });\n      this.loadAccounts();\n    }\n    getOwner() {\n      return this.activitiesservice.getEmailwisePartner();\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        const options = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n        // Assign options to dropdown object\n        this.dropdowns[target] = options;\n        // Set 'Open' as default selected for activityStatus only\n        if (target === 'opportunityStatus') {\n          const openOption = options.find(opt => opt.label.toLowerCase() === 'discover');\n          if (openOption) {\n            this.OpportunityForm.get('life_cycle_status_code')?.setValue(openOption.value);\n          }\n        }\n      });\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Emit default empty options first\n      this.accountInput$.pipe(debounceTime(300),\n      // Add debounce to reduce API calls\n      distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          'filters[roles][bp_role][$in][0]': 'FLCU01',\n          'filters[roles][bp_role][$in][1]': 'FLCU00',\n          'fields[0]': 'bp_id',\n          'fields[1]': 'first_name',\n          'fields[2]': 'last_name',\n          'fields[3]': 'bp_full_name'\n        };\n        if (term) {\n          params['filters[$or][0][bp_id][$containsi]'] = term;\n          params['filters[$or][1][bp_full_name][$containsi]'] = term;\n        }\n        return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n        // Ensure non-null\n        catchError(error => {\n          console.error('Account fetch error:', error);\n          return of([]); // Return empty list on error\n        }), finalize(() => this.accountLoading = false) // Always turn off loading\n        );\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.OpportunityForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.OpportunityForm.value\n        };\n        const data = {\n          name: value?.name,\n          prospect_party_id: value?.prospect_party_id,\n          primary_contact_party_id: value?.primary_contact_party_id,\n          origin_type_code: value?.origin_type_code,\n          expected_revenue_amount: value?.expected_revenue_amount,\n          expected_revenue_start_date: value?.expected_revenue_start_date ? _this.formatDate(value.expected_revenue_start_date) : null,\n          expected_revenue_end_date: value?.expected_revenue_end_date ? _this.formatDate(value.expected_revenue_end_date) : null,\n          life_cycle_status_code: value?.life_cycle_status_code,\n          probability_percent: value?.probability_percent,\n          group_code: value?.group_code,\n          main_employee_responsible_party_id: _this.owner_id,\n          note: value?.note\n        };\n        _this.opportunitiesservice.createOpportunity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: response => {\n            if (response?.data?.opportunity_id) {\n              sessionStorage.setItem('opportunitiesMessage', 'Opportunities created successfully!');\n              window.location.href = `${window.location.origin}#/store/opportunities/${response?.data?.opportunity_id}/overview`;\n            } else {\n              console.error('Missing opportunity_id in response:', response);\n            }\n          },\n          error: res => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.OpportunityForm.controls;\n    }\n    onCancel() {\n      this.router.navigate(['/store/opportunities']);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AddOpportunitieComponent_Factory(t) {\n        return new (t || AddOpportunitieComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.OpportunitiesService), i0.ɵɵdirectiveInject(i5.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddOpportunitieComponent,\n        selectors: [[\"app-add-opportunitie\"]],\n        decls: 108,\n        vars: 57,\n        consts: [[\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select a Source\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"pInputText\", \"\", \"id\", \"expected_revenue_amount\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"expected_revenue_start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"formControlName\", \"expected_revenue_end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [1, \"col-12\", \"lg:col-8\", \"md:col-8\", \"sm:col-6\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter your note here...\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\", \"ml-auto\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Create\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n        template: function AddOpportunitieComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"form\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n            i0.ɵɵtext(4, \"Create Opportunity\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7)(9, \"span\", 8);\n            i0.ɵɵtext(10, \"badge\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Name \");\n            i0.ɵɵelementStart(12, \"span\", 9);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(14, \"input\", 10);\n            i0.ɵɵtemplate(15, AddOpportunitieComponent_div_15_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 5)(17, \"div\", 6)(18, \"label\", 7)(19, \"span\", 8);\n            i0.ɵɵtext(20, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" Account \");\n            i0.ɵɵelementStart(22, \"span\", 9);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"ng-select\", 12);\n            i0.ɵɵpipe(25, \"async\");\n            i0.ɵɵtemplate(26, AddOpportunitieComponent_ng_template_26_Template, 3, 2, \"ng-template\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, AddOpportunitieComponent_div_27_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 5)(29, \"div\", 6)(30, \"label\", 7)(31, \"span\", 8);\n            i0.ɵɵtext(32, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(33, \" Primary Contact \");\n            i0.ɵɵelementStart(34, \"span\", 9);\n            i0.ɵɵtext(35, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"ng-select\", 14);\n            i0.ɵɵpipe(37, \"async\");\n            i0.ɵɵtemplate(38, AddOpportunitieComponent_ng_template_38_Template, 5, 4, \"ng-template\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(39, AddOpportunitieComponent_div_39_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 5)(41, \"div\", 6)(42, \"label\", 7)(43, \"span\", 8);\n            i0.ɵɵtext(44, \"source\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(45, \" Source \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"p-dropdown\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 5)(48, \"div\", 6)(49, \"label\", 7)(50, \"span\", 8);\n            i0.ɵɵtext(51, \"show_chart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(52, \" Expected Value \");\n            i0.ɵɵelementStart(53, \"span\", 9);\n            i0.ɵɵtext(54, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(55, \"input\", 16);\n            i0.ɵɵtemplate(56, AddOpportunitieComponent_div_56_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"div\", 5)(58, \"div\", 6)(59, \"label\", 7)(60, \"span\", 8);\n            i0.ɵɵtext(61, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(62, \" Create Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(63, \"p-calendar\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(64, \"div\", 5)(65, \"div\", 6)(66, \"label\", 7)(67, \"span\", 8);\n            i0.ɵɵtext(68, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(69, \" Expected Decision Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(70, \"p-calendar\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(71, \"div\", 5)(72, \"div\", 6)(73, \"label\", 7)(74, \"span\", 8);\n            i0.ɵɵtext(75, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(76, \" Status \");\n            i0.ɵɵelementStart(77, \"span\", 9);\n            i0.ɵɵtext(78, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(79, \"p-dropdown\", 19);\n            i0.ɵɵtemplate(80, AddOpportunitieComponent_div_80_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"div\", 5)(82, \"div\", 6)(83, \"label\", 7)(84, \"span\", 8);\n            i0.ɵɵtext(85, \"percent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(86, \" Probability \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(87, \"input\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(88, \"div\", 5)(89, \"div\", 6)(90, \"label\", 7)(91, \"span\", 8);\n            i0.ɵɵtext(92, \"category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(93, \" Category \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(94, \"p-dropdown\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 22)(96, \"div\", 6)(97, \"label\", 7)(98, \"span\", 8);\n            i0.ɵɵtext(99, \"notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(100, \" Notes \");\n            i0.ɵɵelementStart(101, \"span\", 9);\n            i0.ɵɵtext(102, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(103, \"p-editor\", 23);\n            i0.ɵɵtemplate(104, AddOpportunitieComponent_div_104_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(105, \"div\", 24)(106, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function AddOpportunitieComponent_Template_button_click_106_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function AddOpportunitieComponent_Template_button_click_107_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.OpportunityForm);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c0, ctx.submitted && ctx.f[\"name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"name\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(25, 40, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(46, _c0, ctx.submitted && ctx.f[\"prospect_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"prospect_party_id\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(37, 42, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(48, _c0, ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunitySource\"]);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c0, ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(52, _c0, ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors);\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityCategory\"]);\n            i0.ɵɵadvance(9);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(54, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c0, ctx.submitted && ctx.f[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.Toast, i8.ButtonDirective, i9.Dropdown, i10.Editor, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.Calendar, i13.InputText, i6.AsyncPipe]\n      });\n    }\n  }\n  return AddOpportunitieComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}