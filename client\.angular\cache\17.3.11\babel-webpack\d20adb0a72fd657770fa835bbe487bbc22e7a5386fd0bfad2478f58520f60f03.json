{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Country, State } from 'country-state-city';\nimport { finalize } from 'rxjs/operators';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../prospects.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dialog\";\nimport * as i14 from \"../employee-select/employee-select.component\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => ({\n  width: \"45rem\"\n});\nfunction AddProspectComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_15_div_1_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"bp_full_name\"].errors && ctx_r1.f[\"bp_full_name\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_25_div_1_Template, 2, 0, \"div\", 49)(2, AddProspectComponent_div_25_div_2_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction AddProspectComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid website URL. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_33_div_1_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"website_url\"].errors[\"pattern\"]);\n  }\n}\nfunction AddProspectComponent_div_64_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Country is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_64_div_1_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"country\"].errors && ctx_r1.f[\"country\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_div_74_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" State is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AddProspectComponent_div_74_div_1_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"region\"].errors && ctx_r1.f[\"region\"].errors[\"required\"]);\n  }\n}\nfunction AddProspectComponent_ng_template_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 50)(2, \"span\", 51)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" First Name\");\n    i0.ɵɵelementStart(6, \"span\", 10);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"th\", 50)(9, \"span\", 51)(10, \"span\", 13);\n    i0.ɵɵtext(11, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Last Name\");\n    i0.ɵɵelementStart(13, \"span\", 10);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"th\", 50)(16, \"span\", 51)(17, \"span\", 13);\n    i0.ɵɵtext(18, \"inbox_text_person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" Department\");\n    i0.ɵɵelementStart(20, \"span\", 10);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"th\", 50)(23, \"span\", 51)(24, \"span\", 13);\n    i0.ɵɵtext(25, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Email Address\");\n    i0.ɵɵelementStart(27, \"span\", 10);\n    i0.ɵɵtext(28, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"th\", 50)(30, \"span\", 51)(31, \"span\", 13);\n    i0.ɵɵtext(32, \"phone_iphone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Phone\");\n    i0.ɵɵelementStart(34, \"span\", 10);\n    i0.ɵɵtext(35, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"th\", 50)(37, \"span\", 51)(38, \"span\", 13);\n    i0.ɵɵtext(39, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddProspectComponent_ng_template_107_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 48);\n    i0.ɵɵtext(1, \"This is Required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 48);\n    i0.ɵɵtext(1, \"This is Required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_small_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 48);\n    i0.ɵɵtext(1, \" Select a valid Department.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_small_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 48);\n    i0.ɵɵtext(1, \"Enter a valid email.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 48);\n    i0.ɵɵtext(1, \"Enter a valid phone.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function AddProspectComponent_ng_template_107_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r6 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 2)(1, \"td\");\n    i0.ɵɵelement(2, \"input\", 52);\n    i0.ɵɵtemplate(3, AddProspectComponent_ng_template_107_small_3_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵelement(5, \"input\", 53);\n    i0.ɵɵtemplate(6, AddProspectComponent_ng_template_107_small_6_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"p-dropdown\", 54);\n    i0.ɵɵlistener(\"onChange\", function AddProspectComponent_ng_template_107_Template_p_dropdown_onChange_8_listener($event) {\n      const contact_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeDepartment($event, contact_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AddProspectComponent_ng_template_107_small_9_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵelement(11, \"input\", 55);\n    i0.ɵɵtemplate(12, AddProspectComponent_ng_template_107_small_12_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵelement(14, \"input\", 56);\n    i0.ɵɵtemplate(15, AddProspectComponent_ng_template_107_small_15_Template, 2, 0, \"small\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 57);\n    i0.ɵɵtemplate(17, AddProspectComponent_ng_template_107_button_17_Template, 1, 0, \"button\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r4 = ctx.$implicit;\n    const i_r6 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"first_name\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"last_name\", \"contacts\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"contact_person_department\", \"contacts\") || ctx_r1.isFieldInvalid(i_r6, \"contact_person_department_name\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"email_address\", \"contacts\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(i_r6, \"phone_number\", \"contacts\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contacts.length > 1);\n  }\n}\nfunction AddProspectComponent_ng_template_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 60)(2, \"span\", 51)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"supervisor_account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Role \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"th\", 60)(7, \"span\", 51)(8, \"span\", 13);\n    i0.ɵɵtext(9, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Employee \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 50)(12, \"span\", 51)(13, \"span\", 13);\n    i0.ɵɵtext(14, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AddProspectComponent_ng_template_117_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function AddProspectComponent_ng_template_117_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r8 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteEmployee(i_r8));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2)(1, \"td\");\n    i0.ɵɵelement(2, \"p-dropdown\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"app-employee-select\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 57);\n    i0.ɵɵtemplate(6, AddProspectComponent_ng_template_117_button_6_Template, 1, 0, \"button\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const employee_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", employee_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.partnerfunction);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.employees.length > 1);\n  }\n}\nfunction AddProspectComponent_ng_template_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProspectComponent_ng_template_132_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.bp_full_name, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_132_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.email, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_132_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r10.phone, \"\");\n  }\n}\nfunction AddProspectComponent_ng_template_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddProspectComponent_ng_template_132_span_2_Template, 2, 1, \"span\", 49)(3, AddProspectComponent_ng_template_132_span_3_Template, 2, 1, \"span\", 49)(4, AddProspectComponent_ng_template_132_span_4_Template, 2, 1, \"span\", 49);\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.phone);\n  }\n}\nexport class AddProspectComponent {\n  constructor(formBuilder, prospectsservice, messageservice, router) {\n    this.formBuilder = formBuilder;\n    this.prospectsservice = prospectsservice;\n    this.messageservice = messageservice;\n    this.router = router;\n    this.ngUnsubscribe = new Subject();\n    this.ProspectForm = this.formBuilder.group({\n      bp_full_name: ['', [Validators.required]],\n      email_address: ['', [Validators.required, Validators.email]],\n      website_url: ['', [Validators.pattern(/^(https?:\\/\\/)?([\\w\\-]+\\.)+[\\w\\-]+(\\/[\\w\\-./?%&=]*)?$/)]],\n      owner: [''],\n      additional_street_prefix_name: [''],\n      additional_street_suffix_name: [''],\n      house_number: [''],\n      street_name: [''],\n      city_name: [''],\n      region: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      postal_code: [''],\n      fax_number: [''],\n      phone_number: [''],\n      contacts: this.formBuilder.array([this.createContactFormGroup()]),\n      employees: this.formBuilder.array([this.createEmployeeFormGroup()])\n    });\n    this.submitted = false;\n    this.saving = false;\n    this.existingMessage = '';\n    this.existingDialogVisible = false;\n    this.position = 'right';\n    this.partnerfunction = [];\n    this.partnerLoading = false;\n    this.countries = [];\n    this.states = [];\n    this.selectedCountry = '';\n    this.selectedState = '';\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.cpDepartments = [];\n  }\n  ngOnInit() {\n    this.loadContacts();\n    this.loadPartners();\n    this.loadDepartment();\n    this.loadCountries();\n  }\n  loadDepartment() {\n    this.prospectsservice.getCPDepartment().pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (response && response.data) {\n        this.cpDepartments = [{\n          name: 'Select Department',\n          value: null\n        }, ...response.data.map(item => ({\n          name: item.description,\n          value: item.code\n        }))];\n      }\n    });\n  }\n  loadCountries() {\n    const allCountries = Country.getAllCountries().map(country => ({\n      name: country.name,\n      isoCode: country.isoCode\n    })).filter(country => State.getStatesOfCountry(country.isoCode).length > 0);\n    const unitedStates = allCountries.find(c => c.isoCode === 'US');\n    const canada = allCountries.find(c => c.isoCode === 'CA');\n    const others = allCountries.filter(c => c.isoCode !== 'US' && c.isoCode !== 'CA').sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically\n    this.countries = [unitedStates, canada, ...others].filter(Boolean);\n  }\n  onCountryChange() {\n    this.states = State.getStatesOfCountry(this.selectedCountry).map(state => ({\n      name: state.name,\n      isoCode: state.isoCode\n    }));\n    this.selectedState = ''; // Reset state\n  }\n  loadPartners() {\n    this.partnerLoading = true;\n    this.prospectsservice.getPartnerfunction().pipe(finalize(() => this.partnerLoading = false)).subscribe({\n      next: data => {\n        // Replace `any` with the correct type if known\n        this.partnerfunction = data;\n      },\n      error: error => {\n        console.error('Error fetching partner data:', error);\n      }\n    });\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.prospectsservice.getContacts(params).pipe(tap(data => console.log('API Response:', data)),\n      // Debug API response\n      map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.contactLoading = false), catchError(error => {\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  onChangeDepartment(event, contact) {\n    contact.get('contact_person_department')?.patchValue(event.value.value);\n    contact.get('contact_person_department_name')?.patchValue(event.value.name);\n    console.log(this.ProspectForm.value);\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.ProspectForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.ProspectForm.value\n      };\n      const selectedcodewisecountry = _this.countries.find(c => c.isoCode === _this.selectedCountry);\n      const selectedState = _this.states.find(state => state.isoCode === value?.region);\n      const data = {\n        bp_full_name: value?.bp_full_name,\n        email_address: value?.email_address,\n        fax_number: value?.fax_number,\n        website_url: value?.website_url,\n        phone_number: value?.phone_number,\n        house_number: value?.house_number,\n        additional_street_prefix_name: value?.additional_street_prefix_name,\n        additional_street_suffix_name: value?.additional_street_suffix_name,\n        street_name: value?.street_name,\n        city_name: value?.city_name,\n        country: selectedcodewisecountry?.name,\n        county_code: selectedcodewisecountry?.isoCode,\n        postal_code: value?.postal_code,\n        region: selectedState?.name,\n        contacts: Array.isArray(value.contacts) ? value.contacts : [],\n        // Ensures contacts is an array\n        employees: value.employees\n      };\n      _this.prospectsservice.createProspect(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n        next: response => {\n          if (response?.data?.documentId) {\n            sessionStorage.setItem('prospectMessage', 'Prospect created successfully!');\n            window.location.href = `${window.location.origin}#/store/prospects/${response?.data?.bp_id}/overview`;\n          } else {\n            console.error('Missing documentId in response:', response);\n          }\n        },\n        error: res => {\n          _this.saving = false;\n          const msg = res?.error?.message || null;\n          if (msg) {\n            if (msg && msg.includes('unique constraint violated') && msg.includes(\"constraint='EMAIL'\")) {\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Given email address already in use.'\n              });\n            } else {\n              _this.messageservice.add({\n                severity: 'error',\n                detail: res?.error?.message\n              });\n            }\n          } else {\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        }\n      });\n    })();\n  }\n  addNewContact() {\n    this.contacts.push(this.createContactFormGroup());\n  }\n  addNewEmployee() {\n    this.employees.push(this.createEmployeeFormGroup());\n  }\n  createContactFormGroup() {\n    return this.formBuilder.group({\n      first_name: ['', Validators.required],\n      last_name: ['', Validators.required],\n      contact_person_department_name: ['', Validators.required],\n      contact_person_department: ['', Validators.required],\n      email_address: ['', Validators.required, Validators.email],\n      phone_number: ['', Validators.required]\n    });\n  }\n  createEmployeeFormGroup() {\n    return this.formBuilder.group({\n      partner_function: [null],\n      bp_customer_number: [null]\n    });\n  }\n  deleteContact(index) {\n    if (this.contacts.length > 1) {\n      this.contacts.removeAt(index);\n    }\n  }\n  deleteEmployee(index) {\n    if (this.employees.length > 1) {\n      this.employees.removeAt(index);\n    }\n  }\n  isFieldInvalid(index, field, arrayName) {\n    const control = this.ProspectForm.get(arrayName).at(index).get(field);\n    return control?.invalid && (control?.touched || this.submitted);\n  }\n  get f() {\n    return this.ProspectForm.controls;\n  }\n  get contacts() {\n    return this.ProspectForm.get('contacts');\n  }\n  get employees() {\n    return this.ProspectForm.get('employees');\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  onCancel() {\n    this.router.navigate(['/store/prospects']);\n  }\n  onReset() {\n    this.submitted = false;\n    this.ProspectForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function AddProspectComponent_Factory(t) {\n      return new (t || AddProspectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProspectsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProspectComponent,\n      selectors: [[\"app-add-prospect\"]],\n      decls: 138,\n      vars: 53,\n      consts: [[\"dt\", \"\"], [\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"bp_full_name\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"pInputText\", \"\", \"id\", \"email_address\", \"type\", \"text\", \"formControlName\", \"email_address\", \"placeholder\", \"Email Address\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"website_url\", \"type\", \"text\", \"formControlName\", \"website_url\", \"placeholder\", \"Website\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"house_number\", \"type\", \"text\", \"formControlName\", \"house_number\", \"placeholder\", \"House Number\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"street_name\", \"type\", \"text\", \"formControlName\", \"street_name\", \"placeholder\", \"Street\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"phone_number\", \"type\", \"text\", \"formControlName\", \"phone_number\", \"placeholder\", \"Mobile\", 1, \"h-3rem\", \"w-full\"], [\"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"formControlName\", \"country\", \"placeholder\", \"Select Country\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"filter\", \"styleClass\", \"ngClass\"], [\"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"formControlName\", \"region\", \"placeholder\", \"Select State\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"city_name\", \"type\", \"text\", \"formControlName\", \"city_name\", \"placeholder\", \"City\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"postal_code\", \"type\", \"text\", \"formControlName\", \"postal_code\", \"placeholder\", \"Zip Code\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"fax_number\", \"type\", \"text\", \"formControlName\", \"fax_number\", \"placeholder\", \"Fax Number\", 1, \"h-3rem\", \"w-full\"], [1, \"border-top-2\", \"border-gray-400\", \"my-5\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"flex\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 1, \"font-semibold\", \"mt-1\", 3, \"click\", \"outlined\"], [\"pButton\", \"\", \"type\", \"button\", \"pTooltip\", \"New Contact\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 1, \"p-button-rounded\", \"p-button-primary\", 3, \"click\"], [\"responsiveLayout\", \"scroll\", 1, \"prospect-add-table\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pButton\", \"\", \"type\", \"button\", \"pTooltip\", \"New Employee\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 1, \"p-button-rounded\", \"p-button-primary\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"CANCEL\", 1, \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"CREATE\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"text-left\", \"w-2\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"text-color\", \"font-medium\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"first_name\", \"placeholder\", \"Enter a First Name\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"last_name\", \"placeholder\", \"Enter a Last Name\", 1, \"h-3rem\", \"w-full\"], [\"optionLabel\", \"name\", \"appendTo\", \"body\", \"placeholder\", \"Select Department\", 3, \"onChange\", \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"phone_number\", \"placeholder\", \"Enter Phone\", 1, \"h-3rem\", \"w-full\"], [1, \"text-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"], [1, \"text-left\", \"w-4\"], [\"optionLabel\", \"label\", \"optionValue\", \"value\", \"appendTo\", \"body\", \"formControlName\", \"partner_function\", \"loading\", \"partnerLoading\", \"placeholder\", \"Select Partner Function\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"bp_customer_number\"]],\n      template: function AddProspectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelement(0, \"p-toast\", 1);\n          i0.ɵɵelementStart(1, \"form\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n          i0.ɵɵtext(4, \"Create Prospect\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"label\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Name \");\n          i0.ɵɵelementStart(12, \"span\", 10);\n          i0.ɵɵtext(13, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(14, \"input\", 11);\n          i0.ɵɵtemplate(15, AddProspectComponent_div_15_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 6)(17, \"div\", 7)(18, \"label\", 8)(19, \"span\", 13);\n          i0.ɵɵtext(20, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Email Address \");\n          i0.ɵɵelementStart(22, \"span\", 10);\n          i0.ɵɵtext(23, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(24, \"input\", 14);\n          i0.ɵɵtemplate(25, AddProspectComponent_div_25_Template, 3, 2, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 6)(27, \"div\", 7)(28, \"label\", 8)(29, \"span\", 13);\n          i0.ɵɵtext(30, \"globe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" Wesbite \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 15);\n          i0.ɵɵtemplate(33, AddProspectComponent_div_33_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 6)(35, \"div\", 7)(36, \"label\", 8)(37, \"span\", 13);\n          i0.ɵɵtext(38, \"pin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39, \" House Number \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 6)(42, \"div\", 7)(43, \"label\", 8)(44, \"span\", 13);\n          i0.ɵɵtext(45, \"near_me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Street \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"input\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 6)(49, \"div\", 7)(50, \"label\", 8)(51, \"span\", 13);\n          i0.ɵɵtext(52, \"phone_iphone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Mobile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"input\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 6)(56, \"div\", 7)(57, \"label\", 8)(58, \"span\", 13);\n          i0.ɵɵtext(59, \"map\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Country \");\n          i0.ɵɵelementStart(61, \"span\", 10);\n          i0.ɵɵtext(62, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"p-dropdown\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProspectComponent_Template_p_dropdown_ngModelChange_63_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCountry, $event) || (ctx.selectedCountry = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function AddProspectComponent_Template_p_dropdown_onChange_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCountryChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(64, AddProspectComponent_div_64_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 6)(66, \"div\", 7)(67, \"label\", 8)(68, \"span\", 13);\n          i0.ɵɵtext(69, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" State \");\n          i0.ɵɵelementStart(71, \"span\", 10);\n          i0.ɵɵtext(72, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"p-dropdown\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AddProspectComponent_Template_p_dropdown_ngModelChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedState, $event) || (ctx.selectedState = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(74, AddProspectComponent_div_74_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 6)(76, \"div\", 7)(77, \"label\", 8)(78, \"span\", 13);\n          i0.ɵɵtext(79, \"home_pin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" City \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 6)(83, \"div\", 7)(84, \"label\", 8)(85, \"span\", 13);\n          i0.ɵɵtext(86, \"code_blocks\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(87, \" Zip Code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(88, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 6)(90, \"div\", 7)(91, \"label\", 8)(92, \"span\", 13);\n          i0.ɵɵtext(93, \"fax\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(94, \" Fax Number \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(95, \"input\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(96, \"div\", 24);\n          i0.ɵɵelementStart(97, \"div\", 25)(98, \"div\", 26)(99, \"h3\", 4);\n          i0.ɵɵtext(100, \"Contacts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 27)(102, \"p-button\", 28);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_p_button_click_102_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_103_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addNewContact());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(104, \"p-table\", 30, 0);\n          i0.ɵɵtemplate(106, AddProspectComponent_ng_template_106_Template, 41, 0, \"ng-template\", 31)(107, AddProspectComponent_ng_template_107_Template, 18, 9, \"ng-template\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(108, \"div\", 24);\n          i0.ɵɵelementStart(109, \"div\", 25)(110, \"div\", 26)(111, \"h3\", 4);\n          i0.ɵɵtext(112, \"Employees\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_113_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addNewEmployee());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"p-table\", 30, 0);\n          i0.ɵɵtemplate(116, AddProspectComponent_ng_template_116_Template, 16, 0, \"ng-template\", 31)(117, AddProspectComponent_ng_template_117_Template, 7, 3, \"ng-template\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(118, \"div\", 34)(119, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_119_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_120_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(121, \"p-dialog\", 37);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AddProspectComponent_Template_p_dialog_visibleChange_121_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(122, AddProspectComponent_ng_template_122_Template, 2, 0, \"ng-template\", 31);\n          i0.ɵɵelementStart(123, \"form\", 38)(124, \"div\", 39)(125, \"label\", 40)(126, \"span\", 41);\n          i0.ɵɵtext(127, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(128, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"div\", 42)(130, \"ng-select\", 43);\n          i0.ɵɵpipe(131, \"async\");\n          i0.ɵɵtemplate(132, AddProspectComponent_ng_template_132_Template, 5, 4, \"ng-template\", 44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(133, \"div\", 45)(134, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_134_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n          });\n          i0.ɵɵtext(135, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"button\", 47);\n          i0.ɵɵlistener(\"click\", function AddProspectComponent_Template_button_click_136_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵtext(137, \" Save \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.ProspectForm);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(42, _c0, ctx.submitted && ctx.f[\"bp_full_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"bp_full_name\"].errors);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c0, ctx.submitted && ctx.f[\"email_address\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c0, ctx.submitted && ctx.f[\"website_url\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"website_url\"].errors);\n          i0.ɵɵadvance(30);\n          i0.ɵɵproperty(\"options\", ctx.countries);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCountry);\n          i0.ɵɵproperty(\"filter\", true)(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(48, _c0, ctx.submitted && ctx.f[\"country\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"country\"].errors);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.states);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedState);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedCountry)(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(50, _c0, ctx.submitted && ctx.f[\"region\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"region\"].errors);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"outlined\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.contacts == null ? null : ctx.contacts.controls)(\"paginator\", false)(\"rows\", 10);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.employees == null ? null : ctx.employees.controls)(\"paginator\", false)(\"rows\", 10);\n          i0.ɵɵadvance(7);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(52, _c1));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ProspectForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(131, 40, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i7.Table, i3.PrimeTemplate, i1.FormGroupDirective, i1.FormControlName, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Tooltip, i11.Toast, i12.InputText, i13.Dialog, i14.EmployeeSelectComponent, i5.AsyncPipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n  .prospect-add-table tbody td {\\n  vertical-align: top;\\n  padding: 8px 6px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL2FkZC1wcm9zcGVjdC9hZGQtcHJvc3BlY3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7RUFJRSxjQUFBO0FBQ0Y7O0FBSUU7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0FBREoiLCJzb3VyY2VzQ29udGVudCI6WyIuaW52YWxpZC1mZWVkYmFjayxcclxuLnAtaW5wdXR0ZXh0OmludmFsaWQsXHJcbi5pcy1jaGVja2JveC1pbnZhbGlkLFxyXG4ucC1pbnB1dHRleHQuaXMtaW52YWxpZCB7XHJcbiAgY29sb3I6ICNkYzM1NDU7XHJcbn1cclxuXHJcbjo6bmctZGVlcCB7XHJcblxyXG4gIC5wcm9zcGVjdC1hZGQtdGFibGUgdGJvZHkgdGQge1xyXG4gICAgdmVydGljYWwtYWxpZ246IHRvcDtcclxuICAgIHBhZGRpbmc6IDhweCA2cHg7XHJcbiAgfVxyXG5cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "Country", "State", "finalize", "distinctUntilChanged", "switchMap", "tap", "catchError", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddProspectComponent_div_15_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "submitted", "f", "errors", "AddProspectComponent_div_25_div_1_Template", "AddProspectComponent_div_25_div_2_Template", "AddProspectComponent_div_33_div_1_Template", "AddProspectComponent_div_64_div_1_Template", "AddProspectComponent_div_74_div_1_Template", "ɵɵlistener", "AddProspectComponent_ng_template_107_button_17_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "i_r6", "ɵɵnextContext", "rowIndex", "ɵɵresetView", "deleteContact", "ɵɵelement", "AddProspectComponent_ng_template_107_small_3_Template", "AddProspectComponent_ng_template_107_small_6_Template", "AddProspectComponent_ng_template_107_Template_p_dropdown_onChange_8_listener", "$event", "contact_r4", "_r3", "$implicit", "onChangeDepartment", "AddProspectComponent_ng_template_107_small_9_Template", "AddProspectComponent_ng_template_107_small_12_Template", "AddProspectComponent_ng_template_107_small_15_Template", "AddProspectComponent_ng_template_107_button_17_Template", "isFieldInvalid", "cpDepartments", "contacts", "length", "AddProspectComponent_ng_template_117_button_6_Template_button_click_0_listener", "_r7", "i_r8", "deleteEmployee", "AddProspectComponent_ng_template_117_button_6_Template", "employee_r9", "partnerfunction", "employees", "ɵɵtextInterpolate1", "item_r10", "bp_full_name", "email", "phone", "AddProspectComponent_ng_template_132_span_2_Template", "AddProspectComponent_ng_template_132_span_3_Template", "AddProspectComponent_ng_template_132_span_4_Template", "ɵɵtextInterpolate", "bp_id", "AddProspectComponent", "constructor", "formBuilder", "prospectsservice", "messageservice", "router", "ngUnsubscribe", "ProspectForm", "group", "required", "email_address", "website_url", "pattern", "owner", "additional_street_prefix_name", "additional_street_suffix_name", "house_number", "street_name", "city_name", "region", "country", "postal_code", "fax_number", "phone_number", "array", "createContactFormGroup", "createEmployeeFormGroup", "saving", "existingMessage", "existingDialogVisible", "position", "partner<PERSON><PERSON><PERSON>", "countries", "states", "selectedCountry", "selectedState", "contactLoading", "contactInput$", "defaultOptions", "ngOnInit", "loadContacts", "loadPartners", "loadDepartment", "loadCountries", "getCPDepartment", "pipe", "subscribe", "response", "data", "name", "value", "item", "description", "code", "allCountries", "getAllCountries", "isoCode", "filter", "getStatesOfCountry", "unitedStates", "find", "c", "canada", "others", "sort", "a", "b", "localeCompare", "Boolean", "onCountryChange", "state", "getPartnerfunction", "next", "error", "console", "contacts$", "term", "params", "getContacts", "log", "event", "contact", "get", "patchValue", "onSubmit", "_this", "_asyncToGenerator", "invalid", "selectedcodewisecountry", "county_code", "Array", "isArray", "createProspect", "documentId", "sessionStorage", "setItem", "window", "location", "href", "origin", "res", "msg", "message", "includes", "add", "severity", "detail", "addNewContact", "push", "addNewEmployee", "first_name", "last_name", "contact_person_department_name", "contact_person_department", "partner_function", "bp_customer_number", "index", "removeAt", "field", "arrayName", "control", "at", "touched", "controls", "showExistingDialog", "onCancel", "navigate", "onReset", "reset", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProspectsService", "i3", "MessageService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "AddProspectComponent_Template", "rf", "ctx", "AddProspectComponent_div_15_Template", "AddProspectComponent_div_25_Template", "AddProspectComponent_div_33_Template", "ɵɵtwoWayListener", "AddProspectComponent_Template_p_dropdown_ngModelChange_63_listener", "_r1", "ɵɵtwoWayBindingSet", "AddProspectComponent_Template_p_dropdown_onChange_63_listener", "AddProspectComponent_div_64_Template", "AddProspectComponent_Template_p_dropdown_ngModelChange_73_listener", "AddProspectComponent_div_74_Template", "AddProspectComponent_Template_p_button_click_102_listener", "AddProspectComponent_Template_button_click_103_listener", "AddProspectComponent_ng_template_106_Template", "AddProspectComponent_ng_template_107_Template", "AddProspectComponent_Template_button_click_113_listener", "AddProspectComponent_ng_template_116_Template", "AddProspectComponent_ng_template_117_Template", "AddProspectComponent_Template_button_click_119_listener", "AddProspectComponent_Template_button_click_120_listener", "AddProspectComponent_Template_p_dialog_visibleChange_121_listener", "AddProspectComponent_ng_template_122_Template", "AddProspectComponent_ng_template_132_Template", "AddProspectComponent_Template_button_click_134_listener", "AddProspectComponent_Template_button_click_136_listener", "ɵɵpureFunction1", "_c0", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\add-prospect\\add-prospect.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\add-prospect\\add-prospect.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ProspectsService } from '../prospects.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\nimport { Country, State } from 'country-state-city';\r\nimport { finalize } from 'rxjs/operators';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n} from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-add-prospect',\r\n  templateUrl: './add-prospect.component.html',\r\n  styleUrl: './add-prospect.component.scss',\r\n})\r\nexport class AddProspectComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public ProspectForm: FormGroup = this.formBuilder.group({\r\n    bp_full_name: ['', [Validators.required]],\r\n    email_address: ['', [Validators.required,Validators.email]],\r\n    website_url: [\r\n      '',\r\n      [\r\n        Validators.pattern(\r\n          /^(https?:\\/\\/)?([\\w\\-]+\\.)+[\\w\\-]+(\\/[\\w\\-./?%&=]*)?$/\r\n        ),\r\n      ],\r\n    ],\r\n    owner: [''],\r\n    additional_street_prefix_name: [''],\r\n    additional_street_suffix_name: [''],\r\n    house_number: [''],\r\n    street_name: [''],\r\n    city_name: [''],\r\n    region: ['', [Validators.required]],\r\n    country: ['', [Validators.required]],\r\n    postal_code: [''],\r\n    fax_number: [''],\r\n    phone_number: [''],\r\n    contacts: this.formBuilder.array([this.createContactFormGroup()]),\r\n    employees: this.formBuilder.array([this.createEmployeeFormGroup()]),\r\n  });\r\n\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingMessage: string = '';\r\n  public existingDialogVisible: boolean = false;\r\n  public position: string = 'right';\r\n  public partnerfunction: { label: string; value: string }[] = [];\r\n  public partnerLoading = false;\r\n  public countries: any[] = [];\r\n  public states: any[] = [];\r\n  public selectedCountry: string = '';\r\n  public selectedState: string = '';\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public cpDepartments: { name: string; value: string }[] = [];\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private prospectsservice: ProspectsService,\r\n    private messageservice: MessageService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadContacts();\r\n    this.loadPartners();\r\n    this.loadDepartment();\r\n    this.loadCountries();\r\n  }\r\n\r\n  public loadDepartment(): void {\r\n    this.prospectsservice\r\n      .getCPDepartment()\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (response && response.data) {\r\n          this.cpDepartments = [\r\n            { name: 'Select Department', value: null },\r\n            ...response.data.map((item: any) => ({\r\n              name: item.description,\r\n              value: item.code,\r\n            })),\r\n          ];\r\n        }\r\n      });\r\n  }\r\n\r\n  loadCountries() {\r\n    const allCountries = Country.getAllCountries()\r\n      .map((country: any) => ({\r\n        name: country.name,\r\n        isoCode: country.isoCode,\r\n      }))\r\n      .filter(\r\n        (country) => State.getStatesOfCountry(country.isoCode).length > 0\r\n      );\r\n\r\n    const unitedStates = allCountries.find((c) => c.isoCode === 'US');\r\n    const canada = allCountries.find((c) => c.isoCode === 'CA');\r\n    const others = allCountries\r\n      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')\r\n      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically\r\n\r\n    this.countries = [unitedStates, canada, ...others].filter(Boolean);\r\n  }\r\n\r\n  onCountryChange() {\r\n    this.states = State.getStatesOfCountry(this.selectedCountry).map(\r\n      (state) => ({\r\n        name: state.name,\r\n        isoCode: state.isoCode,\r\n      })\r\n    );\r\n    this.selectedState = ''; // Reset state\r\n  }\r\n\r\n  private loadPartners(): void {\r\n    this.partnerLoading = true;\r\n    this.prospectsservice\r\n      .getPartnerfunction()\r\n      .pipe(finalize(() => (this.partnerLoading = false)))\r\n      .subscribe({\r\n        next: (data: any) => {\r\n          // Replace `any` with the correct type if known\r\n          this.partnerfunction = data;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching partner data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private loadContacts() {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.contactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.prospectsservice.getContacts(params).pipe(\r\n            tap((data) => console.log('API Response:', data)), // Debug API response\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  onChangeDepartment(event: any, contact: FormGroup) {\r\n    contact.get('contact_person_department')?.patchValue(event.value.value);\r\n    contact.get('contact_person_department_name')?.patchValue(event.value.name);\r\n    console.log(this.ProspectForm.value);\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.ProspectForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.ProspectForm.value };\r\n\r\n    const selectedcodewisecountry = this.countries.find(\r\n      (c) => c.isoCode === this.selectedCountry\r\n    );\r\n\r\n    const selectedState = this.states.find(\r\n      (state) => state.isoCode === value?.region\r\n    );\r\n\r\n    const data = {\r\n      bp_full_name: value?.bp_full_name,\r\n      email_address: value?.email_address,\r\n      fax_number: value?.fax_number,\r\n      website_url: value?.website_url,\r\n      phone_number: value?.phone_number,\r\n      house_number: value?.house_number,\r\n      additional_street_prefix_name: value?.additional_street_prefix_name,\r\n      additional_street_suffix_name: value?.additional_street_suffix_name,\r\n      street_name: value?.street_name,\r\n      city_name: value?.city_name,\r\n      country: selectedcodewisecountry?.name,\r\n      county_code: selectedcodewisecountry?.isoCode,\r\n      postal_code: value?.postal_code,\r\n      region: selectedState?.name,\r\n      contacts: Array.isArray(value.contacts) ? value.contacts : [], // Ensures contacts is an array\r\n      employees: value.employees,\r\n    };\r\n\r\n    this.prospectsservice\r\n      .createProspect(data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data?.documentId) {\r\n            sessionStorage.setItem(\r\n              'prospectMessage',\r\n              'Prospect created successfully!'\r\n            );\r\n            window.location.href = `${window.location.origin}#/store/prospects/${response?.data?.bp_id}/overview`;\r\n          } else {\r\n            console.error('Missing documentId in response:', response);\r\n          }\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          const msg: any = res?.error?.message || null;\r\n          if (msg) {\r\n            if (\r\n              msg &&\r\n              msg.includes('unique constraint violated') &&\r\n              msg.includes(\"constraint='EMAIL'\")\r\n            ) {\r\n              this.messageservice.add({\r\n                severity: 'error',\r\n                detail: 'Given email address already in use.',\r\n              });\r\n            } else {\r\n              this.messageservice.add({\r\n                severity: 'error',\r\n                detail: res?.error?.message,\r\n              });\r\n            }\r\n          } else {\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  addNewContact() {\r\n    this.contacts.push(this.createContactFormGroup());\r\n  }\r\n\r\n  addNewEmployee() {\r\n    this.employees.push(this.createEmployeeFormGroup());\r\n  }\r\n\r\n  createContactFormGroup(): FormGroup {\r\n    return this.formBuilder.group({\r\n      first_name: ['', Validators.required],\r\n      last_name: ['', Validators.required],\r\n      contact_person_department_name: ['', Validators.required],\r\n      contact_person_department: ['', Validators.required],\r\n      email_address: ['', Validators.required, Validators.email],\r\n      phone_number: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  createEmployeeFormGroup(): FormGroup {\r\n    return this.formBuilder.group({\r\n      partner_function: [null],\r\n      bp_customer_number: [null],\r\n    });\r\n  }\r\n\r\n  deleteContact(index: number) {\r\n    if (this.contacts.length > 1) {\r\n      this.contacts.removeAt(index);\r\n    }\r\n  }\r\n\r\n  deleteEmployee(index: number) {\r\n    if (this.employees.length > 1) {\r\n      this.employees.removeAt(index);\r\n    }\r\n  }\r\n\r\n  isFieldInvalid(index: number, field: string, arrayName: string) {\r\n    const control = (this.ProspectForm.get(arrayName) as FormArray)\r\n      .at(index)\r\n      .get(field);\r\n    return control?.invalid && (control?.touched || this.submitted);\r\n  }\r\n\r\n  get f(): any {\r\n    return this.ProspectForm.controls;\r\n  }\r\n\r\n  get contacts(): any {\r\n    return this.ProspectForm.get('contacts') as FormArray;\r\n  }\r\n\r\n  get employees(): any {\r\n    return this.ProspectForm.get('employees') as FormArray;\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  onCancel() {\r\n    this.router.navigate(['/store/prospects']);\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.ProspectForm.reset();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<form [formGroup]=\"ProspectForm\">\r\n    <div class=\"card shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50\">\r\n        <h3 class=\"mb-2 flex align-items-center h-3rem\">Create Prospect</h3>\r\n        <div class=\"p-fluid p-formgrid grid mt-0\">\r\n            <!-- <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">sticky_note_2</span> Prospect ID\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"prospect_id\" type=\"text\" formControlName=\"prospect_id\"\r\n                        placeholder=\"Prospect ID\" [ngClass]=\"{ 'is-invalid': submitted && f['prospect_id'].errors }\"\r\n                        class=\"h-3rem w-full\" />\r\n                    <div *ngIf=\"submitted && f['prospect_id'].errors\" class=\"invalid-feedback\">\r\n                        <div *ngIf=\" submitted && f['prospect_id'].errors && f['prospect_id'].errors['required'] \">\r\n                            Prospect ID is required.\r\n                        </div>\r\n                    </div>\r\n                    <small class=\"invalid-feedback\" *ngIf=\"existingMessage\">{{ existingMessage }}</small>\r\n                </div>\r\n            </div> -->\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-300\">person</span>\r\n                        Name\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"bp_full_name\" type=\"text\" formControlName=\"bp_full_name\" placeholder=\"Name\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['bp_full_name'].errors }\" class=\"h-3rem w-full\" />\r\n                    <div *ngIf=\"submitted && f['bp_full_name'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                submitted &&\r\n                f['bp_full_name'].errors &&\r\n                f['bp_full_name'].errors['required']\r\n              \">\r\n                            Name is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">mail</span>\r\n                        Email Address <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"email_address\" type=\"text\" formControlName=\"email_address\"\r\n                        placeholder=\"Email Address\" [ngClass]=\"{ 'is-invalid': submitted && f['email_address'].errors }\"\r\n                        class=\"h-3rem w-full\" />\r\n                    <div *ngIf=\"submitted && f['email_address'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"f['email_address'].errors['required']\">\r\n                            Email is required.\r\n                        </div>\r\n                        <div *ngIf=\"f['email_address'].errors['email']\">\r\n                            Email is invalid.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">globe</span>\r\n                        Wesbite\r\n                    </label>\r\n                    <input pInputText id=\"website_url\" type=\"text\" formControlName=\"website_url\" placeholder=\"Website\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['website_url'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['website_url'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"f['website_url'].errors['pattern']\">\r\n                            Please enter a valid website URL.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <!-- <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">supervisor_account</span>\r\n                        Owner\r\n                    </label>\r\n                    <input pInputText id=\"owner\" type=\"text\" formControlName=\"owner\" placeholder=\"Owner\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">pin_drop</span>\r\n                        Address Line\r\n                    </label>\r\n                    <input pInputText id=\"additional_street_prefix_name\" type=\"text\"\r\n                        formControlName=\"additional_street_prefix_name\" placeholder=\"Address Line 1\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">pin_drop</span>\r\n                        Address Line 2\r\n                    </label>\r\n                    <input pInputText id=\"additional_street_suffix_name\" type=\"text\"\r\n                        formControlName=\"additional_street_suffix_name\" placeholder=\"Address Line 2\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div> -->\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">pin</span>\r\n                        House Number\r\n                    </label>\r\n                    <input pInputText id=\"house_number\" type=\"text\" formControlName=\"house_number\"\r\n                        placeholder=\"House Number\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">near_me</span>\r\n                        Street\r\n                    </label>\r\n                    <input pInputText id=\"street_name\" type=\"text\" formControlName=\"street_name\" placeholder=\"Street\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">phone_iphone</span>\r\n                        Mobile\r\n                    </label>\r\n                    <input pInputText id=\"phone_number\" type=\"text\" formControlName=\"phone_number\" placeholder=\"Mobile\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">map</span>\r\n                        Country <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"countries\" optionLabel=\"name\" optionValue=\"isoCode\"\r\n                        [(ngModel)]=\"selectedCountry\" (onChange)=\"onCountryChange()\" [filter]=\"true\"\r\n                        formControlName=\"country\" [styleClass]=\"'h-3rem w-full'\" placeholder=\"Select Country\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['country'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['country'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                submitted &&\r\n                f['country'].errors &&\r\n                f['country'].errors['required']\r\n              \">\r\n                            Country is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">location_on</span>\r\n                        State <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"states\" optionLabel=\"name\" optionValue=\"isoCode\" [(ngModel)]=\"selectedState\"\r\n                        formControlName=\"region\" placeholder=\"Select State\" [disabled]=\"!selectedCountry\"\r\n                        [styleClass]=\"'h-3rem w-full'\" [ngClass]=\"{ 'is-invalid': submitted && f['region'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['region'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                submitted &&\r\n                f['region'].errors &&\r\n                f['region'].errors['required']\r\n              \">\r\n                            State is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">home_pin</span>\r\n                        City\r\n                    </label>\r\n                    <input pInputText id=\"city_name\" type=\"text\" formControlName=\"city_name\" placeholder=\"City\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">code_blocks</span>\r\n                        Zip Code\r\n                    </label>\r\n                    <input pInputText id=\"postal_code\" type=\"text\" formControlName=\"postal_code\" placeholder=\"Zip Code\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">fax</span>\r\n                        Fax Number\r\n                    </label>\r\n                    <input pInputText id=\"fax_number\" type=\"text\" formControlName=\"fax_number\" placeholder=\"Fax Number\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"border-top-2 border-gray-400 my-5\"></div>\r\n\r\n    <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n        <div class=\"flex justify-content-between align-items-center mb-3\">\r\n            <h3 class=\"mb-2 flex align-items-center h-3rem\">Contacts</h3>\r\n\r\n            <div class=\"flex gap-3\">\r\n\r\n                <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\"\r\n                    iconPos=\"right\" [outlined]=\"true\" class=\"font-semibold mt-1\"></p-button>\r\n                <button pButton type=\"button\" pTooltip=\"New Contact\" tooltipPosition=\"top\" icon=\"pi pi-plus\"\r\n                    class=\"p-button-rounded p-button-primary\" (click)=\"addNewContact()\"></button>\r\n\r\n            </div>\r\n        </div>\r\n\r\n\r\n        <p-table #dt [value]=\"contacts?.controls\" [paginator]=\"false\" [rows]=\"10\" responsiveLayout=\"scroll\"\r\n            class=\"prospect-add-table\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">person</span>\r\n                            First Name<span class=\"text-red-500\">*</span>\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">person</span>\r\n                            Last Name<span class=\"text-red-500\">*</span>\r\n                        </span>\r\n                    </th>\r\n\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">inbox_text_person</span>\r\n                            Department<span class=\"text-red-500\">*</span>\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">mail</span>\r\n                            Email Address<span class=\"text-red-500\">*</span>\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">phone_iphone</span>\r\n                            Phone<span class=\"text-red-500\">*</span>\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">delete</span>\r\n                            Action\r\n                        </span>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact let-i=\"rowIndex\">\r\n                <tr [formGroup]=\"contact\">\r\n                    <!-- First Name -->\r\n                    <td>\r\n                        <input pInputText type=\"text\" class=\"h-3rem w-full\" formControlName=\"first_name\"\r\n                            placeholder=\"Enter a First Name\" />\r\n                        <small class=\"p-error\" *ngIf=\"isFieldInvalid(i, 'first_name', 'contacts')\">This is\r\n                            Required.</small>\r\n                    </td>\r\n                    <!-- Last Name -->\r\n                    <td>\r\n                        <input pInputText type=\"text\" class=\"h-3rem w-full\" formControlName=\"last_name\"\r\n                            placeholder=\"Enter a Last Name\" />\r\n                        <small class=\"p-error\" *ngIf=\"isFieldInvalid(i, 'last_name', 'contacts')\">This is\r\n                            Required.</small>\r\n                    </td>\r\n\r\n                    <!-- Department -->\r\n                    <td>\r\n                        <p-dropdown [options]=\"cpDepartments\" optionLabel=\"name\" appendTo=\"body\"\r\n                            placeholder=\"Select Department\" [styleClass]=\"'h-3rem w-full'\"\r\n                            (onChange)=\"onChangeDepartment($event, contact)\"></p-dropdown>\r\n                        <small class=\"p-error\" *ngIf=\"\r\n                isFieldInvalid(i, 'contact_person_department', 'contacts') ||\r\n                isFieldInvalid(i, 'contact_person_department_name', 'contacts')\r\n              \">\r\n                            Select a valid Department.</small>\r\n                    </td>\r\n                    <!-- Email -->\r\n                    <td>\r\n                        <input pInputText type=\"email\" class=\"h-3rem w-full\" formControlName=\"email_address\"\r\n                            placeholder=\"Enter Email\" />\r\n                        <small class=\"p-error\" *ngIf=\"isFieldInvalid(i, 'email_address', 'contacts')\">Enter a valid\r\n                            email.</small>\r\n                    </td>\r\n                    <!-- Phone Number -->\r\n                    <td>\r\n                        <input pInputText type=\"text\" class=\"h-3rem w-full\" formControlName=\"phone_number\"\r\n                            placeholder=\"Enter Phone\" />\r\n                        <small class=\"p-error\" *ngIf=\"isFieldInvalid(i, 'phone_number', 'contacts')\">Enter a valid\r\n                            phone.</small>\r\n                    </td>\r\n                    <!-- Delete Button -->\r\n                    <td class=\"text-center\">\r\n                        <button pButton pRipple type=\"button\" icon=\"pi pi-trash\"\r\n                            class=\"p-button-rounded p-button-danger\" (click)=\"deleteContact(i)\" title=\"Delete\"\r\n                            *ngIf=\"contacts.length > 1\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n\r\n    <div class=\"border-top-2 border-gray-400 my-5\"></div>\r\n\r\n    <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n        <div class=\"flex justify-content-between align-items-center mb-3\">\r\n            <h3 class=\"mb-2 flex align-items-center h-3rem\">Employees</h3>\r\n            <button pButton type=\"button\" pTooltip=\"New Employee\" tooltipPosition=\"top\" icon=\"pi pi-plus\"\r\n                class=\"p-button-rounded p-button-primary\" (click)=\"addNewEmployee()\"></button>\r\n        </div>\r\n\r\n        <p-table #dt [value]=\"employees?.controls\" [paginator]=\"false\" [rows]=\"10\" responsiveLayout=\"scroll\"\r\n            class=\"prospect-add-table\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"text-left w-4\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">supervisor_account</span>\r\n                            Role\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-4\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">badge</span>\r\n                            Employee\r\n                        </span>\r\n                    </th>\r\n                    <th class=\"text-left w-2\">\r\n                        <span class=\"flex align-items-center gap-1 text-color font-medium\">\r\n                            <span class=\"material-symbols-rounded text-2xl text-600\">delete</span>\r\n                            Action\r\n                        </span>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-employee let-i=\"rowIndex\">\r\n                <tr [formGroup]=\"employee\">\r\n                    <td>\r\n                        <p-dropdown [options]=\"partnerfunction\" optionLabel=\"label\" optionValue=\"value\" appendTo=\"body\"\r\n                            formControlName=\"partner_function\" loading=\"partnerLoading\"\r\n                            placeholder=\"Select Partner Function\" styleClass=\"h-3rem w-full\">\r\n                        </p-dropdown>\r\n                    </td>\r\n\r\n                    <td>\r\n                        <app-employee-select formControlName=\"bp_customer_number\"></app-employee-select>\r\n                    </td>\r\n\r\n                    <!-- Delete Button -->\r\n                    <td class=\"text-center\">\r\n                        <button pButton pRipple type=\"button\" icon=\"pi pi-trash\"\r\n                            class=\"p-button-rounded p-button-danger\" (click)=\"deleteEmployee(i)\" title=\"Delete\"\r\n                            *ngIf=\"employees.length > 1\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n    <div class=\"flex align-items-center gap-3 mt-4\">\r\n        <button pButton type=\"button\" label=\"CANCEL\"\r\n            class=\"p-button-rounded p-button-outlined justify-content-center w-9rem h-3rem\"\r\n            (click)=\"onCancel()\"></button>\r\n        <button pButton type=\"submit\" label=\"CREATE\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n            (click)=\"onSubmit()\"></button>\r\n    </div>\r\n</form>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n[draggable]=\"false\" class=\"prospect-popup\">\r\n<ng-template pTemplate=\"header\">\r\n    <h4>Contact Information</h4>\r\n</ng-template>\r\n\r\n<form [formGroup]=\"ProspectForm\" class=\"relative flex flex-column gap-1\">\r\n    <div class=\"field flex align-items-center text-base\">\r\n        <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n            <span class=\"material-symbols-rounded\">person</span>Contacts\r\n        </label>\r\n        <div class=\"form-input flex-1 relative\">\r\n            <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_full_name\"\r\n                [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                formControlName=\"contactexisting\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                appendTo=\"body\">\r\n                <ng-template ng-option-tmp let-item=\"item\">\r\n                    <span>{{ item.bp_id }}</span>\r\n                    <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                    <span *ngIf=\"item.phone\"> : {{ item.phone }}</span>\r\n                </ng-template>\r\n            </ng-select>\r\n        </div>\r\n    </div>\r\n    <div class=\"flex justify-content-end gap-2 mt-3\">\r\n        <button pButton type=\"button\"\r\n            class=\"p-button-rounded p-button-outlined justify-content-center w-9rem h-3rem\"\r\n            (click)=\"existingDialogVisible = false\">\r\n            Cancel\r\n        </button>\r\n        <button pButton type=\"submit\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n            (click)=\"onSubmit()\">\r\n            Save\r\n        </button>\r\n    </div>\r\n</form>\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAmB,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAItE,SAASC,OAAO,EAAEC,KAAK,QAAQ,oBAAoB;AACnD,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,QACL,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;ICmBCC,EAAA,CAAAC,cAAA,UAIR;IACYD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAI,UAAA,IAAAC,0CAAA,kBAIR;IAGIL,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIjB;IAJiBN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,iBAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,iBAAAC,MAAA,aAIjB;;;;;IAgBWX,EAAA,CAAAC,cAAA,UAAmD;IAC/CD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IANVH,EAAA,CAAAC,cAAA,cAAoE;IAIhED,EAHA,CAAAI,UAAA,IAAAQ,0CAAA,kBAAmD,IAAAC,0CAAA,kBAGH;IAGpDb,EAAA,CAAAG,YAAA,EAAM;;;;IANIH,EAAA,CAAAM,SAAA,EAA2C;IAA3CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAE,CAAA,kBAAAC,MAAA,aAA2C;IAG3CX,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAE,CAAA,kBAAAC,MAAA,UAAwC;;;;;IAe9CX,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAE,MAAA,0CACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAkE;IAC9DD,EAAA,CAAAI,UAAA,IAAAU,0CAAA,kBAAgD;IAGpDd,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAE,CAAA,gBAAAC,MAAA,YAAwC;;;;;IAiF9CX,EAAA,CAAAC,cAAA,UAIR;IACYD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAI,UAAA,IAAAW,0CAAA,kBAIR;IAGIf,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIjB;IAJiBN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,YAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,YAAAC,MAAA,aAIjB;;;;;IAiBWX,EAAA,CAAAC,cAAA,UAIR;IACYD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA6D;IACzDD,EAAA,CAAAI,UAAA,IAAAY,0CAAA,kBAIR;IAGIhB,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIjB;IAJiBN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,WAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,WAAAC,MAAA,aAIjB;;;;;IA8DeX,EAHZ,CAAAC,cAAA,SAAI,aAC0B,eAC6C,eACN;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC1C,EACN;IAGGH,EAFR,CAAAC,cAAA,aAA0B,eAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAE7CF,EAF6C,CAAAG,YAAA,EAAO,EACzC,EACN;IAIGH,EAFR,CAAAC,cAAA,cAA0B,gBAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAE,MAAA,mBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC1C,EACN;IAGGH,EAFR,CAAAC,cAAA,cAA0B,gBAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAEjDF,EAFiD,CAAAG,YAAA,EAAO,EAC7C,EACN;IAGGH,EAFR,CAAAC,cAAA,cAA0B,gBAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5EH,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAEzCF,EAFyC,CAAAG,YAAA,EAAO,EACrC,EACN;IAGGH,EAFR,CAAAC,cAAA,cAA0B,gBAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAE,MAAA,gBACJ;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACN,EACJ;;;;;IASGH,EAAA,CAAAC,cAAA,gBAA2E;IAAAD,EAAA,CAAAE,MAAA,wBAC9D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMrBH,EAAA,CAAAC,cAAA,gBAA0E;IAAAD,EAAA,CAAAE,MAAA,wBAC7D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAQrBH,EAAA,CAAAC,cAAA,gBAGR;IACYD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMtCH,EAAA,CAAAC,cAAA,gBAA8E;IAAAD,EAAA,CAAAE,MAAA,2BACpE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMlBH,EAAA,CAAAC,cAAA,gBAA6E;IAAAD,EAAA,CAAAE,MAAA,2BACnE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAIlBH,EAAA,CAAAC,cAAA,iBAEgC;IADaD,EAAA,CAAAiB,UAAA,mBAAAC,gFAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAsB,aAAA,GAAAC,QAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAShB,MAAA,CAAAiB,aAAA,CAAAJ,IAAA,CAAgB;IAAA,EAAC;IACvCrB,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3C7CH,EAFJ,CAAAC,cAAA,YAA0B,SAElB;IACAD,EAAA,CAAA0B,SAAA,gBACuC;IACvC1B,EAAA,CAAAI,UAAA,IAAAuB,qDAAA,oBAA2E;IAE/E3B,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,SAAA,gBACsC;IACtC1B,EAAA,CAAAI,UAAA,IAAAwB,qDAAA,oBAA0E;IAE9E5B,EAAA,CAAAG,YAAA,EAAK;IAIDH,EADJ,CAAAC,cAAA,SAAI,qBAGqD;IAAjDD,EAAA,CAAAiB,UAAA,sBAAAY,6EAAAC,MAAA;MAAA,MAAAC,UAAA,GAAA/B,EAAA,CAAAmB,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAR,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAYhB,MAAA,CAAA0B,kBAAA,CAAAJ,MAAA,EAAAC,UAAA,CAAmC;IAAA,EAAC;IAAC/B,EAAA,CAAAG,YAAA,EAAa;IAClEH,EAAA,CAAAI,UAAA,IAAA+B,qDAAA,oBAGR;IAEInC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAA0B,SAAA,iBACgC;IAChC1B,EAAA,CAAAI,UAAA,KAAAgC,sDAAA,oBAA8E;IAElFpC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAA0B,SAAA,iBACgC;IAChC1B,EAAA,CAAAI,UAAA,KAAAiC,sDAAA,oBAA6E;IAEjFrC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAI,UAAA,KAAAkC,uDAAA,qBAEgC;IAExCtC,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;;IA/CDH,EAAA,CAAAO,UAAA,cAAAwB,UAAA,CAAqB;IAKO/B,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,4BAAiD;IAOjDrB,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,2BAAgD;IAM5DrB,EAAA,CAAAM,SAAA,GAAyB;IACDN,EADxB,CAAAO,UAAA,YAAAC,MAAA,CAAAgC,aAAA,CAAyB,+BAC6B;IAE1CxC,EAAA,CAAAM,SAAA,EAGlC;IAHkCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,8CAAAb,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,gDAGlC;IAOkCrB,EAAA,CAAAM,SAAA,GAAoD;IAApDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,+BAAoD;IAOpDrB,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA+B,cAAA,CAAAlB,IAAA,8BAAmD;IAOtErB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAiC,QAAA,CAAAC,MAAA,KAAyB;;;;;IAsB1B1C,EAHZ,CAAAC,cAAA,SAAI,aAC0B,eAC6C,eACN;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClFH,EAAA,CAAAE,MAAA,aACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACN;IAGGH,EAFR,CAAAC,cAAA,aAA0B,eAC6C,eACN;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAE,MAAA,kBACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACN;IAGGH,EAFR,CAAAC,cAAA,cAA0B,gBAC6C,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAE,MAAA,gBACJ;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACN,EACJ;;;;;;IAkBGH,EAAA,CAAAC,cAAA,iBAEiC;IADYD,EAAA,CAAAiB,UAAA,mBAAA0B,+EAAA;MAAA3C,EAAA,CAAAmB,aAAA,CAAAyB,GAAA;MAAA,MAAAC,IAAA,GAAA7C,EAAA,CAAAsB,aAAA,GAAAC,QAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAShB,MAAA,CAAAsC,cAAA,CAAAD,IAAA,CAAiB;IAAA,EAAC;IACvC7C,EAAA,CAAAG,YAAA,EAAS;;;;;IAf9CH,EADJ,CAAAC,cAAA,YAA2B,SACnB;IACAD,EAAA,CAAA0B,SAAA,qBAGa;IACjB1B,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,SAAA,8BAAgF;IACpF1B,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,aAAwB;IACpBD,EAAA,CAAAI,UAAA,IAAA2C,sDAAA,qBAEiC;IAEzC/C,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;IAlBDH,EAAA,CAAAO,UAAA,cAAAyC,WAAA,CAAsB;IAENhD,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAAyC,eAAA,CAA2B;IAclCjD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA0C,SAAA,CAAAR,MAAA,KAA0B;;;;;IAiBnD1C,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAeZH,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAmD,kBAAA,QAAAC,QAAA,CAAAC,YAAA,KAAyB;;;;;IAC1DrD,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAmD,kBAAA,QAAAC,QAAA,CAAAE,KAAA,KAAkB;;;;;IAC5CtD,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAmD,kBAAA,QAAAC,QAAA,CAAAG,KAAA,KAAkB;;;;;IAH5CvD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAFA,CAAAI,UAAA,IAAAoD,oDAAA,mBAAgC,IAAAC,oDAAA,mBACP,IAAAC,oDAAA,mBACA;;;;IAHnB1D,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAA2D,iBAAA,CAAAP,QAAA,CAAAQ,KAAA,CAAgB;IACf5D,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAA6C,QAAA,CAAAC,YAAA,CAAuB;IACvBrD,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAA6C,QAAA,CAAAE,KAAA,CAAgB;IAChBtD,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAA6C,QAAA,CAAAG,KAAA,CAAgB;;;AD1Y3C,OAAM,MAAOM,oBAAoB;EA6C/BC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,cAA8B,EAC9BC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAhDR,KAAAC,aAAa,GAAG,IAAI/E,OAAO,EAAQ;IACpC,KAAAgF,YAAY,GAAc,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC;MACtDhB,YAAY,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAACmF,QAAQ,CAAC,CAAC;MACzCC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACpF,UAAU,CAACmF,QAAQ,EAACnF,UAAU,CAACmE,KAAK,CAAC,CAAC;MAC3DkB,WAAW,EAAE,CACX,EAAE,EACF,CACErF,UAAU,CAACsF,OAAO,CAChB,uDAAuD,CACxD,CACF,CACF;MACDC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,6BAA6B,EAAE,CAAC,EAAE,CAAC;MACnCC,6BAA6B,EAAE,CAAC,EAAE,CAAC;MACnCC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC7F,UAAU,CAACmF,QAAQ,CAAC,CAAC;MACnCW,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAACmF,QAAQ,CAAC,CAAC;MACpCY,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClB3C,QAAQ,EAAE,IAAI,CAACsB,WAAW,CAACsB,KAAK,CAAC,CAAC,IAAI,CAACC,sBAAsB,EAAE,CAAC,CAAC;MACjEpC,SAAS,EAAE,IAAI,CAACa,WAAW,CAACsB,KAAK,CAAC,CAAC,IAAI,CAACE,uBAAuB,EAAE,CAAC;KACnE,CAAC;IAEK,KAAA9E,SAAS,GAAG,KAAK;IACjB,KAAA+E,MAAM,GAAG,KAAK;IACd,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAA1C,eAAe,GAAuC,EAAE;IACxD,KAAA2C,cAAc,GAAG,KAAK;IACtB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,aAAa,GAAW,EAAE;IAE1B,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI9G,OAAO,EAAU;IACpC,KAAA+G,cAAc,GAAQ,EAAE;IACzB,KAAA3D,aAAa,GAAsC,EAAE;EAOzD;EAEH4D,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEOD,cAAcA,CAAA;IACnB,IAAI,CAACvC,gBAAgB,CAClByC,eAAe,EAAE,CACjBC,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC8E,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7B,IAAI,CAACrE,aAAa,GAAG,CACnB;UAAEsE,IAAI,EAAE,mBAAmB;UAAEC,KAAK,EAAE;QAAI,CAAE,EAC1C,GAAGH,QAAQ,CAACC,IAAI,CAACtH,GAAG,CAAEyH,IAAS,KAAM;UACnCF,IAAI,EAAEE,IAAI,CAACC,WAAW;UACtBF,KAAK,EAAEC,IAAI,CAACE;SACb,CAAC,CAAC,CACJ;MACH;IACF,CAAC,CAAC;EACN;EAEAV,aAAaA,CAAA;IACX,MAAMW,YAAY,GAAG1H,OAAO,CAAC2H,eAAe,EAAE,CAC3C7H,GAAG,CAAE0F,OAAY,KAAM;MACtB6B,IAAI,EAAE7B,OAAO,CAAC6B,IAAI;MAClBO,OAAO,EAAEpC,OAAO,CAACoC;KAClB,CAAC,CAAC,CACFC,MAAM,CACJrC,OAAO,IAAKvF,KAAK,CAAC6H,kBAAkB,CAACtC,OAAO,CAACoC,OAAO,CAAC,CAAC3E,MAAM,GAAG,CAAC,CAClE;IAEH,MAAM8E,YAAY,GAAGL,YAAY,CAACM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,OAAO,KAAK,IAAI,CAAC;IACjE,MAAMM,MAAM,GAAGR,YAAY,CAACM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,OAAO,KAAK,IAAI,CAAC;IAC3D,MAAMO,MAAM,GAAGT,YAAY,CACxBG,MAAM,CAAEI,CAAC,IAAKA,CAAC,CAACL,OAAO,KAAK,IAAI,IAAIK,CAAC,CAACL,OAAO,KAAK,IAAI,CAAC,CACvDQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChB,IAAI,CAACkB,aAAa,CAACD,CAAC,CAACjB,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjD,IAAI,CAACjB,SAAS,GAAG,CAAC2B,YAAY,EAAEG,MAAM,EAAE,GAAGC,MAAM,CAAC,CAACN,MAAM,CAACW,OAAO,CAAC;EACpE;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACpC,MAAM,GAAGpG,KAAK,CAAC6H,kBAAkB,CAAC,IAAI,CAACxB,eAAe,CAAC,CAACxG,GAAG,CAC7D4I,KAAK,KAAM;MACVrB,IAAI,EAAEqB,KAAK,CAACrB,IAAI;MAChBO,OAAO,EAAEc,KAAK,CAACd;KAChB,CAAC,CACH;IACD,IAAI,CAACrB,aAAa,GAAG,EAAE,CAAC,CAAC;EAC3B;EAEQM,YAAYA,CAAA;IAClB,IAAI,CAACV,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC5B,gBAAgB,CAClBoE,kBAAkB,EAAE,CACpB1B,IAAI,CAAC/G,QAAQ,CAAC,MAAO,IAAI,CAACiG,cAAc,GAAG,KAAM,CAAC,CAAC,CACnDe,SAAS,CAAC;MACT0B,IAAI,EAAGxB,IAAS,IAAI;QAClB;QACA,IAAI,CAAC5D,eAAe,GAAG4D,IAAI;MAC7B,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACN;EAEQjC,YAAYA,CAAA;IAClB,IAAI,CAACmC,SAAS,GAAGlJ,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC2G,cAAc,CAAC;IAAE;IACzB,IAAI,CAACD,aAAa,CAACQ,IAAI,CACrB9G,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACmG,cAAc,GAAG,IAAK,CAAC,EACvCpG,SAAS,CAAE4I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACzE,gBAAgB,CAAC2E,WAAW,CAACD,MAAM,CAAC,CAAChC,IAAI,CACnD5G,GAAG,CAAE+G,IAAI,IAAK0B,OAAO,CAACK,GAAG,CAAC,eAAe,EAAE/B,IAAI,CAAC,CAAC;MAAE;MACnDtH,GAAG,CAAEsH,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF/G,GAAG,CAAC,MAAO,IAAI,CAACmG,cAAc,GAAG,KAAM,CAAC,EACxClG,UAAU,CAAEuI,KAAK,IAAI;QACnB,IAAI,CAACrC,cAAc,GAAG,KAAK;QAC3B,OAAOzG,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEA0C,kBAAkBA,CAAC2G,KAAU,EAAEC,OAAkB;IAC/CA,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,EAAEC,UAAU,CAACH,KAAK,CAAC9B,KAAK,CAACA,KAAK,CAAC;IACvE+B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAAEC,UAAU,CAACH,KAAK,CAAC9B,KAAK,CAACD,IAAI,CAAC;IAC3EyB,OAAO,CAACK,GAAG,CAAC,IAAI,CAACxE,YAAY,CAAC2C,KAAK,CAAC;EACtC;EAEMkC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACzI,SAAS,GAAG,IAAI;MAErB,IAAIyI,KAAI,CAAC9E,YAAY,CAACgF,OAAO,EAAE;QAC7B;MACF;MAEAF,KAAI,CAAC1D,MAAM,GAAG,IAAI;MAClB,MAAMuB,KAAK,GAAG;QAAE,GAAGmC,KAAI,CAAC9E,YAAY,CAAC2C;MAAK,CAAE;MAE5C,MAAMsC,uBAAuB,GAAGH,KAAI,CAACrD,SAAS,CAAC4B,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACL,OAAO,KAAK6B,KAAI,CAACnD,eAAe,CAC1C;MAED,MAAMC,aAAa,GAAGkD,KAAI,CAACpD,MAAM,CAAC2B,IAAI,CACnCU,KAAK,IAAKA,KAAK,CAACd,OAAO,KAAKN,KAAK,EAAE/B,MAAM,CAC3C;MAED,MAAM6B,IAAI,GAAG;QACXxD,YAAY,EAAE0D,KAAK,EAAE1D,YAAY;QACjCkB,aAAa,EAAEwC,KAAK,EAAExC,aAAa;QACnCY,UAAU,EAAE4B,KAAK,EAAE5B,UAAU;QAC7BX,WAAW,EAAEuC,KAAK,EAAEvC,WAAW;QAC/BY,YAAY,EAAE2B,KAAK,EAAE3B,YAAY;QACjCP,YAAY,EAAEkC,KAAK,EAAElC,YAAY;QACjCF,6BAA6B,EAAEoC,KAAK,EAAEpC,6BAA6B;QACnEC,6BAA6B,EAAEmC,KAAK,EAAEnC,6BAA6B;QACnEE,WAAW,EAAEiC,KAAK,EAAEjC,WAAW;QAC/BC,SAAS,EAAEgC,KAAK,EAAEhC,SAAS;QAC3BE,OAAO,EAAEoE,uBAAuB,EAAEvC,IAAI;QACtCwC,WAAW,EAAED,uBAAuB,EAAEhC,OAAO;QAC7CnC,WAAW,EAAE6B,KAAK,EAAE7B,WAAW;QAC/BF,MAAM,EAAEgB,aAAa,EAAEc,IAAI;QAC3BrE,QAAQ,EAAE8G,KAAK,CAACC,OAAO,CAACzC,KAAK,CAACtE,QAAQ,CAAC,GAAGsE,KAAK,CAACtE,QAAQ,GAAG,EAAE;QAAE;QAC/DS,SAAS,EAAE6D,KAAK,CAAC7D;OAClB;MAEDgG,KAAI,CAAClF,gBAAgB,CAClByF,cAAc,CAAC5C,IAAI,CAAC,CACpBH,IAAI,CAACrH,SAAS,CAAC6J,KAAI,CAAC/E,aAAa,CAAC,CAAC,CACnCwC,SAAS,CAAC;QACT0B,IAAI,EAAGzB,QAAa,IAAI;UACtB,IAAIA,QAAQ,EAAEC,IAAI,EAAE6C,UAAU,EAAE;YAC9BC,cAAc,CAACC,OAAO,CACpB,iBAAiB,EACjB,gCAAgC,CACjC;YACDC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAGF,MAAM,CAACC,QAAQ,CAACE,MAAM,qBAAqBpD,QAAQ,EAAEC,IAAI,EAAEjD,KAAK,WAAW;UACvG,CAAC,MAAM;YACL2E,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAE1B,QAAQ,CAAC;UAC5D;QACF,CAAC;QACD0B,KAAK,EAAG2B,GAAQ,IAAI;UAClBf,KAAI,CAAC1D,MAAM,GAAG,KAAK;UACnB,MAAM0E,GAAG,GAAQD,GAAG,EAAE3B,KAAK,EAAE6B,OAAO,IAAI,IAAI;UAC5C,IAAID,GAAG,EAAE;YACP,IACEA,GAAG,IACHA,GAAG,CAACE,QAAQ,CAAC,4BAA4B,CAAC,IAC1CF,GAAG,CAACE,QAAQ,CAAC,oBAAoB,CAAC,EAClC;cACAlB,KAAI,CAACjF,cAAc,CAACoG,GAAG,CAAC;gBACtBC,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE;eACT,CAAC;YACJ,CAAC,MAAM;cACLrB,KAAI,CAACjF,cAAc,CAACoG,GAAG,CAAC;gBACtBC,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAEN,GAAG,EAAE3B,KAAK,EAAE6B;eACrB,CAAC;YACJ;UACF,CAAC,MAAM;YACLjB,KAAI,CAACjF,cAAc,CAACoG,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;QACF;OACD,CAAC;IAAC;EACP;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC/H,QAAQ,CAACgI,IAAI,CAAC,IAAI,CAACnF,sBAAsB,EAAE,CAAC;EACnD;EAEAoF,cAAcA,CAAA;IACZ,IAAI,CAACxH,SAAS,CAACuH,IAAI,CAAC,IAAI,CAAClF,uBAAuB,EAAE,CAAC;EACrD;EAEAD,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACvB,WAAW,CAACM,KAAK,CAAC;MAC5BsG,UAAU,EAAE,CAAC,EAAE,EAAExL,UAAU,CAACmF,QAAQ,CAAC;MACrCsG,SAAS,EAAE,CAAC,EAAE,EAAEzL,UAAU,CAACmF,QAAQ,CAAC;MACpCuG,8BAA8B,EAAE,CAAC,EAAE,EAAE1L,UAAU,CAACmF,QAAQ,CAAC;MACzDwG,yBAAyB,EAAE,CAAC,EAAE,EAAE3L,UAAU,CAACmF,QAAQ,CAAC;MACpDC,aAAa,EAAE,CAAC,EAAE,EAAEpF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACmE,KAAK,CAAC;MAC1D8B,YAAY,EAAE,CAAC,EAAE,EAAEjG,UAAU,CAACmF,QAAQ;KACvC,CAAC;EACJ;EAEAiB,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACxB,WAAW,CAACM,KAAK,CAAC;MAC5B0G,gBAAgB,EAAE,CAAC,IAAI,CAAC;MACxBC,kBAAkB,EAAE,CAAC,IAAI;KAC1B,CAAC;EACJ;EAEAvJ,aAAaA,CAACwJ,KAAa;IACzB,IAAI,IAAI,CAACxI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACD,QAAQ,CAACyI,QAAQ,CAACD,KAAK,CAAC;IAC/B;EACF;EAEAnI,cAAcA,CAACmI,KAAa;IAC1B,IAAI,IAAI,CAAC/H,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACQ,SAAS,CAACgI,QAAQ,CAACD,KAAK,CAAC;IAChC;EACF;EAEA1I,cAAcA,CAAC0I,KAAa,EAAEE,KAAa,EAAEC,SAAiB;IAC5D,MAAMC,OAAO,GAAI,IAAI,CAACjH,YAAY,CAAC2E,GAAG,CAACqC,SAAS,CAAe,CAC5DE,EAAE,CAACL,KAAK,CAAC,CACTlC,GAAG,CAACoC,KAAK,CAAC;IACb,OAAOE,OAAO,EAAEjC,OAAO,KAAKiC,OAAO,EAAEE,OAAO,IAAI,IAAI,CAAC9K,SAAS,CAAC;EACjE;EAEA,IAAIC,CAACA,CAAA;IACH,OAAO,IAAI,CAAC0D,YAAY,CAACoH,QAAQ;EACnC;EAEA,IAAI/I,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC2B,YAAY,CAAC2E,GAAG,CAAC,UAAU,CAAc;EACvD;EAEA,IAAI7F,SAASA,CAAA;IACX,OAAO,IAAI,CAACkB,YAAY,CAAC2E,GAAG,CAAC,WAAW,CAAc;EACxD;EAEA0C,kBAAkBA,CAAC9F,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,qBAAqB,GAAG,IAAI;EACnC;EAEAgG,QAAQA,CAAA;IACN,IAAI,CAACxH,MAAM,CAACyH,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACnL,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC2D,YAAY,CAACyH,KAAK,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3H,aAAa,CAACkE,IAAI,EAAE;IACzB,IAAI,CAAClE,aAAa,CAAC4H,QAAQ,EAAE;EAC/B;;;uBA9TWlI,oBAAoB,EAAA7D,EAAA,CAAAgM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlM,EAAA,CAAAgM,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAApM,EAAA,CAAAgM,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtM,EAAA,CAAAgM,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB3I,oBAAoB;MAAA4I,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpBjC/M,EAAA,CAAA0B,SAAA,iBAAsD;UAG9C1B,EAFR,CAAAC,cAAA,cAAiC,aACiE,YAC1C;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAsBpDH,EArBhB,CAAAC,cAAA,aAA0C,aAkBS,aACnB,eAC0C,cACD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAAE,MAAA,cACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA0B,SAAA,iBACgG;UAChG1B,EAAA,CAAAI,UAAA,KAAA6M,oCAAA,kBAAmE;UAU3EjN,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC7C;UACRH,EAAA,CAAA0B,SAAA,iBAE4B;UAC5B1B,EAAA,CAAAI,UAAA,KAAA8M,oCAAA,kBAAoE;UAS5ElN,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrEH,EAAA,CAAAE,MAAA,iBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC+F;UAC/F1B,EAAA,CAAAI,UAAA,KAAA+M,oCAAA,kBAAkE;UAM1EnN,EADI,CAAAG,YAAA,EAAM,EACJ;UAoCMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnEH,EAAA,CAAAE,MAAA,sBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBACuD;UAE/D1B,EADI,CAAAG,YAAA,EAAM,EACJ;UAKMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAE,MAAA,gBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC4B;UAEpC1B,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5EH,EAAA,CAAAE,MAAA,gBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC4B;UAEpC1B,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnEH,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACxCF,EADwC,CAAAG,YAAA,EAAO,EACvC;UACRH,EAAA,CAAAC,cAAA,sBAGmE;UAF/DD,EAAA,CAAAoN,gBAAA,2BAAAC,mEAAAvL,MAAA;YAAA9B,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAAtN,EAAA,CAAAuN,kBAAA,CAAAP,GAAA,CAAAjH,eAAA,EAAAjE,MAAA,MAAAkL,GAAA,CAAAjH,eAAA,GAAAjE,MAAA;YAAA,OAAA9B,EAAA,CAAAwB,WAAA,CAAAM,MAAA;UAAA,EAA6B;UAAC9B,EAAA,CAAAiB,UAAA,sBAAAuM,8DAAA;YAAAxN,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAAYwL,GAAA,CAAA9E,eAAA,EAAiB;UAAA,EAAC;UAGhElI,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAI,UAAA,KAAAqN,oCAAA,kBAA8D;UAUtEzN,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3EH,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACrC;UACRH,EAAA,CAAAC,cAAA,sBAEiG;UAFzBD,EAAA,CAAAoN,gBAAA,2BAAAM,mEAAA5L,MAAA;YAAA9B,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAAtN,EAAA,CAAAuN,kBAAA,CAAAP,GAAA,CAAAhH,aAAA,EAAAlE,MAAA,MAAAkL,GAAA,CAAAhH,aAAA,GAAAlE,MAAA;YAAA,OAAA9B,EAAA,CAAAwB,WAAA,CAAAM,MAAA;UAAA,EAA2B;UAGnG9B,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAI,UAAA,KAAAuN,oCAAA,kBAA6D;UAUrE3N,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAE,MAAA,cACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC4B;UAEpC1B,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3EH,EAAA,CAAAE,MAAA,kBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC4B;UAEpC1B,EADI,CAAAG,YAAA,EAAM,EACJ;UAIMH,EAHZ,CAAAC,cAAA,cAA+C,cACnB,gBAC0C,gBACD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnEH,EAAA,CAAAE,MAAA,oBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA0B,SAAA,iBAC4B;UAI5C1B,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;UAENH,EAAA,CAAA0B,SAAA,eAAqD;UAI7C1B,EAFR,CAAAC,cAAA,eAAoF,eACd,aACd;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIzDH,EAFJ,CAAAC,cAAA,gBAAwB,qBAG6C;UAD9BD,EAAA,CAAAiB,UAAA,mBAAA2M,0DAAA;YAAA5N,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAAvB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UACPzL,EAAA,CAAAG,YAAA,EAAW;UAC5EH,EAAA,CAAAC,cAAA,mBACwE;UAA1BD,EAAA,CAAAiB,UAAA,mBAAA4M,wDAAA;YAAA7N,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAAxC,aAAA,EAAe;UAAA,EAAC;UAG/ExK,EAHgF,CAAAG,YAAA,EAAS,EAE/E,EACJ;UAGNH,EAAA,CAAAC,cAAA,uBAC+B;UA2C3BD,EA1CA,CAAAI,UAAA,MAAA0N,6CAAA,2BAAgC,MAAAC,6CAAA,2BA0C2B;UAmDnE/N,EADI,CAAAG,YAAA,EAAU,EACR;UAENH,EAAA,CAAA0B,SAAA,gBAAqD;UAI7C1B,EAFR,CAAAC,cAAA,gBAAoF,gBACd,cACd;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9DH,EAAA,CAAAC,cAAA,mBACyE;UAA3BD,EAAA,CAAAiB,UAAA,mBAAA+M,wDAAA;YAAAhO,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAAtC,cAAA,EAAgB;UAAA,EAAC;UAC5E1K,EAD6E,CAAAG,YAAA,EAAS,EAChF;UAENH,EAAA,CAAAC,cAAA,uBAC+B;UAwB3BD,EAvBA,CAAAI,UAAA,MAAA6N,6CAAA,2BAAgC,MAAAC,6CAAA,0BAuB4B;UAsBpElO,EADI,CAAAG,YAAA,EAAU,EACR;UAEFH,EADJ,CAAAC,cAAA,gBAAgD,mBAGnB;UAArBD,EAAA,CAAAiB,UAAA,mBAAAkN,wDAAA;YAAAnO,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAAtB,QAAA,EAAU;UAAA,EAAC;UAAC1L,EAAA,CAAAG,YAAA,EAAS;UAClCH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAiB,UAAA,mBAAAmN,wDAAA;YAAApO,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAA/D,QAAA,EAAU;UAAA,EAAC;UAEhCjJ,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;UACPH,EAAA,CAAAC,cAAA,qBAC2C;UADlBD,EAAA,CAAAoN,gBAAA,2BAAAiB,kEAAAvM,MAAA;YAAA9B,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAAtN,EAAA,CAAAuN,kBAAA,CAAAP,GAAA,CAAAtH,qBAAA,EAAA5D,MAAA,MAAAkL,GAAA,CAAAtH,qBAAA,GAAA5D,MAAA;YAAA,OAAA9B,EAAA,CAAAwB,WAAA,CAAAM,MAAA;UAAA,EAAmC;UAE5D9B,EAAA,CAAAI,UAAA,MAAAkO,6CAAA,0BAAgC;UAOpBtO,EAHZ,CAAAC,cAAA,iBAAyE,gBAChB,kBAC+C,iBACrD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,kBACxD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEJH,EADJ,CAAAC,cAAA,gBAAwC,sBAIhB;;UAChBD,EAAA,CAAAI,UAAA,MAAAmO,6CAAA,0BAA2C;UAQvDvO,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;UAEFH,EADJ,CAAAC,cAAA,gBAAiD,mBAGD;UAAxCD,EAAA,CAAAiB,UAAA,mBAAAuN,wDAAA;YAAAxO,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAAAwL,GAAA,CAAAtH,qBAAA,GAAiC,KAAK;UAAA,EAAC;UACvC1F,EAAA,CAAAE,MAAA,iBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAiB,UAAA,mBAAAwN,wDAAA;YAAAzO,EAAA,CAAAmB,aAAA,CAAAmM,GAAA;YAAA,OAAAtN,EAAA,CAAAwB,WAAA,CAASwL,GAAA,CAAA/D,QAAA,EAAU;UAAA,EAAC;UACpBjJ,EAAA,CAAAE,MAAA,eACJ;UAGRF,EAHQ,CAAAG,YAAA,EAAS,EACP,EACH,EACI;;;UA/amBH,EAAA,CAAAO,UAAA,cAAa;UACrCP,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAyM,GAAA,CAAA5I,YAAA,CAA0B;UA6BRpE,EAAA,CAAAM,SAAA,IAAmE;UAAnEN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA0O,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,iBAAAC,MAAA,EAAmE;UACjEX,EAAA,CAAAM,SAAA,EAA2C;UAA3CN,EAAA,CAAAO,UAAA,SAAAyM,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,iBAAAC,MAAA,CAA2C;UAkBjBX,EAAA,CAAAM,SAAA,GAAoE;UAApEN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA0O,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,kBAAAC,MAAA,EAAoE;UAE9FX,EAAA,CAAAM,SAAA,EAA4C;UAA5CN,EAAA,CAAAO,UAAA,SAAAyM,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,kBAAAC,MAAA,CAA4C;UAiBxBX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA0O,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,gBAAAC,MAAA,EAAkE;UACtFX,EAAA,CAAAM,SAAA,EAA0C;UAA1CN,EAAA,CAAAO,UAAA,SAAAyM,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,gBAAAC,MAAA,CAA0C;UA4EpCX,EAAA,CAAAM,SAAA,IAAqB;UAArBN,EAAA,CAAAO,UAAA,YAAAyM,GAAA,CAAAnH,SAAA,CAAqB;UAC7B7F,EAAA,CAAA4O,gBAAA,YAAA5B,GAAA,CAAAjH,eAAA,CAA6B;UAE7B/F,EAF6D,CAAAO,UAAA,gBAAe,+BACpB,YAAAP,EAAA,CAAA0O,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,YAAAC,MAAA,EACM;UAE5DX,EAAA,CAAAM,SAAA,EAAsC;UAAtCN,EAAA,CAAAO,UAAA,SAAAyM,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,YAAAC,MAAA,CAAsC;UAiBhCX,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAO,UAAA,YAAAyM,GAAA,CAAAlH,MAAA,CAAkB;UAA0C9F,EAAA,CAAA4O,gBAAA,YAAA5B,GAAA,CAAAhH,aAAA,CAA2B;UAEhEhG,EADqB,CAAAO,UAAA,cAAAyM,GAAA,CAAAjH,eAAA,CAA6B,+BACnD,YAAA/F,EAAA,CAAA0O,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,WAAAC,MAAA,EAA8D;UAE1FX,EAAA,CAAAM,SAAA,EAAqC;UAArCN,EAAA,CAAAO,UAAA,SAAAyM,GAAA,CAAAvM,SAAA,IAAAuM,GAAA,CAAAtM,CAAA,WAAAC,MAAA,CAAqC;UAqD3BX,EAAA,CAAAM,SAAA,IAAiB;UAAjBN,EAAA,CAAAO,UAAA,kBAAiB;UAQhCP,EAAA,CAAAM,SAAA,GAA4B;UAAqBN,EAAjD,CAAAO,UAAA,UAAAyM,GAAA,CAAAvK,QAAA,kBAAAuK,GAAA,CAAAvK,QAAA,CAAA+I,QAAA,CAA4B,oBAAoB,YAAY;UA0G5DxL,EAAA,CAAAM,SAAA,IAA6B;UAAqBN,EAAlD,CAAAO,UAAA,UAAAyM,GAAA,CAAA9J,SAAA,kBAAA8J,GAAA,CAAA9J,SAAA,CAAAsI,QAAA,CAA6B,oBAAoB,YAAY;UAwDrBxL,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAA6O,UAAA,CAAA7O,EAAA,CAAA8O,eAAA,KAAAC,GAAA,EAA4B;UAA/E/O,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAA4O,gBAAA,YAAA5B,GAAA,CAAAtH,qBAAA,CAAmC;UAC5D1F,EAD0F,CAAAO,UAAA,qBAAoB,oBAC3F;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAyM,GAAA,CAAA5I,YAAA,CAA0B;UAMEpE,EAAA,CAAAM,SAAA,GAA2B;UAEiBN,EAF5C,CAAAO,UAAA,UAAAP,EAAA,CAAAgP,WAAA,UAAAhC,GAAA,CAAAxE,SAAA,EAA2B,sBACxB,YAAAwE,GAAA,CAAA/G,cAAA,CAA2B,oBAAoB,cAAA+G,GAAA,CAAA9G,aAAA,CACP,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}