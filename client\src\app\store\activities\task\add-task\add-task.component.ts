import { Component, OnInit } from '@angular/core';
import { ActivitiesService } from '../../activities.service';
import { Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  startWith,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';

interface DropdownOption {
  label: string;
  value: string;
}
@Component({
  selector: 'app-add-task',
  templateUrl: './add-task.component.html',
  styleUrl: './add-task.component.scss',
})
export class AddTaskComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public accounts$?: Observable<any[]>;
  public accountLoading = false;
  public accountInput$ = new Subject<string>();
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  public employees$: Observable<any[]> = new Observable<any[]>();
  public employeeInput$ = new Subject<string>();
  public employeeLoading = false;
  public existingcontacts$?: Observable<any[]>;
  public existingcontactLoading = false;
  public existingcontactInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public submitted = false;
  public saving = false;
  public existingDialogVisible: boolean = false;
  public position: string = 'right';
  private owner_id: string | null = null;

  public TaskForm: FormGroup = this.formBuilder.group({
    document_type: ['', [Validators.required]],
    subject: ['', [Validators.required]],
    main_account_party_id: [null, [Validators.required]],
    main_contact_party_id: [null, [Validators.required]],
    task_category: ['', [Validators.required]],
    processor_party_id: [null],
    start_date: [''],
    end_date: [''],
    priority: [''],
    activity_status: ['', [Validators.required]],
    notes: ['', [Validators.required]],
    //contactexisting: [''],
    involved_parties: this.formBuilder.array([this.createContactFormGroup()]),
  });

  public dropdowns: Record<string, any[]> = {
    activityDocumentTypes: [],
    activityCategory: [],
    activityPriority: [],
    activityStatus: [],
  };

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private messageservice: MessageService,
    private activitiesservice: ActivitiesService
  ) {}

  ngOnInit(): void {
    this.loadActivityDropDown(
      'activityDocumentTypes',
      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'
    );
    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');
    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');
    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');
    this.TaskForm.get('main_account_party_id')
      ?.valueChanges.pipe(
        takeUntil(this.unsubscribe$),
        tap((selectedBpId) => {
          if (selectedBpId) {
            this.loadAccountByContacts(selectedBpId);
          } else {
            this.contacts$ = of(this.defaultOptions);
          }
        }),
        catchError((err) => {
          console.error('Account selection error:', err);
          this.contacts$ = of(this.defaultOptions);
          return of();
        })
      )
      .subscribe();
    this.loadAccounts();
    this.loadExistingContacts();
    this.loadEmployees();
    this.getOwner().subscribe({
      next: (response: string | null) => {
        this.owner_id = response;
      },
      error: (err) => {
        console.error('Error fetching bp_id:', err);
      },
    });
  }

  private getOwner(): Observable<string | null> {
    return this.activitiesservice.getEmailwisePartner();
  }

  loadActivityDropDown(target: string, type: string): void {
    this.activitiesservice
      .getActivityDropdownOptions(type)
      .subscribe((res: any) => {
        const options: DropdownOption[] =
          res?.data?.map(
            (attr: { description: string; code: string }): DropdownOption => ({
              label: attr.description,
              value: attr.code,
            })
          ) ?? [];

        // Assign options to dropdown object
        this.dropdowns[target] = options;

        // Set 'Open' as default selected for activityStatus only
        if (target === 'activityStatus') {
          const openOption = options.find(
            (opt) => opt.label.toLowerCase() === 'open'
          );
          if (openOption) {
            this.TaskForm.get('activity_status')?.setValue(openOption.value);
          }
        }
      });
  }

  private loadAccounts(): void {
    this.accounts$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.accountInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.accountLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'FLCU01',
            'filters[roles][bp_role][$in][1]': 'FLCU00',
            'filters[roles][bp_role][$in][2]': 'PRO001',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Account fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.accountLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  private loadExistingContacts() {
    this.existingcontacts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.existingcontactInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.existingcontactLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq]`]: 'BUP001',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((data: any) => {
              return data || []; // Make sure to return correct data structure
            }),
            tap(() => (this.existingcontactLoading = false)),
            catchError((error) => {
              this.existingcontactLoading = false;
              return of([]);
            })
          );
        })
      )
    );
  }

  private loadAccountByContacts(bpId: string): void {
    this.contacts$ = this.contactInput$.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.contactLoading = true)),
      switchMap((term: string) => {
        const params: any = {
          'filters[bp_company_id][$eq]': bpId,
          'populate[business_partner_person][populate][addresses][populate]':
            '*',
        };

        if (term) {
          params[
            'filters[$or][0][business_partner_person][bp_id][$containsi]'
          ] = term;
          params[
            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'
          ] = term;
        }

        return this.activitiesservice.getPartnersContact(params).pipe(
          map((response: any[]) => response || []),
          tap((contacts: any[]) => {
            this.contactLoading = false;
          }),
          catchError((error) => {
            console.error('Contact loading failed:', error);
            this.contactLoading = false;
            return of([]);
          })
        );
      })
    );
  }

  private loadEmployees(): void {
    this.employees$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.employeeInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.employeeLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'BUP003',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.activitiesservice.getPartners(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Employee fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.employeeLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  // selectExistingContact() {
  //   this.addExistingContact(this.TaskForm.value);
  //   this.existingDialogVisible = false; // Close dialog
  // }

  // addExistingContact(existing: any) {
  //   const contactForm = this.formBuilder.group({
  //     bp_full_name: [existing?.contactexisting?.bp_full_name || ''],
  //     email_address: [existing?.contactexisting?.email || ''],
  //     mobile: [existing?.contactexisting?.mobile?.[0] || ''],
  //     role_code: 'BUP001',
  //     party_id: existing?.contactexisting?.bp_id || '',
  //   });

  //   const firstGroup = this.involved_parties.at(0) as FormGroup;
  //   const bpName = firstGroup?.get('bp_full_name')?.value;

  //   if (!bpName && this.involved_parties.length === 1) {
  //     this.involved_parties.setControl(0, contactForm);
  //   } else {
  //     this.involved_parties.push(contactForm);
  //   }

  //   this.existingDialogVisible = false;
  // }

  // addNewContact() {
  //   this.involved_parties.push(this.createContactFormGroup());
  // }

  // deleteContact(index: number) {
  //   if (this.involved_parties.length > 1) {
  //     this.involved_parties.removeAt(index);
  //   }
  // }

  createContactFormGroup(): FormGroup {
    return this.formBuilder.group({
      bp_full_name: [''],
      email_address: [''],
      mobile: [''],
    });
  }

  async onSubmit() {
    this.submitted = true;
    if (this.TaskForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.TaskForm.value };

    const data = {
      document_type: value?.document_type,
      subject: value?.subject,
      main_account_party_id: value?.main_account_party_id,
      main_contact_party_id: value?.main_contact_party_id,
      task_category: value?.task_category,
      processor_party_id: value?.processor_party_id,
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      priority: value?.priority,
      activity_status: value?.activity_status,
      owner_party_id: this.owner_id,
      note: value?.notes,
      involved_parties: Array.isArray(value.involved_parties)
        ? [
            ...value.involved_parties,
            ...(value?.main_account_party_id
              ? [{ role_code: 'FLCU01', party_id: value.main_account_party_id }]
              : []),
            ...(value?.main_contact_party_id
              ? [{ role_code: 'BUP001', party_id: value.main_contact_party_id }]
              : []),
            ...(this.owner_id
              ? [{ role_code: 'BUP003', party_id: this.owner_id }]
              : []),
          ].filter(
            (item) =>
              item &&
              Object.keys(item).length > 0 &&
              item.party_id &&
              item.role_code
          )
        : [],
    };

    this.activitiesservice
      .createActivityTask(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.activity_id) {
            sessionStorage.setItem('taskMessage', 'Task created successfully!');
            window.location.href = `${window.location.origin}#/store/activities/tasks/${response?.data?.activity_id}/overview`;
          } else {
            console.error('Missing activity_id in response:', response);
          }
        },
        error: (res: any) => {
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  get f(): any {
    return this.TaskForm.controls;
  }

  get involved_parties(): any {
    return this.TaskForm.get('involved_parties') as FormArray;
  }

  showExistingDialog(position: string) {
    this.position = position;
    this.existingDialogVisible = true;
  }

  onCancel() {
    this.router.navigate(['/store/activities/tasks']);
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
