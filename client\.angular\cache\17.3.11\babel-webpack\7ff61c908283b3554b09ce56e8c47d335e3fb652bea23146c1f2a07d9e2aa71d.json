{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SalesQuotesRoutingModule } from './sales-quotes-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { SalesQuotesComponent } from './sales-quotes.component';\nimport { SalesQuotesDetailsComponent } from './sales-quotes-details/sales-quotes-details.component';\nimport { SalesQuotesOverviewComponent } from './sales-quotes-details/sales-quotes-overview/sales-quotes-overview.component';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SalesQuotesContactsComponent } from './sales-quotes-details/sales-quotes-contacts/sales-quotes-contacts.component';\nimport { SalesQuotePartnersComponent } from './sales-quotes-details/sales-quote-partners/sales-quote-partners.component';\nimport { SalesQuoteOpportunitiesComponent } from './sales-quotes-details/sales-quote-opportunities/sales-quote-opportunities.component';\nimport { SalesQuoteOrganizationDataComponent } from './sales-quotes-details/sales-quote-organization-data/sales-quote-organization-data.component';\nimport { SalesQuoteSalesTeamComponent } from './sales-quotes-details/sales-quote-sales-team/sales-quote-sales-team.component';\nimport { SalesQuoteAttachmentsComponent } from './sales-quotes-details/sales-quote-attachments/sales-quote-attachments.component';\nimport { SalesQuoteActivitiesComponent } from './sales-quotes-details/sales-quote-activities/sales-quote-activities.component';\nimport { SalesQuoteRelationshipsComponent } from './sales-quotes-details/sales-quote-relationships/sales-quote-relationships.component';\nimport { SalesQuoteTicketsComponent } from './sales-quotes-details/sales-quote-tickets/sales-quote-tickets.component';\nimport { SalesQuotesNotesComponent } from './sales-quotes-details/sales-quotes-notes/sales-quotes-notes.component';\nimport { SalesQuotesAiInsightsComponent } from './sales-quotes-details/sales-quotes-ai-insights/sales-quotes-ai-insights.component';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport * as i0 from \"@angular/core\";\nexport class SalesQuotesModule {\n  static {\n    this.ɵfac = function SalesQuotesModule_Factory(t) {\n      return new (t || SalesQuotesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SalesQuotesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, SalesQuotesRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesQuotesModule, {\n    declarations: [SalesQuotesComponent, SalesQuotesDetailsComponent, SalesQuotesOverviewComponent, SalesQuotesContactsComponent, SalesQuotePartnersComponent, SalesQuoteOpportunitiesComponent, SalesQuoteOrganizationDataComponent, SalesQuoteSalesTeamComponent, SalesQuoteAttachmentsComponent, SalesQuoteActivitiesComponent, SalesQuoteRelationshipsComponent, SalesQuoteTicketsComponent, SalesQuotesNotesComponent, SalesQuotesAiInsightsComponent],\n    imports: [CommonModule, SalesQuotesRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SalesQuotesRoutingModule", "FormsModule", "MessageService", "ConfirmationService", "AutoCompleteModule", "ButtonModule", "DropdownModule", "TableModule", "TabViewModule", "SalesQuotesComponent", "SalesQuotesDetailsComponent", "SalesQuotesOverviewComponent", "BreadcrumbModule", "CalendarModule", "InputTextModule", "SalesQuotesContactsComponent", "SalesQuotePartnersComponent", "SalesQuoteOpportunitiesComponent", "SalesQuoteOrganizationDataComponent", "SalesQuoteSalesTeamComponent", "SalesQuoteAttachmentsComponent", "SalesQuoteActivitiesComponent", "SalesQuoteRelationshipsComponent", "SalesQuoteTicketsComponent", "SalesQuotesNotesComponent", "SalesQuotesAiInsightsComponent", "PaginatorModule", "ReactiveFormsModule", "ProgressSpinnerModule", "SalesQuotesModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SalesQuotesRoutingModule } from './sales-quotes-routing.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { SalesQuotesComponent } from './sales-quotes.component';\r\nimport { SalesQuotesDetailsComponent } from './sales-quotes-details/sales-quotes-details.component';\r\nimport { SalesQuotesOverviewComponent } from './sales-quotes-details/sales-quotes-overview/sales-quotes-overview.component';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { SalesQuotesContactsComponent } from './sales-quotes-details/sales-quotes-contacts/sales-quotes-contacts.component';\r\nimport { SalesQuotePartnersComponent } from './sales-quotes-details/sales-quote-partners/sales-quote-partners.component';\r\nimport { SalesQuoteOpportunitiesComponent } from './sales-quotes-details/sales-quote-opportunities/sales-quote-opportunities.component';\r\nimport { SalesQuoteOrganizationDataComponent } from './sales-quotes-details/sales-quote-organization-data/sales-quote-organization-data.component';\r\nimport { SalesQuoteSalesTeamComponent } from './sales-quotes-details/sales-quote-sales-team/sales-quote-sales-team.component';\r\nimport { SalesQuoteAttachmentsComponent } from './sales-quotes-details/sales-quote-attachments/sales-quote-attachments.component';\r\nimport { SalesQuoteActivitiesComponent } from './sales-quotes-details/sales-quote-activities/sales-quote-activities.component';\r\nimport { SalesQuoteRelationshipsComponent } from './sales-quotes-details/sales-quote-relationships/sales-quote-relationships.component';\r\nimport { SalesQuoteTicketsComponent } from './sales-quotes-details/sales-quote-tickets/sales-quote-tickets.component';\r\nimport { SalesQuotesNotesComponent } from './sales-quotes-details/sales-quotes-notes/sales-quotes-notes.component';\r\nimport { SalesQuotesAiInsightsComponent } from './sales-quotes-details/sales-quotes-ai-insights/sales-quotes-ai-insights.component';\r\nimport { PaginatorModule } from 'primeng/paginator';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SalesQuotesComponent,\r\n    SalesQuotesDetailsComponent,\r\n    SalesQuotesOverviewComponent,\r\n    SalesQuotesContactsComponent,\r\n    SalesQuotePartnersComponent,\r\n    SalesQuoteOpportunitiesComponent,\r\n    SalesQuoteOrganizationDataComponent,\r\n    SalesQuoteSalesTeamComponent,\r\n    SalesQuoteAttachmentsComponent,\r\n    SalesQuoteActivitiesComponent,\r\n    SalesQuoteRelationshipsComponent,\r\n    SalesQuoteTicketsComponent,\r\n    SalesQuotesNotesComponent,\r\n    SalesQuotesAiInsightsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SalesQuotesRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule,\r\n    PaginatorModule,\r\n    ReactiveFormsModule,\r\n    ProgressSpinnerModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class SalesQuotesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,2BAA2B,QAAQ,4EAA4E;AACxH,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,mCAAmC,QAAQ,8FAA8F;AAClJ,SAASC,4BAA4B,QAAQ,gFAAgF;AAC7H,SAASC,8BAA8B,QAAQ,kFAAkF;AACjI,SAASC,6BAA6B,QAAQ,gFAAgF;AAC9H,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,0BAA0B,QAAQ,0EAA0E;AACrH,SAASC,yBAAyB,QAAQ,wEAAwE;AAClH,SAASC,8BAA8B,QAAQ,oFAAoF;AACnI,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,qBAAqB,QAAQ,yBAAyB;;AAsC/D,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;iBAFjB,CAAC3B,cAAc,EAAEC,mBAAmB,CAAC;MAAA2B,OAAA,GAf9C/B,YAAY,EACZC,wBAAwB,EACxBC,WAAW,EACXM,WAAW,EACXF,YAAY,EACZC,cAAc,EACdE,aAAa,EACbJ,kBAAkB,EAClBQ,gBAAgB,EAChBC,cAAc,EACdC,eAAe,EACfY,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB;IAAA;EAAA;;;2EAIZC,iBAAiB;IAAAE,YAAA,GAjC1BtB,oBAAoB,EACpBC,2BAA2B,EAC3BC,4BAA4B,EAC5BI,4BAA4B,EAC5BC,2BAA2B,EAC3BC,gCAAgC,EAChCC,mCAAmC,EACnCC,4BAA4B,EAC5BC,8BAA8B,EAC9BC,6BAA6B,EAC7BC,gCAAgC,EAChCC,0BAA0B,EAC1BC,yBAAyB,EACzBC,8BAA8B;IAAAK,OAAA,GAG9B/B,YAAY,EACZC,wBAAwB,EACxBC,WAAW,EACXM,WAAW,EACXF,YAAY,EACZC,cAAc,EACdE,aAAa,EACbJ,kBAAkB,EAClBQ,gBAAgB,EAChBC,cAAc,EACdC,eAAe,EACfY,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}