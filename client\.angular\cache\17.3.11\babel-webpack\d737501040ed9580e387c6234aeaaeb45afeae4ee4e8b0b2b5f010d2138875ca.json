{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../account.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/tabview\";\nfunction AccountDetailsComponent_p_tabPanel_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 32);\n    i0.ɵɵtemplate(1, AccountDetailsComponent_p_tabPanel_7_ng_template_1_Template, 2, 2, \"ng-template\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class AccountDetailsComponent {\n  constructor(route, router, accountservice) {\n    this.route = route;\n    this.router = router;\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.accountDetails = null;\n    this.items = [];\n    this.activeItem = {};\n    this.bp_id = '';\n    this.activeMenu = '';\n    this.isSidebarHidden = false;\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Accounts',\n      routerLink: ['/store/account']\n    }, {\n      label: `${this.activeMenu}`\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n    this.makeMenuItems(this.bp_id);\n    this.activeItem = this.items[0];\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const accountId = params.get('id');\n      if (accountId) {\n        this.loadAccountData(accountId);\n      }\n    });\n  }\n  makeMenuItems(bp_id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/account/${bp_id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/account/${bp_id}/contacts`\n    }, {\n      label: 'Partners',\n      routerLink: `/store/account/${bp_id}/partners`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/account/${bp_id}/sales-team`\n    }, {\n      label: 'Opportunities',\n      routerLink: `/store/account/${bp_id}/opportunities`\n    }, {\n      label: 'AI Insights',\n      routerLink: `/store/account/${bp_id}/ai-insights`\n    }, {\n      label: 'Organization Data',\n      routerLink: `/store/account/${bp_id}/organization-data`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/account/${bp_id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/account/${bp_id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/account/${bp_id}/activities`\n    }, {\n      label: 'Relationships',\n      routerLink: `/store/account/${bp_id}/relationships`\n    }, {\n      label: 'Tickets',\n      routerLink: `/store/account/${bp_id}/tickets`\n    }, {\n      label: 'Sales Quotes',\n      routerLink: `/store/account/${bp_id}/sales-quotes`\n    }, {\n      label: 'Sales Orders',\n      routerLink: `/store/account/${bp_id}/sales-orders`\n    }];\n  }\n  loadAccountData(accountId) {\n    this.accountservice.getAccountByID(accountId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.accountDetails = response?.data || null;\n        this.accountservice.accountSubject.next(this.accountDetails);\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/account']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  static {\n    this.ɵfac = function AccountDetailsComponent_Factory(t) {\n      return new (t || AccountDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountDetailsComponent,\n      selectors: [[\"app-account-details\"]],\n      decls: 93,\n      vars: 15,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"scrollable\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"mt-6\", \"mb-1\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-3\"], [\"type\", \"button\", \"icon\", \"pi pi-check\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-3rem\", \"w-3rem\"], [1, \"material-symbols-rounded\", \"text-primary\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function AccountDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"p-tabView\", 6);\n          i0.ɵɵtemplate(7, AccountDetailsComponent_p_tabPanel_7_Template, 2, 1, \"p-tabPanel\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"h5\", 15);\n          i0.ɵɵtext(16, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"h5\", 17);\n          i0.ɵɵtext(19, \" SNJYA Customer Sprint 2 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"ul\", 18)(21, \"li\", 19)(22, \"span\", 20);\n          i0.ɵɵtext(23, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" : 05545SD585 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"li\", 19)(26, \"span\", 20);\n          i0.ɵɵtext(27, \"S4/HANA ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" : 152ASD5585 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"li\", 19)(30, \"span\", 20);\n          i0.ɵɵtext(31, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" : Adam Smith \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 19)(34, \"span\", 20);\n          i0.ɵɵtext(35, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" : Ben Jacobs \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 21)(38, \"button\", 22)(39, \"i\", 23);\n          i0.ɵɵtext(40, \"call\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"button\", 22)(42, \"i\", 23);\n          i0.ɵɵtext(43, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"button\", 22)(45, \"i\", 23);\n          i0.ɵɵtext(46, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"button\", 22)(48, \"i\", 23);\n          i0.ɵɵtext(49, \"language\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"button\", 22)(51, \"i\", 23);\n          i0.ɵɵtext(52, \"edit_square\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(53, \"div\", 24)(54, \"ul\", 25)(55, \"li\", 26)(56, \"span\", 27)(57, \"i\", 28);\n          i0.ɵɵtext(58, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(59, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"span\", 29);\n          i0.ɵɵtext(61, \"3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA 60532.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"li\", 26)(63, \"span\", 27)(64, \"i\", 28);\n          i0.ɵɵtext(65, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"span\", 29);\n          i0.ɵɵtext(68);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"li\", 26)(70, \"span\", 27)(71, \"i\", 28);\n          i0.ɵɵtext(72, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(73, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\", 29);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"li\", 26)(77, \"span\", 27)(78, \"i\", 28);\n          i0.ɵɵtext(79, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"span\", 29);\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"li\", 26)(84, \"span\", 27)(85, \"i\", 28);\n          i0.ɵɵtext(86, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(87, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"span\", 29);\n          i0.ɵɵtext(89, \"www.asardigital.com\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(90, \"div\", 30)(91, \"p-button\", 31);\n          i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_p_button_click_91_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(92, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(58);\n          i0.ɵɵtextInterpolate((ctx.accountDetails == null ? null : ctx.accountDetails.phone) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.accountDetails == null ? null : ctx.accountDetails.phone) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.accountDetails == null ? null : ctx.accountDetails.email) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i3.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.Breadcrumb, i5.PrimeTemplate, i6.Button, i7.TabView, i7.TabPanel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "AccountDetailsComponent_p_tabPanel_7_ng_template_1_Template", "AccountDetailsComponent", "constructor", "route", "router", "accountservice", "unsubscribe$", "accountDetails", "items", "activeItem", "bp_id", "activeMenu", "isSidebarHidden", "ngOnInit", "breadcrumbitems", "home", "icon", "snapshot", "paramMap", "get", "makeMenuItems", "pipe", "subscribe", "params", "accountId", "loadAccountData", "getAccountByID", "next", "response", "data", "accountSubject", "error", "console", "goToBack", "navigate", "toggleSidebar", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "AccountDetailsComponent_p_tabPanel_7_Template", "ɵɵlistener", "AccountDetailsComponent_Template_p_button_click_91_listener", "ɵɵclassProp", "phone", "email"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AccountService } from '../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-account-details',\r\n  templateUrl: './account-details.component.html',\r\n  styleUrl: './account-details.component.scss',\r\n})\r\nexport class AccountDetailsComponent {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public accountDetails: any = null;\r\n  breadcrumbitems: MenuItem[] | any;\r\n  home: MenuItem | any;\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem = {};\r\n  public bp_id: string = '';\r\n  activeMenu: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private accountservice: AccountService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Accounts', routerLink: ['/store/account'] },\r\n      { label: `${this.activeMenu}` },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.makeMenuItems(this.bp_id);\r\n    this.activeItem = this.items[0];\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const accountId = params.get('id');\r\n        if (accountId) {\r\n          this.loadAccountData(accountId);\r\n        }\r\n      });\r\n  }\r\n\r\n  makeMenuItems(bp_id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/account/${bp_id}/overview`,\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        routerLink: `/store/account/${bp_id}/contacts`,\r\n      },\r\n      {\r\n        label: 'Partners',\r\n        routerLink: `/store/account/${bp_id}/partners`,\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        routerLink: `/store/account/${bp_id}/sales-team`,\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        routerLink: `/store/account/${bp_id}/opportunities`,\r\n      },\r\n      {\r\n        label: 'AI Insights',\r\n        routerLink: `/store/account/${bp_id}/ai-insights`,\r\n      },\r\n      {\r\n        label: 'Organization Data',\r\n        routerLink: `/store/account/${bp_id}/organization-data`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/account/${bp_id}/attachments`,\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        routerLink: `/store/account/${bp_id}/notes`,\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        routerLink: `/store/account/${bp_id}/activities`,\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        routerLink: `/store/account/${bp_id}/relationships`,\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        routerLink: `/store/account/${bp_id}/tickets`,\r\n      },\r\n      {\r\n        label: 'Sales Quotes',\r\n        routerLink: `/store/account/${bp_id}/sales-quotes`,\r\n      },\r\n      {\r\n        label: 'Sales Orders',\r\n        routerLink: `/store/account/${bp_id}/sales-orders`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  private loadAccountData(accountId: string): void {\r\n    this.accountservice\r\n      .getAccountByID(accountId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.accountDetails = response?.data || null;\r\n          this.accountservice.accountSubject.next(this.accountDetails);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/account']);\r\n  }\r\n\r\n  isSidebarHidden = false;\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <!-- <h4 class=\"m-0 p-0 ml-auto\"><span class=\"text-orange-600\">Account ID</span> 1280056</h4> -->\r\n        <!-- <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 surface-0 border-1 border-primary font-semibold'\" /> -->\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\">\r\n                <p-tabPanel *ngFor=\"let tab of items\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        SNJYA Customer Sprint 2\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">CRM ID</span> : 05545SD585\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Account Owner </span> : Adam\r\n                                            Smith\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Main Contact</span> : Ben Jacobs\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"mt-6 mb-1 flex align-items-center justify-content-between gap-3\">\r\n                                <button type=\"button\" icon=\"pi pi-check\"\r\n                                    class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                                    <i class=\"material-symbols-rounded text-primary\">call</i>\r\n                                </button>\r\n                                <button type=\"button\" icon=\"pi pi-check\"\r\n                                    class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                                    <i class=\"material-symbols-rounded text-primary\">location_on</i>\r\n                                </button>\r\n                                <button type=\"button\" icon=\"pi pi-check\"\r\n                                    class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                                    <i class=\"material-symbols-rounded text-primary\">email</i>\r\n                                </button>\r\n                                <button type=\"button\" icon=\"pi pi-check\"\r\n                                    class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                                    <i class=\"material-symbols-rounded text-primary\">language</i>\r\n                                </button>\r\n                                <button type=\"button\" icon=\"pi pi-check\"\r\n                                    class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                                    <i class=\"material-symbols-rounded text-primary\">edit_square</i>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">3030 Warrenville Rd, Suite #610, Lisle, IL, United States\r\n                                        of America, USA 60532.</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{accountDetails?.phone || '-'}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span class=\"flex-1\">{{accountDetails?.phone || '-'}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{accountDetails?.email || '-'}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">www.asardigital.com</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICajBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAqE;IACjED,EAAA,CAAAU,UAAA,IAAAC,2DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANyBH,EAAA,CAAAI,UAAA,+BAA8B;;;ADJpF,OAAM,MAAOQ,uBAAuB;EAUlCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B;IAF9B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAZhB,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;IACnC,KAAAoB,cAAc,GAAQ,IAAI;IAG1B,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAa,EAAE;IACzB,KAAAC,KAAK,GAAW,EAAE;IACzB,KAAAC,UAAU,GAAW,EAAE;IA2GvB,KAAAC,eAAe,GAAG,KAAK;EArGnB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEhB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,EACrD;MAAEG,KAAK,EAAE,GAAG,IAAI,CAACa,UAAU;IAAE,CAAE,CAChC;IACD,IAAI,CAACI,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAErB,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACe,KAAK,GAAG,IAAI,CAACP,KAAK,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAACC,aAAa,CAAC,IAAI,CAACV,KAAK,CAAC;IAC9B,IAAI,CAACD,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACL,KAAK,CAACe,QAAQ,CAChBG,IAAI,CAACjC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAACJ,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIK,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;EACN;EAEAJ,aAAaA,CAACV,KAAa;IACzB,IAAI,CAACF,KAAK,GAAG,CACX;MACEV,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,EACD;MACEZ,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE,kBAAkBe,KAAK;KACpC,CACF;EACH;EAEQe,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAACnB,cAAc,CAChBqB,cAAc,CAACF,SAAS,CAAC,CACzBH,IAAI,CAACjC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACTK,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrB,cAAc,GAAGqB,QAAQ,EAAEC,IAAI,IAAI,IAAI;QAC5C,IAAI,CAACxB,cAAc,CAACyB,cAAc,CAACH,IAAI,CAAC,IAAI,CAACpB,cAAc,CAAC;MAC9D,CAAC;MACDwB,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAIAC,aAAaA,CAAA;IACX,IAAI,CAACvB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;;;uBAvHWX,uBAAuB,EAAAZ,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAlD,EAAA,CAAA+C,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAvBxC,uBAAuB;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5B3D,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAA6D,SAAA,sBAA+F;UAKvG7D,EAJI,CAAAG,YAAA,EAAM,EAIJ;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACI;UAC3BD,EAAA,CAAAU,UAAA,IAAAoD,6CAAA,wBAAqE;UAQ7E9D,EADI,CAAAG,YAAA,EAAY,EACV;UASsBH,EAR5B,CAAAC,cAAA,aAAqD,aACjB,eAC+D,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,iCACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACZ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,sBAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,sBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,sBAEpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,sBAClD;UAGZF,EAHY,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ;UAIEH,EAHR,CAAAC,cAAA,eAA6E,kBAEuC,aAC3D;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACpD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC3D;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACrD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EACxD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC3D,EACP,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,wFACK;UAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAChC;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAgC;UACzDF,EADyD,CAAAG,YAAA,EAAO,EAC3D;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAgC;UACzDF,EADyD,CAAAG,YAAA,EAAO,EAC3D;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAgC;UACzDF,EADyD,CAAAG,YAAA,EAAO,EAC3D;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAK5DF,EAL4D,CAAAG,YAAA,EAAO,EAC9C,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAiD,oBAIyB;UAAlED,EAAA,CAAA+D,UAAA,mBAAAC,4DAAA;YAAA,OAASJ,GAAA,CAAAd,aAAA,EAAe;UAAA,EAAC;UAH7B9C,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAA6D,SAAA,qBAA+B;UAKnD7D,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;;;UAvHoBH,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAwD,GAAA,CAAAnC,eAAA,CAAyB,SAAAmC,GAAA,CAAAlC,IAAA,CAAc,uCAAuC;UASjF1B,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UACEJ,EAAA,CAAAO,SAAA,EAAQ;UAARP,EAAA,CAAAI,UAAA,YAAAwD,GAAA,CAAAzC,KAAA,CAAQ;UAWgBnB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAiE,WAAA,iBAAAL,GAAA,CAAArC,eAAA,CAAsC;UAiEjDvB,EAAA,CAAAO,SAAA,IAAgC;UAAhCP,EAAA,CAAAQ,iBAAA,EAAAoD,GAAA,CAAA1C,cAAA,kBAAA0C,GAAA,CAAA1C,cAAA,CAAAgD,KAAA,SAAgC;UAMhClE,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,iBAAA,EAAAoD,GAAA,CAAA1C,cAAA,kBAAA0C,GAAA,CAAA1C,cAAA,CAAAgD,KAAA,SAAgC;UAKhClE,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,iBAAA,EAAAoD,GAAA,CAAA1C,cAAA,kBAAA0C,GAAA,CAAA1C,cAAA,CAAAiD,KAAA,SAAgC;UAgBvCnE,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAiE,WAAA,gBAAAL,GAAA,CAAArC,eAAA,CAAqC;UAF/DvB,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}