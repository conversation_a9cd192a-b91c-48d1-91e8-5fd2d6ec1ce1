{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let UsersService = /*#__PURE__*/(() => {\n  class UsersService {\n    constructor(http) {\n      this.http = http;\n      this.userSubject = new BehaviorSubject(null);\n      this.user = this.userSubject.asObservable();\n    }\n    createUser(userData) {\n      return this.http.post(`${CMS_APIContstant.USER_DETAILS}`, userData);\n    }\n    getUsers(page, pageSize, sortField, sortOrder, searchTerm) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][email][$containsi]', searchTerm).set('filters[$or][1][username][$containsi]', searchTerm).set('filters[$or][2][firstname][$containsi]', searchTerm).set('filters[$or][3][lastname][$containsi]', searchTerm);\n      }\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?populate=*`, {\n        params\n      });\n    }\n    getUserRoles() {\n      return this.http.get(`${CMS_APIContstant.USER_ROLES}`);\n    }\n    getUserForallRoles(userId) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n    }\n    getUserByID(id) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate=*`);\n    }\n    getUserByIDName(data) {\n      return this.http.get(`${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`);\n    }\n    getAllCustomers() {\n      return this.http.get(`${CMS_APIContstant.CUSTOMERS}`);\n    }\n    getUserByCustomer(id, page, pageSize, sortField, sortOrder, searchTerm) {\n      let params = new HttpParams().set('populate[business_partner][populate]', 'addresses').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][customer_id][$containsi]', searchTerm).set('filters[$or][1][customer_name][$containsi]', searchTerm).set('filters[$or][2][business_partner][phone][$containsi]', searchTerm);\n      }\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}/customers`, {\n        params\n      });\n    }\n    updateUser(userId, updatedData) {\n      return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n    }\n    updateAdminUser(userId, updatedData) {\n      return this.http.put(`${ApiConstant.ADMIN_USERS}/${userId}`, updatedData);\n    }\n    getCustomers(data) {\n      const params = new HttpParams().appendAll({\n        ...data\n      });\n      return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n        params\n      });\n    }\n    unlinkCustomers(userId) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/unlink-all-customer`);\n    }\n    linkCustomers(userId) {\n      return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/link-all-customer`);\n    }\n    static {\n      this.ɵfac = function UsersService_Factory(t) {\n        return new (t || UsersService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UsersService,\n        factory: UsersService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UsersService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}