{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let activeTask = null;\n    let lastValue = null;\n    let lastTime = null;\n    const emit = () => {\n      if (activeTask) {\n        activeTask.unsubscribe();\n        activeTask = null;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    function emitWhenIdle() {\n      const targetTime = lastTime + dueTime;\n      const now = scheduler.now();\n      if (now < targetTime) {\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n      emit();\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      lastValue = value;\n      lastTime = scheduler.now();\n      if (!activeTask) {\n        activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n        subscriber.add(activeTask);\n      }\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = activeTask = null;\n    }));\n  });\n}\n//# sourceMappingURL=debounceTime.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}