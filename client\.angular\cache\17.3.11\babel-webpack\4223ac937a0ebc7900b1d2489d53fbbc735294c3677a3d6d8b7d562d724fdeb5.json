{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { ActivitiesFormComponent } from 'src/app/store/common-form/activities-form/activities-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../account.service\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"../../../common-form/activities-form/activities-form.component\";\nimport * as i11 from \"primeng/multiselect\";\nfunction AccountActivitiesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 20);\n    i0.ɵɵlistener(\"click\", function AccountActivitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountActivitiesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, AccountActivitiesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function AccountActivitiesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"subject\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Subject \");\n    i0.ɵɵtemplate(4, AccountActivitiesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 15)(5, AccountActivitiesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountActivitiesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"slice\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r1.stripHtml(activity_r5 == null ? null : activity_r5.globalNote == null ? null : activity_r5.globalNote.note));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.globalNote == null ? null : activity_r5.globalNote.note) ? i0.ɵɵpipeBind3(4, 2, ctx_r1.stripHtml(activity_r5.globalNote.note), 0, 80) + (activity_r5.globalNote.note.length > 80 ? \"...\" : \"\") : \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.business_partner_contact == null ? null : activity_r5.business_partner_contact.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.start_date) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.start_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.end_date) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.createdAt) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.createdAt, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.business_partner_organizer == null ? null : activity_r5.business_partner_organizer.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityCategory\", activity_r5 == null ? null : activity_r5.phone_call_category) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityStatus\", activity_r5 == null ? null : activity_r5.activity_status) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityPriority\", activity_r5 == null ? null : activity_r5.priority) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5.business_partner == null ? null : activity_r5.business_partner.addresses == null ? null : activity_r5.business_partner.addresses[0] == null ? null : activity_r5.business_partner.addresses[0].phone) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", activity_r5 == null ? null : activity_r5.document_type) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 25);\n    i0.ɵɵtemplate(3, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_3_Template, 5, 6, \"ng-container\", 26)(4, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 26)(5, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 26)(6, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_6_Template, 3, 4, \"ng-container\", 26)(7, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_7_Template, 3, 4, \"ng-container\", 26)(8, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 26)(9, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 26)(10, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 26)(11, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 26)(12, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_12_Template, 2, 1, \"ng-container\", 26)(13, AccountActivitiesComponent_ng_template_10_ng_container_5_ng_container_13_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"globalNote.note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_contact.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_organizer.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_call_category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"activity_status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"priority\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"document_type\");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 21)(1, \"td\", 22)(2, \"div\", 23)(3, \"a\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, AccountActivitiesComponent_ng_template_10_ng_container_5_Template, 14, 12, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/activities/calls/\" + (activity_r5 == null ? null : activity_r5.activity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.subject) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountActivitiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \" No activities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountActivitiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \" Loading activities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AccountActivitiesComponent = /*#__PURE__*/(() => {\n  class AccountActivitiesComponent {\n    constructor(router, route, accountservice, activitiesservice) {\n      this.router = router;\n      this.route = route;\n      this.accountservice = accountservice;\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.activitiesDetails = [];\n      this.bp_id = '';\n      this.documentId = '';\n      this.dropdowns = {\n        activityCategory: [],\n        activityStatus: [],\n        activityDocumentType: [],\n        activityPriority: []\n      };\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'globalNote.note',\n        header: 'Notes'\n      }, {\n        field: 'business_partner_contact.bp_full_name',\n        header: 'Primary Contact'\n      }, {\n        field: 'start_date',\n        header: 'Start Date/Time'\n      }, {\n        field: 'end_date',\n        header: 'End Date/Time'\n      }, {\n        field: 'createdAt',\n        header: 'Created On'\n      }, {\n        field: 'business_partner_organizer.bp_full_name',\n        header: 'Organizer'\n      }, {\n        field: 'phone_call_category',\n        header: 'Category'\n      }, {\n        field: 'activity_status',\n        header: 'Status'\n      }, {\n        field: 'priority',\n        header: 'Priority'\n      }, {\n        field: 'createdAt',\n        header: 'Phone'\n      }, {\n        field: 'document_type',\n        header: 'Activity Type'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.activitiesDetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.bp_id = response.bp_id;\n          this.documentId = response.documentId;\n          if (this.bp_id) {\n            this.activitiesservice.getActivity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n              next: activityResponse => {\n                if (Array.isArray(activityResponse.data)) {\n                  const allActivities = activityResponse.data.slice(0, 10);\n                  this.activitiesDetails = allActivities.map(activity => {\n                    // Find global note from activity.notes array\n                    const globalNote = activity.notes?.find(note => note.is_global_note === true);\n                    return {\n                      ...activity,\n                      globalNote: globalNote || null\n                    };\n                  });\n                } else {\n                  this.activitiesDetails = [];\n                }\n              },\n              error: error => {\n                console.error('Error fetching activities:', error);\n              }\n            });\n          }\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    openActivityDialog() {\n      this.activityDialog.showDialog('right');\n    }\n    navigateToactivityDetail(item) {\n      this.router.navigate(['detail', item?.activity_id], {\n        relativeTo: this.route,\n        state: {\n          activitydata: item,\n          moduleId: this.documentId\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountActivitiesComponent_Factory(t) {\n        return new (t || AccountActivitiesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AccountService), i0.ɵɵdirectiveInject(i3.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountActivitiesComponent,\n        selectors: [[\"app-account-activities\"]],\n        viewQuery: function AccountActivitiesComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(ActivitiesFormComponent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.activityDialog = _t.first);\n          }\n        },\n        decls: 14,\n        vars: 11,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [3, \"account_id\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"tooltipPosition\", \"top\", \"tooltipStyleClass\", \"multi-line-tooltip\", 3, \"pTooltip\"], [1, \"note-text\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\", \"pl-3\"]],\n        template: function AccountActivitiesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Activities\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function AccountActivitiesComponent_Template_p_button_click_5_listener() {\n              return ctx.openActivityDialog();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountActivitiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function AccountActivitiesComponent_Template_p_table_onColReorder_8_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(9, AccountActivitiesComponent_ng_template_9_Template, 7, 3, \"ng-template\", 8)(10, AccountActivitiesComponent_ng_template_10_Template, 6, 3, \"ng-template\", 9)(11, AccountActivitiesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, AccountActivitiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(13, \"app-activities-form\", 12);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.activitiesDetails)(\"rows\", 8)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"account_id\", ctx.bp_id);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.Tooltip, i6.PrimeTemplate, i7.Table, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i8.NgControlStatus, i8.NgModel, i9.Button, i10.ActivitiesFormComponent, i11.MultiSelect, i4.SlicePipe, i4.DatePipe],\n        styles: [\".multi-line-tooltip{white-space:normal!important;max-width:300px;word-wrap:break-word}\"]\n      });\n    }\n  }\n  return AccountActivitiesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}