{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\n;\nexport class ContactsService {\n  constructor(http) {\n    this.http = http;\n    this.contactSubject = new BehaviorSubject(null);\n    this.contact = this.contactSubject.asObservable();\n  }\n  getContacts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][email][$containsi]', searchTerm).set('filters[$or][3][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate=contact_company_func_and_depts`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function ContactsService_Factory(t) {\n      return new (t || ContactsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContactsService,\n      factory: ContactsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "ContactsService", "constructor", "http", "contactSubject", "contact", "asObservable", "getContacts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';;\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ContactsService {\r\n\r\n  public contactSubject = new BehaviorSubject<any>(null);\r\n  public contact = this.contactSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getContacts(\r\n      page: number,\r\n      pageSize: number,\r\n      sortField?: string,\r\n      sortOrder?: number,\r\n      searchTerm?: string\r\n    ): Observable<any[]> {\r\n      let params = new HttpParams()\r\n        .set('pagination[page]', page.toString())\r\n        .set('pagination[pageSize]', pageSize.toString());\r\n      if (sortField && sortOrder !== undefined) {\r\n        const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n        params = params.set('sort', `${sortField}:${order}`);\r\n      }\r\n      if (searchTerm) {\r\n        params = params\r\n          .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n          .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)\r\n          .set('filters[$or][2][email][$containsi]', searchTerm)\r\n          .set('filters[$or][3][phone][$containsi]', searchTerm);\r\n      }\r\n      return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}?populate=contact_company_func_and_depts`, {\r\n        params,\r\n      });\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAAC;AAKnE,OAAM,MAAOC,eAAe;EAK1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAM,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACPC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC1BgB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,2CAA2C,EAAEF,UAAU,CAAC,CAC5DE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;IAC1D;IACA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,QAAQ,0CAA0C,EAAE;MAClGN;KACD,CAAC;EACJ;;;uBA/BSZ,eAAe,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAftB,eAAe;MAAAuB,OAAA,EAAfvB,eAAe,CAAAwB,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}