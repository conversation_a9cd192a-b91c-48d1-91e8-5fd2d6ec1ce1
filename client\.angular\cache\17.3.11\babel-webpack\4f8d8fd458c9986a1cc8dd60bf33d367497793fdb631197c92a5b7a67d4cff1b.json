{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nexport let ProfileRoutingModule = /*#__PURE__*/(() => {\n  class ProfileRoutingModule {\n    static {\n      this.ɵfac = function ProfileRoutingModule_Factory(t) {\n        return new (t || ProfileRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ProfileRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ProfileRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}