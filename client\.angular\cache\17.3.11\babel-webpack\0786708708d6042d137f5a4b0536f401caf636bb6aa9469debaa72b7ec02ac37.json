{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AiInsightsComponent } from './ai-insights.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AiInsightsComponent\n}];\nexport let AiInsightsRoutingModule = /*#__PURE__*/(() => {\n  class AiInsightsRoutingModule {\n    static {\n      this.ɵfac = function AiInsightsRoutingModule_Factory(t) {\n        return new (t || AiInsightsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AiInsightsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AiInsightsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}