{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nexport let SessionComponent = /*#__PURE__*/(() => {\n  class SessionComponent {\n    constructor(primengConfig, renderer) {\n      this.primengConfig = primengConfig;\n      this.renderer = renderer;\n    }\n    ngOnInit() {\n      // Inject theme \n      const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n      const link = this.renderer.createElement('link');\n      this.renderer.setAttribute(link, 'id', 'theme-link');\n      this.renderer.setAttribute(link, 'rel', 'stylesheet');\n      this.renderer.setAttribute(link, 'type', 'text/css');\n      this.renderer.setAttribute(link, 'href', href);\n      // Append the link tag to the head of the document\n      this.renderer.appendChild(document.head, link);\n      this.primengConfig.ripple = true; //enables core ripple functionality\n    }\n    ngOnDestroy() {\n      // Find and remove the link tag when the component is destroyed\n      const link = document.getElementById('theme-link');\n      if (link) {\n        link.remove();\n      }\n    }\n    static {\n      this.ɵfac = function SessionComponent_Factory(t) {\n        return new (t || SessionComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SessionComponent,\n        selectors: [[\"ng-component\"]],\n        decls: 1,\n        vars: 0,\n        template: function SessionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [i2.RouterOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return SessionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}