import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrganizationalComponent } from './organizational.component';
import { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';
import { OrganizationalRoutingModule } from './organizational-routing.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { SharedModule } from 'src/app/shared/shared.module';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { CheckboxModule } from 'primeng/checkbox';
import { OrganizationDetailsComponent } from './organization-details/organization-details.component';
import { GeneralComponent } from './organization-details/general/general.component';
import { FunctionsComponent } from './organization-details/functions/functions.component';
import { EmployeesComponent } from './organization-details/employees/employees.component';
import { TabViewModule } from 'primeng/tabview';
import { ConfirmationService, MessageService } from 'primeng/api';
import { MultiSelectModule } from 'primeng/multiselect';
import { DialogModule } from 'primeng/dialog';


@NgModule({
  declarations: [
    OrganizationalComponent,
    AddOrgUnitComponent,
    OrganizationDetailsComponent,
    GeneralComponent,
    FunctionsComponent,
    EmployeesComponent,
  ],
  imports: [
    CommonModule,
    OrganizationalRoutingModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    CalendarModule,
    ToggleButtonModule,
    DropdownModule,
    TabViewModule,
    ToastModule,
    ConfirmDialogModule,
    InputTextModule,
    BreadcrumbModule,
    TableModule,
    ButtonModule,
    CheckboxModule,
    SharedModule,
    MultiSelectModule,
    DialogModule
  ],
  providers: [MessageService, ConfirmationService],
})
export class OrganizationalModule {}
