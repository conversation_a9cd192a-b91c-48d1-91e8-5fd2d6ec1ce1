{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 0,\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"dashboard works!\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}