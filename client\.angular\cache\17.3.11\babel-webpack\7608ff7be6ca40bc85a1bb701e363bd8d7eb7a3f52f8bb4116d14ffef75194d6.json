{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/backoffice/layout/service/app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/inputtext\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/ripple\";\nimport * as i6 from \"primeng/styleclass\";\nimport * as i7 from \"./app.breadcrumb.component\";\nimport * as i8 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = a0 => ({\n  \"topbar-search-active\": a0\n});\nexport let AppTopbarComponent = /*#__PURE__*/(() => {\n  class AppTopbarComponent {\n    constructor(layoutService, el) {\n      this.layoutService = layoutService;\n      this.el = el;\n      this.searchActive = false;\n    }\n    activateSearch() {\n      this.searchActive = true;\n      setTimeout(() => {\n        this.searchInput.nativeElement.focus();\n      }, 100);\n    }\n    deactivateSearch() {\n      this.searchActive = false;\n    }\n    onMenuButtonClick() {\n      this.layoutService.onMenuToggle();\n    }\n    onConfigButtonClick() {\n      this.layoutService.showConfigSidebar();\n    }\n    onSidebarButtonClick() {\n      this.layoutService.showSidebar();\n    }\n    static {\n      this.ɵfac = function AppTopbarComponent_Factory(t) {\n        return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppTopbarComponent,\n        selectors: [[\"app-topbar\"]],\n        viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(AppSidebarComponent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n          }\n        },\n        decls: 47,\n        vars: 4,\n        consts: [[\"menubutton\", \"\"], [\"searchinput\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"topbar-breadcrumb\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [1, \"hidden\", \"lg:block\"], [1, \"topbar-search\", 3, \"ngClass\"], [\"pButton\", \"\", \"icon\", \"pi pi-search\", \"type\", \"button\", 1, \"topbar-searchbutton\", \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"search-input-wrapper\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"blur\", \"keydown.escape\"], [1, \"pi\", \"pi-search\"], [1, \"profile-item\", \"topbar-item\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bell\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-comment\", 1, \"p-button-text\", \"p-button-secondary\", \"relative\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\"], [1, \"ml-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cog\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"p-4\", \"w-15rem\", \"z-5\", \"ng-hidden\", \"border-round\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-3\"], [\"href\", \"#\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\"], [1, \"pi\", \"pi-fw\", \"pi-lock\", \"mr-2\"], [1, \"pi\", \"pi-fw\", \"pi-cog\", \"mr-2\"], [\"role\", \"menuitem\", 1, \"m-0\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"], [1, \"right-panel-button\", \"relative\", \"hidden\", \"lg:block\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Today\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"hidden\", \"md:block\", \"font-normal\", 2, \"width\", \"5.7rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"block\", \"md:hidden\", 3, \"click\"]],\n        template: function AppTopbarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"button\", 5, 0);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onMenuButtonClick());\n            });\n            i0.ɵɵelement(4, \"i\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"app-breadcrumb\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 8);\n            i0.ɵɵelement(7, \"app-sidebar\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 9)(9, \"ul\", 10)(10, \"li\", 11)(11, \"div\", 12)(12, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.activateSearch());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 14)(14, \"span\", 15)(15, \"input\", 16, 1);\n            i0.ɵɵlistener(\"blur\", function AppTopbarComponent_Template_input_blur_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deactivateSearch());\n            })(\"keydown.escape\", function AppTopbarComponent_Template_input_keydown_escape_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deactivateSearch());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"i\", 17);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(18, \"li\", 18);\n            i0.ɵɵelement(19, \"button\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"li\", 18);\n            i0.ɵɵelement(21, \"button\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"li\", 21)(23, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_23_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onConfigButtonClick());\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"li\", 18, 2)(26, \"a\", 23);\n            i0.ɵɵelement(27, \"i\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"ul\", 25)(29, \"li\", 26)(30, \"a\", 27);\n            i0.ɵɵelement(31, \"i\", 28);\n            i0.ɵɵelementStart(32, \"span\");\n            i0.ɵɵtext(33, \"Privacy\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(34, \"li\", 26)(35, \"a\", 27);\n            i0.ɵɵelement(36, \"i\", 29);\n            i0.ɵɵelementStart(37, \"span\");\n            i0.ɵɵtext(38, \"Settings\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(39, \"li\", 30)(40, \"a\", 27);\n            i0.ɵɵelement(41, \"i\", 31);\n            i0.ɵɵelementStart(42, \"span\");\n            i0.ɵɵtext(43, \"Logout\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(44, \"li\", 32)(45, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_46_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n            });\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c2, ctx.searchActive));\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n          }\n        },\n        dependencies: [i2.NgClass, i3.InputText, i4.ButtonDirective, i5.Ripple, i6.StyleClass, i7.AppBreadcrumbComponent, i8.AppSidebarComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppTopbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}