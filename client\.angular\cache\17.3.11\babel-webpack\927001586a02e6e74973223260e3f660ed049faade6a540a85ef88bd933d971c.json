{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet ServiceTicketsListingComponent = class ServiceTicketsListingComponent {};\nServiceTicketsListingComponent = __decorate([Component({\n  selector: 'app-service-tickets-overview',\n  templateUrl: './service-tickets-overview.component.html',\n  styleUrl: './service-tickets-overview.component.scss'\n})], ServiceTicketsListingComponent);\nexport { ServiceTicketsListingComponent };", "map": {"version": 3, "names": ["Component", "ServiceTicketsListingComponent", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets-listing\\service-tickets-listing.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-service-tickets-overview',\r\n  templateUrl: './service-tickets-overview.component.html',\r\n  styleUrl: './service-tickets-overview.component.scss'\r\n})\r\nexport class ServiceTicketsListingComponent {\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B,GAE1C;AAFYA,8BAA8B,GAAAC,UAAA,EAL1CF,SAAS,CAAC;EACTG,QAAQ,EAAE,8BAA8B;EACxCC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE;CACX,CAAC,C,EACWJ,8BAA8B,CAE1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}