{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"../../../../shared/initials.pipe\";\nfunction SalesCallDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction SalesCallDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 32);\n    i0.ɵɵtemplate(1, SalesCallDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let SalesCallDetailsComponent = /*#__PURE__*/(() => {\n  class SalesCallDetailsComponent {\n    constructor(router, route, activitiesservice, messageservice, confirmationservice) {\n      this.router = router;\n      this.route = route;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.activityDetails = null;\n      this.sidebarDetails = null;\n      this.items = [];\n      this.id = '';\n      this.partner_role = '';\n      this.breadcrumbitems = [];\n      this.activeItem = null;\n      this.isSidebarHidden = false;\n      this.Actions = [];\n      this.activeIndex = 0;\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const activityId = params.get('id');\n        if (activityId) {\n          this.loadActivityData(activityId);\n        }\n      });\n      // Listen for route changes to keep active tab in sync\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n      this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        const partner_role = response?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n        this.partner_role = partner_role?.bp_full_name || null;\n        this.activityDetails = response || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.business_partner?.addresses || []);\n      });\n    }\n    formatSidebarDetails(addresses) {\n      return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n        ...address,\n        address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n        email_address: address?.emails?.[0]?.email_address || '-',\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n        website_url: address?.home_page_urls?.[0]?.website_url || '-'\n      }));\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `/store/activities/calls/${id}/overview`\n      },\n      // {\n      //   label: 'Contacts',\n      //   routerLink: `/store/activities/calls/${id}/contacts`,\n      // },\n      // {\n      //   label: 'Sales Team',\n      //   routerLink: `/store/activities/calls/${id}/sales-team`,\n      // },\n      // {\n      //   label: 'AI Insights',\n      //   routerLink: `/store/activities/calls/${id}/ai-insights`,\n      // },\n      // {\n      //   label: 'Organization Data',\n      //   routerLink: `/store/activities/calls/${id}/organization-data`,\n      // },\n      {\n        label: 'Attachments',\n        routerLink: `/store/activities/calls/${id}/attachments`\n      }, {\n        label: 'Follow Up',\n        routerLink: `/store/activities/calls/${id}/follow-items`\n      },\n      // {\n      //   label: 'Related Items',\n      //   routerLink: `/store/activities/calls/${id}/related-items`,\n      // },\n      {\n        label: 'Involved Parties',\n        routerLink: `/store/activities/calls/${id}/involved-parties`\n      }\n      // {\n      //   label: 'Notes',\n      //   routerLink: `/store/activities/calls/${id}/notes`,\n      // },\n      ];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Sales Call',\n        routerLink: ['/store/activities/calls']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    loadActivityData(activityId) {\n      this.activitiesservice.getActivityByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.activityDetails = response?.data[0] || null;\n          console.log(this.activityDetails.activity_status);\n          switch (this.activityDetails.activity_status) {\n            case '3':\n              this.Actions = [{\n                name: 'Set as Canceled',\n                code: 'SCA',\n                disabled: false\n              }];\n              break;\n            case '4':\n              this.Actions = [{\n                name: 'Set as Complete',\n                code: 'SCO',\n                disabled: true\n              }, {\n                name: 'Set as Canceled',\n                code: 'SCA',\n                disabled: true\n              }];\n              break;\n            default:\n              this.Actions = [{\n                name: 'Set as Complete',\n                code: 'SCO',\n                disabled: false\n              }, {\n                name: 'Set as Canceled',\n                code: 'SCA',\n                disabled: false\n              }];\n          }\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    onActionChange(event) {\n      const actionCode = event.value?.code;\n      const actionsMap = {\n        SCO: () => this.UpdateStatus(this.activityDetails.documentId, '3'),\n        SCA: () => this.UpdateStatus(this.activityDetails.documentId, '4')\n      };\n      const action = actionsMap[actionCode];\n      if (action) {\n        this.confirmationservice.confirm({\n          message: 'Are you sure you want to proceed with this action?',\n          header: 'Confirm',\n          icon: 'pi pi-exclamation-triangle',\n          accept: action\n        });\n      }\n    }\n    UpdateStatus(docid, status) {\n      const data = {\n        activity_status: status\n      };\n      this.activitiesservice.updateActivityStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Action Updated Successfully!'\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    goToBack() {\n      this.router.navigate(['/store/activities/calls']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallDetailsComponent_Factory(t) {\n        return new (t || SalesCallDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallDetailsComponent,\n        selectors: [[\"app-sales-call-details\"]],\n        decls: 77,\n        vars: 29,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function SalesCallDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function SalesCallDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n              return ctx.onActionChange($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function SalesCallDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function SalesCallDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(9, SalesCallDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n            i0.ɵɵtext(18);\n            i0.ɵɵpipe(19, \"initials\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n            i0.ɵɵtext(26, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"li\", 21)(29, \"span\", 22);\n            i0.ɵɵtext(30, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"li\", 21)(33, \"span\", 22);\n            i0.ɵɵtext(34, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(36, \"div\", 23)(37, \"ul\", 24)(38, \"li\", 25)(39, \"span\", 26)(40, \"i\", 27);\n            i0.ɵɵtext(41, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(42, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"span\", 28);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"li\", 25)(46, \"span\", 26)(47, \"i\", 27);\n            i0.ɵɵtext(48, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(49, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"span\", 28);\n            i0.ɵɵtext(51);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"li\", 25)(53, \"span\", 26)(54, \"i\", 27);\n            i0.ɵɵtext(55, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(56, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"span\", 28);\n            i0.ɵɵtext(58);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"li\", 25)(60, \"span\", 26)(61, \"i\", 27);\n            i0.ɵɵtext(62, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(63, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\", 28);\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"li\", 25)(67, \"span\", 26)(68, \"i\", 27);\n            i0.ɵɵtext(69, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(70, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"span\", 28);\n            i0.ɵɵtext(72);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(73, \"div\", 29)(74, \"p-button\", 30);\n            i0.ɵɵlistener(\"click\", function SalesCallDetailsComponent_Template_p_button_click_74_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(75, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(76, \"p-confirmDialog\", 31);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 27, ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_full_name));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" : \", (ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ((ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.contact_companies == null ? null : ctx.activityDetails.business_partner.contact_companies[0] == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.contact_companies == null ? null : ctx.activityDetails.business_partner.contact_companies[0] == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i4.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i5.NgControlStatus, i5.NgModel, i3.PrimeTemplate, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.ConfirmDialog, i11.Toast, i12.InitialsPipe]\n      });\n    }\n  }\n  return SalesCallDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}