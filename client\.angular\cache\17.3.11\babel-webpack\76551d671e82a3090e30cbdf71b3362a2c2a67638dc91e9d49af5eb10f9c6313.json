{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { AccountActivitiesComponent } from '../account/account-details/account-activities/account-activities.component';\nimport { AccountAiInsightsComponent } from '../account/account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountAttachmentsComponent } from '../account/account-details/account-attachments/account-attachments.component';\nimport { AccountContactsComponent } from '../account/account-details/account-contacts/account-contacts.component';\nimport { AccountDetailsComponent } from '../account/account-details/account-details.component';\nimport { AccountNotesComponent } from '../account/account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from '../account/account-details/account-opportunities/account-opportunities.component';\nimport { AccountOrganizationDataComponent } from '../account/account-details/account-organization-data/account-organization-data.component';\nimport { AccountOverviewComponent } from '../account/account-details/account-overview/account-overview.component';\nimport { AccountRelationshipsComponent } from '../account/account-details/account-relationships/account-relationships.component';\nimport { AccountSalesOrderDetailsComponent } from '../account/account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { AccountSalesOrdersComponent } from '../account/account-details/account-sales-orders/account-sales-orders.component';\nimport { AccountSalesQuoteDetailsComponent } from '../account/account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesQuotesComponent } from '../account/account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesTeamComponent } from '../account/account-details/account-sales-team/account-sales-team.component';\nimport { AccountTicketsComponent } from '../account/account-details/account-tickets/account-tickets.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':ticket-id',\n  component: ServiceTicketsComponent,\n  children: [{\n    path: ':id',\n    component: AccountDetailsComponent,\n    children: [{\n      path: 'overview',\n      component: AccountOverviewComponent\n    }, {\n      path: 'contacts',\n      component: AccountContactsComponent\n    }, {\n      path: 'sales-team',\n      component: AccountSalesTeamComponent\n    }, {\n      path: 'opportunities',\n      component: AccountOpportunitiesComponent\n    }, {\n      path: 'ai-insights',\n      component: AccountAiInsightsComponent\n    }, {\n      path: 'organization-data',\n      component: AccountOrganizationDataComponent\n    }, {\n      path: 'attachments',\n      component: AccountAttachmentsComponent\n    }, {\n      path: 'notes',\n      component: AccountNotesComponent\n    }, {\n      path: 'activities',\n      component: AccountActivitiesComponent\n    }, {\n      path: 'relationships',\n      component: AccountRelationshipsComponent\n    }, {\n      path: 'tickets',\n      component: AccountTicketsComponent\n    }, {\n      path: 'sales-quotes',\n      component: AccountSalesQuotesComponent\n    }, {\n      path: 'sales-quotes/:id',\n      component: AccountSalesQuoteDetailsComponent\n    }, {\n      path: 'sales-orders',\n      component: AccountSalesOrdersComponent\n    }, {\n      path: 'sales-orders/:id',\n      component: AccountSalesOrderDetailsComponent\n    }, {\n      path: '',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }, {\n      path: '**',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }]\n  }]\n}];\nexport class ServiceTicketsRoutingModule {\n  static {\n    this.ɵfac = function ServiceTicketsRoutingModule_Factory(t) {\n      return new (t || ServiceTicketsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceTicketsComponent", "AccountActivitiesComponent", "AccountAiInsightsComponent", "AccountAttachmentsComponent", "AccountContactsComponent", "AccountDetailsComponent", "AccountNotesComponent", "AccountOpportunitiesComponent", "AccountOrganizationDataComponent", "AccountOverviewComponent", "AccountRelationshipsComponent", "AccountSalesOrderDetailsComponent", "AccountSalesOrdersComponent", "AccountSalesQuoteDetailsComponent", "AccountSalesQuotesComponent", "AccountSalesTeamComponent", "AccountTicketsComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ServiceTicketsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\nimport { AccountActivitiesComponent } from '../account/account-details/account-activities/account-activities.component';\r\nimport { AccountAiInsightsComponent } from '../account/account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountAttachmentsComponent } from '../account/account-details/account-attachments/account-attachments.component';\r\nimport { AccountContactsComponent } from '../account/account-details/account-contacts/account-contacts.component';\r\nimport { AccountDetailsComponent } from '../account/account-details/account-details.component';\r\nimport { AccountNotesComponent } from '../account/account-details/account-notes/account-notes.component';\r\nimport { AccountOpportunitiesComponent } from '../account/account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountOrganizationDataComponent } from '../account/account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountOverviewComponent } from '../account/account-details/account-overview/account-overview.component';\r\nimport { AccountRelationshipsComponent } from '../account/account-details/account-relationships/account-relationships.component';\r\nimport { AccountSalesOrderDetailsComponent } from '../account/account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\r\nimport { AccountSalesOrdersComponent } from '../account/account-details/account-sales-orders/account-sales-orders.component';\r\nimport { AccountSalesQuoteDetailsComponent } from '../account/account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\r\nimport { AccountSalesQuotesComponent } from '../account/account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesTeamComponent } from '../account/account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountTicketsComponent } from '../account/account-details/account-tickets/account-tickets.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: ':ticket-id',\r\n    component: ServiceTicketsComponent,\r\n    children: [\r\n      {\r\n        path: ':id',\r\n        component: AccountDetailsComponent,\r\n        children: [\r\n          { path: 'overview', component: AccountOverviewComponent },\r\n          { path: 'contacts', component: AccountContactsComponent },\r\n          { path: 'sales-team', component: AccountSalesTeamComponent },\r\n          { path: 'opportunities', component: AccountOpportunitiesComponent },\r\n          { path: 'ai-insights', component: AccountAiInsightsComponent },\r\n          {\r\n            path: 'organization-data',\r\n            component: AccountOrganizationDataComponent,\r\n          },\r\n          { path: 'attachments', component: AccountAttachmentsComponent },\r\n          { path: 'notes', component: AccountNotesComponent },\r\n          { path: 'activities', component: AccountActivitiesComponent },\r\n          { path: 'relationships', component: AccountRelationshipsComponent },\r\n          { path: 'tickets', component: AccountTicketsComponent },\r\n          { path: 'sales-quotes', component: AccountSalesQuotesComponent },\r\n          {\r\n            path: 'sales-quotes/:id',\r\n            component: AccountSalesQuoteDetailsComponent,\r\n          },\r\n          { path: 'sales-orders', component: AccountSalesOrdersComponent },\r\n          {\r\n            path: 'sales-orders/:id',\r\n            component: AccountSalesOrderDetailsComponent,\r\n          },\r\n          { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n          { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n        ],\r\n      },\r\n    ]\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceTicketsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,0BAA0B,QAAQ,4EAA4E;AACvH,SAASC,0BAA0B,QAAQ,8EAA8E;AACzH,SAASC,2BAA2B,QAAQ,8EAA8E;AAC1H,SAASC,wBAAwB,QAAQ,wEAAwE;AACjH,SAASC,uBAAuB,QAAQ,sDAAsD;AAC9F,SAASC,qBAAqB,QAAQ,kEAAkE;AACxG,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,gCAAgC,QAAQ,0FAA0F;AAC3I,SAASC,wBAAwB,QAAQ,wEAAwE;AACjH,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,iCAAiC,QAAQ,mHAAmH;AACrK,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,iCAAiC,QAAQ,mHAAmH;AACrK,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,yBAAyB,QAAQ,4EAA4E;AACtH,SAASC,uBAAuB,QAAQ,sEAAsE;;;AAE9G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEnB,uBAAuB;EAClCoB,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEd,uBAAuB;IAClCe,QAAQ,EAAE,CACR;MAAEF,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAEV;IAAwB,CAAE,EACzD;MAAES,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAEf;IAAwB,CAAE,EACzD;MAAEc,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAEJ;IAAyB,CAAE,EAC5D;MAAEG,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAEZ;IAA6B,CAAE,EACnE;MAAEW,IAAI,EAAE,aAAa;MAAEC,SAAS,EAAEjB;IAA0B,CAAE,EAC9D;MACEgB,IAAI,EAAE,mBAAmB;MACzBC,SAAS,EAAEX;KACZ,EACD;MAAEU,IAAI,EAAE,aAAa;MAAEC,SAAS,EAAEhB;IAA2B,CAAE,EAC/D;MAAEe,IAAI,EAAE,OAAO;MAAEC,SAAS,EAAEb;IAAqB,CAAE,EACnD;MAAEY,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAElB;IAA0B,CAAE,EAC7D;MAAEiB,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAET;IAA6B,CAAE,EACnE;MAAEQ,IAAI,EAAE,SAAS;MAAEC,SAAS,EAAEH;IAAuB,CAAE,EACvD;MAAEE,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAEL;IAA2B,CAAE,EAChE;MACEI,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAEN;KACZ,EACD;MAAEK,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAEP;IAA2B,CAAE,EAChE;MACEM,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAER;KACZ,EACD;MAAEO,IAAI,EAAE,EAAE;MAAEG,UAAU,EAAE,UAAU;MAAEC,SAAS,EAAE;IAAM,CAAE,EACvD;MAAEJ,IAAI,EAAE,IAAI;MAAEG,UAAU,EAAE,UAAU;MAAEC,SAAS,EAAE;IAAM,CAAE;GAE5D;CAEJ,CACF;AAMD,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BxB,YAAY,CAACyB,QAAQ,CAACP,MAAM,CAAC,EAC7BlB,YAAY;IAAA;EAAA;;;2EAEXwB,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAF5B5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}