{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let FunctionsComponent = /*#__PURE__*/(() => {\n  class FunctionsComponent {\n    static {\n      this.ɵfac = function FunctionsComponent_Factory(t) {\n        return new (t || FunctionsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FunctionsComponent,\n        selectors: [[\"app-functions\"]],\n        decls: 4,\n        vars: 0,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"]],\n        template: function FunctionsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Functions\");\n            i0.ɵɵelementEnd()()();\n          }\n        }\n      });\n    }\n  }\n  return FunctionsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}