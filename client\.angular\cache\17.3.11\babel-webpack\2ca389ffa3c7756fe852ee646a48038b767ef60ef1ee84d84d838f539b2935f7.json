{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BackofficeComponent } from './backoffice.component';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BackofficeComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      data: {\n        breadcrumb: 'Dashboard'\n      },\n      loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule)\n    }, {\n      path: 'customer',\n      data: {\n        breadcrumb: 'Customer'\n      },\n      loadChildren: () => import('./customer/customer.module').then(m => m.CustomerModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class BackofficeRoutingModule {\n  static {\n    this.ɵfac = function BackofficeRoutingModule_Factory(t) {\n      return new (t || BackofficeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BackofficeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BackofficeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "BackofficeComponent", "AppLayoutComponent", "routes", "path", "component", "children", "data", "breadcrumb", "loadChildren", "then", "m", "DashboardModule", "CustomerModule", "redirectTo", "BackofficeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\backoffice-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { BackofficeComponent } from './backoffice.component';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: BackofficeComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            data: { breadcrumb: 'Dashboard' },\r\n            loadChildren: () =>\r\n              import('./dashboard/dashboard.module').then(\r\n                (m) => m.DashboardModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'customer',\r\n            data: { breadcrumb: 'Customer' },\r\n            loadChildren: () =>\r\n              import('./customer/customer.module').then(\r\n                (m) => m.CustomerModule\r\n              ),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class BackofficeRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,kBAAkB,QAAQ,+BAA+B;;;AAElE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,mBAAmB;EAC9BK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEH,kBAAkB;IAC7BI,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CACxCC,CAAC,IAAKA,CAAC,CAACC,eAAe;KAE7B,EACD;MACER,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACE,cAAc;KAE5B;GAEJ,EACD;IAAET,IAAI,EAAE,IAAI;IAAEU,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBf,YAAY,CAACgB,QAAQ,CAACb,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXe,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAFxBnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}