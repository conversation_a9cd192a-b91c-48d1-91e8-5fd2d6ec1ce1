{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ActivitiesService {\n  constructor(http) {\n    this.http = http;\n    this.activitySubject = new BehaviorSubject(null);\n    this.activity = this.activitySubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createActivity(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      data\n    });\n  }\n  updateActivity(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  getActivityDropdownOptions(type) {\n    const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getActivities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,start_date,end_date,category');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getSalesCall(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getActivityByID(activityId) {\n    const params = new HttpParams().set('filters[activity_id][$eq]', activityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[notes][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    }).pipe(map(response => {\n      const activityDetails = response?.data[0] || null;\n      this.activitySubject.next(activityDetails);\n      return response;\n    }));\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  static {\n    this.ɵfac = function ActivitiesService_Factory(t) {\n      return new (t || ActivitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActivitiesService,\n      factory: ActivitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ActivitiesService", "constructor", "http", "activitySubject", "activity", "asObservable", "createNote", "data", "post", "CRM_NOTE", "createActivity", "CRM_ACTIVITY", "updateActivity", "Id", "put", "updateNote", "deleteNote", "id", "delete", "getActivityDropdownOptions", "type", "params", "set", "get", "CONFIG_DATA", "getActivities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "toString", "undefined", "order", "getSalesCall", "getActivityByID", "activityId", "pipe", "response", "activityDetails", "next", "getPartners", "PARTNERS", "item", "bp_id", "bp_full_name", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ActivitiesService {\r\n  public activitySubject = new BehaviorSubject<any>(null);\r\n  public activity = this.activitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createActivity(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateActivity(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  getActivityDropdownOptions(type: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', type);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getActivities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,subject,activity_status,start_date,end_date,category'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getSalesCall(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt'\r\n      )\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getActivityByID(activityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[activity_id][$eq]', activityId)\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[notes][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const activityDetails = response?.data[0] || null;\r\n          this.activitySubject.next(activityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,iBAAiB;EAI5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,eAAe,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAO,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEd;EAEvCC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,cAAcA,CAACH,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACY,YAAY,EAAE,EAAE;MACxDJ;KACD,CAAC;EACJ;EAEAK,cAAcA,CAACC,EAAU,EAAEN,IAAS;IAClC,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACY,YAAY,IAAIE,EAAE,EAAE,EAAE;MAC7DN;KACD,CAAC;EACJ;EAEAQ,UAAUA,CAACF,EAAU,EAAEN,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACU,QAAQ,IAAII,EAAE,EAAE,EAAE;MACzDN;KACD,CAAC;EACJ;EAEAS,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACf,IAAI,CAACgB,MAAM,CAAM,GAAGnB,gBAAgB,CAACU,QAAQ,IAAIQ,EAAE,EAAE,CAAC;EACpE;EAEAE,0BAA0BA,CAACC,IAAY;IACrC,MAAMC,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC5B0B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;IAElC,OAAO,IAAI,CAAClB,IAAI,CAACqB,GAAG,CAAM,GAAGxB,gBAAgB,CAACyB,WAAW,EAAE,EAAE;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAI,aAAaA,CACXC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIT,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC1B0B,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,4EAA4E,CAC7E;IAEH,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIH,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEQ,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAAC5B,IAAI,CAACqB,GAAG,CAAQ,GAAGxB,gBAAgB,CAACY,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAa,YAAYA,CACVR,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIT,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC1B0B,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,uJAAuJ,CACxJ,CACAA,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIH,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEQ,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAAC5B,IAAI,CAACqB,GAAG,CAAQ,GAAGxB,gBAAgB,CAACY,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAc,eAAeA,CAACC,UAAkB;IAChC,MAAMf,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC5B0B,GAAG,CAAC,2BAA2B,EAAEc,UAAU,CAAC,CAC5Cd,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC,CAClEA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC;IAExC,OAAO,IAAI,CAACpB,IAAI,CACbqB,GAAG,CAAQ,GAAGxB,gBAAgB,CAACY,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC,CAC1DgB,IAAI,CACHvC,GAAG,CAAEwC,QAAa,IAAI;MACpB,MAAMC,eAAe,GAAGD,QAAQ,EAAE/B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACjD,IAAI,CAACJ,eAAe,CAACqC,IAAI,CAACD,eAAe,CAAC;MAC1C,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,WAAWA,CAACpB,MAAW;IACrB,OAAO,IAAI,CAACnB,IAAI,CAACqB,GAAG,CAAM,GAAGxB,gBAAgB,CAAC2C,QAAQ,EAAE,EAAE;MAAErB;IAAM,CAAE,CAAC,CAACgB,IAAI,CACxEvC,GAAG,CAAEwC,QAAQ,IACX,CAACA,QAAQ,EAAE/B,IAAI,IAAI,EAAE,EAAET,GAAG,CAAE6C,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;;;uBA1IW7C,iBAAiB,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBjD,iBAAiB;MAAAkD,OAAA,EAAjBlD,iBAAiB,CAAAmD,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}