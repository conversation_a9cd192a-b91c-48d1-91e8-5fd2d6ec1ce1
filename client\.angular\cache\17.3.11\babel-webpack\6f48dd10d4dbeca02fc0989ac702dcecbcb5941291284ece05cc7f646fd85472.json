{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ServiceTicketsRoutingModule } from './service-tickets-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { MessageService } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountSharedModule } from '../account/account-shared.module';\nimport * as i0 from \"@angular/core\";\nexport let ServiceTicketsModule = /*#__PURE__*/(() => {\n  class ServiceTicketsModule {\n    static {\n      this.ɵfac = function ServiceTicketsModule_Factory(t) {\n        return new (t || ServiceTicketsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ServiceTicketsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService],\n        imports: [CommonModule, AccountSharedModule, ReactiveFormsModule, NgSelectModule, ServiceTicketsRoutingModule, BreadcrumbModule, ConfirmDialogModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, ToastModule, InputTextModule]\n      });\n    }\n  }\n  return ServiceTicketsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}