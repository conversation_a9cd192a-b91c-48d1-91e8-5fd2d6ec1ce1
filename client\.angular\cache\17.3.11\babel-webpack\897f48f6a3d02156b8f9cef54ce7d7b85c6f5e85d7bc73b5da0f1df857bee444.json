{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"../../../../shared/initials.pipe\";\nfunction SalesCallDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction SalesCallDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(1, SalesCallDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class SalesCallDetailsComponent {\n  constructor(router, route, activitiesservice, messageservice, confirmationservice) {\n    this.router = router;\n    this.route = route;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.activityDetails = null;\n    this.sidebarDetails = null;\n    this.items = [];\n    this.id = '';\n    this.partner_role = '';\n    this.breadcrumbitems = [];\n    this.activeItem = null;\n    this.isSidebarHidden = false;\n    this.Actions = [];\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const activityId = params.get('id');\n      if (activityId) {\n        this.loadActivityData(activityId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const partner_role = response?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n      this.partner_role = partner_role?.bp_full_name || null;\n      this.activityDetails = response || null;\n      this.sidebarDetails = this.formatSidebarDetails(response?.business_partner?.addresses || []);\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/activities/calls/${id}/overview`\n    },\n    // {\n    //   label: 'Contacts',\n    //   routerLink: `/store/activities/calls/${id}/contacts`,\n    // },\n    // {\n    //   label: 'Sales Team',\n    //   routerLink: `/store/activities/calls/${id}/sales-team`,\n    // },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/activities/calls/${id}/ai-insights`,\n    // },\n    // {\n    //   label: 'Organization Data',\n    //   routerLink: `/store/activities/calls/${id}/organization-data`,\n    // },\n    {\n      label: 'Attachments',\n      routerLink: `/store/activities/calls/${id}/attachments`\n    }, {\n      label: 'Follow Up',\n      routerLink: `/store/activities/calls/${id}/follow-items`\n    },\n    // {\n    //   label: 'Related Items',\n    //   routerLink: `/store/activities/calls/${id}/related-items`,\n    // },\n    {\n      label: 'Involved Parties',\n      routerLink: `/store/activities/calls/${id}/involved-parties`\n    }\n    // {\n    //   label: 'Notes',\n    //   routerLink: `/store/activities/calls/${id}/notes`,\n    // },\n    ];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Sales Call',\n      routerLink: ['/store/activities/calls']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadActivityData(activityId) {\n    this.activitiesservice.getActivityByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.activityDetails = response?.data[0] || null;\n        console.log(this.activityDetails.activity_status);\n        switch (this.activityDetails.activity_status) {\n          case '3':\n            this.Actions = [{\n              name: 'Set as Canceled',\n              code: 'SCA',\n              disabled: false\n            }];\n            break;\n          case '4':\n            this.Actions = [{\n              name: 'Set as Complete',\n              code: 'SCO',\n              disabled: true\n            }, {\n              name: 'Set as Canceled',\n              code: 'SCA',\n              disabled: true\n            }];\n            break;\n          default:\n            this.Actions = [{\n              name: 'Set as Complete',\n              code: 'SCO',\n              disabled: false\n            }, {\n              name: 'Set as Canceled',\n              code: 'SCA',\n              disabled: false\n            }];\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      SCO: () => this.UpdateStatus(this.activityDetails.documentId, '3'),\n      SCA: () => this.UpdateStatus(this.activityDetails.documentId, '4')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      activity_status: status\n    };\n    this.activitiesservice.updateActivityStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/activities/calls']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallDetailsComponent_Factory(t) {\n      return new (t || SalesCallDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallDetailsComponent,\n      selectors: [[\"app-sales-call-details\"]],\n      decls: 77,\n      vars: 29,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function SalesCallDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function SalesCallDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n            return ctx.onActionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function SalesCallDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function SalesCallDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, SalesCallDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n          i0.ɵɵtext(26, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"li\", 21)(29, \"span\", 22);\n          i0.ɵɵtext(30, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"li\", 21)(33, \"span\", 22);\n          i0.ɵɵtext(34, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"ul\", 24)(38, \"li\", 25)(39, \"span\", 26)(40, \"i\", 27);\n          i0.ɵɵtext(41, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\", 28);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 25)(46, \"span\", 26)(47, \"i\", 27);\n          i0.ɵɵtext(48, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 28);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"li\", 25)(53, \"span\", 26)(54, \"i\", 27);\n          i0.ɵɵtext(55, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"span\", 28);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"li\", 25)(60, \"span\", 26)(61, \"i\", 27);\n          i0.ɵɵtext(62, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 28);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"li\", 25)(67, \"span\", 26)(68, \"i\", 27);\n          i0.ɵɵtext(69, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 28);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(73, \"div\", 29)(74, \"p-button\", 30);\n          i0.ɵɵlistener(\"click\", function SalesCallDetailsComponent_Template_p_button_click_74_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(75, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(76, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 27, ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_full_name));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.contact_companies == null ? null : ctx.activityDetails.business_partner.contact_companies[0] == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.activityDetails == null ? null : ctx.activityDetails.business_partner == null ? null : ctx.activityDetails.business_partner.contact_companies == null ? null : ctx.activityDetails.business_partner.contact_companies[0] == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.activityDetails.business_partner.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i4.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i5.NgControlStatus, i5.NgModel, i3.PrimeTemplate, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.ConfirmDialog, i11.Toast, i12.InitialsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "SalesCallDetailsComponent_p_tabPanel_9_ng_template_1_Template", "SalesCallDetailsComponent", "constructor", "router", "route", "activitiesservice", "messageservice", "confirmationservice", "unsubscribe$", "activityDetails", "sidebarDetails", "items", "id", "partner_role", "breadcrumbitems", "activeItem", "isSidebarHidden", "Actions", "activeIndex", "ngOnInit", "snapshot", "paramMap", "get", "home", "icon", "makeMenuItems", "length", "setActiveTabFromURL", "pipe", "subscribe", "params", "activityId", "loadActivityData", "events", "activity", "response", "business_partner", "customer", "partner_functions", "find", "p", "partner_function", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getActivityByID", "next", "data", "console", "log", "activity_status", "name", "code", "disabled", "error", "onActionChange", "actionCode", "value", "actionsMap", "SCO", "UpdateStatus", "documentId", "SCA", "action", "confirm", "message", "header", "accept", "docid", "status", "updateActivityStatus", "add", "severity", "detail", "setTimeout", "window", "location", "reload", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "ActivitiesService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "SalesCallDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "SalesCallDetailsComponent_Template_p_dropdown_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "ɵɵlistener", "SalesCallDetailsComponent_Template_p_dropdown_onChange_5_listener", "SalesCallDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "SalesCallDetailsComponent_Template_p_tabView_onChange_8_listener", "SalesCallDetailsComponent_p_tabPanel_9_Template", "SalesCallDetailsComponent_Template_p_button_click_74_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "bp_id", "contact_companies", "business_partner_person", "first_name", "last_name"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from '../../activities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n  disabled: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-call-details',\r\n  templateUrl: './sales-call-details.component.html',\r\n  styleUrl: './sales-call-details.component.scss',\r\n})\r\nexport class SalesCallDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public activityDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public items: MenuItem[] = [];\r\n  public home: MenuItem | any;\r\n  public id: string = '';\r\n  public partner_role: string = '';\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public isSidebarHidden = false;\r\n  public Actions: Actions[] = [];\r\n  public selectedActions: Actions | undefined;\r\n  public activeIndex: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.makeMenuItems(this.id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const activityId = params.get('id');\r\n        if (activityId) {\r\n          this.loadActivityData(activityId);\r\n        }\r\n      });\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const partner_role =\r\n          response?.business_partner?.customer?.partner_functions?.find(\r\n            (p: any) => p.partner_function === 'YI'\r\n          );\r\n        this.partner_role = partner_role?.bp_full_name || null;\r\n        this.activityDetails = response || null;\r\n        this.sidebarDetails = this.formatSidebarDetails(\r\n          response?.business_partner?.addresses || []\r\n        );\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/activities/calls/${id}/overview`,\r\n      },\r\n      // {\r\n      //   label: 'Contacts',\r\n      //   routerLink: `/store/activities/calls/${id}/contacts`,\r\n      // },\r\n      // {\r\n      //   label: 'Sales Team',\r\n      //   routerLink: `/store/activities/calls/${id}/sales-team`,\r\n      // },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/activities/calls/${id}/ai-insights`,\r\n      // },\r\n      // {\r\n      //   label: 'Organization Data',\r\n      //   routerLink: `/store/activities/calls/${id}/organization-data`,\r\n      // },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/activities/calls/${id}/attachments`,\r\n      },\r\n      {\r\n        label: 'Follow Up',\r\n        routerLink: `/store/activities/calls/${id}/follow-items`,\r\n      },\r\n      // {\r\n      //   label: 'Related Items',\r\n      //   routerLink: `/store/activities/calls/${id}/related-items`,\r\n      // },\r\n      {\r\n        label: 'Involved Parties',\r\n        routerLink: `/store/activities/calls/${id}/involved-parties`,\r\n      },\r\n      // {\r\n      //   label: 'Notes',\r\n      //   routerLink: `/store/activities/calls/${id}/notes`,\r\n      // },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Sales Call', routerLink: ['/store/activities/calls'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadActivityData(activityId: string): void {\r\n    this.activitiesservice\r\n      .getActivityByID(activityId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.activityDetails = response?.data[0] || null;\r\n          console.log(this.activityDetails.activity_status);\r\n          switch (this.activityDetails.activity_status) {\r\n            case '3':\r\n              this.Actions = [\r\n                { name: 'Set as Canceled', code: 'SCA', disabled: false },\r\n              ];\r\n              break;\r\n            case '4':\r\n              this.Actions = [\r\n                { name: 'Set as Complete', code: 'SCO', disabled: true },\r\n                { name: 'Set as Canceled', code: 'SCA', disabled: true },\r\n              ];\r\n              break;\r\n            default:\r\n              this.Actions = [\r\n                { name: 'Set as Complete', code: 'SCO', disabled: false },\r\n                { name: 'Set as Canceled', code: 'SCA', disabled: false },\r\n              ];\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      SCO: () => this.UpdateStatus(this.activityDetails.documentId, '3'),\r\n      SCA: () => this.UpdateStatus(this.activityDetails.documentId, '4'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      activity_status: status,\r\n    };\r\n    this.activitiesservice\r\n      .updateActivityStatus(docid, data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Action Updated Successfully!',\r\n          });\r\n\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 1000);\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/activities/calls']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange($event)\"\r\n            optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full surface-card border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">{{\r\n                                        activityDetails?.business_partner?.bp_full_name | initials }}</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{activityDetails?.business_partner?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">CRM ID</span> :\r\n                                            {{activityDetails?.business_partner?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Account Owner </span> :\r\n                                            {{partner_role || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Main Contact</span> : {{\r\n                                            (activityDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (activityDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;ICajBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADInG,OAAM,MAAOQ,yBAAyB;EAepCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAwC;IAJxC,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAnBrB,KAAAC,YAAY,GAAG,IAAIrB,OAAO,EAAQ;IACnC,KAAAsB,eAAe,GAAQ,IAAI;IAC3B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,KAAK,GAAe,EAAE;IAEtB,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,UAAU,GAAoB,IAAI;IAClC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAc,EAAE;IAEvB,KAAAC,WAAW,GAAW,CAAC;EAQ3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACP,EAAE,GAAG,IAAI,CAACR,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE7B,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAAC8B,aAAa,CAAC,IAAI,CAACb,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACD,KAAK,CAACe,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACX,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACgB,mBAAmB,EAAE;IAE1B,IAAI,CAACvB,KAAK,CAACiB,QAAQ,CAChBO,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCqB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,UAAU,GAAGD,MAAM,CAACR,GAAG,CAAC,IAAI,CAAC;MACnC,IAAIS,UAAU,EAAE;QACd,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC;IACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC5B,MAAM,CAAC8B,MAAM,CAACL,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAACqB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACtB,iBAAiB,CAAC6B,QAAQ,CAC5BN,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCqB,SAAS,CAAEM,QAAa,IAAI;MAC3B,MAAMtB,YAAY,GAChBsB,QAAQ,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC1DC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;MACH,IAAI,CAAC5B,YAAY,GAAGA,YAAY,EAAE6B,YAAY,IAAI,IAAI;MACtD,IAAI,CAACjC,eAAe,GAAG0B,QAAQ,IAAI,IAAI;MACvC,IAAI,CAACzB,cAAc,GAAG,IAAI,CAACiC,oBAAoB,CAC7CR,QAAQ,EAAEC,gBAAgB,EAAEQ,SAAS,IAAI,EAAE,CAC5C;IACH,CAAC,CAAC;EACN;EAEQD,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAvC,aAAaA,CAACb,EAAU;IACtB,IAAI,CAACD,KAAK,GAAG,CACX;MACEb,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,2BAA2BiB,EAAE;KAC1C;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEd,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,2BAA2BiB,EAAE;KAC1C,EACD;MACEd,KAAK,EAAE,WAAW;MAClBH,UAAU,EAAE,2BAA2BiB,EAAE;KAC1C;IACD;IACA;IACA;IACA;IACA;MACEd,KAAK,EAAE,kBAAkB;MACzBH,UAAU,EAAE,2BAA2BiB,EAAE;;IAE3C;IACA;IACA;IACA;IAAA,CACD;EACH;EAEAe,mBAAmBA,CAAA;IACjB,MAAMuC,QAAQ,GAAG,IAAI,CAAC/D,MAAM,CAACgE,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAAC3D,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAM6C,UAAU,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAAC9E,UAAU,CAAC+E,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAAClD,WAAW,GAAGqD,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACxD,UAAU,GAAG,IAAI,CAACJ,KAAK,CAAC,IAAI,CAACO,WAAW,CAAC,IAAI,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACgE,gBAAgB,CAAC,IAAI,CAAC5D,UAAU,EAAEjB,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA6E,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC9D,eAAe,GAAG,CACrB;MAAEhB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,CAAC,yBAAyB;IAAC,CAAE,EAChE;MAAEG,KAAK,EAAE8E,SAAS;MAAEjF,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAkF,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACnE,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACR,WAAW,GAAG4D,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACrE,KAAK,CAAC,IAAI,CAACO,WAAW,CAAC;IAEhD,IAAI8D,WAAW,EAAErF,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAAC8E,aAAa,CAACD,WAAW,CAACrF,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQqC,gBAAgBA,CAACD,UAAkB;IACzC,IAAI,CAAC1B,iBAAiB,CACnB6E,eAAe,CAACnD,UAAU,CAAC,CAC3BH,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCqB,SAAS,CAAC;MACTsD,IAAI,EAAGhD,QAAa,IAAI;QACtB,IAAI,CAAC1B,eAAe,GAAG0B,QAAQ,EAAEiD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;QAChDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7E,eAAe,CAAC8E,eAAe,CAAC;QACjD,QAAQ,IAAI,CAAC9E,eAAe,CAAC8E,eAAe;UAC1C,KAAK,GAAG;YACN,IAAI,CAACtE,OAAO,GAAG,CACb;cAAEuE,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAK,CAAE,CAC1D;YACD;UACF,KAAK,GAAG;YACN,IAAI,CAACzE,OAAO,GAAG,CACb;cAAEuE,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAI,CAAE,EACxD;cAAEF,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAI,CAAE,CACzD;YACD;UACF;YACE,IAAI,CAACzE,OAAO,GAAG,CACb;cAAEuE,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAK,CAAE,EACzD;cAAEF,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAK,CAAE,CAC1D;QACL;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBN,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAC,cAAcA,CAACd,KAAU;IACvB,MAAMe,UAAU,GAAGf,KAAK,CAACgB,KAAK,EAAEL,IAAI;IAEpC,MAAMM,UAAU,GAAkC;MAChDC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAACxF,eAAe,CAACyF,UAAU,EAAE,GAAG,CAAC;MAClEC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACF,YAAY,CAAC,IAAI,CAACxF,eAAe,CAACyF,UAAU,EAAE,GAAG;KAClE;IAED,MAAME,MAAM,GAAGL,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIO,MAAM,EAAE;MACV,IAAI,CAAC7F,mBAAmB,CAAC8F,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjB/E,IAAI,EAAE,4BAA4B;QAClCgF,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAH,YAAYA,CAACQ,KAAU,EAAEC,MAAW;IAClC,MAAMtB,IAAI,GAAG;MACXG,eAAe,EAAEmB;KAClB;IACD,IAAI,CAACrG,iBAAiB,CACnBsG,oBAAoB,CAACF,KAAK,EAAErB,IAAI,CAAC,CACjCxD,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCqB,SAAS,CAAC;MACTsD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7E,cAAc,CAACsG,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEFC,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDvB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACrF,cAAc,CAACsG,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAK,QAAQA,CAAA;IACN,IAAI,CAAChH,MAAM,CAACiH,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACrG,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAsG,WAAWA,CAAA;IACT,IAAI,CAAC9G,YAAY,CAAC2E,IAAI,EAAE;IACxB,IAAI,CAAC3E,YAAY,CAAC+G,QAAQ,EAAE;EAC9B;;;uBA/PWtH,yBAAyB,EAAAZ,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAtI,EAAA,CAAAmI,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAxI,EAAA,CAAAmI,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA1I,EAAA,CAAAmI,iBAAA,CAAAM,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAzB/H,yBAAyB;MAAAgI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBtClJ,EAAA,CAAAoJ,SAAA,iBAAuD;UAG/CpJ,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UACtDD,EAAA,CAAAoJ,SAAA,sBAA+F;UACnGpJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAEyG;UAFzED,EAAA,CAAAqJ,gBAAA,2BAAAC,uEAAAC,MAAA;YAAAvJ,EAAA,CAAAwJ,kBAAA,CAAAL,GAAA,CAAAM,eAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAM,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACvJ,EAAA,CAAA0J,UAAA,sBAAAC,kEAAAJ,MAAA;YAAA,OAAYJ,GAAA,CAAA5C,cAAA,CAAAgD,MAAA,CAAsB;UAAA,EAAC;UAGrGvJ,EAHI,CAAAG,YAAA,EAEyG,EACvG;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAqJ,gBAAA,+BAAAO,0EAAAL,MAAA;YAAAvJ,EAAA,CAAAwJ,kBAAA,CAAAL,GAAA,CAAAtH,WAAA,EAAA0H,MAAA,MAAAJ,GAAA,CAAAtH,WAAA,GAAA0H,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACvJ,EAAA,CAAA0J,UAAA,sBAAAG,iEAAAN,MAAA;YAAA,OAAYJ,GAAA,CAAA3D,WAAA,CAAA+D,MAAA,CAAmB;UAAA,EAAC;UACzFvJ,EAAA,CAAAU,UAAA,IAAAoJ,+CAAA,wBAAoF;UAQ5F9J,EADI,CAAAG,YAAA,EAAY,EACV;UASsBH,EAR5B,CAAAC,cAAA,eAAqD,eACjB,eAC+D,eAChB,eACL,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,IACsB;;UACrEF,EADqE,CAAAG,YAAA,EAAK,EACpE;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAE1D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAElE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAKhE;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAKpFF,EALoF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA0J,UAAA,mBAAAK,8DAAA;YAAA,OAASZ,GAAA,CAAAnB,aAAA,EAAe;UAAA,EAAC;UAH7BhI,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAoJ,SAAA,qBAA+B;UAKnDpJ,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAoJ,SAAA,uBAAmC;;;UA7GJpJ,EAAA,CAAAI,UAAA,cAAa;UAIlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAA+I,GAAA,CAAA1H,eAAA,CAAyB,SAAA0H,GAAA,CAAAjH,IAAA,CAAc,uCAAuC;UAEpFlC,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAA+I,GAAA,CAAAvH,OAAA,CAAmB;UAAC5B,EAAA,CAAAgK,gBAAA,YAAAb,GAAA,CAAAM,eAAA,CAA6B;UAEzDzJ,EAAA,CAAAI,UAAA,mGAAkG;UAKvFJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAgK,gBAAA,gBAAAb,GAAA,CAAAtH,WAAA,CAA6B;UAC5B7B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAA+I,GAAA,CAAA7H,KAAA,CAAU;UAWctB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAiK,WAAA,iBAAAd,GAAA,CAAAxH,eAAA,CAAsC;UAM3B3B,EAAA,CAAAO,SAAA,GACsB;UADtBP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAkK,WAAA,SAAAf,GAAA,CAAA/H,eAAA,kBAAA+H,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,kBAAAoG,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAM,YAAA,EACsB;UAI7DrD,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAmK,kBAAA,OAAAhB,GAAA,CAAA/H,eAAA,kBAAA+H,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,kBAAAoG,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAM,YAAA,cACJ;UAG8DrD,EAAA,CAAAO,SAAA,GAE1D;UAF0DP,EAAA,CAAAmK,kBAAA,SAAAhB,GAAA,CAAA/H,eAAA,kBAAA+H,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,kBAAAoG,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAqH,KAAA,cAE1D;UAKkEpK,EAAA,CAAAO,SAAA,GAElE;UAFkEP,EAAA,CAAAmK,kBAAA,QAAAhB,GAAA,CAAA3H,YAAA,aAElE;UAEgExB,EAAA,CAAAO,SAAA,GAKhE;UALgEP,EAAA,CAAAmK,kBAAA,UAAAhB,GAAA,CAAA/H,eAAA,kBAAA+H,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,kBAAAoG,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,kBAAAlB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,qBAAAlB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAApB,GAAA,CAAA/H,eAAA,kBAAA+H,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,kBAAAoG,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,kBAAAlB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,qBAAAlB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAA/H,eAAA,CAAA2B,gBAAA,CAAAsH,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAKhE;UAWiBxK,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,iBAAA,EAAA2I,GAAA,CAAA9H,cAAA,kBAAA8H,GAAA,CAAA9H,cAAA,qBAAA8H,GAAA,CAAA9H,cAAA,IAAAoC,OAAA,SAAuC;UAMvCzD,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,iBAAA,EAAA2I,GAAA,CAAA9H,cAAA,kBAAA8H,GAAA,CAAA9H,cAAA,qBAAA8H,GAAA,CAAA9H,cAAA,IAAAoD,YAAA,SAA4C;UAO9CzE,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAQ,iBAAA,EAAA2I,GAAA,CAAA9H,cAAA,kBAAA8H,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,kBAAAlB,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,qBAAAlB,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,CAAA/G,SAAA,kBAAA4F,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,CAAA/G,SAAA,qBAAA4F,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,CAAA/G,SAAA,IAAAmB,aAAA,kBAAAyE,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,CAAA/G,SAAA,IAAAmB,aAAA,qBAAAyE,GAAA,CAAA9H,cAAA,CAAAgJ,iBAAA,IAAAC,uBAAA,CAAA/G,SAAA,IAAAmB,aAAA,IAAAD,YAAA,SACP;UAKSzE,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,iBAAA,EAAA2I,GAAA,CAAA9H,cAAA,kBAAA8H,GAAA,CAAA9H,cAAA,qBAAA8H,GAAA,CAAA9H,cAAA,IAAAkD,aAAA,SAA6C;UAM7CvE,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,iBAAA,EAAA2I,GAAA,CAAA9H,cAAA,kBAAA8H,GAAA,CAAA9H,cAAA,qBAAA8H,GAAA,CAAA9H,cAAA,IAAAsD,WAAA,SAA2C;UAUlD3E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAiK,WAAA,gBAAAd,GAAA,CAAAxH,eAAA,CAAqC;UAF/D3B,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}