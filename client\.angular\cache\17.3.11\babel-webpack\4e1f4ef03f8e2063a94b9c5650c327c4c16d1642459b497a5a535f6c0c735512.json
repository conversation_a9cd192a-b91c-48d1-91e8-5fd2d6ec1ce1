{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from 'rxjs';\nimport { stringify } from 'qs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http, ngZone) {\n    this.http = http;\n    this.ngZone = ngZone;\n    this.permissions = new BehaviorSubject([]);\n    this.sessionChannel = new BroadcastChannel('session');\n    this.logoutTriggered = false;\n    this.TokenKey = 'jwtToken';\n    this.UserDetailsKey = 'userInfo';\n    const user = this.getAuth();\n    this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : '');\n    if (user[this.UserDetailsKey]?.isAdmin && (!user[this.UserDetailsKey]?.cart || !user[this.UserDetailsKey]?.customer)) {\n      const user = this.userDetail;\n      this.getCartDetails(user[this.UserDetailsKey].documentId).subscribe({\n        next: cartres => {\n          if (cartres?.cart) {\n            const cart = cartres.cart || null;\n            this.updateAuth({\n              cart,\n              customer: cart.customer\n            });\n          }\n        }\n      });\n    }\n    this.bindUserActivityEvents();\n  }\n  checkAdminUser() {\n    const user = this.getAuth();\n    if (user && user[this.UserDetailsKey]?.documentId) {\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(tap(cartres => {\n        if (cartres) {\n          this.updateAuth({\n            userDetails: {\n              address: cartres.address,\n              email: cartres.email,\n              firstname: cartres.firstname,\n              lastname: cartres.lastname,\n              username: cartres.username\n            }\n          });\n        }\n      }));\n    } else {\n      return of(null);\n    }\n  }\n  bindUserActivityEvents() {\n    const events = ['click', 'keydown'];\n    for (let i = 0; i < events.length; i++) {\n      const element = events[i];\n      fromEvent(document, element).subscribe(data => {\n        this.setInavtivityTimer();\n        this.sessionChannel.postMessage({\n          type: 'activityFound'\n        });\n      });\n    }\n    this.sessionChannel.onmessage = event => {\n      if (event?.data?.type == 'activityFound') {\n        this.ngZone.run(() => {\n          this.setInavtivityTimer();\n        });\n      }\n      if (event?.data?.type == 'logout') {\n        this.logoutTriggered = true;\n        this.doLogout();\n      }\n    };\n    this.setInavtivityTimer();\n    this.sessionChannel.postMessage({\n      type: 'activityFound'\n    });\n  }\n  setInavtivityTimer() {\n    clearTimeout(this.timer);\n    if (!this.isLoggedIn) {\n      return;\n    }\n    this.timer = setTimeout(() => {\n      this.doLogout();\n    }, AppConstant.SESSION_TIMEOUT);\n  }\n  login(username, password, rememberMe) {\n    return this.http.post(CMS_APIContstant.SINGIN, {\n      identifier: (username || '').toLowerCase(),\n      password\n    }).pipe(tap(res => {\n      if (res) {\n        this.setAuth(res.jwt, res.user, rememberMe);\n      }\n      return res;\n    }), switchMap(res => {\n      if (res?.user) {\n        return this.getCartDetails(res.user.documentId).pipe(map(data => {\n          if (data?.cart) {\n            res.user.cart = data.cart;\n            res.user.customer = data.cart.customer;\n          }\n          this.updateAuth(res.user);\n          return res;\n        }));\n      }\n      return of(null);\n    }));\n  }\n  getCartDetails(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n  }\n  getToken() {\n    const val = this.userSubject.value;\n    return val ? val[this.TokenKey] : null;\n  }\n  get userDetail() {\n    const user = this.userSubject.value;\n    return user ? user[this.UserDetailsKey] : null;\n  }\n  get partnerFunction() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n      return user[this.UserDetailsKey].customer.partner_functions[0];\n    }\n    return {};\n  }\n  get isLoggedIn() {\n    return !!this.userSubject.value;\n  }\n  get isCustomerSelected() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer) {\n      return true;\n    }\n    return false;\n  }\n  updateAuth(user) {\n    const auth = this.getAuth();\n    if (user?.cart) {\n      auth[this.UserDetailsKey].cart = user?.cart;\n    }\n    if (user?.customer) {\n      auth[this.UserDetailsKey].customer = user?.customer;\n    }\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n  }\n  isRememberMeSelected() {\n    return !!localStorage.getItem(this.TokenKey);\n  }\n  doLogout() {\n    this.resetAuth();\n  }\n  resetAuth() {\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\n      this.removeAuthToken();\n      !this.logoutTriggered && this.sessionChannel.postMessage({\n        type: 'logout'\n      });\n      this.userSubject.next(null);\n      window.location.href = '#/auth/login';\n      window.location.reload();\n    });\n  }\n  getAuth() {\n    const authtoken = this.getAuthToken();\n    const userDetails = this.getUserDetails();\n    if (authtoken && this.isJsonString(userDetails)) {\n      return {\n        [this.UserDetailsKey]: JSON.parse(userDetails),\n        [this.TokenKey]: authtoken\n      };\n    }\n    return {};\n  }\n  setAuth(token, user, rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.TokenKey, token);\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    } else {\n      sessionStorage.setItem(this.TokenKey, token);\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    }\n    this.userSubject.next({\n      [this.UserDetailsKey]: user,\n      [this.TokenKey]: token\n    });\n  }\n  getAuthToken() {\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n  }\n  getUserDetails() {\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n  }\n  getUserEmail() {\n    const userData = sessionStorage.getItem('userInfo');\n    if (userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        return parsedUser.email || null;\n      } catch (error) {\n        return null;\n      }\n    }\n    return null;\n  }\n  removeAuthToken() {\n    localStorage.removeItem(this.TokenKey);\n    sessionStorage.removeItem(this.TokenKey);\n    localStorage.removeItem(this.UserDetailsKey);\n    sessionStorage.removeItem(this.UserDetailsKey);\n  }\n  isJsonString(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  getUserPermissions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const userDetails = _this.userDetail;\n      const obj = {};\n      const query = stringify(obj);\n      //TODO: Change api when available and add permissions\n      return yield lastValueFrom(_this.http.get(`${CMS_APIContstant.USER_PERMISSIONS}?${query}`).pipe(map(res => {\n        if (res?.data?.length) {\n          const data = (res?.data || []).map(permission => permission.code);\n          _this.permissions.next(data);\n          return data;\n        }\n        return [];\n      })).pipe(catchError(error => {\n        _this.permissions.next([]);\n        return error;\n      })));\n    })();\n  }\n  get getPermissions() {\n    return this.permissions?.value || [];\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AppConstant", "CMS_APIContstant", "BehaviorSubject", "catchError", "fromEvent", "lastValueFrom", "map", "of", "switchMap", "tap", "stringify", "AuthService", "constructor", "http", "ngZone", "permissions", "sessionChannel", "BroadcastChannel", "logoutTriggered", "TokenKey", "UserDetailsKey", "user", "getAuth", "userSubject", "Object", "keys", "length", "isAdmin", "cart", "customer", "userDetail", "getCartDetails", "documentId", "subscribe", "next", "cartres", "updateAuth", "bindUserActivityEvents", "checkAdminUser", "pipe", "userDetails", "address", "email", "firstname", "lastname", "username", "events", "i", "element", "document", "data", "setInavtivityTimer", "postMessage", "type", "onmessage", "event", "run", "doLogout", "clearTimeout", "timer", "isLoggedIn", "setTimeout", "SESSION_TIMEOUT", "login", "password", "rememberMe", "post", "SINGIN", "identifier", "toLowerCase", "res", "setAuth", "jwt", "userId", "get", "USER_DETAILS", "getToken", "val", "value", "partnerFunction", "partner_functions", "isCustomerSelected", "auth", "isRememberMeSelected", "localStorage", "getItem", "resetAuth", "removeAuthToken", "window", "location", "href", "reload", "authtoken", "getAuthToken", "getUserDetails", "isJsonString", "JSON", "parse", "token", "setItem", "sessionStorage", "getUserEmail", "userData", "parsedUser", "error", "removeItem", "str", "e", "getUserPermissions", "_this", "_asyncToGenerator", "obj", "query", "USER_PERMISSIONS", "permission", "code", "getPermissions", "i0", "ɵɵinject", "i1", "HttpClient", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\authentication\\auth.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AppConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  fromEvent,\r\n  lastValueFrom,\r\n  map,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from 'rxjs';\r\nimport { stringify } from 'qs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  public userSubject: BehaviorSubject<any>;\r\n  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);\r\n  private sessionChannel = new BroadcastChannel('session');\r\n  private timer: any;\r\n  private logoutTriggered = false;\r\n  public TokenKey = 'jwtToken';\r\n  public UserDetailsKey = 'userInfo';\r\n\r\n  constructor(private http: HttpClient, private ngZone: NgZone) {\r\n    const user: any = this.getAuth();\r\n    this.userSubject = new BehaviorSubject<any>(\r\n      Object.keys(user).length ? user : ''\r\n    );\r\n    if (\r\n      user[this.UserDetailsKey]?.isAdmin &&\r\n      (!user[this.UserDetailsKey]?.cart || !user[this.UserDetailsKey]?.customer)\r\n    ) {\r\n      const user = this.userDetail;\r\n      this.getCartDetails(user[this.UserDetailsKey].documentId).subscribe({\r\n        next: (cartres: any) => {\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.updateAuth({\r\n              cart,\r\n              customer: cart.customer,\r\n            });\r\n          }\r\n        },\r\n      });\r\n    }\r\n    this.bindUserActivityEvents();\r\n  }\r\n\r\n  checkAdminUser() {\r\n    const user: any = this.getAuth();\r\n    if (user && user[this.UserDetailsKey]?.documentId) {\r\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(\r\n        tap((cartres: any) => {\r\n          if (cartres) {\r\n            this.updateAuth({\r\n              userDetails: {\r\n                address: cartres.address,\r\n                email: cartres.email,\r\n                firstname: cartres.firstname,\r\n                lastname: cartres.lastname,\r\n                username: cartres.username,\r\n              }\r\n            });\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n  bindUserActivityEvents() {\r\n    const events = ['click', 'keydown'];\r\n    for (let i = 0; i < events.length; i++) {\r\n      const element = events[i];\r\n      fromEvent(document, element).subscribe((data) => {\r\n        this.setInavtivityTimer();\r\n        this.sessionChannel.postMessage({\r\n          type: 'activityFound',\r\n        });\r\n      });\r\n    }\r\n    this.sessionChannel.onmessage = (event) => {\r\n      if (event?.data?.type == 'activityFound') {\r\n        this.ngZone.run(() => {\r\n          this.setInavtivityTimer();\r\n        });\r\n      }\r\n      if (event?.data?.type == 'logout') {\r\n        this.logoutTriggered = true;\r\n        this.doLogout();\r\n      }\r\n    };\r\n    this.setInavtivityTimer();\r\n    this.sessionChannel.postMessage({\r\n      type: 'activityFound',\r\n    });\r\n  }\r\n\r\n  setInavtivityTimer() {\r\n    clearTimeout(this.timer);\r\n    if (!this.isLoggedIn) {\r\n      return;\r\n    }\r\n    this.timer = setTimeout(() => {\r\n      this.doLogout();\r\n    }, AppConstant.SESSION_TIMEOUT);\r\n  }\r\n\r\n  login(username: string, password: string, rememberMe: boolean) {\r\n    return this.http\r\n      .post<any>(CMS_APIContstant.SINGIN, {\r\n        identifier: (username || '').toLowerCase(),\r\n        password,\r\n      })\r\n      .pipe(\r\n        tap((res) => {\r\n          if (res) {\r\n            this.setAuth(res.jwt, res.user, rememberMe);\r\n          }\r\n          return res;\r\n        }),\r\n        switchMap((res) => {\r\n          if (res?.user) {\r\n            return this.getCartDetails(res.user.documentId).pipe(\r\n              map((data: any) => {\r\n                if (data?.cart) {\r\n                  res.user.cart = data.cart;\r\n                  res.user.customer = data.cart.customer;\r\n                }\r\n                this.updateAuth(res.user);\r\n                return res;\r\n              })\r\n            );\r\n          }\r\n          return of(null);\r\n        })\r\n      );\r\n  }\r\n  getCartDetails(userId: string): any {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\r\n  }\r\n\r\n  getToken() {\r\n    const val = this.userSubject.value;\r\n    return val ? val[this.TokenKey] : null;\r\n  }\r\n\r\n  get userDetail() {\r\n    const user = this.userSubject.value;\r\n    return user ? user[this.UserDetailsKey] : null;\r\n  }\r\n  get partnerFunction() {\r\n    const user = this.userSubject.value;\r\n    if (\r\n      user &&\r\n      user[this.UserDetailsKey]?.customer?.partner_functions?.length\r\n    ) {\r\n      return user[this.UserDetailsKey].customer.partner_functions[0];\r\n    }\r\n    return {};\r\n  }\r\n  get isLoggedIn(): boolean {\r\n    return !!this.userSubject.value;\r\n  }\r\n\r\n  get isCustomerSelected(): boolean {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer) {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  updateAuth(user: any) {\r\n    const auth: any = this.getAuth();\r\n    if (user?.cart) {\r\n      auth[this.UserDetailsKey].cart = user?.cart;\r\n    }\r\n    if (user?.customer) {\r\n      auth[this.UserDetailsKey].customer = user?.customer;\r\n    }\r\n    this.setAuth(\r\n      auth[this.TokenKey],\r\n      auth[this.UserDetailsKey],\r\n      this.isRememberMeSelected()\r\n    );\r\n  }\r\n\r\n  isRememberMeSelected(): boolean {\r\n    return !!localStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  doLogout() {\r\n    this.resetAuth();\r\n  }\r\n\r\n  resetAuth() {\r\n    this.http\r\n      .get(\r\n        `${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`\r\n      )\r\n      .subscribe(() => {\r\n        this.removeAuthToken();\r\n        !this.logoutTriggered &&\r\n          this.sessionChannel.postMessage({\r\n            type: 'logout',\r\n          });\r\n        this.userSubject.next(null);\r\n        window.location.href = '#/auth/login';\r\n        window.location.reload();\r\n      });\r\n  }\r\n\r\n  getAuth(): any {\r\n    const authtoken: any = this.getAuthToken();\r\n    const userDetails: any = this.getUserDetails();\r\n    if (authtoken && this.isJsonString(userDetails)) {\r\n      return {\r\n        [this.UserDetailsKey]: JSON.parse(userDetails),\r\n        [this.TokenKey]: authtoken,\r\n      };\r\n    }\r\n    return {};\r\n  }\r\n\r\n  setAuth(token: string, user: any, rememberMe: boolean) {\r\n    if (rememberMe) {\r\n      localStorage.setItem(this.TokenKey, token);\r\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    } else {\r\n      sessionStorage.setItem(this.TokenKey, token);\r\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    }\r\n    this.userSubject.next({\r\n      [this.UserDetailsKey]: user,\r\n      [this.TokenKey]: token,\r\n    });\r\n  }\r\n\r\n  getAuthToken() {\r\n    return (\r\n      localStorage.getItem(this.TokenKey) ||\r\n      sessionStorage.getItem(this.TokenKey)\r\n    );\r\n  }\r\n\r\n  getUserDetails() {\r\n    return (\r\n      localStorage.getItem(this.UserDetailsKey) ||\r\n      sessionStorage.getItem(this.UserDetailsKey)\r\n    );\r\n  }\r\n\r\n  getUserEmail(): string | null {\r\n    const userData = sessionStorage.getItem('userInfo');\r\n\r\n    if (userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        return parsedUser.email || null;\r\n      } catch (error) {\r\n        return null;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  removeAuthToken() {\r\n    localStorage.removeItem(this.TokenKey);\r\n    sessionStorage.removeItem(this.TokenKey);\r\n    localStorage.removeItem(this.UserDetailsKey);\r\n    sessionStorage.removeItem(this.UserDetailsKey);\r\n  }\r\n\r\n  isJsonString(str: any) {\r\n    try {\r\n      JSON.parse(str);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async getUserPermissions() {\r\n    const userDetails = this.userDetail;\r\n    const obj: any = {};\r\n    const query = stringify(obj);\r\n    //TODO: Change api when available and add permissions\r\n    return await lastValueFrom(\r\n      this.http\r\n        .get<any>(`${CMS_APIContstant.USER_PERMISSIONS}?${query}`)\r\n        .pipe(\r\n          map((res) => {\r\n            if (res?.data?.length) {\r\n              const data = (res?.data || []).map(\r\n                (permission: any) => permission.code\r\n              );\r\n              this.permissions.next(data);\r\n              return data;\r\n            }\r\n            return [];\r\n          })\r\n        )\r\n        .pipe(\r\n          catchError((error) => {\r\n            this.permissions.next([]);\r\n            return error;\r\n          })\r\n        )\r\n    );\r\n  }\r\n\r\n  get getPermissions(): any[] {\r\n    return this.permissions?.value || [];\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SACEC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,GAAG,EACHC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;AACb,SAASC,SAAS,QAAQ,IAAI;;;AAK9B,OAAM,MAAOC,WAAW;EAStBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP7C,KAAAC,WAAW,GAAyB,IAAIb,eAAe,CAAM,EAAE,CAAC;IAC/D,KAAAc,cAAc,GAAG,IAAIC,gBAAgB,CAAC,SAAS,CAAC;IAEhD,KAAAC,eAAe,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,UAAU;IACrB,KAAAC,cAAc,GAAG,UAAU;IAGhC,MAAMC,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,IAAIrB,eAAe,CACpCsB,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAGL,IAAI,GAAG,EAAE,CACrC;IACD,IACEA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEO,OAAO,KACjC,CAACN,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEQ,IAAI,IAAI,CAACP,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,CAAC,EAC1E;MACA,MAAMR,IAAI,GAAG,IAAI,CAACS,UAAU;MAC5B,IAAI,CAACC,cAAc,CAACV,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACY,UAAU,CAAC,CAACC,SAAS,CAAC;QAClEC,IAAI,EAAGC,OAAY,IAAI;UACrB,IAAIA,OAAO,EAAEP,IAAI,EAAE;YACjB,MAAMA,IAAI,GAAGO,OAAO,CAACP,IAAI,IAAI,IAAI;YACjC,IAAI,CAACQ,UAAU,CAAC;cACdR,IAAI;cACJC,QAAQ,EAAED,IAAI,CAACC;aAChB,CAAC;UACJ;QACF;OACD,CAAC;IACJ;IACA,IAAI,CAACQ,sBAAsB,EAAE;EAC/B;EAEAC,cAAcA,CAAA;IACZ,MAAMjB,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAID,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEY,UAAU,EAAE;MACjD,OAAO,IAAI,CAACD,cAAc,CAACV,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACY,UAAU,CAAC,CAACO,IAAI,CACnE9B,GAAG,CAAE0B,OAAY,IAAI;QACnB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACC,UAAU,CAAC;YACdI,WAAW,EAAE;cACXC,OAAO,EAAEN,OAAO,CAACM,OAAO;cACxBC,KAAK,EAAEP,OAAO,CAACO,KAAK;cACpBC,SAAS,EAAER,OAAO,CAACQ,SAAS;cAC5BC,QAAQ,EAAET,OAAO,CAACS,QAAQ;cAC1BC,QAAQ,EAAEV,OAAO,CAACU;;WAErB,CAAC;QACJ;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL,OAAOtC,EAAE,CAAC,IAAI,CAAC;IACjB;EACF;EAEA8B,sBAAsBA,CAAA;IACpB,MAAMS,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACpB,MAAM,EAAEqB,CAAC,EAAE,EAAE;MACtC,MAAMC,OAAO,GAAGF,MAAM,CAACC,CAAC,CAAC;MACzB3C,SAAS,CAAC6C,QAAQ,EAAED,OAAO,CAAC,CAACf,SAAS,CAAEiB,IAAI,IAAI;QAC9C,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAACnC,cAAc,CAACoC,WAAW,CAAC;UAC9BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACrC,cAAc,CAACsC,SAAS,GAAIC,KAAK,IAAI;MACxC,IAAIA,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,eAAe,EAAE;QACxC,IAAI,CAACvC,MAAM,CAAC0C,GAAG,CAAC,MAAK;UACnB,IAAI,CAACL,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MACA,IAAII,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,QAAQ,EAAE;QACjC,IAAI,CAACnC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACuC,QAAQ,EAAE;MACjB;IACF,CAAC;IACD,IAAI,CAACN,kBAAkB,EAAE;IACzB,IAAI,CAACnC,cAAc,CAACoC,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAF,kBAAkBA,CAAA;IAChBO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAACD,KAAK,GAAGE,UAAU,CAAC,MAAK;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAEzD,WAAW,CAAC8D,eAAe,CAAC;EACjC;EAEAC,KAAKA,CAAClB,QAAgB,EAAEmB,QAAgB,EAAEC,UAAmB;IAC3D,OAAO,IAAI,CAACpD,IAAI,CACbqD,IAAI,CAAMjE,gBAAgB,CAACkE,MAAM,EAAE;MAClCC,UAAU,EAAE,CAACvB,QAAQ,IAAI,EAAE,EAAEwB,WAAW,EAAE;MAC1CL;KACD,CAAC,CACDzB,IAAI,CACH9B,GAAG,CAAE6D,GAAG,IAAI;MACV,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,OAAO,CAACD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAACjD,IAAI,EAAE4C,UAAU,CAAC;MAC7C;MACA,OAAOK,GAAG;IACZ,CAAC,CAAC,EACF9D,SAAS,CAAE8D,GAAG,IAAI;MAChB,IAAIA,GAAG,EAAEjD,IAAI,EAAE;QACb,OAAO,IAAI,CAACU,cAAc,CAACuC,GAAG,CAACjD,IAAI,CAACW,UAAU,CAAC,CAACO,IAAI,CAClDjC,GAAG,CAAE4C,IAAS,IAAI;UAChB,IAAIA,IAAI,EAAEtB,IAAI,EAAE;YACd0C,GAAG,CAACjD,IAAI,CAACO,IAAI,GAAGsB,IAAI,CAACtB,IAAI;YACzB0C,GAAG,CAACjD,IAAI,CAACQ,QAAQ,GAAGqB,IAAI,CAACtB,IAAI,CAACC,QAAQ;UACxC;UACA,IAAI,CAACO,UAAU,CAACkC,GAAG,CAACjD,IAAI,CAAC;UACzB,OAAOiD,GAAG;QACZ,CAAC,CAAC,CACH;MACH;MACA,OAAO/D,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EACAwB,cAAcA,CAAC0C,MAAc;IAC3B,OAAO,IAAI,CAAC5D,IAAI,CAAC6D,GAAG,CAAC,GAAGzE,gBAAgB,CAAC0E,YAAY,IAAIF,MAAM,KAAK,CAAC;EACvE;EAEAG,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,IAAI,CAACtD,WAAW,CAACuD,KAAK;IAClC,OAAOD,GAAG,GAAGA,GAAG,CAAC,IAAI,CAAC1D,QAAQ,CAAC,GAAG,IAAI;EACxC;EAEA,IAAIW,UAAUA,CAAA;IACZ,MAAMT,IAAI,GAAG,IAAI,CAACE,WAAW,CAACuD,KAAK;IACnC,OAAOzD,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG,IAAI;EAChD;EACA,IAAI2D,eAAeA,CAAA;IACjB,MAAM1D,IAAI,GAAG,IAAI,CAACE,WAAW,CAACuD,KAAK;IACnC,IACEzD,IAAI,IACJA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,EAAEmD,iBAAiB,EAAEtD,MAAM,EAC9D;MACA,OAAOL,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACS,QAAQ,CAACmD,iBAAiB,CAAC,CAAC,CAAC;IAChE;IACA,OAAO,EAAE;EACX;EACA,IAAIpB,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAACrC,WAAW,CAACuD,KAAK;EACjC;EAEA,IAAIG,kBAAkBA,CAAA;IACpB,MAAM5D,IAAI,GAAG,IAAI,CAACE,WAAW,CAACuD,KAAK;IACnC,IAAIzD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAO,UAAUA,CAACf,IAAS;IAClB,MAAM6D,IAAI,GAAQ,IAAI,CAAC5D,OAAO,EAAE;IAChC,IAAID,IAAI,EAAEO,IAAI,EAAE;MACdsD,IAAI,CAAC,IAAI,CAAC9D,cAAc,CAAC,CAACQ,IAAI,GAAGP,IAAI,EAAEO,IAAI;IAC7C;IACA,IAAIP,IAAI,EAAEQ,QAAQ,EAAE;MAClBqD,IAAI,CAAC,IAAI,CAAC9D,cAAc,CAAC,CAACS,QAAQ,GAAGR,IAAI,EAAEQ,QAAQ;IACrD;IACA,IAAI,CAAC0C,OAAO,CACVW,IAAI,CAAC,IAAI,CAAC/D,QAAQ,CAAC,EACnB+D,IAAI,CAAC,IAAI,CAAC9D,cAAc,CAAC,EACzB,IAAI,CAAC+D,oBAAoB,EAAE,CAC5B;EACH;EAEAA,oBAAoBA,CAAA;IAClB,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAClE,QAAQ,CAAC;EAC9C;EAEAsC,QAAQA,CAAA;IACN,IAAI,CAAC6B,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACzE,IAAI,CACN6D,GAAG,CACF,GAAGzE,gBAAgB,CAAC0E,YAAY,IAAI,IAAI,CAAC7C,UAAU,CAACE,UAAU,SAAS,CACxE,CACAC,SAAS,CAAC,MAAK;MACd,IAAI,CAACsD,eAAe,EAAE;MACtB,CAAC,IAAI,CAACrE,eAAe,IACnB,IAAI,CAACF,cAAc,CAACoC,WAAW,CAAC;QAC9BC,IAAI,EAAE;OACP,CAAC;MACJ,IAAI,CAAC9B,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC;MAC3BsD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;IAC1B,CAAC,CAAC;EACN;EAEArE,OAAOA,CAAA;IACL,MAAMsE,SAAS,GAAQ,IAAI,CAACC,YAAY,EAAE;IAC1C,MAAMrD,WAAW,GAAQ,IAAI,CAACsD,cAAc,EAAE;IAC9C,IAAIF,SAAS,IAAI,IAAI,CAACG,YAAY,CAACvD,WAAW,CAAC,EAAE;MAC/C,OAAO;QACL,CAAC,IAAI,CAACpB,cAAc,GAAG4E,IAAI,CAACC,KAAK,CAACzD,WAAW,CAAC;QAC9C,CAAC,IAAI,CAACrB,QAAQ,GAAGyE;OAClB;IACH;IACA,OAAO,EAAE;EACX;EAEArB,OAAOA,CAAC2B,KAAa,EAAE7E,IAAS,EAAE4C,UAAmB;IACnD,IAAIA,UAAU,EAAE;MACdmB,YAAY,CAACe,OAAO,CAAC,IAAI,CAAChF,QAAQ,EAAE+E,KAAK,CAAC;MAC1Cd,YAAY,CAACe,OAAO,CAAC,IAAI,CAAC/E,cAAc,EAAE4E,IAAI,CAACtF,SAAS,CAACW,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL+E,cAAc,CAACD,OAAO,CAAC,IAAI,CAAChF,QAAQ,EAAE+E,KAAK,CAAC;MAC5CE,cAAc,CAACD,OAAO,CAAC,IAAI,CAAC/E,cAAc,EAAE4E,IAAI,CAACtF,SAAS,CAACW,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,CAACE,WAAW,CAACW,IAAI,CAAC;MACpB,CAAC,IAAI,CAACd,cAAc,GAAGC,IAAI;MAC3B,CAAC,IAAI,CAACF,QAAQ,GAAG+E;KAClB,CAAC;EACJ;EAEAL,YAAYA,CAAA;IACV,OACET,YAAY,CAACC,OAAO,CAAC,IAAI,CAAClE,QAAQ,CAAC,IACnCiF,cAAc,CAACf,OAAO,CAAC,IAAI,CAAClE,QAAQ,CAAC;EAEzC;EAEA2E,cAAcA,CAAA;IACZ,OACEV,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjE,cAAc,CAAC,IACzCgF,cAAc,CAACf,OAAO,CAAC,IAAI,CAACjE,cAAc,CAAC;EAE/C;EAEAiF,YAAYA,CAAA;IACV,MAAMC,QAAQ,GAAGF,cAAc,CAACf,OAAO,CAAC,UAAU,CAAC;IAEnD,IAAIiB,QAAQ,EAAE;MACZ,IAAI;QACF,MAAMC,UAAU,GAAGP,IAAI,CAACC,KAAK,CAACK,QAAQ,CAAC;QACvC,OAAOC,UAAU,CAAC7D,KAAK,IAAI,IAAI;MACjC,CAAC,CAAC,OAAO8D,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAEAjB,eAAeA,CAAA;IACbH,YAAY,CAACqB,UAAU,CAAC,IAAI,CAACtF,QAAQ,CAAC;IACtCiF,cAAc,CAACK,UAAU,CAAC,IAAI,CAACtF,QAAQ,CAAC;IACxCiE,YAAY,CAACqB,UAAU,CAAC,IAAI,CAACrF,cAAc,CAAC;IAC5CgF,cAAc,CAACK,UAAU,CAAC,IAAI,CAACrF,cAAc,CAAC;EAChD;EAEA2E,YAAYA,CAACW,GAAQ;IACnB,IAAI;MACFV,IAAI,CAACC,KAAK,CAACS,GAAG,CAAC;IACjB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEMC,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMtE,WAAW,GAAGqE,KAAI,CAAC/E,UAAU;MACnC,MAAMiF,GAAG,GAAQ,EAAE;MACnB,MAAMC,KAAK,GAAGtG,SAAS,CAACqG,GAAG,CAAC;MAC5B;MACA,aAAa1G,aAAa,CACxBwG,KAAI,CAAChG,IAAI,CACN6D,GAAG,CAAM,GAAGzE,gBAAgB,CAACgH,gBAAgB,IAAID,KAAK,EAAE,CAAC,CACzDzE,IAAI,CACHjC,GAAG,CAAEgE,GAAG,IAAI;QACV,IAAIA,GAAG,EAAEpB,IAAI,EAAExB,MAAM,EAAE;UACrB,MAAMwB,IAAI,GAAG,CAACoB,GAAG,EAAEpB,IAAI,IAAI,EAAE,EAAE5C,GAAG,CAC/B4G,UAAe,IAAKA,UAAU,CAACC,IAAI,CACrC;UACDN,KAAI,CAAC9F,WAAW,CAACmB,IAAI,CAACgB,IAAI,CAAC;UAC3B,OAAOA,IAAI;QACb;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH,CACAX,IAAI,CACHpC,UAAU,CAAEqG,KAAK,IAAI;QACnBK,KAAI,CAAC9F,WAAW,CAACmB,IAAI,CAAC,EAAE,CAAC;QACzB,OAAOsE,KAAK;MACd,CAAC,CAAC,CACH,CACJ;IAAC;EACJ;EAEA,IAAIY,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACrG,WAAW,EAAE+D,KAAK,IAAI,EAAE;EACtC;;;uBA7SWnE,WAAW,EAAA0G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAI,MAAA;IAAA;EAAA;;;aAAX9G,WAAW;MAAA+G,OAAA,EAAX/G,WAAW,CAAAgH,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}