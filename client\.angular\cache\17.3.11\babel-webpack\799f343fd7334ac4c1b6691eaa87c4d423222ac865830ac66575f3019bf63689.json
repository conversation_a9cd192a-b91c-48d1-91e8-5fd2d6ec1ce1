{"ast": null, "code": "'use strict';\n\nvar $defineProperty = require('es-define-property');\nvar hasPropertyDescriptors = function hasPropertyDescriptors() {\n  return !!$defineProperty;\n};\nhasPropertyDescriptors.hasArrayLengthDefineBug = function hasArrayLengthDefineBug() {\n  // node v0.6 has a bug where array lengths can be Set but not Defined\n  if (!$defineProperty) {\n    return null;\n  }\n  try {\n    return $defineProperty([], 'length', {\n      value: 1\n    }).length !== 1;\n  } catch (e) {\n    // In Firefox 4-22, defining length on an array throws an exception.\n    return true;\n  }\n};\nmodule.exports = hasPropertyDescriptors;", "map": {"version": 3, "names": ["$defineProperty", "require", "hasPropertyDescriptors", "hasArrayLengthDefineBug", "value", "length", "e", "module", "exports"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/has-property-descriptors/index.js"], "sourcesContent": ["'use strict';\n\nvar $defineProperty = require('es-define-property');\n\nvar hasPropertyDescriptors = function hasPropertyDescriptors() {\n\treturn !!$defineProperty;\n};\n\nhasPropertyDescriptors.hasArrayLengthDefineBug = function hasArrayLengthDefineBug() {\n\t// node v0.6 has a bug where array lengths can be Set but not Defined\n\tif (!$defineProperty) {\n\t\treturn null;\n\t}\n\ttry {\n\t\treturn $defineProperty([], 'length', { value: 1 }).length !== 1;\n\t} catch (e) {\n\t\t// In Firefox 4-22, defining length on an array throws an exception.\n\t\treturn true;\n\t}\n};\n\nmodule.exports = hasPropertyDescriptors;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAEnD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;EAC9D,OAAO,CAAC,CAACF,eAAe;AACzB,CAAC;AAEDE,sBAAsB,CAACC,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;EACnF;EACA,IAAI,CAACH,eAAe,EAAE;IACrB,OAAO,IAAI;EACZ;EACA,IAAI;IACH,OAAOA,eAAe,CAAC,EAAE,EAAE,QAAQ,EAAE;MAAEI,KAAK,EAAE;IAAE,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC;EAChE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACX;IACA,OAAO,IAAI;EACZ;AACD,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGN,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}