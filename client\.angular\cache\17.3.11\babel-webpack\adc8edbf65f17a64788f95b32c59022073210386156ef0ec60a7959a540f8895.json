{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/dropdown\";\nconst _c0 = () => [\"/login\"];\nexport let SignupComponent = /*#__PURE__*/(() => {\n  class SignupComponent {\n    constructor(routter) {\n      this.routter = routter;\n      this.cities = [{\n        name: \"What's was your first car?\",\n        code: \"NY\"\n      }, {\n        name: \"What was your favorite school teacher's name?\",\n        code: \"RM\"\n      }, {\n        name: 'What is your date of birth?',\n        code: 'LDN'\n      }, {\n        name: 'What’s your favorite movie?',\n        code: 'IST'\n      }, {\n        name: 'What is your astrological sign?',\n        code: 'PRS'\n      }];\n    }\n    static {\n      this.ɵfac = function SignupComponent_Factory(t) {\n        return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SignupComponent,\n        selectors: [[\"app-signup\"]],\n        decls: 109,\n        vars: 12,\n        consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"pt-6\", \"pb-6\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"max-w-15rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"max-w-30rem\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col-6\", \"mb-0\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\"], [\"type\", \"text\", \"id\", \"username\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-12\", \"md:col-12\", \"mb-0\"], [\"type\", \"email\", \"id\", \"username\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [1, \"form-group\", \"relative\"], [\"type\", \"password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\"], [\"optionLabel\", \"name\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"showClear\", \"styleClass\"], [1, \"form-footer\", \"mt-4\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\", 3, \"routerLink\"], [\"type\", \"button\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\"]],\n        template: function SignupComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n            i0.ɵɵelement(5, \"img\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6);\n            i0.ɵɵtext(7, \" Do you have an account? \");\n            i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n            i0.ɵɵtext(10, \"input\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Login \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10)(14, \"h1\", 11);\n            i0.ɵɵtext(15, \"Registration \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"p\", 12);\n            i0.ɵɵtext(17, \"Enter your details below to create an account and get started.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"label\", 15);\n            i0.ɵɵtext(21, \"First Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 14)(24, \"label\", 15);\n            i0.ɵɵtext(25, \"Last Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(26, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 14)(28, \"label\", 15);\n            i0.ɵɵtext(29, \"Phone Number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\", 15);\n            i0.ɵɵtext(33, \"Extension\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(34, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 17)(36, \"label\", 15);\n            i0.ɵɵtext(37, \"E-mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"input\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 17)(40, \"label\", 15);\n            i0.ɵɵtext(41, \"Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(42, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 14)(44, \"label\", 15);\n            i0.ɵɵtext(45, \"Country\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 14)(48, \"label\", 15);\n            i0.ɵɵtext(49, \"State\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(50, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 14)(52, \"label\", 15);\n            i0.ɵɵtext(53, \"City\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(54, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\", 14)(56, \"label\", 15);\n            i0.ɵɵtext(57, \"Zip Code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(58, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"div\", 14)(60, \"label\", 15);\n            i0.ɵɵtext(61, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"div\", 19);\n            i0.ɵɵelement(63, \"input\", 20);\n            i0.ɵɵelementStart(64, \"button\", 21)(65, \"span\", 22);\n            i0.ɵɵtext(66, \"visibility\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(67, \"div\", 14)(68, \"label\", 15);\n            i0.ɵɵtext(69, \"Retype Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"div\", 19);\n            i0.ɵɵelement(71, \"input\", 20);\n            i0.ɵɵelementStart(72, \"button\", 21)(73, \"span\", 22);\n            i0.ɵɵtext(74, \"visibility\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(75, \"div\", 17)(76, \"label\", 15);\n            i0.ɵɵtext(77, \"Security Question 1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"p-dropdown\", 23);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SignupComponent_Template_p_dropdown_ngModelChange_78_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(79, \"div\", 17)(80, \"label\", 15);\n            i0.ɵɵtext(81, \"Answer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(82, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 17)(84, \"label\", 15);\n            i0.ɵɵtext(85, \"Security Question 2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"p-dropdown\", 23);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SignupComponent_Template_p_dropdown_ngModelChange_86_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"div\", 17)(88, \"label\", 15);\n            i0.ɵɵtext(89, \"Answer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(90, \"input\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(91, \"div\", 24)(92, \"div\", 13)(93, \"div\", 14)(94, \"button\", 25);\n            i0.ɵɵtext(95, \" Go Back\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(96, \"div\", 14)(97, \"button\", 26);\n            i0.ɵɵtext(98, \" Login\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(99, \"div\", 27)(100, \"p\", 28);\n            i0.ɵɵtext(101, \"\\u00A9 2024 Consolidated Hospitality Supplies\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"ul\", 29)(103, \"li\")(104, \"a\", 30);\n            i0.ɵɵtext(105, \"Terms & Conditions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"li\")(107, \"a\", 30);\n            i0.ɵɵtext(108, \"Privacy Policy\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(10, _c0));\n            i0.ɵɵadvance(70);\n            i0.ɵɵproperty(\"options\", ctx.cities);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n            i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.cities);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n            i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c0));\n          }\n        },\n        dependencies: [i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.NgForm, i1.RouterLink, i3.Dropdown],\n        styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]{max-width:1440px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]{max-width:600px!important}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%]{right:12px;height:24px;width:24px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%]{accent-color:var(--primarycolor)}.p-inputtext[_ngcontent-%COMP%]{height:3rem;appearance:auto!important}.h-3-3rem[_ngcontent-%COMP%]{height:3.3rem}\"]\n      });\n    }\n  }\n  return SignupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}