{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OrganizationalService {\n  constructor(http) {\n    this.http = http;\n    this.organizationalSubject = new BehaviorSubject(null);\n    this.organizational = this.organizationalSubject.asObservable();\n  }\n  createOrganizational(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {\n      data\n    });\n  }\n  createFunction(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}`, {\n      data\n    });\n  }\n  createEmployee(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}`, {\n      data\n    });\n  }\n  createManager(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}`, {\n      data\n    });\n  }\n  updateOrganizational(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL}/${Id}`, {\n      data\n    });\n  }\n  updateFunction(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${Id}`, {\n      data\n    });\n  }\n  updateEmployee(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${Id}`, {\n      data\n    });\n  }\n  updateManager(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${Id}`, {\n      data\n    });\n  }\n  deleteFunction(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${id}`);\n  }\n  deleteEmployee(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${id}`);\n  }\n  deleteManager(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${id}`);\n  }\n  getOrganization(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'organisational_unit_id,name,parent_organisational_unit_id').set('populate[crm_org_unit_functions][fields][0]', 'sales_indicator').set('populate[crm_org_unit_functions][fields][1]', 'sales_organisation_indicator').set('populate[crm_org_unit_functions][fields][2]', 'reporting_line_indicator');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    } else {\n      params = params.set('sort', 'updatedAt:desc');\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][organisational_unit_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {\n      params\n    });\n  }\n  getEmployees(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  getParentUnit(params) {\n    return this.http.get(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        organisational_unit_id: item?.organisational_unit_id || '',\n        name: item?.name || ''\n      };\n    })));\n  }\n  getOrganizationByID(organizationId) {\n    const params = new HttpParams().set('filters[organisational_unit_id][$eq]', organizationId).set('populate[crm_org_unit_functions][populate]', '*').set('populate[crm_org_unit_employees][fields][0]', 'start_date').set('populate[crm_org_unit_employees][fields][1]', 'end_date').set('populate[crm_org_unit_employees][fields][2]', 'job_id').set('populate[crm_org_unit_employees][populate][business_partner][fields][0]', 'bp_full_name').set('populate[crm_org_unit_managers][fields][0]', 'start_date').set('populate[crm_org_unit_managers][fields][1]', 'end_date').set('populate[crm_org_unit_managers][populate][business_partner][fields][0]', 'bp_full_name');\n    return this.http.get(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {\n      params\n    }).pipe(map(response => {\n      const organizationDetails = response?.data[0] || null;\n      this.organizationalSubject.next(organizationDetails);\n      return response;\n    }));\n  }\n  static {\n    this.ɵfac = function OrganizationalService_Factory(t) {\n      return new (t || OrganizationalService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OrganizationalService,\n      factory: OrganizationalService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "OrganizationalService", "constructor", "http", "organizationalSubject", "organizational", "asObservable", "createOrganizational", "data", "post", "CRM_ORGANIZATIONAL", "createFunction", "CRM_ORGANIZATIONAL_FUNCTIONS", "createEmployee", "CRM_ORGANIZATIONAL_EMPLOYEES", "createManager", "CRM_ORGANIZATIONAL_MANAGERS", "updateOrganizational", "Id", "put", "updateFunction", "updateEmployee", "updateManager", "deleteFunction", "id", "delete", "deleteEmployee", "deleteManager", "getOrganization", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "getEmployees", "PARTNERS", "pipe", "response", "item", "bp_id", "bp_full_name", "getParentUnit", "organisational_unit_id", "name", "getOrganizationByID", "organizationId", "organizationDetails", "next", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OrganizationalService {\r\n  public organizationalSubject = new BehaviorSubject<any>(null);\r\n  public organizational = this.organizationalSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createOrganizational(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { data });\r\n  }\r\n\r\n  createFunction(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createEmployee(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createManager(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateOrganizational(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ORGANIZATIONAL}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateFunction(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${Id}`,\r\n      {\r\n        data,\r\n      }\r\n    );\r\n  }\r\n\r\n  updateEmployee(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${Id}`,\r\n      {\r\n        data,\r\n      }\r\n    );\r\n  }\r\n\r\n  updateManager(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${Id}`,\r\n      {\r\n        data,\r\n      }\r\n    );\r\n  }\r\n\r\n  deleteFunction(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_FUNCTIONS}/${id}`\r\n    );\r\n  }\r\n\r\n  deleteEmployee(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_EMPLOYEES}/${id}`\r\n    );\r\n  }\r\n\r\n  deleteManager(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_ORGANIZATIONAL_MANAGERS}/${id}`\r\n    );\r\n  }\r\n\r\n  getOrganization(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'organisational_unit_id,name,parent_organisational_unit_id'\r\n      )\r\n      .set('populate[crm_org_unit_functions][fields][0]', 'sales_indicator')\r\n      .set(\r\n        'populate[crm_org_unit_functions][fields][1]',\r\n        'sales_organisation_indicator'\r\n      )\r\n      .set(\r\n        'populate[crm_org_unit_functions][fields][2]',\r\n        'reporting_line_indicator'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    } else {\r\n      params = params.set('sort', 'updatedAt:desc');\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][organisational_unit_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getEmployees(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  getParentUnit(params: any) {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { params })\r\n      .pipe(\r\n        map((response) =>\r\n          (response?.data || []).map((item: any) => {\r\n            return {\r\n              organisational_unit_id: item?.organisational_unit_id || '',\r\n              name: item?.name || '',\r\n            };\r\n          })\r\n        )\r\n      );\r\n  }\r\n\r\n  getOrganizationByID(organizationId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[organisational_unit_id][$eq]', organizationId)\r\n      .set('populate[crm_org_unit_functions][populate]', '*')\r\n      .set('populate[crm_org_unit_employees][fields][0]', 'start_date')\r\n      .set('populate[crm_org_unit_employees][fields][1]', 'end_date')\r\n      .set('populate[crm_org_unit_employees][fields][2]', 'job_id')\r\n      .set(\r\n        'populate[crm_org_unit_employees][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set('populate[crm_org_unit_managers][fields][0]', 'start_date')\r\n      .set('populate[crm_org_unit_managers][fields][1]', 'end_date')\r\n      .set(\r\n        'populate[crm_org_unit_managers][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      );\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_ORGANIZATIONAL}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const organizationDetails = response?.data[0] || null;\r\n          this.organizationalSubject.next(organizationDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,qBAAqB,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IACtD,KAAAO,cAAc,GAAG,IAAI,CAACD,qBAAqB,CAACE,YAAY,EAAE;EAE1B;EAEvCC,oBAAoBA,CAACC,IAAS;IAC5B,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,kBAAkB,EAAE,EAAE;MAAEF;IAAI,CAAE,CAAC;EAC3E;EAEAG,cAAcA,CAACH,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACY,4BAA4B,EAAE,EAAE;MACxEJ;KACD,CAAC;EACJ;EAEAK,cAAcA,CAACL,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACc,4BAA4B,EAAE,EAAE;MACxEN;KACD,CAAC;EACJ;EAEAO,aAAaA,CAACP,IAAS;IACrB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACgB,2BAA2B,EAAE,EAAE;MACvER;KACD,CAAC;EACJ;EAEAS,oBAAoBA,CAACC,EAAU,EAAEV,IAAS;IACxC,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAAC,GAAGnB,gBAAgB,CAACU,kBAAkB,IAAIQ,EAAE,EAAE,EAAE;MACnEV;KACD,CAAC;EACJ;EAEAY,cAAcA,CAACF,EAAU,EAAEV,IAAS;IAClC,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAClB,GAAGnB,gBAAgB,CAACY,4BAA4B,IAAIM,EAAE,EAAE,EACxD;MACEV;KACD,CACF;EACH;EAEAa,cAAcA,CAACH,EAAU,EAAEV,IAAS;IAClC,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAClB,GAAGnB,gBAAgB,CAACc,4BAA4B,IAAII,EAAE,EAAE,EACxD;MACEV;KACD,CACF;EACH;EAEAc,aAAaA,CAACJ,EAAU,EAAEV,IAAS;IACjC,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAClB,GAAGnB,gBAAgB,CAACgB,2BAA2B,IAAIE,EAAE,EAAE,EACvD;MACEV;KACD,CACF;EACH;EAEAe,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACrB,IAAI,CAACsB,MAAM,CACrB,GAAGzB,gBAAgB,CAACY,4BAA4B,IAAIY,EAAE,EAAE,CACzD;EACH;EAEAE,cAAcA,CAACF,EAAU;IACvB,OAAO,IAAI,CAACrB,IAAI,CAACsB,MAAM,CACrB,GAAGzB,gBAAgB,CAACc,4BAA4B,IAAIU,EAAE,EAAE,CACzD;EACH;EAEAG,aAAaA,CAACH,EAAU;IACtB,OAAO,IAAI,CAACrB,IAAI,CAACsB,MAAM,CACrB,GAAGzB,gBAAgB,CAACgB,2BAA2B,IAAIQ,EAAE,EAAE,CACxD;EACH;EAEAI,eAAeA,CACbC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIrC,UAAU,EAAE,CAC1BsC,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CACF,QAAQ,EACR,2DAA2D,CAC5D,CACAA,GAAG,CAAC,6CAA6C,EAAE,iBAAiB,CAAC,CACrEA,GAAG,CACF,6CAA6C,EAC7C,8BAA8B,CAC/B,CACAA,GAAG,CACF,6CAA6C,EAC7C,0BAA0B,CAC3B;IAEH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD,CAAC,MAAM;MACLJ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAC/C;IAEA,IAAIF,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,qDAAqD,EACrDF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,mCAAmC,EAAEF,UAAU,CAAC;IACtE;IAEA,OAAO,IAAI,CAAC9B,IAAI,CAACoC,GAAG,CAAQ,GAAGvC,gBAAgB,CAACU,kBAAkB,EAAE,EAAE;MACpEwB;KACD,CAAC;EACJ;EAEAM,YAAYA,CAACN,MAAW;IACtB,OAAO,IAAI,CAAC/B,IAAI,CAACoC,GAAG,CAAM,GAAGvC,gBAAgB,CAACyC,QAAQ,EAAE,EAAE;MAAEP;IAAM,CAAE,CAAC,CAACQ,IAAI,CACxE3C,GAAG,CAAE4C,QAAQ,IACX,CAACA,QAAQ,EAAEnC,IAAI,IAAI,EAAE,EAAET,GAAG,CAAE6C,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAC,aAAaA,CAACb,MAAW;IACvB,OAAO,IAAI,CAAC/B,IAAI,CACboC,GAAG,CAAM,GAAGvC,gBAAgB,CAACU,kBAAkB,EAAE,EAAE;MAAEwB;IAAM,CAAE,CAAC,CAC9DQ,IAAI,CACH3C,GAAG,CAAE4C,QAAQ,IACX,CAACA,QAAQ,EAAEnC,IAAI,IAAI,EAAE,EAAET,GAAG,CAAE6C,IAAS,IAAI;MACvC,OAAO;QACLI,sBAAsB,EAAEJ,IAAI,EAAEI,sBAAsB,IAAI,EAAE;QAC1DC,IAAI,EAAEL,IAAI,EAAEK,IAAI,IAAI;OACrB;IACH,CAAC,CAAC,CACH,CACF;EACL;EAEAC,mBAAmBA,CAACC,cAAsB;IACxC,MAAMjB,MAAM,GAAG,IAAIrC,UAAU,EAAE,CAC5BsC,GAAG,CAAC,sCAAsC,EAAEgB,cAAc,CAAC,CAC3DhB,GAAG,CAAC,4CAA4C,EAAE,GAAG,CAAC,CACtDA,GAAG,CAAC,6CAA6C,EAAE,YAAY,CAAC,CAChEA,GAAG,CAAC,6CAA6C,EAAE,UAAU,CAAC,CAC9DA,GAAG,CAAC,6CAA6C,EAAE,QAAQ,CAAC,CAC5DA,GAAG,CACF,yEAAyE,EACzE,cAAc,CACf,CACAA,GAAG,CAAC,4CAA4C,EAAE,YAAY,CAAC,CAC/DA,GAAG,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAC7DA,GAAG,CACF,wEAAwE,EACxE,cAAc,CACf;IAEH,OAAO,IAAI,CAAChC,IAAI,CACboC,GAAG,CAAQ,GAAGvC,gBAAgB,CAACU,kBAAkB,EAAE,EAAE;MAAEwB;IAAM,CAAE,CAAC,CAChEQ,IAAI,CACH3C,GAAG,CAAE4C,QAAa,IAAI;MACpB,MAAMS,mBAAmB,GAAGT,QAAQ,EAAEnC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACrD,IAAI,CAACJ,qBAAqB,CAACiD,IAAI,CAACD,mBAAmB,CAAC;MACpD,OAAOT,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;;;uBAlLW1C,qBAAqB,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBxD,qBAAqB;MAAAyD,OAAA,EAArBzD,qBAAqB,CAAA0D,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}