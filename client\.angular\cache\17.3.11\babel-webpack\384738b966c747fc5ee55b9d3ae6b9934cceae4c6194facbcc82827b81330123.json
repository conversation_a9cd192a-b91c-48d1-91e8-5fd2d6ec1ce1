{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { ApiConstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AccountService {\n  constructor(http) {\n    this.http = http;\n    this.accountSubject = new BehaviorSubject(null);\n    this.account = this.accountSubject.asObservable();\n  }\n  getAccounts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate=roles`, {\n      params\n    });\n  }\n  getSalesOrders(page, pageSize, sortField, sortOrder, salesdata) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (salesdata) {\n      params = params.set('filters[$or][0][SOLDTO][$containsi]', salesdata?.customer_id).set('filters[$or][1][VKORG][$containsi]', salesdata?.sales_organization).set('filters[$or][2][COUNT][$containsi]', 100);\n    }\n    return this.http.get(`${ApiConstant.SALES_ORDER}`, {\n      params\n    });\n  }\n  getSalesQuote(page, pageSize, sortField, sortOrder, quotedata) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (quotedata) {\n      params = params.set('filters[$or][0][SOLDTO][$containsi]', quotedata?.customer_id).set('filters[$or][1][VKORG][$containsi]', quotedata?.sales_organization).set('filters[$or][2][COUNT][$containsi]', 100);\n    }\n    return this.http.get(`${ApiConstant.SALES_QUOTE}`, {\n      params\n    });\n  }\n  getAccountByID(accountId) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}/${accountId}?populate=*`);\n  }\n  static {\n    this.ɵfac = function AccountService_Factory(t) {\n      return new (t || AccountService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AccountService,\n      factory: AccountService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "ApiConstant", "AccountService", "constructor", "http", "accountSubject", "account", "asObservable", "getAccounts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS", "getSalesOrders", "salesdata", "customer_id", "sales_organization", "SALES_ORDER", "getSalesQuote", "quotedata", "SALES_QUOTE", "getAccountByID", "accountId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { ApiConstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AccountService {\r\n  public accountSubject = new BehaviorSubject<any>(null);\r\n  public account = this.accountSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getAccounts(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)\r\n        .set('filters[$or][2][phone][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}?populate=roles`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getSalesOrders(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    salesdata?: any\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (salesdata) {\r\n      params = params\r\n        .set('filters[$or][0][SOLDTO][$containsi]', salesdata?.customer_id)\r\n        .set(\r\n          'filters[$or][1][VKORG][$containsi]',\r\n          salesdata?.sales_organization\r\n        )\r\n        .set('filters[$or][2][COUNT][$containsi]', 100);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${ApiConstant.SALES_ORDER}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getSalesQuote(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    quotedata?: any\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (quotedata) {\r\n      params = params\r\n        .set('filters[$or][0][SOLDTO][$containsi]', quotedata?.customer_id)\r\n        .set(\r\n          'filters[$or][1][VKORG][$containsi]',\r\n          quotedata?.sales_organization\r\n        )\r\n        .set('filters[$or][2][COUNT][$containsi]', 100);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${ApiConstant.SALES_QUOTE}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getAccountByID(accountId: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.PARTNERS}/${accountId}?populate=*`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,QAAQ,iCAAiC;;;AAK7D,OAAM,MAAOC,cAAc;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAO,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,2CAA2C,EAAEF,UAAU,CAAC,CAC5DE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;IAC1D;IACA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGnB,gBAAgB,CAACoB,QAAQ,iBAAiB,EAAE;MACzEN;KACD,CAAC;EACJ;EAEAO,cAAcA,CACZZ,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBU,SAAe;IAEf,IAAIR,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAII,SAAS,EAAE;MACbR,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,qCAAqC,EAAEO,SAAS,EAAEC,WAAW,CAAC,CAClER,GAAG,CACF,oCAAoC,EACpCO,SAAS,EAAEE,kBAAkB,CAC9B,CACAT,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC;IACnD;IAEA,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAAQ,GAAGlB,WAAW,CAACwB,WAAW,EAAE,EAAE;MACxDX;KACD,CAAC;EACJ;EAEAY,aAAaA,CACXjB,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBe,SAAe;IAEf,IAAIb,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIS,SAAS,EAAE;MACbb,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,qCAAqC,EAAEY,SAAS,EAAEJ,WAAW,CAAC,CAClER,GAAG,CACF,oCAAoC,EACpCY,SAAS,EAAEH,kBAAkB,CAC9B,CACAT,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC;IACnD;IAEA,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAAQ,GAAGlB,WAAW,CAAC2B,WAAW,EAAE,EAAE;MACxDd;KACD,CAAC;EACJ;EAEAe,cAAcA,CAACC,SAAiB;IAC9B,OAAO,IAAI,CAAC1B,IAAI,CAACe,GAAG,CAClB,GAAGnB,gBAAgB,CAACoB,QAAQ,IAAIU,SAAS,aAAa,CACvD;EACH;;;uBA7FW5B,cAAc,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdhC,cAAc;MAAAiC,OAAA,EAAdjC,cAAc,CAAAkC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}