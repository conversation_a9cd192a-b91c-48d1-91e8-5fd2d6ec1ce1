{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Lithuanian [lt]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mmozuras\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var units = {\n    ss: 'sekundė_sekundžių_sekundes',\n    m: 'minutė_minutės_minutę',\n    mm: 'minutės_minučių_minutes',\n    h: 'valanda_valandos_valandą',\n    hh: 'valandos_valandų_valandas',\n    d: 'diena_dienos_dieną',\n    dd: 'dienos_dienų_dienas',\n    M: 'mėnuo_mėnesio_mėnesį',\n    MM: 'mėnesiai_mėnesių_mėnesius',\n    y: 'metai_metų_metus',\n    yy: 'metai_metų_metus'\n  };\n  function translateSeconds(number, withoutSuffix, key, isFuture) {\n    if (withoutSuffix) {\n      return 'kelios sekundės';\n    } else {\n      return isFuture ? 'kelių sekundžių' : 'kelias sekundes';\n    }\n  }\n  function translateSingular(number, withoutSuffix, key, isFuture) {\n    return withoutSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n  }\n  function special(number) {\n    return number % 10 === 0 || number > 10 && number < 20;\n  }\n  function forms(key) {\n    return units[key].split('_');\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    if (number === 1) {\n      return result + translateSingular(number, withoutSuffix, key[0], isFuture);\n    } else if (withoutSuffix) {\n      return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n    } else {\n      if (isFuture) {\n        return result + forms(key)[1];\n      } else {\n        return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n      }\n    }\n  }\n  var lt = moment.defineLocale('lt', {\n    months: {\n      format: 'sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio'.split('_'),\n      standalone: 'sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis'.split('_'),\n      isFormat: /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/\n    },\n    monthsShort: 'sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd'.split('_'),\n    weekdays: {\n      format: 'sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį'.split('_'),\n      standalone: 'sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis'.split('_'),\n      isFormat: /dddd HH:mm/\n    },\n    weekdaysShort: 'Sek_Pir_Ant_Tre_Ket_Pen_Šeš'.split('_'),\n    weekdaysMin: 'S_P_A_T_K_Pn_Š'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY [m.] MMMM D [d.]',\n      LLL: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n      LLLL: 'YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]',\n      l: 'YYYY-MM-DD',\n      ll: 'YYYY [m.] MMMM D [d.]',\n      lll: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n      llll: 'YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]'\n    },\n    calendar: {\n      sameDay: '[Šiandien] LT',\n      nextDay: '[Rytoj] LT',\n      nextWeek: 'dddd LT',\n      lastDay: '[Vakar] LT',\n      lastWeek: '[Praėjusį] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'po %s',\n      past: 'prieš %s',\n      s: translateSeconds,\n      ss: translate,\n      m: translateSingular,\n      mm: translate,\n      h: translateSingular,\n      hh: translate,\n      d: translateSingular,\n      dd: translate,\n      M: translateSingular,\n      MM: translate,\n      y: translateSingular,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-oji/,\n    ordinal: function (number) {\n      return number + '-oji';\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return lt;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}