{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError, shareReplay, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"partySelect\"];\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 35);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_4_Template, 1, 1, \"i\", 29)(5, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 27);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_10_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"role_code\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3, \" Role \");\n    i0.ɵɵtemplate(4, SalesCallInvolvedPartiesComponent_ng_template_10_i_4_Template, 1, 1, \"i\", 29)(5, SalesCallInvolvedPartiesComponent_ng_template_10_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 31);\n    i0.ɵɵelementStart(7, \"th\", 32);\n    i0.ɵɵtext(8, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"role_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"role_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.mobile) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.phone_number) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.email_address) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.address) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 39);\n    i0.ɵɵtemplate(3, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 40)(4, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 40)(5, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 40)(6, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_6_Template, 2, 1, \"ng-container\", 40)(7, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_7_Template, 2, 1, \"ng-container\", 40);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"address\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_Template, 8, 6, \"ng-container\", 31);\n    i0.ɵɵelementStart(4, \"td\", 32)(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_11_Template_button_click_5_listener($event) {\n      const partie_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(partie_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partie_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.role_code) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41);\n    i0.ɵɵtext(2, \"No involved parties found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41);\n    i0.ɵɵtext(2, \"Loading involved parties data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Involved Parties\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.bp_full_name, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallInvolvedPartiesComponent_ng_template_34_span_2_Template, 2, 1, \"span\", 42)(3, SalesCallInvolvedPartiesComponent_ng_template_34_span_3_Template, 2, 1, \"span\", 42)(4, SalesCallInvolvedPartiesComponent_ng_template_34_span_4_Template, 2, 1, \"span\", 42)(5, SalesCallInvolvedPartiesComponent_ng_template_34_span_5_Template, 2, 1, \"span\", 42)(6, SalesCallInvolvedPartiesComponent_ng_template_34_span_6_Template, 2, 1, \"span\", 42);\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email && item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email && !item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile && (item_r9.email || item_r9.bp_full_name));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile && !item_r9.email && !item_r9.bp_full_name);\n  }\n}\nexport let SalesCallInvolvedPartiesComponent = /*#__PURE__*/(() => {\n  class SalesCallInvolvedPartiesComponent {\n    constructor(activitiesservice, formBuilder, messageservice, confirmationservice) {\n      this.activitiesservice = activitiesservice;\n      this.formBuilder = formBuilder;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.involvedpartiesdetails = [];\n      this.activity_id = '';\n      this.addDialogVisible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.partyDataLoading = false;\n      this.partyInput$ = new Subject();\n      this.InvolvedPartiesForm = this.formBuilder.group({\n        role_code: [''],\n        party_id: ['']\n      });\n      this.role = [{\n        label: 'Account',\n        value: 'FLCU01'\n      }, {\n        label: 'Contact',\n        value: 'BUP001'\n      }, {\n        label: 'Created By',\n        value: 'YC'\n      }, {\n        label: 'Inside Sales Rep',\n        value: 'YI'\n      }, {\n        label: 'Outside Sales Rep',\n        value: 'YO'\n      }];\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'bp_full_name',\n        header: 'Name'\n      }, {\n        field: 'mobile',\n        header: 'Mobile'\n      }, {\n        field: 'phone_number',\n        header: 'Phone'\n      }, {\n        field: 'email_address',\n        header: 'E-Mail'\n      }, {\n        field: 'address',\n        header: 'Address'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.involvedpartiesdetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadPartyDataOnRoleChange();\n      this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.activity_id = response.activity_id;\n          const involvedParties = response?.involved_parties || [];\n          const roleMap = {\n            YI: 'Inside Sales Rep',\n            //YC: 'Created By',\n            YO: 'Outside Sales Rep'\n          };\n          const allowedPartnerFunctions = ['YI', 'YO'];\n          this.involvedpartiesdetails = involvedParties.map(party => {\n            const addresses = party?.business_partner?.addresses || [];\n            const address = addresses.find(addr => addr?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT'));\n            const processedAddress = address ? {\n              email_address: address?.emails?.[0]?.email_address || '-',\n              mobile: address?.phone_numbers?.find(item => item.phone_number_type === '3')?.phone_number || '-',\n              phone_number: address?.phone_numbers?.find(item => item.phone_number_type === '1')?.phone_number || '-',\n              address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', ')\n            } : {\n              email_address: '-',\n              mobile: '-',\n              phone_number: '-',\n              address: '-'\n            };\n            let roleMatch = '-';\n            if (party?.role_code === 'FLCU01' || party?.role_code === 'BUP001') {\n              roleMatch = this.role.find(r => r.value === party?.role_code || r.value === party?.bp_role)?.label || '-';\n            } else {\n              const partnerFn = party?.business_partner?.customer?.partner_functions?.find(p => allowedPartnerFunctions.includes(p?.partner_function));\n              if (!partnerFn && party?.role_code === 'BUP003' && !['YI', 'YO'].includes(party?.function_code)) {\n                roleMatch = 'Created By';\n              } else if (partnerFn) {\n                roleMatch = roleMap[partnerFn.partner_function] || '-';\n              }\n            }\n            return {\n              ...party,\n              bp_full_name: party?.business_partner?.bp_full_name || '-',\n              role_code: roleMatch,\n              ...processedAddress\n            };\n          });\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadPartyDataOnRoleChange() {\n      // Use valueChanges to watch for role_code changes\n      this.partyData$ = this.InvolvedPartiesForm.get('role_code').valueChanges.pipe(tap(() => {\n        this.clearPartyData(); // Extracted clearing logic\n      }), switchMap(role => {\n        if (!role) return of([]); // Return empty array if no role selected\n        return this.partyInput$.pipe(distinctUntilChanged(),\n        // Only trigger when input changes\n        debounceTime(300),\n        // Prevent rapid search calls\n        tap(() => this.partyDataLoading = true), switchMap(term => this.getPartnersByRoleAndSearch(role, term)));\n      }), shareReplay(1) // Ensure multiple subscribers get the same data\n      );\n    }\n    // Extracted method to clear party data\n    clearPartyData() {\n      this.InvolvedPartiesForm.get('party_id')?.reset();\n      this.partyInput$.next(''); // Clear the search term\n      if (this.partySelect) {\n        this.partySelect.clearModel(); // Clear the search input\n      }\n    }\n    // Method to get partners based on the role and search term\n    getPartnersByRoleAndSearch(role, term) {\n      const params = this.getParamsByRole(role, term);\n      if (!params) return of([]);\n      return this.activitiesservice.getPartners(params).pipe(map(res => res || []), catchError(error => {\n        console.error('Error fetching partners:', error); // Log error for debugging\n        this.partyDataLoading = false;\n        return of([]); // Return empty array in case of error\n      }), tap(() => this.partyDataLoading = false) // Reset loading flag after fetching\n      );\n    }\n    getParamsByRole(role, term) {\n      if (!role) return null;\n      const filters = {\n        'filters[$or][0][bp_full_name][$containsi]': term,\n        'filters[$or][1][bp_id][$containsi]': term,\n        'filters[$or][2][first_name][$containsi]': term,\n        'filters[$or][3][last_name][$containsi]': term\n      };\n      // Define roleFilters with string keys and object values\n      const roleFilters = {\n        FLCU01: {\n          'filters[roles][bp_role][$eq][0]': 'FLCU01',\n          'filters[roles][bp_role][$eq][1]': 'FLCU00'\n        },\n        BUP001: {\n          'filters[roles][bp_role][$eq]': 'BUP001'\n        },\n        YI: {\n          'filters[roles][bp_role][$eq]': 'BUP003',\n          'filters[customer][partner_functions][partner_function][$eq]': 'YI'\n        },\n        YO: {\n          'filters[roles][bp_role][$eq]': 'BUP003',\n          'filters[customer][partner_functions][partner_function][$eq]': 'YO'\n        },\n        YC: {\n          'filters[roles][bp_role][$eq]': 'BUP003'\n          //'filters[customer][partner_functions][partner_function][$eq]': 'YC',\n        }\n      };\n      // Use the roleFilters map to get the filters for the specific role\n      const roleSpecificFilters = roleFilters[role];\n      if (roleSpecificFilters) {\n        return {\n          ...roleSpecificFilters,\n          ...filters // Merge common filters\n        };\n      }\n      return null; // Return null if no filters are found for the given role\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.InvolvedPartiesForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.InvolvedPartiesForm.value\n        };\n        let role_code = value?.role_code;\n        if (['YI', 'YO', 'YC'].includes(role_code)) {\n          role_code = 'BUP003';\n        }\n        const data = {\n          activity_id: _this.activity_id,\n          role_code: role_code,\n          party_id: value?.party_id\n        };\n        _this.activitiesservice.createInvolvedParty(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: response => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.InvolvedPartiesForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Involved Party Added successFully!'\n            });\n            _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: res => {\n            _this.saving = false;\n            _this.addDialogVisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.activitiesservice.deleteInvolvedParty(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    showNewDialog(position) {\n      this.position = position;\n      this.addDialogVisible = true;\n      this.submitted = false;\n      this.InvolvedPartiesForm.reset();\n      if (this.role?.length > 0) {\n        this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);\n      }\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallInvolvedPartiesComponent_Factory(t) {\n        return new (t || SalesCallInvolvedPartiesComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallInvolvedPartiesComponent,\n        selectors: [[\"app-sales-call-involved-parties\"]],\n        viewQuery: function SalesCallInvolvedPartiesComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.partySelect = _t.first);\n          }\n        },\n        decls: 38,\n        vars: 26,\n        consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"party-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Role\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"role_code\", \"placeholder\", \"Select a Role\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Involved Party\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"party_id\", \"appendTo\", \"body\", 3, \"search\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"font-medium\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"7\", 1, \"border-round-left-lg\"], [4, \"ngIf\"]],\n        template: function SalesCallInvolvedPartiesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n            i0.ɵɵtext(3, \"Involved Parties\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"p-button\", 5);\n            i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_p_button_click_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showNewDialog(\"right\"));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallInvolvedPartiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 7)(8, \"p-table\", 8, 0);\n            i0.ɵɵlistener(\"onColReorder\", function SalesCallInvolvedPartiesComponent_Template_p_table_onColReorder_8_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(10, SalesCallInvolvedPartiesComponent_ng_template_10_Template, 9, 3, \"ng-template\", 9)(11, SalesCallInvolvedPartiesComponent_ng_template_11_Template, 6, 2, \"ng-template\", 10)(12, SalesCallInvolvedPartiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11)(13, SalesCallInvolvedPartiesComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"p-dialog\", 13);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallInvolvedPartiesComponent_Template_p_dialog_visibleChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(15, SalesCallInvolvedPartiesComponent_ng_template_15_Template, 2, 0, \"ng-template\", 9);\n            i0.ɵɵelementStart(16, \"form\", 14)(17, \"div\", 15)(18, \"label\", 16)(19, \"span\", 17);\n            i0.ɵɵtext(20, \"supervisor_account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \"Role \");\n            i0.ɵɵelementStart(22, \"span\", 18);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 19);\n            i0.ɵɵelement(25, \"p-dropdown\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 15)(27, \"label\", 21)(28, \"span\", 17);\n            i0.ɵɵtext(29, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \"Involved Party \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 19)(32, \"ng-select\", 22);\n            i0.ɵɵpipe(33, \"async\");\n            i0.ɵɵlistener(\"search\", function SalesCallInvolvedPartiesComponent_Template_ng_select_search_32_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.partyInput$.next($event.term));\n            });\n            i0.ɵɵtemplate(34, SalesCallInvolvedPartiesComponent_ng_template_34_Template, 7, 6, \"ng-template\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 24)(36, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_36_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addDialogVisible = false);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_37_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.involvedpartiesdetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.InvolvedPartiesForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"options\", ctx.role);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(33, 24, ctx.partyData$))(\"hideSelected\", true)(\"loading\", ctx.partyDataLoading)(\"minTermLength\", 3)(\"typeahead\", ctx.partyInput$);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Dialog, i11.MultiSelect, i4.AsyncPipe],\n        styles: [\".party-popup .p-dialog{margin-right:50px}  .party-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .party-popup .p-dialog .p-dialog-header h4{margin:0}  .party-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}  .party-popup .p-dialog.p-component.p-dialog-resizable{width:42rem!important}  .party-popup .field{grid-template-columns:repeat(auto-fill,minmax(360px,1fr))}\"]\n      });\n    }\n  }\n  return SalesCallInvolvedPartiesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}