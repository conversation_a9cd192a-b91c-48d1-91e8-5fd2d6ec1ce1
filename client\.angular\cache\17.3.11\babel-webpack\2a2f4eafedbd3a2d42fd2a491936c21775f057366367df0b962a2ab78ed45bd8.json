{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ExportComponent {\n  static {\n    this.ɵfac = function ExportComponent_Factory(t) {\n      return new (t || ExportComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExportComponent,\n      selectors: [[\"app-export\"]],\n      decls: 2,\n      vars: 0,\n      template: function ExportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"Export\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ExportComponent", "selectors", "decls", "vars", "template", "ExportComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\export\\export.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\export\\export.component.html"], "sourcesContent": ["import { Component } from \"@angular/core\";\r\n\r\n@Component({\r\n    selector: 'app-export',\r\n    templateUrl: './export.component.html',\r\n    styleUrl: './export.component.scss',\r\n})\r\nexport class ExportComponent {\r\n\r\n}", "<p>Export</p>"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}