{"ast": null, "code": "import { style, state, animate, transition, trigger } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nconst _c0 = [\"*\", [[\"p-header\"]]];\nconst _c1 = [\"*\", \"p-header\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nconst _c3 = a0 => ({\n  transitionParams: a0\n});\nconst _c4 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c5 = a0 => ({\n  value: \"hidden\",\n  params: a0\n});\nfunction AccordionTab_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template, 1, 2, \"ChevronDownIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.collapseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.collapseIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_2_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template, 1, 2, \"ChevronRightIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.expandIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.expandIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, AccordionTab_ng_container_3_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selected);\n  }\n}\nfunction AccordionTab_4_ng_template_0_Template(rf, ctx) {}\nfunction AccordionTab_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccordionTab_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AccordionTab_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.header, \" \");\n  }\n}\nfunction AccordionTab_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_content_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\nfunction AccordionTab_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_11_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n  }\n}\nconst _c6 = [\"*\"];\nlet AccordionTab = /*#__PURE__*/(() => {\n  class AccordionTab {\n    el;\n    changeDetector;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    header;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    headerStyle;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    tabStyle;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    tabStyleClass;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    headerStyleClass;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'start';\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    get selected() {\n      return this._selected;\n    }\n    set selected(val) {\n      this._selected = val;\n      if (!this.loaded) {\n        if (this._selected && this.cache) {\n          this.loaded = true;\n        }\n        this.changeDetector.detectChanges();\n      }\n    }\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    headerAriaLevel = 2;\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    selectedChange = new EventEmitter();\n    headerFacet;\n    templates;\n    _selected = false;\n    get iconClass() {\n      if (this.iconPos === 'end') {\n        return 'p-accordion-toggle-icon-end';\n      } else {\n        return 'p-accordion-toggle-icon';\n      }\n    }\n    contentTemplate;\n    headerTemplate;\n    iconTemplate;\n    loaded = false;\n    accordion;\n    constructor(accordion, el, changeDetector) {\n      this.el = el;\n      this.changeDetector = changeDetector;\n      this.accordion = accordion;\n      this.id = UniqueComponentId();\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    toggle(event) {\n      if (this.disabled) {\n        return false;\n      }\n      let index = this.findTabIndex();\n      if (this.selected) {\n        this.selected = false;\n        this.accordion.onClose.emit({\n          originalEvent: event,\n          index: index\n        });\n      } else {\n        if (!this.accordion.multiple) {\n          for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i].selected) {\n              this.accordion.tabs[i].selected = false;\n              this.accordion.tabs[i].selectedChange.emit(false);\n              this.accordion.tabs[i].changeDetector.markForCheck();\n            }\n          }\n        }\n        this.selected = true;\n        this.loaded = true;\n        this.accordion.onOpen.emit({\n          originalEvent: event,\n          index: index\n        });\n      }\n      this.selectedChange.emit(this.selected);\n      this.accordion.updateActiveIndex();\n      this.changeDetector.markForCheck();\n      event?.preventDefault();\n    }\n    findTabIndex() {\n      let index = -1;\n      for (var i = 0; i < this.accordion.tabs.length; i++) {\n        if (this.accordion.tabs[i] == this) {\n          index = i;\n          break;\n        }\n      }\n      return index;\n    }\n    get hasHeaderFacet() {\n      return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n      switch (event.code) {\n        case 'Enter':\n        case 'Space':\n          this.toggle(event);\n          event.preventDefault(); // ???\n          break;\n        default:\n          break;\n      }\n    }\n    getTabHeaderActionId(tabId) {\n      return `${tabId}_header_action`;\n    }\n    getTabContentId(tabId) {\n      return `${tabId}_content`;\n    }\n    ngOnDestroy() {\n      this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n    static ɵfac = function AccordionTab_Factory(t) {\n      return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AccordionTab,\n      selectors: [[\"p-accordionTab\"]],\n      contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 4);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        id: \"id\",\n        header: \"header\",\n        headerStyle: \"headerStyle\",\n        tabStyle: \"tabStyle\",\n        contentStyle: \"contentStyle\",\n        tabStyleClass: \"tabStyleClass\",\n        headerStyleClass: \"headerStyleClass\",\n        contentStyleClass: \"contentStyleClass\",\n        disabled: \"disabled\",\n        cache: \"cache\",\n        transitionOptions: \"transitionOptions\",\n        iconPos: \"iconPos\",\n        selected: \"selected\",\n        headerAriaLevel: \"headerAriaLevel\"\n      },\n      outputs: {\n        selectedChange: \"selectedChange\"\n      },\n      ngContentSelectors: _c1,\n      decls: 12,\n      vars: 45,\n      consts: [[1, \"p-accordion-tab\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"heading\", 1, \"p-accordion-header\"], [\"role\", \"button\", 1, \"p-accordion-header-link\", 3, \"click\", \"keydown\", \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\", 3, \"ngClass\", \"ngStyle\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-accordion-header-text\"]],\n      template: function AccordionTab_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n          i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n            return ctx.toggle($event);\n          })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n            return ctx.onKeydown($event);\n          });\n          i0.ɵɵtemplate(3, AccordionTab_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, AccordionTab_4_Template, 1, 0, null, 4)(5, AccordionTab_span_5_Template, 2, 1, \"span\", 5)(6, AccordionTab_ng_container_6_Template, 1, 0, \"ng-container\", 6)(7, AccordionTab_ng_content_7_Template, 1, 0, \"ng-content\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵprojection(10);\n          i0.ɵɵtemplate(11, AccordionTab_ng_container_11_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-accordion-tab-active\", ctx.selected);\n          i0.ɵɵproperty(\"ngClass\", ctx.tabStyleClass)(\"ngStyle\", ctx.tabStyle);\n          i0.ɵɵattribute(\"data-pc-name\", \"accordiontab\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"p-highlight\", ctx.selected)(\"p-disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"aria-level\", ctx.headerAriaLevel)(\"data-p-disabled\", ctx.disabled)(\"data-pc-section\", \"header\");\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(ctx.headerStyle);\n          i0.ɵɵproperty(\"ngClass\", ctx.headerStyleClass);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.getTabHeaderActionId(ctx.id))(\"aria-controls\", ctx.getTabContentId(ctx.id))(\"aria-expanded\", ctx.selected)(\"aria-disabled\", ctx.disabled)(\"data-pc-section\", \"headeraction\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.iconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(35, _c2, ctx.selected));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(39, _c4, i0.ɵɵpureFunction1(37, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(43, _c5, i0.ɵɵpureFunction1(41, _c3, ctx.transitionOptions)));\n          i0.ɵɵattribute(\"id\", ctx.getTabContentId(ctx.id))(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.getTabHeaderActionId(ctx.id))(\"data-pc-section\", \"toggleablecontent\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.contentStyleClass)(\"ngStyle\", ctx.contentStyle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, ChevronRightIcon, ChevronDownIcon],\n      styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('tabContent', [state('hidden', style({\n          height: '0',\n          visibility: 'hidden'\n        })), state('visible', style({\n          height: '*',\n          visibility: 'visible'\n        })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n      },\n      changeDetection: 0\n    });\n  }\n  return AccordionTab;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nlet Accordion = /*#__PURE__*/(() => {\n  class Accordion {\n    el;\n    changeDetector;\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    expandIcon;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    collapseIcon;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(val) {\n      this._activeIndex = val;\n      if (this.preventActiveIndexPropagation) {\n        this.preventActiveIndexPropagation = false;\n        return;\n      }\n      this.updateSelectionState();\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    get headerAriaLevel() {\n      return this._headerAriaLevel;\n    }\n    set headerAriaLevel(val) {\n      if (typeof val === 'number' && val > 0) {\n        this._headerAriaLevel = val;\n      } else if (this._headerAriaLevel !== 2) {\n        this._headerAriaLevel = 2;\n      }\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    onOpen = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    tabList;\n    tabListSubscription = null;\n    _activeIndex;\n    _headerAriaLevel = 2;\n    preventActiveIndexPropagation = false;\n    tabs = [];\n    constructor(el, changeDetector) {\n      this.el = el;\n      this.changeDetector = changeDetector;\n    }\n    onKeydown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onTabArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onTabArrowUpKey(event);\n          break;\n        case 'Home':\n          if (!event.shiftKey) {\n            this.onTabHomeKey(event);\n          }\n          break;\n        case 'End':\n          if (!event.shiftKey) {\n            this.onTabEndKey(event);\n          }\n          break;\n      }\n    }\n    onTabArrowDownKey(event) {\n      const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n      nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n      event.preventDefault();\n    }\n    onTabArrowUpKey(event) {\n      const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n      prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n      event.preventDefault();\n    }\n    onTabHomeKey(event) {\n      const firstHeaderAction = this.findFirstHeaderAction();\n      this.changeFocusedTab(firstHeaderAction);\n      event.preventDefault();\n    }\n    changeFocusedTab(element) {\n      if (element) {\n        DomHandler.focus(element);\n        if (this.selectOnFocus) {\n          this.tabs.forEach((tab, i) => {\n            let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n            if (this.multiple) {\n              if (!this._activeIndex) {\n                this._activeIndex = [];\n              }\n              if (tab.id == element.id) {\n                tab.selected = !tab.selected;\n                if (!this._activeIndex.includes(i)) {\n                  this._activeIndex.push(i);\n                } else {\n                  this._activeIndex = this._activeIndex.filter(ind => ind !== i);\n                }\n              }\n            } else {\n              if (tab.id == element.id) {\n                tab.selected = !tab.selected;\n                this._activeIndex = i;\n              } else {\n                tab.selected = false;\n              }\n            }\n            tab.selectedChange.emit(selected);\n            this.activeIndexChange.emit(this._activeIndex);\n            tab.changeDetector.markForCheck();\n          });\n        }\n      }\n    }\n    findNextHeaderAction(tabElement, selfCheck = false) {\n      const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n      const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n      const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n      const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findFirstHeaderAction() {\n      const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n      return this.findNextHeaderAction(firstEl, true);\n    }\n    findLastHeaderAction() {\n      const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n      const lastEl = childNodes[childNodes.length - 1];\n      return this.findPrevHeaderAction(lastEl, true);\n    }\n    onTabEndKey(event) {\n      const lastHeaderAction = this.findLastHeaderAction();\n      this.changeFocusedTab(lastHeaderAction);\n      event.preventDefault();\n    }\n    ngAfterContentInit() {\n      this.initTabs();\n      this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n        this.initTabs();\n      });\n    }\n    initTabs() {\n      this.tabs = this.tabList.toArray();\n      this.tabs.forEach(tab => {\n        tab.headerAriaLevel = this._headerAriaLevel;\n      });\n      this.updateSelectionState();\n      this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    updateSelectionState() {\n      if (this.tabs && this.tabs.length && this._activeIndex != null) {\n        for (let i = 0; i < this.tabs.length; i++) {\n          let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n          let changed = selected !== this.tabs[i].selected;\n          if (changed) {\n            this.tabs[i].selected = selected;\n            this.tabs[i].selectedChange.emit(selected);\n            this.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n    }\n    isTabActive(index) {\n      return this.multiple ? this._activeIndex && this._activeIndex.includes(index) : this._activeIndex === index;\n    }\n    getTabProp(tab, name) {\n      return tab.props ? tab.props[name] : undefined;\n    }\n    updateActiveIndex() {\n      let index = this.multiple ? [] : null;\n      this.tabs.forEach((tab, i) => {\n        if (tab.selected) {\n          if (this.multiple) {\n            index.push(i);\n          } else {\n            index = i;\n            return;\n          }\n        }\n      });\n      this.preventActiveIndexPropagation = true;\n      this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n      if (this.tabListSubscription) {\n        this.tabListSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function Accordion_Factory(t) {\n      return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Accordion,\n      selectors: [[\"p-accordion\"]],\n      contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, AccordionTab, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function Accordion_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function Accordion_keydown_HostBindingHandler($event) {\n            return ctx.onKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        multiple: \"multiple\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        expandIcon: \"expandIcon\",\n        collapseIcon: \"collapseIcon\",\n        activeIndex: \"activeIndex\",\n        selectOnFocus: \"selectOnFocus\",\n        headerAriaLevel: \"headerAriaLevel\"\n      },\n      outputs: {\n        onClose: \"onClose\",\n        onOpen: \"onOpen\",\n        activeIndexChange: \"activeIndexChange\"\n      },\n      ngContentSelectors: _c6,\n      decls: 2,\n      vars: 4,\n      consts: [[3, \"ngClass\", \"ngStyle\"]],\n      template: function Accordion_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Accordion;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccordionModule = /*#__PURE__*/(() => {\n  class AccordionModule {\n    static ɵfac = function AccordionModule_Factory(t) {\n      return new (t || AccordionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AccordionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule]\n    });\n  }\n  return AccordionModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n//# sourceMappingURL=primeng-accordion.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}