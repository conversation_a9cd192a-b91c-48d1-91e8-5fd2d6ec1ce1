{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DashboardComponent } from './dashboard.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, DashboardRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent],\n    imports: [CommonModule, DashboardRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DashboardRoutingModule", "DashboardComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "TableModule", "TabViewModule", "DashboardModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { DashboardRoutingModule } from './dashboard-routing.module';\r\nimport { DashboardComponent } from './dashboard.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DashboardComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    DashboardRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ReactiveFormsModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n  ]\r\n})\r\nexport class DashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;;AAqB/C,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAbxBZ,YAAY,EACZC,sBAAsB,EACtBE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBG,YAAY,EACZE,cAAc,EACdE,aAAa,EACbN,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc;IAAA;EAAA;;;2EAGLI,eAAe;IAAAC,YAAA,GAhBxBX,kBAAkB;IAAAY,OAAA,GAGlBd,YAAY,EACZC,sBAAsB,EACtBE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBG,YAAY,EACZE,cAAc,EACdE,aAAa,EACbN,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}