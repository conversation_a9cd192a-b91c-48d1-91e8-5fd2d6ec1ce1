{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Central Atlas Tamazight Latin [tzm-latn]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tzmLatn = moment.defineLocale('tzm-latn', {\n    months: 'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split('_'),\n    monthsShort: 'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split('_'),\n    weekdays: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    weekdaysShort: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    weekdaysMin: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[asdkh g] LT',\n      nextDay: '[aska g] LT',\n      nextWeek: 'dddd [g] LT',\n      lastDay: '[assant g] LT',\n      lastWeek: 'dddd [g] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dadkh s yan %s',\n      past: 'yan %s',\n      s: 'imik',\n      ss: '%d imik',\n      m: 'minuḍ',\n      mm: '%d minuḍ',\n      h: 'saɛa',\n      hh: '%d tassaɛin',\n      d: 'ass',\n      dd: '%d ossan',\n      M: 'ayowr',\n      MM: '%d iyyirn',\n      y: 'asgas',\n      yy: '%d isgasn'\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return tzmLatn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}