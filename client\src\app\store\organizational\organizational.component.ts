import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Subject, forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';
import { Table } from 'primeng/table';
import { Router } from '@angular/router';
import { OrganizationalService } from './organizational.service';
import { DropdownChangeEvent } from 'primeng/dropdown';

interface OrgColumn {
  field: string;
  header: string;
}

interface CreateOption {
  label: string;
  value: 'sub-unit' | 'org-unit';
  disabled: boolean;
}

@Component({
  selector: 'app-organizational',
  templateUrl: './organizational.component.html',
  styleUrl: './organizational.component.scss',
})
export class OrganizationalComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @ViewChild('dt1') dt1!: Table;
  public breadcrumbitems: MenuItem[] | any;
  public home: MenuItem | any;
  public organization: any[] = [];
  public totalRecords: number = 0;
  public loading: boolean = true;
  public globalSearchTerm: string = '';
  public selectedOrganizations: any[] = [];
  public createOptions: CreateOption[] = [];

  constructor(
    private router: Router,
    private organizationalservice: OrganizationalService
  ) {}

  private _selectedOrgColumns: OrgColumn[] = [];

  public OrgCols: OrgColumn[] = [
    {
      field: 'crm_org_unit_managers.business_partner.bp_full_name',
      header: 'Manager',
    },
    { field: 'parent_organisational_unit.name', header: 'Parent Unit Name' },
    { field: 'organisational_unit_id', header: 'ID' },
    { field: 'parent_organisational_unit_id', header: 'Parent Unit ID' },
    { field: 'sales_organisation_indicator', header: 'Sales Organization' },
    { field: 'sales_indicator', header: 'Sales' },
    { field: 'reporting_line_indicator', header: 'Reporting Line' },
  ];

  sortFieldOrg: string = '';
  sortOrderOrg: number = 1;

  ngOnInit() {
    this.breadcrumbitems = [
      { label: 'Organization', routerLink: ['/store/organization'] },
    ];
    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };
    this._selectedOrgColumns = this.OrgCols;
    this.updateCreateOptions();
  }

  get selectedOrgColumns(): any[] {
    return this._selectedOrgColumns;
  }

  set selectedOrgColumns(val: any[]) {
    this._selectedOrgColumns = this.OrgCols.filter((col) => val.includes(col));
  }

  onOrgColumnReorder(event: any) {
    const draggedCol = this.OrgCols[event.dragIndex];
    this.OrgCols.splice(event.dragIndex, 1);
    this.OrgCols.splice(event.dropIndex, 0, draggedCol);
  }

  customSort(field: string, data: any[], type: 'ORG') {
    if (type === 'ORG') {
      if (this.sortFieldOrg === field) {
        // Toggle sort order if same column is clicked
        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;
      } else {
        // Reset to ascending when changing columns
        this.sortFieldOrg = field;
        this.sortOrderOrg = 1;
      }
    }

    data.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrderOrg * result;
    });
  }

  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;

    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      let fields = field.split('.');
      let value = data;
      for (let i = 0; i < fields.length; i++) {
        if (value == null) return null;
        value = value[fields[i]];
      }
      return value;
    }
  }

  updateCreateOptions(): void {
    this.createOptions = [
      { label: 'Org Unit', value: 'org-unit', disabled: false },
      {
        label: 'SubOrg Unit',
        value: 'sub-unit',
        disabled: !(this.selectedOrganizations?.length > 0),
      },
    ];
  }

  loadOrganization(event: any) {
    this.loading = true;
    const page = event.first / event.rows + 1;
    const pageSize = event.rows;
    const sortField = event.sortField;
    const sortOrder = event.sortOrder;

    this.organizationalservice
      .getOrganization(
        page,
        pageSize,
        sortField,
        sortOrder,
        this.globalSearchTerm
      )
      .subscribe({
        next: (response: any) => {
          this.organization = response?.data || [];
          this.totalRecords = response?.meta?.pagination.total;
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error fetching organization', error);
          this.loading = false;
        },
      });
  }

  create(event: DropdownChangeEvent): void {
    const selectedType = event?.value?.value;

    if (selectedType === 'sub-unit' && this.selectedOrganizations?.length) {
      const selected = this.selectedOrganizations[0];
      const parentUnitId = selected?.organisational_unit_id;

      this.router.navigate(['/store/organization/createsub'], {
        state: { parentUnitId },
      });
    } else {
      this.router.navigate(['/store/organization/create']);
    }
  }

  toggle(organization: any): void {
    organization.expanded = !organization.expanded;
    if (!organization.expanded) {
      return;
    }

    const childIds: string[] =
      organization?.child_organisational_units
        ?.map((c: any) => c?.organisational_unit_id)
        ?.filter((id: string | undefined): id is string => !!id) || [];

    if (childIds.length === 0) {
      organization.expanded = false;
      return;
    }

    forkJoin(
      childIds.map((id) =>
        this.organizationalservice.getOrganizationByChildID(id)
      )
    )
      .pipe(map((resArray) => resArray.flatMap((res) => res ?? [])))
      .subscribe({
        next: (units: any[]) => {
          organization.details = units;
        },
        error: (err) => {
          console.error('Error loading expanded data', err);
          organization.expanded = false;
        },
      });
  }

  trackByUnit = (_: number, u: any) => u?.organisational_unit_id ?? _;

  onGlobalFilter(table: Table, event: Event) {
    this.loadOrganization({ first: 0, rows: 10 });
  }

  signup() {
    this.router.navigate(['/store/organization/create']);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
