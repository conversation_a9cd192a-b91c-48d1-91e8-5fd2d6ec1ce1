{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nfunction ProspectsAiInsightsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 9);\n    i0.ɵɵtext(2, \"Sales Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Distribution Channel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Division\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Sales Office\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 10);\n    i0.ɵɵtext(14, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsAiInsightsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 10);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/sales-quotes/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesOrganization, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.DistributionChannel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Division, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesOffice, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesGroup, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Currency, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Action, \" \");\n  }\n}\nexport let ProspectsAiInsightsComponent = /*#__PURE__*/(() => {\n  class ProspectsAiInsightsComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.tableData = [{\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }];\n    }\n    static {\n      this.ɵfac = function ProspectsAiInsightsComponent_Factory(t) {\n        return new (t || ProspectsAiInsightsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProspectsAiInsightsComponent,\n        selectors: [[\"app-prospects-ai-insights\"]],\n        decls: 10,\n        vars: 8,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"icon\", \"pi pi-angle-left\", 1, \"-ml-5\", 3, \"rounded\", \"outlined\", \"styleClass\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\", 3, \"routerLink\"]],\n        template: function ProspectsAiInsightsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"p-button\", 2);\n            i0.ɵɵelementStart(3, \"h4\", 3);\n            i0.ɵɵtext(4, \"AI Insights\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"p-button\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-table\", 6);\n            i0.ɵɵtemplate(8, ProspectsAiInsightsComponent_ng_template_8_Template, 15, 0, \"ng-template\", 7)(9, ProspectsAiInsightsComponent_ng_template_9_Template, 15, 8, \"ng-template\", 8);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.RouterLink, i2.Table, i3.PrimeTemplate, i4.Button]\n      });\n    }\n  }\n  return ProspectsAiInsightsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}