{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nfunction SalesOrdersSalesTeamComponent_ng_template_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 31);\n    i0.ɵɵtext(2, \"ID #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"First name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Last name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Email Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Phone no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 32);\n    i0.ɵɵtext(12, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesOrdersSalesTeamComponent_ng_template_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 32);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/sales-quotes/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Firstname, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Lastname, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.EmailId, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Phoneno, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Status, \" \");\n  }\n}\nexport class SalesOrdersSalesTeamComponent {\n  constructor() {\n    this.tableData = [];\n  }\n  ngOnInit() {\n    this.tableData = [{\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }, {\n      Id: '033241',\n      Firstname: 'Ben',\n      Lastname: 'Carter',\n      EmailId: '<EMAIL>',\n      Phoneno: '****** 525 5455',\n      Status: 'Active'\n    }];\n  }\n  static {\n    this.ɵfac = function SalesOrdersSalesTeamComponent_Factory(t) {\n      return new (t || SalesOrdersSalesTeamComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesOrdersSalesTeamComponent,\n      selectors: [[\"app-sales-orders-sales-team\"]],\n      decls: 92,\n      vars: 8,\n      consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"mt-6\", \"mb-1\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-3\"], [\"type\", \"button\", \"icon\", \"pi pi-check\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-3rem\", \"w-3rem\"], [1, \"material-symbols-rounded\", \"text-primary\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"icon\", \"pi pi-angle-left\", 1, \"-ml-5\", 3, \"rounded\", \"outlined\", \"styleClass\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\", 3, \"routerLink\"]],\n      template: function SalesOrdersSalesTeamComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h5\", 6);\n          i0.ɵɵtext(7, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"h5\", 8);\n          i0.ɵɵtext(10, \"SNJYA Customer Sprint 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"ul\", 9)(12, \"li\", 10)(13, \"span\", 11);\n          i0.ɵɵtext(14, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" : 05545SD585\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\", 10)(17, \"span\", 11);\n          i0.ɵɵtext(18, \"S4/HANA ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" : 152ASD5585\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\", 10)(21, \"span\", 11);\n          i0.ɵɵtext(22, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23, \" : Adam Smith\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\", 10)(25, \"span\", 11);\n          i0.ɵɵtext(26, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \" : Ben Jacobs\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"button\", 13)(30, \"i\", 14);\n          i0.ɵɵtext(31, \"call\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"button\", 13)(33, \"i\", 14);\n          i0.ɵɵtext(34, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"button\", 13)(36, \"i\", 14);\n          i0.ɵɵtext(37, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"button\", 13)(39, \"i\", 14);\n          i0.ɵɵtext(40, \"language\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"button\", 13)(42, \"i\", 14);\n          i0.ɵɵtext(43, \"edit_square\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"ul\", 16)(46, \"li\", 17)(47, \"span\", 18)(48, \"i\", 19);\n          i0.ɵɵtext(49, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 20);\n          i0.ɵɵtext(52, \"3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA 60532.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"li\", 17)(54, \"span\", 18)(55, \"i\", 19);\n          i0.ɵɵtext(56, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 20);\n          i0.ɵɵtext(59, \"******-423-5926\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"li\", 17)(61, \"span\", 18)(62, \"i\", 19);\n          i0.ɵɵtext(63, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\", 20);\n          i0.ɵɵtext(66, \"******-423-5926\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"li\", 17)(68, \"span\", 18)(69, \"i\", 19);\n          i0.ɵɵtext(70, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\", 20);\n          i0.ɵɵtext(73, \"<EMAIL>\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"li\", 17)(75, \"span\", 18)(76, \"i\", 19);\n          i0.ɵɵtext(77, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(78, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\", 20);\n          i0.ɵɵtext(80, \"www.asardigital.com\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(81, \"div\", 21)(82, \"div\", 22)(83, \"div\", 23);\n          i0.ɵɵelement(84, \"p-button\", 24);\n          i0.ɵɵelementStart(85, \"h4\", 25);\n          i0.ɵɵtext(86, \"Sales Team\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(87, \"p-button\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 27)(89, \"p-table\", 28);\n          i0.ɵɵtemplate(90, SalesOrdersSalesTeamComponent_ng_template_90_Template, 13, 0, \"ng-template\", 29)(91, SalesOrdersSalesTeamComponent_ng_template_91_Template, 13, 7, \"ng-template\", 30);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(84);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.Table, i3.PrimeTemplate, i4.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "tableinfo_r1", "Id", "Firstname", "Lastname", "EmailId", "Phoneno", "Status", "SalesOrdersSalesTeamComponent", "constructor", "tableData", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "SalesOrdersSalesTeamComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "SalesOrdersSalesTeamComponent_ng_template_90_Template", "SalesOrdersSalesTeamComponent_ng_template_91_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders-details\\sales-orders-sales-team\\sales-orders-sales-team.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders-details\\sales-orders-sales-team\\sales-orders-sales-team.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\ninterface AccountTableData {\r\n  Id?: string;\r\n  Firstname?: string;\r\n  Lastname?: string;\r\n  EmailId?: string;\r\n  Phoneno?: string;\r\n  Status?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-orders-sales-team',\r\n  templateUrl: './sales-orders-sales-team.component.html',\r\n  styleUrl: './sales-orders-sales-team.component.scss'\r\n})\r\nexport class SalesOrdersSalesTeamComponent {\r\n\r\n  tableData: AccountTableData[] = [];\r\n\r\n  ngOnInit() {\r\n    this.tableData = [\r\n      {\r\n        Id: '033241',\r\n        Firstname: '<PERSON>',\r\n        Lastname: '<PERSON>',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: '<PERSON>',\r\n        Lastname: '<PERSON>',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: '<PERSON>',\r\n        Lastname: '<PERSON>',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: '<PERSON>',\r\n        Lastname: '<PERSON>',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n      {\r\n        Id: '033241',\r\n        Firstname: 'Ben',\r\n        Lastname: 'Carter',\r\n        EmailId: '<EMAIL>',\r\n        Phoneno: '****** 525 5455',\r\n        Status: 'Active',\r\n      },\r\n    ];\r\n  }\r\n\r\n}\r\n", "<div class=\"grid mt-0 relative\">\r\n    <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\">\r\n        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n            <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                <div class=\"flex align-items-start gap-4\">\r\n                    <div class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                        <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                    </div>\r\n                    <div class=\"flex flex-column gap-4 flex-1\">\r\n                        <h5 class=\"mt-3 mb-1 font-semibold\">SNJYA Customer Sprint 2</h5>\r\n                        <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">CRM ID</span> :\r\n                                05545SD585</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">S4/HANA ID</span> :\r\n                                152ASD5585</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Account Owner </span> :\r\n                                Adam Smith</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Main Contact</span> :\r\n                                Ben Jacobs</li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n                <div class=\"mt-6 mb-1 flex align-items-center justify-content-between gap-3\">\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">call</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">location_on</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">email</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">language</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">edit_square</i>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">location_on</i> Address</span>\r\n                        <span class=\"flex-1\">3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA\r\n                            60532.</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">phone_in_talk</i> Phone</span>\r\n                        <span class=\"flex-1\">******-423-5926</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">phone_in_talk</i> Main Contact</span>\r\n                        <span class=\"flex-1\">******-423-5926</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i class=\"material-symbols-rounded\">mail</i>\r\n                            Email</span>\r\n                        <span class=\"flex-1\">info&#64;asardigital.com</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">language</i> Website</span>\r\n                        <span class=\"flex-1\">www.asardigital.com</span>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n        <div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n                <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                    [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\" class=\"-ml-5\" />\r\n                <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Team</h4>\r\n                <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold'\" />\r\n            </div>\r\n\r\n            <div class=\"table-sec\">\r\n                <p-table [value]=\"tableData\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n                    responsiveLayout=\"scroll\" >\r\n        \r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th class=\"border-round-left-lg\">ID #</th>\r\n                            <th>First name</th>\r\n                            <th>Last name</th>\r\n                            <th>Email Id</th>\r\n                            <th>Phone no</th>\r\n                            <th class=\"border-round-right-lg\">Status</th>\r\n                        </tr>\r\n                    </ng-template>\r\n        \r\n                    <ng-template pTemplate=\"body\" let-tableinfo>\r\n                        <tr>\r\n                            <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\" [routerLink]=\"'/store/sales-quotes/overview'\">\r\n                                {{ tableinfo.Id }}\r\n                            </td>\r\n                            <td>\r\n                                {{ tableinfo.Firstname }}\r\n                            </td>\r\n                            <td>\r\n                                {{ tableinfo.Lastname }}\r\n                            </td>\r\n                            <td>\r\n                                {{ tableinfo.EmailId }}\r\n                            </td>\r\n                            <td>\r\n                                {{ tableinfo.Phoneno }}\r\n                            </td>\r\n                            <td class=\"border-round-right-lg\">\r\n                                {{ tableinfo.Status }}\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;IC4F4BA,EADJ,CAAAC,cAAA,SAAI,aACiC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC5CF,EAD4C,CAAAG,YAAA,EAAK,EAC5C;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACoI;IAChID,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IAlBqFH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAK,UAAA,8CAA6C;IAC/HL,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAC,EAAA,MACJ;IAEIR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAE,SAAA,MACJ;IAEIT,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAG,QAAA,MACJ;IAEIV,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAI,OAAA,MACJ;IAEIX,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAK,OAAA,MACJ;IAEIZ,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAM,MAAA,MACJ;;;ADxG5B,OAAM,MAAOC,6BAA6B;EAL1CC,YAAA;IAOE,KAAAC,SAAS,GAAuB,EAAE;;EAElCC,QAAQA,CAAA;IACN,IAAI,CAACD,SAAS,GAAG,CACf;MACER,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,CACF;EACH;;;uBA/GWC,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVlBxB,EANxB,CAAAC,cAAA,aAAgC,aACwB,aACmB,aACD,aAChB,aAC4D,YACnD;UAAAD,EAAA,CAAAE,MAAA,SAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,aAA2C,YACH;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElBH,EAD9C,CAAAC,cAAA,aAAqD,cACP,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBACpE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBACxE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBAC5E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBAC1E;UAG1BF,EAH0B,CAAAG,YAAA,EAAK,EAClB,EACH,EACJ;UAIEH,EAHR,CAAAC,cAAA,eAA6E,kBAEuC,aAC3D;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACpD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC3D;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACrD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EACxD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC3D,EACP,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,wFACX;UACdF,EADc,CAAAG,YAAA,EAAO,EAChB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACxCF,EADwC,CAAAG,YAAA,EAAO,EAC1C;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9EH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACxCF,EADwC,CAAAG,YAAA,EAAO,EAC1C;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aAAoC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5FH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,4BAAwB;UACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAK5DF,EAL4D,CAAAG,YAAA,EAAO,EAC9C,EACJ,EACH,EACJ,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAwC,eACmB,eACgC;UAC/ED,EAAA,CAAA0B,SAAA,oBAC0E;UAC1E1B,EAAA,CAAAC,cAAA,cAA+C;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9DH,EAAA,CAAA0B,SAAA,oBAAyI;UAC7I1B,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,eAAuB,mBAEY;UAa3BD,EAXA,CAAA2B,UAAA,KAAAC,qDAAA,2BAAgC,KAAAC,qDAAA,2BAWY;UA0BhE7B,EAJgB,CAAAG,YAAA,EAAU,EACR,EACJ,EACJ,EACJ;;;UA/C4CH,EAAA,CAAAI,SAAA,IAAgB;UAC9CJ,EAD8B,CAAAK,UAAA,iBAAgB,kBAAkB,sDACX;UAEML,EAAA,CAAAI,SAAA,GAAiB;UAAiBJ,EAAlC,CAAAK,UAAA,kBAAiB,sCAAsD;UAI7HL,EAAA,CAAAI,SAAA,GAAmB;UAAuCJ,EAA1D,CAAAK,UAAA,UAAAoB,GAAA,CAAAT,SAAA,CAAmB,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}