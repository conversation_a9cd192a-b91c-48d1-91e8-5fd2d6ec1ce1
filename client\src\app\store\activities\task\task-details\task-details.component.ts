import { Component, OnInit } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ActivitiesService } from '../../activities.service';
import { MessageService, ConfirmationService } from 'primeng/api';

interface Actions {
  name: string;
  code: string;
  disabled: boolean;
}

@Component({
  selector: 'app-tasks-details',
  templateUrl: './task-details.component.html',
  styleUrl: './task-details.component.scss',
})
export class TaskDetailsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public activityDetails: any = null;
  public sidebarDetails: any = null;
  public items: MenuItem[] = [];
  public home: MenuItem | any;
  public id: string = '';
  public partner_role: string = '';
  public breadcrumbitems: MenuItem[] = [];
  public activeItem: MenuItem | null = null;
  public isSidebarHidden = false;
  public Actions: Actions[] = [];
  public selectedActions: Actions | undefined;
  public activeIndex: number = 0;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private activitiesservice: ActivitiesService,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  ngOnInit() {
    this.id = this.route.snapshot.paramMap.get('id') || '';
    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };
    this.makeMenuItems(this.id);
    if (this.items.length > 0) {
      this.activeItem = this.items[0];
    }

    this.setActiveTabFromURL();

    this.route.paramMap
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((params) => {
        const activityId = params.get('id');
        if (activityId) {
          this.loadActivityData(activityId);
        }
      });
    // Listen for route changes to keep active tab in sync
    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
      this.setActiveTabFromURL();
    });

    this.activitiesservice.activity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        const partner_role =
          response?.business_partner?.customer?.partner_functions?.find(
            (p: any) => p.partner_function === 'YI'
          );
        this.partner_role = partner_role?.bp_full_name || null;
        this.activityDetails = response || null;
        this.sidebarDetails = this.formatSidebarDetails(
          response?.business_partner?.addresses || []
        );
      });
  }

  private formatSidebarDetails(addresses: any[]): any[] {
    return addresses
      .filter((address: any) =>
        address?.address_usages?.some(
          (usage: any) => usage.address_usage === 'XXDEFAULT'
        )
      )
      .map((address: any) => ({
        ...address,
        address: [
          address?.house_number,
          address?.street_name,
          address?.city_name,
          address?.region,
          address?.country,
          address?.postal_code,
        ]
          .filter(Boolean)
          .join(', '),
        email_address: address?.emails?.[0]?.email_address || '-',
        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',
        website_url: address?.home_page_urls?.[0]?.website_url || '-',
      }));
  }

  makeMenuItems(id: string) {
    this.items = [
      {
        label: 'Overview',
        routerLink: `/store/activities/tasks/${id}/overview`,
      },
      {
        label: 'Attachments',
        routerLink: `/store/activities/tasks/${id}/attachments`,
      },
      {
        label: 'Follow Up',
        routerLink: `/store/activities/tasks/${id}/follow-items`,
      },
      {
        label: 'Involved Parties',
        routerLink: `/store/activities/tasks/${id}/involved-parties`,
      },
    ];
  }

  setActiveTabFromURL() {
    const fullPath = this.router.url;
    const currentTab = fullPath.split('/').pop() || 'overview';

    if (this.items.length === 0) return;

    const foundIndex = this.items.findIndex((tab: any) =>
      tab.routerLink.endsWith(currentTab)
    );
    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;
    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;

    this.updateBreadcrumb(this.activeItem?.label || 'Overview');
  }

  updateBreadcrumb(activeTab: string) {
    this.breadcrumbitems = [
      { label: 'Task', routerLink: ['/store/activities/tasks'] },
      { label: activeTab, routerLink: [] },
    ];
  }

  onTabChange(event: { index: number }) {
    if (this.items.length === 0) return;

    this.activeIndex = event.index;
    const selectedTab = this.items[this.activeIndex];

    if (selectedTab?.routerLink) {
      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs
    }
  }

  private loadActivityData(activityId: string): void {
    this.activitiesservice
      .getActivityByID(activityId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          this.activityDetails = response?.data[0] || null;
          switch (this.activityDetails.activity_status) {
            case '3':
              this.Actions = [
                { name: 'Set as Canceled', code: 'SCA', disabled: false },
              ];
              break;
            case '4':
              this.Actions = [
                { name: 'Set as Complete', code: 'SCO', disabled: true },
                { name: 'Set as Canceled', code: 'SCA', disabled: true },
              ];
              break;
            default:
              this.Actions = [
                { name: 'Set as Complete', code: 'SCO', disabled: false },
                { name: 'Set as Canceled', code: 'SCA', disabled: false },
              ];
          }
        },
        error: (error: any) => {
          console.error('Error fetching data:', error);
        },
      });
  }

  onActionChange(event: any) {
    const actionCode = event.value?.code;

    const actionsMap: { [key: string]: () => void } = {
      SCO: () => this.UpdateStatus(this.activityDetails.documentId, '3'),
      SCA: () => this.UpdateStatus(this.activityDetails.documentId, '4'),
    };

    const action = actionsMap[actionCode];

    if (action) {
      this.confirmationservice.confirm({
        message: 'Are you sure you want to proceed with this action?',
        header: 'Confirm',
        icon: 'pi pi-exclamation-triangle',
        accept: action,
      });
    }
  }

  UpdateStatus(docid: any, status: any) {
    const data = {
      activity_status: status,
    };
    this.activitiesservice
      .updateActivityStatus(docid, data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Action Updated Successfully!',
          });

          setTimeout(() => {
            window.location.reload();
          }, 1000);
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  goToBack() {
    this.router.navigate(['/store/activities/tasks']);
  }

  toggleSidebar() {
    this.isSidebarHidden = !this.isSidebarHidden;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
