{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, signal, computed, effect, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i7 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport * as i4 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/overlay';\nimport { OverlayModule } from 'primeng/overlay';\nimport * as i5 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i6 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nconst _c0 = [\"container\"];\nconst _c1 = [\"focusInput\"];\nconst _c2 = [\"multiIn\"];\nconst _c3 = [\"multiContainer\"];\nconst _c4 = [\"ddBtn\"];\nconst _c5 = [\"items\"];\nconst _c6 = [\"scroller\"];\nconst _c7 = [\"overlay\"];\nconst _c8 = a0 => ({\n  \"p-autocomplete-token\": true,\n  \"p-focus\": a0\n});\nconst _c9 = a0 => ({\n  $implicit: a0\n});\nconst _c10 = a0 => ({\n  height: a0\n});\nconst _c11 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c12 = a0 => ({\n  options: a0\n});\nconst _c13 = () => ({});\nconst _c14 = (a0, a1, a2) => ({\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nconst _c15 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction AutoComplete_input_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 3);\n    i0.ɵɵlistener(\"input\", function AutoComplete_input_2_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInput($event));\n    })(\"keydown\", function AutoComplete_input_2_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    })(\"change\", function AutoComplete_input_2_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputChange($event));\n    })(\"focus\", function AutoComplete_input_2_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputFocus($event));\n    })(\"blur\", function AutoComplete_input_2_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputBlur($event));\n    })(\"paste\", function AutoComplete_input_2_Template_input_paste_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputPaste($event));\n    })(\"keyup\", function AutoComplete_input_2_Template_input_keyup_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputKeyUp($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.inputStyleClass);\n    i0.ɵɵproperty(\"autofocus\", ctx_r2.autofocus)(\"ngClass\", ctx_r2.inputClass)(\"ngStyle\", ctx_r2.inputStyle)(\"type\", ctx_r2.type)(\"autocomplete\", ctx_r2.autocomplete)(\"required\", ctx_r2.required)(\"name\", ctx_r2.name)(\"maxlength\", ctx_r2.maxlength)(\"tabindex\", !ctx_r2.disabled ? ctx_r2.tabindex : -1)(\"readonly\", ctx_r2.readonly)(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"value\", ctx_r2.inputValue())(\"id\", ctx_r2.inputId)(\"placeholder\", ctx_r2.placeholder)(\"size\", ctx_r2.size)(\"aria-label\", ctx_r2.ariaLabel)(\"aria-labelledby\", ctx_r2.ariaLabelledBy)(\"aria-required\", ctx_r2.required)(\"aria-expanded\", ctx_r2.overlayVisible)(\"aria-controls\", ctx_r2.id + \"_list\")(\"aria-aria-activedescendant\", ctx_r2.focused ? ctx_r2.focusedOptionId : undefined);\n  }\n}\nfunction AutoComplete_ng_container_3_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 23);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-autocomplete-clear-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AutoComplete_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction AutoComplete_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AutoComplete_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ng_container_3_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵtemplate(1, AutoComplete_ng_container_3_span_2_1_Template, 1, 0, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction AutoComplete_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_ng_container_3_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 21)(2, AutoComplete_ng_container_3_span_2_Template, 2, 2, \"span\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction AutoComplete_ul_4_li_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_ul_4_li_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getOptionLabel(option_r8));\n  }\n}\nfunction AutoComplete_ul_4_li_2_TimesCircleIcon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesCircleIcon\", 36);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-autocomplete-token-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AutoComplete_ul_4_li_2_span_6_1_ng_template_0_Template(rf, ctx) {}\nfunction AutoComplete_ul_4_li_2_span_6_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_ul_4_li_2_span_6_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AutoComplete_ul_4_li_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtemplate(1, AutoComplete_ul_4_li_2_span_6_1_Template, 1, 0, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.removeIconTemplate);\n  }\n}\nfunction AutoComplete_ul_4_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 29, 5);\n    i0.ɵɵtemplate(2, AutoComplete_ul_4_li_2_ng_container_2_Template, 1, 0, \"ng-container\", 30)(3, AutoComplete_ul_4_li_2_span_3_Template, 2, 1, \"span\", 31);\n    i0.ɵɵelementStart(4, \"span\", 32);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ul_4_li_2_Template_span_click_4_listener($event) {\n      const i_r9 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeOption($event, i_r9));\n    });\n    i0.ɵɵtemplate(5, AutoComplete_ul_4_li_2_TimesCircleIcon_5_Template, 1, 2, \"TimesCircleIcon\", 33)(6, AutoComplete_ul_4_li_2_span_6_Template, 2, 2, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c8, ctx_r2.focusedMultipleOptionIndex() === i_r9));\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_multiple_option_\" + i_r9)(\"aria-label\", ctx_r2.getOptionLabel(option_r8))(\"aria-setsize\", ctx_r2.modelValue().length)(\"aria-posinset\", i_r9 + 1)(\"aria-selected\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.selectedItemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c9, option_r8));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedItemTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.removeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.removeIconTemplate);\n  }\n}\nfunction AutoComplete_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 25, 4);\n    i0.ɵɵlistener(\"focus\", function AutoComplete_ul_4_Template_ul_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMultipleContainerFocus($event));\n    })(\"blur\", function AutoComplete_ul_4_Template_ul_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMultipleContainerBlur($event));\n    })(\"keydown\", function AutoComplete_ul_4_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMultipleContainerKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_ul_4_li_2_Template, 7, 15, \"li\", 26);\n    i0.ɵɵelementStart(3, \"li\", 27)(4, \"input\", 28, 3);\n    i0.ɵɵlistener(\"input\", function AutoComplete_ul_4_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInput($event));\n    })(\"keydown\", function AutoComplete_ul_4_Template_input_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    })(\"change\", function AutoComplete_ul_4_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputChange($event));\n    })(\"focus\", function AutoComplete_ul_4_Template_input_focus_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputFocus($event));\n    })(\"blur\", function AutoComplete_ul_4_Template_input_blur_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputBlur($event));\n    })(\"paste\", function AutoComplete_ul_4_Template_input_paste_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputPaste($event));\n    })(\"keyup\", function AutoComplete_ul_4_Template_input_keyup_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInputKeyUp($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.multiContainerClass);\n    i0.ɵɵproperty(\"tabindex\", -1);\n    i0.ɵɵattribute(\"aria-orientation\", \"horizontal\")(\"aria-activedescendant\", ctx_r2.focused ? ctx_r2.focusedMultipleOptionId : undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.modelValue());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r2.inputStyleClass);\n    i0.ɵɵproperty(\"autofocus\", ctx_r2.autofocus)(\"ngClass\", ctx_r2.inputClass)(\"ngStyle\", ctx_r2.inputStyle)(\"autocomplete\", ctx_r2.autocomplete)(\"required\", ctx_r2.required)(\"maxlength\", ctx_r2.maxlength)(\"tabindex\", !ctx_r2.disabled ? ctx_r2.tabindex : -1)(\"readonly\", ctx_r2.readonly)(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"type\", ctx_r2.type)(\"id\", ctx_r2.inputId)(\"name\", ctx_r2.name)(\"placeholder\", ctx_r2.placeholder)(\"size\", ctx_r2.size)(\"aria-label\", ctx_r2.ariaLabel)(\"aria-labelledby\", ctx_r2.ariaLabelledBy)(\"aria-required\", ctx_r2.required)(\"aria-expanded\", ctx_r2.overlayVisible)(\"aria-controls\", ctx_r2.id + \"_list\")(\"aria-aria-activedescendant\", ctx_r2.focused ? ctx_r2.focusedOptionId : undefined);\n  }\n}\nfunction AutoComplete_ng_container_5_SpinnerIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 40);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-autocomplete-loader\")(\"spin\", true);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AutoComplete_ng_container_5_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction AutoComplete_ng_container_5_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_ng_container_5_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AutoComplete_ng_container_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtemplate(1, AutoComplete_ng_container_5_span_2_1_Template, 1, 0, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.loadingIconTemplate);\n  }\n}\nfunction AutoComplete_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_ng_container_5_SpinnerIcon_1_Template, 1, 3, \"SpinnerIcon\", 38)(2, AutoComplete_ng_container_5_span_2_Template, 2, 2, \"span\", 39);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loadingIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loadingIconTemplate);\n  }\n}\nfunction AutoComplete_button_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 44);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.dropdownIcon);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AutoComplete_button_6_ng_container_3_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n}\nfunction AutoComplete_button_6_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction AutoComplete_button_6_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_button_6_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AutoComplete_button_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_button_6_ng_container_3_ChevronDownIcon_1_Template, 1, 0, \"ChevronDownIcon\", 12)(2, AutoComplete_button_6_ng_container_3_2_Template, 1, 0, null, 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.dropdownIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.dropdownIconTemplate);\n  }\n}\nfunction AutoComplete_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42, 6);\n    i0.ɵɵlistener(\"click\", function AutoComplete_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleDropdownClick($event));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_button_6_span_2_Template, 1, 2, \"span\", 43)(3, AutoComplete_button_6_ng_container_3_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.dropdownAriaLabel)(\"tabindex\", ctx_r2.tabindex);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dropdownIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.dropdownIcon);\n  }\n}\nfunction AutoComplete_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_p_scroller_11_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_p_scroller_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_p_scroller_11_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 30);\n  }\n  if (rf & 2) {\n    const items_r12 = ctx.$implicit;\n    const scrollerOptions_r13 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const buildInItems_r14 = i0.ɵɵreference(14);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c11, items_r12, scrollerOptions_r13));\n  }\n}\nfunction AutoComplete_p_scroller_11_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_p_scroller_11_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_p_scroller_11_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 30);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r15 = ctx.options;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c12, scrollerOptions_r15));\n  }\n}\nfunction AutoComplete_p_scroller_11_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_p_scroller_11_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 47);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AutoComplete_p_scroller_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 45, 7);\n    i0.ɵɵlistener(\"onLazyLoad\", function AutoComplete_p_scroller_11_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_p_scroller_11_ng_template_2_Template, 1, 5, \"ng-template\", 46)(3, AutoComplete_p_scroller_11_ng_container_3_Template, 2, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c10, ctx_r2.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r2.visibleOptions())(\"itemSize\", ctx_r2.virtualScrollItemSize || ctx_r2._itemSize)(\"autoSize\", true)(\"lazy\", ctx_r2.lazy)(\"options\", ctx_r2.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loaderTemplate);\n  }\n}\nfunction AutoComplete_ng_container_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_ng_container_12_ng_container_1_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const buildInItems_r14 = i0.ɵɵreference(14);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInItems_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c11, ctx_r2.visibleOptions(), i0.ɵɵpureFunction0(2, _c13)));\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getOptionGroupLabel(option_r16.optionGroup));\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 51);\n    i0.ɵɵtemplate(2, AutoComplete_ng_template_13_ng_template_2_ng_container_0_span_2_Template, 2, 1, \"span\", 12)(3, AutoComplete_ng_template_13_ng_template_2_ng_container_0_ng_container_3_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    const option_r16 = ctx_r16.$implicit;\n    const i_r18 = ctx_r16.index;\n    const scrollerOptions_r19 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c10, scrollerOptions_r19.itemSize + \"px\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_\" + ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.groupTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c9, option_r16.optionGroup));\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getOptionLabel(option_r16));\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 52);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ng_template_13_ng_template_2_ng_container_1_Template_li_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const option_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onOptionSelect($event, option_r16));\n    })(\"mouseenter\", function AutoComplete_ng_template_13_ng_template_2_ng_container_1_Template_li_mouseenter_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const i_r18 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r19 = i0.ɵɵnextContext().options;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onOptionMouseEnter($event, ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19)));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_ng_template_13_ng_template_2_ng_container_1_span_2_Template, 2, 1, \"span\", 12)(3, AutoComplete_ng_template_13_ng_template_2_ng_container_1_ng_container_3_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    const option_r16 = ctx_r16.$implicit;\n    const i_r18 = ctx_r16.index;\n    const scrollerOptions_r19 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c10, scrollerOptions_r19.itemSize + \"px\"))(\"ngClass\", i0.ɵɵpureFunction3(14, _c14, ctx_r2.isSelected(option_r16), ctx_r2.focusedOptionIndex() === ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19), ctx_r2.isOptionDisabled(option_r16)));\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_\" + ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19))(\"aria-label\", ctx_r2.getOptionLabel(option_r16))(\"aria-selected\", ctx_r2.isSelected(option_r16))(\"aria-disabled\", ctx_r2.isOptionDisabled(option_r16))(\"data-p-focused\", ctx_r2.focusedOptionIndex() === ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19))(\"aria-setsize\", ctx_r2.ariaSetSize)(\"aria-posinset\", ctx_r2.getAriaPosInset(ctx_r2.getOptionIndex(i_r18, scrollerOptions_r19)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(18, _c15, option_r16, scrollerOptions_r19.getOptions ? scrollerOptions_r19.getOptions(i_r18) : i_r18));\n  }\n}\nfunction AutoComplete_ng_template_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_ng_template_13_ng_template_2_ng_container_0_Template, 4, 9, \"ng-container\", 12)(1, AutoComplete_ng_template_13_ng_template_2_ng_container_1_Template, 4, 21, \"ng-container\", 12);\n  }\n  if (rf & 2) {\n    const option_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isOptionGroup(option_r16));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isOptionGroup(option_r16));\n  }\n}\nfunction AutoComplete_ng_template_13_li_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.searchResultMessageText, \" \");\n  }\n}\nfunction AutoComplete_ng_template_13_li_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 9);\n  }\n}\nfunction AutoComplete_ng_template_13_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 53);\n    i0.ɵɵtemplate(1, AutoComplete_ng_template_13_li_3_ng_container_1_Template, 2, 1, \"ng-container\", 54)(2, AutoComplete_ng_template_13_li_3_ng_container_2_Template, 2, 0, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r19 = i0.ɵɵnextContext().options;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c10, scrollerOptions_r19.itemSize + \"px\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.emptyTemplate)(\"ngIfElse\", ctx_r2.empty);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.emptyTemplate);\n  }\n}\nfunction AutoComplete_ng_template_13_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoComplete_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 48, 8);\n    i0.ɵɵtemplate(2, AutoComplete_ng_template_13_ng_template_2_Template, 2, 2, \"ng-template\", 49)(3, AutoComplete_ng_template_13_li_3_Template, 3, 6, \"li\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AutoComplete_ng_template_13_ng_container_4_Template, 1, 0, \"ng-container\", 30);\n  }\n  if (rf & 2) {\n    const items_r21 = ctx.$implicit;\n    const scrollerOptions_r19 = ctx.options;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(scrollerOptions_r19.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r19.contentStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_list\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", items_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !items_r21 || items_r21 && items_r21.length === 0 && ctx_r2.showEmptyMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c9, items_r21));\n  }\n}\nconst AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => AutoComplete),\n  multi: true\n};\n/**\n * AutoComplete is an input component that provides real-time suggestions when being typed.\n * @group Components\n */\nlet AutoComplete = /*#__PURE__*/(() => {\n  class AutoComplete {\n    document;\n    el;\n    renderer;\n    cd;\n    config;\n    overlayService;\n    zone;\n    /**\n     * Minimum number of characters to initiate a search.\n     * @group Props\n     */\n    minLength = 1;\n    /**\n     * Delay between keystrokes to wait before sending a query.\n     * @group Props\n     */\n    delay = 300;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Inline style of the overlay panel element.\n     * @group Props\n     */\n    panelStyle;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    panelStyleClass;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * Hint text for the input field.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * When present, it specifies that the input cannot be typed.\n     * @group Props\n     */\n    readonly;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Maximum height of the suggestions panel.\n     * @group Props\n     */\n    scrollHeight = '200px';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    maxlength;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    required;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    size;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * When enabled, highlights the first item in the list by default.\n     * @group Props\n     */\n    autoHighlight;\n    /**\n     * When present, autocomplete clears the manual input if it does not match of the suggestions to force only accepting values from the suggestions.\n     * @group Props\n     */\n    forceSelection;\n    /**\n     * Type of the input, defaults to \"text\".\n     * @group Props\n     */\n    type = 'text';\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Defines a string that labels the dropdown button for accessibility.\n     * @group Props\n     */\n    dropdownAriaLabel;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    dropdownIcon;\n    /**\n     * Ensures uniqueness of selected items on multiple mode.\n     * @group Props\n     */\n    unique = true;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    group;\n    /**\n     * Whether to run a query when input receives focus.\n     * @group Props\n     */\n    completeOnFocus = false;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * Field of a suggested object to resolve and display.\n     * @group Props\n     * @deprecated use optionLabel property instead\n     */\n    field;\n    /**\n     * Displays a button next to the input field when enabled.\n     * @group Props\n     */\n    dropdown;\n    /**\n     * Whether to show the empty message or not.\n     * @group Props\n     */\n    showEmptyMessage;\n    /**\n     * Specifies the behavior dropdown button. Default \"blank\" mode sends an empty string and \"current\" mode sends the input value.\n     * @group Props\n     */\n    dropdownMode = 'blank';\n    /**\n     * Specifies if multiple values can be selected.\n     * @group Props\n     */\n    multiple;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    emptyMessage;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    autocomplete = 'off';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    optionGroupChildren = 'items';\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    optionGroupLabel = 'label';\n    /**\n     * Options for the overlay element.\n     * @group Props\n     */\n    overlayOptions;\n    /**\n     * An array of suggestions to display.\n     * @group Props\n     */\n    get suggestions() {\n      return this._suggestions();\n    }\n    set suggestions(value) {\n      this._suggestions.set(value);\n      this.handleSuggestionsChange();\n    }\n    /**\n     * Element dimensions of option for virtual scrolling.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(val) {\n      this._itemSize = val;\n      console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * Property name or getter function to use as the label of an option.\n     * @group Props\n     */\n    optionLabel;\n    /**\n     * Unique identifier of the component.\n     * @group Props\n     */\n    id;\n    /**\n     * Text to display when the search is active. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} results are available'\n     */\n    searchMessage;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue 'No selected item'\n     */\n    emptySelectionMessage;\n    /**\n     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} items selected'\n     */\n    selectionMessage;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    autoOptionFocus = true;\n    /**\n     * When enabled, the focused option is selected.\n     * @group Props\n     */\n    selectOnFocus;\n    /**\n     * Locale to use in searching. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    searchLocale;\n    /**\n     * Property name or getter function to use as the disabled flag of an option, defaults to false when not defined.\n     * @group Props\n     */\n    optionDisabled;\n    /**\n     * When enabled, the hovered option will be focused.\n     * @group Props\n     */\n    focusOnHover;\n    /**\n     * Callback to invoke to search for suggestions.\n     * @param {AutoCompleteCompleteEvent} event - Custom complete event.\n     * @group Emits\n     */\n    completeMethod = new EventEmitter();\n    /**\n     * Callback to invoke when a suggestion is selected.\n     * @param {AutoCompleteSelectEvent} event - custom select event.\n     * @group Emits\n     */\n    onSelect = new EventEmitter();\n    /**\n     * Callback to invoke when a selected value is removed.\n     * @param {AutoCompleteUnselectEvent} event - custom unselect event.\n     * @group Emits\n     */\n    onUnselect = new EventEmitter();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke to when dropdown button is clicked.\n     * @param {AutoCompleteDropdownClickEvent} event - custom dropdown click event.\n     * @group Emits\n     */\n    onDropdownClick = new EventEmitter();\n    /**\n     * Callback to invoke when clear button is clicked.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    /**\n     * Callback to invoke on input key up.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    onKeyUp = new EventEmitter();\n    /**\n     * Callback to invoke on overlay is shown.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke on overlay is hidden.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke on lazy load data.\n     * @param {AutoCompleteLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    containerEL;\n    inputEL;\n    multiInputEl;\n    multiContainerEL;\n    dropdownButton;\n    itemsViewChild;\n    scroller;\n    overlayViewChild;\n    templates;\n    _itemSize;\n    itemsWrapper;\n    itemTemplate;\n    emptyTemplate;\n    headerTemplate;\n    footerTemplate;\n    selectedItemTemplate;\n    groupTemplate;\n    loaderTemplate;\n    removeIconTemplate;\n    loadingIconTemplate;\n    clearIconTemplate;\n    dropdownIconTemplate;\n    value;\n    _suggestions = signal(null);\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    timeout;\n    overlayVisible;\n    suggestionsUpdated;\n    highlightOption;\n    highlightOptionChanged;\n    focused = false;\n    _filled;\n    get filled() {\n      return this._filled;\n    }\n    set filled(value) {\n      this._filled = value;\n    }\n    loading;\n    scrollHandler;\n    listId;\n    searchTimeout;\n    dirty = false;\n    modelValue = signal(null);\n    focusedMultipleOptionIndex = signal(-1);\n    focusedOptionIndex = signal(-1);\n    visibleOptions = computed(() => {\n      return this.group ? this.flatOptions(this._suggestions()) : this._suggestions() || [];\n    });\n    inputValue = computed(() => {\n      const modelValue = this.modelValue();\n      if (modelValue) {\n        if (typeof modelValue === 'object') {\n          const label = this.getOptionLabel(modelValue);\n          return label != null ? label : modelValue;\n        } else {\n          return modelValue;\n        }\n      } else {\n        return '';\n      }\n    });\n    get focusedMultipleOptionId() {\n      return this.focusedMultipleOptionIndex() !== -1 ? `${this.id}_multiple_option_${this.focusedMultipleOptionIndex()}` : null;\n    }\n    get focusedOptionId() {\n      return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n    get containerClass() {\n      return {\n        'p-autocomplete p-component p-inputwrapper': true,\n        'p-disabled': this.disabled,\n        'p-focus': this.focused,\n        'p-autocomplete-dd': this.dropdown,\n        'p-autocomplete-multiple': this.multiple,\n        'p-inputwrapper-focus': this.focused,\n        'p-overlay-open': this.overlayVisible\n      };\n    }\n    get multiContainerClass() {\n      return 'p-autocomplete-multiple-container p-component p-inputtext';\n    }\n    get panelClass() {\n      return {\n        'p-autocomplete-panel p-component': true,\n        'p-input-filled': this.config.inputStyle === 'filled',\n        'p-ripple-disabled': this.config.ripple === false\n      };\n    }\n    get inputClass() {\n      return {\n        'p-autocomplete-input p-inputtext p-component': !this.multiple,\n        'p-autocomplete-dd-input': this.dropdown\n      };\n    }\n    get searchResultMessageText() {\n      return ObjectUtils.isNotEmpty(this.visibleOptions()) && this.overlayVisible ? this.searchMessageText.replaceAll('{0}', this.visibleOptions().length) : this.emptySearchMessageText;\n    }\n    get searchMessageText() {\n      return this.searchMessage || this.config.translation.searchMessage || '';\n    }\n    get emptySearchMessageText() {\n      return this.emptyMessage || this.config.translation.emptySearchMessage || '';\n    }\n    get selectionMessageText() {\n      return this.selectionMessage || this.config.translation.selectionMessage || '';\n    }\n    get emptySelectionMessageText() {\n      return this.emptySelectionMessage || this.config.translation.emptySelectionMessage || '';\n    }\n    get selectedMessageText() {\n      return this.hasSelectedOption() ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.modelValue().length : '1') : this.emptySelectionMessageText;\n    }\n    get ariaSetSize() {\n      return this.visibleOptions().filter(option => !this.isOptionGroup(option)).length;\n    }\n    get virtualScrollerDisabled() {\n      return !this.virtualScroll;\n    }\n    constructor(document, el, renderer, cd, config, overlayService, zone) {\n      this.document = document;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.config = config;\n      this.overlayService = overlayService;\n      this.zone = zone;\n      effect(() => {\n        this.filled = ObjectUtils.isNotEmpty(this.modelValue());\n      });\n    }\n    ngOnInit() {\n      this.id = this.id || UniqueComponentId();\n      this.cd.detectChanges();\n    }\n    ngAfterViewChecked() {\n      //Use timeouts as since Angular 4.2, AfterViewChecked is broken and not called after panel is updated\n      if (this.suggestionsUpdated && this.overlayViewChild) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => {\n            if (this.overlayViewChild) {\n              this.overlayViewChild.alignOverlay();\n            }\n          }, 1);\n          this.suggestionsUpdated = false;\n        });\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'group':\n            this.groupTemplate = item.template;\n            break;\n          case 'selectedItem':\n            this.selectedItemTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'empty':\n            this.emptyTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'loader':\n            this.loaderTemplate = item.template;\n            break;\n          case 'removetokenicon':\n            this.removeIconTemplate = item.template;\n            break;\n          case 'loadingicon':\n            this.loadingIconTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'dropdownicon':\n            this.dropdownIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    handleSuggestionsChange() {\n      if (this.loading) {\n        this._suggestions() ? this.show() : !!this.emptyTemplate ? this.show() : this.hide();\n        const focusedOptionIndex = this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.suggestionsUpdated = true;\n        this.loading = false;\n        this.cd.markForCheck();\n      }\n    }\n    flatOptions(options) {\n      return (options || []).reduce((result, option, index) => {\n        result.push({\n          optionGroup: option,\n          group: true,\n          index\n        });\n        const optionGroupChildren = this.getOptionGroupChildren(option);\n        optionGroupChildren && optionGroupChildren.forEach(o => result.push(o));\n        return result;\n      }, []);\n    }\n    isOptionGroup(option) {\n      return this.optionGroupLabel && option.optionGroup && option.group;\n    }\n    findFirstOptionIndex() {\n      return this.visibleOptions().findIndex(option => this.isValidOption(option));\n    }\n    findLastOptionIndex() {\n      return ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidOption(option));\n    }\n    findFirstFocusedOptionIndex() {\n      const selectedIndex = this.findSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n    findLastFocusedOptionIndex() {\n      const selectedIndex = this.findSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n    findSelectedOptionIndex() {\n      return this.hasSelectedOption() ? this.visibleOptions().findIndex(option => this.isValidSelectedOption(option)) : -1;\n    }\n    findNextOptionIndex(index) {\n      const matchedOptionIndex = index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n    findPrevOptionIndex(index) {\n      const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    isValidSelectedOption(option) {\n      return this.isValidOption(option) && this.isSelected(option);\n    }\n    isValidOption(option) {\n      return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n    isOptionDisabled(option) {\n      return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : false;\n    }\n    isSelected(option) {\n      return ObjectUtils.equals(this.modelValue(), this.getOptionValue(option), this.equalityKey());\n    }\n    isOptionMatched(option, value) {\n      return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.searchLocale) === value.toLocaleLowerCase(this.searchLocale);\n    }\n    isInputClicked(event) {\n      if (this.multiple) return event.target === this.multiContainerEL.nativeElement || this.multiContainerEL.nativeElement.contains(event.target);else return event.target === this.inputEL.nativeElement;\n    }\n    isDropdownClicked(event) {\n      return this.dropdownButton?.nativeElement ? event.target === this.dropdownButton.nativeElement || this.dropdownButton.nativeElement.contains(event.target) : false;\n    }\n    equalityKey() {\n      return this.dataKey; // TODO: The 'optionValue' properties can be added.\n    }\n    onContainerClick(event) {\n      if (this.disabled || this.loading || this.isInputClicked(event) || this.isDropdownClicked(event)) {\n        return;\n      }\n      if (!this.overlayViewChild || !this.overlayViewChild.overlayViewChild?.nativeElement.contains(event.target)) {\n        DomHandler.focus(this.inputEL.nativeElement);\n      }\n    }\n    handleDropdownClick(event) {\n      let query = undefined;\n      if (this.overlayVisible) {\n        this.hide(true);\n      } else {\n        DomHandler.focus(this.inputEL.nativeElement);\n        query = this.inputEL.nativeElement.value;\n        if (this.dropdownMode === 'blank') this.search(event, '', 'dropdown');else if (this.dropdownMode === 'current') this.search(event, query, 'dropdown');\n      }\n      this.onDropdownClick.emit({\n        originalEvent: event,\n        query\n      });\n    }\n    onInput(event) {\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      let query = event.target.value;\n      if (!this.multiple && !this.forceSelection) {\n        this.updateModel(query);\n      }\n      if (query.length === 0 && !this.multiple) {\n        this.onClear.emit();\n        setTimeout(() => {\n          this.hide();\n        }, this.delay / 2);\n      } else {\n        if (query.length >= this.minLength) {\n          this.focusedOptionIndex.set(-1);\n          this.searchTimeout = setTimeout(() => {\n            this.search(event, query, 'input');\n          }, this.delay);\n        } else {\n          this.hide();\n        }\n      }\n    }\n    onInputChange(event) {\n      if (this.forceSelection) {\n        let valid = false;\n        if (this.visibleOptions()) {\n          const matchedValue = this.visibleOptions().find(option => this.isOptionMatched(option, this.inputEL.nativeElement.value || ''));\n          if (matchedValue !== undefined) {\n            valid = true;\n            !this.isSelected(matchedValue) && this.onOptionSelect(event, matchedValue);\n          }\n        }\n        if (!valid) {\n          this.inputEL.nativeElement.value = '';\n          !this.multiple && this.updateModel(null);\n        }\n      }\n    }\n    onInputFocus(event) {\n      if (this.disabled) {\n        // For ScreenReaders\n        return;\n      }\n      if (!this.dirty && this.completeOnFocus) {\n        this.search(event, event.target.value, 'focus');\n      }\n      this.dirty = true;\n      this.focused = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n      this.onFocus.emit(event);\n    }\n    onMultipleContainerFocus(event) {\n      if (this.disabled) {\n        // For ScreenReaders\n        return;\n      }\n      this.focused = true;\n    }\n    onMultipleContainerBlur(event) {\n      this.focusedMultipleOptionIndex.set(-1);\n      this.focused = false;\n    }\n    onMultipleContainerKeyDown(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      switch (event.code) {\n        case 'ArrowLeft':\n          this.onArrowLeftKeyOnMultiple(event);\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKeyOnMultiple(event);\n          break;\n        case 'Backspace':\n          this.onBackspaceKeyOnMultiple(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onInputBlur(event) {\n      this.dirty = false;\n      this.focused = false;\n      this.focusedOptionIndex.set(-1);\n      this.onModelTouched();\n      this.onBlur.emit(event);\n    }\n    onInputPaste(event) {\n      this.onKeyDown(event);\n    }\n    onInputKeyUp(event) {\n      this.onKeyUp.emit(event);\n    }\n    onKeyDown(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'ArrowLeft':\n          this.onArrowLeftKey(event);\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'PageDown':\n          this.onPageDownKey(event);\n          break;\n        case 'PageUp':\n          this.onPageUpKey(event);\n          break;\n        case 'Enter':\n        case 'NumpadEnter':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event);\n          break;\n        case 'Backspace':\n          this.onBackspaceKey(event);\n          break;\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          //NOOP\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      if (!this.overlayVisible) {\n        return;\n      }\n      const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n      this.changeFocusedOptionIndex(event, optionIndex);\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    onArrowUpKey(event) {\n      if (!this.overlayVisible) {\n        return;\n      }\n      if (event.altKey) {\n        if (this.focusedOptionIndex() !== -1) {\n          this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n        this.overlayVisible && this.hide();\n        event.preventDefault();\n      } else {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n        this.changeFocusedOptionIndex(event, optionIndex);\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    }\n    onArrowLeftKey(event) {\n      const target = event.currentTarget;\n      this.focusedOptionIndex.set(-1);\n      if (this.multiple) {\n        if (ObjectUtils.isEmpty(target.value) && this.hasSelectedOption()) {\n          DomHandler.focus(this.multiContainerEL.nativeElement);\n          this.focusedMultipleOptionIndex.set(this.modelValue().length);\n        } else {\n          event.stopPropagation(); // To prevent onArrowLeftKeyOnMultiple method\n        }\n      }\n    }\n    onArrowRightKey(event) {\n      this.focusedOptionIndex.set(-1);\n      this.multiple && event.stopPropagation(); // To prevent onArrowRightKeyOnMultiple method\n    }\n    onHomeKey(event) {\n      const {\n        currentTarget\n      } = event;\n      const len = currentTarget.value.length;\n      currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n      this.focusedOptionIndex.set(-1);\n      event.preventDefault();\n    }\n    onEndKey(event) {\n      const {\n        currentTarget\n      } = event;\n      const len = currentTarget.value.length;\n      currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n      this.focusedOptionIndex.set(-1);\n      event.preventDefault();\n    }\n    onPageDownKey(event) {\n      this.scrollInView(this.visibleOptions().length - 1);\n      event.preventDefault();\n    }\n    onPageUpKey(event) {\n      this.scrollInView(0);\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      if (!this.overlayVisible) {\n        this.onArrowDownKey(event);\n      } else {\n        if (this.focusedOptionIndex() !== -1) {\n          this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n        this.hide();\n      }\n      event.preventDefault();\n    }\n    onEscapeKey(event) {\n      this.overlayVisible && this.hide(true);\n      event.preventDefault();\n    }\n    onTabKey(event) {\n      if (this.focusedOptionIndex() !== -1) {\n        this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n      }\n      this.overlayVisible && this.hide();\n    }\n    onBackspaceKey(event) {\n      if (this.multiple) {\n        if (ObjectUtils.isNotEmpty(this.modelValue()) && !this.inputEL.nativeElement.value) {\n          const removedValue = this.modelValue()[this.modelValue().length - 1];\n          const newValue = this.modelValue().slice(0, -1);\n          this.updateModel(newValue);\n          this.onUnselect.emit({\n            originalEvent: event,\n            value: removedValue\n          });\n        }\n        event.stopPropagation(); // To prevent onBackspaceKeyOnMultiple method\n      }\n    }\n    onArrowLeftKeyOnMultiple(event) {\n      const optionIndex = this.focusedMultipleOptionIndex() < 1 ? 0 : this.focusedMultipleOptionIndex() - 1;\n      this.focusedMultipleOptionIndex.set(optionIndex);\n    }\n    onArrowRightKeyOnMultiple(event) {\n      let optionIndex = this.focusedMultipleOptionIndex();\n      optionIndex++;\n      this.focusedMultipleOptionIndex.set(optionIndex);\n      if (optionIndex > this.modelValue().length - 1) {\n        this.focusedMultipleOptionIndex.set(-1);\n        DomHandler.focus(this.inputEL.nativeElement);\n      }\n    }\n    onBackspaceKeyOnMultiple(event) {\n      if (this.focusedMultipleOptionIndex() !== -1) {\n        this.removeOption(event, this.focusedMultipleOptionIndex());\n      }\n    }\n    onOptionSelect(event, option, isHide = true) {\n      const value = this.getOptionValue(option);\n      if (this.multiple) {\n        this.inputEL.nativeElement.value = '';\n        if (!this.isSelected(option)) {\n          this.updateModel([...(this.modelValue() || []), value]);\n        }\n      } else {\n        this.updateModel(value);\n      }\n      this.onSelect.emit({\n        originalEvent: event,\n        value: option\n      });\n      isHide && this.hide(true);\n    }\n    onOptionMouseEnter(event, index) {\n      if (this.focusOnHover) {\n        this.changeFocusedOptionIndex(event, index);\n      }\n    }\n    search(event, query, source) {\n      //allow empty string but not undefined or null\n      if (query === undefined || query === null) {\n        return;\n      }\n      //do not search blank values on input change\n      if (source === 'input' && query.trim().length === 0) {\n        return;\n      }\n      this.loading = true;\n      this.completeMethod.emit({\n        originalEvent: event,\n        query\n      });\n    }\n    removeOption(event, index) {\n      event.stopPropagation();\n      const removedOption = this.modelValue()[index];\n      const value = this.modelValue().filter((_, i) => i !== index).map(option => this.getOptionValue(option));\n      this.updateModel(value);\n      this.onUnselect.emit({\n        originalEvent: event,\n        value: removedOption\n      });\n      DomHandler.focus(this.inputEL.nativeElement);\n    }\n    updateModel(value) {\n      this.value = value;\n      this.modelValue.set(value);\n      this.onModelChange(value);\n      this.updateInputValue();\n      this.cd.markForCheck();\n    }\n    updateInputValue() {\n      if (this.inputEL && this.inputEL.nativeElement) {\n        if (!this.multiple) {\n          this.inputEL.nativeElement.value = this.inputValue();\n        } else {\n          this.inputEL.nativeElement.value = '';\n        }\n      }\n    }\n    autoUpdateModel() {\n      if ((this.selectOnFocus || this.autoHighlight) && this.autoOptionFocus && !this.hasSelectedOption()) {\n        const focusedOptionIndex = this.findFirstFocusedOptionIndex();\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()], false);\n      }\n    }\n    scrollInView(index = -1) {\n      const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n      if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n        const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n          element.scrollIntoView && element.scrollIntoView({\n            block: 'nearest',\n            inline: 'nearest'\n          });\n        } else if (!this.virtualScrollerDisabled) {\n          setTimeout(() => {\n            this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n          }, 0);\n        }\n      }\n    }\n    changeFocusedOptionIndex(event, index) {\n      if (this.focusedOptionIndex() !== index) {\n        this.focusedOptionIndex.set(index);\n        this.scrollInView();\n        if (this.selectOnFocus || this.autoHighlight) {\n          this.onOptionSelect(event, this.visibleOptions()[index], false);\n        }\n      }\n    }\n    show(isFocus = false) {\n      this.dirty = true;\n      this.overlayVisible = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      isFocus && DomHandler.focus(this.inputEL.nativeElement);\n      if (isFocus) {\n        DomHandler.focus(this.inputEL.nativeElement);\n      }\n      this.onShow.emit();\n      this.cd.markForCheck();\n    }\n    hide(isFocus = false) {\n      const _hide = () => {\n        this.dirty = isFocus;\n        this.overlayVisible = false;\n        this.focusedOptionIndex.set(-1);\n        isFocus && DomHandler.focus(this.inputEL.nativeElement);\n        this.onHide.emit();\n        this.cd.markForCheck();\n      };\n      setTimeout(() => {\n        _hide();\n      }, 0); // For ScreenReaders\n    }\n    clear() {\n      this.updateModel(null);\n      this.inputEL.nativeElement.value = '';\n      this.onClear.emit();\n    }\n    writeValue(value) {\n      this.value = value;\n      this.modelValue.set(value);\n      this.updateInputValue();\n      this.cd.markForCheck();\n    }\n    hasSelectedOption() {\n      return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n    getAriaPosInset(index) {\n      return (this.optionGroupLabel ? index - this.visibleOptions().slice(0, index).filter(option => this.isOptionGroup(option)).length : index) + 1;\n    }\n    getOptionLabel(option) {\n      return this.field || this.optionLabel ? ObjectUtils.resolveFieldData(option, this.field || this.optionLabel) : option && option.label != undefined ? option.label : option;\n    }\n    getOptionValue(option) {\n      return option; // TODO: The 'optionValue' properties can be added.\n    }\n    getOptionIndex(index, scrollerOptions) {\n      return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n    getOptionGroupLabel(optionGroup) {\n      return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label != undefined ? optionGroup.label : optionGroup;\n    }\n    getOptionGroupChildren(optionGroup) {\n      return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n      if (event.toState === 'visible') {\n        this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-autocomplete-panel');\n        if (this.virtualScroll) {\n          this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n          this.scroller.viewInit();\n        }\n        if (this.visibleOptions() && this.visibleOptions().length) {\n          if (this.virtualScroll) {\n            const selectedIndex = this.modelValue() ? this.focusedOptionIndex() : -1;\n            if (selectedIndex !== -1) {\n              this.scroller?.scrollToIndex(selectedIndex);\n            }\n          } else {\n            let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-autocomplete-item.p-highlight');\n            if (selectedListItem) {\n              selectedListItem.scrollIntoView({\n                block: 'nearest',\n                inline: 'center'\n              });\n            }\n          }\n        }\n      }\n    }\n    ngOnDestroy() {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n    }\n    static ɵfac = function AutoComplete_Factory(t) {\n      return new (t || AutoComplete)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AutoComplete,\n      selectors: [[\"p-autoComplete\"]],\n      contentQueries: function AutoComplete_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function AutoComplete_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerEL = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputEL = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiInputEl = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiContainerEL = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 6,\n      hostBindings: function AutoComplete_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused && !ctx.disabled || ctx.autofocus || ctx.overlayVisible)(\"p-autocomplete-clearable\", ctx.showClear && !ctx.disabled);\n        }\n      },\n      inputs: {\n        minLength: \"minLength\",\n        delay: \"delay\",\n        style: \"style\",\n        panelStyle: \"panelStyle\",\n        styleClass: \"styleClass\",\n        panelStyleClass: \"panelStyleClass\",\n        inputStyle: \"inputStyle\",\n        inputId: \"inputId\",\n        inputStyleClass: \"inputStyleClass\",\n        placeholder: \"placeholder\",\n        readonly: \"readonly\",\n        disabled: \"disabled\",\n        scrollHeight: \"scrollHeight\",\n        lazy: \"lazy\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        maxlength: \"maxlength\",\n        name: \"name\",\n        required: \"required\",\n        size: \"size\",\n        appendTo: \"appendTo\",\n        autoHighlight: \"autoHighlight\",\n        forceSelection: \"forceSelection\",\n        type: \"type\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        ariaLabel: \"ariaLabel\",\n        dropdownAriaLabel: \"dropdownAriaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        dropdownIcon: \"dropdownIcon\",\n        unique: \"unique\",\n        group: \"group\",\n        completeOnFocus: \"completeOnFocus\",\n        showClear: \"showClear\",\n        field: \"field\",\n        dropdown: \"dropdown\",\n        showEmptyMessage: \"showEmptyMessage\",\n        dropdownMode: \"dropdownMode\",\n        multiple: \"multiple\",\n        tabindex: \"tabindex\",\n        dataKey: \"dataKey\",\n        emptyMessage: \"emptyMessage\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        autofocus: \"autofocus\",\n        autocomplete: \"autocomplete\",\n        optionGroupChildren: \"optionGroupChildren\",\n        optionGroupLabel: \"optionGroupLabel\",\n        overlayOptions: \"overlayOptions\",\n        suggestions: \"suggestions\",\n        itemSize: \"itemSize\",\n        optionLabel: \"optionLabel\",\n        id: \"id\",\n        searchMessage: \"searchMessage\",\n        emptySelectionMessage: \"emptySelectionMessage\",\n        selectionMessage: \"selectionMessage\",\n        autoOptionFocus: \"autoOptionFocus\",\n        selectOnFocus: \"selectOnFocus\",\n        searchLocale: \"searchLocale\",\n        optionDisabled: \"optionDisabled\",\n        focusOnHover: \"focusOnHover\"\n      },\n      outputs: {\n        completeMethod: \"completeMethod\",\n        onSelect: \"onSelect\",\n        onUnselect: \"onUnselect\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onDropdownClick: \"onDropdownClick\",\n        onClear: \"onClear\",\n        onKeyUp: \"onKeyUp\",\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        onLazyLoad: \"onLazyLoad\"\n      },\n      features: [i0.ɵɵProvidersFeature([AUTOCOMPLETE_VALUE_ACCESSOR])],\n      decls: 17,\n      vars: 25,\n      consts: [[\"container\", \"\"], [\"overlay\", \"\"], [\"buildInItems\", \"\"], [\"focusInput\", \"\"], [\"multiContainer\", \"\"], [\"token\", \"\"], [\"ddBtn\", \"\"], [\"scroller\", \"\"], [\"items\", \"\"], [\"empty\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [\"pAutoFocus\", \"\", \"aria-autocomplete\", \"list\", \"role\", \"combobox\", 3, \"autofocus\", \"ngClass\", \"ngStyle\", \"class\", \"type\", \"autocomplete\", \"required\", \"name\", \"maxlength\", \"tabindex\", \"readonly\", \"disabled\", \"input\", \"keydown\", \"change\", \"focus\", \"blur\", \"paste\", \"keyup\", 4, \"ngIf\"], [4, \"ngIf\"], [\"role\", \"listbox\", 3, \"class\", \"tabindex\", \"focus\", \"blur\", \"keydown\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-autocomplete-dropdown p-button-icon-only\", \"pRipple\", \"\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [3, \"visibleChange\", \"onAnimationStart\", \"onHide\", \"visible\", \"options\", \"target\", \"appendTo\", \"showTransitionOptions\", \"hideTransitionOptions\"], [3, \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-live\", \"polite\", 1, \"p-hidden-accessible\"], [\"pAutoFocus\", \"\", \"aria-autocomplete\", \"list\", \"role\", \"combobox\", 3, \"input\", \"keydown\", \"change\", \"focus\", \"blur\", \"paste\", \"keyup\", \"autofocus\", \"ngClass\", \"ngStyle\", \"type\", \"autocomplete\", \"required\", \"name\", \"maxlength\", \"tabindex\", \"readonly\", \"disabled\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"styleClass\"], [1, \"p-autocomplete-clear-icon\", 3, \"click\"], [\"role\", \"listbox\", 3, \"focus\", \"blur\", \"keydown\", \"tabindex\"], [\"role\", \"option\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"option\", 1, \"p-autocomplete-input-token\"], [\"pAutoFocus\", \"\", \"role\", \"combobox\", \"aria-autocomplete\", \"list\", 3, \"input\", \"keydown\", \"change\", \"focus\", \"blur\", \"paste\", \"keyup\", \"autofocus\", \"ngClass\", \"ngStyle\", \"autocomplete\", \"required\", \"maxlength\", \"tabindex\", \"readonly\", \"disabled\"], [\"role\", \"option\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-autocomplete-token-label\", 4, \"ngIf\"], [1, \"p-autocomplete-token-icon\", 3, \"click\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-token-icon\", 4, \"ngIf\"], [1, \"p-autocomplete-token-label\"], [3, \"styleClass\"], [1, \"p-autocomplete-token-icon\"], [3, \"styleClass\", \"spin\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-loader pi-spin \", 4, \"ngIf\"], [3, \"styleClass\", \"spin\"], [1, \"p-autocomplete-loader\", \"pi-spin\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-autocomplete-dropdown\", \"p-button-icon-only\", 3, \"click\", \"disabled\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"onLazyLoad\", \"items\", \"itemSize\", \"autoSize\", \"lazy\", \"options\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", 1, \"p-autocomplete-items\", 3, \"ngClass\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-autocomplete-empty-message\", \"role\", \"option\", 3, \"ngStyle\", 4, \"ngIf\"], [\"role\", \"option\", 1, \"p-autocomplete-item-group\", 3, \"ngStyle\"], [\"pRipple\", \"\", \"role\", \"option\", 1, \"p-autocomplete-item\", 3, \"click\", \"mouseenter\", \"ngStyle\", \"ngClass\"], [\"role\", \"option\", 1, \"p-autocomplete-empty-message\", 3, \"ngStyle\"], [4, \"ngIf\", \"ngIfElse\"]],\n      template: function AutoComplete_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 10, 0);\n          i0.ɵɵlistener(\"click\", function AutoComplete_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onContainerClick($event));\n          });\n          i0.ɵɵtemplate(2, AutoComplete_input_2_Template, 2, 23, \"input\", 11)(3, AutoComplete_ng_container_3_Template, 3, 2, \"ng-container\", 12)(4, AutoComplete_ul_4_Template, 6, 28, \"ul\", 13)(5, AutoComplete_ng_container_5_Template, 3, 2, \"ng-container\", 12)(6, AutoComplete_button_6_Template, 4, 5, \"button\", 14);\n          i0.ɵɵelementStart(7, \"p-overlay\", 15, 1);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AutoComplete_Template_p_overlay_visibleChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.overlayVisible, $event) || (ctx.overlayVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onAnimationStart\", function AutoComplete_Template_p_overlay_onAnimationStart_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOverlayAnimationStart($event));\n          })(\"onHide\", function AutoComplete_Template_p_overlay_onHide_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hide());\n          });\n          i0.ɵɵelementStart(9, \"div\", 16);\n          i0.ɵɵtemplate(10, AutoComplete_ng_container_10_Template, 1, 0, \"ng-container\", 17)(11, AutoComplete_p_scroller_11_Template, 4, 10, \"p-scroller\", 18)(12, AutoComplete_ng_container_12_Template, 2, 6, \"ng-container\", 12)(13, AutoComplete_ng_template_13_Template, 5, 10, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 19);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filled && !ctx.disabled && ctx.showClear && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dropdown);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.overlayVisible);\n          i0.ɵɵproperty(\"options\", ctx.overlayOptions)(\"target\", \"@parent\")(\"appendTo\", ctx.appendTo)(\"showTransitionOptions\", ctx.showTransitionOptions)(\"hideTransitionOptions\", ctx.hideTransitionOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.panelStyleClass);\n          i0.ɵɵstyleProp(\"max-height\", ctx.virtualScroll ? \"auto\" : ctx.scrollHeight);\n          i0.ɵɵproperty(\"ngClass\", ctx.panelClass)(\"ngStyle\", ctx.panelStyle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.virtualScroll);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.virtualScroll);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedMessageText, \" \");\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Overlay, i1.PrimeTemplate, i4.ButtonDirective, i5.Ripple, i6.Scroller, i7.AutoFocus, TimesCircleIcon, SpinnerIcon, TimesIcon, ChevronDownIcon],\n      styles: [\"@layer primeng{.p-autocomplete{display:inline-flex;position:relative}.p-autocomplete-loader{position:absolute;top:50%;margin-top:-.5rem}.p-autocomplete-dd .p-autocomplete-input{flex:1 1 auto;width:1%}.p-autocomplete-dd .p-autocomplete-input,.p-autocomplete-dd .p-autocomplete-multiple-container{border-top-right-radius:0;border-bottom-right-radius:0}.p-autocomplete-dd .p-autocomplete-dropdown{border-top-left-radius:0;border-bottom-left-radius:0}.p-autocomplete-panel{overflow:auto}.p-autocomplete-items{margin:0;padding:0;list-style-type:none}.p-autocomplete-item{cursor:pointer;white-space:nowrap;position:relative;overflow:hidden}.p-autocomplete-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-autocomplete-token{width:-moz-fit-content;width:fit-content;cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-autocomplete-token-icon{display:flex;cursor:pointer}.p-autocomplete-input-token{flex:1 1 auto;display:inline-flex}.p-autocomplete-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-autocomplete{display:flex}.p-fluid .p-autocomplete-dd .p-autocomplete-input{width:1%}.p-autocomplete-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-autocomplete-clearable{position:relative}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return AutoComplete;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AutoCompleteModule = /*#__PURE__*/(() => {\n  class AutoCompleteModule {\n    static ɵfac = function AutoCompleteModule_Factory(t) {\n      return new (t || AutoCompleteModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AutoCompleteModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, OverlayModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule, AutoFocusModule, TimesCircleIcon, SpinnerIcon, TimesIcon, ChevronDownIcon, OverlayModule, SharedModule, ScrollerModule, AutoFocusModule]\n    });\n  }\n  return AutoCompleteModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTOCOMPLETE_VALUE_ACCESSOR, AutoComplete, AutoCompleteModule };\n//# sourceMappingURL=primeng-autocomplete.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}