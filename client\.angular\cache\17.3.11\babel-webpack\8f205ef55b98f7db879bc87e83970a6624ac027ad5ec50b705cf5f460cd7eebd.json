{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Basque [eu]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/eillarra\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var eu = moment.defineLocale('eu', {\n    months: 'urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua'.split('_'),\n    monthsShort: 'urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata'.split('_'),\n    weekdaysShort: 'ig._al._ar._az._og._ol._lr.'.split('_'),\n    weekdaysMin: 'ig_al_ar_az_og_ol_lr'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY[ko] MMMM[ren] D[a]',\n      LLL: 'YYYY[ko] MMMM[ren] D[a] HH:mm',\n      LLLL: 'dddd, YYYY[ko] MMMM[ren] D[a] HH:mm',\n      l: 'YYYY-M-D',\n      ll: 'YYYY[ko] MMM D[a]',\n      lll: 'YYYY[ko] MMM D[a] HH:mm',\n      llll: 'ddd, YYYY[ko] MMM D[a] HH:mm'\n    },\n    calendar: {\n      sameDay: '[gaur] LT[etan]',\n      nextDay: '[bihar] LT[etan]',\n      nextWeek: 'dddd LT[etan]',\n      lastDay: '[atzo] LT[etan]',\n      lastWeek: '[aurreko] dddd LT[etan]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s barru',\n      past: 'duela %s',\n      s: 'segundo batzuk',\n      ss: '%d segundo',\n      m: 'minutu bat',\n      mm: '%d minutu',\n      h: 'ordu bat',\n      hh: '%d ordu',\n      d: 'egun bat',\n      dd: '%d egun',\n      M: 'hilabete bat',\n      MM: '%d hilabete',\n      y: 'urte bat',\n      yy: '%d urte'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return eu;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}