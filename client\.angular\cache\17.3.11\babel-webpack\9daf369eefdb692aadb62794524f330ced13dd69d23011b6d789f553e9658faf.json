{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ContactsRoutingModule } from './contacts-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { ContactsComponent } from './contacts.component';\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TabViewModule } from 'primeng/tabview';\nimport { EditorModule } from 'primeng/editor';\nimport { DialogModule } from 'primeng/dialog';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\nimport { AddContactComponent } from './add-contact/add-contact.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { CommonFormModule } from '../common-form/common-form.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport class ContactsModule {\n  static {\n    this.ɵfac = function ContactsModule_Factory(t) {\n      return new (t || ContactsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, NgSelectModule, ContactsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, ButtonModule, CheckboxModule, DialogModule, TabViewModule, AutoCompleteModule, InputTextModule, InputSwitchModule, EditorModule, ToastModule, ConfirmDialogModule, SharedModule, CommonFormModule, MultiSelectModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactsModule, {\n    declarations: [ContactsComponent, ContactsDetailsComponent, ContactsOverviewComponent, ContactsOpportunitiesComponent, ContactsAttachmentsComponent, ContactsNotesComponent, ContactsActivitiesComponent, ContactsRelationshipsComponent, ContactsTicketsComponent, AddContactComponent],\n    imports: [CommonModule, NgSelectModule, ContactsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, ButtonModule, CheckboxModule, DialogModule, TabViewModule, AutoCompleteModule, InputTextModule, InputSwitchModule, EditorModule, ToastModule, ConfirmDialogModule, SharedModule, CommonFormModule, MultiSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgSelectModule", "ContactsRoutingModule", "FormsModule", "ReactiveFormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "ContactsComponent", "ContactsDetailsComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "InputSwitchModule", "TabViewModule", "EditorModule", "DialogModule", "ToastModule", "ConfirmDialogModule", "MessageService", "ConfirmationService", "ContactsOverviewComponent", "ContactsOpportunitiesComponent", "ContactsAttachmentsComponent", "ContactsNotesComponent", "ContactsActivitiesComponent", "ContactsRelationshipsComponent", "ContactsTicketsComponent", "AddContactComponent", "SharedModule", "CheckboxModule", "CommonFormModule", "MultiSelectModule", "ContactsModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ContactsRoutingModule } from './contacts-routing.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ContactsComponent } from './contacts.component';\r\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\r\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\r\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\r\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\r\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\r\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\r\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\r\nimport { AddContactComponent } from './add-contact/add-contact.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { CommonFormModule } from '../common-form/common-form.module';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ContactsComponent,\r\n    ContactsDetailsComponent,\r\n    ContactsOverviewComponent,\r\n    ContactsOpportunitiesComponent,\r\n    ContactsAttachmentsComponent,\r\n    ContactsNotesComponent,\r\n    ContactsActivitiesComponent,\r\n    ContactsRelationshipsComponent,\r\n    ContactsTicketsComponent,\r\n    AddContactComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    NgSelectModule,\r\n    ContactsRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    DialogModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    InputTextModule,\r\n    InputSwitchModule,\r\n    EditorModule,\r\n    ToastModule,\r\n    ConfirmDialogModule,\r\n    SharedModule,\r\n    CommonFormModule,\r\n    MultiSelectModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class ContactsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,2BAA2B,QAAQ,sEAAsE;AAClH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,iBAAiB,QAAQ,qBAAqB;;AAyCvD,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACd,cAAc,EAAEC,mBAAmB,CAAC;MAAAc,OAAA,GAvB9CnC,YAAY,EACZC,cAAc,EACdC,qBAAqB,EACrBG,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXL,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdM,YAAY,EACZmB,cAAc,EACdd,YAAY,EACZF,aAAa,EACbJ,kBAAkB,EAClBE,eAAe,EACfC,iBAAiB,EACjBE,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBW,YAAY,EACZE,gBAAgB,EAChBC,iBAAiB;IAAA;EAAA;;;2EAIRC,cAAc;IAAAE,YAAA,GArCvB3B,iBAAiB,EACjBC,wBAAwB,EACxBY,yBAAyB,EACzBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,sBAAsB,EACtBC,2BAA2B,EAC3BC,8BAA8B,EAC9BC,wBAAwB,EACxBC,mBAAmB;IAAAM,OAAA,GAGnBnC,YAAY,EACZC,cAAc,EACdC,qBAAqB,EACrBG,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXL,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdM,YAAY,EACZmB,cAAc,EACdd,YAAY,EACZF,aAAa,EACbJ,kBAAkB,EAClBE,eAAe,EACfC,iBAAiB,EACjBE,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBW,YAAY,EACZE,gBAAgB,EAChBC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}