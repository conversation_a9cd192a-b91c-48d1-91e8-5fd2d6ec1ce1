{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./prospects.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction ProspectsComponent_ng_template_20_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsComponent_ng_template_20_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 30);\n    i0.ɵɵlistener(\"click\", function ProspectsComponent_ng_template_20_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 24);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ProspectsComponent_ng_template_20_ng_container_8_i_4_Template, 1, 1, \"i\", 25)(5, ProspectsComponent_ng_template_20_ng_container_8_i_5_Template, 1, 0, \"i\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction ProspectsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 22);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 23);\n    i0.ɵɵlistener(\"click\", function ProspectsComponent_ng_template_20_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"bp_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 24);\n    i0.ɵɵtext(5, \" Prospect ID \");\n    i0.ɵɵtemplate(6, ProspectsComponent_ng_template_20_i_6_Template, 1, 1, \"i\", 25)(7, ProspectsComponent_ng_template_20_i_7_Template, 1, 0, \"i\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ProspectsComponent_ng_template_20_ng_container_8_Template, 6, 4, \"ng-container\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"bp_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"bp_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/prospects/\" + prospect_r6.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.email_address) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.city_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.country) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.contact_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.phone_number) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.partner_role) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 35);\n    i0.ɵɵtemplate(3, ProspectsComponent_ng_template_21_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 36)(4, ProspectsComponent_ng_template_21_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 36)(5, ProspectsComponent_ng_template_21_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 36)(6, ProspectsComponent_ng_template_21_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 36)(7, ProspectsComponent_ng_template_21_ng_container_5_ng_container_7_Template, 2, 1, \"ng-container\", 36)(8, ProspectsComponent_ng_template_21_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 36)(9, ProspectsComponent_ng_template_21_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"city_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"country\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_role\");\n  }\n}\nfunction ProspectsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 31)(1, \"td\", 32);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ProspectsComponent_ng_template_21_ng_container_5_Template, 10, 8, \"ng-container\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", prospect_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/prospects/\" + prospect_r6.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ProspectsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"No prospects found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"Loading prospects data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProspectsComponent = /*#__PURE__*/(() => {\n  class ProspectsComponent {\n    constructor(prospectsservice, router, messageservice, confirmationservice) {\n      this.prospectsservice = prospectsservice;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.prospects = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n      this.partner_role = '';\n      this.searchInputChanged = new Subject();\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'bp_full_name',\n        header: 'Name'\n      }, {\n        field: 'email_address',\n        header: 'Email'\n      }, {\n        field: 'city_name',\n        header: 'City'\n      }, {\n        field: 'country',\n        header: 'Country'\n      }, {\n        field: 'contact_name',\n        header: 'Contact'\n      }, {\n        field: 'phone_number',\n        header: 'Phone'\n      }, {\n        field: 'partner_role',\n        header: 'Owner'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.prospects.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.searchInputChanged.pipe(debounceTime(500),\n      // Adjust delay here (ms)\n      distinctUntilChanged()).subscribe(term => {\n        this.globalSearchTerm = term;\n        this.loadProspects({\n          first: 0,\n          rows: 15\n        });\n      });\n      this.breadcrumbitems = [{\n        label: 'Prospects',\n        routerLink: ['/store/prospects']\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'All',\n        code: 'ALL'\n      }, {\n        name: 'My Prospects',\n        code: 'MP'\n      }, {\n        name: 'Obsolete Prospects',\n        code: 'OP'\n      }];\n      this.selectedActions = {\n        name: 'All',\n        code: 'ALL'\n      };\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadProspects(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      const obsolete = this.selectedActions?.code === 'OP';\n      const myprospect = this.selectedActions?.code === 'MP';\n      this.prospectsservice.getProspects(page, pageSize, sortField, sortOrder, this.globalSearchTerm, obsolete, myprospect).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          let prospects = response?.data.map(prospect => {\n            const defaultAddress = prospect.addresses?.find(address => {\n              return address.address_usages.find(usage => usage.address_usage === 'XXDEFAULT');\n            });\n            const partner_role = prospect?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n            return {\n              ...prospect,\n              contact_name: (prospect?.contact_companies?.[0]?.business_partner_person?.first_name || '') + ' ' + (prospect?.contact_companies?.[0]?.business_partner_person?.last_name || '-'),\n              city_name: defaultAddress?.city_name || '-',\n              country: defaultAddress?.country || '-',\n              email_address: defaultAddress?.emails?.[0]?.email_address || '-',\n              phone_number: (defaultAddress?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number || '-',\n              partner_role: partner_role?.business_partner?.bp_full_name || null\n            };\n          }) || [];\n          this.prospects = prospects;\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching prospects', error);\n          this.loading = false;\n        }\n      });\n    }\n    onActionChange() {\n      // Re-trigger the lazy load with current dt1 state\n      const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n        first: 0,\n        rows: 15\n      };\n      this.loadProspects(dt1State);\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.prospectsservice.delete(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.refresh();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    refresh() {\n      this.loadProspects({\n        first: 0,\n        rows: 15\n      });\n    }\n    signup() {\n      this.router.navigate(['/store/prospects/create']);\n    }\n    onSearchInputChange(event) {\n      const input = event.target.value;\n      this.searchInputChanged.next(input);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ProspectsComponent_Factory(t) {\n        return new (t || ProspectsComponent)(i0.ɵɵdirectiveInject(i1.ProspectsService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProspectsComponent,\n        selectors: [[\"app-prospects\"]],\n        viewQuery: function ProspectsComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n          }\n        },\n        decls: 25,\n        vars: 19,\n        consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [\"position\", \"top-right\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Prospect\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"text-blue-600\", \"font-medium\", \"underline\", \"cursor-pointer\", 3, \"routerLink\"], [\"colspan\", \"9\", 1, \"border-round-left-lg\", \"pl-3\"]],\n        template: function ProspectsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"p-toast\", 2);\n            i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"span\", 9)(8, \"input\", 10, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_input_ngModelChange_8_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function ProspectsComponent_Template_input_input_8_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"i\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"p-dropdown\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_p_dropdown_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"onChange\", function ProspectsComponent_Template_p_dropdown_onChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onActionChange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function ProspectsComponent_Template_button_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.signup());\n            });\n            i0.ɵɵelementStart(13, \"span\", 14);\n            i0.ɵɵtext(14, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15, \" Create \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"p-multiSelect\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_p_multiSelect_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 16)(18, \"p-table\", 17, 1);\n            i0.ɵɵlistener(\"onLazyLoad\", function ProspectsComponent_Template_p_table_onLazyLoad_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadProspects($event));\n            })(\"onColReorder\", function ProspectsComponent_Template_p_table_onColReorder_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(20, ProspectsComponent_ng_template_20_Template, 9, 3, \"ng-template\", 18)(21, ProspectsComponent_ng_template_21_Template, 6, 4, \"ng-template\", 19)(22, ProspectsComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20)(23, ProspectsComponent_ng_template_23_Template, 3, 0, \"ng-template\", 21);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(24, \"p-confirmDialog\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.prospects)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i2.RouterLink, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i6.TableCheckbox, i6.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.Toast, i10.ConfirmDialog, i11.MultiSelect],\n        styles: [\".surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{object-fit:cover}.surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";left:0;top:0;width:100%;height:100%;background:linear-gradient(0deg,rgba(0,0,0,.4392156863),transparent);mix-blend-mode:multiply}.home-box-list[_ngcontent-%COMP%]{max-width:1200px}.home-box-list[_ngcontent-%COMP%]   .home-box[_ngcontent-%COMP%]{background:var(--surface-b)}\"]\n      });\n    }\n  }\n  return ProspectsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}