{"ast": null, "code": "import { Subject, of } from 'rxjs';\nimport { takeUntil, tap, switchMap, map, catchError, shareReplay } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dialog\";\nconst _c0 = [\"TypeSelect\"];\nconst _c1 = [\"dt\"];\nconst _c2 = () => ({\n  width: \"70vw\",\n  minWidth: \"400px\"\n});\nconst _c3 = () => ({\n  \"640px\": \"95vw\",\n  \"1024px\": \"75vw\"\n});\nconst _c4 = () => ({\n  height: \"3rem\"\n});\nconst _c5 = () => [];\nconst _c6 = () => [5, 10, 20];\nconst _c7 = () => [\"activity_id\", \"document_type\", \"subject\", \"main_account_party_id\"];\nconst _c8 = a0 => ({\n  \"row-selected\": a0\n});\nfunction SalesCallRelatedItemsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 31)(6, \"div\", 29);\n    i0.ɵɵtext(7, \" Type \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 33)(10, \"div\", 29);\n    i0.ɵɵtext(11, \" Responsible \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 35);\n    i0.ɵɵtext(14, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36);\n    i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_ng_template_8_Template_tr_click_0_listener() {\n      const related_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.navigateToRelatedItemDetail(related_r3));\n    });\n    i0.ɵɵelementStart(1, \"td\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 35)(8, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_ng_template_8_Template_button_click_8_listener($event) {\n      const related_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r3.confirmRemove(related_r3));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const related_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r3 == null ? null : related_r3.activity_transaction == null ? null : related_r3.activity_transaction.subject) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityDocumentType\", related_r3 == null ? null : related_r3.type_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r3 == null ? null : related_r3.partner_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"No related items found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"Loading related items data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_30_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"p-sortIcon\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"field\", col_r5.field);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_30_th_1_Template, 3, 3, \"th\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.displayedColumns);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    const rowData_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityDocumentType\", rowData_r7[col_r6.field]), \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    const rowData_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", rowData_r7[col_r6.field], \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_container_1_Template, 2, 1, \"ng-container\", 45)(2, SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const normalCell_r8 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r6.field === \"document_type\")(\"ngIfElse\", normalCell_r8);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 43);\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_31_td_1_Template, 4, 2, \"td\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowData_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"pSelectableRow\", rowData_r7)(\"ngClass\", i0.ɵɵpureFunction1(3, _c8, (ctx_r3.selectedItem == null ? null : ctx_r3.selectedItem.activity_id) === rowData_r7.activity_id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.displayedColumns);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 46);\n    i0.ɵɵtext(2, \"No items found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 46);\n    i0.ɵɵtext(2, \"Loading items data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SalesCallRelatedItemsComponent = /*#__PURE__*/(() => {\n  class SalesCallRelatedItemsComponent {\n    constructor(activitiesservice, formBuilder, router, route, cdr, messageservice, confirmationservice) {\n      this.activitiesservice = activitiesservice;\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this.route = route;\n      this.cdr = cdr;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.relateditemsdetails = null;\n      this.activity_id = '';\n      this.account_id = '';\n      this.addDialogVisible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.ItemDataLoading = false;\n      this.ItemInput$ = new Subject();\n      this.selectedItem = null;\n      this.displayedColumns = [];\n      this.dropdowns = {\n        activityDocumentType: []\n      };\n      this.RelatedItemsForm = this.formBuilder.group({\n        type_code: ['', Validators.required],\n        activity_transaction_id: [null]\n      });\n    }\n    ngOnInit() {\n      this.loadItemDataOnTypeChange();\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM');\n      this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.activity_id = response.activity_id || '';\n          this.account_id = response.business_partner?.bp_id || '';\n          const allItems = response.follow_up_and_related_items || [];\n          this.relateditemsdetails = allItems.filter(item => item.btd_role_code === '1').map(item => {\n            const partnerFn = item.activity_transaction?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n            const partnerName = partnerFn?.bp_full_name || null;\n            return {\n              ...item,\n              partner_name: partnerName\n            };\n          });\n        }\n      });\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadItemDataOnTypeChange() {\n      this.ItemData$ = this.RelatedItemsForm.get('type_code').valueChanges.pipe(tap(type => {\n        this.RelatedItemsForm.get('activity_transaction_id')?.reset();\n        this.setDisplayedColumns(type);\n        if (this.TypeSelect) {\n          this.TypeSelect.clearModel();\n        }\n        this.ItemDataLoading = true;\n      }), switchMap(type => {\n        if (!type) {\n          this.ItemDataLoading = false;\n          return of([]);\n        }\n        const params = this.getParamsByRole(type);\n        if (!params) {\n          this.ItemDataLoading = false;\n          return of([]);\n        }\n        return this.activitiesservice.getActivityCodeWise(params).pipe(map(res => res.data || []),\n        // <-- Extract array here\n        catchError(() => {\n          this.ItemDataLoading = false;\n          return of([]);\n        }), tap(() => this.ItemDataLoading = false));\n      }), shareReplay(1));\n    }\n    setDisplayedColumns(type) {\n      switch (type) {\n        case '0006':\n          // Phone Call\n          this.displayedColumns = [{\n            field: 'activity_id',\n            header: 'Reference ID'\n          }, {\n            field: 'document_type',\n            header: 'Task Type'\n          }, {\n            field: 'subject',\n            header: 'Description'\n          }, {\n            field: 'main_account_party_id',\n            header: 'Account ID'\n          }];\n          break;\n        case '0002':\n          // Activity Task\n          this.displayedColumns = [{\n            field: 'activity_id',\n            header: 'Reference ID'\n          }, {\n            field: 'document_type',\n            header: 'Task Type'\n          }, {\n            field: 'subject',\n            header: 'Description'\n          }, {\n            field: 'main_account_party_id',\n            header: 'Account ID'\n          }];\n          break;\n        case '0005':\n          // Opportunity\n          this.displayedColumns = [{\n            field: 'activity_id',\n            header: 'Reference ID'\n          }, {\n            field: 'document_type',\n            header: 'Task Type'\n          }, {\n            field: 'subject',\n            header: 'Description'\n          }, {\n            field: 'main_account_party_id',\n            header: 'Account ID'\n          }];\n          break;\n        default:\n          this.displayedColumns = [];\n      }\n    }\n    onSelectReference() {\n      if (!this.selectedItem) {\n        this.messageservice.add({\n          severity: 'warn',\n          detail: 'Please select a record to continue.'\n        });\n        return;\n      }\n      this.RelatedItemsForm.patchValue({\n        activity_transaction_id: this.selectedItem.activity_id\n      });\n      this.onSubmit();\n    }\n    getParamsByRole(type) {\n      if (!type) return null;\n      switch (type) {\n        case '0006':\n          return {\n            'filters[document_type][$eq]': '0006',\n            'filters[main_account_party_id][$eq]': this.account_id\n          };\n        case '0002':\n          return {\n            'filters[document_type][$eq]': '0002',\n            'filters[main_account_party_id][$eq]': this.account_id\n          };\n        case '0005':\n          return {\n            'filters[document_type][$eq]': '0005',\n            'filters[main_account_party_id][$eq]': this.account_id\n          };\n        default:\n          return null;\n      }\n    }\n    onSelectionChange(selected) {\n      this.selectedItem = selected;\n      if (selected) {\n        this.RelatedItemsForm.patchValue({\n          activity_transaction_id: this.selectedItem.activity_id || null\n        });\n      } else {\n        this.RelatedItemsForm.patchValue({\n          activity_transaction_id: null\n        });\n      }\n    }\n    onSubmit() {\n      this.submitted = true;\n      if (this.RelatedItemsForm.invalid) {\n        return;\n      }\n      this.saving = true;\n      const value = {\n        ...this.RelatedItemsForm.value\n      };\n      const data = {\n        activity_id: this.activity_id,\n        activity_transaction_id: value.activity_transaction_id,\n        type_code: value.type_code,\n        btd_role_code: '1'\n      };\n      this.activitiesservice.createRelatedItem(data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.saving = false;\n          this.addDialogVisible = false;\n          this.RelatedItemsForm.reset();\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Related Item Added successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.saving = false;\n          this.addDialogVisible = true;\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => this.remove(item)\n      });\n    }\n    remove(item) {\n      this.activitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    get f() {\n      return this.RelatedItemsForm.controls;\n    }\n    showNewDialog(position) {\n      this.position = position;\n      this.addDialogVisible = true;\n      this.submitted = false;\n      this.RelatedItemsForm.reset();\n      const options = this.dropdowns['activityDocumentType'];\n      if (options?.length > 0) {\n        this.RelatedItemsForm.get('type_code')?.setValue(options[0].value);\n      }\n    }\n    onGlobalFilter(event) {\n      const value = event.target.value;\n      this.table.filterGlobal(value, 'contains');\n      // Ensure the view updates immediately\n      this.cdr.detectChanges();\n    }\n    navigateToRelatedItemDetail(item) {\n      this.router.navigate([item.activity_transaction?.activity_id], {\n        relativeTo: this.route,\n        state: {\n          relateditemdata: item\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallRelatedItemsComponent_Factory(t) {\n        return new (t || SalesCallRelatedItemsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallRelatedItemsComponent,\n        selectors: [[\"app-sales-call-related-items\"]],\n        viewQuery: function SalesCallRelatedItemsComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.TypeSelect = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n          }\n        },\n        decls: 37,\n        vars: 38,\n        consts: [[\"dt\", \"\"], [\"normalCell\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"header\", \"Add Reference\", 3, \"visibleChange\", \"visible\", \"modal\", \"closable\", \"resizable\", \"draggable\", \"breakpoints\"], [1, \"p-fluid\", 3, \"formGroup\"], [1, \"flex\", \"gap-3\", \"mb-3\"], [1, \"w-8\", \"flex\", \"flex-column\"], [\"for\", \"type_code\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"mb-1\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"formControlName\", \"type_code\", \"placeholder\", \"Select Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"w-full text-sm\", 3, \"options\"], [1, \"w-4\", \"flex\", \"flex-column\"], [\"for\", \"search\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"mb-2\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"search\", \"styleClass\", \"w-full text-sm\", \"placeholder\", \"Search...\", 3, \"input\"], [1, \"p-mb-4\"], [\"selectionMode\", \"single\", \"dataKey\", \"activity_id\", \"scrollHeight\", \"280px\", \"styleClass\", \"p-datatable-gridlines\", 3, \"selectionChange\", \"value\", \"paginator\", \"rows\", \"rowsPerPageOptions\", \"responsiveLayout\", \"selection\", \"loading\", \"scrollable\", \"rowHover\", \"globalFilterFields\"], [1, \"flex\", \"justify-content-end\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-sm\", \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-sm\", \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\", \"disabled\"], [\"pSortableColumn\", \"activity_transaction.subject\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"activity_transaction.subject\"], [\"pSortableColumn\", \"type_code\"], [\"field\", \"type_code\"], [\"pSortableColumn\", \"partner_name\"], [\"field\", \"partner_name\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"4\", 1, \"border-round-left-lg\"], [3, \"pSortableColumn\", 4, \"ngFor\", \"ngForOf\"], [3, \"pSortableColumn\"], [3, \"field\"], [3, \"pSelectableRow\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\", \"ngIfElse\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\"]],\n        template: function SalesCallRelatedItemsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h4\", 4);\n            i0.ɵɵtext(3, \"Related Items\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 5);\n            i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_p_button_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showNewDialog(\"right\"));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 6)(6, \"p-table\", 7);\n            i0.ɵɵtemplate(7, SalesCallRelatedItemsComponent_ng_template_7_Template, 15, 0, \"ng-template\", 8)(8, SalesCallRelatedItemsComponent_ng_template_8_Template, 9, 3, \"ng-template\", 9)(9, SalesCallRelatedItemsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10)(10, SalesCallRelatedItemsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"p-dialog\", 12);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallRelatedItemsComponent_Template_p_dialog_visibleChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(12, \"form\", 13)(13, \"div\", 14)(14, \"div\", 15)(15, \"label\", 16)(16, \"span\", 17);\n            i0.ɵɵtext(17, \"supervisor_account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(18, \" Type \");\n            i0.ɵɵelementStart(19, \"span\", 18);\n            i0.ɵɵtext(20, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(21, \"p-dropdown\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 20)(23, \"label\", 21);\n            i0.ɵɵtext(24, \" Search \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"input\", 22);\n            i0.ɵɵlistener(\"input\", function SalesCallRelatedItemsComponent_Template_input_input_25_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onGlobalFilter($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 23)(27, \"p-table\", 24, 0);\n            i0.ɵɵpipe(29, \"async\");\n            i0.ɵɵtwoWayListener(\"selectionChange\", function SalesCallRelatedItemsComponent_Template_p_table_selectionChange_27_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedItem, $event) || (ctx.selectedItem = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(30, SalesCallRelatedItemsComponent_ng_template_30_Template, 2, 1, \"ng-template\", 8)(31, SalesCallRelatedItemsComponent_ng_template_31_Template, 2, 5, \"ng-template\", 9)(32, SalesCallRelatedItemsComponent_ng_template_32_Template, 3, 0, \"ng-template\", 10)(33, SalesCallRelatedItemsComponent_ng_template_33_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"div\", 25)(35, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_35_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addDialogVisible = false);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_36_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectReference());\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.relateditemsdetails)(\"rows\", 10)(\"paginator\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(32, _c2));\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n            i0.ɵɵproperty(\"modal\", true)(\"closable\", true)(\"resizable\", true)(\"draggable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(33, _c3));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.RelatedItemsForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(34, _c4));\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵstyleProp(\"height\", \"3rem\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind1(29, 30, ctx.ItemData$) || i0.ɵɵpureFunction0(35, _c5))(\"paginator\", true)(\"rows\", 5)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(36, _c6))(\"responsiveLayout\", \"scroll\");\n            i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedItem);\n            i0.ɵɵproperty(\"loading\", ctx.ItemDataLoading)(\"scrollable\", true)(\"rowHover\", false)(\"globalFilterFields\", i0.ɵɵpureFunction0(37, _c7));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedItem);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i6.Table, i4.PrimeTemplate, i6.SortableColumn, i6.SelectableRow, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.InputText, i11.Dialog, i5.AsyncPipe],\n        styles: [\"[_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr.row-selected{background-color:#f0f0f0!important;color:#000!important}\"]\n      });\n    }\n  }\n  return SalesCallRelatedItemsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}