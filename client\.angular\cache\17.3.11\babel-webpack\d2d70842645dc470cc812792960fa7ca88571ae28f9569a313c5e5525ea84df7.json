{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../service/app.layout.service\";\nimport * as i2 from \"../app.menu.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/sidebar\";\nimport * as i6 from \"primeng/radiobutton\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputswitch\";\nconst _c0 = a0 => ({\n  \"background-color\": a0\n});\nconst _c1 = a0 => ({\n  \"text-primary-500\": a0\n});\nfunction AppConfigComponent_div_6_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction AppConfigComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AppConfigComponent_div_6_Template_button_click_1_listener() {\n      const theme_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeTheme(theme_r2.name));\n    });\n    i0.ɵɵtemplate(2, AppConfigComponent_div_6_i_2_Template, 1, 0, \"i\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const theme_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c0, theme_r2.color));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", theme_r2.name == ctx_r2.layoutService.config().theme);\n  }\n}\nfunction AppConfigComponent_i_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const s_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, s_r4 === ctx_r2.scale));\n  }\n}\nfunction AppConfigComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h5\");\n    i0.ɵɵtext(2, \"Menu Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 21)(5, \"p-radioButton\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_ng_container_14_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.menuMode, $event) || (ctx_r2.menuMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 23);\n    i0.ɵɵtext(7, \"Static\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.menuMode);\n  }\n}\nfunction AppConfigComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h5\");\n    i0.ɵɵtext(2, \"Ripple Effect\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputSwitch\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_ng_container_25_Template_p_inputSwitch_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.ripple, $event) || (ctx_r2.ripple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.ripple);\n  }\n}\nexport class AppConfigComponent {\n  constructor(layoutService, menuService) {\n    this.layoutService = layoutService;\n    this.menuService = menuService;\n    this.minimal = false;\n    this.componentThemes = [];\n    this.layoutThemes = [];\n    this.scales = [12, 13, 14, 15, 16];\n  }\n  get visible() {\n    return this.layoutService.state.configSidebarVisible;\n  }\n  set visible(_val) {\n    this.layoutService.state.configSidebarVisible = _val;\n  }\n  get scale() {\n    return this.layoutService.config().scale;\n  }\n  set scale(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      scale: _val\n    }));\n  }\n  get menuMode() {\n    return this.layoutService.config().menuMode;\n  }\n  set menuMode(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      menuMode: _val\n    }));\n    if (this.layoutService.isSlimPlus() || this.layoutService.isSlim() || this.layoutService.isHorizontal()) {\n      this.menuService.reset();\n    }\n  }\n  get colorScheme() {\n    return this.layoutService.config().colorScheme;\n  }\n  set colorScheme(_val) {\n    console.log(_val);\n    this.layoutService.config.update(config => ({\n      ...config,\n      colorScheme: _val\n    }));\n  }\n  get ripple() {\n    return this.layoutService.config().ripple;\n  }\n  set ripple(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      ripple: _val\n    }));\n  }\n  get theme() {\n    return this.layoutService.config().theme;\n  }\n  set theme(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      theme: _val\n    }));\n  }\n  ngOnInit() {\n    this.componentThemes = [{\n      name: 'blue',\n      color: '#0F8BFD'\n    }, {\n      name: 'green',\n      color: '#0BD18A'\n    }, {\n      name: 'magenta',\n      color: '#EC4DBC'\n    }, {\n      name: 'orange',\n      color: '#FD9214'\n    }, {\n      name: 'purple',\n      color: '#873EFE'\n    }, {\n      name: 'red',\n      color: '#FC6161'\n    }, {\n      name: 'teal',\n      color: '#00D0DE'\n    }, {\n      name: 'yellow',\n      color: '#EEE500'\n    }, {\n      name: 'snjya',\n      color: '#184997'\n    }];\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  changeColorScheme(colorScheme) {\n    this.colorScheme = colorScheme;\n  }\n  changeTheme(theme) {\n    this.theme = theme;\n  }\n  decrementScale() {\n    this.scale--;\n  }\n  incrementScale() {\n    this.scale++;\n  }\n  static {\n    this.ɵfac = function AppConfigComponent_Factory(t) {\n      return new (t || AppConfigComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.MenuService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppConfigComponent,\n      selectors: [[\"app-config\"]],\n      inputs: {\n        minimal: \"minimal\"\n      },\n      decls: 26,\n      vars: 10,\n      consts: [[\"type\", \"button\", 1, \"layout-config-button\", \"p-link\", 3, \"click\"], [1, \"pi\", \"pi-cog\"], [\"position\", \"right\", \"styleClass\", \"layout-config-sidebar w-18rem\", 3, \"visibleChange\", \"visible\", \"transitionOptions\"], [1, \"flex\", \"flex-wrap\", \"row-gap-3\"], [\"class\", \"w-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [\"icon\", \"pi pi-minus\", \"type\", \"button\", \"pButton\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"w-2rem\", \"h-2rem\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"flex\", \"gap-2\", \"align-items-center\"], [\"class\", \"pi pi-circle-fill text-300\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"icon\", \"pi pi-plus\", \"type\", \"button\", \"pButton\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"w-2rem\", \"h-2rem\", \"ml-2\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"field-radiobutton\"], [\"name\", \"colorScheme\", \"value\", \"light\", \"inputId\", \"mode-light\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode-light\"], [\"name\", \"colorScheme\", \"value\", \"dark\", \"inputId\", \"mode-dark\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode-dark\"], [1, \"w-3\"], [\"type\", \"button\", 1, \"cursor-pointer\", \"p-link\", \"w-2rem\", \"h-2rem\", \"border-circle\", \"flex-shrink-0\", \"flex\", \"align-items-center\", \"justify-content-center\", 3, \"click\", \"ngStyle\"], [\"class\", \"pi pi-check text-white\", 4, \"ngIf\"], [1, \"pi\", \"pi-check\", \"text-white\"], [1, \"pi\", \"pi-circle-fill\", \"text-300\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"w-6\"], [\"name\", \"menuMode\", \"value\", \"static\", \"inputId\", \"mode1\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode1\"], [3, \"ngModelChange\", \"ngModel\"]],\n      template: function AppConfigComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_0_listener() {\n            return ctx.onConfigButtonClick();\n          });\n          i0.ɵɵelement(1, \"i\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"p-sidebar\", 2);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AppConfigComponent_Template_p_sidebar_visibleChange_2_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(3, \"h5\");\n          i0.ɵɵtext(4, \"Themes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, AppConfigComponent_div_6_Template, 3, 4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h5\");\n          i0.ɵɵtext(8, \"Scale\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_10_listener() {\n            return ctx.decrementScale();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtemplate(12, AppConfigComponent_i_12_Template, 1, 3, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_13_listener() {\n            return ctx.incrementScale();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, AppConfigComponent_ng_container_14_Template, 8, 1, \"ng-container\", 10);\n          i0.ɵɵelementStart(15, \"h5\");\n          i0.ɵɵtext(16, \"Color Scheme\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"p-radioButton\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_Template_p_radioButton_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.colorScheme, $event) || (ctx.colorScheme = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 13);\n          i0.ɵɵtext(20, \"Light\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"p-radioButton\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_Template_p_radioButton_ngModelChange_22_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.colorScheme, $event) || (ctx.colorScheme = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"label\", 15);\n          i0.ɵɵtext(24, \"Dark\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, AppConfigComponent_ng_container_25_Template, 4, 1, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"transitionOptions\", \".3s cubic-bezier(0, 0, 0.2, 1)\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.componentThemes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.scale === ctx.scales[0]);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.scales);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.scale === ctx.scales[ctx.scales.length - 1]);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.minimal);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.colorScheme);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.colorScheme);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.minimal);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgStyle, i4.NgControlStatus, i4.NgModel, i5.Sidebar, i6.RadioButton, i7.ButtonDirective, i8.InputSwitch],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "AppConfigComponent_div_6_Template_button_click_1_listener", "theme_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "changeTheme", "name", "ɵɵtemplate", "AppConfigComponent_div_6_i_2_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "color", "layoutService", "config", "theme", "_c1", "s_r4", "scale", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵtwoWayListener", "AppConfigComponent_ng_container_14_Template_p_radioButton_ngModelChange_5_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "menuMode", "ɵɵtwoWayProperty", "AppConfigComponent_ng_container_25_Template_p_inputSwitch_ngModelChange_3_listener", "_r6", "ripple", "AppConfigComponent", "constructor", "menuService", "minimal", "componentThemes", "layoutThemes", "scales", "visible", "state", "configSidebarVisible", "_val", "update", "isSlimPlus", "isSlim", "isHorizontal", "reset", "colorScheme", "console", "log", "ngOnInit", "onConfigButtonClick", "showConfigSidebar", "changeColorScheme", "decrementScale", "incrementScale", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "MenuService", "selectors", "inputs", "decls", "vars", "consts", "template", "AppConfigComponent_Template", "rf", "ctx", "AppConfigComponent_Template_button_click_0_listener", "AppConfigComponent_Template_p_sidebar_visibleChange_2_listener", "AppConfigComponent_div_6_Template", "AppConfigComponent_Template_button_click_10_listener", "AppConfigComponent_i_12_Template", "AppConfigComponent_Template_button_click_13_listener", "AppConfigComponent_ng_container_14_Template", "AppConfigComponent_Template_p_radioButton_ngModelChange_18_listener", "AppConfigComponent_Template_p_radioButton_ngModelChange_22_listener", "AppConfigComponent_ng_container_25_Template", "length"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\config\\app.config.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\config\\app.config.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { MenuService } from '../app.menu.service';\r\nimport {\r\n    ColorScheme,\r\n    LayoutService,\r\n    MenuMode,\r\n} from '../service/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-config',\r\n    templateUrl: './app.config.component.html',\r\n})\r\nexport class AppConfigComponent implements OnInit {\r\n    @Input() minimal: boolean = false;\r\n\r\n    componentThemes: any[] = [];\r\n\r\n    layoutThemes: any[] = [];\r\n\r\n    scales: number[] = [12, 13, 14, 15, 16];\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public menuService: MenuService\r\n    ) {}\r\n\r\n    get visible(): boolean {\r\n        return this.layoutService.state.configSidebarVisible;\r\n    }\r\n    set visible(_val: boolean) {\r\n        this.layoutService.state.configSidebarVisible = _val;\r\n    }\r\n\r\n    get scale(): number {\r\n        return this.layoutService.config().scale;\r\n    }\r\n    set scale(_val: number) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            scale: _val,\r\n        }));\r\n    }\r\n\r\n    get menuMode(): MenuMode {\r\n        return this.layoutService.config().menuMode;\r\n    }\r\n    set menuMode(_val: MenuMode) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            menuMode: _val,\r\n        }));\r\n        if (\r\n            this.layoutService.isSlimPlus() ||\r\n            this.layoutService.isSlim() ||\r\n            this.layoutService.isHorizontal()\r\n        ) {\r\n            this.menuService.reset();\r\n        }\r\n    }\r\n\r\n    get colorScheme(): ColorScheme {\r\n        return this.layoutService.config().colorScheme;\r\n    }\r\n    set colorScheme(_val: ColorScheme) {\r\n        console.log(_val);\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            colorScheme: _val,\r\n        }));\r\n    }\r\n\r\n    get ripple(): boolean {\r\n        return this.layoutService.config().ripple;\r\n    }\r\n    set ripple(_val: boolean) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            ripple: _val,\r\n        }));\r\n    }\r\n\r\n    get theme(): string {\r\n        return this.layoutService.config().theme;\r\n    }\r\n    set theme(_val: string) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            theme: _val,\r\n        }));\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.componentThemes = [\r\n            { name: 'blue', color: '#0F8BFD' },\r\n            { name: 'green', color: '#0BD18A' },\r\n            { name: 'magenta', color: '#EC4DBC' },\r\n            { name: 'orange', color: '#FD9214' },\r\n            { name: 'purple', color: '#873EFE' },\r\n            { name: 'red', color: '#FC6161' },\r\n            { name: 'teal', color: '#00D0DE' },\r\n            { name: 'yellow', color: '#EEE500' },\r\n            { name: 'snjya', color: '#184997' },\r\n        ];\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n\r\n    changeColorScheme(colorScheme: ColorScheme) {\r\n        this.colorScheme = colorScheme;\r\n    }\r\n\r\n    changeTheme(theme: string) {\r\n        this.theme = theme;\r\n    }\r\n\r\n    decrementScale() {\r\n        this.scale--;\r\n    }\r\n\r\n    incrementScale() {\r\n        this.scale++;\r\n    }\r\n}\r\n", "<button class=\"layout-config-button p-link\" type=\"button\" (click)=\"onConfigButtonClick()\">\r\n    <i class=\"pi pi-cog\"></i>\r\n</button>\r\n\r\n<p-sidebar [(visible)]=\"visible\" position=\"right\" [transitionOptions]=\"'.3s cubic-bezier(0, 0, 0.2, 1)'\" styleClass=\"layout-config-sidebar w-18rem\">\r\n    <h5>Themes</h5>\r\n    <div class=\"flex flex-wrap row-gap-3\">\r\n        <div class=\"w-3\" *ngFor=\"let theme of componentThemes\">\r\n            <button type=\"button\" \r\n                class=\"cursor-pointer p-link w-2rem h-2rem border-circle flex-shrink-0 flex align-items-center justify-content-center\" \r\n                (click)=\"changeTheme(theme.name)\" \r\n                [ngStyle]=\"{'background-color': theme.color}\"><i *ngIf=\"theme.name == this.layoutService.config().theme \" class=\"pi pi-check text-white\"></i></button>\r\n        </div>\r\n    </div>\r\n    <h5>Scale</h5>\r\n    <div class=\"flex align-items-center\">\r\n        <button icon=\"pi pi-minus\" type=\"button\" pButton (click)=\"decrementScale()\" class=\"p-button-text p-button-rounded w-2rem h-2rem mr-2\" [disabled]=\"scale === scales[0]\"></button>\r\n        <div class=\"flex gap-2 align-items-center\">\r\n            <i class=\"pi pi-circle-fill text-300\" *ngFor=\"let s of scales\" [ngClass]=\"{'text-primary-500': s === scale}\"></i>\r\n        </div>\r\n        <button icon=\"pi pi-plus\"  type=\"button\" pButton (click)=\"incrementScale()\" class=\"p-button-text p-button-rounded w-2rem h-2rem ml-2\" [disabled]=\"scale === scales[scales.length - 1]\"></button>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"!minimal\">\r\n        <h5>Menu Type</h5>\r\n        <div class=\"flex flex-wrap row-gap-3\">\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"static\" [(ngModel)]=\"menuMode\" inputId=\"mode1\"></p-radioButton>\r\n                <label for=\"mode1\">Static</label>\r\n            </div>\r\n            <!-- <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"overlay\" [(ngModel)]=\"menuMode\" inputId=\"mode2\"></p-radioButton>\r\n                <label for=\"mode2\">Overlay</label>\r\n            </div>\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"slim\" [(ngModel)]=\"menuMode\" inputId=\"mode3\"></p-radioButton>\r\n                <label for=\"mode3\">Slim</label>\r\n            </div>\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"slim-plus\" [(ngModel)]=\"menuMode\" inputId=\"mode4\"></p-radioButton>\r\n                <label for=\"mode3\">Slim +</label>\r\n            </div>\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"reveal\" [(ngModel)]=\"menuMode\" inputId=\"mode6\"></p-radioButton>\r\n                <label for=\"mode5\">Reveal</label>\r\n            </div>\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"drawer\" [(ngModel)]=\"menuMode\" inputId=\"mode7\"></p-radioButton>\r\n                <label for=\"mode6\">Drawer</label>\r\n            </div>\r\n            <div class=\"flex align-items-center gap-2 w-6\">\r\n                <p-radioButton name=\"menuMode\" value=\"horizontal\" [(ngModel)]=\"menuMode\" inputId=\"mode5\"></p-radioButton>\r\n                <label for=\"mode4\">Horizontal</label>\r\n            </div> -->\r\n        </div>\r\n\r\n    </ng-container>\r\n    \r\n    <h5>Color Scheme</h5>\r\n    <div class=\"field-radiobutton\">\r\n        <p-radioButton name=\"colorScheme\" value=\"light\" [(ngModel)]=\"colorScheme\" inputId=\"mode-light\"></p-radioButton>\r\n        <label for=\"mode-light\">Light</label>\r\n    </div>\r\n    <div class=\"field-radiobutton\">\r\n        <p-radioButton name=\"colorScheme\" value=\"dark\" [(ngModel)]=\"colorScheme\" inputId=\"mode-dark\"></p-radioButton>\r\n        <label for=\"mode-dark\">Dark</label>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"!minimal\">\r\n        <h5>Ripple Effect</h5>\r\n        <p-inputSwitch [(ngModel)]=\"ripple\"></p-inputSwitch>\r\n    </ng-container>\r\n</p-sidebar>"], "mappings": ";;;;;;;;;;;;;;;;;ICW8DA,EAAA,CAAAC,SAAA,YAA+F;;;;;;IAHjJD,EADJ,CAAAE,cAAA,cAAuD,iBAID;IAD9CF,EAAA,CAAAG,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,QAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,QAAA,CAAAQ,IAAA,CAAuB;IAAA,EAAC;IACab,EAAA,CAAAc,UAAA,IAAAC,qCAAA,gBAA2F;IACjJf,EADqJ,CAAAgB,YAAA,EAAS,EACxJ;;;;;IADEhB,EAAA,CAAAiB,SAAA,EAA6C;IAA7CjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAAf,QAAA,CAAAgB,KAAA,EAA6C;IAAKrB,EAAA,CAAAiB,SAAA,EAAqD;IAArDjB,EAAA,CAAAkB,UAAA,SAAAb,QAAA,CAAAQ,IAAA,IAAAJ,MAAA,CAAAa,aAAA,CAAAC,MAAA,GAAAC,KAAA,CAAqD;;;;;IAO3GxB,EAAA,CAAAC,SAAA,YAAiH;;;;;IAAlDD,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,IAAAM,GAAA,EAAAC,IAAA,KAAAjB,MAAA,CAAAkB,KAAA,EAA6C;;;;;;IAKpH3B,EAAA,CAAA4B,uBAAA,GAA+B;IAC3B5B,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAA6B,MAAA,gBAAS;IAAA7B,EAAA,CAAAgB,YAAA,EAAK;IAGVhB,EAFR,CAAAE,cAAA,aAAsC,cACa,wBAC0C;IAAvCF,EAAA,CAAA8B,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA2B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAkC,kBAAA,CAAAzB,MAAA,CAAA0B,QAAA,EAAAH,MAAA,MAAAvB,MAAA,CAAA0B,QAAA,GAAAH,MAAA;MAAA,OAAAhC,EAAA,CAAAW,WAAA,CAAAqB,MAAA;IAAA,EAAsB;IAAiBhC,EAAA,CAAAgB,YAAA,EAAgB;IACrGhB,EAAA,CAAAE,cAAA,gBAAmB;IAAAF,EAAA,CAAA6B,MAAA,aAAM;IA0BjC7B,EA1BiC,CAAAgB,YAAA,EAAQ,EAC/B,EAyBJ;;;;;IA3BgDhB,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAoC,gBAAA,YAAA3B,MAAA,CAAA0B,QAAA,CAAsB;;;;;;IAyChFnC,EAAA,CAAA4B,uBAAA,GAA+B;IAC3B5B,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAA6B,MAAA,oBAAa;IAAA7B,EAAA,CAAAgB,YAAA,EAAK;IACtBhB,EAAA,CAAAE,cAAA,wBAAoC;IAArBF,EAAA,CAAA8B,gBAAA,2BAAAO,mFAAAL,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAAgC,GAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAkC,kBAAA,CAAAzB,MAAA,CAAA8B,MAAA,EAAAP,MAAA,MAAAvB,MAAA,CAAA8B,MAAA,GAAAP,MAAA;MAAA,OAAAhC,EAAA,CAAAW,WAAA,CAAAqB,MAAA;IAAA,EAAoB;IAAChC,EAAA,CAAAgB,YAAA,EAAgB;;;;;IAArChB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAoC,gBAAA,YAAA3B,MAAA,CAAA8B,MAAA,CAAoB;;;AD1D3C,OAAM,MAAOC,kBAAkB;EAS3BC,YACWnB,aAA4B,EAC5BoB,WAAwB;IADxB,KAAApB,aAAa,GAAbA,aAAa;IACb,KAAAoB,WAAW,GAAXA,WAAW;IAVb,KAAAC,OAAO,GAAY,KAAK;IAEjC,KAAAC,eAAe,GAAU,EAAE;IAE3B,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,MAAM,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAKpC;EAEH,IAAIC,OAAOA,CAAA;IACP,OAAO,IAAI,CAACzB,aAAa,CAAC0B,KAAK,CAACC,oBAAoB;EACxD;EACA,IAAIF,OAAOA,CAACG,IAAa;IACrB,IAAI,CAAC5B,aAAa,CAAC0B,KAAK,CAACC,oBAAoB,GAAGC,IAAI;EACxD;EAEA,IAAIvB,KAAKA,CAAA;IACL,OAAO,IAAI,CAACL,aAAa,CAACC,MAAM,EAAE,CAACI,KAAK;EAC5C;EACA,IAAIA,KAAKA,CAACuB,IAAY;IAClB,IAAI,CAAC5B,aAAa,CAACC,MAAM,CAAC4B,MAAM,CAAE5B,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTI,KAAK,EAAEuB;KACV,CAAC,CAAC;EACP;EAEA,IAAIf,QAAQA,CAAA;IACR,OAAO,IAAI,CAACb,aAAa,CAACC,MAAM,EAAE,CAACY,QAAQ;EAC/C;EACA,IAAIA,QAAQA,CAACe,IAAc;IACvB,IAAI,CAAC5B,aAAa,CAACC,MAAM,CAAC4B,MAAM,CAAE5B,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTY,QAAQ,EAAEe;KACb,CAAC,CAAC;IACH,IACI,IAAI,CAAC5B,aAAa,CAAC8B,UAAU,EAAE,IAC/B,IAAI,CAAC9B,aAAa,CAAC+B,MAAM,EAAE,IAC3B,IAAI,CAAC/B,aAAa,CAACgC,YAAY,EAAE,EACnC;MACE,IAAI,CAACZ,WAAW,CAACa,KAAK,EAAE;IAC5B;EACJ;EAEA,IAAIC,WAAWA,CAAA;IACX,OAAO,IAAI,CAAClC,aAAa,CAACC,MAAM,EAAE,CAACiC,WAAW;EAClD;EACA,IAAIA,WAAWA,CAACN,IAAiB;IAC7BO,OAAO,CAACC,GAAG,CAACR,IAAI,CAAC;IACjB,IAAI,CAAC5B,aAAa,CAACC,MAAM,CAAC4B,MAAM,CAAE5B,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTiC,WAAW,EAAEN;KAChB,CAAC,CAAC;EACP;EAEA,IAAIX,MAAMA,CAAA;IACN,OAAO,IAAI,CAACjB,aAAa,CAACC,MAAM,EAAE,CAACgB,MAAM;EAC7C;EACA,IAAIA,MAAMA,CAACW,IAAa;IACpB,IAAI,CAAC5B,aAAa,CAACC,MAAM,CAAC4B,MAAM,CAAE5B,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTgB,MAAM,EAAEW;KACX,CAAC,CAAC;EACP;EAEA,IAAI1B,KAAKA,CAAA;IACL,OAAO,IAAI,CAACF,aAAa,CAACC,MAAM,EAAE,CAACC,KAAK;EAC5C;EACA,IAAIA,KAAKA,CAAC0B,IAAY;IAClB,IAAI,CAAC5B,aAAa,CAACC,MAAM,CAAC4B,MAAM,CAAE5B,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTC,KAAK,EAAE0B;KACV,CAAC,CAAC;EACP;EAEAS,QAAQA,CAAA;IACJ,IAAI,CAACf,eAAe,GAAG,CACnB;MAAE/B,IAAI,EAAE,MAAM;MAAEQ,KAAK,EAAE;IAAS,CAAE,EAClC;MAAER,IAAI,EAAE,OAAO;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACnC;MAAER,IAAI,EAAE,SAAS;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACrC;MAAER,IAAI,EAAE,QAAQ;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACpC;MAAER,IAAI,EAAE,QAAQ;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACpC;MAAER,IAAI,EAAE,KAAK;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACjC;MAAER,IAAI,EAAE,MAAM;MAAEQ,KAAK,EAAE;IAAS,CAAE,EAClC;MAAER,IAAI,EAAE,QAAQ;MAAEQ,KAAK,EAAE;IAAS,CAAE,EACpC;MAAER,IAAI,EAAE,OAAO;MAAEQ,KAAK,EAAE;IAAS,CAAE,CACtC;EACL;EAEAuC,mBAAmBA,CAAA;IACf,IAAI,CAACtC,aAAa,CAACuC,iBAAiB,EAAE;EAC1C;EAEAC,iBAAiBA,CAACN,WAAwB;IACtC,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EAEA5C,WAAWA,CAACY,KAAa;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EAEAuC,cAAcA,CAAA;IACV,IAAI,CAACpC,KAAK,EAAE;EAChB;EAEAqC,cAAcA,CAAA;IACV,IAAI,CAACrC,KAAK,EAAE;EAChB;;;uBA/GSa,kBAAkB,EAAAxC,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlB7B,kBAAkB;MAAA8B,SAAA;MAAAC,MAAA;QAAA5B,OAAA;MAAA;MAAA6B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ/B7E,EAAA,CAAAE,cAAA,gBAA0F;UAAhCF,EAAA,CAAAG,UAAA,mBAAA4E,oDAAA;YAAA,OAASD,GAAA,CAAAlB,mBAAA,EAAqB;UAAA,EAAC;UACrF5D,EAAA,CAAAC,SAAA,WAAyB;UAC7BD,EAAA,CAAAgB,YAAA,EAAS;UAEThB,EAAA,CAAAE,cAAA,mBAAoJ;UAAzIF,EAAA,CAAA8B,gBAAA,2BAAAkD,+DAAAhD,MAAA;YAAAhC,EAAA,CAAAkC,kBAAA,CAAA4C,GAAA,CAAA/B,OAAA,EAAAf,MAAA,MAAA8C,GAAA,CAAA/B,OAAA,GAAAf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAC5BhC,EAAA,CAAAE,cAAA,SAAI;UAAAF,EAAA,CAAA6B,MAAA,aAAM;UAAA7B,EAAA,CAAAgB,YAAA,EAAK;UACfhB,EAAA,CAAAE,cAAA,aAAsC;UAClCF,EAAA,CAAAc,UAAA,IAAAmE,iCAAA,iBAAuD;UAM3DjF,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAE,cAAA,SAAI;UAAAF,EAAA,CAAA6B,MAAA,YAAK;UAAA7B,EAAA,CAAAgB,YAAA,EAAK;UAEVhB,EADJ,CAAAE,cAAA,aAAqC,iBACsI;UAAtHF,EAAA,CAAAG,UAAA,mBAAA+E,qDAAA;YAAA,OAASJ,GAAA,CAAAf,cAAA,EAAgB;UAAA,EAAC;UAA4F/D,EAAA,CAAAgB,YAAA,EAAS;UAChLhB,EAAA,CAAAE,cAAA,cAA2C;UACvCF,EAAA,CAAAc,UAAA,KAAAqE,gCAAA,eAA6G;UACjHnF,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAE,cAAA,iBAAuL;UAAtIF,EAAA,CAAAG,UAAA,mBAAAiF,qDAAA;YAAA,OAASN,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAC/EhE,EAD2L,CAAAgB,YAAA,EAAS,EAC9L;UAENhB,EAAA,CAAAc,UAAA,KAAAuE,2CAAA,2BAA+B;UAmC/BrF,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAA6B,MAAA,oBAAY;UAAA7B,EAAA,CAAAgB,YAAA,EAAK;UAEjBhB,EADJ,CAAAE,cAAA,eAA+B,yBACoE;UAA/CF,EAAA,CAAA8B,gBAAA,2BAAAwD,oEAAAtD,MAAA;YAAAhC,EAAA,CAAAkC,kBAAA,CAAA4C,GAAA,CAAAtB,WAAA,EAAAxB,MAAA,MAAA8C,GAAA,CAAAtB,WAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAAsBhC,EAAA,CAAAgB,YAAA,EAAgB;UAC/GhB,EAAA,CAAAE,cAAA,iBAAwB;UAAAF,EAAA,CAAA6B,MAAA,aAAK;UACjC7B,EADiC,CAAAgB,YAAA,EAAQ,EACnC;UAEFhB,EADJ,CAAAE,cAAA,eAA+B,yBACkE;UAA9CF,EAAA,CAAA8B,gBAAA,2BAAAyD,oEAAAvD,MAAA;YAAAhC,EAAA,CAAAkC,kBAAA,CAAA4C,GAAA,CAAAtB,WAAA,EAAAxB,MAAA,MAAA8C,GAAA,CAAAtB,WAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAAqBhC,EAAA,CAAAgB,YAAA,EAAgB;UAC7GhB,EAAA,CAAAE,cAAA,iBAAuB;UAAAF,EAAA,CAAA6B,MAAA,YAAI;UAC/B7B,EAD+B,CAAAgB,YAAA,EAAQ,EACjC;UAENhB,EAAA,CAAAc,UAAA,KAAA0E,2CAAA,2BAA+B;UAInCxF,EAAA,CAAAgB,YAAA,EAAY;;;UApEDhB,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAAoC,gBAAA,YAAA0C,GAAA,CAAA/B,OAAA,CAAqB;UAAkB/C,EAAA,CAAAkB,UAAA,uDAAsD;UAG7DlB,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,YAAA4D,GAAA,CAAAlC,eAAA,CAAkB;UASiF5C,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,aAAA4D,GAAA,CAAAnD,KAAA,KAAAmD,GAAA,CAAAhC,MAAA,IAAgC;UAE9G9C,EAAA,CAAAiB,SAAA,GAAS;UAATjB,EAAA,CAAAkB,UAAA,YAAA4D,GAAA,CAAAhC,MAAA,CAAS;UAEqE9C,EAAA,CAAAiB,SAAA,EAAgD;UAAhDjB,EAAA,CAAAkB,UAAA,aAAA4D,GAAA,CAAAnD,KAAA,KAAAmD,GAAA,CAAAhC,MAAA,CAAAgC,GAAA,CAAAhC,MAAA,CAAA2C,MAAA,MAAgD;UAG3KzF,EAAA,CAAAiB,SAAA,EAAc;UAAdjB,EAAA,CAAAkB,UAAA,UAAA4D,GAAA,CAAAnC,OAAA,CAAc;UAqCuB3C,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAoC,gBAAA,YAAA0C,GAAA,CAAAtB,WAAA,CAAyB;UAI1BxD,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAoC,gBAAA,YAAA0C,GAAA,CAAAtB,WAAA,CAAyB;UAI7DxD,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,UAAA4D,GAAA,CAAAnC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}