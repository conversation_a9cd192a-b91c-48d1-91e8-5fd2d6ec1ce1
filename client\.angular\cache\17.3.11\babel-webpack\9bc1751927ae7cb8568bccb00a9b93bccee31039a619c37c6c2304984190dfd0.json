{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SalesQuotesComponent } from './sales-quotes.component';\nimport { SalesQuotesDetailsComponent } from './sales-quotes-details/sales-quotes-details.component';\nimport { SalesQuotesOverviewComponent } from './sales-quotes-details/sales-quotes-overview/sales-quotes-overview.component';\nimport { SalesQuotesContactsComponent } from './sales-quotes-details/sales-quotes-contacts/sales-quotes-contacts.component';\nimport { SalesQuotePartnersComponent } from './sales-quotes-details/sales-quote-partners/sales-quote-partners.component';\nimport { SalesQuoteOpportunitiesComponent } from './sales-quotes-details/sales-quote-opportunities/sales-quote-opportunities.component';\nimport { SalesQuoteOrganizationDataComponent } from './sales-quotes-details/sales-quote-organization-data/sales-quote-organization-data.component';\nimport { SalesQuoteSalesTeamComponent } from './sales-quotes-details/sales-quote-sales-team/sales-quote-sales-team.component';\nimport { SalesQuoteAttachmentsComponent } from './sales-quotes-details/sales-quote-attachments/sales-quote-attachments.component';\nimport { SalesQuoteActivitiesComponent } from './sales-quotes-details/sales-quote-activities/sales-quote-activities.component';\nimport { SalesQuoteRelationshipsComponent } from './sales-quotes-details/sales-quote-relationships/sales-quote-relationships.component';\nimport { SalesQuoteTicketsComponent } from './sales-quotes-details/sales-quote-tickets/sales-quote-tickets.component';\nimport { SalesQuotesNotesComponent } from './sales-quotes-details/sales-quotes-notes/sales-quotes-notes.component';\nimport { SalesQuotesAiInsightsComponent } from './sales-quotes-details/sales-quotes-ai-insights/sales-quotes-ai-insights.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SalesQuotesComponent\n}, {\n  path: ':id',\n  component: SalesQuotesDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: SalesQuotesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: SalesQuotesContactsComponent\n  }, {\n    path: 'partners',\n    component: SalesQuotePartnersComponent\n  }, {\n    path: 'opportunities',\n    component: SalesQuoteOpportunitiesComponent\n  }, {\n    path: 'organization-data',\n    component: SalesQuoteOrganizationDataComponent\n  }, {\n    path: 'ai-insights',\n    component: SalesQuotesAiInsightsComponent\n  }, {\n    path: 'sales-team',\n    component: SalesQuoteSalesTeamComponent\n  }, {\n    path: 'attachments',\n    component: SalesQuoteAttachmentsComponent\n  }, {\n    path: 'activities',\n    component: SalesQuoteActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: SalesQuoteRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: SalesQuoteTicketsComponent\n  }, {\n    path: 'notes',\n    component: SalesQuotesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let SalesQuotesRoutingModule = /*#__PURE__*/(() => {\n  class SalesQuotesRoutingModule {\n    static {\n      this.ɵfac = function SalesQuotesRoutingModule_Factory(t) {\n        return new (t || SalesQuotesRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SalesQuotesRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return SalesQuotesRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}