{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { animate, state, style, transition, trigger } from '@angular/animations';\nimport { filter } from 'rxjs/operators';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./app.sidebar.component\";\nimport * as i4 from \"./app.menu.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/tooltip\";\nimport * as i7 from \"primeng/ripple\";\nconst _c0 = [\"submenu\"];\nconst _c1 = [\"app-menuitem\", \"\"];\nconst _c2 = () => ({\n  paths: \"exact\",\n  queryParams: \"ignored\",\n  matrixParams: \"ignored\",\n  fragment: \"ignored\"\n});\nfunction AppMenuitemComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n  }\n}\nfunction AppMenuitemComponent_a_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n}\nfunction AppMenuitemComponent_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.itemClick($event));\n    })(\"mouseenter\", function AppMenuitemComponent_a_2_Template_a_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseEnter());\n    });\n    i0.ɵɵelementStart(1, \"i\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AppMenuitemComponent_a_2_i_5_Template, 1, 0, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.item.class)(\"pTooltip\", ctx_r0.item.label)(\"tooltipDisabled\", !(ctx_r0.isSlim && ctx_r0.root && !ctx_r0.active));\n    i0.ɵɵattribute(\"href\", ctx_r0.item.url, i0.ɵɵsanitizeUrl)(\"target\", ctx_r0.item.target);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.items);\n  }\n}\nfunction AppMenuitemComponent_a_3_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n}\nfunction AppMenuitemComponent_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.itemClick($event));\n    })(\"mouseenter\", function AppMenuitemComponent_a_3_Template_a_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseEnter());\n    });\n    i0.ɵɵelementStart(1, \"i\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AppMenuitemComponent_a_3_i_5_Template, 1, 0, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.item.class)(\"routerLink\", ctx_r0.item.routerLink)(\"routerLinkActiveOptions\", ctx_r0.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c2))(\"fragment\", ctx_r0.item.fragment)(\"queryParamsHandling\", ctx_r0.item.queryParamsHandling)(\"preserveFragment\", ctx_r0.item.preserveFragment)(\"skipLocationChange\", ctx_r0.item.skipLocationChange)(\"replaceUrl\", ctx_r0.item.replaceUrl)(\"state\", ctx_r0.item.state)(\"queryParams\", ctx_r0.item.queryParams)(\"pTooltip\", ctx_r0.item.label)(\"tooltipDisabled\", !(ctx_r0.isSlim && ctx_r0.root));\n    i0.ɵɵattribute(\"target\", ctx_r0.item.target);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.items);\n  }\n}\nfunction AppMenuitemComponent_ul_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const child_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(child_r5.badgeClass);\n    i0.ɵɵproperty(\"item\", child_r5)(\"index\", i_r6)(\"parentKey\", ctx_r0.key);\n  }\n}\nfunction AppMenuitemComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", null, 0);\n    i0.ɵɵlistener(\"@children.done\", function AppMenuitemComponent_ul_4_Template_ul_animation_children_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmenuAnimated($event));\n    });\n    i0.ɵɵtemplate(2, AppMenuitemComponent_ul_4_ng_template_2_Template, 1, 5, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@children\", ctx_r0.submenuAnimation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.item.items);\n  }\n}\nexport let AppMenuitemComponent = /*#__PURE__*/(() => {\n  class AppMenuitemComponent {\n    constructor(layoutService, cd, router, appSidebar, menuService) {\n      this.layoutService = layoutService;\n      this.cd = cd;\n      this.router = router;\n      this.appSidebar = appSidebar;\n      this.menuService = menuService;\n      this.active = false;\n      this.key = \"\";\n      this.menuSourceSubscription = this.menuService.menuSource$.subscribe(value => {\n        Promise.resolve(null).then(() => {\n          if (value.routeEvent) {\n            this.active = value.key === this.key || value.key.startsWith(this.key + '-') ? true : false;\n          } else {\n            if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {\n              this.active = false;\n            }\n          }\n        });\n      });\n      this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {\n        this.active = false;\n      });\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(params => {\n        if (this.isSlimPlus || this.isSlim || this.isHorizontal) {\n          this.active = false;\n        } else {\n          if (this.item.routerLink) {\n            this.updateActiveStateFromRoute();\n          }\n        }\n      });\n    }\n    ngOnInit() {\n      this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);\n      if (!(this.isSlimPlus || this.isSlim || this.isHorizontal) && this.item.routerLink) {\n        this.updateActiveStateFromRoute();\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.root && this.active && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\n        this.calculatePosition(this.submenu?.nativeElement, this.submenu?.nativeElement.parentElement);\n      }\n    }\n    updateActiveStateFromRoute() {\n      let activeRoute = this.router.isActive(this.item.routerLink[0], {\n        paths: 'exact',\n        queryParams: 'ignored',\n        matrixParams: 'ignored',\n        fragment: 'ignored'\n      });\n      if (activeRoute) {\n        this.menuService.onMenuStateChange({\n          key: this.key,\n          routeEvent: true\n        });\n      }\n    }\n    onSubmenuAnimated(event) {\n      if (event.toState === 'visible' && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\n        const el = event.element;\n        const elParent = el.parentElement;\n        this.calculatePosition(el, elParent);\n      }\n    }\n    calculatePosition(overlay, target) {\n      if (overlay) {\n        const {\n          left,\n          top\n        } = target.getBoundingClientRect();\n        const [vWidth, vHeight] = [window.innerWidth, window.innerHeight];\n        const [oWidth, oHeight] = [overlay.offsetWidth, overlay.offsetHeight];\n        const scrollbarWidth = DomHandler.calculateScrollbarWidth();\n        // reset\n        overlay.style.top = '';\n        overlay.style.left = '';\n        if (this.layoutService.isHorizontal()) {\n          const width = left + oWidth + scrollbarWidth;\n          overlay.style.left = vWidth < width ? `${left - (width - vWidth)}px` : `${left}px`;\n        } else if (this.layoutService.isSlim() || this.layoutService.isSlimPlus()) {\n          const height = top + oHeight;\n          overlay.style.top = vHeight < height ? `${top - (height - vHeight)}px` : `${top}px`;\n          console.log('top', top, 'vHeight', vHeight, 'oHeight', oHeight, 'height', height);\n        }\n      }\n    }\n    itemClick(event) {\n      // avoid processing disabled items\n      if (this.item.disabled) {\n        event.preventDefault();\n        return;\n      }\n      // navigate with hover\n      if (this.root && this.isSlim || this.isHorizontal || this.isSlimPlus) {\n        this.layoutService.state.menuHoverActive = !this.layoutService.state.menuHoverActive;\n      }\n      // execute command\n      if (this.item.command) {\n        this.item.command({\n          originalEvent: event,\n          item: this.item\n        });\n      }\n      // toggle active state\n      if (this.item.items) {\n        this.active = !this.active;\n        if (this.root && this.active && (this.isSlim || this.isHorizontal || this.isSlimPlus)) {\n          this.layoutService.onOverlaySubmenuOpen();\n        }\n      } else {\n        if (this.layoutService.isMobile()) {\n          this.layoutService.state.staticMenuMobileActive = false;\n        }\n        if (this.isSlim || this.isHorizontal || this.isSlimPlus) {\n          this.menuService.reset();\n          this.layoutService.state.menuHoverActive = false;\n        }\n      }\n      this.menuService.onMenuStateChange({\n        key: this.key\n      });\n    }\n    onMouseEnter() {\n      // activate item on hover\n      if (this.root && (this.isSlim || this.isHorizontal || this.isSlimPlus) && this.layoutService.isDesktop()) {\n        if (this.layoutService.state.menuHoverActive) {\n          this.active = true;\n          this.menuService.onMenuStateChange({\n            key: this.key\n          });\n        }\n      }\n    }\n    get submenuAnimation() {\n      if (this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) return this.active ? 'visible' : 'hidden';else return this.root ? 'expanded' : this.active ? 'expanded' : 'collapsed';\n    }\n    get isHorizontal() {\n      return this.layoutService.isHorizontal();\n    }\n    get isSlim() {\n      return this.layoutService.isSlim();\n    }\n    get isSlimPlus() {\n      return this.layoutService.isSlimPlus();\n    }\n    get activeClass() {\n      return this.active && !this.root;\n    }\n    ngOnDestroy() {\n      if (this.menuSourceSubscription) {\n        this.menuSourceSubscription.unsubscribe();\n      }\n      if (this.menuResetSubscription) {\n        this.menuResetSubscription.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function AppMenuitemComponent_Factory(t) {\n        return new (t || AppMenuitemComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AppSidebarComponent), i0.ɵɵdirectiveInject(i4.MenuService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppMenuitemComponent,\n        selectors: [[\"\", \"app-menuitem\", \"\"]],\n        viewQuery: function AppMenuitemComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenu = _t.first);\n          }\n        },\n        hostVars: 4,\n        hostBindings: function AppMenuitemComponent_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"layout-root-menuitem\", ctx.root)(\"active-menuitem\", ctx.activeClass);\n          }\n        },\n        inputs: {\n          item: \"item\",\n          index: \"index\",\n          root: \"root\",\n          parentKey: \"parentKey\"\n        },\n        attrs: _c1,\n        decls: 5,\n        vars: 4,\n        consts: [[\"submenu\", \"\"], [\"class\", \"layout-menuitem-root-text\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"pTooltip\", \"tooltipDisabled\", \"click\", \"mouseenter\", 4, \"ngIf\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"pTooltip\", \"tooltipDisabled\", \"click\", \"mouseenter\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"layout-menuitem-root-text\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"click\", \"mouseenter\", \"ngClass\", \"pTooltip\", \"tooltipDisabled\"], [1, \"material-symbols-rounded\", \"layout-menuitem-icon\", \"text-2xl\", \"text-orange-700\"], [1, \"layout-menuitem-text\"], [\"class\", \"pi pi-fw pi-angle-down layout-submenu-toggler\", 4, \"ngIf\"], [1, \"pi\", \"pi-fw\", \"pi-angle-down\", \"layout-submenu-toggler\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"click\", \"mouseenter\", \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"pTooltip\", \"tooltipDisabled\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"parentKey\"]],\n        template: function AppMenuitemComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementContainerStart(0);\n            i0.ɵɵtemplate(1, AppMenuitemComponent_div_1_Template, 2, 1, \"div\", 1)(2, AppMenuitemComponent_a_2_Template, 6, 8, \"a\", 2)(3, AppMenuitemComponent_a_3_Template, 6, 17, \"a\", 3)(4, AppMenuitemComponent_ul_4_Template, 3, 2, \"ul\", 4);\n            i0.ɵɵelementContainerEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.root && ctx.item.visible !== false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (!ctx.item.routerLink || ctx.item.items) && ctx.item.visible !== false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.item.routerLink && !ctx.item.items && ctx.item.visible !== false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.item.items && ctx.item.visible !== false);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.Tooltip, i7.Ripple, i2.RouterLink, i2.RouterLinkActive, AppMenuitemComponent],\n        encapsulation: 2,\n        data: {\n          animation: [trigger('children', [state('collapsed', style({\n            height: '0'\n          })), state('expanded', style({\n            height: '*'\n          })), state('hidden', style({\n            display: 'none'\n          })), state('visible', style({\n            display: 'block'\n          })), transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)'))])]\n        }\n      });\n    }\n  }\n  return AppMenuitemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}