{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SalesOrdersRoutingModule } from './sales-orders-routing.module';\nimport { SalesOrdersComponent } from './sales-orders.component';\nimport { FormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { SalesOrdersDetailsComponent } from './sales-orders-details/sales-orders-details.component';\nimport { SalesOrdersOverviewComponent } from './sales-orders-details/sales-orders-overview/sales-orders-overview.component';\nimport { SalesOrdersContactsComponent } from './sales-orders-details/sales-orders-contacts/sales-orders-contacts.component';\nimport { SalesOrdersSalesTeamComponent } from './sales-orders-details/sales-orders-sales-team/sales-orders-sales-team.component';\nimport { SalesOrdersOrganizationDataComponent } from './sales-orders-details/sales-orders-organization-data/sales-orders-organization-data.component';\nimport { SalesOrdersAiInsightsComponent } from './sales-orders-details/sales-orders-ai-insights/sales-orders-ai-insights.component';\nimport { SalesOrdersAttachmentsComponent } from './sales-orders-details/sales-orders-attachments/sales-orders-attachments.component';\nimport { SalesOrdersNotesComponent } from './sales-orders-details/sales-orders-notes/sales-orders-notes.component';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { RouterModule } from '@angular/router';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport class SalesOrdersModule {\n  static {\n    this.ɵfac = function SalesOrdersModule_Factory(t) {\n      return new (t || SalesOrdersModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SalesOrdersModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SalesOrdersRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule, RouterModule, MultiSelectModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesOrdersModule, {\n    declarations: [SalesOrdersComponent, SalesOrdersDetailsComponent, SalesOrdersOverviewComponent, SalesOrdersContactsComponent, SalesOrdersSalesTeamComponent, SalesOrdersOrganizationDataComponent, SalesOrdersAiInsightsComponent, SalesOrdersAttachmentsComponent, SalesOrdersNotesComponent],\n    imports: [CommonModule, SalesOrdersRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule, RouterModule, MultiSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SalesOrdersRoutingModule", "SalesOrdersComponent", "FormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "SalesOrdersDetailsComponent", "SalesOrdersOverviewComponent", "SalesOrdersContactsComponent", "SalesOrdersSalesTeamComponent", "SalesOrdersOrganizationDataComponent", "SalesOrdersAiInsightsComponent", "SalesOrdersAttachmentsComponent", "SalesOrdersNotesComponent", "PaginatorModule", "ReactiveFormsModule", "ProgressSpinnerModule", "RouterModule", "MultiSelectModule", "SalesOrdersModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SalesOrdersRoutingModule } from './sales-orders-routing.module';\r\nimport { SalesOrdersComponent } from './sales-orders.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { SalesOrdersDetailsComponent } from './sales-orders-details/sales-orders-details.component';\r\nimport { SalesOrdersOverviewComponent } from './sales-orders-details/sales-orders-overview/sales-orders-overview.component';\r\nimport { SalesOrdersContactsComponent } from './sales-orders-details/sales-orders-contacts/sales-orders-contacts.component';\r\nimport { SalesOrdersSalesTeamComponent } from './sales-orders-details/sales-orders-sales-team/sales-orders-sales-team.component';\r\nimport { SalesOrdersOrganizationDataComponent } from './sales-orders-details/sales-orders-organization-data/sales-orders-organization-data.component';\r\nimport { SalesOrdersAiInsightsComponent } from './sales-orders-details/sales-orders-ai-insights/sales-orders-ai-insights.component';\r\nimport { SalesOrdersAttachmentsComponent } from './sales-orders-details/sales-orders-attachments/sales-orders-attachments.component';\r\nimport { SalesOrdersNotesComponent } from './sales-orders-details/sales-orders-notes/sales-orders-notes.component';\r\nimport { PaginatorModule } from 'primeng/paginator';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { RouterModule } from '@angular/router';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SalesOrdersComponent,\r\n    SalesOrdersDetailsComponent,\r\n    SalesOrdersOverviewComponent,\r\n    SalesOrdersContactsComponent,\r\n    SalesOrdersSalesTeamComponent,\r\n    SalesOrdersOrganizationDataComponent,\r\n    SalesOrdersAiInsightsComponent,\r\n    SalesOrdersAttachmentsComponent,\r\n    SalesOrdersNotesComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SalesOrdersRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule,\r\n    PaginatorModule,\r\n    ReactiveFormsModule,\r\n    ProgressSpinnerModule,\r\n    RouterModule,\r\n    MultiSelectModule\r\n  ]\r\n})\r\nexport class SalesOrdersModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,oCAAoC,QAAQ,gGAAgG;AACrJ,SAASC,8BAA8B,QAAQ,oFAAoF;AACnI,SAASC,+BAA+B,QAAQ,oFAAoF;AACpI,SAASC,yBAAyB,QAAQ,wEAAwE;AAClH,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,qBAAqB;;AAkCvD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAlB1BzB,YAAY,EACZC,wBAAwB,EACxBE,WAAW,EACXI,WAAW,EACXE,YAAY,EACZH,cAAc,EACdK,aAAa,EACbH,kBAAkB,EAClBJ,gBAAgB,EAChBC,cAAc,EACdK,eAAe,EACfU,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,YAAY,EACZC,iBAAiB;IAAA;EAAA;;;2EAGRC,iBAAiB;IAAAC,YAAA,GA7B1BxB,oBAAoB,EACpBU,2BAA2B,EAC3BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,6BAA6B,EAC7BC,oCAAoC,EACpCC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,yBAAyB;IAAAQ,OAAA,GAGzB3B,YAAY,EACZC,wBAAwB,EACxBE,WAAW,EACXI,WAAW,EACXE,YAAY,EACZH,cAAc,EACdK,aAAa,EACbH,kBAAkB,EAClBJ,gBAAgB,EAChBC,cAAc,EACdK,eAAe,EACfU,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,YAAY,EACZC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}