{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { SignupComponent } from './signup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let SignupModule = /*#__PURE__*/(() => {\n  class SignupModule {\n    static {\n      this.ɵfac = function SignupModule_Factory(t) {\n        return new (t || SignupModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SignupModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, RouterModule.forChild([{\n          path: '',\n          component: SignupComponent\n        }])]\n      });\n    }\n  }\n  return SignupModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}