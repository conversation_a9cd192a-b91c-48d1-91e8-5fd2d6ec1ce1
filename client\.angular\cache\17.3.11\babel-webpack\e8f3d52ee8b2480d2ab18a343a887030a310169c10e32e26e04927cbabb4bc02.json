{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Malay [ms]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/weldan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ms = moment.defineLocale('ms', {\n    months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split('_'),\n    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),\n    weekdays: 'Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu'.split('_'),\n    weekdaysShort: 'Ahd_Isn_Se<PERSON>_Ra<PERSON>_<PERSON>ha_Ju<PERSON>_Sab'.split('_'),\n    weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /pagi|tengahari|petang|malam/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'pagi') {\n        return hour;\n      } else if (meridiem === 'tengahari') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'petang' || meridiem === 'malam') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'pagi';\n      } else if (hours < 15) {\n        return 'tengahari';\n      } else if (hours < 19) {\n        return 'petang';\n      } else {\n        return 'malam';\n      }\n    },\n    calendar: {\n      sameDay: '[Hari ini pukul] LT',\n      nextDay: '[Esok pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kelmarin pukul] LT',\n      lastWeek: 'dddd [lepas pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dalam %s',\n      past: '%s yang lepas',\n      s: 'beberapa saat',\n      ss: '%d saat',\n      m: 'seminit',\n      mm: '%d minit',\n      h: 'sejam',\n      hh: '%d jam',\n      d: 'sehari',\n      dd: '%d hari',\n      M: 'sebulan',\n      MM: '%d bulan',\n      y: 'setahun',\n      yy: '%d tahun'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return ms;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ms", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "meridiemHour", "hour", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/ms.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Malay [ms]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/weldan\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ms = moment.defineLocale('ms', {\n        months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),\n        weekdays: 'Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu'.split('_'),\n        weekdaysShort: 'Ahd_Isn_Sel_Ra<PERSON>_<PERSON>ha_Ju<PERSON>_Sab'.split('_'),\n        weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),\n        longDateFormat: {\n            LT: 'HH.mm',\n            LTS: 'HH.mm.ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY [pukul] HH.mm',\n            LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm',\n        },\n        meridiemParse: /pagi|tengahari|petang|malam/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'pagi') {\n                return hour;\n            } else if (meridiem === 'tengahari') {\n                return hour >= 11 ? hour : hour + 12;\n            } else if (meridiem === 'petang' || meridiem === 'malam') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 11) {\n                return 'pagi';\n            } else if (hours < 15) {\n                return 'tengahari';\n            } else if (hours < 19) {\n                return 'petang';\n            } else {\n                return 'malam';\n            }\n        },\n        calendar: {\n            sameDay: '[Hari ini pukul] LT',\n            nextDay: '[Esok pukul] LT',\n            nextWeek: 'dddd [pukul] LT',\n            lastDay: '[Kelmarin pukul] LT',\n            lastWeek: 'dddd [lepas pukul] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'dalam %s',\n            past: '%s yang lepas',\n            s: 'beberapa saat',\n            ss: '%d saat',\n            m: 'seminit',\n            mm: '%d minit',\n            h: 'sejam',\n            hh: '%d jam',\n            d: 'sehari',\n            dd: '%d hari',\n            M: 'sebulan',\n            MM: '%d bulan',\n            y: 'setahun',\n            yy: '%d tahun',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return ms;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mFAAmF,CAACC,KAAK,CAC7F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,4CAA4C,CAACF,KAAK,CAAC,GAAG,CAAC;IACjEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,2BAA2B;MAChCC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,6BAA6B;IAC5CC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;QACrB,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,WAAW,EAAE;QACjC,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACtD,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;QACnB,OAAO,WAAW;MACtB,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;QACnB,OAAO,QAAQ;MACnB,CAAC,MAAM;QACH,OAAO,OAAO;MAClB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,qBAAqB;MAC9BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,eAAe;MACrBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}