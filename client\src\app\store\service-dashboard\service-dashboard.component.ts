import { Component } from '@angular/core';

interface AccountTableData {
  ticket_no?: string;
  account_no?: string;
  contact_no?: string;
  assign_to?: string;
  created_at?: string;
  status?: string;
}
interface ActivitiesTaskColumn {
  field: string;
  header: string;
}

@Component({
  selector: 'app-service-dashboard',
  templateUrl: './service-dashboard.component.html',
  styleUrl: './service-dashboard.component.scss'
})
export class ServiceDashboardComponent {

  secTableData: AccountTableData[] = [];

  private _selectedActivitiesTaskColumns: ActivitiesTaskColumn[] = [];

  public ActivitiesTaskCols: ActivitiesTaskColumn[] = [
    { field: 'account_no', header: 'Account #' },
    { field: 'contact_no', header: 'Contact #' },
    { field: 'assign_to', header: 'Assigned To' },
    { field: 'created_at', header: 'Created at' }
  ];

  sortFieldActivities: string = '';
  sortOrderActivities: number = 1;

  sortFieldActivitiesTask: string = '';
  sortOrderActivitiesTask: number = 1;

  ngOnInit() {

    this.secTableData = [
      {
        ticket_no: '1',
        account_no: '00830VGB',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '2',
        account_no: 'FF525GG',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'Completed',
      },
      {
        ticket_no: '3',
        account_no: 'SS525668',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Complete',
      },
      {
        ticket_no: '4',
        account_no: 'DCVG5525',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '5',
        account_no: 'JJLO555',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '6',
        account_no: '6654FFF',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '7',
        account_no: '55HNH552',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '8',
        account_no: '00HGTK55',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '9',
        account_no: '525DDOHG',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },
      {
        ticket_no: '10',
        account_no: '00830VGBGG',
        contact_no: '199911',
        assign_to: '**********',
        created_at: '09/06/2025',
        status: 'In Progress',
      },


    ];

    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols;
  }

  get selectedActivitiesTaskColumns(): any[] {
    return this._selectedActivitiesTaskColumns;
  }

  set selectedActivitiesTaskColumns(val: any[]) {
    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols.filter(
      (col) => val.includes(col)
    );
  }

  onActivitiesTaskColumnReorder(event: any) {
    const draggedCol = this.ActivitiesTaskCols[event.dragIndex];
    this.ActivitiesTaskCols.splice(event.dragIndex, 1);
    this.ActivitiesTaskCols.splice(event.dropIndex, 0, draggedCol);
  }

  customSort(
    field: string,
    data: any[],
    type: 'task'
  ) {
    if (type === 'task') {
      this.sortFieldActivitiesTask = field;
      this.sortOrderActivitiesTask =
        this.sortOrderActivitiesTask === 1 ? -1 : 1;
    }

    data.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = null;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return (
        (type === 'task'
          ? this.sortOrderActivitiesTask
          : 1) * result
      );
    });
  }

  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;

    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      let fields = field.split('.');
      let value = data;
      for (let i = 0; i < fields.length; i++) {
        if (value == null) return null;
        value = value[fields[i]];
      }
      return value;
    }
  }

}
