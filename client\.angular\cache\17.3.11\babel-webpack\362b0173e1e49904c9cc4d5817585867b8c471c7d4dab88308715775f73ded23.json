{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../contacts.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/breadcrumb\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"../../../shared/initials.pipe\";\nfunction ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ContactsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 32);\n    i0.ɵɵtemplate(1, ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let ContactsDetailsComponent = /*#__PURE__*/(() => {\n  class ContactsDetailsComponent {\n    constructor(router, route, contactsservice, messageservice, confirmationservice) {\n      this.router = router;\n      this.route = route;\n      this.contactsservice = contactsservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.contactDetails = null;\n      this.statusDetails = null;\n      this.sidebarDetails = null;\n      this.items = [];\n      this.id = '';\n      this.bp_status = '';\n      this.bp_doc_id = '';\n      this.contact_id = '';\n      this.breadcrumbitems = [];\n      this.activeItem = null;\n      this.isSidebarHidden = false;\n      this.Actions = [];\n      this.activeIndex = 0;\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const contactId = params.get('id');\n        if (contactId) {\n          this.loadContactData(contactId);\n        }\n      });\n      // Listen for route changes to keep active tab in sync\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n      this.contactsservice.contact.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        this.contactDetails = response || null;\n        this.statusDetails = {\n          bp_id: response?.bp_company_id,\n          first_name: response?.business_partner_person?.first_name,\n          last_name: response?.business_partner_person?.last_name,\n          email_address: response?.business_partner_person?.addresses?.[0]?.emails?.[0]?.email_address,\n          phone_number: response?.business_partner_person?.addresses?.[0]?.phone_numbers?.find(item => String(item?.phone_number_type) === '1')?.phone_number,\n          mobile: response?.business_partner_person?.addresses?.[0]?.phone_numbers?.find(item => String(item?.phone_number_type) === '3')?.phone_number,\n          job_title: response?.business_partner_person?.bp_extension?.job_title,\n          contact_person_department: response?.person_func_and_dept?.contact_person_department,\n          web_registered: response?.business_partner_person?.bp_extension?.web_registered,\n          emails_opt_in: response?.business_partner_person?.bp_extension?.emails_opt_in,\n          print_marketing_opt_in: response?.business_partner_person?.bp_extension?.print_marketing_opt_in,\n          sms_promotions_opt_in: response?.business_partner_person?.bp_extension?.sms_promotions_opt_in,\n          prfrd_comm_medium_type: response?.business_partner_person?.addresses?.[0]?.prfrd_comm_medium_type,\n          is_marked_for_archiving: response?.business_partner_person?.is_marked_for_archiving\n        };\n        this.sidebarDetails = this.formatSidebarDetails(response.business_partner_person?.addresses || []);\n      });\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `/store/contacts/${id}/overview`\n      },\n      // {\n      //   label: 'Relationships',\n      //   routerLink: `/store/contacts/${id}/relationships`,\n      // },\n      {\n        label: 'Attachments',\n        routerLink: `/store/contacts/${id}/attachments`\n      }, {\n        label: 'Notes',\n        routerLink: `/store/contacts/${id}/notes`\n      }, {\n        label: 'Activities',\n        routerLink: `/store/contacts/${id}/activities`\n      }\n      // {\n      //   label: 'Opportunities',\n      //   routerLink: `/store/contacts/${id}/opportunities`,\n      // },\n      // {\n      //   label: 'Tickets',\n      //   routerLink: `/store/contacts/${id}/tickets`,\n      // },\n      ];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Contacts',\n        routerLink: ['/store/contacts']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    loadContactData(contactId) {\n      this.contactsservice.getContactByID(contactId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response) {\n            this.bp_doc_id = response?.data?.[0]?.business_partner_person?.documentId;\n            this.bp_status = response?.data?.[0]?.business_partner_person?.is_marked_for_archiving;\n            this.Actions = [{\n              name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\n              code: this.bp_status ? 'SAA' : 'SAO'\n            }];\n          }\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    formatSidebarDetails(addresses) {\n      return (addresses || []).map(address => {\n        const phoneNumbers = address?.phone_numbers || [];\n        return {\n          ...address,\n          address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n          email_address: address?.emails?.[0]?.email_address || '-',\n          phone_number: phoneNumbers.find(item => String(item?.phone_number_type) === '1')?.phone_number || '-',\n          website_url: address?.home_page_urls?.[0]?.website_url || '-'\n        };\n      });\n    }\n    onActionChange(event) {\n      const actionCode = event.value?.code;\n      const actionsMap = {\n        SAA: () => this.UpdateStatus(this.id, 'false'),\n        SAO: () => this.UpdateStatus(this.id, 'true')\n      };\n      const action = actionsMap[actionCode];\n      if (action) {\n        this.confirmationservice.confirm({\n          message: 'Are you sure you want to proceed with this action?',\n          header: 'Confirm',\n          icon: 'pi pi-exclamation-triangle',\n          accept: action\n        });\n      }\n    }\n    UpdateStatus(docid, status) {\n      // Ensure statusDetails exists before updating\n      this.statusDetails = this.statusDetails || {};\n      this.statusDetails.is_marked_for_archiving = status;\n      this.contactsservice.updateContact(docid, this.statusDetails).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Action updated successfully!'\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        },\n        error: error => {\n          console.error('Update failed:', error); // Log the actual error\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    goToBack() {\n      this.router.navigate(['/store/contacts']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ContactsDetailsComponent_Factory(t) {\n        return new (t || ContactsDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContactsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContactsDetailsComponent,\n        selectors: [[\"app-contacts-details\"]],\n        decls: 62,\n        vars: 25,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"onChange\", \"options\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function ContactsDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n            i0.ɵɵlistener(\"onChange\", function ContactsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n              return ctx.onActionChange($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function ContactsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function ContactsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(9, ContactsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n            i0.ɵɵtext(18);\n            i0.ɵɵpipe(19, \"initials\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n            i0.ɵɵtext(26, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(28, \"div\", 23)(29, \"ul\", 24)(30, \"li\", 25)(31, \"span\", 26)(32, \"i\", 27);\n            i0.ɵɵtext(33, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"span\", 28);\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n            i0.ɵɵtext(40, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(41, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"span\", 28);\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n            i0.ɵɵtext(47, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(48, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\", 28);\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n            i0.ɵɵtext(54, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(55, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"span\", 28);\n            i0.ɵɵtext(57);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(58, \"div\", 29)(59, \"p-button\", 30);\n            i0.ɵɵlistener(\"click\", function ContactsDetailsComponent_Template_p_button_click_59_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(60, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(61, \"p-confirmDialog\", 31);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.Actions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 23, ctx.contactDetails == null ? null : ctx.contactDetails.business_partner_person == null ? null : ctx.contactDetails.business_partner_person.bp_full_name));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.contactDetails == null ? null : ctx.contactDetails.business_partner_person == null ? null : ctx.contactDetails.business_partner_person.bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" : \", (ctx.contactDetails == null ? null : ctx.contactDetails.bp_person_id) || \"-\", \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i4.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i5.Breadcrumb, i3.PrimeTemplate, i6.Dropdown, i7.Button, i8.TabView, i8.TabPanel, i9.Toast, i10.ConfirmDialog, i11.InitialsPipe]\n      });\n    }\n  }\n  return ContactsDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}