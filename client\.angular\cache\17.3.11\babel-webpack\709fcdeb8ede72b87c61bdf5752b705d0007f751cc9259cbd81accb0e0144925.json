{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { OrganizationalRoutingModule } from './organizational-routing.module';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ToastModule } from 'primeng/toast';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ButtonModule } from 'primeng/button';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let OrganizationalModule = /*#__PURE__*/(() => {\n  class OrganizationalModule {\n    static {\n      this.ɵfac = function OrganizationalModule_Factory(t) {\n        return new (t || OrganizationalModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: OrganizationalModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, OrganizationalRoutingModule, NgSelectModule, FormsModule, ReactiveFormsModule, CalendarModule, ToggleButtonModule, DropdownModule, TabViewModule, ToastModule, ConfirmDialogModule, InputTextModule, BreadcrumbModule, TableModule, ButtonModule, CheckboxModule, SharedModule, MultiSelectModule]\n      });\n    }\n  }\n  return OrganizationalModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}