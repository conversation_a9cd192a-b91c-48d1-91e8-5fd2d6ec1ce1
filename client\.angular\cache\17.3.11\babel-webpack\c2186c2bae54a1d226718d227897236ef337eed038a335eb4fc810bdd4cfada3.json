{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IdentifyAccountRoutingModule } from './identify-account-routing.module';\nimport { IdentifyAccountComponent } from './identify-account.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AccordionModule } from 'primeng/accordion';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport * as i0 from \"@angular/core\";\nexport class IdentifyAccountModule {\n  static {\n    this.ɵfac = function IdentifyAccountModule_Factory(t) {\n      return new (t || IdentifyAccountModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IdentifyAccountModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, IdentifyAccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule, AccordionModule, RadioButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IdentifyAccountModule, {\n    declarations: [IdentifyAccountComponent],\n    imports: [CommonModule, ReactiveFormsModule, IdentifyAccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule, AccordionModule, RadioButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IdentifyAccountRoutingModule", "IdentifyAccountComponent", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "AccordionModule", "RadioButtonModule", "IdentifyAccountModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { IdentifyAccountRoutingModule } from './identify-account-routing.module';\r\nimport { IdentifyAccountComponent } from './identify-account.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { AccordionModule } from 'primeng/accordion';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IdentifyAccountComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    IdentifyAccountRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    InputTextModule,\r\n    AccordionModule,\r\n    RadioButtonModule\r\n  ]\r\n})\r\nexport class IdentifyAccountModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,qBAAqB;;AAuBvD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAhB9Bf,YAAY,EACZE,mBAAmB,EACnBC,4BAA4B,EAC5BG,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXV,WAAW,EACXO,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBK,eAAe,EACfG,eAAe,EACfC,iBAAiB;IAAA;EAAA;;;2EAGRC,qBAAqB;IAAAC,YAAA,GAnB9BZ,wBAAwB;IAAAa,OAAA,GAGxBjB,YAAY,EACZE,mBAAmB,EACnBC,4BAA4B,EAC5BG,gBAAgB,EAChBG,cAAc,EACdE,WAAW,EACXV,WAAW,EACXO,cAAc,EACdD,YAAY,EACZK,aAAa,EACbP,kBAAkB,EAClBK,eAAe,EACfG,eAAe,EACfC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}