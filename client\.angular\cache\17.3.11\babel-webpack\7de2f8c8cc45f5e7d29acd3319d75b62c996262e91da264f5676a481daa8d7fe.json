{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { OrganizationalComponent } from './organizational.component';\nimport { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';\nimport { OrganizationalRoutingModule } from './organizational-routing.module';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ToastModule } from 'primeng/toast';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ButtonModule } from 'primeng/button';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { OrganizationDetailsComponent } from './organization-details/organization-details.component';\nimport { GeneralComponent } from './organization-details/general/general.component';\nimport { FunctionsComponent } from './organization-details/functions/functions.component';\nimport { EmployeesComponent } from './organization-details/employees/employees.component';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport class OrganizationalModule {\n  static {\n    this.ɵfac = function OrganizationalModule_Factory(t) {\n      return new (t || OrganizationalModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OrganizationalModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, OrganizationalRoutingModule, NgSelectModule, FormsModule, ReactiveFormsModule, CalendarModule, ToggleButtonModule, DropdownModule, TabViewModule, ToastModule, ConfirmDialogModule, InputTextModule, BreadcrumbModule, TableModule, ButtonModule, CheckboxModule, SharedModule, MultiSelectModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrganizationalModule, {\n    declarations: [OrganizationalComponent, AddOrgUnitComponent, OrganizationDetailsComponent, GeneralComponent, FunctionsComponent, EmployeesComponent],\n    imports: [CommonModule, OrganizationalRoutingModule, NgSelectModule, FormsModule, ReactiveFormsModule, CalendarModule, ToggleButtonModule, DropdownModule, TabViewModule, ToastModule, ConfirmDialogModule, InputTextModule, BreadcrumbModule, TableModule, ButtonModule, CheckboxModule, SharedModule, MultiSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "OrganizationalComponent", "AddOrgUnitComponent", "OrganizationalRoutingModule", "NgSelectModule", "FormsModule", "ReactiveFormsModule", "CalendarModule", "ToastModule", "DropdownModule", "ButtonModule", "BreadcrumbModule", "ConfirmDialogModule", "InputTextModule", "TableModule", "SharedModule", "ToggleButtonModule", "CheckboxModule", "OrganizationDetailsComponent", "GeneralComponent", "FunctionsComponent", "EmployeesComponent", "TabViewModule", "ConfirmationService", "MessageService", "MultiSelectModule", "OrganizationalModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { OrganizationalComponent } from './organizational.component';\r\nimport { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';\r\nimport { OrganizationalRoutingModule } from './organizational-routing.module';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { OrganizationDetailsComponent } from './organization-details/organization-details.component';\r\nimport { GeneralComponent } from './organization-details/general/general.component';\r\nimport { FunctionsComponent } from './organization-details/functions/functions.component';\r\nimport { EmployeesComponent } from './organization-details/employees/employees.component';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    OrganizationalComponent,\r\n    AddOrgUnitComponent,\r\n    OrganizationDetailsComponent,\r\n    GeneralComponent,\r\n    FunctionsComponent,\r\n    EmployeesComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    OrganizationalRoutingModule,\r\n    NgSelectModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule,\r\n    ToggleButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    ToastModule,\r\n    ConfirmDialogModule,\r\n    InputTextModule,\r\n    BreadcrumbModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    SharedModule,\r\n    MultiSelectModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class OrganizationalModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,4BAA4B,QAAQ,uDAAuD;AACpG,SAASC,gBAAgB,QAAQ,kDAAkD;AACnF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,iBAAiB,QAAQ,qBAAqB;;AAkCvD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAFpB,CAACF,cAAc,EAAED,mBAAmB,CAAC;MAAAI,OAAA,GAnB9C3B,YAAY,EACZG,2BAA2B,EAC3BC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,cAAc,EACdS,kBAAkB,EAClBP,cAAc,EACda,aAAa,EACbd,WAAW,EACXI,mBAAmB,EACnBC,eAAe,EACfF,gBAAgB,EAChBG,WAAW,EACXJ,YAAY,EACZO,cAAc,EACdF,YAAY,EACZU,iBAAiB;IAAA;EAAA;;;2EAIRC,oBAAoB;IAAAE,YAAA,GA7B7B3B,uBAAuB,EACvBC,mBAAmB,EACnBgB,4BAA4B,EAC5BC,gBAAgB,EAChBC,kBAAkB,EAClBC,kBAAkB;IAAAM,OAAA,GAGlB3B,YAAY,EACZG,2BAA2B,EAC3BC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,cAAc,EACdS,kBAAkB,EAClBP,cAAc,EACda,aAAa,EACbd,WAAW,EACXI,mBAAmB,EACnBC,eAAe,EACfF,gBAAgB,EAChBG,WAAW,EACXJ,YAAY,EACZO,cAAc,EACdF,YAAY,EACZU,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}