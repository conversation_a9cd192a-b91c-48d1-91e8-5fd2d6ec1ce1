{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { HomeIcon } from 'primeng/icons/home';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\n/**\n * Breadcrumb provides contextual information about page hierarchy.\n * @group Components\n */\nconst _c0 = a0 => ({\n  \"p-breadcrumb-home\": true,\n  \"p-disabled\": a0\n});\nconst _c1 = () => ({\n  exact: false\n});\nconst _c2 = a0 => ({\n  \"p-disabled\": a0\n});\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nfunction Breadcrumb_li_2_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.home.icon)(\"ngStyle\", ctx_r1.home.iprivateyle);\n  }\n}\nfunction Breadcrumb_li_2_a_1_HomeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"HomeIcon\", 17);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-menuitem-icon\");\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_ng_container_3_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_li_2_a_1_ng_container_3_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlHomeLabel_r3 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.escape !== false)(\"ngIfElse\", htmlHomeLabel_r3);\n  }\n}\nfunction Breadcrumb_li_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClick($event, ctx_r1.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_li_2_a_1_HomeIcon_2_Template, 1, 1, \"HomeIcon\", 14)(3, Breadcrumb_li_2_a_1_ng_container_3_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r1.home.url ? ctx_r1.home.url : null, i0.ɵɵsanitizeUrl)(\"target\", ctx_r1.home.target)(\"ariaCurrentWhenActive\", ctx_r1.isCurrentUrl(ctx_r1.home));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.homeAriaLabel)(\"title\", ctx_r1.home.title)(\"tabindex\", ctx_r1.home.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.home.icon)(\"ngStyle\", ctx_r1.home.iconStyle);\n  }\n}\nfunction Breadcrumb_li_2_a_2_HomeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"HomeIcon\", 17);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-menuitem-icon\");\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_ng_container_3_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_li_2_a_2_ng_container_3_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlHomeRouteLabel_r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.escape !== false)(\"ngIfElse\", htmlHomeRouteLabel_r5);\n  }\n}\nfunction Breadcrumb_li_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClick($event, ctx_r1.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_li_2_a_2_HomeIcon_2_Template, 1, 1, \"HomeIcon\", 14)(3, Breadcrumb_li_2_a_2_ng_container_3_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.home.routerLink)(\"queryParams\", ctx_r1.home.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r1.home.routerLinkActiveOptions || i0.ɵɵpureFunction0(18, _c1))(\"target\", ctx_r1.home.target)(\"ariaCurrentWhenActive\", ctx_r1.isCurrentUrl(ctx_r1.home))(\"fragment\", ctx_r1.home.fragment)(\"queryParamsHandling\", ctx_r1.home.queryParamsHandling)(\"preserveFragment\", ctx_r1.home.preserveFragment)(\"skipLocationChange\", ctx_r1.home.skipLocationChange)(\"replaceUrl\", ctx_r1.home.replaceUrl)(\"state\", ctx_r1.home.state);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.homeAriaLabel)(\"title\", ctx_r1.home.title)(\"tabindex\", ctx_r1.home.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_Template, 4, 9, \"a\", 10)(2, Breadcrumb_li_2_a_2_Template, 4, 19, \"a\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.home.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r1.home.disabled))(\"ngStyle\", ctx_r1.home.style)(\"tooltipOptions\", ctx_r1.home.tooltipOptions);\n    i0.ɵɵattribute(\"id\", ctx_r1.home.id)(\"data-pc-section\", \"home\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.routerLink);\n  }\n}\nfunction Breadcrumb_li_3_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction Breadcrumb_li_3_2_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_li_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_li_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22);\n    i0.ɵɵtemplate(1, Breadcrumb_li_3_ChevronRightIcon_1_Template, 1, 0, \"ChevronRightIcon\", 15)(2, Breadcrumb_li_3_2_Template, 1, 0, null, 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"separator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.separatorTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.separatorTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r7.icon)(\"ngStyle\", item_r7.iconStyle);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r7.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r8 = i0.ɵɵreference(3);\n    const item_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.escape !== false)(\"ngIfElse\", htmlLabel_r8);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_ng_container_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_ng_template_4_a_1_ng_container_1_ng_container_2_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_a_1_ng_container_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_ng_container_2_1_Template, 1, 0, null, 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, item_r7));\n  }\n}\nfunction Breadcrumb_ng_template_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 27);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick($event, item_r7));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Breadcrumb_ng_template_4_a_1_ng_container_2_Template, 2, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", item_r7.target)(\"ariaCurrentWhenActive\", ctx_r1.isCurrentUrl(item_r7));\n    i0.ɵɵattribute(\"href\", item_r7.url ? item_r7.url : null, i0.ɵɵsanitizeUrl)(\"title\", item_r7.title)(\"tabindex\", item_r7.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.itemTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r7.icon)(\"ngStyle\", item_r7.iconStyle);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r7.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r10 = i0.ɵɵreference(3);\n    const item_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.escape !== false)(\"ngIfElse\", htmlRouteLabel_r10);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_ng_container_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_ng_template_4_a_2_ng_container_1_ng_container_2_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_a_2_ng_container_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_ng_container_2_1_Template, 1, 0, null, 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, item_r7));\n  }\n}\nfunction Breadcrumb_ng_template_4_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick($event, item_r7));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Breadcrumb_ng_template_4_a_2_ng_container_2_Template, 2, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r7.routerLink)(\"queryParams\", item_r7.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r7.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c1))(\"target\", item_r7.target)(\"fragment\", item_r7.fragment)(\"queryParamsHandling\", item_r7.queryParamsHandling)(\"preserveFragment\", item_r7.preserveFragment)(\"skipLocationChange\", item_r7.skipLocationChange)(\"replaceUrl\", item_r7.replaceUrl)(\"state\", item_r7.state)(\"ariaCurrentWhenActive\", ctx_r1.isCurrentUrl(item_r7));\n    i0.ɵɵattribute(\"title\", item_r7.title)(\"tabindex\", item_r7.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.itemTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_3_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction Breadcrumb_ng_template_4_li_3_2_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_ng_template_4_li_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_ng_template_4_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_3_ChevronRightIcon_1_Template, 1, 0, \"ChevronRightIcon\", 15)(2, Breadcrumb_ng_template_4_li_3_2_Template, 1, 0, null, 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"separator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.separatorTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.separatorTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 24);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_Template, 3, 7, \"a\", 25)(2, Breadcrumb_ng_template_4_a_2_Template, 3, 17, \"a\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Breadcrumb_ng_template_4_li_3_Template, 3, 3, \"li\", 7);\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const end_r11 = ctx.last;\n    i0.ɵɵclassMap(item_r7.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r7.style)(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, item_r7.disabled))(\"tooltipOptions\", item_r7.tooltipOptions);\n    i0.ɵɵattribute(\"id\", item_r7.id)(\"data-pc-section\", \"menuitem\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r7.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !end_r11);\n  }\n}\nlet Breadcrumb = /*#__PURE__*/(() => {\n  class Breadcrumb {\n    router;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    model;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * MenuItem configuration for the home icon.\n     * @group Props\n     */\n    home;\n    /**\n     * Defines a string that labels the home icon for accessibility.\n     * @group Props\n     */\n    homeAriaLabel;\n    /**\n     * Fired when an item is selected.\n     * @param {BreadcrumbItemClickEvent} event - custom click event.\n     * @group Emits\n     */\n    onItemClick = new EventEmitter();\n    templates;\n    separatorTemplate;\n    itemTemplate;\n    constructor(router) {\n      this.router = router;\n    }\n    onClick(event, item) {\n      if (item.disabled) {\n        event.preventDefault();\n        return;\n      }\n      if (!item.url && !item.routerLink) {\n        event.preventDefault();\n      }\n      if (item.command) {\n        item.command({\n          originalEvent: event,\n          item: item\n        });\n      }\n      this.onItemClick.emit({\n        originalEvent: event,\n        item: item\n      });\n    }\n    onHomeClick(event) {\n      if (this.home) {\n        this.onClick(event, this.home);\n      }\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'separator':\n            this.separatorTemplate = item.template;\n            break;\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    isCurrentUrl(item) {\n      const {\n        routerLink\n      } = item;\n      const lastPath = this.router ? this.router.url : '';\n      return routerLink === lastPath ? 'page' : undefined;\n    }\n    static ɵfac = function Breadcrumb_Factory(t) {\n      return new (t || Breadcrumb)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Breadcrumb,\n      selectors: [[\"p-breadcrumb\"]],\n      contentQueries: function Breadcrumb_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        model: \"model\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        home: \"home\",\n        homeAriaLabel: \"homeAriaLabel\"\n      },\n      outputs: {\n        onItemClick: \"onItemClick\"\n      },\n      decls: 5,\n      vars: 10,\n      consts: [[\"htmlHomeLabel\", \"\"], [\"htmlHomeRouteLabel\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngStyle\", \"ngClass\"], [1, \"p-breadcrumb-list\"], [\"pTooltip\", \"\", 3, \"class\", \"ngClass\", \"ngStyle\", \"tooltipOptions\", 4, \"ngIf\"], [\"class\", \"p-menuitem-separator\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", 3, \"href\", \"target\", \"ariaCurrentWhenActive\", \"click\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ariaCurrentWhenActive\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"click\", \"href\", \"target\", \"ariaCurrentWhenActive\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [3, \"styleClass\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-link\", 3, \"click\", \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ariaCurrentWhenActive\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [1, \"p-menuitem-separator\"], [4, \"ngTemplateOutlet\"], [\"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", 3, \"target\", \"ariaCurrentWhenActive\", \"click\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"ariaCurrentWhenActive\", \"click\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"click\", \"target\", \"ariaCurrentWhenActive\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-menuitem-link\", 3, \"click\", \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"ariaCurrentWhenActive\"]],\n      template: function Breadcrumb_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 4)(1, \"ol\", 5);\n          i0.ɵɵtemplate(2, Breadcrumb_li_2_Template, 3, 11, \"li\", 6)(3, Breadcrumb_li_3_Template, 3, 3, \"li\", 7)(4, Breadcrumb_ng_template_4_Template, 4, 12, \"ng-template\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-breadcrumb p-component\");\n          i0.ɵɵattribute(\"data-pc-name\", \"breadcrumb\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"data-pc-section\", \"menu\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.home);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.home);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.RouterLink, i1.RouterLinkActive, i3.Tooltip, ChevronRightIcon, HomeIcon],\n      styles: [\"@layer primeng{.p-breadcrumb{overflow-x:auto}.p-breadcrumb .p-breadcrumb-list{margin:0;padding:0;list-style-type:none;display:flex;align-items:center;flex-wrap:nowrap}.p-breadcrumb .p-menuitem-text{line-height:1}.p-breadcrumb .p-menuitem-link{text-decoration:none;display:flex;align-items:center}.p-breadcrumb .p-menuitem-separator{display:flex;align-items:center}.p-breadcrumb::-webkit-scrollbar{display:none}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Breadcrumb;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BreadcrumbModule = /*#__PURE__*/(() => {\n  class BreadcrumbModule {\n    static ɵfac = function BreadcrumbModule_Factory(t) {\n      return new (t || BreadcrumbModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BreadcrumbModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, TooltipModule, ChevronRightIcon, HomeIcon, SharedModule, RouterModule, TooltipModule, SharedModule]\n    });\n  }\n  return BreadcrumbModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Breadcrumb, BreadcrumbModule };\n//# sourceMappingURL=primeng-breadcrumb.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}