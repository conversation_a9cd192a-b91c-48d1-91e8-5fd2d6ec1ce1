{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { TableModule } from 'primeng/table';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\nimport * as i0 from \"@angular/core\";\nexport class CommonFormModule {\n  static {\n    this.ɵfac = function CommonFormModule_Factory(t) {\n      return new (t || CommonFormModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CommonFormModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, DropdownModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CommonFormModule, {\n    declarations: [ActivitiesFormComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, DropdownModule, CalendarModule],\n    exports: [ActivitiesFormComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "DialogModule", "TableModule", "NgSelectModule", "ButtonModule", "DropdownModule", "CalendarModule", "ActivitiesFormComponent", "CommonFormModule", "declarations", "imports", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\common-form\\common-form.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { TableModule } from 'primeng/table';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\r\n\r\n@NgModule({\r\n  declarations: [ActivitiesFormComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DialogModule,\r\n    TableModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    CalendarModule,\r\n  ],\r\n  exports: [ActivitiesFormComponent]\r\n})\r\nexport class CommonFormModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,uBAAuB,QAAQ,6CAA6C;;AAiBrF,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAZzBV,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc;IAAA;EAAA;;;2EAILE,gBAAgB;IAAAC,YAAA,GAdZF,uBAAuB;IAAAG,OAAA,GAEpCZ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc;IAAAK,OAAA,GAENJ,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}