{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../account.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"../../../shared/initials.pipe\";\nfunction AccountDetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"p-breadcrumb\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"model\", ctx_r0.breadcrumbitems)(\"home\", ctx_r0.home)(\"styleClass\", \"py-2 px-0 border-none\");\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r2.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r2.label);\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 38);\n    i0.ɵɵtemplate(1, AccountDetailsComponent_p_tabPanel_6_ng_template_1_Template, 2, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let AccountDetailsComponent = /*#__PURE__*/(() => {\n  class AccountDetailsComponent {\n    constructor(route, router, formBuilder, accountservice, messageservice, confirmationservice) {\n      this.route = route;\n      this.router = router;\n      this.formBuilder = formBuilder;\n      this.accountservice = accountservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.accountDetails = null;\n      this.sidebarDetails = null;\n      this.NoteDetails = null;\n      this.activeItem = {};\n      this.bp_id = '';\n      this.bp_status = '';\n      this.bp_doc_id = '';\n      this.partner_id = '';\n      this.partner_role = '';\n      this.loading = false;\n      this.activeIndex = 0;\n      this.isSidebarHidden = false;\n      this.submitted = false;\n      this.saving = false;\n      this.Actions = [];\n      this.hideBreadCrumbs = false;\n      this.GlobalNoteForm = this.formBuilder.group({\n        note: ['']\n      });\n    }\n    ngOnInit() {\n      this.hideBreadCrumbs = this.route.snapshot.data['hideBreadCrumbs'] || false;\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        this.partner_id = response?.bp_id;\n        if (this.partner_id) {\n          this.accountservice.getGlobalNote(this.partner_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n            next: noteResponse => {\n              this.NoteDetails = noteResponse?.data[0] || [];\n              this.GlobalNoteForm.patchValue({\n                note: noteResponse?.data[0]?.note\n              });\n            },\n            error: error => {\n              console.error('Error fetching global note:', error);\n            }\n          });\n        }\n      });\n      this.makeMenuItems(this.bp_id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const accountId = params.get('id');\n        if (accountId) {\n          this.loadAccountData(accountId);\n        }\n      });\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        const partner_role = response?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n        this.partner_role = partner_role?.business_partner?.bp_full_name || null;\n        this.accountDetails = response || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.addresses || []);\n      });\n    }\n    makeMenuItems(bp_id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `overview`\n      }, {\n        label: 'Contacts',\n        routerLink: `contacts`\n      }, {\n        label: 'Sales Team',\n        routerLink: `sales-team`\n      }, {\n        label: 'Relationships',\n        routerLink: `relationships`\n      },\n      // {\n      //   label: 'AI Insights',\n      //   routerLink: `ai-insights`,\n      // },\n      // {\n      //   label: 'Organization Data',\n      //   routerLink: `organization-data`,\n      // },\n      {\n        label: 'Attachments',\n        routerLink: `attachments`\n      }, {\n        label: 'Notes',\n        routerLink: `notes`\n      }, {\n        label: 'Activities',\n        routerLink: `activities`\n      }, {\n        label: 'Opportunities',\n        routerLink: `opportunities`\n      }, {\n        label: 'Tickets',\n        routerLink: `tickets`\n      }, {\n        label: 'Sales Quotes',\n        routerLink: `sales-quotes`\n      }, {\n        label: 'Sales Orders',\n        routerLink: `sales-orders`\n      }, {\n        label: 'Invoices',\n        routerLink: `invoices`\n      }, {\n        label: 'Returns',\n        routerLink: `returns`\n      }, {\n        label: 'Credit Memos',\n        routerLink: `credit-memos`\n      }];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Accounts',\n        routerLink: ['/store/account']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigate([selectedTab.routerLink], {\n          relativeTo: this.route\n        });\n      }\n    }\n    loadAccountData(accountId) {\n      this.loading = true;\n      this.accountservice.getAccountByID(accountId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response) {\n            this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\n            this.bp_doc_id = response?.data?.[0]?.bp_extension?.documentId;\n            this.Actions = [{\n              name: this.bp_status === 'OBSOLETE' ? 'Set As Active' : 'Set As Obsolete',\n              code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO'\n            }];\n            this.loading = false;\n          }\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n          this.loading = false;\n        }\n      });\n    }\n    formatSidebarDetails(addresses) {\n      return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n        ...address,\n        address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n        email_address: address?.emails?.[0]?.email_address || '-',\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n        website_url: address?.home_page_urls?.[0]?.website_url || '-'\n      }));\n    }\n    onActionChange(event) {\n      const actionCode = event.value?.code;\n      const actionsMap = {\n        SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\n        SAO: () => this.UpdateStatus(this.bp_doc_id, 'true')\n      };\n      const action = actionsMap[actionCode];\n      if (action) {\n        this.confirmationservice.confirm({\n          message: 'Are you sure you want to proceed with this action?',\n          header: 'Confirm',\n          icon: 'pi pi-exclamation-triangle',\n          accept: action\n        });\n      }\n    }\n    UpdateStatus(docid, status) {\n      const data = {\n        is_marked_for_archiving: status\n      };\n      this.accountservice.updateBpStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Action Updated Successfully!'\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    onNoteSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.GlobalNoteForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.GlobalNoteForm.value\n        };\n        const data = {\n          note: value?.note,\n          bp_id: _this?.partner_id,\n          ...(!_this.NoteDetails.documentId ? {\n            is_global_note: true\n          } : {})\n        };\n        const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.accountservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n        : _this.accountservice.createNote(data); // Create if not exists\n        apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Account Note Updated successFully!'\n            });\n            _this.accountservice.getAccountByID(_this.bp_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    goToBack() {\n      this.router.navigate(['/store/account']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountDetailsComponent_Factory(t) {\n        return new (t || AccountDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountDetailsComponent,\n        selectors: [[\"app-account-details\"]],\n        decls: 81,\n        vars: 25,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [\"class\", \"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\", 4, \"ngIf\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"onChange\", \"activeIndexChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function AccountDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵtemplate(2, AccountDetailsComponent_div_2_Template, 3, 3, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"p-tabView\", 5);\n            i0.ɵɵlistener(\"onChange\", function AccountDetailsComponent_Template_p_tabView_onChange_5_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function AccountDetailsComponent_Template_p_tabView_activeIndexChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(6, AccountDetailsComponent_p_tabPanel_6_Template, 2, 1, \"p-tabPanel\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"h5\", 14);\n            i0.ɵɵtext(15);\n            i0.ɵɵpipe(16, \"initials\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 15)(18, \"h5\", 16);\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"ul\", 17)(21, \"li\", 18)(22, \"span\", 19);\n            i0.ɵɵtext(23, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"li\", 18)(26, \"span\", 19);\n            i0.ɵɵtext(27, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"li\", 18)(30, \"span\", 19);\n            i0.ɵɵtext(31, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(33, \"div\", 20)(34, \"ul\", 21)(35, \"li\", 22)(36, \"span\", 23)(37, \"i\", 24);\n            i0.ɵɵtext(38, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(39, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"span\", 25);\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"li\", 22)(43, \"span\", 23)(44, \"i\", 24);\n            i0.ɵɵtext(45, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(46, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"span\", 25);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"li\", 22)(50, \"span\", 23)(51, \"i\", 24);\n            i0.ɵɵtext(52, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(53, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"span\", 25);\n            i0.ɵɵtext(55);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"li\", 22)(57, \"span\", 23)(58, \"i\", 24);\n            i0.ɵɵtext(59, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(60, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"span\", 25);\n            i0.ɵɵtext(62);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"li\", 22)(64, \"span\", 23)(65, \"i\", 24);\n            i0.ɵɵtext(66, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(67, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"span\", 25);\n            i0.ɵɵtext(69);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(70, \"form\", 26)(71, \"div\", 27)(72, \"h4\", 28);\n            i0.ɵɵtext(73, \"Global Note\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"div\", 29);\n            i0.ɵɵelement(75, \"textarea\", 30);\n            i0.ɵɵelementStart(76, \"button\", 31);\n            i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_button_click_76_listener() {\n              return ctx.onNoteSubmit();\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(77, \"div\", 32)(78, \"p-button\", 33);\n            i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_p_button_click_78_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(79, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(80, \"p-confirmDialog\", 34);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.hideBreadCrumbs);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 23, ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" : \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.Breadcrumb, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.PrimeTemplate, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i7.ButtonDirective, i7.Button, i8.TabView, i8.TabPanel, i9.Toast, i10.ConfirmDialog, i11.InitialsPipe],\n        styles: [\".account-popup .p-dialog{margin-right:50px}  .account-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .account-popup .p-dialog .p-dialog-header h4{margin:0}  .account-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return AccountDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}