{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * StyleClass manages css classes declaratively to during enter/leave animations or just to toggle classes on an element.\n * @group Components\n */\nlet StyleClass = /*#__PURE__*/(() => {\n  class StyleClass {\n    el;\n    renderer;\n    zone;\n    constructor(el, renderer, zone) {\n      this.el = el;\n      this.renderer = renderer;\n      this.zone = zone;\n    }\n    /**\n     * Selector to define the target element. Available selectors are '@next', '@prev', '@parent' and '@grandparent'.\n     * @group Props\n     */\n    selector;\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     * @deprecated Use enterFromClass instead\n     */\n    set enterClass(value) {\n      this._enterClass = value;\n      console.warn('enterClass is deprecated, use enterFromClass instead');\n    }\n    get enterClass() {\n      return this._enterClass;\n    }\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     */\n    enterFromClass;\n    /**\n     * Style class to add during enter animation.\n     * @group Props\n     */\n    enterActiveClass;\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     */\n    enterToClass;\n    /**\n     * Style class to add when item begins to get hidden.\n     * @group Props\n     * @deprecated Use leaveFromClass instead\n     */\n    set leaveClass(value) {\n      this._leaveClass = value;\n      console.warn('leaveClass is deprecated, use leaveFromClass instead');\n    }\n    get leaveClass() {\n      return this._leaveClass;\n    }\n    /**\n     * Style class to add when item begins to get hidden.\n     * @group Props\n     */\n    leaveFromClass;\n    /**\n     * Style class to add during leave animation.\n     * @group Props\n     */\n    leaveActiveClass;\n    /**\n     * Style class to add when leave animation is completed.\n     * @group Props\n     */\n    leaveToClass;\n    /**\n     * Whether to trigger leave animation when outside of the element is clicked.\n     * @group Props\n     */\n    hideOnOutsideClick;\n    /**\n     * Adds or removes a class when no enter-leave animation is required.\n     * @group Props\n     */\n    toggleClass;\n    /**\n     * Whether to trigger leave animation when escape key pressed.\n     * @group Props\n     */\n    hideOnEscape;\n    eventListener;\n    documentClickListener;\n    documentKeydownListener;\n    target;\n    enterListener;\n    leaveListener;\n    animating;\n    _enterClass;\n    _leaveClass;\n    clickListener() {\n      this.target = this.resolveTarget();\n      if (this.toggleClass) {\n        this.toggle();\n      } else {\n        if (this.target.offsetParent === null) this.enter();else this.leave();\n      }\n    }\n    toggle() {\n      if (DomHandler.hasClass(this.target, this.toggleClass)) DomHandler.removeClass(this.target, this.toggleClass);else DomHandler.addClass(this.target, this.toggleClass);\n    }\n    enter() {\n      if (this.enterActiveClass) {\n        if (!this.animating) {\n          this.animating = true;\n          if (this.enterActiveClass === 'slidedown') {\n            this.target.style.height = '0px';\n            DomHandler.removeClass(this.target, 'hidden');\n            this.target.style.maxHeight = this.target.scrollHeight + 'px';\n            DomHandler.addClass(this.target, 'hidden');\n            this.target.style.height = '';\n          }\n          DomHandler.addClass(this.target, this.enterActiveClass);\n          if (this.enterClass || this.enterFromClass) {\n            DomHandler.removeClass(this.target, this.enterClass || this.enterFromClass);\n          }\n          this.enterListener = this.renderer.listen(this.target, 'animationend', () => {\n            DomHandler.removeClass(this.target, this.enterActiveClass);\n            if (this.enterToClass) {\n              DomHandler.addClass(this.target, this.enterToClass);\n            }\n            this.enterListener && this.enterListener();\n            if (this.enterActiveClass === 'slidedown') {\n              this.target.style.maxHeight = '';\n            }\n            this.animating = false;\n          });\n        }\n      } else {\n        if (this.enterClass || this.enterFromClass) {\n          DomHandler.removeClass(this.target, this.enterClass || this.enterFromClass);\n        }\n        if (this.enterToClass) {\n          DomHandler.addClass(this.target, this.enterToClass);\n        }\n      }\n      if (this.hideOnOutsideClick) {\n        this.bindDocumentClickListener();\n      }\n      if (this.hideOnEscape) {\n        this.bindDocumentKeydownListener();\n      }\n    }\n    leave() {\n      if (this.leaveActiveClass) {\n        if (!this.animating) {\n          this.animating = true;\n          DomHandler.addClass(this.target, this.leaveActiveClass);\n          if (this.leaveClass || this.leaveFromClass) {\n            DomHandler.removeClass(this.target, this.leaveClass || this.leaveFromClass);\n          }\n          this.leaveListener = this.renderer.listen(this.target, 'animationend', () => {\n            DomHandler.removeClass(this.target, this.leaveActiveClass);\n            if (this.leaveToClass) {\n              DomHandler.addClass(this.target, this.leaveToClass);\n            }\n            this.leaveListener && this.leaveListener();\n            this.animating = false;\n          });\n        }\n      } else {\n        if (this.leaveClass || this.leaveFromClass) {\n          DomHandler.removeClass(this.target, this.leaveClass || this.leaveFromClass);\n        }\n        if (this.leaveToClass) {\n          DomHandler.addClass(this.target, this.leaveToClass);\n        }\n      }\n      if (this.hideOnOutsideClick) {\n        this.unbindDocumentClickListener();\n      }\n      if (this.hideOnEscape) {\n        this.unbindDocumentKeydownListener();\n      }\n    }\n    resolveTarget() {\n      if (this.target) {\n        return this.target;\n      }\n      switch (this.selector) {\n        case '@next':\n          return this.el.nativeElement.nextElementSibling;\n        case '@prev':\n          return this.el.nativeElement.previousElementSibling;\n        case '@parent':\n          return this.el.nativeElement.parentElement;\n        case '@grandparent':\n          return this.el.nativeElement.parentElement.parentElement;\n        default:\n          return document.querySelector(this.selector);\n      }\n    }\n    bindDocumentClickListener() {\n      if (!this.documentClickListener) {\n        this.documentClickListener = this.renderer.listen(this.el.nativeElement.ownerDocument, 'click', event => {\n          if (!this.isVisible() || getComputedStyle(this.target).getPropertyValue('position') === 'static') this.unbindDocumentClickListener();else if (this.isOutsideClick(event)) this.leave();\n        });\n      }\n    }\n    bindDocumentKeydownListener() {\n      if (!this.documentKeydownListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentKeydownListener = this.renderer.listen(this.el.nativeElement.ownerDocument, 'keydown', event => {\n            const {\n              key,\n              keyCode,\n              which,\n              type\n            } = event;\n            if (!this.isVisible() || getComputedStyle(this.target).getPropertyValue('position') === 'static') this.unbindDocumentKeydownListener();\n            if (this.isVisible() && key === 'Escape' && keyCode === 27 && which === 27) this.leave();\n          });\n        });\n      }\n    }\n    isVisible() {\n      return this.target.offsetParent !== null;\n    }\n    isOutsideClick(event) {\n      return !this.el.nativeElement.isSameNode(event.target) && !this.el.nativeElement.contains(event.target) && !this.target.contains(event.target);\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n      }\n    }\n    unbindDocumentKeydownListener() {\n      if (this.documentKeydownListener) {\n        this.documentKeydownListener();\n        this.documentKeydownListener = null;\n      }\n    }\n    ngOnDestroy() {\n      this.target = null;\n      if (this.eventListener) {\n        this.eventListener();\n      }\n      this.unbindDocumentClickListener();\n      this.unbindDocumentKeydownListener();\n    }\n    static ɵfac = function StyleClass_Factory(t) {\n      return new (t || StyleClass)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: StyleClass,\n      selectors: [[\"\", \"pStyleClass\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function StyleClass_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function StyleClass_click_HostBindingHandler($event) {\n            return ctx.clickListener($event);\n          });\n        }\n      },\n      inputs: {\n        selector: [i0.ɵɵInputFlags.None, \"pStyleClass\", \"selector\"],\n        enterClass: \"enterClass\",\n        enterFromClass: \"enterFromClass\",\n        enterActiveClass: \"enterActiveClass\",\n        enterToClass: \"enterToClass\",\n        leaveClass: \"leaveClass\",\n        leaveFromClass: \"leaveFromClass\",\n        leaveActiveClass: \"leaveActiveClass\",\n        leaveToClass: \"leaveToClass\",\n        hideOnOutsideClick: \"hideOnOutsideClick\",\n        toggleClass: \"toggleClass\",\n        hideOnEscape: \"hideOnEscape\"\n      }\n    });\n  }\n  return StyleClass;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet StyleClassModule = /*#__PURE__*/(() => {\n  class StyleClassModule {\n    static ɵfac = function StyleClassModule_Factory(t) {\n      return new (t || StyleClassModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: StyleClassModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return StyleClassModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { StyleClass, StyleClassModule };\n//# sourceMappingURL=primeng-styleclass.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}