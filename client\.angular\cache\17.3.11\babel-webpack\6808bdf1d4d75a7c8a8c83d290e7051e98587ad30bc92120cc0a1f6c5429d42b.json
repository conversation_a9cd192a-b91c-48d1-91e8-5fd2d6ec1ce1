{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AccountRoutingModule } from './account-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AccountComponent } from './account.component';\nimport { AccountDetailsComponent } from './account-details/account-details.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\nimport { AccountPartnersComponent } from './account-details/account-partners/account-partners.component';\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\nimport * as i0 from \"@angular/core\";\nexport class AccountModule {\n  static {\n    this.ɵfac = function AccountModule_Factory(t) {\n      return new (t || AccountModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccountModule, {\n    declarations: [AccountComponent, AccountDetailsComponent, AccountOverviewComponent, AccountContactsComponent, AccountSalesTeamComponent, AccountAiInsightsComponent, AccountOrganizationDataComponent, AccountAttachmentsComponent, AccountNotesComponent, AccountPartnersComponent, AccountOpportunitiesComponent, AccountActivitiesComponent, AccountRelationshipsComponent, AccountTicketsComponent, AccountSalesQuotesComponent, AccountSalesOrdersComponent],\n    imports: [CommonModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AccountRoutingModule", "FormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "AccountComponent", "AccountDetailsComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "AccountOverviewComponent", "AccountContactsComponent", "AccountSalesTeamComponent", "AccountAiInsightsComponent", "AccountOrganizationDataComponent", "AccountAttachmentsComponent", "AccountNotesComponent", "AccountPartnersComponent", "AccountOpportunitiesComponent", "AccountActivitiesComponent", "AccountRelationshipsComponent", "AccountTicketsComponent", "AccountSalesQuotesComponent", "AccountSalesOrdersComponent", "AccountModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AccountRoutingModule } from './account-routing.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { AccountComponent } from './account.component';\r\nimport { AccountDetailsComponent } from './account-details/account-details.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\r\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\r\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\r\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\r\nimport { AccountPartnersComponent } from './account-details/account-partners/account-partners.component';\r\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\r\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\r\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\r\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AccountComponent,\r\n    AccountDetailsComponent,\r\n    AccountOverviewComponent,\r\n    AccountContactsComponent,\r\n    AccountSalesTeamComponent,\r\n    AccountAiInsightsComponent,\r\n    AccountOrganizationDataComponent,\r\n    AccountAttachmentsComponent,\r\n    AccountNotesComponent,\r\n    AccountPartnersComponent,\r\n    AccountOpportunitiesComponent,\r\n    AccountActivitiesComponent,\r\n    AccountRelationshipsComponent,\r\n    AccountTicketsComponent,\r\n    AccountSalesQuotesComponent,\r\n    AccountSalesOrdersComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AccountRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    InputTextModule\r\n  ]\r\n})\r\nexport class AccountModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,gCAAgC,QAAQ,iFAAiF;AAClI,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,2BAA2B,QAAQ,uEAAuE;;AAqCnH,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAbtB3B,YAAY,EACZC,oBAAoB,EACpBE,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXJ,WAAW,EACXE,cAAc,EACdM,YAAY,EACZE,aAAa,EACbH,kBAAkB,EAClBE,eAAe;IAAA;EAAA;;;2EAGNgB,aAAa;IAAAC,YAAA,GA/BtBrB,gBAAgB,EAChBC,uBAAuB,EACvBK,wBAAwB,EACxBC,wBAAwB,EACxBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,2BAA2B,EAC3BC,qBAAqB,EACrBC,wBAAwB,EACxBC,6BAA6B,EAC7BC,0BAA0B,EAC1BC,6BAA6B,EAC7BC,uBAAuB,EACvBC,2BAA2B,EAC3BC,2BAA2B;IAAAG,OAAA,GAG3B7B,YAAY,EACZC,oBAAoB,EACpBE,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXJ,WAAW,EACXE,cAAc,EACdM,YAAY,EACZE,aAAa,EACbH,kBAAkB,EAClBE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}