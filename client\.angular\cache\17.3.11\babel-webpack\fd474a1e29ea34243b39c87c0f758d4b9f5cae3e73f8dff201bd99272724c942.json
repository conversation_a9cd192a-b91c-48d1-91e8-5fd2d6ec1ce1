{"ast": null, "code": "import { Subject, forkJoin } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./organizational.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/table\";\nimport * as i10 from \"primeng/checkbox\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nconst _c1 = (a0, a1) => ({\n  \"pi-chevron-down\": a0,\n  \"pi-chevron-right\": a1\n});\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  indent: a1\n});\nconst _c3 = a0 => ({\n  $implicit: a0,\n  indent: 12\n});\nfunction OrganizationalComponent_ng_template_15_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrderOrg === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OrganizationalComponent_ng_template_15_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrderOrg === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_15_ng_container_8_Template_th_click_1_listener() {\n      const col_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field, ctx_r3.organization, \"ORG\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OrganizationalComponent_ng_template_15_ng_container_8_i_4_Template, 1, 1, \"i\", 23)(5, OrganizationalComponent_ng_template_15_ng_container_8_i_5_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg !== col_r6.field);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_15_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"name\", ctx_r3.organization, \"ORG\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵtemplate(6, OrganizationalComponent_ng_template_15_i_6_Template, 1, 1, \"i\", 23)(7, OrganizationalComponent_ng_template_15_i_7_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_15_ng_container_8_Template, 6, 4, \"ng-container\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg === \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg !== \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const organization_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggle(organization_r8));\n    });\n    i0.ɵɵelement(1, \"span\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, organization_r8.expanded, !organization_r8.expanded));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r8 == null ? null : organization_r8.crm_org_unit_managers == null ? null : organization_r8.crm_org_unit_managers[0] == null ? null : organization_r8.crm_org_unit_managers[0].business_partner == null ? null : organization_r8.crm_org_unit_managers[0].business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r8 == null ? null : organization_r8.parent_organisational_unit == null ? null : organization_r8.parent_organisational_unit.name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r8 == null ? null : organization_r8.organisational_unit_id) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r8 == null ? null : organization_r8.parent_organisational_unit_id) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r8 == null ? null : organization_r8.crm_org_unit_functions == null ? null : organization_r8.crm_org_unit_functions[0] == null ? null : organization_r8.crm_org_unit_functions[0].sales_organisation_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r8 == null ? null : organization_r8.crm_org_unit_functions == null ? null : organization_r8.crm_org_unit_functions[0] == null ? null : organization_r8.crm_org_unit_functions[0].sales_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r8 == null ? null : organization_r8.crm_org_unit_functions == null ? null : organization_r8.crm_org_unit_functions[0] == null ? null : organization_r8.crm_org_unit_functions[0].reporting_line_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 39);\n    i0.ɵɵtemplate(3, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template, 2, 1, \"ng-container\", 40)(4, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template, 2, 1, \"ng-container\", 40)(5, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template, 2, 1, \"ng-container\", 40)(6, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template, 2, 1, \"ng-container\", 40)(7, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template, 2, 3, \"ng-container\", 40)(8, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template, 2, 3, \"ng-container\", 40)(9, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template, 2, 3, \"ng-container\", 40);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r9.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"crm_org_unit_managers.business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_organisation_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"reporting_line_indicator\");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const node_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggle(node_r11));\n    });\n    i0.ɵɵelement(1, \"span\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, node_r11.expanded, !node_r11.expanded));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (node_r11.crm_org_unit_managers == null ? null : node_r11.crm_org_unit_managers[0] == null ? null : node_r11.crm_org_unit_managers[0].business_partner == null ? null : node_r11.crm_org_unit_managers[0].business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (node_r11.parent_organisational_unit == null ? null : node_r11.parent_organisational_unit.name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r11.organisational_unit_id || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r11.parent_organisational_unit_id || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r11.crm_org_unit_functions == null ? null : node_r11.crm_org_unit_functions[0] == null ? null : node_r11.crm_org_unit_functions[0].sales_organisation_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r11.crm_org_unit_functions == null ? null : node_r11.crm_org_unit_functions[0] == null ? null : node_r11.crm_org_unit_functions[0].sales_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 41);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r11.crm_org_unit_functions == null ? null : node_r11.crm_org_unit_functions[0] == null ? null : node_r11.crm_org_unit_functions[0].reporting_line_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r12 = i0.ɵɵnextContext().$implicit;\n    const node_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r11[col_r12.field] || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 39);\n    i0.ɵɵtemplate(3, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template, 2, 1, \"ng-container\", 40)(4, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template, 2, 1, \"ng-container\", 40)(5, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template, 2, 1, \"ng-container\", 40)(6, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template, 2, 1, \"ng-container\", 40)(7, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template, 2, 3, \"ng-container\", 40)(8, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template, 2, 3, \"ng-container\", 40)(9, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template, 2, 3, \"ng-container\", 40)(10, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template, 2, 1, \"ng-container\", 45);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r12.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"crm_org_unit_managers.business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_organisation_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"reporting_line_indicator\");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 46);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const child_r13 = ctx.$implicit;\n    const indent_r14 = i0.ɵɵnextContext(3).indent;\n    i0.ɵɵnextContext();\n    const rowTpl_r15 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowTpl_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, child_r13, indent_r14 + 16));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template, 2, 5, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const wrap_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", wrap_r16.data);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", node_r11.details);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 42)(1, \"td\", 43);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 32)(4, \"div\", 44);\n    i0.ɵɵtemplate(5, OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template, 2, 4, \"button\", 34);\n    i0.ɵɵelementStart(6, \"a\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template, 11, 8, \"ng-container\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template, 2, 1, \"ng-container\", 36);\n  }\n  if (rf & 2) {\n    const node_r11 = ctx.$implicit;\n    const indent_r14 = ctx.indent;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", node_r11);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"margin-left\", indent_r14, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", node_r11.child_organisational_units == null ? null : node_r11.child_organisational_units.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/organization/\" + node_r11.organisational_unit_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r11.name || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", node_r11.expanded);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 46);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const child_r17 = ctx.$implicit;\n    i0.ɵɵnextContext(3);\n    const rowTpl_r15 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowTpl_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, child_r17));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template, 2, 4, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const wrap_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", wrap_r18.data);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", organization_r8.details);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 29)(1, \"td\", 30);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 32)(4, \"div\", 33);\n    i0.ɵɵtemplate(5, OrganizationalComponent_ng_template_16_button_5_Template, 2, 4, \"button\", 34);\n    i0.ɵɵelementStart(6, \"a\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_16_ng_container_8_Template, 10, 8, \"ng-container\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, OrganizationalComponent_ng_template_16_ng_template_9_Template, 10, 8, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(11, OrganizationalComponent_ng_template_16_ng_container_11_Template, 2, 1, \"ng-container\", 36);\n  }\n  if (rf & 2) {\n    const organization_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", organization_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (organization_r8 == null ? null : organization_r8.child_organisational_units == null ? null : organization_r8.child_organisational_units.length) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/organization/\" + organization_r8.organisational_unit_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", organization_r8.name || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", organization_r8.expanded);\n  }\n}\nfunction OrganizationalComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 47);\n    i0.ɵɵtext(2, \"No organization found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OrganizationalComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 47);\n    i0.ɵɵtext(2, \"Loading organization data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OrganizationalComponent {\n  constructor(router, organizationalservice) {\n    this.router = router;\n    this.organizationalservice = organizationalservice;\n    this.unsubscribe$ = new Subject();\n    this.organization = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.selectedOrganizations = [];\n    this.createOptions = [];\n    this._selectedOrgColumns = [];\n    this.OrgCols = [{\n      field: 'crm_org_unit_managers.business_partner.bp_full_name',\n      header: 'Manager'\n    }, {\n      field: 'parent_organisational_unit.name',\n      header: 'Parent Unit Name'\n    }, {\n      field: 'organisational_unit_id',\n      header: 'ID'\n    }, {\n      field: 'parent_organisational_unit_id',\n      header: 'Parent Unit ID'\n    }, {\n      field: 'sales_organisation_indicator',\n      header: 'Sales Organization'\n    }, {\n      field: 'sales_indicator',\n      header: 'Sales'\n    }, {\n      field: 'reporting_line_indicator',\n      header: 'Reporting Line'\n    }];\n    this.sortFieldOrg = '';\n    this.sortOrderOrg = 1;\n    this.trackByUnit = (_, u) => u?.organisational_unit_id ?? _;\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Organization',\n      routerLink: ['/store/organization']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this._selectedOrgColumns = this.OrgCols;\n    this.updateCreateOptions();\n  }\n  get selectedOrgColumns() {\n    return this._selectedOrgColumns;\n  }\n  set selectedOrgColumns(val) {\n    this._selectedOrgColumns = this.OrgCols.filter(col => val.includes(col));\n  }\n  onOrgColumnReorder(event) {\n    const draggedCol = this.OrgCols[event.dragIndex];\n    this.OrgCols.splice(event.dragIndex, 1);\n    this.OrgCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'ORG') {\n      if (this.sortFieldOrg === field) {\n        // Toggle sort order if same column is clicked\n        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;\n      } else {\n        // Reset to ascending when changing columns\n        this.sortFieldOrg = field;\n        this.sortOrderOrg = 1;\n      }\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrderOrg * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  updateCreateOptions() {\n    this.createOptions = [{\n      label: 'Org Unit',\n      value: 'org-unit',\n      disabled: false\n    }, {\n      label: 'SubOrg Unit',\n      value: 'sub-unit',\n      disabled: !(this.selectedOrganizations?.length > 0)\n    }];\n  }\n  loadOrganization(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.organizationalservice.getOrganization(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.organization = response?.data || [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching organization', error);\n        this.loading = false;\n      }\n    });\n  }\n  create(event) {\n    if (this.selectedOrganizations?.length && event.value === 'sub-unit') {\n      const selected = this.selectedOrganizations[0];\n      const parentUnitId = selected?.organisational_unit_id;\n      this.router.navigate(['/store/organization/createsub'], {\n        state: {\n          parentUnitId\n        }\n      });\n    } else {\n      this.router.navigate(['/store/organization/create']);\n    }\n  }\n  toggle(organization) {\n    organization.expanded = !organization.expanded;\n    if (!organization.expanded) {\n      return;\n    }\n    const childIds = organization?.child_organisational_units?.map(c => c?.organisational_unit_id)?.filter(id => !!id) || [];\n    if (childIds.length === 0) {\n      organization.expanded = false;\n      return;\n    }\n    forkJoin(childIds.map(id => this.organizationalservice.getOrganizationByChildID(id))).pipe(map(resArray => resArray.flatMap(res => res ?? []))).subscribe({\n      next: units => {\n        organization.details = units;\n      },\n      error: err => {\n        console.error('Error loading expanded data', err);\n        organization.expanded = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadOrganization({\n      first: 0,\n      rows: 10\n    });\n  }\n  signup() {\n    this.router.navigate(['/store/organization/create']);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OrganizationalComponent_Factory(t) {\n      return new (t || OrganizationalComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.OrganizationalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrganizationalComponent,\n      selectors: [[\"app-organizational\"]],\n      viewQuery: function OrganizationalComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 19,\n      vars: 19,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [\"rowTpl\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Organization\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"placeholder\", \"Create\", \"optionLabel\", \"label\", 3, \"onChange\", \"options\", \"styleClass\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"selectionChange\", \"value\", \"rows\", \"paginator\", \"loading\", \"lazy\", \"totalRecords\", \"scrollable\", \"reorderableColumns\", \"selection\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"text-orange-600\", \"font-medium\", \"underline\", 3, \"routerLink\"], [4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button\", \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", \"p-button-icon-only\", \"p-button-sm\", 3, \"click\"], [1, \"pi\", 2, \"font-size\", \"0.75rem\", 3, \"ngClass\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"binary\", \"disabled\", \"ngModel\"], [1, \"bg-surface-50\", \"text-sm\", \"org-child-row\"], [\"pFrozenColumn\", \"\", 1, \"pl-3\", \"w-2rem\", \"text-center\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"ml-5\"], [4, \"ngSwitchDefault\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\"]],\n      template: function OrganizationalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8)(6, \"span\", 9)(7, \"input\", 10, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OrganizationalComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function OrganizationalComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(14);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 12);\n          i0.ɵɵlistener(\"onChange\", function OrganizationalComponent_Template_p_dropdown_onChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.create($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-multiSelect\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OrganizationalComponent_Template_p_multiSelect_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedOrgColumns, $event) || (ctx.selectedOrgColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 14)(13, \"p-table\", 15, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function OrganizationalComponent_Template_p_table_onLazyLoad_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadOrganization($event));\n          })(\"onColReorder\", function OrganizationalComponent_Template_p_table_onColReorder_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOrgColumnReorder($event));\n          });\n          i0.ɵɵtwoWayListener(\"selectionChange\", function OrganizationalComponent_Template_p_table_selectionChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedOrganizations, $event) || (ctx.selectedOrganizations = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectionChange\", function OrganizationalComponent_Template_p_table_selectionChange_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateCreateOptions());\n          });\n          i0.ɵɵtemplate(15, OrganizationalComponent_ng_template_15_Template, 9, 3, \"ng-template\", 16)(16, OrganizationalComponent_ng_template_16_Template, 12, 6, \"ng-template\", 17)(17, OrganizationalComponent_ng_template_17_Template, 3, 0, \"ng-template\", 18)(18, OrganizationalComponent_ng_template_18_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.createOptions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.OrgCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedOrgColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.organization)(\"rows\", 14)(\"paginator\", true)(\"loading\", ctx.loading)(\"paginator\", true)(\"lazy\", true)(\"totalRecords\", ctx.totalRecords)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedOrganizations);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i1.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.ButtonDirective, i6.PrimeTemplate, i7.Dropdown, i8.Breadcrumb, i9.Table, i9.SortableColumn, i9.FrozenColumn, i9.ReorderableColumn, i9.TableCheckbox, i9.TableHeaderCheckbox, i10.Checkbox, i11.MultiSelect],\n      styles: [\".org-child-row {\\n  font-size: 14px !important; \\n\\n}\\n\\n  .org-child-row a {\\n  font-size: 14px !important;\\n}\\n\\n  .org-child-row td {\\n  padding-top: 6px !important;\\n  padding-bottom: 6px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3JnYW5pemF0aW9uYWwvb3JnYW5pemF0aW9uYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSwwQkFBQSxFQUFBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSwwQkFBQTtBQUNGOztBQUVBO0VBQ0UsMkJBQUE7RUFDQSw4QkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5vcmctY2hpbGQtcm93IHtcclxuICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDsgLyogc21hbGxlciBmb250ICovXHJcbn1cclxuXHJcbjo6bmctZGVlcCAub3JnLWNoaWxkLXJvdyBhIHtcclxuICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5vcmctY2hpbGQtcm93IHRkIHtcclxuICBwYWRkaW5nLXRvcDogNnB4ICFpbXBvcnRhbnQ7XHJcbiAgcGFkZGluZy1ib3R0b206IDZweCAhaW1wb3J0YW50O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "fork<PERSON><PERSON>n", "map", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r3", "sortOrderOrg", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "OrganizationalComponent_ng_template_15_ng_container_8_Template_th_click_1_listener", "col_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "organization", "ɵɵtext", "ɵɵtemplate", "OrganizationalComponent_ng_template_15_ng_container_8_i_4_Template", "OrganizationalComponent_ng_template_15_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortFieldOrg", "OrganizationalComponent_ng_template_15_Template_th_click_3_listener", "_r3", "OrganizationalComponent_ng_template_15_i_6_Template", "OrganizationalComponent_ng_template_15_i_7_Template", "OrganizationalComponent_ng_template_15_ng_container_8_Template", "selectedOrgColumns", "OrganizationalComponent_ng_template_16_button_5_Template_button_click_0_listener", "_r7", "organization_r8", "toggle", "ɵɵpureFunction2", "_c1", "expanded", "crm_org_unit_managers", "business_partner", "bp_full_name", "parent_organisational_unit", "name", "organisational_unit_id", "parent_organisational_unit_id", "crm_org_unit_functions", "sales_organisation_indicator", "sales_indicator", "reporting_line_indicator", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template", "col_r9", "OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template_button_click_0_listener", "_r10", "node_r11", "col_r12", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template", "ɵɵelementContainer", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template", "rowTpl_r15", "_c2", "child_r13", "indent_r14", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template", "wrap_r16", "data", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template", "details", "OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template", "ɵɵstyleProp", "child_organisational_units", "length", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template", "ɵɵpureFunction1", "_c3", "child_r17", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template", "wrap_r18", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template", "OrganizationalComponent_ng_template_16_button_5_Template", "OrganizationalComponent_ng_template_16_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_Template", "ɵɵtemplateRefExtractor", "OrganizationalComponent_ng_template_16_ng_container_11_Template", "OrganizationalComponent", "constructor", "router", "organizationalservice", "unsubscribe$", "totalRecords", "loading", "globalSearchTerm", "selectedOrganizations", "createOptions", "_selectedOrgColumns", "OrgCols", "trackByUnit", "_", "u", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "updateCreateOptions", "val", "filter", "col", "includes", "onOrgColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "type", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "value", "i", "disabled", "loadOrganization", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getOrganization", "subscribe", "next", "response", "meta", "pagination", "total", "error", "console", "create", "selected", "parentUnitId", "navigate", "state", "childIds", "c", "id", "getOrganizationByChildID", "pipe", "resArray", "flatMap", "res", "units", "err", "onGlobalFilter", "table", "signup", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "i2", "OrganizationalService", "selectors", "viewQuery", "OrganizationalComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "OrganizationalComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "OrganizationalComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "OrganizationalComponent_Template_p_dropdown_onChange_10_listener", "OrganizationalComponent_Template_p_multiSelect_ngModelChange_11_listener", "OrganizationalComponent_Template_p_table_onLazyLoad_13_listener", "OrganizationalComponent_Template_p_table_onColReorder_13_listener", "OrganizationalComponent_Template_p_table_selectionChange_13_listener", "OrganizationalComponent_ng_template_15_Template", "OrganizationalComponent_ng_template_16_Template", "OrganizationalComponent_ng_template_17_Template", "OrganizationalComponent_ng_template_18_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject, forkJoin } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { Table } from 'primeng/table';\r\nimport { Router } from '@angular/router';\r\nimport { OrganizationalService } from './organizational.service';\r\nimport { DropdownChangeEvent } from 'primeng/dropdown';\r\n\r\ninterface OrgColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\ninterface CreateOption {\r\n  label: string;\r\n  value: 'sub-unit' | 'org-unit';\r\n  disabled: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-organizational',\r\n  templateUrl: './organizational.component.html',\r\n  styleUrl: './organizational.component.scss',\r\n})\r\nexport class OrganizationalComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('dt1') dt1!: Table;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public organization: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public selectedOrganizations: any[] = [];\r\n  public createOptions: CreateOption[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private organizationalservice: OrganizationalService\r\n  ) {}\r\n\r\n  private _selectedOrgColumns: OrgColumn[] = [];\r\n\r\n  public OrgCols: OrgColumn[] = [\r\n    {\r\n      field: 'crm_org_unit_managers.business_partner.bp_full_name',\r\n      header: 'Manager',\r\n    },\r\n    { field: 'parent_organisational_unit.name', header: 'Parent Unit Name' },\r\n    { field: 'organisational_unit_id', header: 'ID' },\r\n    { field: 'parent_organisational_unit_id', header: 'Parent Unit ID' },\r\n    { field: 'sales_organisation_indicator', header: 'Sales Organization' },\r\n    { field: 'sales_indicator', header: 'Sales' },\r\n    { field: 'reporting_line_indicator', header: 'Reporting Line' },\r\n  ];\r\n\r\n  sortFieldOrg: string = '';\r\n  sortOrderOrg: number = 1;\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Organization', routerLink: ['/store/organization'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this._selectedOrgColumns = this.OrgCols;\r\n    this.updateCreateOptions();\r\n  }\r\n\r\n  get selectedOrgColumns(): any[] {\r\n    return this._selectedOrgColumns;\r\n  }\r\n\r\n  set selectedOrgColumns(val: any[]) {\r\n    this._selectedOrgColumns = this.OrgCols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onOrgColumnReorder(event: any) {\r\n    const draggedCol = this.OrgCols[event.dragIndex];\r\n    this.OrgCols.splice(event.dragIndex, 1);\r\n    this.OrgCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(field: string, data: any[], type: 'ORG') {\r\n    if (type === 'ORG') {\r\n      if (this.sortFieldOrg === field) {\r\n        // Toggle sort order if same column is clicked\r\n        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;\r\n      } else {\r\n        // Reset to ascending when changing columns\r\n        this.sortFieldOrg = field;\r\n        this.sortOrderOrg = 1;\r\n      }\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrderOrg * result;\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  updateCreateOptions(): void {\r\n    this.createOptions = [\r\n      { label: 'Org Unit', value: 'org-unit', disabled: false },\r\n      {\r\n        label: 'SubOrg Unit',\r\n        value: 'sub-unit',\r\n        disabled: !(this.selectedOrganizations?.length > 0),\r\n      },\r\n    ];\r\n  }\r\n\r\n  loadOrganization(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.organizationalservice\r\n      .getOrganization(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.organization = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching organization', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  create(event: DropdownChangeEvent): void {\r\n    if (this.selectedOrganizations?.length && event.value === 'sub-unit') {\r\n      const selected = this.selectedOrganizations[0];\r\n      const parentUnitId = selected?.organisational_unit_id;\r\n\r\n      this.router.navigate(['/store/organization/createsub'], {\r\n        state: { parentUnitId },\r\n      });\r\n    } else {\r\n      this.router.navigate(['/store/organization/create']);\r\n    }\r\n  }\r\n\r\n  toggle(organization: any): void {\r\n    organization.expanded = !organization.expanded;\r\n    if (!organization.expanded) {\r\n      return;\r\n    }\r\n\r\n    const childIds: string[] =\r\n      organization?.child_organisational_units\r\n        ?.map((c: any) => c?.organisational_unit_id)\r\n        ?.filter((id: string | undefined): id is string => !!id) || [];\r\n\r\n    if (childIds.length === 0) {\r\n      organization.expanded = false;\r\n      return;\r\n    }\r\n\r\n    forkJoin(\r\n      childIds.map((id) =>\r\n        this.organizationalservice.getOrganizationByChildID(id)\r\n      )\r\n    )\r\n      .pipe(map((resArray) => resArray.flatMap((res) => res ?? [])))\r\n      .subscribe({\r\n        next: (units: any[]) => {\r\n          organization.details = units;\r\n        },\r\n        error: (err) => {\r\n          console.error('Error loading expanded data', err);\r\n          organization.expanded = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  trackByUnit = (_: number, u: any) => u?.organisational_unit_id ?? _;\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadOrganization({ first: 0, rows: 10 });\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/organization/create']);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" placeholder=\"Search Organization\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                        (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n            <!-- <button type=\"button\" (click)=\"create()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button> -->\r\n            <p-dropdown [options]=\"createOptions\" placeholder=\"Create\" optionLabel=\"label\"\r\n                (onChange)=\"create($event)\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\">\r\n            </p-dropdown>\r\n\r\n            <p-multiSelect [options]=\"OrgCols\" [(ngModel)]=\"selectedOrgColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"organization\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadOrganization($event)\"\r\n            styleClass=\"w-full\" [paginator]=\"true\" [loading]=\"loading\" [paginator]=\"true\" [lazy]=\"true\"\r\n            [totalRecords]=\"totalRecords\" [scrollable]=\"true\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onOrgColumnReorder($event)\" [(selection)]=\"selectedOrganizations\" (selectionChange)=\"updateCreateOptions()\" responsiveLayout=\"scroll\"\r\n            class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('name', organization, 'ORG')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortFieldOrg === 'name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldOrg !== 'name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, organization, 'ORG')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldOrg === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldOrg !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-organization>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"organization\" />\r\n                    </td>\r\n\r\n                    <td pFrozenColumn class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <button *ngIf=\"organization?.child_organisational_units?.length > 0\" pButton type=\"button\"\r\n                                class=\"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\"\r\n                                (click)=\"toggle(organization)\">\r\n                                <span class=\"pi\" [ngClass]=\"{\r\n      'pi-chevron-down': organization.expanded,\r\n      'pi-chevron-right': !organization.expanded\r\n    }\" style=\"font-size: 0.75rem;\"></span>\r\n                            </button>\r\n\r\n\r\n                            <a [routerLink]=\"'/store/organization/' + organization.organisational_unit_id\"\r\n                                class=\"text-orange-600 font-medium underline\">\r\n                                {{ organization.name || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n\r\n\r\n                    <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'crm_org_unit_managers.business_partner.bp_full_name'\">\r\n                                    {{ organization?.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-'\r\n                                    }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'parent_organisational_unit.name'\">\r\n                                    {{ organization?.parent_organisational_unit?.name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'organisational_unit_id'\">\r\n                                    {{ organization?.organisational_unit_id || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'parent_organisational_unit_id'\">\r\n                                    {{ organization?.parent_organisational_unit_id || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'sales_organisation_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.sales_organisation_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'sales_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.sales_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'reporting_line_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.reporting_line_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n                <!-- ───── reusable row template ───── -->\r\n                <ng-template #rowTpl let-node let-indent=\"indent\">\r\n                    <tr class=\"bg-surface-50 text-sm org-child-row\">\r\n\r\n                        <!-- checkbox -->\r\n                        <td pFrozenColumn class=\"pl-3 w-2rem text-center\">\r\n                            <p-tableCheckbox [value]=\"node\"></p-tableCheckbox>\r\n                        </td>\r\n\r\n                        <!-- name + expander -->\r\n                        <td pFrozenColumn class=\"border-round-left-lg\">\r\n                            <div class=\"flex align-items-center gap-2 ml-5\" [style.margin-left.px]=\"indent\">\r\n                                <button *ngIf=\"node.child_organisational_units?.length\" pButton type=\"button\"\r\n                                    class=\"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\"\r\n                                    (click)=\"toggle(node)\">\r\n                                    <span class=\"pi\" [ngClass]=\"{\r\n                  'pi-chevron-down': node.expanded,\r\n                  'pi-chevron-right': !node.expanded\r\n                }\" style=\"font-size:0.75rem;\"></span>\r\n                                </button>\r\n\r\n                                <a [routerLink]=\"'/store/organization/' + node.organisational_unit_id\"\r\n                                    class=\"text-orange-600 font-medium underline\">\r\n                                    {{ node.name || '-' }}\r\n                                </a>\r\n                            </div>\r\n                        </td>\r\n\r\n                        <!-- dynamic columns (unchanged, but now use 'node') -->\r\n                        <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                            <td>\r\n                                <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                    <ng-container *ngSwitchCase=\"'crm_org_unit_managers.business_partner.bp_full_name'\">\r\n                                        {{ node.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'parent_organisational_unit.name'\">\r\n                                        {{ node.parent_organisational_unit?.name || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'organisational_unit_id'\">\r\n                                        {{ node.organisational_unit_id || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'parent_organisational_unit_id'\">\r\n                                        {{ node.parent_organisational_unit_id || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'sales_organisation_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.sales_organisation_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'sales_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.sales_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'reporting_line_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.reporting_line_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchDefault>\r\n                                        {{ node[col.field] || '-' }}\r\n                                    </ng-container>\r\n\r\n                                </ng-container>\r\n                            </td>\r\n                        </ng-container>\r\n                    </tr>\r\n\r\n                    <!-- ───── recurse to children, grandchildren, … ───── -->\r\n                    <ng-container *ngIf=\"node.expanded\">\r\n                        <ng-container *ngFor=\"let wrap of node.details\">\r\n                            <ng-container *ngFor=\"let child of wrap.data\">\r\n                                <ng-container\r\n                                    *ngTemplateOutlet=\"rowTpl; context:{ $implicit: child, indent: indent + 16 }\"></ng-container>\r\n                            </ng-container>\r\n                        </ng-container>\r\n                    </ng-container>\r\n                </ng-template>\r\n\r\n                <!-- ───── render first‑level children of current \"organization\" ───── -->\r\n                <ng-container *ngIf=\"organization.expanded\">\r\n                    <ng-container *ngFor=\"let wrap of organization.details\">\r\n                        <ng-container *ngFor=\"let child of wrap.data\">\r\n                            <ng-container\r\n                                *ngTemplateOutlet=\"rowTpl; context:{ $implicit: child, indent: 12 }\"></ng-container>\r\n                        </ng-container>\r\n                    </ng-container>\r\n                </ng-container>\r\n\r\n\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg\">No organization found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg\">Loading organization data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,MAAM;AACxC,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC2CRC,EAAA,CAAAC,SAAA,YACyF;;;;IAArFD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,yDAAgF;;;;;IACpFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;IAQ3DD,EAAA,CAAAC,SAAA,YACyF;;;;IAArFD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,yDAAgF;;;;;IACpFJ,EAAA,CAAAC,SAAA,YAAkE;;;;;;IAP9ED,EAAA,CAAAK,uBAAA,GAAqD;IACjDL,EAAA,CAAAM,cAAA,aACyD;IAArDN,EAAA,CAAAO,UAAA,mBAAAC,mFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,EAAAb,MAAA,CAAAc,YAAA,EAAoC,KAAK,CAAC;IAAA,EAAC;IACpDjB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,GACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAC,kEAAA,gBACqF,IAAAC,kEAAA,gBACvB;IAEtErB,EADI,CAAAsB,YAAA,EAAM,EACL;;;;;;IARDtB,EAAA,CAAAuB,SAAA,EAA6B;IAA7BvB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAGzBhB,EAAA,CAAAuB,SAAA,GACA;IADAvB,EAAA,CAAAwB,kBAAA,MAAAf,MAAA,CAAAgB,MAAA,MACA;IAAIzB,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,YAAA,KAAAjB,MAAA,CAAAO,KAAA,CAAgC;IAEhChB,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,YAAA,KAAAjB,MAAA,CAAAO,KAAA,CAAgC;;;;;;IAlBhDhB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAsB,YAAA,EAAK;IACLtB,EAAA,CAAAM,cAAA,aAAoE;IAAlDN,EAAA,CAAAO,UAAA,mBAAAoB,oEAAA;MAAA3B,EAAA,CAAAU,aAAA,CAAAkB,GAAA;MAAA,MAAAzB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,MAAM,EAAAZ,MAAA,CAAAc,YAAA,EAAgB,KAAK,CAAC;IAAA,EAAC;IAC/DjB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAkB,MAAA,aACA;IAEAlB,EAFA,CAAAmB,UAAA,IAAAU,mDAAA,gBACqF,IAAAC,mDAAA,gBAC1B;IAEnE9B,EADI,CAAAsB,YAAA,EAAM,EACL;IACLtB,EAAA,CAAAmB,UAAA,IAAAY,8DAAA,2BAAqD;IAWzD/B,EAAA,CAAAsB,YAAA,EAAK;;;;IAhBWtB,EAAA,CAAAuB,SAAA,GAA6B;IAA7BvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,YAAA,YAA6B;IAE7B1B,EAAA,CAAAuB,SAAA,EAA6B;IAA7BvB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuB,YAAA,YAA6B;IAGX1B,EAAA,CAAAuB,SAAA,EAAqB;IAArBvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6B,kBAAA,CAAqB;;;;;;IAsB3ChC,EAAA,CAAAM,cAAA,iBAEmC;IAA/BN,EAAA,CAAAO,UAAA,mBAAA0B,iFAAA;MAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA;MAAA,MAAAC,eAAA,GAAAnC,EAAA,CAAAa,aAAA,GAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAiC,MAAA,CAAAD,eAAA,CAAoB;IAAA,EAAC;IAC9BnC,EAAA,CAAAC,SAAA,eAGU;IACdD,EAAA,CAAAsB,YAAA,EAAS;;;;IAJYtB,EAAA,CAAAuB,SAAA,EAG3C;IAH2CvB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAH,eAAA,CAAAI,QAAA,GAAAJ,eAAA,CAAAI,QAAA,EAG3C;;;;;IAe0BvC,EAAA,CAAAK,uBAAA,GAAoF;IAChFL,EAAA,CAAAkB,MAAA,GAEJ;;;;;IAFIlB,EAAA,CAAAuB,SAAA,EAEJ;IAFIvB,EAAA,CAAAwB,kBAAA,OAAAW,eAAA,kBAAAA,eAAA,CAAAK,qBAAA,kBAAAL,eAAA,CAAAK,qBAAA,qBAAAL,eAAA,CAAAK,qBAAA,IAAAC,gBAAA,kBAAAN,eAAA,CAAAK,qBAAA,IAAAC,gBAAA,CAAAC,YAAA,cAEJ;;;;;IAEA1C,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAW,eAAA,kBAAAA,eAAA,CAAAQ,0BAAA,kBAAAR,eAAA,CAAAQ,0BAAA,CAAAC,IAAA,cACJ;;;;;IAEA5C,EAAA,CAAAK,uBAAA,GAAuD;IACnDL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAW,eAAA,kBAAAA,eAAA,CAAAU,sBAAA,cACJ;;;;;IAEA7C,EAAA,CAAAK,uBAAA,GAA8D;IAC1DL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAW,eAAA,kBAAAA,eAAA,CAAAW,6BAAA,cACJ;;;;;IAEA9C,EAAA,CAAAK,uBAAA,GAA6D;IACzDL,EAAA,CAAAC,SAAA,qBACqG;;;;;IADzFD,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAiC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAC,4BAAA,CAC0C;;;;;IAE3FhD,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAC,SAAA,qBACwF;;;;;IAD5ED,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAiC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAE,eAAA,CAC6B;;;;;IAG9EjD,EAAA,CAAAK,uBAAA,GAAyD;IACrDL,EAAA,CAAAC,SAAA,qBACiG;;;;;IADrFD,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAiC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAG,wBAAA,CACsC;;;;;IA/BnGlD,EAAA,CAAAK,uBAAA,GAAqD;IACjDL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IA2BjCL,EA1BA,CAAAmB,UAAA,IAAAgC,6EAAA,2BAAoF,IAAAC,6EAAA,2BAKpB,IAAAC,6EAAA,2BAIT,IAAAC,6EAAA,2BAIO,IAAAC,6EAAA,2BAID,IAAAC,6EAAA,2BAIb,IAAAC,6EAAA,2BAKS;;IAMjEzD,EAAA,CAAAsB,YAAA,EAAK;;;;;IAjCatB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,aAAAwD,MAAA,CAAA1C,KAAA,CAAsB;IACjBhB,EAAA,CAAAuB,SAAA,EAAmE;IAAnEvB,EAAA,CAAAE,UAAA,uEAAmE;IAKnEF,EAAA,CAAAuB,SAAA,EAA+C;IAA/CvB,EAAA,CAAAE,UAAA,mDAA+C;IAI/CF,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAE,UAAA,0CAAsC;IAItCF,EAAA,CAAAuB,SAAA,EAA6C;IAA7CvB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAuB,SAAA,EAA4C;IAA5CvB,EAAA,CAAAE,UAAA,gDAA4C;IAI5CF,EAAA,CAAAuB,SAAA,EAA+B;IAA/BvB,EAAA,CAAAE,UAAA,mCAA+B;IAK/BF,EAAA,CAAAuB,SAAA,EAAwC;IAAxCvB,EAAA,CAAAE,UAAA,4CAAwC;;;;;;IAqBvDF,EAAA,CAAAM,cAAA,iBAE2B;IAAvBN,EAAA,CAAAO,UAAA,mBAAAoD,+FAAA;MAAA3D,EAAA,CAAAU,aAAA,CAAAkD,IAAA;MAAA,MAAAC,QAAA,GAAA7D,EAAA,CAAAa,aAAA,GAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAiC,MAAA,CAAAyB,QAAA,CAAY;IAAA,EAAC;IACtB7D,EAAA,CAAAC,SAAA,eAGiB;IACrBD,EAAA,CAAAsB,YAAA,EAAS;;;;IAJYtB,EAAA,CAAAuB,SAAA,EAGnC;IAHmCvB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAuB,QAAA,CAAAtB,QAAA,GAAAsB,QAAA,CAAAtB,QAAA,EAGnC;;;;;IAekBvC,EAAA,CAAAK,uBAAA,GAAoF;IAChFL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAqC,QAAA,CAAArB,qBAAA,kBAAAqB,QAAA,CAAArB,qBAAA,qBAAAqB,QAAA,CAAArB,qBAAA,IAAAC,gBAAA,kBAAAoB,QAAA,CAAArB,qBAAA,IAAAC,gBAAA,CAAAC,YAAA,cACJ;;;;;IAEA1C,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,OAAAqC,QAAA,CAAAlB,0BAAA,kBAAAkB,QAAA,CAAAlB,0BAAA,CAAAC,IAAA,cACJ;;;;;IAEA5C,EAAA,CAAAK,uBAAA,GAAuD;IACnDL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAqC,QAAA,CAAAhB,sBAAA,aACJ;;;;;IAEA7C,EAAA,CAAAK,uBAAA,GAA8D;IAC1DL,EAAA,CAAAkB,MAAA,GACJ;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAqC,QAAA,CAAAf,6BAAA,aACJ;;;;;IAEA9C,EAAA,CAAAK,uBAAA,GAA6D;IACzDL,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA2D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAC,4BAAA,CACiC;;;;;IAIlFhD,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA2D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAE,eAAA,CACoB;;;;;IAIrEjD,EAAA,CAAAK,uBAAA,GAAyD;IACrDL,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAuB,SAAA,EAAe;IACvBvB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA2D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAG,wBAAA,CAC6B;;;;;IAI9ElD,EAAA,CAAAK,uBAAA,GAA+B;IAC3BL,EAAA,CAAAkB,MAAA,GACJ;;;;;;IADIlB,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAqC,QAAA,CAAAC,OAAA,CAAA9C,KAAA,cACJ;;;;;IAxCZhB,EAAA,CAAAK,uBAAA,GAAqD;IACjDL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAoCjCL,EAlCA,CAAAmB,UAAA,IAAA4C,2FAAA,2BAAoF,IAAAC,2FAAA,2BAIpB,IAAAC,2FAAA,2BAIT,IAAAC,2FAAA,2BAIO,IAAAC,2FAAA,2BAID,IAAAC,2FAAA,2BAMb,IAAAC,2FAAA,2BAMS,KAAAC,4FAAA,2BAM1B;;IAKvCtE,EAAA,CAAAsB,YAAA,EAAK;;;;;IAzCatB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,aAAA4D,OAAA,CAAA9C,KAAA,CAAsB;IAEjBhB,EAAA,CAAAuB,SAAA,EAAmE;IAAnEvB,EAAA,CAAAE,UAAA,uEAAmE;IAInEF,EAAA,CAAAuB,SAAA,EAA+C;IAA/CvB,EAAA,CAAAE,UAAA,mDAA+C;IAI/CF,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAE,UAAA,0CAAsC;IAItCF,EAAA,CAAAuB,SAAA,EAA6C;IAA7CvB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAuB,SAAA,EAA4C;IAA5CvB,EAAA,CAAAE,UAAA,gDAA4C;IAM5CF,EAAA,CAAAuB,SAAA,EAA+B;IAA/BvB,EAAA,CAAAE,UAAA,mCAA+B;IAM/BF,EAAA,CAAAuB,SAAA,EAAwC;IAAxCvB,EAAA,CAAAE,UAAA,4CAAwC;;;;;IAmB3DF,EAAA,CAAAuE,kBAAA,GACiG;;;;;IAFrGvE,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAmB,UAAA,IAAAqD,yHAAA,2BACkF;;;;;;;;IAA7ExE,EAAA,CAAAuB,SAAA,EAA0B;IAAAvB,EAA1B,CAAAE,UAAA,qBAAAuE,UAAA,CAA0B,4BAAAzE,EAAA,CAAAqC,eAAA,IAAAqC,GAAA,EAAAC,SAAA,EAAAC,UAAA,OAAiD;;;;;IAHxF5E,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAmB,UAAA,IAAA0D,0GAAA,2BAA8C;;;;;IAAd7E,EAAA,CAAAuB,SAAA,EAAY;IAAZvB,EAAA,CAAAE,UAAA,YAAA4E,QAAA,CAAAC,IAAA,CAAY;;;;;IAFpD/E,EAAA,CAAAK,uBAAA,GAAoC;IAChCL,EAAA,CAAAmB,UAAA,IAAA6D,2FAAA,2BAAgD;;;;;IAAjBhF,EAAA,CAAAuB,SAAA,EAAe;IAAfvB,EAAA,CAAAE,UAAA,YAAA2D,QAAA,CAAAoB,OAAA,CAAe;;;;;IAzE9CjF,EAHJ,CAAAM,cAAA,aAAgD,aAGM;IAC9CN,EAAA,CAAAC,SAAA,0BAAkD;IACtDD,EAAA,CAAAsB,YAAA,EAAK;IAIDtB,EADJ,CAAAM,cAAA,aAA+C,cACqC;IAC5EN,EAAA,CAAAmB,UAAA,IAAA+D,sEAAA,qBAE2B;IAO3BlF,EAAA,CAAAM,cAAA,YACkD;IAC9CN,EAAA,CAAAkB,MAAA,GACJ;IAERlB,EAFQ,CAAAsB,YAAA,EAAI,EACF,EACL;IAGLtB,EAAA,CAAAmB,UAAA,IAAAgE,4EAAA,4BAAqD;IA6CzDnF,EAAA,CAAAsB,YAAA,EAAK;IAGLtB,EAAA,CAAAmB,UAAA,IAAAiE,4EAAA,2BAAoC;;;;;;IAvEXpF,EAAA,CAAAuB,SAAA,GAAc;IAAdvB,EAAA,CAAAE,UAAA,UAAA2D,QAAA,CAAc;IAKiB7D,EAAA,CAAAuB,SAAA,GAA+B;IAA/BvB,EAAA,CAAAqF,WAAA,gBAAAT,UAAA,OAA+B;IAClE5E,EAAA,CAAAuB,SAAA,EAA6C;IAA7CvB,EAAA,CAAAE,UAAA,SAAA2D,QAAA,CAAAyB,0BAAA,kBAAAzB,QAAA,CAAAyB,0BAAA,CAAAC,MAAA,CAA6C;IASnDvF,EAAA,CAAAuB,SAAA,EAAmE;IAAnEvB,EAAA,CAAAE,UAAA,wCAAA2D,QAAA,CAAAhB,sBAAA,CAAmE;IAElE7C,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAqC,QAAA,CAAAjB,IAAA,aACJ;IAKsB5C,EAAA,CAAAuB,SAAA,EAAqB;IAArBvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6B,kBAAA,CAAqB;IAgDxChC,EAAA,CAAAuB,SAAA,EAAmB;IAAnBvB,EAAA,CAAAE,UAAA,SAAA2D,QAAA,CAAAtB,QAAA,CAAmB;;;;;IAc1BvC,EAAA,CAAAuE,kBAAA,GACwF;;;;;IAF5FvE,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAmB,UAAA,IAAAqE,4GAAA,2BACyE;;;;;;;IAApExF,EAAA,CAAAuB,SAAA,EAA0B;IAAAvB,EAA1B,CAAAE,UAAA,qBAAAuE,UAAA,CAA0B,4BAAAzE,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAC,SAAA,EAAwC;;;;;IAH/E3F,EAAA,CAAAK,uBAAA,GAAwD;IACpDL,EAAA,CAAAmB,UAAA,IAAAyE,6FAAA,2BAA8C;;;;;IAAd5F,EAAA,CAAAuB,SAAA,EAAY;IAAZvB,EAAA,CAAAE,UAAA,YAAA2F,QAAA,CAAAd,IAAA,CAAY;;;;;IAFpD/E,EAAA,CAAAK,uBAAA,GAA4C;IACxCL,EAAA,CAAAmB,UAAA,IAAA2E,8EAAA,2BAAwD;;;;;IAAzB9F,EAAA,CAAAuB,SAAA,EAAuB;IAAvBvB,EAAA,CAAAE,UAAA,YAAAiC,eAAA,CAAA8C,OAAA,CAAuB;;;;;IAvJtDjF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAA0C;IAC9CD,EAAA,CAAAsB,YAAA,EAAK;IAGDtB,EADJ,CAAAM,cAAA,aAA+C,cACA;IACvCN,EAAA,CAAAmB,UAAA,IAAA4E,wDAAA,qBAEmC;IAQnC/F,EAAA,CAAAM,cAAA,YACkD;IAC9CN,EAAA,CAAAkB,MAAA,GACJ;IAERlB,EAFQ,CAAAsB,YAAA,EAAI,EACF,EACL;IAGLtB,EAAA,CAAAmB,UAAA,IAAA6E,8DAAA,4BAAqD;IAqCzDhG,EAAA,CAAAsB,YAAA,EAAK;IAyFLtB,EAvFA,CAAAmB,UAAA,IAAA8E,6DAAA,iCAAAjG,EAAA,CAAAkG,sBAAA,CAAkD,KAAAC,+DAAA,2BAuFN;;;;;IArJnBnG,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAE,UAAA,UAAAiC,eAAA,CAAsB;IAK1BnC,EAAA,CAAAuB,SAAA,GAA0D;IAA1DvB,EAAA,CAAAE,UAAA,UAAAiC,eAAA,kBAAAA,eAAA,CAAAmD,0BAAA,kBAAAnD,eAAA,CAAAmD,0BAAA,CAAAC,MAAA,MAA0D;IAUhEvF,EAAA,CAAAuB,SAAA,EAA2E;IAA3EvB,EAAA,CAAAE,UAAA,wCAAAiC,eAAA,CAAAU,sBAAA,CAA2E;IAE1E7C,EAAA,CAAAuB,SAAA,EACJ;IADIvB,EAAA,CAAAwB,kBAAA,MAAAW,eAAA,CAAAS,IAAA,aACJ;IAKsB5C,EAAA,CAAAuB,SAAA,EAAqB;IAArBvB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6B,kBAAA,CAAqB;IA8HxChC,EAAA,CAAAuB,SAAA,GAA2B;IAA3BvB,EAAA,CAAAE,UAAA,SAAAiC,eAAA,CAAAI,QAAA,CAA2B;;;;;IActCvC,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAkB,MAAA,6BAAsB;IACxElB,EADwE,CAAAsB,YAAA,EAAK,EACxE;;;;;IAIDtB,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAkB,MAAA,8CAAuC;IACzFlB,EADyF,CAAAsB,YAAA,EAAK,EACzF;;;ADpNrB,OAAM,MAAO8E,uBAAuB;EAYlCC,YACUC,MAAc,EACdC,qBAA4C;IAD5C,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IAbvB,KAAAC,YAAY,GAAG,IAAI3G,OAAO,EAAQ;IAInC,KAAAoB,YAAY,GAAU,EAAE;IACxB,KAAAwF,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAOjC,KAAAC,mBAAmB,GAAgB,EAAE;IAEtC,KAAAC,OAAO,GAAgB,CAC5B;MACE/F,KAAK,EAAE,qDAAqD;MAC5DS,MAAM,EAAE;KACT,EACD;MAAET,KAAK,EAAE,iCAAiC;MAAES,MAAM,EAAE;IAAkB,CAAE,EACxE;MAAET,KAAK,EAAE,wBAAwB;MAAES,MAAM,EAAE;IAAI,CAAE,EACjD;MAAET,KAAK,EAAE,+BAA+B;MAAES,MAAM,EAAE;IAAgB,CAAE,EACpE;MAAET,KAAK,EAAE,8BAA8B;MAAES,MAAM,EAAE;IAAoB,CAAE,EACvE;MAAET,KAAK,EAAE,iBAAiB;MAAES,MAAM,EAAE;IAAO,CAAE,EAC7C;MAAET,KAAK,EAAE,0BAA0B;MAAES,MAAM,EAAE;IAAgB,CAAE,CAChE;IAED,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAtB,YAAY,GAAW,CAAC;IA2JxB,KAAA4G,WAAW,GAAG,CAACC,CAAS,EAAEC,CAAM,KAAKA,CAAC,EAAErE,sBAAsB,IAAIoE,CAAC;EA7KhE;EAoBHE,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,cAAc;MAAEC,UAAU,EAAE,CAAC,qBAAqB;IAAC,CAAE,CAC/D;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACR,mBAAmB,GAAG,IAAI,CAACC,OAAO;IACvC,IAAI,CAACU,mBAAmB,EAAE;EAC5B;EAEA,IAAIzF,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAAC8E,mBAAmB;EACjC;EAEA,IAAI9E,kBAAkBA,CAAC0F,GAAU;IAC/B,IAAI,CAACZ,mBAAmB,GAAG,IAAI,CAACC,OAAO,CAACY,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EAC5E;EAEAE,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,UAAU,GAAG,IAAI,CAACjB,OAAO,CAACgB,KAAK,CAACE,SAAS,CAAC;IAChD,IAAI,CAAClB,OAAO,CAACmB,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACvC,IAAI,CAAClB,OAAO,CAACmB,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACrD;EAEAjH,UAAUA,CAACC,KAAa,EAAE+D,IAAW,EAAEqD,IAAW;IAChD,IAAIA,IAAI,KAAK,KAAK,EAAE;MAClB,IAAI,IAAI,CAAC1G,YAAY,KAAKV,KAAK,EAAE;QAC/B;QACA,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACtD,CAAC,MAAM;QACL;QACA,IAAI,CAACsB,YAAY,GAAGV,KAAK;QACzB,IAAI,CAACZ,YAAY,GAAG,CAAC;MACvB;IACF;IAEA2E,IAAI,CAACsD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEtH,KAAK,CAAC;MAC9C,MAAM0H,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEvH,KAAK,CAAC;MAE9C,IAAI2H,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAACtI,YAAY,GAAGuI,MAAM;IACnC,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAAC1D,IAAS,EAAE/D,KAAa;IACvC,IAAI,CAAC+D,IAAI,IAAI,CAAC/D,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAAC6H,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAO9D,IAAI,CAAC/D,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAI8H,MAAM,GAAG9H,KAAK,CAAC+H,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIC,KAAK,GAAGjE,IAAI;MAChB,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACvD,MAAM,EAAE0D,CAAC,EAAE,EAAE;QACtC,IAAID,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOD,KAAK;IACd;EACF;EAEAvB,mBAAmBA,CAAA;IACjB,IAAI,CAACZ,aAAa,GAAG,CACnB;MAAEQ,KAAK,EAAE,UAAU;MAAE2B,KAAK,EAAE,UAAU;MAAEE,QAAQ,EAAE;IAAK,CAAE,EACzD;MACE7B,KAAK,EAAE,aAAa;MACpB2B,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,EAAE,IAAI,CAACtC,qBAAqB,EAAErB,MAAM,GAAG,CAAC;KACnD,CACF;EACH;EAEA4D,gBAAgBA,CAACpB,KAAU;IACzB,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,MAAM0C,IAAI,GAAGrB,KAAK,CAACsB,KAAK,GAAGtB,KAAK,CAACuB,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGxB,KAAK,CAACuB,IAAI;IAC3B,MAAME,SAAS,GAAGzB,KAAK,CAACyB,SAAS;IACjC,MAAMC,SAAS,GAAG1B,KAAK,CAAC0B,SAAS;IAEjC,IAAI,CAAClD,qBAAqB,CACvBmD,eAAe,CACdN,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAAC9C,gBAAgB,CACtB,CACAgD,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC5I,YAAY,GAAG4I,QAAQ,EAAE9E,IAAI,IAAI,EAAE;QACxC,IAAI,CAAC0B,YAAY,GAAGoD,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAACtD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDuD,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAyD,MAAMA,CAACpC,KAA0B;IAC/B,IAAI,IAAI,CAACnB,qBAAqB,EAAErB,MAAM,IAAIwC,KAAK,CAACiB,KAAK,KAAK,UAAU,EAAE;MACpE,MAAMoB,QAAQ,GAAG,IAAI,CAACxD,qBAAqB,CAAC,CAAC,CAAC;MAC9C,MAAMyD,YAAY,GAAGD,QAAQ,EAAEvH,sBAAsB;MAErD,IAAI,CAACyD,MAAM,CAACgE,QAAQ,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACtDC,KAAK,EAAE;UAAEF;QAAY;OACtB,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC/D,MAAM,CAACgE,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;IACtD;EACF;EAEAlI,MAAMA,CAACnB,YAAiB;IACtBA,YAAY,CAACsB,QAAQ,GAAG,CAACtB,YAAY,CAACsB,QAAQ;IAC9C,IAAI,CAACtB,YAAY,CAACsB,QAAQ,EAAE;MAC1B;IACF;IAEA,MAAMiI,QAAQ,GACZvJ,YAAY,EAAEqE,0BAA0B,EACpCvF,GAAG,CAAE0K,CAAM,IAAKA,CAAC,EAAE5H,sBAAsB,CAAC,EAC1C8E,MAAM,CAAE+C,EAAsB,IAAmB,CAAC,CAACA,EAAE,CAAC,IAAI,EAAE;IAElE,IAAIF,QAAQ,CAACjF,MAAM,KAAK,CAAC,EAAE;MACzBtE,YAAY,CAACsB,QAAQ,GAAG,KAAK;MAC7B;IACF;IAEAzC,QAAQ,CACN0K,QAAQ,CAACzK,GAAG,CAAE2K,EAAE,IACd,IAAI,CAACnE,qBAAqB,CAACoE,wBAAwB,CAACD,EAAE,CAAC,CACxD,CACF,CACEE,IAAI,CAAC7K,GAAG,CAAE8K,QAAQ,IAAKA,QAAQ,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7DpB,SAAS,CAAC;MACTC,IAAI,EAAGoB,KAAY,IAAI;QACrB/J,YAAY,CAACgE,OAAO,GAAG+F,KAAK;MAC9B,CAAC;MACDf,KAAK,EAAGgB,GAAG,IAAI;QACbf,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEgB,GAAG,CAAC;QACjDhK,YAAY,CAACsB,QAAQ,GAAG,KAAK;MAC/B;KACD,CAAC;EACN;EAIA2I,cAAcA,CAACC,KAAY,EAAEpD,KAAY;IACvC,IAAI,CAACoB,gBAAgB,CAAC;MAAEE,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEA8B,MAAMA,CAAA;IACJ,IAAI,CAAC9E,MAAM,CAACgE,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;EACtD;EAEAe,WAAWA,CAAA;IACT,IAAI,CAAC7E,YAAY,CAACoD,IAAI,EAAE;IACxB,IAAI,CAACpD,YAAY,CAAC8E,QAAQ,EAAE;EAC9B;;;uBAzMWlF,uBAAuB,EAAApG,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAG,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAvBvF,uBAAuB;MAAAwF,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCvB5B/L,EAFR,CAAAM,cAAA,aAA8D,aACmB,aAC7C;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAsB,YAAA,EAAM;UAKMtB,EAJZ,CAAAM,cAAA,aAA2C,aAEb,cACW,mBAG8E;UAF9CN,EAAA,CAAAiM,gBAAA,2BAAAC,gEAAAC,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAApM,EAAA,CAAAqM,kBAAA,CAAAL,GAAA,CAAArF,gBAAA,EAAAwF,MAAA,MAAAH,GAAA,CAAArF,gBAAA,GAAAwF,MAAA;YAAA,OAAAnM,EAAA,CAAAc,WAAA,CAAAqL,MAAA;UAAA,EAA8B;UACvFnM,EAAA,CAAAO,UAAA,mBAAA+L,wDAAAH,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAA,MAAAG,MAAA,GAAAvM,EAAA,CAAAwM,WAAA;YAAA,OAAAxM,EAAA,CAAAc,WAAA,CAASkL,GAAA,CAAAd,cAAA,CAAAqB,MAAA,EAAAJ,MAAA,CAA2B;UAAA,EAAC;UADzCnM,EAAA,CAAAsB,YAAA,EAE2G;UAC3GtB,EAAA,CAAAC,SAAA,YAAiD;UAEzDD,EADI,CAAAsB,YAAA,EAAO,EACL;UAKNtB,EAAA,CAAAM,cAAA,sBAEuG;UADnGN,EAAA,CAAAO,UAAA,sBAAAkM,iEAAAN,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAA,OAAApM,EAAA,CAAAc,WAAA,CAAYkL,GAAA,CAAA7B,MAAA,CAAAgC,MAAA,CAAc;UAAA,EAAC;UAE/BnM,EAAA,CAAAsB,YAAA,EAAa;UAEbtB,EAAA,CAAAM,cAAA,yBAE+I;UAF5GN,EAAA,CAAAiM,gBAAA,2BAAAS,yEAAAP,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAApM,EAAA,CAAAqM,kBAAA,CAAAL,GAAA,CAAAhK,kBAAA,EAAAmK,MAAA,MAAAH,GAAA,CAAAhK,kBAAA,GAAAmK,MAAA;YAAA,OAAAnM,EAAA,CAAAc,WAAA,CAAAqL,MAAA;UAAA,EAAgC;UAK3EnM,EAFQ,CAAAsB,YAAA,EAAgB,EACd,EACJ;UAGFtB,EADJ,CAAAM,cAAA,eAAuB,sBAKU;UADzBN,EAH0D,CAAAO,UAAA,wBAAAoM,gEAAAR,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAA,OAAApM,EAAA,CAAAc,WAAA,CAAckL,GAAA,CAAA7C,gBAAA,CAAAgD,MAAA,CAAwB;UAAA,EAAC,0BAAAS,kEAAAT,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAA,OAAApM,EAAA,CAAAc,WAAA,CAGjFkL,GAAA,CAAAlE,kBAAA,CAAAqE,MAAA,CAA0B;UAAA,EAAC;UAACnM,EAAA,CAAAiM,gBAAA,6BAAAY,qEAAAV,MAAA;YAAAnM,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAApM,EAAA,CAAAqM,kBAAA,CAAAL,GAAA,CAAApF,qBAAA,EAAAuF,MAAA,MAAAH,GAAA,CAAApF,qBAAA,GAAAuF,MAAA;YAAA,OAAAnM,EAAA,CAAAc,WAAA,CAAAqL,MAAA;UAAA,EAAqC;UAACnM,EAAA,CAAAO,UAAA,6BAAAsM,qEAAA;YAAA7M,EAAA,CAAAU,aAAA,CAAA0L,GAAA;YAAA,OAAApM,EAAA,CAAAc,WAAA,CAAmBkL,GAAA,CAAAvE,mBAAA,EAAqB;UAAA,EAAC;UAuM3HzH,EApMA,CAAAmB,UAAA,KAAA2L,+CAAA,0BAAgC,KAAAC,+CAAA,2BA2Be,KAAAC,+CAAA,0BAoKT,KAAAC,+CAAA,0BAKD;UAOjDjN,EAFQ,CAAAsB,YAAA,EAAU,EACR,EACJ;;;UA9OoBtB,EAAA,CAAAuB,SAAA,GAAyB;UAAevB,EAAxC,CAAAE,UAAA,UAAA8L,GAAA,CAAA5E,eAAA,CAAyB,SAAA4E,GAAA,CAAAzE,IAAA,CAAc,uCAAuC;UAMvBvH,EAAA,CAAAuB,SAAA,GAA8B;UAA9BvB,EAAA,CAAAkN,gBAAA,YAAAlB,GAAA,CAAArF,gBAAA,CAA8B;UAUvF3G,EAAA,CAAAuB,SAAA,GAAyB;UAEjCvB,EAFQ,CAAAE,UAAA,YAAA8L,GAAA,CAAAnF,aAAA,CAAyB,mGAEiE;UAGvF7G,EAAA,CAAAuB,SAAA,EAAmB;UAAnBvB,EAAA,CAAAE,UAAA,YAAA8L,GAAA,CAAAjF,OAAA,CAAmB;UAAC/G,EAAA,CAAAkN,gBAAA,YAAAlB,GAAA,CAAAhK,kBAAA,CAAgC;UAE/DhC,EAAA,CAAAE,UAAA,2IAA0I;UAMpIF,EAAA,CAAAuB,SAAA,GAAsB;UAEkBvB,EAFxC,CAAAE,UAAA,UAAA8L,GAAA,CAAA/K,YAAA,CAAsB,YAAyB,mBACnB,YAAA+K,GAAA,CAAAtF,OAAA,CAAoB,mBAAmB,cAAc,iBAAAsF,GAAA,CAAAvF,YAAA,CAC9D,oBAAoB,4BAA4B;UACjCzG,EAAA,CAAAkN,gBAAA,cAAAlB,GAAA,CAAApF,qBAAA,CAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}