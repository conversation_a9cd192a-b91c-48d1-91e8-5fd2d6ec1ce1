{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ServiceTicketService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAll(query) {\n    return this.http.get(`${CMS_APIContstant.TICKET}?${query}`);\n  }\n  getById(id) {\n    return this.http.get(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);\n  }\n  createTicket(data) {\n    return this.http.post(`${CMS_APIContstant.TICKET}`, data);\n  }\n  updateTicket(id, data) {\n    return this.http.put(`${CMS_APIContstant.TICKET}/${id}`, data);\n  }\n  getAllTicketStatus() {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');\n  }\n  static {\n    this.ɵfac = function ServiceTicketService_Factory(t) {\n      return new (t || ServiceTicketService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceTicketService,\n      factory: ServiceTicketService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CMS_APIContstant", "ServiceTicketService", "constructor", "http", "getAll", "query", "get", "TICKET", "getById", "id", "createTicket", "data", "post", "updateTicket", "put", "getAllTicketStatus", "CONFIG_DATA", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\service-ticket.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ServiceTicketService {\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(query: string) {\r\n    return this.http.get<any>(`${CMS_APIContstant.TICKET}?${query}`);\r\n  }\r\n\r\n  getById(id: string) {\r\n    return this.http.get<any>(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);\r\n  }\r\n\r\n  createTicket(data: any) {\r\n    return this.http.post<any>(`${CMS_APIContstant.TICKET}`, data);\r\n  }\r\n\r\n  updateTicket(id: string, data: any) {\r\n    return this.http.put<any>(`${CMS_APIContstant.TICKET}/${id}`, data);\r\n  }\r\n\r\n  getAllTicketStatus() {\r\n    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAE/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,MAAMA,CAACC,KAAa;IAClB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAM,GAAGN,gBAAgB,CAACO,MAAM,IAAIF,KAAK,EAAE,CAAC;EAClE;EAEAG,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACN,IAAI,CAACG,GAAG,CAAM,GAAGN,gBAAgB,CAACO,MAAM,gBAAgBE,EAAE,EAAE,CAAC;EAC3E;EAEAC,YAAYA,CAACC,IAAS;IACpB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAM,GAAGZ,gBAAgB,CAACO,MAAM,EAAE,EAAEI,IAAI,CAAC;EAChE;EAEAE,YAAYA,CAACJ,EAAU,EAAEE,IAAS;IAChC,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAM,GAAGd,gBAAgB,CAACO,MAAM,IAAIE,EAAE,EAAE,EAAEE,IAAI,CAAC;EACrE;EAEAI,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACZ,IAAI,CAACG,GAAG,CAAMN,gBAAgB,CAACgB,WAAW,GAAG,mCAAmC,CAAC;EAC/F;;;uBAtBWf,oBAAoB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBnB,oBAAoB;MAAAoB,OAAA,EAApBpB,oBAAoB,CAAAqB,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}