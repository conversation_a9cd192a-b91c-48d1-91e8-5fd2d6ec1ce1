{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ContactsComponent } from './contacts.component';\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\nimport { AddContactComponent } from './add-contact/add-contact.component';\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ContactsComponent\n}, {\n  path: 'create',\n  component: AddContactComponent\n}, {\n  path: ':id',\n  component: ContactsDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ContactsOverviewComponent\n  }, {\n    path: 'opportunities',\n    component: ContactsOpportunitiesComponent\n  }, {\n    path: 'attachments',\n    component: ContactsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ContactsNotesComponent\n  }, {\n    path: 'activities',\n    component: ContactsActivitiesComponent\n  }, {\n    path: 'activities/detail/:id',\n    component: ActivitiesItemDetailComponent\n  }, {\n    path: 'relationships',\n    component: ContactsRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: ContactsTicketsComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class ContactsRoutingModule {\n  static {\n    this.ɵfac = function ContactsRoutingModule_Factory(t) {\n      return new (t || ContactsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ContactsComponent", "ContactsDetailsComponent", "ContactsOverviewComponent", "ContactsOpportunitiesComponent", "ContactsAttachmentsComponent", "ContactsNotesComponent", "ContactsActivitiesComponent", "ContactsRelationshipsComponent", "ContactsTicketsComponent", "AddContactComponent", "ActivitiesItemDetailComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ContactsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ContactsComponent } from './contacts.component';\r\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\r\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\r\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\r\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\r\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\r\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\r\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\r\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\r\nimport { AddContactComponent } from './add-contact/add-contact.component';\r\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ContactsComponent },\r\n  { path: 'create', component: AddContactComponent },\r\n  {\r\n    path: ':id',\r\n    component: ContactsDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: ContactsOverviewComponent },\r\n      { path: 'opportunities', component: ContactsOpportunitiesComponent },\r\n      { path: 'attachments', component: ContactsAttachmentsComponent },\r\n      { path: 'notes', component: ContactsNotesComponent },\r\n      { path: 'activities', component: ContactsActivitiesComponent },\r\n      {\r\n        path: 'activities/detail/:id',\r\n        component: ActivitiesItemDetailComponent,\r\n      },\r\n      { path: 'relationships', component: ContactsRelationshipsComponent },\r\n      { path: 'tickets', component: ContactsTicketsComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ContactsRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,2BAA2B,QAAQ,sEAAsE;AAClH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,6BAA6B,QAAQ,wEAAwE;;;AAEtH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEb;AAAiB,CAAE,EAC1C;EAAEY,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEJ;AAAmB,CAAE,EAClD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEZ,wBAAwB;EACnCa,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEX;EAAyB,CAAE,EAC1D;IAAEU,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEV;EAA8B,CAAE,EACpE;IAAES,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAET;EAA4B,CAAE,EAChE;IAAEQ,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAsB,CAAE,EACpD;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA2B,CAAE,EAC9D;IACEM,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEH;GACZ,EACD;IAAEE,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEN;EAA8B,CAAE,EACpE;IAAEK,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEL;EAAwB,CAAE,EACxD;IAAEI,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBlB,YAAY,CAACmB,QAAQ,CAACP,MAAM,CAAC,EAC7BZ,YAAY;IAAA;EAAA;;;2EAEXkB,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFtBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}