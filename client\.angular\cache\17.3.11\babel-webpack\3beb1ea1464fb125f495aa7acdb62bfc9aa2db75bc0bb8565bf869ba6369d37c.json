{"ast": null, "code": "import { forkJoin, Subject, take, takeUntil } from 'rxjs';\nimport * as moment from 'moment';\nimport { ApiConstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/progressspinner\";\nfunction AccountInvoicesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountInvoicesComponent_div_4_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.emailToSend, $event) || (ctx_r1.emailToSend = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AccountInvoicesComponent_div_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendToEmail());\n    });\n    i0.ɵɵtext(3, \"Send to Email\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.emailToSend);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isEmailValid);\n  }\n}\nfunction AccountInvoicesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountInvoicesComponent_p_table_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 16);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 17);\n    i0.ɵɵtext(4, \"Billing Doc # \");\n    i0.ɵɵelement(5, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 19);\n    i0.ɵɵtext(9, \"PO # \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"Total Amount \");\n    i0.ɵɵelement(13, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Open Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 21);\n    i0.ɵɵtext(17, \"Billing Date \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 23);\n    i0.ɵɵtext(20, \"Due Date \");\n    i0.ɵɵelement(21, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 25);\n    i0.ɵɵtext(23, \"Days Past Due \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 27);\n    i0.ɵɵtext(26, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountInvoicesComponent_p_table_7_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 27)(21, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AccountInvoicesComponent_p_table_7_ng_template_3_Template_button_click_21_listener() {\n      const invoice_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadPDF(invoice_r5.INVOICE));\n    });\n    i0.ɵɵtext(22, \"View Details/PDF\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invoice_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", invoice_r5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", invoice_r5.INVOICE, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(invoice_r5.PURCH_NO);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 5, invoice_r5.AMOUNT, invoice_r5.CURRENCY), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatDate(invoice_r5.DOC_DATE), \" \");\n  }\n}\nfunction AccountInvoicesComponent_p_table_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 13, 0);\n    i0.ɵɵlistener(\"sortFunction\", function AccountInvoicesComponent_p_table_7_Template_p_table_sortFunction_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort($event));\n    });\n    i0.ɵɵtwoWayListener(\"selectionChange\", function AccountInvoicesComponent_p_table_7_Template_p_table_selectionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedInvoices, $event) || (ctx_r1.selectedInvoices = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, AccountInvoicesComponent_p_table_7_ng_template_2_Template, 27, 0, \"ng-template\", 14)(3, AccountInvoicesComponent_p_table_7_ng_template_3_Template, 23, 8, \"ng-template\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.invoices)(\"rows\", 10)(\"rowHover\", true)(\"loading\", ctx_r1.loading)(\"paginator\", true)(\"customSort\", true);\n    i0.ɵɵtwoWayProperty(\"selection\", ctx_r1.selectedInvoices);\n  }\n}\nfunction AccountInvoicesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(\"No records found.\");\n  }\n}\nexport class AccountInvoicesComponent {\n  get isEmailValid() {\n    return !!this.emailToSend && /^\\S+@\\S+\\.\\S+$/.test(this.emailToSend) && !!this.selectedInvoices.length;\n  }\n  constructor(accountservice, messageservice) {\n    this.accountservice = accountservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.invoices = [];\n    this.loading = false;\n    this.customer = {};\n    this.statuses = '';\n    this.types = '';\n    this.loadingPdf = false;\n    this.emailToSend = '';\n    this.selectedInvoices = [];\n    this.isSidebarHidden = false;\n  }\n  ngOnInit() {\n    this.loading = true;\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.loadInitialData(response.customer.customer_id);\n      }\n    });\n    this.accountservice.contact.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        const address = response?.addresses?.[0] || null;\n        if (address) {\n          this.emailToSend = address?.emails?.length ? address.emails[0].email_address : '';\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  loadInitialData(soldToParty) {\n    forkJoin({\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\n      invoiceStatuses: this.accountservice.fetchOrderStatuses({\n        'filters[type][$eq]': 'INVOICE_STATUS'\n      }),\n      invoiceTypes: this.accountservice.fetchOrderStatuses({\n        'filters[type][$eq]': 'INVOICE_TYPE'\n      })\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: ({\n        partnerFunction,\n        invoiceStatuses,\n        invoiceTypes\n      }) => {\n        this.statuses = (invoiceStatuses?.data || []).map(val => val.code).join(';');\n        this.types = (invoiceTypes?.data || []).map(val => val.code).join(';');\n        this.customer = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n        if (this.customer) {\n          this.fetchInvoices();\n        }\n      },\n      error: error => {\n        console.error('Error fetching initial data:', error);\n      }\n    });\n  }\n  fetchInvoices() {\n    this.accountservice.getInvoices({\n      DOC_STATUS: this.statuses,\n      DOC_TYPE: this.types,\n      SOLDTO: this.customer?.customer_id,\n      VKORG: this.customer?.sales_organization,\n      COUNT: 1000,\n      DOCUMENT_DATE: '',\n      DOCUMENT_DATE_TO: ''\n    }).subscribe(response => {\n      this.loading = false;\n      this.invoices = response?.INVOICELIST || [];\n    }, () => {\n      this.loading = false;\n    });\n  }\n  formatDate(input) {\n    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');\n  }\n  downloadPDF(invoiceId) {\n    this.loadingPdf = true;\n    const url = `${ApiConstant['INVOICE']}/${invoiceId}/pdf-form`;\n    this.accountservice.invoicePdf(url).pipe(take(1)).subscribe(response => {\n      const downloadLink = document.createElement('a');\n      //@ts-ignore\n      downloadLink.href = URL.createObjectURL(new Blob([response.body], {\n        type: response.body.type\n      }));\n      // downloadLink.download = 'invoice.pdf';\n      downloadLink.target = '_blank';\n      downloadLink.click();\n      this.loadingPdf = false;\n    });\n  }\n  sendToEmail() {\n    if (!this.emailToSend) {\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Please enter an email address.'\n      });\n      return;\n    }\n    if (!this.selectedInvoices.length) {\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Please select at least one invoice.'\n      });\n      return;\n    }\n    const invoiceIds = this.selectedInvoices.map(inv => inv.INVOICE);\n    this.accountservice.sendInvoicesByEmail({\n      email: this.emailToSend,\n      invoiceIds: invoiceIds\n    }).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Invoices sent successfully to ' + this.emailToSend\n        });\n      },\n      error: err => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  customSort(event) {\n    const sort = {\n      All: (a, b) => {\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\n        return 0;\n      },\n      Support_Team: (a, b) => {\n        const field = event.field || '';\n        const aValue = a[field] ?? '';\n        const bValue = b[field] ?? '';\n        return aValue.toString().localeCompare(bValue.toString()) * (event.order || 1);\n      }\n    };\n    event.data?.sort(event.field == \"support_team\" || event.field == \"assigned_to\" || event.field == \"subject\" ? sort.Support_Team : sort.All);\n  }\n  static {\n    this.ɵfac = function AccountInvoicesComponent_Factory(t) {\n      return new (t || AccountInvoicesComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountInvoicesComponent,\n      selectors: [[\"app-account-invoices\"]],\n      decls: 9,\n      vars: 4,\n      consts: [[\"myTab\", \"\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"class\", \"flex gap-3\", 4, \"ngIf\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"INVOICE\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"multiple\", 3, \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\", \"selection\", \"sortFunction\", \"selectionChange\", 4, \"ngIf\"], [\"class\", \"w-100\", 4, \"ngIf\"], [1, \"flex\", \"gap-3\"], [\"type\", \"email\", \"pInputText\", \"\", \"disabled\", \"true\", \"readonly\", \"\", \"placeholder\", \"Enter email\", 1, \"p-inputtext-sm\", 2, \"width\", \"220px\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"button\", 1, \"p-button\", \"p-button-sm\", 3, \"click\", \"disabled\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"INVOICE\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"multiple\", 3, \"sortFunction\", \"selectionChange\", \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\", \"selection\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\", 2, \"width\", \"3em\"], [\"pSortableColumn\", \"INVOICE\"], [\"field\", \"SD_DOC\"], [\"pSortableColumn\", \"PURCH_NO\"], [\"pSortableColumn\", \"AMOUNT\"], [\"pSortableColumn\", \"DOC_DATE\"], [\"field\", \"DOC_DATE\"], [\"pSortableColumn\", \"DUE_DATE\"], [\"field\", \"DUE_DATE\"], [\"pSortableColumn\", \"DAYS_PAST_DUE\"], [\"field\", \"DAYS_PAST_DUE\"], [1, \"border-round-right-lg\"], [1, \"border-round-left-lg\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-semibold\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"justify-content-center\", \"h-2rem\", 3, \"click\"], [1, \"w-100\"]],\n      template: function AccountInvoicesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Invoices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AccountInvoicesComponent_div_4_Template, 4, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtemplate(6, AccountInvoicesComponent_div_6_Template, 2, 0, \"div\", 6)(7, AccountInvoicesComponent_p_table_7_Template, 4, 7, \"p-table\", 7)(8, AccountInvoicesComponent_div_8_Template, 2, 1, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.emailToSend);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.invoices.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.invoices.length);\n        }\n      },\n      dependencies: [i3.NgIf, i2.PrimeTemplate, i4.Table, i4.SortableColumn, i4.SortIcon, i4.TableCheckbox, i4.TableHeaderCheckbox, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.InputText, i7.ProgressSpinner, i3.CurrencyPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["fork<PERSON><PERSON>n", "Subject", "take", "takeUntil", "moment", "ApiConstant", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "AccountInvoicesComponent_div_4_Template_input_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "emailToSend", "ɵɵresetView", "ɵɵelementEnd", "ɵɵlistener", "AccountInvoicesComponent_div_4_Template_button_click_2_listener", "sendToEmail", "ɵɵtext", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "isEmail<PERSON><PERSON>d", "ɵɵelement", "AccountInvoicesComponent_p_table_7_ng_template_3_Template_button_click_21_listener", "invoice_r5", "_r4", "$implicit", "downloadPDF", "INVOICE", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "PURCH_NO", "ɵɵpipeBind2", "AMOUNT", "CURRENCY", "formatDate", "DOC_DATE", "AccountInvoicesComponent_p_table_7_Template_p_table_sortFunction_0_listener", "_r3", "customSort", "AccountInvoicesComponent_p_table_7_Template_p_table_selectionChange_0_listener", "selectedInvoices", "ɵɵtemplate", "AccountInvoicesComponent_p_table_7_ng_template_2_Template", "AccountInvoicesComponent_p_table_7_ng_template_3_Template", "invoices", "loading", "AccountInvoicesComponent", "test", "length", "constructor", "accountservice", "messageservice", "unsubscribe$", "customer", "statuses", "types", "loadingPdf", "isSidebarHidden", "ngOnInit", "account", "pipe", "subscribe", "response", "loadInitialData", "customer_id", "contact", "address", "addresses", "emails", "email_address", "ngOnDestroy", "next", "complete", "soldToParty", "partnerFunction", "getPartnerFunction", "invoiceStatuses", "fetchOrderStatuses", "invoiceTypes", "data", "map", "val", "code", "join", "find", "o", "partner_function", "fetchInvoices", "error", "console", "getInvoices", "DOC_STATUS", "DOC_TYPE", "SOLDTO", "VKORG", "sales_organization", "COUNT", "DOCUMENT_DATE", "DOCUMENT_DATE_TO", "INVOICELIST", "input", "format", "invoiceId", "url", "invoicePdf", "downloadLink", "document", "createElement", "href", "URL", "createObjectURL", "Blob", "body", "type", "target", "click", "add", "severity", "detail", "invoiceIds", "inv", "sendInvoicesByEmail", "email", "err", "toggleSidebar", "event", "sort", "All", "a", "b", "field", "order", "Support_Team", "aValue", "bValue", "toString", "localeCompare", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "MessageService", "selectors", "decls", "vars", "consts", "template", "AccountInvoicesComponent_Template", "rf", "ctx", "AccountInvoicesComponent_div_4_Template", "AccountInvoicesComponent_div_6_Template", "AccountInvoicesComponent_p_table_7_Template", "AccountInvoicesComponent_div_8_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-invoices\\account-invoices.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { forkJoin, Subject, take, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService, SortEvent } from 'primeng/api';\r\nimport { stringify } from 'qs';\r\nimport { ServiceTicketService } from 'src/app/store/services/service-ticket.service';\r\nimport { SettingsService } from 'src/app/store/services/setting.service';\r\nimport * as moment from 'moment';\r\nimport { ApiConstant } from 'src/app/constants/api.constants';\r\n\r\n@Component({\r\n  selector: 'app-account-invoices',\r\n  templateUrl: './account-invoices.component.html',\r\n  styleUrl: './account-invoices.component.scss',\r\n})\r\nexport class AccountInvoicesComponent implements OnInit {\r\n\r\n  private unsubscribe$ = new Subject<void>();\r\n\r\n  invoices: any[] = [];\r\n  loading = false;\r\n  public customer: any = {};\r\n  statuses = '';\r\n  types = '';\r\n  loadingPdf = false;\r\n  emailToSend: string = '';\r\n  selectedInvoices: any[] = [];\r\n  get isEmailValid(): boolean {\r\n    return !!this.emailToSend && /^\\S+@\\S+\\.\\S+$/.test(this.emailToSend) && !!this.selectedInvoices.length;\r\n  }\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private messageservice: MessageService,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loading = true;\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.loadInitialData(response.customer.customer_id);\r\n        }\r\n      });\r\n    this.accountservice.contact\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          const address = response?.addresses?.[0] || null;\r\n          if (address) {\r\n            this.emailToSend = address?.emails?.length ? address.emails[0].email_address : ''\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n\r\n  loadInitialData(soldToParty: string) {\r\n    forkJoin({\r\n      partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\r\n      invoiceStatuses: this.accountservice.fetchOrderStatuses({ 'filters[type][$eq]': 'INVOICE_STATUS' }),\r\n      invoiceTypes: this.accountservice.fetchOrderStatuses({ 'filters[type][$eq]': 'INVOICE_TYPE' })\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: ({ partnerFunction, invoiceStatuses, invoiceTypes }) => {\r\n          this.statuses = (invoiceStatuses?.data || []).map((val: any) => val.code).join(';');\r\n          this.types = (invoiceTypes?.data || []).map((val: any) => val.code).join(';');\r\n          this.customer = partnerFunction.find(\r\n            (o: any) =>\r\n              o.customer_id === soldToParty && o.partner_function === 'SH'\r\n          );\r\n\r\n          if (this.customer) {\r\n            this.fetchInvoices();\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching initial data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchInvoices() {\r\n    this.accountservice.getInvoices({\r\n      DOC_STATUS: this.statuses,\r\n      DOC_TYPE: this.types,\r\n      SOLDTO: this.customer?.customer_id,\r\n      VKORG: this.customer?.sales_organization,\r\n      COUNT: 1000,\r\n      DOCUMENT_DATE: '',\r\n      DOCUMENT_DATE_TO: '',\r\n    }).subscribe((response: any) => {\r\n      this.loading = false;\r\n      this.invoices = response?.INVOICELIST || [];\r\n    }, () => {\r\n      this.loading = false;\r\n    });\r\n  }\r\n\r\n  formatDate(input: string) {\r\n    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');\r\n  }\r\n\r\n  downloadPDF(invoiceId: string) {\r\n    this.loadingPdf = true;\r\n    const url = `${ApiConstant['INVOICE']}/${invoiceId}/pdf-form`;\r\n    this.accountservice.invoicePdf(url)\r\n      .pipe(take(1))\r\n      .subscribe((response) => {\r\n        const downloadLink = document.createElement('a');\r\n        //@ts-ignore\r\n        downloadLink.href = URL.createObjectURL(new Blob([response.body], { type: response.body.type }));\r\n        // downloadLink.download = 'invoice.pdf';\r\n        downloadLink.target = '_blank';\r\n        downloadLink.click();\r\n        this.loadingPdf = false;\r\n      });\r\n  }\r\n\r\n  sendToEmail() {\r\n    if (!this.emailToSend) {\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail: 'Please enter an email address.',\r\n      });\r\n      return;\r\n    }\r\n    if (!this.selectedInvoices.length) {\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail: 'Please select at least one invoice.',\r\n      });\r\n      return;\r\n    }\r\n    const invoiceIds = this.selectedInvoices.map(inv => inv.INVOICE);\r\n    this.accountservice.sendInvoicesByEmail({\r\n      email: this.emailToSend,\r\n      invoiceIds: invoiceIds\r\n    }).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Invoices sent successfully to ' + this.emailToSend,\r\n        });\r\n      },\r\n      error: (err) => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  isSidebarHidden = false;\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  customSort(event: SortEvent) {\r\n    const sort = {\r\n      All: (a: any, b: any) => {\r\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\r\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\r\n        return 0;\r\n      },\r\n      Support_Team: (a: any, b: any) => {\r\n        const field = event.field || '';\r\n        const aValue = a[field] ?? '';\r\n        const bValue = b[field] ?? '';\r\n        return aValue.toString().localeCompare(bValue.toString()) * (event.order || 1);\r\n      }\r\n    };\r\n    event.data?.sort(event.field == \"support_team\" || event.field == \"assigned_to\" || event.field == \"subject\" ? sort.Support_Team : sort.All);\r\n  }\r\n\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Invoices</h4>\r\n        <div class=\"flex gap-3\" *ngIf=\"emailToSend\">\r\n            <input type=\"email\" pInputText disabled=\"true\" readonly [(ngModel)]=\"emailToSend\" placeholder=\"Enter email\" class=\"p-inputtext-sm\" style=\"width: 220px;\" />\r\n            <button type=\"button\" class=\"p-button p-button-sm\" (click)=\"sendToEmail()\" [disabled]=\"!isEmailValid\">Send to Email</button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"loading\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n        <p-table #myTab [value]=\"invoices\" dataKey=\"INVOICE\" [rows]=\"10\" [rowHover]=\"true\" [loading]=\"loading\"\r\n            [paginator]=\"true\" responsiveLayout=\"scroll\" *ngIf=\"!loading && invoices.length\"\r\n            (sortFunction)=\"customSort($event)\" [customSort]=\"true\"\r\n            [(selection)]=\"selectedInvoices\" selectionMode=\"multiple\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3em\" class=\"border-round-left-lg\" >\r\n                        <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\r\n                    </th>\r\n                    <th pSortableColumn=\"INVOICE\">Billing Doc # <p-sortIcon\r\n                            field=\"SD_DOC\" /></th>\r\n                    <th>Order #</th>\r\n                    <th pSortableColumn=\"PURCH_NO\">PO # <p-sortIcon field=\"SD_DOC\" /></th>\r\n                    <th pSortableColumn=\"AMOUNT\">Total Amount <p-sortIcon field=\"SD_DOC\" /></th>\r\n                    <th>Open Amount</th>\r\n                    <th pSortableColumn=\"DOC_DATE\">Billing Date <p-sortIcon field=\"DOC_DATE\" /></th>\r\n                    <th pSortableColumn=\"DUE_DATE\">Due Date <p-sortIcon field=\"DUE_DATE\" /></th>\r\n                    <th pSortableColumn=\"DAYS_PAST_DUE\">Days Past Due <p-sortIcon field=\"DAYS_PAST_DUE\" /></th>\r\n                    <th class=\"border-round-right-lg\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-invoice let-rowIndex=\"rowIndex\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" >\r\n                        <p-tableCheckbox [value]=\"invoice\"></p-tableCheckbox>\r\n                    </td>\r\n                    <td class=\"text-orange-600 cursor-pointer font-semibold\">\r\n                        {{ invoice.INVOICE }}\r\n                    </td>\r\n                    <td>-</td>\r\n                    <td>{{ invoice.PURCH_NO }}</td>\r\n                    <td>\r\n                        {{ invoice.AMOUNT | currency: invoice.CURRENCY }}\r\n                    </td>\r\n                    <td>-</td>\r\n                    <td>\r\n                        {{ formatDate(invoice.DOC_DATE) }}\r\n                    </td>\r\n                    <td>-</td>\r\n                    <td>-</td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-ripple p-button p-component justify-content-center h-2rem\"\r\n                            (click)=\"downloadPDF(invoice.INVOICE)\">View Details/PDF</button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n        <div class=\"w-100\" *ngIf=\"!loading && !invoices.length\">{{ 'No records found.'}}</div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,QAAQ,MAAM;AAOzD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,WAAW,QAAQ,iCAAiC;;;;;;;;;;;;ICLjDC,EADJ,CAAAC,cAAA,aAA4C,gBACmH;IAAnGD,EAAA,CAAAE,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,WAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,WAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAyB;IAAjFJ,EAAA,CAAAY,YAAA,EAA2J;IAC3JZ,EAAA,CAAAC,cAAA,iBAAsG;IAAnDD,EAAA,CAAAa,UAAA,mBAAAC,gEAAA;MAAAd,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAQ,WAAA,EAAa;IAAA,EAAC;IAA4Bf,EAAA,CAAAgB,MAAA,oBAAa;IACvHhB,EADuH,CAAAY,YAAA,EAAS,EAC1H;;;;IAFsDZ,EAAA,CAAAiB,SAAA,EAAyB;IAAzBjB,EAAA,CAAAkB,gBAAA,YAAAX,MAAA,CAAAG,WAAA,CAAyB;IACNV,EAAA,CAAAiB,SAAA,EAA0B;IAA1BjB,EAAA,CAAAmB,UAAA,cAAAZ,MAAA,CAAAa,YAAA,CAA0B;;;;;IAKzGpB,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAqB,SAAA,wBAAuC;IAC3CrB,EAAA,CAAAY,YAAA,EAAM;;;;;IAQMZ,EADJ,CAAAC,cAAA,SAAI,aACqD;IACjDD,EAAA,CAAAqB,SAAA,4BAA+C;IACnDrB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAgB,MAAA,qBAAc;IAAAhB,EAAA,CAAAqB,SAAA,qBACnB;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAC9BZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAgB,MAAA,cAAO;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IAChBZ,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAgB,MAAA,YAAK;IAAAhB,EAAA,CAAAqB,SAAA,sBAA6B;IAAArB,EAAA,CAAAY,YAAA,EAAK;IACtEZ,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAgB,MAAA,qBAAa;IAAAhB,EAAA,CAAAqB,SAAA,sBAA6B;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAC5EZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAgB,MAAA,mBAAW;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACpBZ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAgB,MAAA,qBAAa;IAAAhB,EAAA,CAAAqB,SAAA,sBAA+B;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAChFZ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAgB,MAAA,iBAAS;IAAAhB,EAAA,CAAAqB,SAAA,sBAA+B;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAC5EZ,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAgB,MAAA,sBAAc;IAAAhB,EAAA,CAAAqB,SAAA,sBAAoC;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAC3FZ,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAgB,MAAA,cAAM;IAC5ChB,EAD4C,CAAAY,YAAA,EAAK,EAC5C;;;;;;IAIDZ,EADJ,CAAAC,cAAA,SAAI,aACkC;IAC9BD,EAAA,CAAAqB,SAAA,0BAAqD;IACzDrB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAAyD;IACrDD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACVZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAgB,MAAA,GAAsB;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IAC/BZ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAgB,MAAA,IACJ;;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAgB,MAAA,SAAC;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACVZ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAgB,MAAA,IACJ;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAgB,MAAA,SAAC;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IACVZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAgB,MAAA,SAAC;IAAAhB,EAAA,CAAAY,YAAA,EAAK;IAENZ,EADJ,CAAAC,cAAA,cAAkC,kBAGa;IAAvCD,EAAA,CAAAa,UAAA,mBAAAS,mFAAA;MAAA,MAAAC,UAAA,GAAAvB,EAAA,CAAAK,aAAA,CAAAmB,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAmB,WAAA,CAAAH,UAAA,CAAAI,OAAA,CAA4B;IAAA,EAAC;IAAC3B,EAAA,CAAAgB,MAAA,wBAAgB;IAEnEhB,EAFmE,CAAAY,YAAA,EAAS,EACnE,EACJ;;;;;IArBoBZ,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAmB,UAAA,UAAAI,UAAA,CAAiB;IAGlCvB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAA4B,kBAAA,MAAAL,UAAA,CAAAI,OAAA,MACJ;IAEI3B,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAA6B,iBAAA,CAAAN,UAAA,CAAAO,QAAA,CAAsB;IAEtB9B,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA+B,WAAA,QAAAR,UAAA,CAAAS,MAAA,EAAAT,UAAA,CAAAU,QAAA,OACJ;IAGIjC,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAA4B,kBAAA,MAAArB,MAAA,CAAA2B,UAAA,CAAAX,UAAA,CAAAY,QAAA,OACJ;;;;;;IAtCZnC,EAAA,CAAAC,cAAA,qBAG8D;IAD1DD,EAAA,CAAAa,UAAA,0BAAAuB,4EAAAhC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAgBJ,MAAA,CAAA+B,UAAA,CAAAlC,MAAA,CAAkB;IAAA,EAAC;IACnCJ,EAAA,CAAAE,gBAAA,6BAAAqC,+EAAAnC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAiC,gBAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAiC,gBAAA,GAAApC,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgC;IAmBhCJ,EAjBA,CAAAyC,UAAA,IAAAC,yDAAA,2BAAgC,IAAAC,yDAAA,2BAiBkC;IA0BtE3C,EAAA,CAAAY,YAAA,EAAU;;;;IA9C8BZ,EAFxB,CAAAmB,UAAA,UAAAZ,MAAA,CAAAqC,QAAA,CAAkB,YAA8B,kBAAkB,YAAArC,MAAA,CAAAsC,OAAA,CAAoB,mBAChF,oBACqC;IACvD7C,EAAA,CAAAkB,gBAAA,cAAAX,MAAA,CAAAiC,gBAAA,CAAgC;;;;;IA8CpCxC,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAY,YAAA,EAAM;;;IAA9BZ,EAAA,CAAAiB,SAAA,EAAwB;IAAxBjB,EAAA,CAAA6B,iBAAA,qBAAwB;;;AD9CxF,OAAM,MAAOiB,wBAAwB;EAYnC,IAAI1B,YAAYA,CAAA;IACd,OAAO,CAAC,CAAC,IAAI,CAACV,WAAW,IAAI,gBAAgB,CAACqC,IAAI,CAAC,IAAI,CAACrC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC8B,gBAAgB,CAACQ,MAAM;EACxG;EAEAC,YACUC,cAA8B,EAC9BC,cAA8B;IAD9B,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAhBhB,KAAAC,YAAY,GAAG,IAAIzD,OAAO,EAAQ;IAE1C,KAAAiD,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAG,KAAK;IACR,KAAAQ,QAAQ,GAAQ,EAAE;IACzB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAA9C,WAAW,GAAW,EAAE;IACxB,KAAA8B,gBAAgB,GAAU,EAAE;IAsI5B,KAAAiB,eAAe,GAAG,KAAK;EA9HnB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACK,cAAc,CAACS,OAAO,CACxBC,IAAI,CAAC/D,SAAS,CAAC,IAAI,CAACuD,YAAY,CAAC,CAAC,CAClCS,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACC,eAAe,CAACD,QAAQ,CAACT,QAAQ,CAACW,WAAW,CAAC;MACrD;IACF,CAAC,CAAC;IACJ,IAAI,CAACd,cAAc,CAACe,OAAO,CACxBL,IAAI,CAAC/D,SAAS,CAAC,IAAI,CAACuD,YAAY,CAAC,CAAC,CAClCS,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,MAAMI,OAAO,GAAGJ,QAAQ,EAAEK,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;QAChD,IAAID,OAAO,EAAE;UACX,IAAI,CAACxD,WAAW,GAAGwD,OAAO,EAAEE,MAAM,EAAEpB,MAAM,GAAGkB,OAAO,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,aAAa,GAAG,EAAE;QACnF;MACF;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClB,YAAY,CAACmB,IAAI,EAAE;IACxB,IAAI,CAACnB,YAAY,CAACoB,QAAQ,EAAE;EAC9B;EAEAT,eAAeA,CAACU,WAAmB;IACjC/E,QAAQ,CAAC;MACPgF,eAAe,EAAE,IAAI,CAACxB,cAAc,CAACyB,kBAAkB,CAACF,WAAW,CAAC;MACpEG,eAAe,EAAE,IAAI,CAAC1B,cAAc,CAAC2B,kBAAkB,CAAC;QAAE,oBAAoB,EAAE;MAAgB,CAAE,CAAC;MACnGC,YAAY,EAAE,IAAI,CAAC5B,cAAc,CAAC2B,kBAAkB,CAAC;QAAE,oBAAoB,EAAE;MAAc,CAAE;KAC9F,CAAC,CACCjB,IAAI,CAAC/D,SAAS,CAAC,IAAI,CAACuD,YAAY,CAAC,CAAC,CAClCS,SAAS,CAAC;MACTU,IAAI,EAAEA,CAAC;QAAEG,eAAe;QAAEE,eAAe;QAAEE;MAAY,CAAE,KAAI;QAC3D,IAAI,CAACxB,QAAQ,GAAG,CAACsB,eAAe,EAAEG,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QACnF,IAAI,CAAC5B,KAAK,GAAG,CAACuB,YAAY,EAAEC,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAC7E,IAAI,CAAC9B,QAAQ,GAAGqB,eAAe,CAACU,IAAI,CACjCC,CAAM,IACLA,CAAC,CAACrB,WAAW,KAAKS,WAAW,IAAIY,CAAC,CAACC,gBAAgB,KAAK,IAAI,CAC/D;QAED,IAAI,IAAI,CAACjC,QAAQ,EAAE;UACjB,IAAI,CAACkC,aAAa,EAAE;QACtB;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACN;EAEAD,aAAaA,CAAA;IACX,IAAI,CAACrC,cAAc,CAACwC,WAAW,CAAC;MAC9BC,UAAU,EAAE,IAAI,CAACrC,QAAQ;MACzBsC,QAAQ,EAAE,IAAI,CAACrC,KAAK;MACpBsC,MAAM,EAAE,IAAI,CAACxC,QAAQ,EAAEW,WAAW;MAClC8B,KAAK,EAAE,IAAI,CAACzC,QAAQ,EAAE0C,kBAAkB;MACxCC,KAAK,EAAE,IAAI;MACXC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE;KACnB,CAAC,CAACrC,SAAS,CAAEC,QAAa,IAAI;MAC7B,IAAI,CAACjB,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,QAAQ,GAAGkB,QAAQ,EAAEqC,WAAW,IAAI,EAAE;IAC7C,CAAC,EAAE,MAAK;MACN,IAAI,CAACtD,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAEAX,UAAUA,CAACkE,KAAa;IACtB,OAAOtG,MAAM,CAACsG,KAAK,EAAE,UAAU,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;EACvD;EAEA3E,WAAWA,CAAC4E,SAAiB;IAC3B,IAAI,CAAC9C,UAAU,GAAG,IAAI;IACtB,MAAM+C,GAAG,GAAG,GAAGxG,WAAW,CAAC,SAAS,CAAC,IAAIuG,SAAS,WAAW;IAC7D,IAAI,CAACpD,cAAc,CAACsD,UAAU,CAACD,GAAG,CAAC,CAChC3C,IAAI,CAAChE,IAAI,CAAC,CAAC,CAAC,CAAC,CACbiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,MAAM2C,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MAChD;MACAF,YAAY,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACjD,QAAQ,CAACkD,IAAI,CAAC,EAAE;QAAEC,IAAI,EAAEnD,QAAQ,CAACkD,IAAI,CAACC;MAAI,CAAE,CAAC,CAAC;MAChG;MACAR,YAAY,CAACS,MAAM,GAAG,QAAQ;MAC9BT,YAAY,CAACU,KAAK,EAAE;MACpB,IAAI,CAAC3D,UAAU,GAAG,KAAK;IACzB,CAAC,CAAC;EACN;EAEAzC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACL,WAAW,EAAE;MACrB,IAAI,CAACyC,cAAc,CAACiE,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IACA,IAAI,CAAC,IAAI,CAAC9E,gBAAgB,CAACQ,MAAM,EAAE;MACjC,IAAI,CAACG,cAAc,CAACiE,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC/E,gBAAgB,CAACwC,GAAG,CAACwC,GAAG,IAAIA,GAAG,CAAC7F,OAAO,CAAC;IAChE,IAAI,CAACuB,cAAc,CAACuE,mBAAmB,CAAC;MACtCC,KAAK,EAAE,IAAI,CAAChH,WAAW;MACvB6G,UAAU,EAAEA;KACb,CAAC,CAAC1D,SAAS,CAAC;MACXU,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACpB,cAAc,CAACiE,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE,gCAAgC,GAAG,IAAI,CAAC5G;SACjD,CAAC;MACJ,CAAC;MACD8E,KAAK,EAAGmC,GAAG,IAAI;QACb,IAAI,CAACxE,cAAc,CAACiE,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAIAM,aAAaA,CAAA;IACX,IAAI,CAACnE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAnB,UAAUA,CAACuF,KAAgB;IACzB,MAAMC,IAAI,GAAG;MACXC,GAAG,EAAEA,CAACC,CAAM,EAAEC,CAAM,KAAI;QACtB,IAAID,CAAC,CAACH,KAAK,CAACK,KAAK,IAAI,EAAE,CAAC,GAAGD,CAAC,CAACJ,KAAK,CAACK,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,IAAIL,KAAK,CAACM,KAAK,IAAI,CAAC,CAAC;QAC/E,IAAIH,CAAC,CAACH,KAAK,CAACK,KAAK,IAAI,EAAE,CAAC,GAAGD,CAAC,CAACJ,KAAK,CAACK,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAIL,KAAK,CAACM,KAAK,IAAI,CAAC,CAAC;QAC9E,OAAO,CAAC;MACV,CAAC;MACDC,YAAY,EAAEA,CAACJ,CAAM,EAAEC,CAAM,KAAI;QAC/B,MAAMC,KAAK,GAAGL,KAAK,CAACK,KAAK,IAAI,EAAE;QAC/B,MAAMG,MAAM,GAAGL,CAAC,CAACE,KAAK,CAAC,IAAI,EAAE;QAC7B,MAAMI,MAAM,GAAGL,CAAC,CAACC,KAAK,CAAC,IAAI,EAAE;QAC7B,OAAOG,MAAM,CAACE,QAAQ,EAAE,CAACC,aAAa,CAACF,MAAM,CAACC,QAAQ,EAAE,CAAC,IAAIV,KAAK,CAACM,KAAK,IAAI,CAAC,CAAC;MAChF;KACD;IACDN,KAAK,CAAC9C,IAAI,EAAE+C,IAAI,CAACD,KAAK,CAACK,KAAK,IAAI,cAAc,IAAIL,KAAK,CAACK,KAAK,IAAI,aAAa,IAAIL,KAAK,CAACK,KAAK,IAAI,SAAS,GAAGJ,IAAI,CAACM,YAAY,GAAGN,IAAI,CAACC,GAAG,CAAC;EAC5I;;;uBAtKWjF,wBAAwB,EAAA9C,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxB/F,wBAAwB;MAAAgG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd7BpJ,EAFR,CAAAC,cAAA,aAAuD,aACkC,YAClC;UAAAD,EAAA,CAAAgB,MAAA,eAAQ;UAAAhB,EAAA,CAAAY,YAAA,EAAK;UAC5DZ,EAAA,CAAAyC,UAAA,IAAA6G,uCAAA,iBAA4C;UAIhDtJ,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAAC,cAAA,aAAuB;UAqDnBD,EApDA,CAAAyC,UAAA,IAAA8G,uCAAA,iBAAwF,IAAAC,2CAAA,qBAM1B,IAAAC,uCAAA,iBA8CN;UAEhEzJ,EADI,CAAAY,YAAA,EAAM,EACJ;;;UA7D2BZ,EAAA,CAAAiB,SAAA,GAAiB;UAAjBjB,EAAA,CAAAmB,UAAA,SAAAkI,GAAA,CAAA3I,WAAA,CAAiB;UAO+BV,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAmB,UAAA,SAAAkI,GAAA,CAAAxG,OAAA,CAAa;UAIpC7C,EAAA,CAAAiB,SAAA,EAAiC;UAAjCjB,EAAA,CAAAmB,UAAA,UAAAkI,GAAA,CAAAxG,OAAA,IAAAwG,GAAA,CAAAzG,QAAA,CAAAI,MAAA,CAAiC;UAgD/DhD,EAAA,CAAAiB,SAAA,EAAkC;UAAlCjB,EAAA,CAAAmB,UAAA,UAAAkI,GAAA,CAAAxG,OAAA,KAAAwG,GAAA,CAAAzG,QAAA,CAAAI,MAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}