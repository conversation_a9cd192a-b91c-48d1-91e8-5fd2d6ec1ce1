{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./reset-password.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => [\"/auth/login\"];\nfunction ResetPasswordComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Capital Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Small Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Special Character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_16_div_1_Template, 2, 0, \"div\", 20)(2, ResetPasswordComponent_div_16_div_2_Template, 2, 0, \"div\", 20)(3, ResetPasswordComponent_div_16_div_3_Template, 2, 0, \"div\", 20)(4, ResetPasswordComponent_div_16_div_4_Template, 2, 0, \"div\", 20)(5, ResetPasswordComponent_div_16_div_5_Template, 2, 0, \"div\", 20)(6, ResetPasswordComponent_div_16_div_6_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"minlength\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasNumber\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasCapitalCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSmallCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSpecialCharacters\"]);\n  }\n}\nfunction ResetPasswordComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_26_div_1_Template, 2, 0, \"div\", 20)(2, ResetPasswordComponent_div_26_div_2_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"confirmedValidator\"]);\n  }\n}\nfunction ConfirmedValidator(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\n      return;\n    }\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        confirmedValidator: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}\nfunction patternValidator(regex, error) {\n  return control => {\n    if (!control.value) {\n      // if control is empty return no error\n      return null;\n    }\n    // test the value of the control against the regexp supplied\n    const valid = regex.test(control.value);\n    // if true, return no error (no error), else return error passed in the second parameter\n    return valid ? null : error;\n  };\n}\nexport class ResetPasswordComponent {\n  constructor(formBuilder, service, route, router) {\n    this.formBuilder = formBuilder;\n    this.service = service;\n    this.route = route;\n    this.router = router;\n    this.form = this.formBuilder.group({\n      password: ['', [Validators.required, Validators.minLength(8),\n      // check whether the entered password has a number\n      patternValidator(/\\d/, {\n        hasNumber: true\n      }),\n      // check whether the entered password has upper case letter\n      patternValidator(/[A-Z]/, {\n        hasCapitalCase: true\n      }),\n      // check whether the entered password has a lower case letter\n      patternValidator(/[a-z]/, {\n        hasSmallCase: true\n      }),\n      // check whether the entered password has a special character\n      patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\n        hasSpecialCharacters: true\n      })]],\n      passwordConfirm: ['', Validators.required]\n    }, {\n      validators: ConfirmedValidator('password', 'passwordConfirm')\n    });\n    this.submitted = false;\n    this.saving = false;\n    this.token = '';\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      this.token = params['code'];\n    });\n  }\n  get f() {\n    return this.form.controls;\n  }\n  onSubmit() {\n    this.submitted = true;\n    if (this.form.invalid) {\n      return;\n    }\n    if (!this.token) {\n      // this._snackBar.open('Invalid Url.', { type: 'Warning' });\n      return;\n    }\n    this.saving = true;\n    this.service.resetPassword({\n      passwordConfirmation: this.form.value.password,\n      password: this.form.value.password,\n      code: this.token\n    }).subscribe({\n      complete: () => {\n        this.onReset();\n        this.saving = false;\n        // this._snackBar.open('Password reset successfully!');\n      },\n      error: err => {\n        this.saving = false;\n        // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\n      }\n    });\n  }\n  onReset() {\n    this.submitted = false;\n    this.form.reset();\n    setTimeout(() => {\n      this.router.navigate([\"store\"]);\n    }, 2000);\n  }\n  static {\n    this.ɵfac = function ResetPasswordComponent_Factory(t) {\n      return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ResetPasswordService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordComponent,\n      selectors: [[\"app-reset-password\"]],\n      decls: 35,\n      vars: 12,\n      consts: [[1, \"login-sec\", \"bg-white\", \"min-h-screen\", \"flex\", \"align-items-center\", \"lg:pt-6\", \"pb-6\", \"md:pt-4\", \"pb-4\"], [1, \"login-page-body\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"mb-0\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\"], [1, \"form-group\", \"relative\"], [1, \"relative\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\"], [\"class\", \"invalid-feedback text-red-500\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"passwordConfirm\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"ngClass\"], [1, \"form-footer\", \"mt-4\"], [1, \"field\", \"col-12\", \"md:col-6\", \"mb-0\"], [\"type\", \"button\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\", 3, \"routerLink\"], [1, \"invalid-feedback\", \"text-red-500\"], [4, \"ngIf\"]],\n      template: function ResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Reset Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7);\n          i0.ɵɵtext(9, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵelement(12, \"input\", 10);\n          i0.ɵɵelementStart(13, \"button\", 11)(14, \"span\", 12);\n          i0.ɵɵtext(15, \"visibility\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, ResetPasswordComponent_div_16_Template, 7, 6, \"div\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 7);\n          i0.ɵɵtext(19, \"Retype Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9);\n          i0.ɵɵelement(22, \"input\", 14);\n          i0.ɵɵelementStart(23, \"button\", 11)(24, \"span\", 12);\n          i0.ɵɵtext(25, \"visibility\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(26, ResetPasswordComponent_div_26_Template, 3, 2, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"div\", 5)(29, \"div\", 16)(30, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_30_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(31, \" Login\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"button\", 18);\n          i0.ɵɵtext(34, \" Cancel \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.submitted && ctx.f[\"password\"].errors));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"password\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.submitted && ctx.f[\"passwordConfirm\"].errors));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"passwordConfirm\"].errors);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.saving);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c1));\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 480px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n\\n.min-h-screen[_ngcontent-%COMP%] {\\n  min-height: 100vh !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9yZXNldC1wYXNzd29yZC9yZXNldC1wYXNzd29yZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNJLGlCQUFBO0FBQVI7QUFFUTtFQUNJLDJCQUFBO0FBQVo7QUFJb0I7RUFDSSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7QUFGeEI7QUFLb0I7RUFDSSxpQ0FBQTtBQUh4Qjs7QUFXQTtFQUNJLFlBQUE7RUFDQSwyQkFBQTtBQVJKOztBQVdBO0VBQ0ksY0FBQTtBQVJKOztBQVdBO0VBQ0MsNEJBQUE7QUFSRCIsInNvdXJjZXNDb250ZW50IjpbIi5sb2dpbi1zZWMge1xyXG4gICAgLmxvZ2luLXBhZ2UtYm9keSB7XHJcbiAgICAgICAgbWF4LXdpZHRoOiAxNDQwcHg7XHJcblxyXG4gICAgICAgIC5sb2dpbi1mb3JtIHtcclxuICAgICAgICAgICAgbWF4LXdpZHRoOiA0ODBweCAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgZm9ybSB7XHJcbiAgICAgICAgICAgICAgICAuZm9ybS1ncm91cCB7XHJcbiAgICAgICAgICAgICAgICAgICAgLnBhc3Mtc2hvdy1idG4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByaWdodDogMTJweDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAyNHB4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMjRweDtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5mb3JtLWNoZWNrLWJveCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VudC1jb2xvcjogdmFyKC0tcHJpbWFyeWNvbG9yKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5wLWlucHV0dGV4dCB7XHJcbiAgICBoZWlnaHQ6IDNyZW07XHJcbiAgICBhcHBlYXJhbmNlOiBhdXRvICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5oLTMtM3JlbSB7XHJcbiAgICBoZWlnaHQ6IDMuM3JlbTtcclxufVxyXG5cclxuLm1pbi1oLXNjcmVlbiB7XHJcblx0bWluLWhlaWdodDogMTAwdmggIWltcG9ydGFudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ResetPasswordComponent_div_16_div_1_Template", "ResetPasswordComponent_div_16_div_2_Template", "ResetPasswordComponent_div_16_div_3_Template", "ResetPasswordComponent_div_16_div_4_Template", "ResetPasswordComponent_div_16_div_5_Template", "ResetPasswordComponent_div_16_div_6_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "f", "errors", "ResetPasswordComponent_div_26_div_1_Template", "ResetPasswordComponent_div_26_div_2_Template", "ConfirmedValidator", "controlName", "matchingControlName", "formGroup", "control", "controls", "matchingControl", "value", "setErrors", "confirmedValidator", "patternValidator", "regex", "error", "valid", "test", "ResetPasswordComponent", "constructor", "formBuilder", "service", "route", "router", "form", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "hasNumber", "hasCapitalCase", "hasSmallCase", "hasSpecialCharacters", "passwordConfirm", "validators", "submitted", "saving", "token", "ngOnInit", "queryParams", "subscribe", "params", "onSubmit", "invalid", "resetPassword", "passwordConfirmation", "code", "complete", "onReset", "err", "reset", "setTimeout", "navigate", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ResetPasswordService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵelement", "ResetPasswordComponent_div_16_Template", "ResetPasswordComponent_div_26_Template", "ɵɵlistener", "ResetPasswordComponent_Template_button_click_30_listener", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\session\\reset-password\\reset-password.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\session\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { ResetPasswordService } from './reset-password.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\nfunction ConfirmedValidator(controlName: string, matchingControlName: string) {\r\n  return (formGroup: FormGroup) => {\r\n    const control = formGroup.controls[controlName];\r\n    const matchingControl = formGroup.controls[matchingControlName];\r\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\r\n      return;\r\n    }\r\n    if (control.value !== matchingControl.value) {\r\n      matchingControl.setErrors({ confirmedValidator: true });\r\n    } else {\r\n      matchingControl.setErrors(null);\r\n    }\r\n  }\r\n}\r\n\r\nfunction patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {\r\n  return (control: AbstractControl) => {\r\n    if (!control.value) {\r\n      // if control is empty return no error\r\n      return null;\r\n    }\r\n\r\n    // test the value of the control against the regexp supplied\r\n    const valid = regex.test(control.value);\r\n\r\n    // if true, return no error (no error), else return error passed in the second parameter\r\n    return valid ? null : error;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.scss']\r\n})\r\nexport class ResetPasswordComponent {\r\n  form: FormGroup = this.formBuilder.group(\r\n    {\r\n      password: ['',\r\n        [\r\n          Validators.required, Validators.minLength(8),\r\n          // check whether the entered password has a number\r\n          patternValidator(/\\d/, {\r\n            hasNumber: true\r\n          }),\r\n          // check whether the entered password has upper case letter\r\n          patternValidator(/[A-Z]/, {\r\n            hasCapitalCase: true\r\n          }),\r\n          // check whether the entered password has a lower case letter\r\n          patternValidator(/[a-z]/, {\r\n            hasSmallCase: true\r\n          }),\r\n          // check whether the entered password has a special character\r\n          patternValidator(\r\n            /[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/,\r\n            {\r\n              hasSpecialCharacters: true\r\n            }\r\n          ),\r\n        ]\r\n      ],\r\n      passwordConfirm: ['', Validators.required],\r\n    },\r\n    {\r\n      validators: ConfirmedValidator('password', 'passwordConfirm'),\r\n    },\r\n  );\r\n  submitted = false;\r\n  saving = false;\r\n  token = '';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private service: ResetPasswordService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams\r\n      .subscribe(params => {\r\n        this.token = params['code'];\r\n      }\r\n      );\r\n  }\r\n\r\n  get f(): { [key: string]: AbstractControl } {\r\n    return this.form.controls;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    this.submitted = true;\r\n\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n\r\n    if (!this.token) {\r\n      // this._snackBar.open('Invalid Url.', { type: 'Warning' });\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    this.service.resetPassword({\r\n      passwordConfirmation: this.form.value.password,\r\n      password: this.form.value.password,\r\n      code: this.token\r\n    }).subscribe({\r\n      complete: () => {\r\n        this.onReset();\r\n        this.saving = false;\r\n        // this._snackBar.open('Password reset successfully!');\r\n      },\r\n      error: (err) => {\r\n        this.saving = false;\r\n        // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\r\n      },\r\n    })\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.form.reset();\r\n    setTimeout(() => {\r\n      this.router.navigate([\"store\"]);\r\n    }, 2000);\r\n  }\r\n}\r\n", "<section class=\"login-sec bg-white min-h-screen flex align-items-center lg:pt-6 pb-6 md:pt-4 pb-4\">\r\n    <div class=\"login-page-body flex align-items-center justify-content-center gap-8 m-auto h-full px-5\">\r\n        <div class=\"login-form mx-auto p-5 w-full bg-white border-round-3xl shadow-2\">\r\n            <form class=\"flex flex-column position-relative\" [formGroup]=\"form\">\r\n                <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">Reset Password\r\n                </h1>\r\n                <div class=\"p-fluid p-formgrid grid\">\r\n                    <div class=\"field col-12 mb-0\">\r\n                        <label class=\"text-base font-medium text-gray-600\">Password</label>\r\n                        <div class=\"form-group relative\">\r\n                            <div class=\"relative\">\r\n                                <input type=\"password\" formControlName=\"password\"\r\n                                    class=\"p-inputtext p-component p-element w-full bg-gray-50\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && f['password'].errors }\" />\r\n                                <button type=\"button\"\r\n                                    class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n                                        class=\"material-symbols-rounded\">visibility</span></button>\r\n                            </div>\r\n                            <div *ngIf=\"submitted && f['password'].errors\" class=\"invalid-feedback text-red-500\">\r\n                                <div *ngIf=\"f['password'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"f['password'].errors['minlength']\">\r\n                                    Must be at least 8 characters</div>\r\n                                <div *ngIf=\"f['password'].errors['hasNumber']\">\r\n                                    Must contain at least one number</div>\r\n                                <div *ngIf=\"f['password'].errors['hasCapitalCase']\">\r\n                                    Must contain at least one Letter in Capital Case</div>\r\n                                <div *ngIf=\"f['password'].errors['hasSmallCase']\">\r\n                                    Must contain at least one Letter in Small Case</div>\r\n                                <div *ngIf=\"f['password'].errors['hasSpecialCharacters']\">\r\n                                    Must contain at least one Special Character</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field col-12 mb-0\">\r\n                        <label class=\"text-base font-medium text-gray-600\">Retype Password</label>\r\n                        <div class=\"form-group relative\">\r\n                            <div class=\"relative\">\r\n                                <input type=\"password\" formControlName=\"passwordConfirm\"\r\n                                    class=\"p-inputtext p-component p-element w-full bg-gray-50\"\r\n                                    [ngClass]=\"{ 'is-invalid': submitted && f['passwordConfirm'].errors }\" />\r\n                                <button type=\"button\"\r\n                                    class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n                                        class=\"material-symbols-rounded\">visibility</span></button>\r\n                            </div>\r\n                            <div *ngIf=\"submitted && f['passwordConfirm'].errors\" class=\"invalid-feedback text-red-500\">\r\n                                <div *ngIf=\"f['passwordConfirm'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"f['passwordConfirm'].errors['confirmedValidator']\">\r\n                                    Passwords must match\r\n                                </div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"form-footer mt-4\">\r\n                    <div class=\"p-fluid p-formgrid grid\">\r\n                        <div class=\"field col-12 md:col-6 mb-0\">\r\n                            <button type=\"button\"\r\n                                class=\"p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold\"\r\n                                [disabled]=\"saving\" (click)=\"onSubmit()\">\r\n                                Login</button>\r\n                        </div>\r\n                        <div class=\"field col-12 md:col-6 mb-0\">\r\n                            <button type=\"button\" [routerLink]=\"['/auth/login']\"\r\n                                class=\"p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20\">\r\n                                Cancel\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</section>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICkBnFC,EAAA,CAAAC,cAAA,UAA8C;IAC1CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1CH,EAAA,CAAAC,cAAA,UAAoD;IAChDD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1DH,EAAA,CAAAC,cAAA,UAAkD;IAC9CD,EAAA,CAAAE,MAAA,sDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxDH,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAZzDH,EAAA,CAAAC,cAAA,cAAqF;IAWjFD,EAVA,CAAAI,UAAA,IAAAC,4CAAA,kBAA8C,IAAAC,4CAAA,kBAEC,IAAAC,4CAAA,kBAEA,IAAAC,4CAAA,kBAEK,IAAAC,4CAAA,kBAEF,IAAAC,4CAAA,kBAEQ;IAE9DV,EAAA,CAAAG,YAAA,EAAM;;;;IAZIH,EAAA,CAAAW,SAAA,EAAsC;IAAtCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;IAEtCf,EAAA,CAAAW,SAAA,EAAuC;IAAvCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,cAAuC;IAEvCf,EAAA,CAAAW,SAAA,EAAuC;IAAvCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,cAAuC;IAEvCf,EAAA,CAAAW,SAAA,EAA4C;IAA5CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,mBAA4C;IAE5Cf,EAAA,CAAAW,SAAA,EAA0C;IAA1CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,iBAA0C;IAE1Cf,EAAA,CAAAW,SAAA,EAAkD;IAAlDX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,yBAAkD;;;;;IAiBxDf,EAAA,CAAAC,cAAA,UAAqD;IACjDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAA+D;IAC3DD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALVH,EAAA,CAAAC,cAAA,cAA4F;IAGxFD,EAFA,CAAAI,UAAA,IAAAY,4CAAA,kBAAqD,IAAAC,4CAAA,kBAEU;IAGnEjB,EAAA,CAAAG,YAAA,EAAM;;;;IALIH,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,oBAAAC,MAAA,aAA6C;IAE7Cf,EAAA,CAAAW,SAAA,EAAuD;IAAvDX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,oBAAAC,MAAA,uBAAuD;;;AD1C7F,SAASG,kBAAkBA,CAACC,WAAmB,EAAEC,mBAA2B;EAC1E,OAAQC,SAAoB,IAAI;IAC9B,MAAMC,OAAO,GAAGD,SAAS,CAACE,QAAQ,CAACJ,WAAW,CAAC;IAC/C,MAAMK,eAAe,GAAGH,SAAS,CAACE,QAAQ,CAACH,mBAAmB,CAAC;IAC/D,IAAII,eAAe,CAACT,MAAM,IAAI,CAACS,eAAe,CAACT,MAAM,CAAC,oBAAoB,CAAC,EAAE;MAC3E;IACF;IACA,IAAIO,OAAO,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3CD,eAAe,CAACE,SAAS,CAAC;QAAEC,kBAAkB,EAAE;MAAI,CAAE,CAAC;IACzD,CAAC,MAAM;MACLH,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;AACH;AAEA,SAASE,gBAAgBA,CAACC,KAAa,EAAEC,KAAuB;EAC9D,OAAQR,OAAwB,IAAI;IAClC,IAAI,CAACA,OAAO,CAACG,KAAK,EAAE;MAClB;MACA,OAAO,IAAI;IACb;IAEA;IACA,MAAMM,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACV,OAAO,CAACG,KAAK,CAAC;IAEvC;IACA,OAAOM,KAAK,GAAG,IAAI,GAAGD,KAAK;EAC7B,CAAC;AACH;AAOA,OAAM,MAAOG,sBAAsB;EAqCjCC,YACUC,WAAwB,EACxBC,OAA6B,EAC7BC,KAAqB,EACtBC,MAAc;IAHb,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IAxCf,KAAAC,IAAI,GAAc,IAAI,CAACJ,WAAW,CAACK,KAAK,CACtC;MACEC,QAAQ,EAAE,CAAC,EAAE,EACX,CACE1C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC;MAC5C;MACAf,gBAAgB,CAAC,IAAI,EAAE;QACrBgB,SAAS,EAAE;OACZ,CAAC;MACF;MACAhB,gBAAgB,CAAC,OAAO,EAAE;QACxBiB,cAAc,EAAE;OACjB,CAAC;MACF;MACAjB,gBAAgB,CAAC,OAAO,EAAE;QACxBkB,YAAY,EAAE;OACf,CAAC;MACF;MACAlB,gBAAgB,CACd,wCAAwC,EACxC;QACEmB,oBAAoB,EAAE;OACvB,CACF,CACF,CACF;MACDC,eAAe,EAAE,CAAC,EAAE,EAAEjD,UAAU,CAAC2C,QAAQ;KAC1C,EACD;MACEO,UAAU,EAAE/B,kBAAkB,CAAC,UAAU,EAAE,iBAAiB;KAC7D,CACF;IACD,KAAAgC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,KAAK,GAAG,EAAE;EAON;EAEJC,QAAQA,CAAA;IACN,IAAI,CAAChB,KAAK,CAACiB,WAAW,CACnBC,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACJ,KAAK,GAAGI,MAAM,CAAC,MAAM,CAAC;IAC7B,CAAC,CACA;EACL;EAEA,IAAI1C,CAACA,CAAA;IACH,OAAO,IAAI,CAACyB,IAAI,CAAChB,QAAQ;EAC3B;EAEAkC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACX,IAAI,CAACmB,OAAO,EAAE;MACrB;IACF;IAEA,IAAI,CAAC,IAAI,CAACN,KAAK,EAAE;MACf;MACA;IACF;IAEA,IAAI,CAACD,MAAM,GAAG,IAAI;IAClB,IAAI,CAACf,OAAO,CAACuB,aAAa,CAAC;MACzBC,oBAAoB,EAAE,IAAI,CAACrB,IAAI,CAACd,KAAK,CAACgB,QAAQ;MAC9CA,QAAQ,EAAE,IAAI,CAACF,IAAI,CAACd,KAAK,CAACgB,QAAQ;MAClCoB,IAAI,EAAE,IAAI,CAACT;KACZ,CAAC,CAACG,SAAS,CAAC;MACXO,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACC,OAAO,EAAE;QACd,IAAI,CAACZ,MAAM,GAAG,KAAK;QACnB;MACF,CAAC;MACDrB,KAAK,EAAGkC,GAAG,IAAI;QACb,IAAI,CAACb,MAAM,GAAG,KAAK;QACnB;MACF;KACD,CAAC;EACJ;EAEAY,OAAOA,CAAA;IACL,IAAI,CAACb,SAAS,GAAG,KAAK;IACtB,IAAI,CAACX,IAAI,CAAC0B,KAAK,EAAE;IACjBC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;;;uBA5FWlC,sBAAsB,EAAAjC,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAoE,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAAtB1C,sBAAsB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCnBlF,EAJhB,CAAAC,cAAA,iBAAmG,aACM,aACnB,cACN,YACa;UAAAD,EAAA,CAAAE,MAAA,sBAC7E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,aAAqC,aACF,eACwB;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/DH,EADJ,CAAAC,cAAA,cAAiC,cACP;UAClBD,EAAA,CAAAoF,SAAA,iBAEsE;UAEkDpF,EADxH,CAAAC,cAAA,kBACwH,gBAC/E;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UACvDF,EADuD,CAAAG,YAAA,EAAO,EAAS,EACjE;UACNH,EAAA,CAAAI,UAAA,KAAAiF,sCAAA,kBAAqF;UAe7FrF,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,cAA+B,gBACwB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtEH,EADJ,CAAAC,cAAA,cAAiC,cACP;UAClBD,EAAA,CAAAoF,SAAA,iBAE6E;UAE2CpF,EADxH,CAAAC,cAAA,kBACwH,gBAC/E;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UACvDF,EADuD,CAAAG,YAAA,EAAO,EAAS,EACjE;UACNH,EAAA,CAAAI,UAAA,KAAAkF,sCAAA,kBAA4F;UAUxGtF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UAIMH,EAHZ,CAAAC,cAAA,eAA8B,cACW,eACO,kBAGS;UAArBD,EAAA,CAAAuF,UAAA,mBAAAC,yDAAA;YAAA,OAASL,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UACxCzD,EAAA,CAAAE,MAAA,cAAK;UACbF,EADa,CAAAG,YAAA,EAAS,EAChB;UAEFH,EADJ,CAAAC,cAAA,eAAwC,kBAE0H;UAC1JD,EAAA,CAAAE,MAAA,gBACJ;UAO5BF,EAP4B,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ,EACH,EACL,EACJ,EACA;;;UAxEmDH,EAAA,CAAAW,SAAA,GAAkB;UAAlBX,EAAA,CAAAY,UAAA,cAAAuE,GAAA,CAAA5C,IAAA,CAAkB;UAU3CvC,EAAA,CAAAW,SAAA,GAA+D;UAA/DX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,aAAAC,MAAA,EAA+D;UAKjEf,EAAA,CAAAW,SAAA,GAAuC;UAAvCX,EAAA,CAAAY,UAAA,SAAAuE,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,aAAAC,MAAA,CAAuC;UAsBrCf,EAAA,CAAAW,SAAA,GAAsE;UAAtEX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,oBAAAC,MAAA,EAAsE;UAKxEf,EAAA,CAAAW,SAAA,GAA8C;UAA9CX,EAAA,CAAAY,UAAA,SAAAuE,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,oBAAAC,MAAA,CAA8C;UAgBhDf,EAAA,CAAAW,SAAA,GAAmB;UAAnBX,EAAA,CAAAY,UAAA,aAAAuE,GAAA,CAAAhC,MAAA,CAAmB;UAIDnD,EAAA,CAAAW,SAAA,GAA8B;UAA9BX,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA2F,eAAA,KAAAC,GAAA,EAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}