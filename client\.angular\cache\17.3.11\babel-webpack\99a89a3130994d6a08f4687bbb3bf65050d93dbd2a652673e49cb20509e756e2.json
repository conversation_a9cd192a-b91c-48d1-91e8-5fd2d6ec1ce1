{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ContactsService {\n  constructor(http) {\n    this.http = http;\n    this.contactSubject = new BehaviorSubject(null);\n    this.contact = this.contactSubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  updateContact(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  getContacts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'bp_id,bp_full_name').set('filters[roles][bp_role][$in][0]', 'BUP001').set('populate[addresses][populate]', '*').set('populate[bp_extension][fields][0]', 'contact_status').set('populate[bp_extension][fields][1]', 'web_registered').set('populate[address_usages][fields][0]', 'address_usage').set('populate[address_usages][populate][business_partner_address][populate][emails][fields][0]', 'email_address').set('populate[contact_persons][populate][fields][0]', 'bp_company_id').set('populate[contact_persons][populate][business_partner_company][fields][0]', 'bp_full_name').set('populate[contact_persons][populate][person_func_and_dept][fields][0]', 'contact_person_department_name').set('populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate][phone_numbers][fields][0]', 'phone_number');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][bp_full_name][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getContactByID(contactId) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', contactId).set('populate[addresses][populate]', '*').set('populate[bp_extension][populate]', '*').set('populate[notes][populate]', '*').set('populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]', '*').set('populate[contact_companies][populate][person_func_and_dept][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate]', '*').set('populate[address_usages][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => {\n      const contactDetails = response?.data[0] || null;\n      this.contactSubject.next(contactDetails);\n      return response;\n    }));\n  }\n  getCPDepartment() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getCPFunction() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  static {\n    this.ɵfac = function ContactsService_Factory(t) {\n      return new (t || ContactsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContactsService,\n      factory: ContactsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ContactsService", "constructor", "http", "contactSubject", "contact", "asObservable", "createNote", "data", "post", "CRM_NOTE", "updateContact", "Id", "put", "PROSPECT_CONTACT", "updateNote", "getContacts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS", "getContactByID", "contactId", "pipe", "response", "contactDetails", "next", "getCPDepartment", "CONFIG_DATA", "getCPFunction", "deleteNote", "id", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ContactsService {\r\n  public contactSubject = new BehaviorSubject<any>(null);\r\n  public contact = this.contactSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateContact(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`,\r\n      data\r\n    );\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  getContacts(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set('fields', 'bp_id,bp_full_name')\r\n      .set('filters[roles][bp_role][$in][0]', 'BUP001')\r\n      .set('populate[addresses][populate]', '*')\r\n      .set('populate[bp_extension][fields][0]', 'contact_status')\r\n      .set('populate[bp_extension][fields][1]', 'web_registered')\r\n      .set('populate[address_usages][fields][0]', 'address_usage')\r\n      .set(\r\n        'populate[address_usages][populate][business_partner_address][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set('populate[contact_persons][populate][fields][0]', 'bp_company_id')\r\n      .set(\r\n        'populate[contact_persons][populate][business_partner_company][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set(\r\n        'populate[contact_persons][populate][person_func_and_dept][fields][0]',\r\n        'contact_person_department_name'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][1][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params });\r\n  }\r\n\r\n  getContactByID(contactId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', contactId)\r\n      .set('populate[addresses][populate]', '*')\r\n      .set('populate[bp_extension][populate]', '*')\r\n      .set('populate[notes][populate]', '*')\r\n      .set(\r\n        'populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][person_func_and_dept][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate]',\r\n        '*'\r\n      )\r\n      .set('populate[address_usages][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const contactDetails = response?.data[0] || null;\r\n          this.contactSubject.next(contactDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getCPDepartment() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'CP_DEPARTMENTS');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getCPFunction() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'FUNCTION_CP');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAO,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,aAAaA,CAACC,EAAU,EAAEJ,IAAS;IACjC,OAAO,IAAI,CAACL,IAAI,CAACU,GAAG,CAClB,GAAGb,gBAAgB,CAACc,gBAAgB,IAAIF,EAAE,OAAO,EACjDJ,IAAI,CACL;EACH;EAEAO,UAAUA,CAACH,EAAU,EAAEJ,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACU,GAAG,CAAC,GAAGb,gBAAgB,CAACU,QAAQ,IAAIE,EAAE,EAAE,EAAE;MACzDJ;KACD,CAAC;EACJ;EAEAQ,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC1B0B,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CACnCA,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAChDA,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,CACzCA,GAAG,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,CAC1DA,GAAG,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,CAC1DA,GAAG,CAAC,qCAAqC,EAAE,eAAe,CAAC,CAC3DA,GAAG,CACF,2FAA2F,EAC3F,eAAe,CAChB,CACAA,GAAG,CAAC,gDAAgD,EAAE,eAAe,CAAC,CACtEA,GAAG,CACF,0EAA0E,EAC1E,cAAc,CACf,CACAA,GAAG,CACF,sEAAsE,EACtE,gCAAgC,CACjC,CACAA,GAAG,CACF,wIAAwI,EACxI,cAAc,CACf;IAEH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;MACrEC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,2CAA2C,EAC3CF,UAAU,CACX;IACH;IAEA,OAAO,IAAI,CAAClB,IAAI,CAACwB,GAAG,CAAQ,GAAG3B,gBAAgB,CAAC4B,QAAQ,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC;EACzE;EAEAO,cAAcA,CAACC,SAAiB;IAC9B,MAAMR,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC5B0B,GAAG,CAAC,qBAAqB,EAAEO,SAAS,CAAC,CACrCP,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,CACzCA,GAAG,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAC5CA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CACF,yIAAyI,EACzI,GAAG,CACJ,CACAA,GAAG,CACF,uEAAuE,EACvE,GAAG,CACJ,CACAA,GAAG,CACF,8GAA8G,EAC9G,GAAG,CACJ,CACAA,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC;IAEjD,OAAO,IAAI,CAACpB,IAAI,CACbwB,GAAG,CAAQ,GAAG3B,gBAAgB,CAAC4B,QAAQ,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC,CACtDS,IAAI,CACHhC,GAAG,CAAEiC,QAAa,IAAI;MACpB,MAAMC,cAAc,GAAGD,QAAQ,EAAExB,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MAChD,IAAI,CAACJ,cAAc,CAAC8B,IAAI,CAACD,cAAc,CAAC;MACxC,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,eAAeA,CAAA;IACb,IAAIb,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC1B0B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9C,OAAO,IAAI,CAACpB,IAAI,CAACwB,GAAG,CAAM,GAAG3B,gBAAgB,CAACoC,WAAW,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC1E;EAEAe,aAAaA,CAAA;IACX,IAAIf,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC1B0B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,aAAa,CAAC;IAE3C,OAAO,IAAI,CAACpB,IAAI,CAACwB,GAAG,CAAM,GAAG3B,gBAAgB,CAACoC,WAAW,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC1E;EAEAgB,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACpC,IAAI,CAACqC,MAAM,CAAM,GAAGxC,gBAAgB,CAACU,QAAQ,IAAI6B,EAAE,EAAE,CAAC;EACpE;;;uBA5HWtC,eAAe,EAAAwC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf3C,eAAe;MAAA4C,OAAA,EAAf5C,eAAe,CAAA6C,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}