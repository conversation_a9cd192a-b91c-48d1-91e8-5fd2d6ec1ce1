{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./contacts.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = [\"dt1\"];\nconst _c1 = a0 => ({\n  \"text-orange-600 cursor-pointer font-medium\": true,\n  \"underline\": a0\n});\nconst _c2 = a0 => ({\n  \"text-blue-600 cursor-pointer font-medium\": true,\n  \"underline\": a0\n});\nfunction ContactsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 21)(4, \"div\", 22);\n    i0.ɵɵtext(5, \" Contact ID \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 24)(8, \"div\", 22);\n    i0.ɵɵtext(9, \" Name \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\")(14, \"div\", 22);\n    i0.ɵɵtext(15, \" Department \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"th\")(17, \"div\", 22);\n    i0.ɵɵtext(18, \" Phone \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\")(20, \"div\", 22);\n    i0.ɵɵtext(21, \" E-Mail \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"th\")(23, \"div\", 22);\n    i0.ɵɵtext(24, \" Web Registered \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\")(26, \"div\", 22);\n    i0.ɵɵtext(27, \" Mobile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"th\")(29, \"div\", 22);\n    i0.ɵɵtext(30, \" Account ID \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"th\")(32, \"div\", 22);\n    i0.ɵɵtext(33, \" Status \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ContactsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 20);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 29);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, contact_r3 == null ? null : contact_r3.bp_id))(\"routerLink\", \"/store/contacts/\" + contact_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c2, contact_r3 == null ? null : contact_r3.bp_full_name))(\"routerLink\", \"/store/contacts/\" + contact_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.account_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.department_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.email_address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_contact_extension == null ? null : contact_r3.bp_contact_extension.web_registered) === true ? \"Yes\" : (contact_r3 == null ? null : contact_r3.bp_contact_extension == null ? null : contact_r3.bp_contact_extension.web_registered) === false ? \"No\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.mobile) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.account_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_contact_extension == null ? null : contact_r3.bp_contact_extension.bp_status) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"Loading contacts data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ContactsComponent {\n  constructor(contactsservice, router) {\n    this.contactsservice = contactsservice;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.contacts = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.contactDetails = null;\n    this.cpDepartments = [];\n    this.cpFunctions = [];\n  }\n  ngOnInit() {\n    this.loadDepartment();\n    this.loadFunctions();\n    this.breadcrumbitems = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Contacts',\n      code: 'MC'\n    }, {\n      name: 'Obsolete Contacts',\n      code: 'OC'\n    }];\n    this.selectedActions = {\n      name: 'All',\n      code: 'ALL'\n    };\n  }\n  loadContacts(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.contactsservice.getContacts(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        let contacts = (response?.data || []).map(contact => {\n          const defaultAddress = contact?.addresses?.find(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT'));\n          return {\n            ...contact,\n            account_id: contact?.contact_persons?.[0]?.bp_company_id || '-',\n            account_name: contact?.contact_persons?.[0]?.business_partner_company?.bp_full_name || '-',\n            department_name: contact?.contact_persons?.[0]?.person_func_and_dept?.contact_person_department_name || '-',\n            email_address: defaultAddress?.emails?.[0]?.email_address || '-',\n            phone_number: defaultAddress?.phone_numbers?.[0]?.phone_number || '-'\n          };\n        });\n        // 🔍 Filter for 'Obsolete Prospects' if selected\n        if (this.selectedActions?.code === 'OC') {\n          contacts = contacts.filter(p => p.bp_extension?.bp_status === 'OBSOLETE');\n        }\n        this.contacts = contacts;\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching contacts', error);\n        this.loading = false;\n      }\n    });\n  }\n  loadDepartment() {\n    this.contactsservice.getCPDepartment().pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response && response.data) {\n        this.cpDepartments = response.data.map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n      }\n    });\n  }\n  loadFunctions() {\n    this.contactsservice.getCPFunction().pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response && response.data) {\n        this.cpFunctions = response.data.map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 15\n    };\n    this.loadContacts(dt1State);\n  }\n  signup() {\n    this.router.navigate(['/store/contacts/create']);\n  }\n  onGlobalFilter(table, event) {\n    this.loadContacts({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ContactsComponent_Factory(t) {\n      return new (t || ContactsComponent)(i0.ɵɵdirectiveInject(i1.ContactsService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsComponent,\n      selectors: [[\"app-contacts\"]],\n      viewQuery: function ContactsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 22,\n      vars: 13,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Contact\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"bg-orange-700\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"bp_id\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"bp_id\"], [\"pSortableColumn\", \"bp_full_name\"], [\"field\", \"bp_full_name\"], [1, \"cursor-pointer\"], [3, \"value\"], [3, \"ngClass\", \"routerLink\"], [1, \"border-round-right-lg\"], [\"colspan\", \"11\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function ContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function ContactsComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(17);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function ContactsComponent_Template_p_dropdown_onChange_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ContactsComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.signup());\n          });\n          i0.ɵɵelementStart(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"p-table\", 15, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function ContactsComponent_Template_p_table_onLazyLoad_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadContacts($event));\n          });\n          i0.ɵɵtemplate(18, ContactsComponent_ng_template_18_Template, 34, 0, \"ng-template\", 16)(19, ContactsComponent_ng_template_19_Template, 23, 19, \"ng-template\", 17)(20, ContactsComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18)(21, ContactsComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.contacts)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i3.NgClass, i2.RouterLink, i4.Breadcrumb, i5.PrimeTemplate, i6.Dropdown, i7.Table, i7.SortableColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "contact_r3", "ɵɵpureFunction1", "_c1", "bp_id", "ɵɵtextInterpolate1", "_c2", "bp_full_name", "account_name", "department_name", "phone_number", "email_address", "bp_contact_extension", "web_registered", "mobile", "account_id", "bp_status", "ContactsComponent", "constructor", "contactsservice", "router", "unsubscribe$", "contacts", "totalRecords", "loading", "globalSearchTerm", "contactDetails", "cpDepartments", "cpFunctions", "ngOnInit", "loadDepartment", "loadFunctions", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "selectedActions", "loadContacts", "event", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getContacts", "subscribe", "next", "response", "data", "map", "contact", "defaultAddress", "addresses", "find", "address", "address_usages", "some", "usage", "address_usage", "contact_persons", "bp_company_id", "business_partner_company", "person_func_and_dept", "contact_person_department_name", "emails", "phone_numbers", "filter", "p", "bp_extension", "meta", "pagination", "total", "error", "console", "getCPDepartment", "pipe", "item", "description", "value", "getCPFunction", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "signup", "navigate", "onGlobalFilter", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ContactsService", "i2", "Router", "selectors", "viewQuery", "ContactsComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "ContactsComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "ContactsComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "ContactsComponent_Template_p_dropdown_ngModelChange_10_listener", "ContactsComponent_Template_p_dropdown_onChange_10_listener", "ContactsComponent_Template_button_click_11_listener", "ContactsComponent_Template_p_table_onLazyLoad_16_listener", "ɵɵtemplate", "ContactsComponent_ng_template_18_Template", "ContactsComponent_ng_template_19_Template", "ContactsComponent_ng_template_20_Template", "ContactsComponent_ng_template_21_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Table } from 'primeng/table';\r\nimport { ContactsService } from './contacts.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n@Component({\r\n  selector: 'app-contacts',\r\n  templateUrl: './contacts.component.html',\r\n  styleUrl: './contacts.component.scss',\r\n})\r\nexport class ContactsComponent implements OnInit {\r\n  @ViewChild('dt1') dt1!: Table;\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n  public contacts: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public contactDetails: any = null;\r\n  public cpDepartments: { name: string; value: string }[] = [];\r\n  public cpFunctions: { name: string; value: string }[] = [];\r\n\r\n  constructor(\r\n    private contactsservice: ContactsService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadDepartment();\r\n    this.loadFunctions();\r\n    this.breadcrumbitems = [\r\n      { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Contacts', code: 'MC' },\r\n      { name: 'Obsolete Contacts', code: 'OC' },\r\n    ];\r\n    this.selectedActions = { name: 'All', code: 'ALL' };\r\n  }\r\n\r\n  loadContacts(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.contactsservice\r\n      .getContacts(page, pageSize, sortField, sortOrder, this.globalSearchTerm)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          let contacts = (response?.data || []).map((contact: any) => {\r\n            const defaultAddress = contact?.addresses?.find((address: any) =>\r\n              address?.address_usages?.some(\r\n                (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n              )\r\n            );\r\n\r\n            return {\r\n              ...contact,\r\n              account_id: contact?.contact_persons?.[0]?.bp_company_id || '-',\r\n              account_name:\r\n                contact?.contact_persons?.[0]?.business_partner_company\r\n                  ?.bp_full_name || '-',\r\n              department_name:\r\n                contact?.contact_persons?.[0]?.person_func_and_dept\r\n                  ?.contact_person_department_name || '-',\r\n              email_address: defaultAddress?.emails?.[0]?.email_address || '-',\r\n              phone_number:\r\n                defaultAddress?.phone_numbers?.[0]?.phone_number || '-',\r\n            };\r\n          });\r\n\r\n          // 🔍 Filter for 'Obsolete Prospects' if selected\r\n          if (this.selectedActions?.code === 'OC') {\r\n            contacts = contacts.filter(\r\n              (p: any) => p.bp_extension?.bp_status === 'OBSOLETE'\r\n            );\r\n          }\r\n\r\n          this.contacts = contacts;\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching contacts', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  public loadDepartment(): void {\r\n    this.contactsservice\r\n      .getCPDepartment()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response && response.data) {\r\n          this.cpDepartments = response.data.map((item: any) => ({\r\n            name: item.description,\r\n            value: item.code,\r\n          }));\r\n        }\r\n      });\r\n  }\r\n\r\n  public loadFunctions(): void {\r\n    this.contactsservice\r\n      .getCPFunction()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response && response.data) {\r\n          this.cpFunctions = response.data.map((item: any) => ({\r\n            name: item.description,\r\n            value: item.code,\r\n          }));\r\n        }\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 15,\r\n    };\r\n    this.loadContacts(dt1State);\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/contacts/create']);\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadContacts({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" placeholder=\"Search Contact\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                        (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange()\" optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold'\" />\r\n            <button type=\"button\" (click)=\"signup()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component bg-orange-700 w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"contacts\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadContacts($event)\"\r\n            [loading]=\"loading\" styleClass=\"\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Contact ID\r\n                            <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_full_name\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Name\r\n                            <p-sortIcon field=\"bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        Account\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Department\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Phone\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            E-Mail\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Web Registered\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Mobile\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Account ID\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Status\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true, \r\n                        'underline': contact?.bp_id\r\n                      }\" [routerLink]=\"'/store/contacts/' + contact.bp_id\">\r\n                        {{ contact?.bp_id || '-' }}\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-blue-600 cursor-pointer font-medium': true, \r\n                        'underline': contact?.bp_full_name\r\n                      }\" [routerLink]=\"'/store/contacts/' + contact.bp_id\">\r\n                        {{ contact?.bp_full_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.account_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.department_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.phone_number || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.email_address || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.bp_contact_extension?.web_registered === true ? 'Yes' :\r\n                        contact?.bp_contact_extension?.web_registered === false ? 'No' : '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.mobile || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.account_id || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        {{ contact?.bp_contact_extension?.bp_status || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"11\" class=\"border-round-left-lg pl-3\">No contacts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"11\" class=\"border-round-left-lg pl-3\">Loading contacts data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;IC0BrBC,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAGDH,EADJ,CAAAC,cAAA,aAA4B,cACa;IACjCD,EAAA,CAAAI,MAAA,mBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAAuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAmC,cACM;IACjCD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,iBACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,oBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,eACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,gBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,wBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,gBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,oBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,gBACJ;IAERJ,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;IAKDH,EADJ,CAAAC,cAAA,aAA2B,aACkC;IACrDD,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGuD;IACnDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGuD;IACnDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IAEJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IAvCoBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,UAAA,UAAAC,UAAA,CAAiB;IAElCP,EAAA,CAAAK,SAAA,EAGA;IAACL,EAHD,CAAAM,UAAA,YAAAN,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAF,UAAA,kBAAAA,UAAA,CAAAG,KAAA,EAGA,oCAAAH,UAAA,CAAAG,KAAA,CAAkD;IAClDV,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAG,KAAA,cACJ;IACIV,EAAA,CAAAK,SAAA,EAGA;IAACL,EAHD,CAAAM,UAAA,YAAAN,EAAA,CAAAQ,eAAA,KAAAI,GAAA,EAAAL,UAAA,kBAAAA,UAAA,CAAAM,YAAA,EAGA,oCAAAN,UAAA,CAAAG,KAAA,CAAkD;IAClDV,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAM,YAAA,cACJ;IAEIb,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAO,YAAA,cACJ;IAEId,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAQ,eAAA,cACJ;IAEIf,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAS,YAAA,cACJ;IAEIhB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAU,aAAA,cACJ;IAEIjB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAW,oBAAA,kBAAAX,UAAA,CAAAW,oBAAA,CAAAC,cAAA,sBAAAZ,UAAA,kBAAAA,UAAA,CAAAW,oBAAA,kBAAAX,UAAA,CAAAW,oBAAA,CAAAC,cAAA,8BAEJ;IAEInB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAa,MAAA,cACJ;IAEIpB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAc,UAAA,cACJ;IAEIrB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAJ,UAAA,kBAAAA,UAAA,CAAAW,oBAAA,kBAAAX,UAAA,CAAAW,oBAAA,CAAAI,SAAA,cACJ;;;;;IAKAtB,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACzEJ,EADyE,CAAAG,YAAA,EAAK,EACzE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACmD;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IAC1FJ,EAD0F,CAAAG,YAAA,EAAK,EAC1F;;;AD3HrB,OAAM,MAAOoB,iBAAiB;EAe5BC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAfR,KAAAC,YAAY,GAAG,IAAI7B,OAAO,EAAQ;IAKnC,KAAA8B,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,aAAa,GAAsC,EAAE;IACrD,KAAAC,WAAW,GAAsC,EAAE;EAKvD;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,CACvD;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IACrD,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAI,CAAE,EACnC;MAAED,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC1C;IACD,IAAI,CAACC,eAAe,GAAG;MAAEF,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE;EACrD;EAEAE,YAAYA,CAACC,KAAU;IACrB,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,MAAMmB,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,MAAME,SAAS,GAAGL,KAAK,CAACK,SAAS;IACjC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAEjC,IAAI,CAAC7B,eAAe,CACjB8B,WAAW,CAACN,IAAI,EAAEG,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAE,IAAI,CAACvB,gBAAgB,CAAC,CACxEyB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI9B,QAAQ,GAAG,CAAC8B,QAAQ,EAAEC,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAEC,OAAY,IAAI;UACzD,MAAMC,cAAc,GAAGD,OAAO,EAAEE,SAAS,EAAEC,IAAI,CAAEC,OAAY,IAC3DA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF;UAED,OAAO;YACL,GAAGR,OAAO;YACVxC,UAAU,EAAEwC,OAAO,EAAES,eAAe,GAAG,CAAC,CAAC,EAAEC,aAAa,IAAI,GAAG;YAC/DzD,YAAY,EACV+C,OAAO,EAAES,eAAe,GAAG,CAAC,CAAC,EAAEE,wBAAwB,EACnD3D,YAAY,IAAI,GAAG;YACzBE,eAAe,EACb8C,OAAO,EAAES,eAAe,GAAG,CAAC,CAAC,EAAEG,oBAAoB,EAC/CC,8BAA8B,IAAI,GAAG;YAC3CzD,aAAa,EAAE6C,cAAc,EAAEa,MAAM,GAAG,CAAC,CAAC,EAAE1D,aAAa,IAAI,GAAG;YAChED,YAAY,EACV8C,cAAc,EAAEc,aAAa,GAAG,CAAC,CAAC,EAAE5D,YAAY,IAAI;WACvD;QACH,CAAC,CAAC;QAEF;QACA,IAAI,IAAI,CAAC8B,eAAe,EAAED,IAAI,KAAK,IAAI,EAAE;UACvCjB,QAAQ,GAAGA,QAAQ,CAACiD,MAAM,CACvBC,CAAM,IAAKA,CAAC,CAACC,YAAY,EAAEzD,SAAS,KAAK,UAAU,CACrD;QACH;QAEA,IAAI,CAACM,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACC,YAAY,GAAG6B,QAAQ,EAAEsB,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqD,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACrD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEOM,cAAcA,CAAA;IACnB,IAAI,CAACX,eAAe,CACjB4D,eAAe,EAAE,CACjBC,IAAI,CAACvF,SAAS,CAAC,IAAI,CAAC4B,YAAY,CAAC,CAAC,CAClC6B,SAAS,CAAEE,QAAa,IAAI;MAC3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7B,IAAI,CAAC1B,aAAa,GAAGyB,QAAQ,CAACC,IAAI,CAACC,GAAG,CAAE2B,IAAS,KAAM;UACrD3C,IAAI,EAAE2C,IAAI,CAACC,WAAW;UACtBC,KAAK,EAAEF,IAAI,CAAC1C;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACN;EAEOR,aAAaA,CAAA;IAClB,IAAI,CAACZ,eAAe,CACjBiE,aAAa,EAAE,CACfJ,IAAI,CAACvF,SAAS,CAAC,IAAI,CAAC4B,YAAY,CAAC,CAAC,CAClC6B,SAAS,CAAEE,QAAa,IAAI;MAC3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7B,IAAI,CAACzB,WAAW,GAAGwB,QAAQ,CAACC,IAAI,CAACC,GAAG,CAAE2B,IAAS,KAAM;UACnD3C,IAAI,EAAE2C,IAAI,CAACC,WAAW;UACtBC,KAAK,EAAEF,IAAI,CAAC1C;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACN;EAEA8C,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvD5C,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACJ,YAAY,CAAC6C,QAAQ,CAAC;EAC7B;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAC,cAAcA,CAACC,KAAY,EAAElD,KAAY;IACvC,IAAI,CAACD,YAAY,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC3C;EAEAgD,WAAWA,CAAA;IACT,IAAI,CAACxE,YAAY,CAAC8B,IAAI,EAAE;IACxB,IAAI,CAAC9B,YAAY,CAACyE,QAAQ,EAAE;EAC9B;;;uBAvIW7E,iBAAiB,EAAAvB,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBlF,iBAAiB;MAAAmF,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCdtB7G,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAG4E;UAFjDD,EAAA,CAAA+G,gBAAA,2BAAAC,0DAAAC,MAAA;YAAAjH,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAAnH,EAAA,CAAAoH,kBAAA,CAAAN,GAAA,CAAA/E,gBAAA,EAAAkF,MAAA,MAAAH,GAAA,CAAA/E,gBAAA,GAAAkF,MAAA;YAAA,OAAAjH,EAAA,CAAAqH,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UAClFjH,EAAA,CAAAsH,UAAA,mBAAAC,kDAAAN,MAAA;YAAAjH,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAA,MAAAK,MAAA,GAAAxH,EAAA,CAAAyH,WAAA;YAAA,OAAAzH,EAAA,CAAAqH,WAAA,CAASP,GAAA,CAAAb,cAAA,CAAAuB,MAAA,EAAAP,MAAA,CAA2B;UAAA,EAAC;UADzCjH,EAAA,CAAAG,YAAA,EAEyG;UACzGH,EAAA,CAAAE,SAAA,YAA4B;UAEpCF,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAC,cAAA,sBACwF;UADxDD,EAAA,CAAA+G,gBAAA,2BAAAW,gEAAAT,MAAA;YAAAjH,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAAnH,EAAA,CAAAoH,kBAAA,CAAAN,GAAA,CAAAhE,eAAA,EAAAmE,MAAA,MAAAH,GAAA,CAAAhE,eAAA,GAAAmE,MAAA;YAAA,OAAAjH,EAAA,CAAAqH,WAAA,CAAAJ,MAAA;UAAA,EAA6B;UAACjH,EAAA,CAAAsH,UAAA,sBAAAK,2DAAA;YAAA3H,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAA,OAAAnH,EAAA,CAAAqH,WAAA,CAAYP,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAA3F3F,EAAA,CAAAG,YAAA,EACwF;UACxFH,EAAA,CAAAC,cAAA,kBACuI;UADjHD,EAAA,CAAAsH,UAAA,mBAAAM,oDAAA;YAAA5H,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAA,OAAAnH,EAAA,CAAAqH,WAAA,CAASP,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAEpC/F,EAAA,CAAAC,cAAA,gBAAgD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAGW;UAF4BD,EAAA,CAAAsH,UAAA,wBAAAO,0DAAAZ,MAAA;YAAAjH,EAAA,CAAAkH,aAAA,CAAAC,GAAA;YAAA,OAAAnH,EAAA,CAAAqH,WAAA,CAAcP,GAAA,CAAA/D,YAAA,CAAAkE,MAAA,CAAoB;UAAA,EAAC;UA+GzFjH,EA5GA,CAAA8H,UAAA,KAAAC,yCAAA,2BAAgC,KAAAC,yCAAA,4BA2DU,KAAAC,yCAAA,0BA4CJ,KAAAC,yCAAA,0BAKD;UAOjDlI,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UA5IoBH,EAAA,CAAAK,SAAA,GAAyB;UAAeL,EAAxC,CAAAM,UAAA,UAAAwG,GAAA,CAAAxE,eAAA,CAAyB,SAAAwE,GAAA,CAAArE,IAAA,CAAc,uCAAuC;UAM5BzC,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAmI,gBAAA,YAAArB,GAAA,CAAA/E,gBAAA,CAA8B;UAMlF/B,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAM,UAAA,YAAAwG,GAAA,CAAAnE,OAAA,CAAmB;UAAC3C,EAAA,CAAAmI,gBAAA,YAAArB,GAAA,CAAAhE,eAAA,CAA6B;UACzD9C,EAAA,CAAAM,UAAA,kFAAiF;UAS3EN,EAAA,CAAAK,SAAA,GAAkB;UACuDL,EADzE,CAAAM,UAAA,UAAAwG,GAAA,CAAAlF,QAAA,CAAkB,YAAyB,YAAAkF,GAAA,CAAAhF,OAAA,CAClC,mBAAiC,iBAAAgF,GAAA,CAAAjF,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}