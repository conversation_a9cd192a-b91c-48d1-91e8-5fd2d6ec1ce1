{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./service/app.layout.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"searchinput\"];\nfunction AppBreadcrumbComponent_ng_template_3_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 4);\n    i0.ɵɵtext(1, \" / \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppBreadcrumbComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AppBreadcrumbComponent_ng_template_3_li_2_Template, 2, 0, \"li\", 3);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const last_r2 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r2);\n  }\n}\nexport let AppBreadcrumbComponent = /*#__PURE__*/(() => {\n  class AppBreadcrumbComponent {\n    constructor(router, layoutService) {\n      this.router = router;\n      this.layoutService = layoutService;\n      this._breadcrumbs$ = new BehaviorSubject([]);\n      this.breadcrumbs$ = this._breadcrumbs$.asObservable();\n      this.searchActive = false;\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        const root = this.router.routerState.snapshot.root;\n        const breadcrumbs = [];\n        this.addBreadcrumb(root, [], breadcrumbs);\n        this._breadcrumbs$.next(breadcrumbs);\n      });\n    }\n    activateSearch() {\n      this.searchActive = true;\n      setTimeout(() => {\n        this.searchInput.nativeElement.focus();\n      }, 100);\n    }\n    deactivateSearch() {\n      this.searchActive = false;\n    }\n    onConfigButtonClick() {\n      this.layoutService.showConfigSidebar();\n    }\n    onSidebarButtonClick() {\n      this.layoutService.showSidebar();\n    }\n    addBreadcrumb(route, parentUrl, breadcrumbs) {\n      const routeUrl = parentUrl.concat(route.url.map(url => url.path));\n      const breadcrumb = route.data['breadcrumb'];\n      const parentBreadcrumb = route.parent && route.parent.data ? route.parent.data['breadcrumb'] : null;\n      if (breadcrumb && breadcrumb !== parentBreadcrumb) {\n        breadcrumbs.push({\n          label: route.data['breadcrumb'],\n          url: '/' + routeUrl.join('/')\n        });\n      }\n      if (route.firstChild) {\n        this.addBreadcrumb(route.firstChild, routeUrl, breadcrumbs);\n      }\n    }\n    static {\n      this.ɵfac = function AppBreadcrumbComponent_Factory(t) {\n        return new (t || AppBreadcrumbComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LayoutService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppBreadcrumbComponent,\n        selectors: [[\"app-breadcrumb\"]],\n        viewQuery: function AppBreadcrumbComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          }\n        },\n        decls: 5,\n        vars: 3,\n        consts: [[1, \"layout-breadcrumb\", \"flex\", \"align-items-center\", \"relative\", \"h-3rem\"], [1, \"relative\", \"z-2\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"layout-breadcrumb-chevron\", 4, \"ngIf\"], [1, \"layout-breadcrumb-chevron\"]],\n        template: function AppBreadcrumbComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\")(2, \"ol\", 1);\n            i0.ɵɵtemplate(3, AppBreadcrumbComponent_ng_template_3_Template, 3, 2, \"ng-template\", 2);\n            i0.ɵɵpipe(4, \"async\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(4, 1, ctx.breadcrumbs$));\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i3.AsyncPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppBreadcrumbComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}