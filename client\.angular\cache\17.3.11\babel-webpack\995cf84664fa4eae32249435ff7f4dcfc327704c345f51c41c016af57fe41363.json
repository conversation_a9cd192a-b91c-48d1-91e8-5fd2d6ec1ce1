{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nfunction SalesQuotesAiInsightsComponent_ng_template_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 31);\n    i0.ɵɵtext(2, \"Sales Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Distribution Channel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Division\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Sales Office\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 32);\n    i0.ɵɵtext(14, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesQuotesAiInsightsComponent_ng_template_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 32);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/sales-quotes/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesOrganization, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.DistributionChannel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Division, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesOffice, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesGroup, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Currency, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Action, \" \");\n  }\n}\nexport let SalesQuotesAiInsightsComponent = /*#__PURE__*/(() => {\n  class SalesQuotesAiInsightsComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.tableData = [{\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }, {\n        SalesOrganization: 'Organization name',\n        DistributionChannel: 'Direct Sales',\n        Division: 'Division 1',\n        SalesOffice: 'Sales Office',\n        SalesGroup: 'Development Partner',\n        Currency: 'USD',\n        Action: 'Active'\n      }];\n    }\n    static {\n      this.ɵfac = function SalesQuotesAiInsightsComponent_Factory(t) {\n        return new (t || SalesQuotesAiInsightsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesQuotesAiInsightsComponent,\n        selectors: [[\"app-sales-quotes-ai-insights\"]],\n        decls: 92,\n        vars: 8,\n        consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"mt-6\", \"mb-1\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-3\"], [\"type\", \"button\", \"icon\", \"pi pi-check\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-3rem\", \"w-3rem\"], [1, \"material-symbols-rounded\", \"text-primary\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:col-9\", \"md:col-9\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"icon\", \"pi pi-angle-left\", 1, \"-ml-5\", 3, \"rounded\", \"outlined\", \"styleClass\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\", 3, \"routerLink\"]],\n        template: function SalesQuotesAiInsightsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h5\", 6);\n            i0.ɵɵtext(7, \"JS\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"h5\", 8);\n            i0.ɵɵtext(10, \"SNJYA Customer Sprint 2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"ul\", 9)(12, \"li\", 10)(13, \"span\", 11);\n            i0.ɵɵtext(14, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15, \" : 05545SD585\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"li\", 10)(17, \"span\", 11);\n            i0.ɵɵtext(18, \"S4/HANA ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(19, \" : 152ASD5585\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"li\", 10)(21, \"span\", 11);\n            i0.ɵɵtext(22, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(23, \" : Adam Smith\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"li\", 10)(25, \"span\", 11);\n            i0.ɵɵtext(26, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(27, \" : Ben Jacobs\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(28, \"div\", 12)(29, \"button\", 13)(30, \"i\", 14);\n            i0.ɵɵtext(31, \"call\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"button\", 13)(33, \"i\", 14);\n            i0.ɵɵtext(34, \"location_on\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"button\", 13)(36, \"i\", 14);\n            i0.ɵɵtext(37, \"email\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"button\", 13)(39, \"i\", 14);\n            i0.ɵɵtext(40, \"language\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"button\", 13)(42, \"i\", 14);\n            i0.ɵɵtext(43, \"edit_square\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(44, \"div\", 15)(45, \"ul\", 16)(46, \"li\", 17)(47, \"span\", 18)(48, \"i\", 19);\n            i0.ɵɵtext(49, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(50, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"span\", 20);\n            i0.ɵɵtext(52, \"3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA 60532.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"li\", 17)(54, \"span\", 18)(55, \"i\", 19);\n            i0.ɵɵtext(56, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(57, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"span\", 20);\n            i0.ɵɵtext(59, \"******-423-5926\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"li\", 17)(61, \"span\", 18)(62, \"i\", 19);\n            i0.ɵɵtext(63, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(64, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"span\", 20);\n            i0.ɵɵtext(66, \"******-423-5926\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"li\", 17)(68, \"span\", 18)(69, \"i\", 19);\n            i0.ɵɵtext(70, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(71, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"span\", 20);\n            i0.ɵɵtext(73, \"<EMAIL>\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"li\", 17)(75, \"span\", 18)(76, \"i\", 19);\n            i0.ɵɵtext(77, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(78, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"span\", 20);\n            i0.ɵɵtext(80, \"www.asardigital.com\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(81, \"div\", 21)(82, \"div\", 22)(83, \"div\", 23);\n            i0.ɵɵelement(84, \"p-button\", 24);\n            i0.ɵɵelementStart(85, \"h4\", 25);\n            i0.ɵɵtext(86, \"AI Insights\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(87, \"p-button\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"div\", 27)(89, \"p-table\", 28);\n            i0.ɵɵtemplate(90, SalesQuotesAiInsightsComponent_ng_template_90_Template, 15, 0, \"ng-template\", 29)(91, SalesQuotesAiInsightsComponent_ng_template_91_Template, 15, 8, \"ng-template\", 30);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(84);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.RouterLink, i2.Table, i3.PrimeTemplate, i4.Button]\n      });\n    }\n  }\n  return SalesQuotesAiInsightsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}