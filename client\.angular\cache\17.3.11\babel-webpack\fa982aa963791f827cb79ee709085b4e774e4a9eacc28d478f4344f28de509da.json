{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AiInsightsRoutingModule } from './ai-insights-routing.module';\nimport { AiInsightsComponent } from './ai-insights.component';\nimport { FormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport class AiInsightsModule {\n  static {\n    this.ɵfac = function AiInsightsModule_Factory(t) {\n      return new (t || AiInsightsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AiInsightsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AiInsightsRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AiInsightsModule, {\n    declarations: [AiInsightsComponent],\n    imports: [CommonModule, AiInsightsRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AiInsightsRoutingModule", "AiInsightsComponent", "FormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "AiInsightsModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\ai-insights\\ai-insights.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AiInsightsRoutingModule } from './ai-insights-routing.module';\r\nimport { AiInsightsComponent } from './ai-insights.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AiInsightsComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AiInsightsRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule\r\n  ]\r\n})\r\nexport class AiInsightsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;;AAqB/C,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAbzBZ,YAAY,EACZC,uBAAuB,EACvBE,WAAW,EACXO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdG,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;IAAA;EAAA;;;2EAGNG,gBAAgB;IAAAC,YAAA,GAhBzBX,mBAAmB;IAAAY,OAAA,GAGnBd,YAAY,EACZC,uBAAuB,EACvBE,WAAW,EACXO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdG,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}