{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ca = moment.defineLocale('ca', {\n    months: {\n      standalone: 'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split('_'),\n      format: \"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre\".split('_'),\n      isFormat: /D[oD]?(\\s)+MMMM/\n    },\n    monthsShort: 'gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split('_'),\n    weekdaysShort: 'dg._dl._dt._dc._dj._dv._ds.'.split('_'),\n    weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [de] YYYY',\n      ll: 'D MMM YYYY',\n      LLL: 'D MMMM [de] YYYY [a les] H:mm',\n      lll: 'D MMM YYYY, H:mm',\n      LLLL: 'dddd D MMMM [de] YYYY [a les] H:mm',\n      llll: 'ddd D MMM YYYY, H:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[avui a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextDay: function () {\n        return '[demà a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastDay: function () {\n        return '[ahir a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastWeek: function () {\n        return '[el] dddd [passat a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: \"d'aquí %s\",\n      past: 'fa %s',\n      s: 'uns segons',\n      ss: '%d segons',\n      m: 'un minut',\n      mm: '%d minuts',\n      h: 'una hora',\n      hh: '%d hores',\n      d: 'un dia',\n      dd: '%d dies',\n      M: 'un mes',\n      MM: '%d mesos',\n      y: 'un any',\n      yy: '%d anys'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n    ordinal: function (number, period) {\n      var output = number === 1 ? 'r' : number === 2 ? 'n' : number === 3 ? 'r' : number === 4 ? 't' : 'è';\n      if (period === 'w' || period === 'W') {\n        output = 'a';\n      }\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ca;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}