{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport let ActivitiesService = /*#__PURE__*/(() => {\n  class ActivitiesService {\n    constructor(http, authservice) {\n      this.http = http;\n      this.authservice = authservice;\n      this.activitySubject = new BehaviorSubject(null);\n      this.activity = this.activitySubject.asObservable();\n    }\n    createNote(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n        data\n      });\n    }\n    createActivity(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`, data);\n    }\n    createFollowup(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`, data);\n    }\n    createOpportuniyFollowup(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION}`, data);\n    }\n    createInvolvedParty(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {\n        data\n      });\n    }\n    createRelatedItem(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}`, {\n        data\n      });\n    }\n    updateActivity(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n        data\n      });\n    }\n    updateNote(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n        data\n      });\n    }\n    updateActivityStatus(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n        data\n      });\n    }\n    deleteNote(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n    }\n    deleteInvolvedParty(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`);\n    }\n    deleteFollowupItem(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}/${id}`);\n    }\n    getActivityDropdownOptions(type) {\n      const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getActivities(page, pageSize, sortField, sortOrder, searchTerm) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,start_date,end_date,category');\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n        params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n        params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n      }\n      return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n        params\n      });\n    }\n    getSalesCall(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note').set('populate[business_partner][populate][addresses][fields][0]', 'region').set('populate[business_partner][populate][addresses][fields][1]', 'country').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      } else {\n        // Default sort by updatedAt descending\n        params = params.set('sort', 'updatedAt:desc');\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n        params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n        params = params.set('filters[$or][2][phone_call_category][$containsi]', searchTerm);\n        params = params.set('filters[$or][3][brand][$containsi]', searchTerm);\n        params = params.set('filters[$or][4][business_partner][bp_full_name][$containsi]', searchTerm);\n      }\n      if (filter) {\n        if (filter === 'MSCT') {\n          const today = new Date();\n          const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n          const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n          params = params.set('filters[createdAt][$gte]', startOfDay).set('filters[createdAt][$lte]', endOfDay);\n        } else if (filter === 'MSCTW') {\n          const now = new Date();\n          // Get the start of the week (Monday)\n          const startOfWeek = new Date(now);\n          const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\n          const diffToMonday = day === 0 ? -6 : 1 - day;\n          startOfWeek.setDate(now.getDate() + diffToMonday);\n          startOfWeek.setHours(0, 0, 0, 0);\n          // Get the end of the week (Sunday)\n          const endOfWeek = new Date(startOfWeek);\n          endOfWeek.setDate(startOfWeek.getDate() + 6);\n          endOfWeek.setHours(23, 59, 59, 999);\n          const startISO = startOfWeek.toISOString();\n          const endISO = endOfWeek.toISOString();\n          params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n        } else if (filter === 'MSCTM') {\n          const now = new Date();\n          // Start of the month: 1st day at 00:00:00\n          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n          // End of the month: last day at 23:59:59.999\n          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n          const startISO = startOfMonth.toISOString();\n          const endISO = endOfMonth.toISOString();\n          params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n        } else if (filter === 'MCSC') {\n          params = params.set('filters[activity_status][$eq]', '3');\n        } else if (filter === 'MSC') {\n          const email = this.authservice.getUserEmail();\n          if (email) {\n            params = params.set(`filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n          }\n        }\n      }\n      return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n        params\n      });\n    }\n    getActivityByID(activityId) {\n      const params = new HttpParams().set('filters[activity_id][$eq]', activityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner][fields][1]', 'bp_id').set('populate[business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][4]', 'country').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]', 'website_url').set('populate[involved_parties][populate][business_partner][fields][0]', 'bp_full_name').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][0]', 'city_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][1]', 'country').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][2]', 'house_number').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][3]', 'region').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][4]', 'street_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_contact][populate][contact_persons][fields][0]', 'bp_company_id').set('populate[notes][populate]', '*').set('populate[opportunity_followups][populate]', '*');\n      return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n        params\n      }).pipe(map(response => {\n        const activityDetails = response?.data[0] || null;\n        this.activitySubject.next(activityDetails);\n        return response;\n      }));\n    }\n    getPartners(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]=*`, {\n        params\n      }).pipe(map(response => (response?.data || []).map(item => {\n        const contact = item?.addresses?.[0];\n        const email = contact?.emails?.[0]?.email_address || '';\n        const mobile = (contact?.phone_numbers || []).filter(item => item.phone_number_type === '3').map(item => item.phone_number);\n        return {\n          bp_id: item?.bp_id || '',\n          bp_full_name: item?.bp_full_name || '',\n          email: email,\n          mobile: mobile\n        };\n      })));\n    }\n    getPartnersContact(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n        params\n      }).pipe(map(response => {\n        return (response?.data || []).map(item => {\n          const contact = item?.business_partner_person?.addresses?.[0];\n          const email = contact?.emails?.[0]?.email_address || '';\n          const mobile = (contact?.phone_numbers || []).filter(ph => ph.phone_number_type === '3').map(ph => ph.phone_number).join(', '); // Combine multiple numbers into one string\n          return {\n            bp_id: item?.business_partner_person?.bp_id || '',\n            bp_full_name: item?.business_partner_person?.bp_full_name || '',\n            email: email,\n            mobile: mobile\n          };\n        });\n      }));\n    }\n    getEmailwisePartner() {\n      const email = this.authservice.getUserEmail() ?? '';\n      let params = new HttpParams().set('filters[business_partner_person][addresses][emails][email_address][$containsi]', email).set('populate[business_partner_person][populate][addresses][populate]', '*');\n      return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n        params\n      }).pipe(map(response => {\n        const firstItem = response?.data?.[0]?.business_partner_person;\n        return firstItem?.bp_id || null;\n      }));\n    }\n    getActivityCodeWise(params) {\n      return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n        params\n      });\n    }\n    getActivity(partnerId) {\n      let params = new HttpParams().set('filters[main_account_party_id][$eq]', partnerId).set('fields', 'subject,activity_id,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_organizer][fields][0]', 'bp_full_name').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note');\n      return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n        params\n      });\n    }\n    getTodayRange() {\n      const today = new Date();\n      const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n      const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n      return {\n        start,\n        end\n      };\n    }\n    getThisWeekRange() {\n      const now = new Date();\n      const startOfWeek = new Date(now);\n      const day = now.getDay();\n      const diffToMonday = day === 0 ? -6 : 1 - day;\n      startOfWeek.setDate(now.getDate() + diffToMonday);\n      startOfWeek.setHours(0, 0, 0, 0);\n      const endOfWeek = new Date(startOfWeek);\n      endOfWeek.setDate(startOfWeek.getDate() + 6);\n      endOfWeek.setHours(23, 59, 59, 999);\n      return {\n        start: startOfWeek.toISOString(),\n        end: endOfWeek.toISOString()\n      };\n    }\n    getThisMonthRange() {\n      const now = new Date();\n      const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n      return {\n        start: start.toISOString(),\n        end: end.toISOString()\n      };\n    }\n    static {\n      this.ɵfac = function ActivitiesService_Factory(t) {\n        return new (t || ActivitiesService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ActivitiesService,\n        factory: ActivitiesService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ActivitiesService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}