<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading mb-2 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Overview</h4>
        <p-button [label]="isEditMode ? 'Close' : 'Edit'" [icon]="!isEditMode ? 'pi pi-pencil':''" iconPos="right"
            class="ml-auto" [styleClass]="'w-5rem font-semibold px-3'" (click)="toggleEdit()" [rounded]="true" />
    </div>
    <div *ngIf="!isEditMode" class="p-fluid p-formgrid grid m-0">
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">badge</span> ID
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ overviewDetails?.activity_id || '-'
                    }}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">subject</span> Subject
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{overviewDetails?.subject || '-'}}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">description</span>Transaction Type
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ getLabelFromDropdown('activityDocumentType',
                    overviewDetails?.document_type) || '-'
                    }}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">account_circle</span> Account
                </label>
                <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
                    <a [href]="'/#/store/account/' + overviewDetails?.business_partner?.documentId + '/overview'"
                        style="text-decoration: none; color: inherit;" target="_blank">
                        {{ overviewDetails?.business_partner?.bp_full_name || '-' }}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">person</span> Contact
                </label>
                <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
                    <a [href]="'/#/store/contacts/' + overviewDetails?.business_partner_contact?.contact_persons?.[0]?.documentId + '/overview'"
                        style="text-decoration: none; color: inherit;" target="_blank">
                        {{ overviewDetails?.business_partner_contact?.bp_full_name || '-' }}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">category</span> Category
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{
                    getLabelFromDropdown('activityCategory',overviewDetails?.task_category)
                    || '-'}}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">person</span> Processor
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{
                    overviewDetails?.business_partner_processor?.bp_full_name
                    || '-'}}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">schedule</span> Create Date
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ overviewDetails?.start_date ?
                    (overviewDetails?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-'
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">schedule</span> Expected Decision Date
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ overviewDetails?.end_date ?
                    (overviewDetails?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-'
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">flag</span> Priority
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ getLabelFromDropdown('activityPriority',
                    overviewDetails?.priority) || '-' }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">check_circle</span> Status
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ getLabelFromDropdown('activityStatus',
                    overviewDetails?.activity_status) || '-' }}
                </div>
            </div>
        </div>
    </div>
    <form *ngIf="isEditMode" [formGroup]="TaskOverviewForm">
        <div class="p-fluid p-formgrid grid m-0">
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">account_circle</span>Account
                        <span class="text-red-500">*</span>
                    </label>
                    <ng-select pInputText [items]="accounts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="accountLoading" [minTermLength]="0"
                        formControlName="main_account_party_id" [typeahead]="accountInput$" [maxSelectedItems]="10"
                        appendTo="body" [compareWith]="compareById"
                        [ngClass]="{ 'is-invalid': submitted && f['main_account_party_id'].errors }"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.bp_id }}</span>
                            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                        </ng-template>
                    </ng-select>
                    <div *ngIf="submitted && f['main_account_party_id'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['main_account_party_id'].errors &&
                                f['main_account_party_id'].errors['required']
                              ">
                            Account is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">person</span>Contact
                        <span class="text-red-500">*</span>
                    </label>
                    <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="contactLoading" [minTermLength]="0"
                        formControlName="main_contact_party_id" [typeahead]="contactInput$" [maxSelectedItems]="10"
                        appendTo="body" [closeOnSelect]="false" [compareWith]="compareById"
                        [ngClass]="{ 'is-invalid': submitted && f['main_contact_party_id'].errors }"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                            <span *ngIf="item.email"> : {{ item.email }}</span>
                            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                        </ng-template>
                    </ng-select>
                    <div *ngIf="submitted && f['main_contact_party_id'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['main_contact_party_id'].errors &&
                                f['main_contact_party_id'].errors['required']
                              ">
                            Contact is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">subject</span> Subject
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="subject" type="text" formControlName="subject" placeholder="Subject"
                        class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['subject'].errors }" />
                    <div *ngIf="submitted && f['subject'].errors" class="p-error">
                        <div *ngIf="
                                    submitted &&
                                    f['subject'].errors &&
                                    f['subject'].errors['required']
                                  ">
                            Subject is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">category</span>Category
                        <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="dropdowns['activityCategory']" formControlName="task_category"
                        placeholder="Select Category" optionLabel="label" optionValue="value"
                        [styleClass]="'h-3rem w-full'"
                        [ngClass]="{ 'is-invalid': submitted && f['task_category'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['task_category'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['task_category'].errors &&
                                f['task_category'].errors['required']
                              ">
                            Category is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">person</span>Processor
                    </label>
                    <ng-select pInputText [items]="employees$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="employeeLoading" [minTermLength]="0"
                        formControlName="processor_party_id" [typeahead]="employeeInput$" [maxSelectedItems]="10"
                        appendTo="body" [closeOnSelect]="false" [compareWith]="compareById"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                        <ng-template ng-option-tmp let-item="item">
                            <div class="flex align-items-center gap-2">
                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                                <span *ngIf="item.email"> : {{ item.email }}</span>
                                <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">schedule</span>Create Date
                    </label>
                    <p-calendar formControlName="start_date" [showButtonBar]="true" dateFormat="yy-mm-dd"
                        placeholder="Create Date" [showIcon]="true" styleClass="h-3rem w-full"></p-calendar>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">schedule</span>Expected Decision Date
                    </label>
                    <p-calendar formControlName="end_date" [showButtonBar]="true" dateFormat="yy-mm-dd"
                        placeholder="Expected Decision Date" [showIcon]="true" styleClass="h-3rem w-full"></p-calendar>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">flag</span>Priority
                    </label>
                    <p-dropdown [options]="dropdowns['activityPriority']" formControlName="priority"
                        placeholder="Select a Prioriry" optionLabel="label" optionValue="value"
                        styleClass="h-3rem w-full">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">check_circle</span>Status
                        <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="dropdowns['activityStatus']" formControlName="activity_status"
                        placeholder="Select a Status" optionLabel="label" optionValue="value" styleClass="h-3rem w-full"
                        [ngClass]="{ 'is-invalid': submitted && f['activity_status'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['activity_status'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['activity_status'].errors &&
                                f['activity_status'].errors['required']
                              ">
                            Status is required.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onUpdate()"></button>
        </div>
    </form>
</div>

<div class="p-3 w-full surface-card border-round shadow-1 mt-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Notes</h4>

        <div class="flex align-items-center gap-3">
            <p-button label="Add" (click)="showDialog('right')" icon="pi pi-plus-circle" iconPos="right" class="ml-auto"
                [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="NotesCols" [(ngModel)]="selectedNotesColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">

        <p-table [value]="notedetails" dataKey="bp_id" [rows]="10" styleClass="w-full" [paginator]="true"
            [scrollable]="true" [reorderableColumns]="true" (onColReorder)="onNotesColumnReorder($event)"
            responsiveLayout="scroll" class="scrollable-table">

            <ng-template pTemplate="header">
                <tr>
                    <th class="border-round-left-lg" style="min-width: 70rem;"
                        (click)="customSort('note', notedetails, 'Notes')">
                        <div class="flex align-items-center cursor-pointer">
                            Note
                            <i *ngIf="sortFieldNotes === 'note'" class="ml-2 pi"
                                [ngClass]="sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldNotes !== 'note'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>

                    <ng-container *ngFor="let col of selectedNotesColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field, notedetails, 'Notes')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldNotes === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldNotes !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg">Actions</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-notes>
                <tr class="cursor-pointer">
                    <td class="border-round-left-lg note-text" [innerHTML]="notes?.note || '-'">
                    </td>
                    <ng-container *ngFor="let col of selectedNotesColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'updatedAt'">
                                    {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'updatedBy'">
                                    {{ notes?.updatedBy || '-'}}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td class="border-round-right-lg">
                        <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                            (click)="editNote(notes)"></button>
                        <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                            (click)="$event.stopPropagation(); confirmRemove(notes);"></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="8" class="border-round-left-lg">No notes found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="8" class="border-round-left-lg">Loading notes data. Please wait...</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="notevisible" [style]="{ width: '50rem' }" [position]="'right'" [draggable]="false"
    class="note-popup">
    <ng-template pTemplate="header">
        <h4>Note</h4>
    </ng-template>

    <form [formGroup]="NoteForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <div class="form-input flex-1 relative">
                <p-editor formControlName="note" placeholder="Enter text here..." [style]="{ height: '200px' }"
                    [ngClass]="{ 'is-invalid': notesubmitted && fNote['note'].errors }" />
                <div *ngIf="notesubmitted && fNote['note'].errors"
                    class="invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
                    <div *ngIf="fNote['note'].errors['required']">Note is required.</div>
                </div>
            </div>
        </div>
        <div class="flex align-items-center gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="notevisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onNoteSubmit()"></button>
        </div>
    </form>

</p-dialog>