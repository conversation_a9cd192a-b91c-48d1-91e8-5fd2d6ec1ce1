{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../organizational.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/calendar\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/table\";\nimport * as i12 from \"primeng/multiselect\";\nimport * as i13 from \"primeng/dialog\";\nconst _c0 = () => ({\n  width: \"45rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction GeneralComponent_ng_template_9_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderMap[\"parent\"] === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction GeneralComponent_ng_template_9_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction GeneralComponent_ng_template_9_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderMap[\"parent\"] === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction GeneralComponent_ng_template_9_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction GeneralComponent_ng_template_9_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 39);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_9_ng_container_8_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field, \"parent\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, GeneralComponent_ng_template_9_ng_container_8_i_4_Template, 1, 1, \"i\", 33)(5, GeneralComponent_ng_template_9_ng_container_8_i_5_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"parent\"] === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"parent\"] !== col_r4.field);\n  }\n}\nfunction GeneralComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 30);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 31);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_9_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"start_date\", \"parent\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5, \" Valid From \");\n    i0.ɵɵtemplate(6, GeneralComponent_ng_template_9_i_6_Template, 1, 1, \"i\", 33)(7, GeneralComponent_ng_template_9_i_7_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, GeneralComponent_ng_template_9_ng_container_8_Template, 6, 4, \"ng-container\", 35);\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 36);\n    i0.ɵɵtext(11, \"Actions\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"parent\"] === \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"parent\"] !== \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedColumns(\"parent\"));\n  }\n}\nfunction GeneralComponent_ng_template_10_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const unit_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (unit_r6 == null ? null : unit_r6.end_date) ? i0.ɵɵpipeBind2(2, 1, unit_r6.end_date, \"dd/MM/yyyy\") : \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_10_ng_container_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const unit_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (unit_r6 == null ? null : unit_r6.organisational_unit_id) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_10_ng_container_6_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const unit_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (unit_r6 == null ? null : unit_r6.name) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 46);\n    i0.ɵɵtemplate(3, GeneralComponent_ng_template_10_ng_container_6_ng_container_3_Template, 3, 4, \"ng-container\", 47)(4, GeneralComponent_ng_template_10_ng_container_6_ng_container_4_Template, 2, 1, \"ng-container\", 47)(5, GeneralComponent_ng_template_10_ng_container_6_ng_container_5_Template, 2, 1, \"ng-container\", 47);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n  }\n}\nfunction GeneralComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 40)(1, \"td\", 41);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GeneralComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 35);\n    i0.ɵɵelementStart(7, \"td\")(8, \"div\", 36)(9, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_10_Template_button_click_9_listener() {\n      const unit_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editUnit(unit_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_10_Template_button_click_10_listener($event) {\n      const unit_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(unit_r6, \"parent\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const unit_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", unit_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (unit_r6 == null ? null : unit_r6.start_date) ? i0.ɵɵpipeBind2(5, 3, unit_r6.start_date, \"dd/MM/yyyy\") : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedColumns(\"parent\"));\n  }\n}\nfunction GeneralComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 48);\n    i0.ɵɵtext(2, \"No parent units found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 48);\n    i0.ɵɵtext(2, \" Loading parent units data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Parent Unit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Valid From is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, GeneralComponent_div_25_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"start_date\"].errors && ctx_r1.f[\"start_date\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Valid To is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, GeneralComponent_div_35_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"end_date\"].errors && ctx_r1.f[\"end_date\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_ng_template_44_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.name, \"\");\n  }\n}\nfunction GeneralComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, GeneralComponent_ng_template_44_span_2_Template, 2, 1, \"span\", 50);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.organisational_unit_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.name);\n  }\n}\nfunction GeneralComponent_ng_template_56_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderMap[\"address\"] === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction GeneralComponent_ng_template_56_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction GeneralComponent_ng_template_56_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrderMap[\"address\"] === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction GeneralComponent_ng_template_56_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction GeneralComponent_ng_template_56_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 39);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_56_ng_container_8_Template_th_click_1_listener() {\n      const col_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r11.field, \"address\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, GeneralComponent_ng_template_56_ng_container_8_i_4_Template, 1, 1, \"i\", 33)(5, GeneralComponent_ng_template_56_ng_container_8_i_5_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r11.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r11.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"address\"] === col_r11.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"address\"] !== col_r11.field);\n  }\n}\nfunction GeneralComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 30);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 31);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_56_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"start_date\", \"address\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5, \" Valid From \");\n    i0.ɵɵtemplate(6, GeneralComponent_ng_template_56_i_6_Template, 1, 1, \"i\", 33)(7, GeneralComponent_ng_template_56_i_7_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, GeneralComponent_ng_template_56_ng_container_8_Template, 6, 4, \"ng-container\", 35);\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 36);\n    i0.ɵɵtext(11, \"Actions\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"address\"] === \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortFieldMap[\"address\"] !== \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedColumns(\"address\"));\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.end_date) ? i0.ɵɵpipeBind2(2, 1, address_r13.end_date, \"dd/MM/yyyy\") : \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.name) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.company_name) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.department_name) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.address) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.phone) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.fax) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.mobile) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.email) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const address_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.website) || \"-\", \" \");\n  }\n}\nfunction GeneralComponent_ng_template_57_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 46);\n    i0.ɵɵtemplate(3, GeneralComponent_ng_template_57_ng_container_6_ng_container_3_Template, 3, 4, \"ng-container\", 47)(4, GeneralComponent_ng_template_57_ng_container_6_ng_container_4_Template, 2, 1, \"ng-container\", 47)(5, GeneralComponent_ng_template_57_ng_container_6_ng_container_5_Template, 2, 1, \"ng-container\", 47)(6, GeneralComponent_ng_template_57_ng_container_6_ng_container_6_Template, 2, 1, \"ng-container\", 47)(7, GeneralComponent_ng_template_57_ng_container_6_ng_container_7_Template, 2, 1, \"ng-container\", 47)(8, GeneralComponent_ng_template_57_ng_container_6_ng_container_8_Template, 2, 1, \"ng-container\", 47)(9, GeneralComponent_ng_template_57_ng_container_6_ng_container_9_Template, 2, 1, \"ng-container\", 47)(10, GeneralComponent_ng_template_57_ng_container_6_ng_container_10_Template, 2, 1, \"ng-container\", 47)(11, GeneralComponent_ng_template_57_ng_container_6_ng_container_11_Template, 2, 1, \"ng-container\", 47)(12, GeneralComponent_ng_template_57_ng_container_6_ng_container_12_Template, 2, 1, \"ng-container\", 47);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r14.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"company_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"department_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"fax\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"website\");\n  }\n}\nfunction GeneralComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 40)(1, \"td\", 41);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GeneralComponent_ng_template_57_ng_container_6_Template, 13, 11, \"ng-container\", 35);\n    i0.ɵɵelementStart(7, \"td\")(8, \"div\", 36)(9, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function GeneralComponent_ng_template_57_Template_button_click_9_listener($event) {\n      const address_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(address_r13, \"address\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const address_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", address_r13);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r13 == null ? null : address_r13.start_date) ? i0.ɵɵpipeBind2(5, 3, address_r13.start_date, \"dd/MM/yyyy\") : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedColumns(\"address\"));\n  }\n}\nfunction GeneralComponent_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \"No addresses found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \" Loading addresses data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class GeneralComponent {\n  constructor(route, organizationalservice, formBuilder, messageservice, confirmationservice) {\n    this.route = route;\n    this.organizationalservice = organizationalservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.parentunitDetails = [];\n    this.addressDetails = [];\n    this.unitLoading = false;\n    this.unitInput$ = new Subject();\n    this.organisational_unit_id = '';\n    this.addParentDialogVisible = false;\n    this.addAddressDialogVisible = false;\n    this.visible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.editid = '';\n    this.saving = false;\n    this.defaultOptions = [];\n    this.ParentUnitForm = this.formBuilder.group({\n      start_date: ['', [Validators.required]],\n      end_date: ['', [Validators.required]],\n      organisational_unit_id: ['']\n    });\n    this.AddressForm = this.formBuilder.group({\n      start_date: ['', [Validators.required]],\n      end_date: ['', [Validators.required]],\n      business_partner_internal_id: ['']\n    });\n    this._selectedColumnsMap = {\n      employee: [],\n      manager: []\n    };\n    this.cols = [{\n      field: 'end_date',\n      header: 'Valid To'\n    }, {\n      field: 'organisational_unit_id',\n      header: 'Parent Unit ID'\n    }, {\n      field: 'name',\n      header: 'Parent Unit Name'\n    }];\n    this.colsaddress = [{\n      field: 'end_date',\n      header: 'Valid To'\n    }, {\n      field: 'name',\n      header: 'Name'\n    }, {\n      field: 'company_name',\n      header: 'Company Name'\n    }, {\n      field: 'department_name',\n      header: 'Department Name'\n    }, {\n      field: 'address',\n      header: 'Address'\n    }, {\n      field: 'phone',\n      header: 'Phone'\n    }, {\n      field: 'fax',\n      header: 'Fax'\n    }, {\n      field: 'mobile',\n      header: 'Mobile'\n    }, {\n      field: 'email',\n      header: 'E-Mail'\n    }, {\n      field: 'website',\n      header: 'WebSite'\n    }];\n    // Separate sort field/order for employee and manager\n    this.sortFieldMap = {\n      employee: '',\n      manager: ''\n    };\n    this.sortOrderMap = {\n      employee: 1,\n      manager: 1\n    };\n  }\n  // Sorting method\n  customSort(field, module) {\n    let sortdetails;\n    if (module === 'parent') {\n      sortdetails = this.parentunitDetails;\n    } else if (module === 'address') {\n      sortdetails = this.addressDetails;\n    } else {\n      console.warn('Unknown module:', module);\n      return;\n    }\n    let currentField = this.sortFieldMap[module];\n    let currentOrder = this.sortOrderMap[module];\n    if (currentField === field) {\n      currentOrder = -currentOrder;\n    } else {\n      currentField = field;\n      currentOrder = 1;\n    }\n    this.sortFieldMap[module] = currentField;\n    this.sortOrderMap[module] = currentOrder;\n    sortdetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') {\n        result = value1.localeCompare(value2);\n      } else {\n        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      }\n      return currentOrder * result;\n    });\n  }\n  // Utility to resolve nested field values\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    return field.indexOf('.') === -1 ? data[field] : field.split('.').reduce((obj, key) => obj?.[key], data);\n  }\n  // Dynamic selected columns getter/setter\n  getSelectedColumns(module) {\n    return this._selectedColumnsMap[module];\n  }\n  setSelectedColumns(module, val) {\n    const baseCols = module === 'parent' ? this.cols : this.colsaddress;\n    this._selectedColumnsMap[module] = baseCols.filter(col => val.includes(col));\n  }\n  // Column reorder handler (per module)\n  onColumnReorder(event, module) {\n    const draggedCol = this._selectedColumnsMap[module][event.dragIndex];\n    this._selectedColumnsMap[module].splice(event.dragIndex, 1);\n    this._selectedColumnsMap[module].splice(event.dropIndex, 0, draggedCol);\n  }\n  ngOnInit() {\n    this.loadParentUnit();\n    this.organisational_unit_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.organizationalservice.organizational.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.parentunitDetails = Array.isArray(response) ? response : response?.data || [];\n        this.addressDetails = response || [];\n      }\n    });\n    this._selectedColumnsMap['parent'] = this.cols;\n    this._selectedColumnsMap['address'] = this.colsaddress;\n  }\n  loadParentUnit() {\n    this.units$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.unitInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.unitLoading = true), switchMap(term => {\n      const params = {\n        'fields[0]': 'organisational_unit_id',\n        'fields[2]': 'name'\n      };\n      if (term) {\n        params['filters[$or][0][organisational_unit_id][$containsi]'] = term;\n        params['filters[$or][1][name][$containsi]'] = term;\n      }\n      return this.organizationalservice.getParentUnit(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Parent Unit fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.unitLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  editUnit(unit) {\n    this.addParentDialogVisible = true;\n    this.editid = unit?.documentId;\n    this.ParentUnitForm.patchValue({\n      start_date: unit?.start_date ? new Date(unit?.start_date) : null,\n      end_date: unit?.end_date ? new Date(unit?.end_date) : null,\n      organisational_unit_id: unit?.organisational_unit_id\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.visible = true;\n      if (_this.ParentUnitForm.invalid) {\n        console.log('Form is invalid:', _this.ParentUnitForm.errors);\n        _this.visible = true;\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.ParentUnitForm.value\n      };\n      const data = {\n        start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n        organisational_unit_id: value?.organisational_unit_id,\n        parent_organisational_unit_id: _this.organisational_unit_id\n      };\n      let unitRequest$;\n      if (_this.editid) {\n        unitRequest$ = _this.organizationalservice.updateOrganizational(_this.editid, data);\n      } else {\n        unitRequest$ = _this.organizationalservice.createOrganizational(data);\n      }\n      unitRequest$.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        complete: () => {\n          _this.saving = false;\n          _this.addParentDialogVisible = false;\n          _this.ParentUnitForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: _this.editid ? 'Parent Unit updated successfully!' : 'Parent Unit created successfully!'\n          });\n          _this.organizationalservice.getOrganizationByID(_this.organisational_unit_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.addParentDialogVisible = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  onSubmitAddress() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      _this2.visible = true;\n      if (_this2.AddressForm.invalid) {\n        console.log('Form is invalid:', _this2.AddressForm.errors);\n        _this2.visible = true;\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.AddressForm.value\n      };\n      const data = {\n        start_date: value?.start_date ? _this2.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this2.formatDate(value.end_date) : null,\n        business_partner_internal_id: value?.business_partner_internal_id,\n        organisational_unit_id: _this2.organisational_unit_id\n      };\n      _this2.organizationalservice.createManager(data).pipe(takeUntil(_this2.unsubscribe$)).subscribe({\n        complete: () => {\n          _this2.saving = false;\n          _this2.addAddressDialogVisible = false;\n          _this2.AddressForm.reset();\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Address created successfully!.'\n          });\n          _this2.organizationalservice.getOrganizationByID(_this2.organisational_unit_id).pipe(takeUntil(_this2.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this2.saving = false;\n          _this2.addAddressDialogVisible = false;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  confirmRemove(item, module) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item, module);\n      }\n    });\n  }\n  remove(item, module) {\n    let deleteObservable;\n    if (module === 'parent') {\n      deleteObservable = this.organizationalservice.deleteEmployee(item.documentId);\n    } else if (module === 'address') {\n      deleteObservable = this.organizationalservice.deleteManager(item.documentId);\n    } else {\n      console.warn('Unknown module:', module);\n      return;\n    }\n    deleteObservable.pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.organizationalservice.getOrganizationByID(this.organisational_unit_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showNewDialog(position, dialog) {\n    this.position = position;\n    this.submitted = false;\n    if (dialog === 'parent') {\n      this.addParentDialogVisible = true;\n      this.ParentUnitForm.reset();\n    } else if (dialog === 'address') {\n      this.addAddressDialogVisible = true;\n      this.AddressForm.reset();\n    }\n  }\n  get f() {\n    return this.ParentUnitForm.controls;\n  }\n  get faddress() {\n    return this.AddressForm.controls;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function GeneralComponent_Factory(t) {\n      return new (t || GeneralComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OrganizationalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneralComponent,\n      selectors: [[\"app-general\"]],\n      decls: 60,\n      vars: 48,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add New\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"onSort\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-contact-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Valid From\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Valid From\", \"appendTo\", \"body\", 3, \"showIcon\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Valid To\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Valid To\", \"appendTo\", \"body\", 3, \"showIcon\", \"ngClass\"], [\"for\", \"Parent Unit ID\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"name\", \"bindValue\", \"organisational_unit_id\", \"formControlName\", \"organisational_unit_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"table-checkbox\", \"text-center\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"font-medium\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"6\", 1, \"border-round-left-lg\"], [1, \"p-error\"], [4, \"ngIf\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\"]],\n      template: function GeneralComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Parent Unit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function GeneralComponent_Template_p_button_click_5_listener() {\n            return ctx.showNewDialog(\"right\", \"parent\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function GeneralComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            return ctx.setSelectedColumns(\"parent\", $event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function GeneralComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event, \"parent\");\n          })(\"onSort\", function GeneralComponent_Template_p_table_onSort_8_listener($event) {\n            return ctx.customSort($event.field, \"parent\");\n          });\n          i0.ɵɵtemplate(9, GeneralComponent_ng_template_9_Template, 12, 3, \"ng-template\", 8)(10, GeneralComponent_ng_template_10_Template, 11, 6, \"ng-template\", 9)(11, GeneralComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, GeneralComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function GeneralComponent_Template_p_dialog_visibleChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addParentDialogVisible, $event) || (ctx.addParentDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(14, GeneralComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n          i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"label\", 15)(18, \"span\", 16);\n          i0.ɵɵtext(19, \"badge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20, \"Valid From \");\n          i0.ɵɵelementStart(21, \"span\", 17);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 18);\n          i0.ɵɵelement(24, \"p-calendar\", 19);\n          i0.ɵɵtemplate(25, GeneralComponent_div_25_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"label\", 21)(28, \"span\", 16);\n          i0.ɵɵtext(29, \"badge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \"Valid To \");\n          i0.ɵɵelementStart(31, \"span\", 17);\n          i0.ɵɵtext(32, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 18);\n          i0.ɵɵelement(34, \"p-calendar\", 22);\n          i0.ɵɵtemplate(35, GeneralComponent_div_35_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 14)(37, \"label\", 23)(38, \"span\", 16);\n          i0.ɵɵtext(39, \"account_tree\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40, \"Parent Unit ID \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 18)(42, \"ng-select\", 24);\n          i0.ɵɵpipe(43, \"async\");\n          i0.ɵɵtemplate(44, GeneralComponent_ng_template_44_Template, 3, 2, \"ng-template\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 26)(46, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function GeneralComponent_Template_button_click_46_listener() {\n            return ctx.addParentDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function GeneralComponent_Template_button_click_47_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 0)(49, \"div\", 1)(50, \"h4\", 2);\n          i0.ɵɵtext(51, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 3)(53, \"p-multiSelect\", 5);\n          i0.ɵɵlistener(\"ngModelChange\", function GeneralComponent_Template_p_multiSelect_ngModelChange_53_listener($event) {\n            return ctx.setSelectedColumns(\"address\", $event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 6)(55, \"p-table\", 29);\n          i0.ɵɵlistener(\"onColReorder\", function GeneralComponent_Template_p_table_onColReorder_55_listener($event) {\n            return ctx.onColumnReorder($event, \"address\");\n          });\n          i0.ɵɵtemplate(56, GeneralComponent_ng_template_56_Template, 12, 3, \"ng-template\", 8)(57, GeneralComponent_ng_template_57_Template, 10, 6, \"ng-template\", 9)(58, GeneralComponent_ng_template_58_Template, 3, 0, \"ng-template\", 10)(59, GeneralComponent_ng_template_59_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols)(\"ngModel\", ctx.getSelectedColumns(\"parent\"))(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.parentunitDetails)(\"rows\", 14)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(43, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addParentDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ParentUnitForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"showIcon\", true)(\"ngClass\", i0.ɵɵpureFunction1(44, _c1, ctx.submitted && ctx.f[\"start_date\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"start_date\"].errors);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"showIcon\", true)(\"ngClass\", i0.ɵɵpureFunction1(46, _c1, ctx.submitted && ctx.f[\"end_date\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"end_date\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(43, 41, ctx.units$))(\"hideSelected\", true)(\"loading\", ctx.unitLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.unitInput$)(\"maxSelectedItems\", 10);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"options\", ctx.colsaddress)(\"ngModel\", ctx.getSelectedColumns(\"address\"))(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.addressDetails)(\"rows\", 14)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.FormGroupDirective, i3.FormControlName, i7.Calendar, i8.ButtonDirective, i8.Button, i4.PrimeTemplate, i9.InputText, i10.Tooltip, i11.Table, i11.SortableColumn, i11.FrozenColumn, i11.ReorderableColumn, i11.TableCheckbox, i11.TableHeaderCheckbox, i12.MultiSelect, i13.Dialog, i5.AsyncPipe, i5.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .opportunity-contact-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3JnYW5pemF0aW9uYWwvb3JnYW5pemF0aW9uLWRldGFpbHMvZ2VuZXJhbC9nZW5lcmFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUkscUJBQUE7RUFDQSxXQUFBO0FBQ0o7O0FBSVE7RUFDSSxrQkFBQTtBQURaO0FBR1k7RUFDSSw0QkFBQTtFQUNBLDJDQUFBO0FBRGhCO0FBR2dCO0VBQ0ksU0FBQTtBQURwQjtBQUtZO0VBQ0ksNEJBQUE7RUFDQSxpQkFBQTtBQUhoQiIsInNvdXJjZXNDb250ZW50IjpbIi5pbnZhbGlkLWZlZWRiYWNrLFxyXG4ucC1pbnB1dHRleHQ6aW52YWxpZCxcclxuLmlzLWNoZWNrYm94LWludmFsaWQsXHJcbi5wLWlucHV0dGV4dC5pcy1pbnZhbGlkIHtcclxuICAgIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICAgIHJpZ2h0OiAxMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAge1xyXG4gICAgLm9wcG9ydHVuaXR5LWNvbnRhY3QtcG9wdXAge1xyXG4gICAgICAgIC5wLWRpYWxvZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNTBweDtcclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "catchError", "debounceTime", "finalize", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrderMap", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "GeneralComponent_ng_template_9_ng_container_8_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "GeneralComponent_ng_template_9_ng_container_8_i_4_Template", "GeneralComponent_ng_template_9_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortFieldMap", "GeneralComponent_ng_template_9_Template_th_click_3_listener", "_r1", "GeneralComponent_ng_template_9_i_6_Template", "GeneralComponent_ng_template_9_i_7_Template", "GeneralComponent_ng_template_9_ng_container_8_Template", "getSelectedColumns", "unit_r6", "end_date", "ɵɵpipeBind2", "organisational_unit_id", "name", "GeneralComponent_ng_template_10_ng_container_6_ng_container_3_Template", "GeneralComponent_ng_template_10_ng_container_6_ng_container_4_Template", "GeneralComponent_ng_template_10_ng_container_6_ng_container_5_Template", "col_r7", "GeneralComponent_ng_template_10_ng_container_6_Template", "GeneralComponent_ng_template_10_Template_button_click_9_listener", "_r5", "editUnit", "GeneralComponent_ng_template_10_Template_button_click_10_listener", "$event", "stopPropagation", "confirmRemove", "start_date", "GeneralComponent_div_25_div_1_Template", "submitted", "f", "errors", "GeneralComponent_div_35_div_1_Template", "item_r8", "GeneralComponent_ng_template_44_span_2_Template", "ɵɵtextInterpolate", "GeneralComponent_ng_template_56_ng_container_8_Template_th_click_1_listener", "col_r11", "_r10", "GeneralComponent_ng_template_56_ng_container_8_i_4_Template", "GeneralComponent_ng_template_56_ng_container_8_i_5_Template", "GeneralComponent_ng_template_56_Template_th_click_3_listener", "_r9", "GeneralComponent_ng_template_56_i_6_Template", "GeneralComponent_ng_template_56_i_7_Template", "GeneralComponent_ng_template_56_ng_container_8_Template", "address_r13", "company_name", "department_name", "address", "phone", "fax", "mobile", "email", "website", "GeneralComponent_ng_template_57_ng_container_6_ng_container_3_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_4_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_5_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_6_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_7_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_8_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_9_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_10_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_11_Template", "GeneralComponent_ng_template_57_ng_container_6_ng_container_12_Template", "col_r14", "GeneralComponent_ng_template_57_ng_container_6_Template", "GeneralComponent_ng_template_57_Template_button_click_9_listener", "_r12", "GeneralComponent", "constructor", "route", "organizationalservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "parentunitDetails", "addressDetails", "unitLoading", "unitInput$", "addParentDialogVisible", "addAddressDialogVisible", "visible", "position", "editid", "saving", "defaultOptions", "ParentUnitForm", "group", "required", "AddressForm", "business_partner_internal_id", "_selectedColumnsMap", "employee", "manager", "cols", "colsaddress", "module", "sortdetails", "console", "warn", "current<PERSON><PERSON>", "currentOrder", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "setSelectedColumns", "val", "baseCols", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "ngOnInit", "loadParentUnit", "parent", "snapshot", "paramMap", "get", "organizational", "pipe", "subscribe", "response", "Array", "isArray", "units$", "term", "params", "getParentUnit", "error", "unit", "documentId", "patchValue", "Date", "onSubmit", "_this", "_asyncToGenerator", "invalid", "log", "value", "formatDate", "parent_organisational_unit_id", "unitRequest$", "updateOrganizational", "createOrganizational", "complete", "reset", "add", "severity", "detail", "getOrganizationByID", "onSubmitAddress", "_this2", "createManager", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "item", "confirm", "message", "icon", "accept", "remove", "deleteObservable", "deleteEmployee", "deleteManager", "next", "showNewDialog", "dialog", "controls", "faddress", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "OrganizationalService", "i3", "FormBuilder", "i4", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "GeneralComponent_Template", "rf", "ctx", "GeneralComponent_Template_p_button_click_5_listener", "GeneralComponent_Template_p_multiSelect_ngModelChange_6_listener", "GeneralComponent_Template_p_table_onColReorder_8_listener", "GeneralComponent_Template_p_table_onSort_8_listener", "GeneralComponent_ng_template_9_Template", "GeneralComponent_ng_template_10_Template", "GeneralComponent_ng_template_11_Template", "GeneralComponent_ng_template_12_Template", "ɵɵtwoWayListener", "GeneralComponent_Template_p_dialog_visibleChange_13_listener", "ɵɵtwoWayBindingSet", "GeneralComponent_ng_template_14_Template", "GeneralComponent_div_25_Template", "GeneralComponent_div_35_Template", "GeneralComponent_ng_template_44_Template", "GeneralComponent_Template_button_click_46_listener", "GeneralComponent_Template_button_click_47_listener", "GeneralComponent_Template_p_multiSelect_ngModelChange_53_listener", "GeneralComponent_Template_p_table_onColReorder_55_listener", "GeneralComponent_ng_template_56_Template", "GeneralComponent_ng_template_57_Template", "GeneralComponent_ng_template_58_Template", "GeneralComponent_ng_template_59_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c1", "ɵɵclassMap", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organization-details\\general\\general.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organization-details\\general\\general.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { OrganizationalService } from '../../organizational.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-general',\r\n  templateUrl: './general.component.html',\r\n  styleUrl: './general.component.scss',\r\n})\r\nexport class GeneralComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public parentunitDetails: any[] = [];\r\n  public addressDetails: any[] = [];\r\n  public units$?: Observable<any[]>;\r\n  public unitLoading = false;\r\n  public unitInput$ = new Subject<string>();\r\n  public organisational_unit_id: string = '';\r\n  public addParentDialogVisible: boolean = false;\r\n  public addAddressDialogVisible: boolean = false;\r\n  public visible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public editid: string = '';\r\n  public saving = false;\r\n  private defaultOptions: any = [];\r\n\r\n  public ParentUnitForm: FormGroup = this.formBuilder.group({\r\n    start_date: ['', [Validators.required]],\r\n    end_date: ['', [Validators.required]],\r\n    organisational_unit_id: [''],\r\n  });\r\n\r\n  public AddressForm: FormGroup = this.formBuilder.group({\r\n    start_date: ['', [Validators.required]],\r\n    end_date: ['', [Validators.required]],\r\n    business_partner_internal_id: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private organizationalservice: OrganizationalService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  private _selectedColumnsMap: { [key: string]: Column[] } = {\r\n    employee: [],\r\n    manager: [],\r\n  };\r\n\r\n  public cols: Column[] = [\r\n    { field: 'end_date', header: 'Valid To' },\r\n    { field: 'organisational_unit_id', header: 'Parent Unit ID' },\r\n    { field: 'name', header: 'Parent Unit Name' },\r\n  ];\r\n\r\n  public colsaddress: Column[] = [\r\n    { field: 'end_date', header: 'Valid To' },\r\n    { field: 'name', header: 'Name' },\r\n    { field: 'company_name', header: 'Company Name' },\r\n    { field: 'department_name', header: 'Department Name' },\r\n    { field: 'address', header: 'Address' },\r\n    { field: 'phone', header: 'Phone' },\r\n    { field: 'fax', header: 'Fax' },\r\n    { field: 'mobile', header: 'Mobile' },\r\n    { field: 'email', header: 'E-Mail' },\r\n    { field: 'website', header: 'WebSite' },\r\n  ];\r\n\r\n  // Separate sort field/order for employee and manager\r\n  sortFieldMap: { [key: string]: string } = {\r\n    employee: '',\r\n    manager: '',\r\n  };\r\n  sortOrderMap: { [key: string]: number } = {\r\n    employee: 1,\r\n    manager: 1,\r\n  };\r\n\r\n  // Sorting method\r\n  customSort(field: string, module: 'parent' | 'address'): void {\r\n    let sortdetails;\r\n    if (module === 'parent') {\r\n      sortdetails = this.parentunitDetails;\r\n    } else if (module === 'address') {\r\n      sortdetails = this.addressDetails;\r\n    } else {\r\n      console.warn('Unknown module:', module);\r\n      return;\r\n    }\r\n\r\n    let currentField = this.sortFieldMap[module];\r\n    let currentOrder = this.sortOrderMap[module];\r\n\r\n    if (currentField === field) {\r\n      currentOrder = -currentOrder;\r\n    } else {\r\n      currentField = field;\r\n      currentOrder = 1;\r\n    }\r\n\r\n    this.sortFieldMap[module] = currentField;\r\n    this.sortOrderMap[module] = currentOrder;\r\n\r\n    sortdetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string') {\r\n        result = value1.localeCompare(value2);\r\n      } else {\r\n        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n      }\r\n\r\n      return currentOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested field values\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    return field.indexOf('.') === -1\r\n      ? data[field]\r\n      : field.split('.').reduce((obj, key) => obj?.[key], data);\r\n  }\r\n\r\n  // Dynamic selected columns getter/setter\r\n  getSelectedColumns(module: 'parent' | 'address'): Column[] {\r\n    return this._selectedColumnsMap[module];\r\n  }\r\n\r\n  setSelectedColumns(module: 'parent' | 'address', val: Column[]) {\r\n    const baseCols = module === 'parent' ? this.cols : this.colsaddress;\r\n    this._selectedColumnsMap[module] = baseCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  // Column reorder handler (per module)\r\n  onColumnReorder(event: any, module: 'parent' | 'address') {\r\n    const draggedCol = this._selectedColumnsMap[module][event.dragIndex];\r\n    this._selectedColumnsMap[module].splice(event.dragIndex, 1);\r\n    this._selectedColumnsMap[module].splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadParentUnit();\r\n    this.organisational_unit_id =\r\n      this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.organizationalservice.organizational\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.parentunitDetails = Array.isArray(response)\r\n            ? response\r\n            : response?.data || [];\r\n          this.addressDetails = response || [];\r\n        }\r\n      });\r\n\r\n    this._selectedColumnsMap['parent'] = this.cols;\r\n    this._selectedColumnsMap['address'] = this.colsaddress;\r\n  }\r\n\r\n  private loadParentUnit(): void {\r\n    this.units$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.unitInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.unitLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'fields[0]': 'organisational_unit_id',\r\n            'fields[2]': 'name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][organisational_unit_id][$containsi]'] =\r\n              term;\r\n            params['filters[$or][1][name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.organizationalservice.getParentUnit(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Parent Unit fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.unitLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  editUnit(unit: any) {\r\n    this.addParentDialogVisible = true;\r\n    this.editid = unit?.documentId;\r\n\r\n    this.ParentUnitForm.patchValue({\r\n      start_date: unit?.start_date ? new Date(unit?.start_date) : null,\r\n      end_date: unit?.end_date ? new Date(unit?.end_date) : null,\r\n      organisational_unit_id: unit?.organisational_unit_id,\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n\r\n    if (this.ParentUnitForm.invalid) {\r\n      console.log('Form is invalid:', this.ParentUnitForm.errors);\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.ParentUnitForm.value };\r\n\r\n    const data = {\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      organisational_unit_id: value?.organisational_unit_id,\r\n      parent_organisational_unit_id: this.organisational_unit_id,\r\n    };\r\n\r\n    let unitRequest$: Observable<any>;\r\n\r\n    if (this.editid) {\r\n      unitRequest$ = this.organizationalservice.updateOrganizational(\r\n        this.editid,\r\n        data\r\n      );\r\n    } else {\r\n      unitRequest$ = this.organizationalservice.createOrganizational(data);\r\n    }\r\n\r\n    unitRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      complete: () => {\r\n        this.saving = false;\r\n        this.addParentDialogVisible = false;\r\n        this.ParentUnitForm.reset();\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: this.editid\r\n            ? 'Parent Unit updated successfully!'\r\n            : 'Parent Unit created successfully!',\r\n        });\r\n        this.organizationalservice\r\n          .getOrganizationByID(this.organisational_unit_id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.addParentDialogVisible = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  async onSubmitAddress() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n\r\n    if (this.AddressForm.invalid) {\r\n      console.log('Form is invalid:', this.AddressForm.errors);\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.AddressForm.value };\r\n\r\n    const data = {\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      business_partner_internal_id: value?.business_partner_internal_id,\r\n      organisational_unit_id: this.organisational_unit_id,\r\n    };\r\n\r\n    this.organizationalservice\r\n      .createManager(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        complete: () => {\r\n          this.saving = false;\r\n          this.addAddressDialogVisible = false;\r\n          this.AddressForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Address created successfully!.',\r\n          });\r\n          this.organizationalservice\r\n            .getOrganizationByID(this.organisational_unit_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.addAddressDialogVisible = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  confirmRemove(item: any, module: string) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item, module);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any, module: string) {\r\n    let deleteObservable;\r\n\r\n    if (module === 'parent') {\r\n      deleteObservable = this.organizationalservice.deleteEmployee(\r\n        item.documentId\r\n      );\r\n    } else if (module === 'address') {\r\n      deleteObservable = this.organizationalservice.deleteManager(\r\n        item.documentId\r\n      );\r\n    } else {\r\n      console.warn('Unknown module:', module);\r\n      return;\r\n    }\r\n    deleteObservable.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Record Deleted Successfully!',\r\n        });\r\n        this.organizationalservice\r\n          .getOrganizationByID(this.organisational_unit_id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  showNewDialog(position: string, dialog: string) {\r\n    this.position = position;\r\n    this.submitted = false;\r\n\r\n    if (dialog === 'parent') {\r\n      this.addParentDialogVisible = true;\r\n      this.ParentUnitForm.reset();\r\n    } else if (dialog === 'address') {\r\n      this.addAddressDialogVisible = true;\r\n      this.AddressForm.reset();\r\n    }\r\n  }\r\n\r\n  get f(): any {\r\n    return this.ParentUnitForm.controls;\r\n  }\r\n\r\n  get faddress(): any {\r\n    return this.AddressForm.controls;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Parent Unit</h4>\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <p-button label=\"Add New\" (click)=\"showNewDialog('right','parent')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [ngModel]=\"getSelectedColumns('parent')\"\r\n                (ngModelChange)=\"setSelectedColumns('parent', $event)\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\" [styleClass]=\"\r\n          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\r\n        \">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"parentunitDetails\" dataKey=\"id\" [rows]=\"14\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event,'parent')\" (onSort)=\"customSort($event.field,'parent')\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem table-checkbox text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('start_date','parent')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Valid From\r\n                            <i *ngIf=\"sortFieldMap['parent'] === 'start_date'\" class=\"ml-2 pi\" [ngClass]=\"\r\n                  sortOrderMap['parent'] === 1\r\n                    ? 'pi-sort-amount-up-alt'\r\n                    : 'pi-sort-amount-down'\r\n                \">\r\n                            </i>\r\n                            <i *ngIf=\"sortFieldMap['parent'] !== 'start_date'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of getSelectedColumns('parent')\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field,'parent')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldMap['parent'] === col.field\" class=\"ml-2 pi\" [ngClass]=\"\r\n                    sortOrderMap['parent'] === 1\r\n                      ? 'pi-sort-amount-up-alt'\r\n                      : 'pi-sort-amount-down'\r\n                  \">\r\n                                </i>\r\n                                <i *ngIf=\"sortFieldMap['parent'] !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">Actions</div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-unit let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"unit\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"font-medium\">\r\n                        {{ unit?.start_date ? (unit.start_date | date: 'dd/MM/yyyy') : '-' }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of getSelectedColumns('parent')\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'end_date'\">\r\n                                    {{ unit?.end_date ? (unit.end_date | date: 'dd/MM/yyyy') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'organisational_unit_id'\">\r\n                                    {{ unit?.organisational_unit_id || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'name'\">\r\n                                    {{ unit?.name || '-' }}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n\r\n                    <td>\r\n                        <div class=\"flex align-items-center\">\r\n                            <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                                (click)=\"editUnit(unit)\"></button>\r\n                            <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                                (click)=\"$event.stopPropagation(); confirmRemove(unit,'parent')\"></button>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" colspan=\"6\">No parent units found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-left-lg\">\r\n                        Loading parent units data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addParentDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Parent Unit</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ParentUnitForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Valid From\">\r\n                <span class=\"material-symbols-rounded\">badge</span>Valid From\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-calendar formControlName=\"start_date\" inputId=\"calendar-12h\" hourFormat=\"12\" [showIcon]=\"true\"\r\n                    styleClass=\"h-3rem w-full\" placeholder=\"Valid From\" appendTo=\"body\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['start_date'].errors }\" />\r\n                <div *ngIf=\"submitted && f['start_date'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n              submitted &&\r\n              f['start_date'].errors &&\r\n              f['start_date'].errors['required']\r\n            \">\r\n                        Valid From is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Valid To\">\r\n                <span class=\"material-symbols-rounded\">badge</span>Valid To\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-calendar formControlName=\"end_date\" inputId=\"calendar-12h\" hourFormat=\"12\" [showIcon]=\"true\"\r\n                    styleClass=\"h-3rem w-full\" placeholder=\"Valid To\" appendTo=\"body\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['end_date'].errors }\" />\r\n                <div *ngIf=\"submitted && f['end_date'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n              submitted &&\r\n              f['end_date'].errors &&\r\n              f['end_date'].errors['required']\r\n            \">\r\n                        Valid To is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Parent Unit ID\">\r\n                <span class=\"material-symbols-rounded\">account_tree</span>Parent Unit ID\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select pInputText [items]=\"units$ | async\" bindLabel=\"name\" bindValue=\"organisational_unit_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"unitLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"organisational_unit_id\" [typeahead]=\"unitInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.organisational_unit_id }}</span>\r\n                        <span *ngIf=\"item.name\"> : {{ item.name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addParentDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Address</h4>\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <!-- <p-button label=\"Add New\" (click)=\"showNewDialog('right','address')\" icon=\"pi pi-plus-circle\"\r\n                iconPos=\"right\" class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" /> -->\r\n\r\n            <p-multiSelect [options]=\"colsaddress\" [ngModel]=\"getSelectedColumns('address')\"\r\n                (ngModelChange)=\"setSelectedColumns('address', $event)\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\" [styleClass]=\"\r\n          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\r\n        \">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"addressDetails\" dataKey=\"id\" [rows]=\"14\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event,'address')\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem table-checkbox text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('start_date','address')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Valid From\r\n                            <i *ngIf=\"sortFieldMap['address'] === 'start_date'\" class=\"ml-2 pi\" [ngClass]=\"\r\n                  sortOrderMap['address'] === 1\r\n                    ? 'pi-sort-amount-up-alt'\r\n                    : 'pi-sort-amount-down'\r\n                \">\r\n                            </i>\r\n                            <i *ngIf=\"sortFieldMap['address'] !== 'start_date'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of getSelectedColumns('address')\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field,'address')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldMap['address'] === col.field\" class=\"ml-2 pi\" [ngClass]=\"\r\n                    sortOrderMap['address'] === 1\r\n                      ? 'pi-sort-amount-up-alt'\r\n                      : 'pi-sort-amount-down'\r\n                  \">\r\n                                </i>\r\n                                <i *ngIf=\"sortFieldMap['address'] !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">Actions</div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-address let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"address\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"font-medium\">\r\n                        {{ address?.start_date ? (address.start_date | date: 'dd/MM/yyyy') : '-' }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of getSelectedColumns('address')\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'end_date'\">\r\n                                    {{ address?.end_date ? (address.end_date | date: 'dd/MM/yyyy') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'name'\">\r\n                                    {{ address?.name || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'company_name'\">\r\n                                    {{ address?.company_name || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'department_name'\">\r\n                                    {{ address?.department_name || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'address'\">\r\n                                    {{ address?.address || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'phone'\">\r\n                                    {{ address?.phone || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'fax'\">\r\n                                    {{ address?.fax || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'mobile'\">\r\n                                    {{ address?.mobile || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'email'\">\r\n                                    {{ address?.email || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'website'\">\r\n                                    {{ address?.website || '-' }}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n\r\n                    <td>\r\n                        <div class=\"flex align-items-center\">\r\n                            <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                                (click)=\"$event.stopPropagation(); confirmRemove(address,'address')\"></button>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" colspan=\"12\">No addresses found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg\">\r\n                        Loading addresses data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AACtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;ICiBKC,EAAA,CAAAC,SAAA,YAKI;;;;IAL+DD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,mEAI9E;;;;;IAEWJ,EAAA,CAAAC,SAAA,YAA+E;;;;;IAO3ED,EAAA,CAAAC,SAAA,YAKI;;;;IAL4DD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,mEAI7E;;;;;IAEaJ,EAAA,CAAAC,SAAA,YAA4E;;;;;;IAVxFD,EAAA,CAAAK,uBAAA,GAA+D;IAC3DL,EAAA,CAAAM,cAAA,aAA8F;IAAzCN,EAAA,CAAAO,UAAA,mBAAAC,2EAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IACzFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAMAjB,EANA,CAAAkB,UAAA,IAAAC,0DAAA,gBAIZ,IAAAC,0DAAA,gBAEoF;IAEhFpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IAXDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA0C;IAA1CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,eAAAhB,MAAA,CAAAO,KAAA,CAA0C;IAM1ChB,EAAA,CAAAsB,SAAA,EAA0C;IAA1CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,eAAAhB,MAAA,CAAAO,KAAA,CAA0C;;;;;;IAzB1DhB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAA8D;IAA5CN,EAAA,CAAAO,UAAA,mBAAAmB,4DAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,YAAY,EAAC,QAAQ,CAAC;IAAA,EAAC;IACzDf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,mBACA;IAMAjB,EANA,CAAAkB,UAAA,IAAAU,2CAAA,gBAIV,IAAAC,2CAAA,gBAEqF;IAEnF7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,sDAAA,2BAA+D;IAe3D9B,EADJ,CAAAM,cAAA,SAAI,eACqC;IAAAN,EAAA,CAAAiB,MAAA,eAAO;IAEpDjB,EAFoD,CAAAqB,YAAA,EAAM,EACjD,EACJ;;;;IA1BWrB,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,4BAA6C;IAM7CzB,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,4BAA6C;IAG3BzB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,kBAAA,WAA+B;;;;;IAgCjD/B,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAC,QAAA,IAAAjC,EAAA,CAAAkC,WAAA,OAAAF,OAAA,CAAAC,QAAA,2BACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAAuD;IACnDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAG,sBAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAAqC;IACjCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAI,IAAA,cACJ;;;;;IAbZpC,EAAA,CAAAK,uBAAA,GAA+D;IAC3DL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAkB,UAAA,IAAAmB,sEAAA,2BAAyC,IAAAC,sEAAA,2BAIc,IAAAC,sEAAA,2BAIlB;;IAI7CvC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAbarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAsC,MAAA,CAAAxB,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAIxBF,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,0CAAsC;IAItCF,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,wBAAoB;;;;;;IAlB/CF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAkC;IACtCD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAsC;IAClCN,EAAA,CAAAiB,MAAA,GACJ;;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAuB,uDAAA,2BAA+D;IAoBvDzC,EAFR,CAAAM,cAAA,SAAI,cACqC,iBAEJ;IAAzBN,EAAA,CAAAO,UAAA,mBAAAmC,iEAAA;MAAA,MAAAV,OAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAyC,QAAA,CAAAZ,OAAA,CAAc;IAAA,EAAC;IAAChC,EAAA,CAAAqB,YAAA,EAAS;IACtCrB,EAAA,CAAAM,cAAA,kBACqE;IAAjEN,EAAA,CAAAO,UAAA,mBAAAsC,kEAAAC,MAAA;MAAA,MAAAd,OAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASiC,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAAEX,MAAA,CAAA6C,aAAA,CAAAhB,OAAA,EAAmB,QAAQ,CAAC;IAAA,EAAC;IAGhFhC,EAHiF,CAAAqB,YAAA,EAAS,EAC5E,EACL,EACJ;;;;;IAhCoBrB,EAAA,CAAAsB,SAAA,GAAc;IAAdtB,EAAA,CAAAE,UAAA,UAAA8B,OAAA,CAAc;IAG/BhC,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAiB,UAAA,IAAAjD,EAAA,CAAAkC,WAAA,OAAAF,OAAA,CAAAiB,UAAA,2BACJ;IAE8BjD,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,kBAAA,WAA+B;;;;;IA+B7D/B,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,6BAAsB;IACvEjB,EADuE,CAAAqB,YAAA,EAAK,EACvE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC6C;IACzCN,EAAA,CAAAiB,MAAA,kDACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAQbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAcRrB,EAAA,CAAAM,cAAA,UAIN;IACUN,EAAA,CAAAiB,MAAA,gCACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAPVrB,EAAA,CAAAM,cAAA,cAAiE;IAC7DN,EAAA,CAAAkB,UAAA,IAAAgC,sCAAA,kBAIN;IAGElD,EAAA,CAAAqB,YAAA,EAAM;;;;IAPIrB,EAAA,CAAAsB,SAAA,EAIf;IAJetB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAgD,SAAA,IAAAhD,MAAA,CAAAiD,CAAA,eAAAC,MAAA,IAAAlD,MAAA,CAAAiD,CAAA,eAAAC,MAAA,aAIf;;;;;IAgBSrD,EAAA,CAAAM,cAAA,UAIN;IACUN,EAAA,CAAAiB,MAAA,8BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAPVrB,EAAA,CAAAM,cAAA,cAA+D;IAC3DN,EAAA,CAAAkB,UAAA,IAAAoC,sCAAA,kBAIN;IAGEtD,EAAA,CAAAqB,YAAA,EAAM;;;;IAPIrB,EAAA,CAAAsB,SAAA,EAIf;IAJetB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAgD,SAAA,IAAAhD,MAAA,CAAAiD,CAAA,aAAAC,MAAA,IAAAlD,MAAA,CAAAiD,CAAA,aAAAC,MAAA,aAIf;;;;;IAiBarD,EAAA,CAAAM,cAAA,WAAwB;IAACN,EAAA,CAAAiB,MAAA,GAAiB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAxBrB,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAAuB,kBAAA,QAAAgC,OAAA,CAAAnB,IAAA,KAAiB;;;;;IAD1CpC,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAiB,MAAA,GAAiC;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAC9CrB,EAAA,CAAAkB,UAAA,IAAAsC,+CAAA,mBAAwB;;;;IADlBxD,EAAA,CAAAsB,SAAA,EAAiC;IAAjCtB,EAAA,CAAAyD,iBAAA,CAAAF,OAAA,CAAApB,sBAAA,CAAiC;IAChCnC,EAAA,CAAAsB,SAAA,EAAe;IAAftB,EAAA,CAAAE,UAAA,SAAAqD,OAAA,CAAAnB,IAAA,CAAe;;;;;IA2ClBpC,EAAA,CAAAC,SAAA,YAKI;;;;IALgED,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,oEAI/E;;;;;IAEWJ,EAAA,CAAAC,SAAA,YAAgF;;;;;IAO5ED,EAAA,CAAAC,SAAA,YAKI;;;;IAL6DD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,oEAI9E;;;;;IAEaJ,EAAA,CAAAC,SAAA,YAA6E;;;;;;IAVzFD,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAM,cAAA,aAA+F;IAA1CN,EAAA,CAAAO,UAAA,mBAAAmD,4EAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAU,aAAA,CAAAkD,IAAA,EAAAhD,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAA4C,OAAA,CAAA3C,KAAA,EAAqB,SAAS,CAAC;IAAA,EAAC;IAC1FhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAMAjB,EANA,CAAAkB,UAAA,IAAA2C,2DAAA,gBAIZ,IAAAC,2DAAA,gBAEqF;IAEjF9D,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IAXDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAyD,OAAA,CAAA3C,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAoC,OAAA,CAAAnC,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,gBAAAkC,OAAA,CAAA3C,KAAA,CAA2C;IAM3ChB,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,gBAAAkC,OAAA,CAAA3C,KAAA,CAA2C;;;;;;IAzB3DhB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAA+D;IAA7CN,EAAA,CAAAO,UAAA,mBAAAwD,6DAAA;MAAA/D,EAAA,CAAAU,aAAA,CAAAsD,GAAA;MAAA,MAAA7D,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,YAAY,EAAC,SAAS,CAAC;IAAA,EAAC;IAC1Df,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,mBACA;IAMAjB,EANA,CAAAkB,UAAA,IAAA+C,4CAAA,gBAIV,IAAAC,4CAAA,gBAEsF;IAEpFlE,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAiD,uDAAA,2BAAgE;IAe5DnE,EADJ,CAAAM,cAAA,SAAI,eACqC;IAAAN,EAAA,CAAAiB,MAAA,eAAO;IAEpDjB,EAFoD,CAAAqB,YAAA,EAAM,EACjD,EACJ;;;;IA1BWrB,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,6BAA8C;IAM9CzB,EAAA,CAAAsB,SAAA,EAA8C;IAA9CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,6BAA8C;IAG5BzB,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,kBAAA,YAAgC;;;;;IAgClD/B,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAnC,QAAA,IAAAjC,EAAA,CAAAkC,WAAA,OAAAkC,WAAA,CAAAnC,QAAA,2BACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAAqC;IACjCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAhC,IAAA,cACJ;;;;;IACApC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAC,YAAA,cACJ;;;;;IACArE,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAE,eAAA,cACJ;;;;;IACAtE,EAAA,CAAAK,uBAAA,GAAwC;IACpCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAG,OAAA,cACJ;;;;;IACAvE,EAAA,CAAAK,uBAAA,GAAsC;IAClCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAI,KAAA,cACJ;;;;;IACAxE,EAAA,CAAAK,uBAAA,GAAoC;IAChCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAK,GAAA,cACJ;;;;;IACAzE,EAAA,CAAAK,uBAAA,GAAuC;IACnCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAM,MAAA,cACJ;;;;;IACA1E,EAAA,CAAAK,uBAAA,GAAsC;IAClCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAO,KAAA,cACJ;;;;;IACA3E,EAAA,CAAAK,uBAAA,GAAwC;IACpCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAQ,OAAA,cACJ;;;;;IAjCZ5E,EAAA,CAAAK,uBAAA,GAAgE;IAC5DL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IA6BjCL,EA5BA,CAAAkB,UAAA,IAAA2D,sEAAA,2BAAyC,IAAAC,sEAAA,2BAIJ,IAAAC,sEAAA,2BAGQ,IAAAC,sEAAA,2BAGG,IAAAC,sEAAA,2BAGR,IAAAC,sEAAA,2BAGF,IAAAC,sEAAA,2BAGF,KAAAC,uEAAA,2BAGG,KAAAC,uEAAA,2BAGD,KAAAC,uEAAA,2BAGE;;IAIhDtF,EAAA,CAAAqB,YAAA,EAAK;;;;;IAjCarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAqF,OAAA,CAAAvE,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAIxBF,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,wBAAoB;IAGpBF,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAG5BF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAG/BF,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,2BAAuB;IAGvBF,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,yBAAqB;IAGrBF,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAE,UAAA,uBAAmB;IAGnBF,EAAA,CAAAsB,SAAA,EAAsB;IAAtBtB,EAAA,CAAAE,UAAA,0BAAsB;IAGtBF,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,yBAAqB;IAGrBF,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,2BAAuB;;;;;;IAtClDF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAqC;IACzCD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAsC;IAClCN,EAAA,CAAAiB,MAAA,GACJ;;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAsE,uDAAA,6BAAgE;IAwCxDxF,EAFR,CAAAM,cAAA,SAAI,cACqC,iBAEwC;IAArEN,EAAA,CAAAO,UAAA,mBAAAkF,iEAAA3C,MAAA;MAAA,MAAAsB,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAAgF,IAAA,EAAA9E,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASiC,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA/C,EAAA,CAAAc,WAAA,CAAEX,MAAA,CAAA6C,aAAA,CAAAoB,WAAA,EAAsB,SAAS,CAAC;IAAA,EAAC;IAGpFpE,EAHqF,CAAAqB,YAAA,EAAS,EAChF,EACL,EACJ;;;;;IAlDoBrB,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAAE,UAAA,UAAAkE,WAAA,CAAiB;IAGlCpE,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA6C,WAAA,kBAAAA,WAAA,CAAAnB,UAAA,IAAAjD,EAAA,CAAAkC,WAAA,OAAAkC,WAAA,CAAAnB,UAAA,2BACJ;IAE8BjD,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,kBAAA,YAAgC;;;;;IAiD9D/B,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,0BAAmB;IACrEjB,EADqE,CAAAqB,YAAA,EAAK,EACrE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAC1CN,EAAA,CAAAiB,MAAA,+CACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;ADzRrB,OAAM,MAAOsE,gBAAgB;EA6B3BC,YACUC,KAAqB,EACrBC,qBAA4C,EAC5CC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAJxC,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAjCrB,KAAAC,YAAY,GAAG,IAAI7G,OAAO,EAAQ;IACnC,KAAA8G,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,cAAc,GAAU,EAAE;IAE1B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,IAAIjH,OAAO,EAAU;IAClC,KAAA8C,sBAAsB,GAAW,EAAE;IACnC,KAAAoE,sBAAsB,GAAY,KAAK;IACvC,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAvD,SAAS,GAAG,KAAK;IACjB,KAAAwD,MAAM,GAAW,EAAE;IACnB,KAAAC,MAAM,GAAG,KAAK;IACb,KAAAC,cAAc,GAAQ,EAAE;IAEzB,KAAAC,cAAc,GAAc,IAAI,CAACf,WAAW,CAACgB,KAAK,CAAC;MACxD9D,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAAC4H,QAAQ,CAAC,CAAC;MACvC/E,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC4H,QAAQ,CAAC,CAAC;MACrC7E,sBAAsB,EAAE,CAAC,EAAE;KAC5B,CAAC;IAEK,KAAA8E,WAAW,GAAc,IAAI,CAAClB,WAAW,CAACgB,KAAK,CAAC;MACrD9D,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAAC4H,QAAQ,CAAC,CAAC;MACvC/E,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC4H,QAAQ,CAAC,CAAC;MACrCE,4BAA4B,EAAE,CAAC,EAAE;KAClC,CAAC;IAUM,KAAAC,mBAAmB,GAAgC;MACzDC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;KACV;IAEM,KAAAC,IAAI,GAAa,CACtB;MAAEtG,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAU,CAAE,EACzC;MAAER,KAAK,EAAE,wBAAwB;MAAEQ,MAAM,EAAE;IAAgB,CAAE,EAC7D;MAAER,KAAK,EAAE,MAAM;MAAEQ,MAAM,EAAE;IAAkB,CAAE,CAC9C;IAEM,KAAA+F,WAAW,GAAa,CAC7B;MAAEvG,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAU,CAAE,EACzC;MAAER,KAAK,EAAE,MAAM;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACjC;MAAER,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAc,CAAE,EACjD;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EACvD;MAAER,KAAK,EAAE,SAAS;MAAEQ,MAAM,EAAE;IAAS,CAAE,EACvC;MAAER,KAAK,EAAE,OAAO;MAAEQ,MAAM,EAAE;IAAO,CAAE,EACnC;MAAER,KAAK,EAAE,KAAK;MAAEQ,MAAM,EAAE;IAAK,CAAE,EAC/B;MAAER,KAAK,EAAE,QAAQ;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrC;MAAER,KAAK,EAAE,OAAO;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACpC;MAAER,KAAK,EAAE,SAAS;MAAEQ,MAAM,EAAE;IAAS,CAAE,CACxC;IAED;IACA,KAAAC,YAAY,GAA8B;MACxC2F,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;KACV;IACD,KAAAjH,YAAY,GAA8B;MACxCgH,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;KACV;EAlCE;EAoCH;EACAtG,UAAUA,CAACC,KAAa,EAAEwG,MAA4B;IACpD,IAAIC,WAAW;IACf,IAAID,MAAM,KAAK,QAAQ,EAAE;MACvBC,WAAW,GAAG,IAAI,CAACtB,iBAAiB;IACtC,CAAC,MAAM,IAAIqB,MAAM,KAAK,SAAS,EAAE;MAC/BC,WAAW,GAAG,IAAI,CAACrB,cAAc;IACnC,CAAC,MAAM;MACLsB,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEH,MAAM,CAAC;MACvC;IACF;IAEA,IAAII,YAAY,GAAG,IAAI,CAACnG,YAAY,CAAC+F,MAAM,CAAC;IAC5C,IAAIK,YAAY,GAAG,IAAI,CAACzH,YAAY,CAACoH,MAAM,CAAC;IAE5C,IAAII,YAAY,KAAK5G,KAAK,EAAE;MAC1B6G,YAAY,GAAG,CAACA,YAAY;IAC9B,CAAC,MAAM;MACLD,YAAY,GAAG5G,KAAK;MACpB6G,YAAY,GAAG,CAAC;IAClB;IAEA,IAAI,CAACpG,YAAY,CAAC+F,MAAM,CAAC,GAAGI,YAAY;IACxC,IAAI,CAACxH,YAAY,CAACoH,MAAM,CAAC,GAAGK,YAAY;IAExCJ,WAAW,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACxB,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE/G,KAAK,CAAC;MAC9C,MAAMmH,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEhH,KAAK,CAAC;MAE9C,IAAIoH,MAAM,GAAG,CAAC;MACd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAAE;QACjEC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC;MACvC,CAAC,MAAM;QACLC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACzD;MAEA,OAAON,YAAY,GAAGO,MAAM;IAC9B,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAEtH,KAAa;IACvC,IAAI,CAACsH,IAAI,IAAI,CAACtH,KAAK,EAAE,OAAO,IAAI;IAChC,OAAOA,KAAK,CAACuH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAC5BD,IAAI,CAACtH,KAAK,CAAC,GACXA,KAAK,CAACwH,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;EAC7D;EAEA;EACAvG,kBAAkBA,CAACyF,MAA4B;IAC7C,OAAO,IAAI,CAACL,mBAAmB,CAACK,MAAM,CAAC;EACzC;EAEAoB,kBAAkBA,CAACpB,MAA4B,EAAEqB,GAAa;IAC5D,MAAMC,QAAQ,GAAGtB,MAAM,KAAK,QAAQ,GAAG,IAAI,CAACF,IAAI,GAAG,IAAI,CAACC,WAAW;IACnE,IAAI,CAACJ,mBAAmB,CAACK,MAAM,CAAC,GAAGsB,QAAQ,CAACC,MAAM,CAAEC,GAAG,IACrDH,GAAG,CAACI,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEA;EACAE,eAAeA,CAACC,KAAU,EAAE3B,MAA4B;IACtD,MAAM4B,UAAU,GAAG,IAAI,CAACjC,mBAAmB,CAACK,MAAM,CAAC,CAAC2B,KAAK,CAACE,SAAS,CAAC;IACpE,IAAI,CAAClC,mBAAmB,CAACK,MAAM,CAAC,CAAC8B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAC3D,IAAI,CAAClC,mBAAmB,CAACK,MAAM,CAAC,CAAC8B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACzE;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACtH,sBAAsB,GACzB,IAAI,CAAC0D,KAAK,CAAC6D,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAAC/D,qBAAqB,CAACgE,cAAc,CACtCC,IAAI,CAACzK,SAAS,CAAC,IAAI,CAAC4G,YAAY,CAAC,CAAC,CAClC8D,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC9D,iBAAiB,GAAG+D,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAC5CA,QAAQ,GACRA,QAAQ,EAAE3B,IAAI,IAAI,EAAE;QACxB,IAAI,CAAClC,cAAc,GAAG6D,QAAQ,IAAI,EAAE;MACtC;IACF,CAAC,CAAC;IAEJ,IAAI,CAAC9C,mBAAmB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAACG,IAAI;IAC9C,IAAI,CAACH,mBAAmB,CAAC,SAAS,CAAC,GAAG,IAAI,CAACI,WAAW;EACxD;EAEQkC,cAAcA,CAAA;IACpB,IAAI,CAACW,MAAM,GAAG7K,MAAM,CAClBE,EAAE,CAAC,IAAI,CAACoH,cAAc,CAAC;IAAE;IACzB,IAAI,CAACP,UAAU,CAACyD,IAAI,CAClBjK,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBJ,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACyG,WAAW,GAAG,IAAK,CAAC,EACpC1G,SAAS,CAAE0K,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,WAAW,EAAE,wBAAwB;QACrC,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,qDAAqD,CAAC,GAC3DD,IAAI;QACNC,MAAM,CAAC,mCAAmC,CAAC,GAAGD,IAAI;MACpD;MAEA,OAAO,IAAI,CAACvE,qBAAqB,CAACyE,aAAa,CAACD,MAAM,CAAC,CAACP,IAAI,CAC1DvK,GAAG,CAAEyK,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxCpK,UAAU,CAAE2K,KAAK,IAAI;QACnB9C,OAAO,CAAC8C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO/K,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFM,QAAQ,CAAC,MAAO,IAAI,CAACsG,WAAW,GAAG,KAAM,CAAC,CAAC;OAC5C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAzD,QAAQA,CAAC6H,IAAS;IAChB,IAAI,CAAClE,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACI,MAAM,GAAG8D,IAAI,EAAEC,UAAU;IAE9B,IAAI,CAAC5D,cAAc,CAAC6D,UAAU,CAAC;MAC7B1H,UAAU,EAAEwH,IAAI,EAAExH,UAAU,GAAG,IAAI2H,IAAI,CAACH,IAAI,EAAExH,UAAU,CAAC,GAAG,IAAI;MAChEhB,QAAQ,EAAEwI,IAAI,EAAExI,QAAQ,GAAG,IAAI2I,IAAI,CAACH,IAAI,EAAExI,QAAQ,CAAC,GAAG,IAAI;MAC1DE,sBAAsB,EAAEsI,IAAI,EAAEtI;KAC/B,CAAC;EACJ;EAEM0I,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC3H,SAAS,GAAG,IAAI;MACrB2H,KAAI,CAACrE,OAAO,GAAG,IAAI;MAEnB,IAAIqE,KAAI,CAAChE,cAAc,CAACkE,OAAO,EAAE;QAC/BtD,OAAO,CAACuD,GAAG,CAAC,kBAAkB,EAAEH,KAAI,CAAChE,cAAc,CAACzD,MAAM,CAAC;QAC3DyH,KAAI,CAACrE,OAAO,GAAG,IAAI;QACnB;MACF;MAEAqE,KAAI,CAAClE,MAAM,GAAG,IAAI;MAClB,MAAMsE,KAAK,GAAG;QAAE,GAAGJ,KAAI,CAAChE,cAAc,CAACoE;MAAK,CAAE;MAE9C,MAAM5C,IAAI,GAAG;QACXrF,UAAU,EAAEiI,KAAK,EAAEjI,UAAU,GAAG6H,KAAI,CAACK,UAAU,CAACD,KAAK,CAACjI,UAAU,CAAC,GAAG,IAAI;QACxEhB,QAAQ,EAAEiJ,KAAK,EAAEjJ,QAAQ,GAAG6I,KAAI,CAACK,UAAU,CAACD,KAAK,CAACjJ,QAAQ,CAAC,GAAG,IAAI;QAClEE,sBAAsB,EAAE+I,KAAK,EAAE/I,sBAAsB;QACrDiJ,6BAA6B,EAAEN,KAAI,CAAC3I;OACrC;MAED,IAAIkJ,YAA6B;MAEjC,IAAIP,KAAI,CAACnE,MAAM,EAAE;QACf0E,YAAY,GAAGP,KAAI,CAAChF,qBAAqB,CAACwF,oBAAoB,CAC5DR,KAAI,CAACnE,MAAM,EACX2B,IAAI,CACL;MACH,CAAC,MAAM;QACL+C,YAAY,GAAGP,KAAI,CAAChF,qBAAqB,CAACyF,oBAAoB,CAACjD,IAAI,CAAC;MACtE;MAEA+C,YAAY,CAACtB,IAAI,CAACzK,SAAS,CAACwL,KAAI,CAAC5E,YAAY,CAAC,CAAC,CAAC8D,SAAS,CAAC;QACxDwB,QAAQ,EAAEA,CAAA,KAAK;UACbV,KAAI,CAAClE,MAAM,GAAG,KAAK;UACnBkE,KAAI,CAACvE,sBAAsB,GAAG,KAAK;UACnCuE,KAAI,CAAChE,cAAc,CAAC2E,KAAK,EAAE;UAC3BX,KAAI,CAAC9E,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAEd,KAAI,CAACnE,MAAM,GACf,mCAAmC,GACnC;WACL,CAAC;UACFmE,KAAI,CAAChF,qBAAqB,CACvB+F,mBAAmB,CAACf,KAAI,CAAC3I,sBAAsB,CAAC,CAChD4H,IAAI,CAACzK,SAAS,CAACwL,KAAI,CAAC5E,YAAY,CAAC,CAAC,CAClC8D,SAAS,EAAE;QAChB,CAAC;QACDQ,KAAK,EAAEA,CAAA,KAAK;UACVM,KAAI,CAAClE,MAAM,GAAG,KAAK;UACnBkE,KAAI,CAACvE,sBAAsB,GAAG,KAAK;UACnCuE,KAAI,CAAC9E,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEME,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MACnBgB,MAAI,CAAC5I,SAAS,GAAG,IAAI;MACrB4I,MAAI,CAACtF,OAAO,GAAG,IAAI;MAEnB,IAAIsF,MAAI,CAAC9E,WAAW,CAAC+D,OAAO,EAAE;QAC5BtD,OAAO,CAACuD,GAAG,CAAC,kBAAkB,EAAEc,MAAI,CAAC9E,WAAW,CAAC5D,MAAM,CAAC;QACxD0I,MAAI,CAACtF,OAAO,GAAG,IAAI;QACnB;MACF;MAEAsF,MAAI,CAACnF,MAAM,GAAG,IAAI;MAClB,MAAMsE,KAAK,GAAG;QAAE,GAAGa,MAAI,CAAC9E,WAAW,CAACiE;MAAK,CAAE;MAE3C,MAAM5C,IAAI,GAAG;QACXrF,UAAU,EAAEiI,KAAK,EAAEjI,UAAU,GAAG8I,MAAI,CAACZ,UAAU,CAACD,KAAK,CAACjI,UAAU,CAAC,GAAG,IAAI;QACxEhB,QAAQ,EAAEiJ,KAAK,EAAEjJ,QAAQ,GAAG8J,MAAI,CAACZ,UAAU,CAACD,KAAK,CAACjJ,QAAQ,CAAC,GAAG,IAAI;QAClEiF,4BAA4B,EAAEgE,KAAK,EAAEhE,4BAA4B;QACjE/E,sBAAsB,EAAE4J,MAAI,CAAC5J;OAC9B;MAED4J,MAAI,CAACjG,qBAAqB,CACvBkG,aAAa,CAAC1D,IAAI,CAAC,CACnByB,IAAI,CAACzK,SAAS,CAACyM,MAAI,CAAC7F,YAAY,CAAC,CAAC,CAClC8D,SAAS,CAAC;QACTwB,QAAQ,EAAEA,CAAA,KAAK;UACbO,MAAI,CAACnF,MAAM,GAAG,KAAK;UACnBmF,MAAI,CAACvF,uBAAuB,GAAG,KAAK;UACpCuF,MAAI,CAAC9E,WAAW,CAACwE,KAAK,EAAE;UACxBM,MAAI,CAAC/F,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFG,MAAI,CAACjG,qBAAqB,CACvB+F,mBAAmB,CAACE,MAAI,CAAC5J,sBAAsB,CAAC,CAChD4H,IAAI,CAACzK,SAAS,CAACyM,MAAI,CAAC7F,YAAY,CAAC,CAAC,CAClC8D,SAAS,EAAE;QAChB,CAAC;QACDQ,KAAK,EAAEA,CAAA,KAAK;UACVuB,MAAI,CAACnF,MAAM,GAAG,KAAK;UACnBmF,MAAI,CAACvF,uBAAuB,GAAG,KAAK;UACpCuF,MAAI,CAAC/F,cAAc,CAAC0F,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAT,UAAUA,CAACc,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEAxJ,aAAaA,CAAC0J,IAAS,EAAElF,MAAc;IACrC,IAAI,CAACvB,mBAAmB,CAAC0G,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEpL,MAAM,EAAE,SAAS;MACjBqL,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACL,IAAI,EAAElF,MAAM,CAAC;MAC3B;KACD,CAAC;EACJ;EAEAuF,MAAMA,CAACL,IAAS,EAAElF,MAAc;IAC9B,IAAIwF,gBAAgB;IAEpB,IAAIxF,MAAM,KAAK,QAAQ,EAAE;MACvBwF,gBAAgB,GAAG,IAAI,CAAClH,qBAAqB,CAACmH,cAAc,CAC1DP,IAAI,CAAChC,UAAU,CAChB;IACH,CAAC,MAAM,IAAIlD,MAAM,KAAK,SAAS,EAAE;MAC/BwF,gBAAgB,GAAG,IAAI,CAAClH,qBAAqB,CAACoH,aAAa,CACzDR,IAAI,CAAChC,UAAU,CAChB;IACH,CAAC,MAAM;MACLhD,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEH,MAAM,CAAC;MACvC;IACF;IACAwF,gBAAgB,CAACjD,IAAI,CAACzK,SAAS,CAAC,IAAI,CAAC4G,YAAY,CAAC,CAAC,CAAC8D,SAAS,CAAC;MAC5DmD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnH,cAAc,CAAC0F,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC9F,qBAAqB,CACvB+F,mBAAmB,CAAC,IAAI,CAAC1J,sBAAsB,CAAC,CAChD4H,IAAI,CAACzK,SAAS,CAAC,IAAI,CAAC4G,YAAY,CAAC,CAAC,CAClC8D,SAAS,EAAE;MAChB,CAAC;MACDQ,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxE,cAAc,CAAC0F,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAEAwB,aAAaA,CAAC1G,QAAgB,EAAE2G,MAAc;IAC5C,IAAI,CAAC3G,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACvD,SAAS,GAAG,KAAK;IAEtB,IAAIkK,MAAM,KAAK,QAAQ,EAAE;MACvB,IAAI,CAAC9G,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAACO,cAAc,CAAC2E,KAAK,EAAE;IAC7B,CAAC,MAAM,IAAI4B,MAAM,KAAK,SAAS,EAAE;MAC/B,IAAI,CAAC7G,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAACS,WAAW,CAACwE,KAAK,EAAE;IAC1B;EACF;EAEA,IAAIrI,CAACA,CAAA;IACH,OAAO,IAAI,CAAC0D,cAAc,CAACwG,QAAQ;EACrC;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACtG,WAAW,CAACqG,QAAQ;EAClC;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACtH,YAAY,CAACiH,IAAI,EAAE;IACxB,IAAI,CAACjH,YAAY,CAACsF,QAAQ,EAAE;EAC9B;;;uBAnYW7F,gBAAgB,EAAA3F,EAAA,CAAAyN,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3N,EAAA,CAAAyN,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAA7N,EAAA,CAAAyN,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/N,EAAA,CAAAyN,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAjO,EAAA,CAAAyN,iBAAA,CAAAO,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAhBvI,gBAAgB;MAAAwI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBrBzO,EAFR,CAAAM,cAAA,aAA2D,aACuC,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,kBAAW;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAE3DrB,EADJ,CAAAM,cAAA,aAAmD,kBAE4B;UADjDN,EAAA,CAAAO,UAAA,mBAAAoO,oDAAA;YAAA,OAASD,GAAA,CAAAtB,aAAA,CAAc,OAAO,EAAC,QAAQ,CAAC;UAAA,EAAC;UAAnEpN,EAAA,CAAAqB,YAAA,EAC2E;UAE3ErB,EAAA,CAAAM,cAAA,uBAIF;UAHMN,EAAA,CAAAO,UAAA,2BAAAqO,iEAAA9L,MAAA;YAAA,OAAiB4L,GAAA,CAAA9F,kBAAA,CAAmB,QAAQ,EAAA9F,MAAA,CAAS;UAAA,EAAC;UAMlE9C,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAGgF;UAA7CN,EAAlD,CAAAO,UAAA,0BAAAsO,0DAAA/L,MAAA;YAAA,OAAgB4L,GAAA,CAAAxF,eAAA,CAAApG,MAAA,EAAuB,QAAQ,CAAC;UAAA,EAAC,oBAAAgM,oDAAAhM,MAAA;YAAA,OAAW4L,GAAA,CAAA3N,UAAA,CAAA+B,MAAA,CAAA9B,KAAA,EAAwB,QAAQ,CAAC;UAAA,EAAC;UAiF9FhB,EAhFA,CAAAkB,UAAA,IAAA6N,uCAAA,0BAAgC,KAAAC,wCAAA,0BAqC6B,KAAAC,wCAAA,0BAsCvB,KAAAC,wCAAA,0BAKD;UASjDlP,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAM,cAAA,oBAC0D;UADjCN,EAAA,CAAAmP,gBAAA,2BAAAC,6DAAAtM,MAAA;YAAA9C,EAAA,CAAAqP,kBAAA,CAAAX,GAAA,CAAAnI,sBAAA,EAAAzD,MAAA,MAAA4L,GAAA,CAAAnI,sBAAA,GAAAzD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UAEzD9C,EAAA,CAAAkB,UAAA,KAAAoO,wCAAA,yBAAgC;UAOpBtP,EAHZ,CAAAM,cAAA,gBAA2E,eAClB,iBACiD,gBACvD;UAAAN,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,mBACnD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,sBAEwE;UACxED,EAAA,CAAAkB,UAAA,KAAAqO,gCAAA,kBAAiE;UAUzEvP,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBAC+C,gBACrD;UAAAN,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,iBACnD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,sBAEsE;UACtED,EAAA,CAAAkB,UAAA,KAAAsO,gCAAA,kBAA+D;UAUvExP,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACqD,gBAC3D;UAAAN,EAAA,CAAAiB,MAAA,oBAAY;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,uBAC9D;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UAEJrB,EADJ,CAAAM,cAAA,eAAwC,qBAImD;;UACnFN,EAAA,CAAAkB,UAAA,KAAAuO,wCAAA,0BAA2C;UAMvDzP,EAFQ,CAAAqB,YAAA,EAAY,EACV,EACJ;UAEFrB,EADJ,CAAAM,cAAA,eAAoD,kBAGH;UAAzCN,EAAA,CAAAO,UAAA,mBAAAmP,mDAAA;YAAA,OAAAhB,GAAA,CAAAnI,sBAAA,GAAkC,KAAK;UAAA,EAAC;UAACvG,EAAA,CAAAqB,YAAA,EAAS;UACtDrB,EAAA,CAAAM,cAAA,kBACyB;UAArBN,EAAA,CAAAO,UAAA,mBAAAoP,mDAAA;YAAA,OAASjB,GAAA,CAAA7D,QAAA,EAAU;UAAA,EAAC;UAGpC7K,EAHqC,CAAAqB,YAAA,EAAS,EAChC,EACH,EACA;UAIHrB,EAFR,CAAAM,cAAA,cAA2D,cACuC,aAC3C;UAAAN,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAKvDrB,EAJJ,CAAAM,cAAA,cAAmD,wBAQjD;UAHMN,EAAA,CAAAO,UAAA,2BAAAqP,kEAAA9M,MAAA;YAAA,OAAiB4L,GAAA,CAAA9F,kBAAA,CAAmB,SAAS,EAAA9F,MAAA,CAAS;UAAA,EAAC;UAMnE9C,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,cAAuB,mBAGoC;UAAnDN,EAAA,CAAAO,UAAA,0BAAAsP,2DAAA/M,MAAA;YAAA,OAAgB4L,GAAA,CAAAxF,eAAA,CAAApG,MAAA,EAAuB,SAAS,CAAC;UAAA,EAAC;UAmGlD9C,EAlGA,CAAAkB,UAAA,KAAA4O,wCAAA,0BAAgC,KAAAC,wCAAA,0BAqCgC,KAAAC,wCAAA,0BAwD1B,KAAAC,wCAAA,0BAKD;UASjDjQ,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UAjT0BrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzDF,EAAA,CAAAsB,SAAA,EAAgB;UAEQtB,EAFxB,CAAAE,UAAA,YAAAwO,GAAA,CAAApH,IAAA,CAAgB,YAAAoH,GAAA,CAAA3M,kBAAA,WAAyC,2IAI3E;UAMQ/B,EAAA,CAAAsB,SAAA,GAA2B;UACuCtB,EADlE,CAAAE,UAAA,UAAAwO,GAAA,CAAAvI,iBAAA,CAA2B,YAAyB,mBAAmB,cAAc,oBAC7C,4BAAqD;UA4FhDnG,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAkQ,UAAA,CAAAlQ,EAAA,CAAAmQ,eAAA,KAAAC,GAAA,EAA4B;UAAhFpQ,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAAqQ,gBAAA,YAAA3B,GAAA,CAAAnI,sBAAA,CAAoC;UACzDvG,EADuF,CAAAE,UAAA,qBAAoB,oBACxF;UAKbF,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAE,UAAA,cAAAwO,GAAA,CAAA5H,cAAA,CAA4B;UAO0D9G,EAAA,CAAAsB,SAAA,GAAiB;UAE7FtB,EAF4E,CAAAE,UAAA,kBAAiB,YAAAF,EAAA,CAAAsQ,eAAA,KAAAC,GAAA,EAAA7B,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtL,CAAA,eAAAC,MAAA,EAE5B;UAC/DrD,EAAA,CAAAsB,SAAA,EAAyC;UAAzCtB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtL,CAAA,eAAAC,MAAA,CAAyC;UAiB+BrD,EAAA,CAAAsB,SAAA,GAAiB;UAE3FtB,EAF0E,CAAAE,UAAA,kBAAiB,YAAAF,EAAA,CAAAsQ,eAAA,KAAAC,GAAA,EAAA7B,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtL,CAAA,aAAAC,MAAA,EAE5B;UAC7DrD,EAAA,CAAAsB,SAAA,EAAuC;UAAvCtB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtL,CAAA,aAAAC,MAAA,CAAuC;UAmBzBrD,EAAA,CAAAsB,SAAA,GAAkE;UAAlEtB,EAAA,CAAAwQ,UAAA,0DAAkE;UADhBxQ,EAFhD,CAAAE,UAAA,UAAAF,EAAA,CAAAyQ,WAAA,SAAA/B,GAAA,CAAAtE,MAAA,EAAwB,sBACrB,YAAAsE,GAAA,CAAArI,WAAA,CAAwB,oBAAoB,cAAAqI,GAAA,CAAApI,UAAA,CACA,wBAAwB;UA0BlFtG,EAAA,CAAAsB,SAAA,IAAuB;UAECtB,EAFxB,CAAAE,UAAA,YAAAwO,GAAA,CAAAnH,WAAA,CAAuB,YAAAmH,GAAA,CAAA3M,kBAAA,YAA0C,2IAInF;UAMQ/B,EAAA,CAAAsB,SAAA,GAAwB;UAC0CtB,EADlE,CAAAE,UAAA,UAAAwO,GAAA,CAAAtI,cAAA,CAAwB,YAAyB,mBAAmB,cAAc,oBAC1C,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}