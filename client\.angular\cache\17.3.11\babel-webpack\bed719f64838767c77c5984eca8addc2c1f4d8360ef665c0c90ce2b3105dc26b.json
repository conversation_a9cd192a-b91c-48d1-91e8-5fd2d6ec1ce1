{"ast": null, "code": "import { inject } from '@angular/core';\nimport { of } from 'rxjs';\nimport { ContentVendorService } from './services/content-vendor.service';\nexport const contentResolver = route => {\n  const contentVendor = inject(ContentVendorService);\n  // Get slug from route data or params\n  const slug = route?.data['slug'];\n  if (slug) {\n    return contentVendor.getContentBySlug(slug);\n  } else {\n    return of(null);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}