<div class="p-3 w-full surface-card border-round shadow-1 mb-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Follow-Up Activities for Sales Calls</h4>

        <div class="flex align-items-center gap-3">
            <p-button label="Add" (click)="openActivityFollowUpDialog()" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="ActivitiesCols" [(ngModel)]="selectedActivitiesColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">

        <p-table [value]="followupdetails" dataKey="bp_id" [rows]="10" styleClass="w-full" [paginator]="true"
            [scrollable]="true" [reorderableColumns]="true" (onColReorder)="onActivitiesColumnReorder($event)"
            responsiveLayout="scroll" class="scrollable-table">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg"
                        (click)="customSort('activity_transaction.subject', followupdetails, 'activities')">
                        <div class="flex align-items-center cursor-pointer">
                            Name
                            <i *ngIf="sortFieldActivities === 'activity_transaction.subject'" class="ml-2 pi"
                                [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldActivities !== 'activity_transaction.subject'"
                                class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>

                    <ng-container *ngFor="let col of selectedActivitiesColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field, followupdetails, 'activities')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldActivities === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldActivities !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg text-center">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-followup>
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="text-orange-600 cursor-pointer font-medium underline border-round-left-lg">
                        <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
                            <a [href]="'/#/store/activities/calls/' + followup?.activity_transaction?.activity_id + '/overview'"
                                style="text-decoration: none; color: inherit;" target="_blank">
                                {{ followup?.activity_transaction?.subject || '-' }}
                            </a>
                        </div>
                    </td>
                    <ng-container *ngFor="let col of selectedActivitiesColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'type_code'">
                                    {{ getLabelFromDropdown('activityDocumentType',followup?.type_code) || '-'}}
                                </ng-container>

                                <ng-container *ngSwitchCase="'partner_name'">
                                    {{ followup?.partner_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'createdAt'">
                                    {{ followup?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td class="border-round-right-lg text-center">
                        <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                            (click)="$event.stopPropagation();confirmRemove(followup);"></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">No sales call follow-ups found.
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">Loading sales call follow-up
                        data. Please
                        wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>


<div class="p-3 w-full surface-card border-round shadow-1 mb-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Follow-Up Opportunities</h4>
        <div class="flex align-items-center gap-3">
            <p-button label="Add" (click)="openOpportunityFollowUpDialog()" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="OpportunitiesCols" [(ngModel)]="selectedOpportunitiesColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="followupopportunitydetails" dataKey="bp_id" [rows]="10" styleClass="w-full" [paginator]="true"
            [scrollable]="true" [reorderableColumns]="true" (onColReorder)="onOpportunitiesColumnReorder($event)"
            responsiveLayout="scroll" class="scrollable-table">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg"
                        (click)="customSort('opportunity.name', followupopportunitydetails, 'opportunities')">
                        <div class="flex align-items-center cursor-pointer">
                            Name
                            <i *ngIf="sortFieldOpportunities === 'opportunity.name'" class="ml-2 pi"
                                [ngClass]="sortOrderOpportunities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldOpportunities !== 'opportunity.name'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>

                    <ng-container *ngFor="let col of selectedOpportunitiesColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field, followupopportunitydetails, 'opportunities')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldOpportunities === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderOpportunities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldOpportunities !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg text-center">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-followupopportunity>
                <tr class="cursor-pointer">
                    <td class="text-orange-600 cursor-pointer font-medium underline border-round-left-lg">
                        <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
                            <a [href]="'/#/store/opportunities/' + followupopportunity?.opportunity?.opportunity_id + '/overview'"
                                style="text-decoration: none; color: inherit;" target="_blank">
                                {{ followupopportunity?.opportunity?.name || '-' }}
                            </a>
                        </div>
                    </td>
                    <ng-container *ngFor="let col of selectedOpportunitiesColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'type_code'">
                                    {{ getLabelFromDropdown('activityDocumentType',followupopportunity?.type_code) ||
                                    '-'}}
                                </ng-container>

                                <ng-container *ngSwitchCase="'partner_name'">
                                    {{ followupopportunity?.partner_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'createdAt'">
                                    {{ followupopportunity?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td class="border-round-right-lg text-center">
                        <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                            (click)="$event.stopPropagation();confirmOpportunityRemove(followupopportunity);"></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">No opportunity follow-ups found.
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">Loading opportunity follow up
                        data. Please
                        wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<div class="p-3 w-full surface-card border-round shadow-1 mb-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Follow-Up Activities for Tasks</h4>

        <div class="flex align-items-center gap-3">
            <p-button label="Add" (click)="openActivityTaskFollowUpDialog()" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="ActivitiesTaskCols" [(ngModel)]="selectedActivitiesTaskColumns"
                optionLabel="header" class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">

        <p-table [value]="followuptaskdetails" dataKey="bp_id" [rows]="10" styleClass="w-full" [paginator]="true"
            [scrollable]="true" [reorderableColumns]="true" (onColReorder)="onActivitiesTaskColumnReorder($event)"
            responsiveLayout="scroll" class="scrollable-table">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg"
                        (click)="customSort('activity_transaction.subject', followupdetails, 'task')">
                        <div class="flex align-items-center cursor-pointer">
                            Name
                            <i *ngIf="sortFieldActivitiesTask === 'activity_transaction.subject'" class="ml-2 pi"
                                [ngClass]="sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldActivitiesTask !== 'activity_transaction.subject'"
                                class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>

                    <ng-container *ngFor="let col of selectedActivitiesTaskColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field, followuptaskdetails, 'task')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldActivitiesTask === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldActivitiesTask !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg text-center">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-followuptask>
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="text-orange-600 cursor-pointer font-medium underline border-round-left-lg">
                        <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
                            <a [href]="'/#/store/activities/tasks/' + followuptask?.activity_transaction?.activity_id + '/overview'"
                                style="text-decoration: none; color: inherit;" target="_blank">
                                {{ followuptask?.activity_transaction?.subject || '-' }}
                            </a>
                        </div>
                    </td>
                    <ng-container *ngFor="let col of selectedActivitiesTaskColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'type_code'">
                                    Activity Task
                                </ng-container>

                                <ng-container *ngSwitchCase="'partner_name'">
                                    {{ followuptask?.partner_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'createdAt'">
                                    {{ followuptask?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td class="border-round-right-lg text-center">
                        <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                            (click)="$event.stopPropagation();confirmRemove(followuptask);"></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">No task follow-ups found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="5" class="border-round-left-lg border-round-right-lg">Loading task follow-up data.
                        Please wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<app-opportunities-followup-form></app-opportunities-followup-form>

<app-activities-call-followup-form></app-activities-call-followup-form>

<app-activities-task-followup-form></app-activities-task-followup-form>