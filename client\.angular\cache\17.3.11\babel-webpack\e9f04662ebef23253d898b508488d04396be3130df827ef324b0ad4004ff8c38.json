{"ast": null, "code": "import { AppConstant } from 'src/app/constants/api.constants';\nimport * as moment from 'moment';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../sales-orders.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/progressspinner\";\nfunction SalesOrdersOverviewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesOrdersOverviewComponent_div_1_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesOrdersOverviewComponent_div_1_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 33);\n    i0.ɵɵtext(2, \"Item Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 34);\n    i0.ɵɵtext(4, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 35);\n    i0.ɵɵtext(6, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesOrdersOverviewComponent_div_1_ng_template_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 36)(2, \"div\", 37)(3, \"div\", 38)(4, \"h5\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 41)(9, \"p\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 43)(12, \"p\", 44);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 45);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tableData_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tableData_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", tableData_r1.meterial, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", tableData_r1.quantity, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", tableData_r1.price, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableData_r1.eachPrice, \" each \");\n  }\n}\nfunction SalesOrdersOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"h4\", 7);\n    i0.ɵɵtext(5, \"Order Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"ul\", 9)(8, \"li\", 10)(9, \"div\", 11)(10, \"i\", 12);\n    i0.ɵɵtext(11, \"tag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"h6\", 14);\n    i0.ɵɵtext(14, \"Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"li\", 10)(18, \"div\", 11)(19, \"i\", 12);\n    i0.ɵɵtext(20, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h6\", 14);\n    i0.ɵɵtext(23, \"Customer #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 15);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"li\", 10)(27, \"div\", 11)(28, \"i\", 12);\n    i0.ɵɵtext(29, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 13)(31, \"h6\", 14);\n    i0.ɵɵtext(32, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 15);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"li\", 10)(36, \"div\", 11)(37, \"i\", 12);\n    i0.ɵɵtext(38, \"list_alt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 13)(40, \"h6\", 14);\n    i0.ɵɵtext(41, \"Purchase Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\", 15);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"li\", 10)(45, \"div\", 11)(46, \"i\", 12);\n    i0.ɵɵtext(47, \"calendar_month\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 13)(49, \"h6\", 14);\n    i0.ɵɵtext(50, \"Requested Delivery Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 15);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"li\", 10)(54, \"div\", 11)(55, \"i\", 12);\n    i0.ɵɵtext(56, \"event\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 13)(58, \"h6\", 14);\n    i0.ɵɵtext(59, \"Date Placed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"p\", 15);\n    i0.ɵɵtext(61);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"li\", 10)(63, \"div\", 11)(64, \"i\", 12);\n    i0.ɵɵtext(65, \"description\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 13)(67, \"h6\", 14);\n    i0.ɵɵtext(68, \"Special Instruction\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"p\", 15);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(71, \"div\", 5)(72, \"div\", 6)(73, \"h4\", 7);\n    i0.ɵɵtext(74, \"Shipping Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 8)(76, \"ul\", 9)(77, \"li\", 10)(78, \"div\", 11)(79, \"i\", 12);\n    i0.ɵɵtext(80, \"pin\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 13)(82, \"h6\", 14);\n    i0.ɵɵtext(83, \"Business Partner #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"p\", 15);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(86, \"li\", 10)(87, \"div\", 11)(88, \"i\", 12);\n    i0.ɵɵtext(89, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"div\", 13)(91, \"h6\", 14);\n    i0.ɵɵtext(92, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"p\", 15);\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"li\", 10)(96, \"div\", 11)(97, \"i\", 12);\n    i0.ɵɵtext(98, \"location_on\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(99, \"div\", 13)(100, \"h6\", 14);\n    i0.ɵɵtext(101, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"p\", 15);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(104, \"div\", 16)(105, \"div\", 17)(106, \"h4\", 7);\n    i0.ɵɵtext(107, \"Items to be shipped\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(108, \"div\", 18);\n    i0.ɵɵtemplate(109, SalesOrdersOverviewComponent_div_1_div_109_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementStart(110, \"p-table\", 20);\n    i0.ɵɵtemplate(111, SalesOrdersOverviewComponent_div_1_ng_template_111_Template, 7, 0, \"ng-template\", 21)(112, SalesOrdersOverviewComponent_div_1_ng_template_112_Template, 16, 6, \"ng-template\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(113, \"div\", 23)(114, \"div\", 24)(115, \"h5\", 25);\n    i0.ɵɵtext(116, \"Order Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(117, \"div\", 26)(118, \"ul\", 27)(119, \"li\", 28)(120, \"span\", 29);\n    i0.ɵɵtext(121, \"Subtotal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"li\", 28)(124, \"span\", 29);\n    i0.ɵɵtext(125, \"Tax\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(127, \"li\", 28)(128, \"span\", 29);\n    i0.ɵɵtext(129, \"Shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(131, \"div\", 30)(132, \"h5\", 31);\n    i0.ɵɵtext(133, \"Total \");\n    i0.ɵɵelementStart(134, \"span\");\n    i0.ɵɵtext(135);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.orderId);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.orderData.customer);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r1.customer == null ? null : ctx_r1.customer.customer == null ? null : ctx_r1.customer.customer.customer_name) || \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.orderData.purchaseOrder || \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.orderData.requestedDate);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.orderData.placeDate);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.orderData.specialInstruction);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate((ctx_r1.shipToParty == null ? null : ctx_r1.shipToParty.bp_customer_number) || \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r1.shipToParty == null ? null : ctx_r1.shipToParty.customer == null ? null : ctx_r1.shipToParty.customer.customer_name) || \" - \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r1.shipToParty == null ? null : ctx_r1.shipToParty.address) || \" - \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.tableData);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.summary.subtotal, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.summary.tax, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.summary.shipping, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.summary.total, \"\");\n  }\n}\nexport let SalesOrdersOverviewComponent = /*#__PURE__*/(() => {\n  class SalesOrdersOverviewComponent {\n    constructor(route, salesOrdersService) {\n      this.route = route;\n      this.salesOrdersService = salesOrdersService;\n      this.unsubscribe$ = new Subject();\n      this.loading = false;\n      this.orderId = '';\n      this.orderDetails = {};\n      this.customer = {};\n      this.shipToParty = {};\n      this.orderData = {\n        orderId: '',\n        customer: '',\n        customerName: '',\n        placeDate: '',\n        requestedDate: '',\n        specialIntruction: ''\n      };\n      this.shippingData = {\n        businessPartner: '',\n        name: '',\n        address: ''\n      };\n      this.summary = {};\n      this.tableData = [];\n      this.address = [];\n    }\n    ngOnInit() {\n      this.orderId = this.route.parent?.snapshot.paramMap.get('id') || '';\n      if (this.orderId) {\n        this.fetchOrderDetails();\n      } else {\n        console.error('No order ID provided');\n      }\n    }\n    getPartnerAddress(bp_id, callback) {\n      this.salesOrdersService.fetchPartnerById(bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: value => {\n          const formattedAddresses = value?.data.map(account => {\n            const defaultAddress = account?.address_usages?.find(usage => usage?.address_usage === 'XXDEFAULT')?.business_partner_address;\n            return {\n              ...account,\n              address: [defaultAddress?.house_number || '-', defaultAddress?.street_name || '-', defaultAddress?.city_name || '-', defaultAddress?.region || '-', defaultAddress?.country || '-', defaultAddress?.postal_code || '-'].filter(part => part && part !== '-').join(', ')\n            };\n          }) || [];\n          this.address = formattedAddresses;\n          if (callback && this.address.length > 0) {\n            callback(this.address[0].address);\n          }\n        },\n        error: err => {\n          console.error('Error fetching partner address:', err);\n        }\n      });\n    }\n    getPartnerFunction(soldToParty, shipToParty, bp_id) {\n      this.salesOrdersService.getPartnerFunction(soldToParty).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: value => {\n          this.customer = value.find(o => o.customer_id === soldToParty && o.partner_function === 'SP');\n          this.shipToParty = value.find(o => o.bp_customer_number === shipToParty && o.partner_function === 'SH');\n          this.getPartnerAddress(bp_id, formattedAddress => {\n            if (this.shipToParty) {\n              this.shipToParty.address = formattedAddress;\n            }\n          });\n          return {\n            customer: this.customer,\n            shipToParty: this.shipToParty\n          };\n        },\n        error: err => {\n          console.log('Error while processing get ship to request.', {\n            type: 'Error'\n          });\n        }\n      });\n    }\n    // setOtherDetails(data: any) {\n    //   if (!data.ORDER_LINE_DETAIL?.length) return;\n    //   for (let j = 0; j < data.ORDER_LINE_DETAIL.length; j++) {\n    //     const item = data.ORDER_LINE_DETAIL[j];\n    //     this.setImage(item);\n    //   }\n    // }\n    setImage(item) {\n      item.imageUrl = AppConstant.PRODUCT_IMAGE_FALLBACK;\n      this.salesOrdersService.getImages(item.MATERIAL).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: value => {\n          if (value?.data?.length) {\n            const images = value.data.filter(item => item.dimension == '1200X1200');\n            if (images.length) {\n              item.imageUrl = images[0].url;\n            }\n          }\n        }\n      });\n    }\n    fetchOrderDetails() {\n      this.loading = true;\n      if (!this.orderId) {\n        console.error('No order ID provided');\n        return;\n      }\n      this.salesOrdersService.fetchOrderById(this.orderId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response && response?.INFO && response?.INFO?.STATUS == 'Success' && response?.SALESORDER) {\n            /* order Details */\n            this.orderDetails = response.SALESORDER;\n            this.summary = {\n              tax: response?.SALESORDER?.formatted_sales_tax || '$0.00',\n              total: response?.SALESORDER?.formatted_total || '$0.00',\n              subtotal: response?.SALESORDER?.formatted_sub_total || '$0.00',\n              shipping: response?.SALESORDER?.formatted_shipping || '$0.00'\n            };\n            this.orderData = {\n              orderId: this.orderId,\n              customer: response?.SALESORDER?.SOLDTO?.SOLDTOPARTY || '',\n              customerName: '',\n              placeDate: moment(response?.SALESORDER?.ORDER_HDR?.DOC_DATE, 'YYYYMMDD').format('MM/DD/YYYY') || '-',\n              // new Date(Number(response?.SALESORDER?.ORDER_HDR?.DOC_DATE)).toISOString().slice(0, 10),\n              requestedDate: moment(response?.SALESORDER?.ORDER_HDR?.REQ_DATE, 'YYYYMMDD').format('MM/DD/YYYY') || '-',\n              //  new Date(response?.SALESORDER?.ORDER_HDR?.REQ_DATE).toISOString().slice(0, 10),\n              specialIntruction: '',\n              purchaseOrder: response?.SALESORDER?.ORDER_HDR?.PURCH_NO,\n              specialInstruction: response?.SALESORDER?.ORDER_HDR?.ORDER_HDR_TEXT ? response?.SALESORDER?.ORDER_HDR?.ORDER_HDR_TEXT[0]?.TEXT : '-'\n            };\n            this.getPartnerFunction(this.orderDetails?.SOLDTO?.SOLDTOPARTY, this.orderDetails?.SHIPTO?.SHIPTOPARTY, this.orderDetails?.SHIPTO?.BP_ID);\n            /* shipping Details */\n            this.shippingData = {\n              businessPartner: this.shipToParty.bp_customer_number || '-',\n              name: this.shipToParty?.name || '-',\n              address: this.shipToParty.address || '-'\n            };\n            /* shipped tabledata */\n            response?.SALESORDER?.ORDER_LINE_DETAIL?.map(item => {\n              this.setImage(item);\n              this.tableData.push({\n                description: item.SHORT_TEXT,\n                meterial: item.MATERIAL,\n                quantity: item.REQ_QTY,\n                price: item.formatted_base_price,\n                eachPrice: item.formatted_base_price_each,\n                imageUrl: item.imageUrl\n              });\n            });\n            this.loading = false;\n          } else {\n            console.log('No data found for this order');\n            this.loading = false;\n          }\n        },\n        error: error => {\n          console.error('Error fetching order details:', error);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesOrdersOverviewComponent_Factory(t) {\n        return new (t || SalesOrdersOverviewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.SalesOrdersService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesOrdersOverviewComponent,\n        selectors: [[\"app-sales-orders-overview\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"class\", \"grid mt-0 relative\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"table-data\", \"border-round\", \"overflow-hidden\"], [\"class\", \"flex justify-content-cente  r align-items-center w-full my-4\", 4, \"ngIf\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"lg:w-30rem\", \"md:w-30rem\", \"sm:w-full\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"mt-2\", \"mb-4\", \"uppercase\", \"text-center\", \"text-primary\"], [1, \"cart-sidebar-price\", \"py-4\", \"border-none\", \"border-y-1\", \"border-solid\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"font-semibold\"], [1, \"text-color-secondary\"], [1, \"cart-sidebar-t-price\", \"py-4\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"text-primary\"], [1, \"flex\", \"justify-content-cente\", \"r\", \"align-items-center\", \"w-full\", \"my-4\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"]],\n        template: function SalesOrdersOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SalesOrdersOverviewComponent_div_0_Template, 2, 0, \"div\", 0)(1, SalesOrdersOverviewComponent_div_1_Template, 136, 16, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i3.NgIf, i4.Table, i5.PrimeTemplate, i6.ProgressSpinner],\n        styles: [\".card-heading h4.ml-0{margin-left:0!important}\"]\n      });\n    }\n  }\n  return SalesOrdersOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}