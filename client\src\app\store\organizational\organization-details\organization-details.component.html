<p-toast position="top-center" [life]="3000"></p-toast>
<div class="col-12 all-overview-body m-0 p-0 border-round-lg">
    <div class="filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3">
        <div class="breadcrumb-sec flex align-items-center gap-3">
            <p-breadcrumb [model]="breadcrumbitems" [home]="home" [styleClass]="'py-2 px-0 border-none'" />
        </div>
    </div>

    <div class="details-tabs-sec">
        <div class="details-tabs-list">
            <p-tabView [scrollable]="true" [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
                <p-tabPanel *ngFor="let tab of items; let i = index" [headerStyleClass]="'m-0 p-0'">
                    <ng-template pTemplate="header">
                        <a [routerLink]="tab.routerLink" routerLinkActive="active-tab"
                            class="tab-link flex align-items-center justify-content-center white-space-nowrap">{{
                            tab.label }}</a>
                    </ng-template>
                </p-tabPanel>
            </p-tabView>
        </div>
        <div class="details-tabs-result p-3 bg-whight-light">
            <div class="grid mt-0 relative flex-nowrap">
                <div class="col-12 lg:w-28rem md:w-28rem sm:w-full sidebar-c-details"
                    [class.sidebar-hide]="isSidebarHidden">
                    <div class="w-full bg-white border-round shadow-1 overflow-hidden">
                        <div class="left-side-bar-top p-4 bg-primary overflow-hidden">
                            <div class="flex align-items-start gap-4">
                                <div
                                    class="flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle">
                                    <h5 class="m-0 p-0 text-primary font-bold">{{
                                        organizationDetails?.business_partner?.bp_full_name || "-" | initials }}</h5>
                                </div>
                                <div class="flex flex-column gap-4 flex-1">
                                    <h5 class="mt-3 mb-1 font-semibold">
                                        {{organizationDetails?.business_partner?.bp_full_name || "-"}}
                                    </h5>
                                    <ul class="flex flex-column gap-3 p-0 m-0 list-none">
                                        <li class="flex align-items-center gap-2">
                                            <span class="flex w-9rem">CRM ID</span> :
                                            {{organizationDetails?.business_partner?.bp_id || "-"}}
                                        </li>
                                        <!-- <li class="flex align-items-center gap-2">
                                            <span class="flex w-9rem">S4/HANA ID</span> : 152ASD5585
                                        </li> -->
                                        <li class="flex align-items-center gap-2">
                                            <span class="flex w-9rem">Account Owner </span> : {{partner_role || "-"}}
                                        </li>
                                        <li class="flex align-items-center gap-2">
                                            <span class="flex w-9rem">Main Contact</span> : {{
                                            (organizationDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.first_name
                                            || "-") + " " +
                                            (organizationDetails?.business_partner?.contact_companies?.[0]?.business_partner_person?.last_name
                                            || "-") }}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="left-side-bar-bottom px-3 py-4">
                            <ul class="flex flex-column gap-5 p-0 m-0 list-none">
                                <li class="flex gap-2 font-medium text-color-secondary align-items-start">
                                    <span class="flex w-10rem align-items-center gap-2"><i
                                            class="material-symbols-rounded">location_on</i>
                                        Address</span>
                                    <span class="flex-1">{{sidebarDetails?.[0]?.address || "-"}}</span>
                                </li>
                                <li class="flex gap-2 font-medium text-color-secondary align-items-start">
                                    <span class="flex w-10rem align-items-center gap-2"><i
                                            class="material-symbols-rounded">phone_in_talk</i>
                                        Phone</span>
                                    <span class="flex-1">{{sidebarDetails?.[0]?.phone_number || "-"}}</span>
                                </li>
                                <li class="flex gap-2 font-medium text-color-secondary align-items-start">
                                    <span class="flex w-10rem align-items-center gap-2"><i
                                            class="material-symbols-rounded">phone_in_talk</i> Main
                                        Contact</span>
                                    <span
                                        class="flex-1">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number
                                        || "-"}}</span>
                                </li>
                                <li class="flex gap-2 font-medium text-color-secondary align-items-start">
                                    <span class="flex w-10rem align-items-center gap-2"><i
                                            class="material-symbols-rounded">mail</i> Email</span>
                                    <span class="flex-1">{{sidebarDetails?.[0]?.email_address || "-"}}</span>
                                </li>
                                <li class="flex gap-2 font-medium text-color-secondary align-items-start">
                                    <span class="flex w-10rem align-items-center gap-2"><i
                                            class="material-symbols-rounded">language</i>
                                        Website</span>
                                    <span class="flex-1">{{sidebarDetails?.[0]?.website_url || "-"}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 lg:flex-1 md:flex-1 relative all-page-details">
                    <p-button icon="pi pi-angle-left" [rounded]="true" [outlined]="true"
                        [styleClass]="'p-0 w-2rem h-2rem border-2 surface-0'"
                        class="arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out"
                        (click)="toggleSidebar()" [class.arrow-round]="isSidebarHidden" />
                    <router-outlet></router-outlet>
                </div>
            </div>
        </div>
    </div>
</div>
<p-confirmDialog></p-confirmDialog>