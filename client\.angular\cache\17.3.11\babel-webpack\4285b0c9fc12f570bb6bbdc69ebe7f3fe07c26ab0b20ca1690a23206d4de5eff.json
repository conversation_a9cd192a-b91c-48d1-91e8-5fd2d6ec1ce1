{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/authentication/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routerOptions = {\n  anchorScrolling: 'enabled',\n  useHash: true\n};\nconst routes = [{\n  path: 'store',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./store/store.module').then(m => m.StoreModule)\n}, {\n  path: 'backoffice',\n  // canActivate: [AuthGuard],\n  loadChildren: () => import('./backoffice/backoffice.module').then(m => m.BackofficeModule)\n}, {\n  path: \"auth\",\n  loadChildren: () => import(\"./session/session.module\").then(mod => mod.SessionModule)\n}, {\n  path: '',\n  redirectTo: 'store',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: 'store'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, routerOptions), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routerOptions", "anchorScrolling", "useHash", "routes", "path", "canActivate", "loadChildren", "then", "m", "StoreModule", "BackofficeModule", "mod", "SessionModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './core/authentication/auth.guard';\r\n\r\nconst routerOptions: ExtraOptions = {\r\n  anchorScrolling: 'enabled',\r\n  useHash: true\r\n};\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'store',\r\n    canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./store/store.module').then((m) => m.StoreModule),\r\n  },\r\n  {\r\n    path: 'backoffice',\r\n    // canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./backoffice/backoffice.module').then((m) => m.BackofficeModule),\r\n  },\r\n  {\r\n    path: \"auth\",\r\n    loadChildren: () =>\r\n      import(\"./session/session.module\").then((mod) => mod.SessionModule),\r\n  },\r\n  { path: '', redirectTo: 'store', pathMatch: 'full' },\r\n  { path: '**', redirectTo: 'store' },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes, routerOptions)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AACA,SAAuBA,YAAY,QAAgB,iBAAiB;AACpE,SAASC,SAAS,QAAQ,kCAAkC;;;AAE5D,MAAMC,aAAa,GAAiB;EAClCC,eAAe,EAAE,SAAS;EAC1BC,OAAO,EAAE;CACV;AAED,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,CAACN,SAAS,CAAC;EACxBO,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW;CAC3D,EACD;EACEL,IAAI,EAAE,YAAY;EAClB;EACAE,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,gBAAgB;CAC1E,EACD;EACEN,IAAI,EAAE,MAAM;EACZE,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEI,GAAG,IAAKA,GAAG,CAACC,aAAa;CACrE,EACD;EAAER,IAAI,EAAE,EAAE;EAAES,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEV,IAAI,EAAE,IAAI;EAAES,UAAU,EAAE;AAAO,CAAE,CACpC;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjB,YAAY,CAACkB,OAAO,CAACb,MAAM,EAAEH,aAAa,CAAC,EAC3CF,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAFjBrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}