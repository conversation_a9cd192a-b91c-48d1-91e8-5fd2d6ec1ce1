{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { IdentifyAccountComponent } from './identify-account.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: IdentifyAccountComponent\n}];\nexport let IdentifyAccountRoutingModule = /*#__PURE__*/(() => {\n  class IdentifyAccountRoutingModule {\n    static {\n      this.ɵfac = function IdentifyAccountRoutingModule_Factory(t) {\n        return new (t || IdentifyAccountRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: IdentifyAccountRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return IdentifyAccountRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}