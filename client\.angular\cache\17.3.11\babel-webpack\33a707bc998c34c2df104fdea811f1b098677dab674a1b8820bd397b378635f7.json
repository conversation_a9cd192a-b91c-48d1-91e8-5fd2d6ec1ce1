{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport * as i0 from \"@angular/core\";\nexport let AppConfigModule = /*#__PURE__*/(() => {\n  class AppConfigModule {\n    static {\n      this.ɵfac = function AppConfigModule_Factory(t) {\n        return new (t || AppConfigModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppConfigModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule]\n      });\n    }\n  }\n  return AppConfigModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}