{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nconst _c3 = () => ({\n  width: \"45rem\"\n});\nfunction ActivitiesSalesCallFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Sales Call\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Document Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_12_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"document_type\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_21_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_32_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"input\", 56);\n    i0.ɵɵlistener(\"change\", function ActivitiesSalesCallFormComponent_ng_template_42_Template_input_change_1_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).item;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSelection(item_r5.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template, 2, 1, \"span\", 54)(5, ActivitiesSalesCallFormComponent_ng_template_42_span_5_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.isSelected(item_r5.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_43_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_52_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_81_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_81_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_90_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_99_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_div_99_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"notes\"].errors && ctx_r1.f[\"notes\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 57)(2, \"span\", 58)(3, \"span\", 59);\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"th\", 60)(7, \"span\", 58)(8, \"span\", 59);\n    i0.ɵɵtext(9, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Email Address \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 61)(12, \"span\", 58)(13, \"span\", 59);\n    i0.ɵɵtext(14, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_109_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_ng_template_109_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r7 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteContact(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 62)(1, \"td\")(2, \"div\", 63);\n    i0.ɵɵelement(3, \"input\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"div\", 63);\n    i0.ɵɵelement(6, \"input\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 66);\n    i0.ɵɵtemplate(8, ActivitiesSalesCallFormComponent_ng_template_109_button_8_Template, 1, 0, \"button\", 67);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", contact_r8);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.involved_parties.length > 1);\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_121_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_121_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_121_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction ActivitiesSalesCallFormComponent_ng_template_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesSalesCallFormComponent_ng_template_121_span_2_Template, 2, 1, \"span\", 54)(3, ActivitiesSalesCallFormComponent_ng_template_121_span_3_Template, 2, 1, \"span\", 54)(4, ActivitiesSalesCallFormComponent_ng_template_121_span_4_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile);\n  }\n}\nexport class ActivitiesSalesCallFormComponent {\n  constructor(formBuilder, route, activitiesservice, messageservice) {\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.visible = false;\n    this.onClose = new EventEmitter();\n    this.activity_id = '';\n    this.submitted = false;\n    this.saving = false;\n    this.position = 'right';\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.defaultOptions = [];\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.existingcontactLoading = false;\n    this.existingcontactInput$ = new Subject();\n    this.owner_id = null;\n    this.FollowUpForm = this.formBuilder.group({\n      document_type: ['', [Validators.required]],\n      subject: ['', [Validators.required]],\n      main_account_party_id: ['', [Validators.required]],\n      main_contact_party_id: ['', [Validators.required]],\n      phone_call_category: ['', [Validators.required]],\n      disposition_code: [''],\n      start_date: [''],\n      end_date: [''],\n      initiator_code: ['', [Validators.required]],\n      activity_status: ['', [Validators.required]],\n      notes: ['', [Validators.required]],\n      contactexisting: [''],\n      involved_parties: this.formBuilder.array([this.createContactFormGroup()])\n    });\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityCategory: [],\n      activityStatus: [],\n      activitydisposition: [],\n      activityInitiatorCode: []\n    };\n  }\n  ngOnInit() {\n    this.activity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    setTimeout(() => {\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n    }, 50);\n    this.FollowUpForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.getOwner().subscribe({\n      next: response => {\n        this.owner_id = response;\n      },\n      error: err => {\n        console.error('Error fetching bp_id:', err);\n      }\n    });\n    this.loadAccounts();\n  }\n  getOwner() {\n    return this.activitiesservice.getEmailwisePartner();\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      const options = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n      // Assign options to dropdown object\n      this.dropdowns[target] = options;\n      // Set 'Open' as default selected for activityStatus only\n      if (target === 'activityStatus') {\n        const openOption = options.find(opt => opt.label.toLowerCase() === 'open');\n        if (openOption) {\n          const control = this.FollowUpForm?.get('activity_status');\n          if (control) {\n            control.setValue(openOption.value);\n          }\n        }\n      }\n    });\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadExistingContacts() {\n    this.existingcontacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.existingcontactInput$.pipe(distinctUntilChanged(), tap(() => this.existingcontactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.existingcontactLoading = false), catchError(error => {\n        this.existingcontactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  toggleSelection(id) {\n    const control = this.FollowUpForm.get('main_contact_party_id');\n    let currentValue = control?.value || [];\n    if (currentValue.includes(id)) {\n      currentValue = currentValue.filter(v => v !== id);\n    } else {\n      currentValue = [...currentValue, id];\n    }\n    control?.setValue(currentValue);\n  }\n  isSelected(id) {\n    return this.FollowUpForm.get('main_contact_party_id')?.value?.includes(id);\n  }\n  selectExistingContact() {\n    this.addExistingContact(this.FollowUpForm.value);\n    this.existingDialogVisible = false; // Close dialog\n  }\n  addExistingContact(existing) {\n    const contactForm = this.formBuilder.group({\n      bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\n      email_address: [existing?.contactexisting?.email || ''],\n      role_code: 'BUP001',\n      party_id: existing?.contactexisting?.bp_id || ''\n    });\n    const firstGroup = this.involved_parties.at(0);\n    const bpName = firstGroup?.get('bp_full_name')?.value;\n    if (!bpName && this.involved_parties.length === 1) {\n      // Replace the default empty group\n      this.involved_parties.setControl(0, contactForm);\n    } else {\n      // Otherwise, add a new contact\n      this.involved_parties.push(contactForm);\n    }\n    this.existingDialogVisible = false; // Close dialog\n  }\n  deleteContact(index) {\n    if (this.involved_parties.length > 1) {\n      this.involved_parties.removeAt(index);\n    }\n  }\n  createContactFormGroup() {\n    return this.formBuilder.group({\n      bp_full_name: [''],\n      email_address: ['']\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.FollowUpForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.FollowUpForm.value\n      };\n      const data = {\n        document_type: value?.document_type,\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        phone_call_category: value?.phone_call_category,\n        start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n        disposition_code: value?.disposition_code,\n        initiator_code: value?.initiator_code,\n        owner_party_id: _this.owner_id,\n        activity_status: value?.activity_status,\n        note: value?.notes,\n        involved_parties: Array.isArray(value.involved_parties) ? [...value.involved_parties, ...(value?.main_account_party_id ? [{\n          role_code: 'FLCU01',\n          party_id: value.main_account_party_id\n        }] : []), ...(Array.isArray(value.main_contact_party_id) ? value.main_contact_party_id.map(id => ({\n          role_code: 'BUP001',\n          party_id: id\n        })) : []), ...(_this.owner_id ? [{\n          role_code: 'BUP003',\n          party_id: _this.owner_id\n        }] : [])] : [],\n        type_code: '0002',\n        activity_id: _this.activity_id,\n        btd_role_code: '2'\n      };\n      _this.activitiesservice.createFollowup(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.addDialogVisible = false;\n          _this.saving = false;\n          _this.visible = false;\n          _this.FollowUpForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Follow Up Added Successfully!'\n          });\n          _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.FollowUpForm.controls;\n  }\n  get involved_parties() {\n    return this.FollowUpForm.get('involved_parties');\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  hideDialog() {\n    this.onClose.emit();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ActivitiesSalesCallFormComponent_Factory(t) {\n      return new (t || ActivitiesSalesCallFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesSalesCallFormComponent,\n      selectors: [[\"app-activities-sales-call-form\"]],\n      inputs: {\n        visible: \"visible\"\n      },\n      outputs: {\n        onClose: \"onClose\"\n      },\n      decls: 130,\n      vars: 91,\n      consts: [[\"dt\", \"\"], [1, \"followup-popup\", 3, \"onHide\", \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [\"pTemplate\", \"header\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Transaction Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"text-red-500\"], [\"formControlName\", \"document_type\", \"placeholder\", \"Select a Document Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Subject\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Disposition Code\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"disposition_code\", \"placeholder\", \"Select a Disposition Code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"start_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Call Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"end_date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"End Date/Time\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"notes\", \"placeholder\", \"Enter your note here...\", \"styleClass\", \"w-full\", 3, \"ngClass\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"flex\", \"gap-3\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [\"responsiveLayout\", \"scroll\", 1, \"followup-add-table\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"body\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"font-medium\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"], [1, \"border-round-left-lg\", \"text-left\", \"w-5\", \"text-white\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-white\"], [1, \"text-left\", \"w-5\", \"text-white\"], [1, \"text-left\", \"text-white\", \"border-round-right-lg\", 2, \"width\", \"60px\"], [3, \"formGroup\"], [1, \"field\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Enter a Name\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"type\", \"email\", \"formControlName\", \"email_address\", \"placeholder\", \"Enter Email\", \"readonly\", \"\", 1, \"h-3rem\", \"w-full\"], [1, \"pl-5\", \"pt-4\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"class\", \"p-button-rounded p-button-danger\", \"title\", \"Delete\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"title\", \"Delete\", 1, \"p-button-rounded\", \"p-button-danger\", 3, \"click\"]],\n      template: function ActivitiesSalesCallFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p-dialog\", 1);\n          i0.ɵɵlistener(\"onHide\", function ActivitiesSalesCallFormComponent_Template_p_dialog_onHide_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideDialog());\n          });\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(1, ActivitiesSalesCallFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", 2);\n          i0.ɵɵelementStart(2, \"form\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"label\", 6)(6, \"span\", 7);\n          i0.ɵɵtext(7, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \"Transaction Type \");\n          i0.ɵɵelementStart(9, \"span\", 8);\n          i0.ɵɵtext(10, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(11, \"p-dropdown\", 9);\n          i0.ɵɵtemplate(12, ActivitiesSalesCallFormComponent_div_12_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 11)(15, \"span\", 7);\n          i0.ɵɵtext(16, \"subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \"Subject \");\n          i0.ɵɵelementStart(18, \"span\", 8);\n          i0.ɵɵtext(19, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"input\", 12);\n          i0.ɵɵtemplate(21, ActivitiesSalesCallFormComponent_div_21_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 13)(24, \"span\", 7);\n          i0.ɵɵtext(25, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \"Account \");\n          i0.ɵɵelementStart(27, \"span\", 8);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"ng-select\", 14);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵtemplate(31, ActivitiesSalesCallFormComponent_ng_template_31_Template, 3, 2, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ActivitiesSalesCallFormComponent_div_32_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 16)(34, \"label\", 17)(35, \"span\", 7);\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \"Contact \");\n          i0.ɵɵelementStart(38, \"span\", 8);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"ng-select\", 18);\n          i0.ɵɵpipe(41, \"async\");\n          i0.ɵɵtemplate(42, ActivitiesSalesCallFormComponent_ng_template_42_Template, 6, 5, \"ng-template\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, ActivitiesSalesCallFormComponent_div_43_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 16)(45, \"label\", 19)(46, \"span\", 7);\n          i0.ɵɵtext(47, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \"Category \");\n          i0.ɵɵelementStart(49, \"span\", 8);\n          i0.ɵɵtext(50, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(51, \"p-dropdown\", 20);\n          i0.ɵɵtemplate(52, ActivitiesSalesCallFormComponent_div_52_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 16)(54, \"label\", 21)(55, \"span\", 7);\n          i0.ɵɵtext(56, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \"Disposition Code \");\n          i0.ɵɵelementStart(58, \"span\", 8);\n          i0.ɵɵtext(59, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(60, \"p-dropdown\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 16)(62, \"label\", 23)(63, \"span\", 7);\n          i0.ɵɵtext(64, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \"Call Date/Time \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(66, \"p-calendar\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 16)(68, \"label\", 25)(69, \"span\", 7);\n          i0.ɵɵtext(70, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \"End Date/Time \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"p-calendar\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 16)(74, \"label\", 27)(75, \"span\", 7);\n          i0.ɵɵtext(76, \"label\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \"Type \");\n          i0.ɵɵelementStart(78, \"span\", 8);\n          i0.ɵɵtext(79, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(80, \"p-dropdown\", 28);\n          i0.ɵɵtemplate(81, ActivitiesSalesCallFormComponent_div_81_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 16)(83, \"label\", 29)(84, \"span\", 7);\n          i0.ɵɵtext(85, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(86, \"Status \");\n          i0.ɵɵelementStart(87, \"span\", 8);\n          i0.ɵɵtext(88, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(89, \"p-dropdown\", 30);\n          i0.ɵɵtemplate(90, ActivitiesSalesCallFormComponent_div_90_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 31)(92, \"label\", 32)(93, \"span\", 7);\n          i0.ɵɵtext(94, \"notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(95, \"Notes \");\n          i0.ɵɵelementStart(96, \"span\", 8);\n          i0.ɵɵtext(97, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(98, \"p-editor\", 33);\n          i0.ɵɵtemplate(99, ActivitiesSalesCallFormComponent_div_99_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"div\", 34)(101, \"div\", 35)(102, \"h4\", 36);\n          i0.ɵɵtext(103, \"Additional Contacts Persons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 37)(105, \"p-button\", 38);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_p_button_click_105_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showExistingDialog(\"right\"));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(106, \"p-table\", 39, 0);\n          i0.ɵɵtemplate(108, ActivitiesSalesCallFormComponent_ng_template_108_Template, 16, 0, \"ng-template\", 2)(109, ActivitiesSalesCallFormComponent_ng_template_109_Template, 9, 2, \"ng-template\", 40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(110, \"p-dialog\", 41);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_110_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(111, ActivitiesSalesCallFormComponent_ng_template_111_Template, 2, 0, \"ng-template\", 2);\n          i0.ɵɵelementStart(112, \"form\", 3)(113, \"div\", 42)(114, \"label\", 43)(115, \"span\", 44);\n          i0.ɵɵtext(116, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(117, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 45)(119, \"ng-select\", 46);\n          i0.ɵɵpipe(120, \"async\");\n          i0.ɵɵtemplate(121, ActivitiesSalesCallFormComponent_ng_template_121_Template, 5, 4, \"ng-template\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 47)(123, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_123_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.existingDialogVisible = false);\n          });\n          i0.ɵɵtext(124, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_125_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectExistingContact());\n          });\n          i0.ɵɵtext(126, \" Save \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(127, \"div\", 50)(128, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_128_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.visible = false);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function ActivitiesSalesCallFormComponent_Template_button_click_129_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(72, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.FollowUpForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"])(\"ngClass\", i0.ɵɵpureFunction1(73, _c1, ctx.submitted && ctx.f[\"document_type\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"document_type\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c1, ctx.submitted && ctx.f[\"subject\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"subject\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(30, 66, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(77, _c1, ctx.submitted && ctx.f[\"main_account_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_account_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(41, 68, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(79, _c1, ctx.submitted && ctx.f[\"main_contact_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_contact_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityCategory\"])(\"ngClass\", i0.ɵɵpureFunction1(81, _c1, ctx.submitted && ctx.f[\"phone_call_category\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"phone_call_category\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activitydisposition\"]);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(83, _c1, ctx.submitted && ctx.f[\"initiator_code\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"initiator_code\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(85, _c1, ctx.submitted && ctx.f[\"activity_status\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"activity_status\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(87, _c2));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(88, _c1, ctx.submitted && ctx.f[\"notes\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"notes\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", ctx.involved_parties == null ? null : ctx.involved_parties.controls)(\"paginator\", false)(\"rows\", 10);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(90, _c3));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.FollowUpForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(120, 70, ctx.existingcontacts$))(\"hideSelected\", true)(\"loading\", ctx.existingcontactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.existingcontactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.Table, i4.PrimeTemplate, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i5.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subject", "takeUntil", "concat", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ActivitiesSalesCallFormComponent_div_12_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "submitted", "f", "errors", "ActivitiesSalesCallFormComponent_div_21_div_1_Template", "ɵɵtextInterpolate1", "item_r3", "bp_full_name", "ActivitiesSalesCallFormComponent_ng_template_31_span_2_Template", "ɵɵtextInterpolate", "bp_id", "ActivitiesSalesCallFormComponent_div_32_div_1_Template", "item_r5", "email", "mobile", "ɵɵlistener", "ActivitiesSalesCallFormComponent_ng_template_42_Template_input_change_1_listener", "ɵɵrestoreView", "_r4", "item", "ɵɵnextContext", "ɵɵresetView", "toggleSelection", "ActivitiesSalesCallFormComponent_ng_template_42_span_4_Template", "ActivitiesSalesCallFormComponent_ng_template_42_span_5_Template", "isSelected", "ɵɵtextInterpolate2", "ActivitiesSalesCallFormComponent_div_43_div_1_Template", "ActivitiesSalesCallFormComponent_div_52_div_1_Template", "ActivitiesSalesCallFormComponent_div_81_div_1_Template", "ActivitiesSalesCallFormComponent_div_90_div_1_Template", "ActivitiesSalesCallFormComponent_div_99_div_1_Template", "ActivitiesSalesCallFormComponent_ng_template_109_button_8_Template_button_click_0_listener", "_r6", "i_r7", "rowIndex", "deleteContact", "ɵɵelement", "ActivitiesSalesCallFormComponent_ng_template_109_button_8_Template", "contact_r8", "involved_parties", "length", "item_r9", "ActivitiesSalesCallFormComponent_ng_template_121_span_2_Template", "ActivitiesSalesCallFormComponent_ng_template_121_span_3_Template", "ActivitiesSalesCallFormComponent_ng_template_121_span_4_Template", "ActivitiesSalesCallFormComponent", "constructor", "formBuilder", "route", "activitiesservice", "messageservice", "unsubscribe$", "visible", "onClose", "activity_id", "saving", "position", "addDialogVisible", "existingDialogVisible", "defaultOptions", "accountLoading", "accountInput$", "contactLoading", "contactInput$", "existingcontactLoading", "existingcontactInput$", "owner_id", "FollowUpForm", "group", "document_type", "required", "subject", "main_account_party_id", "main_contact_party_id", "phone_call_category", "disposition_code", "start_date", "end_date", "initiator_code", "activity_status", "notes", "contactexisting", "array", "createContactFormGroup", "dropdowns", "activityDocumentType", "activityCategory", "activityStatus", "activitydisposition", "activityInitiatorCode", "ngOnInit", "parent", "snapshot", "paramMap", "get", "setTimeout", "loadActivityDropDown", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "contacts$", "err", "console", "error", "subscribe", "get<PERSON>wner", "next", "response", "loadAccounts", "getEmail<PERSON><PERSON><PERSON><PERSON>", "target", "type", "getActivityDropdownOptions", "res", "options", "data", "attr", "label", "description", "value", "code", "openOption", "find", "opt", "toLowerCase", "control", "setValue", "accounts$", "term", "params", "getPartners", "loadExistingContacts", "existingcontacts$", "bpId", "getPartnersContact", "contacts", "id", "currentValue", "includes", "filter", "v", "selectExistingContact", "addExistingContact", "existing", "contactForm", "email_address", "role_code", "party_id", "firstGroup", "at", "bpName", "setControl", "push", "index", "removeAt", "onSubmit", "_this", "_asyncToGenerator", "invalid", "formatDate", "owner_party_id", "note", "Array", "isArray", "type_code", "btd_role_code", "createFollowup", "reset", "add", "severity", "detail", "getActivityByID", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showExistingDialog", "hideDialog", "emit", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "ActivitiesService", "i4", "MessageService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ActivitiesSalesCallFormComponent_Template", "rf", "ctx", "ActivitiesSalesCallFormComponent_Template_p_dialog_onHide_0_listener", "_r1", "ɵɵtwoWayListener", "ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ActivitiesSalesCallFormComponent_ng_template_1_Template", "ActivitiesSalesCallFormComponent_div_12_Template", "ActivitiesSalesCallFormComponent_div_21_Template", "ActivitiesSalesCallFormComponent_ng_template_31_Template", "ActivitiesSalesCallFormComponent_div_32_Template", "ActivitiesSalesCallFormComponent_ng_template_42_Template", "ActivitiesSalesCallFormComponent_div_43_Template", "ActivitiesSalesCallFormComponent_div_52_Template", "ActivitiesSalesCallFormComponent_div_81_Template", "ActivitiesSalesCallFormComponent_div_90_Template", "ActivitiesSalesCallFormComponent_div_99_Template", "ActivitiesSalesCallFormComponent_Template_p_button_click_105_listener", "ActivitiesSalesCallFormComponent_ng_template_108_Template", "ActivitiesSalesCallFormComponent_ng_template_109_Template", "ActivitiesSalesCallFormComponent_Template_p_dialog_visibleChange_110_listener", "ActivitiesSalesCallFormComponent_ng_template_111_Template", "ActivitiesSalesCallFormComponent_ng_template_121_Template", "ActivitiesSalesCallFormComponent_Template_button_click_123_listener", "ActivitiesSalesCallFormComponent_Template_button_click_125_listener", "ActivitiesSalesCallFormComponent_Template_button_click_128_listener", "ActivitiesSalesCallFormComponent_Template_button_click_129_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c1", "ɵɵpipeBind1", "_c2", "_c3"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\activities-sales-call-form\\activities-sales-call-form.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\activities-sales-call-form\\activities-sales-call-form.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface DropdownOption {\r\n  label: string;\r\n  value: string;\r\n}\r\n@Component({\r\n  selector: 'app-activities-sales-call-form',\r\n  templateUrl: './activities-sales-call-form.component.html',\r\n  styleUrl: './activities-sales-call-form.component.scss',\r\n})\r\nexport class ActivitiesSalesCallFormComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @Input() visible: boolean = false;\r\n  @Output() onClose = new EventEmitter<void>();\r\n  public activity_id: string = '';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public position: string = 'right';\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  private defaultOptions: any = [];\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public existingcontacts$?: Observable<any[]>;\r\n  public existingcontactLoading = false;\r\n  public existingcontactInput$ = new Subject<string>();\r\n  private owner_id: string | null = null;\r\n\r\n  public FollowUpForm: FormGroup = this.formBuilder.group({\r\n    document_type: ['', [Validators.required]],\r\n    subject: ['', [Validators.required]],\r\n    main_account_party_id: ['', [Validators.required]],\r\n    main_contact_party_id: ['', [Validators.required]],\r\n    phone_call_category: ['', [Validators.required]],\r\n    disposition_code: [''],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    initiator_code: ['', [Validators.required]],\r\n    activity_status: ['', [Validators.required]],\r\n    notes: ['', [Validators.required]],\r\n    contactexisting: [''],\r\n    involved_parties: this.formBuilder.array([this.createContactFormGroup()]),\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activitydisposition: [],\r\n    activityInitiatorCode: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.activity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    setTimeout(() => {\r\n      this.loadActivityDropDown(\r\n        'activityDocumentType',\r\n        'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n      );\r\n      this.loadActivityDropDown(\r\n        'activityCategory',\r\n        'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n      );\r\n      this.loadActivityDropDown(\r\n        'activitydisposition',\r\n        'CRM_ACTIVITY_DISPOSITION_CODE'\r\n      );\r\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n      this.loadActivityDropDown(\r\n        'activityInitiatorCode',\r\n        'CRM_ACTIVITY_INITIATOR_CODE'\r\n      );\r\n    }, 50);\r\n    this.FollowUpForm.get('main_account_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.unsubscribe$),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.getOwner().subscribe({\r\n      next: (response: string | null) => {\r\n        this.owner_id = response;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching bp_id:', err);\r\n      },\r\n    });\r\n    this.loadAccounts();\r\n  }\r\n\r\n  private getOwner(): Observable<string | null> {\r\n    return this.activitiesservice.getEmailwisePartner();\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        const options: DropdownOption[] =\r\n          res?.data?.map(\r\n            (attr: { description: string; code: string }): DropdownOption => ({\r\n              label: attr.description,\r\n              value: attr.code,\r\n            })\r\n          ) ?? [];\r\n\r\n        // Assign options to dropdown object\r\n        this.dropdowns[target] = options;\r\n\r\n        // Set 'Open' as default selected for activityStatus only\r\n        if (target === 'activityStatus') {\r\n          const openOption = options.find(\r\n            (opt) => opt.label.toLowerCase() === 'open'\r\n          );\r\n\r\n          if (openOption) {\r\n            const control = this.FollowUpForm?.get('activity_status');\r\n            if (control) {\r\n              control.setValue(openOption.value);\r\n            }\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadExistingContacts() {\r\n    this.existingcontacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.existingcontactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.existingcontactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.existingcontactLoading = false)),\r\n            catchError((error) => {\r\n              this.existingcontactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  toggleSelection(id: string): void {\r\n    const control = this.FollowUpForm.get('main_contact_party_id');\r\n    let currentValue = control?.value || [];\r\n\r\n    if (currentValue.includes(id)) {\r\n      currentValue = currentValue.filter((v: any) => v !== id);\r\n    } else {\r\n      currentValue = [...currentValue, id];\r\n    }\r\n\r\n    control?.setValue(currentValue);\r\n  }\r\n\r\n  isSelected(id: string): boolean {\r\n    return this.FollowUpForm.get('main_contact_party_id')?.value?.includes(id);\r\n  }\r\n\r\n  selectExistingContact() {\r\n    this.addExistingContact(this.FollowUpForm.value);\r\n    this.existingDialogVisible = false; // Close dialog\r\n  }\r\n\r\n  addExistingContact(existing: any) {\r\n    const contactForm = this.formBuilder.group({\r\n      bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\r\n      email_address: [existing?.contactexisting?.email || ''],\r\n      role_code: 'BUP001',\r\n      party_id: existing?.contactexisting?.bp_id || '',\r\n    });\r\n\r\n    const firstGroup = this.involved_parties.at(0) as FormGroup;\r\n    const bpName = firstGroup?.get('bp_full_name')?.value;\r\n\r\n    if (!bpName && this.involved_parties.length === 1) {\r\n      // Replace the default empty group\r\n      this.involved_parties.setControl(0, contactForm);\r\n    } else {\r\n      // Otherwise, add a new contact\r\n      this.involved_parties.push(contactForm);\r\n    }\r\n\r\n    this.existingDialogVisible = false; // Close dialog\r\n  }\r\n\r\n  deleteContact(index: number) {\r\n    if (this.involved_parties.length > 1) {\r\n      this.involved_parties.removeAt(index);\r\n    }\r\n  }\r\n\r\n  createContactFormGroup(): FormGroup {\r\n    return this.formBuilder.group({\r\n      bp_full_name: [''],\r\n      email_address: [''],\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.FollowUpForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.FollowUpForm.value };\r\n\r\n    const data = {\r\n      document_type: value?.document_type,\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      phone_call_category: value?.phone_call_category,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      disposition_code: value?.disposition_code,\r\n      initiator_code: value?.initiator_code,\r\n      owner_party_id: this.owner_id,\r\n      activity_status: value?.activity_status,\r\n      note: value?.notes,\r\n      involved_parties: Array.isArray(value.involved_parties)\r\n        ? [\r\n            ...value.involved_parties,\r\n            ...(value?.main_account_party_id\r\n              ? [{ role_code: 'FLCU01', party_id: value.main_account_party_id }]\r\n              : []),\r\n            ...(Array.isArray(value.main_contact_party_id)\r\n              ? value.main_contact_party_id.map((id: any) => ({\r\n                  role_code: 'BUP001',\r\n                  party_id: id,\r\n                }))\r\n              : []),\r\n            ...(this.owner_id\r\n              ? [{ role_code: 'BUP003', party_id: this.owner_id }]\r\n              : []),\r\n          ]\r\n        : [],\r\n      type_code: '0002',\r\n      activity_id: this.activity_id,\r\n      btd_role_code: '2',\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createFollowup(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.addDialogVisible = false;\r\n          this.saving = false;\r\n          this.visible = false;\r\n          this.FollowUpForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Follow Up Added Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.FollowUpForm.controls;\r\n  }\r\n\r\n  get involved_parties(): any {\r\n    return this.FollowUpForm.get('involved_parties') as FormArray;\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  hideDialog(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-dialog (onHide)=\"hideDialog()\" [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"followup-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Sales Call</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field grid mt-0 text-base\">\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Transaction Type\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">description</span>Transaction Type\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityDocumentType']\" formControlName=\"document_type\"\r\n                    placeholder=\"Select a Document Type\" optionLabel=\"label\" optionValue=\"value\"\r\n                    styleClass=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['document_type'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['document_type'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['document_type'].errors['required']\">\r\n                        Document Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Subject\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">subject</span>Subject\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                    class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['subject'].errors['required']\">\r\n                        Subject is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Account\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">account_circle</span>Account\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_account_party_id\" [typeahead]=\"accountInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors['required']\">\r\n                        Account is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Contact\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">person</span>Contact\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <input type=\"checkbox\" [checked]=\"isSelected(item.bp_id)\"\r\n                                (change)=\"toggleSelection(item.bp_id)\"\r\n                                style=\"width: 20px; height: 20px; margin-right: 6px;\" />\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </div>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors['required']\">\r\n                        Contact is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Category\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">category</span>Category\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"phone_call_category\"\r\n                    placeholder=\"Select a Category\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['phone_call_category'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['phone_call_category'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['phone_call_category'].errors['required']\">\r\n                        Category is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Disposition Code\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">code</span>Disposition Code\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activitydisposition']\" formControlName=\"disposition_code\"\r\n                    placeholder=\"Select a Disposition Code\" optionLabel=\"label\" optionValue=\"value\"\r\n                    styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"start_date\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">schedule</span>Call Date/Time\r\n                </label>\r\n                <p-calendar formControlName=\"start_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"Call Date/Time\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"end_date\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">schedule</span>End Date/Time\r\n                </label>\r\n                <p-calendar formControlName=\"end_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"End Date/Time\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Type\">\r\n                    <span class=\"material-symbols-rounded text-2xl \">label</span>Type\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityInitiatorCode']\" formControlName=\"initiator_code\"\r\n                    placeholder=\"Select a Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['initiator_code'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['initiator_code'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['initiator_code'].errors['required']\">\r\n                        Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Status\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">check_circle</span>Status\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                    placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['activity_status'].errors['required']\">\r\n                        Status is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Notes\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">notes</span>Notes\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-editor formControlName=\"notes\" placeholder=\"Enter your note here...\" [style]=\"{ height: '125px'}\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['notes'].errors }\" styleClass=\"w-full\" />\r\n                <div *ngIf=\"submitted && f['notes'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['notes'].errors &&\r\n                                    f['notes'].errors['required']\r\n                                  \">\r\n                        Notes is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n            <div class=\"flex justify-content-between align-items-center mb-3\">\r\n                <h4 class=\"m-0 flex align-items-center h-3rem\">Additional Contacts Persons</h4>\r\n\r\n                <div class=\"flex gap-3\">\r\n                    <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\"\r\n                        iconPos=\"right\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\"></p-button>\r\n                </div>\r\n            </div>\r\n\r\n            <p-table #dt [value]=\"involved_parties?.controls\" [paginator]=\"false\" [rows]=\"10\" responsiveLayout=\"scroll\"\r\n                class=\"followup-add-table\">\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th class=\"border-round-left-lg text-left w-5 text-white\">\r\n                            <span class=\"flex align-items-center gap-1 font-semibold\">\r\n                                <span class=\"material-symbols-rounded text-2xl text-white\">person</span>\r\n                                Name\r\n                            </span>\r\n                        </th>\r\n\r\n                        <th class=\"text-left w-5 text-white\">\r\n                            <span class=\"flex align-items-center gap-1  font-semibold\">\r\n                                <span class=\"material-symbols-rounded text-2xl text-white\">mail</span>\r\n                                Email Address\r\n                            </span>\r\n                        </th>\r\n                        <th class=\"text-left text-white border-round-right-lg\" style=\"width: 60px;\">\r\n                            <span class=\"flex align-items-center gap-1 font-semibold\">\r\n                                <span class=\"material-symbols-rounded text-2xl text-white\">delete</span>\r\n                                Action\r\n                            </span>\r\n                        </th>\r\n                    </tr>\r\n                </ng-template>\r\n\r\n                <ng-template pTemplate=\"body\" let-contact let-i=\"rowIndex\">\r\n                    <tr [formGroup]=\"contact\">\r\n                        <td>\r\n                            <div class=\"field\">\r\n                                <input pInputText type=\"text\" class=\"h-3rem w-full\" formControlName=\"bp_full_name\"\r\n                                    placeholder=\"Enter a Name\" readonly />\r\n                            </div>\r\n                        </td>\r\n                        <td>\r\n                            <div class=\"field\">\r\n                                <input pInputText type=\"email\" class=\"h-3rem w-full\" formControlName=\"email_address\"\r\n                                    placeholder=\"Enter Email\" readonly />\r\n                            </div>\r\n                        </td>\r\n                        <td class=\"pl-5 pt-4\">\r\n                            <button pButton pRipple type=\"button\" icon=\"pi pi-trash\"\r\n                                class=\"p-button-rounded p-button-danger\" (click)=\"deleteContact(i)\" title=\"Delete\"\r\n                                *ngIf=\"involved_parties.length > 1\"></button>\r\n                        </td>\r\n                    </tr>\r\n                </ng-template>\r\n\r\n            </p-table>\r\n        </div>\r\n        <p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n            [draggable]=\"false\" class=\"prospect-popup\">\r\n            <ng-template pTemplate=\"header\">\r\n                <h4>Contact Information</h4>\r\n            </ng-template>\r\n\r\n            <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n                <div class=\"field flex align-items-center text-base\">\r\n                    <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                        <span class=\"material-symbols-rounded\">person</span>Contacts\r\n                    </label>\r\n                    <div class=\"form-input flex-1 relative\">\r\n                        <ng-select pInputText [items]=\"existingcontacts$ | async\" bindLabel=\"bp_full_name\"\r\n                            [hideSelected]=\"true\" [loading]=\"existingcontactLoading\" [minTermLength]=\"0\"\r\n                            formControlName=\"contactexisting\" [typeahead]=\"existingcontactInput$\"\r\n                            [maxSelectedItems]=\"10\" appendTo=\"body\">\r\n                            <ng-template ng-option-tmp let-item=\"item\">\r\n                                <span>{{ item.bp_id }}</span>\r\n                                <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </ng-template>\r\n                        </ng-select>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-end gap-2 mt-3\">\r\n                    <button pButton type=\"button\"\r\n                        class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center font-medium w-9rem h-3rem\"\r\n                        (click)=\"existingDialogVisible = false\">\r\n                        Cancel\r\n                    </button>\r\n                    <button pButton type=\"button\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                        (click)=\"selectExistingContact()\">\r\n                        Save\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </p-dialog>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAGtE,SAAiCC,UAAU,QAAmB,gBAAgB;AAC9E,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICVfC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAePH,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,mCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAI,UAAA,IAAAC,sDAAA,kBAAgE;IAGpEL,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,kBAAAC,MAAA,aAAwD;;;;;IAa9DX,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAI,UAAA,IAAAQ,sDAAA,kBAA0D;IAG9DZ,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,YAAAC,MAAA,aAAkD;;;;;IAgBpDX,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAa,kBAAA,QAAAC,OAAA,CAAAC,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAI,UAAA,IAAAY,+DAAA,mBAAgC;;;;IAD1BhB,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAiB,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACflB,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAO,OAAA,CAAAC,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAe,sDAAA,kBAAwE;IAG5EnB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAqB9DX,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAE,MAAA,KAAmB;;;;;;IAL9CtB,EADJ,CAAAC,cAAA,cAA2C,gBAGqB;IADxDD,EAAA,CAAAuB,UAAA,oBAAAC,iFAAA;MAAA,MAAAJ,OAAA,GAAApB,EAAA,CAAAyB,aAAA,CAAAC,GAAA,EAAAC,IAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAUrB,MAAA,CAAAsB,eAAA,CAAAV,OAAA,CAAAF,KAAA,CAA2B;IAAA,EAAC;IAD1ClB,EAAA,CAAAG,YAAA,EAE4D;IAC5DH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAI,UAAA,IAAA2B,+DAAA,mBAAyB,IAAAC,+DAAA,mBACC;IAC9BhC,EAAA,CAAAG,YAAA,EAAM;;;;;IANqBH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAO,UAAA,YAAAC,MAAA,CAAAyB,UAAA,CAAAb,OAAA,CAAAF,KAAA,EAAkC;IAGnDlB,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAkC,kBAAA,KAAAd,OAAA,CAAAF,KAAA,QAAAE,OAAA,CAAAL,YAAA,KAAyC;IACxCf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAC,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhCtB,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAA+B,sDAAA,kBAAwE;IAG5EnC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAetEX,EAAA,CAAAC,cAAA,UAAsE;IAClED,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAI,UAAA,IAAAgC,sDAAA,kBAAsE;IAG1EpC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,wBAAAC,MAAA,aAA8D;;;;;IAuCpEX,EAAA,CAAAC,cAAA,UAAiE;IAC7DD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAI,UAAA,IAAAiC,sDAAA,kBAAiE;IAGrErC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAyD;IAAzDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,mBAAAC,MAAA,aAAyD;;;;;IAe/DX,EAAA,CAAAC,cAAA,UAAkE;IAC9DD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAI,UAAA,IAAAkC,sDAAA,kBAAkE;IAGtEtC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,oBAAAC,MAAA,aAA0D;;;;;IAahEX,EAAA,CAAAC,cAAA,UAIgB;IACZD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAI,UAAA,IAAAmC,sDAAA,kBAIgB;IAGpBvC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIO;IAJPN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,UAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,UAAAC,MAAA,aAIO;;;;;IAuBDX,EAHZ,CAAAC,cAAA,SAAI,aAC0D,eACI,eACK;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAE,MAAA,aACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACN;IAIGH,EAFR,CAAAC,cAAA,aAAqC,eAC0B,eACI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAE,MAAA,uBACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACN;IAGGH,EAFR,CAAAC,cAAA,cAA4E,gBACd,gBACK;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAE,MAAA,gBACJ;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACN,EACJ;;;;;;IAkBGH,EAAA,CAAAC,cAAA,iBAEwC;IADKD,EAAA,CAAAuB,UAAA,mBAAAiB,2FAAA;MAAAxC,EAAA,CAAAyB,aAAA,CAAAgB,GAAA;MAAA,MAAAC,IAAA,GAAA1C,EAAA,CAAA4B,aAAA,GAAAe,QAAA;MAAA,MAAAnC,MAAA,GAAAR,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASrB,MAAA,CAAAoC,aAAA,CAAAF,IAAA,CAAgB;IAAA,EAAC;IAC/B1C,EAAA,CAAAG,YAAA,EAAS;;;;;IAdjDH,EAFR,CAAAC,cAAA,aAA0B,SAClB,cACmB;IACfD,EAAA,CAAA6C,SAAA,gBAC0C;IAElD7C,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,SAAI,cACmB;IACfD,EAAA,CAAA6C,SAAA,gBACyC;IAEjD7C,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,aAAsB;IAClBD,EAAA,CAAAI,UAAA,IAAA0C,kEAAA,qBAEwC;IAEhD9C,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;IAlBDH,EAAA,CAAAO,UAAA,cAAAwC,UAAA,CAAqB;IAgBZ/C,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAwC,gBAAA,CAAAC,MAAA,KAAiC;;;;;IAUlDjD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAeZH,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAa,kBAAA,QAAAqC,OAAA,CAAAnC,YAAA,KAAyB;;;;;IAC1Df,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAqC,OAAA,CAAA7B,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAqC,OAAA,CAAA5B,MAAA,KAAmB;;;;;IAH9CtB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAFA,CAAAI,UAAA,IAAA+C,gEAAA,mBAAgC,IAAAC,gEAAA,mBACP,IAAAC,gEAAA,mBACC;;;;IAHpBrD,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAiB,iBAAA,CAAAiC,OAAA,CAAAhC,KAAA,CAAgB;IACflB,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAA2C,OAAA,CAAAnC,YAAA,CAAuB;IACvBf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAA2C,OAAA,CAAA7B,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAA2C,OAAA,CAAA5B,MAAA,CAAiB;;;ADlOxD,OAAM,MAAOgC,gCAAgC;EA8C3CC,YACUC,WAAwB,EACxBC,KAAqB,EACrBC,iBAAoC,EACpCC,cAA8B;IAH9B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IAjDhB,KAAAC,YAAY,GAAG,IAAIzE,OAAO,EAAQ;IACjC,KAAA0E,OAAO,GAAY,KAAK;IACvB,KAAAC,OAAO,GAAG,IAAI5E,YAAY,EAAQ;IACrC,KAAA6E,WAAW,GAAW,EAAE;IACxB,KAAAtD,SAAS,GAAG,KAAK;IACjB,KAAAuD,MAAM,GAAG,KAAK;IACd,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACrC,KAAAC,cAAc,GAAQ,EAAE;IAEzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAInF,OAAO,EAAU;IAErC,KAAAoF,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIrF,OAAO,EAAU;IAErC,KAAAsF,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,qBAAqB,GAAG,IAAIvF,OAAO,EAAU;IAC5C,KAAAwF,QAAQ,GAAkB,IAAI;IAE/B,KAAAC,YAAY,GAAc,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACtDC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACtF,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAC1CC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACxF,UAAU,CAACuF,QAAQ,CAAC,CAAC;MACpCE,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACzF,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAClDG,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAC1F,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAClDI,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAChDK,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC/F,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAC3CS,eAAe,EAAE,CAAC,EAAE,EAAE,CAAChG,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAC5CU,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjG,UAAU,CAACuF,QAAQ,CAAC,CAAC;MAClCW,eAAe,EAAE,CAAC,EAAE,CAAC;MACrB1C,gBAAgB,EAAE,IAAI,CAACQ,WAAW,CAACmC,KAAK,CAAC,CAAC,IAAI,CAACC,sBAAsB,EAAE,CAAC;KACzE,CAAC;IAEK,KAAAC,SAAS,GAA0B;MACxCC,oBAAoB,EAAE,EAAE;MACxBC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE;KACxB;EAOE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACpC,WAAW,GAAG,IAAI,CAACN,KAAK,CAAC2C,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACvEC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;MACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;MACD,IAAI,CAACA,oBAAoB,CACvB,qBAAqB,EACrB,+BAA+B,CAChC;MACD,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;MAClE,IAAI,CAACA,oBAAoB,CACvB,uBAAuB,EACvB,6BAA6B,CAC9B;IACH,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAAC7B,YAAY,CAAC2B,GAAG,CAAC,uBAAuB,CAAC,EAC1CG,YAAY,CAACC,IAAI,CACjBvH,SAAS,CAAC,IAAI,CAACwE,YAAY,CAAC,EAC5BjE,GAAG,CAAEiH,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACE,SAAS,GAAGvH,EAAE,CAAC,IAAI,CAAC6E,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACFvE,UAAU,CAAEkH,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACD,SAAS,GAAGvH,EAAE,CAAC,IAAI,CAAC6E,cAAc,CAAC;MACxC,OAAO7E,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACA2H,SAAS,EAAE;IACd,IAAI,CAACC,QAAQ,EAAE,CAACD,SAAS,CAAC;MACxBE,IAAI,EAAGC,QAAuB,IAAI;QAChC,IAAI,CAAC1C,QAAQ,GAAG0C,QAAQ;MAC1B,CAAC;MACDJ,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C;KACD,CAAC;IACF,IAAI,CAACO,YAAY,EAAE;EACrB;EAEQH,QAAQA,CAAA;IACd,OAAO,IAAI,CAACzD,iBAAiB,CAAC6D,mBAAmB,EAAE;EACrD;EAEAd,oBAAoBA,CAACe,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAC/D,iBAAiB,CACnBgE,0BAA0B,CAACD,IAAI,CAAC,CAChCP,SAAS,CAAES,GAAQ,IAAI;MACtB,MAAMC,OAAO,GACXD,GAAG,EAAEE,IAAI,EAAEvI,GAAG,CACXwI,IAA2C,KAAsB;QAChEC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CACH,IAAI,EAAE;MAET;MACA,IAAI,CAACrC,SAAS,CAAC2B,MAAM,CAAC,GAAGI,OAAO;MAEhC;MACA,IAAIJ,MAAM,KAAK,gBAAgB,EAAE;QAC/B,MAAMW,UAAU,GAAGP,OAAO,CAACQ,IAAI,CAC5BC,GAAG,IAAKA,GAAG,CAACN,KAAK,CAACO,WAAW,EAAE,KAAK,MAAM,CAC5C;QAED,IAAIH,UAAU,EAAE;UACd,MAAMI,OAAO,GAAG,IAAI,CAAC3D,YAAY,EAAE2B,GAAG,CAAC,iBAAiB,CAAC;UACzD,IAAIgC,OAAO,EAAE;YACXA,OAAO,CAACC,QAAQ,CAACL,UAAU,CAACF,KAAK,CAAC;UACpC;QACF;MACF;IACF,CAAC,CAAC;EACN;EAEQX,YAAYA,CAAA;IAClB,IAAI,CAACmB,SAAS,GAAGpJ,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC6E,cAAc,CAAC;IAAE;IACzB,IAAI,CAACE,aAAa,CAACqC,IAAI,CACrB7G,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC0E,cAAc,GAAG,IAAK,CAAC,EACvC3E,SAAS,CAAEgJ,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAChF,iBAAiB,CAACkF,WAAW,CAACD,MAAM,CAAC,CAAChC,IAAI,CACpDrH,GAAG,CAAE+H,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxCxH,UAAU,CAAEoH,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAO1H,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFQ,QAAQ,CAAC,MAAO,IAAI,CAACsE,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQwE,oBAAoBA,CAAA;IAC1B,IAAI,CAACC,iBAAiB,GAAGzJ,MAAM,CAC7BE,EAAE,CAAC,IAAI,CAAC6E,cAAc,CAAC;IAAE;IACzB,IAAI,CAACM,qBAAqB,CAACiC,IAAI,CAC7BlH,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC8E,sBAAsB,GAAG,IAAK,CAAC,EAC/C/E,SAAS,CAAEgJ,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAChF,iBAAiB,CAACkF,WAAW,CAACD,MAAM,CAAC,CAAChC,IAAI,CACpDrH,GAAG,CAAEuI,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFlI,GAAG,CAAC,MAAO,IAAI,CAAC8E,sBAAsB,GAAG,KAAM,CAAC,EAChD5E,UAAU,CAAEoH,KAAK,IAAI;QACnB,IAAI,CAACxC,sBAAsB,GAAG,KAAK;QACnC,OAAOlF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQsH,qBAAqBA,CAACkC,IAAY;IACxC,IAAI,CAACjC,SAAS,GAAG,IAAI,CAACtC,aAAa,CAACmC,IAAI,CACtC/G,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC4E,cAAc,GAAG,IAAK,CAAC,EACvC7E,SAAS,CAAEgJ,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEI,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIL,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAChF,iBAAiB,CAACsF,kBAAkB,CAACL,MAAM,CAAC,CAAChC,IAAI,CAC3DrH,GAAG,CAAE+H,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxC1H,GAAG,CAAEsJ,QAAe,IAAI;QACtB,IAAI,CAAC1E,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACF1E,UAAU,CAAEoH,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC1C,cAAc,GAAG,KAAK;QAC3B,OAAOhF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEAuC,eAAeA,CAACoH,EAAU;IACxB,MAAMX,OAAO,GAAG,IAAI,CAAC3D,YAAY,CAAC2B,GAAG,CAAC,uBAAuB,CAAC;IAC9D,IAAI4C,YAAY,GAAGZ,OAAO,EAAEN,KAAK,IAAI,EAAE;IAEvC,IAAIkB,YAAY,CAACC,QAAQ,CAACF,EAAE,CAAC,EAAE;MAC7BC,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAEC,CAAM,IAAKA,CAAC,KAAKJ,EAAE,CAAC;IAC1D,CAAC,MAAM;MACLC,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAED,EAAE,CAAC;IACtC;IAEAX,OAAO,EAAEC,QAAQ,CAACW,YAAY,CAAC;EACjC;EAEAlH,UAAUA,CAACiH,EAAU;IACnB,OAAO,IAAI,CAACtE,YAAY,CAAC2B,GAAG,CAAC,uBAAuB,CAAC,EAAE0B,KAAK,EAAEmB,QAAQ,CAACF,EAAE,CAAC;EAC5E;EAEAK,qBAAqBA,CAAA;IACnB,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC5E,YAAY,CAACqD,KAAK,CAAC;IAChD,IAAI,CAAC9D,qBAAqB,GAAG,KAAK,CAAC,CAAC;EACtC;EAEAqF,kBAAkBA,CAACC,QAAa;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAAClG,WAAW,CAACqB,KAAK,CAAC;MACzC9D,YAAY,EAAE,CAAC0I,QAAQ,EAAE/D,eAAe,EAAE3E,YAAY,IAAI,EAAE,CAAC;MAC7D4I,aAAa,EAAE,CAACF,QAAQ,EAAE/D,eAAe,EAAErE,KAAK,IAAI,EAAE,CAAC;MACvDuI,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAEJ,QAAQ,EAAE/D,eAAe,EAAExE,KAAK,IAAI;KAC/C,CAAC;IAEF,MAAM4I,UAAU,GAAG,IAAI,CAAC9G,gBAAgB,CAAC+G,EAAE,CAAC,CAAC,CAAc;IAC3D,MAAMC,MAAM,GAAGF,UAAU,EAAEvD,GAAG,CAAC,cAAc,CAAC,EAAE0B,KAAK;IAErD,IAAI,CAAC+B,MAAM,IAAI,IAAI,CAAChH,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE;MACjD;MACA,IAAI,CAACD,gBAAgB,CAACiH,UAAU,CAAC,CAAC,EAAEP,WAAW,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAAC1G,gBAAgB,CAACkH,IAAI,CAACR,WAAW,CAAC;IACzC;IAEA,IAAI,CAACvF,qBAAqB,GAAG,KAAK,CAAC,CAAC;EACtC;EAEAvB,aAAaA,CAACuH,KAAa;IACzB,IAAI,IAAI,CAACnH,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACpC,IAAI,CAACD,gBAAgB,CAACoH,QAAQ,CAACD,KAAK,CAAC;IACvC;EACF;EAEAvE,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACpC,WAAW,CAACqB,KAAK,CAAC;MAC5B9D,YAAY,EAAE,CAAC,EAAE,CAAC;MAClB4I,aAAa,EAAE,CAAC,EAAE;KACnB,CAAC;EACJ;EAEMU,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC7J,SAAS,GAAG,IAAI;MAErB,IAAI6J,KAAI,CAAC1F,YAAY,CAAC4F,OAAO,EAAE;QAC7B;MACF;MAEAF,KAAI,CAACtG,MAAM,GAAG,IAAI;MAClB,MAAMiE,KAAK,GAAG;QAAE,GAAGqC,KAAI,CAAC1F,YAAY,CAACqD;MAAK,CAAE;MAE5C,MAAMJ,IAAI,GAAG;QACX/C,aAAa,EAAEmD,KAAK,EAAEnD,aAAa;QACnCE,OAAO,EAAEiD,KAAK,EAAEjD,OAAO;QACvBC,qBAAqB,EAAEgD,KAAK,EAAEhD,qBAAqB;QACnDC,qBAAqB,EAAE+C,KAAK,EAAE/C,qBAAqB;QACnDC,mBAAmB,EAAE8C,KAAK,EAAE9C,mBAAmB;QAC/CE,UAAU,EAAE4C,KAAK,EAAE5C,UAAU,GAAGiF,KAAI,CAACG,UAAU,CAACxC,KAAK,CAAC5C,UAAU,CAAC,GAAG,IAAI;QACxEC,QAAQ,EAAE2C,KAAK,EAAE3C,QAAQ,GAAGgF,KAAI,CAACG,UAAU,CAACxC,KAAK,CAAC3C,QAAQ,CAAC,GAAG,IAAI;QAClEF,gBAAgB,EAAE6C,KAAK,EAAE7C,gBAAgB;QACzCG,cAAc,EAAE0C,KAAK,EAAE1C,cAAc;QACrCmF,cAAc,EAAEJ,KAAI,CAAC3F,QAAQ;QAC7Ba,eAAe,EAAEyC,KAAK,EAAEzC,eAAe;QACvCmF,IAAI,EAAE1C,KAAK,EAAExC,KAAK;QAClBzC,gBAAgB,EAAE4H,KAAK,CAACC,OAAO,CAAC5C,KAAK,CAACjF,gBAAgB,CAAC,GACnD,CACE,GAAGiF,KAAK,CAACjF,gBAAgB,EACzB,IAAIiF,KAAK,EAAEhD,qBAAqB,GAC5B,CAAC;UAAE2E,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAE5B,KAAK,CAAChD;QAAqB,CAAE,CAAC,GAChE,EAAE,CAAC,EACP,IAAI2F,KAAK,CAACC,OAAO,CAAC5C,KAAK,CAAC/C,qBAAqB,CAAC,GAC1C+C,KAAK,CAAC/C,qBAAqB,CAAC5F,GAAG,CAAE4J,EAAO,KAAM;UAC5CU,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAEX;SACX,CAAC,CAAC,GACH,EAAE,CAAC,EACP,IAAIoB,KAAI,CAAC3F,QAAQ,GACb,CAAC;UAAEiF,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAES,KAAI,CAAC3F;QAAQ,CAAE,CAAC,GAClD,EAAE,CAAC,CACR,GACD,EAAE;QACNmG,SAAS,EAAE,MAAM;QACjB/G,WAAW,EAAEuG,KAAI,CAACvG,WAAW;QAC7BgH,aAAa,EAAE;OAChB;MAEDT,KAAI,CAAC5G,iBAAiB,CACnBsH,cAAc,CAACnD,IAAI,CAAC,CACpBlB,IAAI,CAACvH,SAAS,CAACkL,KAAI,CAAC1G,YAAY,CAAC,CAAC,CAClCsD,SAAS,CAAC;QACTE,IAAI,EAAEA,CAAA,KAAK;UACTkD,KAAI,CAACpG,gBAAgB,GAAG,KAAK;UAC7BoG,KAAI,CAACtG,MAAM,GAAG,KAAK;UACnBsG,KAAI,CAACzG,OAAO,GAAG,KAAK;UACpByG,KAAI,CAAC1F,YAAY,CAACqG,KAAK,EAAE;UACzBX,KAAI,CAAC3G,cAAc,CAACuH,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFd,KAAI,CAAC5G,iBAAiB,CACnB2H,eAAe,CAACf,KAAI,CAACvG,WAAW,CAAC,CACjC4C,IAAI,CAACvH,SAAS,CAACkL,KAAI,CAAC1G,YAAY,CAAC,CAAC,CAClCsD,SAAS,EAAE;QAChB,CAAC;QACDD,KAAK,EAAEA,CAAA,KAAK;UACVqD,KAAI,CAACtG,MAAM,GAAG,KAAK;UACnBsG,KAAI,CAAC3G,cAAc,CAACuH,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAX,UAAUA,CAACa,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAInL,CAACA,CAAA;IACH,OAAO,IAAI,CAACkE,YAAY,CAACmH,QAAQ;EACnC;EAEA,IAAI/I,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC4B,YAAY,CAAC2B,GAAG,CAAC,kBAAkB,CAAc;EAC/D;EAEAyF,kBAAkBA,CAAC/H,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,qBAAqB,GAAG,IAAI;EACnC;EAEA8H,UAAUA,CAAA;IACR,IAAI,CAACnI,OAAO,CAACoI,IAAI,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvI,YAAY,CAACwD,IAAI,EAAE;IACxB,IAAI,CAACxD,YAAY,CAACwI,QAAQ,EAAE;EAC9B;;;uBAlZW9I,gCAAgC,EAAAtD,EAAA,CAAAqM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvM,EAAA,CAAAqM,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzM,EAAA,CAAAqM,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA3M,EAAA,CAAAqM,iBAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhCvJ,gCAAgC;MAAAwJ,SAAA;MAAAC,MAAA;QAAAlJ,OAAA;MAAA;MAAAmJ,OAAA;QAAAlJ,OAAA;MAAA;MAAAmJ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCzB7CtN,EAAA,CAAAC,cAAA,kBAC+C;UADrCD,EAAA,CAAAuB,UAAA,oBAAAiM,qEAAA;YAAAxN,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAU0L,GAAA,CAAAtB,UAAA,EAAY;UAAA,EAAC;UAAgBjM,EAAA,CAAA0N,gBAAA,2BAAAC,4EAAAC,MAAA;YAAA5N,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAAzN,EAAA,CAAA6N,kBAAA,CAAAN,GAAA,CAAA1J,OAAA,EAAA+J,MAAA,MAAAL,GAAA,CAAA1J,OAAA,GAAA+J,MAAA;YAAA,OAAA5N,EAAA,CAAA6B,WAAA,CAAA+L,MAAA;UAAA,EAAqB;UAElE5N,EAAA,CAAAI,UAAA,IAAA0N,uDAAA,yBAAgC;UAQhB9N,EAJhB,CAAAC,cAAA,cAAyE,aAC9B,aACa,eAC2C,cACnC;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,wBAClE;UAAAF,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,qBAGa;UACb7C,EAAA,CAAAI,UAAA,KAAA2N,gDAAA,kBAAoE;UAKxE/N,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAgD,iBACkC,eAC1B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBAC9D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,iBAC2F;UAC3F7C,EAAA,CAAAI,UAAA,KAAA4N,gDAAA,kBAA8D;UAKlEhO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAgD,iBACkC,eAC1B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBACrE;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAGiG;;UAC7FD,EAAA,CAAAI,UAAA,KAAA6N,wDAAA,0BAA2C;UAI/CjO,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAA8N,gDAAA,kBAA4E;UAKhFlO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC6B,eAC1B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBAC7D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAIiF;;UAC7ED,EAAA,CAAAI,UAAA,KAAA+N,wDAAA,0BAA2C;UAU/CnO,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAAgO,gDAAA,kBAA4E;UAKhFpO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eAC3B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,iBAC/D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,sBAGa;UACb7C,EAAA,CAAAI,UAAA,KAAAiO,gDAAA,kBAA0E;UAK9ErO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACsC,eACnC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,yBAC3D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,sBAGa;UACjB7C,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgC,eAC7B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,uBACnE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA6C,SAAA,sBACgF;UACpF7C,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eAC3B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,sBACnE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAA6C,SAAA,sBAC+E;UACnF7C,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC0B,eACtB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,aAC7D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,sBAGa;UACb7C,EAAA,CAAAI,UAAA,KAAAkO,gDAAA,kBAAqE;UAKzEtO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4B,eACzB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,eACnE;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,sBAGa;UACb7C,EAAA,CAAAI,UAAA,KAAAmO,gDAAA,kBAAsE;UAK1EvO,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAyB,iBACuD,eACxB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,cAC5D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAA6C,SAAA,oBACuF;UACvF7C,EAAA,CAAAI,UAAA,KAAAoO,gDAAA,kBAA4D;UAUpExO,EADI,CAAAG,YAAA,EAAM,EACJ;UAIEH,EAFR,CAAAC,cAAA,gBAAoF,gBACd,eACf;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3EH,EADJ,CAAAC,cAAA,gBAAwB,qBAEqD;UADtCD,EAAA,CAAAuB,UAAA,mBAAAkN,sEAAA;YAAAzO,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAS0L,GAAA,CAAAvB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAGhFhM,EAFiF,CAAAG,YAAA,EAAW,EAClF,EACJ;UAENH,EAAA,CAAAC,cAAA,uBAC+B;UAyB3BD,EAxBA,CAAAI,UAAA,MAAAsO,yDAAA,0BAAgC,MAAAC,yDAAA,0BAwB2B;UAuBnE3O,EADI,CAAAG,YAAA,EAAU,EACR;UACNH,EAAA,CAAAC,cAAA,qBAC+C;UADtBD,EAAA,CAAA0N,gBAAA,2BAAAkB,8EAAAhB,MAAA;YAAA5N,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAAzN,EAAA,CAAA6N,kBAAA,CAAAN,GAAA,CAAApJ,qBAAA,EAAAyJ,MAAA,MAAAL,GAAA,CAAApJ,qBAAA,GAAAyJ,MAAA;YAAA,OAAA5N,EAAA,CAAA6B,WAAA,CAAA+L,MAAA;UAAA,EAAmC;UAExD5N,EAAA,CAAAI,UAAA,MAAAyO,yDAAA,yBAAgC;UAOpB7O,EAHZ,CAAAC,cAAA,gBAAyE,gBAChB,kBAC+C,iBACrD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,kBACxD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEJH,EADJ,CAAAC,cAAA,gBAAwC,sBAIQ;;UACxCD,EAAA,CAAAI,UAAA,MAAA0O,yDAAA,0BAA2C;UAQvD9O,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;UAEFH,EADJ,CAAAC,cAAA,gBAAiD,mBAGD;UAAxCD,EAAA,CAAAuB,UAAA,mBAAAwN,oEAAA;YAAA/O,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAA0L,GAAA,CAAApJ,qBAAA,GAAiC,KAAK;UAAA,EAAC;UACvCnE,EAAA,CAAAE,MAAA,iBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBACsC;UAAlCD,EAAA,CAAAuB,UAAA,mBAAAyN,oEAAA;YAAAhP,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAS0L,GAAA,CAAAhE,qBAAA,EAAuB;UAAA,EAAC;UACjCvJ,EAAA,CAAAE,MAAA,eACJ;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACH,EACA;UAEPH,EADJ,CAAAC,cAAA,gBAAgD,mBAGd;UAA1BD,EAAA,CAAAuB,UAAA,mBAAA0N,oEAAA;YAAAjP,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAA0L,GAAA,CAAA1J,OAAA,GAAmB,KAAK;UAAA,EAAC;UAAC7D,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAuB,UAAA,mBAAA2N,oEAAA;YAAAlP,EAAA,CAAAyB,aAAA,CAAAgM,GAAA;YAAA,OAAAzN,EAAA,CAAA6B,WAAA,CAAS0L,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAIpCrK,EAJqC,CAAAG,YAAA,EAAS,EAChC,EACH,EAEA;;;UAtR4DH,EAAA,CAAAmP,UAAA,CAAAnP,EAAA,CAAAoP,eAAA,KAAAC,GAAA,EAA4B;UAAjErP,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAAsP,gBAAA,YAAA/B,GAAA,CAAA1J,OAAA,CAAqB;UAClE7D,EADgG,CAAAO,UAAA,qBAAoB,oBACjG;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAgN,GAAA,CAAA3I,YAAA,CAA0B;UAOR5E,EAAA,CAAAM,SAAA,GAA6C;UAE1BN,EAFnB,CAAAO,UAAA,YAAAgN,GAAA,CAAA1H,SAAA,yBAA6C,YAAA7F,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,kBAAAC,MAAA,EAE0C;UAE7FX,EAAA,CAAAM,SAAA,EAA4C;UAA5CN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,kBAAAC,MAAA,CAA4C;UAYxBX,EAAA,CAAAM,SAAA,GAA8D;UAA9DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,YAAAC,MAAA,EAA8D;UAClFX,EAAA,CAAAM,SAAA,EAAsC;UAAtCN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,YAAAC,MAAA,CAAsC;UAWtBX,EAAA,CAAAM,SAAA,GAA2B;UAG7BN,EAHE,CAAAO,UAAA,UAAAP,EAAA,CAAAyP,WAAA,SAAAlC,GAAA,CAAA9E,SAAA,EAA2B,sBACxB,YAAA8E,GAAA,CAAAlJ,cAAA,CAA2B,oBAAoB,cAAAkJ,GAAA,CAAAjJ,aAAA,CACD,wBAAwB,YAAAtE,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,0BAAAC,MAAA,EACC;UAM1FX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,0BAAAC,MAAA,CAAoD;UAWpCX,EAAA,CAAAM,SAAA,GAA2B;UAI7CN,EAJkB,CAAAO,UAAA,UAAAP,EAAA,CAAAyP,WAAA,SAAAlC,GAAA,CAAAzG,SAAA,EAA2B,sBACxB,YAAAyG,GAAA,CAAAhJ,cAAA,CAA2B,oBAAoB,cAAAgJ,GAAA,CAAA/I,aAAA,CACD,wBAAwB,kBAC1D,wBAAwB,YAAAxE,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,0BAAAC,MAAA,EACmB;UAY1EX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,0BAAAC,MAAA,CAAoD;UAW9CX,EAAA,CAAAM,SAAA,GAAyC;UAEjDN,EAFQ,CAAAO,UAAA,YAAAgN,GAAA,CAAA1H,SAAA,qBAAyC,YAAA7F,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,wBAAAC,MAAA,EAEyB;UAExEX,EAAA,CAAAM,SAAA,EAAkD;UAAlDN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,wBAAAC,MAAA,CAAkD;UAW5CX,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAO,UAAA,YAAAgN,GAAA,CAAA1H,SAAA,wBAA4C;UASQ7F,EAAA,CAAAM,SAAA,GAAiB;UAC7EN,EAD4D,CAAAO,UAAA,kBAAiB,kBAC5D;UAMyCP,EAAA,CAAAM,SAAA,GAAiB;UAC3EN,EAD0D,CAAAO,UAAA,kBAAiB,kBAC1D;UAOTP,EAAA,CAAAM,SAAA,GAA8C;UAEtDN,EAFQ,CAAAO,UAAA,YAAAgN,GAAA,CAAA1H,SAAA,0BAA8C,YAAA7F,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,mBAAAC,MAAA,EAEe;UAEnEX,EAAA,CAAAM,SAAA,EAA6C;UAA7CN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,mBAAAC,MAAA,CAA6C;UAWvCX,EAAA,CAAAM,SAAA,GAAuC;UAE/CN,EAFQ,CAAAO,UAAA,YAAAgN,GAAA,CAAA1H,SAAA,mBAAuC,YAAA7F,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,oBAAAC,MAAA,EAEuB;UAEpEX,EAAA,CAAAM,SAAA,EAA8C;UAA9CN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,oBAAAC,MAAA,CAA8C;UAWoBX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAmP,UAAA,CAAAnP,EAAA,CAAAoP,eAAA,KAAAM,GAAA,EAA4B;UAChG1P,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAuP,eAAA,KAAAC,GAAA,EAAAjC,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,UAAAC,MAAA,EAA4D;UAC1DX,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,SAAAgN,GAAA,CAAA9M,SAAA,IAAA8M,GAAA,CAAA7M,CAAA,UAAAC,MAAA,CAAoC;UAkBlBX,EAAA,CAAAM,SAAA,GAAmC;UAACN,EAApC,CAAAO,UAAA,oCAAmC,iBAAiB;UAInEP,EAAA,CAAAM,SAAA,EAAoC;UAAqBN,EAAzD,CAAAO,UAAA,UAAAgN,GAAA,CAAAvK,gBAAA,kBAAAuK,GAAA,CAAAvK,gBAAA,CAAA+I,QAAA,CAAoC,oBAAoB,YAAY;UAkDxB/L,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAmP,UAAA,CAAAnP,EAAA,CAAAoP,eAAA,KAAAO,GAAA,EAA4B;UAA/E3P,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAAsP,gBAAA,YAAA/B,GAAA,CAAApJ,qBAAA,CAAmC;UACxDnE,EADsF,CAAAO,UAAA,qBAAoB,oBACvF;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAgN,GAAA,CAAA3I,YAAA,CAA0B;UAME5E,EAAA,CAAAM,SAAA,GAAmC;UAGrDN,EAHkB,CAAAO,UAAA,UAAAP,EAAA,CAAAyP,WAAA,UAAAlC,GAAA,CAAAzE,iBAAA,EAAmC,sBAChC,YAAAyE,GAAA,CAAA9I,sBAAA,CAAmC,oBAAoB,cAAA8I,GAAA,CAAA7I,qBAAA,CACP,wBAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}