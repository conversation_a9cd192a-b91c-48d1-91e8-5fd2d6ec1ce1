{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/tabview\";\nfunction ContactsDetailsComponent_p_tabPanel_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.RouterLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ContactsDetailsComponent_p_tabPanel_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 9);\n    i0.ɵɵtemplate(1, ContactsDetailsComponent_p_tabPanel_7_ng_template_1_Template, 2, 2, \"ng-template\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ContactsDetailsComponent {\n  setActiveMenu(label) {\n    this.activeMenu = label;\n  }\n  constructor(router) {\n    this.router = router;\n    this.activeMenu = '';\n    this.activeIndex = 0;\n    this.scrollableTabs = [{\n      label: 'Overview',\n      RouterLink: '/store/contacts/overview'\n    }, {\n      label: 'Contacts',\n      RouterLink: '/store/contacts/contacts'\n    }, {\n      label: 'Partners',\n      RouterLink: '/store/contacts/partners'\n    }, {\n      label: 'Sales Team',\n      RouterLink: '/store/contacts/sales-team'\n    }, {\n      label: 'Opportunities',\n      RouterLink: '/store/contacts/opportunities'\n    }, {\n      label: 'AI Insights',\n      RouterLink: '/store/contacts/ai-insights'\n    }, {\n      label: 'Organization Data',\n      RouterLink: '/store/contacts/organization-data'\n    }, {\n      label: 'Attachments',\n      RouterLink: '/store/contacts/attachments'\n    }, {\n      label: 'Notes',\n      RouterLink: '/store/contacts/notes'\n    }, {\n      label: 'Activities',\n      RouterLink: '/store/contacts/activities'\n    }, {\n      label: 'Relationships',\n      RouterLink: '/store/contacts/relationships'\n    }, {\n      label: 'Tickets',\n      RouterLink: '/store/contacts/tickets'\n    }, {\n      label: 'Sales Quotes',\n      RouterLink: '/store/contacts/sales-quotes'\n    }, {\n      label: 'Sales Orders',\n      RouterLink: '/store/contacts/sales-orders'\n    }];\n  }\n  ngOnInit() {\n    this.items = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }, {\n      label: `${this.activeMenu}`\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'Change Image',\n      code: 'CI'\n    }, {\n      name: 'Block',\n      code: 'B'\n    }, {\n      name: 'Set as Obsolate',\n      code: 'SAO'\n    }];\n  }\n  goToBack() {\n    this.router.navigate(['/store/contacts']);\n  }\n  static {\n    this.ɵfac = function ContactsDetailsComponent_Factory(t) {\n      return new (t || ContactsDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsDetailsComponent,\n      selectors: [[\"app-contacts-details\"]],\n      decls: 10,\n      vars: 5,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"scrollable\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ContactsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"p-tabView\", 6);\n          i0.ɵɵtemplate(7, ContactsDetailsComponent_p_tabPanel_7_Template, 2, 1, \"p-tabPanel\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵelement(9, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.scrollableTabs);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.Breadcrumb, i4.PrimeTemplate, i5.TabView, i5.TabPanel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "RouterLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ContactsDetailsComponent_p_tabPanel_7_ng_template_1_Template", "ContactsDetailsComponent", "setActiveMenu", "activeMenu", "constructor", "router", "activeIndex", "scrollableTabs", "ngOnInit", "items", "routerLink", "home", "icon", "Actions", "name", "code", "goToBack", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "ContactsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ContactsDetailsComponent_p_tabPanel_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-contacts-details',\r\n  templateUrl: './contacts-details.component.html',\r\n  styleUrl: './contacts-details.component.scss'\r\n})\r\nexport class ContactsDetailsComponent {\r\n\r\n  items: MenuItem[] | any;\r\n    home: MenuItem | any;\r\n  \r\n    activeMenu: string = '';\r\n  \r\n    setActiveMenu(label: string): void {\r\n      this.activeMenu = label;\r\n    }\r\n  \r\n    Actions: Actions[] | undefined;\r\n    selectedActions: Actions | undefined;\r\n  \r\n    constructor(\r\n      private router: Router,\r\n    ) { }\r\n  \r\n    ngOnInit() {\r\n      this.items = [\r\n        { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n        { label: `${this.activeMenu}` },\r\n      ];\r\n  \r\n      this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n  \r\n      this.Actions = [\r\n        { name: 'Change Image', code: 'CI' },\r\n        { name: 'Block', code: 'B' },\r\n        { name: 'Set as Obsolate', code: 'SAO' },\r\n      ];\r\n    }\r\n  \r\n    activeIndex: number = 0;\r\n    scrollableTabs: any[] = [\r\n      {\r\n        label: 'Overview',\r\n        RouterLink: '/store/contacts/overview',\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        RouterLink: '/store/contacts/contacts'\r\n      },\r\n      {\r\n        label: 'Partners',\r\n        RouterLink: '/store/contacts/partners'\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        RouterLink: '/store/contacts/sales-team'\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        RouterLink: '/store/contacts/opportunities'\r\n      },\r\n      {\r\n        label: 'AI Insights',\r\n        RouterLink: '/store/contacts/ai-insights'\r\n      },\r\n      {\r\n        label: 'Organization Data',\r\n        RouterLink: '/store/contacts/organization-data'\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        RouterLink: '/store/contacts/attachments'\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        RouterLink: '/store/contacts/notes'\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        RouterLink: '/store/contacts/activities'\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        RouterLink: '/store/contacts/relationships'\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        RouterLink: '/store/contacts/tickets'\r\n      },\r\n      {\r\n        label: 'Sales Quotes',\r\n        RouterLink: '/store/contacts/sales-quotes'\r\n      },\r\n      {\r\n        label: 'Sales Orders',\r\n        RouterLink: '/store/contacts/sales-orders'\r\n      },\r\n    ];\r\n  \r\n  \r\n  \r\n    goToBack() {\r\n      this.router.navigate(['/store/contacts']);\r\n    }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <!-- <h4 class=\"m-0 p-0 ml-auto\"><span class=\"text-orange-600\">Account ID</span> 1280056</h4> -->\r\n        <!-- <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 surface-0 border-1 border-primary font-semibold'\" /> -->\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\">\r\n                <p-tabPanel *ngFor=\"let tab of scrollableTabs\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.RouterLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;ICiBwBA,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,oBAA8E;IAC1ED,EAAA,CAAAU,UAAA,IAAAC,4DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANkCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADD7F,OAAM,MAAOQ,wBAAwB;EAOjCC,aAAaA,CAACJ,KAAa;IACzB,IAAI,CAACK,UAAU,GAAGL,KAAK;EACzB;EAKAM,YACUC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAVhB,KAAAF,UAAU,GAAW,EAAE;IA4BvB,KAAAG,WAAW,GAAW,CAAC;IACvB,KAAAC,cAAc,GAAU,CACtB;MACET,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,cAAc;MACrBH,UAAU,EAAE;KACb,CACF;EA3EG;EAEJa,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAG,CACX;MAAEX,KAAK,EAAE,UAAU;MAAEY,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,EACtD;MAAEZ,KAAK,EAAE,GAAG,IAAI,CAACK,UAAU;IAAE,CAAE,CAChC;IAED,IAAI,CAACQ,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE,EACpC;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAG,CAAE,EAC5B;MAAED,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAK,CAAE,CACzC;EACH;EAgEAC,QAAQA,CAAA;IACN,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;;;uBAjGShB,wBAAwB,EAAAZ,EAAA,CAAA6B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBnB,wBAAwB;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ7BtC,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAwC,SAAA,sBAAqF;UAK7FxC,EAJI,CAAAG,YAAA,EAAM,EAIJ;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACI;UAC3BD,EAAA,CAAAU,UAAA,IAAA+B,8CAAA,wBAA8E;UAQtFzC,EADI,CAAAG,YAAA,EAAY,EACV;UACNH,EAAA,CAAAC,cAAA,aAAqD;UACjDD,EAAA,CAAAwC,SAAA,oBAA+B;UAG3CxC,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;UAvBoBH,EAAA,CAAAO,SAAA,GAAe;UAAeP,EAA9B,CAAAI,UAAA,UAAAmC,GAAA,CAAAnB,KAAA,CAAe,SAAAmB,GAAA,CAAAjB,IAAA,CAAc,uCAAuC;UASvEtB,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UACEJ,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAmC,GAAA,CAAArB,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}