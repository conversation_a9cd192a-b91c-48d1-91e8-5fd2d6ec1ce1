{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../contacts.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/tabview\";\nimport * as i10 from \"primeng/toast\";\nimport * as i11 from \"primeng/confirmdialog\";\nfunction ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ContactsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(1, ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ContactsDetailsComponent {\n  constructor(router, route, contactsservice) {\n    this.router = router;\n    this.route = route;\n    this.contactsservice = contactsservice;\n    this.unsubscribe$ = new Subject();\n    this.contactDetails = null;\n    this.sidebarDetails = null;\n    this.items = [];\n    this.id = '';\n    this.breadcrumbitems = [];\n    this.activeItem = null;\n    this.isSidebarHidden = false;\n    this.Actions = [];\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'Set As Obsolete',\n      code: 'SAO'\n    }];\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const contactId = params.get('id');\n      if (contactId) {\n        this.loadContactData(contactId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/contacts/${id}/overview`\n    }, {\n      label: 'Relationships',\n      routerLink: `/store/contacts/${id}/relationships`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/contacts/${id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/contacts/${id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/contacts/${id}/activities`\n    }, {\n      label: 'Opportunities',\n      routerLink: `/store/contacts/${id}/opportunities`\n    }, {\n      label: 'Tickets',\n      routerLink: `/store/contacts/${id}/tickets`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadContactData(contactId) {\n    this.contactsservice.getContactByID(contactId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.contactDetails = response?.data[0] || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.addresses || []);\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  goToBack() {\n    this.router.navigate(['/store/contacts']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ContactsDetailsComponent_Factory(t) {\n      return new (t || ContactsDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContactsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsDetailsComponent,\n      selectors: [[\"app-contacts-details\"]],\n      decls: 76,\n      vars: 26,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\", \"sidebar-c-details\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ContactsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function ContactsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ContactsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, ContactsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"h5\", 19);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 20)(23, \"li\", 21)(24, \"span\", 22);\n          i0.ɵɵtext(25, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"li\", 21)(28, \"span\", 22);\n          i0.ɵɵtext(29, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21)(32, \"span\", 22);\n          i0.ɵɵtext(33, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"ul\", 24)(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n          i0.ɵɵtext(40, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 28);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n          i0.ɵɵtext(47, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 28);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n          i0.ɵɵtext(54, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 28);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 25)(59, \"span\", 26)(60, \"i\", 27);\n          i0.ɵɵtext(61, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 28);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"li\", 25)(66, \"span\", 26)(67, \"i\", 27);\n          i0.ɵɵtext(68, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 28);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(72, \"div\", 29)(73, \"p-button\", 30);\n          i0.ɵɵlistener(\"click\", function ContactsDetailsComponent_Template_p_button_click_73_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(75, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.contactDetails == null ? null : ctx.contactDetails.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.contactDetails == null ? null : ctx.contactDetails.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.contactDetails == null ? null : ctx.contactDetails.account_owner) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.contactDetails == null ? null : ctx.contactDetails.contact_companies == null ? null : ctx.contactDetails.contact_companies[0] == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.contactDetails == null ? null : ctx.contactDetails.contact_companies == null ? null : ctx.contactDetails.contact_companies[0] == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.contactDetails == null ? null : ctx.contactDetails.contact_companies == null ? null : ctx.contactDetails.contact_companies[0] == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.contact_person_addresses == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.contact_person_addresses[0] == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0] == null ? null : ctx.contactDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i3.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.Breadcrumb, i5.PrimeTemplate, i6.Dropdown, i7.NgControlStatus, i7.NgModel, i8.Button, i9.TabView, i9.TabPanel, i10.Toast, i11.ConfirmDialog],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template", "ContactsDetailsComponent", "constructor", "router", "route", "contactsservice", "unsubscribe$", "contactDetails", "sidebarDetails", "items", "id", "breadcrumbitems", "activeItem", "isSidebarHidden", "Actions", "activeIndex", "ngOnInit", "snapshot", "paramMap", "get", "home", "icon", "name", "code", "makeMenuItems", "length", "setActiveTabFromURL", "pipe", "subscribe", "params", "contactId", "loadContactData", "events", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getContactByID", "next", "response", "data", "formatSidebarDetails", "addresses", "error", "console", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "ContactsService", "selectors", "decls", "vars", "consts", "template", "ContactsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "ContactsDetailsComponent_Template_p_dropdown_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "ContactsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵlistener", "ContactsDetailsComponent_Template_p_tabView_onChange_8_listener", "ContactsDetailsComponent_p_tabPanel_9_Template", "ContactsDetailsComponent_Template_p_button_click_73_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵtextInterpolate1", "bp_full_name", "bp_id", "account_owner", "contact_companies", "business_partner_person", "first_name", "last_name", "contact_person_addresses"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ContactsService } from '../contacts.service';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-contacts-details',\r\n  templateUrl: './contacts-details.component.html',\r\n  styleUrl: './contacts-details.component.scss',\r\n})\r\nexport class ContactsDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contactDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public items: MenuItem[] = [];\r\n  public home: MenuItem | any;\r\n  public id: string = '';\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public isSidebarHidden = false;\r\n  public Actions: Actions[] = [];\r\n  public selectedActions: Actions | undefined;\r\n  public activeIndex: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private contactsservice: ContactsService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'Set As Obsolete', code: 'SAO' },\r\n    ];\r\n    this.makeMenuItems(this.id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const contactId = params.get('id');\r\n        if (contactId) {\r\n          this.loadContactData(contactId);\r\n        }\r\n      });\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/contacts/${id}/overview`,\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        routerLink: `/store/contacts/${id}/relationships`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/contacts/${id}/attachments`,\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        routerLink: `/store/contacts/${id}/notes`,\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        routerLink: `/store/contacts/${id}/activities`,\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        routerLink: `/store/contacts/${id}/opportunities`,\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        routerLink: `/store/contacts/${id}/tickets`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadContactData(contactId: string): void {\r\n    this.contactsservice\r\n      .getContactByID(contactId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.contactDetails = response?.data[0] || null;\r\n          this.sidebarDetails = this.formatSidebarDetails(\r\n            response?.data[0]?.addresses || []\r\n          );\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/contacts']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full sidebar-c-details\"\r\n                    [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{contactDetails?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">CRM ID</span> : {{contactDetails?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Account Owner </span> :\r\n                                            {{contactDetails?.account_owner || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Main Contact</span> : {{\r\n                                            (contactDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (contactDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{contactDetails?.contact_companies?.[0]?.business_partner_person?.contact_person_addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICcjBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,4DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADCnG,OAAM,MAAOQ,wBAAwB;EAcnCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,eAAgC;IAFhC,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IAhBjB,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;IACnC,KAAAoB,cAAc,GAAQ,IAAI;IAC1B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,KAAK,GAAe,EAAE;IAEtB,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,UAAU,GAAoB,IAAI;IAClC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAc,EAAE;IAEvB,KAAAC,WAAW,GAAW,CAAC;EAM3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,EAAE,GAAG,IAAI,CAACN,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE1B,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACmB,OAAO,GAAG,CACb;MAAEQ,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAK,CAAE,CACzC;IACD,IAAI,CAACC,aAAa,CAAC,IAAI,CAACd,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACD,KAAK,CAACgB,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACb,UAAU,GAAG,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACiB,mBAAmB,EAAE;IAE1B,IAAI,CAACtB,KAAK,CAACc,QAAQ,CAChBS,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAACV,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIW,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC3B,MAAM,CAAC6B,MAAM,CAACL,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAACsB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAF,aAAaA,CAACd,EAAU;IACtB,IAAI,CAACD,KAAK,GAAG,CACX;MACEX,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,EACD;MACEZ,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE,mBAAmBe,EAAE;KAClC,CACF;EACH;EAEAgB,mBAAmBA,CAAA;IACjB,MAAMO,QAAQ,GAAG,IAAI,CAAC9B,MAAM,CAAC+B,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAAC5B,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMa,UAAU,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAAC7C,UAAU,CAAC8C,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAACpB,WAAW,GAAGuB,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAACH,KAAK,CAAC,IAAI,CAACM,WAAW,CAAC,IAAI,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACiC,gBAAgB,CAAC,IAAI,CAAC9B,UAAU,EAAEd,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA4C,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAChC,eAAe,GAAG,CACrB;MAAEb,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,EACtD;MAAEG,KAAK,EAAE6C,SAAS;MAAEhD,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAiD,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACpC,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACV,WAAW,GAAG8B,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACtC,KAAK,CAAC,IAAI,CAACM,WAAW,CAAC;IAEhD,IAAIgC,WAAW,EAAEpD,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAAC6C,aAAa,CAACD,WAAW,CAACpD,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQoC,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAACzB,eAAe,CACjB4C,cAAc,CAACnB,SAAS,CAAC,CACzBH,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAC;MACTsB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC5C,cAAc,GAAG4C,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;QAC/C,IAAI,CAAC5C,cAAc,GAAG,IAAI,CAAC6C,oBAAoB,CAC7CF,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,IAAI,EAAE,CACnC;MACH,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQF,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbG,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC3E,MAAM,CAAC4E,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACnE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAoE,WAAWA,CAAA;IACT,IAAI,CAAC3E,YAAY,CAAC4C,IAAI,EAAE;IACxB,IAAI,CAAC5C,YAAY,CAAC4E,QAAQ,EAAE;EAC9B;;;uBAtKWjF,wBAAwB,EAAAZ,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhG,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAjG,EAAA,CAAA8F,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAxBvF,wBAAwB;MAAAwF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBrC1G,EAAA,CAAA4G,SAAA,iBAAuD;UAG/C5G,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAA4G,SAAA,sBAA+F;UACnG5G,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBACwF;UADxDD,EAAA,CAAA6G,gBAAA,2BAAAC,sEAAAC,MAAA;YAAA/G,EAAA,CAAAgH,kBAAA,CAAAL,GAAA,CAAAM,eAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAM,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAEjE/G,EAFI,CAAAG,YAAA,EACwF,EACtF;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAA6G,gBAAA,+BAAAK,yEAAAH,MAAA;YAAA/G,EAAA,CAAAgH,kBAAA,CAAAL,GAAA,CAAAjF,WAAA,EAAAqF,MAAA,MAAAJ,GAAA,CAAAjF,WAAA,GAAAqF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAAC/G,EAAA,CAAAmH,UAAA,sBAAAC,gEAAAL,MAAA;YAAA,OAAYJ,GAAA,CAAApD,WAAA,CAAAwD,MAAA,CAAmB;UAAA,EAAC;UACzF/G,EAAA,CAAAU,UAAA,IAAA2G,8CAAA,wBAAoF;UAQ5FrH,EADI,CAAAG,YAAA,EAAY,EACV;UAUsBH,EAT5B,CAAAC,cAAA,eAAqD,eACjB,eAEe,eAC4B,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACZ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAEpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAKlD;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAChD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAKpFF,EALoF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAiD,oBAIyB;UAAlED,EAAA,CAAAmH,UAAA,mBAAAG,6DAAA;YAAA,OAASX,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAH7B3F,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAA4G,SAAA,qBAA+B;UAKnD5G,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAA4G,SAAA,uBAAmC;;;UA7GJ5G,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAuG,GAAA,CAAArF,eAAA,CAAyB,SAAAqF,GAAA,CAAA5E,IAAA,CAAc,uCAAuC;UAEpF/B,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAuG,GAAA,CAAAlF,OAAA,CAAmB;UAACzB,EAAA,CAAAuH,gBAAA,YAAAZ,GAAA,CAAAM,eAAA,CAA6B;UACzDjH,EAAA,CAAAI,UAAA,kFAAiF;UAKtEJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAuH,gBAAA,gBAAAZ,GAAA,CAAAjF,WAAA,CAA6B;UAC5B1B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAuG,GAAA,CAAAvF,KAAA,CAAU;UAYlCpB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAwH,WAAA,iBAAAb,GAAA,CAAAnF,eAAA,CAAsC;UAUlBxB,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAyH,kBAAA,OAAAd,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAAwG,YAAA,cACJ;UAGgD1H,EAAA,CAAAO,SAAA,GAC5C;UAD4CP,EAAA,CAAAyH,kBAAA,SAAAd,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAAyG,KAAA,cAC5C;UAKoD3H,EAAA,CAAAO,SAAA,GAEpD;UAFoDP,EAAA,CAAAyH,kBAAA,SAAAd,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAA0G,aAAA,cAEpD;UAEkD5H,EAAA,CAAAO,SAAA,GAKlD;UALkDP,EAAA,CAAAyH,kBAAA,UAAAd,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,kBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,qBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAApB,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,kBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,qBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAKlD;UAWiBhI,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAAxF,cAAA,kBAAAwF,GAAA,CAAAxF,cAAA,qBAAAwF,GAAA,CAAAxF,cAAA,IAAAkD,OAAA,SAAuC;UAMvCrE,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAAxF,cAAA,kBAAAwF,GAAA,CAAAxF,cAAA,qBAAAwF,GAAA,CAAAxF,cAAA,IAAAkE,YAAA,SAA4C;UAO9CrF,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAAzF,cAAA,kBAAAyF,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,kBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,qBAAAlB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,kBAAAnB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,kBAAAtB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,qBAAAtB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3C,aAAA,kBAAAqB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3C,aAAA,qBAAAqB,GAAA,CAAAzF,cAAA,CAAA2G,iBAAA,IAAAC,uBAAA,CAAAG,wBAAA,IAAA3C,aAAA,IAAAD,YAAA,SACP;UAKSrF,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAAxF,cAAA,kBAAAwF,GAAA,CAAAxF,cAAA,qBAAAwF,GAAA,CAAAxF,cAAA,IAAAgE,aAAA,SAA6C;UAM7CnF,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAAxF,cAAA,kBAAAwF,GAAA,CAAAxF,cAAA,qBAAAwF,GAAA,CAAAxF,cAAA,IAAAoE,WAAA,SAA2C;UAUlDvF,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAwH,WAAA,gBAAAb,GAAA,CAAAnF,eAAA,CAAqC;UAF/DxB,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}