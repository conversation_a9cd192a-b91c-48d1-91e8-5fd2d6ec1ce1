{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../prospects.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/toast\";\nimport * as i11 from \"primeng/confirmdialog\";\nfunction ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tab_r1.label, \" \");\n  }\n}\nfunction ProspectsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 37);\n    i0.ɵɵtemplate(1, ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ProspectsDetailsComponent {\n  constructor(route, router, messageservice, formBuilder, confirmationservice, prospectsservice) {\n    this.route = route;\n    this.router = router;\n    this.messageservice = messageservice;\n    this.formBuilder = formBuilder;\n    this.confirmationservice = confirmationservice;\n    this.prospectsservice = prospectsservice;\n    this.unsubscribe$ = new Subject();\n    this.prospectDetails = null;\n    this.sidebarDetails = null;\n    this.NoteDetails = null;\n    this.customerData = null;\n    this.items = [];\n    this.activeItem = null;\n    this.breadcrumbitems = [];\n    this.id = '';\n    this.partner_role = '';\n    this.bp_status = '';\n    this.bp_extension_docId = '';\n    this.Actions = [];\n    this.activeIndex = 0;\n    this.isSidebarHidden = false;\n    this.submitted = false;\n    this.saving = false;\n    this.GlobalNoteForm = this.formBuilder.group({\n      note: ['']\n    });\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.prospectsservice.getGlobalNote(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.NoteDetails = response?.data[0] || [];\n        this.GlobalNoteForm.patchValue({\n          note: response?.data[0]?.note\n        });\n      },\n      error: error => {\n        console.error('Error fetching global note:', error);\n      }\n    });\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const prospectId = params.get('id');\n      if (prospectId) {\n        this.loadProspectData(prospectId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/prospects/${id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/prospects/${id}/contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/prospects/${id}/sales-team`\n    },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/prospects/${id}/ai-insights`,\n    // },\n    {\n      label: 'Organization Data',\n      routerLink: `/store/prospects/${id}/organization-data`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/prospects/${id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/prospects/${id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/prospects/${id}/activities`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Prospects',\n      routerLink: ['/store/prospects']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadProspectData(prospectId) {\n    this.prospectsservice.getProspectByID(prospectId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response) {\n          this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\n          this.bp_extension_docId = response?.data?.[0]?.bp_extension?.documentId;\n          this.Actions = [{\n            name: 'Convert to Customer',\n            code: 'CI'\n          }, {\n            name: this.bp_status === 'OBSOLETE' ? 'Set As Active' : 'Set As Obsolete',\n            code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO'\n          }];\n          const partner_role = response?.data?.[0]?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n          this.partner_role = partner_role?.bp_identification?.business_partner?.bp_full_name || null;\n          this.prospectDetails = response?.data[0] || null;\n          this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.addresses || []);\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      CI: () => this.ConvertToCustomer(event),\n      SAA: () => this.UpdateStatus(this.bp_extension_docId, 'ACTIVE'),\n      SAO: () => this.UpdateStatus(this.bp_extension_docId, 'OBSOLETE')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  ConvertToCustomer(event) {\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\n    this.prospectsservice.bpCreation(this.customerData).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Convert to Customer Successfully!'\n        });\n        this.prospectsservice.getProspectByID(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: error => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: error?.error?.message || 'Error while processing your request.'\n        });\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  createBusinessPartnerObject(data) {\n    return {\n      BusinessPartner: data?.bp_id || '',\n      OrganizationBPName1: data?.bp_full_name || '',\n      to_BusinessPartnerAddress: data?.addresses?.map(address => ({\n        Country: address.country_code || 'US',\n        Region: address.region || '',\n        HouseNumber: address.house_number || '',\n        AdditionalStreetPrefixName: address.additional_street_prefix_name || '',\n        AdditionalStreetSuffixName: address.additional_street_suffix_name || '',\n        StreetName: address.street_name || '',\n        PostalCode: address.postal_code || '',\n        CityName: address.city_name || '',\n        Language: 'EN',\n        ...(address.address_usages?.length ? {\n          to_AddressUsage: address.address_usages.map(usage => ({\n            AddressUsage: usage.address_usage || ''\n          }))\n        } : {}),\n        to_EmailAddress: address.emails?.map(email => ({\n          EmailAddress: email.email_address || ''\n        })) || [],\n        to_PhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '1').map(phone => ({\n          PhoneNumber: phone.phone_number || ''\n        })) || [],\n        to_URLAddress: address.home_page_urls?.map(url => ({\n          WebsiteURL: url.website_url || ''\n        })) || [],\n        to_FaxNumber: address.fax_numbers?.map(fax => ({\n          FaxNumber: fax.fax_number || ''\n        })) || [],\n        to_MobilePhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '3').map(phone => ({\n          PhoneNumber: phone.phone_number || ''\n        })) || []\n      })) || [],\n      to_BusinessPartnerRole: [{\n        BusinessPartnerRole: 'FLCU01'\n      }, {\n        BusinessPartnerRole: 'FLCU00'\n      }],\n      to_Customer: data?.customer ? {\n        Customer: data.customer.customer_id || '',\n        OrderIsBlockedForCustomer: data.customer.order_is_blocked_for_customer || '',\n        to_CustomerSalesArea: Array.isArray(data.customer.partner_functions) ? Object.values(data.customer.partner_functions.reduce((acc, salesArea) => {\n          const key = `${salesArea.sales_organization}-${salesArea.distribution_channel}-${salesArea.division}`;\n          if (!acc[key]) {\n            acc[key] = {\n              SalesOrganization: salesArea.sales_organization || '',\n              DistributionChannel: salesArea.distribution_channel || '',\n              Division: salesArea.division || '',\n              to_PartnerFunction: new Set() // Use a Set to remove duplicates\n            };\n          }\n          // Collect partner functions\n          data.customer.partner_functions.filter(partner => partner.sales_organization === salesArea.sales_organization && partner.distribution_channel === salesArea.distribution_channel && partner.division === salesArea.division).forEach(partner => {\n            acc[key].to_PartnerFunction.add(JSON.stringify({\n              PartnerFunction: partner.partner_function || '',\n              BPCustomerNumber: partner.bp_customer_number || ''\n            }));\n          });\n          // Add new partner functions (CP)\n          data?.contact_companies?.forEach(company => {\n            acc[key].to_PartnerFunction.add(JSON.stringify({\n              PartnerFunction: 'CP',\n              BPCustomerNumber: company?.bp_person_id || ''\n            }));\n          });\n          return acc;\n        }, {})).map(area => ({\n          ...area,\n          to_PartnerFunction: Array.from(area.to_PartnerFunction).map(pf => JSON.parse(pf))\n        })) : []\n      } : null,\n      to_BusinessPartnerContact: Array.isArray(data?.contact_companies) ? data.contact_companies.map(company => ({\n        BusinessPartnerPerson: company?.bp_person_id || '',\n        FirstName: company?.business_partner_person?.first_name || '',\n        MiddleName: company?.business_partner_person?.middle_name || '',\n        LastName: company?.business_partner_person?.last_name || '',\n        to_ContactAddress: Array.isArray(company?.business_partner_person?.contact_person_addresses) ? company.business_partner_person.contact_person_addresses.map(addr => ({\n          Country: addr.country_code || 'US',\n          Region: addr.region || '',\n          HouseNumber: addr.house_number || '',\n          AdditionalStreetPrefixName: addr.additional_street_prefix_name || '',\n          AdditionalStreetSuffixName: addr.additional_street_suffix_name || '',\n          StreetName: addr.street_name || '',\n          PostalCode: addr.postal_code || '',\n          CityName: addr.city_name || '',\n          Language: 'EN',\n          ...(Array.isArray(addr.to_AddressUsage) && addr.to_AddressUsage.length > 0 ? {\n            to_AddressUsage: addr.to_AddressUsage.map(usage => ({\n              AddressUsage: usage.AddressUsage || ''\n            }))\n          } : {}),\n          to_EmailAddress: addr.emails?.map(email => ({\n            EmailAddress: email.email_address || ''\n          })) || [],\n          to_PhoneNumber: addr.phone_numbers?.filter(ph => ph.phone_number_type === '1').map(phone => ({\n            PhoneNumber: phone.phone_number || ''\n          })) || [],\n          to_URLAddress: addr.home_page_urls?.map(url => ({\n            WebsiteURL: url.website_url || ''\n          })) || [],\n          to_FaxNumber: addr.fax_numbers?.map(fax => ({\n            FaxNumber: fax.fax_number || ''\n          })) || [],\n          to_MobilePhoneNumber: addr.phone_numbers?.filter(ph => ph.phone_number_type === '3').map(phone => ({\n            PhoneNumber: phone.phone_number || ''\n          })) || []\n        })) : [],\n        to_ContactRelationship: {\n          ContactPersonDepartment: company?.person_func_and_dept?.contact_person_department || '',\n          ContactPersonFunction: company?.person_func_and_dept?.contact_person_function || ''\n        },\n        to_BusinessPartnerRole: [{\n          BusinessPartnerRole: 'BUP001'\n        }]\n      })) : []\n    };\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.GlobalNoteForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.GlobalNoteForm.value\n      };\n      const data = {\n        note: value?.note,\n        bp_id: _this?.id,\n        ...(!_this.NoteDetails.documentId ? {\n          is_global_note: true\n        } : {})\n      };\n      const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.prospectsservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n      : _this.prospectsservice.createNote(data); // Create if not exists\n      apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Prospect Note Updated successFully!'\n          });\n          _this.prospectsservice.getProspectByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      bp_status: status,\n      bp_id: this.id\n    };\n    const apiCall = docid ? this.prospectsservice.updateBpExtension(docid, data) // Update if exists\n    : this.prospectsservice.createBpExtension(data); // Create if not exists\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/prospects']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ProspectsDetailsComponent_Factory(t) {\n      return new (t || ProspectsDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i4.ProspectsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsDetailsComponent,\n      selectors: [[\"app-prospects-details\"]],\n      decls: 83,\n      vars: 26,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"onChange\", \"options\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ProspectsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n            return ctx.onActionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, ProspectsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"h5\", 19);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 20)(23, \"li\", 21)(24, \"span\", 22);\n          i0.ɵɵtext(25, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"li\", 21)(28, \"span\", 22);\n          i0.ɵɵtext(29, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21)(32, \"span\", 22);\n          i0.ɵɵtext(33, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"ul\", 24)(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n          i0.ɵɵtext(40, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 28);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n          i0.ɵɵtext(47, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 28);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n          i0.ɵɵtext(54, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 28);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 25)(59, \"span\", 26)(60, \"i\", 27);\n          i0.ɵɵtext(61, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 28);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"li\", 25)(66, \"span\", 26)(67, \"i\", 27);\n          i0.ɵɵtext(68, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 28);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(72, \"form\", 29)(73, \"div\", 30)(74, \"h4\", 31);\n          i0.ɵɵtext(75, \"Global Note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 32);\n          i0.ɵɵelement(77, \"textarea\", 33);\n          i0.ɵɵelementStart(78, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_button_click_78_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(79, \"div\", 35)(80, \"p-button\", 36);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_p_button_click_80_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(82, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name) || \"-\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_id) || \"-\", \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i5.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i2.PrimeTemplate, i3.FormGroupDirective, i3.FormControlName, i6.ButtonDirective, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.Toast, i11.ConfirmDialog],\n      styles: [\".prospect-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .prospect-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .prospect-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .prospect-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL3Byb3NwZWN0cy1kZXRhaWxzL3Byb3NwZWN0cy1kZXRhaWxzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVRO0VBQ0ksa0JBQUE7QUFEWjtBQUdZO0VBQ0ksNEJBQUE7RUFDQSwyQ0FBQTtBQURoQjtBQUdnQjtFQUNJLFNBQUE7QUFEcEI7QUFLWTtFQUNJLDRCQUFBO0VBQ0EsaUJBQUE7QUFIaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLnByb3NwZWN0LXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cge1xyXG4gICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS0xMDApO1xyXG5cclxuICAgICAgICAgICAgICAgIGg0IHtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxLjcxNHJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtemplate", "ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template", "ProspectsDetailsComponent", "constructor", "route", "router", "messageservice", "formBuilder", "confirmationservice", "prospectsservice", "unsubscribe$", "prospectDetails", "sidebarDetails", "NoteDetails", "customerData", "items", "activeItem", "breadcrumbitems", "id", "partner_role", "bp_status", "bp_extension_docId", "Actions", "activeIndex", "isSidebarHidden", "submitted", "saving", "GlobalNoteForm", "group", "note", "ngOnInit", "snapshot", "paramMap", "get", "getGlobalNote", "pipe", "subscribe", "next", "response", "data", "patchValue", "error", "console", "home", "icon", "makeMenuItems", "length", "setActiveTabFromURL", "params", "prospectId", "loadProspectData", "events", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getProspectByID", "bp_extension", "documentId", "name", "code", "customer", "partner_functions", "find", "p", "partner_function", "bp_identification", "business_partner", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "onActionChange", "actionCode", "value", "actionsMap", "CI", "ConvertToCustomer", "SAA", "UpdateStatus", "SAO", "action", "confirm", "message", "header", "accept", "createBusinessPartnerObject", "bpCreation", "add", "severity", "detail", "BusinessPartner", "bp_id", "OrganizationBPName1", "to_BusinessPartnerAddress", "Country", "country_code", "Region", "HouseNumber", "AdditionalStreetPrefixName", "additional_street_prefix_name", "AdditionalStreetSuffixName", "additional_street_suffix_name", "StreetName", "PostalCode", "CityName", "Language", "to_AddressUsage", "AddressUsage", "to_<PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_PhoneNumber", "ph", "phone_number_type", "phone", "PhoneNumber", "to_URLAddress", "WebsiteURL", "to_FaxNumber", "fax_numbers", "fax", "FaxNumber", "fax_number", "to_MobilePhoneNumber", "to_BusinessPartnerRole", "BusinessPartnerRole", "to_Customer", "Customer", "customer_id", "OrderIsBlockedForCustomer", "order_is_blocked_for_customer", "to_CustomerSalesArea", "Array", "isArray", "Object", "values", "reduce", "acc", "salesArea", "key", "sales_organization", "distribution_channel", "division", "SalesOrganization", "DistributionChannel", "Division", "to_PartnerFunction", "Set", "partner", "for<PERSON>ach", "JSON", "stringify", "PartnerFunction", "BPCustomerNumber", "bp_customer_number", "contact_companies", "company", "bp_person_id", "area", "from", "pf", "parse", "to_BusinessPartnerContact", "BusinessPartnerPerson", "FirstName", "business_partner_person", "first_name", "MiddleName", "middle_name", "LastName", "last_name", "to_ContactAddress", "contact_person_addresses", "addr", "to_ContactRelationship", "ContactPersonDepartment", "person_func_and_dept", "contact_person_department", "ContactPersonFunction", "contact_person_function", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "is_global_note", "apiCall", "updateNote", "createNote", "docid", "status", "updateBpExtension", "createBpExtension", "setTimeout", "window", "location", "reload", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "MessageService", "i3", "FormBuilder", "ConfirmationService", "i4", "ProspectsService", "selectors", "decls", "vars", "consts", "template", "ProspectsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener", "$event", "ɵɵtwoWayListener", "ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵtwoWayBindingSet", "ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener", "ProspectsDetailsComponent_p_tabPanel_9_Template", "ProspectsDetailsComponent_Template_button_click_78_listener", "ProspectsDetailsComponent_Template_p_button_click_80_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵtextInterpolate"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProspectsService } from '../prospects.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects-details',\r\n  templateUrl: './prospects-details.component.html',\r\n  styleUrl: './prospects-details.component.scss',\r\n})\r\nexport class ProspectsDetailsComponent implements OnInit, OnDestroy {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public prospectDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public NoteDetails: any = null;\r\n  public customerData: any = null;\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public home: MenuItem | any;\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public id: string = '';\r\n  public partner_role: string = '';\r\n  public bp_status: string = '';\r\n  public bp_extension_docId: string = '';\r\n  public Actions: Actions[] = [];\r\n  public activeIndex: number = 0;\r\n  public isSidebarHidden = false;\r\n  public submitted = false;\r\n  public saving = false;\r\n\r\n  public GlobalNoteForm: FormGroup = this.formBuilder.group({\r\n    note: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private messageservice: MessageService,\r\n    private formBuilder: FormBuilder,\r\n    private confirmationservice: ConfirmationService,\r\n    private prospectsservice: ProspectsService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.prospectsservice\r\n      .getGlobalNote(this.id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.NoteDetails = response?.data[0] || [];\r\n          this.GlobalNoteForm.patchValue({\r\n            note: response?.data[0]?.note,\r\n          });\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching global note:', error);\r\n        },\r\n      });\r\n\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.makeMenuItems(this.id);\r\n\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const prospectId = params.get('id');\r\n        if (prospectId) {\r\n          this.loadProspectData(prospectId);\r\n        }\r\n      });\r\n\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      { label: 'Overview', routerLink: `/store/prospects/${id}/overview` },\r\n      { label: 'Contacts', routerLink: `/store/prospects/${id}/contacts` },\r\n      { label: 'Sales Team', routerLink: `/store/prospects/${id}/sales-team` },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/prospects/${id}/ai-insights`,\r\n      // },\r\n      {\r\n        label: 'Organization Data',\r\n        routerLink: `/store/prospects/${id}/organization-data`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/prospects/${id}/attachments`,\r\n      },\r\n      { label: 'Notes', routerLink: `/store/prospects/${id}/notes` },\r\n      { label: 'Activities', routerLink: `/store/prospects/${id}/activities` },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Prospects', routerLink: ['/store/prospects'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadProspectData(prospectId: string): void {\r\n    this.prospectsservice\r\n      .getProspectByID(prospectId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response) {\r\n            this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\r\n            this.bp_extension_docId =\r\n              response?.data?.[0]?.bp_extension?.documentId;\r\n            this.Actions = [\r\n              { name: 'Convert to Customer', code: 'CI' },\r\n              {\r\n                name:\r\n                  this.bp_status === 'OBSOLETE'\r\n                    ? 'Set As Active'\r\n                    : 'Set As Obsolete',\r\n                code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO',\r\n              },\r\n            ];\r\n\r\n            const partner_role =\r\n              response?.data?.[0]?.customer?.partner_functions?.find(\r\n                (p: any) => p.partner_function === 'YI'\r\n              );\r\n            this.partner_role =\r\n              partner_role?.bp_identification?.business_partner?.bp_full_name ||\r\n              null;\r\n            this.prospectDetails = response?.data[0] || null;\r\n            this.sidebarDetails = this.formatSidebarDetails(\r\n              response?.data[0]?.addresses || []\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      CI: () => this.ConvertToCustomer(event),\r\n      SAA: () => this.UpdateStatus(this.bp_extension_docId, 'ACTIVE'),\r\n      SAO: () => this.UpdateStatus(this.bp_extension_docId, 'OBSOLETE'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  ConvertToCustomer(event: any) {\r\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\r\n    this.prospectsservice\r\n      .bpCreation(this.customerData)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Convert to Customer Successfully!',\r\n          });\r\n          this.prospectsservice\r\n            .getProspectByID(this.id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (error: any) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail:\r\n              error?.error?.message || 'Error while processing your request.',\r\n          });\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  createBusinessPartnerObject(data: any) {\r\n    return {\r\n      BusinessPartner: data?.bp_id || '',\r\n      OrganizationBPName1: data?.bp_full_name || '',\r\n      to_BusinessPartnerAddress:\r\n        data?.addresses?.map((address: any) => ({\r\n          Country: address.country_code || 'US',\r\n          Region: address.region || '',\r\n          HouseNumber: address.house_number || '',\r\n          AdditionalStreetPrefixName:\r\n            address.additional_street_prefix_name || '',\r\n          AdditionalStreetSuffixName:\r\n            address.additional_street_suffix_name || '',\r\n          StreetName: address.street_name || '',\r\n          PostalCode: address.postal_code || '',\r\n          CityName: address.city_name || '',\r\n          Language: 'EN',\r\n          ...(address.address_usages?.length\r\n            ? {\r\n                to_AddressUsage: address.address_usages.map((usage: any) => ({\r\n                  AddressUsage: usage.address_usage || '',\r\n                })),\r\n              }\r\n            : {}),\r\n          to_EmailAddress:\r\n            address.emails?.map((email: any) => ({\r\n              EmailAddress: email.email_address || '',\r\n            })) || [],\r\n          to_PhoneNumber:\r\n            address.phone_numbers\r\n              ?.filter((ph: any) => ph.phone_number_type === '1')\r\n              .map((phone: any) => ({\r\n                PhoneNumber: phone.phone_number || '',\r\n              })) || [],\r\n          to_URLAddress:\r\n            address.home_page_urls?.map((url: any) => ({\r\n              WebsiteURL: url.website_url || '',\r\n            })) || [],\r\n          to_FaxNumber:\r\n            address.fax_numbers?.map((fax: any) => ({\r\n              FaxNumber: fax.fax_number || '',\r\n            })) || [],\r\n          to_MobilePhoneNumber:\r\n            address.phone_numbers\r\n              ?.filter((ph: any) => ph.phone_number_type === '3')\r\n              .map((phone: any) => ({\r\n                PhoneNumber: phone.phone_number || '',\r\n              })) || [],\r\n        })) || [],\r\n\r\n      to_BusinessPartnerRole: [\r\n        { BusinessPartnerRole: 'FLCU01' },\r\n        { BusinessPartnerRole: 'FLCU00' },\r\n      ],\r\n\r\n      to_Customer: data?.customer\r\n        ? {\r\n            Customer: data.customer.customer_id || '',\r\n            OrderIsBlockedForCustomer:\r\n              data.customer.order_is_blocked_for_customer || '',\r\n            to_CustomerSalesArea: Array.isArray(data.customer.partner_functions)\r\n              ? Object.values(\r\n                  data.customer.partner_functions.reduce(\r\n                    (acc: any, salesArea: any) => {\r\n                      const key = `${salesArea.sales_organization}-${salesArea.distribution_channel}-${salesArea.division}`;\r\n\r\n                      if (!acc[key]) {\r\n                        acc[key] = {\r\n                          SalesOrganization: salesArea.sales_organization || '',\r\n                          DistributionChannel:\r\n                            salesArea.distribution_channel || '',\r\n                          Division: salesArea.division || '',\r\n                          to_PartnerFunction: new Set(), // Use a Set to remove duplicates\r\n                        };\r\n                      }\r\n\r\n                      // Collect partner functions\r\n                      data.customer.partner_functions\r\n                        .filter(\r\n                          (partner: any) =>\r\n                            partner.sales_organization ===\r\n                              salesArea.sales_organization &&\r\n                            partner.distribution_channel ===\r\n                              salesArea.distribution_channel &&\r\n                            partner.division === salesArea.division\r\n                        )\r\n                        .forEach((partner: any) => {\r\n                          acc[key].to_PartnerFunction.add(\r\n                            JSON.stringify({\r\n                              PartnerFunction: partner.partner_function || '',\r\n                              BPCustomerNumber:\r\n                                partner.bp_customer_number || '',\r\n                            })\r\n                          );\r\n                        });\r\n\r\n                      // Add new partner functions (CP)\r\n                      data?.contact_companies?.forEach((company: any) => {\r\n                        acc[key].to_PartnerFunction.add(\r\n                          JSON.stringify({\r\n                            PartnerFunction: 'CP',\r\n                            BPCustomerNumber: company?.bp_person_id || '',\r\n                          })\r\n                        );\r\n                      });\r\n\r\n                      return acc;\r\n                    },\r\n                    {}\r\n                  )\r\n                ).map((area: any) => ({\r\n                  ...area,\r\n                  to_PartnerFunction: Array.from(area.to_PartnerFunction).map(\r\n                    (pf: any) => JSON.parse(pf)\r\n                  ),\r\n                }))\r\n              : [],\r\n          }\r\n        : null,\r\n\r\n      to_BusinessPartnerContact: Array.isArray(data?.contact_companies)\r\n        ? data.contact_companies.map((company: any) => ({\r\n            BusinessPartnerPerson: company?.bp_person_id || '',\r\n            FirstName: company?.business_partner_person?.first_name || '',\r\n            MiddleName: company?.business_partner_person?.middle_name || '',\r\n            LastName: company?.business_partner_person?.last_name || '',\r\n            to_ContactAddress: Array.isArray(\r\n              company?.business_partner_person?.contact_person_addresses\r\n            )\r\n              ? company.business_partner_person.contact_person_addresses.map(\r\n                  (addr: any) => ({\r\n                    Country: addr.country_code || 'US',\r\n                    Region: addr.region || '',\r\n                    HouseNumber: addr.house_number || '',\r\n                    AdditionalStreetPrefixName:\r\n                      addr.additional_street_prefix_name || '',\r\n                    AdditionalStreetSuffixName:\r\n                      addr.additional_street_suffix_name || '',\r\n                    StreetName: addr.street_name || '',\r\n                    PostalCode: addr.postal_code || '',\r\n                    CityName: addr.city_name || '',\r\n                    Language: 'EN',\r\n                    ...(Array.isArray(addr.to_AddressUsage) &&\r\n                    addr.to_AddressUsage.length > 0\r\n                      ? {\r\n                          to_AddressUsage: addr.to_AddressUsage.map(\r\n                            (usage: any) => ({\r\n                              AddressUsage: usage.AddressUsage || '',\r\n                            })\r\n                          ),\r\n                        }\r\n                      : {}),\r\n                    to_EmailAddress:\r\n                      addr.emails?.map((email: any) => ({\r\n                        EmailAddress: email.email_address || '',\r\n                      })) || [],\r\n                    to_PhoneNumber:\r\n                      addr.phone_numbers\r\n                        ?.filter((ph: any) => ph.phone_number_type === '1')\r\n                        .map((phone: any) => ({\r\n                          PhoneNumber: phone.phone_number || '',\r\n                        })) || [],\r\n                    to_URLAddress:\r\n                      addr.home_page_urls?.map((url: any) => ({\r\n                        WebsiteURL: url.website_url || '',\r\n                      })) || [],\r\n                    to_FaxNumber:\r\n                      addr.fax_numbers?.map((fax: any) => ({\r\n                        FaxNumber: fax.fax_number || '',\r\n                      })) || [],\r\n                    to_MobilePhoneNumber:\r\n                      addr.phone_numbers\r\n                        ?.filter((ph: any) => ph.phone_number_type === '3')\r\n                        .map((phone: any) => ({\r\n                          PhoneNumber: phone.phone_number || '',\r\n                        })) || [],\r\n                  })\r\n                )\r\n              : [],\r\n            to_ContactRelationship: {\r\n              ContactPersonDepartment:\r\n                company?.person_func_and_dept?.contact_person_department || '',\r\n              ContactPersonFunction:\r\n                company?.person_func_and_dept?.contact_person_function || '',\r\n            },\r\n            to_BusinessPartnerRole: [{ BusinessPartnerRole: 'BUP001' }],\r\n          }))\r\n        : [],\r\n    };\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.GlobalNoteForm.invalid) {\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    const value = { ...this.GlobalNoteForm.value };\r\n\r\n    const data = {\r\n      note: value?.note,\r\n      bp_id: this?.id,\r\n      ...(!this.NoteDetails.documentId ? { is_global_note: true } : {}),\r\n    };\r\n\r\n    const apiCall =\r\n      this.NoteDetails && this.NoteDetails.documentId\r\n        ? this.prospectsservice.updateNote(this.NoteDetails.documentId, data) // Update if exists\r\n        : this.prospectsservice.createNote(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Prospect Note Updated successFully!',\r\n        });\r\n\r\n        this.prospectsservice\r\n          .getProspectByID(this.id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      bp_status: status,\r\n      bp_id: this.id,\r\n    };\r\n    const apiCall = docid\r\n      ? this.prospectsservice.updateBpExtension(docid, data) // Update if exists\r\n      : this.prospectsservice.createBpExtension(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Action Updated Successfully!',\r\n        });\r\n\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 1000);\r\n      },\r\n      error: () => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/prospects']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" optionLabel=\"name\"\r\n            (onChange)=\"onActionChange($event)\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n    </div>\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\"\r\n                            routerLinkActive=\"active-tab\">\r\n                            {{ tab.label }}\r\n                        </a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">{{prospectDetails?.bp_full_name || \"-\"}}</h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">CRM\r\n                                                ID</span> :\r\n                                            {{prospectDetails?.bp_id || \"-\"}}</li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">S4/HANA\r\n                                                ID</span> :\r\n                                            152ASD5585</li> -->\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">Account\r\n                                                Owner </span> :\r\n                                            {{partner_role || \"-\"}}</li>\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">Main\r\n                                                Contact</span> :\r\n                                            {{\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i> Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{prospectDetails?.contact_companies?.[0]?.business_partner_person?.contact_person_addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i>\r\n                                        Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i> Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                    <form [formGroup]=\"GlobalNoteForm\">\r\n                        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden mt-5 p-3\">\r\n                            <h4 class=\"mb-3 font-semibold text-color\">Global Note</h4>\r\n                            <div class=\"flex flex-column gap-3\">\r\n                                <textarea formControlName=\"note\" rows=\"4\"\r\n                                    class=\"w-full h-8rem p-2 border-1 border-round\"\r\n                                    placeholder=\"Enter your note here...\"></textarea>\r\n                                <button pButton type=\"button\" (click)=\"onNoteSubmit()\" label=\"Save Note\"\r\n                                    class=\"p-button-rounded justify-content-center w-9rem h-3rem\"></button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n                <!-- <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n                    <router-outlet></router-outlet>\r\n                </div> -->\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": ";AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICajBC,EAAA,CAAAC,cAAA,YAEkC;IAC9BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IAG5BN,EAAA,CAAAO,SAAA,EACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACJ;;;;;IANRT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAOpCX,EAAA,CAAAG,YAAA,EAAa;;;IARwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADGnG,OAAM,MAAOQ,yBAAyB;EAwBpCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,mBAAwC,EACxCC,gBAAkC;IALlC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA7BlB,KAAAC,YAAY,GAAG,IAAItB,OAAO,EAAQ;IACnC,KAAAuB,eAAe,GAAQ,IAAI;IAC3B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAoB,IAAI;IAElC,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,OAAO,GAAc,EAAE;IACvB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAC,cAAc,GAAc,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACxDC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACZ,EAAE,GAAG,IAAI,CAACd,KAAK,CAAC2B,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACxB,gBAAgB,CAClByB,aAAa,CAAC,IAAI,CAAChB,EAAE,CAAC,CACtBiB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzB,WAAW,GAAGyB,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;QAC1C,IAAI,CAACZ,cAAc,CAACa,UAAU,CAAC;UAC7BX,IAAI,EAAES,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEV;SAC1B,CAAC;MACJ,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;IAEJ,IAAI,CAACE,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEhD,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACiD,aAAa,CAAC,IAAI,CAAC3B,EAAE,CAAC;IAE3B,IAAI,IAAI,CAACH,KAAK,CAAC+B,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC9B,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACgC,mBAAmB,EAAE;IAE1B,IAAI,CAAC3C,KAAK,CAAC4B,QAAQ,CAChBG,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAEY,MAAM,IAAI;MACpB,MAAMC,UAAU,GAAGD,MAAM,CAACf,GAAG,CAAC,IAAI,CAAC;MACnC,IAAIgB,UAAU,EAAE;QACd,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC;IACF,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5C,MAAM,CAAC8C,MAAM,CAAChB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAAC0B,SAAS,CAAC,MAAK;MACnE,IAAI,CAACW,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAF,aAAaA,CAAC3B,EAAU;IACtB,IAAI,CAACH,KAAK,GAAG,CACX;MAAEhB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE;IACxE;IACA;IACA;IACA;IACA;MACEnB,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE,oBAAoBsB,EAAE;KACnC,EACD;MACEnB,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,oBAAoBsB,EAAE;KACnC,EACD;MAAEnB,KAAK,EAAE,OAAO;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAQ,CAAE,EAC9D;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE,CACzE;EACH;EAEA6B,mBAAmBA,CAAA;IACjB,MAAMK,QAAQ,GAAG,IAAI,CAAC/C,MAAM,CAACgD,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAACzC,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMW,UAAU,GAAG,IAAI,CAAC1C,KAAK,CAAC2C,SAAS,CAAEC,GAAG,IAC1CA,GAAG,CAAC/D,UAAU,CAACgE,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAAC/B,WAAW,GAAGkC,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACzC,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC,IAAI,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAAC8C,gBAAgB,CAAC,IAAI,CAAC7C,UAAU,EAAEjB,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA8D,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC7C,eAAe,GAAG,CACrB;MAAElB,KAAK,EAAE,WAAW;MAAEH,UAAU,EAAE,CAAC,kBAAkB;IAAC,CAAE,EACxD;MAAEG,KAAK,EAAE+D,SAAS;MAAElE,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAmE,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACjD,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACvB,WAAW,GAAGyC,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACnD,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC;IAEhD,IAAI2C,WAAW,EAAEtE,UAAU,EAAE;MAC3B,IAAI,CAACS,MAAM,CAAC8D,aAAa,CAACD,WAAW,CAACtE,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQsD,gBAAgBA,CAACD,UAAkB;IACzC,IAAI,CAACxC,gBAAgB,CAClB2D,eAAe,CAACnB,UAAU,CAAC,CAC3Bd,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAClB,SAAS,GAAGkB,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE8B,YAAY,EAAEjD,SAAS;UAC7D,IAAI,CAACC,kBAAkB,GACrBiB,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE8B,YAAY,EAAEC,UAAU;UAC/C,IAAI,CAAChD,OAAO,GAAG,CACb;YAAEiD,IAAI,EAAE,qBAAqB;YAAEC,IAAI,EAAE;UAAI,CAAE,EAC3C;YACED,IAAI,EACF,IAAI,CAACnD,SAAS,KAAK,UAAU,GACzB,eAAe,GACf,iBAAiB;YACvBoD,IAAI,EAAE,IAAI,CAACpD,SAAS,KAAK,UAAU,GAAG,KAAK,GAAG;WAC/C,CACF;UAED,MAAMD,YAAY,GAChBmB,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAEkC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CACnDC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;UACH,IAAI,CAAC1D,YAAY,GACfA,YAAY,EAAE2D,iBAAiB,EAAEC,gBAAgB,EAAEC,YAAY,IAC/D,IAAI;UACN,IAAI,CAACrE,eAAe,GAAG2B,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;UAChD,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACqE,oBAAoB,CAC7C3C,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAE2C,SAAS,IAAI,EAAE,CACnC;QACH;MACF,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQwC,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,cAAcA,CAACxC,KAAU;IACvB,MAAMyC,UAAU,GAAGzC,KAAK,CAAC0C,KAAK,EAAElC,IAAI;IAEpC,MAAMmC,UAAU,GAAkC;MAChDC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACC,iBAAiB,CAAC7C,KAAK,CAAC;MACvC8C,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC1F,kBAAkB,EAAE,QAAQ,CAAC;MAC/D2F,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC1F,kBAAkB,EAAE,UAAU;KACjE;IAED,MAAM4F,MAAM,GAAGN,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIQ,MAAM,EAAE;MACV,IAAI,CAACzG,mBAAmB,CAAC0G,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjBxE,IAAI,EAAE,4BAA4B;QAClCyE,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAJ,iBAAiBA,CAAC7C,KAAU;IAC1B,IAAI,CAAClD,YAAY,GAAG,IAAI,CAACwG,2BAA2B,CAAC,IAAI,CAAC3G,eAAe,CAAC;IAC1E,IAAI,CAACF,gBAAgB,CAClB8G,UAAU,CAAC,IAAI,CAACzG,YAAY,CAAC,CAC7BqB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAChC,cAAc,CAACkH,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACjH,gBAAgB,CAClB2D,eAAe,CAAC,IAAI,CAAClD,EAAE,CAAC,CACxBiB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,EAAE;MAChB,CAAC;MACDK,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACnC,cAAc,CAACkH,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EACJjF,KAAK,EAAEA,KAAK,EAAE0E,OAAO,IAAI;SAC5B,CAAC;QACFzE,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEA6E,2BAA2BA,CAAC/E,IAAS;IACnC,OAAO;MACLoF,eAAe,EAAEpF,IAAI,EAAEqF,KAAK,IAAI,EAAE;MAClCC,mBAAmB,EAAEtF,IAAI,EAAEyC,YAAY,IAAI,EAAE;MAC7C8C,yBAAyB,EACvBvF,IAAI,EAAE2C,SAAS,EAAEO,GAAG,CAAEL,OAAY,KAAM;QACtC2C,OAAO,EAAE3C,OAAO,CAAC4C,YAAY,IAAI,IAAI;QACrCC,MAAM,EAAE7C,OAAO,CAACS,MAAM,IAAI,EAAE;QAC5BqC,WAAW,EAAE9C,OAAO,CAACM,YAAY,IAAI,EAAE;QACvCyC,0BAA0B,EACxB/C,OAAO,CAACgD,6BAA6B,IAAI,EAAE;QAC7CC,0BAA0B,EACxBjD,OAAO,CAACkD,6BAA6B,IAAI,EAAE;QAC7CC,UAAU,EAAEnD,OAAO,CAACO,WAAW,IAAI,EAAE;QACrC6C,UAAU,EAAEpD,OAAO,CAACW,WAAW,IAAI,EAAE;QACrC0C,QAAQ,EAAErD,OAAO,CAACQ,SAAS,IAAI,EAAE;QACjC8C,QAAQ,EAAE,IAAI;QACd,IAAItD,OAAO,CAACC,cAAc,EAAEvC,MAAM,GAC9B;UACE6F,eAAe,EAAEvD,OAAO,CAACC,cAAc,CAACI,GAAG,CAAEF,KAAU,KAAM;YAC3DqD,YAAY,EAAErD,KAAK,CAACC,aAAa,IAAI;WACtC,CAAC;SACH,GACD,EAAE,CAAC;QACPqD,eAAe,EACbzD,OAAO,CAACe,MAAM,EAAEV,GAAG,CAAEqD,KAAU,KAAM;UACnCC,YAAY,EAAED,KAAK,CAAC5C,aAAa,IAAI;SACtC,CAAC,CAAC,IAAI,EAAE;QACX8C,cAAc,EACZ5D,OAAO,CAACiB,aAAa,EACjBlB,MAAM,CAAE8D,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClDzD,GAAG,CAAE0D,KAAU,KAAM;UACpBC,WAAW,EAAED,KAAK,CAAC/C,YAAY,IAAI;SACpC,CAAC,CAAC,IAAI,EAAE;QACbiD,aAAa,EACXjE,OAAO,CAACmB,cAAc,EAAEd,GAAG,CAAEpC,GAAQ,KAAM;UACzCiG,UAAU,EAAEjG,GAAG,CAACiD,WAAW,IAAI;SAChC,CAAC,CAAC,IAAI,EAAE;QACXiD,YAAY,EACVnE,OAAO,CAACoE,WAAW,EAAE/D,GAAG,CAAEgE,GAAQ,KAAM;UACtCC,SAAS,EAAED,GAAG,CAACE,UAAU,IAAI;SAC9B,CAAC,CAAC,IAAI,EAAE;QACXC,oBAAoB,EAClBxE,OAAO,CAACiB,aAAa,EACjBlB,MAAM,CAAE8D,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClDzD,GAAG,CAAE0D,KAAU,KAAM;UACpBC,WAAW,EAAED,KAAK,CAAC/C,YAAY,IAAI;SACpC,CAAC,CAAC,IAAI;OACZ,CAAC,CAAC,IAAI,EAAE;MAEXyD,sBAAsB,EAAE,CACtB;QAAEC,mBAAmB,EAAE;MAAQ,CAAE,EACjC;QAAEA,mBAAmB,EAAE;MAAQ,CAAE,CAClC;MAEDC,WAAW,EAAExH,IAAI,EAAEkC,QAAQ,GACvB;QACEuF,QAAQ,EAAEzH,IAAI,CAACkC,QAAQ,CAACwF,WAAW,IAAI,EAAE;QACzCC,yBAAyB,EACvB3H,IAAI,CAACkC,QAAQ,CAAC0F,6BAA6B,IAAI,EAAE;QACnDC,oBAAoB,EAAEC,KAAK,CAACC,OAAO,CAAC/H,IAAI,CAACkC,QAAQ,CAACC,iBAAiB,CAAC,GAChE6F,MAAM,CAACC,MAAM,CACXjI,IAAI,CAACkC,QAAQ,CAACC,iBAAiB,CAAC+F,MAAM,CACpC,CAACC,GAAQ,EAAEC,SAAc,KAAI;UAC3B,MAAMC,GAAG,GAAG,GAAGD,SAAS,CAACE,kBAAkB,IAAIF,SAAS,CAACG,oBAAoB,IAAIH,SAAS,CAACI,QAAQ,EAAE;UAErG,IAAI,CAACL,GAAG,CAACE,GAAG,CAAC,EAAE;YACbF,GAAG,CAACE,GAAG,CAAC,GAAG;cACTI,iBAAiB,EAAEL,SAAS,CAACE,kBAAkB,IAAI,EAAE;cACrDI,mBAAmB,EACjBN,SAAS,CAACG,oBAAoB,IAAI,EAAE;cACtCI,QAAQ,EAAEP,SAAS,CAACI,QAAQ,IAAI,EAAE;cAClCI,kBAAkB,EAAE,IAAIC,GAAG,EAAE,CAAE;aAChC;UACH;UAEA;UACA7I,IAAI,CAACkC,QAAQ,CAACC,iBAAiB,CAC5BS,MAAM,CACJkG,OAAY,IACXA,OAAO,CAACR,kBAAkB,KACxBF,SAAS,CAACE,kBAAkB,IAC9BQ,OAAO,CAACP,oBAAoB,KAC1BH,SAAS,CAACG,oBAAoB,IAChCO,OAAO,CAACN,QAAQ,KAAKJ,SAAS,CAACI,QAAQ,CAC1C,CACAO,OAAO,CAAED,OAAY,IAAI;YACxBX,GAAG,CAACE,GAAG,CAAC,CAACO,kBAAkB,CAAC3D,GAAG,CAC7B+D,IAAI,CAACC,SAAS,CAAC;cACbC,eAAe,EAAEJ,OAAO,CAACxG,gBAAgB,IAAI,EAAE;cAC/C6G,gBAAgB,EACdL,OAAO,CAACM,kBAAkB,IAAI;aACjC,CAAC,CACH;UACH,CAAC,CAAC;UAEJ;UACApJ,IAAI,EAAEqJ,iBAAiB,EAAEN,OAAO,CAAEO,OAAY,IAAI;YAChDnB,GAAG,CAACE,GAAG,CAAC,CAACO,kBAAkB,CAAC3D,GAAG,CAC7B+D,IAAI,CAACC,SAAS,CAAC;cACbC,eAAe,EAAE,IAAI;cACrBC,gBAAgB,EAAEG,OAAO,EAAEC,YAAY,IAAI;aAC5C,CAAC,CACH;UACH,CAAC,CAAC;UAEF,OAAOpB,GAAG;QACZ,CAAC,EACD,EAAE,CACH,CACF,CAACjF,GAAG,CAAEsG,IAAS,KAAM;UACpB,GAAGA,IAAI;UACPZ,kBAAkB,EAAEd,KAAK,CAAC2B,IAAI,CAACD,IAAI,CAACZ,kBAAkB,CAAC,CAAC1F,GAAG,CACxDwG,EAAO,IAAKV,IAAI,CAACW,KAAK,CAACD,EAAE,CAAC;SAE9B,CAAC,CAAC,GACH;OACL,GACD,IAAI;MAERE,yBAAyB,EAAE9B,KAAK,CAACC,OAAO,CAAC/H,IAAI,EAAEqJ,iBAAiB,CAAC,GAC7DrJ,IAAI,CAACqJ,iBAAiB,CAACnG,GAAG,CAAEoG,OAAY,KAAM;QAC5CO,qBAAqB,EAAEP,OAAO,EAAEC,YAAY,IAAI,EAAE;QAClDO,SAAS,EAAER,OAAO,EAAES,uBAAuB,EAAEC,UAAU,IAAI,EAAE;QAC7DC,UAAU,EAAEX,OAAO,EAAES,uBAAuB,EAAEG,WAAW,IAAI,EAAE;QAC/DC,QAAQ,EAAEb,OAAO,EAAES,uBAAuB,EAAEK,SAAS,IAAI,EAAE;QAC3DC,iBAAiB,EAAEvC,KAAK,CAACC,OAAO,CAC9BuB,OAAO,EAAES,uBAAuB,EAAEO,wBAAwB,CAC3D,GACGhB,OAAO,CAACS,uBAAuB,CAACO,wBAAwB,CAACpH,GAAG,CACzDqH,IAAS,KAAM;UACd/E,OAAO,EAAE+E,IAAI,CAAC9E,YAAY,IAAI,IAAI;UAClCC,MAAM,EAAE6E,IAAI,CAACjH,MAAM,IAAI,EAAE;UACzBqC,WAAW,EAAE4E,IAAI,CAACpH,YAAY,IAAI,EAAE;UACpCyC,0BAA0B,EACxB2E,IAAI,CAAC1E,6BAA6B,IAAI,EAAE;UAC1CC,0BAA0B,EACxByE,IAAI,CAACxE,6BAA6B,IAAI,EAAE;UAC1CC,UAAU,EAAEuE,IAAI,CAACnH,WAAW,IAAI,EAAE;UAClC6C,UAAU,EAAEsE,IAAI,CAAC/G,WAAW,IAAI,EAAE;UAClC0C,QAAQ,EAAEqE,IAAI,CAAClH,SAAS,IAAI,EAAE;UAC9B8C,QAAQ,EAAE,IAAI;UACd,IAAI2B,KAAK,CAACC,OAAO,CAACwC,IAAI,CAACnE,eAAe,CAAC,IACvCmE,IAAI,CAACnE,eAAe,CAAC7F,MAAM,GAAG,CAAC,GAC3B;YACE6F,eAAe,EAAEmE,IAAI,CAACnE,eAAe,CAAClD,GAAG,CACtCF,KAAU,KAAM;cACfqD,YAAY,EAAErD,KAAK,CAACqD,YAAY,IAAI;aACrC,CAAC;WAEL,GACD,EAAE,CAAC;UACPC,eAAe,EACbiE,IAAI,CAAC3G,MAAM,EAAEV,GAAG,CAAEqD,KAAU,KAAM;YAChCC,YAAY,EAAED,KAAK,CAAC5C,aAAa,IAAI;WACtC,CAAC,CAAC,IAAI,EAAE;UACX8C,cAAc,EACZ8D,IAAI,CAACzG,aAAa,EACdlB,MAAM,CAAE8D,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClDzD,GAAG,CAAE0D,KAAU,KAAM;YACpBC,WAAW,EAAED,KAAK,CAAC/C,YAAY,IAAI;WACpC,CAAC,CAAC,IAAI,EAAE;UACbiD,aAAa,EACXyD,IAAI,CAACvG,cAAc,EAAEd,GAAG,CAAEpC,GAAQ,KAAM;YACtCiG,UAAU,EAAEjG,GAAG,CAACiD,WAAW,IAAI;WAChC,CAAC,CAAC,IAAI,EAAE;UACXiD,YAAY,EACVuD,IAAI,CAACtD,WAAW,EAAE/D,GAAG,CAAEgE,GAAQ,KAAM;YACnCC,SAAS,EAAED,GAAG,CAACE,UAAU,IAAI;WAC9B,CAAC,CAAC,IAAI,EAAE;UACXC,oBAAoB,EAClBkD,IAAI,CAACzG,aAAa,EACdlB,MAAM,CAAE8D,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClDzD,GAAG,CAAE0D,KAAU,KAAM;YACpBC,WAAW,EAAED,KAAK,CAAC/C,YAAY,IAAI;WACpC,CAAC,CAAC,IAAI;SACZ,CAAC,CACH,GACD,EAAE;QACN2G,sBAAsB,EAAE;UACtBC,uBAAuB,EACrBnB,OAAO,EAAEoB,oBAAoB,EAAEC,yBAAyB,IAAI,EAAE;UAChEC,qBAAqB,EACnBtB,OAAO,EAAEoB,oBAAoB,EAAEG,uBAAuB,IAAI;SAC7D;QACDvD,sBAAsB,EAAE,CAAC;UAAEC,mBAAmB,EAAE;QAAQ,CAAE;OAC3D,CAAC,CAAC,GACH;KACL;EACH;EAEMuD,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC7L,SAAS,GAAG,IAAI;MAErB,IAAI6L,KAAI,CAAC3L,cAAc,CAAC6L,OAAO,EAAE;QAC/B;MACF;MACAF,KAAI,CAAC5L,MAAM,GAAG,IAAI;MAClB,MAAMgF,KAAK,GAAG;QAAE,GAAG4G,KAAI,CAAC3L,cAAc,CAAC+E;MAAK,CAAE;MAE9C,MAAMnE,IAAI,GAAG;QACXV,IAAI,EAAE6E,KAAK,EAAE7E,IAAI;QACjB+F,KAAK,EAAE0F,KAAI,EAAEpM,EAAE;QACf,IAAI,CAACoM,KAAI,CAACzM,WAAW,CAACyD,UAAU,GAAG;UAAEmJ,cAAc,EAAE;QAAI,CAAE,GAAG,EAAE;OACjE;MAED,MAAMC,OAAO,GACXJ,KAAI,CAACzM,WAAW,IAAIyM,KAAI,CAACzM,WAAW,CAACyD,UAAU,GAC3CgJ,KAAI,CAAC7M,gBAAgB,CAACkN,UAAU,CAACL,KAAI,CAACzM,WAAW,CAACyD,UAAU,EAAE/B,IAAI,CAAC,CAAC;MAAA,EACpE+K,KAAI,CAAC7M,gBAAgB,CAACmN,UAAU,CAACrL,IAAI,CAAC,CAAC,CAAC;MAC9CmL,OAAO,CAACvL,IAAI,CAAC9C,SAAS,CAACiO,KAAI,CAAC5M,YAAY,CAAC,CAAC,CAAC0B,SAAS,CAAC;QACnDC,IAAI,EAAEA,CAAA,KAAK;UACTiL,KAAI,CAAChN,cAAc,CAACkH,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UAEF4F,KAAI,CAAC7M,gBAAgB,CAClB2D,eAAe,CAACkJ,KAAI,CAACpM,EAAE,CAAC,CACxBiB,IAAI,CAAC9C,SAAS,CAACiO,KAAI,CAAC5M,YAAY,CAAC,CAAC,CAClC0B,SAAS,EAAE;QAChB,CAAC;QACDK,KAAK,EAAEA,CAAA,KAAK;UACV6K,KAAI,CAAC5L,MAAM,GAAG,KAAK;UACnB4L,KAAI,CAAChN,cAAc,CAACkH,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEAX,YAAYA,CAAC8G,KAAU,EAAEC,MAAW;IAClC,MAAMvL,IAAI,GAAG;MACXnB,SAAS,EAAE0M,MAAM;MACjBlG,KAAK,EAAE,IAAI,CAAC1G;KACb;IACD,MAAMwM,OAAO,GAAGG,KAAK,GACjB,IAAI,CAACpN,gBAAgB,CAACsN,iBAAiB,CAACF,KAAK,EAAEtL,IAAI,CAAC,CAAC;IAAA,EACrD,IAAI,CAAC9B,gBAAgB,CAACuN,iBAAiB,CAACzL,IAAI,CAAC,CAAC,CAAC;IACnDmL,OAAO,CAACvL,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAAC0B,SAAS,CAAC;MACnDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/B,cAAc,CAACkH,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEFuG,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD3L,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACnC,cAAc,CAACkH,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAEA2G,QAAQA,CAAA;IACN,IAAI,CAAChO,MAAM,CAACiO,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC/M,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAgN,WAAWA,CAAA;IACT,IAAI,CAAC9N,YAAY,CAAC2B,IAAI,EAAE;IACxB,IAAI,CAAC3B,YAAY,CAAC+N,QAAQ,EAAE;EAC9B;;;uBAlgBWvO,yBAAyB,EAAAZ,EAAA,CAAAoP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtP,EAAA,CAAAoP,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvP,EAAA,CAAAoP,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAzP,EAAA,CAAAoP,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA3P,EAAA,CAAAoP,iBAAA,CAAAI,EAAA,CAAAI,mBAAA,GAAA5P,EAAA,CAAAoP,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAzBlP,yBAAyB;MAAAmP,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBtCrQ,EAAA,CAAAuQ,SAAA,iBAAuD;UAG/CvQ,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAuQ,SAAA,sBAA+F;UACnGvQ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAEyG;UADrGD,EAAA,CAAAwQ,UAAA,sBAAAC,kEAAAC,MAAA;YAAA,OAAYJ,GAAA,CAAApJ,cAAA,CAAAwJ,MAAA,CAAsB;UAAA,EAAC;UAE3C1Q,EAHI,CAAAG,YAAA,EAEyG,EACvG;UAGEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAA2Q,gBAAA,+BAAAC,0EAAAF,MAAA;YAAA1Q,EAAA,CAAA6Q,kBAAA,CAAAP,GAAA,CAAArO,WAAA,EAAAyO,MAAA,MAAAJ,GAAA,CAAArO,WAAA,GAAAyO,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAAC1Q,EAAA,CAAAwQ,UAAA,sBAAAM,iEAAAJ,MAAA;YAAA,OAAYJ,GAAA,CAAA7L,WAAA,CAAAiM,MAAA,CAAmB;UAAA,EAAC;UACzF1Q,EAAA,CAAAU,UAAA,IAAAqQ,+CAAA,wBAAoF;UAW5F/Q,EAFI,CAAAG,YAAA,EAAY,EAEV;UASsBH,EAR5B,CAAAC,cAAA,eAAqD,eACjB,eAC+D,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAAAD,EAAA,CAAAE,MAAA,IAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnCH,EAD9C,CAAAC,cAAA,cAAqD,cACP,gBACA;UAAAD,EAAA,CAAAE,MAAA,cAChC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIAH,EAA1C,CAAAC,cAAA,cAA0C,gBACA;UAAAD,EAAA,CAAAE,MAAA,sBAC5B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACUH,EAA1C,CAAAC,cAAA,cAA0C,gBACA;UAAAD,EAAA,CAAAE,MAAA,oBAC3B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAMvB;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9EH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7CH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAIhFF,EAJgF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,eAC6C,cAC9B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAC,cAAA,eAAoC;UAChCD,EAAA,CAAAuQ,SAAA,oBAEqD;UACrDvQ,EAAA,CAAAC,cAAA,kBACkE;UADpCD,EAAA,CAAAwQ,UAAA,mBAAAQ,4DAAA;YAAA,OAASV,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAMtE/N,EALkF,CAAAG,YAAA,EAAS,EACzE,EACJ,EACH,EAEL;UAEFH,EADJ,CAAAC,cAAA,eAAiD,oBAIyB;UAAlED,EAAA,CAAAwQ,UAAA,mBAAAS,8DAAA;YAAA,OAASX,GAAA,CAAArB,aAAA,EAAe;UAAA,EAAC;UAH7BjP,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAuQ,SAAA,qBAA+B;UASnDvQ,EARgB,CAAAG,YAAA,EAAM,EAIJ,EACJ,EAEJ,EACJ;UACNH,EAAA,CAAAuQ,SAAA,uBAAmC;;;UA9HJvQ,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAkQ,GAAA,CAAA3O,eAAA,CAAyB,SAAA2O,GAAA,CAAAjN,IAAA,CAAc,uCAAuC;UAEpFrD,EAAA,CAAAO,SAAA,EAAmB;UAE3BP,EAFQ,CAAAI,UAAA,YAAAkQ,GAAA,CAAAtO,OAAA,CAAmB,mGAEuE;UAIvFhC,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAkR,gBAAA,gBAAAZ,GAAA,CAAArO,WAAA,CAA6B;UAC5BjC,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAkQ,GAAA,CAAA7O,KAAA,CAAU;UAcczB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAmR,WAAA,iBAAAb,GAAA,CAAApO,eAAA,CAAsC;UASlClC,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAjP,eAAA,kBAAAiP,GAAA,CAAAjP,eAAA,CAAAqE,YAAA,SAAwC;UAItD1F,EAAA,CAAAO,SAAA,GACmB;UADnBP,EAAA,CAAAQ,kBAAA,SAAA8P,GAAA,CAAAjP,eAAA,kBAAAiP,GAAA,CAAAjP,eAAA,CAAAiH,KAAA,aACmB;UAMftI,EAAA,CAAAO,SAAA,GACK;UADLP,EAAA,CAAAQ,kBAAA,QAAA8P,GAAA,CAAAzO,YAAA,YACK;UAGJ7B,EAAA,CAAAO,SAAA,GAMvB;UANuBP,EAAA,CAAAQ,kBAAA,UAAA8P,GAAA,CAAAjP,eAAA,kBAAAiP,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,kBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,qBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,kBAAAsD,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAC,UAAA,oBAAAqD,GAAA,CAAAjP,eAAA,kBAAAiP,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,kBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,qBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,kBAAAsD,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAK,SAAA,eAMvB;UAUiBrN,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAhP,cAAA,kBAAAgP,GAAA,CAAAhP,cAAA,qBAAAgP,GAAA,CAAAhP,cAAA,IAAAwE,OAAA,SAAuC;UAKvC9F,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAhP,cAAA,kBAAAgP,GAAA,CAAAhP,cAAA,qBAAAgP,GAAA,CAAAhP,cAAA,IAAAwF,YAAA,SAA4C;UAM9C9G,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAjP,eAAA,kBAAAiP,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,kBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,qBAAAgE,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,kBAAAsD,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,kBAAA+C,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,qBAAA+C,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAAxG,aAAA,kBAAAuJ,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAAxG,aAAA,qBAAAuJ,GAAA,CAAAjP,eAAA,CAAAiL,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAAxG,aAAA,IAAAD,YAAA,SACP;UAMS9G,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAhP,cAAA,kBAAAgP,GAAA,CAAAhP,cAAA,qBAAAgP,GAAA,CAAAhP,cAAA,IAAAsF,aAAA,SAA6C;UAK7C5G,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAoR,iBAAA,EAAAd,GAAA,CAAAhP,cAAA,kBAAAgP,GAAA,CAAAhP,cAAA,qBAAAgP,GAAA,CAAAhP,cAAA,IAAA0F,WAAA,SAA2C;UAK1EhH,EAAA,CAAAO,SAAA,EAA4B;UAA5BP,EAAA,CAAAI,UAAA,cAAAkQ,GAAA,CAAAjO,cAAA,CAA4B;UAkBJrC,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAmR,WAAA,gBAAAb,GAAA,CAAApO,eAAA,CAAqC;UAF/DlC,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}