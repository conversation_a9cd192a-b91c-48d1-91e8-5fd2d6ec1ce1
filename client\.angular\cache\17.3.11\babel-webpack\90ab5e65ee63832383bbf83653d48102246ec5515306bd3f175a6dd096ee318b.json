{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/table\";\nimport * as i4 from \"primeng/button\";\nfunction ContactsOpportunitiesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 8);\n    i0.ɵɵtext(2, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Close Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Sales Phase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Owner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 9);\n    i0.ɵɵtext(12, \"Progress\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsOpportunitiesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 9);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/sales-quotes/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.CloseDate, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.SalesPhase, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Owner, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Progress, \" \");\n  }\n}\nexport let ContactsOpportunitiesComponent = /*#__PURE__*/(() => {\n  class ContactsOpportunitiesComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.tableData = [{\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }, {\n        Name: 'Test Post Won Automation for New Store',\n        CloseDate: '04/10/2024',\n        SalesPhase: 'Leads Converted',\n        Owner: 'Kirsten Scott',\n        Status: 'Active',\n        Progress: 'Not Relevant'\n      }];\n    }\n    static {\n      this.ɵfac = function ContactsOpportunitiesComponent_Factory(t) {\n        return new (t || ContactsOpportunitiesComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContactsOpportunitiesComponent,\n        selectors: [[\"app-contacts-opportunities\"]],\n        decls: 9,\n        vars: 5,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\", 3, \"routerLink\"]],\n        template: function ContactsOpportunitiesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Opportunities\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"p-button\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n            i0.ɵɵtemplate(7, ContactsOpportunitiesComponent_ng_template_7_Template, 13, 0, \"ng-template\", 6)(8, ContactsOpportunitiesComponent_ng_template_8_Template, 13, 7, \"ng-template\", 7);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.RouterLink, i2.PrimeTemplate, i3.Table, i4.Button]\n      });\n    }\n  }\n  return ContactsOpportunitiesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}