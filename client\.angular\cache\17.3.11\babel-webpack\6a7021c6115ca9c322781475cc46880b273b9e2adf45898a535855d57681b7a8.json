{"ast": null, "code": "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n  if (typeof origSymbol !== 'function') {\n    return false;\n  }\n  if (typeof Symbol !== 'function') {\n    return false;\n  }\n  if (typeof origSymbol('foo') !== 'symbol') {\n    return false;\n  }\n  if (typeof Symbol('bar') !== 'symbol') {\n    return false;\n  }\n  return hasSymbolSham();\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}