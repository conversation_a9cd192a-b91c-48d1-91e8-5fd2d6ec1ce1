{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ServiceTicketsRoutingModule } from './service-tickets-routing.module';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountSharedModule } from '../account/account-shared.module';\nimport * as i0 from \"@angular/core\";\nexport class ServiceTicketsModule {\n  static {\n    this.ɵfac = function ServiceTicketsModule_Factory(t) {\n      return new (t || ServiceTicketsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, AccountSharedModule, ReactiveFormsModule, NgSelectModule, ServiceTicketsRoutingModule, BreadcrumbModule, ConfirmDialogModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, ToastModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsModule, {\n    declarations: [ServiceTicketsComponent],\n    imports: [CommonModule, AccountSharedModule, ReactiveFormsModule, NgSelectModule, ServiceTicketsRoutingModule, BreadcrumbModule, ConfirmDialogModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, ToastModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ServiceTicketsRoutingModule", "ServiceTicketsComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "ConfirmDialogModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "MessageService", "ConfirmationService", "ToastModule", "NgSelectModule", "AccountSharedModule", "ServiceTicketsModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ServiceTicketsRoutingModule } from './service-tickets-routing.module';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\n\r\nimport { AccountSharedModule } from '../account/account-shared.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ServiceTicketsComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AccountSharedModule,\r\n    ReactiveFormsModule,\r\n    NgSelectModule,\r\n    ServiceTicketsRoutingModule,\r\n    BreadcrumbModule,\r\n    ConfirmDialogModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    ToastModule,\r\n    InputTextModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService]\r\n})\r\nexport class ServiceTicketsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AAErD,SAASC,mBAAmB,QAAQ,kCAAkC;;AA0BtE,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAFpB,CAACL,cAAc,EAAEC,mBAAmB,CAAC;MAAAK,OAAA,GAjB9CpB,YAAY,EACZkB,mBAAmB,EACnBd,mBAAmB,EACnBa,cAAc,EACdhB,2BAA2B,EAC3BK,gBAAgB,EAChBG,mBAAmB,EACnBC,cAAc,EACdE,WAAW,EACXT,WAAW,EACXK,cAAc,EACdD,YAAY,EACZM,aAAa,EACbR,kBAAkB,EAClBW,WAAW,EACXL,eAAe;IAAA;EAAA;;;2EAINQ,oBAAoB;IAAAE,YAAA,GAtB7BnB,uBAAuB;IAAAkB,OAAA,GAGvBpB,YAAY,EACZkB,mBAAmB,EACnBd,mBAAmB,EACnBa,cAAc,EACdhB,2BAA2B,EAC3BK,gBAAgB,EAChBG,mBAAmB,EACnBC,cAAc,EACdE,WAAW,EACXT,WAAW,EACXK,cAAc,EACdD,YAAY,EACZM,aAAa,EACbR,kBAAkB,EAClBW,WAAW,EACXL,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}