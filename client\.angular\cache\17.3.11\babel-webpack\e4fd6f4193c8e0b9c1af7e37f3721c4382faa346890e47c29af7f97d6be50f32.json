{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nfunction EmailsAttachmentsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10);\n    i0.ɵɵtext(2, \"File Icon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Changed On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Changed By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 11);\n    i0.ɵɵtext(12, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EmailsAttachmentsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 10)(2, \"i\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 11)(13, \"button\", 13)(14, \"i\", 14);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const attachment_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(attachment_r1.FileIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (attachment_r1 == null ? null : attachment_r1.Title) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (attachment_r1 == null ? null : attachment_r1.Type) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (attachment_r1 == null ? null : attachment_r1.ChangedOn) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (attachment_r1 == null ? null : attachment_r1.ChangedBy) || \"-\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(attachment_r1.Action);\n  }\n}\nfunction EmailsAttachmentsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2, \"No attachments found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EmailsAttachmentsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2, \"Loading attachments data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class EmailsAttachmentsComponent {\n  constructor(activitiesservice) {\n    this.activitiesservice = activitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.attachmentdetails = null;\n    this.bp_id = '';\n  }\n  ngOnInit() {\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.attachmentdetails = response?.contact_activity;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function EmailsAttachmentsComponent_Factory(t) {\n      return new (t || EmailsAttachmentsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EmailsAttachmentsComponent,\n      selectors: [[\"app-emails-attachments\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"material-symbols-rounded\", \"text-primary\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"], [\"colspan\", \"6\"]],\n      template: function EmailsAttachmentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Attachments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, EmailsAttachmentsComponent_ng_template_7_Template, 13, 0, \"ng-template\", 6)(8, EmailsAttachmentsComponent_ng_template_8_Template, 16, 6, \"ng-template\", 7)(9, EmailsAttachmentsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, EmailsAttachmentsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.attachmentdetails)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i2.Table, i3.PrimeTemplate, i4.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "attachment_r1", "FileIcon", "ɵɵtextInterpolate1", "Title", "Type", "ChangedOn", "ChangedBy", "Action", "EmailsAttachmentsComponent", "constructor", "activitiesservice", "unsubscribe$", "attachmentdetails", "bp_id", "ngOnInit", "activity", "pipe", "subscribe", "response", "contact_activity", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "selectors", "decls", "vars", "consts", "template", "EmailsAttachmentsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "EmailsAttachmentsComponent_ng_template_7_Template", "EmailsAttachmentsComponent_ng_template_8_Template", "EmailsAttachmentsComponent_ng_template_9_Template", "EmailsAttachmentsComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\emails\\emails-details\\emails-attachments\\emails-attachments.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\emails\\emails-details\\emails-attachments\\emails-attachments.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\n\r\n@Component({\r\n  selector: 'app-emails-attachments',\r\n  templateUrl: './emails-attachments.component.html',\r\n  styleUrl: './emails-attachments.component.scss',\r\n})\r\nexport class EmailsAttachmentsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public attachmentdetails: any = null;\r\n  public bp_id: string = '';\r\n\r\n  constructor(private activitiesservice: ActivitiesService) {}\r\n\r\n  ngOnInit() {\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.attachmentdetails = response?.contact_activity;\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Attachments</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\"\r\n            [styleClass]=\"'w-5rem font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"attachmentdetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\">File Icon</th>\r\n                    <th>Title</th>\r\n                    <th>Type</th>\r\n                    <th>Changed On</th>\r\n                    <th>Changed By</th>\r\n                    <th class=\"border-round-right-lg text-center\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-attachment>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        <i class=\"material-symbols-rounded text-primary\">{{ attachment.FileIcon }}</i>\r\n                    </td>\r\n                    <td>\r\n                        {{ attachment?.Title || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ attachment?.Type || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ attachment?.ChangedOn || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ attachment?.ChangedBy || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\">\r\n                            <i class=\"material-symbols-rounded text-red-500\">{{ attachment.Action }}</i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">No attachments found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\">Loading attachments data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;ICYrBC,EADJ,CAAAC,cAAA,SAAI,aACiC;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAE,MAAA,eAAO;IACzDF,EADyD,CAAAG,YAAA,EAAK,EACzD;;;;;IAMGH,EAFR,CAAAC,cAAA,SAAI,aACiC,YACoB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAC9EF,EAD8E,CAAAG,YAAA,EAAI,EAC7E;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIGH,EAHR,CAAAC,cAAA,cAA8C,kBAEwD,aAC7C;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAGpFF,EAHoF,CAAAG,YAAA,EAAI,EACvE,EACR,EACJ;;;;IApBoDH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAAC,QAAA,CAAyB;IAG1EP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAQ,kBAAA,OAAAF,aAAA,kBAAAA,aAAA,CAAAG,KAAA,cACJ;IAEIT,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAQ,kBAAA,OAAAF,aAAA,kBAAAA,aAAA,CAAAI,IAAA,cACJ;IAEIV,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAQ,kBAAA,OAAAF,aAAA,kBAAAA,aAAA,CAAAK,SAAA,cACJ;IAEIX,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAQ,kBAAA,OAAAF,aAAA,kBAAAA,aAAA,CAAAM,SAAA,cACJ;IAIyDZ,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAAO,MAAA,CAAuB;;;;;IAOhFb,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IACzCF,EADyC,CAAAG,YAAA,EAAK,EACzC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAC1DF,EAD0D,CAAAG,YAAA,EAAK,EAC1D;;;AD9CrB,OAAM,MAAOW,0BAA0B;EAKrCC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAJ7B,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;IACnC,KAAAoB,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,KAAK,GAAW,EAAE;EAEkC;EAE3DC,QAAQA,CAAA;IACN,IAAI,CAACJ,iBAAiB,CAACK,QAAQ,CAC5BC,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACkB,YAAY,CAAC,CAAC,CAClCM,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACL,KAAK,GAAGK,QAAQ,EAAEL,KAAK;QAC5B,IAAI,CAACD,iBAAiB,GAAGM,QAAQ,EAAEC,gBAAgB;MACrD;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,IAAI,EAAE;IACxB,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;EAC9B;;;uBArBWd,0BAA0B,EAAAd,EAAA,CAAA6B,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAA1BjB,0BAA0B;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP/BtC,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAwC,SAAA,kBAC4C;UAChDxC,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UA2C1BD,EAzCA,CAAAyC,UAAA,IAAAC,iDAAA,0BAAgC,IAAAC,iDAAA,0BAWa,IAAAC,iDAAA,yBAyBP,KAAAC,kDAAA,yBAKD;UAOjD7C,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAxDiEH,EAAA,CAAAI,SAAA,GAAiB;UAC5EJ,EAD2D,CAAA8C,UAAA,kBAAiB,sCACvC;UAIhC9C,EAAA,CAAAI,SAAA,GAA2B;UAAuCJ,EAAlE,CAAA8C,UAAA,UAAAP,GAAA,CAAArB,iBAAA,CAA2B,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}