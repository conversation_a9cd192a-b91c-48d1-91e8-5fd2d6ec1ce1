{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./contacts.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = () => [\"/auth/signup\"];\nconst _c1 = a0 => ({\n  \"text-orange-600 cursor-pointer font-medium\": true,\n  \"underline\": a0\n});\nfunction ContactsComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 19);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 20)(4, \"div\", 21);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵelementStart(6, \"div\", 22);\n    i0.ɵɵelement(7, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"th\", 24)(9, \"div\", 21);\n    i0.ɵɵtext(10, \" Account \");\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵelement(12, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"th\", 24)(14, \"div\", 21);\n    i0.ɵɵtext(15, \" Function \");\n    i0.ɵɵelementStart(16, \"div\", 22);\n    i0.ɵɵelement(17, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"th\", 24)(19, \"div\", 21);\n    i0.ɵɵtext(20, \" Department \");\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵelement(22, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"th\", 26)(24, \"div\", 21);\n    i0.ɵɵtext(25, \" Phone \");\n    i0.ɵɵelementStart(26, \"div\", 22);\n    i0.ɵɵelement(27, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"th\", 28)(29, \"div\", 21);\n    i0.ɵɵtext(30, \" Email \");\n    i0.ɵɵelementStart(31, \"div\", 22);\n    i0.ɵɵelement(32, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"th\", 30)(34, \"div\", 21);\n    i0.ɵɵtext(35, \" Status \");\n    i0.ɵɵelementStart(36, \"div\", 22);\n    i0.ɵɵelement(37, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ContactsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 31)(1, \"td\", 19);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 34);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/store/contacts/\" + contact_r3.documentId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, contact_r3 == null ? null : contact_r3.bp_full_name));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, contact_r3 == null ? null : contact_r3.bp_id));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.Function) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.Department) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.phone) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.email) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r3 == null ? null : contact_r3.status) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"Loading contacts data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ContactsComponent {\n  constructor(contactsservice) {\n    this.contactsservice = contactsservice;\n    this.contacts = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n  }\n  loadContacts(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.contactsservice.getContacts(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.contacts = response?.data || [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching contacts', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadContacts({\n      first: 0,\n      rows: 10\n    });\n  }\n  static {\n    this.ɵfac = function ContactsComponent_Factory(t) {\n      return new (t || ContactsComponent)(i0.ɵɵdirectiveInject(i1.ContactsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsComponent,\n      selectors: [[\"app-contacts\"]],\n      decls: 21,\n      vars: 12,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Contact\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"bp_full_name\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"bp_full_name\"], [\"pSortableColumn\", \"bp_id\"], [\"field\", \"bp_id\"], [\"pSortableColumn\", \"phone\"], [\"field\", \"phone\"], [\"pSortableColumn\", \"email\"], [\"field\", \"email\"], [\"pSortableColumn\", \"bp_id\", 1, \"border-round-right-lg\"], [1, \"cursor-pointer\", 3, \"routerLink\"], [3, \"value\"], [3, \"ngClass\"], [1, \"border-round-right-lg\"], [\"colspan\", \"8\"]],\n      template: function ContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function ContactsComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(16);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"button\", 11)(11, \"span\", 12);\n          i0.ɵɵtext(12, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"p-table\", 14, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function ContactsComponent_Template_p_table_onLazyLoad_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadContacts($event));\n          });\n          i0.ɵɵtemplate(17, ContactsComponent_ng_template_17_Template, 38, 0, \"ng-template\", 15)(18, ContactsComponent_ng_template_18_Template, 17, 15, \"ng-template\", 16)(19, ContactsComponent_ng_template_19_Template, 3, 0, \"ng-template\", 17)(20, ContactsComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.contacts)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i3.RouterLink, i4.Breadcrumb, i5.PrimeTemplate, i6.Table, i6.SortableColumn, i6.SortIcon, i6.TableCheckbox, i6.TableHeaderCheckbox, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "contact_r3", "documentId", "ɵɵadvance", "ɵɵpureFunction1", "_c1", "bp_full_name", "ɵɵtextInterpolate1", "bp_id", "Function", "Department", "phone", "email", "status", "ContactsComponent", "constructor", "contactsservice", "contacts", "totalRecords", "loading", "globalSearchTerm", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "loadContacts", "event", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getContacts", "subscribe", "next", "response", "data", "meta", "pagination", "total", "error", "console", "onGlobalFilter", "table", "ɵɵdirectiveInject", "i1", "ContactsService", "selectors", "decls", "vars", "consts", "template", "ContactsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ContactsComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "ContactsComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "ContactsComponent_Template_p_table_onLazyLoad_15_listener", "ɵɵtemplate", "ContactsComponent_ng_template_17_Template", "ContactsComponent_ng_template_18_Template", "ContactsComponent_ng_template_19_Template", "ContactsComponent_ng_template_20_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Table } from 'primeng/table';\r\nimport { ContactsService } from './contacts.service';\r\n\r\n@Component({\r\n  selector: 'app-contacts',\r\n  templateUrl: './contacts.component.html',\r\n  styleUrl: './contacts.component.scss',\r\n})\r\nexport class ContactsComponent {\r\n  breadcrumbitems: MenuItem[] | any;\r\n  home: MenuItem | any;\r\n  public contacts: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n\r\n  constructor(private contactsservice: ContactsService) {}\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n  }\r\n\r\n  loadContacts(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.contactsservice\r\n      .getContacts(page, pageSize, sortField, sortOrder, this.globalSearchTerm)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.contacts = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching contacts', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadContacts({ first: 0, rows: 10 });\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" placeholder=\"Search Contact\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                        (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\"></i>\r\n                </span>\r\n            </div>\r\n            <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n                class=\"h-3rem p-element p-ripple p-button-outlined p-button p-component w-8rem justify-content-center gap-2 font-semibold\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"contacts\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadContacts($event)\"\r\n            [loading]=\"loading\" styleClass=\"\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_full_name\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Name\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"bp_full_name\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Account\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Function\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Department\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"phone\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Phone\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"phone\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"email\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Email\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"email\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\" class=\"border-round-right-lg\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            Status\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact>\r\n                <tr class=\"cursor-pointer\" [routerLink]=\"'/store/contacts/' + contact.documentId\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true, \r\n                        'underline': contact?.bp_full_name\r\n                      }\">\r\n                        {{ contact?.bp_full_name || '-' }}\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true, \r\n                        'underline': contact?.bp_id\r\n                      }\">\r\n                        {{ contact?.bp_id || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.Function || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.Department || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.phone || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.email || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        {{ contact?.status || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No contacts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading contacts data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;IC4BoBA,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAmC,cAC8B;IACzDD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAC,cAAA,cAAqC;IACjCD,EAAA,CAAAE,SAAA,qBAA8C;IAG1DF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,aAA4B,cACqC;IACzDD,EAAA,CAAAI,MAAA,iBACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA4B,eACqC;IACzDD,EAAA,CAAAI,MAAA,kBACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA4B,eACqC;IACzDD,EAAA,CAAAI,MAAA,oBACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA4B,eACqC;IACzDD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA4B,eACqC;IACzDD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA0D,eACO;IACzDD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAE,SAAA,sBAAuC;IAIvDF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACL,EACJ;;;;;IAKDH,EADJ,CAAAC,cAAA,aAAkF,aACrB;IACrDD,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGK;IACDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAGK;IACDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA/BsBH,EAAA,CAAAK,UAAA,oCAAAC,UAAA,CAAAC,UAAA,CAAsD;IAExDP,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAK,UAAA,UAAAC,UAAA,CAAiB;IAElCN,EAAA,CAAAQ,SAAA,EAGA;IAHAR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAJ,UAAA,kBAAAA,UAAA,CAAAK,YAAA,EAGA;IACAX,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAK,YAAA,cACJ;IACIX,EAAA,CAAAQ,SAAA,EAGA;IAHAR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAJ,UAAA,kBAAAA,UAAA,CAAAO,KAAA,EAGA;IACAb,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAO,KAAA,cACJ;IAEIb,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAQ,QAAA,cACJ;IAEId,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAS,UAAA,cACJ;IAEIf,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAU,KAAA,cACJ;IAEIhB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAW,KAAA,cACJ;IAEIjB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAY,kBAAA,OAAAN,UAAA,kBAAAA,UAAA,CAAAY,MAAA,cACJ;;;;;IAKAlB,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACtCJ,EADsC,CAAAG,YAAA,EAAK,EACtC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IACvDJ,EADuD,CAAAG,YAAA,EAAK,EACvD;;;AD1HrB,OAAM,MAAOgB,iBAAiB;EAQ5BC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAL5B,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;EAEmB;EAEvDC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,CACvD;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;EACvD;EAEAG,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,MAAMU,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,MAAME,SAAS,GAAGL,KAAK,CAACK,SAAS;IACjC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAEjC,IAAI,CAAClB,eAAe,CACjBmB,WAAW,CAACN,IAAI,EAAEG,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAE,IAAI,CAACd,gBAAgB,CAAC,CACxEgB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrB,QAAQ,GAAGqB,QAAQ,EAAEC,IAAI,IAAI,EAAE;QACpC,IAAI,CAACrB,YAAY,GAAGoB,QAAQ,EAAEE,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDwB,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACxB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA0B,cAAcA,CAACC,KAAY,EAAElB,KAAY;IACvC,IAAI,CAACD,YAAY,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC3C;;;uBA1CWjB,iBAAiB,EAAAnB,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAjBnC,iBAAiB;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCRtB7D,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAG4E;UAFjDD,EAAA,CAAA+D,gBAAA,2BAAAC,0DAAAC,MAAA;YAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAAnE,EAAA,CAAAoE,kBAAA,CAAAN,GAAA,CAAArC,gBAAA,EAAAwC,MAAA,MAAAH,GAAA,CAAArC,gBAAA,GAAAwC,MAAA;YAAA,OAAAjE,EAAA,CAAAqE,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UAClFjE,EAAA,CAAAsE,UAAA,mBAAAC,kDAAAN,MAAA;YAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAA,MAAAK,MAAA,GAAAxE,EAAA,CAAAyE,WAAA;YAAA,OAAAzE,EAAA,CAAAqE,WAAA,CAASP,GAAA,CAAAZ,cAAA,CAAAsB,MAAA,EAAAP,MAAA,CAA2B;UAAA,EAAC;UADzCjE,EAAA,CAAAG,YAAA,EAEyG;UACzGH,EAAA,CAAAE,SAAA,YAA4B;UAEpCF,EADI,CAAAG,YAAA,EAAO,EACL;UAGFH,EAFJ,CAAAC,cAAA,kBAC+H,gBAC3E;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAGW;UAF4BD,EAAA,CAAAsE,UAAA,wBAAAI,0DAAAT,MAAA;YAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAA,OAAAnE,EAAA,CAAAqE,WAAA,CAAcP,GAAA,CAAA9B,YAAA,CAAAiC,MAAA,CAAoB;UAAA,EAAC;UA0GzFjE,EAvGA,CAAA2E,UAAA,KAAAC,yCAAA,2BAAgC,KAAAC,yCAAA,4BAgEU,KAAAC,yCAAA,0BAkCJ,KAAAC,yCAAA,0BAKD;UAOjD/E,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UArIoBH,EAAA,CAAAQ,SAAA,GAAyB;UAAeR,EAAxC,CAAAK,UAAA,UAAAyD,GAAA,CAAAnC,eAAA,CAAyB,SAAAmC,GAAA,CAAAhC,IAAA,CAAc,uCAAuC;UAM5B9B,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAgF,gBAAA,YAAAlB,GAAA,CAAArC,gBAAA,CAA8B;UAMxEzB,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAiF,eAAA,KAAAC,GAAA,EAA+B;UAQ3ClF,EAAA,CAAAQ,SAAA,GAAkB;UACuDR,EADzE,CAAAK,UAAA,UAAAyD,GAAA,CAAAxC,QAAA,CAAkB,YAAyB,YAAAwC,GAAA,CAAAtC,OAAA,CAClC,mBAAiC,iBAAAsC,GAAA,CAAAvC,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}