{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * Carousel is a content slider featuring various customization options.\n * @group Components\n */\nconst _c0 = [\"itemsContainer\"];\nconst _c1 = [\"indicatorContent\"];\nconst _c2 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c3 = [\"p-header\", \"p-footer\"];\nconst _c4 = (a0, a1) => ({\n  \"p-carousel p-component\": true,\n  \"p-carousel-vertical\": a0,\n  \"p-carousel-horizontal\": a1\n});\nconst _c5 = a0 => ({\n  height: a0\n});\nconst _c6 = a0 => ({\n  \"p-carousel-prev p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c7 = (a0, a1, a2) => ({\n  \"p-carousel-item p-carousel-item-cloned\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c8 = a0 => ({\n  $implicit: a0\n});\nconst _c9 = (a0, a1, a2) => ({\n  \"p-carousel-item\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c10 = a0 => ({\n  \"p-carousel-next p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c11 = a0 => ({\n  \"p-carousel-indicator\": true,\n  \"p-highlight\": a0\n});\nfunction Carousel_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Carousel_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n  }\n}\nfunction Carousel_button_4_ng_container_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_4_ng_container_1_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_button_4_ng_container_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 17)(2, Carousel_button_4_ng_container_1_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_button_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_button_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_button_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_button_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtemplate(1, Carousel_button_4_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Carousel_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function Carousel_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_button_4_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Carousel_button_4_span_2_Template, 2, 1, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c6, ctx_r1.isBackwardNavDisabled()))(\"disabled\", ctx_r1.isBackwardNavDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPrevButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Carousel_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c7, ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length, 0 === index_r5, ctx_r1.clonedItemsForStarting.length - 1 === index_r5));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r5))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c8, item_r4));\n  }\n}\nfunction Carousel_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_9_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c9, ctx_r1.firstIndex() <= index_r7 && ctx_r1.lastIndex() >= index_r7, ctx_r1.firstIndex() === index_r7, ctx_r1.lastIndex() === index_r7));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r7))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c8, item_r6));\n  }\n}\nfunction Carousel_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_10_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const index_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c7, ctx_r1.totalShiftedItems * -1 === ctx_r1.numVisible, 0 === index_r9, ctx_r1.clonedItemsForFinishing.length - 1 === index_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c8, item_r8));\n  }\n}\nfunction Carousel_button_11_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_11_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_button_11_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 17)(2, Carousel_button_11_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_button_11_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_button_11_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_button_11_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_button_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtemplate(1, Carousel_button_11_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nextIconTemplate);\n  }\n}\nfunction Carousel_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function Carousel_button_11_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_button_11_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Carousel_button_11_span_2_Template, 2, 1, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c10, ctx_r1.isForwardNavDisabled()))(\"disabled\", ctx_r1.isForwardNavDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaNextButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.nextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nextIconTemplate);\n  }\n}\nfunction Carousel_ul_12_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function Carousel_ul_12_li_2_Template_button_click_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDotClick($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c11, ctx_r1._page === i_r13));\n    i0.ɵɵattribute(\"data-pc-section\", \"indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.indicatorStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-link\")(\"ngStyle\", ctx_r1.indicatorStyle)(\"tabindex\", ctx_r1._page === i_r13 ? 0 : -1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(i_r13 + 1))(\"aria-current\", ctx_r1._page === i_r13 ? \"page\" : undefined);\n  }\n}\nfunction Carousel_ul_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 21, 1);\n    i0.ɵɵlistener(\"keydown\", function Carousel_ul_12_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Carousel_ul_12_li_2_Template, 2, 11, \"li\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.indicatorsContentClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-carousel-indicators p-reset\")(\"ngStyle\", ctx_r1.indicatorsContentStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.totalDotsArray());\n  }\n}\nfunction Carousel_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Carousel_div_13_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nlet Carousel = /*#__PURE__*/(() => {\n  class Carousel {\n    el;\n    zone;\n    cd;\n    renderer;\n    document;\n    platformId;\n    config;\n    /**\n     * Index of the first item.\n     * @defaultValue 0\n     * @group Props\n     */\n    get page() {\n      return this._page;\n    }\n    set page(val) {\n      if (this.isCreated && val !== this._page) {\n        if (this.autoplayInterval) {\n          this.stopAutoplay();\n        }\n        if (val > this._page && val <= this.totalDots() - 1) {\n          this.step(-1, val);\n        } else if (val < this._page) {\n          this.step(1, val);\n        }\n      }\n      this._page = val;\n    }\n    /**\n     * Number of items per page.\n     * @defaultValue 1\n     * @group Props\n     */\n    get numVisible() {\n      return this._numVisible;\n    }\n    set numVisible(val) {\n      this._numVisible = val;\n    }\n    /**\n     * Number of items to scroll.\n     * @defaultValue 1\n     * @group Props\n     */\n    get numScroll() {\n      return this._numVisible;\n    }\n    set numScroll(val) {\n      this._numScroll = val;\n    }\n    /**\n     * An array of options for responsive design.\n     * @see {CarouselResponsiveOptions}\n     * @group Props\n     */\n    responsiveOptions;\n    /**\n     * Specifies the layout of the component.\n     * @group Props\n     */\n    orientation = 'horizontal';\n    /**\n     * Height of the viewport in vertical layout.\n     * @group Props\n     */\n    verticalViewPortHeight = '300px';\n    /**\n     * Style class of main content.\n     * @group Props\n     */\n    contentClass = '';\n    /**\n     * Style class of the indicator items.\n     * @group Props\n     */\n    indicatorsContentClass = '';\n    /**\n     * Inline style of the indicator items.\n     * @group Props\n     */\n    indicatorsContentStyle;\n    /**\n     * Style class of the indicators.\n     * @group Props\n     */\n    indicatorStyleClass = '';\n    /**\n     * Style of the indicators.\n     * @group Props\n     */\n    indicatorStyle;\n    /**\n     * An array of objects to display.\n     * @defaultValue null\n     * @group Props\n     */\n    get value() {\n      return this._value;\n    }\n    set value(val) {\n      this._value = val;\n    }\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    circular = false;\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    showIndicators = true;\n    /**\n     * Whether to display navigation buttons in container.\n     * @group Props\n     */\n    showNavigators = true;\n    /**\n     * Time in milliseconds to scroll items automatically.\n     * @group Props\n     */\n    autoplayInterval = 0;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the viewport container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Callback to invoke after scroll.\n     * @param {CarouselPageEvent} event - Custom page event.\n     * @group Emits\n     */\n    onPage = new EventEmitter();\n    itemsContainer;\n    indicatorContent;\n    headerFacet;\n    footerFacet;\n    templates;\n    _numVisible = 1;\n    _numScroll = 1;\n    _oldNumScroll = 0;\n    prevState = {\n      numScroll: 0,\n      numVisible: 0,\n      value: []\n    };\n    defaultNumScroll = 1;\n    defaultNumVisible = 1;\n    _page = 0;\n    _value;\n    carouselStyle;\n    id;\n    totalShiftedItems;\n    isRemainingItemsAdded = false;\n    animationTimeout;\n    translateTimeout;\n    remainingItems = 0;\n    _items;\n    startPos;\n    documentResizeListener;\n    clonedItemsForStarting;\n    clonedItemsForFinishing;\n    allowAutoplay;\n    interval;\n    isCreated;\n    swipeThreshold = 20;\n    itemTemplate;\n    headerTemplate;\n    footerTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    window;\n    constructor(el, zone, cd, renderer, document, platformId, config) {\n      this.el = el;\n      this.zone = zone;\n      this.cd = cd;\n      this.renderer = renderer;\n      this.document = document;\n      this.platformId = platformId;\n      this.config = config;\n      this.totalShiftedItems = this.page * this.numScroll * -1;\n      this.window = this.document.defaultView;\n    }\n    ngOnChanges(simpleChange) {\n      if (isPlatformBrowser(this.platformId)) {\n        if (simpleChange.value) {\n          if (this.circular && this._value) {\n            this.setCloneItems();\n          }\n        }\n        if (this.isCreated) {\n          if (simpleChange.numVisible) {\n            if (this.responsiveOptions) {\n              this.defaultNumVisible = this.numVisible;\n            }\n            if (this.isCircular()) {\n              this.setCloneItems();\n            }\n            this.createStyle();\n            this.calculatePosition();\n          }\n          if (simpleChange.numScroll) {\n            if (this.responsiveOptions) {\n              this.defaultNumScroll = this.numScroll;\n            }\n          }\n        }\n      }\n      this.cd.markForCheck();\n    }\n    ngAfterContentInit() {\n      this.id = UniqueComponentId();\n      if (isPlatformBrowser(this.platformId)) {\n        this.allowAutoplay = !!this.autoplayInterval;\n        if (this.circular) {\n          this.setCloneItems();\n        }\n        if (this.responsiveOptions) {\n          this.defaultNumScroll = this._numScroll;\n          this.defaultNumVisible = this._numVisible;\n        }\n        this.createStyle();\n        this.calculatePosition();\n        if (this.responsiveOptions) {\n          this.bindDocumentListeners();\n        }\n      }\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'previousicon':\n            this.previousIconTemplate = item.template;\n            break;\n          case 'nexticon':\n            this.nextIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n      this.cd.detectChanges();\n    }\n    ngAfterContentChecked() {\n      if (isPlatformBrowser(this.platformId)) {\n        const isCircular = this.isCircular();\n        let totalShiftedItems = this.totalShiftedItems;\n        if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n          if (this.autoplayInterval) {\n            this.stopAutoplay(false);\n          }\n          this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n          let page = this._page;\n          if (this.totalDots() !== 0 && page >= this.totalDots()) {\n            page = this.totalDots() - 1;\n            this._page = page;\n            this.onPage.emit({\n              page: this.page\n            });\n          }\n          totalShiftedItems = page * this._numScroll * -1;\n          if (isCircular) {\n            totalShiftedItems -= this._numVisible;\n          }\n          if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n            totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n            this.isRemainingItemsAdded = true;\n          } else {\n            this.isRemainingItemsAdded = false;\n          }\n          if (totalShiftedItems !== this.totalShiftedItems) {\n            this.totalShiftedItems = totalShiftedItems;\n          }\n          this._oldNumScroll = this._numScroll;\n          this.prevState.numScroll = this._numScroll;\n          this.prevState.numVisible = this._numVisible;\n          this.prevState.value = [...this._value];\n          if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n            this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n          }\n          this.isCreated = true;\n          if (this.autoplayInterval && this.isAutoplay()) {\n            this.startAutoplay();\n          }\n        }\n        if (isCircular) {\n          if (this.page === 0) {\n            totalShiftedItems = -1 * this._numVisible;\n          } else if (totalShiftedItems === 0) {\n            totalShiftedItems = -1 * this.value.length;\n            if (this.remainingItems > 0) {\n              this.isRemainingItemsAdded = true;\n            }\n          }\n          if (totalShiftedItems !== this.totalShiftedItems) {\n            this.totalShiftedItems = totalShiftedItems;\n          }\n        }\n      }\n    }\n    createStyle() {\n      if (!this.carouselStyle) {\n        this.carouselStyle = this.renderer.createElement('style');\n        this.carouselStyle.type = 'text/css';\n        this.renderer.appendChild(this.document.head, this.carouselStyle);\n      }\n      let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n      if (this.responsiveOptions) {\n        this.responsiveOptions.sort((data1, data2) => {\n          const value1 = data1.breakpoint;\n          const value2 = data2.breakpoint;\n          let result = null;\n          if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n            numeric: true\n          });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n          return -1 * result;\n        });\n        for (let i = 0; i < this.responsiveOptions.length; i++) {\n          let res = this.responsiveOptions[i];\n          innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n        }\n      }\n      this.carouselStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n      if (this.responsiveOptions) {\n        let matchedResponsiveData = {\n          numVisible: this.defaultNumVisible,\n          numScroll: this.defaultNumScroll\n        };\n        if (typeof window !== 'undefined') {\n          let windowWidth = window.innerWidth;\n          for (let i = 0; i < this.responsiveOptions.length; i++) {\n            let res = this.responsiveOptions[i];\n            if (parseInt(res.breakpoint, 10) >= windowWidth) {\n              matchedResponsiveData = res;\n            }\n          }\n        }\n        if (this._numScroll !== matchedResponsiveData.numScroll) {\n          let page = this._page;\n          page = Math.floor(page * this._numScroll / matchedResponsiveData.numScroll);\n          let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n          if (this.isCircular()) {\n            totalShiftedItems -= matchedResponsiveData.numVisible;\n          }\n          this.totalShiftedItems = totalShiftedItems;\n          this._numScroll = matchedResponsiveData.numScroll;\n          this._page = page;\n          this.onPage.emit({\n            page: this.page\n          });\n        }\n        if (this._numVisible !== matchedResponsiveData.numVisible) {\n          this._numVisible = matchedResponsiveData.numVisible;\n          this.setCloneItems();\n        }\n        this.cd.markForCheck();\n      }\n    }\n    setCloneItems() {\n      this.clonedItemsForStarting = [];\n      this.clonedItemsForFinishing = [];\n      if (this.isCircular()) {\n        this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n        this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n      }\n    }\n    firstIndex() {\n      return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n    }\n    lastIndex() {\n      return this.firstIndex() + this.numVisible - 1;\n    }\n    totalDots() {\n      return this.value?.length ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n    }\n    totalDotsArray() {\n      const totalDots = this.totalDots();\n      return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n    }\n    isVertical() {\n      return this.orientation === 'vertical';\n    }\n    isCircular() {\n      return this.circular && this.value && this.value.length >= this.numVisible;\n    }\n    isAutoplay() {\n      return this.autoplayInterval && this.allowAutoplay;\n    }\n    isForwardNavDisabled() {\n      return this.isEmpty() || this._page >= this.totalDots() - 1 && !this.isCircular();\n    }\n    isBackwardNavDisabled() {\n      return this.isEmpty() || this._page <= 0 && !this.isCircular();\n    }\n    isEmpty() {\n      return !this.value || this.value.length === 0;\n    }\n    navForward(e, index) {\n      if (this.isCircular() || this._page < this.totalDots() - 1) {\n        this.step(-1, index);\n      }\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (e && e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    navBackward(e, index) {\n      if (this.isCircular() || this._page !== 0) {\n        this.step(1, index);\n      }\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (e && e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    onDotClick(e, index) {\n      let page = this._page;\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (index > page) {\n        this.navForward(e, index);\n      } else if (index < page) {\n        this.navBackward(e, index);\n      }\n    }\n    onIndicatorKeydown(event) {\n      switch (event.code) {\n        case 'ArrowRight':\n          this.onRightKey();\n          break;\n        case 'ArrowLeft':\n          this.onLeftKey();\n          break;\n      }\n    }\n    onRightKey() {\n      const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n    onLeftKey() {\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n    onHomeKey() {\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, 0);\n    }\n    onEndKey() {\n      const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]r')];\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n    onTabKey() {\n      const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n      const highlightedIndex = indicators.findIndex(ind => DomHandler.getAttribute(ind, 'data-p-highlight') === true);\n      const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n      const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n      indicators[activeIndex].children[0].tabIndex = '-1';\n      indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n    findFocusedIndicatorIndex() {\n      const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n      const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n      return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    }\n    changedFocusedIndicator(prevInd, nextInd) {\n      const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n      indicators[prevInd].children[0].tabIndex = '-1';\n      indicators[nextInd].children[0].tabIndex = '0';\n      indicators[nextInd].children[0].focus();\n    }\n    step(dir, page) {\n      let totalShiftedItems = this.totalShiftedItems;\n      const isCircular = this.isCircular();\n      if (page != null) {\n        totalShiftedItems = this._numScroll * page * -1;\n        if (isCircular) {\n          totalShiftedItems -= this._numVisible;\n        }\n        this.isRemainingItemsAdded = false;\n      } else {\n        totalShiftedItems += this._numScroll * dir;\n        if (this.isRemainingItemsAdded) {\n          totalShiftedItems += this.remainingItems - this._numScroll * dir;\n          this.isRemainingItemsAdded = false;\n        }\n        let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n        page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n      }\n      if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n        totalShiftedItems = -1 * (this.value.length + this._numVisible);\n        page = 0;\n      } else if (isCircular && this.page === 0 && dir === 1) {\n        totalShiftedItems = 0;\n        page = this.totalDots() - 1;\n      } else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n        totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n        this.isRemainingItemsAdded = true;\n      }\n      if (this.itemsContainer) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n        this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n      }\n      this.totalShiftedItems = totalShiftedItems;\n      this._page = page;\n      this.onPage.emit({\n        page: this.page\n      });\n      this.cd.markForCheck();\n    }\n    startAutoplay() {\n      this.interval = setInterval(() => {\n        if (this.totalDots() > 0) {\n          if (this.page === this.totalDots() - 1) {\n            this.step(-1, 0);\n          } else {\n            this.step(-1, this.page + 1);\n          }\n        }\n      }, this.autoplayInterval);\n      this.allowAutoplay = true;\n      this.cd.markForCheck();\n    }\n    stopAutoplay(changeAllow = true) {\n      if (this.interval) {\n        clearInterval(this.interval);\n        this.interval = undefined;\n        if (changeAllow) {\n          this.allowAutoplay = false;\n        }\n      }\n      this.cd.markForCheck();\n    }\n    isPlaying() {\n      return !!this.interval;\n    }\n    onTransitionEnd() {\n      if (this.itemsContainer) {\n        this.itemsContainer.nativeElement.style.transition = '';\n        if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n          this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n        }\n      }\n    }\n    onTouchStart(e) {\n      let touchobj = e.changedTouches[0];\n      this.startPos = {\n        x: touchobj.pageX,\n        y: touchobj.pageY\n      };\n    }\n    onTouchMove(e) {\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    onTouchEnd(e) {\n      let touchobj = e.changedTouches[0];\n      if (this.isVertical()) {\n        this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n      } else {\n        this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n      }\n    }\n    changePageOnTouch(e, diff) {\n      if (Math.abs(diff) > this.swipeThreshold) {\n        if (diff < 0) {\n          this.navForward(e);\n        } else {\n          this.navBackward(e);\n        }\n      }\n    }\n    ariaPrevButtonLabel() {\n      return this.config.translation.aria ? this.config.translation.aria.prevPageLabel : undefined;\n    }\n    ariaSlideLabel() {\n      return this.config.translation.aria ? this.config.translation.aria.slide : undefined;\n    }\n    ariaNextButtonLabel() {\n      return this.config.translation.aria ? this.config.translation.aria.nextPageLabel : undefined;\n    }\n    ariaSlideNumber(value) {\n      return this.config.translation.aria ? this.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n    ariaPageLabel(value) {\n      return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    bindDocumentListeners() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.documentResizeListener) {\n          this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n            this.calculatePosition();\n          });\n        }\n      }\n    }\n    unbindDocumentListeners() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.documentResizeListener) {\n          this.documentResizeListener();\n          this.documentResizeListener = null;\n        }\n      }\n    }\n    ngOnDestroy() {\n      if (this.responsiveOptions) {\n        this.unbindDocumentListeners();\n      }\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n    }\n    static ɵfac = function Carousel_Factory(t) {\n      return new (t || Carousel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Carousel,\n      selectors: [[\"p-carousel\"]],\n      contentQueries: function Carousel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Carousel_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.indicatorContent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        page: \"page\",\n        numVisible: \"numVisible\",\n        numScroll: \"numScroll\",\n        responsiveOptions: \"responsiveOptions\",\n        orientation: \"orientation\",\n        verticalViewPortHeight: \"verticalViewPortHeight\",\n        contentClass: \"contentClass\",\n        indicatorsContentClass: \"indicatorsContentClass\",\n        indicatorsContentStyle: \"indicatorsContentStyle\",\n        indicatorStyleClass: \"indicatorStyleClass\",\n        indicatorStyle: \"indicatorStyle\",\n        value: \"value\",\n        circular: \"circular\",\n        showIndicators: \"showIndicators\",\n        showNavigators: \"showNavigators\",\n        autoplayInterval: \"autoplayInterval\",\n        style: \"style\",\n        styleClass: \"styleClass\"\n      },\n      outputs: {\n        onPage: \"onPage\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 14,\n      vars: 23,\n      consts: [[\"itemsContainer\", \"\"], [\"indicatorContent\", \"\"], [\"role\", \"region\", 3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-carousel-header\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-carousel-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-carousel-items-content\", 3, \"ngStyle\"], [1, \"p-carousel-items-container\", 3, \"transitionend\", \"touchend\", \"touchstart\", \"touchmove\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\", \"class\", \"ngStyle\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-carousel-footer\", 4, \"ngIf\"], [1, \"p-carousel-header\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [4, \"ngIf\"], [\"class\", \"p-carousel-prev-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-carousel-prev-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"keydown\", \"ngClass\", \"ngStyle\"], [\"type\", \"button\", 3, \"click\", \"ngClass\", \"ngStyle\", \"tabindex\"], [1, \"p-carousel-footer\"]],\n      template: function Carousel_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, Carousel_div_1_Template, 3, 1, \"div\", 3);\n          i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5);\n          i0.ɵɵtemplate(4, Carousel_button_4_Template, 3, 7, \"button\", 6);\n          i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8, 0);\n          i0.ɵɵlistener(\"transitionend\", function Carousel_Template_div_transitionend_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTransitionEnd());\n          })(\"touchend\", function Carousel_Template_div_touchend_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchEnd($event));\n          })(\"touchstart\", function Carousel_Template_div_touchstart_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function Carousel_Template_div_touchmove_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          });\n          i0.ɵɵtemplate(8, Carousel_div_8_Template, 2, 12, \"div\", 9)(9, Carousel_div_9_Template, 2, 12, \"div\", 9)(10, Carousel_div_10_Template, 2, 9, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, Carousel_button_11_Template, 3, 7, \"button\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, Carousel_ul_12_Template, 3, 5, \"ul\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, Carousel_div_13_Template, 3, 1, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx.isVertical(), !ctx.isVertical()))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.contentClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-carousel-content\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-live\", ctx.allowAutoplay ? \"polite\" : \"off\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c5, ctx.isVertical() ? ctx.verticalViewPortHeight : \"auto\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForStarting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.value);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForFinishing);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon],\n      styles: [\"@layer primeng{.p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Carousel;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CarouselModule = /*#__PURE__*/(() => {\n  class CarouselModule {\n    static ɵfac = function CarouselModule_Factory(t) {\n      return new (t || CarouselModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CarouselModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon, CommonModule, SharedModule]\n    });\n  }\n  return CarouselModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Carousel, CarouselModule };\n//# sourceMappingURL=primeng-carousel.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}