{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/multiselect\";\nfunction SalesCallAttachmentsComponent_ng_template_10_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_ng_template_10_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"Title\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" File Name \");\n    i0.ɵɵtemplate(4, SalesCallAttachmentsComponent_ng_template_10_i_4_Template, 1, 1, \"i\", 15)(5, SalesCallAttachmentsComponent_ng_template_10_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"Title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"Title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.Type, \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.ChangedOn, \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.ChangedBy, \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 28);\n    i0.ɵɵtemplate(3, SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_3_Template, 2, 1, \"ng-container\", 29)(4, SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_4_Template, 2, 1, \"ng-container\", 29)(5, SalesCallAttachmentsComponent_ng_template_11_ng_container_6_ng_container_5_Template, 2, 1, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Type\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ChangedOn\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ChangedBy\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23)(2, \"div\", 24)(3, \"i\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallAttachmentsComponent_ng_template_11_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"td\")(8, \"button\", 26)(9, \"i\", 27);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const attachment_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(attachment_r6.FileIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.Title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(attachment_r6.Action);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"No attachments found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 30);\n    i0.ɵɵtext(2, \"Loading attachments data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SalesCallAttachmentsComponent = /*#__PURE__*/(() => {\n  class SalesCallAttachmentsComponent {\n    constructor(activitiesservice) {\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.attachmentdetails = [];\n      this.bp_id = '';\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'Type',\n        header: 'Type'\n      }, {\n        field: 'ChangedOn',\n        header: 'Changed On'\n      }, {\n        field: 'ChangedBy',\n        header: 'Changed By'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.attachmentdetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.bp_id = response?.bp_id;\n          this.attachmentdetails = response?.contact_activity;\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallAttachmentsComponent_Factory(t) {\n        return new (t || SalesCallAttachmentsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallAttachmentsComponent,\n        selectors: [[\"app-sales-call-attachments\"]],\n        decls: 14,\n        vars: 11,\n        consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\", \"text-primary\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"6\", 1, \"border-round-left-lg\"]],\n        template: function SalesCallAttachmentsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n            i0.ɵɵtext(3, \"Attachments\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵelement(5, \"p-button\", 5);\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallAttachmentsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 7)(8, \"p-table\", 8, 0);\n            i0.ɵɵlistener(\"onColReorder\", function SalesCallAttachmentsComponent_Template_p_table_onColReorder_8_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(10, SalesCallAttachmentsComponent_ng_template_10_Template, 9, 3, \"ng-template\", 9)(11, SalesCallAttachmentsComponent_ng_template_11_Template, 11, 4, \"ng-template\", 10)(12, SalesCallAttachmentsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11)(13, SalesCallAttachmentsComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.attachmentdetails)(\"rows\", 8)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.NgControlStatus, i3.NgModel, i4.Table, i5.PrimeTemplate, i4.SortableColumn, i4.FrozenColumn, i4.ReorderableColumn, i6.Button, i7.MultiSelect]\n      });\n    }\n  }\n  return SalesCallAttachmentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}