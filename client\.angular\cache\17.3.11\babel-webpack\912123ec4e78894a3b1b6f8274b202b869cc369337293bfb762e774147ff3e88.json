{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./service/app.layout.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/button\";\nconst _c0 = [\"searchinput\"];\nconst _c1 = a0 => ({\n  \"breadcrumb-search-active\": a0\n});\nfunction AppBreadcrumbComponent_ng_template_3_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 16);\n    i0.ɵɵtext(1, \" / \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppBreadcrumbComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AppBreadcrumbComponent_ng_template_3_li_2_Template, 2, 0, \"li\", 15);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const last_r3 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r3);\n  }\n}\nexport let AppBreadcrumbComponent = /*#__PURE__*/(() => {\n  class AppBreadcrumbComponent {\n    constructor(router, layoutService) {\n      this.router = router;\n      this.layoutService = layoutService;\n      this._breadcrumbs$ = new BehaviorSubject([]);\n      this.breadcrumbs$ = this._breadcrumbs$.asObservable();\n      this.searchActive = false;\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        const root = this.router.routerState.snapshot.root;\n        const breadcrumbs = [];\n        this.addBreadcrumb(root, [], breadcrumbs);\n        this._breadcrumbs$.next(breadcrumbs);\n      });\n    }\n    activateSearch() {\n      this.searchActive = true;\n      setTimeout(() => {\n        this.searchInput.nativeElement.focus();\n      }, 100);\n    }\n    deactivateSearch() {\n      this.searchActive = false;\n    }\n    onConfigButtonClick() {\n      this.layoutService.showConfigSidebar();\n    }\n    onSidebarButtonClick() {\n      this.layoutService.showSidebar();\n    }\n    addBreadcrumb(route, parentUrl, breadcrumbs) {\n      const routeUrl = parentUrl.concat(route.url.map(url => url.path));\n      const breadcrumb = route.data['breadcrumb'];\n      const parentBreadcrumb = route.parent && route.parent.data ? route.parent.data['breadcrumb'] : null;\n      if (breadcrumb && breadcrumb !== parentBreadcrumb) {\n        breadcrumbs.push({\n          label: route.data['breadcrumb'],\n          url: '/' + routeUrl.join('/')\n        });\n      }\n      if (route.firstChild) {\n        this.addBreadcrumb(route.firstChild, routeUrl, breadcrumbs);\n      }\n    }\n    static {\n      this.ɵfac = function AppBreadcrumbComponent_Factory(t) {\n        return new (t || AppBreadcrumbComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LayoutService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppBreadcrumbComponent,\n        selectors: [[\"app-breadcrumb\"]],\n        viewQuery: function AppBreadcrumbComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          }\n        },\n        decls: 17,\n        vars: 6,\n        consts: [[\"searchinput\", \"\"], [1, \"layout-breadcrumb\", \"flex\", \"align-items-center\", \"relative\", \"h-3rem\"], [1, \"relative\", \"z-2\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"breadcrumb-menu\", \"flex\", \"align-items-center\", \"justify-content-end\", \"lg:hidden\", \"absolute\", \"right-0\", \"top-0\", \"z-4\", \"h-3rem\", \"w-screen\"], [1, \"w-full\", \"m-0\", \"ml-3\"], [1, \"breadcrumb-search\", \"flex\", \"justify-content-end\", 3, \"ngClass\"], [\"pButton\", \"\", \"icon\", \"pi pi-search\", \"type\", \"button\", 1, \"breadcrumb-searchbutton\", \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"search-input-wrapper\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"blur\", \"keydown.escape\"], [1, \"pi\", \"pi-search\"], [1, \"right-panel-button\", \"relative\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Today\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"hidden\", \"md:block\", \"font-normal\", 2, \"width\", \"5.7rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"flex\", \"md:hidden\", 3, \"click\"], [\"class\", \"layout-breadcrumb-chevron\", 4, \"ngIf\"], [1, \"layout-breadcrumb-chevron\"]],\n        template: function AppBreadcrumbComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"nav\")(2, \"ol\", 2);\n            i0.ɵɵtemplate(3, AppBreadcrumbComponent_ng_template_3_Template, 3, 2, \"ng-template\", 3);\n            i0.ɵɵpipe(4, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\", 5)(7, \"div\", 6)(8, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_8_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.activateSearch());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"span\", 9)(11, \"input\", 10, 0);\n            i0.ɵɵlistener(\"blur\", function AppBreadcrumbComponent_Template_input_blur_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deactivateSearch());\n            })(\"keydown.escape\", function AppBreadcrumbComponent_Template_input_keydown_escape_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deactivateSearch());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(14, \"li\", 12)(15, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(4, 2, ctx.breadcrumbs$));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx.searchActive));\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.InputText, i5.ButtonDirective, i3.AsyncPipe],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppBreadcrumbComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}