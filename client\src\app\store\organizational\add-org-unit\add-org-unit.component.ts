import { Component, OnInit } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Router, ActivatedRoute } from '@angular/router';
import { OrganizationalService } from '../organizational.service';
import { MessageService } from 'primeng/api';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil, Observable, concat, of, map } from 'rxjs';
import { Country, State } from 'country-state-city';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
  debounceTime,
  startWith,
  finalize,
} from 'rxjs/operators';

@Component({
  selector: 'app-add-org-unit',
  templateUrl: './add-org-unit.component.html',
  styleUrl: './add-org-unit.component.scss',
})
export class AddOrgUnitComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public units$?: Observable<any[]>;
  public unitLoading = false;
  public unitInput$ = new Subject<string>();
  public managers$?: Observable<any[]>;
  public managerLoading = false;
  public managerInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public submitted = false;
  public saving = false;
  public countries: any[] = [];
  public states: any[] = [];
  public selectedCountry: string = '';
  public selectedState: string = '';
  public parentUnitId: string | null = null;

  public OrganizationForm: FormGroup = this.formBuilder.group({
    name: [''],
    valid_from: ['', [Validators.required]],
    valid_to: ['', [Validators.required]],
    parent_organisational_unit_id: [[]],
    company_name: [''],
    country_code: ['', [Validators.required]],
    state: ['', [Validators.required]],
    city: [''],
    house_number: [''],
    street: [''],
    postal_code: [''],
    sales_indicator: [false],
    sales_organisation_indicator: [false],
    service_indicator: [false],
    service_organisation_indicator: [false],
    marketing_indicator: [false],
    reporting_line_indicator: [false],
    business_partner_internal_id: [[]],
  });

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private organizationalservice: OrganizationalService,
    private messageservice: MessageService
  ) {}

  ngOnInit(): void {
    this.parentUnitId = history.state?.parentUnitId ?? null;
    if (this.parentUnitId) {
      const params = {
        'fields[0]': 'organisational_unit_id',
        'fields[1]': 'name',
        'filters[organisational_unit_id][$eq]': this.parentUnitId,
      };

      this.organizationalservice
        .getParentUnit(params)
        .subscribe((resp: any) => {
          this.defaultOptions = resp ?? [];
          this.patchParentControl();
          this.loadParentUnit();
        });
    } else {
      this.loadParentUnit();
    }
    this.loadCountries();
    this.loadManager();
  }

  private patchParentControl() {
    if (this.parentUnitId) {
      this.OrganizationForm.patchValue({
        parent_organisational_unit_id: String(this.parentUnitId), // make sure types match
      });
    }
  }

  loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  onCountryChange() {
    this.states = State.getStatesOfCountry(this.selectedCountry).map(
      (state) => ({
        name: state.name,
        isoCode: state.isoCode,
      })
    );
    this.selectedState = ''; // Reset state
  }

  private loadParentUnit(): void {
    this.units$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.unitInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.unitLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'fields[0]': 'organisational_unit_id',
            'fields[2]': 'name',
          };

          if (term) {
            params['filters[$or][0][organisational_unit_id][$containsi]'] =
              term;
            params['filters[$or][1][name][$containsi]'] = term;
          }

          return this.organizationalservice.getParentUnit(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.unitLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  private loadManager(): void {
    this.managers$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.managerInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.managerLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'fields[0]': 'business_partner_internal_id',
            'populate[business_partner][fields][0]': 'bp_full_name',
          };

          if (term) {
            params[
              'filters[$or][0][business_partner_internal_id][$containsi]'
            ] = term;
            params[
              'filters[$or][1][business_partner][bp_full_name][$containsi]'
            ] = term;
          }

          return this.organizationalservice.getManagers(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.managerLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  async onSubmit() {
    this.submitted = true;

    if (this.OrganizationForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.OrganizationForm.value };

    const data = {
      name: value?.name,
      valid_from: value?.valid_from ? this.formatDate(value.valid_from) : null,
      valid_to: value?.valid_to ? this.formatDate(value.valid_to) : null,
      parent_organisational_unit_id: value?.parent_organisational_unit_id,
      company_name: value?.company_name,
      country_code: value?.country_code,
      state: value?.state,
      house_number: value?.house_number,
      street: value?.street,
      city: value?.city,
      postal_code: value?.postal_code,
      sales_indicator: value?.sales_indicator,
      sales_organisation_indicator: value?.sales_organisation_indicator,
      service_indicator: value?.service_indicator,
      service_organisation_indicator: value?.service_organisation_indicator,
      marketing_indicator: value?.marketing_indicator,
      reporting_line_indicator: value?.reporting_line_indicator,
      business_partner_internal_id: value?.business_partner_internal_id,
    };

    this.organizationalservice
      .createOrganization(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.organisational_unit_id) {
            sessionStorage.setItem(
              'organizationMessage',
              'Organization created successfully!'
            );
            window.location.href = `${window.location.origin}#/store/organization/${response?.data?.organisational_unit_id}/general`;
          } else {
            console.error(
              'Missing organisational_unit_id in response:',
              response
            );
          }
        },
        error: (res: any) => {
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  get f(): any {
    return this.OrganizationForm.controls;
  }

  onCancel() {
    this.router.navigate(['/store/organization']);
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
