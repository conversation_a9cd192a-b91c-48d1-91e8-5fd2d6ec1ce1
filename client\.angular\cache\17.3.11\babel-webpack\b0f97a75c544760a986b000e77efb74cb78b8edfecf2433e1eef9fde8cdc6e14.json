{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../prospects.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/toast\";\nimport * as i11 from \"primeng/confirmdialog\";\nfunction ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tab_r1.label, \" \");\n  }\n}\nfunction ProspectsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 37);\n    i0.ɵɵtemplate(1, ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ProspectsDetailsComponent {\n  constructor(route, router, messageservice, formBuilder, confirmationservice, prospectsservice) {\n    this.route = route;\n    this.router = router;\n    this.messageservice = messageservice;\n    this.formBuilder = formBuilder;\n    this.confirmationservice = confirmationservice;\n    this.prospectsservice = prospectsservice;\n    this.unsubscribe$ = new Subject();\n    this.prospectDetails = null;\n    this.sidebarDetails = null;\n    this.NoteDetails = null;\n    this.customerData = null;\n    this.items = [];\n    this.activeItem = null;\n    this.breadcrumbitems = [];\n    this.id = '';\n    this.Actions = [];\n    this.activeIndex = 0;\n    this.isSidebarHidden = false;\n    this.submitted = false;\n    this.saving = false;\n    this.GlobalNoteForm = this.formBuilder.group({\n      note: ['']\n    });\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.prospectsservice.getGlobalNote(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.NoteDetails = response?.data[0] || [];\n        this.GlobalNoteForm.patchValue({\n          note: response?.data[0]?.note\n        });\n      },\n      error: error => {\n        console.error('Error fetching global note:', error);\n      }\n    });\n    this.Actions = [{\n      name: 'Convert to Customer',\n      code: 'CI'\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const prospectId = params.get('id');\n      if (prospectId) {\n        this.loadProspectData(prospectId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/prospects/${id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/prospects/${id}/contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/prospects/${id}/sales-team`\n    },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/prospects/${id}/ai-insights`,\n    // },\n    {\n      label: 'Organization Data',\n      routerLink: `/store/prospects/${id}/organization-data`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/prospects/${id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/prospects/${id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/prospects/${id}/activities`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Prospects',\n      routerLink: ['/store/prospects']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadProspectData(prospectId) {\n    this.prospectsservice.getProspectByID(prospectId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.prospectDetails = response?.data[0] || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.addresses || []);\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  onActionChange(event) {\n    if (event.value?.code === 'CI') {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.ConvertToCustomer(event);\n        }\n      });\n    }\n  }\n  ConvertToCustomer(event) {\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\n    console.log('Customer Data:', this.customerData);\n    this.prospectsservice.bpCreation(this.customerData).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Convert to Customer Successfully!'\n        });\n      },\n      error: error => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: error?.error?.message || 'Error while processing your request.'\n        });\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  createBusinessPartnerObject(data) {\n    return {\n      BusinessPartner: data?.bp_id || '',\n      OrganizationBPName1: data?.bp_full_name || '',\n      to_BusinessPartnerAddress: data?.addresses?.map(address => ({\n        Country: address.country_code || 'US',\n        Region: address.region || '',\n        HouseNumber: address.house_number || '',\n        AdditionalStreetPrefixName: address.additional_street_prefix_name || '',\n        AdditionalStreetSuffixName: address.additional_street_suffix_name || '',\n        StreetName: address.street_name || '',\n        PostalCode: address.postal_code || '',\n        CityName: address.city_name || '',\n        Language: 'EN',\n        ...(address.address_usages?.length ? {\n          to_AddressUsage: address.address_usages.map(usage => ({\n            AddressUsage: usage.address_usage || ''\n          }))\n        } : {}),\n        to_EmailAddress: address.emails?.map(email => ({\n          EmailAddress: email.email_address || ''\n        })) || [],\n        to_PhoneNumber: address.phone_numbers?.map(phone => ({\n          PhoneNumber: phone.phone_number || ''\n        })) || [],\n        to_URLAddress: address.home_page_urls?.map(url => ({\n          WebsiteURL: url.website_url || ''\n        })) || [],\n        to_FaxNumber: address.fax_numbers?.map(fax => ({\n          FaxNumber: fax.fax_number || ''\n        })) || [],\n        to_MobilePhoneNumber: address.phone_numbers?.map(phone => ({\n          PhoneNumber: phone.phone_number || ''\n        })) || []\n      })) || [],\n      to_BusinessPartnerRole: [{\n        BusinessPartnerRole: 'FLCU01'\n      }, {\n        BusinessPartnerRole: 'FLCU00'\n      }],\n      to_Customer: data?.customer ? {\n        Customer: data.customer.customer_id || '',\n        OrderIsBlockedForCustomer: data.customer.order_is_blocked_for_customer || '',\n        to_CustomerSalesArea: Array.isArray(data.customer.partner_functions) ? Object.values(data.customer.partner_functions.reduce((acc, salesArea) => {\n          const key = `${salesArea.sales_organization}-${salesArea.distribution_channel}-${salesArea.division}`;\n          if (!acc[key]) {\n            acc[key] = {\n              SalesOrganization: salesArea.sales_organization || '',\n              DistributionChannel: salesArea.distribution_channel || '',\n              Division: salesArea.division || '',\n              to_PartnerFunction: new Set() // Use a Set to remove duplicates\n            };\n          }\n          // Collect partner functions\n          data.customer.partner_functions.filter(partner => partner.sales_organization === salesArea.sales_organization && partner.distribution_channel === salesArea.distribution_channel && partner.division === salesArea.division).forEach(partner => {\n            acc[key].to_PartnerFunction.add(JSON.stringify({\n              PartnerFunction: partner.partner_function || '',\n              BPCustomerNumber: partner.bp_customer_number || ''\n            }));\n          });\n          // Add new partner functions (CP)\n          data?.contact_companies?.forEach(company => {\n            acc[key].to_PartnerFunction.add(JSON.stringify({\n              PartnerFunction: 'CP',\n              BPCustomerNumber: company?.bp_person_id || ''\n            }));\n          });\n          return acc;\n        }, {})).map(area => ({\n          ...area,\n          to_PartnerFunction: Array.from(area.to_PartnerFunction).map(pf => JSON.parse(pf))\n        })) : []\n      } : null,\n      to_BusinessPartnerContact: Array.isArray(data?.contact_companies) ? data.contact_companies.map(company => ({\n        BusinessPartnerPerson: company?.bp_person_id || '',\n        FirstName: company?.business_partner_person?.first_name || '',\n        MiddleName: company?.business_partner_person?.middle_name || '',\n        LastName: company?.business_partner_person?.last_name || '',\n        to_ContactAddress: Array.isArray(company?.business_partner_person?.contact_person_addresses) ? company.business_partner_person.contact_person_addresses.map(addr => ({\n          Country: addr.country_code || 'US',\n          Region: addr.region || '',\n          HouseNumber: addr.house_number || '',\n          AdditionalStreetPrefixName: addr.additional_street_prefix_name || '',\n          AdditionalStreetSuffixName: addr.additional_street_suffix_name || '',\n          StreetName: addr.street_name || '',\n          PostalCode: addr.postal_code || '',\n          CityName: addr.city_name || '',\n          Language: 'EN',\n          ...(Array.isArray(addr.to_AddressUsage) && addr.to_AddressUsage.length > 0 ? {\n            to_AddressUsage: addr.to_AddressUsage.map(usage => ({\n              AddressUsage: usage.AddressUsage || ''\n            }))\n          } : {}),\n          to_EmailAddress: addr.emails?.map(email => ({\n            EmailAddress: email.email_address || ''\n          })) || [],\n          to_PhoneNumber: addr.phone_numbers?.map(phone => ({\n            PhoneNumber: phone.phone_number || ''\n          })) || [],\n          to_URLAddress: addr.home_page_urls?.map(url => ({\n            WebsiteURL: url.website_url || ''\n          })) || [],\n          to_FaxNumber: addr.fax_numbers?.map(fax => ({\n            FaxNumber: fax.fax_number || ''\n          })) || [],\n          to_MobilePhoneNumber: addr.phone_numbers?.map(phone => ({\n            PhoneNumber: phone.phone_number || ''\n          })) || []\n        })) : [],\n        to_ContactRelationship: {\n          ContactPersonDepartment: company?.person_func_and_dept?.contact_person_department || '',\n          ContactPersonFunction: company?.person_func_and_dept?.contact_person_function || ''\n        },\n        to_BusinessPartnerRole: [{\n          BusinessPartnerRole: 'BUP001'\n        }]\n      })) : []\n    };\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.GlobalNoteForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.GlobalNoteForm.value\n      };\n      const data = {\n        note: value?.note,\n        bp_id: _this?.id,\n        ...(!_this.NoteDetails.documentId ? {\n          is_global_note: true\n        } : {})\n      };\n      const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.prospectsservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n      : _this.prospectsservice.createNote(data); // Create if not exists\n      apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Prospect Note Updated successFully!'\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  goToBack() {\n    this.router.navigate(['/store/prospects']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ProspectsDetailsComponent_Factory(t) {\n      return new (t || ProspectsDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i4.ProspectsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsDetailsComponent,\n      selectors: [[\"app-prospects-details\"]],\n      decls: 83,\n      vars: 27,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-primary\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-8rem\", \"h-2rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ProspectsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n            return ctx.onActionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, ProspectsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"h5\", 19);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 20)(23, \"li\", 21)(24, \"span\", 22);\n          i0.ɵɵtext(25, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"li\", 21)(28, \"span\", 22);\n          i0.ɵɵtext(29, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21)(32, \"span\", 22);\n          i0.ɵɵtext(33, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"ul\", 24)(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n          i0.ɵɵtext(40, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 28);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n          i0.ɵɵtext(47, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 28);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n          i0.ɵɵtext(54, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 28);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 25)(59, \"span\", 26)(60, \"i\", 27);\n          i0.ɵɵtext(61, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 28);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"li\", 25)(66, \"span\", 26)(67, \"i\", 27);\n          i0.ɵɵtext(68, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\", 28);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(72, \"form\", 29)(73, \"div\", 30)(74, \"h5\", 31);\n          i0.ɵɵtext(75, \"Global Note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 32);\n          i0.ɵɵelement(77, \"textarea\", 33);\n          i0.ɵɵelementStart(78, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_button_click_78_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(79, \"div\", 35)(80, \"p-button\", 36);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_p_button_click_80_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(82, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name) || \"-\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_id) || \"-\", \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.prospectDetails == null ? null : ctx.prospectDetails.account_owner) || \"-\", \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.contact_person_addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i5.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i2.PrimeTemplate, i3.FormGroupDirective, i3.FormControlName, i6.ButtonDirective, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.Toast, i11.ConfirmDialog],\n      styles: [\".prospect-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .prospect-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .prospect-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .prospect-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL3Byb3NwZWN0cy1kZXRhaWxzL3Byb3NwZWN0cy1kZXRhaWxzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVRO0VBQ0ksa0JBQUE7QUFEWjtBQUdZO0VBQ0ksNEJBQUE7RUFDQSwyQ0FBQTtBQURoQjtBQUdnQjtFQUNJLFNBQUE7QUFEcEI7QUFLWTtFQUNJLDRCQUFBO0VBQ0EsaUJBQUE7QUFIaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLnByb3NwZWN0LXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cge1xyXG4gICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS0xMDApO1xyXG5cclxuICAgICAgICAgICAgICAgIGg0IHtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxLjcxNHJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtemplate", "ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template", "ProspectsDetailsComponent", "constructor", "route", "router", "messageservice", "formBuilder", "confirmationservice", "prospectsservice", "unsubscribe$", "prospectDetails", "sidebarDetails", "NoteDetails", "customerData", "items", "activeItem", "breadcrumbitems", "id", "Actions", "activeIndex", "isSidebarHidden", "submitted", "saving", "GlobalNoteForm", "group", "note", "ngOnInit", "snapshot", "paramMap", "get", "getGlobalNote", "pipe", "subscribe", "next", "response", "data", "patchValue", "error", "console", "name", "code", "home", "icon", "makeMenuItems", "length", "setActiveTabFromURL", "params", "prospectId", "loadProspectData", "events", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getProspectByID", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "onActionChange", "value", "confirm", "message", "header", "accept", "ConvertToCustomer", "createBusinessPartnerObject", "log", "bpCreation", "add", "severity", "detail", "BusinessPartner", "bp_id", "OrganizationBPName1", "bp_full_name", "to_BusinessPartnerAddress", "Country", "country_code", "Region", "HouseNumber", "AdditionalStreetPrefixName", "additional_street_prefix_name", "AdditionalStreetSuffixName", "additional_street_suffix_name", "StreetName", "PostalCode", "CityName", "Language", "to_AddressUsage", "AddressUsage", "to_<PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_PhoneNumber", "phone", "PhoneNumber", "to_URLAddress", "WebsiteURL", "to_FaxNumber", "fax_numbers", "fax", "FaxNumber", "fax_number", "to_MobilePhoneNumber", "to_BusinessPartnerRole", "BusinessPartnerRole", "to_Customer", "customer", "Customer", "customer_id", "OrderIsBlockedForCustomer", "order_is_blocked_for_customer", "to_CustomerSalesArea", "Array", "isArray", "partner_functions", "Object", "values", "reduce", "acc", "salesArea", "key", "sales_organization", "distribution_channel", "division", "SalesOrganization", "DistributionChannel", "Division", "to_PartnerFunction", "Set", "partner", "for<PERSON>ach", "JSON", "stringify", "PartnerFunction", "partner_function", "BPCustomerNumber", "bp_customer_number", "contact_companies", "company", "bp_person_id", "area", "from", "pf", "parse", "to_BusinessPartnerContact", "BusinessPartnerPerson", "FirstName", "business_partner_person", "first_name", "MiddleName", "middle_name", "LastName", "last_name", "to_ContactAddress", "contact_person_addresses", "addr", "to_ContactRelationship", "ContactPersonDepartment", "person_func_and_dept", "contact_person_department", "ContactPersonFunction", "contact_person_function", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "documentId", "is_global_note", "apiCall", "updateNote", "createNote", "setTimeout", "window", "location", "reload", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "MessageService", "i3", "FormBuilder", "ConfirmationService", "i4", "ProspectsService", "selectors", "decls", "vars", "consts", "template", "ProspectsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "ProspectsDetailsComponent_Template_p_dropdown_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "selectedActions", "ɵɵlistener", "ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener", "ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener", "ProspectsDetailsComponent_p_tabPanel_9_Template", "ProspectsDetailsComponent_Template_button_click_78_listener", "ProspectsDetailsComponent_Template_p_button_click_80_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵtextInterpolate", "account_owner"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProspectsService } from '../prospects.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects-details',\r\n  templateUrl: './prospects-details.component.html',\r\n  styleUrl: './prospects-details.component.scss',\r\n})\r\nexport class ProspectsDetailsComponent implements OnInit, OnDestroy {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public prospectDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public NoteDetails: any = null;\r\n  public customerData: any = null;\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public home: MenuItem | any;\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public id: string = '';\r\n  public Actions: Actions[] = [];\r\n  public selectedActions: Actions | undefined;\r\n  public activeIndex: number = 0;\r\n  public isSidebarHidden = false;\r\n  public submitted = false;\r\n  public saving = false;\r\n\r\n  public GlobalNoteForm: FormGroup = this.formBuilder.group({\r\n    note: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private messageservice: MessageService,\r\n    private formBuilder: FormBuilder,\r\n    private confirmationservice: ConfirmationService,\r\n    private prospectsservice: ProspectsService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.prospectsservice\r\n      .getGlobalNote(this.id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.NoteDetails = response?.data[0] || [];\r\n          this.GlobalNoteForm.patchValue({\r\n            note: response?.data[0]?.note,\r\n          });\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching global note:', error);\r\n        },\r\n      });\r\n\r\n    this.Actions = [{ name: 'Convert to Customer', code: 'CI' }];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.makeMenuItems(this.id);\r\n\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const prospectId = params.get('id');\r\n        if (prospectId) {\r\n          this.loadProspectData(prospectId);\r\n        }\r\n      });\r\n\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      { label: 'Overview', routerLink: `/store/prospects/${id}/overview` },\r\n      { label: 'Contacts', routerLink: `/store/prospects/${id}/contacts` },\r\n      { label: 'Sales Team', routerLink: `/store/prospects/${id}/sales-team` },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/prospects/${id}/ai-insights`,\r\n      // },\r\n      {\r\n        label: 'Organization Data',\r\n        routerLink: `/store/prospects/${id}/organization-data`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/prospects/${id}/attachments`,\r\n      },\r\n      { label: 'Notes', routerLink: `/store/prospects/${id}/notes` },\r\n      { label: 'Activities', routerLink: `/store/prospects/${id}/activities` },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Prospects', routerLink: ['/store/prospects'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadProspectData(prospectId: string): void {\r\n    this.prospectsservice\r\n      .getProspectByID(prospectId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.prospectDetails = response?.data[0] || null;\r\n          this.sidebarDetails = this.formatSidebarDetails(\r\n            response?.data[0]?.addresses || []\r\n          );\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    if (event.value?.code === 'CI') {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: () => {\r\n          this.ConvertToCustomer(event);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  ConvertToCustomer(event: any) {\r\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\r\n    console.log('Customer Data:', this.customerData);\r\n    this.prospectsservice\r\n      .bpCreation(this.customerData)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Convert to Customer Successfully!',\r\n          });\r\n        },\r\n        error: (error: any) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail:\r\n              error?.error?.message || 'Error while processing your request.',\r\n          });\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  createBusinessPartnerObject(data: any) {\r\n    return {\r\n      BusinessPartner: data?.bp_id || '',\r\n      OrganizationBPName1: data?.bp_full_name || '',\r\n      to_BusinessPartnerAddress:\r\n        data?.addresses?.map((address: any) => ({\r\n          Country: address.country_code || 'US',\r\n          Region: address.region || '',\r\n          HouseNumber: address.house_number || '',\r\n          AdditionalStreetPrefixName:\r\n            address.additional_street_prefix_name || '',\r\n          AdditionalStreetSuffixName:\r\n            address.additional_street_suffix_name || '',\r\n          StreetName: address.street_name || '',\r\n          PostalCode: address.postal_code || '',\r\n          CityName: address.city_name || '',\r\n          Language: 'EN',\r\n          ...(address.address_usages?.length\r\n            ? {\r\n                to_AddressUsage: address.address_usages.map((usage: any) => ({\r\n                  AddressUsage: usage.address_usage || '',\r\n                })),\r\n              }\r\n            : {}),\r\n          to_EmailAddress:\r\n            address.emails?.map((email: any) => ({\r\n              EmailAddress: email.email_address || '',\r\n            })) || [],\r\n          to_PhoneNumber:\r\n            address.phone_numbers?.map((phone: any) => ({\r\n              PhoneNumber: phone.phone_number || '',\r\n            })) || [],\r\n          to_URLAddress:\r\n            address.home_page_urls?.map((url: any) => ({\r\n              WebsiteURL: url.website_url || '',\r\n            })) || [],\r\n          to_FaxNumber:\r\n            address.fax_numbers?.map((fax: any) => ({\r\n              FaxNumber: fax.fax_number || '',\r\n            })) || [],\r\n          to_MobilePhoneNumber:\r\n            address.phone_numbers?.map((phone: any) => ({\r\n              PhoneNumber: phone.phone_number || '',\r\n            })) || [],\r\n        })) || [],\r\n\r\n      to_BusinessPartnerRole: [\r\n        { BusinessPartnerRole: 'FLCU01' },\r\n        { BusinessPartnerRole: 'FLCU00' },\r\n      ],\r\n\r\n      to_Customer: data?.customer\r\n        ? {\r\n            Customer: data.customer.customer_id || '',\r\n            OrderIsBlockedForCustomer:\r\n              data.customer.order_is_blocked_for_customer || '',\r\n            to_CustomerSalesArea: Array.isArray(data.customer.partner_functions)\r\n              ? Object.values(\r\n                  data.customer.partner_functions.reduce(\r\n                    (acc: any, salesArea: any) => {\r\n                      const key = `${salesArea.sales_organization}-${salesArea.distribution_channel}-${salesArea.division}`;\r\n\r\n                      if (!acc[key]) {\r\n                        acc[key] = {\r\n                          SalesOrganization: salesArea.sales_organization || '',\r\n                          DistributionChannel:\r\n                            salesArea.distribution_channel || '',\r\n                          Division: salesArea.division || '',\r\n                          to_PartnerFunction: new Set(), // Use a Set to remove duplicates\r\n                        };\r\n                      }\r\n\r\n                      // Collect partner functions\r\n                      data.customer.partner_functions\r\n                        .filter(\r\n                          (partner: any) =>\r\n                            partner.sales_organization ===\r\n                              salesArea.sales_organization &&\r\n                            partner.distribution_channel ===\r\n                              salesArea.distribution_channel &&\r\n                            partner.division === salesArea.division\r\n                        )\r\n                        .forEach((partner: any) => {\r\n                          acc[key].to_PartnerFunction.add(\r\n                            JSON.stringify({\r\n                              PartnerFunction: partner.partner_function || '',\r\n                              BPCustomerNumber:\r\n                                partner.bp_customer_number || '',\r\n                            })\r\n                          );\r\n                        });\r\n\r\n                      // Add new partner functions (CP)\r\n                      data?.contact_companies?.forEach((company: any) => {\r\n                        acc[key].to_PartnerFunction.add(\r\n                          JSON.stringify({\r\n                            PartnerFunction: 'CP',\r\n                            BPCustomerNumber: company?.bp_person_id || '',\r\n                          })\r\n                        );\r\n                      });\r\n\r\n                      return acc;\r\n                    },\r\n                    {}\r\n                  )\r\n                ).map((area: any) => ({\r\n                  ...area,\r\n                  to_PartnerFunction: Array.from(area.to_PartnerFunction).map(\r\n                    (pf: any) => JSON.parse(pf)\r\n                  ),\r\n                }))\r\n              : [],\r\n          }\r\n        : null,\r\n\r\n      to_BusinessPartnerContact: Array.isArray(data?.contact_companies)\r\n        ? data.contact_companies.map((company: any) => ({\r\n            BusinessPartnerPerson: company?.bp_person_id || '',\r\n            FirstName: company?.business_partner_person?.first_name || '',\r\n            MiddleName: company?.business_partner_person?.middle_name || '',\r\n            LastName: company?.business_partner_person?.last_name || '',\r\n            to_ContactAddress: Array.isArray(\r\n              company?.business_partner_person?.contact_person_addresses\r\n            )\r\n              ? company.business_partner_person.contact_person_addresses.map(\r\n                  (addr: any) => ({\r\n                    Country: addr.country_code || 'US',\r\n                    Region: addr.region || '',\r\n                    HouseNumber: addr.house_number || '',\r\n                    AdditionalStreetPrefixName:\r\n                      addr.additional_street_prefix_name || '',\r\n                    AdditionalStreetSuffixName:\r\n                      addr.additional_street_suffix_name || '',\r\n                    StreetName: addr.street_name || '',\r\n                    PostalCode: addr.postal_code || '',\r\n                    CityName: addr.city_name || '',\r\n                    Language: 'EN',\r\n                    ...(Array.isArray(addr.to_AddressUsage) &&\r\n                    addr.to_AddressUsage.length > 0\r\n                      ? {\r\n                          to_AddressUsage: addr.to_AddressUsage.map(\r\n                            (usage: any) => ({\r\n                              AddressUsage: usage.AddressUsage || '',\r\n                            })\r\n                          ),\r\n                        }\r\n                      : {}),\r\n                    to_EmailAddress:\r\n                      addr.emails?.map((email: any) => ({\r\n                        EmailAddress: email.email_address || '',\r\n                      })) || [],\r\n                    to_PhoneNumber:\r\n                      addr.phone_numbers?.map((phone: any) => ({\r\n                        PhoneNumber: phone.phone_number || '',\r\n                      })) || [],\r\n                    to_URLAddress:\r\n                      addr.home_page_urls?.map((url: any) => ({\r\n                        WebsiteURL: url.website_url || '',\r\n                      })) || [],\r\n                    to_FaxNumber:\r\n                      addr.fax_numbers?.map((fax: any) => ({\r\n                        FaxNumber: fax.fax_number || '',\r\n                      })) || [],\r\n                    to_MobilePhoneNumber:\r\n                      addr.phone_numbers?.map((phone: any) => ({\r\n                        PhoneNumber: phone.phone_number || '',\r\n                      })) || [],\r\n                  })\r\n                )\r\n              : [],\r\n            to_ContactRelationship: {\r\n              ContactPersonDepartment:\r\n                company?.person_func_and_dept?.contact_person_department || '',\r\n              ContactPersonFunction:\r\n                company?.person_func_and_dept?.contact_person_function || '',\r\n            },\r\n            to_BusinessPartnerRole: [{ BusinessPartnerRole: 'BUP001' }],\r\n          }))\r\n        : [],\r\n    };\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.GlobalNoteForm.invalid) {\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    const value = { ...this.GlobalNoteForm.value };\r\n\r\n    const data = {\r\n      note: value?.note,\r\n      bp_id: this?.id,\r\n      ...(!this.NoteDetails.documentId ? { is_global_note: true } : {}),\r\n    };\r\n\r\n    const apiCall =\r\n      this.NoteDetails && this.NoteDetails.documentId\r\n        ? this.prospectsservice.updateNote(this.NoteDetails.documentId, data) // Update if exists\r\n        : this.prospectsservice.createNote(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Prospect Note Updated successFully!',\r\n        });\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 1000);\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/prospects']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\"\r\n            (onChange)=\"onActionChange($event)\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold'\" />\r\n    </div>\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\"\r\n                            routerLinkActive=\"active-tab\">\r\n                            {{ tab.label }}\r\n                        </a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">{{prospectDetails?.bp_full_name || \"-\"}}</h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">CRM\r\n                                                ID</span> :\r\n                                            {{prospectDetails?.bp_id || \"-\"}}</li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">S4/HANA\r\n                                                ID</span> :\r\n                                            152ASD5585</li> -->\r\n                                        <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Account\r\n                                                Owner </span> :\r\n                                            {{prospectDetails?.account_owner || \"-\"}}</li>\r\n                                        <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Main\r\n                                                Contact</span> :\r\n                                            {{\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i> Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{prospectDetails?.contact_companies?.[0]?.business_partner_person?.contact_person_addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i>\r\n                                        Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                            class=\"material-symbols-rounded\">language</i> Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                    <form [formGroup]=\"GlobalNoteForm\">\r\n                        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden mt-5 p-3\">\r\n                            <h5 class=\"mb-3 font-semibold text-primary\">Global Note</h5>\r\n                            <div class=\"flex flex-column gap-3\">\r\n                                <textarea formControlName=\"note\" rows=\"4\" class=\"w-full p-2 border-1 border-round\"\r\n                                    placeholder=\"Enter your note here...\"></textarea>\r\n                                <button pButton type=\"button\" (click)=\"onNoteSubmit()\" label=\"Save Note\"\r\n                                    class=\"p-button-rounded justify-content-center w-8rem h-2rem\"></button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n\r\n                <!-- <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n\r\n                    <router-outlet></router-outlet>\r\n                </div> -->\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": ";AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICajBC,EAAA,CAAAC,cAAA,YAEkC;IAC9BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IAG5BN,EAAA,CAAAO,SAAA,EACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACJ;;;;;IANRT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAOpCX,EAAA,CAAAG,YAAA,EAAa;;;IARwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADGnG,OAAM,MAAOQ,yBAAyB;EAsBpCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,mBAAwC,EACxCC,gBAAkC;IALlC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA3BlB,KAAAC,YAAY,GAAG,IAAItB,OAAO,EAAQ;IACnC,KAAAuB,eAAe,GAAQ,IAAI;IAC3B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAoB,IAAI;IAElC,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,OAAO,GAAc,EAAE;IAEvB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAC,cAAc,GAAc,IAAI,CAACjB,WAAW,CAACkB,KAAK,CAAC;MACxDC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,EAAE,GAAG,IAAI,CAACd,KAAK,CAACwB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACrB,gBAAgB,CAClBsB,aAAa,CAAC,IAAI,CAACb,EAAE,CAAC,CACtBc,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACtB,WAAW,GAAGsB,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;QAC1C,IAAI,CAACZ,cAAc,CAACa,UAAU,CAAC;UAC7BX,IAAI,EAAES,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEV;SAC1B,CAAC;MACJ,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;IAEJ,IAAI,CAACnB,OAAO,GAAG,CAAC;MAAEqB,IAAI,EAAE,qBAAqB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAAC;IAC5D,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE/C,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACgD,aAAa,CAAC,IAAI,CAAC1B,EAAE,CAAC;IAE3B,IAAI,IAAI,CAACH,KAAK,CAAC8B,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC7B,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAAC+B,mBAAmB,EAAE;IAE1B,IAAI,CAAC1C,KAAK,CAACyB,QAAQ,CAChBG,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAEc,MAAM,IAAI;MACpB,MAAMC,UAAU,GAAGD,MAAM,CAACjB,GAAG,CAAC,IAAI,CAAC;MACnC,IAAIkB,UAAU,EAAE;QACd,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC;IACF,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC3C,MAAM,CAAC6C,MAAM,CAAClB,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAACuB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACa,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAF,aAAaA,CAAC1B,EAAU;IACtB,IAAI,CAACH,KAAK,GAAG,CACX;MAAEhB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE;IACxE;IACA;IACA;IACA;IACA;MACEnB,KAAK,EAAE,mBAAmB;MAC1BH,UAAU,EAAE,oBAAoBsB,EAAE;KACnC,EACD;MACEnB,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,oBAAoBsB,EAAE;KACnC,EACD;MAAEnB,KAAK,EAAE,OAAO;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAQ,CAAE,EAC9D;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE,CACzE;EACH;EAEA4B,mBAAmBA,CAAA;IACjB,MAAMK,QAAQ,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAACxC,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMW,UAAU,GAAG,IAAI,CAACzC,KAAK,CAAC0C,SAAS,CAAEC,GAAG,IAC1CA,GAAG,CAAC9D,UAAU,CAAC+D,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAACjC,WAAW,GAAGoC,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,IAAI,CAACK,WAAW,CAAC,IAAI,IAAI,CAACL,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAAC6C,gBAAgB,CAAC,IAAI,CAAC5C,UAAU,EAAEjB,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA6D,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC5C,eAAe,GAAG,CACrB;MAAElB,KAAK,EAAE,WAAW;MAAEH,UAAU,EAAE,CAAC,kBAAkB;IAAC,CAAE,EACxD;MAAEG,KAAK,EAAE8D,SAAS;MAAEjE,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAkE,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAAChD,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACzB,WAAW,GAAG2C,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAAClD,KAAK,CAAC,IAAI,CAACK,WAAW,CAAC;IAEhD,IAAI6C,WAAW,EAAErE,UAAU,EAAE;MAC3B,IAAI,CAACS,MAAM,CAAC6D,aAAa,CAACD,WAAW,CAACrE,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQqD,gBAAgBA,CAACD,UAAkB;IACzC,IAAI,CAACvC,gBAAgB,CAClB0D,eAAe,CAACnB,UAAU,CAAC,CAC3BhB,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACxB,eAAe,GAAGwB,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;QAChD,IAAI,CAACxB,cAAc,GAAG,IAAI,CAACwD,oBAAoB,CAC7CjC,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEiC,SAAS,IAAI,EAAE,CACnC;MACH,CAAC;MACD/B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQ8B,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,cAAcA,CAAC5B,KAAU;IACvB,IAAIA,KAAK,CAAC6B,KAAK,EAAEnD,IAAI,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACjC,mBAAmB,CAACqF,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjBpD,IAAI,EAAE,4BAA4B;QAClCqD,MAAM,EAAEA,CAAA,KAAK;UACX,IAAI,CAACC,iBAAiB,CAAClC,KAAK,CAAC;QAC/B;OACD,CAAC;IACJ;EACF;EAEAkC,iBAAiBA,CAAClC,KAAU;IAC1B,IAAI,CAACjD,YAAY,GAAG,IAAI,CAACoF,2BAA2B,CAAC,IAAI,CAACvF,eAAe,CAAC;IAC1E4B,OAAO,CAAC4D,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACrF,YAAY,CAAC;IAChD,IAAI,CAACL,gBAAgB,CAClB2F,UAAU,CAAC,IAAI,CAACtF,YAAY,CAAC,CAC7BkB,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClCuB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC7B,cAAc,CAAC+F,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDjE,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAChC,cAAc,CAAC+F,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EACJjE,KAAK,EAAEA,KAAK,EAAEwD,OAAO,IAAI;SAC5B,CAAC;QACFvD,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEA4D,2BAA2BA,CAAC9D,IAAS;IACnC,OAAO;MACLoE,eAAe,EAAEpE,IAAI,EAAEqE,KAAK,IAAI,EAAE;MAClCC,mBAAmB,EAAEtE,IAAI,EAAEuE,YAAY,IAAI,EAAE;MAC7CC,yBAAyB,EACvBxE,IAAI,EAAEiC,SAAS,EAAEO,GAAG,CAAEL,OAAY,KAAM;QACtCsC,OAAO,EAAEtC,OAAO,CAACuC,YAAY,IAAI,IAAI;QACrCC,MAAM,EAAExC,OAAO,CAACS,MAAM,IAAI,EAAE;QAC5BgC,WAAW,EAAEzC,OAAO,CAACM,YAAY,IAAI,EAAE;QACvCoC,0BAA0B,EACxB1C,OAAO,CAAC2C,6BAA6B,IAAI,EAAE;QAC7CC,0BAA0B,EACxB5C,OAAO,CAAC6C,6BAA6B,IAAI,EAAE;QAC7CC,UAAU,EAAE9C,OAAO,CAACO,WAAW,IAAI,EAAE;QACrCwC,UAAU,EAAE/C,OAAO,CAACW,WAAW,IAAI,EAAE;QACrCqC,QAAQ,EAAEhD,OAAO,CAACQ,SAAS,IAAI,EAAE;QACjCyC,QAAQ,EAAE,IAAI;QACd,IAAIjD,OAAO,CAACC,cAAc,EAAE3B,MAAM,GAC9B;UACE4E,eAAe,EAAElD,OAAO,CAACC,cAAc,CAACI,GAAG,CAAEF,KAAU,KAAM;YAC3DgD,YAAY,EAAEhD,KAAK,CAACC,aAAa,IAAI;WACtC,CAAC;SACH,GACD,EAAE,CAAC;QACPgD,eAAe,EACbpD,OAAO,CAACe,MAAM,EAAEV,GAAG,CAAEgD,KAAU,KAAM;UACnCC,YAAY,EAAED,KAAK,CAACvC,aAAa,IAAI;SACtC,CAAC,CAAC,IAAI,EAAE;QACXyC,cAAc,EACZvD,OAAO,CAACiB,aAAa,EAAEZ,GAAG,CAAEmD,KAAU,KAAM;UAC1CC,WAAW,EAAED,KAAK,CAACxC,YAAY,IAAI;SACpC,CAAC,CAAC,IAAI,EAAE;QACX0C,aAAa,EACX1D,OAAO,CAACmB,cAAc,EAAEd,GAAG,CAAExB,GAAQ,KAAM;UACzC8E,UAAU,EAAE9E,GAAG,CAACqC,WAAW,IAAI;SAChC,CAAC,CAAC,IAAI,EAAE;QACX0C,YAAY,EACV5D,OAAO,CAAC6D,WAAW,EAAExD,GAAG,CAAEyD,GAAQ,KAAM;UACtCC,SAAS,EAAED,GAAG,CAACE,UAAU,IAAI;SAC9B,CAAC,CAAC,IAAI,EAAE;QACXC,oBAAoB,EAClBjE,OAAO,CAACiB,aAAa,EAAEZ,GAAG,CAAEmD,KAAU,KAAM;UAC1CC,WAAW,EAAED,KAAK,CAACxC,YAAY,IAAI;SACpC,CAAC,CAAC,IAAI;OACV,CAAC,CAAC,IAAI,EAAE;MAEXkD,sBAAsB,EAAE,CACtB;QAAEC,mBAAmB,EAAE;MAAQ,CAAE,EACjC;QAAEA,mBAAmB,EAAE;MAAQ,CAAE,CAClC;MAEDC,WAAW,EAAEvG,IAAI,EAAEwG,QAAQ,GACvB;QACEC,QAAQ,EAAEzG,IAAI,CAACwG,QAAQ,CAACE,WAAW,IAAI,EAAE;QACzCC,yBAAyB,EACvB3G,IAAI,CAACwG,QAAQ,CAACI,6BAA6B,IAAI,EAAE;QACnDC,oBAAoB,EAAEC,KAAK,CAACC,OAAO,CAAC/G,IAAI,CAACwG,QAAQ,CAACQ,iBAAiB,CAAC,GAChEC,MAAM,CAACC,MAAM,CACXlH,IAAI,CAACwG,QAAQ,CAACQ,iBAAiB,CAACG,MAAM,CACpC,CAACC,GAAQ,EAAEC,SAAc,KAAI;UAC3B,MAAMC,GAAG,GAAG,GAAGD,SAAS,CAACE,kBAAkB,IAAIF,SAAS,CAACG,oBAAoB,IAAIH,SAAS,CAACI,QAAQ,EAAE;UAErG,IAAI,CAACL,GAAG,CAACE,GAAG,CAAC,EAAE;YACbF,GAAG,CAACE,GAAG,CAAC,GAAG;cACTI,iBAAiB,EAAEL,SAAS,CAACE,kBAAkB,IAAI,EAAE;cACrDI,mBAAmB,EACjBN,SAAS,CAACG,oBAAoB,IAAI,EAAE;cACtCI,QAAQ,EAAEP,SAAS,CAACI,QAAQ,IAAI,EAAE;cAClCI,kBAAkB,EAAE,IAAIC,GAAG,EAAE,CAAE;aAChC;UACH;UAEA;UACA9H,IAAI,CAACwG,QAAQ,CAACQ,iBAAiB,CAC5B9E,MAAM,CACJ6F,OAAY,IACXA,OAAO,CAACR,kBAAkB,KACxBF,SAAS,CAACE,kBAAkB,IAC9BQ,OAAO,CAACP,oBAAoB,KAC1BH,SAAS,CAACG,oBAAoB,IAChCO,OAAO,CAACN,QAAQ,KAAKJ,SAAS,CAACI,QAAQ,CAC1C,CACAO,OAAO,CAAED,OAAY,IAAI;YACxBX,GAAG,CAACE,GAAG,CAAC,CAACO,kBAAkB,CAAC5D,GAAG,CAC7BgE,IAAI,CAACC,SAAS,CAAC;cACbC,eAAe,EAAEJ,OAAO,CAACK,gBAAgB,IAAI,EAAE;cAC/CC,gBAAgB,EACdN,OAAO,CAACO,kBAAkB,IAAI;aACjC,CAAC,CACH;UACH,CAAC,CAAC;UAEJ;UACAtI,IAAI,EAAEuI,iBAAiB,EAAEP,OAAO,CAAEQ,OAAY,IAAI;YAChDpB,GAAG,CAACE,GAAG,CAAC,CAACO,kBAAkB,CAAC5D,GAAG,CAC7BgE,IAAI,CAACC,SAAS,CAAC;cACbC,eAAe,EAAE,IAAI;cACrBE,gBAAgB,EAAEG,OAAO,EAAEC,YAAY,IAAI;aAC5C,CAAC,CACH;UACH,CAAC,CAAC;UAEF,OAAOrB,GAAG;QACZ,CAAC,EACD,EAAE,CACH,CACF,CAAC5E,GAAG,CAAEkG,IAAS,KAAM;UACpB,GAAGA,IAAI;UACPb,kBAAkB,EAAEf,KAAK,CAAC6B,IAAI,CAACD,IAAI,CAACb,kBAAkB,CAAC,CAACrF,GAAG,CACxDoG,EAAO,IAAKX,IAAI,CAACY,KAAK,CAACD,EAAE,CAAC;SAE9B,CAAC,CAAC,GACH;OACL,GACD,IAAI;MAERE,yBAAyB,EAAEhC,KAAK,CAACC,OAAO,CAAC/G,IAAI,EAAEuI,iBAAiB,CAAC,GAC7DvI,IAAI,CAACuI,iBAAiB,CAAC/F,GAAG,CAAEgG,OAAY,KAAM;QAC5CO,qBAAqB,EAAEP,OAAO,EAAEC,YAAY,IAAI,EAAE;QAClDO,SAAS,EAAER,OAAO,EAAES,uBAAuB,EAAEC,UAAU,IAAI,EAAE;QAC7DC,UAAU,EAAEX,OAAO,EAAES,uBAAuB,EAAEG,WAAW,IAAI,EAAE;QAC/DC,QAAQ,EAAEb,OAAO,EAAES,uBAAuB,EAAEK,SAAS,IAAI,EAAE;QAC3DC,iBAAiB,EAAEzC,KAAK,CAACC,OAAO,CAC9ByB,OAAO,EAAES,uBAAuB,EAAEO,wBAAwB,CAC3D,GACGhB,OAAO,CAACS,uBAAuB,CAACO,wBAAwB,CAAChH,GAAG,CACzDiH,IAAS,KAAM;UACdhF,OAAO,EAAEgF,IAAI,CAAC/E,YAAY,IAAI,IAAI;UAClCC,MAAM,EAAE8E,IAAI,CAAC7G,MAAM,IAAI,EAAE;UACzBgC,WAAW,EAAE6E,IAAI,CAAChH,YAAY,IAAI,EAAE;UACpCoC,0BAA0B,EACxB4E,IAAI,CAAC3E,6BAA6B,IAAI,EAAE;UAC1CC,0BAA0B,EACxB0E,IAAI,CAACzE,6BAA6B,IAAI,EAAE;UAC1CC,UAAU,EAAEwE,IAAI,CAAC/G,WAAW,IAAI,EAAE;UAClCwC,UAAU,EAAEuE,IAAI,CAAC3G,WAAW,IAAI,EAAE;UAClCqC,QAAQ,EAAEsE,IAAI,CAAC9G,SAAS,IAAI,EAAE;UAC9ByC,QAAQ,EAAE,IAAI;UACd,IAAI0B,KAAK,CAACC,OAAO,CAAC0C,IAAI,CAACpE,eAAe,CAAC,IACvCoE,IAAI,CAACpE,eAAe,CAAC5E,MAAM,GAAG,CAAC,GAC3B;YACE4E,eAAe,EAAEoE,IAAI,CAACpE,eAAe,CAAC7C,GAAG,CACtCF,KAAU,KAAM;cACfgD,YAAY,EAAEhD,KAAK,CAACgD,YAAY,IAAI;aACrC,CAAC;WAEL,GACD,EAAE,CAAC;UACPC,eAAe,EACbkE,IAAI,CAACvG,MAAM,EAAEV,GAAG,CAAEgD,KAAU,KAAM;YAChCC,YAAY,EAAED,KAAK,CAACvC,aAAa,IAAI;WACtC,CAAC,CAAC,IAAI,EAAE;UACXyC,cAAc,EACZ+D,IAAI,CAACrG,aAAa,EAAEZ,GAAG,CAAEmD,KAAU,KAAM;YACvCC,WAAW,EAAED,KAAK,CAACxC,YAAY,IAAI;WACpC,CAAC,CAAC,IAAI,EAAE;UACX0C,aAAa,EACX4D,IAAI,CAACnG,cAAc,EAAEd,GAAG,CAAExB,GAAQ,KAAM;YACtC8E,UAAU,EAAE9E,GAAG,CAACqC,WAAW,IAAI;WAChC,CAAC,CAAC,IAAI,EAAE;UACX0C,YAAY,EACV0D,IAAI,CAACzD,WAAW,EAAExD,GAAG,CAAEyD,GAAQ,KAAM;YACnCC,SAAS,EAAED,GAAG,CAACE,UAAU,IAAI;WAC9B,CAAC,CAAC,IAAI,EAAE;UACXC,oBAAoB,EAClBqD,IAAI,CAACrG,aAAa,EAAEZ,GAAG,CAAEmD,KAAU,KAAM;YACvCC,WAAW,EAAED,KAAK,CAACxC,YAAY,IAAI;WACpC,CAAC,CAAC,IAAI;SACV,CAAC,CACH,GACD,EAAE;QACNuG,sBAAsB,EAAE;UACtBC,uBAAuB,EACrBnB,OAAO,EAAEoB,oBAAoB,EAAEC,yBAAyB,IAAI,EAAE;UAChEC,qBAAqB,EACnBtB,OAAO,EAAEoB,oBAAoB,EAAEG,uBAAuB,IAAI;SAC7D;QACD1D,sBAAsB,EAAE,CAAC;UAAEC,mBAAmB,EAAE;QAAQ,CAAE;OAC3D,CAAC,CAAC,GACH;KACL;EACH;EAEM0D,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC/K,SAAS,GAAG,IAAI;MAErB,IAAI+K,KAAI,CAAC7K,cAAc,CAAC+K,OAAO,EAAE;QAC/B;MACF;MACAF,KAAI,CAAC9K,MAAM,GAAG,IAAI;MAClB,MAAMqE,KAAK,GAAG;QAAE,GAAGyG,KAAI,CAAC7K,cAAc,CAACoE;MAAK,CAAE;MAE9C,MAAMxD,IAAI,GAAG;QACXV,IAAI,EAAEkE,KAAK,EAAElE,IAAI;QACjB+E,KAAK,EAAE4F,KAAI,EAAEnL,EAAE;QACf,IAAI,CAACmL,KAAI,CAACxL,WAAW,CAAC2L,UAAU,GAAG;UAAEC,cAAc,EAAE;QAAI,CAAE,GAAG,EAAE;OACjE;MAED,MAAMC,OAAO,GACXL,KAAI,CAACxL,WAAW,IAAIwL,KAAI,CAACxL,WAAW,CAAC2L,UAAU,GAC3CH,KAAI,CAAC5L,gBAAgB,CAACkM,UAAU,CAACN,KAAI,CAACxL,WAAW,CAAC2L,UAAU,EAAEpK,IAAI,CAAC,CAAC;MAAA,EACpEiK,KAAI,CAAC5L,gBAAgB,CAACmM,UAAU,CAACxK,IAAI,CAAC,CAAC,CAAC;MAC9CsK,OAAO,CAAC1K,IAAI,CAAC3C,SAAS,CAACgN,KAAI,CAAC3L,YAAY,CAAC,CAAC,CAACuB,SAAS,CAAC;QACnDC,IAAI,EAAEA,CAAA,KAAK;UACTmK,KAAI,CAAC/L,cAAc,CAAC+F,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFsG,UAAU,CAAC,MAAK;YACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACD1K,KAAK,EAAEA,CAAA,KAAK;UACV+J,KAAI,CAAC9K,MAAM,GAAG,KAAK;UACnB8K,KAAI,CAAC/L,cAAc,CAAC+F,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEA0G,QAAQA,CAAA;IACN,IAAI,CAAC5M,MAAM,CAAC6M,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC9L,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA+L,WAAWA,CAAA;IACT,IAAI,CAAC1M,YAAY,CAACwB,IAAI,EAAE;IACxB,IAAI,CAACxB,YAAY,CAAC2M,QAAQ,EAAE;EAC9B;;;uBAzbWnN,yBAAyB,EAAAZ,EAAA,CAAAgO,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlO,EAAA,CAAAgO,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAnO,EAAA,CAAAgO,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAArO,EAAA,CAAAgO,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAvO,EAAA,CAAAgO,iBAAA,CAAAI,EAAA,CAAAI,mBAAA,GAAAxO,EAAA,CAAAgO,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAzB9N,yBAAyB;MAAA+N,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBtCjP,EAAA,CAAAmP,SAAA,iBAAuD;UAG/CnP,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAAmP,SAAA,sBAA+F;UACnGnP,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAEwF;UAFxDD,EAAA,CAAAoP,gBAAA,2BAAAC,uEAAAC,MAAA;YAAAtP,EAAA,CAAAuP,kBAAA,CAAAL,GAAA,CAAAM,eAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAM,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UACzDtP,EAAA,CAAAyP,UAAA,sBAAAC,kEAAAJ,MAAA;YAAA,OAAYJ,GAAA,CAAA7I,cAAA,CAAAiJ,MAAA,CAAsB;UAAA,EAAC;UAE3CtP,EAHI,CAAAG,YAAA,EAEwF,EACtF;UAGEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAoP,gBAAA,+BAAAO,0EAAAL,MAAA;YAAAtP,EAAA,CAAAuP,kBAAA,CAAAL,GAAA,CAAApN,WAAA,EAAAwN,MAAA,MAAAJ,GAAA,CAAApN,WAAA,GAAAwN,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACtP,EAAA,CAAAyP,UAAA,sBAAAG,iEAAAN,MAAA;YAAA,OAAYJ,GAAA,CAAA1K,WAAA,CAAA8K,MAAA,CAAmB;UAAA,EAAC;UACzFtP,EAAA,CAAAU,UAAA,IAAAmP,+CAAA,wBAAoF;UAW5F7P,EAFI,CAAAG,YAAA,EAAY,EAEV;UASsBH,EAR5B,CAAAC,cAAA,eAAqD,eACjB,eAC+D,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAAAD,EAAA,CAAAE,MAAA,IAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnCH,EAD9C,CAAAC,cAAA,cAAqD,cACP,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAC1D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIAH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBACtD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACRH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBACrD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAMvB;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9EH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAE,MAAA,IACP;UAChBF,EADgB,CAAAG,YAAA,EAAO,EAClB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7CH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAIhFF,EAJgF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,eAC6C,cAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,eAAoC;UAChCD,EAAA,CAAAmP,SAAA,oBACqD;UACrDnP,EAAA,CAAAC,cAAA,kBACkE;UADpCD,EAAA,CAAAyP,UAAA,mBAAAK,4DAAA;YAAA,OAASZ,GAAA,CAAApC,YAAA,EAAc;UAAA,EAAC;UAMtE9M,EALkF,CAAAG,YAAA,EAAS,EACzE,EACJ,EACH,EAEL;UAEFH,EADJ,CAAAC,cAAA,eAAiD,oBAIyB;UAAlED,EAAA,CAAAyP,UAAA,mBAAAM,8DAAA;YAAA,OAASb,GAAA,CAAArB,aAAA,EAAe;UAAA,EAAC;UAH7B7N,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAmP,SAAA,qBAA+B;UAWnDnP,EAVgB,CAAAG,YAAA,EAAM,EAMJ,EACJ,EAEJ,EACJ;UACNH,EAAA,CAAAmP,SAAA,uBAAmC;;;UA5HJnP,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAA8O,GAAA,CAAAvN,eAAA,CAAyB,SAAAuN,GAAA,CAAA9L,IAAA,CAAc,uCAAuC;UAEpFpD,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAA8O,GAAA,CAAArN,OAAA,CAAmB;UAAC7B,EAAA,CAAAgQ,gBAAA,YAAAd,GAAA,CAAAM,eAAA,CAA6B;UAEzDxP,EAAA,CAAAI,UAAA,kFAAiF;UAItEJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAgQ,gBAAA,gBAAAd,GAAA,CAAApN,WAAA,CAA6B;UAC5B9B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAA8O,GAAA,CAAAzN,KAAA,CAAU;UAcczB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAiQ,WAAA,iBAAAf,GAAA,CAAAnN,eAAA,CAAsC;UASlC/B,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAAgG,YAAA,SAAwC;UAGtDrH,EAAA,CAAAO,SAAA,GACmB;UADnBP,EAAA,CAAAQ,kBAAA,SAAA0O,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAA8F,KAAA,aACmB;UAKfnH,EAAA,CAAAO,SAAA,GACuB;UADvBP,EAAA,CAAAQ,kBAAA,SAAA0O,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAA8O,aAAA,aACuB;UAEtBnQ,EAAA,CAAAO,SAAA,GAMvB;UANuBP,EAAA,CAAAQ,kBAAA,UAAA0O,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,kBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,qBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,kBAAAmD,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAC,UAAA,oBAAAkD,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,kBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,qBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,kBAAAmD,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAK,SAAA,eAMvB;UAUiBpM,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA5N,cAAA,kBAAA4N,GAAA,CAAA5N,cAAA,qBAAA4N,GAAA,CAAA5N,cAAA,IAAA2D,OAAA,SAAuC;UAKvCjF,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA5N,cAAA,kBAAA4N,GAAA,CAAA5N,cAAA,qBAAA4N,GAAA,CAAA5N,cAAA,IAAA2E,YAAA,SAA4C;UAM9CjG,EAAA,CAAAO,SAAA,GACP;UADOP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA7N,eAAA,kBAAA6N,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,kBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,qBAAA6D,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,kBAAAmD,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,kBAAA4C,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,qBAAA4C,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAApG,aAAA,kBAAAgJ,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAApG,aAAA,qBAAAgJ,GAAA,CAAA7N,eAAA,CAAAgK,iBAAA,IAAAU,uBAAA,CAAAO,wBAAA,IAAApG,aAAA,IAAAD,YAAA,SACP;UAMSjG,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA5N,cAAA,kBAAA4N,GAAA,CAAA5N,cAAA,qBAAA4N,GAAA,CAAA5N,cAAA,IAAAyE,aAAA,SAA6C;UAK7C/F,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAkQ,iBAAA,EAAAhB,GAAA,CAAA5N,cAAA,kBAAA4N,GAAA,CAAA5N,cAAA,qBAAA4N,GAAA,CAAA5N,cAAA,IAAA6E,WAAA,SAA2C;UAK1EnG,EAAA,CAAAO,SAAA,EAA4B;UAA5BP,EAAA,CAAAI,UAAA,cAAA8O,GAAA,CAAAhN,cAAA,CAA4B;UAiBJlC,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAiQ,WAAA,gBAAAf,GAAA,CAAAnN,eAAA,CAAqC;UAF/D/B,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}