{"ast": null, "code": "import { HttpEventType, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, takeUntil, of, Subject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ImportService {\n  constructor(http) {\n    this.http = http;\n    this.uploadSubject = new BehaviorSubject(null);\n    this.flexupload = this.uploadSubject.asObservable();\n    this.unsubscribe$ = new Subject();\n  }\n  save(apiurl, data) {\n    return this.http.post(apiurl, data, {\n      reportProgress: true,\n      observe: 'events'\n    }).pipe(map(event => {\n      switch (event.type) {\n        case HttpEventType.UploadProgress:\n          const progress = Math.round(event.loaded / (event.total || 1) * 100);\n          return {\n            status: 'PROGRESS',\n            progress\n          };\n        case HttpEventType.Response:\n          return {\n            status: 'COMPLETED',\n            body: event.body\n          };\n        default:\n          return {\n            status: 'UNKNOWN',\n            event\n          };\n      }\n    }));\n  }\n  getProgessStatus(apiurl, table_name) {\n    const params = new HttpParams().set('filters[table_name]', table_name).set('sort[0]', 'createdAt:desc').set('pagination[limit]', '1');\n    return this.http.get(apiurl, {\n      params\n    });\n  }\n  getFilelog(apiurl, table_name) {\n    const params = new HttpParams().set('sort[0]', 'createdAt:desc').set('filters[table_name]', table_name);\n    return this.http.get(apiurl, {\n      params\n    });\n  }\n  delete(deleteurl) {\n    return this.http.delete(deleteurl);\n  }\n  export(id, exporturl, tabname) {\n    return new Promise((resolve, reject) => {\n      this.http.get(exporturl, {\n        responseType: 'blob'\n      }).pipe(takeUntil(this.unsubscribe$), catchError(error => {\n        reject('Error fetching data: ' + error); // Reject the Promise on error\n        return of(null); // Handle error and return default value\n      })).subscribe(blob => {\n        if (blob) {\n          const file = new Blob([blob], {\n            type: 'application/zip'\n          });\n          const fileURL = URL.createObjectURL(file);\n          const a = document.createElement('a');\n          a.href = fileURL;\n          a.download = `${tabname}_${id}.zip`;\n          a.click();\n          URL.revokeObjectURL(fileURL);\n          resolve(blob);\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function ImportService_Factory(t) {\n      return new (t || ImportService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ImportService,\n      factory: ImportService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpEventType", "HttpParams", "BehaviorSubject", "takeUntil", "of", "Subject", "map", "catchError", "ImportService", "constructor", "http", "uploadSubject", "flexupload", "asObservable", "unsubscribe$", "save", "a<PERSON><PERSON><PERSON>", "data", "post", "reportProgress", "observe", "pipe", "event", "type", "UploadProgress", "progress", "Math", "round", "loaded", "total", "status", "Response", "body", "getProgessStatus", "table_name", "params", "set", "get", "getFilelog", "delete", "deleteurl", "export", "id", "exporturl", "tabname", "Promise", "resolve", "reject", "responseType", "error", "subscribe", "blob", "file", "Blob", "fileURL", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\import\\import.service.ts"], "sourcesContent": ["import { HttpClient, HttpEventType, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, takeUntil, of, Subject } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ImportService {\r\n  public uploadSubject = new BehaviorSubject<any>(null);\r\n  public flexupload = this.uploadSubject.asObservable();\r\n  private unsubscribe$ = new Subject<void>();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  save(apiurl: any, data: any) {\r\n    return this.http\r\n      .post(apiurl, data, {\r\n        reportProgress: true,\r\n        observe: 'events',\r\n      })\r\n      .pipe(\r\n        map((event) => {\r\n          switch (event.type) {\r\n            case HttpEventType.UploadProgress:\r\n              const progress = Math.round(\r\n                (event.loaded / (event.total || 1)) * 100\r\n              );\r\n              return { status: 'PROGRESS', progress };\r\n            case HttpEventType.Response:\r\n              return { status: 'COMPLETED', body: event.body };\r\n            default:\r\n              return { status: 'UNKNOWN', event };\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  getProgessStatus(apiurl: string, table_name: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[table_name]', table_name)\r\n      .set('sort[0]', 'createdAt:desc')\r\n      .set('pagination[limit]', '1');\r\n\r\n    return this.http.get<{ progress: number }>(apiurl, { params });\r\n  }\r\n\r\n  getFilelog(apiurl: string, table_name: string) {\r\n    const params = new HttpParams()\r\n      .set('sort[0]', 'createdAt:desc')\r\n      .set('filters[table_name]', table_name);\r\n\r\n    return this.http.get<{ progress: number }>(apiurl, { params });\r\n  }\r\n\r\n  delete(deleteurl: string) {\r\n    return this.http.delete<any>(deleteurl);\r\n  }\r\n\r\n  export(id: number, exporturl: string, tabname: string): Promise<any> {\r\n    return new Promise((resolve, reject) => {\r\n      this.http\r\n        .get(exporturl, { responseType: 'blob' })\r\n        .pipe(\r\n          takeUntil(this.unsubscribe$),\r\n          catchError((error) => {\r\n            reject('Error fetching data: ' + error); // Reject the Promise on error\r\n            return of(null); // Handle error and return default value\r\n          })\r\n        )\r\n        .subscribe((blob) => {\r\n          if (blob) {\r\n            const file = new Blob([blob], { type: 'application/zip' });\r\n            const fileURL = URL.createObjectURL(file);\r\n            const a = document.createElement('a');\r\n            a.href = fileURL;\r\n            a.download = `${tabname}_${id}.zip`;\r\n            a.click();\r\n            URL.revokeObjectURL(fileURL);\r\n            resolve(blob);\r\n          } \r\n        });\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAE5E,SAASC,eAAe,EAAEC,SAAS,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,aAAa;EAKxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJjB,KAAAC,aAAa,GAAG,IAAIT,eAAe,CAAM,IAAI,CAAC;IAC9C,KAAAU,UAAU,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IAC7C,KAAAC,YAAY,GAAG,IAAIT,OAAO,EAAQ;EAEH;EAEvCU,IAAIA,CAACC,MAAW,EAAEC,IAAS;IACzB,OAAO,IAAI,CAACP,IAAI,CACbQ,IAAI,CAACF,MAAM,EAAEC,IAAI,EAAE;MAClBE,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACV,CAAC,CACDC,IAAI,CACHf,GAAG,CAAEgB,KAAK,IAAI;MACZ,QAAQA,KAAK,CAACC,IAAI;QAChB,KAAKvB,aAAa,CAACwB,cAAc;UAC/B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACxBL,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACO,KAAK,IAAI,CAAC,CAAC,GAAI,GAAG,CAC1C;UACD,OAAO;YAAEC,MAAM,EAAE,UAAU;YAAEL;UAAQ,CAAE;QACzC,KAAKzB,aAAa,CAAC+B,QAAQ;UACzB,OAAO;YAAED,MAAM,EAAE,WAAW;YAAEE,IAAI,EAAEV,KAAK,CAACU;UAAI,CAAE;QAClD;UACE,OAAO;YAAEF,MAAM,EAAE,SAAS;YAAER;UAAK,CAAE;MACvC;IACF,CAAC,CAAC,CACH;EACL;EAEAW,gBAAgBA,CAACjB,MAAc,EAAEkB,UAAkB;IACjD,MAAMC,MAAM,GAAG,IAAIlC,UAAU,EAAE,CAC5BmC,GAAG,CAAC,qBAAqB,EAAEF,UAAU,CAAC,CACtCE,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAChCA,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC;IAEhC,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAuBrB,MAAM,EAAE;MAAEmB;IAAM,CAAE,CAAC;EAChE;EAEAG,UAAUA,CAACtB,MAAc,EAAEkB,UAAkB;IAC3C,MAAMC,MAAM,GAAG,IAAIlC,UAAU,EAAE,CAC5BmC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAChCA,GAAG,CAAC,qBAAqB,EAAEF,UAAU,CAAC;IAEzC,OAAO,IAAI,CAACxB,IAAI,CAAC2B,GAAG,CAAuBrB,MAAM,EAAE;MAAEmB;IAAM,CAAE,CAAC;EAChE;EAEAI,MAAMA,CAACC,SAAiB;IACtB,OAAO,IAAI,CAAC9B,IAAI,CAAC6B,MAAM,CAAMC,SAAS,CAAC;EACzC;EAEAC,MAAMA,CAACC,EAAU,EAAEC,SAAiB,EAAEC,OAAe;IACnD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACrC,IAAI,CACN2B,GAAG,CAACM,SAAS,EAAE;QAAEK,YAAY,EAAE;MAAM,CAAE,CAAC,CACxC3B,IAAI,CACHlB,SAAS,CAAC,IAAI,CAACW,YAAY,CAAC,EAC5BP,UAAU,CAAE0C,KAAK,IAAI;QACnBF,MAAM,CAAC,uBAAuB,GAAGE,KAAK,CAAC,CAAC,CAAC;QACzC,OAAO7C,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CACH,CACA8C,SAAS,CAAEC,IAAI,IAAI;QAClB,IAAIA,IAAI,EAAE;UACR,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;YAAE5B,IAAI,EAAE;UAAiB,CAAE,CAAC;UAC1D,MAAM+B,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UACzC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGN,OAAO;UAChBG,CAAC,CAACI,QAAQ,GAAG,GAAGjB,OAAO,IAAIF,EAAE,MAAM;UACnCe,CAAC,CAACK,KAAK,EAAE;UACTP,GAAG,CAACQ,eAAe,CAACT,OAAO,CAAC;UAC5BR,OAAO,CAACK,IAAI,CAAC;QACf;MACF,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;;;uBA3EW3C,aAAa,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb3D,aAAa;MAAA4D,OAAA,EAAb5D,aAAa,CAAA6D,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}