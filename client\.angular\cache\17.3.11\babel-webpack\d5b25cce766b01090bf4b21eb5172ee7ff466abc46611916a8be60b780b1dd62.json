{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ActivitiesService {\n  constructor(http) {\n    this.http = http;\n    this.activitySubject = new BehaviorSubject(null);\n    this.activity = this.activitySubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createActivity(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`, data);\n  }\n  createFollowup(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`, data);\n  }\n  createInvolvedParty(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {\n      data\n    });\n  }\n  createRelatedItem(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_FOLLOWUP_RELATED_ITEMS}`, {\n      data\n    });\n  }\n  updateActivity(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  updateActivityStatus(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  deleteInvolvedParty(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`);\n  }\n  deleteFollowupItem(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_FOLLOWUP_RELATED_ITEMS}/${id}`);\n  }\n  getActivityDropdownOptions(type) {\n    const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getActivities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,start_date,end_date,category');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getSalesCall(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note').set('populate[business_partner][populate][addresses][fields][0]', 'region').set('populate[business_partner][populate][addresses][fields][1]', 'country').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    } else {\n      // Default sort by updatedAt descending\n      params = params.set('sort', 'updatedAt:desc');\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][phone_call_category][$containsi]', searchTerm);\n      params = params.set('filters[$or][3][brand][$containsi]', searchTerm);\n      params = params.set('filters[$or][4][business_partner][bp_full_name][$containsi]', searchTerm);\n    }\n    if (filter) {\n      if (filter === 'MSCT') {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n        params = params.set('filters[createdAt][$gte]', startOfDay).set('filters[createdAt][$lte]', endOfDay);\n      } else if (filter === 'MSCTW') {\n        const now = new Date();\n        // Get the start of the week (Monday)\n        const startOfWeek = new Date(now);\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\n        const diffToMonday = day === 0 ? -6 : 1 - day;\n        startOfWeek.setDate(now.getDate() + diffToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        // Get the end of the week (Sunday)\n        const endOfWeek = new Date(startOfWeek);\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\n        endOfWeek.setHours(23, 59, 59, 999);\n        const startISO = startOfWeek.toISOString();\n        const endISO = endOfWeek.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MSCTM') {\n        const now = new Date();\n        // Start of the month: 1st day at 00:00:00\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n        // End of the month: last day at 23:59:59.999\n        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n        const startISO = startOfMonth.toISOString();\n        const endISO = endOfMonth.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MCSC') {\n        params = params.set('filters[activity_status][$eq]', '3');\n      }\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getActivityByID(activityId) {\n    const params = new HttpParams().set('filters[activity_id][$eq]', activityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner][fields][1]', 'bp_id').set('populate[business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][4]', 'country').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]', 'website_url').set('populate[involved_parties][populate][business_partner][fields][0]', 'bp_full_name').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][0]', 'city_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][1]', 'country').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][2]', 'house_number').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][3]', 'region').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][4]', 'street_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[notes][populate]', '*').set('populate[opportunity_followups][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    }).pipe(map(response => {\n      const activityDetails = response?.data[0] || null;\n      this.activitySubject.next(activityDetails);\n      return response;\n    }));\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      const contact = item?.addresses?.[0];\n      const email = contact?.emails?.[0]?.email_address || '';\n      const mobile = (contact?.phone_numbers || []).filter(item => item.phone_number_type === '3').map(item => item.phone_number);\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || '',\n        email: email,\n        mobile: mobile\n      };\n    })));\n  }\n  getActivityCodeWise(params) {\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getActivity(partnerId) {\n    let params = new HttpParams().set('filters[main_account_party_id][$eq]', partnerId).set('fields', 'subject,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_organizer][fields][0]', 'bp_full_name').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getTodayRange() {\n    const today = new Date();\n    const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n    const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n    return {\n      start,\n      end\n    };\n  }\n  getThisWeekRange() {\n    const now = new Date();\n    const startOfWeek = new Date(now);\n    const day = now.getDay();\n    const diffToMonday = day === 0 ? -6 : 1 - day;\n    startOfWeek.setDate(now.getDate() + diffToMonday);\n    startOfWeek.setHours(0, 0, 0, 0);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n    endOfWeek.setHours(23, 59, 59, 999);\n    return {\n      start: startOfWeek.toISOString(),\n      end: endOfWeek.toISOString()\n    };\n  }\n  getThisMonthRange() {\n    const now = new Date();\n    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n    return {\n      start: start.toISOString(),\n      end: end.toISOString()\n    };\n  }\n  static {\n    this.ɵfac = function ActivitiesService_Factory(t) {\n      return new (t || ActivitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActivitiesService,\n      factory: ActivitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ActivitiesService", "constructor", "http", "activitySubject", "activity", "asObservable", "createNote", "data", "post", "CRM_NOTE", "createActivity", "CRM_ACTIVITY_PHONE_CALL_REGISTRATION", "createFollowup", "CRM_ACTIVITY_FOLLOW_UP_REGISTRATION", "createInvolvedParty", "CRM_INVOLVED_PARTIES", "createRelatedItem", "CRM_FOLLOWUP_RELATED_ITEMS", "updateActivity", "Id", "put", "CRM_ACTIVITY", "updateNote", "updateActivityStatus", "deleteNote", "id", "delete", "deleteInvolvedParty", "deleteFollowupItem", "getActivityDropdownOptions", "type", "params", "set", "get", "CONFIG_DATA", "getActivities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "toString", "undefined", "order", "getSalesCall", "filter", "today", "Date", "startOfDay", "setHours", "toISOString", "endOfDay", "now", "startOfWeek", "day", "getDay", "diffToMonday", "setDate", "getDate", "endOfWeek", "startISO", "endISO", "startOfMonth", "getFullYear", "getMonth", "endOfMonth", "getActivityByID", "activityId", "pipe", "response", "activityDetails", "next", "getPartners", "PARTNERS", "item", "contact", "addresses", "email", "emails", "email_address", "mobile", "phone_numbers", "phone_number_type", "phone_number", "bp_id", "bp_full_name", "getActivityCodeWise", "getActivity", "partnerId", "getTodayRange", "start", "end", "getThisWeekRange", "getThisMonthRange", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ActivitiesService {\r\n  public activitySubject = new BehaviorSubject<any>(null);\r\n  public activity = this.activitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createActivity(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createFollowup(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createInvolvedParty(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createRelatedItem(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_FOLLOWUP_RELATED_ITEMS}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateActivity(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateActivityStatus(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, { data });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  deleteInvolvedParty(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`\r\n    );\r\n  }\r\n\r\n  deleteFollowupItem(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_FOLLOWUP_RELATED_ITEMS}/${id}`\r\n    );\r\n  }\r\n\r\n  getActivityDropdownOptions(type: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', type);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getActivities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,subject,activity_status,start_date,end_date,category'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getSalesCall(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    filter?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt'\r\n      )\r\n      .set('populate[notes][fields][0]', 'note')\r\n      .set('populate[notes][fields][1]', 'is_global_note')\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'country'\r\n      )\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    } else {\r\n      // Default sort by updatedAt descending\r\n      params = params.set('sort', 'updatedAt:desc');\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][2][phone_call_category][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][3][brand][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][4][business_partner][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n    if (filter) {\r\n      if (filter === 'MSCT') {\r\n        const today = new Date();\r\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\r\n        const endOfDay = new Date(\r\n          today.setHours(23, 59, 59, 999)\r\n        ).toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startOfDay)\r\n          .set('filters[createdAt][$lte]', endOfDay);\r\n      } else if (filter === 'MSCTW') {\r\n        const now = new Date();\r\n\r\n        // Get the start of the week (Monday)\r\n        const startOfWeek = new Date(now);\r\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\r\n        const diffToMonday = day === 0 ? -6 : 1 - day;\r\n        startOfWeek.setDate(now.getDate() + diffToMonday);\r\n        startOfWeek.setHours(0, 0, 0, 0);\r\n\r\n        // Get the end of the week (Sunday)\r\n        const endOfWeek = new Date(startOfWeek);\r\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n        endOfWeek.setHours(23, 59, 59, 999);\r\n\r\n        const startISO = startOfWeek.toISOString();\r\n        const endISO = endOfWeek.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MSCTM') {\r\n        const now = new Date();\r\n\r\n        // Start of the month: 1st day at 00:00:00\r\n        const startOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth(),\r\n          1,\r\n          0,\r\n          0,\r\n          0,\r\n          0\r\n        );\r\n\r\n        // End of the month: last day at 23:59:59.999\r\n        const endOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth() + 1,\r\n          0,\r\n          23,\r\n          59,\r\n          59,\r\n          999\r\n        );\r\n\r\n        const startISO = startOfMonth.toISOString();\r\n        const endISO = endOfMonth.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MCSC') {\r\n        params = params.set('filters[activity_status][$eq]', '3');\r\n      }\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getActivityByID(activityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[activity_id][$eq]', activityId)\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner][fields][1]', 'bp_id')\r\n      .set(\r\n        'populate[business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]',\r\n        'first_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]',\r\n        'last_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'house_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'street_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][2]',\r\n        'city_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][3]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][4]',\r\n        'country'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][5]',\r\n        'postal_code'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]',\r\n        'website_url'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][0]',\r\n        'city_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][1]',\r\n        'country'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][2]',\r\n        'house_number'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][3]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][4]',\r\n        'street_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][5]',\r\n        'postal_code'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number_type'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[notes][populate]', '*')\r\n      .set('populate[opportunity_followups][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const activityDetails = response?.data[0] || null;\r\n          this.activitySubject.next(activityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http\r\n      .get<any>(\r\n        `${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`,\r\n        { params }\r\n      )\r\n      .pipe(\r\n        map((response) =>\r\n          (response?.data || []).map((item: any) => {\r\n            const contact = item?.addresses?.[0];\r\n\r\n            const email = contact?.emails?.[0]?.email_address || '';\r\n            const mobile = (contact?.phone_numbers || [])\r\n              .filter((item: any) => item.phone_number_type === '3')\r\n              .map((item: any) => item.phone_number);\r\n\r\n            return {\r\n              bp_id: item?.bp_id || '',\r\n              bp_full_name: item?.bp_full_name || '',\r\n              email: email,\r\n              mobile: mobile,\r\n            };\r\n          })\r\n        )\r\n      );\r\n  }\r\n\r\n  getActivityCodeWise(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getActivity(partnerId: string): Observable<{ data: any[]; meta: any }> {\r\n    let params = new HttpParams()\r\n      .set('filters[main_account_party_id][$eq]', partnerId)\r\n      .set(\r\n        'fields',\r\n        'subject,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number_type'\r\n      )\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_organizer][fields][0]', 'bp_full_name')\r\n      .set('populate[notes][fields][0]', 'note')\r\n      .set('populate[notes][fields][1]', 'is_global_note');\r\n\r\n    return this.http.get<{ data: any[]; meta: any }>(\r\n      `${CMS_APIContstant.CRM_ACTIVITY}`,\r\n      { params }\r\n    );\r\n  }\r\n\r\n  getTodayRange() {\r\n    const today = new Date();\r\n    const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();\r\n    const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();\r\n    return { start, end };\r\n  }\r\n\r\n  getThisWeekRange() {\r\n    const now = new Date();\r\n    const startOfWeek = new Date(now);\r\n    const day = now.getDay();\r\n    const diffToMonday = day === 0 ? -6 : 1 - day;\r\n    startOfWeek.setDate(now.getDate() + diffToMonday);\r\n    startOfWeek.setHours(0, 0, 0, 0);\r\n\r\n    const endOfWeek = new Date(startOfWeek);\r\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n    endOfWeek.setHours(23, 59, 59, 999);\r\n\r\n    return {\r\n      start: startOfWeek.toISOString(),\r\n      end: endOfWeek.toISOString(),\r\n    };\r\n  }\r\n\r\n  getThisMonthRange() {\r\n    const now = new Date();\r\n    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\r\n    const end = new Date(\r\n      now.getFullYear(),\r\n      now.getMonth() + 1,\r\n      0,\r\n      23,\r\n      59,\r\n      59,\r\n      999\r\n    );\r\n    return {\r\n      start: start.toISOString(),\r\n      end: end.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,iBAAiB;EAI5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,eAAe,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAO,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEd;EAEvCC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,cAAcA,CAACH,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAGT,gBAAgB,CAACY,oCAAoC,EAAE,EAC1DJ,IAAI,CACL;EACH;EAEAK,cAAcA,CAACL,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAGT,gBAAgB,CAACc,mCAAmC,EAAE,EACzDN,IAAI,CACL;EACH;EAEAO,mBAAmBA,CAACP,IAAS;IAC3B,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACgB,oBAAoB,EAAE,EAAE;MAChER;KACD,CAAC;EACJ;EAEAS,iBAAiBA,CAACT,IAAS;IACzB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACkB,0BAA0B,EAAE,EAAE;MACtEV;KACD,CAAC;EACJ;EAEAW,cAAcA,CAACC,EAAU,EAAEZ,IAAS;IAClC,OAAO,IAAI,CAACL,IAAI,CAACkB,GAAG,CAAC,GAAGrB,gBAAgB,CAACsB,YAAY,IAAIF,EAAE,EAAE,EAAE;MAC7DZ;KACD,CAAC;EACJ;EAEAe,UAAUA,CAACH,EAAU,EAAEZ,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACkB,GAAG,CAAC,GAAGrB,gBAAgB,CAACU,QAAQ,IAAIU,EAAE,EAAE,EAAE;MACzDZ;KACD,CAAC;EACJ;EAEAgB,oBAAoBA,CAACJ,EAAU,EAAEZ,IAAS;IACxC,OAAO,IAAI,CAACL,IAAI,CAACkB,GAAG,CAAC,GAAGrB,gBAAgB,CAACsB,YAAY,IAAIF,EAAE,EAAE,EAAE;MAAEZ;IAAI,CAAE,CAAC;EAC1E;EAEAiB,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACvB,IAAI,CAACwB,MAAM,CAAM,GAAG3B,gBAAgB,CAACU,QAAQ,IAAIgB,EAAE,EAAE,CAAC;EACpE;EAEAE,mBAAmBA,CAACF,EAAU;IAC5B,OAAO,IAAI,CAACvB,IAAI,CAACwB,MAAM,CACrB,GAAG3B,gBAAgB,CAACgB,oBAAoB,IAAIU,EAAE,EAAE,CACjD;EACH;EAEAG,kBAAkBA,CAACH,EAAU;IAC3B,OAAO,IAAI,CAACvB,IAAI,CAACwB,MAAM,CACrB,GAAG3B,gBAAgB,CAACkB,0BAA0B,IAAIQ,EAAE,EAAE,CACvD;EACH;EAEAI,0BAA0BA,CAACC,IAAY;IACrC,MAAMC,MAAM,GAAG,IAAInC,UAAU,EAAE,CAC5BoC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;IAElC,OAAO,IAAI,CAAC5B,IAAI,CAAC+B,GAAG,CAAM,GAAGlC,gBAAgB,CAACmC,WAAW,EAAE,EAAE;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAI,aAAaA,CACXC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIT,MAAM,GAAG,IAAInC,UAAU,EAAE,CAC1BoC,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,4EAA4E,CAC7E;IAEH,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIH,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEQ,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAACtC,IAAI,CAAC+B,GAAG,CAAQ,GAAGlC,gBAAgB,CAACsB,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAa,YAAYA,CACVR,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBK,MAAe;IAEf,IAAId,MAAM,GAAG,IAAInC,UAAU,EAAE,CAC1BoC,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,iKAAiK,CAClK,CACAA,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CACzCA,GAAG,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,CACnDA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD,CAAC,MAAM;MACL;MACAZ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAC/C;IAEA,IAAIQ,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,kDAAkD,EAClDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,UAAU,CAAC;MACrET,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6DAA6D,EAC7DQ,UAAU,CACX;IACH;IACA,IAAIK,MAAM,EAAE;MACV,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;QACrE,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CACvBD,KAAK,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAChC,CAACC,WAAW,EAAE;QAEfnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAEgB,UAAU,CAAC,CAC3ChB,GAAG,CAAC,0BAA0B,EAAEmB,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAIN,MAAM,KAAK,OAAO,EAAE;QAC7B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMM,WAAW,GAAG,IAAIN,IAAI,CAACK,GAAG,CAAC;QACjC,MAAME,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAE,CAAC,CAAC;QAC1B,MAAMC,YAAY,GAAGF,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG;QAC7CD,WAAW,CAACI,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGF,YAAY,CAAC;QACjDH,WAAW,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEhC;QACA,MAAMU,SAAS,GAAG,IAAIZ,IAAI,CAACM,WAAW,CAAC;QACvCM,SAAS,CAACF,OAAO,CAACJ,WAAW,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5CC,SAAS,CAACV,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;QAEnC,MAAMW,QAAQ,GAAGP,WAAW,CAACH,WAAW,EAAE;QAC1C,MAAMW,MAAM,GAAGF,SAAS,CAACT,WAAW,EAAE;QAEtCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,OAAO,EAAE;QAC7B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMe,YAAY,GAAG,IAAIf,IAAI,CAC3BK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,EACd,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CACF;QAED;QACA,MAAMC,UAAU,GAAG,IAAIlB,IAAI,CACzBK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,GAAG,CAAC,EAClB,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,CACJ;QAED,MAAMJ,QAAQ,GAAGE,YAAY,CAACZ,WAAW,EAAE;QAC3C,MAAMW,MAAM,GAAGI,UAAU,CAACf,WAAW,EAAE;QAEvCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,MAAM,EAAE;QAC5Bd,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC;MAC3D;IACF;IAEA,OAAO,IAAI,CAAC9B,IAAI,CAAC+B,GAAG,CAAQ,GAAGlC,gBAAgB,CAACsB,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAmC,eAAeA,CAACC,UAAkB;IAChC,MAAMpC,MAAM,GAAG,IAAInC,UAAU,EAAE,CAC5BoC,GAAG,CAAC,2BAA2B,EAAEmC,UAAU,CAAC,CAC5CnC,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,CACrDA,GAAG,CACF,wFAAwF,EACxF,kBAAkB,CACnB,CACAA,GAAG,CACF,uGAAuG,EACvG,YAAY,CACb,CACAA,GAAG,CACF,uGAAuG,EACvG,WAAW,CACZ,CACAA,GAAG,CACF,sFAAsF,EACtF,eAAe,CAChB,CACAA,GAAG,CACF,4DAA4D,EAC5D,cAAc,CACf,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,4DAA4D,EAC5D,WAAW,CACZ,CACAA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,8EAA8E,EAC9E,eAAe,CAChB,CACAA,GAAG,CACF,qFAAqF,EACrF,cAAc,CACf,CACAA,GAAG,CACF,sFAAsF,EACtF,aAAa,CACd,CACAA,GAAG,CACF,mEAAmE,EACnE,cAAc,CACf,CACAA,GAAG,CACF,kHAAkH,EAClH,eAAe,CAChB,CACAA,GAAG,CACF,wFAAwF,EACxF,WAAW,CACZ,CACAA,GAAG,CACF,wFAAwF,EACxF,SAAS,CACV,CACAA,GAAG,CACF,wFAAwF,EACxF,cAAc,CACf,CACAA,GAAG,CACF,wFAAwF,EACxF,QAAQ,CACT,CACAA,GAAG,CACF,wFAAwF,EACxF,aAAa,CACd,CACAA,GAAG,CACF,wFAAwF,EACxF,aAAa,CACd,CACAA,GAAG,CACF,0GAA0G,EAC1G,eAAe,CAChB,CACAA,GAAG,CACF,iHAAiH,EACjH,cAAc,CACf,CACAA,GAAG,CACF,iHAAiH,EACjH,mBAAmB,CACpB,CACAA,GAAG,CACF,oHAAoH,EACpH,kBAAkB,CACnB,CACAA,GAAG,CACF,+JAA+J,EAC/J,kBAAkB,CACnB,CACAA,GAAG,CACF,2LAA2L,EAC3L,cAAc,CACf,CACAA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC,CAClEA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CAAC,2CAA2C,EAAE,GAAG,CAAC;IAExD,OAAO,IAAI,CAAC9B,IAAI,CACb+B,GAAG,CAAQ,GAAGlC,gBAAgB,CAACsB,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC,CAC1DqC,IAAI,CACHtE,GAAG,CAAEuE,QAAa,IAAI;MACpB,MAAMC,eAAe,GAAGD,QAAQ,EAAE9D,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACjD,IAAI,CAACJ,eAAe,CAACoE,IAAI,CAACD,eAAe,CAAC;MAC1C,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,WAAWA,CAACzC,MAAW;IACrB,OAAO,IAAI,CAAC7B,IAAI,CACb+B,GAAG,CACF,GAAGlC,gBAAgB,CAAC0E,QAAQ,qEAAqE,EACjG;MAAE1C;IAAM,CAAE,CACX,CACAqC,IAAI,CACHtE,GAAG,CAAEuE,QAAQ,IACX,CAACA,QAAQ,EAAE9D,IAAI,IAAI,EAAE,EAAET,GAAG,CAAE4E,IAAS,IAAI;MACvC,MAAMC,OAAO,GAAGD,IAAI,EAAEE,SAAS,GAAG,CAAC,CAAC;MAEpC,MAAMC,KAAK,GAAGF,OAAO,EAAEG,MAAM,GAAG,CAAC,CAAC,EAAEC,aAAa,IAAI,EAAE;MACvD,MAAMC,MAAM,GAAG,CAACL,OAAO,EAAEM,aAAa,IAAI,EAAE,EACzCpC,MAAM,CAAE6B,IAAS,IAAKA,IAAI,CAACQ,iBAAiB,KAAK,GAAG,CAAC,CACrDpF,GAAG,CAAE4E,IAAS,IAAKA,IAAI,CAACS,YAAY,CAAC;MAExC,OAAO;QACLC,KAAK,EAAEV,IAAI,EAAEU,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEX,IAAI,EAAEW,YAAY,IAAI,EAAE;QACtCR,KAAK,EAAEA,KAAK;QACZG,MAAM,EAAEA;OACT;IACH,CAAC,CAAC,CACH,CACF;EACL;EAEAM,mBAAmBA,CAACvD,MAAW;IAC7B,OAAO,IAAI,CAAC7B,IAAI,CAAC+B,GAAG,CAAM,GAAGlC,gBAAgB,CAACsB,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC3E;EAEAwD,WAAWA,CAACC,SAAiB;IAC3B,IAAIzD,MAAM,GAAG,IAAInC,UAAU,EAAE,CAC1BoC,GAAG,CAAC,qCAAqC,EAAEwD,SAAS,CAAC,CACrDxD,GAAG,CACF,QAAQ,EACR,kGAAkG,CACnG,CACAA,GAAG,CACF,qFAAqF,EACrF,cAAc,CACf,CACAA,GAAG,CACF,qFAAqF,EACrF,mBAAmB,CACpB,CACAA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,iDAAiD,EAAE,cAAc,CAAC,CACtEA,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CACzCA,GAAG,CAAC,4BAA4B,EAAE,gBAAgB,CAAC;IAEtD,OAAO,IAAI,CAAC9B,IAAI,CAAC+B,GAAG,CAClB,GAAGlC,gBAAgB,CAACsB,YAAY,EAAE,EAClC;MAAEU;IAAM,CAAE,CACX;EACH;EAEA0D,aAAaA,CAAA;IACX,MAAM3C,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAM2C,KAAK,GAAG,IAAI3C,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;IAChE,MAAMyC,GAAG,GAAG,IAAI5C,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAACC,WAAW,EAAE;IACnE,OAAO;MAAEwC,KAAK;MAAEC;IAAG,CAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,MAAMxC,GAAG,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAMM,WAAW,GAAG,IAAIN,IAAI,CAACK,GAAG,CAAC;IACjC,MAAME,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAE;IACxB,MAAMC,YAAY,GAAGF,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG;IAC7CD,WAAW,CAACI,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGF,YAAY,CAAC;IACjDH,WAAW,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEhC,MAAMU,SAAS,GAAG,IAAIZ,IAAI,CAACM,WAAW,CAAC;IACvCM,SAAS,CAACF,OAAO,CAACJ,WAAW,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5CC,SAAS,CAACV,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAEnC,OAAO;MACLyC,KAAK,EAAErC,WAAW,CAACH,WAAW,EAAE;MAChCyC,GAAG,EAAEhC,SAAS,CAACT,WAAW;KAC3B;EACH;EAEA2C,iBAAiBA,CAAA;IACf,MAAMzC,GAAG,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAM2C,KAAK,GAAG,IAAI3C,IAAI,CAACK,GAAG,CAACW,WAAW,EAAE,EAAEX,GAAG,CAACY,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxE,MAAM2B,GAAG,GAAG,IAAI5C,IAAI,CAClBK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,GAAG,CAAC,EAClB,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,CACJ;IACD,OAAO;MACL0B,KAAK,EAAEA,KAAK,CAACxC,WAAW,EAAE;MAC1ByC,GAAG,EAAEA,GAAG,CAACzC,WAAW;KACrB;EACH;;;uBA5cWlD,iBAAiB,EAAA8F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBjG,iBAAiB;MAAAkG,OAAA,EAAjBlG,iBAAiB,CAAAmG,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}