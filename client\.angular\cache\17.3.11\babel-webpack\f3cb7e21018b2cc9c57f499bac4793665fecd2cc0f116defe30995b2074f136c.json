{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/table\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/button\";\nfunction ActivitiesSalesTeamComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \" First Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 13)(6, \"div\", 11);\n    i0.ɵɵtext(7, \" Last Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 15)(10, \"div\", 11);\n    i0.ɵɵtext(11, \" Role \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 17)(14, \"div\", 11);\n    i0.ɵɵtext(15, \" Email \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 19)(18, \"div\", 11);\n    i0.ɵɵtext(19, \" Phone \");\n    i0.ɵɵelement(20, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivitiesSalesTeamComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const team_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.bp_identification == null ? null : team_r1.bp_identification.business_partner == null ? null : team_r1.bp_identification.business_partner.first_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.bp_identification == null ? null : team_r1.bp_identification.business_partner == null ? null : team_r1.bp_identification.business_partner.last_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.partner_role) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.bp_identification == null ? null : team_r1.bp_identification.business_partner == null ? null : team_r1.bp_identification.business_partner.addresses == null ? null : team_r1.bp_identification.business_partner.addresses[0] == null ? null : team_r1.bp_identification.business_partner.addresses[0].emails == null ? null : team_r1.bp_identification.business_partner.addresses[0].emails[0] == null ? null : team_r1.bp_identification.business_partner.addresses[0].emails[0].email_address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.bp_identification == null ? null : team_r1.bp_identification.business_partner == null ? null : team_r1.bp_identification.business_partner.addresses == null ? null : team_r1.bp_identification.business_partner.addresses[0] == null ? null : team_r1.bp_identification.business_partner.addresses[0].phone_numbers == null ? null : team_r1.bp_identification.business_partner.addresses[0].phone_numbers[0] == null ? null : team_r1.bp_identification.business_partner.addresses[0].phone_numbers[0].phone_number) || \"-\", \" \");\n  }\n}\nfunction ActivitiesSalesTeamComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2, \"No Sales Teams found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActivitiesSalesTeamComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2, \"Loading Sales Teams data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ActivitiesSalesTeamComponent {\n  constructor() {\n    this.salesteamDetails = null;\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ActivitiesSalesTeamComponent_Factory(t) {\n      return new (t || ActivitiesSalesTeamComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesSalesTeamComponent,\n      selectors: [[\"app-activities-sales-team\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"outlined\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"bp_identification.business_partner.first_name\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"bp_identification.business_partner.first_name\"], [\"pSortableColumn\", \"bp_identification.business_partner.last_name\"], [\"field\", \"bp_identification.business_partner.last_name\"], [\"pSortableColumn\", \"partner_role\"], [\"field\", \"partner_role\"], [\"pSortableColumn\", \"address.email_address\"], [\"field\", \"address.email_address\"], [\"pSortableColumn\", \"address.phone_number\"], [\"field\", \"address.phone_number\"], [1, \"border-round-left-lg\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"]],\n      template: function ActivitiesSalesTeamComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Sales Team\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, ActivitiesSalesTeamComponent_ng_template_7_Template, 21, 0, \"ng-template\", 6)(8, ActivitiesSalesTeamComponent_ng_template_8_Template, 11, 5, \"ng-template\", 7)(9, ActivitiesSalesTeamComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, ActivitiesSalesTeamComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.salesteamDetails)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i1.Table, i2.PrimeTemplate, i1.SortableColumn, i1.SortIcon, i3.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "team_r1", "bp_identification", "business_partner", "first_name", "last_name", "partner_role", "addresses", "emails", "email_address", "phone_numbers", "phone_number", "ActivitiesSalesTeamComponent", "constructor", "salesteamDetails", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "ActivitiesSalesTeamComponent_Template", "rf", "ctx", "ɵɵtemplate", "ActivitiesSalesTeamComponent_ng_template_7_Template", "ActivitiesSalesTeamComponent_ng_template_8_Template", "ActivitiesSalesTeamComponent_ng_template_9_Template", "ActivitiesSalesTeamComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities-details\\activities-sales-team\\activities-sales-team.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities-details\\activities-sales-team\\activities-sales-team.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-activities-sales-team',\r\n  templateUrl: './activities-sales-team.component.html',\r\n  styleUrl: './activities-sales-team.component.scss',\r\n})\r\nexport class ActivitiesSalesTeamComponent implements OnInit {\r\n  public salesteamDetails: any = null;\r\n\r\n  ngOnInit() {}\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Team</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\"\r\n            [styleClass]=\"'w-5rem font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"salesteamDetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <!-- <th class=\"border-round-left-lg\" pSortableColumn=\"id\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            ID #\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th> -->\r\n                    <th pSortableColumn=\"bp_identification.business_partner.first_name\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            First Name\r\n                            <p-sortIcon field=\"bp_identification.business_partner.first_name\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_identification.business_partner.last_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Last Name\r\n                            <p-sortIcon field=\"bp_identification.business_partner.last_name\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"partner_role\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Role\r\n                            <p-sortIcon field=\"partner_role\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"address.email_address\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Email\r\n                            <p-sortIcon field=\"address.email_address\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"address.phone_number\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Phone\r\n                            <p-sortIcon field=\"address.phone_number\" />\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-team>\r\n                <tr>\r\n                    <!-- <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ team?.id || '-'}}\r\n                    </td> -->\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ team?.bp_identification?.business_partner?.first_name || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.bp_identification?.business_partner?.last_name || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.partner_role || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.bp_identification?.business_partner?.addresses?.[0]?.emails?.[0]?.email_address ||\r\n                        '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.bp_identification?.business_partner?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                        ||\r\n                        '-'}}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">No Sales Teams found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">Loading Sales Teams data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;ICsBwBA,EAVR,CAAAC,cAAA,SAAI,aASiG,cAClD;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAG,SAAA,qBAAoE;IAE5EH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAmE,cACpB;IACvCD,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAG,SAAA,qBAAmE;IAE3EH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAmC,eACY;IACvCD,EAAA,CAAAE,MAAA,cACA;IAAAF,EAAA,CAAAG,SAAA,sBAAmC;IAE3CH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA4C,eACG;IACvCD,EAAA,CAAAE,MAAA,eACA;IAAAF,EAAA,CAAAG,SAAA,sBAA4C;IAEpDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA2C,eACI;IACvCD,EAAA,CAAAE,MAAA,eACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAGvDH,EAFQ,CAAAI,YAAA,EAAM,EACL,EACJ;;;;;IAQDJ,EAJJ,CAAAC,cAAA,SAAI,aAIiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,IAGJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;IAjBGJ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,kBAAAF,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,UAAA,cACJ;IAEIV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,kBAAAF,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAE,SAAA,cACJ;IAEIX,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAK,YAAA,cACJ;IAEIZ,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,kBAAAF,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,kBAAAN,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,qBAAAN,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,kBAAAP,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,qBAAAP,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,IAAAC,aAAA,cAEJ;IAEIf,EAAA,CAAAK,SAAA,GAGJ;IAHIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,kBAAAF,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,kBAAAN,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,qBAAAN,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAG,aAAA,kBAAAT,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAG,aAAA,qBAAAT,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAG,aAAA,IAAAC,YAAA,cAGJ;;;;;IAKAjB,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IACtEF,EADsE,CAAAI,YAAA,EAAK,EACtE;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IACzFF,EADyF,CAAAI,YAAA,EAAK,EACzF;;;ADhFrB,OAAM,MAAOc,4BAA4B;EALzCC,YAAA;IAMS,KAAAC,gBAAgB,GAAQ,IAAI;;EAEnCC,QAAQA,CAAA,GAAI;;;uBAHDH,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLjC5B,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAI,YAAA,EAAK;UAC9DJ,EAAA,CAAAG,SAAA,kBAC4C;UAChDH,EAAA,CAAAI,YAAA,EAAM;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UA2E1BD,EAzEA,CAAA8B,UAAA,IAAAC,mDAAA,0BAAgC,IAAAC,mDAAA,0BA2CO,IAAAC,mDAAA,yBAyBD,KAAAC,oDAAA,yBAKD;UAOjDlC,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;;;UAxFiEJ,EAAA,CAAAK,SAAA,GAAiB;UAC5EL,EAD2D,CAAAmC,UAAA,kBAAiB,sCACvC;UAIhCnC,EAAA,CAAAK,SAAA,GAA0B;UAAuCL,EAAjE,CAAAmC,UAAA,UAAAN,GAAA,CAAAT,gBAAA,CAA0B,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}