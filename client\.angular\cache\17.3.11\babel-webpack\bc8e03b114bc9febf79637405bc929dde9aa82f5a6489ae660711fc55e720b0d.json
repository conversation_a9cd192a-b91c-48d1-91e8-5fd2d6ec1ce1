{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/sidebar\";\nimport * as i5 from \"primeng/calendar\";\nconst _c0 = a0 => ({\n  \"layout-rightmenu-active\": a0\n});\nexport let AppProfileSidebarComponent = /*#__PURE__*/(() => {\n  class AppProfileSidebarComponent {\n    constructor(layoutService) {\n      this.layoutService = layoutService;\n      this.date = new Date();\n    }\n    get visible() {\n      return this.layoutService.state.rightMenuActive;\n    }\n    set visible(_val) {\n      this.layoutService.state.rightMenuActive = _val;\n    }\n    static {\n      this.ɵfac = function AppProfileSidebarComponent_Factory(t) {\n        return new (t || AppProfileSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppProfileSidebarComponent,\n        selectors: [[\"app-profilemenu\"]],\n        decls: 51,\n        vars: 7,\n        consts: [[\"position\", \"right\", \"styleClass\", \"layout-profile-sidebar w-full sm:w-28rem\", 3, \"visibleChange\", \"visible\", \"transitionOptions\"], [1, \"layout-rightmenu\", \"h-full\", \"overflow-y-auto\", \"overflow-x-hidden\", 3, \"ngClass\"], [1, \"user-detail-wrapper\", \"text-center\", 2, \"padding\", \"4.5rem 0 2rem 0\"], [1, \"user-detail-content\", \"mb-4\"], [\"src\", \"assets/layout/images/avatar.png\", \"alt\", \"atlantis\", 1, \"user-image\"], [1, \"user-name\", \"text-2xl\", \"text-center\", \"block\", \"mt-4\", \"mb-1\"], [1, \"user-number\"], [1, \"user-tasks\", \"flex\", \"justify-content-between\", \"align-items-center\", \"py-4\", \"px-3\", \"border-bottom-1\", \"surface-border\"], [1, \"user-tasks-item\", \"in-progress\", \"font-medium\"], [1, \"task-number\", \"text-red-500\", \"flex\", \"justify-content-center\", \"align-items-center\", \"border-round\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\", \"padding\", \"9px\", \"width\", \"50px\", \"height\", \"50px\", \"font-size\", \"30px\"], [1, \"task-name\", \"block\", \"mt-3\"], [1, \"user-tasks-item\", \"font-medium\"], [1, \"task-number\", \"flex\", \"justify-content-center\", \"align-items-center\", \"border-round\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\", \"padding\", \"9px\", \"width\", \"50px\", \"height\", \"50px\", \"font-size\", \"30px\"], [\"styleClass\", \"w-full p-0\", 3, \"ngModelChange\", \"ngModel\", \"inline\"], [1, \"daily-plan-wrapper\", \"mt-5\"], [1, \"today-date\"], [1, \"list-none\", \"overflow-hidden\", \"p-0\", \"m-0\"], [1, \"mt-3\", \"border-round\", \"py-2\", \"px-3\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\"], [1, \"event-time\", \"block\", \"font-semibold\", \"text-color-secondary\"], [1, \"event-topic\", \"block\", \"mt-2\"]],\n        template: function AppProfileSidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-sidebar\", 0);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AppProfileSidebarComponent_Template_p_sidebar_visibleChange_0_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"img\", 4);\n            i0.ɵɵelementStart(5, \"span\", 5);\n            i0.ɵɵtext(6, \"Gene Russell\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 6);\n            i0.ɵɵtext(8, \"(406) 555-0120\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"a\", 9);\n            i0.ɵɵtext(12, \"23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"span\", 10);\n            i0.ɵɵtext(14, \"Progress\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 11)(16, \"a\", 12);\n            i0.ɵɵtext(17, \"6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"span\", 10);\n            i0.ɵɵtext(19, \"Overdue\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 11)(21, \"a\", 12);\n            i0.ɵɵtext(22, \"38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"span\", 10);\n            i0.ɵɵtext(24, \"All deals\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"div\")(26, \"p-calendar\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AppProfileSidebarComponent_Template_p_calendar_ngModelChange_26_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.date, $event) || (ctx.date = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 14)(28, \"span\", 15);\n            i0.ɵɵtext(29, \"14 Sunday, Jun 2020\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"ul\", 16)(31, \"li\", 17)(32, \"span\", 18);\n            i0.ɵɵtext(33, \"1:00 PM - 2:00 PM\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"span\", 19);\n            i0.ɵɵtext(35, \"Meeting with Alfredo Rhiel Madsen\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"li\", 17)(37, \"span\", 18);\n            i0.ɵɵtext(38, \"2:00 PM - 3:00 PM\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"span\", 19);\n            i0.ɵɵtext(40, \"Team Sync\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"li\", 17)(42, \"span\", 18);\n            i0.ɵɵtext(43, \"5:00 PM - 6:00 PM\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\", 19);\n            i0.ɵɵtext(45, \"Team Sync\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"li\", 17)(47, \"span\", 18);\n            i0.ɵɵtext(48, \"7:00 PM - 7:30 PM\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\", 19);\n            i0.ɵɵtext(50, \"Meeting with Engineering managers\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"transitionOptions\", \".3s cubic-bezier(0, 0, 0.2, 1)\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx.layoutService.state.rightMenuActive));\n            i0.ɵɵadvance(25);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.date);\n            i0.ɵɵproperty(\"inline\", true);\n          }\n        },\n        dependencies: [i2.NgClass, i3.NgControlStatus, i3.NgModel, i4.Sidebar, i5.Calendar],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppProfileSidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}