{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./store/services/sessionsync.service\";\nimport * as i3 from \"@angular/router\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(messageservice, sessionsyncservice) {\n      this.messageservice = messageservice;\n      this.sessionsyncservice = sessionsyncservice;\n      this.isSidebarCollapsed = true;\n      // Display error message while user try to login with \"Microsoft SSO\" button.\n      const urlParams = new URLSearchParams(window.location.search);\n      if (urlParams.has('error')) {\n        const error = urlParams.get('error');\n        setTimeout(() => {\n          this.messageservice.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: error\n          });\n        }, 100);\n      }\n    }\n    toggleSidebar() {\n      this.isSidebarCollapsed = !this.isSidebarCollapsed;\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.SessionsyncService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 1,\n        vars: 0,\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [i3.RouterOutlet],\n        styles: [\"@import\\\"https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200\\\";\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}