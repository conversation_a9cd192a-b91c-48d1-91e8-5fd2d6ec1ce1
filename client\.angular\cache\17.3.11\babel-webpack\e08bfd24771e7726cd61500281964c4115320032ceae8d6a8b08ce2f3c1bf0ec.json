{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"../../../opportunities.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/editor\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/calendar\";\nimport * as i13 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nfunction ActivitiesTaskFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Task\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Document Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_12_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"document_type\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_21_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r2.bp_full_name, \"\");\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ActivitiesTaskFormComponent_ng_template_31_span_2_Template, 2, 1, \"span\", 37);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.bp_full_name);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_32_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_42_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.email, \"\");\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_42_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.mobile, \"\");\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesTaskFormComponent_ng_template_42_span_3_Template, 2, 1, \"span\", 37)(4, ActivitiesTaskFormComponent_ng_template_42_span_4_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r3.bp_id, \": \", item_r3.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.mobile);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_43_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_52_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"task_category\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_60_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_60_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction ActivitiesTaskFormComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActivitiesTaskFormComponent_ng_template_60_span_3_Template, 2, 1, \"span\", 37)(4, ActivitiesTaskFormComponent_ng_template_60_span_4_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bp_id, \": \", item_r4.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_87_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors && ctx_r0.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction ActivitiesTaskFormComponent_div_96_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActivitiesTaskFormComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_div_96_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"notes\"].errors && ctx_r0.f[\"notes\"].errors[\"required\"]);\n  }\n}\nexport class ActivitiesTaskFormComponent {\n  constructor(formBuilder, route, activitiesservice, opportunitiesservice, messageservice) {\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.activitiesservice = activitiesservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.visible = false;\n    this.onClose = new EventEmitter();\n    this.opportunity_id = '';\n    this.submitted = false;\n    this.saving = false;\n    this.position = 'right';\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.defaultOptions = [];\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.existingcontactLoading = false;\n    this.existingcontactInput$ = new Subject();\n    this.owner_id = null;\n    this.FollowUpForm = this.formBuilder.group({\n      document_type: ['', [Validators.required]],\n      subject: ['', [Validators.required]],\n      main_account_party_id: [null, [Validators.required]],\n      main_contact_party_id: [null, [Validators.required]],\n      task_category: ['', [Validators.required]],\n      processor_party_id: [null],\n      start_date: [''],\n      end_date: [''],\n      priority: [''],\n      activity_status: ['', [Validators.required]],\n      notes: ['', [Validators.required]],\n      //contactexisting: [''],\n      involved_parties: this.formBuilder.array([this.createContactFormGroup()])\n    });\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityCategory: [],\n      activityStatus: [],\n      activityPriority: []\n    };\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'email',\n      header: 'Email Address'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  ngOnInit() {\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    setTimeout(() => {\n      this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    }, 50);\n    this.FollowUpForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.getOwner().subscribe({\n      next: response => {\n        this.owner_id = response;\n      },\n      error: err => {\n        console.error('Error fetching bp_id:', err);\n      }\n    });\n    this.loadAccounts();\n    this.loadEmployees();\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  TableColumnReorder(event) {\n    const draggedCol = this.cols[event.dragIndex];\n    this.cols.splice(event.dragIndex, 1);\n    this.cols.splice(event.dropIndex, 0, draggedCol);\n  }\n  getOwner() {\n    return this.activitiesservice.getEmailwisePartner();\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      const options = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n      // Assign options to dropdown object\n      this.dropdowns[target] = options;\n      // Set 'Open' as default selected for activityStatus only\n      if (target === 'activityStatus') {\n        const openOption = options.find(opt => opt.label.toLowerCase() === 'open');\n        if (openOption) {\n          const control = this.FollowUpForm?.get('activity_status');\n          if (control) {\n            control.setValue(openOption.value);\n          }\n        }\n      }\n    });\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'filters[roles][bp_role][$in][2]': 'PRO001',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadExistingContacts() {\n    this.existingcontacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.existingcontactInput$.pipe(distinctUntilChanged(), tap(() => this.existingcontactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.existingcontactLoading = false), catchError(error => {\n        this.existingcontactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  loadEmployees() {\n    this.employees$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.employeeInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'BUP003',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Employee fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.employeeLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  // selectExistingContact() {\n  //   this.addExistingContact(this.FollowUpForm.value);\n  //   this.existingDialogVisible = false; // Close dialog\n  // }\n  // addExistingContact(existing: any) {\n  //   const contactForm = this.formBuilder.group({\n  //     bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\n  //     email_address: [existing?.contactexisting?.email || ''],\n  //     role_code: 'BUP001',\n  //     party_id: existing?.contactexisting?.bp_id || '',\n  //   });\n  //   const firstGroup = this.involved_parties.at(0) as FormGroup;\n  //   const bpName = firstGroup?.get('bp_full_name')?.value;\n  //   if (!bpName && this.involved_parties.length === 1) {\n  //     // Replace the default empty group\n  //     this.involved_parties.setControl(0, contactForm);\n  //   } else {\n  //     // Otherwise, add a new contact\n  //     this.involved_parties.push(contactForm);\n  //   }\n  //   this.existingDialogVisible = false; // Close dialog\n  // }\n  // deleteContact(index: number) {\n  //   if (this.involved_parties.length > 1) {\n  //     this.involved_parties.removeAt(index);\n  //   }\n  // }\n  createContactFormGroup() {\n    return this.formBuilder.group({\n      bp_full_name: [''],\n      email_address: ['']\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.FollowUpForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.FollowUpForm.value\n      };\n      const data = {\n        document_type: value?.document_type,\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        task_category: value?.task_category,\n        processor_party_id: value?.processor_party_id,\n        start_date: value?.start_date ? _this.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this.formatDate(value.end_date) : null,\n        priority: value?.priority,\n        activity_status: value?.activity_status,\n        owner_party_id: _this.owner_id,\n        note: value?.notes,\n        involved_parties: Array.isArray(value.involved_parties) ? [...value.involved_parties, ...(value?.main_account_party_id ? [{\n          role_code: 'FLCU01',\n          party_id: value.main_account_party_id\n        }] : []), ...(value?.main_contact_party_id ? [{\n          role_code: 'BUP001',\n          party_id: value.main_contact_party_id\n        }] : []), ...(_this.owner_id ? [{\n          role_code: 'BUP003',\n          party_id: _this.owner_id\n        }] : [])].filter(item => item && Object.keys(item).length > 0 && item.party_id && item.role_code) : [],\n        type_code: '0006',\n        opportunity_id: _this.opportunity_id,\n        btd_role_code: '2'\n      };\n      _this.activitiesservice.createOpportuniyFollowup(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.addDialogVisible = false;\n          _this.saving = false;\n          _this.visible = false;\n          _this.FollowUpForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Follow Up Added Successfully!'\n          });\n          _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.FollowUpForm.controls;\n  }\n  get involved_parties() {\n    return this.FollowUpForm.get('involved_parties');\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  showDialog(position) {\n    this.position = position;\n    this.visible = true;\n    this.submitted = false;\n    this.FollowUpForm.reset();\n  }\n  hideDialog() {\n    this.onClose.emit();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ActivitiesTaskFormComponent_Factory(t) {\n      return new (t || ActivitiesTaskFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.OpportunitiesService), i0.ɵɵdirectiveInject(i5.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActivitiesTaskFormComponent,\n      selectors: [[\"app-activities-task-form\"]],\n      inputs: {\n        visible: \"visible\"\n      },\n      outputs: {\n        onClose: \"onClose\"\n      },\n      decls: 100,\n      vars: 79,\n      consts: [[1, \"followup-popup\", 3, \"onHide\", \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [\"pTemplate\", \"header\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Transaction Type\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"text-red-500\"], [\"formControlName\", \"document_type\", \"placeholder\", \"Select a Document Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Subject\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", \"placeholder\", \"Select a Contact\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"task_category\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Processor\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"processor_party_id\", \"appendTo\", \"body\", \"placeholder\", \"Search for a processor\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\"], [\"for\", \"Create Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Expected Decision Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Priority\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"priority\", \"placeholder\", \"Select a Prioriry\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"notes\", \"placeholder\", \"Enter your note here...\", \"styleClass\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n      template: function ActivitiesTaskFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dialog\", 0);\n          i0.ɵɵlistener(\"onHide\", function ActivitiesTaskFormComponent_Template_p_dialog_onHide_0_listener() {\n            return ctx.hideDialog();\n          });\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActivitiesTaskFormComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(1, ActivitiesTaskFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", 1);\n          i0.ɵɵelementStart(2, \"form\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5)(6, \"span\", 6);\n          i0.ɵɵtext(7, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \"Transaction Type \");\n          i0.ɵɵelementStart(9, \"span\", 7);\n          i0.ɵɵtext(10, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(11, \"p-dropdown\", 8);\n          i0.ɵɵtemplate(12, ActivitiesTaskFormComponent_div_12_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"label\", 10)(15, \"span\", 6);\n          i0.ɵɵtext(16, \"subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \"Subject \");\n          i0.ɵɵelementStart(18, \"span\", 7);\n          i0.ɵɵtext(19, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"input\", 11);\n          i0.ɵɵtemplate(21, ActivitiesTaskFormComponent_div_21_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 4)(23, \"label\", 12)(24, \"span\", 6);\n          i0.ɵɵtext(25, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \"Account \");\n          i0.ɵɵelementStart(27, \"span\", 7);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"ng-select\", 13);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵtemplate(31, ActivitiesTaskFormComponent_ng_template_31_Template, 3, 2, \"ng-template\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ActivitiesTaskFormComponent_div_32_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 15)(34, \"label\", 16)(35, \"span\", 6);\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \"Contact \");\n          i0.ɵɵelementStart(38, \"span\", 7);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"ng-select\", 17);\n          i0.ɵɵpipe(41, \"async\");\n          i0.ɵɵtemplate(42, ActivitiesTaskFormComponent_ng_template_42_Template, 5, 4, \"ng-template\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, ActivitiesTaskFormComponent_div_43_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"label\", 18)(46, \"span\", 6);\n          i0.ɵɵtext(47, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \"Category \");\n          i0.ɵɵelementStart(49, \"span\", 7);\n          i0.ɵɵtext(50, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(51, \"p-dropdown\", 19);\n          i0.ɵɵtemplate(52, ActivitiesTaskFormComponent_div_52_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 15)(54, \"label\", 20)(55, \"span\", 6);\n          i0.ɵɵtext(56, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \"Processor \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"ng-select\", 21);\n          i0.ɵɵpipe(59, \"async\");\n          i0.ɵɵtemplate(60, ActivitiesTaskFormComponent_ng_template_60_Template, 5, 4, \"ng-template\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 15)(62, \"label\", 22)(63, \"span\", 6);\n          i0.ɵɵtext(64, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \"Create Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(66, \"p-calendar\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 15)(68, \"label\", 24)(69, \"span\", 6);\n          i0.ɵɵtext(70, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \"Expected Decision Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"p-calendar\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 15)(74, \"label\", 26)(75, \"span\", 6);\n          i0.ɵɵtext(76, \"flag\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \"Priority \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(78, \"p-dropdown\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 15)(80, \"label\", 28)(81, \"span\", 6);\n          i0.ɵɵtext(82, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(83, \"Status \");\n          i0.ɵɵelementStart(84, \"span\", 7);\n          i0.ɵɵtext(85, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(86, \"p-dropdown\", 29);\n          i0.ɵɵtemplate(87, ActivitiesTaskFormComponent_div_87_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 30)(89, \"label\", 31)(90, \"span\", 6);\n          i0.ɵɵtext(91, \"notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(92, \"Notes \");\n          i0.ɵɵelementStart(93, \"span\", 7);\n          i0.ɵɵtext(94, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(95, \"p-editor\", 32);\n          i0.ɵɵtemplate(96, ActivitiesTaskFormComponent_div_96_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 33)(98, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function ActivitiesTaskFormComponent_Template_button_click_98_listener() {\n            return ctx.visible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function ActivitiesTaskFormComponent_Template_button_click_99_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(63, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.FollowUpForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"])(\"ngClass\", i0.ɵɵpureFunction1(64, _c1, ctx.submitted && ctx.f[\"document_type\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"document_type\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(66, _c1, ctx.submitted && ctx.f[\"subject\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"subject\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(30, 57, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(68, _c1, ctx.submitted && ctx.f[\"main_account_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_account_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(41, 59, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(70, _c1, ctx.submitted && ctx.f[\"main_contact_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"main_contact_party_id\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityCategory\"])(\"ngClass\", i0.ɵɵpureFunction1(72, _c1, ctx.submitted && ctx.f[\"task_category\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"task_category\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(59, 61, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityPriority\"]);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(74, _c1, ctx.submitted && ctx.f[\"activity_status\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"activity_status\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(76, _c2));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(77, _c1, ctx.submitted && ctx.f[\"notes\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"notes\"].errors);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.PrimeTemplate, i7.ButtonDirective, i8.Dropdown, i9.Dialog, i10.Editor, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.Calendar, i13.InputText, i6.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subject", "takeUntil", "concat", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ActivitiesTaskFormComponent_div_12_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "submitted", "f", "errors", "ActivitiesTaskFormComponent_div_21_div_1_Template", "ɵɵtextInterpolate1", "item_r2", "bp_full_name", "ActivitiesTaskFormComponent_ng_template_31_span_2_Template", "ɵɵtextInterpolate", "bp_id", "ActivitiesTaskFormComponent_div_32_div_1_Template", "item_r3", "email", "mobile", "ActivitiesTaskFormComponent_ng_template_42_span_3_Template", "ActivitiesTaskFormComponent_ng_template_42_span_4_Template", "ɵɵtextInterpolate2", "ActivitiesTaskFormComponent_div_43_div_1_Template", "ActivitiesTaskFormComponent_div_52_div_1_Template", "item_r4", "ActivitiesTaskFormComponent_ng_template_60_span_3_Template", "ActivitiesTaskFormComponent_ng_template_60_span_4_Template", "ActivitiesTaskFormComponent_div_87_div_1_Template", "ActivitiesTaskFormComponent_div_96_div_1_Template", "ActivitiesTaskFormComponent", "constructor", "formBuilder", "route", "activitiesservice", "opportunitiesservice", "messageservice", "unsubscribe$", "visible", "onClose", "opportunity_id", "saving", "position", "addDialogVisible", "existingDialogVisible", "defaultOptions", "accountLoading", "accountInput$", "contactLoading", "contactInput$", "employeeLoading", "employeeInput$", "existingcontactLoading", "existingcontactInput$", "owner_id", "FollowUpForm", "group", "document_type", "required", "subject", "main_account_party_id", "main_contact_party_id", "task_category", "processor_party_id", "start_date", "end_date", "priority", "activity_status", "notes", "involved_parties", "array", "createContactFormGroup", "dropdowns", "activityDocumentType", "activityCategory", "activityStatus", "activityPriority", "_selectedColumns", "cols", "field", "header", "sortField", "sortOrder", "ngOnInit", "parent", "snapshot", "paramMap", "get", "setTimeout", "loadActivityDropDown", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "contacts$", "err", "console", "error", "subscribe", "get<PERSON>wner", "next", "response", "loadAccounts", "loadEmployees", "selectedColumns", "val", "filter", "col", "includes", "TableColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "getEmail<PERSON><PERSON><PERSON><PERSON>", "target", "type", "getActivityDropdownOptions", "res", "options", "data", "attr", "label", "description", "value", "code", "openOption", "find", "opt", "toLowerCase", "control", "setValue", "accounts$", "term", "params", "getPartners", "loadExistingContacts", "existingcontacts$", "bpId", "getPartnersContact", "contacts", "employees$", "email_address", "onSubmit", "_this", "_asyncToGenerator", "invalid", "formatDate", "owner_party_id", "note", "Array", "isArray", "role_code", "party_id", "item", "Object", "keys", "length", "type_code", "btd_role_code", "createOpportuniyFollowup", "reset", "add", "severity", "detail", "getOpportunityByID", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showExistingDialog", "showDialog", "hideDialog", "emit", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "ActivitiesService", "i4", "OpportunitiesService", "i5", "MessageService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ActivitiesTaskFormComponent_Template", "rf", "ctx", "ɵɵlistener", "ActivitiesTaskFormComponent_Template_p_dialog_onHide_0_listener", "ɵɵtwoWayListener", "ActivitiesTaskFormComponent_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ActivitiesTaskFormComponent_ng_template_1_Template", "ɵɵelement", "ActivitiesTaskFormComponent_div_12_Template", "ActivitiesTaskFormComponent_div_21_Template", "ActivitiesTaskFormComponent_ng_template_31_Template", "ActivitiesTaskFormComponent_div_32_Template", "ActivitiesTaskFormComponent_ng_template_42_Template", "ActivitiesTaskFormComponent_div_43_Template", "ActivitiesTaskFormComponent_div_52_Template", "ActivitiesTaskFormComponent_ng_template_60_Template", "ActivitiesTaskFormComponent_div_87_Template", "ActivitiesTaskFormComponent_div_96_Template", "ActivitiesTaskFormComponent_Template_button_click_98_listener", "ActivitiesTaskFormComponent_Template_button_click_99_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c1", "ɵɵclassMap", "ɵɵpipeBind1", "_c2"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\activities-task-form\\activities-task-form.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\activities-task-form\\activities-task-form.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { OpportunitiesService } from '../../../opportunities.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\ninterface DropdownOption {\r\n  label: string;\r\n  value: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-activities-task-form',\r\n  templateUrl: './activities-task-form.component.html',\r\n  styleUrl: './activities-task-form.component.scss',\r\n})\r\nexport class ActivitiesTaskFormComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @Input() visible: boolean = false;\r\n  @Output() onClose = new EventEmitter<void>();\r\n  public opportunity_id: string = '';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public position: string = 'right';\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  private defaultOptions: any = [];\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  public existingcontacts$?: Observable<any[]>;\r\n  public existingcontactLoading = false;\r\n  public existingcontactInput$ = new Subject<string>();\r\n  private owner_id: string | null = null;\r\n\r\n  public FollowUpForm: FormGroup = this.formBuilder.group({\r\n    document_type: ['', [Validators.required]],\r\n    subject: ['', [Validators.required]],\r\n    main_account_party_id: [null, [Validators.required]],\r\n    main_contact_party_id: [null, [Validators.required]],\r\n    task_category: ['', [Validators.required]],\r\n    processor_party_id: [null],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    priority: [''],\r\n    activity_status: ['', [Validators.required]],\r\n    notes: ['', [Validators.required]],\r\n    //contactexisting: [''],\r\n    involved_parties: this.formBuilder.array([this.createContactFormGroup()]),\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activityPriority: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private activitiesservice: ActivitiesService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [{ field: 'email', header: 'Email Address' }];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  ngOnInit() {\r\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    setTimeout(() => {\r\n      this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\r\n      this.loadActivityDropDown(\r\n        'activityDocumentType',\r\n        'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n      );\r\n      this.loadActivityDropDown(\r\n        'activityCategory',\r\n        'CRM_ACTIVITY_TASK_CATEGORY'\r\n      );\r\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    }, 50);\r\n    this.FollowUpForm.get('main_account_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.unsubscribe$),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.getOwner().subscribe({\r\n      next: (response: string | null) => {\r\n        this.owner_id = response;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching bp_id:', err);\r\n      },\r\n    });\r\n    this.loadAccounts();\r\n    this.loadEmployees();\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n  TableColumnReorder(event: any) {\r\n    const draggedCol = this.cols[event.dragIndex];\r\n    this.cols.splice(event.dragIndex, 1);\r\n    this.cols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  private getOwner(): Observable<string | null> {\r\n    return this.activitiesservice.getEmailwisePartner();\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        const options: DropdownOption[] =\r\n          res?.data?.map(\r\n            (attr: { description: string; code: string }): DropdownOption => ({\r\n              label: attr.description,\r\n              value: attr.code,\r\n            })\r\n          ) ?? [];\r\n\r\n        // Assign options to dropdown object\r\n        this.dropdowns[target] = options;\r\n\r\n        // Set 'Open' as default selected for activityStatus only\r\n        if (target === 'activityStatus') {\r\n          const openOption = options.find(\r\n            (opt) => opt.label.toLowerCase() === 'open'\r\n          );\r\n\r\n          if (openOption) {\r\n            const control = this.FollowUpForm?.get('activity_status');\r\n            if (control) {\r\n              control.setValue(openOption.value);\r\n            }\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'filters[roles][bp_role][$in][2]': 'PRO001',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadExistingContacts() {\r\n    this.existingcontacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.existingcontactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.existingcontactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.existingcontactLoading = false)),\r\n            catchError((error) => {\r\n              this.existingcontactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  private loadEmployees(): void {\r\n    this.employees$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.employeeInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'BUP003',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Employee fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.employeeLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  // selectExistingContact() {\r\n  //   this.addExistingContact(this.FollowUpForm.value);\r\n  //   this.existingDialogVisible = false; // Close dialog\r\n  // }\r\n\r\n  // addExistingContact(existing: any) {\r\n  //   const contactForm = this.formBuilder.group({\r\n  //     bp_full_name: [existing?.contactexisting?.bp_full_name || ''],\r\n  //     email_address: [existing?.contactexisting?.email || ''],\r\n  //     role_code: 'BUP001',\r\n  //     party_id: existing?.contactexisting?.bp_id || '',\r\n  //   });\r\n\r\n  //   const firstGroup = this.involved_parties.at(0) as FormGroup;\r\n  //   const bpName = firstGroup?.get('bp_full_name')?.value;\r\n\r\n  //   if (!bpName && this.involved_parties.length === 1) {\r\n  //     // Replace the default empty group\r\n  //     this.involved_parties.setControl(0, contactForm);\r\n  //   } else {\r\n  //     // Otherwise, add a new contact\r\n  //     this.involved_parties.push(contactForm);\r\n  //   }\r\n\r\n  //   this.existingDialogVisible = false; // Close dialog\r\n  // }\r\n\r\n  // deleteContact(index: number) {\r\n  //   if (this.involved_parties.length > 1) {\r\n  //     this.involved_parties.removeAt(index);\r\n  //   }\r\n  // }\r\n\r\n  createContactFormGroup(): FormGroup {\r\n    return this.formBuilder.group({\r\n      bp_full_name: [''],\r\n      email_address: [''],\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.FollowUpForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.FollowUpForm.value };\r\n\r\n    const data = {\r\n      document_type: value?.document_type,\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      task_category: value?.task_category,\r\n      processor_party_id: value?.processor_party_id,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      priority: value?.priority,\r\n      activity_status: value?.activity_status,\r\n      owner_party_id: this.owner_id,\r\n      note: value?.notes,\r\n      involved_parties: Array.isArray(value.involved_parties)\r\n        ? [\r\n            ...value.involved_parties,\r\n            ...(value?.main_account_party_id\r\n              ? [{ role_code: 'FLCU01', party_id: value.main_account_party_id }]\r\n              : []),\r\n            ...(value?.main_contact_party_id\r\n              ? [{ role_code: 'BUP001', party_id: value.main_contact_party_id }]\r\n              : []),\r\n            ...(this.owner_id\r\n              ? [{ role_code: 'BUP003', party_id: this.owner_id }]\r\n              : []),\r\n          ].filter(\r\n            (item) =>\r\n              item &&\r\n              Object.keys(item).length > 0 &&\r\n              item.party_id &&\r\n              item.role_code\r\n          )\r\n        : [],\r\n      type_code: '0006',\r\n      opportunity_id: this.opportunity_id,\r\n      btd_role_code: '2',\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createOpportuniyFollowup(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.addDialogVisible = false;\r\n          this.saving = false;\r\n          this.visible = false;\r\n          this.FollowUpForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Follow Up Added Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.FollowUpForm.controls;\r\n  }\r\n\r\n  get involved_parties(): any {\r\n    return this.FollowUpForm.get('involved_parties') as FormArray;\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.position = position;\r\n    this.visible = true;\r\n    this.submitted = false;\r\n    this.FollowUpForm.reset();\r\n  }\r\n\r\n  hideDialog(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-dialog (onHide)=\"hideDialog()\" [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"followup-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Task</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field grid mt-0 text-base\">\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Transaction Type\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">description</span>Transaction Type\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityDocumentType']\" formControlName=\"document_type\"\r\n                    placeholder=\"Select a Document Type\" optionLabel=\"label\" optionValue=\"value\"\r\n                    styleClass=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['document_type'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['document_type'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['document_type'].errors['required']\">\r\n                        Document Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Subject\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">subject</span>Subject\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                    class=\"p-inputtext p-component p-element h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['subject'].errors['required']\">\r\n                        Subject is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Account\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">account_circle</span>Account\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_account_party_id\" [typeahead]=\"accountInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\"\r\n                    [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors['required']\">\r\n                        Account is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Contact\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">person</span>Contact\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\"\r\n                    [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\" placeholder=\"Select a Contact\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </div>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors['required']\">\r\n                        Contact is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Category\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">category</span>Category\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"task_category\"\r\n                    placeholder=\"Select a Category\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['task_category'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['task_category'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['task_category'].errors['required']\">\r\n                        Category is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Processor\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">person</span>Processor\r\n                </label>\r\n                <ng-select pInputText [items]=\"employees$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"employeeLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"processor_party_id\" [typeahead]=\"employeeInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                    [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\" placeholder=\"Search for a processor\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </div>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Create Date\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">schedule</span>Create Date\r\n                </label>\r\n                <p-calendar formControlName=\"start_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"Create Date\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Expected Decision Date\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">schedule</span>Expected Decision Date\r\n                </label>\r\n                <p-calendar formControlName=\"end_date\" inputId=\"calendar-12h\" [showTime]=\"true\" hourFormat=\"12\"\r\n                    [showIcon]=\"true\" styleClass=\"h-3rem w-full\" placeholder=\"Expected Decision Date\" />\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Priority\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">flag</span>Priority\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityPriority']\" formControlName=\"priority\"\r\n                    placeholder=\"Select a Prioriry\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Status\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">check_circle</span>Status\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                    placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                submitted &&\r\n                                f['activity_status'].errors &&\r\n                                f['activity_status'].errors['required']\r\n                              \">\r\n                        Status is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 mt-3\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Notes\">\r\n                    <span class=\"material-symbols-rounded text-2xl\">notes</span>Notes\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-editor formControlName=\"notes\" placeholder=\"Enter your note here...\" [style]=\"{ height: '125px'}\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['notes'].errors }\" styleClass=\"w-full\" />\r\n                <div *ngIf=\"submitted && f['notes'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['notes'].errors &&\r\n                                    f['notes'].errors['required']\r\n                                  \">\r\n                        Notes is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n            <div class=\"flex justify-content-between align-items-center mb-3\">\r\n                <h4 class=\"m-0 flex align-items-center h-3rem\">Additional Contacts Persons</h4>\r\n\r\n                <div class=\"flex gap-3\">\r\n                    <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\"\r\n                        iconPos=\"right\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\"></p-button>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"table-sec\">\r\n                <p-table #dt [value]=\"involved_parties?.controls\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n                    [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"TableColumnReorder($event)\"\r\n                    responsiveLayout=\"scroll\" class=\"scrollable-table followup-add-table\">\r\n\r\n\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th class=\"border-round-left-lg\">Name</th>\r\n                            <ng-container *ngFor=\"let col of selectedColumns\">\r\n                                <th pReorderableColumn>\r\n                                    <div class=\"flex align-items-center cursor-pointer\">\r\n                                        {{ col.header }}\r\n                                    </div>\r\n                                </th>\r\n                            </ng-container>\r\n                            <th class=\"border-round-right-lg\">Actions</th>\r\n                        </tr>\r\n                    </ng-template>\r\n\r\n                    <ng-template pTemplate=\"body\" let-contact let-columns=\"columns\" let-i=\"rowIndex\">\r\n                        <tr [formGroup]=\"contact\">\r\n                            <td class=\"border-round-left-lg\">\r\n                                <input pInputText type=\"text\" class=\"p-inputtext p-component p-element h-3rem w-full\" formControlName=\"bp_full_name\"\r\n                                    placeholder=\"Enter a Name\" readonly />\r\n                            </td>\r\n                            <ng-container *ngFor=\"let col of selectedColumns\">\r\n                                <td>\r\n                                    <ng-container [ngSwitch]=\"col.field\">\r\n                                        <ng-container *ngSwitchCase=\"'email'\">\r\n                                            <input pInputText type=\"email\" class=\"p-inputtext p-component p-element h-3rem w-full\"\r\n                                                formControlName=\"email_address\" placeholder=\"Enter Email\" readonly />\r\n                                        </ng-container>\r\n                                    </ng-container>\r\n                                </td>\r\n                            </ng-container>\r\n\r\n                            <td class=\"border-round-right-lg\">\r\n                                <button pButton pRipple type=\"button\" icon=\"pi pi-trash\"\r\n                                    class=\"p-button-rounded p-button-danger\" (click)=\"deleteContact(i)\" title=\"Delete\"\r\n                                    *ngIf=\"involved_parties.length > 1\"></button>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n\r\n                </p-table>\r\n            </div>\r\n\r\n        </div> -->\r\n        <!-- <p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n            [draggable]=\"false\" class=\"prospect-popup\">\r\n            <ng-template pTemplate=\"header\">\r\n                <h4>Contact Information</h4>\r\n            </ng-template>\r\n\r\n            <form [formGroup]=\"FollowUpForm\" class=\"relative flex flex-column gap-1\">\r\n                <div class=\"field flex align-items-center text-base\">\r\n                    <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                        <span class=\"material-symbols-rounded\">person</span>Contacts\r\n                    </label>\r\n                    <div class=\"form-input flex-1 relative\">\r\n                        <ng-select pInputText [items]=\"existingcontacts$ | async\" bindLabel=\"bp_full_name\"\r\n                            [hideSelected]=\"true\" [loading]=\"existingcontactLoading\" [minTermLength]=\"0\"\r\n                            formControlName=\"contactexisting\" [typeahead]=\"existingcontactInput$\"\r\n                            [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                            [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                            <ng-template ng-option-tmp let-item=\"item\">\r\n                                <span>{{ item.bp_id }}</span>\r\n                                <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </ng-template>\r\n                        </ng-select>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-end gap-2 mt-3\">\r\n                    <button pButton type=\"button\"\r\n                        class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center font-medium w-9rem h-3rem\"\r\n                        (click)=\"existingDialogVisible = false\">\r\n                        Cancel\r\n                    </button>\r\n                    <button pButton type=\"button\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                        (click)=\"selectExistingContact()\">\r\n                        Save\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </p-dialog> -->\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAItE,SAAiCC,UAAU,QAAmB,gBAAgB;AAC9E,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;ICXfC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAeDH,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,mCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAI,UAAA,IAAAC,iDAAA,kBAAgE;IAGpEL,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,kBAAAC,MAAA,aAAwD;;;;;IAc9DX,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAI,UAAA,IAAAQ,iDAAA,kBAA0D;IAG9DZ,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,YAAAC,MAAA,aAAkD;;;;;IAiBpDX,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAa,kBAAA,QAAAC,OAAA,CAAAC,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAI,UAAA,IAAAY,0DAAA,mBAAgC;;;;IAD1BhB,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAiB,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACflB,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAO,OAAA,CAAAC,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAe,iDAAA,kBAAwE;IAG5EnB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAmB9DX,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAO,OAAA,CAAAE,MAAA,KAAmB;;;;;IAF9CtB,EADJ,CAAAC,cAAA,cAA2C,WACjC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAI,UAAA,IAAAmB,0DAAA,mBAAyB,IAAAC,0DAAA,mBACC;IAC9BxB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyB,kBAAA,KAAAL,OAAA,CAAAF,KAAA,QAAAE,OAAA,CAAAL,YAAA,KAAyC;IACxCf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAC,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAAa,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhCtB,EAAA,CAAAC,cAAA,UAAwE;IACpED,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAsB,iDAAA,kBAAwE;IAG5E1B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,0BAAAC,MAAA,aAAgE;;;;;IAetEX,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAI,UAAA,IAAAuB,iDAAA,kBAAgE;IAGpE3B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,kBAAAC,MAAA,aAAwD;;;;;IAiBtDX,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAa,kBAAA,QAAAe,OAAA,CAAAP,KAAA,KAAkB;;;;;IAC5CrB,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAa,kBAAA,QAAAe,OAAA,CAAAN,MAAA,KAAmB;;;;;IAF9CtB,EADJ,CAAAC,cAAA,cAA2C,WACjC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAI,UAAA,IAAAyB,0DAAA,mBAAyB,IAAAC,0DAAA,mBACC;IAC9B9B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyB,kBAAA,KAAAG,OAAA,CAAAV,KAAA,QAAAU,OAAA,CAAAb,YAAA,KAAyC;IACxCf,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,SAAAqB,OAAA,CAAAP,KAAA,CAAgB;IAChBrB,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,SAAAqB,OAAA,CAAAN,MAAA,CAAiB;;;;;IAqChCtB,EAAA,CAAAC,cAAA,UAIY;IACRD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAI,UAAA,IAAA2B,iDAAA,kBAIY;IAGhB/B,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIG;IAJHN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,oBAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,oBAAAC,MAAA,aAIG;;;;;IAaTX,EAAA,CAAAC,cAAA,UAIgB;IACZD,EAAA,CAAAE,MAAA,2BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAI,UAAA,IAAA4B,iDAAA,kBAIgB;IAGpBhC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAM,SAAA,EAIO;IAJPN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,UAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,UAAAC,MAAA,aAIO;;;ADzIjC,OAAM,MAAOsB,2BAA2B;EAgDtCC,YACUC,WAAwB,EACxBC,KAAqB,EACrBC,iBAAoC,EACpCC,oBAA0C,EAC1CC,cAA8B;IAJ9B,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IApDhB,KAAAC,YAAY,GAAG,IAAIrD,OAAO,EAAQ;IACjC,KAAAsD,OAAO,GAAY,KAAK;IACvB,KAAAC,OAAO,GAAG,IAAIxD,YAAY,EAAQ;IACrC,KAAAyD,cAAc,GAAW,EAAE;IAC3B,KAAAlC,SAAS,GAAG,KAAK;IACjB,KAAAmC,MAAM,GAAG,KAAK;IACd,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACrC,KAAAC,cAAc,GAAQ,EAAE;IAEzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI/D,OAAO,EAAU;IAErC,KAAAgE,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIjE,OAAO,EAAU;IAErC,KAAAkE,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAInE,OAAO,EAAU;IAEtC,KAAAoE,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,qBAAqB,GAAG,IAAIrE,OAAO,EAAU;IAC5C,KAAAsE,QAAQ,GAAkB,IAAI;IAE/B,KAAAC,YAAY,GAAc,IAAI,CAACvB,WAAW,CAACwB,KAAK,CAAC;MACtDC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAACqE,QAAQ,CAAC,CAAC;MAC1CC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACtE,UAAU,CAACqE,QAAQ,CAAC,CAAC;MACpCE,qBAAqB,EAAE,CAAC,IAAI,EAAE,CAACvE,UAAU,CAACqE,QAAQ,CAAC,CAAC;MACpDG,qBAAqB,EAAE,CAAC,IAAI,EAAE,CAACxE,UAAU,CAACqE,QAAQ,CAAC,CAAC;MACpDI,aAAa,EAAE,CAAC,EAAE,EAAE,CAACzE,UAAU,CAACqE,QAAQ,CAAC,CAAC;MAC1CK,kBAAkB,EAAE,CAAC,IAAI,CAAC;MAC1BC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC9E,UAAU,CAACqE,QAAQ,CAAC,CAAC;MAC5CU,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAACqE,QAAQ,CAAC,CAAC;MAClC;MACAW,gBAAgB,EAAE,IAAI,CAACrC,WAAW,CAACsC,KAAK,CAAC,CAAC,IAAI,CAACC,sBAAsB,EAAE,CAAC;KACzE,CAAC;IAEK,KAAAC,SAAS,GAA0B;MACxCC,oBAAoB,EAAE,EAAE;MACxBC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE;KACnB;IAUO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CAAC;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAe,CAAE,CAAC;IAErE,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,SAAS,GAAW,CAAC;EAPlB;EASHC,QAAQA,CAAA;IACN,IAAI,CAAC3C,cAAc,GAAG,IAAI,CAACP,KAAK,CAACmD,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1EC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,oBAAoB,CAAC,kBAAkB,EAAE,uBAAuB,CAAC;MACtE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;MACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,4BAA4B,CAC7B;MACD,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IACpE,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAAClC,YAAY,CAACgC,GAAG,CAAC,uBAAuB,CAAC,EAC1CG,YAAY,CAACC,IAAI,CACjB1G,SAAS,CAAC,IAAI,CAACoD,YAAY,CAAC,EAC5B7C,GAAG,CAAEoG,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACE,SAAS,GAAG1G,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACFnD,UAAU,CAAEqG,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACD,SAAS,GAAG1G,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;MACxC,OAAOzD,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACA8G,SAAS,EAAE;IACd,IAAI,CAACC,QAAQ,EAAE,CAACD,SAAS,CAAC;MACxBE,IAAI,EAAGC,QAAuB,IAAI;QAChC,IAAI,CAAC/C,QAAQ,GAAG+C,QAAQ;MAC1B,CAAC;MACDJ,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C;KACD,CAAC;IACF,IAAI,CAACO,YAAY,EAAE;IACnB,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAI0B,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC3B,gBAAgB;EAC9B;EAEA,IAAI2B,eAAeA,CAACC,GAAU;IAC5B,IAAI,CAAC5B,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC4B,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EACAE,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,UAAU,GAAG,IAAI,CAACjC,IAAI,CAACgC,KAAK,CAACE,SAAS,CAAC;IAC7C,IAAI,CAAClC,IAAI,CAACmC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACpC,IAAI,CAAClC,IAAI,CAACmC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAClD;EAEQZ,QAAQA,CAAA;IACd,OAAO,IAAI,CAACjE,iBAAiB,CAACiF,mBAAmB,EAAE;EACrD;EAEA1B,oBAAoBA,CAAC2B,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAACnF,iBAAiB,CACnBoF,0BAA0B,CAACD,IAAI,CAAC,CAChCnB,SAAS,CAAEqB,GAAQ,IAAI;MACtB,MAAMC,OAAO,GACXD,GAAG,EAAEE,IAAI,EAAEtI,GAAG,CACXuI,IAA2C,KAAsB;QAChEC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CACH,IAAI,EAAE;MAET;MACA,IAAI,CAACtD,SAAS,CAAC4C,MAAM,CAAC,GAAGI,OAAO;MAEhC;MACA,IAAIJ,MAAM,KAAK,gBAAgB,EAAE;QAC/B,MAAMW,UAAU,GAAGP,OAAO,CAACQ,IAAI,CAC5BC,GAAG,IAAKA,GAAG,CAACN,KAAK,CAACO,WAAW,EAAE,KAAK,MAAM,CAC5C;QAED,IAAIH,UAAU,EAAE;UACd,MAAMI,OAAO,GAAG,IAAI,CAAC5E,YAAY,EAAEgC,GAAG,CAAC,iBAAiB,CAAC;UACzD,IAAI4C,OAAO,EAAE;YACXA,OAAO,CAACC,QAAQ,CAACL,UAAU,CAACF,KAAK,CAAC;UACpC;QACF;MACF;IACF,CAAC,CAAC;EACN;EAEQvB,YAAYA,CAAA;IAClB,IAAI,CAAC+B,SAAS,GAAGnJ,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;IAAE;IACzB,IAAI,CAACE,aAAa,CAAC4C,IAAI,CACrBhG,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACsD,cAAc,GAAG,IAAK,CAAC,EACvCvD,SAAS,CAAE+I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACpG,iBAAiB,CAACsG,WAAW,CAACD,MAAM,CAAC,CAAC5C,IAAI,CACpDxG,GAAG,CAAEkH,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxC3G,UAAU,CAAEuG,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAO7G,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFQ,QAAQ,CAAC,MAAO,IAAI,CAACkD,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQ2F,oBAAoBA,CAAA;IAC1B,IAAI,CAACC,iBAAiB,GAAGxJ,MAAM,CAC7BE,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;IAAE;IACzB,IAAI,CAACQ,qBAAqB,CAACsC,IAAI,CAC7BrG,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC4D,sBAAsB,GAAG,IAAK,CAAC,EAC/C7D,SAAS,CAAE+I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACpG,iBAAiB,CAACsG,WAAW,CAACD,MAAM,CAAC,CAAC5C,IAAI,CACpDxG,GAAG,CAAEsI,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFjI,GAAG,CAAC,MAAO,IAAI,CAAC4D,sBAAsB,GAAG,KAAM,CAAC,EAChD1D,UAAU,CAAEuG,KAAK,IAAI;QACnB,IAAI,CAAC7C,sBAAsB,GAAG,KAAK;QACnC,OAAOhE,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQyG,qBAAqBA,CAAC8C,IAAY;IACxC,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAAC7C,aAAa,CAAC0C,IAAI,CACtClG,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACwD,cAAc,GAAG,IAAK,CAAC,EACvCzD,SAAS,CAAE+I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEI,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIL,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAACpG,iBAAiB,CAAC0G,kBAAkB,CAACL,MAAM,CAAC,CAAC5C,IAAI,CAC3DxG,GAAG,CAAEkH,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxC7G,GAAG,CAAEqJ,QAAe,IAAI;QACtB,IAAI,CAAC7F,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACFtD,UAAU,CAAEuG,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjD,cAAc,GAAG,KAAK;QAC3B,OAAO5D,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEQmH,aAAaA,CAAA;IACnB,IAAI,CAACuC,UAAU,GAAG5J,MAAM,CACtBE,EAAE,CAAC,IAAI,CAACyD,cAAc,CAAC;IAAE;IACzB,IAAI,CAACM,cAAc,CAACwC,IAAI,CACtBhG,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC0D,eAAe,GAAG,IAAK,CAAC,EACxC3D,SAAS,CAAE+I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACpG,iBAAiB,CAACsG,WAAW,CAACD,MAAM,CAAC,CAAC5C,IAAI,CACpDxG,GAAG,CAAEkH,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxC3G,UAAU,CAAEuG,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO7G,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFQ,QAAQ,CAAC,MAAO,IAAI,CAACsD,eAAe,GAAG,KAAM,CAAC,CAAC;OAChD;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEAqB,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACvC,WAAW,CAACwB,KAAK,CAAC;MAC5B5C,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBmI,aAAa,EAAE,CAAC,EAAE;KACnB,CAAC;EACJ;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC3I,SAAS,GAAG,IAAI;MAErB,IAAI2I,KAAI,CAAC1F,YAAY,CAAC4F,OAAO,EAAE;QAC7B;MACF;MAEAF,KAAI,CAACxG,MAAM,GAAG,IAAI;MAClB,MAAMoF,KAAK,GAAG;QAAE,GAAGoB,KAAI,CAAC1F,YAAY,CAACsE;MAAK,CAAE;MAE5C,MAAMJ,IAAI,GAAG;QACXhE,aAAa,EAAEoE,KAAK,EAAEpE,aAAa;QACnCE,OAAO,EAAEkE,KAAK,EAAElE,OAAO;QACvBC,qBAAqB,EAAEiE,KAAK,EAAEjE,qBAAqB;QACnDC,qBAAqB,EAAEgE,KAAK,EAAEhE,qBAAqB;QACnDC,aAAa,EAAE+D,KAAK,EAAE/D,aAAa;QACnCC,kBAAkB,EAAE8D,KAAK,EAAE9D,kBAAkB;QAC7CC,UAAU,EAAE6D,KAAK,EAAE7D,UAAU,GAAGiF,KAAI,CAACG,UAAU,CAACvB,KAAK,CAAC7D,UAAU,CAAC,GAAG,IAAI;QACxEC,QAAQ,EAAE4D,KAAK,EAAE5D,QAAQ,GAAGgF,KAAI,CAACG,UAAU,CAACvB,KAAK,CAAC5D,QAAQ,CAAC,GAAG,IAAI;QAClEC,QAAQ,EAAE2D,KAAK,EAAE3D,QAAQ;QACzBC,eAAe,EAAE0D,KAAK,EAAE1D,eAAe;QACvCkF,cAAc,EAAEJ,KAAI,CAAC3F,QAAQ;QAC7BgG,IAAI,EAAEzB,KAAK,EAAEzD,KAAK;QAClBC,gBAAgB,EAAEkF,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAACxD,gBAAgB,CAAC,GACnD,CACE,GAAGwD,KAAK,CAACxD,gBAAgB,EACzB,IAAIwD,KAAK,EAAEjE,qBAAqB,GAC5B,CAAC;UAAE6F,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAE7B,KAAK,CAACjE;QAAqB,CAAE,CAAC,GAChE,EAAE,CAAC,EACP,IAAIiE,KAAK,EAAEhE,qBAAqB,GAC5B,CAAC;UAAE4F,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAE7B,KAAK,CAAChE;QAAqB,CAAE,CAAC,GAChE,EAAE,CAAC,EACP,IAAIoF,KAAI,CAAC3F,QAAQ,GACb,CAAC;UAAEmG,SAAS,EAAE,QAAQ;UAAEC,QAAQ,EAAET,KAAI,CAAC3F;QAAQ,CAAE,CAAC,GAClD,EAAE,CAAC,CACR,CAACoD,MAAM,CACLiD,IAAI,IACHA,IAAI,IACJC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,IAC5BH,IAAI,CAACD,QAAQ,IACbC,IAAI,CAACF,SAAS,CACjB,GACD,EAAE;QACNM,SAAS,EAAE,MAAM;QACjBvH,cAAc,EAAEyG,KAAI,CAACzG,cAAc;QACnCwH,aAAa,EAAE;OAChB;MAEDf,KAAI,CAAC/G,iBAAiB,CACnB+H,wBAAwB,CAACxC,IAAI,CAAC,CAC9B9B,IAAI,CAAC1G,SAAS,CAACgK,KAAI,CAAC5G,YAAY,CAAC,CAAC,CAClC6D,SAAS,CAAC;QACTE,IAAI,EAAEA,CAAA,KAAK;UACT6C,KAAI,CAACtG,gBAAgB,GAAG,KAAK;UAC7BsG,KAAI,CAACxG,MAAM,GAAG,KAAK;UACnBwG,KAAI,CAAC3G,OAAO,GAAG,KAAK;UACpB2G,KAAI,CAAC1F,YAAY,CAAC2G,KAAK,EAAE;UACzBjB,KAAI,CAAC7G,cAAc,CAAC+H,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFpB,KAAI,CAAC9G,oBAAoB,CACtBmI,kBAAkB,CAACrB,KAAI,CAACzG,cAAc,CAAC,CACvCmD,IAAI,CAAC1G,SAAS,CAACgK,KAAI,CAAC5G,YAAY,CAAC,CAAC,CAClC6D,SAAS,EAAE;QAChB,CAAC;QACDD,KAAK,EAAEA,CAAA,KAAK;UACVgD,KAAI,CAACxG,MAAM,GAAG,KAAK;UACnBwG,KAAI,CAAC7G,cAAc,CAAC+H,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAjB,UAAUA,CAACmB,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAIvK,CAACA,CAAA;IACH,OAAO,IAAI,CAACgD,YAAY,CAACyH,QAAQ;EACnC;EAEA,IAAI3G,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACd,YAAY,CAACgC,GAAG,CAAC,kBAAkB,CAAc;EAC/D;EAEA0F,kBAAkBA,CAACvI,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,qBAAqB,GAAG,IAAI;EACnC;EAEAsI,UAAUA,CAACxI,QAAgB;IACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAAChC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiD,YAAY,CAAC2G,KAAK,EAAE;EAC3B;EAEAiB,UAAUA,CAAA;IACR,IAAI,CAAC5I,OAAO,CAAC6I,IAAI,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChJ,YAAY,CAAC+D,IAAI,EAAE;IACxB,IAAI,CAAC/D,YAAY,CAACiJ,QAAQ,EAAE;EAC9B;;;uBAjcWxJ,2BAA2B,EAAAjC,EAAA,CAAA0L,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5L,EAAA,CAAA0L,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAA0L,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAhM,EAAA,CAAA0L,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAlM,EAAA,CAAA0L,iBAAA,CAAAS,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA3BnK,2BAA2B;MAAAoK,SAAA;MAAAC,MAAA;QAAA7J,OAAA;MAAA;MAAA8J,OAAA;QAAA7J,OAAA;MAAA;MAAA8J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCxC7M,EAAA,CAAAC,cAAA,kBAC+C;UADrCD,EAAA,CAAA+M,UAAA,oBAAAC,gEAAA;YAAA,OAAUF,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAAgBtL,EAAA,CAAAiN,gBAAA,2BAAAC,uEAAAC,MAAA;YAAAnN,EAAA,CAAAoN,kBAAA,CAAAN,GAAA,CAAArK,OAAA,EAAA0K,MAAA,MAAAL,GAAA,CAAArK,OAAA,GAAA0K,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAElEnN,EAAA,CAAAI,UAAA,IAAAiN,kDAAA,yBAAgC;UAQhBrN,EAJhB,CAAAC,cAAA,cAAyE,aAC9B,aACa,eAC2C,cACnC;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,wBAClE;UAAAF,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAsN,SAAA,qBAGa;UACbtN,EAAA,CAAAI,UAAA,KAAAmN,2CAAA,iBAAoE;UAKxEvN,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAgD,iBACkC,eAC1B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBAC9D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAsN,SAAA,iBAEqE;UACrEtN,EAAA,CAAAI,UAAA,KAAAoN,2CAAA,iBAA8D;UAKlExN,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,cAAgD,iBACkC,eAC1B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBACrE;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAIuE;;UACnED,EAAA,CAAAI,UAAA,KAAAqN,mDAAA,0BAA2C;UAI/CzN,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAAsN,2CAAA,iBAA4E;UAKhF1N,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC6B,eAC1B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,gBAC7D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,qBAKsG;;UAClGD,EAAA,CAAAI,UAAA,KAAAuN,mDAAA,0BAA2C;UAO/C3N,EAAA,CAAAG,YAAA,EAAY;UACZH,EAAA,CAAAI,UAAA,KAAAwN,2CAAA,iBAA4E;UAKhF5N,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eAC3B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,iBAC/D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAsN,SAAA,sBAGa;UACbtN,EAAA,CAAAI,UAAA,KAAAyN,2CAAA,iBAAoE;UAKxE7N,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC+B,eAC5B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,kBACjE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAI4G;;UACxGD,EAAA,CAAAI,UAAA,KAAA0N,mDAAA,0BAA2C;UAQnD9N,EADI,CAAAG,YAAA,EAAY,EACV;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACiC,eAC9B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,oBACnE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAsN,SAAA,sBAC6E;UACjFtN,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4C,eACzC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,+BACnE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAsN,SAAA,sBACwF;UAC5FtN,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC8B,eAC3B;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,iBAC/D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAsN,SAAA,sBAEa;UACjBtN,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4B,eACzB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,eACnE;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAsN,SAAA,sBAGa;UACbtN,EAAA,CAAAI,UAAA,KAAA2N,2CAAA,iBAAsE;UAS1E/N,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,eAAyB,iBACuD,eACxB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,cAC5D;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAsN,SAAA,oBACuF;UACvFtN,EAAA,CAAAI,UAAA,KAAA4N,2CAAA,iBAA4D;UAUpEhO,EADI,CAAAG,YAAA,EAAM,EACJ;UAqGFH,EADJ,CAAAC,cAAA,eAAgD,kBAGd;UAA1BD,EAAA,CAAA+M,UAAA,mBAAAkB,8DAAA;YAAA,OAAAnB,GAAA,CAAArK,OAAA,GAAmB,KAAK;UAAA,EAAC;UAACzC,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,kBACyB;UAArBD,EAAA,CAAA+M,UAAA,mBAAAmB,8DAAA;YAAA,OAASpB,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC;UAIpCnJ,EAJqC,CAAAG,YAAA,EAAS,EAChC,EACH,EAEA;;;UA3R4DH,EAAA,CAAAmO,UAAA,CAAAnO,EAAA,CAAAoO,eAAA,KAAAC,GAAA,EAA4B;UAAjErO,EAAA,CAAAO,UAAA,eAAc;UAACP,EAAA,CAAAsO,gBAAA,YAAAxB,GAAA,CAAArK,OAAA,CAAqB;UAClEzC,EADgG,CAAAO,UAAA,qBAAoB,oBACjG;UAKbP,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,cAAAuM,GAAA,CAAApJ,YAAA,CAA0B;UAOR1D,EAAA,CAAAM,SAAA,GAA6C;UAE1BN,EAFnB,CAAAO,UAAA,YAAAuM,GAAA,CAAAnI,SAAA,yBAA6C,YAAA3E,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,kBAAAC,MAAA,EAE0C;UAE7FX,EAAA,CAAAM,SAAA,EAA4C;UAA5CN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,kBAAAC,MAAA,CAA4C;UAa9CX,EAAA,CAAAM,SAAA,GAA8D;UAA9DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,YAAAC,MAAA,EAA8D;UAC5DX,EAAA,CAAAM,SAAA,EAAsC;UAAtCN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,YAAAC,MAAA,CAAsC;UAexCX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAAyO,UAAA,0DAAkE;UADlDzO,EAHE,CAAAO,UAAA,UAAAP,EAAA,CAAA0O,WAAA,SAAA5B,GAAA,CAAAtE,SAAA,EAA2B,sBACxB,YAAAsE,GAAA,CAAA7J,cAAA,CAA2B,oBAAoB,cAAA6J,GAAA,CAAA5J,aAAA,CACD,wBAAwB,YAAAlD,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,0BAAAC,MAAA,EACC;UAO1FX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,0BAAAC,MAAA,CAAoD;UAgBtDX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAAyO,UAAA,0DAAkE;UADlEzO,EAJkB,CAAAO,UAAA,UAAAP,EAAA,CAAA0O,WAAA,SAAA5B,GAAA,CAAA7G,SAAA,EAA2B,sBACxB,YAAA6G,GAAA,CAAA3J,cAAA,CAA2B,oBAAoB,cAAA2J,GAAA,CAAA1J,aAAA,CACD,wBAAwB,wBACpD,YAAApD,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,0BAAAC,MAAA,EACqC;UAU1EX,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,0BAAAC,MAAA,CAAoD;UAW9CX,EAAA,CAAAM,SAAA,GAAyC;UAEjDN,EAFQ,CAAAO,UAAA,YAAAuM,GAAA,CAAAnI,SAAA,qBAAyC,YAAA3E,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,kBAAAC,MAAA,EAEmB;UAElEX,EAAA,CAAAM,SAAA,EAA4C;UAA5CN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,kBAAAC,MAAA,CAA4C;UAc9CX,EAAA,CAAAM,SAAA,GAAkE;UAAlEN,EAAA,CAAAyO,UAAA,0DAAkE;UADlDzO,EAHE,CAAAO,UAAA,UAAAP,EAAA,CAAA0O,WAAA,SAAA5B,GAAA,CAAA7D,UAAA,EAA4B,sBACzB,YAAA6D,GAAA,CAAAzJ,eAAA,CAA4B,oBAAoB,cAAAyJ,GAAA,CAAAxJ,cAAA,CACJ,wBAAwB,wBAClD;UAeqBtD,EAAA,CAAAM,SAAA,GAAiB;UAC7EN,EAD4D,CAAAO,UAAA,kBAAiB,kBAC5D;UAMyCP,EAAA,CAAAM,SAAA,GAAiB;UAC3EN,EAD0D,CAAAO,UAAA,kBAAiB,kBAC1D;UAMTP,EAAA,CAAAM,SAAA,GAAyC;UAAzCN,EAAA,CAAAO,UAAA,YAAAuM,GAAA,CAAAnI,SAAA,qBAAyC;UASzC3E,EAAA,CAAAM,SAAA,GAAuC;UAE/CN,EAFQ,CAAAO,UAAA,YAAAuM,GAAA,CAAAnI,SAAA,mBAAuC,YAAA3E,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,oBAAAC,MAAA,EAEuB;UAEpEX,EAAA,CAAAM,SAAA,EAA8C;UAA9CN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,oBAAAC,MAAA,CAA8C;UAeoBX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAmO,UAAA,CAAAnO,EAAA,CAAAoO,eAAA,KAAAO,GAAA,EAA4B;UAChG3O,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAuO,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,UAAAC,MAAA,EAA4D;UAC1DX,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,SAAAuM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAApM,CAAA,UAAAC,MAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}