{"ast": null, "code": "import { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport let SalesOrdersService = /*#__PURE__*/(() => {\n  class SalesOrdersService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n    }\n    fetchOrders(params) {\n      // const headers = new HttpHeaders().set('Authorization', `Bearer ${this.authService.getToken()}`);\n      return this.http.get(ApiConstant.SALES_ORDER_GENERIC, {\n        params\n        // headers,\n      });\n    }\n    fetchOrderStatuses(headers) {\n      return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n        params: headers\n      });\n    }\n    getPartnerFunction(custId) {\n      return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`).pipe(map(res => res.data));\n    }\n    fetchPartnerById(bp_Id) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?filters[bp_id][$eq]=${bp_Id}&populate=address_usages.business_partner_address`);\n    }\n    fetchOrderById(orderId) {\n      return this.http.get(`${ApiConstant.SALES_ORDER}/${orderId}`);\n    }\n    getImages(productId) {\n      console.log(this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), 'image res');\n      return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\n    }\n    static {\n      this.ɵfac = function SalesOrdersService_Factory(t) {\n        return new (t || SalesOrdersService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SalesOrdersService,\n        factory: SalesOrdersService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SalesOrdersService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}