{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/table\";\nimport * as i2 from \"primeng/api\";\nfunction SalesQuotesOverviewComponent_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 29);\n    i0.ɵɵelementStart(2, \"th\", 30);\n    i0.ɵɵtext(3, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\", 31);\n    i0.ɵɵtext(5, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesQuotesOverviewComponent_ng_template_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵelement(4, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"h5\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 39)(11, \"p\", 40);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 41)(14, \"p\", 42);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 43);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const OrderDetailsItems_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", OrderDetailsItems_r1.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(OrderDetailsItems_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", OrderDetailsItems_r1.skuid, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", OrderDetailsItems_r1.qty, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" $\", OrderDetailsItems_r1.price, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", OrderDetailsItems_r1.each_price, \" each \");\n  }\n}\nexport class SalesQuotesOverviewComponent {\n  constructor() {\n    this.OrderDetailsItems = [];\n  }\n  ngOnInit() {\n    this.OrderDetailsItems = [{\n      title: 'Titanium Dioxide TiO2 Pigment & Colorant',\n      skuid: 'A9SMS001',\n      qty: '2',\n      price: '20.98',\n      each_price: '10.49',\n      img: ''\n    }, {\n      title: 'Titanium Dioxide TiO2',\n      skuid: 'A9SMS002',\n      qty: '5',\n      price: '50.25',\n      each_price: '10.5',\n      img: ''\n    }];\n  }\n  static {\n    this.ɵfac = function SalesQuotesOverviewComponent_Factory(t) {\n      return new (t || SalesQuotesOverviewComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesQuotesOverviewComponent,\n      selectors: [[\"app-sales-quotes-overview\"]],\n      decls: 95,\n      vars: 1,\n      consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"v-details-list\", \"grid\", \"relative\"], [1, \"v-details-box\", \"col-4\", \"flex\", \"align-items-center\", \"gap-2\", \"font-medium\", \"text-700\"], [1, \"pi\", \"pi-user\"], [1, \"text\", \"flex\", \"font-semibold\"], [1, \"pi\", \"pi-envelope\"], [1, \"pi\", \"pi-file\"], [1, \"pi\", \"pi-shopping-cart\"], [1, \"pi\", \"pi-briefcase\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"table-data\", \"border-round\", \"overflow-hidden\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"lg:w-30rem\", \"md:w-30rem\", \"sm:w-full\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"mt-2\", \"mb-4\", \"uppercase\", \"text-center\", \"text-primary\"], [1, \"cart-sidebar-price\", \"py-4\", \"border-none\", \"border-y-1\", \"border-solid\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"font-semibold\"], [1, \"text-color-secondary\"], [1, \"cart-sidebar-t-price\", \"py-4\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"text-primary\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-9rem\", \"h-9rem\", \"overflow-hidden\", \"border-round\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"w-full\", \"h-full\", \"object-fit-contain\", 3, \"src\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"]],\n      template: function SalesQuotesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n          i0.ɵɵtext(5, \"Quote Details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"Order #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" : 183001035 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 7);\n          i0.ɵɵelement(14, \"i\", 8);\n          i0.ɵɵelementStart(15, \"div\", 9);\n          i0.ɵɵtext(16, \"Customer #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" : 17100001 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 7);\n          i0.ɵɵelement(19, \"i\", 10);\n          i0.ɵɵelementStart(20, \"div\", 9);\n          i0.ɵɵtext(21, \"Customer Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22, \" : Smith Corp. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 7);\n          i0.ɵɵelement(24, \"i\", 11);\n          i0.ɵɵelementStart(25, \"div\", 9);\n          i0.ɵɵtext(26, \"Purchase Order #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \" : 12358888 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 7);\n          i0.ɵɵelement(29, \"i\", 12);\n          i0.ɵɵelementStart(30, \"div\", 9);\n          i0.ɵɵtext(31, \"Requested Delivery Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" : 02/22/2025 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 7);\n          i0.ɵɵelement(34, \"i\", 13);\n          i0.ɵɵelementStart(35, \"div\", 9);\n          i0.ɵɵtext(36, \"Date Placed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \" : 02/18/2025 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 7);\n          i0.ɵɵelement(39, \"i\", 13);\n          i0.ɵɵelementStart(40, \"div\", 9);\n          i0.ɵɵtext(41, \"Special Instruction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" : \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 2)(44, \"div\", 3)(45, \"h4\", 4);\n          i0.ɵɵtext(46, \"Shipping Details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 5)(48, \"div\", 6)(49, \"div\", 7);\n          i0.ɵɵelement(50, \"i\", 8);\n          i0.ɵɵelementStart(51, \"div\", 9);\n          i0.ɵɵtext(52, \"Business Partner #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" : 183001035 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 7);\n          i0.ɵɵelement(55, \"i\", 8);\n          i0.ɵɵelementStart(56, \"div\", 9);\n          i0.ɵɵtext(57, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(58, \" : 17100001 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 7);\n          i0.ɵɵelement(60, \"i\", 10);\n          i0.ɵɵelementStart(61, \"div\", 9);\n          i0.ɵɵtext(62, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" : Smith Corp. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(64, \"div\", 14)(65, \"div\", 15)(66, \"h4\", 4);\n          i0.ɵɵtext(67, \"Items to be shipped\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 16)(69, \"p-table\", 17);\n          i0.ɵɵtemplate(70, SalesQuotesOverviewComponent_ng_template_70_Template, 6, 0, \"ng-template\", 18)(71, SalesQuotesOverviewComponent_ng_template_71_Template, 18, 7, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"div\", 20)(73, \"div\", 21)(74, \"h5\", 22);\n          i0.ɵɵtext(75, \"Quote Summary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 23)(77, \"ul\", 24)(78, \"li\", 25)(79, \"span\", 26);\n          i0.ɵɵtext(80, \"Subtotal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(81, \" $31.47 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"li\", 25)(83, \"span\", 26);\n          i0.ɵɵtext(84, \"Shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(85, \" $27.00 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"li\", 25)(87, \"span\", 26);\n          i0.ɵɵtext(88, \"Shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(89, \" $2.60 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 27)(91, \"h5\", 28);\n          i0.ɵɵtext(92, \"Total \");\n          i0.ɵɵelementStart(93, \"span\");\n          i0.ɵɵtext(94, \"$61.07\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(69);\n          i0.ɵɵproperty(\"value\", ctx.OrderDetailsItems);\n        }\n      },\n      dependencies: [i1.Table, i2.PrimeTemplate],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "OrderDetailsItems_r1", "img", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "ɵɵtextInterpolate1", "skuid", "qty", "price", "each_price", "SalesQuotesOverviewComponent", "constructor", "OrderDetailsItems", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "SalesQuotesOverviewComponent_Template", "rf", "ctx", "ɵɵtemplate", "SalesQuotesOverviewComponent_ng_template_70_Template", "SalesQuotesOverviewComponent_ng_template_71_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes-details\\sales-quotes-overview\\sales-quotes-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes-details\\sales-quotes-overview\\sales-quotes-overview.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\ninterface OrderDetailsItems {\r\n  title?: string;\r\n  skuid?: string;\r\n  qty?: string;\r\n  price?: string;\r\n  each_price?: string;\r\n  img?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-quotes-overview',\r\n  templateUrl: './sales-quotes-overview.component.html',\r\n  styleUrl: './sales-quotes-overview.component.scss'\r\n})\r\nexport class SalesQuotesOverviewComponent {\r\n\r\n  OrderDetailsItems: OrderDetailsItems[] = [];\r\n\r\n  ngOnInit() {\r\n  \r\n    this.OrderDetailsItems = [\r\n      {\r\n        title: 'Titanium Dioxide TiO2 Pigment & Colorant',\r\n        skuid: 'A9SMS001',\r\n        qty: '2',\r\n        price: '20.98',\r\n        each_price: '10.49',\r\n        img: '',\r\n      },\r\n      {\r\n        title: 'Titanium Dioxide TiO2',\r\n        skuid: 'A9SMS002',\r\n        qty: '5',\r\n        price: '50.25',\r\n        each_price: '10.5',\r\n        img: '',\r\n      },\r\n    ];\r\n  }\r\n}\r\n", "<div class=\"grid mt-0 relative\">\r\n    <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Quote Details</h4>\r\n            </div>\r\n            <div class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <div class=\"v-details-list grid relative\">\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-user\"></i>\r\n                        <div class=\"text flex font-semibold\">Order #</div>\r\n                        : 183001035\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-user\"></i>\r\n                        <div class=\"text flex font-semibold\">Customer #</div>\r\n                        : 17100001\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-envelope\"></i>\r\n                        <div class=\"text flex font-semibold\">Customer Name</div>\r\n                        : Smith Corp.\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-file\"></i>\r\n                        <div class=\"text flex font-semibold\">Purchase Order #</div>\r\n                        : 12358888\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-shopping-cart\"></i>\r\n                        <div class=\"text flex font-semibold\">Requested Delivery Date</div>\r\n                        : 02/22/2025\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-briefcase\"></i>\r\n                        <div class=\"text flex font-semibold\">Date Placed</div>\r\n                        : 02/18/2025\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-briefcase\"></i>\r\n                        <div class=\"text flex font-semibold\">Special Instruction</div>\r\n                        :\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Shipping Details</h4>\r\n            </div>\r\n            <div class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <div class=\"v-details-list grid relative\">\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-user\"></i>\r\n                        <div class=\"text flex font-semibold\">Business Partner #</div>\r\n                        : 183001035\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-user\"></i>\r\n                        <div class=\"text flex font-semibold\">Name</div>\r\n                        : 17100001\r\n                    </div>\r\n                    <div class=\"v-details-box col-4 flex align-items-center gap-2 font-medium text-700\">\r\n                        <i class=\"pi pi-envelope\"></i>\r\n                        <div class=\"text flex font-semibold\">Address</div>\r\n                        : Smith Corp.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading mb-3 flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Items to be shipped</h4>\r\n            </div>\r\n            <div class=\"table-data border-round overflow-hidden\">\r\n                <p-table [value]=\"OrderDetailsItems\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th class=\"surface-50 px-4 py-3 text-700 font-semibold uppercase\"></th>\r\n                            <th class=\"surface-50 py-3 text-700 font-semibold uppercase\">Quantity</th>\r\n                            <th class=\"surface-50 py-3 px-4 text-700 font-semibold uppercase text-right\">Price</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-OrderDetailsItems>\r\n                        <tr>\r\n                            <td class=\"px-0 py-4 border-none border-bottom-1 border-solid border-50\" [width]=\"'60%'\">\r\n                                <div class=\"relative flex gap-3\">\r\n                                    <div\r\n                                        class=\"flex align-items-center justify-content-center w-9rem h-9rem overflow-hidden border-round border-1 border-solid border-50\">\r\n                                        <img [src]=\"OrderDetailsItems.img\" class=\"w-full h-full object-fit-contain\" />\r\n                                    </div>\r\n                                    <div class=\"flex flex-column\">\r\n                                        <h5 class=\"my-2 text-lg\">{{OrderDetailsItems.title}}</h5>\r\n                                        <p class=\"m-0 text-sm font-semibold text-color-secondary\">{{OrderDetailsItems.skuid}}\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            </td>\r\n                            <td class=\"py-4 border-none border-bottom-1 border-solid border-50 vertical-align-top\">\r\n                                <p\r\n                                    class=\"m-0 py-2 font-semibold text-color-secondary border-1 border-round surface-border text-center\">\r\n                                    {{OrderDetailsItems.qty}}\r\n                                </p>\r\n                            </td>\r\n                            <td class=\"py-4 px-4 border-none border-bottom-1 border-solid border-50 vertical-align-top\">\r\n                                <p class=\"m-0 text-lg font-semibold text-right\">\r\n                                    ${{OrderDetailsItems.price}}\r\n                                </p>\r\n                                <p class=\"m-0 font-semibold text-color-secondary text-right\">\r\n                                    {{OrderDetailsItems.each_price}} each\r\n                                </p>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:w-30rem md:w-30rem sm:w-full\">\r\n        <div class=\"p-4 mb-0 w-full bg-white border-round shadow-1 overflow-hidden\">\r\n            <h5 class=\"mt-2 mb-4 uppercase text-center text-primary\">Quote Summary</h5>\r\n            <div class=\"cart-sidebar-price py-4 border-none border-y-1 border-solid surface-border\">\r\n                <ul class=\"flex flex-column gap-3 p-0 m-0\">\r\n                    <li class=\"flex align-items-center justify-content-between font-semibold\">\r\n                        <span class=\"text-color-secondary\">Subtotal</span> $31.47\r\n                    </li>\r\n                    <li class=\"flex align-items-center justify-content-between font-semibold\">\r\n                        <span class=\"text-color-secondary\">Shipping</span> $27.00\r\n                    </li>\r\n                    <li class=\"flex align-items-center justify-content-between font-semibold\">\r\n                        <span class=\"text-color-secondary\">Shipping</span> $2.60\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"cart-sidebar-t-price py-4\">\r\n                <h5 class=\"mb-2 flex align-items-center justify-content-between text-primary\">Total\r\n                    <span>$61.07</span>\r\n                </h5>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;IC+EwBA,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,SAAA,aAAuE;IACvEF,EAAA,CAAAC,cAAA,aAA6D;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1EJ,EAAA,CAAAC,cAAA,aAA6E;IAAAD,EAAA,CAAAG,MAAA,YAAK;IACtFH,EADsF,CAAAI,YAAA,EAAK,EACtF;;;;;IAMOJ,EAHZ,CAAAC,cAAA,SAAI,aACyF,cACpD,cAEyG;IAClID,EAAA,CAAAE,SAAA,cAA8E;IAClFF,EAAA,CAAAI,YAAA,EAAM;IAEFJ,EADJ,CAAAC,cAAA,cAA8B,aACD;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzDJ,EAAA,CAAAC,cAAA,YAA0D;IAAAD,EAAA,CAAAG,MAAA,GAC1D;IAGZH,EAHY,CAAAI,YAAA,EAAI,EACF,EACJ,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAuF,aAEsB;IACrGD,EAAA,CAAAG,MAAA,IACJ;IACJH,EADI,CAAAI,YAAA,EAAI,EACH;IAEDJ,EADJ,CAAAC,cAAA,cAA4F,aACxC;IAC5CD,EAAA,CAAAG,MAAA,IACJ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAA6D;IACzDD,EAAA,CAAAG,MAAA,IACJ;IAERH,EAFQ,CAAAI,YAAA,EAAI,EACH,EACJ;;;;IA3BwEJ,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAM,UAAA,gBAAe;IAIvEN,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,QAAAC,oBAAA,CAAAC,GAAA,EAAAR,EAAA,CAAAS,aAAA,CAA6B;IAGTT,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAU,iBAAA,CAAAH,oBAAA,CAAAI,KAAA,CAA2B;IACMX,EAAA,CAAAK,SAAA,GAC1D;IAD0DL,EAAA,CAAAY,kBAAA,KAAAL,oBAAA,CAAAM,KAAA,MAC1D;IAOJb,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,MAAAL,oBAAA,CAAAO,GAAA,MACJ;IAIId,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,oBAAA,CAAAQ,KAAA,MACJ;IAEIf,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,MAAAL,oBAAA,CAAAS,UAAA,WACJ;;;ADhGhC,OAAM,MAAOC,4BAA4B;EALzCC,YAAA;IAOE,KAAAC,iBAAiB,GAAwB,EAAE;;EAE3CC,QAAQA,CAAA;IAEN,IAAI,CAACD,iBAAiB,GAAG,CACvB;MACER,KAAK,EAAE,0CAA0C;MACjDE,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,OAAO;MACnBR,GAAG,EAAE;KACN,EACD;MACEG,KAAK,EAAE,uBAAuB;MAC9BE,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,MAAM;MAClBR,GAAG,EAAE;KACN,CACF;EACH;;;uBAxBWS,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZzB3B,EAJhB,CAAAC,cAAA,aAAgC,aACY,aACwB,aACsB,YACtB;UAAAD,EAAA,CAAAG,MAAA,oBAAa;UACrEH,EADqE,CAAAI,YAAA,EAAK,EACpE;UAGEJ,EAFR,CAAAC,cAAA,aAAiG,aACnD,aAC8C;UAChFD,EAAA,CAAAE,SAAA,WAA0B;UAC1BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAClDJ,EAAA,CAAAG,MAAA,qBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,YAA0B;UAC1BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACrDJ,EAAA,CAAAG,MAAA,oBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAA8B;UAC9BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACxDJ,EAAA,CAAAG,MAAA,uBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAA0B;UAC1BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC3DJ,EAAA,CAAAG,MAAA,oBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAAmC;UACnCF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAClEJ,EAAA,CAAAG,MAAA,sBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACtDJ,EAAA,CAAAG,MAAA,sBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC9DJ,EAAA,CAAAG,MAAA,WACJ;UAGZH,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UAIEJ,EAFR,CAAAC,cAAA,cAA4D,cACsB,aACtB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UACxEH,EADwE,CAAAI,YAAA,EAAK,EACvE;UAGEJ,EAFR,CAAAC,cAAA,cAAiG,cACnD,cAC8C;UAChFD,EAAA,CAAAE,SAAA,YAA0B;UAC1BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC7DJ,EAAA,CAAAG,MAAA,qBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,YAA0B;UAC1BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC/CJ,EAAA,CAAAG,MAAA,oBACJ;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAoF;UAChFD,EAAA,CAAAE,SAAA,aAA8B;UAC9BF,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAClDJ,EAAA,CAAAG,MAAA,uBACJ;UAGZH,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UAIEJ,EAFR,CAAAC,cAAA,eAAuD,eACgC,aAC3B;UAAAD,EAAA,CAAAG,MAAA,2BAAmB;UAC3EH,EAD2E,CAAAI,YAAA,EAAK,EAC1E;UAEFJ,EADJ,CAAAC,cAAA,eAAqD,mBACZ;UAQjCD,EAPA,CAAA6B,UAAA,KAAAC,oDAAA,0BAAgC,KAAAC,oDAAA,2BAOoB;UAmCpE/B,EAJY,CAAAI,YAAA,EAAU,EACR,EAEJ,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAoD,eAC4B,cACf;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI/DJ,EAHZ,CAAAC,cAAA,eAAwF,cACzC,cACmC,gBACnC;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,gBACvD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEDJ,EADJ,CAAAC,cAAA,cAA0E,gBACnC;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,gBACvD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEDJ,EADJ,CAAAC,cAAA,cAA0E,gBACnC;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,eACvD;UAERH,EAFQ,CAAAI,YAAA,EAAK,EACJ,EACH;UAEFJ,EADJ,CAAAC,cAAA,eAAuC,cAC2C;UAAAD,EAAA,CAAAG,MAAA,cAC1E;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAKhCH,EALgC,CAAAI,YAAA,EAAO,EAClB,EACH,EACJ,EACJ,EACJ;;;UAnEmBJ,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAM,UAAA,UAAAsB,GAAA,CAAAT,iBAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}