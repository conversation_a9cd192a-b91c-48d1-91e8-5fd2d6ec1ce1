{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport let OpportunitiesService = /*#__PURE__*/(() => {\n  class OpportunitiesService {\n    constructor(http, authservice) {\n      this.http = http;\n      this.authservice = authservice;\n      this.opportunitySubject = new BehaviorSubject(null);\n      this.opportunity = this.opportunitySubject.asObservable();\n    }\n    createOpportunity(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`, data);\n    }\n    createFollowup(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION}`, data);\n    }\n    createNote(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n        data\n      });\n    }\n    createEmployee(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}`, {\n        data\n      });\n    }\n    createHierarchy(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}`, {\n        data\n      });\n    }\n    createExistingContact(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}`, {\n        data\n      });\n    }\n    createOpportunityContact(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT_REGISTRATION}`, data);\n    }\n    updateOpportunity(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {\n        data\n      });\n    }\n    updateEmployee(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}/${Id}`, {\n        data\n      });\n    }\n    updateNote(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n        data\n      });\n    }\n    deleteNote(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n    }\n    deleteFollowupItem(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}/${id}`);\n    }\n    deleteEmployee(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}/${id}`);\n    }\n    deleteHierarchy(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}/${id}`);\n    }\n    deleteContact(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}/${id}`);\n    }\n    getOpportunityDropdownOptions(type) {\n      const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getPartners(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      }).pipe(map(response => (response?.data || []).map(item => {\n        return {\n          bp_id: item?.bp_id || '',\n          bp_full_name: item?.bp_full_name || ''\n        };\n      })));\n    }\n    getHierarchy(parentid) {\n      const params = new HttpParams().set('filters[business_transaction_document_relationship_role_code][$eq]', '14').set('filters[parent_object_id][$eq]', parentid);\n      return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}`, {\n        params\n      });\n    }\n    getHierarchyOpportunity(params) {\n      return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n        params\n      }).pipe(map(response => (response?.data || []).map(item => {\n        return {\n          opportunity_id: item?.opportunity_id || '',\n          name: item?.name || ''\n        };\n      })));\n    }\n    getOpportunities(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'opportunity_id,name,expected_revenue_amount,life_cycle_status_code,probability_percent,expected_revenue_start_date,expected_revenue_end_date,updatedAt,last_changed_by').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      } else {\n        params = params.set('sort', 'updatedAt:desc');\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][opportunity_id][$containsi]', searchTerm);\n        params = params.set('filters[$or][1][name][$containsi]', searchTerm);\n      }\n      if (filter) {\n        if (filter === 'MO') {\n          const email = this.authservice.getUserEmail();\n          if (email) {\n            params = params.set(`filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n          }\n        }\n      }\n      return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n        params\n      });\n    }\n    getOpportunityByID(opportunityId) {\n      const params = new HttpParams().set('filters[opportunity_id][$eq]', opportunityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner][fields][1]', 'bp_id').set('populate[business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][4]', 'country').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]', 'website_url').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_contact][populate][contact_persons][fields][0]', 'bp_company_id').set('populate[notes][populate]', '*').set('populate[opportunity_followups][populate]', '*').set('populate[opportunity_contact_parties][populate][business_partner][populate][addresses][populate]', '*').set('populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][0]', 'contact_person_department').set('populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][1]', 'contact_person_department_name').set('populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][2]', 'contact_person_function').set('populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][3]', 'contact_person_function_name').set('populate[opportunity_contact_parties][populate][business_partner][populate][contact_persons][fields][0]', 'id').set('populate[opportunity_sales_team_parties][fields][0]', 'role_code').set('populate[opportunity_sales_team_parties][fields][1]', 'party_id').set('populate[opportunity_sales_team_parties][fields][2]', 'opportunity_id').set('populate[opportunity_sales_team_parties][populate][business_partner][fields][0]', 'first_name').set('populate[opportunity_sales_team_parties][populate][business_partner][fields][1]', 'last_name').set('populate[opportunity_sales_team_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]', 'email_address');\n      return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n        params\n      }).pipe(map(response => {\n        const opportunityDetails = response?.data[0] || null;\n        this.opportunitySubject.next(opportunityDetails);\n        return response;\n      }));\n    }\n    getOpportunity(partnerId) {\n      let params = new HttpParams().set('filters[prospect_party_id][$eq]', partnerId).set('fields', 'opportunity_id,name,expected_revenue_end_date,life_cycle_status_code,group_code,last_changed_by').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n      return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function OpportunitiesService_Factory(t) {\n        return new (t || OpportunitiesService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: OpportunitiesService,\n        factory: OpportunitiesService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return OpportunitiesService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}