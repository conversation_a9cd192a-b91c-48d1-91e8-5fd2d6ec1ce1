{"ast": null, "code": "import { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Directive, Inject, Optional, NgModule } from '@angular/core';\nimport { DomHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\n\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\nlet Ripple = /*#__PURE__*/(() => {\n  class Ripple {\n    document;\n    platformId;\n    renderer;\n    el;\n    zone;\n    config;\n    constructor(document, platformId, renderer, el, zone, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.el = el;\n      this.zone = zone;\n      this.config = config;\n    }\n    animationListener;\n    mouseDownListener;\n    timeout;\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.config && this.config.ripple) {\n          this.zone.runOutsideAngular(() => {\n            this.create();\n            this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n          });\n        }\n      }\n    }\n    onMouseDown(event) {\n      let ink = this.getInk();\n      if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n        return;\n      }\n      DomHandler.removeClass(ink, 'p-ink-active');\n      if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n        let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n        ink.style.height = d + 'px';\n        ink.style.width = d + 'px';\n      }\n      let offset = DomHandler.getOffset(this.el.nativeElement);\n      let x = event.pageX - offset.left + this.document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n      let y = event.pageY - offset.top + this.document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n      this.renderer.setStyle(ink, 'top', y + 'px');\n      this.renderer.setStyle(ink, 'left', x + 'px');\n      DomHandler.addClass(ink, 'p-ink-active');\n      this.timeout = setTimeout(() => {\n        let ink = this.getInk();\n        if (ink) {\n          DomHandler.removeClass(ink, 'p-ink-active');\n        }\n      }, 401);\n    }\n    getInk() {\n      const children = this.el.nativeElement.children;\n      for (let i = 0; i < children.length; i++) {\n        if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n          return children[i];\n        }\n      }\n      return null;\n    }\n    resetInk() {\n      let ink = this.getInk();\n      if (ink) {\n        DomHandler.removeClass(ink, 'p-ink-active');\n      }\n    }\n    onAnimationEnd(event) {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n      }\n      DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n    }\n    create() {\n      let ink = this.renderer.createElement('span');\n      this.renderer.addClass(ink, 'p-ink');\n      this.renderer.appendChild(this.el.nativeElement, ink);\n      this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n      this.renderer.setAttribute(ink, 'role', 'presentation');\n      if (!this.animationListener) {\n        this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n      }\n    }\n    remove() {\n      let ink = this.getInk();\n      if (ink) {\n        this.mouseDownListener && this.mouseDownListener();\n        this.animationListener && this.animationListener();\n        this.mouseDownListener = null;\n        this.animationListener = null;\n        DomHandler.removeElement(ink);\n      }\n    }\n    ngOnDestroy() {\n      if (this.config && this.config.ripple) {\n        this.remove();\n      }\n    }\n    static ɵfac = function Ripple_Factory(t) {\n      return new (t || Ripple)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig, 8));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Ripple,\n      selectors: [[\"\", \"pRipple\", \"\"]],\n      hostAttrs: [1, \"p-ripple\", \"p-element\"]\n    });\n  }\n  return Ripple;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RippleModule = /*#__PURE__*/(() => {\n  class RippleModule {\n    static ɵfac = function RippleModule_Factory(t) {\n      return new (t || RippleModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RippleModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return RippleModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleModule };\n//# sourceMappingURL=primeng-ripple.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}