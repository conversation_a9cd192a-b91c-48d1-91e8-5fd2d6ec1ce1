{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { ActivitiesFormComponent } from 'src/app/store/common-form/activities-form/activities-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"src/app/store/activities/activities.service\";\nimport * as i3 from \"primeng/tooltip\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-form/activities-form/activities-form.component\";\nimport * as i8 from \"@angular/common\";\nfunction AccountActivitiesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 11)(2, \"div\", 12);\n    i0.ɵɵtext(3, \" Subject \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 14)(6, \"div\", 12);\n    i0.ɵɵtext(7, \" Notes \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 16)(10, \"div\", 12);\n    i0.ɵɵtext(11, \" Primary Contact \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 18)(14, \"div\", 12);\n    i0.ɵɵtext(15, \" Start Date/Time \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 20)(18, \"div\", 12);\n    i0.ɵɵtext(19, \" End Date/Time \");\n    i0.ɵɵelement(20, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 22)(22, \"div\", 12);\n    i0.ɵɵtext(23, \" Created On \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\", 24)(26, \"div\", 12);\n    i0.ɵɵtext(27, \" Organizer \");\n    i0.ɵɵelement(28, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"th\", 26)(30, \"div\", 12);\n    i0.ɵɵtext(31, \" Category \");\n    i0.ɵɵelement(32, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"th\", 28)(34, \"div\", 12);\n    i0.ɵɵtext(35, \" Status \");\n    i0.ɵɵelement(36, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"th\", 30)(38, \"div\", 12);\n    i0.ɵɵtext(39, \" Priority \");\n    i0.ɵɵelement(40, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"th\", 22)(42, \"div\", 12);\n    i0.ɵɵtext(43, \" Phone \");\n    i0.ɵɵelement(44, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"th\", 32)(46, \"div\", 12);\n    i0.ɵɵtext(47, \" Activity Type \");\n    i0.ɵɵelement(48, \"p-sortIcon\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountActivitiesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", activity_r1.subject || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r1.stripHtml(activity_r1 == null ? null : activity_r1.globalNote == null ? null : activity_r1.globalNote.note));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.globalNote == null ? null : activity_r1.globalNote.note) ? i0.ɵɵpipeBind3(5, 13, ctx_r1.stripHtml(activity_r1.globalNote.note), 0, 80) + (activity_r1.globalNote.note.length > 80 ? \"...\" : \"\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.business_partner_contact == null ? null : activity_r1.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.start_date) ? i0.ɵɵpipeBind2(10, 17, activity_r1 == null ? null : activity_r1.start_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.end_date) ? i0.ɵɵpipeBind2(13, 20, activity_r1 == null ? null : activity_r1.end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.createdAt) ? i0.ɵɵpipeBind2(16, 23, activity_r1 == null ? null : activity_r1.createdAt, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1 == null ? null : activity_r1.business_partner_organizer == null ? null : activity_r1.business_partner_organizer.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityCategory\", activity_r1 == null ? null : activity_r1.phone_call_category) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityStatus\", activity_r1 == null ? null : activity_r1.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityPriority\", activity_r1 == null ? null : activity_r1.priority) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r1.business_partner == null ? null : activity_r1.business_partner.addresses == null ? null : activity_r1.business_partner.addresses[0] == null ? null : activity_r1.business_partner.addresses[0].phone) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", activity_r1 == null ? null : activity_r1.document_type) || \"-\", \" \");\n  }\n}\nfunction AccountActivitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \" No activities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountActivitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \" Loading activities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountActivitiesComponent {\n  constructor(accountservice, activitiesservice) {\n    this.accountservice = accountservice;\n    this.activitiesservice = activitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.activitiesDetails = null;\n    this.bp_id = '';\n    this.dropdowns = {\n      activityCategory: [],\n      activityStatus: [],\n      activityDocumentType: [],\n      activityPriority: []\n    };\n  }\n  ngOnInit() {\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response.bp_id;\n        if (this.bp_id) {\n          this.activitiesservice.getActivity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n            next: activityResponse => {\n              if (Array.isArray(activityResponse.data)) {\n                const allActivities = activityResponse.data.slice(0, 10);\n                this.activitiesDetails = allActivities.map(activity => {\n                  // Find global note from activity.notes array\n                  const globalNote = activity.notes?.find(note => note.is_global_note === true);\n                  return {\n                    ...activity,\n                    globalNote: globalNote || null\n                  };\n                });\n              } else {\n                this.activitiesDetails = [];\n              }\n            },\n            error: error => {\n              console.error('Error fetching activities:', error);\n            }\n          });\n        }\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  openActivityDialog() {\n    this.activityDialog.showDialog('right');\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountActivitiesComponent_Factory(t) {\n      return new (t || AccountActivitiesComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.ActivitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountActivitiesComponent,\n      selectors: [[\"app-account-activities\"]],\n      viewQuery: function AccountActivitiesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ActivitiesFormComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.activityDialog = _t.first);\n        }\n      },\n      decls: 12,\n      vars: 7,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"value\", \"rows\", \"paginator\", \"scrollable\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [3, \"account_id\"], [\"pSortableColumn\", \"subject\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"subject\"], [\"pSortableColumn\", \"globalNote.note\"], [\"field\", \"globalNote.note\"], [\"pSortableColumn\", \"business_partner_contact.bp_full_name\"], [\"field\", \"business_partner_contact.bp_full_name\"], [\"pSortableColumn\", \"start_date\"], [\"field\", \"start_date\"], [\"pSortableColumn\", \"end_date\"], [\"field\", \"end_date\"], [\"pSortableColumn\", \"createdAt\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"business_partner_organizer.bp_full_name\"], [\"field\", \"business_partner_organizer.bp_full_name\"], [\"pSortableColumn\", \"phone_call_category\"], [\"field\", \"phone_call_category\"], [\"pSortableColumn\", \"activity_status\"], [\"field\", \"activity_status\"], [\"pSortableColumn\", \"priority\"], [\"field\", \"priority\"], [\"pSortableColumn\", \"document_type\"], [\"field\", \"document_type\"], [\"tooltipPosition\", \"top\", \"tooltipStyleClass\", \"multi-line-tooltip\", 3, \"pTooltip\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function AccountActivitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Activities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function AccountActivitiesComponent_Template_p_button_click_4_listener() {\n            return ctx.openActivityDialog();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, AccountActivitiesComponent_ng_template_7_Template, 49, 0, \"ng-template\", 6)(8, AccountActivitiesComponent_ng_template_8_Template, 29, 26, \"ng-template\", 7)(9, AccountActivitiesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, AccountActivitiesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(11, \"app-activities-form\", 10);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.activitiesDetails)(\"rows\", 8)(\"paginator\", true)(\"scrollable\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"account_id\", ctx.bp_id);\n        }\n      },\n      dependencies: [i3.Tooltip, i4.PrimeTemplate, i5.Table, i5.SortableColumn, i5.SortIcon, i6.Button, i7.ActivitiesFormComponent, i8.SlicePipe, i8.DatePipe],\n      styles: [\".multi-line-tooltip {\\n  white-space: normal !important;\\n  max-width: 300px;\\n  word-wrap: break-word;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWNjb3VudC9hY2NvdW50LWRldGFpbHMvYWNjb3VudC1hY3Rpdml0aWVzL2FjY291bnQtYWN0aXZpdGllcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLDhCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5tdWx0aS1saW5lLXRvb2x0aXAge1xyXG4gICAgd2hpdGUtc3BhY2U6IG5vcm1hbCAhaW1wb3J0YW50O1xyXG4gICAgbWF4LXdpZHRoOiAzMDBweDtcclxuICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "ActivitiesFormComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "activity_r1", "subject", "ɵɵpropertyInterpolate", "ctx_r1", "stripHtml", "globalNote", "note", "ɵɵpipeBind3", "length", "business_partner_contact", "bp_full_name", "start_date", "ɵɵpipeBind2", "end_date", "createdAt", "business_partner_organizer", "getLabelFromDropdown", "phone_call_category", "activity_status", "priority", "business_partner", "addresses", "phone", "document_type", "AccountActivitiesComponent", "constructor", "accountservice", "activitiesservice", "unsubscribe$", "activitiesDetails", "bp_id", "dropdowns", "activityCategory", "activityStatus", "activityDocumentType", "activityPriority", "ngOnInit", "loadActivityDropDown", "account", "pipe", "subscribe", "response", "getActivity", "next", "activityResponse", "Array", "isArray", "data", "allActivities", "slice", "map", "activity", "notes", "find", "is_global_note", "error", "console", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "target", "type", "getActivityDropdownOptions", "res", "attr", "label", "description", "value", "code", "dropdownKey", "item", "opt", "openActivityDialog", "activityDialog", "showDialog", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "ActivitiesService", "selectors", "viewQuery", "AccountActivitiesComponent_Query", "rf", "ctx", "ɵɵlistener", "AccountActivitiesComponent_Template_p_button_click_4_listener", "ɵɵtemplate", "AccountActivitiesComponent_ng_template_7_Template", "AccountActivitiesComponent_ng_template_8_Template", "AccountActivitiesComponent_ng_template_9_Template", "AccountActivitiesComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-activities\\account-activities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-activities\\account-activities.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { ActivitiesFormComponent } from 'src/app/store/common-form/activities-form/activities-form.component';\r\n\r\n@Component({\r\n  selector: 'app-account-activities',\r\n  templateUrl: './account-activities.component.html',\r\n  styleUrl: './account-activities.component.scss',\r\n})\r\nexport class AccountActivitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild(ActivitiesFormComponent) activityDialog!: ActivitiesFormComponent;\r\n  public activitiesDetails: any = null;\r\n  public bp_id: string = '';\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activityDocumentType: [],\r\n    activityPriority: [],\r\n  };\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private activitiesservice: ActivitiesService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadActivityDropDown(\r\n      'activityCategory',\r\n      'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n    );\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activityPriority',\r\n      'CRM_ACTIVITY_PRIORITY'\r\n    );\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response.bp_id;\r\n          if (this.bp_id) {\r\n            this.activitiesservice\r\n              .getActivity(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe({\r\n                next: (activityResponse: any) => {\r\n                  if (Array.isArray(activityResponse.data)) {\r\n                    const allActivities = activityResponse.data.slice(0, 10);\r\n                    this.activitiesDetails = allActivities.map(\r\n                      (activity: any) => {\r\n                        // Find global note from activity.notes array\r\n                        const globalNote = activity.notes?.find(\r\n                          (note: any) => note.is_global_note === true\r\n                        );\r\n\r\n                        return {\r\n                          ...activity,\r\n                          globalNote: globalNote || null,\r\n                        };\r\n                      }\r\n                    );\r\n                  } else {\r\n                    this.activitiesDetails = [];\r\n                  }\r\n                },\r\n                error: (error: any) => {\r\n                  console.error('Error fetching activities:', error);\r\n                },\r\n              });\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  openActivityDialog() {\r\n    this.activityDialog.showDialog('right');\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Activities</h4>\r\n\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" (click)=\"openActivityDialog()\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"activitiesDetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"subject\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Subject\r\n                            <p-sortIcon field=\"subject\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"globalNote.note\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Notes\r\n                            <p-sortIcon field=\"globalNote.note\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"business_partner_contact.bp_full_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Primary Contact\r\n                            <p-sortIcon field=\"business_partner_contact.bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"start_date\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Start Date/Time\r\n                            <p-sortIcon field=\"start_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"end_date\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            End Date/Time\r\n                            <p-sortIcon field=\"end_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Created On\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"business_partner_organizer.bp_full_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Organizer\r\n                            <p-sortIcon field=\"business_partner_organizer.bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"phone_call_category\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Category\r\n                            <p-sortIcon field=\"phone_call_category\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"activity_status\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Status\r\n                            <p-sortIcon field=\"activity_status\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"priority\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Priority\r\n                            <p-sortIcon field=\"priority\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Phone\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"document_type\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Activity Type\r\n                            <p-sortIcon field=\"document_type\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-activity>\r\n                <tr>\r\n                    <td>\r\n                        {{ activity.subject || '-' }}\r\n                    </td>\r\n                    <td pTooltip=\"{{ stripHtml(activity?.globalNote?.note) }}\" tooltipPosition=\"top\"\r\n                        tooltipStyleClass=\"multi-line-tooltip\">\r\n                        {{ activity?.globalNote?.note ? (stripHtml(activity.globalNote.note) | slice:0:80) +\r\n                        (activity.globalNote.note.length > 80 ? '...' : '') : '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.business_partner_contact?.bp_full_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.start_date ? (activity?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.end_date ? (activity?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.createdAt ? (activity?.createdAt | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity?.business_partner_organizer?.bp_full_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('activityCategory',\r\n                        activity?.phone_call_category) || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('activityStatus',\r\n                        activity?.activity_status) || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('activityPriority',\r\n                        activity?.priority) || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ activity.business_partner?.addresses?.[0]?.phone || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('activityDocumentType',\r\n                        activity?.document_type) || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">\r\n                        No activities found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">\r\n                        Loading activities data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<app-activities-form [account_id]=\"bp_id\"></app-activities-form>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAGzC,SAASC,uBAAuB,QAAQ,qEAAqE;;;;;;;;;;;;ICWrFC,EAFR,CAAAC,cAAA,SAAI,aAC8B,cACiB;IACvCD,EAAA,CAAAE,MAAA,gBACA;IAAAF,EAAA,CAAAG,SAAA,qBAAyC;IAEjDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAsC,cACS;IACvCD,EAAA,CAAAE,MAAA,cACA;IAAAF,EAAA,CAAAG,SAAA,qBAAiD;IAEzDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAA4D,eACb;IACvCD,EAAA,CAAAE,MAAA,yBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAuE;IAE/EH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAiC,eACc;IACvCD,EAAA,CAAAE,MAAA,yBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA4C;IAEpDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA+B,eACgB;IACvCD,EAAA,CAAAE,MAAA,uBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA0C;IAElDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAgC,eACe;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAEnDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA8D,eACf;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAyE;IAEjFH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA0C,eACK;IACvCD,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAqD;IAE7DH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAsC,eACS;IACvCD,EAAA,CAAAE,MAAA,gBACA;IAAAF,EAAA,CAAAG,SAAA,sBAAiD;IAEzDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA+B,eACgB;IACvCD,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA0C;IAElDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAgC,eACe;IACvCD,EAAA,CAAAE,MAAA,eACA;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAEnDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAAoC,eACW;IACvCD,EAAA,CAAAE,MAAA,uBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA+C;IAG3DH,EAFQ,CAAAI,YAAA,EAAM,EACL,EACJ;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,SACI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAC2C;IACvCD,EAAA,CAAAE,MAAA,GAEJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAEJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IAEJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IA5CGJ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,WAAA,CAAAC,OAAA,aACJ;IACIR,EAAA,CAAAK,SAAA,EAAsD;IAAtDL,EAAA,CAAAS,qBAAA,aAAAC,MAAA,CAAAC,SAAA,CAAAJ,WAAA,kBAAAA,WAAA,CAAAK,UAAA,kBAAAL,WAAA,CAAAK,UAAA,CAAAC,IAAA,EAAsD;IAEtDb,EAAA,CAAAK,SAAA,EAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAK,UAAA,kBAAAL,WAAA,CAAAK,UAAA,CAAAC,IAAA,IAAAb,EAAA,CAAAc,WAAA,QAAAJ,MAAA,CAAAC,SAAA,CAAAJ,WAAA,CAAAK,UAAA,CAAAC,IAAA,aAAAN,WAAA,CAAAK,UAAA,CAAAC,IAAA,CAAAE,MAAA,+BAEJ;IAEIf,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAS,wBAAA,kBAAAT,WAAA,CAAAS,wBAAA,CAAAC,YAAA,cACJ;IAEIjB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAW,UAAA,IAAAlB,EAAA,CAAAmB,WAAA,SAAAZ,WAAA,kBAAAA,WAAA,CAAAW,UAAA,mCAEJ;IAEIlB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAa,QAAA,IAAApB,EAAA,CAAAmB,WAAA,SAAAZ,WAAA,kBAAAA,WAAA,CAAAa,QAAA,mCAEJ;IAEIpB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAc,SAAA,IAAArB,EAAA,CAAAmB,WAAA,SAAAZ,WAAA,kBAAAA,WAAA,CAAAc,SAAA,mCAEJ;IAEIrB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAe,0BAAA,kBAAAf,WAAA,CAAAe,0BAAA,CAAAL,YAAA,cACJ;IAEIjB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,MAAAI,MAAA,CAAAa,oBAAA,qBAAAhB,WAAA,kBAAAA,WAAA,CAAAiB,mBAAA,cAEJ;IAEIxB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,MAAAI,MAAA,CAAAa,oBAAA,mBAAAhB,WAAA,kBAAAA,WAAA,CAAAkB,eAAA,cAEJ;IAEIzB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,MAAAI,MAAA,CAAAa,oBAAA,qBAAAhB,WAAA,kBAAAA,WAAA,CAAAmB,QAAA,cAEJ;IAEI1B,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,WAAA,CAAAoB,gBAAA,kBAAApB,WAAA,CAAAoB,gBAAA,CAAAC,SAAA,kBAAArB,WAAA,CAAAoB,gBAAA,CAAAC,SAAA,qBAAArB,WAAA,CAAAoB,gBAAA,CAAAC,SAAA,IAAAC,KAAA,cACJ;IAEI7B,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,MAAAI,MAAA,CAAAa,oBAAA,yBAAAhB,WAAA,kBAAAA,WAAA,CAAAuB,aAAA,cAEJ;;;;;IAKA9B,EADJ,CAAAC,cAAA,SAAI,aACmD;IAC/CD,EAAA,CAAAE,MAAA,6BACJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACmD;IAC/CD,EAAA,CAAAE,MAAA,gDACJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;AD3IrB,OAAM,MAAO2B,0BAA0B;EAarCC,YACUC,cAA8B,EAC9BC,iBAAoC;IADpC,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAdnB,KAAAC,YAAY,GAAG,IAAItC,OAAO,EAAQ;IAEnC,KAAAuC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,KAAK,GAAW,EAAE;IAElB,KAAAC,SAAS,GAA0B;MACxCC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,oBAAoB,EAAE,EAAE;MACxBC,gBAAgB,EAAE;KACnB;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,uBAAuB,CACxB;IACD,IAAI,CAACX,cAAc,CAACY,OAAO,CACxBC,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACqC,YAAY,CAAC,CAAC,CAClCY,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACX,KAAK,GAAGW,QAAQ,CAACX,KAAK;QAC3B,IAAI,IAAI,CAACA,KAAK,EAAE;UACd,IAAI,CAACH,iBAAiB,CACnBe,WAAW,CAAC,IAAI,CAACZ,KAAK,CAAC,CACvBS,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACqC,YAAY,CAAC,CAAC,CAClCY,SAAS,CAAC;YACTG,IAAI,EAAGC,gBAAqB,IAAI;cAC9B,IAAIC,KAAK,CAACC,OAAO,CAACF,gBAAgB,CAACG,IAAI,CAAC,EAAE;gBACxC,MAAMC,aAAa,GAAGJ,gBAAgB,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACxD,IAAI,CAACpB,iBAAiB,GAAGmB,aAAa,CAACE,GAAG,CACvCC,QAAa,IAAI;kBAChB;kBACA,MAAM9C,UAAU,GAAG8C,QAAQ,CAACC,KAAK,EAAEC,IAAI,CACpC/C,IAAS,IAAKA,IAAI,CAACgD,cAAc,KAAK,IAAI,CAC5C;kBAED,OAAO;oBACL,GAAGH,QAAQ;oBACX9C,UAAU,EAAEA,UAAU,IAAI;mBAC3B;gBACH,CAAC,CACF;cACH,CAAC,MAAM;gBACL,IAAI,CAACwB,iBAAiB,GAAG,EAAE;cAC7B;YACF,CAAC;YACD0B,KAAK,EAAGA,KAAU,IAAI;cACpBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YACpD;WACD,CAAC;QACN;MACF;IACF,CAAC,CAAC;EACN;EAEAnD,SAASA,CAACqD,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEA1B,oBAAoBA,CAAC2B,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAACtC,iBAAiB,CACnBuC,0BAA0B,CAACD,IAAI,CAAC,CAChCzB,SAAS,CAAE2B,GAAQ,IAAI;MACtB,IAAI,CAACpC,SAAS,CAACiC,MAAM,CAAC,GACpBG,GAAG,EAAEpB,IAAI,EAAEG,GAAG,CAAEkB,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAxD,oBAAoBA,CAACyD,WAAmB,EAAEF,KAAa;IACrD,MAAMG,IAAI,GAAG,IAAI,CAAC3C,SAAS,CAAC0C,WAAW,CAAC,EAAEpB,IAAI,CAC3CsB,GAAG,IAAKA,GAAG,CAACJ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOG,IAAI,EAAEL,KAAK,IAAIE,KAAK;EAC7B;EAEAK,kBAAkBA,CAAA;IAChB,IAAI,CAACC,cAAc,CAACC,UAAU,CAAC,OAAO,CAAC;EACzC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnD,YAAY,CAACe,IAAI,EAAE;IACxB,IAAI,CAACf,YAAY,CAACoD,QAAQ,EAAE;EAC9B;;;uBAvGWxD,0BAA0B,EAAA/B,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1F,EAAA,CAAAwF,iBAAA,CAAAG,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAA1B7D,0BAA0B;MAAA8D,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAE1BjG,uBAAuB;;;;;;;;;;;;UCX5BC,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAI,YAAA,EAAK;UAE9DJ,EAAA,CAAAC,cAAA,kBAC0F;UAAjCD,EAAA,CAAAkG,UAAA,mBAAAC,8DAAA;YAAA,OAASF,GAAA,CAAAd,kBAAA,EAAoB;UAAA,EAAC;UAC3FnF,EAFI,CAAAI,YAAA,EAC0F,EACxF;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEwD;UAuIvED,EArIA,CAAAoG,UAAA,IAAAC,iDAAA,0BAAgC,IAAAC,iDAAA,2BA6EW,IAAAC,iDAAA,yBAiDL,KAAAC,kDAAA,yBAOD;UASjDxG,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UACNJ,EAAA,CAAAG,SAAA,+BAAgE;;;UAtJpDH,EAAA,CAAAK,SAAA,GAAmC;UAACL,EAApC,CAAAyG,UAAA,oCAAmC,iBAAiB;UAI/CzG,EAAA,CAAAK,SAAA,GAA2B;UACNL,EADrB,CAAAyG,UAAA,UAAAR,GAAA,CAAA7D,iBAAA,CAA2B,WAAwB,mBAAiC,oBAC5C;UAiJpCpC,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAyG,UAAA,eAAAR,GAAA,CAAA5D,KAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}