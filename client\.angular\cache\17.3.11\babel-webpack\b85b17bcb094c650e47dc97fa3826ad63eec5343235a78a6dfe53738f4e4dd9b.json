{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let SalesQuotesService = /*#__PURE__*/(() => {\n  class SalesQuotesService {\n    constructor(http) {\n      this.http = http;\n    }\n    fetchSalesquoteOrders(params) {\n      return this.http.get(ApiConstant.SALES_QUOTE_GENERIC, {\n        params\n        // headers,\n      });\n    }\n    getPartnerFunction(custId) {\n      console.log(custId, 'CustId-->');\n      return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`).pipe(map(res => res.data));\n    }\n    fetchOrderStatuses(headers) {\n      return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n        params: headers\n      });\n    }\n    getImages(productId) {\n      console.log(this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), 'image res');\n      return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\n    }\n    getQuoteDetails(data) {\n      const params = new HttpParams().append('DOC_TYPE', data.DOC_TYPE);\n      return this.http.get(`${ApiConstant.SALES_QUOTE}/${data.SD_DOC}`, {\n        params\n      });\n    }\n    getAllStatuses() {\n      return this.http.get(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=QUOTE_STATUS');\n    }\n    static {\n      this.ɵfac = function SalesQuotesService_Factory(t) {\n        return new (t || SalesQuotesService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SalesQuotesService,\n        factory: SalesQuotesService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SalesQuotesService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}