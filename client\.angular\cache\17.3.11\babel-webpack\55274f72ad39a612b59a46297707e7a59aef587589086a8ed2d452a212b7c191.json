{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport { catchError, of } from \"rxjs\";\nimport { environment } from \"src/environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/core/services/content-vendor.service\";\nimport * as i5 from \"../forgot-password/forgot-password.component\";\nconst _c0 = () => [\"/auth/signup\"];\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, auth, router, route, CMSservice) {\n      this.fb = fb;\n      this.auth = auth;\n      this.router = router;\n      this.route = route;\n      this.CMSservice = CMSservice;\n      this.API_ENDPOINT = environment.apiEndpoint;\n      this.isSubmitting = false;\n      this.loginForm = this.fb.nonNullable.group({\n        email: [\"\", [Validators.required, Validators.email]],\n        password: [\"\", [Validators.required]],\n        rememberMe: [false]\n      });\n      this.errMsg = null;\n      this.loading = false;\n      this.showPass = false;\n      this.logo = '';\n      this.loginDetails = {};\n      this.privecyPolicy = {};\n      this.termsAndConditions = {};\n      this.copyright = \"\";\n      this.rorigin = window.location.origin;\n      // forgotPassword() {\n      //   // this.dialog.open(ForgotPasswordComponent);\n      // \n      this.isDialogVisible = false;\n    }\n    ngOnInit() {\n      this.commonContent = this.route.snapshot.parent?.data['commonContent'];\n      const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"crm.logo\");\n      if (logoComponent?.length) {\n        this.logo = logoComponent[0].Logo?.url || '';\n      }\n    }\n    get email() {\n      return this.loginForm.get(\"email\");\n    }\n    get password() {\n      return this.loginForm.get(\"password\");\n    }\n    get rememberMe() {\n      return this.loginForm.get(\"rememberMe\");\n    }\n    login() {\n      this.isSubmitting = true;\n      this.auth.login(this.email.value, this.password.value, this.rememberMe.value).pipe(catchError(err => of(err.error))).subscribe(res => {\n        this.isSubmitting = false;\n        if (res.jwt) {\n          this.router.navigate([\"store\"]);\n        } else {\n          this.errMsg = res?.error?.message || 'Error while login';\n        }\n      });\n    }\n    reset() {\n      this.loginForm.reset();\n      this.errMsg = null;\n    }\n    forgotPassword() {\n      this.isDialogVisible = true;\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ContentVendorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 53,\n        vars: 15,\n        consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"lg:pt-6\", \"pb-6\", \"md:pt-4\", \"pb-4\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"xl:max-w-15rem\", \"lg:max-w-12rem\", \"max-w-12rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"alt\", \"Logo\", 1, \"w-full\", 3, \"src\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\", \"lg:p-6\", \"md:p-4\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"form-group\", \"relative\", \"mb-3\"], [\"type\", \"email\", \"id\", \"username\", \"formControlName\", \"email\", \"placeholder\", \"Enter Email Address\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [\"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"type\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\", 3, \"click\"], [1, \"form-group\", \"relative\", \"mb-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"type\", \"checkbox\", \"formControlName\", \"rememberMe\", \"id\", \"exampleCheck1\", 1, \"form-check-box\", \"m-0\", \"w-1rem\", \"h-1rem\"], [\"for\", \"exampleCheck1\", 1, \"text-m\", \"text-gray-700\"], [\"type\", \"button\", 1, \"p-component\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\", \"border-none\", \"bg-white-alpha-10\", \"underline\", \"cursor-pointer\", 3, \"click\"], [1, \"form-footer\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\", 3, \"click\", \"disabled\"], [1, \"mt-3\", \"mb-3\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\"], [1, \"material-symbols-rounded\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\", 3, \"href\"], [3, \"visibleChange\", \"visible\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n            i0.ɵɵelement(5, \"img\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6);\n            i0.ɵɵtext(7, \" You don't have any account? \");\n            i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n            i0.ɵɵtext(10, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Sign Up \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10)(14, \"h1\", 11);\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"p\", 12);\n            i0.ɵɵtext(17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 13);\n            i0.ɵɵelement(19, \"input\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 13);\n            i0.ɵɵelement(21, \"input\", 15);\n            i0.ɵɵelementStart(22, \"button\", 16)(23, \"span\", 17);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_23_listener() {\n              return ctx.showPass = !ctx.showPass;\n            });\n            i0.ɵɵtext(24, \"visibility\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19);\n            i0.ɵɵelement(27, \"input\", 20);\n            i0.ɵɵelementStart(28, \"label\", 21);\n            i0.ɵɵtext(29, \"Remember Me\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_30_listener() {\n              return ctx.forgotPassword();\n            });\n            i0.ɵɵtext(31, \"Having Trouble in Login?\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 23)(33, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_33_listener() {\n              return ctx.login();\n            });\n            i0.ɵɵtext(34, \" Login\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 25)(36, \"span\");\n            i0.ɵɵtext(37, \"Or Login With\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"a\", 26)(39, \"span\", 27);\n            i0.ɵɵtext(40, \"key\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(41, \" Login with SSO\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(42, \"div\", 28)(43, \"p\", 29);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"ul\", 30)(46, \"li\")(47, \"a\", 31);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"li\")(50, \"a\", 31);\n            i0.ɵɵtext(51);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(52, \"app-forgot-password\", 32);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function LoginComponent_Template_app_forgot_password_visibleChange_52_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.isDialogVisible, $event) || (ctx.isDialogVisible = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c0));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.loginDetails.Title, \" Welcome \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.loginDetails.Sub_Title, \" Login in to your account.\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.showPass ? \"text\" : \"password\");\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"disabled\", !!ctx.loginForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance(5);\n            i0.ɵɵattribute(\"href\", ctx.API_ENDPOINT + \"/api/auth/login?origin=\" + ctx.rorigin, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"label.copyright\"]) || \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"href\", (ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"link.tearmsAndConditions\"]) || \"\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"label.tearmsAndConditions\"]) || \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"href\", (ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"link.privacyPolicy\"]) || \"\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"label.privacyPolicy\"]) || \"\");\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.isDialogVisible);\n          }\n        },\n        dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i5.ForgotPasswordComponent],\n        styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]{max-width:1440px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]{max-width:480px!important}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%]{right:12px;height:24px;width:24px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%]{accent-color:var(--primarycolor)}.p-inputtext[_ngcontent-%COMP%]{height:3rem;appearance:auto!important}.h-3-3rem[_ngcontent-%COMP%]{height:3.3rem}@media only screen and (max-width: 996px){.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]{max-width:380px!important}}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}