{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { CustomerRoutingModule } from './customer-routing.module';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { RippleModule } from 'primeng/ripple';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { SliderModule } from 'primeng/slider';\nimport { RatingModule } from 'primeng/rating';\nimport { HttpClientModule } from '@angular/common/http';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nexport let CustomerModule = /*#__PURE__*/(() => {\n  class CustomerModule {\n    static {\n      this.ɵfac = function CustomerModule_Factory(t) {\n        return new (t || CustomerModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CustomerModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, CustomerRoutingModule, FormsModule, TableModule, RatingModule, ButtonModule, SliderModule, InputTextModule, ToggleButtonModule, RippleModule, MultiSelectModule, DropdownModule, ProgressBarModule, ToastModule, HttpClientModule, InputNumberModule, TabViewModule, AutoCompleteModule]\n      });\n    }\n  }\n  return CustomerModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}