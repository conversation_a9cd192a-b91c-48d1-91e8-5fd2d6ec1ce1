{"ast": null, "code": "import { forkJoin, map, tap } from 'rxjs';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/service-ticket.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nexport class ServiceTicketsListingComponent {\n  constructor(service, _snackBar, authService) {\n    this.service = service;\n    this._snackBar = _snackBar;\n    this.authService = authService;\n    this.items = [{\n      label: 'Invoices',\n      routerLink: ['/store/invoices']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.statuses = [];\n    this.types = [];\n    this.invoices = [];\n    this.loading = false;\n    this.sellerDetails = {};\n    this.searchParams = {\n      fromDate: \"\",\n      toDate: \"\",\n      status: \"all\"\n    };\n    this.statusByCode = {};\n    this.maxDate = new Date();\n    this.sellerDetails = {\n      ...this.authService.partnerFunction\n    };\n  }\n  ngOnInit() {\n    this.loadOptions();\n  }\n  search() {\n    this.loading = true;\n    const obj = {\n      ...this.getDateRange(),\n      DOC_STATUS: this.searchParams.status,\n      DOC_TYPE: this.searchParams.type,\n      SOLDTO: this.sellerDetails.customer_id,\n      VKORG: this.sellerDetails.sales_organization,\n      COUNT: 100\n    };\n    this.service.getAll(obj).pipe(map(x => {\n      this.invoices = x.INVOICELIST;\n      return x.INVOICELIST;\n    }), tap(_ => this.loading = false)).subscribe();\n  }\n  loadOptions() {\n    this.loading = true;\n    forkJoin([this.service.getAllTicketStatus()]).subscribe({\n      next: results => {\n        this.statuses = [{\n          code: results[0].data.map(val => val.code).join(';'),\n          description: \"All\"\n        }, ...results[0].data];\n        this.searchParams.status = this.statuses[0].code;\n        this.statuses.reduce((acc, value) => {\n          acc[value.code] = value.description;\n          return acc;\n        }, this.statusByCode);\n        this.search();\n      },\n      error: () => {\n        this.loading = false;\n        // this._snackBar.open(\"Error while processing your request.\", { type: 'Error' });\n      }\n    });\n  }\n  clear() {\n    this.searchParams = {\n      fromDate: \"\",\n      toDate: \"\",\n      status: this.statuses[0].code\n    };\n  }\n  getDateRange() {\n    const fromDate = this.formatSearchDate(this.searchParams.fromDate);\n    let toDate = this.formatSearchDate(this.searchParams.toDate);\n    if (fromDate && !toDate) {\n      toDate = this.formatSearchDate(new Date());\n    }\n    return {\n      DOCUMENT_DATE: fromDate,\n      DOCUMENT_DATE_TO: toDate\n    };\n  }\n  formatSearchDate(date) {\n    if (!date) return \"\";\n    return moment(date).format(\"YYYYMMDD\");\n  }\n  formatDate(input) {\n    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');\n  }\n  customSort(event) {\n    const sort = {\n      DAYS_PAST_DUE: (a, b) => {\n        return (parseInt(a.SD_DOC) - parseInt(b.SD_DOC)) * (event.order || 1);\n      },\n      All: (a, b) => {\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\n        return 0;\n      }\n    };\n    event.data?.sort(event.field == \"DAYS_PAST_DUE\" ? sort.DAYS_PAST_DUE : sort.All);\n  }\n  static {\n    this.ɵfac = function ServiceTicketsListingComponent_Factory(t) {\n      return new (t || ServiceTicketsListingComponent)(i0.ɵɵdirectiveInject(i1.ServiceTicketService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ServiceTicketsListingComponent,\n      selectors: [[\"app-service-tickets-listing\"]],\n      decls: 2,\n      vars: 0,\n      template: function ServiceTicketsListingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"Service Ticket listing\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["fork<PERSON><PERSON>n", "map", "tap", "moment", "ServiceTicketsListingComponent", "constructor", "service", "_snackBar", "authService", "items", "label", "routerLink", "home", "icon", "statuses", "types", "invoices", "loading", "sellerDetails", "searchParams", "fromDate", "toDate", "status", "statusByCode", "maxDate", "Date", "partnerFunction", "ngOnInit", "loadOptions", "search", "obj", "getDateRange", "DOC_STATUS", "DOC_TYPE", "type", "SOLDTO", "customer_id", "VKORG", "sales_organization", "COUNT", "getAll", "pipe", "x", "INVOICELIST", "_", "subscribe", "getAllTicketStatus", "next", "results", "code", "data", "val", "join", "description", "reduce", "acc", "value", "error", "clear", "formatSearchDate", "DOCUMENT_DATE", "DOCUMENT_DATE_TO", "date", "format", "formatDate", "input", "customSort", "event", "sort", "DAYS_PAST_DUE", "a", "b", "parseInt", "SD_DOC", "order", "All", "field", "i0", "ɵɵdirectiveInject", "i1", "ServiceTicketService", "i2", "MessageService", "i3", "AuthService", "selectors", "decls", "vars", "template", "ServiceTicketsListingComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets-listing\\service-tickets-listing\\service-tickets-listing.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets-listing\\service-tickets-listing\\service-tickets-listing.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem, MessageService, SortEvent } from 'primeng/api';\r\nimport { ServiceTicketService } from '../../services/service-ticket.service';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { forkJoin, map, tap } from 'rxjs';\r\nimport * as moment from 'moment';\r\n\r\n@Component({\r\n  selector: 'app-service-tickets-listing',\r\n  templateUrl: './service-tickets-listing.component.html',\r\n  styleUrl: './service-tickets-listing.component.scss'\r\n})\r\nexport class ServiceTicketsListingComponent {\r\n  items: MenuItem[] | any = [\r\n    { label: 'Invoices', routerLink: ['/store/invoices'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n\r\n  statuses: any[] = [];\r\n  types: any[] = [];\r\n  invoices: any[] = [];\r\n  loading = false;\r\n  sellerDetails: any = {};\r\n  searchParams: any = {\r\n    fromDate: \"\",\r\n    toDate: \"\",\r\n    status: \"all\",\r\n  };\r\n  statusByCode: any = {};\r\n\r\n  maxDate = new Date();\r\n\r\n  constructor(\r\n    private service: ServiceTicketService,\r\n    private _snackBar: MessageService,\r\n    public authService: AuthService,\r\n  ) {\r\n    this.sellerDetails = {\r\n      ...this.authService.partnerFunction\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadOptions();\r\n  }\r\n\r\n  search(): void {\r\n    this.loading = true;\r\n    const obj: any = {\r\n      ...this.getDateRange(),\r\n      DOC_STATUS: this.searchParams.status,\r\n      DOC_TYPE: this.searchParams.type,\r\n      SOLDTO: this.sellerDetails.customer_id,\r\n      VKORG: this.sellerDetails.sales_organization,\r\n      COUNT: 100\r\n    };\r\n    this.service.getAll(obj).pipe(\r\n      map((x) => {\r\n        this.invoices = x.INVOICELIST;\r\n        return x.INVOICELIST\r\n      }),\r\n      tap((_) => (this.loading = false))\r\n    ).subscribe();\r\n  }\r\n\r\n  loadOptions() {\r\n    this.loading = true;\r\n    forkJoin([\r\n      this.service.getAllTicketStatus(),\r\n    ]).subscribe({\r\n      next: (results) => {\r\n        this.statuses = [\r\n          { code: results[0].data.map((val: any) => val.code).join(';'), description: \"All\" },\r\n          ...results[0].data,\r\n        ];\r\n        this.searchParams.status = this.statuses[0].code;\r\n        this.statuses.reduce((acc: { [x: string]: any; }, value: { code: string | number; description: any; }) => {\r\n          acc[value.code] = value.description;\r\n          return acc;\r\n        }, this.statusByCode);\r\n        this.search();\r\n      },\r\n      error: () => {\r\n        this.loading = false;\r\n        // this._snackBar.open(\"Error while processing your request.\", { type: 'Error' });\r\n      },\r\n    });\r\n  }\r\n\r\n  clear() {\r\n    this.searchParams = {\r\n      fromDate: \"\",\r\n      toDate: \"\",\r\n      status: this.statuses[0].code,\r\n    };\r\n  }\r\n\r\n  getDateRange() {\r\n    const fromDate = this.formatSearchDate(this.searchParams.fromDate);\r\n    let toDate = this.formatSearchDate(this.searchParams.toDate);\r\n    if (fromDate && !toDate) {\r\n      toDate = this.formatSearchDate(new Date());\r\n    }\r\n    return {\r\n      DOCUMENT_DATE: fromDate,\r\n      DOCUMENT_DATE_TO: toDate\r\n    }\r\n  }\r\n\r\n  formatSearchDate(date: Date) {\r\n    if (!date) return \"\";\r\n    return moment(date).format(\"YYYYMMDD\");\r\n  }\r\n\r\n  formatDate(input: string) {\r\n    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');\r\n  }\r\n\r\n  customSort(event: SortEvent) {\r\n    const sort = {\r\n      DAYS_PAST_DUE: (a: any, b: any) => {\r\n        return (parseInt(a.SD_DOC) - parseInt(b.SD_DOC)) * (event.order || 1);\r\n      },\r\n      All: (a: any, b: any) => {\r\n        if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\r\n        if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\r\n        return 0;\r\n      }\r\n    };\r\n    event.data?.sort(event.field == \"DAYS_PAST_DUE\" ? sort.DAYS_PAST_DUE : sort.All);\r\n  }\r\n}\r\n", "<p>Service Ticket listing</p>"], "mappings": "AAIA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACzC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;;;AAOhC,OAAM,MAAOC,8BAA8B;EAoBzCC,YACUC,OAA6B,EAC7BC,SAAyB,EAC1BC,WAAwB;IAFvB,KAAAF,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACV,KAAAC,WAAW,GAAXA,WAAW;IAtBpB,KAAAC,KAAK,GAAqB,CACxB;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,CACvD;IACD,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAEhE,KAAAG,QAAQ,GAAU,EAAE;IACpB,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,aAAa,GAAQ,EAAE;IACvB,KAAAC,YAAY,GAAQ;MAClBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;KACT;IACD,KAAAC,YAAY,GAAQ,EAAE;IAEtB,KAAAC,OAAO,GAAG,IAAIC,IAAI,EAAE;IAOlB,IAAI,CAACP,aAAa,GAAG;MACnB,GAAG,IAAI,CAACV,WAAW,CAACkB;KACrB;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAMa,GAAG,GAAQ;MACf,GAAG,IAAI,CAACC,YAAY,EAAE;MACtBC,UAAU,EAAE,IAAI,CAACb,YAAY,CAACG,MAAM;MACpCW,QAAQ,EAAE,IAAI,CAACd,YAAY,CAACe,IAAI;MAChCC,MAAM,EAAE,IAAI,CAACjB,aAAa,CAACkB,WAAW;MACtCC,KAAK,EAAE,IAAI,CAACnB,aAAa,CAACoB,kBAAkB;MAC5CC,KAAK,EAAE;KACR;IACD,IAAI,CAACjC,OAAO,CAACkC,MAAM,CAACV,GAAG,CAAC,CAACW,IAAI,CAC3BxC,GAAG,CAAEyC,CAAC,IAAI;MACR,IAAI,CAAC1B,QAAQ,GAAG0B,CAAC,CAACC,WAAW;MAC7B,OAAOD,CAAC,CAACC,WAAW;IACtB,CAAC,CAAC,EACFzC,GAAG,CAAE0C,CAAC,IAAM,IAAI,CAAC3B,OAAO,GAAG,KAAM,CAAC,CACnC,CAAC4B,SAAS,EAAE;EACf;EAEAjB,WAAWA,CAAA;IACT,IAAI,CAACX,OAAO,GAAG,IAAI;IACnBjB,QAAQ,CAAC,CACP,IAAI,CAACM,OAAO,CAACwC,kBAAkB,EAAE,CAClC,CAAC,CAACD,SAAS,CAAC;MACXE,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAAClC,QAAQ,GAAG,CACd;UAAEmC,IAAI,EAAED,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI,CAACjD,GAAG,CAAEkD,GAAQ,IAAKA,GAAG,CAACF,IAAI,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;UAAEC,WAAW,EAAE;QAAK,CAAE,EACnF,GAAGL,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI,CACnB;QACD,IAAI,CAAC/B,YAAY,CAACG,MAAM,GAAG,IAAI,CAACR,QAAQ,CAAC,CAAC,CAAC,CAACmC,IAAI;QAChD,IAAI,CAACnC,QAAQ,CAACwC,MAAM,CAAC,CAACC,GAA0B,EAAEC,KAAmD,KAAI;UACvGD,GAAG,CAACC,KAAK,CAACP,IAAI,CAAC,GAAGO,KAAK,CAACH,WAAW;UACnC,OAAOE,GAAG;QACZ,CAAC,EAAE,IAAI,CAAChC,YAAY,CAAC;QACrB,IAAI,CAACM,MAAM,EAAE;MACf,CAAC;MACD4B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxC,OAAO,GAAG,KAAK;QACpB;MACF;KACD,CAAC;EACJ;EAEAyC,KAAKA,CAAA;IACH,IAAI,CAACvC,YAAY,GAAG;MAClBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,IAAI,CAACR,QAAQ,CAAC,CAAC,CAAC,CAACmC;KAC1B;EACH;EAEAlB,YAAYA,CAAA;IACV,MAAMX,QAAQ,GAAG,IAAI,CAACuC,gBAAgB,CAAC,IAAI,CAACxC,YAAY,CAACC,QAAQ,CAAC;IAClE,IAAIC,MAAM,GAAG,IAAI,CAACsC,gBAAgB,CAAC,IAAI,CAACxC,YAAY,CAACE,MAAM,CAAC;IAC5D,IAAID,QAAQ,IAAI,CAACC,MAAM,EAAE;MACvBA,MAAM,GAAG,IAAI,CAACsC,gBAAgB,CAAC,IAAIlC,IAAI,EAAE,CAAC;IAC5C;IACA,OAAO;MACLmC,aAAa,EAAExC,QAAQ;MACvByC,gBAAgB,EAAExC;KACnB;EACH;EAEAsC,gBAAgBA,CAACG,IAAU;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,OAAO3D,MAAM,CAAC2D,IAAI,CAAC,CAACC,MAAM,CAAC,UAAU,CAAC;EACxC;EAEAC,UAAUA,CAACC,KAAa;IACtB,OAAO9D,MAAM,CAAC8D,KAAK,EAAE,UAAU,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;EACvD;EAEAG,UAAUA,CAACC,KAAgB;IACzB,MAAMC,IAAI,GAAG;MACXC,aAAa,EAAEA,CAACC,CAAM,EAAEC,CAAM,KAAI;QAChC,OAAO,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAAC,GAAGD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,KAAKN,KAAK,CAACO,KAAK,IAAI,CAAC,CAAC;MACvE,CAAC;MACDC,GAAG,EAAEA,CAACL,CAAM,EAAEC,CAAM,KAAI;QACtB,IAAID,CAAC,CAACH,KAAK,CAACS,KAAK,IAAI,EAAE,CAAC,GAAGL,CAAC,CAACJ,KAAK,CAACS,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,IAAIT,KAAK,CAACO,KAAK,IAAI,CAAC,CAAC;QAC/E,IAAIJ,CAAC,CAACH,KAAK,CAACS,KAAK,IAAI,EAAE,CAAC,GAAGL,CAAC,CAACJ,KAAK,CAACS,KAAK,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAIT,KAAK,CAACO,KAAK,IAAI,CAAC,CAAC;QAC9E,OAAO,CAAC;MACV;KACD;IACDP,KAAK,CAACjB,IAAI,EAAEkB,IAAI,CAACD,KAAK,CAACS,KAAK,IAAI,eAAe,GAAGR,IAAI,CAACC,aAAa,GAAGD,IAAI,CAACO,GAAG,CAAC;EAClF;;;uBAtHWvE,8BAA8B,EAAAyE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BhF,8BAA8B;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ3Cb,EAAA,CAAAe,cAAA,QAAG;UAAAf,EAAA,CAAAgB,MAAA,6BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}