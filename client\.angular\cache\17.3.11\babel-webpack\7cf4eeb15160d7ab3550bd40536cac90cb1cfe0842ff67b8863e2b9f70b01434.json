{"ast": null, "code": "import { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class SalesOrdersService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n  }\n  fetchOrders(params) {\n    // const headers = new HttpHeaders().set('Authorization', `Bearer ${this.authService.getToken()}`);\n    return this.http.get(ApiConstant.SALES_ORDER_GENERIC, {\n      params\n      // headers,\n    });\n  }\n  fetchOrderStatuses(headers) {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n      params: headers\n    });\n  }\n  getPartnerFunction(custId) {\n    return this.http.get(`${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`).pipe(map(res => res.data));\n  }\n  fetchPartnerById(bp_Id) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?filters[bp_id][$eq]=${bp_Id}&populate=address_usages.business_partner_address`);\n  }\n  fetchOrderById(orderId) {\n    return this.http.get(`${ApiConstant.SALES_ORDER}/${orderId}`);\n  }\n  getImages(productId) {\n    console.log(this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`), 'image res');\n    return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`);\n  }\n  static {\n    this.ɵfac = function SalesOrdersService_Factory(t) {\n      return new (t || SalesOrdersService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SalesOrdersService,\n      factory: SalesOrdersService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConstant", "CMS_APIContstant", "map", "SalesOrdersService", "constructor", "http", "authService", "fetchOrders", "params", "get", "SALES_ORDER_GENERIC", "fetchOrderStatuses", "headers", "CONFIG_DATA", "getPartnerFunction", "custId", "CUSTOMER_PARTNER_FUNCTION", "pipe", "res", "data", "fetchPartnerById", "bp_Id", "PARTNERS", "fetchOrderById", "orderId", "SALES_ORDER", "getImages", "productId", "console", "log", "PRODUCT_MDEIA", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { map } from 'rxjs';\r\ninterface AccountTableData {\r\n  PURCH_NO?: string;\r\n  SD_DOC?: string;\r\n  CHANNEL?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_STATUS?: string;\r\n  TXN_CURRENCY?: string;\r\n  DOC_DATE?: string;\r\n  TOTAL_NET_AMOUNT?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SalesOrdersService {\r\n  constructor(private http: HttpClient, private authService: AuthService) {}\r\n\r\n  fetchOrders(params: any): Observable<{ resultData: AccountTableData[] }> {\r\n    // const headers = new HttpHeaders().set('Authorization', `Bearer ${this.authService.getToken()}`);\r\n    return this.http.get<{ resultData: AccountTableData[] }>(\r\n      ApiConstant.SALES_ORDER_GENERIC,\r\n      {\r\n        params,\r\n        // headers,\r\n      }\r\n    );\r\n  }\r\n\r\n  fetchOrderStatuses(headers: any): Observable<string[]> {\r\n    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA, {\r\n      params: headers,\r\n    });\r\n  }\r\n\r\n  getPartnerFunction(custId: string) {\r\n    return this.http\r\n      .get<any>(\r\n        `${CMS_APIContstant.CUSTOMER_PARTNER_FUNCTION}?filters[customer_id][$eq]=${custId}&&populate=customer`\r\n      )\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  fetchPartnerById(bp_Id: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.PARTNERS}?filters[bp_id][$eq]=${bp_Id}&populate=address_usages.business_partner_address`\r\n    );\r\n  }\r\n\r\n  fetchOrderById(orderId: string) {\r\n    return this.http.get<any>(`${ApiConstant.SALES_ORDER}/${orderId}`);\r\n  }\r\n\r\n  getImages(productId: string) {\r\n    console.log(\r\n      this.http.get<any>(\r\n        `${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`\r\n      ),\r\n      'image res'\r\n    );\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.PRODUCT_MDEIA}?filters[product_id][$eq]=${productId}`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAIA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SAASC,GAAG,QAAQ,MAAM;;;;AAe1B,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB,EAAUC,WAAwB;IAAlD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,WAAW,GAAXA,WAAW;EAAgB;EAEzEC,WAAWA,CAACC,MAAW;IACrB;IACA,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAClBT,WAAW,CAACU,mBAAmB,EAC/B;MACEF;MACA;KACD,CACF;EACH;EAEAG,kBAAkBA,CAACC,OAAY;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACI,GAAG,CAAMR,gBAAgB,CAACY,WAAW,EAAE;MACtDL,MAAM,EAAEI;KACT,CAAC;EACJ;EAEAE,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAACV,IAAI,CACbI,GAAG,CACF,GAAGR,gBAAgB,CAACe,yBAAyB,8BAA8BD,MAAM,qBAAqB,CACvG,CACAE,IAAI,CAACf,GAAG,CAAEgB,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC;EACjC;EAEAC,gBAAgBA,CAACC,KAAa;IAC5B,OAAO,IAAI,CAAChB,IAAI,CAACI,GAAG,CAClB,GAAGR,gBAAgB,CAACqB,QAAQ,wBAAwBD,KAAK,mDAAmD,CAC7G;EACH;EAEAE,cAAcA,CAACC,OAAe;IAC5B,OAAO,IAAI,CAACnB,IAAI,CAACI,GAAG,CAAM,GAAGT,WAAW,CAACyB,WAAW,IAAID,OAAO,EAAE,CAAC;EACpE;EAEAE,SAASA,CAACC,SAAiB;IACzBC,OAAO,CAACC,GAAG,CACT,IAAI,CAACxB,IAAI,CAACI,GAAG,CACX,GAAGR,gBAAgB,CAAC6B,aAAa,6BAA6BH,SAAS,EAAE,CAC1E,EACD,WAAW,CACZ;IACD,OAAO,IAAI,CAACtB,IAAI,CAACI,GAAG,CAClB,GAAGR,gBAAgB,CAAC6B,aAAa,6BAA6BH,SAAS,EAAE,CAC1E;EACH;;;uBAhDWxB,kBAAkB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAlBjC,kBAAkB;MAAAkC,OAAA,EAAlBlC,kBAAkB,CAAAmC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}