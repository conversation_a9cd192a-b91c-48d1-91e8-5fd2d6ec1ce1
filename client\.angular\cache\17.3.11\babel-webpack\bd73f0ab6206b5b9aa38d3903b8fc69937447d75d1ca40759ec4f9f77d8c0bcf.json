{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OpportunitiesService {\n  constructor(http) {\n    this.http = http;\n    this.opportunitySubject = new BehaviorSubject(null);\n    this.opportunity = this.opportunitySubject.asObservable();\n  }\n  createOpportunity(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`, data);\n  }\n  createFollowup(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION}`, data);\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createExistingContact(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}`, {\n      data\n    });\n  }\n  updateOpportunity(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  getOpportunityDropdownOptions(type) {\n    const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  getOpportunities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'opportunity_id,name,expected_revenue_amount,life_cycle_status_code,probability_percent,expected_revenue_start_date,expected_revenue_end_date,updatedAt,last_changed_by').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    } else {\n      params = params.set('sort', 'updatedAt:desc');\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][opportunity_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    });\n  }\n  getOpportunityByID(opportunityId) {\n    const params = new HttpParams().set('filters[opportunity_id][$eq]', opportunityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner][fields][1]', 'bp_id').set('populate[business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][4]', 'country').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]', 'website_url').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[notes][populate]', '*').set('populate[opportunity_prospect_contact_parties][populate][business_partner][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    }).pipe(map(response => {\n      const opportunityDetails = response?.data[0] || null;\n      this.opportunitySubject.next(opportunityDetails);\n      return response;\n    }));\n  }\n  getOpportunity(partnerId) {\n    let params = new HttpParams().set('filters[prospect_party_id][$eq]', partnerId).set('fields', 'opportunity_id,name,expected_revenue_end_date,life_cycle_status_code,group_code,last_changed_by').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function OpportunitiesService_Factory(t) {\n      return new (t || OpportunitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OpportunitiesService,\n      factory: OpportunitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "OpportunitiesService", "constructor", "http", "opportunitySubject", "opportunity", "asObservable", "createOpportunity", "data", "post", "CRM_OPPORTUNITY_REGISTRATION", "createFollowup", "CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION", "createNote", "CRM_NOTE", "createExistingContact", "CRM_OPPORTUNITY_CONTACT", "updateOpportunity", "Id", "put", "CRM_OPPORTUNITY", "updateNote", "deleteNote", "id", "delete", "getOpportunityDropdownOptions", "type", "params", "set", "get", "CONFIG_DATA", "getPartners", "PARTNERS", "pipe", "response", "item", "bp_id", "bp_full_name", "getOpportunities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "toString", "undefined", "order", "getOpportunityByID", "opportunityId", "opportunityDetails", "next", "getOpportunity", "partnerId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OpportunitiesService {\r\n  public opportunitySubject = new BehaviorSubject<any>(null);\r\n  public opportunity = this.opportunitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createOpportunity(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createFollowup(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createExistingContact(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateOpportunity(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  getOpportunityDropdownOptions(type: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', type);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  getOpportunities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'opportunity_id,name,expected_revenue_amount,life_cycle_status_code,probability_percent,expected_revenue_start_date,expected_revenue_end_date,updatedAt,last_changed_by'\r\n      )\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    } else {\r\n      params = params.set('sort', 'updatedAt:desc');\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][opportunity_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getOpportunityByID(opportunityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[opportunity_id][$eq]', opportunityId)\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner][fields][1]', 'bp_id')\r\n      .set(\r\n        'populate[business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]',\r\n        'first_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]',\r\n        'last_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'house_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'street_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][2]',\r\n        'city_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][3]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][4]',\r\n        'country'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][5]',\r\n        'postal_code'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]',\r\n        'website_url'\r\n      )\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')\r\n      .set('populate[notes][populate]', '*')\r\n      .set(\r\n        'populate[opportunity_prospect_contact_parties][populate][business_partner][populate]',\r\n        '*'\r\n      );\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const opportunityDetails = response?.data[0] || null;\r\n          this.opportunitySubject.next(opportunityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getOpportunity(partnerId: string): Observable<{ data: any[]; meta: any }> {\r\n    let params = new HttpParams()\r\n      .set('filters[prospect_party_id][$eq]', partnerId)\r\n      .set(\r\n        'fields',\r\n        'opportunity_id,name,expected_revenue_end_date,life_cycle_status_code,group_code,last_changed_by'\r\n      )\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    return this.http.get<{ data: any[]; meta: any }>(\r\n      `${CMS_APIContstant.CRM_OPPORTUNITY}`,\r\n      { params }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,kBAAkB,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IACnD,KAAAO,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAEpB;EAEvCC,iBAAiBA,CAACC,IAAS;IACzB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAGT,gBAAgB,CAACU,4BAA4B,EAAE,EAClDF,IAAI,CACL;EACH;EAEAG,cAAcA,CAACH,IAAS;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAGT,gBAAgB,CAACY,sCAAsC,EAAE,EAC5DJ,IAAI,CACL;EACH;EAEAK,UAAUA,CAACL,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACc,QAAQ,EAAE,EAAE;MACpDN;KACD,CAAC;EACJ;EAEAO,qBAAqBA,CAACP,IAAS;IAC7B,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACgB,uBAAuB,EAAE,EAAE;MACnER;KACD,CAAC;EACJ;EAEAS,iBAAiBA,CAACC,EAAU,EAAEV,IAAS;IACrC,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAAC,GAAGnB,gBAAgB,CAACoB,eAAe,IAAIF,EAAE,EAAE,EAAE;MAChEV;KACD,CAAC;EACJ;EAEAa,UAAUA,CAACH,EAAU,EAAEV,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACgB,GAAG,CAAC,GAAGnB,gBAAgB,CAACc,QAAQ,IAAII,EAAE,EAAE,EAAE;MACzDV;KACD,CAAC;EACJ;EAEAc,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACpB,IAAI,CAACqB,MAAM,CAAM,GAAGxB,gBAAgB,CAACc,QAAQ,IAAIS,EAAE,EAAE,CAAC;EACpE;EAEAE,6BAA6BA,CAACC,IAAY;IACxC,MAAMC,MAAM,GAAG,IAAI9B,UAAU,EAAE,CAC5B+B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;IAElC,OAAO,IAAI,CAACvB,IAAI,CAAC0B,GAAG,CAAM,GAAG7B,gBAAgB,CAAC8B,WAAW,EAAE,EAAE;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAI,WAAWA,CAACJ,MAAW;IACrB,OAAO,IAAI,CAACxB,IAAI,CAAC0B,GAAG,CAAM,GAAG7B,gBAAgB,CAACgC,QAAQ,EAAE,EAAE;MAAEL;IAAM,CAAE,CAAC,CAACM,IAAI,CACxElC,GAAG,CAAEmC,QAAQ,IACX,CAACA,QAAQ,EAAE1B,IAAI,IAAI,EAAE,EAAET,GAAG,CAAEoC,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAC,gBAAgBA,CACdC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIhB,MAAM,GAAG,IAAI9B,UAAU,EAAE,CAC1B+B,GAAG,CAAC,kBAAkB,EAAEW,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxChB,GAAG,CAAC,sBAAsB,EAAEY,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDhB,GAAG,CACF,QAAQ,EACR,wKAAwK,CACzK,CACAA,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,IAAIa,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9Cf,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGa,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD,CAAC,MAAM;MACLnB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAC/C;IAEA,IAAIe,UAAU,EAAE;MACdhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6CAA6C,EAC7Ce,UAAU,CACX;MACDhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,mCAAmC,EAAEe,UAAU,CAAC;IACtE;IAEA,OAAO,IAAI,CAACxC,IAAI,CAAC0B,GAAG,CAAQ,GAAG7B,gBAAgB,CAACoB,eAAe,EAAE,EAAE;MACjEO;KACD,CAAC;EACJ;EAEAoB,kBAAkBA,CAACC,aAAqB;IACtC,MAAMrB,MAAM,GAAG,IAAI9B,UAAU,EAAE,CAC5B+B,GAAG,CAAC,8BAA8B,EAAEoB,aAAa,CAAC,CAClDpB,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,CACrDA,GAAG,CACF,wFAAwF,EACxF,kBAAkB,CACnB,CACAA,GAAG,CACF,uGAAuG,EACvG,YAAY,CACb,CACAA,GAAG,CACF,uGAAuG,EACvG,WAAW,CACZ,CACAA,GAAG,CACF,sFAAsF,EACtF,eAAe,CAChB,CACAA,GAAG,CACF,4DAA4D,EAC5D,cAAc,CACf,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,4DAA4D,EAC5D,WAAW,CACZ,CACAA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,8EAA8E,EAC9E,eAAe,CAChB,CACAA,GAAG,CACF,qFAAqF,EACrF,cAAc,CACf,CACAA,GAAG,CACF,sFAAsF,EACtF,aAAa,CACd,CACAA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC,CAClEA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CACF,sFAAsF,EACtF,GAAG,CACJ;IAEH,OAAO,IAAI,CAACzB,IAAI,CACb0B,GAAG,CAAQ,GAAG7B,gBAAgB,CAACoB,eAAe,EAAE,EAAE;MAAEO;IAAM,CAAE,CAAC,CAC7DM,IAAI,CACHlC,GAAG,CAAEmC,QAAa,IAAI;MACpB,MAAMe,kBAAkB,GAAGf,QAAQ,EAAE1B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACpD,IAAI,CAACJ,kBAAkB,CAAC8C,IAAI,CAACD,kBAAkB,CAAC;MAChD,OAAOf,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAiB,cAAcA,CAACC,SAAiB;IAC9B,IAAIzB,MAAM,GAAG,IAAI9B,UAAU,EAAE,CAC1B+B,GAAG,CAAC,iCAAiC,EAAEwB,SAAS,CAAC,CACjDxB,GAAG,CACF,QAAQ,EACR,iGAAiG,CAClG,CACAA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,OAAO,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAClB,GAAG7B,gBAAgB,CAACoB,eAAe,EAAE,EACrC;MAAEO;IAAM,CAAE,CACX;EACH;;;uBAlMW1B,oBAAoB,EAAAoD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBvD,oBAAoB;MAAAwD,OAAA,EAApBxD,oBAAoB,CAAAyD,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}