{"ast": null, "code": "import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, Directive, HostListener, Optional, NgModule } from '@angular/core';\nimport * as i6 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { FilterOperator, FilterMatchMode, PrimeTemplate, TranslationKeys, SharedModule } from 'primeng/api';\nimport * as i7 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport * as i10 from 'primeng/calendar';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i5 from 'primeng/dropdown';\nimport { DropdownModule } from 'primeng/dropdown';\nimport * as i8 from 'primeng/inputnumber';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport * as i9 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/paginator';\nimport { PaginatorModule } from 'primeng/paginator';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport * as i11 from 'primeng/tristatecheckbox';\nimport { TriStateCheckboxModule } from 'primeng/tristatecheckbox';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { Subject } from 'rxjs';\nimport { ArrowDownIcon } from 'primeng/icons/arrowdown';\nimport { ArrowUpIcon } from 'primeng/icons/arrowup';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { FilterIcon } from 'primeng/icons/filter';\nimport { SortAltIcon } from 'primeng/icons/sortalt';\nimport { SortAmountDownIcon } from 'primeng/icons/sortamountdown';\nimport { SortAmountUpAltIcon } from 'primeng/icons/sortamountupalt';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { FilterSlashIcon } from 'primeng/icons/filterslash';\nconst _c0 = [\"container\"];\nconst _c1 = [\"resizeHelper\"];\nconst _c2 = [\"reorderIndicatorUp\"];\nconst _c3 = [\"reorderIndicatorDown\"];\nconst _c4 = [\"wrapper\"];\nconst _c5 = [\"table\"];\nconst _c6 = [\"thead\"];\nconst _c7 = [\"tfoot\"];\nconst _c8 = [\"scroller\"];\nconst _c9 = (a0, a1, a2) => ({\n  \"p-datatable p-component\": true,\n  \"p-datatable-hoverable-rows\": a0,\n  \"p-datatable-scrollable\": a1,\n  \"p-datatable-flex-scrollable\": a2\n});\nconst _c10 = a0 => ({\n  maxHeight: a0\n});\nconst _c11 = a0 => ({\n  height: a0\n});\nconst _c12 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c13 = a0 => ({\n  columns: a0\n});\nconst _c14 = (a0, a1, a2) => ({\n  \"p-datatable-table\": true,\n  \"p-datatable-scrollable-table\": a0,\n  \"p-datatable-resizable-table\": a1,\n  \"p-datatable-resizable-table-fit\": a2\n});\nconst _c15 = a0 => ({\n  $implicit: a0\n});\nfunction Table_div_2_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"p-datatable-loading-icon \" + ctx_r0.loadingIcon);\n  }\n}\nfunction Table_div_2_ng_container_2_SpinnerIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"spin\", true)(\"styleClass\", \"p-datatable-loading-icon\");\n  }\n}\nfunction Table_div_2_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Table_div_2_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_div_2_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Table_div_2_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtemplate(1, Table_div_2_ng_container_2_span_2_1_Template, 1, 0, null, 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.loadingIconTemplate);\n  }\n}\nfunction Table_div_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Table_div_2_ng_container_2_SpinnerIcon_1_Template, 1, 2, \"SpinnerIcon\", 24)(2, Table_div_2_ng_container_2_span_2_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIconTemplate);\n  }\n}\nfunction Table_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, Table_div_2_i_1_Template, 1, 2, \"i\", 23)(2, Table_div_2_ng_container_2_Template, 3, 2, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIcon);\n  }\n}\nfunction Table_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, Table_div_3_ng_container_1_Template, 1, 0, \"ng-container\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.captionTemplate);\n  }\n}\nfunction Table_p_paginator_4_1_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_4_1_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_1_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorFirstPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_1_ng_template_0_Template, 1, 1, \"ng-template\", 31);\n  }\n}\nfunction Table_p_paginator_4_2_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_4_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_2_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorPreviousPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_2_ng_template_0_Template, 1, 1, \"ng-template\", 32);\n  }\n}\nfunction Table_p_paginator_4_3_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_4_3_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_3_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorLastPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_4_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_3_ng_template_0_Template, 1, 1, \"ng-template\", 33);\n  }\n}\nfunction Table_p_paginator_4_4_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_4_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_4_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_4_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_4_4_ng_template_0_Template, 1, 1, \"ng-template\", 34);\n  }\n}\nfunction Table_p_paginator_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 30);\n    i0.ɵɵlistener(\"onPageChange\", function Table_p_paginator_4_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵtemplate(1, Table_p_paginator_4_1_Template, 1, 0, null, 16)(2, Table_p_paginator_4_2_Template, 1, 0, null, 16)(3, Table_p_paginator_4_3_Template, 1, 0, null, 16)(4, Table_p_paginator_4_4_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r0.rows)(\"first\", ctx_r0.first)(\"totalRecords\", ctx_r0.totalRecords)(\"pageLinkSize\", ctx_r0.pageLinks)(\"alwaysShow\", ctx_r0.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r0.rowsPerPageOptions)(\"templateLeft\", ctx_r0.paginatorLeftTemplate)(\"templateRight\", ctx_r0.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r0.paginatorDropdownAppendTo)(\"dropdownScrollHeight\", ctx_r0.paginatorDropdownScrollHeight)(\"currentPageReportTemplate\", ctx_r0.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r0.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r0.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r0.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r0.showJumpToPageDropdown)(\"showJumpToPageInput\", ctx_r0.showJumpToPageInput)(\"showPageLinks\", ctx_r0.showPageLinks)(\"styleClass\", ctx_r0.paginatorStyleClass)(\"locale\", ctx_r0.paginatorLocale);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorFirstPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorPreviousPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorLastPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction Table_p_scroller_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_scroller_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_scroller_7_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 37);\n  }\n  if (rf & 2) {\n    const items_r4 = ctx.$implicit;\n    const scrollerOptions_r5 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const buildInTable_r6 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInTable_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c12, items_r4, scrollerOptions_r5));\n  }\n}\nfunction Table_p_scroller_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 35, 3);\n    i0.ɵɵlistener(\"onLazyLoad\", function Table_p_scroller_7_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onLazyItemLoad($event));\n    });\n    i0.ɵɵtemplate(2, Table_p_scroller_7_ng_template_2_Template, 1, 5, \"ng-template\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(15, _c11, ctx_r0.scrollHeight !== \"flex\" ? ctx_r0.scrollHeight : undefined));\n    i0.ɵɵproperty(\"items\", ctx_r0.processedData)(\"columns\", ctx_r0.columns)(\"scrollHeight\", ctx_r0.scrollHeight !== \"flex\" ? undefined : \"100%\")(\"itemSize\", ctx_r0.virtualScrollItemSize || ctx_r0._virtualRowHeight)(\"step\", ctx_r0.rows)(\"delay\", ctx_r0.lazy ? ctx_r0.virtualScrollDelay : 0)(\"inline\", true)(\"lazy\", ctx_r0.lazy)(\"loaderDisabled\", true)(\"showSpacer\", false)(\"showLoader\", ctx_r0.loadingBodyTemplate)(\"options\", ctx_r0.virtualScrollOptions)(\"autoSize\", true);\n  }\n}\nfunction Table_ng_container_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Table_ng_container_8_ng_container_1_Template, 1, 0, \"ng-container\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const buildInTable_r6 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", buildInTable_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(4, _c12, ctx_r0.processedData, i0.ɵɵpureFunction1(2, _c13, ctx_r0.columns)));\n  }\n}\nfunction Table_ng_template_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_ng_template_9_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_ng_template_9_tbody_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tbody\", 44);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r7 = i0.ɵɵnextContext().options;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r0.frozenValue)(\"frozenRows\", true)(\"pTableBody\", scrollerOptions_r7.columns)(\"pTableBodyTemplate\", ctx_r0.frozenBodyTemplate)(\"frozen\", true);\n  }\n}\nfunction Table_ng_template_9_tbody_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tbody\", 45);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r7 = i0.ɵɵnextContext().options;\n    i0.ɵɵstyleMap(\"height: calc(\" + scrollerOptions_r7.spacerStyle.height + \" - \" + scrollerOptions_r7.rows.length * scrollerOptions_r7.itemSize + \"px);\");\n  }\n}\nfunction Table_ng_template_9_tfoot_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_ng_template_9_tfoot_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tfoot\", 46, 6);\n    i0.ɵɵtemplate(2, Table_ng_template_9_tfoot_9_ng_container_2_Template, 1, 0, \"ng-container\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r7 = i0.ɵɵnextContext().options;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerGroupedTemplate || ctx_r0.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c15, scrollerOptions_r7.columns));\n  }\n}\nfunction Table_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 38, 4);\n    i0.ɵɵtemplate(2, Table_ng_template_9_ng_container_2_Template, 1, 0, \"ng-container\", 37);\n    i0.ɵɵelementStart(3, \"thead\", 39, 5);\n    i0.ɵɵtemplate(5, Table_ng_template_9_ng_container_5_Template, 1, 0, \"ng-container\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Table_ng_template_9_tbody_6_Template, 1, 5, \"tbody\", 40);\n    i0.ɵɵelement(7, \"tbody\", 41);\n    i0.ɵɵtemplate(8, Table_ng_template_9_tbody_8_Template, 1, 2, \"tbody\", 42)(9, Table_ng_template_9_tfoot_9_Template, 3, 4, \"tfoot\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scrollerOptions_r7 = ctx.options;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r0.tableStyle);\n    i0.ɵɵclassMap(ctx_r0.tableStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(20, _c14, ctx_r0.scrollable, ctx_r0.resizableColumns, ctx_r0.resizableColumns && ctx_r0.columnResizeMode === \"fit\"));\n    i0.ɵɵattribute(\"id\", ctx_r0.id + \"-table\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(24, _c15, scrollerOptions_r7.columns));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerGroupedTemplate || ctx_r0.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(26, _c15, scrollerOptions_r7.columns));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.frozenValue || ctx_r0.frozenBodyTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(scrollerOptions_r7.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r7.contentStyleClass)(\"value\", ctx_r0.dataToRender(scrollerOptions_r7.rows))(\"pTableBody\", scrollerOptions_r7.columns)(\"pTableBodyTemplate\", ctx_r0.bodyTemplate)(\"scrollerOptions\", scrollerOptions_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", scrollerOptions_r7.spacerStyle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.footerGroupedTemplate || ctx_r0.footerTemplate);\n  }\n}\nfunction Table_p_paginator_11_1_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_11_1_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_1_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorFirstPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_11_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_1_ng_template_0_Template, 1, 1, \"ng-template\", 31);\n  }\n}\nfunction Table_p_paginator_11_2_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_11_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_2_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorPreviousPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_11_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_2_ng_template_0_Template, 1, 1, \"ng-template\", 32);\n  }\n}\nfunction Table_p_paginator_11_3_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_11_3_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_3_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorLastPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_11_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_3_ng_template_0_Template, 1, 1, \"ng-template\", 33);\n  }\n}\nfunction Table_p_paginator_11_4_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_p_paginator_11_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_4_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction Table_p_paginator_11_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_p_paginator_11_4_ng_template_0_Template, 1, 1, \"ng-template\", 34);\n  }\n}\nfunction Table_p_paginator_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 47);\n    i0.ɵɵlistener(\"onPageChange\", function Table_p_paginator_11_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵtemplate(1, Table_p_paginator_11_1_Template, 1, 0, null, 16)(2, Table_p_paginator_11_2_Template, 1, 0, null, 16)(3, Table_p_paginator_11_3_Template, 1, 0, null, 16)(4, Table_p_paginator_11_4_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r0.rows)(\"first\", ctx_r0.first)(\"totalRecords\", ctx_r0.totalRecords)(\"pageLinkSize\", ctx_r0.pageLinks)(\"alwaysShow\", ctx_r0.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r0.rowsPerPageOptions)(\"templateLeft\", ctx_r0.paginatorLeftTemplate)(\"templateRight\", ctx_r0.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r0.paginatorDropdownAppendTo)(\"dropdownScrollHeight\", ctx_r0.paginatorDropdownScrollHeight)(\"currentPageReportTemplate\", ctx_r0.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r0.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r0.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r0.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r0.showJumpToPageDropdown)(\"showJumpToPageInput\", ctx_r0.showJumpToPageInput)(\"showPageLinks\", ctx_r0.showPageLinks)(\"styleClass\", ctx_r0.paginatorStyleClass)(\"locale\", ctx_r0.paginatorLocale);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorFirstPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorPreviousPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorLastPageLinkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction Table_div_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Table_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, Table_div_12_ng_container_1_Template, 1, 0, \"ng-container\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.summaryTemplate);\n  }\n}\nfunction Table_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 49, 7);\n  }\n}\nfunction Table_span_14_ArrowDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ArrowDownIcon\");\n  }\n}\nfunction Table_span_14_3_ng_template_0_Template(rf, ctx) {}\nfunction Table_span_14_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_span_14_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Table_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50, 8);\n    i0.ɵɵtemplate(2, Table_span_14_ArrowDownIcon_2_Template, 1, 0, \"ArrowDownIcon\", 16)(3, Table_span_14_3_Template, 1, 0, null, 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.reorderIndicatorUpIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.reorderIndicatorUpIconTemplate);\n  }\n}\nfunction Table_span_15_ArrowUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ArrowUpIcon\");\n  }\n}\nfunction Table_span_15_3_ng_template_0_Template(rf, ctx) {}\nfunction Table_span_15_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Table_span_15_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Table_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51, 9);\n    i0.ɵɵtemplate(2, Table_span_15_ArrowUpIcon_2_Template, 1, 0, \"ArrowUpIcon\", 16)(3, Table_span_15_3_Template, 1, 0, null, 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.reorderIndicatorDownIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.reorderIndicatorDownIconTemplate);\n  }\n}\nconst _c16 = [\"pTableBody\", \"\"];\nconst _c17 = (a0, a1, a2, a3, a4) => ({\n  $implicit: a0,\n  rowIndex: a1,\n  columns: a2,\n  editing: a3,\n  frozen: a4\n});\nconst _c18 = (a0, a1, a2, a3, a4, a5, a6) => ({\n  $implicit: a0,\n  rowIndex: a1,\n  columns: a2,\n  editing: a3,\n  frozen: a4,\n  rowgroup: a5,\n  rowspan: a6\n});\nconst _c19 = (a0, a1, a2, a3, a4, a5) => ({\n  $implicit: a0,\n  rowIndex: a1,\n  columns: a2,\n  expanded: a3,\n  editing: a4,\n  frozen: a5\n});\nconst _c20 = (a0, a1, a2, a3) => ({\n  $implicit: a0,\n  rowIndex: a1,\n  columns: a2,\n  frozen: a3\n});\nconst _c21 = (a0, a1) => ({\n  $implicit: a0,\n  frozen: a1\n});\nfunction TableBody_ng_container_0_ng_template_1_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 3);\n    i0.ɵɵtemplate(1, TableBody_ng_container_0_ng_template_1_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const rowData_r2 = ctx_r0.$implicit;\n    const rowIndex_r3 = ctx_r0.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.groupHeaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c17, rowData_r2, ctx_r3.getRowIndex(rowIndex_r3), ctx_r3.columns, ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r2), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_0_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const rowData_r2 = ctx_r0.$implicit;\n    const rowIndex_r3 = ctx_r0.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowData_r2 ? ctx_r3.template : ctx_r3.dt.loadingBodyTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c17, rowData_r2, ctx_r3.getRowIndex(rowIndex_r3), ctx_r3.columns, ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r2), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_0_ng_template_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const rowData_r2 = ctx_r0.$implicit;\n    const rowIndex_r3 = ctx_r0.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowData_r2 ? ctx_r3.template : ctx_r3.dt.loadingBodyTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction7(2, _c18, rowData_r2, ctx_r3.getRowIndex(rowIndex_r3), ctx_r3.columns, ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r2), ctx_r3.frozen, ctx_r3.shouldRenderRowspan(ctx_r3.value, rowData_r2, rowIndex_r3), ctx_r3.calculateRowGroupSize(ctx_r3.value, rowData_r2, rowIndex_r3)));\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 3);\n    i0.ɵɵtemplate(1, TableBody_ng_container_0_ng_template_1_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const rowData_r2 = ctx_r0.$implicit;\n    const rowIndex_r3 = ctx_r0.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.groupFooterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c17, rowData_r2, ctx_r3.getRowIndex(rowIndex_r3), ctx_r3.columns, ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r2), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_0_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableBody_ng_container_0_ng_template_1_ng_container_0_Template, 2, 8, \"ng-container\", 2)(1, TableBody_ng_container_0_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 0)(2, TableBody_ng_container_0_ng_template_1_ng_container_2_Template, 2, 10, \"ng-container\", 0)(3, TableBody_ng_container_0_ng_template_1_ng_container_3_Template, 2, 8, \"ng-container\", 2);\n  }\n  if (rf & 2) {\n    const rowData_r2 = ctx.$implicit;\n    const rowIndex_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.groupHeaderTemplate && !ctx_r3.dt.virtualScroll && ctx_r3.dt.rowGroupMode === \"subheader\" && ctx_r3.shouldRenderRowGroupHeader(ctx_r3.value, rowData_r2, rowIndex_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.rowGroupMode !== \"rowspan\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.rowGroupMode === \"rowspan\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.groupFooterTemplate && !ctx_r3.dt.virtualScroll && ctx_r3.dt.rowGroupMode === \"subheader\" && ctx_r3.shouldRenderRowGroupFooter(ctx_r3.value, rowData_r2, rowIndex_r3));\n  }\n}\nfunction TableBody_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_0_ng_template_1_Template, 4, 4, \"ng-template\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.value)(\"ngForTrackBy\", ctx_r3.dt.rowTrackBy);\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_1_ng_template_1_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const rowData_r6 = ctx_r4.$implicit;\n    const rowIndex_r7 = ctx_r4.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(2, _c19, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7), ctx_r3.columns, ctx_r3.dt.isRowExpanded(rowData_r6), ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r6), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 3);\n    i0.ɵɵtemplate(1, TableBody_ng_container_1_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const rowData_r6 = ctx_r4.$implicit;\n    const rowIndex_r7 = ctx_r4.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.groupHeaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(2, _c19, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7), ctx_r3.columns, ctx_r3.dt.isRowExpanded(rowData_r6), ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r6), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 3);\n    i0.ɵɵtemplate(1, TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    const rowData_r6 = ctx_r4.$implicit;\n    const rowIndex_r7 = ctx_r4.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.groupFooterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(2, _c19, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7), ctx_r3.columns, ctx_r3.dt.isRowExpanded(rowData_r6), ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r6), ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 4)(2, TableBody_ng_container_1_ng_template_1_ng_container_2_ng_container_2_Template, 2, 9, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const rowData_r6 = ctx_r4.$implicit;\n    const rowIndex_r7 = ctx_r4.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.expandedRowTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(3, _c20, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7), ctx_r3.columns, ctx_r3.frozen));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.groupFooterTemplate && ctx_r3.dt.rowGroupMode === \"subheader\" && ctx_r3.shouldRenderRowGroupFooter(ctx_r3.value, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7)));\n  }\n}\nfunction TableBody_ng_container_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableBody_ng_container_1_ng_template_1_ng_container_0_Template, 2, 9, \"ng-container\", 0)(1, TableBody_ng_container_1_ng_template_1_ng_container_1_Template, 2, 9, \"ng-container\", 2)(2, TableBody_ng_container_1_ng_template_1_ng_container_2_Template, 3, 8, \"ng-container\", 0);\n  }\n  if (rf & 2) {\n    const rowData_r6 = ctx.$implicit;\n    const rowIndex_r7 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.dt.groupHeaderTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.groupHeaderTemplate && ctx_r3.dt.rowGroupMode === \"subheader\" && ctx_r3.shouldRenderRowGroupHeader(ctx_r3.value, rowData_r6, ctx_r3.getRowIndex(rowIndex_r7)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.isRowExpanded(rowData_r6));\n  }\n}\nfunction TableBody_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_1_ng_template_1_Template, 3, 3, \"ng-template\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.value)(\"ngForTrackBy\", ctx_r3.dt.rowTrackBy);\n  }\n}\nfunction TableBody_ng_container_2_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_2_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_2_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_2_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const rowData_r9 = ctx_r7.$implicit;\n    const rowIndex_r10 = ctx_r7.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.frozenExpandedRowTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(2, _c20, rowData_r9, ctx_r3.getRowIndex(rowIndex_r10), ctx_r3.columns, ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableBody_ng_container_2_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 4)(1, TableBody_ng_container_2_ng_template_1_ng_container_1_Template, 2, 7, \"ng-container\", 0);\n  }\n  if (rf & 2) {\n    const rowData_r9 = ctx.$implicit;\n    const rowIndex_r10 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(3, _c19, rowData_r9, ctx_r3.getRowIndex(rowIndex_r10), ctx_r3.columns, ctx_r3.dt.isRowExpanded(rowData_r9), ctx_r3.dt.editMode === \"row\" && ctx_r3.dt.isRowEditing(rowData_r9), ctx_r3.frozen));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dt.isRowExpanded(rowData_r9));\n  }\n}\nfunction TableBody_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_2_ng_template_1_Template, 2, 10, \"ng-template\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.value)(\"ngForTrackBy\", ctx_r3.dt.rowTrackBy);\n  }\n}\nfunction TableBody_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.loadingBodyTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c21, ctx_r3.columns, ctx_r3.frozen));\n  }\n}\nfunction TableBody_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TableBody_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableBody_ng_container_4_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.dt.emptyMessageTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c21, ctx_r3.columns, ctx_r3.frozen));\n  }\n}\nfunction SortIcon_ng_container_0_SortAltIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAltIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction SortIcon_ng_container_0_SortAmountUpAltIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAmountUpAltIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction SortIcon_ng_container_0_SortAmountDownIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAmountDownIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction SortIcon_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SortIcon_ng_container_0_SortAltIcon_1_Template, 1, 1, \"SortAltIcon\", 3)(2, SortIcon_ng_container_0_SortAmountUpAltIcon_2_Template, 1, 1, \"SortAmountUpAltIcon\", 3)(3, SortIcon_ng_container_0_SortAmountDownIcon_3_Template, 1, 1, \"SortAmountDownIcon\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === -1);\n  }\n}\nfunction SortIcon_span_1_1_ng_template_0_Template(rf, ctx) {}\nfunction SortIcon_span_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SortIcon_span_1_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction SortIcon_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, SortIcon_span_1_1_Template, 1, 0, null, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.dt.sortIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c15, ctx_r0.sortOrder));\n  }\n}\nfunction SortIcon_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.getBadgeValue());\n  }\n}\nfunction CellEditor_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CellEditor_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CellEditor_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.inputTemplate);\n  }\n}\nfunction CellEditor_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CellEditor_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CellEditor_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.outputTemplate);\n  }\n}\nconst _c22 = [\"rb\"];\nconst _c23 = (a0, a1, a2) => ({\n  \"p-radiobutton-focused\": a0,\n  \"p-radiobutton-checked\": a1,\n  \"p-radiobutton-disabled\": a2\n});\nconst _c24 = (a0, a1, a2) => ({\n  \"p-radiobutton-box p-component\": true,\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nconst _c25 = (a0, a1) => ({\n  \"p-checkbox-focused\": a0,\n  \"p-checkbox-disabled\": a1\n});\nconst _c26 = (a0, a1, a2) => ({\n  \"p-checkbox-box p-component\": true,\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nfunction TableCheckbox_ng_container_5_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n  }\n}\nfunction TableCheckbox_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableCheckbox_ng_container_5_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checked);\n  }\n}\nfunction TableCheckbox_span_6_1_ng_template_0_Template(rf, ctx) {}\nfunction TableCheckbox_span_6_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableCheckbox_span_6_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TableCheckbox_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, TableCheckbox_span_6_1_Template, 1, 0, null, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.dt.checkboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c15, ctx_r1.checked));\n  }\n}\nconst _c27 = (a0, a1, a2) => ({\n  \"p-checkbox-box\": true,\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nfunction TableHeaderCheckbox_ng_container_6_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n  }\n}\nfunction TableHeaderCheckbox_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableHeaderCheckbox_ng_container_6_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checked);\n  }\n}\nfunction TableHeaderCheckbox_span_7_1_ng_template_0_Template(rf, ctx) {}\nfunction TableHeaderCheckbox_span_7_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableHeaderCheckbox_span_7_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TableHeaderCheckbox_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtemplate(1, TableHeaderCheckbox_span_7_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.dt.headerCheckboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c15, ctx_r1.checked));\n  }\n}\nconst _c28 = [\"icon\"];\nconst _c29 = [\"clearBtn\"];\nconst _c30 = (a0, a1) => ({\n  \"p-column-filter-row\": a0,\n  \"p-column-filter-menu\": a1\n});\nconst _c31 = (a0, a1) => ({\n  \"p-column-filter-menu-button-open\": a0,\n  \"p-column-filter-menu-button-active\": a1\n});\nconst _c32 = a0 => ({\n  \"p-hidden-space\": a0\n});\nconst _c33 = a0 => ({\n  \"p-column-filter-overlay p-component p-fluid\": true,\n  \"p-column-filter-overlay-menu\": a0\n});\nconst _c34 = a0 => ({\n  \"p-highlight\": a0\n});\nfunction ColumnFilter_p_columnFilterFormElement_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-columnFilterFormElement\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"type\", ctx_r0.type)(\"field\", ctx_r0.field)(\"filterConstraint\", ctx_r0.dt.filters[ctx_r0.field])(\"filterTemplate\", ctx_r0.filterTemplate)(\"placeholder\", ctx_r0.placeholder)(\"minFractionDigits\", ctx_r0.minFractionDigits)(\"maxFractionDigits\", ctx_r0.maxFractionDigits)(\"prefix\", ctx_r0.prefix)(\"suffix\", ctx_r0.suffix)(\"locale\", ctx_r0.locale)(\"localeMatcher\", ctx_r0.localeMatcher)(\"currency\", ctx_r0.currency)(\"currencyDisplay\", ctx_r0.currencyDisplay)(\"useGrouping\", ctx_r0.useGrouping)(\"showButtons\", ctx_r0.showButtons);\n  }\n}\nfunction ColumnFilter_button_2_FilterIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"FilterIcon\", 12);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"pi-filter-icon\");\n  }\n}\nfunction ColumnFilter_button_2_span_3_1_ng_template_0_Template(rf, ctx) {}\nfunction ColumnFilter_button_2_span_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ColumnFilter_button_2_span_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ColumnFilter_button_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtemplate(1, ColumnFilter_button_2_span_3_1_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.filterIconTemplate);\n  }\n}\nfunction ColumnFilter_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9, 0);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleMenu());\n    })(\"keydown\", function ColumnFilter_button_2_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onToggleButtonKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, ColumnFilter_button_2_FilterIcon_2_Template, 1, 1, \"FilterIcon\", 10)(3, ColumnFilter_button_2_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c31, ctx_r0.overlayVisible, ctx_r0.hasFilter()));\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.showMenuButtonAriaLabel)(\"aria-controls\", ctx_r0.overlayId)(\"aria-expanded\", ctx_r0.overlayVisible);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.filterIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filterIconTemplate);\n  }\n}\nfunction ColumnFilter_button_3_FilterSlashIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"FilterSlashIcon\");\n  }\n}\nfunction ColumnFilter_button_3_3_ng_template_0_Template(rf, ctx) {}\nfunction ColumnFilter_button_3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ColumnFilter_button_3_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ColumnFilter_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15, 0);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.clearFilter());\n    });\n    i0.ɵɵtemplate(2, ColumnFilter_button_3_FilterSlashIcon_2_Template, 1, 0, \"FilterSlashIcon\", 16)(3, ColumnFilter_button_3_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c32, !ctx_r0.hasRowFilter()));\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.clearButtonLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.clearFilterIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.clearFilterIconTemplate);\n  }\n}\nfunction ColumnFilter_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ColumnFilter_div_4_ul_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 24);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ul_2_li_1_Template_li_click_0_listener() {\n      const matchMode_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onRowMatchModeChange(matchMode_r7.value));\n    })(\"keydown\", function ColumnFilter_div_4_ul_2_li_1_Template_li_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onRowMatchModeKeyDown($event));\n    })(\"keydown.enter\", function ColumnFilter_div_4_ul_2_li_1_Template_li_keydown_enter_0_listener() {\n      const matchMode_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onRowMatchModeChange(matchMode_r7.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const matchMode_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c34, ctx_r0.isRowMatchModeSelected(matchMode_r7.value)));\n    i0.ɵɵattribute(\"tabindex\", i_r8 === 0 ? \"0\" : null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", matchMode_r7.label, \" \");\n  }\n}\nfunction ColumnFilter_div_4_ul_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 20);\n    i0.ɵɵtemplate(1, ColumnFilter_div_4_ul_2_li_1_Template, 2, 5, \"li\", 21);\n    i0.ɵɵelement(2, \"li\", 22);\n    i0.ɵɵelementStart(3, \"li\", 23);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ul_2_Template_li_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onRowClearItemClick());\n    })(\"keydown\", function ColumnFilter_div_4_ul_2_Template_li_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onRowMatchModeKeyDown($event));\n    })(\"keydown.enter\", function ColumnFilter_div_4_ul_2_Template_li_keydown_enter_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onRowClearItemClick());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matchModes);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.noFilterLabel);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"p-dropdown\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function ColumnFilter_div_4_ng_template_3_div_0_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onOperatorChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.operatorOptions)(\"ngModel\", ctx_r0.operator);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_2_p_dropdown_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function ColumnFilter_div_4_ng_template_3_div_2_p_dropdown_1_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const fieldConstraint_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onMenuMatchModeChange($event, fieldConstraint_r11));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fieldConstraint_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"options\", ctx_r0.matchModes)(\"ngModel\", fieldConstraint_r11.matchMode);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_2_button_4_TrashIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TrashIcon\");\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_2_button_4_2_ng_template_0_Template(rf, ctx) {}\nfunction ColumnFilter_div_4_ng_template_3_div_2_button_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ColumnFilter_div_4_ng_template_3_div_2_button_4_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_2_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ng_template_3_div_2_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const fieldConstraint_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeConstraint(fieldConstraint_r11));\n    });\n    i0.ɵɵtemplate(1, ColumnFilter_div_4_ng_template_3_div_2_button_4_TrashIcon_1_Template, 1, 0, \"TrashIcon\", 16)(2, ColumnFilter_div_4_ng_template_3_div_2_button_4_2_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"label\", ctx_r0.removeRuleButtonLabel);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.removeRuleButtonLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.removeRuleIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.removeRuleIconTemplate);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, ColumnFilter_div_4_ng_template_3_div_2_p_dropdown_1_Template, 1, 2, \"p-dropdown\", 35);\n    i0.ɵɵelement(2, \"p-columnFilterFormElement\", 36);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtemplate(4, ColumnFilter_div_4_ng_template_3_div_2_button_4_Template, 3, 4, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fieldConstraint_r11 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showMatchModes && ctx_r0.matchModes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", ctx_r0.type)(\"field\", ctx_r0.field)(\"filterConstraint\", fieldConstraint_r11)(\"filterTemplate\", ctx_r0.filterTemplate)(\"placeholder\", ctx_r0.placeholder)(\"minFractionDigits\", ctx_r0.minFractionDigits)(\"maxFractionDigits\", ctx_r0.maxFractionDigits)(\"prefix\", ctx_r0.prefix)(\"suffix\", ctx_r0.suffix)(\"locale\", ctx_r0.locale)(\"localeMatcher\", ctx_r0.localeMatcher)(\"currency\", ctx_r0.currency)(\"currencyDisplay\", ctx_r0.currencyDisplay)(\"useGrouping\", ctx_r0.useGrouping);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showRemoveIcon);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_3_PlusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\");\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_3_3_ng_template_0_Template(rf, ctx) {}\nfunction ColumnFilter_div_4_ng_template_3_div_3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ColumnFilter_div_4_ng_template_3_div_3_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ng_template_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.addConstraint());\n    });\n    i0.ɵɵtemplate(2, ColumnFilter_div_4_ng_template_3_div_3_PlusIcon_2_Template, 1, 0, \"PlusIcon\", 16)(3, ColumnFilter_div_4_ng_template_3_div_3_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r0.addRuleButtonLabel);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.addRuleButtonLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.addRuleIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.addRuleIconTemplate);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42, 2);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ng_template_3_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.clearFilter());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"label\", ctx_r0.clearButtonLabel);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.clearButtonLabel);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_ng_template_3_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.applyFilter());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"label\", ctx_r0.applyButtonLabel);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.applyButtonLabel);\n  }\n}\nfunction ColumnFilter_div_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ColumnFilter_div_4_ng_template_3_div_0_Template, 2, 2, \"div\", 25);\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵtemplate(2, ColumnFilter_div_4_ng_template_3_div_2_Template, 5, 16, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ColumnFilter_div_4_ng_template_3_div_3_Template, 4, 4, \"div\", 28);\n    i0.ɵɵelementStart(4, \"div\", 29);\n    i0.ɵɵtemplate(5, ColumnFilter_div_4_ng_template_3_button_5_Template, 2, 2, \"button\", 30)(6, ColumnFilter_div_4_ng_template_3_button_6_Template, 1, 2, \"button\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isShowOperator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.fieldConstraints);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isShowAddConstraint);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showClearButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showApplyButton);\n  }\n}\nfunction ColumnFilter_div_4_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ColumnFilter_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function ColumnFilter_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onContentClick());\n    })(\"@overlayAnimation.start\", function ColumnFilter_div_4_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function ColumnFilter_div_4_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onOverlayAnimationEnd($event));\n    })(\"keydown.escape\", function ColumnFilter_div_4_Template_div_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onEscape());\n    });\n    i0.ɵɵtemplate(1, ColumnFilter_div_4_ng_container_1_Template, 1, 0, \"ng-container\", 18)(2, ColumnFilter_div_4_ul_2_Template, 5, 2, \"ul\", 19)(3, ColumnFilter_div_4_ng_template_3_Template, 7, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, ColumnFilter_div_4_ng_container_5_Template, 1, 0, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menu_r16 = i0.ɵɵreference(4);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c33, ctx_r0.display === \"menu\"))(\"id\", ctx_r0.overlayId)(\"@overlayAnimation\", \"visible\");\n    i0.ɵɵattribute(\"aria-modal\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c15, ctx_r0.field));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.display === \"row\")(\"ngIfElse\", menu_r16);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c15, ctx_r0.field));\n  }\n}\nconst _c35 = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15) => ({\n  $implicit: a0,\n  filterCallback: a1,\n  type: a2,\n  field: a3,\n  filterConstraint: a4,\n  placeholder: a5,\n  minFractionDigits: a6,\n  maxFractionDigits: a7,\n  prefix: a8,\n  suffix: a9,\n  locale: a10,\n  localeMatcher: a11,\n  currency: a12,\n  currencyDisplay: a13,\n  useGrouping: a14,\n  showButtons: a15\n});\nfunction ColumnFilterFormElement_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ColumnFilterFormElement_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ColumnFilterFormElement_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c35, [ctx_r0.filterConstraint.value, ctx_r0.filterCallback, ctx_r0.type, ctx_r0.field, ctx_r0.filterConstraint, ctx_r0.placeholder, ctx_r0.minFractionDigits, ctx_r0.maxFractionDigits, ctx_r0.prefix, ctx_r0.suffix, ctx_r0.locale, ctx_r0.localeMatcher, ctx_r0.currency, ctx_r0.currencyDisplay, ctx_r0.useGrouping, ctx_r0.showButtons]));\n  }\n}\nfunction ColumnFilterFormElement_ng_template_1_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 8);\n    i0.ɵɵlistener(\"input\", function ColumnFilterFormElement_ng_template_1_input_1_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onModelChange($event.target.value));\n    })(\"keydown.enter\", function ColumnFilterFormElement_ng_template_1_input_1_Template_input_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onTextInputEnterKeyDown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r0.filterConstraint == null ? null : ctx_r0.filterConstraint.value);\n    i0.ɵɵattribute(\"placeholder\", ctx_r0.placeholder);\n  }\n}\nfunction ColumnFilterFormElement_ng_template_1_p_inputNumber_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-inputNumber\", 9);\n    i0.ɵɵlistener(\"ngModelChange\", function ColumnFilterFormElement_ng_template_1_p_inputNumber_2_Template_p_inputNumber_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onModelChange($event));\n    })(\"onKeyDown\", function ColumnFilterFormElement_ng_template_1_p_inputNumber_2_Template_p_inputNumber_onKeyDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onNumericInputKeyDown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.filterConstraint == null ? null : ctx_r0.filterConstraint.value)(\"showButtons\", ctx_r0.showButtons)(\"minFractionDigits\", ctx_r0.minFractionDigits)(\"maxFractionDigits\", ctx_r0.maxFractionDigits)(\"prefix\", ctx_r0.prefix)(\"suffix\", ctx_r0.suffix)(\"placeholder\", ctx_r0.placeholder)(\"mode\", ctx_r0.currency ? \"currency\" : \"decimal\")(\"locale\", ctx_r0.locale)(\"localeMatcher\", ctx_r0.localeMatcher)(\"currency\", ctx_r0.currency)(\"currencyDisplay\", ctx_r0.currencyDisplay)(\"useGrouping\", ctx_r0.useGrouping);\n  }\n}\nfunction ColumnFilterFormElement_ng_template_1_p_triStateCheckbox_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-triStateCheckbox\", 10);\n    i0.ɵɵlistener(\"ngModelChange\", function ColumnFilterFormElement_ng_template_1_p_triStateCheckbox_3_Template_p_triStateCheckbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onModelChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.filterConstraint == null ? null : ctx_r0.filterConstraint.value);\n  }\n}\nfunction ColumnFilterFormElement_ng_template_1_p_calendar_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-calendar\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function ColumnFilterFormElement_ng_template_1_p_calendar_4_Template_p_calendar_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onModelChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeholder)(\"ngModel\", ctx_r0.filterConstraint == null ? null : ctx_r0.filterConstraint.value);\n  }\n}\nfunction ColumnFilterFormElement_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 3);\n    i0.ɵɵtemplate(1, ColumnFilterFormElement_ng_template_1_input_1_Template, 1, 2, \"input\", 4)(2, ColumnFilterFormElement_ng_template_1_p_inputNumber_2_Template, 1, 13, \"p-inputNumber\", 5)(3, ColumnFilterFormElement_ng_template_1_p_triStateCheckbox_3_Template, 1, 1, \"p-triStateCheckbox\", 6)(4, ColumnFilterFormElement_ng_template_1_p_calendar_4_Template, 1, 2, \"p-calendar\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r0.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"numeric\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"boolean\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"date\");\n  }\n}\nlet TableService = /*#__PURE__*/(() => {\n  class TableService {\n    sortSource = new Subject();\n    selectionSource = new Subject();\n    contextMenuSource = new Subject();\n    valueSource = new Subject();\n    totalRecordsSource = new Subject();\n    columnsSource = new Subject();\n    sortSource$ = this.sortSource.asObservable();\n    selectionSource$ = this.selectionSource.asObservable();\n    contextMenuSource$ = this.contextMenuSource.asObservable();\n    valueSource$ = this.valueSource.asObservable();\n    totalRecordsSource$ = this.totalRecordsSource.asObservable();\n    columnsSource$ = this.columnsSource.asObservable();\n    onSort(sortMeta) {\n      this.sortSource.next(sortMeta);\n    }\n    onSelectionChange() {\n      this.selectionSource.next(null);\n    }\n    onContextMenu(data) {\n      this.contextMenuSource.next(data);\n    }\n    onValueChange(value) {\n      this.valueSource.next(value);\n    }\n    onTotalRecordsChange(value) {\n      this.totalRecordsSource.next(value);\n    }\n    onColumnsChange(columns) {\n      this.columnsSource.next(columns);\n    }\n    static ɵfac = function TableService_Factory(t) {\n      return new (t || TableService)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TableService,\n      factory: TableService.ɵfac\n    });\n  }\n  return TableService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Table displays data in tabular format.\n * @group Components\n */\nlet Table = /*#__PURE__*/(() => {\n  class Table {\n    document;\n    platformId;\n    renderer;\n    el;\n    zone;\n    tableService;\n    cd;\n    filterService;\n    overlayService;\n    config;\n    /**\n     * An array of objects to represent dynamic columns that are frozen.\n     * @group Props\n     */\n    frozenColumns;\n    /**\n     * An array of objects to display as frozen.\n     * @group Props\n     */\n    frozenValue;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the table.\n     * @group Props\n     */\n    tableStyle;\n    /**\n     * Style class of the table.\n     * @group Props\n     */\n    tableStyleClass;\n    /**\n     * When specified as true, enables the pagination.\n     * @group Props\n     */\n    paginator;\n    /**\n     * Number of page links to display in paginator.\n     * @group Props\n     */\n    pageLinks = 5;\n    /**\n     * Array of integer/object values to display inside rows per page dropdown of paginator\n     * @group Props\n     */\n    rowsPerPageOptions;\n    /**\n     * Whether to show it even there is only one page.\n     * @group Props\n     */\n    alwaysShowPaginator = true;\n    /**\n     * Position of the paginator, options are \"top\", \"bottom\" or \"both\".\n     * @group Props\n     */\n    paginatorPosition = 'bottom';\n    /**\n     * Custom style class for paginator\n     * @group Props\n     */\n    paginatorStyleClass;\n    /**\n     * Target element to attach the paginator dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    paginatorDropdownAppendTo;\n    /**\n     * Paginator dropdown height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    paginatorDropdownScrollHeight = '200px';\n    /**\n     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}\n     * @group Props\n     */\n    currentPageReportTemplate = '{currentPage} of {totalPages}';\n    /**\n     * Whether to display current page report.\n     * @group Props\n     */\n    showCurrentPageReport;\n    /**\n     * Whether to display a dropdown to navigate to any page.\n     * @group Props\n     */\n    showJumpToPageDropdown;\n    /**\n     * Whether to display a input to navigate to any page.\n     * @group Props\n     */\n    showJumpToPageInput;\n    /**\n     * When enabled, icons are displayed on paginator to go first and last page.\n     * @group Props\n     */\n    showFirstLastIcon = true;\n    /**\n     * Whether to show page links.\n     * @group Props\n     */\n    showPageLinks = true;\n    /**\n     * Sort order to use when an unsorted column gets sorted by user interaction.\n     * @group Props\n     */\n    defaultSortOrder = 1;\n    /**\n     * Defines whether sorting works on single column or on multiple columns.\n     * @group Props\n     */\n    sortMode = 'single';\n    /**\n     * When true, resets paginator to first page after sorting. Available only when sortMode is set to single.\n     * @group Props\n     */\n    resetPageOnSort = true;\n    /**\n     * Specifies the selection mode, valid values are \"single\" and \"multiple\".\n     * @group Props\n     */\n    selectionMode;\n    /**\n     * When enabled with paginator and checkbox selection mode, the select all checkbox in the header will select all rows on the current page.\n     * @group Props\n     */\n    selectionPageOnly;\n    /**\n     * Selected row with a context menu.\n     * @group Props\n     */\n    contextMenuSelection;\n    /**\n     * Callback to invoke on context menu selection change.\n     * @param {*} object - row data.\n     * @group Emits\n     */\n    contextMenuSelectionChange = new EventEmitter();\n    /**\n     *  Defines the behavior of context menu selection, in \"separate\" mode context menu updates contextMenuSelection property whereas in joint mode selection property is used instead so that when row selection is enabled, both row selection and context menu selection use the same property.\n     * @group Props\n     */\n    contextMenuSelectionMode = 'separate';\n    /**\n     * A property to uniquely identify a record in data.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Defines whether metaKey should be considered for the selection. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    metaKeySelection = true;\n    /**\n     * Defines if the row is selectable.\n     * @group Props\n     */\n    rowSelectable;\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.\n     * @group Props\n     */\n    rowTrackBy = (index, item) => item;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether to call lazy loading on initialization.\n     * @group Props\n     */\n    lazyLoadOnInit = true;\n    /**\n     * Algorithm to define if a row is selected, valid values are \"equals\" that compares by reference and \"deepEquals\" that compares all fields.\n     * @group Props\n     */\n    compareSelectionBy = 'deepEquals';\n    /**\n     * Character to use as the csv separator.\n     * @group Props\n     */\n    csvSeparator = ',';\n    /**\n     * Name of the exported file.\n     * @group Props\n     */\n    exportFilename = 'download';\n    /**\n     * An array of FilterMetadata objects to provide external filters.\n     * @group Props\n     */\n    filters = {};\n    /**\n     * An array of fields as string to use in global filtering.\n     * @group Props\n     */\n    globalFilterFields;\n    /**\n     * Delay in milliseconds before filtering the data.\n     * @group Props\n     */\n    filterDelay = 300;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    filterLocale;\n    /**\n     * Map instance to keep the expanded rows where key of the map is the data key of the row.\n     * @group Props\n     */\n    expandedRowKeys = {};\n    /**\n     * Map instance to keep the rows being edited where key of the map is the data key of the row.\n     * @group Props\n     */\n    editingRowKeys = {};\n    /**\n     * Whether multiple rows can be expanded at any time. Valid values are \"multiple\" and \"single\".\n     * @group Props\n     */\n    rowExpandMode = 'multiple';\n    /**\n     * Enables scrollable tables.\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Orientation of the scrolling, options are \"vertical\", \"horizontal\" and \"both\".\n     * @group Props\n     * @deprecated Property is obselete since v14.2.0.\n     */\n    scrollDirection = 'vertical';\n    /**\n     * Type of the row grouping, valid values are \"subheader\" and \"rowspan\".\n     * @group Props\n     */\n    rowGroupMode;\n    /**\n     * Height of the scroll viewport in fixed pixels or the \"flex\" keyword for a dynamic size.\n     * @group Props\n     */\n    scrollHeight;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of a row to use in calculations of virtual scrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * Threshold in milliseconds to delay lazy loading during scrolling.\n     * @group Props\n     */\n    virtualScrollDelay = 250;\n    /**\n     * Width of the frozen columns container.\n     * @group Props\n     */\n    frozenWidth;\n    /**\n     * Defines if the table is responsive.\n     * @group Props\n     * @deprecated table is always responsive with scrollable behavior.\n     */\n    get responsive() {\n      return this._responsive;\n    }\n    set responsive(val) {\n      this._responsive = val;\n      console.warn('responsive property is deprecated as table is always responsive with scrollable behavior.');\n    }\n    _responsive;\n    /**\n     * Local ng-template varilable of a ContextMenu.\n     * @group Props\n     */\n    contextMenu;\n    /**\n     * When enabled, columns can be resized using drag and drop.\n     * @group Props\n     */\n    resizableColumns;\n    /**\n     * Defines whether the overall table width should change on column resize, valid values are \"fit\" and \"expand\".\n     * @group Props\n     */\n    columnResizeMode = 'fit';\n    /**\n     * When enabled, columns can be reordered using drag and drop.\n     * @group Props\n     */\n    reorderableColumns;\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    loading;\n    /**\n     * The icon to show while indicating data load is in progress.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Whether to show the loading mask when loading property is true.\n     * @group Props\n     */\n    showLoader = true;\n    /**\n     * Adds hover effect to rows without the need for selectionMode. Note that tr elements that can be hovered need to have \"p-selectable-row\" class for rowHover to work.\n     * @group Props\n     */\n    rowHover;\n    /**\n     * Whether to use the default sorting or a custom one using sortFunction.\n     * @group Props\n     */\n    customSort;\n    /**\n     * Whether to use the initial sort badge or not.\n     * @group Props\n     */\n    showInitialSortBadge = true;\n    /**\n     * Whether the cell widths scale according to their content or not.  Deprecated:  Table layout is always \"auto\".\n     * @group Props\n     */\n    autoLayout;\n    /**\n     * Export function.\n     * @group Props\n     */\n    exportFunction;\n    /**\n     * Custom export header of the column to be exported as CSV.\n     * @group Props\n     */\n    exportHeader;\n    /**\n     * Unique identifier of a stateful table to use in state storage.\n     * @group Props\n     */\n    stateKey;\n    /**\n     * Defines where a stateful table keeps its state, valid values are \"session\" for sessionStorage and \"local\" for localStorage.\n     * @group Props\n     */\n    stateStorage = 'session';\n    /**\n     * Defines the editing mode, valid values are \"cell\" and \"row\".\n     * @group Props\n     */\n    editMode = 'cell';\n    /**\n     * Field name to use in row grouping.\n     * @group Props\n     */\n    groupRowsBy;\n    /**\n     * Order to sort when default row grouping is enabled.\n     * @group Props\n     */\n    groupRowsByOrder = 1;\n    /**\n     * Defines the responsive mode, valid options are \"stack\" and \"scroll\".\n     * @group Props\n     */\n    responsiveLayout = 'scroll';\n    /**\n     * The breakpoint to define the maximum width boundary when using stack responsive layout.\n     * @group Props\n     */\n    breakpoint = '960px';\n    /**\n     * Locale to be used in paginator formatting.\n     * @group Props\n     */\n    paginatorLocale;\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    get value() {\n      return this._value;\n    }\n    set value(val) {\n      this._value = val;\n    }\n    /**\n     * An array of objects to represent dynamic columns.\n     * @group Props\n     */\n    get columns() {\n      return this._columns;\n    }\n    set columns(cols) {\n      this._columns = cols;\n    }\n    /**\n     * Index of the first row to be displayed.\n     * @group Props\n     */\n    get first() {\n      return this._first;\n    }\n    set first(val) {\n      this._first = val;\n    }\n    /**\n     * Number of rows to display per page.\n     * @group Props\n     */\n    get rows() {\n      return this._rows;\n    }\n    set rows(val) {\n      this._rows = val;\n    }\n    /**\n     * Number of total records, defaults to length of value when not defined.\n     * @group Props\n     */\n    get totalRecords() {\n      return this._totalRecords;\n    }\n    set totalRecords(val) {\n      this._totalRecords = val;\n      this.tableService.onTotalRecordsChange(this._totalRecords);\n    }\n    /**\n     * Name of the field to sort data by default.\n     * @group Props\n     */\n    get sortField() {\n      return this._sortField;\n    }\n    set sortField(val) {\n      this._sortField = val;\n    }\n    /**\n     * Order to sort when default sorting is enabled.\n     * @group Props\n     */\n    get sortOrder() {\n      return this._sortOrder;\n    }\n    set sortOrder(val) {\n      this._sortOrder = val;\n    }\n    /**\n     * An array of SortMeta objects to sort the data by default in multiple sort mode.\n     * @group Props\n     */\n    get multiSortMeta() {\n      return this._multiSortMeta;\n    }\n    set multiSortMeta(val) {\n      this._multiSortMeta = val;\n    }\n    /**\n     * Selected row in single mode or an array of values in multiple mode.\n     * @group Props\n     */\n    get selection() {\n      return this._selection;\n    }\n    set selection(val) {\n      this._selection = val;\n    }\n    /**\n     * Whether all data is selected.\n     * @group Props\n     */\n    get selectAll() {\n      return this._selection;\n    }\n    set selectAll(val) {\n      this._selection = val;\n    }\n    /**\n     * Emits when the all of the items selected or unselected.\n     * @param {TableSelectAllChangeEvent} event - custom  all selection change event.\n     * @group Emits\n     */\n    selectAllChange = new EventEmitter();\n    /**\n     * Callback to invoke on selection changed.\n     * @param {any | null} value - selected data.\n     * @group Emits\n     */\n    selectionChange = new EventEmitter();\n    /**\n     * Callback to invoke when a row is selected.\n     * @param {TableRowSelectEvent} event - custom select event.\n     * @group Emits\n     */\n    onRowSelect = new EventEmitter();\n    /**\n     * Callback to invoke when a row is unselected.\n     * @param {TableRowUnSelectEvent} event - custom unselect event.\n     * @group Emits\n     */\n    onRowUnselect = new EventEmitter();\n    /**\n     * Callback to invoke when pagination occurs.\n     * @param {TablePageEvent} event - custom pagination event.\n     * @group Emits\n     */\n    onPage = new EventEmitter();\n    /**\n     * Callback to invoke when a column gets sorted.\n     * @param {Object} object - sort meta.\n     * @group Emits\n     */\n    onSort = new EventEmitter();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {TableFilterEvent} event - custom filtering event.\n     * @group Emits\n     */\n    onFilter = new EventEmitter();\n    /**\n     * Callback to invoke when paging, sorting or filtering happens in lazy mode.\n     * @param {TableLazyLoadEvent} event - custom lazy loading event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke when a row is expanded.\n     * @param {TableRowExpandEvent} event - custom row expand event.\n     * @group Emits\n     */\n    onRowExpand = new EventEmitter();\n    /**\n     * Callback to invoke when a row is collapsed.\n     * @param {TableRowCollapseEvent} event - custom row collapse event.\n     * @group Emits\n     */\n    onRowCollapse = new EventEmitter();\n    /**\n     * Callback to invoke when a row is selected with right click.\n     * @param {TableContextMenuSelectEvent} event - custom context menu select event.\n     * @group Emits\n     */\n    onContextMenuSelect = new EventEmitter();\n    /**\n     * Callback to invoke when a column is resized.\n     * @param {TableColResizeEvent} event - custom column resize event.\n     * @group Emits\n     */\n    onColResize = new EventEmitter();\n    /**\n     * Callback to invoke when a column is reordered.\n     * @param {TableColumnReorderEvent} event - custom column reorder event.\n     * @group Emits\n     */\n    onColReorder = new EventEmitter();\n    /**\n     * Callback to invoke when a row is reordered.\n     * @param {TableRowReorderEvent} event - custom row reorder event.\n     * @group Emits\n     */\n    onRowReorder = new EventEmitter();\n    /**\n     * Callback to invoke when a cell switches to edit mode.\n     * @param {TableEditInitEvent} event - custom edit init event.\n     * @group Emits\n     */\n    onEditInit = new EventEmitter();\n    /**\n     * Callback to invoke when cell edit is completed.\n     * @param {TableEditCompleteEvent} event - custom edit complete event.\n     * @group Emits\n     */\n    onEditComplete = new EventEmitter();\n    /**\n     * Callback to invoke when cell edit is cancelled with escape key.\n     * @param {TableEditCancelEvent} event - custom edit cancel event.\n     * @group Emits\n     */\n    onEditCancel = new EventEmitter();\n    /**\n     * Callback to invoke when state of header checkbox changes.\n     * @param {TableHeaderCheckboxToggleEvent} event - custom header checkbox event.\n     * @group Emits\n     */\n    onHeaderCheckboxToggle = new EventEmitter();\n    /**\n     * A function to implement custom sorting, refer to sorting section for details.\n     * @param {any} any - sort meta.\n     * @group Emits\n     */\n    sortFunction = new EventEmitter();\n    /**\n     * Callback to invoke on pagination.\n     * @param {number} number - first element.\n     * @group Emits\n     */\n    firstChange = new EventEmitter();\n    /**\n     * Callback to invoke on rows change.\n     * @param {number} number - Row count.\n     * @group Emits\n     */\n    rowsChange = new EventEmitter();\n    /**\n     * Callback to invoke table state is saved.\n     * @param {TableState} object - table state.\n     * @group Emits\n     */\n    onStateSave = new EventEmitter();\n    /**\n     * Callback to invoke table state is restored.\n     * @param {TableState} object - table state.\n     * @group Emits\n     */\n    onStateRestore = new EventEmitter();\n    containerViewChild;\n    resizeHelperViewChild;\n    reorderIndicatorUpViewChild;\n    reorderIndicatorDownViewChild;\n    wrapperViewChild;\n    tableViewChild;\n    tableHeaderViewChild;\n    tableFooterViewChild;\n    scroller;\n    templates;\n    /**\n     * Indicates the height of rows to be scrolled.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    get virtualRowHeight() {\n      return this._virtualRowHeight;\n    }\n    set virtualRowHeight(val) {\n      this._virtualRowHeight = val;\n      console.warn('The virtualRowHeight property is deprecated.');\n    }\n    _virtualRowHeight = 28;\n    _value = [];\n    _columns;\n    _totalRecords = 0;\n    _first = 0;\n    _rows;\n    filteredValue;\n    headerTemplate;\n    headerGroupedTemplate;\n    bodyTemplate;\n    loadingBodyTemplate;\n    captionTemplate;\n    footerTemplate;\n    footerGroupedTemplate;\n    summaryTemplate;\n    colGroupTemplate;\n    expandedRowTemplate;\n    groupHeaderTemplate;\n    groupFooterTemplate;\n    frozenExpandedRowTemplate;\n    frozenHeaderTemplate;\n    frozenBodyTemplate;\n    frozenFooterTemplate;\n    frozenColGroupTemplate;\n    emptyMessageTemplate;\n    paginatorLeftTemplate;\n    paginatorRightTemplate;\n    paginatorDropdownItemTemplate;\n    loadingIconTemplate;\n    reorderIndicatorUpIconTemplate;\n    reorderIndicatorDownIconTemplate;\n    sortIconTemplate;\n    checkboxIconTemplate;\n    headerCheckboxIconTemplate;\n    paginatorFirstPageLinkIconTemplate;\n    paginatorLastPageLinkIconTemplate;\n    paginatorPreviousPageLinkIconTemplate;\n    paginatorNextPageLinkIconTemplate;\n    selectionKeys = {};\n    lastResizerHelperX;\n    reorderIconWidth;\n    reorderIconHeight;\n    draggedColumn;\n    draggedRowIndex;\n    droppedRowIndex;\n    rowDragging;\n    dropPosition;\n    editingCell;\n    editingCellData;\n    editingCellField;\n    editingCellRowIndex;\n    selfClick;\n    documentEditListener;\n    _multiSortMeta;\n    _sortField;\n    _sortOrder = 1;\n    preventSelectionSetterPropagation;\n    _selection;\n    _selectAll = null;\n    anchorRowIndex;\n    rangeRowIndex;\n    filterTimeout;\n    initialized;\n    rowTouched;\n    restoringSort;\n    restoringFilter;\n    stateRestored;\n    columnOrderStateRestored;\n    columnWidthsState;\n    tableWidthState;\n    overlaySubscription;\n    resizeColumnElement;\n    columnResizing = false;\n    rowGroupHeaderStyleObject = {};\n    id = UniqueComponentId();\n    styleElement;\n    responsiveStyleElement;\n    window;\n    constructor(document, platformId, renderer, el, zone, tableService, cd, filterService, overlayService, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.el = el;\n      this.zone = zone;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.filterService = filterService;\n      this.overlayService = overlayService;\n      this.config = config;\n      this.window = this.document.defaultView;\n    }\n    ngOnInit() {\n      if (this.lazy && this.lazyLoadOnInit) {\n        if (!this.virtualScroll) {\n          this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n        if (this.restoringFilter) {\n          this.restoringFilter = false;\n        }\n      }\n      if (this.responsiveLayout === 'stack' && !this.scrollable) {\n        this.createResponsiveStyle();\n      }\n      this.initialized = true;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'caption':\n            this.captionTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'headergrouped':\n            this.headerGroupedTemplate = item.template;\n            break;\n          case 'body':\n            this.bodyTemplate = item.template;\n            break;\n          case 'loadingbody':\n            this.loadingBodyTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'footergrouped':\n            this.footerGroupedTemplate = item.template;\n            break;\n          case 'summary':\n            this.summaryTemplate = item.template;\n            break;\n          case 'colgroup':\n            this.colGroupTemplate = item.template;\n            break;\n          case 'rowexpansion':\n            this.expandedRowTemplate = item.template;\n            break;\n          case 'groupheader':\n            this.groupHeaderTemplate = item.template;\n            break;\n          case 'groupfooter':\n            this.groupFooterTemplate = item.template;\n            break;\n          case 'frozenheader':\n            this.frozenHeaderTemplate = item.template;\n            break;\n          case 'frozenbody':\n            this.frozenBodyTemplate = item.template;\n            break;\n          case 'frozenfooter':\n            this.frozenFooterTemplate = item.template;\n            break;\n          case 'frozencolgroup':\n            this.frozenColGroupTemplate = item.template;\n            break;\n          case 'frozenrowexpansion':\n            this.frozenExpandedRowTemplate = item.template;\n            break;\n          case 'emptymessage':\n            this.emptyMessageTemplate = item.template;\n            break;\n          case 'paginatorleft':\n            this.paginatorLeftTemplate = item.template;\n            break;\n          case 'paginatorright':\n            this.paginatorRightTemplate = item.template;\n            break;\n          case 'paginatordropdownitem':\n            this.paginatorDropdownItemTemplate = item.template;\n            break;\n          case 'paginatorfirstpagelinkicon':\n            this.paginatorFirstPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatorlastpagelinkicon':\n            this.paginatorLastPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatorpreviouspagelinkicon':\n            this.paginatorPreviousPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatornextpagelinkicon':\n            this.paginatorNextPageLinkIconTemplate = item.template;\n            break;\n          case 'loadingicon':\n            this.loadingIconTemplate = item.template;\n            break;\n          case 'reorderindicatorupicon':\n            this.reorderIndicatorUpIconTemplate = item.template;\n            break;\n          case 'reorderindicatordownicon':\n            this.reorderIndicatorDownIconTemplate = item.template;\n            break;\n          case 'sorticon':\n            this.sortIconTemplate = item.template;\n            break;\n          case 'checkboxicon':\n            this.checkboxIconTemplate = item.template;\n            break;\n          case 'headercheckboxicon':\n            this.headerCheckboxIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.isStateful() && this.resizableColumns) {\n          this.restoreColumnWidths();\n        }\n      }\n    }\n    ngOnChanges(simpleChange) {\n      if (simpleChange.value) {\n        if (this.isStateful() && !this.stateRestored && isPlatformBrowser(this.platformId)) {\n          this.restoreState();\n        }\n        this._value = simpleChange.value.currentValue;\n        if (!this.lazy) {\n          this.totalRecords = this._value ? this._value.length : 0;\n          if (this.sortMode == 'single' && (this.sortField || this.groupRowsBy)) this.sortSingle();else if (this.sortMode == 'multiple' && (this.multiSortMeta || this.groupRowsBy)) this.sortMultiple();else if (this.hasFilter())\n            //sort already filters\n            this._filter();\n        }\n        this.tableService.onValueChange(simpleChange.value.currentValue);\n      }\n      if (simpleChange.columns) {\n        this._columns = simpleChange.columns.currentValue;\n        this.tableService.onColumnsChange(simpleChange.columns.currentValue);\n        if (this._columns && this.isStateful() && this.reorderableColumns && !this.columnOrderStateRestored) {\n          this.restoreColumnOrder();\n        }\n      }\n      if (simpleChange.sortField) {\n        this._sortField = simpleChange.sortField.currentValue;\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.groupRowsBy) {\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.sortOrder) {\n        this._sortOrder = simpleChange.sortOrder.currentValue;\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.groupRowsByOrder) {\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.multiSortMeta) {\n        this._multiSortMeta = simpleChange.multiSortMeta.currentValue;\n        if (this.sortMode === 'multiple' && (this.initialized || !this.lazy && !this.virtualScroll)) {\n          this.sortMultiple();\n        }\n      }\n      if (simpleChange.selection) {\n        this._selection = simpleChange.selection.currentValue;\n        if (!this.preventSelectionSetterPropagation) {\n          this.updateSelectionKeys();\n          this.tableService.onSelectionChange();\n        }\n        this.preventSelectionSetterPropagation = false;\n      }\n      if (simpleChange.selectAll) {\n        this._selectAll = simpleChange.selectAll.currentValue;\n        if (!this.preventSelectionSetterPropagation) {\n          this.updateSelectionKeys();\n          this.tableService.onSelectionChange();\n          if (this.isStateful()) {\n            this.saveState();\n          }\n        }\n        this.preventSelectionSetterPropagation = false;\n      }\n    }\n    get processedData() {\n      return this.filteredValue || this.value || [];\n    }\n    _initialColWidths;\n    dataToRender(data) {\n      const _data = data || this.processedData;\n      if (_data && this.paginator) {\n        const first = this.lazy ? 0 : this.first;\n        return _data.slice(first, first + this.rows);\n      }\n      return _data;\n    }\n    updateSelectionKeys() {\n      if (this.dataKey && this._selection) {\n        this.selectionKeys = {};\n        if (Array.isArray(this._selection)) {\n          for (let data of this._selection) {\n            this.selectionKeys[String(ObjectUtils.resolveFieldData(data, this.dataKey))] = 1;\n          }\n        } else {\n          this.selectionKeys[String(ObjectUtils.resolveFieldData(this._selection, this.dataKey))] = 1;\n        }\n      }\n    }\n    onPageChange(event) {\n      this.first = event.first;\n      this.rows = event.rows;\n      this.onPage.emit({\n        first: this.first,\n        rows: this.rows\n      });\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      }\n      this.firstChange.emit(this.first);\n      this.rowsChange.emit(this.rows);\n      this.tableService.onValueChange(this.value);\n      if (this.isStateful()) {\n        this.saveState();\n      }\n      this.anchorRowIndex = null;\n      if (this.scrollable) {\n        this.resetScrollTop();\n      }\n    }\n    sort(event) {\n      let originalEvent = event.originalEvent;\n      if (this.sortMode === 'single') {\n        this._sortOrder = this.sortField === event.field ? this.sortOrder * -1 : this.defaultSortOrder;\n        this._sortField = event.field;\n        if (this.resetPageOnSort) {\n          this._first = 0;\n          this.firstChange.emit(this._first);\n          if (this.scrollable) {\n            this.resetScrollTop();\n          }\n        }\n        this.sortSingle();\n      }\n      if (this.sortMode === 'multiple') {\n        let metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n        let sortMeta = this.getSortMeta(event.field);\n        if (sortMeta) {\n          if (!metaKey) {\n            this._multiSortMeta = [{\n              field: event.field,\n              order: sortMeta.order * -1\n            }];\n            if (this.resetPageOnSort) {\n              this._first = 0;\n              this.firstChange.emit(this._first);\n              if (this.scrollable) {\n                this.resetScrollTop();\n              }\n            }\n          } else {\n            sortMeta.order = sortMeta.order * -1;\n          }\n        } else {\n          if (!metaKey || !this.multiSortMeta) {\n            this._multiSortMeta = [];\n            if (this.resetPageOnSort) {\n              this._first = 0;\n              this.firstChange.emit(this._first);\n            }\n          }\n          this._multiSortMeta.push({\n            field: event.field,\n            order: this.defaultSortOrder\n          });\n        }\n        this.sortMultiple();\n      }\n      if (this.isStateful()) {\n        this.saveState();\n      }\n      this.anchorRowIndex = null;\n    }\n    sortSingle() {\n      let field = this.sortField || this.groupRowsBy;\n      let order = this.sortField ? this.sortOrder : this.groupRowsByOrder;\n      if (this.groupRowsBy && this.sortField && this.groupRowsBy !== this.sortField) {\n        this._multiSortMeta = [this.getGroupRowsMeta(), {\n          field: this.sortField,\n          order: this.sortOrder\n        }];\n        this.sortMultiple();\n        return;\n      }\n      if (field && order) {\n        if (this.restoringSort) {\n          this.restoringSort = false;\n        }\n        if (this.lazy) {\n          this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else if (this.value) {\n          if (this.customSort) {\n            this.sortFunction.emit({\n              data: this.value,\n              mode: this.sortMode,\n              field: field,\n              order: order\n            });\n          } else {\n            this.value.sort((data1, data2) => {\n              let value1 = ObjectUtils.resolveFieldData(data1, field);\n              let value2 = ObjectUtils.resolveFieldData(data2, field);\n              let result = null;\n              if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n              return order * result;\n            });\n            this._value = [...this.value];\n          }\n          if (this.hasFilter()) {\n            this._filter();\n          }\n        }\n        let sortMeta = {\n          field: field,\n          order: order\n        };\n        this.onSort.emit(sortMeta);\n        this.tableService.onSort(sortMeta);\n      }\n    }\n    sortMultiple() {\n      if (this.groupRowsBy) {\n        if (!this._multiSortMeta) this._multiSortMeta = [this.getGroupRowsMeta()];else if (this.multiSortMeta[0].field !== this.groupRowsBy) this._multiSortMeta = [this.getGroupRowsMeta(), ...this._multiSortMeta];\n      }\n      if (this.multiSortMeta) {\n        if (this.lazy) {\n          this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else if (this.value) {\n          if (this.customSort) {\n            this.sortFunction.emit({\n              data: this.value,\n              mode: this.sortMode,\n              multiSortMeta: this.multiSortMeta\n            });\n          } else {\n            this.value.sort((data1, data2) => {\n              return this.multisortField(data1, data2, this.multiSortMeta, 0);\n            });\n            this._value = [...this.value];\n          }\n          if (this.hasFilter()) {\n            this._filter();\n          }\n        }\n        this.onSort.emit({\n          multisortmeta: this.multiSortMeta\n        });\n        this.tableService.onSort(this.multiSortMeta);\n      }\n    }\n    multisortField(data1, data2, multiSortMeta, index) {\n      const value1 = ObjectUtils.resolveFieldData(data1, multiSortMeta[index].field);\n      const value2 = ObjectUtils.resolveFieldData(data2, multiSortMeta[index].field);\n      if (ObjectUtils.compare(value1, value2, this.filterLocale) === 0) {\n        return multiSortMeta.length - 1 > index ? this.multisortField(data1, data2, multiSortMeta, index + 1) : 0;\n      }\n      return this.compareValuesOnSort(value1, value2, multiSortMeta[index].order);\n    }\n    compareValuesOnSort(value1, value2, order) {\n      return ObjectUtils.sort(value1, value2, order, this.filterLocale, this.sortOrder);\n    }\n    getSortMeta(field) {\n      if (this.multiSortMeta && this.multiSortMeta.length) {\n        for (let i = 0; i < this.multiSortMeta.length; i++) {\n          if (this.multiSortMeta[i].field === field) {\n            return this.multiSortMeta[i];\n          }\n        }\n      }\n      return null;\n    }\n    isSorted(field) {\n      if (this.sortMode === 'single') {\n        return this.sortField && this.sortField === field;\n      } else if (this.sortMode === 'multiple') {\n        let sorted = false;\n        if (this.multiSortMeta) {\n          for (let i = 0; i < this.multiSortMeta.length; i++) {\n            if (this.multiSortMeta[i].field == field) {\n              sorted = true;\n              break;\n            }\n          }\n        }\n        return sorted;\n      }\n    }\n    handleRowClick(event) {\n      let target = event.originalEvent.target;\n      let targetNode = target.nodeName;\n      let parentNode = target.parentElement && target.parentElement.nodeName;\n      if (targetNode == 'INPUT' || targetNode == 'BUTTON' || targetNode == 'A' || parentNode == 'INPUT' || parentNode == 'BUTTON' || parentNode == 'A' || DomHandler.hasClass(event.originalEvent.target, 'p-clickable')) {\n        return;\n      }\n      if (this.selectionMode) {\n        let rowData = event.rowData;\n        let rowIndex = event.rowIndex;\n        this.preventSelectionSetterPropagation = true;\n        if (this.isMultipleSelectionMode() && event.originalEvent.shiftKey && this.anchorRowIndex != null) {\n          DomHandler.clearSelection();\n          if (this.rangeRowIndex != null) {\n            this.clearSelectionRange(event.originalEvent);\n          }\n          this.rangeRowIndex = rowIndex;\n          this.selectRange(event.originalEvent, rowIndex);\n        } else {\n          let selected = this.isSelected(rowData);\n          if (!selected && !this.isRowSelectable(rowData, rowIndex)) {\n            return;\n          }\n          let metaSelection = this.rowTouched ? false : this.metaKeySelection;\n          let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowData, this.dataKey)) : null;\n          this.anchorRowIndex = rowIndex;\n          this.rangeRowIndex = rowIndex;\n          if (metaSelection) {\n            let metaKey = event.originalEvent.metaKey || event.originalEvent.ctrlKey;\n            if (selected && metaKey) {\n              if (this.isSingleSelectionMode()) {\n                this._selection = null;\n                this.selectionKeys = {};\n                this.selectionChange.emit(null);\n              } else {\n                let selectionIndex = this.findIndexInSelection(rowData);\n                this._selection = this.selection.filter((val, i) => i != selectionIndex);\n                this.selectionChange.emit(this.selection);\n                if (dataKeyValue) {\n                  delete this.selectionKeys[dataKeyValue];\n                }\n              }\n              this.onRowUnselect.emit({\n                originalEvent: event.originalEvent,\n                data: rowData,\n                type: 'row'\n              });\n            } else {\n              if (this.isSingleSelectionMode()) {\n                this._selection = rowData;\n                this.selectionChange.emit(rowData);\n                if (dataKeyValue) {\n                  this.selectionKeys = {};\n                  this.selectionKeys[dataKeyValue] = 1;\n                }\n              } else if (this.isMultipleSelectionMode()) {\n                if (metaKey) {\n                  this._selection = this.selection || [];\n                } else {\n                  this._selection = [];\n                  this.selectionKeys = {};\n                }\n                this._selection = [...this.selection, rowData];\n                this.selectionChange.emit(this.selection);\n                if (dataKeyValue) {\n                  this.selectionKeys[dataKeyValue] = 1;\n                }\n              }\n              this.onRowSelect.emit({\n                originalEvent: event.originalEvent,\n                data: rowData,\n                type: 'row',\n                index: rowIndex\n              });\n            }\n          } else {\n            if (this.selectionMode === 'single') {\n              if (selected) {\n                this._selection = null;\n                this.selectionKeys = {};\n                this.selectionChange.emit(this.selection);\n                this.onRowUnselect.emit({\n                  originalEvent: event.originalEvent,\n                  data: rowData,\n                  type: 'row',\n                  index: rowIndex\n                });\n              } else {\n                this._selection = rowData;\n                this.selectionChange.emit(this.selection);\n                this.onRowSelect.emit({\n                  originalEvent: event.originalEvent,\n                  data: rowData,\n                  type: 'row',\n                  index: rowIndex\n                });\n                if (dataKeyValue) {\n                  this.selectionKeys = {};\n                  this.selectionKeys[dataKeyValue] = 1;\n                }\n              }\n            } else if (this.selectionMode === 'multiple') {\n              if (selected) {\n                let selectionIndex = this.findIndexInSelection(rowData);\n                this._selection = this.selection.filter((val, i) => i != selectionIndex);\n                this.selectionChange.emit(this.selection);\n                this.onRowUnselect.emit({\n                  originalEvent: event.originalEvent,\n                  data: rowData,\n                  type: 'row',\n                  index: rowIndex\n                });\n                if (dataKeyValue) {\n                  delete this.selectionKeys[dataKeyValue];\n                }\n              } else {\n                this._selection = this.selection ? [...this.selection, rowData] : [rowData];\n                this.selectionChange.emit(this.selection);\n                this.onRowSelect.emit({\n                  originalEvent: event.originalEvent,\n                  data: rowData,\n                  type: 'row',\n                  index: rowIndex\n                });\n                if (dataKeyValue) {\n                  this.selectionKeys[dataKeyValue] = 1;\n                }\n              }\n            }\n          }\n        }\n        this.tableService.onSelectionChange();\n        if (this.isStateful()) {\n          this.saveState();\n        }\n      }\n      this.rowTouched = false;\n    }\n    handleRowTouchEnd(event) {\n      this.rowTouched = true;\n    }\n    handleRowRightClick(event) {\n      if (this.contextMenu) {\n        const rowData = event.rowData;\n        const rowIndex = event.rowIndex;\n        if (this.contextMenuSelectionMode === 'separate') {\n          this.contextMenuSelection = rowData;\n          this.contextMenuSelectionChange.emit(rowData);\n          this.onContextMenuSelect.emit({\n            originalEvent: event.originalEvent,\n            data: rowData,\n            index: event.rowIndex\n          });\n          this.contextMenu.show(event.originalEvent);\n          this.tableService.onContextMenu(rowData);\n        } else if (this.contextMenuSelectionMode === 'joint') {\n          this.preventSelectionSetterPropagation = true;\n          let selected = this.isSelected(rowData);\n          let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowData, this.dataKey)) : null;\n          if (!selected) {\n            if (!this.isRowSelectable(rowData, rowIndex)) {\n              return;\n            }\n            if (this.isSingleSelectionMode()) {\n              this.selection = rowData;\n              this.selectionChange.emit(rowData);\n              if (dataKeyValue) {\n                this.selectionKeys = {};\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            } else if (this.isMultipleSelectionMode()) {\n              this._selection = this.selection ? [...this.selection, rowData] : [rowData];\n              this.selectionChange.emit(this.selection);\n              if (dataKeyValue) {\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            }\n          }\n          this.tableService.onSelectionChange();\n          this.contextMenu.show(event.originalEvent);\n          this.onContextMenuSelect.emit({\n            originalEvent: event,\n            data: rowData,\n            index: event.rowIndex\n          });\n        }\n      }\n    }\n    selectRange(event, rowIndex) {\n      let rangeStart, rangeEnd;\n      if (this.anchorRowIndex > rowIndex) {\n        rangeStart = rowIndex;\n        rangeEnd = this.anchorRowIndex;\n      } else if (this.anchorRowIndex < rowIndex) {\n        rangeStart = this.anchorRowIndex;\n        rangeEnd = rowIndex;\n      } else {\n        rangeStart = rowIndex;\n        rangeEnd = rowIndex;\n      }\n      if (this.lazy && this.paginator) {\n        rangeStart -= this.first;\n      }\n      let rangeRowsData = [];\n      for (let i = rangeStart; i <= rangeEnd; i++) {\n        let rangeRowData = this.filteredValue ? this.filteredValue[i] : this.value[i];\n        if (!this.isSelected(rangeRowData)) {\n          if (!this.isRowSelectable(rangeRowData, rowIndex)) {\n            continue;\n          }\n          rangeRowsData.push(rangeRowData);\n          this._selection = [...this.selection, rangeRowData];\n          let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rangeRowData, this.dataKey)) : null;\n          if (dataKeyValue) {\n            this.selectionKeys[dataKeyValue] = 1;\n          }\n        }\n      }\n      this.selectionChange.emit(this.selection);\n      this.onRowSelect.emit({\n        originalEvent: event,\n        data: rangeRowsData,\n        type: 'row'\n      });\n    }\n    clearSelectionRange(event) {\n      let rangeStart, rangeEnd;\n      let rangeRowIndex = this.rangeRowIndex;\n      let anchorRowIndex = this.anchorRowIndex;\n      if (rangeRowIndex > anchorRowIndex) {\n        rangeStart = this.anchorRowIndex;\n        rangeEnd = this.rangeRowIndex;\n      } else if (rangeRowIndex < anchorRowIndex) {\n        rangeStart = this.rangeRowIndex;\n        rangeEnd = this.anchorRowIndex;\n      } else {\n        rangeStart = this.rangeRowIndex;\n        rangeEnd = this.rangeRowIndex;\n      }\n      for (let i = rangeStart; i <= rangeEnd; i++) {\n        let rangeRowData = this.value[i];\n        let selectionIndex = this.findIndexInSelection(rangeRowData);\n        this._selection = this.selection.filter((val, i) => i != selectionIndex);\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rangeRowData, this.dataKey)) : null;\n        if (dataKeyValue) {\n          delete this.selectionKeys[dataKeyValue];\n        }\n        this.onRowUnselect.emit({\n          originalEvent: event,\n          data: rangeRowData,\n          type: 'row'\n        });\n      }\n    }\n    isSelected(rowData) {\n      if (rowData && this.selection) {\n        if (this.dataKey) {\n          return this.selectionKeys[ObjectUtils.resolveFieldData(rowData, this.dataKey)] !== undefined;\n        } else {\n          if (Array.isArray(this.selection)) return this.findIndexInSelection(rowData) > -1;else return this.equals(rowData, this.selection);\n        }\n      }\n      return false;\n    }\n    findIndexInSelection(rowData) {\n      let index = -1;\n      if (this.selection && this.selection.length) {\n        for (let i = 0; i < this.selection.length; i++) {\n          if (this.equals(rowData, this.selection[i])) {\n            index = i;\n            break;\n          }\n        }\n      }\n      return index;\n    }\n    isRowSelectable(data, index) {\n      if (this.rowSelectable && !this.rowSelectable({\n        data,\n        index\n      })) {\n        return false;\n      }\n      return true;\n    }\n    toggleRowWithRadio(event, rowData) {\n      this.preventSelectionSetterPropagation = true;\n      if (this.selection != rowData) {\n        if (!this.isRowSelectable(rowData, event.rowIndex)) {\n          return;\n        }\n        this._selection = rowData;\n        this.selectionChange.emit(this.selection);\n        this.onRowSelect.emit({\n          originalEvent: event.originalEvent,\n          index: event.rowIndex,\n          data: rowData,\n          type: 'radiobutton'\n        });\n        if (this.dataKey) {\n          this.selectionKeys = {};\n          this.selectionKeys[String(ObjectUtils.resolveFieldData(rowData, this.dataKey))] = 1;\n        }\n      } else {\n        this._selection = null;\n        this.selectionChange.emit(this.selection);\n        this.onRowUnselect.emit({\n          originalEvent: event.originalEvent,\n          index: event.rowIndex,\n          data: rowData,\n          type: 'radiobutton'\n        });\n      }\n      this.tableService.onSelectionChange();\n      if (this.isStateful()) {\n        this.saveState();\n      }\n    }\n    toggleRowWithCheckbox(event, rowData) {\n      this.selection = this.selection || [];\n      let selected = this.isSelected(rowData);\n      let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowData, this.dataKey)) : null;\n      this.preventSelectionSetterPropagation = true;\n      if (selected) {\n        let selectionIndex = this.findIndexInSelection(rowData);\n        this._selection = this.selection.filter((val, i) => i != selectionIndex);\n        this.selectionChange.emit(this.selection);\n        this.onRowUnselect.emit({\n          originalEvent: event.originalEvent,\n          index: event.rowIndex,\n          data: rowData,\n          type: 'checkbox'\n        });\n        if (dataKeyValue) {\n          delete this.selectionKeys[dataKeyValue];\n        }\n      } else {\n        if (!this.isRowSelectable(rowData, event.rowIndex)) {\n          return;\n        }\n        this._selection = this.selection ? [...this.selection, rowData] : [rowData];\n        this.selectionChange.emit(this.selection);\n        this.onRowSelect.emit({\n          originalEvent: event.originalEvent,\n          index: event.rowIndex,\n          data: rowData,\n          type: 'checkbox'\n        });\n        if (dataKeyValue) {\n          this.selectionKeys[dataKeyValue] = 1;\n        }\n      }\n      this.tableService.onSelectionChange();\n      if (this.isStateful()) {\n        this.saveState();\n      }\n    }\n    toggleRowsWithCheckbox(event, check) {\n      if (this._selectAll !== null) {\n        this.selectAllChange.emit({\n          originalEvent: event,\n          checked: check\n        });\n      } else {\n        const data = this.selectionPageOnly ? this.dataToRender(this.processedData) : this.processedData;\n        let selection = this.selectionPageOnly && this._selection ? this._selection.filter(s => !data.some(d => this.equals(s, d))) : [];\n        if (check) {\n          selection = this.frozenValue ? [...selection, ...this.frozenValue, ...data] : [...selection, ...data];\n          selection = this.rowSelectable ? selection.filter((data, index) => this.rowSelectable({\n            data,\n            index\n          })) : selection;\n        }\n        this._selection = selection;\n        this.preventSelectionSetterPropagation = true;\n        this.updateSelectionKeys();\n        this.selectionChange.emit(this._selection);\n        this.tableService.onSelectionChange();\n        this.onHeaderCheckboxToggle.emit({\n          originalEvent: event,\n          checked: check\n        });\n        if (this.isStateful()) {\n          this.saveState();\n        }\n      }\n    }\n    equals(data1, data2) {\n      return this.compareSelectionBy === 'equals' ? data1 === data2 : ObjectUtils.equals(data1, data2, this.dataKey);\n    }\n    /* Legacy Filtering for custom elements */\n    filter(value, field, matchMode) {\n      if (this.filterTimeout) {\n        clearTimeout(this.filterTimeout);\n      }\n      if (!this.isFilterBlank(value)) {\n        this.filters[field] = {\n          value: value,\n          matchMode: matchMode\n        };\n      } else if (this.filters[field]) {\n        delete this.filters[field];\n      }\n      this.filterTimeout = setTimeout(() => {\n        this._filter();\n        this.filterTimeout = null;\n      }, this.filterDelay);\n      this.anchorRowIndex = null;\n    }\n    filterGlobal(value, matchMode) {\n      this.filter(value, 'global', matchMode);\n    }\n    isFilterBlank(filter) {\n      if (filter !== null && filter !== undefined) {\n        if (typeof filter === 'string' && filter.trim().length == 0 || Array.isArray(filter) && filter.length == 0) return true;else return false;\n      }\n      return true;\n    }\n    _filter() {\n      if (!this.restoringFilter) {\n        this.first = 0;\n        this.firstChange.emit(this.first);\n      }\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else {\n        if (!this.value) {\n          return;\n        }\n        if (!this.hasFilter()) {\n          this.filteredValue = null;\n          if (this.paginator) {\n            this.totalRecords = this.value ? this.value.length : 0;\n          }\n        } else {\n          let globalFilterFieldsArray;\n          if (this.filters['global']) {\n            if (!this.columns && !this.globalFilterFields) throw new Error('Global filtering requires dynamic columns or globalFilterFields to be defined.');else globalFilterFieldsArray = this.globalFilterFields || this.columns;\n          }\n          this.filteredValue = [];\n          for (let i = 0; i < this.value.length; i++) {\n            let localMatch = true;\n            let globalMatch = false;\n            let localFiltered = false;\n            for (let prop in this.filters) {\n              if (this.filters.hasOwnProperty(prop) && prop !== 'global') {\n                localFiltered = true;\n                let filterField = prop;\n                let filterMeta = this.filters[filterField];\n                if (Array.isArray(filterMeta)) {\n                  for (let meta of filterMeta) {\n                    localMatch = this.executeLocalFilter(filterField, this.value[i], meta);\n                    if (meta.operator === FilterOperator.OR && localMatch || meta.operator === FilterOperator.AND && !localMatch) {\n                      break;\n                    }\n                  }\n                } else {\n                  localMatch = this.executeLocalFilter(filterField, this.value[i], filterMeta);\n                }\n                if (!localMatch) {\n                  break;\n                }\n              }\n            }\n            if (this.filters['global'] && !globalMatch && globalFilterFieldsArray) {\n              for (let j = 0; j < globalFilterFieldsArray.length; j++) {\n                let globalFilterField = globalFilterFieldsArray[j].field || globalFilterFieldsArray[j];\n                globalMatch = this.filterService.filters[this.filters['global'].matchMode](ObjectUtils.resolveFieldData(this.value[i], globalFilterField), this.filters['global'].value, this.filterLocale);\n                if (globalMatch) {\n                  break;\n                }\n              }\n            }\n            let matches;\n            if (this.filters['global']) {\n              matches = localFiltered ? localFiltered && localMatch && globalMatch : globalMatch;\n            } else {\n              matches = localFiltered && localMatch;\n            }\n            if (matches) {\n              this.filteredValue.push(this.value[i]);\n            }\n          }\n          if (this.filteredValue.length === this.value.length) {\n            this.filteredValue = null;\n          }\n          if (this.paginator) {\n            this.totalRecords = this.filteredValue ? this.filteredValue.length : this.value ? this.value.length : 0;\n          }\n        }\n      }\n      this.onFilter.emit({\n        filters: this.filters,\n        filteredValue: this.filteredValue || this.value\n      });\n      this.tableService.onValueChange(this.value);\n      if (this.isStateful() && !this.restoringFilter) {\n        this.saveState();\n      }\n      if (this.restoringFilter) {\n        this.restoringFilter = false;\n      }\n      this.cd.markForCheck();\n      if (this.scrollable) {\n        this.resetScrollTop();\n      }\n    }\n    executeLocalFilter(field, rowData, filterMeta) {\n      let filterValue = filterMeta.value;\n      let filterMatchMode = filterMeta.matchMode || FilterMatchMode.STARTS_WITH;\n      let dataFieldValue = ObjectUtils.resolveFieldData(rowData, field);\n      let filterConstraint = this.filterService.filters[filterMatchMode];\n      return filterConstraint(dataFieldValue, filterValue, this.filterLocale);\n    }\n    hasFilter() {\n      let empty = true;\n      for (let prop in this.filters) {\n        if (this.filters.hasOwnProperty(prop)) {\n          empty = false;\n          break;\n        }\n      }\n      return !empty;\n    }\n    createLazyLoadMetadata() {\n      return {\n        first: this.first,\n        rows: this.rows,\n        sortField: this.sortField,\n        sortOrder: this.sortOrder,\n        filters: this.filters,\n        globalFilter: this.filters && this.filters['global'] ? this.filters['global'].value : null,\n        multiSortMeta: this.multiSortMeta,\n        forceUpdate: () => this.cd.detectChanges()\n      };\n    }\n    clear() {\n      this._sortField = null;\n      this._sortOrder = this.defaultSortOrder;\n      this._multiSortMeta = null;\n      this.tableService.onSort(null);\n      this.clearFilterValues();\n      this.filteredValue = null;\n      this.first = 0;\n      this.firstChange.emit(this.first);\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else {\n        this.totalRecords = this._value ? this._value.length : 0;\n      }\n    }\n    clearFilterValues() {\n      for (const [, filterMetadata] of Object.entries(this.filters)) {\n        if (Array.isArray(filterMetadata)) {\n          for (let filter of filterMetadata) {\n            filter.value = null;\n          }\n        } else if (filterMetadata) {\n          filterMetadata.value = null;\n        }\n      }\n    }\n    reset() {\n      this.clear();\n    }\n    getExportHeader(column) {\n      return column[this.exportHeader] || column.header || column.field;\n    }\n    /**\n     * Data export method.\n     * @param {ExportCSVOptions} object - Export options.\n     * @group Method\n     */\n    exportCSV(options) {\n      let data;\n      let csv = '';\n      let columns = this.columns;\n      if (options && options.selectionOnly) {\n        data = this.selection || [];\n      } else if (options && options.allValues) {\n        data = this.value || [];\n      } else {\n        data = this.filteredValue || this.value;\n        if (this.frozenValue) {\n          data = data ? [...this.frozenValue, ...data] : this.frozenValue;\n        }\n      }\n      //headers\n      for (let i = 0; i < columns.length; i++) {\n        let column = columns[i];\n        if (column.exportable !== false && column.field) {\n          csv += '\"' + this.getExportHeader(column) + '\"';\n          if (i < columns.length - 1) {\n            csv += this.csvSeparator;\n          }\n        }\n      }\n      //body\n      data.forEach((record, i) => {\n        csv += '\\n';\n        for (let i = 0; i < columns.length; i++) {\n          let column = columns[i];\n          if (column.exportable !== false && column.field) {\n            let cellData = ObjectUtils.resolveFieldData(record, column.field);\n            if (cellData != null) {\n              if (this.exportFunction) {\n                cellData = this.exportFunction({\n                  data: cellData,\n                  field: column.field\n                });\n              } else cellData = String(cellData).replace(/\"/g, '\"\"');\n            } else cellData = '';\n            csv += '\"' + cellData + '\"';\n            if (i < columns.length - 1) {\n              csv += this.csvSeparator;\n            }\n          }\n        }\n      });\n      let blob = new Blob([csv], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      let link = this.renderer.createElement('a');\n      link.style.display = 'none';\n      this.renderer.appendChild(this.document.body, link);\n      if (link.download !== undefined) {\n        link.setAttribute('href', URL.createObjectURL(blob));\n        link.setAttribute('download', this.exportFilename + '.csv');\n        link.click();\n      } else {\n        csv = 'data:text/csv;charset=utf-8,' + csv;\n        this.window.open(encodeURI(csv));\n      }\n      this.renderer.removeChild(this.document.body, link);\n    }\n    onLazyItemLoad(event) {\n      this.onLazyLoad.emit({\n        ...this.createLazyLoadMetadata(),\n        ...event,\n        rows: event.last - event.first\n      });\n    }\n    /**\n     * Resets scroll to top.\n     * @group Method\n     */\n    resetScrollTop() {\n      if (this.virtualScroll) this.scrollToVirtualIndex(0);else this.scrollTo({\n        top: 0\n      });\n    }\n    /**\n     * Scrolls to given index when using virtual scroll.\n     * @param {number} index - index of the element.\n     * @group Method\n     */\n    scrollToVirtualIndex(index) {\n      this.scroller && this.scroller.scrollToIndex(index);\n    }\n    /**\n     * Scrolls to given index.\n     * @param {ScrollToOptions} options - scroll options.\n     * @group Method\n     */\n    scrollTo(options) {\n      if (this.virtualScroll) {\n        this.scroller?.scrollTo(options);\n      } else if (this.wrapperViewChild && this.wrapperViewChild.nativeElement) {\n        if (this.wrapperViewChild.nativeElement.scrollTo) {\n          this.wrapperViewChild.nativeElement.scrollTo(options);\n        } else {\n          this.wrapperViewChild.nativeElement.scrollLeft = options.left;\n          this.wrapperViewChild.nativeElement.scrollTop = options.top;\n        }\n      }\n    }\n    updateEditingCell(cell, data, field, index) {\n      this.editingCell = cell;\n      this.editingCellData = data;\n      this.editingCellField = field;\n      this.editingCellRowIndex = index;\n      this.bindDocumentEditListener();\n    }\n    isEditingCellValid() {\n      return this.editingCell && DomHandler.find(this.editingCell, '.ng-invalid.ng-dirty').length === 0;\n    }\n    bindDocumentEditListener() {\n      if (!this.documentEditListener) {\n        this.documentEditListener = this.renderer.listen(this.document, 'click', event => {\n          if (this.editingCell && !this.selfClick && this.isEditingCellValid()) {\n            DomHandler.removeClass(this.editingCell, 'p-cell-editing');\n            this.editingCell = null;\n            this.onEditComplete.emit({\n              field: this.editingCellField,\n              data: this.editingCellData,\n              originalEvent: event,\n              index: this.editingCellRowIndex\n            });\n            this.editingCellField = null;\n            this.editingCellData = null;\n            this.editingCellRowIndex = null;\n            this.unbindDocumentEditListener();\n            this.cd.markForCheck();\n            if (this.overlaySubscription) {\n              this.overlaySubscription.unsubscribe();\n            }\n          }\n          this.selfClick = false;\n        });\n      }\n    }\n    unbindDocumentEditListener() {\n      if (this.documentEditListener) {\n        this.documentEditListener();\n        this.documentEditListener = null;\n      }\n    }\n    initRowEdit(rowData) {\n      let dataKeyValue = String(ObjectUtils.resolveFieldData(rowData, this.dataKey));\n      this.editingRowKeys[dataKeyValue] = true;\n    }\n    saveRowEdit(rowData, rowElement) {\n      if (DomHandler.find(rowElement, '.ng-invalid.ng-dirty').length === 0) {\n        let dataKeyValue = String(ObjectUtils.resolveFieldData(rowData, this.dataKey));\n        delete this.editingRowKeys[dataKeyValue];\n      }\n    }\n    cancelRowEdit(rowData) {\n      let dataKeyValue = String(ObjectUtils.resolveFieldData(rowData, this.dataKey));\n      delete this.editingRowKeys[dataKeyValue];\n    }\n    toggleRow(rowData, event) {\n      if (!this.dataKey) {\n        throw new Error('dataKey must be defined to use row expansion');\n      }\n      let dataKeyValue = String(ObjectUtils.resolveFieldData(rowData, this.dataKey));\n      if (this.expandedRowKeys[dataKeyValue] != null) {\n        delete this.expandedRowKeys[dataKeyValue];\n        this.onRowCollapse.emit({\n          originalEvent: event,\n          data: rowData\n        });\n      } else {\n        if (this.rowExpandMode === 'single') {\n          this.expandedRowKeys = {};\n        }\n        this.expandedRowKeys[dataKeyValue] = true;\n        this.onRowExpand.emit({\n          originalEvent: event,\n          data: rowData\n        });\n      }\n      if (event) {\n        event.preventDefault();\n      }\n      if (this.isStateful()) {\n        this.saveState();\n      }\n    }\n    isRowExpanded(rowData) {\n      return this.expandedRowKeys[String(ObjectUtils.resolveFieldData(rowData, this.dataKey))] === true;\n    }\n    isRowEditing(rowData) {\n      return this.editingRowKeys[String(ObjectUtils.resolveFieldData(rowData, this.dataKey))] === true;\n    }\n    isSingleSelectionMode() {\n      return this.selectionMode === 'single';\n    }\n    isMultipleSelectionMode() {\n      return this.selectionMode === 'multiple';\n    }\n    onColumnResizeBegin(event) {\n      let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n      this.resizeColumnElement = event.target.parentElement;\n      this.columnResizing = true;\n      this.lastResizerHelperX = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft;\n      this.onColumnResize(event);\n      event.preventDefault();\n    }\n    onColumnResize(event) {\n      let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n      DomHandler.addClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n      this.resizeHelperViewChild.nativeElement.style.height = this.containerViewChild?.nativeElement.offsetHeight + 'px';\n      this.resizeHelperViewChild.nativeElement.style.top = 0 + 'px';\n      this.resizeHelperViewChild.nativeElement.style.left = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft + 'px';\n      this.resizeHelperViewChild.nativeElement.style.display = 'block';\n    }\n    onColumnResizeEnd() {\n      let delta = this.resizeHelperViewChild?.nativeElement.offsetLeft - this.lastResizerHelperX;\n      let columnWidth = this.resizeColumnElement.offsetWidth;\n      let newColumnWidth = columnWidth + delta;\n      let minWidth = this.resizeColumnElement.style.minWidth.replace(/[^\\d.]/g, '') || 15;\n      if (newColumnWidth >= minWidth) {\n        if (this.columnResizeMode === 'fit') {\n          let nextColumn = this.resizeColumnElement.nextElementSibling;\n          let nextColumnWidth = nextColumn.offsetWidth - delta;\n          if (newColumnWidth > 15 && nextColumnWidth > 15) {\n            this.resizeTableCells(newColumnWidth, nextColumnWidth);\n          }\n        } else if (this.columnResizeMode === 'expand') {\n          this._initialColWidths = this._totalTableWidth();\n          let tableWidth = this.tableViewChild?.nativeElement.offsetWidth + delta;\n          this.setResizeTableWidth(tableWidth + 'px');\n          this.resizeTableCells(newColumnWidth, null);\n        }\n        this.onColResize.emit({\n          element: this.resizeColumnElement,\n          delta: delta\n        });\n        if (this.isStateful()) {\n          this.saveState();\n        }\n      }\n      this.resizeHelperViewChild.nativeElement.style.display = 'none';\n      DomHandler.removeClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n    }\n    _totalTableWidth() {\n      let widths = [];\n      const tableHead = DomHandler.findSingle(this.containerViewChild.nativeElement, '.p-datatable-thead');\n      let headers = DomHandler.find(tableHead, 'tr > th');\n      headers.forEach(header => widths.push(DomHandler.getOuterWidth(header)));\n      return widths;\n    }\n    onColumnDragStart(event, columnElement) {\n      this.reorderIconWidth = DomHandler.getHiddenElementOuterWidth(this.reorderIndicatorUpViewChild?.nativeElement);\n      this.reorderIconHeight = DomHandler.getHiddenElementOuterHeight(this.reorderIndicatorDownViewChild?.nativeElement);\n      this.draggedColumn = columnElement;\n      event.dataTransfer.setData('text', 'b'); // For firefox\n    }\n    onColumnDragEnter(event, dropHeader) {\n      if (this.reorderableColumns && this.draggedColumn && dropHeader) {\n        event.preventDefault();\n        let containerOffset = DomHandler.getOffset(this.containerViewChild?.nativeElement);\n        let dropHeaderOffset = DomHandler.getOffset(dropHeader);\n        if (this.draggedColumn != dropHeader) {\n          let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'preorderablecolumn');\n          let dropIndex = DomHandler.indexWithinGroup(dropHeader, 'preorderablecolumn');\n          let targetLeft = dropHeaderOffset.left - containerOffset.left;\n          let targetTop = containerOffset.top - dropHeaderOffset.top;\n          let columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;\n          this.reorderIndicatorUpViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top - (this.reorderIconHeight - 1) + 'px';\n          this.reorderIndicatorDownViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + 'px';\n          if (event.pageX > columnCenter) {\n            this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.dropPosition = 1;\n          } else {\n            this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.dropPosition = -1;\n          }\n          this.reorderIndicatorUpViewChild.nativeElement.style.display = 'block';\n          this.reorderIndicatorDownViewChild.nativeElement.style.display = 'block';\n        } else {\n          event.dataTransfer.dropEffect = 'none';\n        }\n      }\n    }\n    onColumnDragLeave(event) {\n      if (this.reorderableColumns && this.draggedColumn) {\n        event.preventDefault();\n      }\n    }\n    onColumnDrop(event, dropColumn) {\n      event.preventDefault();\n      if (this.draggedColumn) {\n        let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'preorderablecolumn');\n        let dropIndex = DomHandler.indexWithinGroup(dropColumn, 'preorderablecolumn');\n        let allowDrop = dragIndex != dropIndex;\n        if (allowDrop && (dropIndex - dragIndex == 1 && this.dropPosition === -1 || dragIndex - dropIndex == 1 && this.dropPosition === 1)) {\n          allowDrop = false;\n        }\n        if (allowDrop && dropIndex < dragIndex && this.dropPosition === 1) {\n          dropIndex = dropIndex + 1;\n        }\n        if (allowDrop && dropIndex > dragIndex && this.dropPosition === -1) {\n          dropIndex = dropIndex - 1;\n        }\n        if (allowDrop) {\n          ObjectUtils.reorderArray(this.columns, dragIndex, dropIndex);\n          this.onColReorder.emit({\n            dragIndex: dragIndex,\n            dropIndex: dropIndex,\n            columns: this.columns\n          });\n          if (this.isStateful()) {\n            this.zone.runOutsideAngular(() => {\n              setTimeout(() => {\n                this.saveState();\n              });\n            });\n          }\n        }\n        if (this.resizableColumns && this.resizeColumnElement) {\n          let width = this.columnResizeMode === 'expand' ? this._initialColWidths : this._totalTableWidth();\n          ObjectUtils.reorderArray(width, dragIndex + 1, dropIndex + 1);\n          this.updateStyleElement(width, dragIndex, null, null);\n        }\n        this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n        this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n        this.draggedColumn.draggable = false;\n        this.draggedColumn = null;\n        this.dropPosition = null;\n      }\n    }\n    resizeTableCells(newColumnWidth, nextColumnWidth) {\n      let colIndex = DomHandler.index(this.resizeColumnElement);\n      let width = this.columnResizeMode === 'expand' ? this._initialColWidths : this._totalTableWidth();\n      this.updateStyleElement(width, colIndex, newColumnWidth, nextColumnWidth);\n    }\n    updateStyleElement(width, colIndex, newColumnWidth, nextColumnWidth) {\n      this.destroyStyleElement();\n      this.createStyleElement();\n      let innerHTML = '';\n      width.forEach((width, index) => {\n        let colWidth = index === colIndex ? newColumnWidth : nextColumnWidth && index === colIndex + 1 ? nextColumnWidth : width;\n        let style = `width: ${colWidth}px !important; max-width: ${colWidth}px !important;`;\n        innerHTML += `\n                #${this.id}-table > .p-datatable-thead > tr > th:nth-child(${index + 1}),\n                #${this.id}-table > .p-datatable-tbody > tr > td:nth-child(${index + 1}),\n                #${this.id}-table > .p-datatable-tfoot > tr > td:nth-child(${index + 1}) {\n                    ${style}\n                }\n            `;\n      });\n      this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n    }\n    onRowDragStart(event, index) {\n      this.rowDragging = true;\n      this.draggedRowIndex = index;\n      event.dataTransfer.setData('text', 'b'); // For firefox\n    }\n    onRowDragOver(event, index, rowElement) {\n      if (this.rowDragging && this.draggedRowIndex !== index) {\n        let rowY = DomHandler.getOffset(rowElement).top;\n        let pageY = event.pageY;\n        let rowMidY = rowY + DomHandler.getOuterHeight(rowElement) / 2;\n        let prevRowElement = rowElement.previousElementSibling;\n        if (pageY < rowMidY) {\n          DomHandler.removeClass(rowElement, 'p-datatable-dragpoint-bottom');\n          this.droppedRowIndex = index;\n          if (prevRowElement) DomHandler.addClass(prevRowElement, 'p-datatable-dragpoint-bottom');else DomHandler.addClass(rowElement, 'p-datatable-dragpoint-top');\n        } else {\n          if (prevRowElement) DomHandler.removeClass(prevRowElement, 'p-datatable-dragpoint-bottom');else DomHandler.addClass(rowElement, 'p-datatable-dragpoint-top');\n          this.droppedRowIndex = index + 1;\n          DomHandler.addClass(rowElement, 'p-datatable-dragpoint-bottom');\n        }\n      }\n    }\n    onRowDragLeave(event, rowElement) {\n      let prevRowElement = rowElement.previousElementSibling;\n      if (prevRowElement) {\n        DomHandler.removeClass(prevRowElement, 'p-datatable-dragpoint-bottom');\n      }\n      DomHandler.removeClass(rowElement, 'p-datatable-dragpoint-bottom');\n      DomHandler.removeClass(rowElement, 'p-datatable-dragpoint-top');\n    }\n    onRowDragEnd(event) {\n      this.rowDragging = false;\n      this.draggedRowIndex = null;\n      this.droppedRowIndex = null;\n    }\n    onRowDrop(event, rowElement) {\n      if (this.droppedRowIndex != null) {\n        let dropIndex = this.draggedRowIndex > this.droppedRowIndex ? this.droppedRowIndex : this.droppedRowIndex === 0 ? 0 : this.droppedRowIndex - 1;\n        ObjectUtils.reorderArray(this.value, this.draggedRowIndex, dropIndex);\n        if (this.virtualScroll) {\n          // TODO: Check\n          this._value = [...this._value];\n        }\n        this.onRowReorder.emit({\n          dragIndex: this.draggedRowIndex,\n          dropIndex: dropIndex\n        });\n      }\n      //cleanup\n      this.onRowDragLeave(event, rowElement);\n      this.onRowDragEnd(event);\n    }\n    isEmpty() {\n      let data = this.filteredValue || this.value;\n      return data == null || data.length == 0;\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    getStorage() {\n      if (isPlatformBrowser(this.platformId)) {\n        switch (this.stateStorage) {\n          case 'local':\n            return window.localStorage;\n          case 'session':\n            return window.sessionStorage;\n          default:\n            throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n        }\n      } else {\n        throw new Error('Browser storage is not available in the server side.');\n      }\n    }\n    isStateful() {\n      return this.stateKey != null;\n    }\n    saveState() {\n      const storage = this.getStorage();\n      let state = {};\n      if (this.paginator) {\n        state.first = this.first;\n        state.rows = this.rows;\n      }\n      if (this.sortField) {\n        state.sortField = this.sortField;\n        state.sortOrder = this.sortOrder;\n      }\n      if (this.multiSortMeta) {\n        state.multiSortMeta = this.multiSortMeta;\n      }\n      if (this.hasFilter()) {\n        state.filters = this.filters;\n      }\n      if (this.resizableColumns) {\n        this.saveColumnWidths(state);\n      }\n      if (this.reorderableColumns) {\n        this.saveColumnOrder(state);\n      }\n      if (this.selection) {\n        state.selection = this.selection;\n      }\n      if (Object.keys(this.expandedRowKeys).length) {\n        state.expandedRowKeys = this.expandedRowKeys;\n      }\n      storage.setItem(this.stateKey, JSON.stringify(state));\n      this.onStateSave.emit(state);\n    }\n    clearState() {\n      const storage = this.getStorage();\n      if (this.stateKey) {\n        storage.removeItem(this.stateKey);\n      }\n    }\n    restoreState() {\n      const storage = this.getStorage();\n      const stateString = storage.getItem(this.stateKey);\n      const dateFormat = /\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}Z/;\n      const reviver = function (key, value) {\n        if (typeof value === 'string' && dateFormat.test(value)) {\n          return new Date(value);\n        }\n        return value;\n      };\n      if (stateString) {\n        let state = JSON.parse(stateString, reviver);\n        if (this.paginator) {\n          if (this.first !== undefined) {\n            this.first = state.first;\n            this.firstChange.emit(this.first);\n          }\n          if (this.rows !== undefined) {\n            this.rows = state.rows;\n            this.rowsChange.emit(this.rows);\n          }\n        }\n        if (state.sortField) {\n          this.restoringSort = true;\n          this._sortField = state.sortField;\n          this._sortOrder = state.sortOrder;\n        }\n        if (state.multiSortMeta) {\n          this.restoringSort = true;\n          this._multiSortMeta = state.multiSortMeta;\n        }\n        if (state.filters) {\n          this.restoringFilter = true;\n          this.filters = state.filters;\n        }\n        if (this.resizableColumns) {\n          this.columnWidthsState = state.columnWidths;\n          this.tableWidthState = state.tableWidth;\n        }\n        if (state.expandedRowKeys) {\n          this.expandedRowKeys = state.expandedRowKeys;\n        }\n        if (state.selection) {\n          Promise.resolve(null).then(() => this.selectionChange.emit(state.selection));\n        }\n        this.stateRestored = true;\n        this.onStateRestore.emit(state);\n      }\n    }\n    saveColumnWidths(state) {\n      let widths = [];\n      let headers = DomHandler.find(this.containerViewChild?.nativeElement, '.p-datatable-thead > tr > th');\n      headers.forEach(header => widths.push(DomHandler.getOuterWidth(header)));\n      state.columnWidths = widths.join(',');\n      if (this.columnResizeMode === 'expand') {\n        state.tableWidth = DomHandler.getOuterWidth(this.tableViewChild?.nativeElement);\n      }\n    }\n    setResizeTableWidth(width) {\n      this.tableViewChild.nativeElement.style.width = width;\n      this.tableViewChild.nativeElement.style.minWidth = width;\n    }\n    restoreColumnWidths() {\n      if (this.columnWidthsState) {\n        let widths = this.columnWidthsState.split(',');\n        if (this.columnResizeMode === 'expand' && this.tableWidthState) {\n          this.setResizeTableWidth(this.tableWidthState + 'px');\n        }\n        if (ObjectUtils.isNotEmpty(widths)) {\n          this.createStyleElement();\n          let innerHTML = '';\n          widths.forEach((width, index) => {\n            let style = `width: ${width}px !important; max-width: ${width}px !important`;\n            innerHTML += `\n                        #${this.id}-table > .p-datatable-thead > tr > th:nth-child(${index + 1}),\n                        #${this.id}-table > .p-datatable-tbody > tr > td:nth-child(${index + 1}),\n                        #${this.id}-table > .p-datatable-tfoot > tr > td:nth-child(${index + 1}) {\n                            ${style}\n                        }\n                    `;\n          });\n          this.styleElement.innerHTML = innerHTML;\n        }\n      }\n    }\n    saveColumnOrder(state) {\n      if (this.columns) {\n        let columnOrder = [];\n        this.columns.map(column => {\n          columnOrder.push(column.field || column.key);\n        });\n        state.columnOrder = columnOrder;\n      }\n    }\n    restoreColumnOrder() {\n      const storage = this.getStorage();\n      const stateString = storage.getItem(this.stateKey);\n      if (stateString) {\n        let state = JSON.parse(stateString);\n        let columnOrder = state.columnOrder;\n        if (columnOrder) {\n          let reorderedColumns = [];\n          columnOrder.map(key => {\n            let col = this.findColumnByKey(key);\n            if (col) {\n              reorderedColumns.push(col);\n            }\n          });\n          this.columnOrderStateRestored = true;\n          this.columns = reorderedColumns;\n        }\n      }\n    }\n    findColumnByKey(key) {\n      if (this.columns) {\n        for (let col of this.columns) {\n          if (col.key === key || col.field === key) return col;else continue;\n        }\n      } else {\n        return null;\n      }\n    }\n    createStyleElement() {\n      this.styleElement = this.renderer.createElement('style');\n      this.styleElement.type = 'text/css';\n      this.renderer.appendChild(this.document.head, this.styleElement);\n    }\n    getGroupRowsMeta() {\n      return {\n        field: this.groupRowsBy,\n        order: this.groupRowsByOrder\n      };\n    }\n    createResponsiveStyle() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.responsiveStyleElement) {\n          this.responsiveStyleElement = this.renderer.createElement('style');\n          this.responsiveStyleElement.type = 'text/css';\n          this.renderer.appendChild(this.document.head, this.responsiveStyleElement);\n          let innerHTML = `\n    @media screen and (max-width: ${this.breakpoint}) {\n        #${this.id}-table > .p-datatable-thead > tr > th,\n        #${this.id}-table > .p-datatable-tfoot > tr > td {\n            display: none !important;\n        }\n\n        #${this.id}-table > .p-datatable-tbody > tr > td {\n            display: flex;\n            width: 100% !important;\n            align-items: center;\n            justify-content: space-between;\n        }\n\n        #${this.id}-table > .p-datatable-tbody > tr > td:not(:last-child) {\n            border: 0 none;\n        }\n\n        #${this.id}.p-datatable-gridlines > .p-datatable-wrapper > .p-datatable-table > .p-datatable-tbody > tr > td:last-child {\n            border-top: 0;\n            border-right: 0;\n            border-left: 0;\n        }\n\n        #${this.id}-table > .p-datatable-tbody > tr > td > .p-column-title {\n            display: block;\n        }\n    }\n    `;\n          this.renderer.setProperty(this.responsiveStyleElement, 'innerHTML', innerHTML);\n        }\n      }\n    }\n    destroyResponsiveStyle() {\n      if (this.responsiveStyleElement) {\n        this.renderer.removeChild(this.document.head, this.responsiveStyleElement);\n        this.responsiveStyleElement = null;\n      }\n    }\n    destroyStyleElement() {\n      if (this.styleElement) {\n        this.renderer.removeChild(this.document.head, this.styleElement);\n        this.styleElement = null;\n      }\n    }\n    ngOnDestroy() {\n      this.unbindDocumentEditListener();\n      this.editingCell = null;\n      this.initialized = null;\n      this.destroyStyleElement();\n      this.destroyResponsiveStyle();\n    }\n    static ɵfac = function Table_Factory(t) {\n      return new (t || Table)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FilterService), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Table,\n      selectors: [[\"p-table\"]],\n      contentQueries: function Table_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Table_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n          i0.ɵɵviewQuery(_c8, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resizeHelperViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorUpViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorDownViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapperViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableHeaderViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableFooterViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        frozenColumns: \"frozenColumns\",\n        frozenValue: \"frozenValue\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        tableStyle: \"tableStyle\",\n        tableStyleClass: \"tableStyleClass\",\n        paginator: \"paginator\",\n        pageLinks: \"pageLinks\",\n        rowsPerPageOptions: \"rowsPerPageOptions\",\n        alwaysShowPaginator: \"alwaysShowPaginator\",\n        paginatorPosition: \"paginatorPosition\",\n        paginatorStyleClass: \"paginatorStyleClass\",\n        paginatorDropdownAppendTo: \"paginatorDropdownAppendTo\",\n        paginatorDropdownScrollHeight: \"paginatorDropdownScrollHeight\",\n        currentPageReportTemplate: \"currentPageReportTemplate\",\n        showCurrentPageReport: \"showCurrentPageReport\",\n        showJumpToPageDropdown: \"showJumpToPageDropdown\",\n        showJumpToPageInput: \"showJumpToPageInput\",\n        showFirstLastIcon: \"showFirstLastIcon\",\n        showPageLinks: \"showPageLinks\",\n        defaultSortOrder: \"defaultSortOrder\",\n        sortMode: \"sortMode\",\n        resetPageOnSort: \"resetPageOnSort\",\n        selectionMode: \"selectionMode\",\n        selectionPageOnly: \"selectionPageOnly\",\n        contextMenuSelection: \"contextMenuSelection\",\n        contextMenuSelectionMode: \"contextMenuSelectionMode\",\n        dataKey: \"dataKey\",\n        metaKeySelection: \"metaKeySelection\",\n        rowSelectable: \"rowSelectable\",\n        rowTrackBy: \"rowTrackBy\",\n        lazy: \"lazy\",\n        lazyLoadOnInit: \"lazyLoadOnInit\",\n        compareSelectionBy: \"compareSelectionBy\",\n        csvSeparator: \"csvSeparator\",\n        exportFilename: \"exportFilename\",\n        filters: \"filters\",\n        globalFilterFields: \"globalFilterFields\",\n        filterDelay: \"filterDelay\",\n        filterLocale: \"filterLocale\",\n        expandedRowKeys: \"expandedRowKeys\",\n        editingRowKeys: \"editingRowKeys\",\n        rowExpandMode: \"rowExpandMode\",\n        scrollable: \"scrollable\",\n        scrollDirection: \"scrollDirection\",\n        rowGroupMode: \"rowGroupMode\",\n        scrollHeight: \"scrollHeight\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        virtualScrollDelay: \"virtualScrollDelay\",\n        frozenWidth: \"frozenWidth\",\n        responsive: \"responsive\",\n        contextMenu: \"contextMenu\",\n        resizableColumns: \"resizableColumns\",\n        columnResizeMode: \"columnResizeMode\",\n        reorderableColumns: \"reorderableColumns\",\n        loading: \"loading\",\n        loadingIcon: \"loadingIcon\",\n        showLoader: \"showLoader\",\n        rowHover: \"rowHover\",\n        customSort: \"customSort\",\n        showInitialSortBadge: \"showInitialSortBadge\",\n        autoLayout: \"autoLayout\",\n        exportFunction: \"exportFunction\",\n        exportHeader: \"exportHeader\",\n        stateKey: \"stateKey\",\n        stateStorage: \"stateStorage\",\n        editMode: \"editMode\",\n        groupRowsBy: \"groupRowsBy\",\n        groupRowsByOrder: \"groupRowsByOrder\",\n        responsiveLayout: \"responsiveLayout\",\n        breakpoint: \"breakpoint\",\n        paginatorLocale: \"paginatorLocale\",\n        value: \"value\",\n        columns: \"columns\",\n        first: \"first\",\n        rows: \"rows\",\n        totalRecords: \"totalRecords\",\n        sortField: \"sortField\",\n        sortOrder: \"sortOrder\",\n        multiSortMeta: \"multiSortMeta\",\n        selection: \"selection\",\n        selectAll: \"selectAll\",\n        virtualRowHeight: \"virtualRowHeight\"\n      },\n      outputs: {\n        contextMenuSelectionChange: \"contextMenuSelectionChange\",\n        selectAllChange: \"selectAllChange\",\n        selectionChange: \"selectionChange\",\n        onRowSelect: \"onRowSelect\",\n        onRowUnselect: \"onRowUnselect\",\n        onPage: \"onPage\",\n        onSort: \"onSort\",\n        onFilter: \"onFilter\",\n        onLazyLoad: \"onLazyLoad\",\n        onRowExpand: \"onRowExpand\",\n        onRowCollapse: \"onRowCollapse\",\n        onContextMenuSelect: \"onContextMenuSelect\",\n        onColResize: \"onColResize\",\n        onColReorder: \"onColReorder\",\n        onRowReorder: \"onRowReorder\",\n        onEditInit: \"onEditInit\",\n        onEditComplete: \"onEditComplete\",\n        onEditCancel: \"onEditCancel\",\n        onHeaderCheckboxToggle: \"onHeaderCheckboxToggle\",\n        sortFunction: \"sortFunction\",\n        firstChange: \"firstChange\",\n        rowsChange: \"rowsChange\",\n        onStateSave: \"onStateSave\",\n        onStateRestore: \"onStateRestore\"\n      },\n      features: [i0.ɵɵProvidersFeature([TableService]), i0.ɵɵNgOnChangesFeature],\n      decls: 16,\n      vars: 22,\n      consts: [[\"container\", \"\"], [\"wrapper\", \"\"], [\"buildInTable\", \"\"], [\"scroller\", \"\"], [\"table\", \"\"], [\"thead\", \"\"], [\"tfoot\", \"\"], [\"resizeHelper\", \"\"], [\"reorderIndicatorUp\", \"\"], [\"reorderIndicatorDown\", \"\"], [3, \"ngStyle\", \"ngClass\"], [\"class\", \"p-datatable-loading-overlay p-component-overlay\", 4, \"ngIf\"], [\"class\", \"p-datatable-header\", 4, \"ngIf\"], [\"styleClass\", \"p-paginator-top\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"dropdownScrollHeight\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showJumpToPageInput\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\", 4, \"ngIf\"], [1, \"p-datatable-wrapper\", 3, \"ngStyle\"], [3, \"items\", \"columns\", \"style\", \"scrollHeight\", \"itemSize\", \"step\", \"delay\", \"inline\", \"lazy\", \"loaderDisabled\", \"showSpacer\", \"showLoader\", \"options\", \"autoSize\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"dropdownScrollHeight\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showJumpToPageInput\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\", 4, \"ngIf\"], [\"class\", \"p-datatable-footer\", 4, \"ngIf\"], [\"class\", \"p-column-resizer-helper\", \"style\", \"display:none\", 4, \"ngIf\"], [\"class\", \"p-datatable-reorder-indicator-up\", \"style\", \"display: none;\", 4, \"ngIf\"], [\"class\", \"p-datatable-reorder-indicator-down\", \"style\", \"display: none;\", 4, \"ngIf\"], [1, \"p-datatable-loading-overlay\", \"p-component-overlay\"], [3, \"class\", 4, \"ngIf\"], [3, \"spin\", \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-datatable-loading-icon\", 4, \"ngIf\"], [3, \"spin\", \"styleClass\"], [1, \"p-datatable-loading-icon\"], [4, \"ngTemplateOutlet\"], [1, \"p-datatable-header\"], [\"styleClass\", \"p-paginator-top\", 3, \"onPageChange\", \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"dropdownScrollHeight\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showJumpToPageInput\", \"showPageLinks\", \"styleClass\", \"locale\"], [\"pTemplate\", \"firstpagelinkicon\"], [\"pTemplate\", \"previouspagelinkicon\"], [\"pTemplate\", \"lastpagelinkicon\"], [\"pTemplate\", \"nextpagelinkicon\"], [3, \"onLazyLoad\", \"items\", \"columns\", \"scrollHeight\", \"itemSize\", \"step\", \"delay\", \"inline\", \"lazy\", \"loaderDisabled\", \"showSpacer\", \"showLoader\", \"options\", \"autoSize\"], [\"pTemplate\", \"content\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"table\", 3, \"ngClass\"], [\"role\", \"rowgroup\", 1, \"p-datatable-thead\"], [\"role\", \"rowgroup\", \"class\", \"p-datatable-tbody p-datatable-frozen-tbody\", 3, \"value\", \"frozenRows\", \"pTableBody\", \"pTableBodyTemplate\", \"frozen\", 4, \"ngIf\"], [\"role\", \"rowgroup\", 1, \"p-datatable-tbody\", 3, \"ngClass\", \"value\", \"pTableBody\", \"pTableBodyTemplate\", \"scrollerOptions\"], [\"role\", \"rowgroup\", \"class\", \"p-datatable-scroller-spacer\", 3, \"style\", 4, \"ngIf\"], [\"role\", \"rowgroup\", \"class\", \"p-datatable-tfoot\", 4, \"ngIf\"], [\"role\", \"rowgroup\", 1, \"p-datatable-tbody\", \"p-datatable-frozen-tbody\", 3, \"value\", \"frozenRows\", \"pTableBody\", \"pTableBodyTemplate\", \"frozen\"], [\"role\", \"rowgroup\", 1, \"p-datatable-scroller-spacer\"], [\"role\", \"rowgroup\", 1, \"p-datatable-tfoot\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"onPageChange\", \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"dropdownScrollHeight\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showJumpToPageInput\", \"showPageLinks\", \"styleClass\", \"locale\"], [1, \"p-datatable-footer\"], [1, \"p-column-resizer-helper\", 2, \"display\", \"none\"], [1, \"p-datatable-reorder-indicator-up\", 2, \"display\", \"none\"], [1, \"p-datatable-reorder-indicator-down\", 2, \"display\", \"none\"]],\n      template: function Table_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 10, 0);\n          i0.ɵɵtemplate(2, Table_div_2_Template, 3, 2, \"div\", 11)(3, Table_div_3_Template, 2, 1, \"div\", 12)(4, Table_p_paginator_4_Template, 5, 23, \"p-paginator\", 13);\n          i0.ɵɵelementStart(5, \"div\", 14, 1);\n          i0.ɵɵtemplate(7, Table_p_scroller_7_Template, 3, 17, \"p-scroller\", 15)(8, Table_ng_container_8_Template, 2, 7, \"ng-container\", 16)(9, Table_ng_template_9_Template, 10, 28, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, Table_p_paginator_11_Template, 5, 23, \"p-paginator\", 17)(12, Table_div_12_Template, 2, 1, \"div\", 18)(13, Table_div_13_Template, 2, 0, \"div\", 19)(14, Table_span_14_Template, 4, 2, \"span\", 20)(15, Table_span_15_Template, 4, 2, \"span\", 21);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction3(16, _c9, ctx.rowHover || ctx.selectionMode, ctx.scrollable, ctx.scrollable && ctx.scrollHeight === \"flex\"));\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.showLoader);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.captionTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"top\" || ctx.paginatorPosition == \"both\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c10, ctx.virtualScroll ? \"\" : ctx.scrollHeight));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.virtualScroll);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.virtualScroll);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"bottom\" || ctx.paginatorPosition == \"both\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.summaryTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.resizableColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Paginator, i1.PrimeTemplate, i4.Scroller, ArrowDownIcon, ArrowUpIcon, SpinnerIcon, TableBody],\n      styles: [\"@layer primeng{.p-datatable{position:relative}.p-datatable>.p-datatable-wrapper{overflow:auto}.p-datatable-table{border-spacing:0px;width:100%}.p-datatable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-datatable .p-sortable-column .p-column-title,.p-datatable .p-sortable-column .p-sortable-column-icon,.p-datatable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-datatable .p-sortable-column .p-icon-wrapper{display:inline}.p-datatable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-datatable-hoverable-rows .p-selectable-row{cursor:pointer}.p-datatable-scrollable>.p-datatable-wrapper{position:relative}.p-datatable-scrollable-table>.p-datatable-thead{position:sticky;top:0;z-index:2}.p-datatable-scrollable-table>.p-datatable-frozen-tbody{position:sticky;z-index:1}.p-datatable-scrollable-table>.p-datatable-tfoot{position:sticky;bottom:0;z-index:1}.p-datatable-scrollable .p-frozen-column{position:sticky;background:inherit;z-index:1}.p-datatable-scrollable th.p-frozen-column{z-index:1}.p-datatable-flex-scrollable{display:flex;flex-direction:column;height:100%}.p-datatable-flex-scrollable>.p-datatable-wrapper{display:flex;flex-direction:column;flex:1;height:100%}.p-datatable-scrollable-table>.p-datatable-tbody>.p-rowgroup-header{position:sticky;z-index:2}.p-datatable-resizable-table>.p-datatable-thead>tr>th,.p-datatable-resizable-table>.p-datatable-tfoot>tr>td,.p-datatable-resizable-table>.p-datatable-tbody>tr>td{overflow:hidden;white-space:nowrap}.p-datatable-resizable-table>.p-datatable-thead>tr>th.p-resizable-column:not(.p-frozen-column){background-clip:padding-box;position:relative}.p-datatable-resizable-table-fit>.p-datatable-thead>tr>th.p-resizable-column:last-child .p-column-resizer{display:none}.p-datatable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-datatable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-datatable .p-row-editor-init,.p-datatable .p-row-editor-save,.p-datatable .p-row-editor-cancel,.p-datatable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-datatable-reorder-indicator-up,.p-datatable-reorder-indicator-down{position:absolute}.p-datatable-reorderablerow-handle,[pReorderableColumn]{cursor:move}.p-datatable .p-datatable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:3}.p-column-filter-row{display:flex;align-items:center;width:100%}.p-column-filter-menu{display:inline-flex}.p-column-filter-row p-columnfilterformelement{flex:1 1 auto;width:1%}.p-column-filter-menu-button,.p-column-filter-clear-button{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;text-decoration:none;overflow:hidden;position:relative}.p-column-filter-overlay{position:absolute;top:0;left:0}.p-column-filter-row-items{margin:0;padding:0;list-style:none}.p-column-filter-row-item{cursor:pointer}.p-column-filter-add-button,.p-column-filter-remove-button{justify-content:center}.p-column-filter-add-button .p-button-label,.p-column-filter-remove-button .p-button-label{flex-grow:0}.p-column-filter-buttonbar{display:flex;align-items:center;justify-content:space-between}.p-column-filter-buttonbar .p-button{width:auto}.p-datatable-tbody>tr>td>.p-column-title{display:none}.p-datatable-scroller-spacer{display:flex}.p-datatable .p-scroller .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return Table;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TableBody = /*#__PURE__*/(() => {\n  class TableBody {\n    dt;\n    tableService;\n    cd;\n    el;\n    columns;\n    template;\n    get value() {\n      return this._value;\n    }\n    set value(val) {\n      this._value = val;\n      if (this.frozenRows) {\n        this.updateFrozenRowStickyPosition();\n      }\n      if (this.dt.scrollable && this.dt.rowGroupMode === 'subheader') {\n        this.updateFrozenRowGroupHeaderStickyPosition();\n      }\n    }\n    frozen;\n    frozenRows;\n    scrollerOptions;\n    subscription;\n    _value;\n    ngAfterViewInit() {\n      if (this.frozenRows) {\n        this.updateFrozenRowStickyPosition();\n      }\n      if (this.dt.scrollable && this.dt.rowGroupMode === 'subheader') {\n        this.updateFrozenRowGroupHeaderStickyPosition();\n      }\n    }\n    constructor(dt, tableService, cd, el) {\n      this.dt = dt;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.el = el;\n      this.subscription = this.dt.tableService.valueSource$.subscribe(() => {\n        if (this.dt.virtualScroll) {\n          this.cd.detectChanges();\n        }\n      });\n    }\n    shouldRenderRowGroupHeader(value, rowData, i) {\n      let currentRowFieldData = ObjectUtils.resolveFieldData(rowData, this.dt.groupRowsBy);\n      let prevRowData = value[i - 1];\n      if (prevRowData) {\n        let previousRowFieldData = ObjectUtils.resolveFieldData(prevRowData, this.dt.groupRowsBy);\n        return currentRowFieldData !== previousRowFieldData;\n      } else {\n        return true;\n      }\n    }\n    shouldRenderRowGroupFooter(value, rowData, i) {\n      let currentRowFieldData = ObjectUtils.resolveFieldData(rowData, this.dt.groupRowsBy);\n      let nextRowData = value[i + 1];\n      if (nextRowData) {\n        let nextRowFieldData = ObjectUtils.resolveFieldData(nextRowData, this.dt.groupRowsBy);\n        return currentRowFieldData !== nextRowFieldData;\n      } else {\n        return true;\n      }\n    }\n    shouldRenderRowspan(value, rowData, i) {\n      let currentRowFieldData = ObjectUtils.resolveFieldData(rowData, this.dt.groupRowsBy);\n      let prevRowData = value[i - 1];\n      if (prevRowData) {\n        let previousRowFieldData = ObjectUtils.resolveFieldData(prevRowData, this.dt.groupRowsBy);\n        return currentRowFieldData !== previousRowFieldData;\n      } else {\n        return true;\n      }\n    }\n    calculateRowGroupSize(value, rowData, index) {\n      let currentRowFieldData = ObjectUtils.resolveFieldData(rowData, this.dt.groupRowsBy);\n      let nextRowFieldData = currentRowFieldData;\n      let groupRowSpan = 0;\n      while (currentRowFieldData === nextRowFieldData) {\n        groupRowSpan++;\n        let nextRowData = value[++index];\n        if (nextRowData) {\n          nextRowFieldData = ObjectUtils.resolveFieldData(nextRowData, this.dt.groupRowsBy);\n        } else {\n          break;\n        }\n      }\n      return groupRowSpan === 1 ? null : groupRowSpan;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    updateFrozenRowStickyPosition() {\n      this.el.nativeElement.style.top = DomHandler.getOuterHeight(this.el.nativeElement.previousElementSibling) + 'px';\n    }\n    updateFrozenRowGroupHeaderStickyPosition() {\n      if (this.el.nativeElement.previousElementSibling) {\n        let tableHeaderHeight = DomHandler.getOuterHeight(this.el.nativeElement.previousElementSibling);\n        this.dt.rowGroupHeaderStyleObject.top = tableHeaderHeight + 'px';\n      }\n    }\n    getScrollerOption(option, options) {\n      if (this.dt.virtualScroll) {\n        options = options || this.scrollerOptions;\n        return options ? options[option] : null;\n      }\n      return null;\n    }\n    getRowIndex(rowIndex) {\n      const index = this.dt.paginator ? this.dt.first + rowIndex : rowIndex;\n      const getItemOptions = this.getScrollerOption('getItemOptions');\n      return getItemOptions ? getItemOptions(index).index : index;\n    }\n    static ɵfac = function TableBody_Factory(t) {\n      return new (t || TableBody)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TableBody,\n      selectors: [[\"\", \"pTableBody\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"pTableBody\", \"columns\"],\n        template: [i0.ɵɵInputFlags.None, \"pTableBodyTemplate\", \"template\"],\n        value: \"value\",\n        frozen: \"frozen\",\n        frozenRows: \"frozenRows\",\n        scrollerOptions: \"scrollerOptions\"\n      },\n      attrs: _c16,\n      decls: 5,\n      vars: 5,\n      consts: [[4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"row\", 4, \"ngIf\"], [\"role\", \"row\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TableBody_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TableBody_ng_container_0_Template, 2, 2, \"ng-container\", 0)(1, TableBody_ng_container_1_Template, 2, 2, \"ng-container\", 0)(2, TableBody_ng_container_2_Template, 2, 2, \"ng-container\", 0)(3, TableBody_ng_container_3_Template, 2, 5, \"ng-container\", 0)(4, TableBody_ng_container_4_Template, 2, 5, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.dt.expandedRowTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.expandedRowTemplate && !(ctx.frozen && ctx.dt.frozenExpandedRowTemplate));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.frozenExpandedRowTemplate && ctx.frozen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.isEmpty() && !ctx.dt.loading);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n  return TableBody;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RowGroupHeader = /*#__PURE__*/(() => {\n  class RowGroupHeader {\n    dt;\n    constructor(dt) {\n      this.dt = dt;\n    }\n    get getFrozenRowGroupHeaderStickyPosition() {\n      return this.dt.rowGroupHeaderStyleObject ? this.dt.rowGroupHeaderStyleObject.top : '';\n    }\n    static ɵfac = function RowGroupHeader_Factory(t) {\n      return new (t || RowGroupHeader)(i0.ɵɵdirectiveInject(Table));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RowGroupHeader,\n      selectors: [[\"\", \"pRowGroupHeader\", \"\"]],\n      hostAttrs: [1, \"p-rowgroup-header\", \"p-element\"],\n      hostVars: 2,\n      hostBindings: function RowGroupHeader_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"top\", ctx.getFrozenRowGroupHeaderStickyPosition);\n        }\n      }\n    });\n  }\n  return RowGroupHeader;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet FrozenColumn = /*#__PURE__*/(() => {\n  class FrozenColumn {\n    el;\n    get frozen() {\n      return this._frozen;\n    }\n    set frozen(val) {\n      this._frozen = val;\n      this.updateStickyPosition();\n    }\n    alignFrozen = 'left';\n    constructor(el) {\n      this.el = el;\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        this.updateStickyPosition();\n      }, 1000);\n    }\n    _frozen = true;\n    updateStickyPosition() {\n      if (this._frozen) {\n        if (this.alignFrozen === 'right') {\n          let right = 0;\n          let next = this.el.nativeElement.nextElementSibling;\n          if (next) {\n            right = DomHandler.getOuterWidth(next) + (parseFloat(next.style.right) || 0);\n          }\n          this.el.nativeElement.style.right = right + 'px';\n        } else {\n          let left = 0;\n          let prev = this.el.nativeElement.previousElementSibling;\n          if (prev) {\n            left = DomHandler.getOuterWidth(prev) + (parseFloat(prev.style.left) || 0);\n          }\n          this.el.nativeElement.style.left = left + 'px';\n        }\n        const filterRow = this.el.nativeElement?.parentElement?.nextElementSibling;\n        if (filterRow) {\n          let index = DomHandler.index(this.el.nativeElement);\n          if (filterRow.children && filterRow.children[index]) {\n            filterRow.children[index].style.left = this.el.nativeElement.style.left;\n            filterRow.children[index].style.right = this.el.nativeElement.style.right;\n          }\n        }\n      }\n    }\n    static ɵfac = function FrozenColumn_Factory(t) {\n      return new (t || FrozenColumn)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FrozenColumn,\n      selectors: [[\"\", \"pFrozenColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 2,\n      hostBindings: function FrozenColumn_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-frozen-column\", ctx.frozen);\n        }\n      },\n      inputs: {\n        frozen: \"frozen\",\n        alignFrozen: \"alignFrozen\"\n      }\n    });\n  }\n  return FrozenColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SortableColumn = /*#__PURE__*/(() => {\n  class SortableColumn {\n    dt;\n    field;\n    pSortableColumnDisabled;\n    sorted;\n    sortOrder;\n    subscription;\n    constructor(dt) {\n      this.dt = dt;\n      if (this.isEnabled()) {\n        this.subscription = this.dt.tableService.sortSource$.subscribe(sortMeta => {\n          this.updateSortState();\n        });\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.updateSortState();\n      }\n    }\n    updateSortState() {\n      this.sorted = this.dt.isSorted(this.field);\n      this.sortOrder = this.sorted ? this.dt.sortOrder === 1 ? 'ascending' : 'descending' : 'none';\n    }\n    onClick(event) {\n      if (this.isEnabled() && !this.isFilterElement(event.target)) {\n        this.updateSortState();\n        this.dt.sort({\n          originalEvent: event,\n          field: this.field\n        });\n        DomHandler.clearSelection();\n      }\n    }\n    onEnterKey(event) {\n      this.onClick(event);\n      event.preventDefault();\n    }\n    isEnabled() {\n      return this.pSortableColumnDisabled !== true;\n    }\n    isFilterElement(element) {\n      return this.isFilterElementIconOrButton(element) || this.isFilterElementIconOrButton(element?.parentElement?.parentElement);\n    }\n    isFilterElementIconOrButton(element) {\n      return DomHandler.hasClass(element, 'pi-filter-icon') || DomHandler.hasClass(element, 'p-column-filter-menu-button');\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function SortableColumn_Factory(t) {\n      return new (t || SortableColumn)(i0.ɵɵdirectiveInject(Table));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SortableColumn,\n      selectors: [[\"\", \"pSortableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 7,\n      hostBindings: function SortableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SortableColumn_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown.space\", function SortableColumn_keydown_space_HostBindingHandler($event) {\n            return ctx.onEnterKey($event);\n          })(\"keydown.enter\", function SortableColumn_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKey($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? \"0\" : null)(\"role\", \"columnheader\")(\"aria-sort\", ctx.sortOrder);\n          i0.ɵɵclassProp(\"p-sortable-column\", ctx.isEnabled())(\"p-highlight\", ctx.sorted);\n        }\n      },\n      inputs: {\n        field: [i0.ɵɵInputFlags.None, \"pSortableColumn\", \"field\"],\n        pSortableColumnDisabled: \"pSortableColumnDisabled\"\n      }\n    });\n  }\n  return SortableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SortIcon = /*#__PURE__*/(() => {\n  class SortIcon {\n    dt;\n    cd;\n    field;\n    subscription;\n    sortOrder;\n    constructor(dt, cd) {\n      this.dt = dt;\n      this.cd = cd;\n      this.subscription = this.dt.tableService.sortSource$.subscribe(sortMeta => {\n        this.updateSortState();\n      });\n    }\n    ngOnInit() {\n      this.updateSortState();\n    }\n    onClick(event) {\n      event.preventDefault();\n    }\n    updateSortState() {\n      if (this.dt.sortMode === 'single') {\n        this.sortOrder = this.dt.isSorted(this.field) ? this.dt.sortOrder : 0;\n      } else if (this.dt.sortMode === 'multiple') {\n        let sortMeta = this.dt.getSortMeta(this.field);\n        this.sortOrder = sortMeta ? sortMeta.order : 0;\n      }\n      this.cd.markForCheck();\n    }\n    getMultiSortMetaIndex() {\n      let multiSortMeta = this.dt._multiSortMeta;\n      let index = -1;\n      if (multiSortMeta && this.dt.sortMode === 'multiple' && this.dt.showInitialSortBadge && multiSortMeta.length > 1) {\n        for (let i = 0; i < multiSortMeta.length; i++) {\n          let meta = multiSortMeta[i];\n          if (meta.field === this.field || meta.field === this.field) {\n            index = i;\n            break;\n          }\n        }\n      }\n      return index;\n    }\n    getBadgeValue() {\n      let index = this.getMultiSortMetaIndex();\n      return this.dt.groupRowsBy && index > -1 ? index : index + 1;\n    }\n    isMultiSorted() {\n      return this.dt.sortMode === 'multiple' && this.getMultiSortMetaIndex() > -1;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function SortIcon_Factory(t) {\n      return new (t || SortIcon)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SortIcon,\n      selectors: [[\"p-sortIcon\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        field: \"field\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[4, \"ngIf\"], [\"class\", \"p-sortable-column-icon\", 4, \"ngIf\"], [\"class\", \"p-sortable-column-badge\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-sortable-column-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-sortable-column-badge\"]],\n      template: function SortIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SortIcon_ng_container_0_Template, 4, 3, \"ng-container\", 0)(1, SortIcon_span_1_Template, 2, 4, \"span\", 1)(2, SortIcon_span_2_Template, 2, 1, \"span\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.dt.sortIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.sortIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMultiSorted());\n        }\n      },\n      dependencies: () => [i2.NgIf, i2.NgTemplateOutlet, SortAltIcon, SortAmountUpAltIcon, SortAmountDownIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return SortIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SelectableRow = /*#__PURE__*/(() => {\n  class SelectableRow {\n    dt;\n    tableService;\n    el;\n    data;\n    index;\n    pSelectableRowDisabled;\n    selected;\n    subscription;\n    constructor(dt, tableService, el) {\n      this.dt = dt;\n      this.tableService = tableService;\n      this.el = el;\n      if (this.isEnabled()) {\n        this.subscription = this.dt.tableService.selectionSource$.subscribe(() => {\n          this.selected = this.dt.isSelected(this.data);\n        });\n      }\n    }\n    setRowTabIndex() {\n      if (this.dt.selectionMode === 'single' || this.dt.selectionMode === 'multiple') {\n        return !this.dt.selection ? 0 : this.dt.anchorRowIndex === this.index ? 0 : -1;\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.selected = this.dt.isSelected(this.data);\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.dt.handleRowClick({\n          originalEvent: event,\n          rowData: this.data,\n          rowIndex: this.index\n        });\n      }\n    }\n    onTouchEnd(event) {\n      if (this.isEnabled()) {\n        this.dt.handleRowTouchEnd(event);\n      }\n    }\n    onKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'Space':\n          this.onSpaceKey(event);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        default:\n          if (event.code === 'KeyA' && (event.metaKey || event.ctrlKey)) {\n            const data = this.dt.dataToRender(this.dt.rows);\n            this.dt.selection = [...data];\n            this.dt.selectRange(event, data.length - 1);\n            event.preventDefault();\n          }\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      if (!this.isEnabled()) {\n        return;\n      }\n      const row = event.currentTarget;\n      const nextRow = this.findNextSelectableRow(row);\n      if (nextRow) {\n        nextRow.focus();\n      }\n      event.preventDefault();\n    }\n    onArrowUpKey(event) {\n      if (!this.isEnabled()) {\n        return;\n      }\n      const row = event.currentTarget;\n      const prevRow = this.findPrevSelectableRow(row);\n      if (prevRow) {\n        prevRow.focus();\n      }\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      if (!this.isEnabled()) {\n        return;\n      }\n      this.dt.handleRowClick({\n        originalEvent: event,\n        rowData: this.data,\n        rowIndex: this.index\n      });\n    }\n    onEndKey(event) {\n      const lastRow = this.findLastSelectableRow();\n      lastRow && this.focusRowChange(this.el.nativeElement, lastRow);\n      if (event.ctrlKey && event.shiftKey) {\n        const data = this.dt.dataToRender(this.dt.rows);\n        const lastSelectableRowIndex = DomHandler.getAttribute(lastRow, 'index');\n        this.dt.anchorRowIndex = lastSelectableRowIndex;\n        this.dt.selection = data.slice(this.index, data.length);\n        this.dt.selectRange(event, this.index);\n      }\n      event.preventDefault();\n    }\n    onHomeKey(event) {\n      const firstRow = this.findFirstSelectableRow();\n      firstRow && this.focusRowChange(this.el.nativeElement, firstRow);\n      if (event.ctrlKey && event.shiftKey) {\n        const data = this.dt.dataToRender(this.dt.rows);\n        const firstSelectableRowIndex = DomHandler.getAttribute(firstRow, 'index');\n        this.dt.anchorRowIndex = this.dt.anchorRowIndex || firstSelectableRowIndex;\n        this.dt.selection = data.slice(0, this.index + 1);\n        this.dt.selectRange(event, this.index);\n      }\n      event.preventDefault();\n    }\n    onSpaceKey(event) {\n      this.onEnterKey(event);\n      if (event.shiftKey && this.dt.selection !== null) {\n        const data = this.dt.dataToRender(this.dt.rows);\n        let index;\n        if (ObjectUtils.isNotEmpty(this.dt.selection) && this.dt.selection.length > 0) {\n          let firstSelectedRowIndex, lastSelectedRowIndex;\n          firstSelectedRowIndex = ObjectUtils.findIndexInList(this.dt.selection[0], data);\n          lastSelectedRowIndex = ObjectUtils.findIndexInList(this.dt.selection[this.dt.selection.length - 1], data);\n          index = this.index <= firstSelectedRowIndex ? lastSelectedRowIndex : firstSelectedRowIndex;\n        } else {\n          index = ObjectUtils.findIndexInList(this.dt.selection, data);\n        }\n        this.dt.anchorRowIndex = index;\n        this.dt.selection = index !== this.index ? data.slice(Math.min(index, this.index), Math.max(index, this.index) + 1) : [this.data];\n        this.dt.selectRange(event, this.index);\n      }\n      event.preventDefault();\n    }\n    focusRowChange(firstFocusableRow, currentFocusedRow) {\n      firstFocusableRow.tabIndex = '-1';\n      currentFocusedRow.tabIndex = '0';\n      DomHandler.focus(currentFocusedRow);\n    }\n    findLastSelectableRow() {\n      const rows = DomHandler.find(this.dt.el.nativeElement, '.p-selectable-row');\n      return rows ? rows[rows.length - 1] : null;\n    }\n    findFirstSelectableRow() {\n      const firstRow = DomHandler.findSingle(this.dt.el.nativeElement, '.p-selectable-row');\n      return firstRow;\n    }\n    findNextSelectableRow(row) {\n      let nextRow = row.nextElementSibling;\n      if (nextRow) {\n        if (DomHandler.hasClass(nextRow, 'p-selectable-row')) return nextRow;else return this.findNextSelectableRow(nextRow);\n      } else {\n        return null;\n      }\n    }\n    findPrevSelectableRow(row) {\n      let prevRow = row.previousElementSibling;\n      if (prevRow) {\n        if (DomHandler.hasClass(prevRow, 'p-selectable-row')) return prevRow;else return this.findPrevSelectableRow(prevRow);\n      } else {\n        return null;\n      }\n    }\n    isEnabled() {\n      return this.pSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function SelectableRow_Factory(t) {\n      return new (t || SelectableRow)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SelectableRow,\n      selectors: [[\"\", \"pSelectableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 7,\n      hostBindings: function SelectableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SelectableRow_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"touchend\", function SelectableRow_touchend_HostBindingHandler($event) {\n            return ctx.onTouchEnd($event);\n          })(\"keydown\", function SelectableRow_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx.setRowTabIndex())(\"data-p-highlight\", ctx.selected)(\"data-p-selectable-row\", true);\n          i0.ɵɵclassProp(\"p-selectable-row\", ctx.isEnabled())(\"p-highlight\", ctx.selected);\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pSelectableRow\", \"data\"],\n        index: [i0.ɵɵInputFlags.None, \"pSelectableRowIndex\", \"index\"],\n        pSelectableRowDisabled: \"pSelectableRowDisabled\"\n      }\n    });\n  }\n  return SelectableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SelectableRowDblClick = /*#__PURE__*/(() => {\n  class SelectableRowDblClick {\n    dt;\n    tableService;\n    data;\n    index;\n    pSelectableRowDisabled;\n    selected;\n    subscription;\n    constructor(dt, tableService) {\n      this.dt = dt;\n      this.tableService = tableService;\n      if (this.isEnabled()) {\n        this.subscription = this.dt.tableService.selectionSource$.subscribe(() => {\n          this.selected = this.dt.isSelected(this.data);\n        });\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.selected = this.dt.isSelected(this.data);\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.dt.handleRowClick({\n          originalEvent: event,\n          rowData: this.data,\n          rowIndex: this.index\n        });\n      }\n    }\n    isEnabled() {\n      return this.pSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function SelectableRowDblClick_Factory(t) {\n      return new (t || SelectableRowDblClick)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SelectableRowDblClick,\n      selectors: [[\"\", \"pSelectableRowDblClick\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 4,\n      hostBindings: function SelectableRowDblClick_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"dblclick\", function SelectableRowDblClick_dblclick_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-selectable-row\", ctx.isEnabled())(\"p-highlight\", ctx.selected);\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pSelectableRowDblClick\", \"data\"],\n        index: [i0.ɵɵInputFlags.None, \"pSelectableRowIndex\", \"index\"],\n        pSelectableRowDisabled: \"pSelectableRowDisabled\"\n      }\n    });\n  }\n  return SelectableRowDblClick;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ContextMenuRow = /*#__PURE__*/(() => {\n  class ContextMenuRow {\n    dt;\n    tableService;\n    el;\n    data;\n    index;\n    pContextMenuRowDisabled;\n    selected;\n    subscription;\n    constructor(dt, tableService, el) {\n      this.dt = dt;\n      this.tableService = tableService;\n      this.el = el;\n      if (this.isEnabled()) {\n        this.subscription = this.dt.tableService.contextMenuSource$.subscribe(data => {\n          this.selected = this.dt.equals(this.data, data);\n        });\n      }\n    }\n    onContextMenu(event) {\n      if (this.isEnabled()) {\n        this.dt.handleRowRightClick({\n          originalEvent: event,\n          rowData: this.data,\n          rowIndex: this.index\n        });\n        this.el.nativeElement.focus();\n        event.preventDefault();\n      }\n    }\n    isEnabled() {\n      return this.pContextMenuRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function ContextMenuRow_Factory(t) {\n      return new (t || ContextMenuRow)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ContextMenuRow,\n      selectors: [[\"\", \"pContextMenuRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 3,\n      hostBindings: function ContextMenuRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"contextmenu\", function ContextMenuRow_contextmenu_HostBindingHandler($event) {\n            return ctx.onContextMenu($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? 0 : undefined);\n          i0.ɵɵclassProp(\"p-highlight-contextmenu\", ctx.selected);\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pContextMenuRow\", \"data\"],\n        index: [i0.ɵɵInputFlags.None, \"pContextMenuRowIndex\", \"index\"],\n        pContextMenuRowDisabled: \"pContextMenuRowDisabled\"\n      }\n    });\n  }\n  return ContextMenuRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RowToggler = /*#__PURE__*/(() => {\n  class RowToggler {\n    dt;\n    data;\n    pRowTogglerDisabled;\n    constructor(dt) {\n      this.dt = dt;\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.dt.toggleRow(this.data, event);\n        event.preventDefault();\n      }\n    }\n    isEnabled() {\n      return this.pRowTogglerDisabled !== true;\n    }\n    static ɵfac = function RowToggler_Factory(t) {\n      return new (t || RowToggler)(i0.ɵɵdirectiveInject(Table));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RowToggler,\n      selectors: [[\"\", \"pRowToggler\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function RowToggler_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function RowToggler_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pRowToggler\", \"data\"],\n        pRowTogglerDisabled: \"pRowTogglerDisabled\"\n      }\n    });\n  }\n  return RowToggler;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ResizableColumn = /*#__PURE__*/(() => {\n  class ResizableColumn {\n    document;\n    platformId;\n    renderer;\n    dt;\n    el;\n    zone;\n    pResizableColumnDisabled;\n    resizer;\n    resizerMouseDownListener;\n    documentMouseMoveListener;\n    documentMouseUpListener;\n    constructor(document, platformId, renderer, dt, el, zone) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.dt = dt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.isEnabled()) {\n          DomHandler.addClass(this.el.nativeElement, 'p-resizable-column');\n          this.resizer = this.renderer.createElement('span');\n          this.renderer.addClass(this.resizer, 'p-column-resizer');\n          this.renderer.appendChild(this.el.nativeElement, this.resizer);\n          this.zone.runOutsideAngular(() => {\n            this.resizerMouseDownListener = this.renderer.listen(this.resizer, 'mousedown', this.onMouseDown.bind(this));\n          });\n        }\n      }\n    }\n    bindDocumentEvents() {\n      this.zone.runOutsideAngular(() => {\n        this.documentMouseMoveListener = this.renderer.listen(this.document, 'mousemove', this.onDocumentMouseMove.bind(this));\n        this.documentMouseUpListener = this.renderer.listen(this.document, 'mouseup', this.onDocumentMouseUp.bind(this));\n      });\n    }\n    unbindDocumentEvents() {\n      if (this.documentMouseMoveListener) {\n        this.documentMouseMoveListener();\n        this.documentMouseMoveListener = null;\n      }\n      if (this.documentMouseUpListener) {\n        this.documentMouseUpListener();\n        this.documentMouseUpListener = null;\n      }\n    }\n    onMouseDown(event) {\n      if (event.which === 1) {\n        this.dt.onColumnResizeBegin(event);\n        this.bindDocumentEvents();\n      }\n    }\n    onDocumentMouseMove(event) {\n      this.dt.onColumnResize(event);\n    }\n    onDocumentMouseUp(event) {\n      this.dt.onColumnResizeEnd();\n      this.unbindDocumentEvents();\n    }\n    isEnabled() {\n      return this.pResizableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.resizerMouseDownListener) {\n        this.resizerMouseDownListener();\n        this.resizerMouseDownListener = null;\n      }\n      this.unbindDocumentEvents();\n    }\n    static ɵfac = function ResizableColumn_Factory(t) {\n      return new (t || ResizableColumn)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ResizableColumn,\n      selectors: [[\"\", \"pResizableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        pResizableColumnDisabled: \"pResizableColumnDisabled\"\n      }\n    });\n  }\n  return ResizableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ReorderableColumn = /*#__PURE__*/(() => {\n  class ReorderableColumn {\n    platformId;\n    renderer;\n    dt;\n    el;\n    zone;\n    pReorderableColumnDisabled;\n    dragStartListener;\n    dragOverListener;\n    dragEnterListener;\n    dragLeaveListener;\n    mouseDownListener;\n    constructor(platformId, renderer, dt, el, zone) {\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.dt = dt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (this.isEnabled()) {\n        this.bindEvents();\n      }\n    }\n    bindEvents() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.zone.runOutsideAngular(() => {\n          this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n          this.dragStartListener = this.renderer.listen(this.el.nativeElement, 'dragstart', this.onDragStart.bind(this));\n          this.dragOverListener = this.renderer.listen(this.el.nativeElement, 'dragover', this.onDragOver.bind(this));\n          this.dragEnterListener = this.renderer.listen(this.el.nativeElement, 'dragenter', this.onDragEnter.bind(this));\n          this.dragLeaveListener = this.renderer.listen(this.el.nativeElement, 'dragleave', this.onDragLeave.bind(this));\n        });\n      }\n    }\n    unbindEvents() {\n      if (this.mouseDownListener) {\n        this.mouseDownListener();\n        this.mouseDownListener = null;\n      }\n      if (this.dragStartListener) {\n        this.dragStartListener();\n        this.dragStartListener = null;\n      }\n      if (this.dragOverListener) {\n        this.dragOverListener();\n        this.dragOverListener = null;\n      }\n      if (this.dragEnterListener) {\n        this.dragEnterListener();\n        this.dragEnterListener = null;\n      }\n      if (this.dragLeaveListener) {\n        this.dragLeaveListener();\n        this.dragLeaveListener = null;\n      }\n    }\n    onMouseDown(event) {\n      if (event.target.nodeName === 'INPUT' || event.target.nodeName === 'TEXTAREA' || DomHandler.hasClass(event.target, 'p-column-resizer')) this.el.nativeElement.draggable = false;else this.el.nativeElement.draggable = true;\n    }\n    onDragStart(event) {\n      this.dt.onColumnDragStart(event, this.el.nativeElement);\n    }\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    onDragEnter(event) {\n      this.dt.onColumnDragEnter(event, this.el.nativeElement);\n    }\n    onDragLeave(event) {\n      this.dt.onColumnDragLeave(event);\n    }\n    onDrop(event) {\n      if (this.isEnabled()) {\n        this.dt.onColumnDrop(event, this.el.nativeElement);\n      }\n    }\n    isEnabled() {\n      return this.pReorderableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      this.unbindEvents();\n    }\n    static ɵfac = function ReorderableColumn_Factory(t) {\n      return new (t || ReorderableColumn)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ReorderableColumn,\n      selectors: [[\"\", \"pReorderableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function ReorderableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function ReorderableColumn_drop_HostBindingHandler($event) {\n            return ctx.onDrop($event);\n          });\n        }\n      },\n      inputs: {\n        pReorderableColumnDisabled: \"pReorderableColumnDisabled\"\n      }\n    });\n  }\n  return ReorderableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet EditableColumn = /*#__PURE__*/(() => {\n  class EditableColumn {\n    dt;\n    el;\n    zone;\n    data;\n    field;\n    rowIndex;\n    pEditableColumnDisabled;\n    pFocusCellSelector;\n    overlayEventListener;\n    constructor(dt, el, zone) {\n      this.dt = dt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngOnChanges({\n      data\n    }) {\n      if (this.el.nativeElement && !data.firstChange) {\n        this.dt.updateEditingCell(this.el.nativeElement, this.data, this.field, this.rowIndex);\n      }\n    }\n    ngAfterViewInit() {\n      if (this.isEnabled()) {\n        DomHandler.addClass(this.el.nativeElement, 'p-editable-column');\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.dt.selfClick = true;\n        if (this.dt.editingCell) {\n          if (this.dt.editingCell !== this.el.nativeElement) {\n            if (!this.dt.isEditingCellValid()) {\n              return;\n            }\n            this.closeEditingCell(true, event);\n            this.openCell();\n          }\n        } else {\n          this.openCell();\n        }\n      }\n    }\n    openCell() {\n      this.dt.updateEditingCell(this.el.nativeElement, this.data, this.field, this.rowIndex);\n      DomHandler.addClass(this.el.nativeElement, 'p-cell-editing');\n      this.dt.onEditInit.emit({\n        field: this.field,\n        data: this.data,\n        index: this.rowIndex\n      });\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => {\n          let focusCellSelector = this.pFocusCellSelector || 'input, textarea, select';\n          let focusableElement = DomHandler.findSingle(this.el.nativeElement, focusCellSelector);\n          if (focusableElement) {\n            focusableElement.focus();\n          }\n        }, 50);\n      });\n      this.overlayEventListener = e => {\n        if (this.el && this.el.nativeElement.contains(e.target)) {\n          this.dt.selfClick = true;\n        }\n      };\n      this.dt.overlaySubscription = this.dt.overlayService.clickObservable.subscribe(this.overlayEventListener);\n    }\n    closeEditingCell(completed, event) {\n      const eventData = {\n        field: this.dt.editingCellField,\n        data: this.dt.editingCellData,\n        originalEvent: event,\n        index: this.dt.editingCellRowIndex\n      };\n      if (completed) {\n        this.dt.onEditComplete.emit(eventData);\n      } else {\n        this.dt.onEditCancel.emit(eventData);\n        this.dt.value.forEach(element => {\n          if (element[this.dt.editingCellField] === this.data) {\n            element[this.dt.editingCellField] = this.dt.editingCellData;\n          }\n        });\n      }\n      DomHandler.removeClass(this.dt.editingCell, 'p-cell-editing');\n      this.dt.editingCell = null;\n      this.dt.editingCellData = null;\n      this.dt.editingCellField = null;\n      this.dt.unbindDocumentEditListener();\n      if (this.dt.overlaySubscription) {\n        this.dt.overlaySubscription.unsubscribe();\n      }\n    }\n    onEnterKeyDown(event) {\n      if (this.isEnabled() && !event.shiftKey) {\n        if (this.dt.isEditingCellValid()) {\n          this.closeEditingCell(true, event);\n        }\n        event.preventDefault();\n      }\n    }\n    onTabKeyDown(event) {\n      if (this.isEnabled()) {\n        if (this.dt.isEditingCellValid()) {\n          this.closeEditingCell(true, event);\n        }\n        event.preventDefault();\n      }\n    }\n    onEscapeKeyDown(event) {\n      if (this.isEnabled()) {\n        if (this.dt.isEditingCellValid()) {\n          this.closeEditingCell(false, event);\n        }\n        event.preventDefault();\n      }\n    }\n    onShiftKeyDown(event) {\n      if (this.isEnabled()) {\n        if (event.shiftKey) this.moveToPreviousCell(event);else {\n          this.moveToNextCell(event);\n        }\n      }\n    }\n    onArrowDown(event) {\n      if (this.isEnabled()) {\n        let currentCell = this.findCell(event.target);\n        if (currentCell) {\n          let cellIndex = DomHandler.index(currentCell);\n          let targetCell = this.findNextEditableColumnByIndex(currentCell, cellIndex);\n          if (targetCell) {\n            if (this.dt.isEditingCellValid()) {\n              this.closeEditingCell(true, event);\n            }\n            DomHandler.invokeElementMethod(event.target, 'blur');\n            DomHandler.invokeElementMethod(targetCell, 'click');\n          }\n          event.preventDefault();\n        }\n      }\n    }\n    onArrowUp(event) {\n      if (this.isEnabled()) {\n        let currentCell = this.findCell(event.target);\n        if (currentCell) {\n          let cellIndex = DomHandler.index(currentCell);\n          let targetCell = this.findPrevEditableColumnByIndex(currentCell, cellIndex);\n          if (targetCell) {\n            if (this.dt.isEditingCellValid()) {\n              this.closeEditingCell(true, event);\n            }\n            DomHandler.invokeElementMethod(event.target, 'blur');\n            DomHandler.invokeElementMethod(targetCell, 'click');\n          }\n          event.preventDefault();\n        }\n      }\n    }\n    onArrowLeft(event) {\n      if (this.isEnabled()) {\n        this.moveToPreviousCell(event);\n      }\n    }\n    onArrowRight(event) {\n      if (this.isEnabled()) {\n        this.moveToNextCell(event);\n      }\n    }\n    findCell(element) {\n      if (element) {\n        let cell = element;\n        while (cell && !DomHandler.hasClass(cell, 'p-cell-editing')) {\n          cell = cell.parentElement;\n        }\n        return cell;\n      } else {\n        return null;\n      }\n    }\n    moveToPreviousCell(event) {\n      let currentCell = this.findCell(event.target);\n      if (currentCell) {\n        let targetCell = this.findPreviousEditableColumn(currentCell);\n        if (targetCell) {\n          if (this.dt.isEditingCellValid()) {\n            this.closeEditingCell(true, event);\n          }\n          DomHandler.invokeElementMethod(event.target, 'blur');\n          DomHandler.invokeElementMethod(targetCell, 'click');\n          event.preventDefault();\n        }\n      }\n    }\n    moveToNextCell(event) {\n      let currentCell = this.findCell(event.target);\n      if (currentCell) {\n        let targetCell = this.findNextEditableColumn(currentCell);\n        if (targetCell) {\n          if (this.dt.isEditingCellValid()) {\n            this.closeEditingCell(true, event);\n          }\n          DomHandler.invokeElementMethod(event.target, 'blur');\n          DomHandler.invokeElementMethod(targetCell, 'click');\n          event.preventDefault();\n        } else {\n          if (this.dt.isEditingCellValid()) {\n            this.closeEditingCell(true, event);\n          }\n        }\n      }\n    }\n    findPreviousEditableColumn(cell) {\n      let prevCell = cell.previousElementSibling;\n      if (!prevCell) {\n        let previousRow = cell.parentElement?.previousElementSibling;\n        if (previousRow) {\n          prevCell = previousRow.lastElementChild;\n        }\n      }\n      if (prevCell) {\n        if (DomHandler.hasClass(prevCell, 'p-editable-column')) return prevCell;else return this.findPreviousEditableColumn(prevCell);\n      } else {\n        return null;\n      }\n    }\n    findNextEditableColumn(cell) {\n      let nextCell = cell.nextElementSibling;\n      if (!nextCell) {\n        let nextRow = cell.parentElement?.nextElementSibling;\n        if (nextRow) {\n          nextCell = nextRow.firstElementChild;\n        }\n      }\n      if (nextCell) {\n        if (DomHandler.hasClass(nextCell, 'p-editable-column')) return nextCell;else return this.findNextEditableColumn(nextCell);\n      } else {\n        return null;\n      }\n    }\n    findNextEditableColumnByIndex(cell, index) {\n      let nextRow = cell.parentElement?.nextElementSibling;\n      if (nextRow) {\n        let nextCell = nextRow.children[index];\n        if (nextCell && DomHandler.hasClass(nextCell, 'p-editable-column')) {\n          return nextCell;\n        }\n        return null;\n      } else {\n        return null;\n      }\n    }\n    findPrevEditableColumnByIndex(cell, index) {\n      let prevRow = cell.parentElement?.previousElementSibling;\n      if (prevRow) {\n        let prevCell = prevRow.children[index];\n        if (prevCell && DomHandler.hasClass(prevCell, 'p-editable-column')) {\n          return prevCell;\n        }\n        return null;\n      } else {\n        return null;\n      }\n    }\n    isEnabled() {\n      return this.pEditableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.dt.overlaySubscription) {\n        this.dt.overlaySubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function EditableColumn_Factory(t) {\n      return new (t || EditableColumn)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: EditableColumn,\n      selectors: [[\"\", \"pEditableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function EditableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function EditableColumn_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown.enter\", function EditableColumn_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKeyDown($event);\n          })(\"keydown.tab\", function EditableColumn_keydown_tab_HostBindingHandler($event) {\n            return ctx.onShiftKeyDown($event);\n          })(\"keydown.escape\", function EditableColumn_keydown_escape_HostBindingHandler($event) {\n            return ctx.onEscapeKeyDown($event);\n          })(\"keydown.shift.tab\", function EditableColumn_keydown_shift_tab_HostBindingHandler($event) {\n            return ctx.onShiftKeyDown($event);\n          })(\"keydown.meta.tab\", function EditableColumn_keydown_meta_tab_HostBindingHandler($event) {\n            return ctx.onShiftKeyDown($event);\n          })(\"keydown.arrowdown\", function EditableColumn_keydown_arrowdown_HostBindingHandler($event) {\n            return ctx.onArrowDown($event);\n          })(\"keydown.arrowup\", function EditableColumn_keydown_arrowup_HostBindingHandler($event) {\n            return ctx.onArrowUp($event);\n          })(\"keydown.arrowleft\", function EditableColumn_keydown_arrowleft_HostBindingHandler($event) {\n            return ctx.onArrowLeft($event);\n          })(\"keydown.arrowright\", function EditableColumn_keydown_arrowright_HostBindingHandler($event) {\n            return ctx.onArrowRight($event);\n          });\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pEditableColumn\", \"data\"],\n        field: [i0.ɵɵInputFlags.None, \"pEditableColumnField\", \"field\"],\n        rowIndex: [i0.ɵɵInputFlags.None, \"pEditableColumnRowIndex\", \"rowIndex\"],\n        pEditableColumnDisabled: \"pEditableColumnDisabled\",\n        pFocusCellSelector: \"pFocusCellSelector\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return EditableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet EditableRow = /*#__PURE__*/(() => {\n  class EditableRow {\n    el;\n    data;\n    pEditableRowDisabled;\n    constructor(el) {\n      this.el = el;\n    }\n    isEnabled() {\n      return this.pEditableRowDisabled !== true;\n    }\n    static ɵfac = function EditableRow_Factory(t) {\n      return new (t || EditableRow)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: EditableRow,\n      selectors: [[\"\", \"pEditableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"pEditableRow\", \"data\"],\n        pEditableRowDisabled: \"pEditableRowDisabled\"\n      }\n    });\n  }\n  return EditableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InitEditableRow = /*#__PURE__*/(() => {\n  class InitEditableRow {\n    dt;\n    editableRow;\n    constructor(dt, editableRow) {\n      this.dt = dt;\n      this.editableRow = editableRow;\n    }\n    onClick(event) {\n      this.dt.initRowEdit(this.editableRow.data);\n      event.preventDefault();\n    }\n    static ɵfac = function InitEditableRow_Factory(t) {\n      return new (t || InitEditableRow)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(EditableRow));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InitEditableRow,\n      selectors: [[\"\", \"pInitEditableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function InitEditableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function InitEditableRow_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      }\n    });\n  }\n  return InitEditableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SaveEditableRow = /*#__PURE__*/(() => {\n  class SaveEditableRow {\n    dt;\n    editableRow;\n    constructor(dt, editableRow) {\n      this.dt = dt;\n      this.editableRow = editableRow;\n    }\n    onClick(event) {\n      this.dt.saveRowEdit(this.editableRow.data, this.editableRow.el.nativeElement);\n      event.preventDefault();\n    }\n    static ɵfac = function SaveEditableRow_Factory(t) {\n      return new (t || SaveEditableRow)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(EditableRow));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SaveEditableRow,\n      selectors: [[\"\", \"pSaveEditableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function SaveEditableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SaveEditableRow_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      }\n    });\n  }\n  return SaveEditableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CancelEditableRow = /*#__PURE__*/(() => {\n  class CancelEditableRow {\n    dt;\n    editableRow;\n    constructor(dt, editableRow) {\n      this.dt = dt;\n      this.editableRow = editableRow;\n    }\n    onClick(event) {\n      this.dt.cancelRowEdit(this.editableRow.data);\n      event.preventDefault();\n    }\n    static ɵfac = function CancelEditableRow_Factory(t) {\n      return new (t || CancelEditableRow)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(EditableRow));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CancelEditableRow,\n      selectors: [[\"\", \"pCancelEditableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function CancelEditableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CancelEditableRow_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      }\n    });\n  }\n  return CancelEditableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CellEditor = /*#__PURE__*/(() => {\n  class CellEditor {\n    dt;\n    editableColumn;\n    editableRow;\n    templates;\n    inputTemplate;\n    outputTemplate;\n    constructor(dt, editableColumn, editableRow) {\n      this.dt = dt;\n      this.editableColumn = editableColumn;\n      this.editableRow = editableRow;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'input':\n            this.inputTemplate = item.template;\n            break;\n          case 'output':\n            this.outputTemplate = item.template;\n            break;\n        }\n      });\n    }\n    get editing() {\n      return this.dt.editingCell && this.editableColumn && this.dt.editingCell === this.editableColumn.el.nativeElement || this.editableRow && this.dt.editMode === 'row' && this.dt.isRowEditing(this.editableRow.data);\n    }\n    static ɵfac = function CellEditor_Factory(t) {\n      return new (t || CellEditor)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(EditableColumn, 8), i0.ɵɵdirectiveInject(EditableRow, 8));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CellEditor,\n      selectors: [[\"p-cellEditor\"]],\n      contentQueries: function CellEditor_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\"]],\n      template: function CellEditor_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CellEditor_ng_container_0_Template, 2, 1, \"ng-container\", 0)(1, CellEditor_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.editing);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.editing);\n        }\n      },\n      dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n  return CellEditor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TableRadioButton = /*#__PURE__*/(() => {\n  class TableRadioButton {\n    dt;\n    cd;\n    disabled;\n    value;\n    index;\n    inputId;\n    name;\n    ariaLabel;\n    inputViewChild;\n    checked;\n    focused;\n    subscription;\n    constructor(dt, cd) {\n      this.dt = dt;\n      this.cd = cd;\n      this.subscription = this.dt.tableService.selectionSource$.subscribe(() => {\n        this.checked = this.dt.isSelected(this.value);\n        this.ariaLabel = this.ariaLabel || this.dt.config.translation.aria ? this.checked ? this.dt.config.translation.aria.selectRow : this.dt.config.translation.aria.unselectRow : undefined;\n        this.cd.markForCheck();\n      });\n    }\n    ngOnInit() {\n      this.checked = this.dt.isSelected(this.value);\n    }\n    onClick(event) {\n      if (!this.disabled) {\n        this.dt.toggleRowWithRadio({\n          originalEvent: event,\n          rowIndex: this.index\n        }, this.value);\n        this.inputViewChild?.nativeElement?.focus();\n      }\n      DomHandler.clearSelection();\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TableRadioButton_Factory(t) {\n      return new (t || TableRadioButton)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TableRadioButton,\n      selectors: [[\"p-tableRadioButton\"]],\n      viewQuery: function TableRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c22, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: \"disabled\",\n        value: \"value\",\n        index: \"index\",\n        inputId: \"inputId\",\n        name: \"name\",\n        ariaLabel: \"ariaLabel\"\n      },\n      decls: 7,\n      vars: 16,\n      consts: [[\"rb\", \"\"], [\"box\", \"\"], [1, \"p-radiobutton\", \"p-component\", 3, \"click\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"radio\", 3, \"focus\", \"blur\", \"checked\", \"disabled\", \"tabindex\"], [3, \"ngClass\"], [1, \"p-radiobutton-icon\"]],\n      template: function TableRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function TableRadioButton_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClick($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"input\", 4, 0);\n          i0.ɵɵlistener(\"focus\", function TableRadioButton_Template_input_focus_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus());\n          })(\"blur\", function TableRadioButton_Template_input_blur_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 5, 1);\n          i0.ɵɵelement(6, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c23, ctx.focused, ctx.checked, ctx.disabled));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"tabindex\", ctx.disabled ? null : \"0\");\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c24, ctx.checked, ctx.focused, ctx.disabled));\n        }\n      },\n      dependencies: [i2.NgClass],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TableRadioButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TableCheckbox = /*#__PURE__*/(() => {\n  class TableCheckbox {\n    dt;\n    tableService;\n    cd;\n    disabled;\n    value;\n    index;\n    inputId;\n    name;\n    required;\n    ariaLabel;\n    checked;\n    focused;\n    subscription;\n    constructor(dt, tableService, cd) {\n      this.dt = dt;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.subscription = this.dt.tableService.selectionSource$.subscribe(() => {\n        this.checked = this.dt.isSelected(this.value);\n        this.ariaLabel = this.ariaLabel || this.dt.config.translation.aria ? this.checked ? this.dt.config.translation.aria.selectRow : this.dt.config.translation.aria.unselectRow : undefined;\n        this.cd.markForCheck();\n      });\n    }\n    ngOnInit() {\n      this.checked = this.dt.isSelected(this.value);\n    }\n    onClick(event) {\n      if (!this.disabled) {\n        this.dt.toggleRowWithCheckbox({\n          originalEvent: event,\n          rowIndex: this.index\n        }, this.value);\n      }\n      DomHandler.clearSelection();\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TableCheckbox_Factory(t) {\n      return new (t || TableCheckbox)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TableCheckbox,\n      selectors: [[\"p-tableCheckbox\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: \"disabled\",\n        value: \"value\",\n        index: \"index\",\n        inputId: \"inputId\",\n        name: \"name\",\n        required: \"required\",\n        ariaLabel: \"ariaLabel\"\n      },\n      decls: 7,\n      vars: 18,\n      consts: [[\"box\", \"\"], [1, \"p-checkbox\", \"p-component\", 3, \"click\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"focus\", \"blur\", \"checked\", \"disabled\", \"tabindex\"], [3, \"ngClass\"], [4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TableCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function TableCheckbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClick($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3);\n          i0.ɵɵlistener(\"focus\", function TableCheckbox_Template_input_focus_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus());\n          })(\"blur\", function TableCheckbox_Template_input_blur_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵtemplate(5, TableCheckbox_ng_container_5_Template, 2, 1, \"ng-container\", 5)(6, TableCheckbox_span_6_Template, 2, 4, \"span\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c25, ctx.focused, ctx.disabled));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"tabindex\", ctx.disabled ? null : \"0\");\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"required\", ctx.required)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(14, _c26, ctx.checked, ctx.focused, ctx.disabled));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.dt.checkboxIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.checkboxIconTemplate);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, CheckIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TableCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TableHeaderCheckbox = /*#__PURE__*/(() => {\n  class TableHeaderCheckbox {\n    dt;\n    tableService;\n    cd;\n    disabled;\n    inputId;\n    name;\n    ariaLabel;\n    checked;\n    focused;\n    selectionChangeSubscription;\n    valueChangeSubscription;\n    constructor(dt, tableService, cd) {\n      this.dt = dt;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.valueChangeSubscription = this.dt.tableService.valueSource$.subscribe(() => {\n        this.checked = this.updateCheckedState();\n        this.ariaLabel = this.ariaLabel || this.dt.config.translation.aria ? this.checked ? this.dt.config.translation.aria.selectAll : this.dt.config.translation.aria.unselectAll : undefined;\n      });\n      this.selectionChangeSubscription = this.dt.tableService.selectionSource$.subscribe(() => {\n        this.checked = this.updateCheckedState();\n      });\n    }\n    ngOnInit() {\n      this.checked = this.updateCheckedState();\n    }\n    onClick(event) {\n      if (!this.disabled) {\n        if (this.dt.value && this.dt.value.length > 0) {\n          this.dt.toggleRowsWithCheckbox(event, !this.checked);\n        }\n      }\n      DomHandler.clearSelection();\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n    }\n    isDisabled() {\n      return this.disabled || !this.dt.value || !this.dt.value.length;\n    }\n    ngOnDestroy() {\n      if (this.selectionChangeSubscription) {\n        this.selectionChangeSubscription.unsubscribe();\n      }\n      if (this.valueChangeSubscription) {\n        this.valueChangeSubscription.unsubscribe();\n      }\n    }\n    updateCheckedState() {\n      this.cd.markForCheck();\n      if (this.dt._selectAll !== null) {\n        return this.dt._selectAll;\n      } else {\n        const data = this.dt.selectionPageOnly ? this.dt.dataToRender(this.dt.processedData) : this.dt.processedData;\n        const val = this.dt.frozenValue ? [...this.dt.frozenValue, ...data] : data;\n        const selectableVal = this.dt.rowSelectable ? val.filter((data, index) => this.dt.rowSelectable({\n          data,\n          index\n        })) : val;\n        return ObjectUtils.isNotEmpty(selectableVal) && ObjectUtils.isNotEmpty(this.dt.selection) && selectableVal.every(v => this.dt.selection.some(s => this.dt.equals(v, s)));\n      }\n    }\n    static ɵfac = function TableHeaderCheckbox_Factory(t) {\n      return new (t || TableHeaderCheckbox)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(TableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TableHeaderCheckbox,\n      selectors: [[\"p-tableHeaderCheckbox\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: \"disabled\",\n        inputId: \"inputId\",\n        name: \"name\",\n        ariaLabel: \"ariaLabel\"\n      },\n      decls: 8,\n      vars: 17,\n      consts: [[\"cb\", \"\"], [\"box\", \"\"], [1, \"p-checkbox\", \"p-component\", 3, \"click\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"focus\", \"blur\", \"tabindex\", \"checked\", \"disabled\"], [3, \"ngClass\"], [4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TableHeaderCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function TableHeaderCheckbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClick($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"input\", 4, 0);\n          i0.ɵɵlistener(\"focus\", function TableHeaderCheckbox_Template_input_focus_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus());\n          })(\"blur\", function TableHeaderCheckbox_Template_input_blur_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 5, 1);\n          i0.ɵɵtemplate(6, TableHeaderCheckbox_ng_container_6_Template, 2, 1, \"ng-container\", 6)(7, TableHeaderCheckbox_span_7_Template, 2, 4, \"span\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c25, ctx.focused, ctx.isDisabled()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"tabindex\", ctx.disabled ? null : \"0\")(\"checked\", ctx.checked)(\"disabled\", ctx.isDisabled());\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(13, _c27, ctx.checked, ctx.focused, ctx.isDisabled()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.dt.headerCheckboxIconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dt.headerCheckboxIconTemplate);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, CheckIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TableHeaderCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ReorderableRowHandle = /*#__PURE__*/(() => {\n  class ReorderableRowHandle {\n    el;\n    constructor(el) {\n      this.el = el;\n    }\n    ngAfterViewInit() {\n      DomHandler.addClass(this.el.nativeElement, 'p-datatable-reorderablerow-handle');\n    }\n    static ɵfac = function ReorderableRowHandle_Factory(t) {\n      return new (t || ReorderableRowHandle)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ReorderableRowHandle,\n      selectors: [[\"\", \"pReorderableRowHandle\", \"\"]],\n      hostAttrs: [1, \"p-element\"]\n    });\n  }\n  return ReorderableRowHandle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ReorderableRow = /*#__PURE__*/(() => {\n  class ReorderableRow {\n    renderer;\n    dt;\n    el;\n    zone;\n    index;\n    pReorderableRowDisabled;\n    mouseDownListener;\n    dragStartListener;\n    dragEndListener;\n    dragOverListener;\n    dragLeaveListener;\n    dropListener;\n    constructor(renderer, dt, el, zone) {\n      this.renderer = renderer;\n      this.dt = dt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (this.isEnabled()) {\n        this.el.nativeElement.droppable = true;\n        this.bindEvents();\n      }\n    }\n    bindEvents() {\n      this.zone.runOutsideAngular(() => {\n        this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n        this.dragStartListener = this.renderer.listen(this.el.nativeElement, 'dragstart', this.onDragStart.bind(this));\n        this.dragEndListener = this.renderer.listen(this.el.nativeElement, 'dragend', this.onDragEnd.bind(this));\n        this.dragOverListener = this.renderer.listen(this.el.nativeElement, 'dragover', this.onDragOver.bind(this));\n        this.dragLeaveListener = this.renderer.listen(this.el.nativeElement, 'dragleave', this.onDragLeave.bind(this));\n      });\n    }\n    unbindEvents() {\n      if (this.mouseDownListener) {\n        this.mouseDownListener();\n        this.mouseDownListener = null;\n      }\n      if (this.dragStartListener) {\n        this.dragStartListener();\n        this.dragStartListener = null;\n      }\n      if (this.dragEndListener) {\n        this.dragEndListener();\n        this.dragEndListener = null;\n      }\n      if (this.dragOverListener) {\n        this.dragOverListener();\n        this.dragOverListener = null;\n      }\n      if (this.dragLeaveListener) {\n        this.dragLeaveListener();\n        this.dragLeaveListener = null;\n      }\n    }\n    onMouseDown(event) {\n      if (DomHandler.hasClass(event.target, 'p-datatable-reorderablerow-handle')) this.el.nativeElement.draggable = true;else this.el.nativeElement.draggable = false;\n    }\n    onDragStart(event) {\n      this.dt.onRowDragStart(event, this.index);\n    }\n    onDragEnd(event) {\n      this.dt.onRowDragEnd(event);\n      this.el.nativeElement.draggable = false;\n    }\n    onDragOver(event) {\n      this.dt.onRowDragOver(event, this.index, this.el.nativeElement);\n      event.preventDefault();\n    }\n    onDragLeave(event) {\n      this.dt.onRowDragLeave(event, this.el.nativeElement);\n    }\n    isEnabled() {\n      return this.pReorderableRowDisabled !== true;\n    }\n    onDrop(event) {\n      if (this.isEnabled() && this.dt.rowDragging) {\n        this.dt.onRowDrop(event, this.el.nativeElement);\n      }\n      event.preventDefault();\n    }\n    ngOnDestroy() {\n      this.unbindEvents();\n    }\n    static ɵfac = function ReorderableRow_Factory(t) {\n      return new (t || ReorderableRow)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ReorderableRow,\n      selectors: [[\"\", \"pReorderableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function ReorderableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function ReorderableRow_drop_HostBindingHandler($event) {\n            return ctx.onDrop($event);\n          });\n        }\n      },\n      inputs: {\n        index: [i0.ɵɵInputFlags.None, \"pReorderableRow\", \"index\"],\n        pReorderableRowDisabled: \"pReorderableRowDisabled\"\n      }\n    });\n  }\n  return ReorderableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Column Filter element of Table.\n * @group Components\n */\nlet ColumnFilter = /*#__PURE__*/(() => {\n  class ColumnFilter {\n    document;\n    el;\n    dt;\n    renderer;\n    config;\n    overlayService;\n    cd;\n    /**\n     * Property represented by the column.\n     * @group Props\n     */\n    field;\n    /**\n     * Type of the input.\n     * @group Props\n     */\n    type = 'text';\n    /**\n     * Filter display.\n     * @group Props\n     */\n    display = 'row';\n    /**\n     * Decides whether to display filter menu popup.\n     * @group Props\n     */\n    showMenu = true;\n    /**\n     * Filter match mode.\n     * @group Props\n     */\n    matchMode;\n    /**\n     * Filter operator.\n     * @defaultValue 'AND'\n     * @group Props\n     */\n    operator = FilterOperator.AND;\n    /**\n     * Decides whether to display filter operator.\n     * @group Props\n     */\n    showOperator = true;\n    /**\n     * Decides whether to display clear filter button.\n     * @group Props\n     */\n    showClearButton = true;\n    /**\n     * Decides whether to display apply filter button.\n     * @group Props\n     */\n    showApplyButton = true;\n    /**\n     * Decides whether to display filter match modes.\n     * @group Props\n     */\n    showMatchModes = true;\n    /**\n     * Decides whether to display add filter button.\n     * @group Props\n     */\n    showAddButton = true;\n    /**\n     * Decides whether to close popup on clear button click.\n     * @group Props\n     */\n    hideOnClear = false;\n    /**\n     * Filter placeholder.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Filter match mode options.\n     * @group Props\n     */\n    matchModeOptions;\n    /**\n     * Defines maximum amount of constraints.\n     * @group Props\n     */\n    maxConstraints = 2;\n    /**\n     * Defines minimum fraction of digits.\n     * @group Props\n     */\n    minFractionDigits;\n    /**\n     * Defines maximum fraction of digits.\n     * @group Props\n     */\n    maxFractionDigits;\n    /**\n     * Defines prefix of the filter.\n     * @group Props\n     */\n    prefix;\n    /**\n     * Defines suffix of the filter.\n     * @group Props\n     */\n    suffix;\n    /**\n     * Defines filter locale.\n     * @group Props\n     */\n    locale;\n    /**\n     * Defines filter locale matcher.\n     * @group Props\n     */\n    localeMatcher;\n    /**\n     * Enables currency input.\n     * @group Props\n     */\n    currency;\n    /**\n     * Defines the display of the currency input.\n     * @group Props\n     */\n    currencyDisplay;\n    /**\n     * Defines if filter grouping will be enabled.\n     * @group Props\n     */\n    useGrouping = true;\n    /**\n     * Defines the visibility of buttons.\n     * @group Props\n     */\n    showButtons = true;\n    icon;\n    clearButtonViewChild;\n    templates;\n    overlaySubscription;\n    headerTemplate;\n    filterTemplate;\n    footerTemplate;\n    filterIconTemplate;\n    removeRuleIconTemplate;\n    addRuleIconTemplate;\n    clearFilterIconTemplate;\n    operatorOptions;\n    overlayVisible;\n    overlay;\n    scrollHandler;\n    documentClickListener;\n    documentResizeListener;\n    matchModes;\n    translationSubscription;\n    resetSubscription;\n    selfClick;\n    overlayEventListener;\n    window;\n    overlayId;\n    get fieldConstraints() {\n      return this.dt.filters ? this.dt.filters[this.field] : null;\n    }\n    get showRemoveIcon() {\n      return this.fieldConstraints ? this.fieldConstraints.length > 1 : false;\n    }\n    get showMenuButton() {\n      return this.showMenu && (this.display === 'row' ? this.type !== 'boolean' : true);\n    }\n    get isShowOperator() {\n      return this.showOperator && this.type !== 'boolean';\n    }\n    get isShowAddConstraint() {\n      return this.showAddButton && this.type !== 'boolean' && this.fieldConstraints && this.fieldConstraints.length < this.maxConstraints;\n    }\n    get applyButtonLabel() {\n      return this.config.getTranslation(TranslationKeys.APPLY);\n    }\n    get clearButtonLabel() {\n      return this.config.getTranslation(TranslationKeys.CLEAR);\n    }\n    get addRuleButtonLabel() {\n      return this.config.getTranslation(TranslationKeys.ADD_RULE);\n    }\n    get removeRuleButtonLabel() {\n      return this.config.getTranslation(TranslationKeys.REMOVE_RULE);\n    }\n    get noFilterLabel() {\n      return this.config.getTranslation(TranslationKeys.NO_FILTER);\n    }\n    get filterMenuButtonAriaLabel() {\n      return this.config.translation ? this.overlayVisible ? this.config.translation.aria.showFilterMenu : this.config.translation.aria.hideFilterMenu : undefined;\n    }\n    get removeRuleButtonAriaLabel() {\n      return this.config.translation ? this.config.translation.removeRule : undefined;\n    }\n    get filterOperatorAriaLabel() {\n      return this.config.translation ? this.config.translation.aria.filterOperator : undefined;\n    }\n    get filterConstraintAriaLabel() {\n      return this.config.translation ? this.config.translation.aria.filterConstraint : undefined;\n    }\n    constructor(document, el, dt, renderer, config, overlayService, cd) {\n      this.document = document;\n      this.el = el;\n      this.dt = dt;\n      this.renderer = renderer;\n      this.config = config;\n      this.overlayService = overlayService;\n      this.cd = cd;\n      this.window = this.document.defaultView;\n    }\n    ngOnInit() {\n      this.overlayId = UniqueComponentId();\n      if (!this.dt.filters[this.field]) {\n        this.initFieldFilterConstraint();\n      }\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        this.generateMatchModeOptions();\n        this.generateOperatorOptions();\n      });\n      this.generateMatchModeOptions();\n      this.generateOperatorOptions();\n    }\n    generateMatchModeOptions() {\n      this.matchModes = this.matchModeOptions || this.config.filterMatchModeOptions[this.type]?.map(key => {\n        return {\n          label: this.config.getTranslation(key),\n          value: key\n        };\n      });\n    }\n    generateOperatorOptions() {\n      this.operatorOptions = [{\n        label: this.config.getTranslation(TranslationKeys.MATCH_ALL),\n        value: FilterOperator.AND\n      }, {\n        label: this.config.getTranslation(TranslationKeys.MATCH_ANY),\n        value: FilterOperator.OR\n      }];\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'filter':\n            this.filterTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'filtericon':\n            this.filterIconTemplate = item.template;\n            break;\n          case 'clearfiltericon':\n            this.clearFilterIconTemplate = item.template;\n            break;\n          case 'removeruleicon':\n            this.removeRuleIconTemplate = item.template;\n            break;\n          case 'addruleicon':\n            this.addRuleIconTemplate = item.template;\n            break;\n          default:\n            this.filterTemplate = item.template;\n            break;\n        }\n      });\n    }\n    initFieldFilterConstraint() {\n      let defaultMatchMode = this.getDefaultMatchMode();\n      this.dt.filters[this.field] = this.display == 'row' ? {\n        value: null,\n        matchMode: defaultMatchMode\n      } : [{\n        value: null,\n        matchMode: defaultMatchMode,\n        operator: this.operator\n      }];\n    }\n    onMenuMatchModeChange(value, filterMeta) {\n      filterMeta.matchMode = value;\n      if (!this.showApplyButton) {\n        this.dt._filter();\n      }\n    }\n    onRowMatchModeChange(matchMode) {\n      this.dt.filters[this.field].matchMode = matchMode;\n      this.dt._filter();\n      this.hide();\n    }\n    onRowMatchModeKeyDown(event) {\n      let item = event.target;\n      switch (event.key) {\n        case 'ArrowDown':\n          var nextItem = this.findNextItem(item);\n          if (nextItem) {\n            item.removeAttribute('tabindex');\n            nextItem.tabIndex = '0';\n            nextItem.focus();\n          }\n          event.preventDefault();\n          break;\n        case 'ArrowUp':\n          var prevItem = this.findPrevItem(item);\n          if (prevItem) {\n            item.removeAttribute('tabindex');\n            prevItem.tabIndex = '0';\n            prevItem.focus();\n          }\n          event.preventDefault();\n          break;\n      }\n    }\n    onRowClearItemClick() {\n      this.clearFilter();\n      this.hide();\n    }\n    isRowMatchModeSelected(matchMode) {\n      return this.dt.filters[this.field].matchMode === matchMode;\n    }\n    addConstraint() {\n      this.dt.filters[this.field].push({\n        value: null,\n        matchMode: this.getDefaultMatchMode(),\n        operator: this.getDefaultOperator()\n      });\n      DomHandler.focus(this.clearButtonViewChild.nativeElement);\n    }\n    removeConstraint(filterMeta) {\n      this.dt.filters[this.field] = this.dt.filters[this.field].filter(meta => meta !== filterMeta);\n      this.dt._filter();\n      DomHandler.focus(this.clearButtonViewChild.nativeElement);\n    }\n    onOperatorChange(value) {\n      this.dt.filters[this.field].forEach(filterMeta => {\n        filterMeta.operator = value;\n        this.operator = value;\n      });\n      if (!this.showApplyButton) {\n        this.dt._filter();\n      }\n    }\n    toggleMenu() {\n      this.overlayVisible = !this.overlayVisible;\n    }\n    onToggleButtonKeyDown(event) {\n      switch (event.key) {\n        case 'Escape':\n        case 'Tab':\n          this.overlayVisible = false;\n          break;\n        case 'ArrowDown':\n          if (this.overlayVisible) {\n            let focusable = DomHandler.getFocusableElements(this.overlay);\n            if (focusable) {\n              focusable[0].focus();\n            }\n            event.preventDefault();\n          } else if (event.altKey) {\n            this.overlayVisible = true;\n            event.preventDefault();\n          }\n          break;\n      }\n    }\n    onEscape() {\n      this.overlayVisible = false;\n      this.icon?.nativeElement.focus();\n    }\n    findNextItem(item) {\n      let nextItem = item.nextElementSibling;\n      if (nextItem) return DomHandler.hasClass(nextItem, 'p-column-filter-separator') ? this.findNextItem(nextItem) : nextItem;else return item.parentElement?.firstElementChild;\n    }\n    findPrevItem(item) {\n      let prevItem = item.previousElementSibling;\n      if (prevItem) return DomHandler.hasClass(prevItem, 'p-column-filter-separator') ? this.findPrevItem(prevItem) : prevItem;else return item.parentElement?.lastElementChild;\n    }\n    onContentClick() {\n      this.selfClick = true;\n    }\n    onOverlayAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.overlay = event.element;\n          this.renderer.appendChild(this.document.body, this.overlay);\n          ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n          DomHandler.absolutePosition(this.overlay, this.icon?.nativeElement);\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n          this.overlayEventListener = e => {\n            if (this.overlay && this.overlay.contains(e.target)) {\n              this.selfClick = true;\n            }\n          };\n          this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n          break;\n        case 'void':\n          this.onOverlayHide();\n          if (this.overlaySubscription) {\n            this.overlaySubscription.unsubscribe();\n          }\n          break;\n      }\n    }\n    onOverlayAnimationEnd(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.focusOnFirstElement();\n          break;\n        case 'void':\n          ZIndexUtils.clear(event.element);\n          break;\n      }\n    }\n    focusOnFirstElement() {\n      if (this.overlay) {\n        DomHandler.focus(DomHandler.getFirstFocusableElement(this.overlay, ''));\n      }\n    }\n    getDefaultMatchMode() {\n      if (this.matchMode) {\n        return this.matchMode;\n      } else {\n        if (this.type === 'text') return FilterMatchMode.STARTS_WITH;else if (this.type === 'numeric') return FilterMatchMode.EQUALS;else if (this.type === 'date') return FilterMatchMode.DATE_IS;else return FilterMatchMode.CONTAINS;\n      }\n    }\n    getDefaultOperator() {\n      return this.dt.filters ? this.dt.filters[this.field][0].operator : this.operator;\n    }\n    hasRowFilter() {\n      return this.dt.filters[this.field] && !this.dt.isFilterBlank(this.dt.filters[this.field].value);\n    }\n    hasFilter() {\n      let fieldFilter = this.dt.filters[this.field];\n      if (fieldFilter) {\n        if (Array.isArray(fieldFilter)) return !this.dt.isFilterBlank(fieldFilter[0].value);else return !this.dt.isFilterBlank(fieldFilter.value);\n      }\n      return false;\n    }\n    isOutsideClicked(event) {\n      return !(this.overlay?.isSameNode(event.target) || this.overlay?.contains(event.target) || this.icon?.nativeElement.isSameNode(event.target) || this.icon?.nativeElement.contains(event.target) || DomHandler.hasClass(event.target, 'p-column-filter-add-button') || DomHandler.hasClass(event.target.parentElement, 'p-column-filter-add-button') || DomHandler.hasClass(event.target, 'p-column-filter-remove-button') || DomHandler.hasClass(event.target.parentElement, 'p-column-filter-remove-button'));\n    }\n    bindDocumentClickListener() {\n      if (!this.documentClickListener) {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n          if (this.overlayVisible && !this.selfClick && this.isOutsideClicked(event)) {\n            this.hide();\n          }\n          this.selfClick = false;\n        });\n      }\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n        this.selfClick = false;\n      }\n    }\n    bindDocumentResizeListener() {\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n          if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n          }\n        });\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.icon?.nativeElement, () => {\n          if (this.overlayVisible) {\n            this.hide();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    hide() {\n      this.overlayVisible = false;\n      this.cd.markForCheck();\n    }\n    onOverlayHide() {\n      this.unbindDocumentClickListener();\n      this.unbindDocumentResizeListener();\n      this.unbindScrollListener();\n      this.overlay = null;\n    }\n    clearFilter() {\n      this.initFieldFilterConstraint();\n      this.dt._filter();\n      if (this.hideOnClear) this.hide();\n    }\n    applyFilter() {\n      this.dt._filter();\n      this.hide();\n    }\n    ngOnDestroy() {\n      if (this.overlay) {\n        this.renderer.appendChild(this.el.nativeElement, this.overlay);\n        ZIndexUtils.clear(this.overlay);\n        this.onOverlayHide();\n      }\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n      if (this.resetSubscription) {\n        this.resetSubscription.unsubscribe();\n      }\n      if (this.overlaySubscription) {\n        this.overlaySubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function ColumnFilter_Factory(t) {\n      return new (t || ColumnFilter)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ColumnFilter,\n      selectors: [[\"p-columnFilter\"]],\n      contentQueries: function ColumnFilter_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function ColumnFilter_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c28, 5);\n          i0.ɵɵviewQuery(_c29, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.icon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearButtonViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        field: \"field\",\n        type: \"type\",\n        display: \"display\",\n        showMenu: \"showMenu\",\n        matchMode: \"matchMode\",\n        operator: \"operator\",\n        showOperator: \"showOperator\",\n        showClearButton: \"showClearButton\",\n        showApplyButton: \"showApplyButton\",\n        showMatchModes: \"showMatchModes\",\n        showAddButton: \"showAddButton\",\n        hideOnClear: \"hideOnClear\",\n        placeholder: \"placeholder\",\n        matchModeOptions: \"matchModeOptions\",\n        maxConstraints: \"maxConstraints\",\n        minFractionDigits: \"minFractionDigits\",\n        maxFractionDigits: \"maxFractionDigits\",\n        prefix: \"prefix\",\n        suffix: \"suffix\",\n        locale: \"locale\",\n        localeMatcher: \"localeMatcher\",\n        currency: \"currency\",\n        currencyDisplay: \"currencyDisplay\",\n        useGrouping: \"useGrouping\",\n        showButtons: \"showButtons\"\n      },\n      decls: 5,\n      vars: 8,\n      consts: [[\"icon\", \"\"], [\"menu\", \"\"], [\"clearBtn\", \"\"], [1, \"p-column-filter\", 3, \"ngClass\"], [\"class\", \"p-fluid\", 3, \"type\", \"field\", \"filterConstraint\", \"filterTemplate\", \"placeholder\", \"minFractionDigits\", \"maxFractionDigits\", \"prefix\", \"suffix\", \"locale\", \"localeMatcher\", \"currency\", \"currencyDisplay\", \"useGrouping\", \"showButtons\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-column-filter-menu-button p-link\", \"aria-haspopup\", \"true\", 3, \"ngClass\", \"click\", \"keydown\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-column-filter-clear-button p-link\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"role\", \"dialog\", 3, \"ngClass\", \"id\", \"click\", \"keydown.escape\", 4, \"ngIf\"], [1, \"p-fluid\", 3, \"type\", \"field\", \"filterConstraint\", \"filterTemplate\", \"placeholder\", \"minFractionDigits\", \"maxFractionDigits\", \"prefix\", \"suffix\", \"locale\", \"localeMatcher\", \"currency\", \"currencyDisplay\", \"useGrouping\", \"showButtons\"], [\"type\", \"button\", \"aria-haspopup\", \"true\", 1, \"p-column-filter-menu-button\", \"p-link\", 3, \"click\", \"keydown\", \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"pi-filter-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"pi-filter-icon\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", 1, \"p-column-filter-clear-button\", \"p-link\", 3, \"click\", \"ngClass\"], [4, \"ngIf\"], [\"role\", \"dialog\", 3, \"click\", \"keydown.escape\", \"ngClass\", \"id\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-column-filter-row-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-column-filter-row-items\"], [\"class\", \"p-column-filter-row-item\", 3, \"ngClass\", \"click\", \"keydown\", \"keydown.enter\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-column-filter-separator\"], [1, \"p-column-filter-row-item\", 3, \"click\", \"keydown\", \"keydown.enter\"], [1, \"p-column-filter-row-item\", 3, \"click\", \"keydown\", \"keydown.enter\", \"ngClass\"], [\"class\", \"p-column-filter-operator\", 4, \"ngIf\"], [1, \"p-column-filter-constraints\"], [\"class\", \"p-column-filter-constraint\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-column-filter-add-rule\", 4, \"ngIf\"], [1, \"p-column-filter-buttonbar\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-button-outlined p-button-sm\", \"pRipple\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-button-sm\", \"pRipple\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"p-column-filter-operator\"], [\"styleClass\", \"p-column-filter-operator-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"p-column-filter-constraint\"], [\"styleClass\", \"p-column-filter-matchmode-dropdown\", 3, \"options\", \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [3, \"type\", \"field\", \"filterConstraint\", \"filterTemplate\", \"placeholder\", \"minFractionDigits\", \"maxFractionDigits\", \"prefix\", \"suffix\", \"locale\", \"localeMatcher\", \"currency\", \"currencyDisplay\", \"useGrouping\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-column-filter-remove-button p-button-text p-button-danger p-button-sm\", \"pRipple\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"p-column-filter-matchmode-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-column-filter-remove-button\", \"p-button-text\", \"p-button-danger\", \"p-button-sm\", 3, \"click\", \"label\"], [1, \"p-column-filter-add-rule\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-column-filter-add-button\", \"p-button-text\", \"p-button-sm\", 3, \"click\", \"label\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-outlined\", \"p-button-sm\", 3, \"click\", \"label\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-sm\", 3, \"click\", \"label\"]],\n      template: function ColumnFilter_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, ColumnFilter_p_columnFilterFormElement_1_Template, 1, 15, \"p-columnFilterFormElement\", 4)(2, ColumnFilter_button_2_Template, 4, 9, \"button\", 5)(3, ColumnFilter_button_3_Template, 4, 6, \"button\", 6)(4, ColumnFilter_div_4_Template, 6, 16, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c30, ctx.display === \"row\", ctx.display === \"menu\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.display === \"row\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMenuButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showClearButton && ctx.display === \"row\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMenu && ctx.overlayVisible);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i5.Dropdown, i6.NgControlStatus, i6.NgModel, i7.ButtonDirective, FilterIcon, FilterSlashIcon, ColumnFilterFormElement],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('overlayAnimation', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'scaleY(0.8)'\n        }), animate('.12s cubic-bezier(0, 0, 0.2, 1)')]), transition(':leave', [animate('.1s linear', style({\n          opacity: 0\n        }))])])]\n      }\n    });\n  }\n  return ColumnFilter;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ColumnFilterFormElement = /*#__PURE__*/(() => {\n  class ColumnFilterFormElement {\n    dt;\n    colFilter;\n    field;\n    type;\n    filterConstraint;\n    filterTemplate;\n    placeholder;\n    minFractionDigits;\n    maxFractionDigits;\n    prefix;\n    suffix;\n    locale;\n    localeMatcher;\n    currency;\n    currencyDisplay;\n    useGrouping = true;\n    get showButtons() {\n      return this.colFilter.showButtons;\n    }\n    filterCallback;\n    constructor(dt, colFilter) {\n      this.dt = dt;\n      this.colFilter = colFilter;\n    }\n    ngOnInit() {\n      this.filterCallback = value => {\n        this.filterConstraint.value = value;\n        this.dt._filter();\n      };\n    }\n    onModelChange(value) {\n      this.filterConstraint.value = value;\n      if (this.type === 'date' || this.type === 'boolean' || value === '') {\n        this.dt._filter();\n      }\n    }\n    onTextInputEnterKeyDown(event) {\n      this.dt._filter();\n      event.preventDefault();\n    }\n    onNumericInputKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.dt._filter();\n        event.preventDefault();\n      }\n    }\n    static ɵfac = function ColumnFilterFormElement_Factory(t) {\n      return new (t || ColumnFilterFormElement)(i0.ɵɵdirectiveInject(Table), i0.ɵɵdirectiveInject(ColumnFilter));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ColumnFilterFormElement,\n      selectors: [[\"p-columnFilterFormElement\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        field: \"field\",\n        type: \"type\",\n        filterConstraint: \"filterConstraint\",\n        filterTemplate: \"filterTemplate\",\n        placeholder: \"placeholder\",\n        minFractionDigits: \"minFractionDigits\",\n        maxFractionDigits: \"maxFractionDigits\",\n        prefix: \"prefix\",\n        suffix: \"suffix\",\n        locale: \"locale\",\n        localeMatcher: \"localeMatcher\",\n        currency: \"currency\",\n        currencyDisplay: \"currencyDisplay\",\n        useGrouping: \"useGrouping\"\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[\"builtInElement\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngSwitch\"], [\"type\", \"text\", \"pInputText\", \"\", 3, \"value\", \"input\", \"keydown.enter\", 4, \"ngSwitchCase\"], [3, \"ngModel\", \"showButtons\", \"minFractionDigits\", \"maxFractionDigits\", \"prefix\", \"suffix\", \"placeholder\", \"mode\", \"locale\", \"localeMatcher\", \"currency\", \"currencyDisplay\", \"useGrouping\", \"ngModelChange\", \"onKeyDown\", 4, \"ngSwitchCase\"], [3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [3, \"placeholder\", \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"text\", \"pInputText\", \"\", 3, \"input\", \"keydown.enter\", \"value\"], [3, \"ngModelChange\", \"onKeyDown\", \"ngModel\", \"showButtons\", \"minFractionDigits\", \"maxFractionDigits\", \"prefix\", \"suffix\", \"placeholder\", \"mode\", \"locale\", \"localeMatcher\", \"currency\", \"currencyDisplay\", \"useGrouping\"], [3, \"ngModelChange\", \"ngModel\"], [3, \"ngModelChange\", \"placeholder\", \"ngModel\"]],\n      template: function ColumnFilterFormElement_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ColumnFilterFormElement_ng_container_0_Template, 2, 19, \"ng-container\", 1)(1, ColumnFilterFormElement_ng_template_1_Template, 5, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const builtInElement_r6 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filterTemplate)(\"ngIfElse\", builtInElement_r6);\n        }\n      },\n      dependencies: [i2.NgIf, i2.NgTemplateOutlet, i2.NgSwitch, i2.NgSwitchCase, i8.InputNumber, i6.NgControlStatus, i6.NgModel, i9.InputText, i10.Calendar, i11.TriStateCheckbox],\n      encapsulation: 2\n    });\n  }\n  return ColumnFilterFormElement;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TableModule = /*#__PURE__*/(() => {\n  class TableModule {\n    static ɵfac = function TableModule_Factory(t) {\n      return new (t || TableModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TableModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, PaginatorModule, InputTextModule, DropdownModule, FormsModule, ButtonModule, SelectButtonModule, CalendarModule, InputNumberModule, TriStateCheckboxModule, ScrollerModule, ArrowDownIcon, ArrowUpIcon, SpinnerIcon, SortAltIcon, SortAmountUpAltIcon, SortAmountDownIcon, CheckIcon, FilterIcon, FilterSlashIcon, SharedModule, ScrollerModule]\n    });\n  }\n  return TableModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CancelEditableRow, CellEditor, ColumnFilter, ColumnFilterFormElement, ContextMenuRow, EditableColumn, EditableRow, FrozenColumn, InitEditableRow, ReorderableColumn, ReorderableRow, ReorderableRowHandle, ResizableColumn, RowGroupHeader, RowToggler, SaveEditableRow, SelectableRow, SelectableRowDblClick, SortIcon, SortableColumn, Table, TableBody, TableCheckbox, TableHeaderCheckbox, TableModule, TableRadioButton, TableService };\n//# sourceMappingURL=primeng-table.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}