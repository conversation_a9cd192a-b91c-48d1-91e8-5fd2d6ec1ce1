{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SalesOrdersComponent } from './sales-orders.component';\nimport { SalesOrdersDetailsComponent } from './sales-orders-details/sales-orders-details.component';\nimport { SalesOrdersOverviewComponent } from './sales-orders-details/sales-orders-overview/sales-orders-overview.component';\nimport { SalesOrdersContactsComponent } from './sales-orders-details/sales-orders-contacts/sales-orders-contacts.component';\nimport { SalesOrdersSalesTeamComponent } from './sales-orders-details/sales-orders-sales-team/sales-orders-sales-team.component';\nimport { SalesOrdersAiInsightsComponent } from './sales-orders-details/sales-orders-ai-insights/sales-orders-ai-insights.component';\nimport { SalesOrdersOrganizationDataComponent } from './sales-orders-details/sales-orders-organization-data/sales-orders-organization-data.component';\nimport { SalesOrdersAttachmentsComponent } from './sales-orders-details/sales-orders-attachments/sales-orders-attachments.component';\nimport { SalesOrdersNotesComponent } from './sales-orders-details/sales-orders-notes/sales-orders-notes.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SalesOrdersComponent\n}, {\n  path: ':id',\n  component: SalesOrdersDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: SalesOrdersOverviewComponent\n  }, {\n    path: 'contacts',\n    component: SalesOrdersContactsComponent\n  }, {\n    path: 'sales-team',\n    component: SalesOrdersSalesTeamComponent\n  }, {\n    path: 'ai-insight',\n    component: SalesOrdersAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: SalesOrdersOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: SalesOrdersAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: SalesOrdersNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class SalesOrdersRoutingModule {\n  static {\n    this.ɵfac = function SalesOrdersRoutingModule_Factory(t) {\n      return new (t || SalesOrdersRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SalesOrdersRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesOrdersRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SalesOrdersComponent", "SalesOrdersDetailsComponent", "SalesOrdersOverviewComponent", "SalesOrdersContactsComponent", "SalesOrdersSalesTeamComponent", "SalesOrdersAiInsightsComponent", "SalesOrdersOrganizationDataComponent", "SalesOrdersAttachmentsComponent", "SalesOrdersNotesComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "SalesOrdersRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-orders\\sales-orders-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SalesOrdersComponent } from './sales-orders.component';\r\nimport { SalesOrdersDetailsComponent } from './sales-orders-details/sales-orders-details.component';\r\nimport { SalesOrdersOverviewComponent } from './sales-orders-details/sales-orders-overview/sales-orders-overview.component';\r\nimport { SalesOrdersContactsComponent } from './sales-orders-details/sales-orders-contacts/sales-orders-contacts.component';\r\nimport { SalesOrdersSalesTeamComponent } from './sales-orders-details/sales-orders-sales-team/sales-orders-sales-team.component';\r\nimport { SalesOrdersAiInsightsComponent } from './sales-orders-details/sales-orders-ai-insights/sales-orders-ai-insights.component';\r\nimport { SalesOrdersOrganizationDataComponent } from './sales-orders-details/sales-orders-organization-data/sales-orders-organization-data.component';\r\nimport { SalesOrdersAttachmentsComponent } from './sales-orders-details/sales-orders-attachments/sales-orders-attachments.component';\r\nimport { SalesOrdersNotesComponent } from './sales-orders-details/sales-orders-notes/sales-orders-notes.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: SalesOrdersComponent },\r\n  {\r\n    path: ':id',\r\n    component: SalesOrdersDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: SalesOrdersOverviewComponent },\r\n      { path: 'contacts', component: SalesOrdersContactsComponent },\r\n      { path: 'sales-team', component: SalesOrdersSalesTeamComponent },\r\n      { path: 'ai-insight', component: SalesOrdersAiInsightsComponent },\r\n      { path: 'organization-data', component: SalesOrdersOrganizationDataComponent },\r\n      { path: 'attachments', component: SalesOrdersAttachmentsComponent },\r\n      { path: 'notes', component: SalesOrdersNotesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class SalesOrdersRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,6BAA6B,QAAQ,kFAAkF;AAChI,SAASC,8BAA8B,QAAQ,oFAAoF;AACnI,SAASC,oCAAoC,QAAQ,gGAAgG;AACrJ,SAASC,+BAA+B,QAAQ,oFAAoF;AACpI,SAASC,yBAAyB,QAAQ,wEAAwE;;;AAElH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEX;AAAoB,CAAE,EAC7C;EACEU,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEV,2BAA2B;EACtCW,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAA4B,CAAE,EAC7D;IAAEQ,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAER;EAA4B,CAAE,EAC7D;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA6B,CAAE,EAChE;IAAEM,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEN;EAA8B,CAAE,EACjE;IAAEK,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEL;EAAoC,CAAE,EAC9E;IAAEI,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEJ;EAA+B,CAAE,EACnE;IAAEG,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEH;EAAyB,CAAE,EACvD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAHzBhB,YAAY,CAACiB,QAAQ,CAACP,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXgB,wBAAwB;IAAAE,OAAA,GAAAC,EAAA,CAAAnB,YAAA;IAAAoB,OAAA,GAFzBpB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}