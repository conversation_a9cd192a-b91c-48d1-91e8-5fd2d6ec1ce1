import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OrganizationalComponent } from './organizational.component';
import { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';
import { EmployeesComponent } from './organization-details/employees/employees.component';
import { FunctionsComponent } from './organization-details/functions/functions.component';
import { GeneralComponent } from './organization-details/general/general.component';
import { OrganizationDetailsComponent } from './organization-details/organization-details.component';

const routes: Routes = [
  { path: '', component: OrganizationalComponent },
  { path: 'create', component: AddOrgUnitComponent },
  { path: 'createsub', component: AddOrgUnitComponent },
  {
    path: ':id',
    component: OrganizationDetailsComponent,
    children: [
      { path: 'general', component: GeneralComponent },
      { path: 'functions', component: FunctionsComponent },
      { path: 'employees', component: EmployeesComponent },
      { path: '', redirectTo: 'general', pathMatch: 'full' },
      { path: '**', redirectTo: 'general', pathMatch: 'full' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OrganizationalRoutingModule {}
