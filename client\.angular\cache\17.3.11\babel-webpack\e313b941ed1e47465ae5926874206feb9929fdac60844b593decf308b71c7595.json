{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { OpportunitiesComponent } from './opportunities.component';\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: OpportunitiesComponent\n}, {\n  path: 'create',\n  component: AddOpportunitieComponent\n}, {\n  path: ':id',\n  component: OpportunitiesDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: OpportunitiesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: OpportunitiesContactsComponent\n  }, {\n    path: 'sales-team',\n    component: OpportunitiesSalesTeamComponent\n  }, {\n    path: 'ai-insights',\n    component: OpportunitiesAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: OpportunitiesOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: OpportunitiesAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: OpportunitiesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class OpportunitiesRoutingModule {\n  static {\n    this.ɵfac = function OpportunitiesRoutingModule_Factory(t) {\n      return new (t || OpportunitiesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OpportunitiesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OpportunitiesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "OpportunitiesComponent", "OpportunitiesDetailsComponent", "OpportunitiesOverviewComponent", "OpportunitiesContactsComponent", "OpportunitiesSalesTeamComponent", "OpportunitiesAiInsightsComponent", "OpportunitiesOrganizationDataComponent", "OpportunitiesAttachmentsComponent", "OpportunitiesNotesComponent", "AddOpportunitieComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "OpportunitiesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { OpportunitiesComponent } from './opportunities.component';\r\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\r\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\r\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\r\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\r\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\r\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\r\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\r\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\r\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: OpportunitiesComponent },\r\n  { path: 'create', component: AddOpportunitieComponent },\r\n  {\r\n    path: ':id',\r\n    component: OpportunitiesDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: OpportunitiesOverviewComponent },\r\n      { path: 'contacts', component: OpportunitiesContactsComponent },\r\n      { path: 'sales-team', component: OpportunitiesSalesTeamComponent },\r\n      { path: 'ai-insights', component: OpportunitiesAiInsightsComponent },\r\n      {\r\n        path: 'organization-data',\r\n        component: OpportunitiesOrganizationDataComponent,\r\n      },\r\n      { path: 'attachments', component: OpportunitiesAttachmentsComponent },\r\n      { path: 'notes', component: OpportunitiesNotesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class OpportunitiesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,6BAA6B,QAAQ,yDAAyD;AACvG,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,gCAAgC,QAAQ,uFAAuF;AACxI,SAASC,sCAAsC,QAAQ,mGAAmG;AAC1J,SAASC,iCAAiC,QAAQ,uFAAuF;AACzI,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,wBAAwB,QAAQ,+CAA+C;;;AAExF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEZ;AAAsB,CAAE,EAC/C;EAAEW,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEH;AAAwB,CAAE,EACvD;EACEE,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEX,6BAA6B;EACxCY,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEV;EAA8B,CAAE,EAC/D;IAAES,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAA8B,CAAE,EAC/D;IAAEQ,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAER;EAA+B,CAAE,EAClE;IAAEO,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEP;EAAgC,CAAE,EACpE;IACEM,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEN;GACZ,EACD;IAAEK,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEL;EAAiC,CAAE,EACrE;IAAEI,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEJ;EAA2B,CAAE,EACzD;IAAEG,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BjB,YAAY,CAACkB,QAAQ,CAACP,MAAM,CAAC,EAC7BX,YAAY;IAAA;EAAA;;;2EAEXiB,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAF3BrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}