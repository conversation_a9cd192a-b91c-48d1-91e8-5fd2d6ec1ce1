{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { HttpEventType } from '@angular/common/http';\nimport { Subject } from 'rxjs';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./export.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/toast\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/button\";\nfunction ExportComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"Downloading file\\u2026\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"progress\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.fakeProgress, \"%\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r0.fakeProgress);\n  }\n}\nexport class ExportComponent {\n  constructor(exportservice, messageservice, cd) {\n    this.exportservice = exportservice;\n    this.messageservice = messageservice;\n    this.cd = cd;\n    this.unsubscribe$ = new Subject();\n    this.exporturl = `${CMS_APIContstant.EXPORT_URL}`;\n    this.selectedexport = '';\n    this.loading = false;\n    this.fakeProgress = 0;\n    this.selectedItem = null;\n    this.subItems = [];\n    this.selectedSubItem = null;\n    this.bitems = [{\n      label: 'Export',\n      routerLink: ['/store/export']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.items = [{\n      label: 'Prospects',\n      value: 'prospects'\n    }, {\n      label: 'Accounts',\n      value: 'accounts'\n    }, {\n      label: 'Contacts',\n      value: 'contacts'\n    }, {\n      label: 'Activities Sales Call',\n      value: 'phone-call-activity'\n    }, {\n      label: 'Activities Task',\n      value: 'task-activity'\n    }, {\n      label: 'Opportunities',\n      value: 'opportunity'\n    }];\n    this.subItemsMap = {\n      prospect: [{\n        label: 'Prospect Overview',\n        value: 'prospect-overview'\n      }, {\n        label: 'Prospect Contacts',\n        value: 'prospect-contacts'\n      }, {\n        label: 'Marketing Attributes',\n        value: 'marketing-attributes'\n      }],\n      account: [{\n        label: 'Business Partner Relationship',\n        value: 'business-partner-relationship'\n      }, {\n        label: 'Accounts',\n        value: 'accounts'\n      }, {\n        label: 'Account Team',\n        value: 'account-team'\n      }, {\n        label: 'Account Sales Data',\n        value: 'account-sales-data'\n      }, {\n        label: 'Account Contact Persons',\n        value: 'account-contact-persons'\n      }, {\n        label: 'Account Addresses',\n        value: 'account-addresses'\n      }],\n      contact: [{\n        label: 'Contact Is Contact Person For',\n        value: 'contact-is-contact-person-for'\n      }, {\n        label: 'Contact',\n        value: 'contact'\n      }, {\n        label: 'Contact Personal Addresses',\n        value: 'contact-personal-addresses'\n      }, {\n        label: 'Contact Notes',\n        value: 'contact-notes'\n      }],\n      activities: [{\n        label: 'Sales Call',\n        value: 'sales-call'\n      }],\n      opportunities: [{\n        label: 'Opportunity Sales Team Party Information',\n        value: 'opportunity-sales-team-party-information'\n      }, {\n        label: 'Opportunity Prospect Contact Party Information',\n        value: 'opportunity-prospect-contact-party-information'\n      }, {\n        label: 'Opportunity Preceding and Follow-Up Documents',\n        value: 'opportunity-preceding-and-follow-up-documents'\n      }, {\n        label: 'Opportunity Party Information',\n        value: 'opportunity-party-information'\n      }, {\n        label: 'Opportunity History',\n        value: 'opportunity-history'\n      }, {\n        label: 'Opportunity External Party Information',\n        value: 'opportunity-external-party-information'\n      }, {\n        label: 'Opportunity',\n        value: 'opportunity'\n      }]\n    };\n  }\n  ngOnInit() {\n    const inProgress = sessionStorage.getItem('exportInProgress') === 'true';\n    const storedProgress = parseInt(sessionStorage.getItem('exportProgress') || '0', 10);\n    const storedExport = sessionStorage.getItem('exportSelected');\n    const storedUrl = sessionStorage.getItem('exportUrl');\n    if (inProgress && storedExport && storedUrl) {\n      this.selectedexport = storedExport;\n      this.selectedItem = this.items.find(item => item['value'] === storedExport);\n      this.fakeProgress = storedProgress;\n      this.loading = true;\n      this.startSimulatedProgress();\n      this.exportservice.download(storedUrl).pipe(finalize(() => {\n        this.stopSimulatedProgress();\n        this.loading = false;\n        ['exportInProgress', 'exportSelected', 'exportProgress', 'exportUrl'].forEach(k => sessionStorage.removeItem(k));\n      })).subscribe({\n        next: event => {\n          if (event.type === HttpEventType.Response) {\n            const blob = new Blob([event.body], {\n              type: 'application/zip'\n            });\n            const filename = `crm-${storedExport}-${new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]}.zip`;\n            this.exportservice.save(blob, filename);\n            this.messageservice.add({\n              severity: 'success',\n              detail: 'Your file has been downloaded successfully.'\n            });\n            setTimeout(() => {\n              location.reload();\n            }, 500);\n          }\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'The download could not be completed. Please try again.'\n          });\n        }\n      });\n    }\n  }\n  onItemChange(event) {\n    const value = event.value ? event.value.value : null;\n    this.selectedexport = value;\n    this.subItems = value ? this.subItemsMap[value] || [] : [];\n    this.selectedSubItem = null;\n  }\n  onExport() {\n    if (!this.exporturl || !this.selectedexport) {\n      this.messageservice.add({\n        severity: 'warn',\n        detail: 'Export URL or type is missing.'\n      });\n      return;\n    }\n    const url = new URL(this.selectedexport, this.exporturl).toString();\n    this.loading = true;\n    // Save state to sessionStorage\n    sessionStorage.setItem('exportInProgress', 'true');\n    sessionStorage.setItem('exportSelected', this.selectedexport);\n    sessionStorage.setItem('exportProgress', this.fakeProgress.toString());\n    sessionStorage.setItem('exportUrl', url);\n    this.startSimulatedProgress(); // Starts from existing fakeProgress if resumed\n    this.exportservice.download(url).pipe(finalize(() => {\n      this.stopSimulatedProgress();\n      this.loading = false;\n      // Clear session state\n      ['exportInProgress', 'exportSelected', 'exportProgress', 'exportUrl'].forEach(k => sessionStorage.removeItem(k));\n    })).subscribe({\n      next: event => {\n        if (event.type === HttpEventType.Response) {\n          const blob = new Blob([event.body], {\n            type: 'application/zip'\n          });\n          const filename = `crm-${this.selectedexport}-${new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]}.zip`;\n          this.exportservice.save(blob, filename);\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Your file has been downloaded successfully.'\n          });\n          setTimeout(() => {\n            location.reload();\n          }, 500);\n        }\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'The download could not be completed. Please try again.'\n        });\n      }\n    });\n  }\n  startSimulatedProgress() {\n    this.progressInterval = setInterval(() => {\n      if (this.fakeProgress < 95) {\n        this.fakeProgress += 5;\n        sessionStorage.setItem('exportProgress', this.fakeProgress.toString());\n        this.cd.detectChanges(); // Update UI manually if needed\n      }\n    }, 300);\n  }\n  stopSimulatedProgress() {\n    clearInterval(this.progressInterval);\n    this.fakeProgress = 100;\n    this.cd.detectChanges();\n    setTimeout(() => {\n      location.reload();\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ExportComponent_Factory(t) {\n      return new (t || ExportComponent)(i0.ɵɵdirectiveInject(i1.ExportService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExportComponent,\n      selectors: [[\"app-export\"]],\n      decls: 19,\n      vars: 11,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"item-dropdown\", 1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"inputId\", \"item-dropdown\", \"optionLabel\", \"label\", \"placeholder\", \"Select Item\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"showClear\", \"disabled\", \"styleClass\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Export\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\", \"disabled\"], [1, \"mt-3\"], [1, \"surface-100\", \"border-1\", \"border-round\", \"px-3\", \"py-2\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\", \"text-sm\"], [1, \"text-600\", \"font-medium\"], [1, \"text-primary\", \"font-medium\"], [\"max\", \"100\", 1, \"custom-progress\", \"w-full\", \"h-1rem\", 3, \"value\"]],\n      template: function ExportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"h3\", 5);\n          i0.ɵɵtext(6, \"Export\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8)(10, \"span\", 9);\n          i0.ɵɵtext(11, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" Select Item \");\n          i0.ɵɵelementStart(13, \"span\", 10);\n          i0.ɵɵtext(14, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ExportComponent_Template_p_dropdown_ngModelChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedItem, $event) || (ctx.selectedItem = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ExportComponent_Template_p_dropdown_onChange_15_listener($event) {\n            return ctx.onItemChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, ExportComponent_div_16_Template, 8, 2, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ExportComponent_Template_button_click_18_listener() {\n            return ctx.onExport();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.bitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"options\", ctx.items);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedItem);\n          i0.ɵɵproperty(\"showClear\", false)(\"disabled\", ctx.loading)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedItem || ctx.loading);\n        }\n      },\n      dependencies: [i3.NgIf, i4.NgControlStatus, i4.NgModel, i5.Toast, i6.Breadcrumb, i7.Dropdown, i8.ButtonDirective],\n      styles: [\".export-dropdowns[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.export-dropdowns[_ngcontent-%COMP%]   .dropdown-label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #495057;\\n}\\n\\n.export-dropdowns[_ngcontent-%COMP%]   .dropdown-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.export-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  letter-spacing: 2px;\\n  text-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);\\n}\\n\\n  .custom-progress {\\n  width: 100%;\\n  height: 10px;\\n  border-radius: 5px;\\n  appearance: none;\\n}\\n\\n  .custom-progress::-webkit-progress-bar {\\n  background-color: #f3f3f3;\\n  border-radius: 5px;\\n}\\n\\n  .custom-progress::-webkit-progress-value {\\n  background-color: #184997;\\n  \\n\\n  border-radius: 5px;\\n}\\n\\n  .custom-progress::-moz-progress-bar {\\n  background-color: #184997;\\n  \\n\\n  border-radius: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvZXhwb3J0L2V4cG9ydC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLCtDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0Esa0JBQUE7QUFDRjs7QUFFQTtFQUNFLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtBQUNGOztBQUVBO0VBQ0UseUJBQUE7RUFDQSxvQkFBQTtFQUNBLGtCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuZXhwb3J0LWRyb3Bkb3ducyB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLmV4cG9ydC1kcm9wZG93bnMgLmRyb3Bkb3duLWxhYmVsIHtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICBjb2xvcjogIzQ5NTA1NztcclxufVxyXG5cclxuLmV4cG9ydC1kcm9wZG93bnMgLmRyb3Bkb3duLXNlY3Rpb24ge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5leHBvcnQtdGl0bGUge1xyXG4gIGZvbnQtc2l6ZTogMS41cmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gIGxldHRlci1zcGFjaW5nOiAycHg7XHJcbiAgdGV4dC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDI1LCAxMTgsIDIxMCwgMC4wOCk7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY3VzdG9tLXByb2dyZXNzIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwcHg7XHJcbiAgYm9yZGVyLXJhZGl1czogNXB4O1xyXG4gIGFwcGVhcmFuY2U6IG5vbmU7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY3VzdG9tLXByb2dyZXNzOjotd2Via2l0LXByb2dyZXNzLWJhciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2YzZjNmMztcclxuICBib3JkZXItcmFkaXVzOiA1cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY3VzdG9tLXByb2dyZXNzOjotd2Via2l0LXByb2dyZXNzLXZhbHVlIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTg0OTk3O1xyXG4gIC8qIE1hdGNoIGJ1dHRvbiBjb2xvciAqL1xyXG4gIGJvcmRlci1yYWRpdXM6IDVweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5jdXN0b20tcHJvZ3Jlc3M6Oi1tb3otcHJvZ3Jlc3MtYmFyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTg0OTk3O1xyXG4gIC8qIEZpcmVmb3ggc3VwcG9ydCAqL1xyXG4gIGJvcmRlci1yYWRpdXM6IDVweDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CMS_APIContstant", "HttpEventType", "Subject", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "fakeProgress", "ɵɵproperty", "ExportComponent", "constructor", "exportservice", "messageservice", "cd", "unsubscribe$", "exporturl", "EXPORT_URL", "selectedexport", "loading", "selectedItem", "subItems", "selectedSubItem", "bitems", "label", "routerLink", "home", "icon", "items", "value", "subItemsMap", "prospect", "account", "contact", "activities", "opportunities", "ngOnInit", "inProgress", "sessionStorage", "getItem", "storedProgress", "parseInt", "storedExport", "storedUrl", "find", "item", "startSimulatedProgress", "download", "pipe", "stopSimulatedProgress", "for<PERSON>ach", "k", "removeItem", "subscribe", "next", "event", "type", "Response", "blob", "Blob", "body", "filename", "Date", "toISOString", "replace", "split", "save", "add", "severity", "detail", "setTimeout", "location", "reload", "error", "onItemChange", "onExport", "url", "URL", "toString", "setItem", "progressInterval", "setInterval", "detectChanges", "clearInterval", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ExportService", "i2", "MessageService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "ExportComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ExportComponent_Template_p_dropdown_ngModelChange_15_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "ExportComponent_Template_p_dropdown_onChange_15_listener", "ɵɵtemplate", "ExportComponent_div_16_Template", "ExportComponent_Template_button_click_18_listener", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\export\\export.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\export\\export.component.html"], "sourcesContent": ["import { Component, ChangeDetectorRef, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ExportService } from './export.service';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { HttpEventType } from '@angular/common/http';\r\nimport { Subject, takeUntil, Observable } from 'rxjs';\r\nimport { finalize } from 'rxjs/operators';\r\n@Component({\r\n  selector: 'app-export',\r\n  templateUrl: './export.component.html',\r\n  styleUrl: './export.component.scss',\r\n})\r\nexport class ExportComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public exporturl: string = `${CMS_APIContstant.EXPORT_URL}`;\r\n  public selectedexport: string = '';\r\n  public loading: boolean = false;\r\n  public fakeProgress = 0;\r\n  private progressInterval: any;\r\n  public selectedItem: any = null;\r\n  public subItems: MenuItem[] = [];\r\n  public selectedSubItem: any = null;\r\n\r\n  public bitems: MenuItem[] | any = [\r\n    { label: 'Export', routerLink: ['/store/export'] },\r\n  ];\r\n  public home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n  public items: MenuItem[] = [\r\n    { label: 'Prospects', value: 'prospects' },\r\n    { label: 'Accounts', value: 'accounts' },\r\n    { label: 'Contacts', value: 'contacts' },\r\n    { label: 'Activities Sales Call', value: 'phone-call-activity' },\r\n    { label: 'Activities Task', value: 'task-activity' },\r\n    { label: 'Opportunities', value: 'opportunity' },\r\n  ];\r\n  public subItemsMap: { [key: string]: MenuItem[] } = {\r\n    prospect: [\r\n      { label: 'Prospect Overview', value: 'prospect-overview' },\r\n      { label: 'Prospect Contacts', value: 'prospect-contacts' },\r\n      { label: 'Marketing Attributes', value: 'marketing-attributes' },\r\n    ],\r\n    account: [\r\n      {\r\n        label: 'Business Partner Relationship',\r\n        value: 'business-partner-relationship',\r\n      },\r\n      { label: 'Accounts', value: 'accounts' },\r\n      { label: 'Account Team', value: 'account-team' },\r\n      { label: 'Account Sales Data', value: 'account-sales-data' },\r\n      { label: 'Account Contact Persons', value: 'account-contact-persons' },\r\n      { label: 'Account Addresses', value: 'account-addresses' },\r\n    ],\r\n    contact: [\r\n      {\r\n        label: 'Contact Is Contact Person For',\r\n        value: 'contact-is-contact-person-for',\r\n      },\r\n      { label: 'Contact', value: 'contact' },\r\n      {\r\n        label: 'Contact Personal Addresses',\r\n        value: 'contact-personal-addresses',\r\n      },\r\n      { label: 'Contact Notes', value: 'contact-notes' },\r\n    ],\r\n    activities: [{ label: 'Sales Call', value: 'sales-call' }],\r\n    opportunities: [\r\n      {\r\n        label: 'Opportunity Sales Team Party Information',\r\n        value: 'opportunity-sales-team-party-information',\r\n      },\r\n      {\r\n        label: 'Opportunity Prospect Contact Party Information',\r\n        value: 'opportunity-prospect-contact-party-information',\r\n      },\r\n      {\r\n        label: 'Opportunity Preceding and Follow-Up Documents',\r\n        value: 'opportunity-preceding-and-follow-up-documents',\r\n      },\r\n      {\r\n        label: 'Opportunity Party Information',\r\n        value: 'opportunity-party-information',\r\n      },\r\n      { label: 'Opportunity History', value: 'opportunity-history' },\r\n      {\r\n        label: 'Opportunity External Party Information',\r\n        value: 'opportunity-external-party-information',\r\n      },\r\n      { label: 'Opportunity', value: 'opportunity' },\r\n    ],\r\n  };\r\n\r\n  constructor(\r\n    private exportservice: ExportService,\r\n    private messageservice: MessageService,\r\n    private cd: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const inProgress = sessionStorage.getItem('exportInProgress') === 'true';\r\n    const storedProgress = parseInt(\r\n      sessionStorage.getItem('exportProgress') || '0',\r\n      10\r\n    );\r\n    const storedExport = sessionStorage.getItem('exportSelected');\r\n    const storedUrl = sessionStorage.getItem('exportUrl');\r\n\r\n    if (inProgress && storedExport && storedUrl) {\r\n      this.selectedexport = storedExport;\r\n      this.selectedItem = this.items.find(\r\n        (item) => item['value'] === storedExport\r\n      );\r\n      this.fakeProgress = storedProgress;\r\n      this.loading = true;\r\n\r\n      this.startSimulatedProgress();\r\n\r\n      this.exportservice\r\n        .download(storedUrl)\r\n        .pipe(\r\n          finalize(() => {\r\n            this.stopSimulatedProgress();\r\n            this.loading = false;\r\n            [\r\n              'exportInProgress',\r\n              'exportSelected',\r\n              'exportProgress',\r\n              'exportUrl',\r\n            ].forEach((k) => sessionStorage.removeItem(k));\r\n          })\r\n        )\r\n        .subscribe({\r\n          next: (event) => {\r\n            if (event.type === HttpEventType.Response) {\r\n              const blob = new Blob([event.body!], { type: 'application/zip' });\r\n              const filename = `crm-${storedExport}-${\r\n                new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]\r\n              }.zip`;\r\n              this.exportservice.save(blob, filename);\r\n              this.messageservice.add({\r\n                severity: 'success',\r\n                detail: 'Your file has been downloaded successfully.',\r\n              });\r\n              setTimeout(() => {\r\n                location.reload();\r\n              }, 500);\r\n            }\r\n          },\r\n          error: () => {\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'The download could not be completed. Please try again.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  onItemChange(event: any) {\r\n    const value = event.value ? event.value.value : null;\r\n    this.selectedexport = value;\r\n    this.subItems = value ? this.subItemsMap[value] || [] : [];\r\n    this.selectedSubItem = null;\r\n  }\r\n\r\n  onExport(): void {\r\n    if (!this.exporturl || !this.selectedexport) {\r\n      this.messageservice.add({\r\n        severity: 'warn',\r\n        detail: 'Export URL or type is missing.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    const url = new URL(this.selectedexport, this.exporturl).toString();\r\n\r\n    this.loading = true;\r\n\r\n    // Save state to sessionStorage\r\n    sessionStorage.setItem('exportInProgress', 'true');\r\n    sessionStorage.setItem('exportSelected', this.selectedexport);\r\n    sessionStorage.setItem('exportProgress', this.fakeProgress.toString());\r\n    sessionStorage.setItem('exportUrl', url);\r\n\r\n    this.startSimulatedProgress(); // Starts from existing fakeProgress if resumed\r\n\r\n    this.exportservice\r\n      .download(url)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.stopSimulatedProgress();\r\n          this.loading = false;\r\n\r\n          // Clear session state\r\n          [\r\n            'exportInProgress',\r\n            'exportSelected',\r\n            'exportProgress',\r\n            'exportUrl',\r\n          ].forEach((k) => sessionStorage.removeItem(k));\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (event) => {\r\n          if (event.type === HttpEventType.Response) {\r\n            const blob = new Blob([event.body!], { type: 'application/zip' });\r\n            const filename = `crm-${this.selectedexport}-${\r\n              new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]\r\n            }.zip`;\r\n            this.exportservice.save(blob, filename);\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Your file has been downloaded successfully.',\r\n            });\r\n            setTimeout(() => {\r\n              location.reload();\r\n            }, 500);\r\n          }\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'The download could not be completed. Please try again.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  startSimulatedProgress(): void {\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.fakeProgress < 95) {\r\n        this.fakeProgress += 5;\r\n        sessionStorage.setItem('exportProgress', this.fakeProgress.toString());\r\n        this.cd.detectChanges(); // Update UI manually if needed\r\n      }\r\n    }, 300);\r\n  }\r\n\r\n  stopSimulatedProgress(): void {\r\n    clearInterval(this.progressInterval);\r\n    this.fakeProgress = 100;\r\n    this.cd.detectChanges();\r\n\r\n    setTimeout(() => {\r\n      location.reload();\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <p-breadcrumb [model]=\"bitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n    </div>\r\n\r\n    <div class=\"card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50\">\r\n        <h3 class=\"mb-2 flex align-items-center h-3rem\">Export</h3>\r\n\r\n        <div class=\"p-fluid p-formgrid grid mt-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <label for=\"item-dropdown\" class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-300\">person</span>\r\n                    Select Item\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown inputId=\"item-dropdown\" [options]=\"items\" [(ngModel)]=\"selectedItem\" optionLabel=\"label\"\r\n                    placeholder=\"Select Item\" (onChange)=\"onItemChange($event)\" [showClear]=\"false\" [disabled]=\"loading\"\r\n                    [styleClass]=\"'h-3rem w-full'\">\r\n                </p-dropdown>\r\n\r\n                <!-- Progress bar below dropdown -->\r\n                <div *ngIf=\"loading\" class=\"mt-3\">\r\n                    <div class=\"surface-100 border-1 border-round px-3 py-2\">\r\n                        <div class=\"flex justify-content-between align-items-center mb-2 text-sm\">\r\n                            <span class=\"text-600 font-medium\">Downloading file…</span>\r\n                            <span class=\"text-primary font-medium\">{{ fakeProgress }}%</span>\r\n                        </div>\r\n                        <progress [value]=\"fakeProgress\" max=\"100\" class=\"custom-progress w-full h-1rem\"></progress>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"flex align-items-center gap-3 mt-4\">\r\n        <button pButton type=\"button\" label=\"Export\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n            (click)=\"onExport()\" [disabled]=\"!selectedItem || loading\">\r\n        </button>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,OAAO,QAA+B,MAAM;AACrD,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;;;ICkBbC,EAHZ,CAAAC,cAAA,cAAkC,cAC2B,cACqB,eACnC;IAAAD,EAAA,CAAAE,MAAA,6BAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IACNH,EAAA,CAAAI,SAAA,mBAA4F;IAEpGJ,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAJ6CH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAC,YAAA,MAAmB;IAEpDR,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAS,UAAA,UAAAF,MAAA,CAAAC,YAAA,CAAsB;;;ADfxD,OAAM,MAAOE,eAAe;EA+E1BC,YACUC,aAA4B,EAC5BC,cAA8B,EAC9BC,EAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IAjFJ,KAAAC,YAAY,GAAG,IAAIjB,OAAO,EAAQ;IACnC,KAAAkB,SAAS,GAAW,GAAGpB,gBAAgB,CAACqB,UAAU,EAAE;IACpD,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAX,YAAY,GAAG,CAAC;IAEhB,KAAAY,YAAY,GAAQ,IAAI;IACxB,KAAAC,QAAQ,GAAe,EAAE;IACzB,KAAAC,eAAe,GAAQ,IAAI;IAE3B,KAAAC,MAAM,GAAqB,CAChC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,UAAU,EAAE,CAAC,eAAe;IAAC,CAAE,CACnD;IACM,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAChE,KAAAG,KAAK,GAAe,CACzB;MAAEJ,KAAK,EAAE,WAAW;MAAEK,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEL,KAAK,EAAE,UAAU;MAAEK,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEL,KAAK,EAAE,UAAU;MAAEK,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEL,KAAK,EAAE,uBAAuB;MAAEK,KAAK,EAAE;IAAqB,CAAE,EAChE;MAAEL,KAAK,EAAE,iBAAiB;MAAEK,KAAK,EAAE;IAAe,CAAE,EACpD;MAAEL,KAAK,EAAE,eAAe;MAAEK,KAAK,EAAE;IAAa,CAAE,CACjD;IACM,KAAAC,WAAW,GAAkC;MAClDC,QAAQ,EAAE,CACR;QAAEP,KAAK,EAAE,mBAAmB;QAAEK,KAAK,EAAE;MAAmB,CAAE,EAC1D;QAAEL,KAAK,EAAE,mBAAmB;QAAEK,KAAK,EAAE;MAAmB,CAAE,EAC1D;QAAEL,KAAK,EAAE,sBAAsB;QAAEK,KAAK,EAAE;MAAsB,CAAE,CACjE;MACDG,OAAO,EAAE,CACP;QACER,KAAK,EAAE,+BAA+B;QACtCK,KAAK,EAAE;OACR,EACD;QAAEL,KAAK,EAAE,UAAU;QAAEK,KAAK,EAAE;MAAU,CAAE,EACxC;QAAEL,KAAK,EAAE,cAAc;QAAEK,KAAK,EAAE;MAAc,CAAE,EAChD;QAAEL,KAAK,EAAE,oBAAoB;QAAEK,KAAK,EAAE;MAAoB,CAAE,EAC5D;QAAEL,KAAK,EAAE,yBAAyB;QAAEK,KAAK,EAAE;MAAyB,CAAE,EACtE;QAAEL,KAAK,EAAE,mBAAmB;QAAEK,KAAK,EAAE;MAAmB,CAAE,CAC3D;MACDI,OAAO,EAAE,CACP;QACET,KAAK,EAAE,+BAA+B;QACtCK,KAAK,EAAE;OACR,EACD;QAAEL,KAAK,EAAE,SAAS;QAAEK,KAAK,EAAE;MAAS,CAAE,EACtC;QACEL,KAAK,EAAE,4BAA4B;QACnCK,KAAK,EAAE;OACR,EACD;QAAEL,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE;MAAe,CAAE,CACnD;MACDK,UAAU,EAAE,CAAC;QAAEV,KAAK,EAAE,YAAY;QAAEK,KAAK,EAAE;MAAY,CAAE,CAAC;MAC1DM,aAAa,EAAE,CACb;QACEX,KAAK,EAAE,0CAA0C;QACjDK,KAAK,EAAE;OACR,EACD;QACEL,KAAK,EAAE,gDAAgD;QACvDK,KAAK,EAAE;OACR,EACD;QACEL,KAAK,EAAE,+CAA+C;QACtDK,KAAK,EAAE;OACR,EACD;QACEL,KAAK,EAAE,+BAA+B;QACtCK,KAAK,EAAE;OACR,EACD;QAAEL,KAAK,EAAE,qBAAqB;QAAEK,KAAK,EAAE;MAAqB,CAAE,EAC9D;QACEL,KAAK,EAAE,wCAAwC;QAC/CK,KAAK,EAAE;OACR,EACD;QAAEL,KAAK,EAAE,aAAa;QAAEK,KAAK,EAAE;MAAa,CAAE;KAEjD;EAME;EAEHO,QAAQA,CAAA;IACN,MAAMC,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC,KAAK,MAAM;IACxE,MAAMC,cAAc,GAAGC,QAAQ,CAC7BH,cAAc,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,GAAG,EAC/C,EAAE,CACH;IACD,MAAMG,YAAY,GAAGJ,cAAc,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAMI,SAAS,GAAGL,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIF,UAAU,IAAIK,YAAY,IAAIC,SAAS,EAAE;MAC3C,IAAI,CAACzB,cAAc,GAAGwB,YAAY;MAClC,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACQ,KAAK,CAACgB,IAAI,CAChCC,IAAI,IAAKA,IAAI,CAAC,OAAO,CAAC,KAAKH,YAAY,CACzC;MACD,IAAI,CAAClC,YAAY,GAAGgC,cAAc;MAClC,IAAI,CAACrB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAAC2B,sBAAsB,EAAE;MAE7B,IAAI,CAAClC,aAAa,CACfmC,QAAQ,CAACJ,SAAS,CAAC,CACnBK,IAAI,CACHjD,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkD,qBAAqB,EAAE;QAC5B,IAAI,CAAC9B,OAAO,GAAG,KAAK;QACpB,CACE,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,CACZ,CAAC+B,OAAO,CAAEC,CAAC,IAAKb,cAAc,CAACc,UAAU,CAACD,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CACH,CACAE,SAAS,CAAC;QACTC,IAAI,EAAGC,KAAK,IAAI;UACd,IAAIA,KAAK,CAACC,IAAI,KAAK3D,aAAa,CAAC4D,QAAQ,EAAE;YACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,KAAK,CAACK,IAAK,CAAC,EAAE;cAAEJ,IAAI,EAAE;YAAiB,CAAE,CAAC;YACjE,MAAMK,QAAQ,GAAG,OAAOnB,YAAY,IAClC,IAAIoB,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7D,MAAM;YACN,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACR,IAAI,EAAEG,QAAQ,CAAC;YACvC,IAAI,CAAChD,cAAc,CAACsD,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFC,UAAU,CAAC,MAAK;cACdC,QAAQ,CAACC,MAAM,EAAE;YACnB,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAC5D,cAAc,CAACsD,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACN;EACF;EAEAK,YAAYA,CAACnB,KAAU;IACrB,MAAM1B,KAAK,GAAG0B,KAAK,CAAC1B,KAAK,GAAG0B,KAAK,CAAC1B,KAAK,CAACA,KAAK,GAAG,IAAI;IACpD,IAAI,CAACX,cAAc,GAAGW,KAAK;IAC3B,IAAI,CAACR,QAAQ,GAAGQ,KAAK,GAAG,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;IAC1D,IAAI,CAACP,eAAe,GAAG,IAAI;EAC7B;EAEAqD,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC3D,SAAS,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;MAC3C,IAAI,CAACL,cAAc,CAACsD,GAAG,CAAC;QACtBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,MAAMO,GAAG,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC3D,cAAc,EAAE,IAAI,CAACF,SAAS,CAAC,CAAC8D,QAAQ,EAAE;IAEnE,IAAI,CAAC3D,OAAO,GAAG,IAAI;IAEnB;IACAmB,cAAc,CAACyC,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;IAClDzC,cAAc,CAACyC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC7D,cAAc,CAAC;IAC7DoB,cAAc,CAACyC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACvE,YAAY,CAACsE,QAAQ,EAAE,CAAC;IACtExC,cAAc,CAACyC,OAAO,CAAC,WAAW,EAAEH,GAAG,CAAC;IAExC,IAAI,CAAC9B,sBAAsB,EAAE,CAAC,CAAC;IAE/B,IAAI,CAAClC,aAAa,CACfmC,QAAQ,CAAC6B,GAAG,CAAC,CACb5B,IAAI,CACHjD,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACkD,qBAAqB,EAAE;MAC5B,IAAI,CAAC9B,OAAO,GAAG,KAAK;MAEpB;MACA,CACE,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,CACZ,CAAC+B,OAAO,CAAEC,CAAC,IAAKb,cAAc,CAACc,UAAU,CAACD,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CACH,CACAE,SAAS,CAAC;MACTC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK3D,aAAa,CAAC4D,QAAQ,EAAE;UACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,KAAK,CAACK,IAAK,CAAC,EAAE;YAAEJ,IAAI,EAAE;UAAiB,CAAE,CAAC;UACjE,MAAMK,QAAQ,GAAG,OAAO,IAAI,CAAC3C,cAAc,IACzC,IAAI4C,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7D,MAAM;UACN,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACR,IAAI,EAAEG,QAAQ,CAAC;UACvC,IAAI,CAAChD,cAAc,CAACsD,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFC,UAAU,CAAC,MAAK;YACdC,QAAQ,CAACC,MAAM,EAAE;UACnB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC5D,cAAc,CAACsD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAvB,sBAAsBA,CAAA;IACpB,IAAI,CAACkC,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAACzE,YAAY,GAAG,EAAE,EAAE;QAC1B,IAAI,CAACA,YAAY,IAAI,CAAC;QACtB8B,cAAc,CAACyC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACvE,YAAY,CAACsE,QAAQ,EAAE,CAAC;QACtE,IAAI,CAAChE,EAAE,CAACoE,aAAa,EAAE,CAAC,CAAC;MAC3B;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EAEAjC,qBAAqBA,CAAA;IACnBkC,aAAa,CAAC,IAAI,CAACH,gBAAgB,CAAC;IACpC,IAAI,CAACxE,YAAY,GAAG,GAAG;IACvB,IAAI,CAACM,EAAE,CAACoE,aAAa,EAAE;IAEvBZ,UAAU,CAAC,MAAK;MACdC,QAAQ,CAACC,MAAM,EAAE;IACnB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAY,WAAWA,CAAA;IACT,IAAI,CAACrE,YAAY,CAACuC,IAAI,EAAE;IACxB,IAAI,CAACvC,YAAY,CAACsE,QAAQ,EAAE;EAC9B;;;uBA9OW3E,eAAe,EAAAV,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1F,EAAA,CAAAsF,iBAAA,CAAAtF,EAAA,CAAA2F,iBAAA;IAAA;EAAA;;;YAAfjF,eAAe;MAAAkF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb5BlG,EAAA,CAAAI,SAAA,iBAAsD;UAElDJ,EADJ,CAAAC,cAAA,aAA2E,aACc;UACjFD,EAAA,CAAAI,SAAA,sBAAsF;UAC1FJ,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,aAAoF,YAChC;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAK/CH,EAHZ,CAAAC,cAAA,aAA0C,aACU,eACsC,eACrB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAAE,MAAA,qBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,sBAEmC;UAFmBD,EAAA,CAAAoG,gBAAA,2BAAAC,8DAAAC,MAAA;YAAAtG,EAAA,CAAAuG,kBAAA,CAAAJ,GAAA,CAAA/E,YAAA,EAAAkF,MAAA,MAAAH,GAAA,CAAA/E,YAAA,GAAAkF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAClDtG,EAAA,CAAAwG,UAAA,sBAAAC,yDAAAH,MAAA;YAAA,OAAYH,GAAA,CAAAzB,YAAA,CAAA4B,MAAA,CAAoB;UAAA,EAAC;UAE/DtG,EAAA,CAAAG,YAAA,EAAa;UAGbH,EAAA,CAAA0G,UAAA,KAAAC,+BAAA,kBAAkC;UAW9C3G,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAgD,kBAEmB;UAA3DD,EAAA,CAAAwG,UAAA,mBAAAI,kDAAA;YAAA,OAAST,GAAA,CAAAxB,QAAA,EAAU;UAAA,EAAC;UAGhC3E,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;;;UAxCwBH,EAAA,CAAAS,UAAA,cAAa;UAGrBT,EAAA,CAAAK,SAAA,GAAgB;UAAeL,EAA/B,CAAAS,UAAA,UAAA0F,GAAA,CAAA5E,MAAA,CAAgB,SAAA4E,GAAA,CAAAzE,IAAA,CAAc,uCAAuC;UAavC1B,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAS,UAAA,YAAA0F,GAAA,CAAAvE,KAAA,CAAiB;UAAC5B,EAAA,CAAA6G,gBAAA,YAAAV,GAAA,CAAA/E,YAAA,CAA0B;UAE5EpB,EAD4D,CAAAS,UAAA,oBAAmB,aAAA0F,GAAA,CAAAhF,OAAA,CAAqB,+BACtE;UAI5BnB,EAAA,CAAAK,SAAA,EAAa;UAAbL,EAAA,CAAAS,UAAA,SAAA0F,GAAA,CAAAhF,OAAA,CAAa;UAeFnB,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAS,UAAA,cAAA0F,GAAA,CAAA/E,YAAA,IAAA+E,GAAA,CAAAhF,OAAA,CAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}