{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./contacts.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nconst _c1 = a0 => ({\n  \"text-orange-600 cursor-pointer font-medium\": true,\n  underline: a0\n});\nconst _c2 = a0 => ({\n  \"text-blue-600 cursor-pointer font-medium\": true,\n  underline: a0\n});\nfunction ContactsComponent_ng_template_19_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsComponent_ng_template_19_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction ContactsComponent_ng_template_19_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsComponent_ng_template_19_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction ContactsComponent_ng_template_19_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function ContactsComponent_ng_template_19_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ContactsComponent_ng_template_19_ng_container_8_i_4_Template, 1, 1, \"i\", 24)(5, ContactsComponent_ng_template_19_ng_container_8_i_5_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction ContactsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function ContactsComponent_ng_template_19_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"bp_person_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" Contact ID \");\n    i0.ɵɵtemplate(6, ContactsComponent_ng_template_19_i_6_Template, 1, 1, \"i\", 24)(7, ContactsComponent_ng_template_19_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ContactsComponent_ng_template_19_ng_container_8_Template, 6, 4, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"bp_person_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"bp_person_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c2, contact_r6 == null ? null : contact_r6.contact_name))(\"routerLink\", \"/store/contacts/\" + contact_r6.documentId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.contact_name) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.account_name) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDepartmentLabel(contact_r6 == null ? null : contact_r6.business_department) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.phone_number) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.email_address) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r6.web_registered)(\"disabled\", true);\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.mobile) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.account_id) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.status) || \"-\", \" \");\n  }\n}\nfunction ContactsComponent_ng_template_20_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, ContactsComponent_ng_template_20_ng_container_5_ng_container_3_Template, 3, 5, \"ng-container\", 35)(4, ContactsComponent_ng_template_20_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 35)(5, ContactsComponent_ng_template_20_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 35)(6, ContactsComponent_ng_template_20_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 35)(7, ContactsComponent_ng_template_20_ng_container_5_ng_container_7_Template, 2, 1, \"ng-container\", 35)(8, ContactsComponent_ng_template_20_ng_container_5_ng_container_8_Template, 2, 3, \"ng-container\", 35)(9, ContactsComponent_ng_template_20_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 35)(10, ContactsComponent_ng_template_20_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 35)(11, ContactsComponent_ng_template_20_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 35);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_person.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_company.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"department\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_person.addresses.phone_numbers.phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_person.addresses.emails.email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"web_registered\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_person.addresses.phone_numbers.phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"bp_company_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_person.is_marked_for_archiving\");\n  }\n}\nfunction ContactsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ContactsComponent_ng_template_20_ng_container_5_Template, 12, 10, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, contact_r6 == null ? null : contact_r6.contact_id))(\"routerLink\", \"/store/contacts/\" + contact_r6.documentId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.contact_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ContactsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \" No contacts found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \" Loading contacts data. Please wait. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ContactsComponent {\n  constructor(contactsservice, router) {\n    this.contactsservice = contactsservice;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.contacts = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.contactDetails = null;\n    this.cpFunctions = [];\n    this.cpDepartment = [];\n    this.searchInputChanged = new Subject();\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'business_partner_person.bp_full_name',\n      header: 'Name'\n    }, {\n      field: 'business_partner_company.bp_full_name',\n      header: 'Account'\n    }, {\n      field: 'department',\n      header: 'Department'\n    }, {\n      field: 'business_partner_person.addresses.phone_numbers.phone_number',\n      header: 'Phone'\n    }, {\n      field: 'business_partner_person.addresses.emails.email_address',\n      header: 'E-Mail'\n    }, {\n      field: 'web_registered',\n      header: 'Web Registered'\n    }, {\n      field: 'business_partner_person.addresses.phone_numbers.phone_number',\n      header: 'Mobile'\n    }, {\n      field: 'bp_company_id',\n      header: 'Account ID'\n    }, {\n      field: 'business_partner_person.is_marked_for_archiving',\n      header: 'Status'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.contacts.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.searchInputChanged.pipe(debounceTime(500),\n    // Adjust delay here (ms)\n    distinctUntilChanged()).subscribe(term => {\n      this.globalSearchTerm = term;\n      this.loadContacts({\n        first: 0,\n        rows: 15\n      });\n    });\n    this.loadFunctions();\n    this.loadDepartment();\n    this.breadcrumbitems = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Contacts',\n      code: 'MC'\n    }, {\n      name: 'Obsolete Contacts',\n      code: 'OC'\n    }];\n    this.selectedActions = {\n      name: 'All',\n      code: 'ALL'\n    };\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadContacts(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    const obsolete = this.selectedActions?.code === 'OC';\n    this.contactsservice.getContacts(page, pageSize, sortField, sortOrder, this.globalSearchTerm, obsolete).subscribe({\n      next: response => {\n        let contacts = (response?.data || []).map(contact => {\n          const addresses = contact?.business_partner_person?.addresses || [];\n          const getPhoneNumberByType = type => (addresses?.[0]?.phone_numbers || []).filter(item => item.phone_number_type === type).map(item => item.phone_number);\n          return {\n            ...contact,\n            account_id: contact?.bp_company_id || '-',\n            contact_id: contact?.bp_person_id || '-',\n            contact_name: contact?.business_partner_person?.bp_full_name || '-',\n            account_name: contact?.business_partner_company?.bp_full_name || '-',\n            business_department: contact?.person_func_and_dept?.contact_person_department || '-',\n            email_address: addresses?.[0]?.emails?.[0]?.email_address,\n            phone_number: getPhoneNumberByType('1'),\n            mobile: getPhoneNumberByType('3'),\n            web_registered: contact?.business_partner_person?.bp_extension?.web_registered ? true : false,\n            status: contact?.business_partner_person?.is_marked_for_archiving ? 'Obsolete' : 'Active'\n          };\n        });\n        this.contacts = contacts;\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching contacts', error);\n        this.loading = false;\n      }\n    });\n  }\n  loadDepartment() {\n    this.contactsservice.getCPDepartment().pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response && response.data) {\n        this.cpDepartment = response.data.map(item => ({\n          label: item.description,\n          value: item.code\n        }));\n      }\n    });\n  }\n  getDepartmentLabel(value) {\n    return this.cpDepartment.find(opt => opt.value === value)?.label;\n  }\n  loadFunctions() {\n    this.contactsservice.getCPFunction().pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response && response.data) {\n        this.cpFunctions = response.data.map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 14\n    };\n    this.loadContacts(dt1State);\n  }\n  signup() {\n    this.router.navigate(['/store/contacts/create']);\n  }\n  onSearchInputChange(event) {\n    const input = event.target.value;\n    this.searchInputChanged.next(input);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ContactsComponent_Factory(t) {\n      return new (t || ContactsComponent)(i0.ɵɵdirectiveInject(i1.ContactsService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsComponent,\n      selectors: [[\"app-contacts\"]],\n      viewQuery: function ContactsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 23,\n      vars: 18,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Contact\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 3, \"ngClass\", \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"ngClass\", \"routerLink\"], [3, \"binary\", \"ngModel\", \"disabled\"], [\"colspan\", \"11\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function ContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function ContactsComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function ContactsComponent_Template_p_dropdown_onChange_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ContactsComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.signup());\n          });\n          i0.ɵɵelementStart(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-multiSelect\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsComponent_Template_p_multiSelect_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 15)(17, \"p-table\", 16, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function ContactsComponent_Template_p_table_onLazyLoad_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadContacts($event));\n          })(\"onColReorder\", function ContactsComponent_Template_p_table_onColReorder_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(19, ContactsComponent_ng_template_19_Template, 9, 3, \"ng-template\", 17)(20, ContactsComponent_ng_template_20_Template, 6, 7, \"ng-template\", 18)(21, ContactsComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19)(22, ContactsComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.contacts)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i2.RouterLink, i4.Breadcrumb, i5.PrimeTemplate, i6.Dropdown, i7.Table, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i9.Checkbox, i10.MultiSelect],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "ContactsComponent_ng_template_19_ng_container_8_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "ContactsComponent_ng_template_19_ng_container_8_i_4_Template", "ContactsComponent_ng_template_19_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "ContactsComponent_ng_template_19_Template_th_click_3_listener", "_r2", "ContactsComponent_ng_template_19_i_6_Template", "ContactsComponent_ng_template_19_i_7_Template", "ContactsComponent_ng_template_19_ng_container_8_Template", "selectedColumns", "ɵɵpureFunction1", "_c2", "contact_r6", "contact_name", "documentId", "account_name", "getDepartmentLabel", "business_department", "phone_number", "email_address", "web_registered", "mobile", "account_id", "status", "ContactsComponent_ng_template_20_ng_container_5_ng_container_3_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_4_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_5_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_6_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_7_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_8_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_9_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_10_Template", "ContactsComponent_ng_template_20_ng_container_5_ng_container_11_Template", "col_r7", "ContactsComponent_ng_template_20_ng_container_5_Template", "_c1", "contact_id", "ContactsComponent", "constructor", "contactsservice", "router", "unsubscribe$", "contacts", "totalRecords", "loading", "globalSearchTerm", "contactDetails", "cpFunctions", "cpDepartment", "searchInputChanged", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "pipe", "subscribe", "term", "loadContacts", "first", "rows", "loadFunctions", "loadDepartment", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "selectedActions", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "page", "pageSize", "obsolete", "getContacts", "next", "response", "map", "contact", "addresses", "business_partner_person", "getPhoneNumberByType", "type", "phone_numbers", "item", "phone_number_type", "bp_company_id", "bp_person_id", "bp_full_name", "business_partner_company", "person_func_and_dept", "contact_person_department", "emails", "bp_extension", "is_marked_for_archiving", "meta", "pagination", "total", "error", "console", "getCPDepartment", "description", "value", "find", "opt", "getCPFunction", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "signup", "navigate", "onSearchInputChange", "input", "target", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ContactsService", "i2", "Router", "selectors", "viewQuery", "ContactsComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "ContactsComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "ContactsComponent_Template_input_input_7_listener", "ContactsComponent_Template_p_dropdown_ngModelChange_10_listener", "ContactsComponent_Template_p_dropdown_onChange_10_listener", "ContactsComponent_Template_button_click_11_listener", "ContactsComponent_Template_p_multiSelect_ngModelChange_15_listener", "ContactsComponent_Template_p_table_onLazyLoad_17_listener", "ContactsComponent_Template_p_table_onColReorder_17_listener", "ContactsComponent_ng_template_19_Template", "ContactsComponent_ng_template_20_Template", "ContactsComponent_ng_template_21_Template", "ContactsComponent_ng_template_22_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Table } from 'primeng/table';\r\nimport { ContactsService } from './contacts.service';\r\nimport { Subject, takeUntil, Observable } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-contacts',\r\n  templateUrl: './contacts.component.html',\r\n  styleUrl: './contacts.component.scss',\r\n})\r\nexport class ContactsComponent implements OnInit {\r\n  @ViewChild('dt1') dt1!: Table;\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n  public contacts: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public contactDetails: any = null;\r\n  public cpFunctions: { name: string; value: string }[] = [];\r\n  public cpDepartment: { label: string; value: string }[] = [];\r\n  public searchInputChanged: Subject<string> = new Subject<string>();\r\n\r\n  constructor(\r\n    private contactsservice: ContactsService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'business_partner_person.bp_full_name', header: 'Name' },\r\n    { field: 'business_partner_company.bp_full_name', header: 'Account' },\r\n    { field: 'department', header: 'Department' },\r\n    { field: 'business_partner_person.addresses.phone_numbers.phone_number', header: 'Phone' },\r\n    { field: 'business_partner_person.addresses.emails.email_address', header: 'E-Mail' },\r\n    { field: 'web_registered', header: 'Web Registered' },\r\n    { field: 'business_partner_person.addresses.phone_numbers.phone_number', header: 'Mobile' },\r\n    { field: 'bp_company_id', header: 'Account ID' },\r\n    { field: 'business_partner_person.is_marked_for_archiving', header: 'Status' }\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.contacts.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.searchInputChanged\r\n      .pipe(\r\n        debounceTime(500), // Adjust delay here (ms)\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe((term: string) => {\r\n        this.globalSearchTerm = term;\r\n        this.loadContacts({ first: 0, rows: 15 });\r\n      });\r\n    this.loadFunctions();\r\n    this.loadDepartment();\r\n    this.breadcrumbitems = [\r\n      { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Contacts', code: 'MC' },\r\n      { name: 'Obsolete Contacts', code: 'OC' },\r\n    ];\r\n    this.selectedActions = { name: 'All', code: 'ALL' };\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadContacts(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    const obsolete = this.selectedActions?.code === 'OC';\r\n\r\n    this.contactsservice\r\n      .getContacts(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm,\r\n        obsolete\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          let contacts = (response?.data || []).map((contact: any) => {\r\n            const addresses = contact?.business_partner_person?.addresses || [];\r\n            const getPhoneNumberByType = (type: string) =>\r\n              (addresses?.[0]?.phone_numbers || [])\r\n                .filter((item: any) => item.phone_number_type === type)\r\n                .map((item: any) => item.phone_number);\r\n\r\n            return {\r\n              ...contact,\r\n              account_id: contact?.bp_company_id || '-',\r\n              contact_id: contact?.bp_person_id || '-',\r\n              contact_name:\r\n                contact?.business_partner_person?.bp_full_name || '-',\r\n              account_name:\r\n                contact?.business_partner_company?.bp_full_name || '-',\r\n              business_department:\r\n                contact?.person_func_and_dept?.contact_person_department || '-',\r\n              email_address: addresses?.[0]?.emails?.[0]?.email_address,\r\n              phone_number: getPhoneNumberByType('1'),\r\n              mobile: getPhoneNumberByType('3'),\r\n              web_registered: contact?.business_partner_person?.bp_extension\r\n                ?.web_registered\r\n                ? true\r\n                : false,\r\n              status: contact?.business_partner_person?.is_marked_for_archiving\r\n                ? 'Obsolete'\r\n                : 'Active',\r\n            };\r\n          });\r\n\r\n          this.contacts = contacts;\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching contacts', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  public loadDepartment(): void {\r\n    this.contactsservice\r\n      .getCPDepartment()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response && response.data) {\r\n          this.cpDepartment = response.data.map((item: any) => ({\r\n            label: item.description,\r\n            value: item.code,\r\n          }));\r\n        }\r\n      });\r\n  }\r\n\r\n  getDepartmentLabel(value: string): string | undefined {\r\n    return this.cpDepartment.find((opt) => opt.value === value)?.label;\r\n  }\r\n\r\n  public loadFunctions(): void {\r\n    this.contactsservice\r\n      .getCPFunction()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response && response.data) {\r\n          this.cpFunctions = response.data.map((item: any) => ({\r\n            name: item.description,\r\n            value: item.code,\r\n          }));\r\n        }\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 14,\r\n    };\r\n    this.loadContacts(dt1State);\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/contacts/create']);\r\n  }\r\n\r\n  onSearchInputChange(event: Event) {\r\n    const input = (event.target as HTMLInputElement).value;\r\n    this.searchInputChanged.next(input);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onSearchInputChange($event)\"\r\n                        placeholder=\"Search Contact\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\" style=\"right: 16px\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange()\"\r\n                optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n\r\n            <button type=\"button\" (click)=\"signup()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table #dt1 [value]=\"contacts\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadContacts($event)\"\r\n            [loading]=\"loading\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\"\r\n            [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n\r\n                    <th pFrozenColumn (click)=\"customSort('bp_person_id')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Contact ID\r\n                            <i *ngIf=\"sortField === 'bp_person_id'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'bp_person_id'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td pFrozenColumn\r\n                        [ngClass]=\"{ 'text-orange-600 cursor-pointer font-medium': true, underline: contact?.contact_id }\"\r\n                        [routerLink]=\"'/store/contacts/' + contact.documentId\">\r\n                        {{ contact?.contact_id || \"-\" }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'business_partner_person.bp_full_name'\">\r\n                                    <span\r\n                                        [ngClass]=\"{ 'text-blue-600 cursor-pointer font-medium': true, underline: contact?.contact_name }\"\r\n                                        [routerLink]=\"'/store/contacts/' + contact.documentId\">\r\n                                        {{ contact?.contact_name || \"-\" }}\r\n                                    </span>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_company.bp_full_name'\">\r\n                                    {{ contact?.account_name || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'department'\">\r\n                                    {{ getDepartmentLabel(contact?.business_department) || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container\r\n                                    *ngSwitchCase=\"'business_partner_person.addresses.phone_numbers.phone_number'\">\r\n                                    {{ contact?.phone_number || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_person.addresses.emails.email_address'\">\r\n                                    {{ contact?.email_address || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'web_registered'\">\r\n                                    <p-checkbox [binary]=\"true\" [ngModel]=\"contact.web_registered\"\r\n                                        [disabled]=\"true\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                                <ng-container\r\n                                    *ngSwitchCase=\"'business_partner_person.addresses.phone_numbers.phone_number'\">\r\n                                    {{ contact?.mobile || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'bp_company_id'\">\r\n                                    {{ contact?.account_id || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_person.is_marked_for_archiving'\">\r\n                                    {{ contact?.status || \"-\" }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"11\" class=\"border-round-left-lg pl-3\">\r\n                        No contacts found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"11\" class=\"border-round-left-lg pl-3\">\r\n                        Loading contacts data. Please wait.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAoB,MAAM;AAErD,SAASC,YAAY,EAAEC,oBAAoB,QAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;ICyC5CC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAoE;;;;;IAQhED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,6EAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,4DAAA,gBACkF,IAAAC,4DAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IArB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAM,cAAA,aAAuD;IAArCN,EAAA,CAAAO,UAAA,mBAAAmB,8DAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,cAAc,CAAC;IAAA,EAAC;IAClDf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,mBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,6CAAA,gBACkF,IAAAC,6CAAA,gBAElB;IAExE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAY,wDAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,oBAAkC;IAGlCzB,EAAA,CAAAsB,SAAA,EAAkC;IAAlCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,oBAAkC;IAIhBzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAK,uBAAA,GAAqE;IACjEL,EAAA,CAAAM,cAAA,eAE2D;IACvDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;;IAHHrB,EAAA,CAAAsB,SAAA,EAAkG;IAClGtB,EADA,CAAAE,UAAA,YAAAF,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAC,UAAA,kBAAAA,UAAA,CAAAC,YAAA,EAAkG,oCAAAD,UAAA,CAAAE,UAAA,CAC5C;IACtDpC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAC,YAAA,cACJ;;;;;IAGJnC,EAAA,CAAAK,uBAAA,GAAsE;IAClEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAG,YAAA,cACJ;;;;;IAEArC,EAAA,CAAAK,uBAAA,GAA2C;IACvCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAmC,kBAAA,CAAAJ,UAAA,kBAAAA,UAAA,CAAAK,mBAAA,cACJ;;;;;IAEAvC,EAAA,CAAAK,uBAAA,GACmF;IAC/EL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAM,YAAA,cACJ;;;;;IAEAxC,EAAA,CAAAK,uBAAA,GAAuF;IACnFL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAO,aAAA,cACJ;;;;;IAEAzC,EAAA,CAAAK,uBAAA,GAA+C;IAC3CL,EAAA,CAAAC,SAAA,qBACmC;;;;;IADvBD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,YAAAgC,UAAA,CAAAQ,cAAA,CAAmC,kBACzC;;;;;IAGzB1C,EAAA,CAAAK,uBAAA,GACmF;IAC/EL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAS,MAAA,cACJ;;;;;IAEA3C,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAU,UAAA,cACJ;;;;;IAEA5C,EAAA,CAAAK,uBAAA,GAAgF;IAC5EL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAW,MAAA,cACJ;;;;;IA5CZ7C,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAwCjCL,EAvCA,CAAAkB,UAAA,IAAA4B,uEAAA,2BAAqE,IAAAC,uEAAA,2BAQC,IAAAC,uEAAA,2BAI3B,IAAAC,uEAAA,2BAKwC,IAAAC,uEAAA,2BAII,IAAAC,uEAAA,2BAIxC,IAAAC,uEAAA,2BAMoC,KAAAC,wEAAA,2BAIrC,KAAAC,wEAAA,2BAIkC;;IAKxFtD,EAAA,CAAAqB,YAAA,EAAK;;;;;IA7CarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAqD,MAAA,CAAAvC,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAoD;IAApDtB,EAAA,CAAAE,UAAA,wDAAoD;IAQpDF,EAAA,CAAAsB,SAAA,EAAqD;IAArDtB,EAAA,CAAAE,UAAA,yDAAqD;IAIrDF,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,8BAA0B;IAKpCF,EAAA,CAAAsB,SAAA,EAA4E;IAA5EtB,EAAA,CAAAE,UAAA,gFAA4E;IAIlEF,EAAA,CAAAsB,SAAA,EAAsE;IAAtEtB,EAAA,CAAAE,UAAA,0EAAsE;IAItEF,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAAE,UAAA,kCAA8B;IAMxCF,EAAA,CAAAsB,SAAA,EAA4E;IAA5EtB,EAAA,CAAAE,UAAA,gFAA4E;IAIlEF,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,iCAA6B;IAI7BF,EAAA,CAAAsB,SAAA,EAA+D;IAA/DtB,EAAA,CAAAE,UAAA,mEAA+D;;;;;IAnD1FF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAqC;IACzCD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAE2D;IACvDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAsC,wDAAA,6BAAkD;IAiDtDxD,EAAA,CAAAqB,YAAA,EAAK;;;;;IAzDoBrB,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAAE,UAAA,UAAAgC,UAAA,CAAiB;IAGlClC,EAAA,CAAAsB,SAAA,EAAkG;IAClGtB,EADA,CAAAE,UAAA,YAAAF,EAAA,CAAAgC,eAAA,IAAAyB,GAAA,EAAAvB,UAAA,kBAAAA,UAAA,CAAAwB,UAAA,EAAkG,oCAAAxB,UAAA,CAAAE,UAAA,CAC5C;IACtDpC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,UAAA,kBAAAA,UAAA,CAAAwB,UAAA,cACJ;IAE8B1D,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAsDhD/B,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,2BACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,4CACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;ADxHrB,OAAM,MAAOsC,iBAAiB;EAgB5BC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAhBR,KAAAC,YAAY,GAAG,IAAInE,OAAO,EAAQ;IAKnC,KAAAoE,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAsC,EAAE;IACnD,KAAAC,YAAY,GAAuC,EAAE;IACrD,KAAAC,kBAAkB,GAAoB,IAAI3E,OAAO,EAAU;IAO1D,KAAA4E,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEzD,KAAK,EAAE,sCAAsC;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACjE;MAAER,KAAK,EAAE,uCAAuC;MAAEQ,MAAM,EAAE;IAAS,CAAE,EACrE;MAAER,KAAK,EAAE,YAAY;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC7C;MAAER,KAAK,EAAE,8DAA8D;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAC1F;MAAER,KAAK,EAAE,wDAAwD;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrF;MAAER,KAAK,EAAE,gBAAgB;MAAEQ,MAAM,EAAE;IAAgB,CAAE,EACrD;MAAER,KAAK,EAAE,8DAA8D;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EAC3F;MAAER,KAAK,EAAE,eAAe;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAChD;MAAER,KAAK,EAAE,iDAAiD;MAAEQ,MAAM,EAAE;IAAQ,CAAE,CAC/E;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAjBjB;EAmBJW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC4D,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC1B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE3D,KAAK,CAAC;MAC9C,MAAM+D,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAE5D,KAAK,CAAC;MAE9C,IAAIgE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAAC3E,SAAS,GAAG4E,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAElE,KAAa;IACvC,IAAI,CAACkE,IAAI,IAAI,CAAClE,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACmE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAClE,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACoE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACjB,kBAAkB,CACpBkB,IAAI,CACH3F,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CACvB,CACA2F,SAAS,CAAEC,IAAY,IAAI;MAC1B,IAAI,CAACxB,gBAAgB,GAAGwB,IAAI;MAC5B,IAAI,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE,CAAC;IAC3C,CAAC,CAAC;IACJ,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,CACvD;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IACrD,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAI,CAAE,EACnC;MAAED,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC1C;IACD,IAAI,CAACC,eAAe,GAAG;MAAEF,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE;IAEnD,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAI1C,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACyC,gBAAgB;EAC9B;EAEA,IAAIzC,eAAeA,CAAC2E,GAAU;IAC5B,IAAI,CAAClC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACkC,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACxC,gBAAgB,CAACuC,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACzC,gBAAgB,CAAC0C,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACzC,gBAAgB,CAAC0C,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEApB,YAAYA,CAACmB,KAAU;IACrB,IAAI,CAAC7C,OAAO,GAAG,IAAI;IACnB,MAAMkD,IAAI,GAAGL,KAAK,CAAClB,KAAK,GAAGkB,KAAK,CAACjB,IAAI,GAAG,CAAC;IACzC,MAAMuB,QAAQ,GAAGN,KAAK,CAACjB,IAAI;IAC3B,MAAMrE,SAAS,GAAGsF,KAAK,CAACtF,SAAS;IACjC,MAAMrB,SAAS,GAAG2G,KAAK,CAAC3G,SAAS;IACjC,MAAMkH,QAAQ,GAAG,IAAI,CAACb,eAAe,EAAED,IAAI,KAAK,IAAI;IAEpD,IAAI,CAAC3C,eAAe,CACjB0D,WAAW,CACVH,IAAI,EACJC,QAAQ,EACR5F,SAAS,EACTrB,SAAS,EACT,IAAI,CAAC+D,gBAAgB,EACrBmD,QAAQ,CACT,CACA5B,SAAS,CAAC;MACT8B,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIzD,QAAQ,GAAG,CAACyD,QAAQ,EAAEvC,IAAI,IAAI,EAAE,EAAEwC,GAAG,CAAEC,OAAY,IAAI;UACzD,MAAMC,SAAS,GAAGD,OAAO,EAAEE,uBAAuB,EAAED,SAAS,IAAI,EAAE;UACnE,MAAME,oBAAoB,GAAIC,IAAY,IACxC,CAACH,SAAS,GAAG,CAAC,CAAC,EAAEI,aAAa,IAAI,EAAE,EACjCrB,MAAM,CAAEsB,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAKH,IAAI,CAAC,CACtDL,GAAG,CAAEO,IAAS,IAAKA,IAAI,CAACzF,YAAY,CAAC;UAE1C,OAAO;YACL,GAAGmF,OAAO;YACV/E,UAAU,EAAE+E,OAAO,EAAEQ,aAAa,IAAI,GAAG;YACzCzE,UAAU,EAAEiE,OAAO,EAAES,YAAY,IAAI,GAAG;YACxCjG,YAAY,EACVwF,OAAO,EAAEE,uBAAuB,EAAEQ,YAAY,IAAI,GAAG;YACvDhG,YAAY,EACVsF,OAAO,EAAEW,wBAAwB,EAAED,YAAY,IAAI,GAAG;YACxD9F,mBAAmB,EACjBoF,OAAO,EAAEY,oBAAoB,EAAEC,yBAAyB,IAAI,GAAG;YACjE/F,aAAa,EAAEmF,SAAS,GAAG,CAAC,CAAC,EAAEa,MAAM,GAAG,CAAC,CAAC,EAAEhG,aAAa;YACzDD,YAAY,EAAEsF,oBAAoB,CAAC,GAAG,CAAC;YACvCnF,MAAM,EAAEmF,oBAAoB,CAAC,GAAG,CAAC;YACjCpF,cAAc,EAAEiF,OAAO,EAAEE,uBAAuB,EAAEa,YAAY,EAC1DhG,cAAc,GACd,IAAI,GACJ,KAAK;YACTG,MAAM,EAAE8E,OAAO,EAAEE,uBAAuB,EAAEc,uBAAuB,GAC7D,UAAU,GACV;WACL;QACH,CAAC,CAAC;QAEF,IAAI,CAAC3E,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACC,YAAY,GAAGwD,QAAQ,EAAEmB,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAC5E,OAAO,GAAG,KAAK;MACtB,CAAC;MACD6E,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC7E,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEO8B,cAAcA,CAAA;IACnB,IAAI,CAACnC,eAAe,CACjBoF,eAAe,EAAE,CACjBxD,IAAI,CAAC5F,SAAS,CAAC,IAAI,CAACkE,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAE+B,QAAa,IAAI;MAC3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACvC,IAAI,EAAE;QAC7B,IAAI,CAACZ,YAAY,GAAGmD,QAAQ,CAACvC,IAAI,CAACwC,GAAG,CAAEO,IAAS,KAAM;UACpD/B,KAAK,EAAE+B,IAAI,CAACiB,WAAW;UACvBC,KAAK,EAAElB,IAAI,CAACzB;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACN;EAEAlE,kBAAkBA,CAAC6G,KAAa;IAC9B,OAAO,IAAI,CAAC7E,YAAY,CAAC8E,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACF,KAAK,KAAKA,KAAK,CAAC,EAAEjD,KAAK;EACpE;EAEOH,aAAaA,CAAA;IAClB,IAAI,CAAClC,eAAe,CACjByF,aAAa,EAAE,CACf7D,IAAI,CAAC5F,SAAS,CAAC,IAAI,CAACkE,YAAY,CAAC,CAAC,CAClC2B,SAAS,CAAE+B,QAAa,IAAI;MAC3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACvC,IAAI,EAAE;QAC7B,IAAI,CAACb,WAAW,GAAGoD,QAAQ,CAACvC,IAAI,CAACwC,GAAG,CAAEO,IAAS,KAAM;UACnD1B,IAAI,EAAE0B,IAAI,CAACiB,WAAW;UACtBC,KAAK,EAAElB,IAAI,CAACzB;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACN;EAEA+C,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvD7D,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACF,YAAY,CAAC4D,QAAQ,CAAC;EAC7B;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAAC7F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAC,mBAAmBA,CAAC9C,KAAY;IAC9B,MAAM+C,KAAK,GAAI/C,KAAK,CAACgD,MAA2B,CAACZ,KAAK;IACtD,IAAI,CAAC5E,kBAAkB,CAACiD,IAAI,CAACsC,KAAK,CAAC;EACrC;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACjG,YAAY,CAACyD,IAAI,EAAE;IACxB,IAAI,CAACzD,YAAY,CAACkG,QAAQ,EAAE;EAC9B;;;uBAnOWtG,iBAAiB,EAAA3D,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjB3G,iBAAiB;MAAA4G,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCrBtB1K,EAFR,CAAAM,cAAA,aAA8D,aACmB,aAC7C;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAM,cAAA,aAA2C,aAEb,cACW,kBAGgF;UAFlFN,EAAA,CAAA4K,gBAAA,2BAAAC,0DAAAC,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA/K,EAAA,CAAAgL,kBAAA,CAAAL,GAAA,CAAAxG,gBAAA,EAAA2G,MAAA,MAAAH,GAAA,CAAAxG,gBAAA,GAAA2G,MAAA;YAAA,OAAA9K,EAAA,CAAAc,WAAA,CAAAgK,MAAA;UAAA,EAA8B;UAAC9K,EAAA,CAAAO,UAAA,mBAAA0K,kDAAAH,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA,OAAA/K,EAAA,CAAAc,WAAA,CAAS6J,GAAA,CAAAd,mBAAA,CAAAiB,MAAA,CAA2B;UAAA,EAAC;UAA/F9K,EAAA,CAAAqB,YAAA,EAE6G;UAC7GrB,EAAA,CAAAC,SAAA,YAAgD;UAExDD,EADI,CAAAqB,YAAA,EAAO,EACL;UACNrB,EAAA,CAAAM,cAAA,sBAEyG;UAFzEN,EAAA,CAAA4K,gBAAA,2BAAAM,gEAAAJ,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA/K,EAAA,CAAAgL,kBAAA,CAAAL,GAAA,CAAAlE,eAAA,EAAAqE,MAAA,MAAAH,GAAA,CAAAlE,eAAA,GAAAqE,MAAA;YAAA,OAAA9K,EAAA,CAAAc,WAAA,CAAAgK,MAAA;UAAA,EAA6B;UAAC9K,EAAA,CAAAO,UAAA,sBAAA4K,2DAAA;YAAAnL,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA,OAAA/K,EAAA,CAAAc,WAAA,CAAY6J,GAAA,CAAApB,cAAA,EAAgB;UAAA,EAAC;UAA3FvJ,EAAA,CAAAqB,YAAA,EAEyG;UAEzGrB,EAAA,CAAAM,cAAA,kBAC0I;UADpHN,EAAA,CAAAO,UAAA,mBAAA6K,oDAAA;YAAApL,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA,OAAA/K,EAAA,CAAAc,WAAA,CAAS6J,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UAEpC3J,EAAA,CAAAM,cAAA,gBAAgD;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAACrB,EAAA,CAAAiB,MAAA,gBACpE;UAAAjB,EAAA,CAAAqB,YAAA,EAAS;UAETrB,EAAA,CAAAM,cAAA,yBAE+I;UAF/GN,EAAA,CAAA4K,gBAAA,2BAAAS,mEAAAP,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA/K,EAAA,CAAAgL,kBAAA,CAAAL,GAAA,CAAA5I,eAAA,EAAA+I,MAAA,MAAAH,GAAA,CAAA5I,eAAA,GAAA+I,MAAA;YAAA,OAAA9K,EAAA,CAAAc,WAAA,CAAAgK,MAAA;UAAA,EAA6B;UAKrE9K,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAIFrB,EAFJ,CAAAM,cAAA,eAAuB,sBAK0B;UAAzCN,EAHsD,CAAAO,UAAA,wBAAA+K,0DAAAR,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA,OAAA/K,EAAA,CAAAc,WAAA,CAAc6J,GAAA,CAAA/E,YAAA,CAAAkF,MAAA,CAAoB;UAAA,EAAC,0BAAAS,4DAAAT,MAAA;YAAA9K,EAAA,CAAAU,aAAA,CAAAqK,GAAA;YAAA,OAAA/K,EAAA,CAAAc,WAAA,CAGzE6J,GAAA,CAAA7D,eAAA,CAAAgE,MAAA,CAAuB;UAAA,EAAC;UAsGxC9K,EApGA,CAAAkB,UAAA,KAAAsK,yCAAA,0BAAgC,KAAAC,yCAAA,0BA8BgC,KAAAC,yCAAA,0BA+D1B,KAAAC,yCAAA,0BAOD;UAUjD3L,EAHQ,CAAAqB,YAAA,EAAU,EAER,EACJ;;;UAjJoBrB,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAAyK,GAAA,CAAA1E,eAAA,CAAyB,SAAA0E,GAAA,CAAAvE,IAAA,CAAc,uCAAuC;UAMzDpG,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAA4L,gBAAA,YAAAjB,GAAA,CAAAxG,gBAAA,CAA8B;UAMrDnE,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAE,UAAA,YAAAyK,GAAA,CAAArE,OAAA,CAAmB;UAACtG,EAAA,CAAA4L,gBAAA,YAAAjB,GAAA,CAAAlE,eAAA,CAA6B;UAEzDzG,EAAA,CAAAE,UAAA,mGAAkG;UAOvFF,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAyK,GAAA,CAAAlG,IAAA,CAAgB;UAACzE,EAAA,CAAA4L,gBAAA,YAAAjB,GAAA,CAAA5I,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAOpIF,EAAA,CAAAsB,SAAA,GAAkB;UAEiBtB,EAFnC,CAAAE,UAAA,UAAAyK,GAAA,CAAA3G,QAAA,CAAkB,YAAyB,YAAA2G,GAAA,CAAAzG,OAAA,CAClC,mBAAmB,iBAAAyG,GAAA,CAAA1G,YAAA,CAA8B,cAAc,oBAC/D,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}