{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ServiceDashboardRoutingModule } from './service-dashboard-routing.module';\nimport { ServiceDashboardComponent } from './service-dashboard.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport class ServiceDashboardModule {\n  static {\n    this.ɵfac = function ServiceDashboardModule_Factory(t) {\n      return new (t || ServiceDashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceDashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ServiceDashboardRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceDashboardModule, {\n    declarations: [ServiceDashboardComponent],\n    imports: [CommonModule, ServiceDashboardRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ServiceDashboardRoutingModule", "ServiceDashboardComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "TableModule", "TabViewModule", "ServiceDashboardModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-dashboard\\service-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ServiceDashboardRoutingModule } from './service-dashboard-routing.module';\r\nimport { ServiceDashboardComponent } from './service-dashboard.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ServiceDashboardComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ServiceDashboardRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ReactiveFormsModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n  ]\r\n})\r\nexport class ServiceDashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;;AAqB/C,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAb/BZ,YAAY,EACZC,6BAA6B,EAC7BE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBG,YAAY,EACZE,cAAc,EACdE,aAAa,EACbN,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc;IAAA;EAAA;;;2EAGLI,sBAAsB;IAAAC,YAAA,GAhB/BX,yBAAyB;IAAAY,OAAA,GAGzBd,YAAY,EACZC,6BAA6B,EAC7BE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBG,YAAY,EACZE,cAAc,EACdE,aAAa,EACbN,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}