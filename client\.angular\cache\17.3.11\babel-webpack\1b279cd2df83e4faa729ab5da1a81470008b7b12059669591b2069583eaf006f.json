{"ast": null, "code": "import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils, UniqueComponentId } from 'primeng/utils';\nimport * as i4 from 'primeng/focustrap';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * Galleria is an advanced content gallery component.\n * @group Components\n */\nconst _c0 = [\"mask\"];\nconst _c1 = [\"container\"];\nconst _c2 = a0 => ({\n  \"p-galleria-mask p-component-overlay p-component-overlay-enter\": true,\n  \"p-galleria-visible\": a0\n});\nconst _c3 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c4 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Galleria_div_0_div_2_p_galleriaContent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 7);\n    i0.ɵɵlistener(\"@animation.start\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    })(\"maskHide\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_maskHide_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onMaskHide());\n    })(\"activeItemChange\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@animation\", i0.ɵɵpureFunction1(8, _c4, i0.ɵɵpureFunction2(5, _c3, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible)(\"ngStyle\", ctx_r1.containerStyle);\n  }\n}\nfunction Galleria_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5, 2);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_p_galleriaContent_2_Template, 1, 10, \"p-galleriaContent\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.maskClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, ctx_r1.visible));\n    i0.ɵɵattribute(\"role\", ctx_r1.fullScreen ? \"dialog\" : \"region\")(\"aria-modal\", ctx_r1.fullScreen ? \"true\" : undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nfunction Galleria_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", null, 1);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_Template, 3, 8, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maskVisible);\n  }\n}\nfunction Galleria_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 8);\n    i0.ɵɵlistener(\"activeItemChange\", function Galleria_ng_template_1_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible);\n  }\n}\nconst _c5 = [\"closeButton\"];\nconst _c6 = (a0, a1, a2) => ({\n  \"p-galleria p-component\": true,\n  \"p-galleria-fullscreen\": a0,\n  \"p-galleria-indicator-onitem\": a1,\n  \"p-galleria-item-nav-onhover\": a2\n});\nconst _c7 = () => ({});\nfunction GalleriaContent_div_0_button_1_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-close-icon\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaContent_div_0_button_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaContent_div_0_button_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaContent_div_0_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.maskHide.emit());\n    });\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 9)(2, GalleriaContent_div_0_button_1_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.closeAriaLabel())(\"data-pc-section\", \"closebutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.closeIconTemplate);\n  }\n}\nfunction GalleriaContent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_p_galleriaThumbnails_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaThumbnails\", 14);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_onActiveIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"stopSlideShow\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_stopSlideShow_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"containerId\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"templates\", ctx_r2.galleria.templates)(\"numVisible\", ctx_r2.numVisible)(\"responsiveOptions\", ctx_r2.galleria.responsiveOptions)(\"circular\", ctx_r2.galleria.circular)(\"isVertical\", ctx_r2.isVertical())(\"contentHeight\", ctx_r2.galleria.verticalThumbnailViewPortHeight)(\"showThumbnailNavigators\", ctx_r2.galleria.showThumbnailNavigators)(\"slideShowActive\", ctx_r2.slideShowActive);\n  }\n}\nfunction GalleriaContent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_Template, 3, 4, \"button\", 2)(2, GalleriaContent_div_0_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-galleriaItem\", 5);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_Template_p_galleriaItem_onActiveIndexChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"startSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_startSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startSlideShow());\n    })(\"stopSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_stopSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GalleriaContent_div_0_p_galleriaThumbnails_5_Template, 1, 11, \"p-galleriaThumbnails\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GalleriaContent_div_0_div_6_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.galleriaClass());\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(23, _c6, ctx_r2.galleria.fullScreen, ctx_r2.galleria.showIndicatorsOnItem, ctx_r2.galleria.showItemNavigatorsOnHover && !ctx_r2.galleria.fullScreen))(\"ngStyle\", !ctx_r2.galleria.fullScreen ? ctx_r2.galleria.containerStyle : i0.ɵɵpureFunction0(27, _c7));\n    i0.ɵɵattribute(\"id\", ctx_r2.id)(\"role\", \"region\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.fullScreen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.templates && ctx_r2.galleria.headerFacet);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-live\", ctx_r2.galleria.autoPlay ? \"polite\" : \"off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"circular\", ctx_r2.galleria.circular)(\"templates\", ctx_r2.galleria.templates)(\"showIndicators\", ctx_r2.galleria.showIndicators)(\"changeItemOnIndicatorHover\", ctx_r2.galleria.changeItemOnIndicatorHover)(\"indicatorFacet\", ctx_r2.galleria.indicatorFacet)(\"captionFacet\", ctx_r2.galleria.captionFacet)(\"showItemNavigators\", ctx_r2.galleria.showItemNavigators)(\"autoPlay\", ctx_r2.galleria.autoPlay)(\"slideShowActive\", ctx_r2.slideShowActive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.showThumbnails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.templates && ctx_r2.galleria.footerFacet);\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaItemSlot_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate)(\"ngTemplateOutletContext\", ctx_r0.context);\n  }\n}\nconst _c8 = a0 => ({\n  \"p-galleria-item-prev p-galleria-item-nav p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c9 = a0 => ({\n  \"p-galleria-item-next p-galleria-item-nav p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c10 = a0 => ({\n  \"p-galleria-indicator\": true,\n  \"p-highlight\": a0\n});\nfunction GalleriaItem_button_2_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-item-prev-icon\");\n  }\n}\nfunction GalleriaItem_button_2_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_2_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 9)(2, GalleriaItem_button_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c8, ctx_r1.isNavBackwardDisabled()))(\"disabled\", ctx_r1.isNavBackwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemPreviousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemPreviousIconTemplate);\n  }\n}\nfunction GalleriaItem_button_5_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-item-next-icon\");\n  }\n}\nfunction GalleriaItem_button_5_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_5_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_5_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_5_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_5_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 9)(2, GalleriaItem_button_5_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, ctx_r1.isNavForwardDisabled()))(\"disabled\", ctx_r1.isNavForwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemNextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemNextIconTemplate);\n  }\n}\nfunction GalleriaItem_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", ctx_r1.activeItem)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_7_li_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 20);\n  }\n}\nfunction GalleriaItem_ul_7_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_ul_7_li_1_Template_li_click_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorClick(index_r5));\n    })(\"mouseenter\", function GalleriaItem_ul_7_li_1_Template_li_mouseenter_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorMouseEnter(index_r5));\n    })(\"keydown\", function GalleriaItem_ul_7_li_1_Template_li_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeyDown($event, index_r5));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_ul_7_li_1_button_1_Template, 1, 0, \"button\", 18);\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c10, ctx_r1.isIndicatorItemActive(index_r5)));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(index_r5 + 1))(\"aria-selected\", ctx_r1.activeIndex === index_r5)(\"aria-controls\", ctx_r1.id + \"_item_\" + index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.indicatorFacet);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"index\", index_r5)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 15);\n    i0.ɵɵtemplate(1, GalleriaItem_ul_7_li_1_Template, 3, 9, \"li\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.value);\n  }\n}\nconst _c11 = [\"itemsContainer\"];\nconst _c12 = a0 => ({\n  height: a0\n});\nconst _c13 = a0 => ({\n  \"p-galleria-thumbnail-prev p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c14 = (a0, a1, a2, a3) => ({\n  \"p-galleria-thumbnail-item\": true,\n  \"p-galleria-thumbnail-item-current\": a0,\n  \"p-galleria-thumbnail-item-active\": a1,\n  \"p-galleria-thumbnail-item-start\": a2,\n  \"p-galleria-thumbnail-item-end\": a3\n});\nconst _c15 = a0 => ({\n  \"p-galleria-thumbnail-next p-link\": true,\n  \"p-disabled\": a0\n});\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 10)(2, GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_2_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_2_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c13, ctx_r2.isNavBackwardDisabled()))(\"disabled\", ctx_r2.isNavBackwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaPrevButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.previousThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.previousThumbnailIconTemplate);\n  }\n}\nfunction GalleriaThumbnails_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"keydown\", function GalleriaThumbnails_div_6_Template_div_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onThumbnailKeydown($event, index_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_div_6_Template_div_click_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"touchend\", function GalleriaThumbnails_div_6_Template_div_touchend_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"keydown.enter\", function GalleriaThumbnails_div_6_Template_div_keydown_enter_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    });\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(10, _c14, ctx_r2.activeIndex === index_r5, ctx_r2.isItemActive(index_r5), ctx_r2.firstItemAciveIndex() === index_r5, ctx_r2.lastItemActiveIndex() === index_r5));\n    i0.ɵɵattribute(\"aria-selected\", ctx_r2.activeIndex === index_r5)(\"aria-controls\", ctx_r2.containerId + \"_item_\" + index_r5)(\"data-pc-section\", \"thumbnailitem\")(\"data-p-active\", ctx_r2.activeIndex === index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"tabindex\", ctx_r2.activeIndex === index_r5 ? 0 : -1)(\"aria-current\", ctx_r2.activeIndex === index_r5 ? \"page\" : undefined)(\"aria-label\", ctx_r2.ariaPageLabel(index_r5 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", item_r6)(\"templates\", ctx_r2.templates);\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 15)(2, GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_7_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_7_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_7_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navForward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_7_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c15, ctx_r2.isNavForwardDisabled()))(\"disabled\", ctx_r2.isNavForwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaNextButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.nextThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.nextThumbnailIconTemplate);\n  }\n}\nlet Galleria = /*#__PURE__*/(() => {\n  class Galleria {\n    document;\n    platformId;\n    element;\n    cd;\n    config;\n    /**\n     * Index of the first item.\n     * @group Props\n     */\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n      this._activeIndex = activeIndex;\n    }\n    /**\n     * Whether to display the component on fullscreen.\n     * @group Props\n     */\n    fullScreen = false;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    id;\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    value;\n    /**\n     * Number of items per page.\n     * @group Props\n     */\n    numVisible = 3;\n    /**\n     * An array of options for responsive design.\n     * @see {GalleriaResponsiveOptions}\n     * @group Props\n     */\n    responsiveOptions;\n    /**\n     * Whether to display navigation buttons in item section.\n     * @group Props\n     */\n    showItemNavigators = false;\n    /**\n     * Whether to display navigation buttons in thumbnail container.\n     * @group Props\n     */\n    showThumbnailNavigators = true;\n    /**\n     * Whether to display navigation buttons on item hover.\n     * @group Props\n     */\n    showItemNavigatorsOnHover = false;\n    /**\n     * When enabled, item is changed on indicator hover.\n     * @group Props\n     */\n    changeItemOnIndicatorHover = false;\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    circular = false;\n    /**\n     * Items are displayed with a slideshow in autoPlay mode.\n     * @group Props\n     */\n    autoPlay = false;\n    /**\n     * When enabled, autorun should stop by click.\n     * @group Props\n     */\n    shouldStopAutoplayByClick = true;\n    /**\n     * Time in milliseconds to scroll items.\n     * @group Props\n     */\n    transitionInterval = 4000;\n    /**\n     * Whether to display thumbnail container.\n     * @group Props\n     */\n    showThumbnails = true;\n    /**\n     * Position of thumbnails.\n     * @group Props\n     */\n    thumbnailsPosition = 'bottom';\n    /**\n     * Height of the viewport in vertical thumbnail.\n     * @group Props\n     */\n    verticalThumbnailViewPortHeight = '300px';\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    showIndicators = false;\n    /**\n     * When enabled, indicator container is displayed on item container.\n     * @group Props\n     */\n    showIndicatorsOnItem = false;\n    /**\n     * Position of indicators.\n     * @group Props\n     */\n    indicatorsPosition = 'bottom';\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Style class of the mask on fullscreen mode.\n     * @group Props\n     */\n    maskClass;\n    /**\n     * Style class of the component on fullscreen mode. Otherwise, the 'class' property can be used.\n     * @group Props\n     */\n    containerClass;\n    /**\n     * Inline style of the component on fullscreen mode. Otherwise, the 'style' property can be used.\n     * @group Props\n     */\n    containerStyle;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the mask on fullscreen mode.\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(visible) {\n      this._visible = visible;\n      if (this._visible && !this.maskVisible) {\n        this.maskVisible = true;\n      }\n    }\n    /**\n     * Callback to invoke on active index change.\n     * @param {number} number - Active index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    /**\n     * Callback to invoke on visiblity change.\n     * @param {boolean} boolean - Visible value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    mask;\n    container;\n    templates;\n    _visible = false;\n    _activeIndex = 0;\n    headerFacet;\n    footerFacet;\n    indicatorFacet;\n    captionFacet;\n    closeIconTemplate;\n    previousThumbnailIconTemplate;\n    nextThumbnailIconTemplate;\n    itemPreviousIconTemplate;\n    itemNextIconTemplate;\n    maskVisible = false;\n    numVisibleLimit = 0;\n    constructor(document, platformId, element, cd, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.element = element;\n      this.cd = cd;\n      this.config = config;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerFacet = item.template;\n            break;\n          case 'footer':\n            this.footerFacet = item.template;\n            break;\n          case 'indicator':\n            this.indicatorFacet = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          case 'itemnexticon':\n            this.itemNextIconTemplate = item.template;\n            break;\n          case 'itempreviousicon':\n            this.itemPreviousIconTemplate = item.template;\n            break;\n          case 'previousthumbnailicon':\n            this.previousThumbnailIconTemplate = item.template;\n            break;\n          case 'nextthumbnailicon':\n            this.nextThumbnailIconTemplate = item.template;\n            break;\n          case 'caption':\n            this.captionFacet = item.template;\n            break;\n        }\n      });\n    }\n    ngOnChanges(simpleChanges) {\n      if (simpleChanges.value && simpleChanges.value.currentValue?.length < this.numVisible) {\n        this.numVisibleLimit = simpleChanges.value.currentValue.length;\n      } else {\n        this.numVisibleLimit = 0;\n      }\n    }\n    onMaskHide() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n    }\n    onActiveItemChange(index) {\n      if (this.activeIndex !== index) {\n        this.activeIndex = index;\n        this.activeIndexChange.emit(index);\n      }\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.enableModality();\n          setTimeout(() => {\n            DomHandler.focus(DomHandler.findSingle(this.container.nativeElement, '[data-pc-section=\"closebutton\"]'));\n          }, 25);\n          break;\n        case 'void':\n          DomHandler.addClass(this.mask?.nativeElement, 'p-component-overlay-leave');\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          this.disableModality();\n          break;\n      }\n    }\n    enableModality() {\n      DomHandler.blockBodyScroll();\n      this.cd.markForCheck();\n      if (this.mask) {\n        ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n      }\n    }\n    disableModality() {\n      DomHandler.unblockBodyScroll();\n      this.maskVisible = false;\n      this.cd.markForCheck();\n      if (this.mask) {\n        ZIndexUtils.clear(this.mask.nativeElement);\n      }\n    }\n    ngOnDestroy() {\n      if (this.fullScreen) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (this.mask) {\n        this.disableModality();\n      }\n    }\n    static ɵfac = function Galleria_Factory(t) {\n      return new (t || Galleria)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Galleria,\n      selectors: [[\"p-galleria\"]],\n      contentQueries: function Galleria_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Galleria_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mask = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.container = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        activeIndex: \"activeIndex\",\n        fullScreen: \"fullScreen\",\n        id: \"id\",\n        value: \"value\",\n        numVisible: \"numVisible\",\n        responsiveOptions: \"responsiveOptions\",\n        showItemNavigators: \"showItemNavigators\",\n        showThumbnailNavigators: \"showThumbnailNavigators\",\n        showItemNavigatorsOnHover: \"showItemNavigatorsOnHover\",\n        changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n        circular: \"circular\",\n        autoPlay: \"autoPlay\",\n        shouldStopAutoplayByClick: \"shouldStopAutoplayByClick\",\n        transitionInterval: \"transitionInterval\",\n        showThumbnails: \"showThumbnails\",\n        thumbnailsPosition: \"thumbnailsPosition\",\n        verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\",\n        showIndicators: \"showIndicators\",\n        showIndicatorsOnItem: \"showIndicatorsOnItem\",\n        indicatorsPosition: \"indicatorsPosition\",\n        baseZIndex: \"baseZIndex\",\n        maskClass: \"maskClass\",\n        containerClass: \"containerClass\",\n        containerStyle: \"containerStyle\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        visible: \"visible\"\n      },\n      outputs: {\n        activeIndexChange: \"activeIndexChange\",\n        visibleChange: \"visibleChange\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"windowed\", \"\"], [\"container\", \"\"], [\"mask\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"maskHide\", \"activeItemChange\", 4, \"ngIf\"], [3, \"maskHide\", \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\"], [3, \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\"]],\n      template: function Galleria_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, Galleria_div_0_Template, 3, 1, \"div\", 3)(1, Galleria_ng_template_1_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const windowed_r4 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.fullScreen)(\"ngIfElse\", windowed_r4);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgStyle, GalleriaContent],\n      styles: [\"@layer primeng{.p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('animation', [transition('void => visible', [style({\n          transform: 'scale(0.7)',\n          opacity: 0\n        }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n          transform: 'scale(0.7)',\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Galleria;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GalleriaContent = /*#__PURE__*/(() => {\n  class GalleriaContent {\n    galleria;\n    cd;\n    differs;\n    config;\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n      this._activeIndex = activeIndex;\n    }\n    value = [];\n    numVisible;\n    maskHide = new EventEmitter();\n    activeItemChange = new EventEmitter();\n    closeButton;\n    id;\n    _activeIndex = 0;\n    slideShowActive = true;\n    interval;\n    styleClass;\n    differ;\n    constructor(galleria, cd, differs, config) {\n      this.galleria = galleria;\n      this.cd = cd;\n      this.differs = differs;\n      this.config = config;\n      this.id = this.galleria.id || UniqueComponentId();\n      this.differ = this.differs.find(this.galleria).create();\n    }\n    ngDoCheck() {\n      if (isPlatformBrowser(this.galleria.platformId)) {\n        const changes = this.differ.diff(this.galleria);\n        if (changes && changes.forEachItem.length > 0) {\n          // Because we change the properties of the parent component,\n          // and the children take our entity from the injector.\n          // We can tell the children to redraw themselves when we change the properties of the parent component.\n          // Since we have an onPush strategy\n          this.cd.markForCheck();\n        }\n      }\n    }\n    galleriaClass() {\n      const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n      const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n      return (this.galleria.containerClass ? this.galleria.containerClass + ' ' : '') + (thumbnailsPosClass ? thumbnailsPosClass + ' ' : '') + (indicatorPosClass ? indicatorPosClass + ' ' : '');\n    }\n    startSlideShow() {\n      if (isPlatformBrowser(this.galleria.platformId)) {\n        this.interval = setInterval(() => {\n          let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n          this.onActiveIndexChange(activeIndex);\n          this.activeIndex = activeIndex;\n        }, this.galleria.transitionInterval);\n        this.slideShowActive = true;\n      }\n    }\n    stopSlideShow() {\n      if (this.galleria.autoPlay && !this.galleria.shouldStopAutoplayByClick) {\n        return;\n      }\n      if (this.interval) {\n        clearInterval(this.interval);\n      }\n      this.slideShowActive = false;\n    }\n    getPositionClass(preClassName, position) {\n      const positions = ['top', 'left', 'bottom', 'right'];\n      const pos = positions.find(item => item === position);\n      return pos ? `${preClassName}-${pos}` : '';\n    }\n    isVertical() {\n      return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n    }\n    onActiveIndexChange(index) {\n      if (this.activeIndex !== index) {\n        this.activeIndex = index;\n        this.activeItemChange.emit(this.activeIndex);\n      }\n    }\n    closeAriaLabel() {\n      return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n    static ɵfac = function GalleriaContent_Factory(t) {\n      return new (t || GalleriaContent)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: GalleriaContent,\n      selectors: [[\"p-galleriaContent\"]],\n      viewQuery: function GalleriaContent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.closeButton = _t.first);\n        }\n      },\n      inputs: {\n        activeIndex: \"activeIndex\",\n        value: \"value\",\n        numVisible: \"numVisible\"\n      },\n      outputs: {\n        maskHide: \"maskHide\",\n        activeItemChange: \"activeItemChange\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\"], [\"type\", \"button\", \"class\", \"p-galleria-close p-link\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-header\", 4, \"ngIf\"], [1, \"p-galleria-content\"], [3, \"onActiveIndexChange\", \"startSlideShow\", \"stopSlideShow\", \"id\", \"value\", \"activeIndex\", \"circular\", \"templates\", \"showIndicators\", \"changeItemOnIndicatorHover\", \"indicatorFacet\", \"captionFacet\", \"showItemNavigators\", \"autoPlay\", \"slideShowActive\"], [3, \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\", \"onActiveIndexChange\", \"stopSlideShow\", 4, \"ngIf\"], [\"class\", \"p-galleria-footer\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-galleria-close\", \"p-link\", 3, \"click\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [1, \"p-galleria-header\"], [\"type\", \"header\", 3, \"templates\"], [3, \"onActiveIndexChange\", \"stopSlideShow\", \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\"], [1, \"p-galleria-footer\"], [\"type\", \"footer\", 3, \"templates\"]],\n      template: function GalleriaContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, GalleriaContent_div_0_Template, 7, 28, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.value && ctx.value.length > 0);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon, i4.FocusTrap, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return GalleriaContent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GalleriaItemSlot = /*#__PURE__*/(() => {\n  class GalleriaItemSlot {\n    templates;\n    index;\n    get item() {\n      return this._item;\n    }\n    set item(item) {\n      this._item = item;\n      if (this.templates) {\n        this.templates.forEach(item => {\n          if (item.getType() === this.type) {\n            switch (this.type) {\n              case 'item':\n              case 'caption':\n              case 'thumbnail':\n                this.context = {\n                  $implicit: this.item\n                };\n                this.contentTemplate = item.template;\n                break;\n            }\n          }\n        });\n      }\n    }\n    type;\n    contentTemplate;\n    context;\n    _item;\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        if (item.getType() === this.type) {\n          switch (this.type) {\n            case 'item':\n            case 'caption':\n            case 'thumbnail':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n            case 'indicator':\n              this.context = {\n                $implicit: this.index\n              };\n              this.contentTemplate = item.template;\n              break;\n            default:\n              this.context = {};\n              this.contentTemplate = item.template;\n              break;\n          }\n        }\n      });\n    }\n    static ɵfac = function GalleriaItemSlot_Factory(t) {\n      return new (t || GalleriaItemSlot)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: GalleriaItemSlot,\n      selectors: [[\"p-galleriaItemSlot\"]],\n      inputs: {\n        templates: \"templates\",\n        index: \"index\",\n        item: \"item\",\n        type: \"type\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function GalleriaItemSlot_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, GalleriaItemSlot_ng_container_0_Template, 2, 2, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate);\n        }\n      },\n      dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return GalleriaItemSlot;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GalleriaItem = /*#__PURE__*/(() => {\n  class GalleriaItem {\n    galleria;\n    id;\n    circular = false;\n    value;\n    showItemNavigators = false;\n    showIndicators = true;\n    slideShowActive = true;\n    changeItemOnIndicatorHover = true;\n    autoPlay = false;\n    templates;\n    indicatorFacet;\n    captionFacet;\n    startSlideShow = new EventEmitter();\n    stopSlideShow = new EventEmitter();\n    onActiveIndexChange = new EventEmitter();\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n      this._activeIndex = activeIndex;\n    }\n    get activeItem() {\n      return this.value && this.value[this._activeIndex];\n    }\n    _activeIndex = 0;\n    constructor(galleria) {\n      this.galleria = galleria;\n    }\n    ngOnChanges({\n      autoPlay\n    }) {\n      if (autoPlay?.currentValue) {\n        this.startSlideShow.emit();\n      }\n      if (autoPlay && autoPlay.currentValue === false) {\n        this.stopTheSlideShow();\n      }\n    }\n    next() {\n      let nextItemIndex = this.activeIndex + 1;\n      let activeIndex = this.circular && this.value.length - 1 === this.activeIndex ? 0 : nextItemIndex;\n      this.onActiveIndexChange.emit(activeIndex);\n    }\n    prev() {\n      let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n      let activeIndex = this.circular && this.activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n      this.onActiveIndexChange.emit(activeIndex);\n    }\n    stopTheSlideShow() {\n      if (this.slideShowActive && this.stopSlideShow) {\n        this.stopSlideShow.emit();\n      }\n    }\n    navForward(e) {\n      this.stopTheSlideShow();\n      this.next();\n      if (e && e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    navBackward(e) {\n      this.stopTheSlideShow();\n      this.prev();\n      if (e && e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    onIndicatorClick(index) {\n      this.stopTheSlideShow();\n      this.onActiveIndexChange.emit(index);\n    }\n    onIndicatorMouseEnter(index) {\n      if (this.changeItemOnIndicatorHover) {\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n      }\n    }\n    onIndicatorKeyDown(event, index) {\n      switch (event.code) {\n        case 'Enter':\n        case 'Space':\n          this.stopTheSlideShow();\n          this.onActiveIndexChange.emit(index);\n          event.preventDefault();\n          break;\n        case 'ArrowDown':\n        case 'ArrowUp':\n          event.preventDefault();\n          break;\n        default:\n          break;\n      }\n    }\n    isNavForwardDisabled() {\n      return !this.circular && this.activeIndex === this.value.length - 1;\n    }\n    isNavBackwardDisabled() {\n      return !this.circular && this.activeIndex === 0;\n    }\n    isIndicatorItemActive(index) {\n      return this.activeIndex === index;\n    }\n    ariaSlideLabel() {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slide : undefined;\n    }\n    ariaSlideNumber(value) {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n    ariaPageLabel(value) {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    static ɵfac = function GalleriaItem_Factory(t) {\n      return new (t || GalleriaItem)(i0.ɵɵdirectiveInject(Galleria));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: GalleriaItem,\n      selectors: [[\"p-galleriaItem\"]],\n      inputs: {\n        id: \"id\",\n        circular: \"circular\",\n        value: \"value\",\n        showItemNavigators: \"showItemNavigators\",\n        showIndicators: \"showIndicators\",\n        slideShowActive: \"slideShowActive\",\n        changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n        autoPlay: \"autoPlay\",\n        templates: \"templates\",\n        indicatorFacet: \"indicatorFacet\",\n        captionFacet: \"captionFacet\",\n        activeIndex: \"activeIndex\"\n      },\n      outputs: {\n        startSlideShow: \"startSlideShow\",\n        stopSlideShow: \"stopSlideShow\",\n        onActiveIndexChange: \"onActiveIndexChange\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 8,\n      vars: 11,\n      consts: [[1, \"p-galleria-item-wrapper\"], [1, \"p-galleria-item-container\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"role\", \"group\", 3, \"id\"], [\"type\", \"item\", 1, \"p-galleria-item\", 3, \"item\", \"templates\"], [\"type\", \"button\", \"pRipple\", \"\", \"role\", \"navigation\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-caption\", 4, \"ngIf\"], [\"class\", \"p-galleria-indicators p-reset\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [\"type\", \"button\", \"pRipple\", \"\", \"role\", \"navigation\", 3, \"click\", \"ngClass\", \"disabled\"], [1, \"p-galleria-caption\"], [\"type\", \"caption\", 3, \"item\", \"templates\"], [1, \"p-galleria-indicators\", \"p-reset\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"mouseenter\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"tabindex\", \"0\", 3, \"click\", \"mouseenter\", \"keydown\", \"ngClass\"], [\"type\", \"button\", \"tabIndex\", \"-1\", \"class\", \"p-link\", 4, \"ngIf\"], [\"type\", \"indicator\", 3, \"index\", \"templates\"], [\"type\", \"button\", \"tabIndex\", \"-1\", 1, \"p-link\"]],\n      template: function GalleriaItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, GalleriaItem_button_2_Template, 3, 6, \"button\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-galleriaItemSlot\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, GalleriaItem_button_5_Template, 3, 6, \"button\", 5)(6, GalleriaItem_div_6_Template, 2, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, GalleriaItem_ul_7_Template, 2, 1, \"ul\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"width\", \"100%\");\n          i0.ɵɵproperty(\"id\", ctx.id + \"_item_\" + ctx.activeIndex);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaSlideNumber(ctx.activeIndex + 1))(\"aria-roledescription\", ctx.ariaSlideLabel());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"item\", ctx.activeItem)(\"templates\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.captionFacet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, GalleriaItemSlot],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return GalleriaItem;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GalleriaThumbnails = /*#__PURE__*/(() => {\n  class GalleriaThumbnails {\n    galleria;\n    document;\n    platformId;\n    renderer;\n    cd;\n    containerId;\n    value;\n    isVertical = false;\n    slideShowActive = false;\n    circular = false;\n    responsiveOptions;\n    contentHeight = '300px';\n    showThumbnailNavigators = true;\n    templates;\n    onActiveIndexChange = new EventEmitter();\n    stopSlideShow = new EventEmitter();\n    itemsContainer;\n    get numVisible() {\n      return this._numVisible;\n    }\n    set numVisible(numVisible) {\n      this._numVisible = numVisible;\n      this._oldNumVisible = this.d_numVisible;\n      this.d_numVisible = numVisible;\n    }\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n      this._oldactiveIndex = this._activeIndex;\n      this._activeIndex = activeIndex;\n    }\n    index;\n    startPos = null;\n    thumbnailsStyle = null;\n    sortedResponsiveOptions = null;\n    totalShiftedItems = 0;\n    page = 0;\n    documentResizeListener;\n    _numVisible = 0;\n    d_numVisible = 0;\n    _oldNumVisible = 0;\n    _activeIndex = 0;\n    _oldactiveIndex = 0;\n    constructor(galleria, document, platformId, renderer, cd) {\n      this.galleria = galleria;\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.cd = cd;\n    }\n    ngOnInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.createStyle();\n        if (this.responsiveOptions) {\n          this.bindDocumentListeners();\n        }\n      }\n    }\n    ngAfterContentChecked() {\n      let totalShiftedItems = this.totalShiftedItems;\n      if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n        if (this._activeIndex <= this.getMedianItemIndex()) {\n          totalShiftedItems = 0;\n        } else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n          totalShiftedItems = this.d_numVisible - this.value.length;\n        } else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n          totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n        } else {\n          totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n        }\n        if (totalShiftedItems !== this.totalShiftedItems) {\n          this.totalShiftedItems = totalShiftedItems;\n        }\n        if (this.itemsContainer && this.itemsContainer.nativeElement) {\n          this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n        }\n        if (this._oldactiveIndex !== this._activeIndex) {\n          DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n          this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n        this._oldactiveIndex = this._activeIndex;\n        this._oldNumVisible = this.d_numVisible;\n      }\n    }\n    ngAfterViewInit() {\n      if (platformBrowser(this.platformId)) {\n        this.calculatePosition();\n      }\n    }\n    createStyle() {\n      if (!this.thumbnailsStyle) {\n        this.thumbnailsStyle = this.document.createElement('style');\n        this.document.body.appendChild(this.thumbnailsStyle);\n      }\n      let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n      if (this.responsiveOptions) {\n        this.sortedResponsiveOptions = [...this.responsiveOptions];\n        this.sortedResponsiveOptions.sort((data1, data2) => {\n          const value1 = data1.breakpoint;\n          const value2 = data2.breakpoint;\n          let result = null;\n          if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n            numeric: true\n          });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n          return -1 * result;\n        });\n        for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n          let res = this.sortedResponsiveOptions[i];\n          innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n        }\n      }\n      this.thumbnailsStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.itemsContainer && this.sortedResponsiveOptions) {\n          let windowWidth = window.innerWidth;\n          let matchedResponsiveData = {\n            numVisible: this._numVisible\n          };\n          for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n            let res = this.sortedResponsiveOptions[i];\n            if (parseInt(res.breakpoint, 10) >= windowWidth) {\n              matchedResponsiveData = res;\n            }\n          }\n          if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n            this.d_numVisible = matchedResponsiveData.numVisible;\n            this.cd.markForCheck();\n          }\n        }\n      }\n    }\n    getTabIndex(index) {\n      return this.isItemActive(index) ? 0 : null;\n    }\n    navForward(e) {\n      this.stopTheSlideShow();\n      let nextItemIndex = this._activeIndex + 1;\n      if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n        this.step(-1);\n      }\n      let activeIndex = this.circular && this.value.length - 1 === this._activeIndex ? 0 : nextItemIndex;\n      this.onActiveIndexChange.emit(activeIndex);\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    navBackward(e) {\n      this.stopTheSlideShow();\n      let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n      let diff = prevItemIndex + this.totalShiftedItems;\n      if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n        this.step(1);\n      }\n      let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n      this.onActiveIndexChange.emit(activeIndex);\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    onItemClick(index) {\n      this.stopTheSlideShow();\n      let selectedItemIndex = index;\n      if (selectedItemIndex !== this._activeIndex) {\n        const diff = selectedItemIndex + this.totalShiftedItems;\n        let dir = 0;\n        if (selectedItemIndex < this._activeIndex) {\n          dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n          if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n            this.step(dir);\n          }\n        } else {\n          dir = this.getMedianItemIndex() - diff;\n          if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n            this.step(dir);\n          }\n        }\n        this.activeIndex = selectedItemIndex;\n        this.onActiveIndexChange.emit(this.activeIndex);\n      }\n    }\n    onThumbnailKeydown(event, index) {\n      if (event.code === 'Enter' || event.code === 'Space') {\n        this.onItemClick(index);\n        event.preventDefault();\n      }\n      switch (event.code) {\n        case 'ArrowRight':\n          this.onRightKey();\n          break;\n        case 'ArrowLeft':\n          this.onLeftKey();\n          break;\n        case 'Home':\n          this.onHomeKey();\n          event.preventDefault();\n          break;\n        case 'End':\n          this.onEndKey();\n          event.preventDefault();\n          break;\n        case 'ArrowUp':\n        case 'ArrowDown':\n          event.preventDefault();\n          break;\n        case 'Tab':\n          this.onTabKey();\n          break;\n        default:\n          break;\n      }\n    }\n    onRightKey() {\n      const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n    onLeftKey() {\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n    onHomeKey() {\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, 0);\n    }\n    onEndKey() {\n      const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n      const activeIndex = this.findFocusedIndicatorIndex();\n      this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n    onTabKey() {\n      const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n      const highlightedIndex = indicators.findIndex(ind => DomHandler.getAttribute(ind, 'data-p-active') === true);\n      const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[tabindex=\"0\"]');\n      const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n      indicators[activeIndex].children[0].tabIndex = '-1';\n      indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n    findFocusedIndicatorIndex() {\n      const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n      const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"] > [tabindex=\"0\"]');\n      return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    }\n    changedFocusedIndicator(prevInd, nextInd) {\n      const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n      indicators[prevInd].children[0].tabIndex = '-1';\n      indicators[nextInd].children[0].tabIndex = '0';\n      indicators[nextInd].children[0].focus();\n    }\n    step(dir) {\n      let totalShiftedItems = this.totalShiftedItems + dir;\n      if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > this.value.length - 1) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      } else if (dir > 0 && totalShiftedItems > 0) {\n        totalShiftedItems = 0;\n      }\n      if (this.circular) {\n        if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n          totalShiftedItems = 0;\n        } else if (dir > 0 && this._activeIndex === 0) {\n          totalShiftedItems = this.d_numVisible - this.value.length;\n        }\n      }\n      if (this.itemsContainer) {\n        DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n        this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n        this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n      }\n      this.totalShiftedItems = totalShiftedItems;\n    }\n    stopTheSlideShow() {\n      if (this.slideShowActive && this.stopSlideShow) {\n        this.stopSlideShow.emit();\n      }\n    }\n    changePageOnTouch(e, diff) {\n      if (diff < 0) {\n        // left\n        this.navForward(e);\n      } else {\n        // right\n        this.navBackward(e);\n      }\n    }\n    getTotalPageNumber() {\n      return this.value.length > this.d_numVisible ? this.value.length - this.d_numVisible + 1 : 0;\n    }\n    getMedianItemIndex() {\n      let index = Math.floor(this.d_numVisible / 2);\n      return this.d_numVisible % 2 ? index : index - 1;\n    }\n    onTransitionEnd() {\n      if (this.itemsContainer && this.itemsContainer.nativeElement) {\n        DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n        this.itemsContainer.nativeElement.style.transition = '';\n      }\n    }\n    onTouchEnd(e) {\n      let touchobj = e.changedTouches[0];\n      if (this.isVertical) {\n        this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n      } else {\n        this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n      }\n    }\n    onTouchMove(e) {\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n    onTouchStart(e) {\n      let touchobj = e.changedTouches[0];\n      this.startPos = {\n        x: touchobj.pageX,\n        y: touchobj.pageY\n      };\n    }\n    isNavBackwardDisabled() {\n      return !this.circular && this._activeIndex === 0 || this.value.length <= this.d_numVisible;\n    }\n    isNavForwardDisabled() {\n      return !this.circular && this._activeIndex === this.value.length - 1 || this.value.length <= this.d_numVisible;\n    }\n    firstItemAciveIndex() {\n      return this.totalShiftedItems * -1;\n    }\n    lastItemActiveIndex() {\n      return this.firstItemAciveIndex() + this.d_numVisible - 1;\n    }\n    isItemActive(index) {\n      return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n    }\n    bindDocumentListeners() {\n      if (isPlatformBrowser(this.platformId)) {\n        const window = this.document.defaultView || 'window';\n        this.documentResizeListener = this.renderer.listen(window, 'resize', () => {\n          this.calculatePosition();\n        });\n      }\n    }\n    unbindDocumentListeners() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    ngOnDestroy() {\n      if (this.responsiveOptions) {\n        this.unbindDocumentListeners();\n      }\n      if (this.thumbnailsStyle) {\n        this.thumbnailsStyle.parentNode?.removeChild(this.thumbnailsStyle);\n      }\n    }\n    ariaPrevButtonLabel() {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.prevPageLabel : undefined;\n    }\n    ariaNextButtonLabel() {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.nextPageLabel : undefined;\n    }\n    ariaPageLabel(value) {\n      return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    static ɵfac = function GalleriaThumbnails_Factory(t) {\n      return new (t || GalleriaThumbnails)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: GalleriaThumbnails,\n      selectors: [[\"p-galleriaThumbnails\"]],\n      viewQuery: function GalleriaThumbnails_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c11, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n        }\n      },\n      inputs: {\n        containerId: \"containerId\",\n        value: \"value\",\n        isVertical: \"isVertical\",\n        slideShowActive: \"slideShowActive\",\n        circular: \"circular\",\n        responsiveOptions: \"responsiveOptions\",\n        contentHeight: \"contentHeight\",\n        showThumbnailNavigators: \"showThumbnailNavigators\",\n        templates: \"templates\",\n        numVisible: \"numVisible\",\n        activeIndex: \"activeIndex\"\n      },\n      outputs: {\n        onActiveIndexChange: \"onActiveIndexChange\",\n        stopSlideShow: \"stopSlideShow\"\n      },\n      decls: 8,\n      vars: 6,\n      consts: [[\"itemsContainer\", \"\"], [1, \"p-galleria-thumbnail-wrapper\"], [1, \"p-galleria-thumbnail-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-galleria-thumbnail-items-container\", 3, \"ngStyle\"], [\"role\", \"tablist\", 1, \"p-galleria-thumbnail-items\", 3, \"transitionend\", \"touchstart\", \"touchmove\"], [3, \"ngClass\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"keydown\", \"ngClass\"], [1, \"p-galleria-thumbnail-item-content\", 3, \"click\", \"touchend\", \"keydown.enter\"], [\"type\", \"thumbnail\", 3, \"item\", \"templates\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n      template: function GalleriaThumbnails_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, GalleriaThumbnails_button_2_Template, 3, 7, \"button\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5, 0);\n          i0.ɵɵlistener(\"transitionend\", function GalleriaThumbnails_Template_div_transitionend_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTransitionEnd());\n          })(\"touchstart\", function GalleriaThumbnails_Template_div_touchstart_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function GalleriaThumbnails_Template_div_touchmove_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          });\n          i0.ɵɵtemplate(6, GalleriaThumbnails_div_6_Template, 3, 15, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, GalleriaThumbnails_button_7_Template, 3, 7, \"button\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c12, ctx.isVertical ? ctx.contentHeight : \"\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.value);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, GalleriaItemSlot],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return GalleriaThumbnails;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GalleriaModule = /*#__PURE__*/(() => {\n  class GalleriaModule {\n    static ɵfac = function GalleriaModule_Factory(t) {\n      return new (t || GalleriaModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: GalleriaModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule, CommonModule, SharedModule]\n    });\n  }\n  return GalleriaModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Galleria, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaThumbnails };\n//# sourceMappingURL=primeng-galleria.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}