{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, catchError, shareReplay } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/dialog\";\nconst _c0 = [\"TypeSelect\"];\nconst _c1 = () => ({\n  width: \"38rem\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction SalesCallRelatedItemsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 25)(2, \"div\", 26);\n    i0.ɵɵtext(3, \"Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 28)(6, \"div\", 26);\n    i0.ɵɵtext(7, \"Type \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 26);\n    i0.ɵɵtext(11, \"Responsible\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"th\", 30);\n    i0.ɵɵtext(13, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 30)(8, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_ng_template_8_Template_button_click_8_listener($event) {\n      const related_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(related_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const related_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r2 == null ? null : related_r2.activity == null ? null : related_r2.activity.subject) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getLabelFromDropdown(\"activityDocumentType\", related_r2 == null ? null : related_r2.acitivity_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r2 == null ? null : related_r2.btp_role_code) || \"-\", \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \"No related items found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \"Loading related items data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Add Reference\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallRelatedItemsComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallRelatedItemsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_div_23_div_1_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitted && ctx_r2.f[\"acitivity_type_code\"].errors && ctx_r2.f[\"acitivity_type_code\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_32_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.subject, \"\");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallRelatedItemsComponent_ng_template_32_span_2_Template, 2, 1, \"span\", 34);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.activity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.subject);\n  }\n}\nexport class SalesCallRelatedItemsComponent {\n  constructor(activitiesservice, formBuilder, messageservice, confirmationservice) {\n    this.activitiesservice = activitiesservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.relateditemsdetails = null;\n    this.activity_id = '';\n    this.addDialogVisible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.saving = false;\n    this.ItemDataLoading = false;\n    this.ItemInput$ = new Subject();\n    this.dropdowns = {\n      activityDocumentType: []\n    };\n    this.RelatedItemsForm = this.formBuilder.group({\n      activity_id: [''],\n      acitivity_type_code: ['', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.loadItemDataOnTypeChange();\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOCUMENT_TYPE');\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response?.activity_id;\n        this.relateditemsdetails = response?.follow_up_and_related_items;\n      }\n    });\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadItemDataOnTypeChange() {\n    this.ItemData$ = this.RelatedItemsForm.get('acitivity_type_code').valueChanges.pipe(tap(() => {\n      this.RelatedItemsForm.get('activity_id')?.reset();\n      this.ItemInput$.next(''); // Clear the search term\n      if (this.TypeSelect) {\n        this.TypeSelect.clearModel(); // Clear the search input\n      }\n    }), switchMap(type => {\n      if (!type) return of([]);\n      return this.ItemInput$.pipe(distinctUntilChanged(), tap(() => this.ItemDataLoading = true), switchMap(term => {\n        const params = this.getParamsByRole(type, term);\n        if (!params) return of([]);\n        return this.activitiesservice.getActivityCodeWise(params).pipe(map(res => res || []), catchError(() => {\n          this.ItemDataLoading = false;\n          return of([]);\n        }), tap(() => this.ItemDataLoading = false));\n      }));\n    }), shareReplay(1));\n  }\n  getParamsByRole(type, term) {\n    if (!type) return null;\n    const filters = {\n      'filters[$or][0][subject][$containsi]': term,\n      'filters[$or][1][activity_id][$containsi]': term\n    };\n    switch (type) {\n      case '0006':\n        return {\n          'filters[document_type][$eq]': '0006',\n          ...filters\n        };\n      case '0001':\n        return {\n          'filters[document_type][$eq]': '0001',\n          ...filters\n        };\n      case '0004':\n        return {\n          'filters[document_type][$eq]': '0004',\n          ...filters\n        };\n      case 'lead':\n      case 'opportunity':\n      case '0002':\n        return {\n          'filters[document_type][$eq]': '0002',\n          ...filters\n        };\n      default:\n        return null;\n    }\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.RelatedItemsForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.RelatedItemsForm.value\n      };\n      const data = {\n        activity_id: value?.activity_id,\n        acitivity_type_code: value?.acitivity_type_code\n      };\n      _this.activitiesservice.createRelatedItem(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.RelatedItemsForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Related Item Added successFully!'\n          });\n          _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: res => {\n          _this.saving = false;\n          _this.addDialogVisible = true;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  get f() {\n    return this.RelatedItemsForm.controls;\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.RelatedItemsForm.reset();\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallRelatedItemsComponent_Factory(t) {\n      return new (t || SalesCallRelatedItemsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallRelatedItemsComponent,\n      selectors: [[\"app-sales-call-related-items\"]],\n      viewQuery: function SalesCallRelatedItemsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.TypeSelect = _t.first);\n        }\n      },\n      decls: 36,\n      vars: 25,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"prospect-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Type\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"acitivity_type_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Related Item\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"bindLabel\", \"subject\", \"bindValue\", \"activity_id\", \"formControlName\", \"activity_id\", \"appendTo\", \"body\", 1, \"w-full\", \"h-3rem\", 3, \"search\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pSortableColumn\", \"activity.subject\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"activity.subject\"], [\"pSortableColumn\", \"acitivity_type_code\"], [\"field\", \"acitivity_type_code\"], [1, \"border-round-right-lg\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"6\"], [1, \"p-error\"], [4, \"ngIf\"]],\n      template: function SalesCallRelatedItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Related Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_p_button_click_4_listener() {\n            return ctx.showNewDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, SalesCallRelatedItemsComponent_ng_template_7_Template, 14, 0, \"ng-template\", 6)(8, SalesCallRelatedItemsComponent_ng_template_8_Template, 9, 3, \"ng-template\", 7)(9, SalesCallRelatedItemsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, SalesCallRelatedItemsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"p-dialog\", 10);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallRelatedItemsComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(12, SalesCallRelatedItemsComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n          i0.ɵɵelementStart(13, \"form\", 11)(14, \"div\", 12)(15, \"label\", 13)(16, \"span\", 14);\n          i0.ɵɵtext(17, \"supervisor_account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \"Type \");\n          i0.ɵɵelementStart(19, \"span\", 15);\n          i0.ɵɵtext(20, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵelement(22, \"p-dropdown\", 17);\n          i0.ɵɵtemplate(23, SalesCallRelatedItemsComponent_div_23_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 19)(26, \"span\", 14);\n          i0.ɵɵtext(27, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \"Related Item \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 16)(30, \"ng-select\", 20);\n          i0.ɵɵpipe(31, \"async\");\n          i0.ɵɵlistener(\"search\", function SalesCallRelatedItemsComponent_Template_ng_select_search_30_listener($event) {\n            return ctx.ItemInput$.next($event.term);\n          });\n          i0.ɵɵtemplate(32, SalesCallRelatedItemsComponent_ng_template_32_Template, 3, 2, \"ng-template\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 22)(34, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_34_listener() {\n            return ctx.addDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_35_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.relateditemsdetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c1));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.RelatedItemsForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"])(\"ngClass\", i0.ɵɵpureFunction1(23, _c2, ctx.submitted && ctx.f[\"acitivity_type_code\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"acitivity_type_code\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(31, 20, ctx.ItemData$))(\"hideSelected\", true)(\"loading\", ctx.ItemDataLoading)(\"minTermLength\", 3)(\"typeahead\", ctx.ItemInput$);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Dialog, i4.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "catchError", "shareReplay", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "SalesCallRelatedItemsComponent_ng_template_8_Template_button_click_8_listener", "$event", "related_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "stopPropagation", "ɵɵresetView", "confirmRemove", "ɵɵadvance", "ɵɵtextInterpolate1", "activity", "subject", "getLabelFromDropdown", "acitivity_type_code", "btp_role_code", "ɵɵtemplate", "SalesCallRelatedItemsComponent_div_23_div_1_Template", "ɵɵproperty", "submitted", "f", "errors", "item_r4", "SalesCallRelatedItemsComponent_ng_template_32_span_2_Template", "ɵɵtextInterpolate", "activity_id", "SalesCallRelatedItemsComponent", "constructor", "activitiesservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "relateditemsdetails", "addDialogVisible", "position", "saving", "ItemDataLoading", "ItemInput$", "dropdowns", "activityDocumentType", "RelatedItemsForm", "group", "required", "ngOnInit", "loadItemDataOnTypeChange", "loadActivityDropDown", "pipe", "subscribe", "response", "follow_up_and_related_items", "target", "type", "getActivityDropdownOptions", "res", "data", "attr", "label", "description", "value", "code", "dropdownKey", "item", "find", "opt", "ItemData$", "get", "valueChanges", "reset", "next", "TypeSelect", "clearModel", "term", "params", "getParamsByRole", "getActivityCodeWise", "filters", "onSubmit", "_this", "_asyncToGenerator", "invalid", "createRelatedItem", "add", "severity", "detail", "getActivityByID", "error", "confirm", "message", "header", "icon", "accept", "remove", "deleteFollowupItem", "documentId", "controls", "showNewDialog", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "FormBuilder", "i3", "MessageService", "ConfirmationService", "selectors", "viewQuery", "SalesCallRelatedItemsComponent_Query", "rf", "ctx", "SalesCallRelatedItemsComponent_Template_p_button_click_4_listener", "SalesCallRelatedItemsComponent_ng_template_7_Template", "SalesCallRelatedItemsComponent_ng_template_8_Template", "SalesCallRelatedItemsComponent_ng_template_9_Template", "SalesCallRelatedItemsComponent_ng_template_10_Template", "ɵɵtwoWayListener", "SalesCallRelatedItemsComponent_Template_p_dialog_visibleChange_11_listener", "ɵɵtwoWayBindingSet", "SalesCallRelatedItemsComponent_ng_template_12_Template", "SalesCallRelatedItemsComponent_div_23_Template", "SalesCallRelatedItemsComponent_Template_ng_select_search_30_listener", "SalesCallRelatedItemsComponent_ng_template_32_Template", "SalesCallRelatedItemsComponent_Template_button_click_34_listener", "SalesCallRelatedItemsComponent_Template_button_click_35_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c2", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-related-items\\sales-call-related-items.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-related-items\\sales-call-related-items.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  shareReplay,\r\n} from 'rxjs/operators';\r\nimport { NgSelectComponent } from '@ng-select/ng-select';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-sales-call-related-items',\r\n  templateUrl: './sales-call-related-items.component.html',\r\n  styleUrl: './sales-call-related-items.component.scss',\r\n})\r\nexport class SalesCallRelatedItemsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('TypeSelect') TypeSelect!: NgSelectComponent;\r\n  public relateditemsdetails: any = null;\r\n  public activity_id: string = '';\r\n  public addDialogVisible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public ItemData$?: Observable<any[]>;\r\n  public ItemDataLoading = false;\r\n  public ItemInput$ = new Subject<string>();\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n  };\r\n\r\n  public RelatedItemsForm: FormGroup = this.formBuilder.group({\r\n    activity_id: [''],\r\n    acitivity_type_code: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadItemDataOnTypeChange();\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOCUMENT_TYPE'\r\n    );\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response?.activity_id;\r\n          this.relateditemsdetails = response?.follow_up_and_related_items;\r\n        }\r\n      });\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  loadItemDataOnTypeChange(): void {\r\n    this.ItemData$ = this.RelatedItemsForm.get(\r\n      'acitivity_type_code'\r\n    )!.valueChanges.pipe(\r\n      tap(() => {\r\n        this.RelatedItemsForm.get('activity_id')?.reset();\r\n        this.ItemInput$.next(''); // Clear the search term\r\n        if (this.TypeSelect) {\r\n          this.TypeSelect.clearModel(); // Clear the search input\r\n        }\r\n      }),\r\n      switchMap((type: string) => {\r\n        if (!type) return of([]);\r\n\r\n        return this.ItemInput$.pipe(\r\n          distinctUntilChanged(),\r\n          tap(() => (this.ItemDataLoading = true)),\r\n          switchMap((term: string) => {\r\n            const params = this.getParamsByRole(type, term);\r\n            if (!params) return of([]);\r\n\r\n            return this.activitiesservice.getActivityCodeWise(params).pipe(\r\n              map((res: any) => res || []),\r\n              catchError(() => {\r\n                this.ItemDataLoading = false;\r\n                return of([]);\r\n              }),\r\n              tap(() => (this.ItemDataLoading = false))\r\n            );\r\n          })\r\n        );\r\n      }),\r\n      shareReplay(1)\r\n    );\r\n  }\r\n\r\n  private getParamsByRole(type: string, term: string): any | null {\r\n    if (!type) return null;\r\n\r\n    const filters: any = {\r\n      'filters[$or][0][subject][$containsi]': term,\r\n      'filters[$or][1][activity_id][$containsi]': term,\r\n    };\r\n\r\n    switch (type) {\r\n      case '0006':\r\n        return {\r\n          'filters[document_type][$eq]': '0006',\r\n          ...filters,\r\n        };\r\n      case '0001':\r\n        return {\r\n          'filters[document_type][$eq]': '0001',\r\n          ...filters,\r\n        };\r\n      case '0004':\r\n        return {\r\n          'filters[document_type][$eq]': '0004',\r\n          ...filters,\r\n        };\r\n      case 'lead':\r\n      case 'opportunity':\r\n      case '0002':\r\n        return {\r\n          'filters[document_type][$eq]': '0002',\r\n          ...filters,\r\n        };\r\n      default:\r\n        return null;\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.RelatedItemsForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.RelatedItemsForm.value };\r\n\r\n    const data = {\r\n      activity_id: value?.activity_id,\r\n      acitivity_type_code: value?.acitivity_type_code,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createRelatedItem(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.RelatedItemsForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Related Item Added successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteFollowupItem(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  get f(): any {\r\n    return this.RelatedItemsForm.controls;\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.RelatedItemsForm.reset();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Related Items</h4>\r\n        <p-button label=\"Add\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"relateditemsdetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"activity.subject\">\r\n                        <div class=\"flex align-items-center gap-2\">Name <p-sortIcon\r\n                                field=\"activity.subject\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"acitivity_type_code\">\r\n                        <div class=\"flex align-items-center gap-2\">Type <p-sortIcon\r\n                                field=\"acitivity_type_code\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center gap-2\">Responsible</div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg text-center\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-related>\r\n                <tr>\r\n                    <td>\r\n                        {{ related?.activity?.subject || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{\r\n                        getLabelFromDropdown('activityDocumentType',related?.acitivity_type_code)\r\n                        || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ related?.btp_role_code || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();confirmRemove(related);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">No related items found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\">Loading related items data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '38rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"prospect-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Add Reference</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"RelatedItemsForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Type\">\r\n                <span class=\"material-symbols-rounded\">supervisor_account</span>Type\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"dropdowns['activityDocumentType']\" formControlName=\"acitivity_type_code\"\r\n                    placeholder=\"Select a Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['acitivity_type_code'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['acitivity_type_code'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                            submitted &&\r\n                            f['acitivity_type_code'].errors &&\r\n                            f['acitivity_type_code'].errors['required']\r\n                          \">\r\n                        Type is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Related Item\">\r\n                <span class=\"material-symbols-rounded\">category</span>Related Item\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select [items]=\"ItemData$ | async\" bindLabel=\"subject\" bindValue=\"activity_id\" [hideSelected]=\"true\"\r\n                    [loading]=\"ItemDataLoading\" [minTermLength]=\"3\" [typeahead]=\"ItemInput$\"\r\n                    formControlName=\"activity_id\" appendTo=\"body\" class=\"w-full h-3rem\"\r\n                    (search)=\"ItemInput$.next($event.term)\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.activity_id }}</span>\r\n                        <span *ngIf=\"item.subject\"> : {{ item.subject }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,EAAcC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAE9D,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,WAAW,QACN,gBAAgB;;;;;;;;;;;;;;;;;;;;;ICICC,EAFR,CAAAC,cAAA,SAAI,aACuC,cACQ;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,SAAA,qBACF;IAClDH,EADkD,CAAAI,YAAA,EAAM,EACnD;IAEDJ,EADJ,CAAAC,cAAA,aAA0C,cACK;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,SAAA,qBACC;IAErDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,SAAI,eAC2C;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAC1DF,EAD0D,CAAAI,YAAA,EAAM,EAC3D;IACLJ,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACxDF,EADwD,CAAAI,YAAA,EAAK,EACxD;;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,SACI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAGJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAEDJ,EADJ,CAAAC,cAAA,aAA8C,iBAEqB;IAA3DD,EAAA,CAAAK,UAAA,mBAAAC,8EAAAC,MAAA;MAAA,MAAAC,UAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAASN,MAAA,CAAAO,eAAA,EAAwB;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAACH,MAAA,CAAAI,aAAA,CAAAR,UAAA,CAAsB;IAAA,EAAE;IAEtER,EAFuE,CAAAI,YAAA,EAAS,EACvE,EACJ;;;;;IAdGJ,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAW,QAAA,kBAAAX,UAAA,CAAAW,QAAA,CAAAC,OAAA,cACJ;IAEIpB,EAAA,CAAAiB,SAAA,GAGJ;IAHIjB,EAAA,CAAAkB,kBAAA,MAAAN,MAAA,CAAAS,oBAAA,yBAAAb,UAAA,kBAAAA,UAAA,CAAAc,mBAAA,cAGJ;IAEItB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAe,aAAA,cACJ;;;;;IASAvB,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC3CF,EAD2C,CAAAI,YAAA,EAAK,EAC3C;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC5DF,EAD4D,CAAAI,YAAA,EAAK,EAC5D;;;;;IAQbJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAI,YAAA,EAAK;;;;;IAeVJ,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAPVJ,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAwB,UAAA,IAAAC,oDAAA,kBAIQ;IAGZzB,EAAA,CAAAI,YAAA,EAAM;;;;IAPIJ,EAAA,CAAAiB,SAAA,EAID;IAJCjB,EAAA,CAAA0B,UAAA,SAAAd,MAAA,CAAAe,SAAA,IAAAf,MAAA,CAAAgB,CAAA,wBAAAC,MAAA,IAAAjB,MAAA,CAAAgB,CAAA,wBAAAC,MAAA,aAID;;;;;IAiBD7B,EAAA,CAAAC,cAAA,WAA2B;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAI,YAAA,EAAO;;;;IAA3BJ,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAAkB,kBAAA,QAAAY,OAAA,CAAAV,OAAA,KAAoB;;;;;IADhDpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAI,YAAA,EAAO;IACnCJ,EAAA,CAAAwB,UAAA,IAAAO,6DAAA,mBAA2B;;;;IADrB/B,EAAA,CAAAiB,SAAA,EAAsB;IAAtBjB,EAAA,CAAAgC,iBAAA,CAAAF,OAAA,CAAAG,WAAA,CAAsB;IACrBjC,EAAA,CAAAiB,SAAA,EAAkB;IAAlBjB,EAAA,CAAA0B,UAAA,SAAAI,OAAA,CAAAV,OAAA,CAAkB;;;ADjFjD,OAAM,MAAOc,8BAA8B;EAsBzCC,YACUC,iBAAoC,EACpCC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzBrB,KAAAC,YAAY,GAAG,IAAIlD,OAAO,EAAQ;IAEnC,KAAAmD,mBAAmB,GAAQ,IAAI;IAC/B,KAAAR,WAAW,GAAW,EAAE;IACxB,KAAAS,gBAAgB,GAAY,KAAK;IACjC,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAhB,SAAS,GAAG,KAAK;IACjB,KAAAiB,MAAM,GAAG,KAAK;IAEd,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,UAAU,GAAG,IAAIxD,OAAO,EAAU;IAElC,KAAAyD,SAAS,GAA0B;MACxCC,oBAAoB,EAAE;KACvB;IAEM,KAAAC,gBAAgB,GAAc,IAAI,CAACZ,WAAW,CAACa,KAAK,CAAC;MAC1DjB,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBX,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAACyD,QAAQ,CAAC;KAChD,CAAC;EAOC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,4BAA4B,CAC7B;IACD,IAAI,CAAClB,iBAAiB,CAACjB,QAAQ,CAC5BoC,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiD,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACxB,WAAW,GAAGwB,QAAQ,EAAExB,WAAW;QACxC,IAAI,CAACQ,mBAAmB,GAAGgB,QAAQ,EAAEC,2BAA2B;MAClE;IACF,CAAC,CAAC;EACN;EAEAJ,oBAAoBA,CAACK,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAACxB,iBAAiB,CACnByB,0BAA0B,CAACD,IAAI,CAAC,CAChCJ,SAAS,CAAEM,GAAQ,IAAI;MACtB,IAAI,CAACf,SAAS,CAACY,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAEvE,GAAG,CAAEwE,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEA/C,oBAAoBA,CAACgD,WAAmB,EAAEF,KAAa;IACrD,MAAMG,IAAI,GAAG,IAAI,CAACvB,SAAS,CAACsB,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACL,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOG,IAAI,EAAEL,KAAK,IAAIE,KAAK;EAC7B;EAEAd,wBAAwBA,CAAA;IACtB,IAAI,CAACoB,SAAS,GAAG,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CACxC,qBAAqB,CACrB,CAACC,YAAY,CAACpB,IAAI,CAClB1D,GAAG,CAAC,MAAK;MACP,IAAI,CAACoD,gBAAgB,CAACyB,GAAG,CAAC,aAAa,CAAC,EAAEE,KAAK,EAAE;MACjD,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,UAAU,EAAE,CAAC,CAAC;MAChC;IACF,CAAC,CAAC,EACFnF,SAAS,CAAEgE,IAAY,IAAI;MACzB,IAAI,CAACA,IAAI,EAAE,OAAOnE,EAAE,CAAC,EAAE,CAAC;MAExB,OAAO,IAAI,CAACqD,UAAU,CAACS,IAAI,CACzB5D,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACgD,eAAe,GAAG,IAAK,CAAC,EACxCjD,SAAS,CAAEoF,IAAY,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACtB,IAAI,EAAEoB,IAAI,CAAC;QAC/C,IAAI,CAACC,MAAM,EAAE,OAAOxF,EAAE,CAAC,EAAE,CAAC;QAE1B,OAAO,IAAI,CAAC2C,iBAAiB,CAAC+C,mBAAmB,CAACF,MAAM,CAAC,CAAC1B,IAAI,CAC5D/D,GAAG,CAAEsE,GAAQ,IAAKA,GAAG,IAAI,EAAE,CAAC,EAC5BhE,UAAU,CAAC,MAAK;UACd,IAAI,CAAC+C,eAAe,GAAG,KAAK;UAC5B,OAAOpD,EAAE,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,EACFI,GAAG,CAAC,MAAO,IAAI,CAACgD,eAAe,GAAG,KAAM,CAAC,CAC1C;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACF9C,WAAW,CAAC,CAAC,CAAC,CACf;EACH;EAEQmF,eAAeA,CAACtB,IAAY,EAAEoB,IAAY;IAChD,IAAI,CAACpB,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMwB,OAAO,GAAQ;MACnB,sCAAsC,EAAEJ,IAAI;MAC5C,0CAA0C,EAAEA;KAC7C;IAED,QAAQpB,IAAI;MACV,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,GAAGwB;SACJ;MACH,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,GAAGA;SACJ;MACH,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,GAAGA;SACJ;MACH,KAAK,MAAM;MACX,KAAK,aAAa;MAClB,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,GAAGA;SACJ;MACH;QACE,OAAO,IAAI;IACf;EACF;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC3D,SAAS,GAAG,IAAI;MAErB,IAAI2D,KAAI,CAACrC,gBAAgB,CAACuC,OAAO,EAAE;QACjC;MACF;MAEAF,KAAI,CAAC1C,MAAM,GAAG,IAAI;MAClB,MAAMuB,KAAK,GAAG;QAAE,GAAGmB,KAAI,CAACrC,gBAAgB,CAACkB;MAAK,CAAE;MAEhD,MAAMJ,IAAI,GAAG;QACX9B,WAAW,EAAEkC,KAAK,EAAElC,WAAW;QAC/BX,mBAAmB,EAAE6C,KAAK,EAAE7C;OAC7B;MAEDgE,KAAI,CAAClD,iBAAiB,CACnBqD,iBAAiB,CAAC1B,IAAI,CAAC,CACvBR,IAAI,CAAChE,SAAS,CAAC+F,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;QACTqB,IAAI,EAAGpB,QAAa,IAAI;UACtB6B,KAAI,CAAC1C,MAAM,GAAG,KAAK;UACnB0C,KAAI,CAAC5C,gBAAgB,GAAG,KAAK;UAC7B4C,KAAI,CAACrC,gBAAgB,CAAC2B,KAAK,EAAE;UAC7BU,KAAI,CAAChD,cAAc,CAACoD,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFN,KAAI,CAAClD,iBAAiB,CACnByD,eAAe,CAACP,KAAI,CAACrD,WAAW,CAAC,CACjCsB,IAAI,CAAChE,SAAS,CAAC+F,KAAI,CAAC9C,YAAY,CAAC,CAAC,CAClCgB,SAAS,EAAE;QAChB,CAAC;QACDsC,KAAK,EAAGhC,GAAQ,IAAI;UAClBwB,KAAI,CAAC1C,MAAM,GAAG,KAAK;UACnB0C,KAAI,CAAC5C,gBAAgB,GAAG,IAAI;UAC5B4C,KAAI,CAAChD,cAAc,CAACoD,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEA5E,aAAaA,CAACsD,IAAS;IACrB,IAAI,CAAC/B,mBAAmB,CAACwD,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC9B,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA8B,MAAMA,CAAC9B,IAAS;IACd,IAAI,CAAClC,iBAAiB,CACnBiE,kBAAkB,CAAC/B,IAAI,CAACgC,UAAU,CAAC,CACnC/C,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiD,YAAY,CAAC,CAAC,CAClCgB,SAAS,CAAC;MACTqB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvC,cAAc,CAACoD,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACxD,iBAAiB,CACnByD,eAAe,CAAC,IAAI,CAAC5D,WAAW,CAAC,CACjCsB,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiD,YAAY,CAAC,CAAC,CAClCgB,SAAS,EAAE;MAChB,CAAC;MACDsC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxD,cAAc,CAACoD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA,IAAIhE,CAACA,CAAA;IACH,OAAO,IAAI,CAACqB,gBAAgB,CAACsD,QAAQ;EACvC;EAEAC,aAAaA,CAAC7D,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACf,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsB,gBAAgB,CAAC2B,KAAK,EAAE;EAC/B;EAEA6B,WAAWA,CAAA;IACT,IAAI,CAACjE,YAAY,CAACqC,IAAI,EAAE;IACxB,IAAI,CAACrC,YAAY,CAACkE,QAAQ,EAAE;EAC9B;;;uBApOWxE,8BAA8B,EAAAlC,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAA9BhF,8BAA8B;MAAAiF,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCjBnCtH,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACjEJ,EAAA,CAAAC,cAAA,kBAC2D;UADrCD,EAAA,CAAAK,UAAA,mBAAAmH,kEAAA;YAAA,OAASD,GAAA,CAAAf,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAE1DxG,EAFI,CAAAI,YAAA,EAC2D,EACzD;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UA4C1BD,EA1CA,CAAAwB,UAAA,IAAAiG,qDAAA,0BAAgC,IAAAC,qDAAA,yBAkBU,IAAAC,qDAAA,yBAmBJ,KAAAC,sDAAA,yBAKD;UAOjD5H,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UACNJ,EAAA,CAAAC,cAAA,oBAC+C;UADtBD,EAAA,CAAA6H,gBAAA,2BAAAC,2EAAAvH,MAAA;YAAAP,EAAA,CAAA+H,kBAAA,CAAAR,GAAA,CAAA7E,gBAAA,EAAAnC,MAAA,MAAAgH,GAAA,CAAA7E,gBAAA,GAAAnC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAEnDP,EAAA,CAAAwB,UAAA,KAAAwG,sDAAA,yBAAgC;UAOpBhI,EAHZ,CAAAC,cAAA,gBAA6E,eACpB,iBAC2C,gBACjD;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,aAChE;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAChCF,EADgC,CAAAI,YAAA,EAAO,EAC/B;UACRJ,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAG,SAAA,sBAGa;UACbH,EAAA,CAAAwB,UAAA,KAAAyG,8CAAA,kBAA0E;UAUlFjI,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAqD,iBACmD,gBACzD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAAAJ,EAAA,CAAAE,MAAA,qBAC1D;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UAEJJ,EADJ,CAAAC,cAAA,eAAwC,qBAIQ;;UAAxCD,EAAA,CAAAK,UAAA,oBAAA6H,qEAAA3H,MAAA;YAAA,OAAUgH,GAAA,CAAAzE,UAAA,CAAA+B,IAAA,CAAAtE,MAAA,CAAAyE,IAAA,CAA4B;UAAA,EAAC;UACvChF,EAAA,CAAAwB,UAAA,KAAA2G,sDAAA,0BAA2C;UAMvDnI,EAFQ,CAAAI,YAAA,EAAY,EACV,EACJ;UAEFJ,EADJ,CAAAC,cAAA,eAAoD,kBAGT;UAAnCD,EAAA,CAAAK,UAAA,mBAAA+H,iEAAA;YAAA,OAAAb,GAAA,CAAA7E,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAC1C,EAAA,CAAAI,YAAA,EAAS;UAChDJ,EAAA,CAAAC,cAAA,kBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAAgI,iEAAA;YAAA,OAASd,GAAA,CAAAlC,QAAA,EAAU;UAAA,EAAC;UAGpCrF,EAHqC,CAAAI,YAAA,EAAS,EAChC,EACH,EACA;;;UA7GCJ,EAAA,CAAAiB,SAAA,GAAmC;UAACjB,EAApC,CAAA0B,UAAA,oCAAmC,iBAAiB;UAI/C1B,EAAA,CAAAiB,SAAA,GAA6B;UAAwCjB,EAArE,CAAA0B,UAAA,UAAA6F,GAAA,CAAA9E,mBAAA,CAA6B,YAAyB,mBAAiC;UAqDhDzC,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAsI,UAAA,CAAAtI,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAA4B;UAA1ExI,EAAA,CAAA0B,UAAA,eAAc;UAAC1B,EAAA,CAAAyI,gBAAA,YAAAlB,GAAA,CAAA7E,gBAAA,CAA8B;UACnD1C,EADiF,CAAA0B,UAAA,qBAAoB,oBAClF;UAKb1B,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAA0B,UAAA,cAAA6F,GAAA,CAAAtE,gBAAA,CAA8B;UAOZjD,EAAA,CAAAiB,SAAA,GAA6C;UAErDjB,EAFQ,CAAA0B,UAAA,YAAA6F,GAAA,CAAAxE,SAAA,yBAA6C,YAAA/C,EAAA,CAAA0I,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAA5F,SAAA,IAAA4F,GAAA,CAAA3F,CAAA,wBAAAC,MAAA,EAEqB;UAExE7B,EAAA,CAAAiB,SAAA,EAAkD;UAAlDjB,EAAA,CAAA0B,UAAA,SAAA6F,GAAA,CAAA5F,SAAA,IAAA4F,GAAA,CAAA3F,CAAA,wBAAAC,MAAA,CAAkD;UAgB7C7B,EAAA,CAAAiB,SAAA,GAA2B;UACcjB,EADzC,CAAA0B,UAAA,UAAA1B,EAAA,CAAA4I,WAAA,SAAArB,GAAA,CAAA9C,SAAA,EAA2B,sBAAkE,YAAA8C,GAAA,CAAA1E,eAAA,CACzE,oBAAoB,cAAA0E,GAAA,CAAAzE,UAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}