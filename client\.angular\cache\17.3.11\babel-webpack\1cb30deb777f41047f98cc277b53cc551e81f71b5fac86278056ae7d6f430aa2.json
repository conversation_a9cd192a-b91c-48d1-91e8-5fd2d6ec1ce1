{"ast": null, "code": "'use strict';\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;", "map": {"version": 3, "names": ["module", "exports", "Reflect", "apply"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/call-bind-apply-helpers/reflectApply.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,IAAIA,OAAO,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}