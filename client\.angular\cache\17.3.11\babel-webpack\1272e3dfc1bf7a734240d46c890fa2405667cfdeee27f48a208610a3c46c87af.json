{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ExportComponent } from './export.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ExportComponent\n}];\nexport let ExportRoutingModule = /*#__PURE__*/(() => {\n  class ExportRoutingModule {\n    static {\n      this.ɵfac = function ExportRoutingModule_Factory(t) {\n        return new (t || ExportRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ExportRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ExportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}