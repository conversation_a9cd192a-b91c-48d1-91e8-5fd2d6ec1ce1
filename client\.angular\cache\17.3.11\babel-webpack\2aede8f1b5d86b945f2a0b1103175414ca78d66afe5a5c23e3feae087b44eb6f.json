{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProspectsComponent } from './prospects.component';\nimport { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';\nimport { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';\nimport { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';\nimport { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';\nimport { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';\nimport { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';\nimport { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';\nimport { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';\nimport { AddProspectComponent } from './add-prospect/add-prospect.component';\nimport { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProspectsComponent\n}, {\n  path: 'create',\n  component: AddProspectComponent\n}, {\n  path: ':id',\n  component: ProspectsDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ProspectsOverviewComponent\n  }, {\n    path: 'contacts',\n    component: ProspectsContactsComponent\n  }, {\n    path: 'sales-team',\n    component: ProspectsSalesTeamComponent\n  }, {\n    path: 'ai-insights',\n    component: ProspectsAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: ProspectsOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: ProspectsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ProspectsNotesComponent\n  }, {\n    path: 'activities',\n    component: ProspectActivitiesComponent\n  }, {\n    path: 'activities/detail/:id',\n    component: ActivitiesItemDetailComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class ProspectsRoutingModule {\n  static {\n    this.ɵfac = function ProspectsRoutingModule_Factory(t) {\n      return new (t || ProspectsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProspectsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProspectsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ProspectsComponent", "ProspectsDetailsComponent", "ProspectsOverviewComponent", "ProspectsAiInsightsComponent", "ProspectsAttachmentsComponent", "ProspectsContactsComponent", "ProspectsNotesComponent", "ProspectsOrganizationDataComponent", "ProspectsSalesTeamComponent", "AddProspectComponent", "ProspectActivitiesComponent", "ActivitiesItemDetailComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ProspectsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ProspectsComponent } from './prospects.component';\r\nimport { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';\r\nimport { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';\r\nimport { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';\r\nimport { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';\r\nimport { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';\r\nimport { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';\r\nimport { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';\r\nimport { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';\r\nimport { AddProspectComponent } from './add-prospect/add-prospect.component';\r\nimport { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';\r\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ProspectsComponent },\r\n  { path: 'create', component: AddProspectComponent },\r\n  {\r\n    path: ':id',\r\n    component: ProspectsDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: ProspectsOverviewComponent },\r\n      { path: 'contacts', component: ProspectsContactsComponent },\r\n      { path: 'sales-team', component: ProspectsSalesTeamComponent },\r\n      { path: 'ai-insights', component: ProspectsAiInsightsComponent },\r\n      {\r\n        path: 'organization-data',\r\n        component: ProspectsOrganizationDataComponent,\r\n      },\r\n      { path: 'attachments', component: ProspectsAttachmentsComponent },\r\n      { path: 'notes', component: ProspectsNotesComponent },\r\n      { path: 'activities', component: ProspectActivitiesComponent },\r\n      {\r\n        path: 'activities/detail/:id',\r\n        component: ActivitiesItemDetailComponent,\r\n      },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ProspectsRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,4BAA4B,QAAQ,2EAA2E;AACxH,SAASC,6BAA6B,QAAQ,2EAA2E;AACzH,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,kCAAkC,QAAQ,uFAAuF;AAC1I,SAASC,2BAA2B,QAAQ,yEAAyE;AACrH,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,6BAA6B,QAAQ,wEAAwE;;;AAEtH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEd;AAAkB,CAAE,EAC3C;EAAEa,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEL;AAAoB,CAAE,EACnD;EACEI,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEb,yBAAyB;EACpCc,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEZ;EAA0B,CAAE,EAC3D;IAAEW,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAA0B,CAAE,EAC3D;IAAEQ,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEN;EAA2B,CAAE,EAC9D;IAAEK,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEX;EAA4B,CAAE,EAChE;IACEU,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEP;GACZ,EACD;IAAEM,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEV;EAA6B,CAAE,EACjE;IAAES,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAuB,CAAE,EACrD;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEJ;EAA2B,CAAE,EAC9D;IACEG,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEH;GACZ,EACD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBnB,YAAY,CAACoB,QAAQ,CAACP,MAAM,CAAC,EAC7Bb,YAAY;IAAA;EAAA;;;2EAEXmB,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAtB,YAAA;IAAAuB,OAAA,GAFvBvB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}