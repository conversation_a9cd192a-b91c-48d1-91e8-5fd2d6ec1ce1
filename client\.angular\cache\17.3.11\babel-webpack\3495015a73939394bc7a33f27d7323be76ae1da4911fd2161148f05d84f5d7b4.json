{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i2 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputnumber p-component\": true,\n  \"p-inputnumber-buttons-stacked\": a0,\n  \"p-inputnumber-buttons-horizontal\": a1,\n  \"p-inputnumber-buttons-vertical\": a2\n});\nconst _c2 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-up\": true\n});\nconst _c3 = () => ({\n  \"p-inputnumber-button p-inputnumber-button-down\": true\n});\nfunction InputNumber_ng_container_3_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 8);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵlistener(\"click\", function InputNumber_ng_container_3_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clear());\n    });\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_span_2_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_ng_container_3_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 6)(2, InputNumber_ng_container_3_span_2_Template, 2, 2, \"span\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_3_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_span_4_ng_container_3_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_span_4_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_span_4_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_span_4_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_span_4_ng_container_6_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_span_4_ng_container_6_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(2, InputNumber_span_4_span_2_Template, 1, 2, \"span\", 13)(3, InputNumber_span_4_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(5, InputNumber_span_4_span_5_Template, 1, 2, \"span\", 13)(6, InputNumber_span_4_ng_container_6_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"buttonGroup\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(17, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(18, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", ctx_r2.decrementbutton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nfunction InputNumber_button_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleUpIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"incrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_5_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_5_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_5_ng_container_2_AngleUpIcon_1_Template, 1, 1, \"AngleUpIcon\", 3)(2, InputNumber_button_5_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.incrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_5_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_5_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_5_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_5_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_5_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_5_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_5_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c2))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"incrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.incrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.incrementButtonIcon);\n  }\n}\nfunction InputNumber_button_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"decrementbuttonicon\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction InputNumber_button_6_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, InputNumber_button_6_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction InputNumber_button_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputNumber_button_6_ng_container_2_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 3)(2, InputNumber_button_6_ng_container_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.decrementButtonIconTemplate);\n  }\n}\nfunction InputNumber_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_6_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_6_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_6_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_6_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDownButtonKeyUp());\n    });\n    i0.ɵɵtemplate(1, InputNumber_button_6_span_1_Template, 1, 2, \"span\", 13)(2, InputNumber_button_6_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(8, _c3))(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"decrementbutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.decrementButtonIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.decrementButtonIcon);\n  }\n}\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputNumber),\n  multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\nlet InputNumber = /*#__PURE__*/(() => {\n  class InputNumber {\n    document;\n    el;\n    cd;\n    injector;\n    /**\n     * Displays spinner buttons.\n     * @group Props\n     */\n    showButtons = false;\n    /**\n     * Whether to format the value.\n     * @group Props\n     */\n    format = true;\n    /**\n     * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n     * @group Props\n     */\n    buttonLayout = 'stacked';\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    size;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    maxlength;\n    /**\n     * Specifies tab order of the element.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Title text of the input text.\n     * @group Props\n     */\n    title;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Used to indicate that user input is required on an element before a form can be submitted.\n     * @group Props\n     */\n    ariaRequired;\n    /**\n     * Name of the input field.\n     * @group Props\n     */\n    name;\n    /**\n     * Indicates that whether the input field is required.\n     * @group Props\n     */\n    required;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    autocomplete;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    min;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    max;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonClass;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonClass;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    incrementButtonIcon;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    decrementButtonIcon;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    readonly = false;\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    step = 1;\n    /**\n     * Determines whether the input field is empty.\n     * @group Props\n     */\n    allowEmpty = true;\n    /**\n     * Locale to be used in formatting.\n     * @group Props\n     */\n    locale;\n    /**\n     * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n     * @group Props\n     */\n    localeMatcher;\n    /**\n     * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n     * @group Props\n     */\n    mode = 'decimal';\n    /**\n     * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n     * @group Props\n     */\n    currency;\n    /**\n     * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n     * @group Props\n     */\n    currencyDisplay;\n    /**\n     * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n     * @group Props\n     */\n    useGrouping = true;\n    /**\n     * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    minFractionDigits;\n    /**\n     * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    maxFractionDigits;\n    /**\n     * Text to display before the value.\n     * @group Props\n     */\n    prefix;\n    /**\n     * Text to display after the value.\n     * @group Props\n     */\n    suffix;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(disabled) {\n      if (disabled) this.focused = false;\n      this._disabled = disabled;\n      if (this.timer) this.clearTimer();\n    }\n    /**\n     * Callback to invoke on input.\n     * @param {InputNumberInputEvent} event - Custom input event.\n     * @group Emits\n     */\n    onInput = new EventEmitter();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke on input key press.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    onKeyDown = new EventEmitter();\n    /**\n     * Callback to invoke when clear token is clicked.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    input;\n    templates;\n    clearIconTemplate;\n    incrementButtonIconTemplate;\n    decrementButtonIconTemplate;\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    focused;\n    initialized;\n    groupChar = '';\n    prefixChar = '';\n    suffixChar = '';\n    isSpecialChar;\n    timer;\n    lastValue;\n    _numeral;\n    numberFormat;\n    _decimal;\n    _group;\n    _minusSign;\n    _currency;\n    _prefix;\n    _suffix;\n    _index;\n    _disabled;\n    ngControl = null;\n    constructor(document, el, cd, injector) {\n      this.document = document;\n      this.el = el;\n      this.cd = cd;\n      this.injector = injector;\n    }\n    ngOnChanges(simpleChange) {\n      const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n      if (props.some(p => !!simpleChange[p])) {\n        this.updateConstructParser();\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'incrementbuttonicon':\n            this.incrementButtonIconTemplate = item.template;\n            break;\n          case 'decrementbuttonicon':\n            this.decrementButtonIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnInit() {\n      this.ngControl = this.injector.get(NgControl, null, {\n        optional: true\n      });\n      this.constructParser();\n      this.initialized = true;\n    }\n    getOptions() {\n      return {\n        localeMatcher: this.localeMatcher,\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        useGrouping: this.useGrouping,\n        minimumFractionDigits: this.minFractionDigits,\n        maximumFractionDigits: this.maxFractionDigits\n      };\n    }\n    constructParser() {\n      this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n      const numerals = [...new Intl.NumberFormat(this.locale, {\n        useGrouping: false\n      }).format(9876543210)].reverse();\n      const index = new Map(numerals.map((d, i) => [d, i]));\n      this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n      this._group = this.getGroupingExpression();\n      this._minusSign = this.getMinusSignExpression();\n      this._currency = this.getCurrencyExpression();\n      this._decimal = this.getDecimalExpression();\n      this._suffix = this.getSuffixExpression();\n      this._prefix = this.getPrefixExpression();\n      this._index = d => index.get(d);\n    }\n    updateConstructParser() {\n      if (this.initialized) {\n        this.constructParser();\n      }\n    }\n    escapeRegExp(text) {\n      return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n    }\n    getDecimalExpression() {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        ...this.getOptions(),\n        useGrouping: false\n      });\n      return new RegExp(`[${formatter.format(1.1).replace(this._currency, '').trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getGroupingExpression() {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        useGrouping: true\n      });\n      this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n      return new RegExp(`[${this.groupChar}]`, 'g');\n    }\n    getMinusSignExpression() {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        useGrouping: false\n      });\n      return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getCurrencyExpression() {\n      if (this.currency) {\n        const formatter = new Intl.NumberFormat(this.locale, {\n          style: 'currency',\n          currency: this.currency,\n          currencyDisplay: this.currencyDisplay,\n          minimumFractionDigits: 0,\n          maximumFractionDigits: 0\n        });\n        return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n      }\n      return new RegExp(`[]`, 'g');\n    }\n    getPrefixExpression() {\n      if (this.prefix) {\n        this.prefixChar = this.prefix;\n      } else {\n        const formatter = new Intl.NumberFormat(this.locale, {\n          style: this.mode,\n          currency: this.currency,\n          currencyDisplay: this.currencyDisplay\n        });\n        this.prefixChar = formatter.format(1).split('1')[0];\n      }\n      return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n    }\n    getSuffixExpression() {\n      if (this.suffix) {\n        this.suffixChar = this.suffix;\n      } else {\n        const formatter = new Intl.NumberFormat(this.locale, {\n          style: this.mode,\n          currency: this.currency,\n          currencyDisplay: this.currencyDisplay,\n          minimumFractionDigits: 0,\n          maximumFractionDigits: 0\n        });\n        this.suffixChar = formatter.format(1).split('1')[1];\n      }\n      return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n    }\n    formatValue(value) {\n      if (value != null) {\n        if (value === '-') {\n          // Minus sign\n          return value;\n        }\n        if (this.format) {\n          let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n          let formattedValue = formatter.format(value);\n          if (this.prefix) {\n            formattedValue = this.prefix + formattedValue;\n          }\n          if (this.suffix) {\n            formattedValue = formattedValue + this.suffix;\n          }\n          return formattedValue;\n        }\n        return value.toString();\n      }\n      return '';\n    }\n    parseValue(text) {\n      let filteredText = text.replace(this._suffix, '').replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '').replace(this._group, '').replace(this._minusSign, '-').replace(this._decimal, '.').replace(this._numeral, this._index);\n      if (filteredText) {\n        if (filteredText === '-')\n          // Minus sign\n          return filteredText;\n        let parsedValue = +filteredText;\n        return isNaN(parsedValue) ? null : parsedValue;\n      }\n      return null;\n    }\n    repeat(event, interval, dir) {\n      if (this.readonly) {\n        return;\n      }\n      let i = interval || 500;\n      this.clearTimer();\n      this.timer = setTimeout(() => {\n        this.repeat(event, 40, dir);\n      }, i);\n      this.spin(event, dir);\n    }\n    spin(event, dir) {\n      let step = this.step * dir;\n      let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n      let newValue = this.validateValue(currentValue + step);\n      if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n        return;\n      }\n      this.updateInput(newValue, null, 'spin', null);\n      this.updateModel(event, newValue);\n      this.handleOnInput(event, currentValue, newValue);\n    }\n    clear() {\n      this.value = null;\n      this.onModelChange(this.value);\n      this.onClear.emit();\n    }\n    onUpButtonMouseDown(event) {\n      if (event.button === 2) {\n        this.clearTimer();\n        return;\n      }\n      if (!this.disabled) {\n        this.input?.nativeElement.focus();\n        this.repeat(event, null, 1);\n        event.preventDefault();\n      }\n    }\n    onUpButtonMouseUp() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onUpButtonMouseLeave() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onUpButtonKeyDown(event) {\n      if (event.keyCode === 32 || event.keyCode === 13) {\n        this.repeat(event, null, 1);\n      }\n    }\n    onUpButtonKeyUp() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onDownButtonMouseDown(event) {\n      if (event.button === 2) {\n        this.clearTimer();\n        return;\n      }\n      if (!this.disabled) {\n        this.input?.nativeElement.focus();\n        this.repeat(event, null, -1);\n        event.preventDefault();\n      }\n    }\n    onDownButtonMouseUp() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onDownButtonMouseLeave() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onDownButtonKeyUp() {\n      if (!this.disabled) {\n        this.clearTimer();\n      }\n    }\n    onDownButtonKeyDown(event) {\n      if (event.keyCode === 32 || event.keyCode === 13) {\n        this.repeat(event, null, -1);\n      }\n    }\n    onUserInput(event) {\n      if (this.readonly) {\n        return;\n      }\n      if (this.isSpecialChar) {\n        event.target.value = this.lastValue;\n      }\n      this.isSpecialChar = false;\n    }\n    onInputKeyDown(event) {\n      if (this.readonly) {\n        return;\n      }\n      this.lastValue = event.target.value;\n      if (event.shiftKey || event.altKey) {\n        this.isSpecialChar = true;\n        return;\n      }\n      let selectionStart = event.target.selectionStart;\n      let selectionEnd = event.target.selectionEnd;\n      let inputValue = event.target.value;\n      let newValueStr = null;\n      if (event.altKey) {\n        event.preventDefault();\n      }\n      switch (event.code) {\n        case 'ArrowUp':\n          this.spin(event, 1);\n          event.preventDefault();\n          break;\n        case 'ArrowDown':\n          this.spin(event, -1);\n          event.preventDefault();\n          break;\n        case 'ArrowLeft':\n          if (!this.isNumeralChar(inputValue.charAt(selectionStart - 1))) {\n            event.preventDefault();\n          }\n          break;\n        case 'ArrowRight':\n          if (!this.isNumeralChar(inputValue.charAt(selectionStart))) {\n            event.preventDefault();\n          }\n          break;\n        case 'Tab':\n        case 'Enter':\n          newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n          this.input.nativeElement.value = this.formatValue(newValueStr);\n          this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n          this.updateModel(event, newValueStr);\n          break;\n        case 'Backspace':\n          {\n            event.preventDefault();\n            if (selectionStart === selectionEnd) {\n              const deleteChar = inputValue.charAt(selectionStart - 1);\n              const {\n                decimalCharIndex,\n                decimalCharIndexWithoutPrefix\n              } = this.getDecimalCharIndexes(inputValue);\n              if (this.isNumeralChar(deleteChar)) {\n                const decimalLength = this.getDecimalLength(inputValue);\n                if (this._group.test(deleteChar)) {\n                  this._group.lastIndex = 0;\n                  newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n                } else if (this._decimal.test(deleteChar)) {\n                  this._decimal.lastIndex = 0;\n                  if (decimalLength) {\n                    this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                  } else {\n                    newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                  }\n                } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                  const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n                } else if (decimalCharIndexWithoutPrefix === 1) {\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                  newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                } else {\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                }\n              } else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n                newValueStr = inputValue.slice(1);\n              }\n              this.updateValue(event, newValueStr, null, 'delete-single');\n            } else {\n              newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n              this.updateValue(event, newValueStr, null, 'delete-range');\n            }\n            break;\n          }\n        case 'Delete':\n          event.preventDefault();\n          if (selectionStart === selectionEnd) {\n            const deleteChar = inputValue.charAt(selectionStart);\n            const {\n              decimalCharIndex,\n              decimalCharIndexWithoutPrefix\n            } = this.getDecimalCharIndexes(inputValue);\n            if (this.isNumeralChar(deleteChar)) {\n              const decimalLength = this.getDecimalLength(inputValue);\n              if (this._group.test(deleteChar)) {\n                this._group.lastIndex = 0;\n                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n              } else if (this._decimal.test(deleteChar)) {\n                this._decimal.lastIndex = 0;\n                if (decimalLength) {\n                  this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n                } else {\n                  newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                }\n              } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n              } else if (decimalCharIndexWithoutPrefix === 1) {\n                newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n                newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n              }\n            }\n            this.updateValue(event, newValueStr, null, 'delete-back-single');\n          } else {\n            newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n            this.updateValue(event, newValueStr, null, 'delete-range');\n          }\n          break;\n        case 'Home':\n          if (this.min) {\n            this.updateModel(event, this.min);\n            event.preventDefault();\n          }\n          break;\n        case 'End':\n          if (this.max) {\n            this.updateModel(event, this.max);\n            event.preventDefault();\n          }\n          break;\n        default:\n          break;\n      }\n      this.onKeyDown.emit(event);\n    }\n    onInputKeyPress(event) {\n      if (this.readonly) {\n        return;\n      }\n      let code = event.which || event.keyCode;\n      let char = String.fromCharCode(code);\n      const isDecimalSign = this.isDecimalSign(char);\n      const isMinusSign = this.isMinusSign(char);\n      if (code != 13) {\n        event.preventDefault();\n      }\n      const newValue = this.parseValue(this.input.nativeElement.value + char);\n      const newValueStr = newValue != null ? newValue.toString() : '';\n      if (this.maxlength && newValueStr.length > this.maxlength) {\n        return;\n      }\n      if (48 <= code && code <= 57 || isMinusSign || isDecimalSign) {\n        this.insert(event, char, {\n          isDecimalSign,\n          isMinusSign\n        });\n      }\n    }\n    onPaste(event) {\n      if (!this.disabled && !this.readonly) {\n        event.preventDefault();\n        let data = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n        if (data) {\n          if (this.maxlength) {\n            data = data.toString().substring(0, this.maxlength);\n          }\n          let filteredData = this.parseValue(data);\n          if (filteredData != null) {\n            this.insert(event, filteredData.toString());\n          }\n        }\n      }\n    }\n    allowMinusSign() {\n      return this.min == null || this.min < 0;\n    }\n    isMinusSign(char) {\n      if (this._minusSign.test(char) || char === '-') {\n        this._minusSign.lastIndex = 0;\n        return true;\n      }\n      return false;\n    }\n    isDecimalSign(char) {\n      if (this._decimal.test(char)) {\n        this._decimal.lastIndex = 0;\n        return true;\n      }\n      return false;\n    }\n    isDecimalMode() {\n      return this.mode === 'decimal';\n    }\n    getDecimalCharIndexes(val) {\n      let decimalCharIndex = val.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      const filteredVal = val.replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '');\n      const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      return {\n        decimalCharIndex,\n        decimalCharIndexWithoutPrefix\n      };\n    }\n    getCharIndexes(val) {\n      const decimalCharIndex = val.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      const minusCharIndex = val.search(this._minusSign);\n      this._minusSign.lastIndex = 0;\n      const suffixCharIndex = val.search(this._suffix);\n      this._suffix.lastIndex = 0;\n      const currencyCharIndex = val.search(this._currency);\n      this._currency.lastIndex = 0;\n      return {\n        decimalCharIndex,\n        minusCharIndex,\n        suffixCharIndex,\n        currencyCharIndex\n      };\n    }\n    insert(event, text, sign = {\n      isDecimalSign: false,\n      isMinusSign: false\n    }) {\n      const minusCharIndexOnText = text.search(this._minusSign);\n      this._minusSign.lastIndex = 0;\n      if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n        return;\n      }\n      let selectionStart = this.input?.nativeElement.selectionStart;\n      let selectionEnd = this.input?.nativeElement.selectionEnd;\n      let inputValue = this.input?.nativeElement.value.trim();\n      const {\n        decimalCharIndex,\n        minusCharIndex,\n        suffixCharIndex,\n        currencyCharIndex\n      } = this.getCharIndexes(inputValue);\n      let newValueStr;\n      if (sign.isMinusSign) {\n        if (selectionStart === 0) {\n          newValueStr = inputValue;\n          if (minusCharIndex === -1 || selectionEnd !== 0) {\n            newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n          }\n          this.updateValue(event, newValueStr, text, 'insert');\n        }\n      } else if (sign.isDecimalSign) {\n        if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n          this.updateValue(event, inputValue, text, 'insert');\n        } else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n          newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, text, 'insert');\n        } else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n          newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, text, 'insert');\n        }\n      } else {\n        const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n        const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n        if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n          if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n            const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n            newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n            this.updateValue(event, newValueStr, text, operation);\n          }\n        } else {\n          newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, text, operation);\n        }\n      }\n    }\n    insertText(value, text, start, end) {\n      let textSplit = text === '.' ? text : text.split('.');\n      if (textSplit.length === 2) {\n        const decimalCharIndex = value.slice(start, end).search(this._decimal);\n        this._decimal.lastIndex = 0;\n        return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n      } else if (end - start === value.length) {\n        return this.formatValue(text);\n      } else if (start === 0) {\n        return text + value.slice(end);\n      } else if (end === value.length) {\n        return value.slice(0, start) + text;\n      } else {\n        return value.slice(0, start) + text + value.slice(end);\n      }\n    }\n    deleteRange(value, start, end) {\n      let newValueStr;\n      if (end - start === value.length) newValueStr = '';else if (start === 0) newValueStr = value.slice(end);else if (end === value.length) newValueStr = value.slice(0, start);else newValueStr = value.slice(0, start) + value.slice(end);\n      return newValueStr;\n    }\n    initCursor() {\n      let selectionStart = this.input?.nativeElement.selectionStart;\n      let inputValue = this.input?.nativeElement.value;\n      let valueLength = inputValue.length;\n      let index = null;\n      // remove prefix\n      let prefixLength = (this.prefixChar || '').length;\n      inputValue = inputValue.replace(this._prefix, '');\n      selectionStart = selectionStart - prefixLength;\n      let char = inputValue.charAt(selectionStart);\n      if (this.isNumeralChar(char)) {\n        return selectionStart + prefixLength;\n      }\n      //left\n      let i = selectionStart - 1;\n      while (i >= 0) {\n        char = inputValue.charAt(i);\n        if (this.isNumeralChar(char)) {\n          index = i + prefixLength;\n          break;\n        } else {\n          i--;\n        }\n      }\n      if (index !== null) {\n        this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n      } else {\n        i = selectionStart;\n        while (i < valueLength) {\n          char = inputValue.charAt(i);\n          if (this.isNumeralChar(char)) {\n            index = i + prefixLength;\n            break;\n          } else {\n            i++;\n          }\n        }\n        if (index !== null) {\n          this.input?.nativeElement.setSelectionRange(index, index);\n        }\n      }\n      return index || 0;\n    }\n    onInputClick() {\n      const currentValue = this.input?.nativeElement.value;\n      if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n        this.initCursor();\n      }\n    }\n    isNumeralChar(char) {\n      if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n        this.resetRegex();\n        return true;\n      }\n      return false;\n    }\n    resetRegex() {\n      this._numeral.lastIndex = 0;\n      this._decimal.lastIndex = 0;\n      this._group.lastIndex = 0;\n      this._minusSign.lastIndex = 0;\n    }\n    updateValue(event, valueStr, insertedValueStr, operation) {\n      let currentValue = this.input?.nativeElement.value;\n      let newValue = null;\n      if (valueStr != null) {\n        newValue = this.parseValue(valueStr);\n        newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n        this.updateInput(newValue, insertedValueStr, operation, valueStr);\n        this.handleOnInput(event, currentValue, newValue);\n      }\n    }\n    handleOnInput(event, currentValue, newValue) {\n      if (this.isValueChanged(currentValue, newValue)) {\n        this.input.nativeElement.value = this.formatValue(newValue);\n        this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n        this.updateModel(event, newValue);\n        this.onInput.emit({\n          originalEvent: event,\n          value: newValue,\n          formattedValue: currentValue\n        });\n      }\n    }\n    isValueChanged(currentValue, newValue) {\n      if (newValue === null && currentValue !== null) {\n        return true;\n      }\n      if (newValue != null) {\n        let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n        return newValue !== parsedCurrentValue;\n      }\n      return false;\n    }\n    validateValue(value) {\n      if (value === '-' || value == null) {\n        return null;\n      }\n      if (this.min != null && value < this.min) {\n        return this.min;\n      }\n      if (this.max != null && value > this.max) {\n        return this.max;\n      }\n      return value;\n    }\n    updateInput(value, insertedValueStr, operation, valueStr) {\n      insertedValueStr = insertedValueStr || '';\n      let inputValue = this.input?.nativeElement.value;\n      let newValue = this.formatValue(value);\n      let currentLength = inputValue.length;\n      if (newValue !== valueStr) {\n        newValue = this.concatValues(newValue, valueStr);\n      }\n      if (currentLength === 0) {\n        this.input.nativeElement.value = newValue;\n        this.input.nativeElement.setSelectionRange(0, 0);\n        const index = this.initCursor();\n        const selectionEnd = index + insertedValueStr.length;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else {\n        let selectionStart = this.input.nativeElement.selectionStart;\n        let selectionEnd = this.input.nativeElement.selectionEnd;\n        if (this.maxlength && newValue.length > this.maxlength) {\n          newValue = newValue.slice(0, this.maxlength);\n          selectionStart = Math.min(selectionStart, this.maxlength);\n          selectionEnd = Math.min(selectionEnd, this.maxlength);\n        }\n        if (this.maxlength && this.maxlength < newValue.length) {\n          return;\n        }\n        this.input.nativeElement.value = newValue;\n        let newLength = newValue.length;\n        if (operation === 'range-insert') {\n          const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n          const startValueStr = startValue !== null ? startValue.toString() : '';\n          const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n          const sRegex = new RegExp(startExpr, 'g');\n          sRegex.test(newValue);\n          const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n          const tRegex = new RegExp(tExpr, 'g');\n          tRegex.test(newValue.slice(sRegex.lastIndex));\n          selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n          this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        } else if (newLength === currentLength) {\n          if (operation === 'insert' || operation === 'delete-back-single') this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);else if (operation === 'delete-single') this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);else if (operation === 'delete-range' || operation === 'spin') this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        } else if (operation === 'delete-back-single') {\n          let prevChar = inputValue.charAt(selectionEnd - 1);\n          let nextChar = inputValue.charAt(selectionEnd);\n          let diff = currentLength - newLength;\n          let isGroupChar = this._group.test(nextChar);\n          if (isGroupChar && diff === 1) {\n            selectionEnd += 1;\n          } else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n            selectionEnd += -1 * diff + 1;\n          }\n          this._group.lastIndex = 0;\n          this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        } else if (inputValue === '-' && operation === 'insert') {\n          this.input.nativeElement.setSelectionRange(0, 0);\n          const index = this.initCursor();\n          const selectionEnd = index + insertedValueStr.length + 1;\n          this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        } else {\n          selectionEnd = selectionEnd + (newLength - currentLength);\n          this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        }\n      }\n      this.input.nativeElement.setAttribute('aria-valuenow', value);\n    }\n    concatValues(val1, val2) {\n      if (val1 && val2) {\n        let decimalCharIndex = val2.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        if (this.suffixChar) {\n          return val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n        } else {\n          return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n        }\n      }\n      return val1;\n    }\n    getDecimalLength(value) {\n      if (value) {\n        const valueSplit = value.split(this._decimal);\n        if (valueSplit.length === 2) {\n          return valueSplit[1].replace(this._suffix, '').trim().replace(/\\s/g, '').replace(this._currency, '').length;\n        }\n      }\n      return 0;\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      let newValue = this.validateValue(this.parseValue(this.input.nativeElement.value));\n      this.onBlur.emit(event);\n      this.input.nativeElement.value = this.formatValue(newValue);\n      this.input.nativeElement.setAttribute('aria-valuenow', newValue);\n      this.updateModel(event, newValue);\n    }\n    formattedValue() {\n      const val = !this.value && !this.allowEmpty ? 0 : this.value;\n      return this.formatValue(val);\n    }\n    updateModel(event, value) {\n      const isBlurUpdateOnMode = this.ngControl?.control?.updateOn === 'blur';\n      if (this.value !== value) {\n        this.value = value;\n        if (!(isBlurUpdateOnMode && this.focused)) {\n          this.onModelChange(value);\n        }\n      } else if (isBlurUpdateOnMode) {\n        this.onModelChange(value);\n      }\n      this.onModelTouched();\n    }\n    writeValue(value) {\n      this.value = value;\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    get filled() {\n      return this.value != null && this.value.toString().length > 0;\n    }\n    clearTimer() {\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n    }\n    getFormatter() {\n      return this.numberFormat;\n    }\n    static ɵfac = function InputNumber_Factory(t) {\n      return new (t || InputNumber)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: InputNumber,\n      selectors: [[\"p-inputNumber\"]],\n      contentQueries: function InputNumber_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function InputNumber_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 6,\n      hostBindings: function InputNumber_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused)(\"p-inputnumber-clearable\", ctx.showClear && ctx.buttonLayout != \"vertical\");\n        }\n      },\n      inputs: {\n        showButtons: \"showButtons\",\n        format: \"format\",\n        buttonLayout: \"buttonLayout\",\n        inputId: \"inputId\",\n        styleClass: \"styleClass\",\n        style: \"style\",\n        placeholder: \"placeholder\",\n        size: \"size\",\n        maxlength: \"maxlength\",\n        tabindex: \"tabindex\",\n        title: \"title\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        ariaLabel: \"ariaLabel\",\n        ariaRequired: \"ariaRequired\",\n        name: \"name\",\n        required: \"required\",\n        autocomplete: \"autocomplete\",\n        min: \"min\",\n        max: \"max\",\n        incrementButtonClass: \"incrementButtonClass\",\n        decrementButtonClass: \"decrementButtonClass\",\n        incrementButtonIcon: \"incrementButtonIcon\",\n        decrementButtonIcon: \"decrementButtonIcon\",\n        readonly: \"readonly\",\n        step: \"step\",\n        allowEmpty: \"allowEmpty\",\n        locale: \"locale\",\n        localeMatcher: \"localeMatcher\",\n        mode: \"mode\",\n        currency: \"currency\",\n        currencyDisplay: \"currencyDisplay\",\n        useGrouping: \"useGrouping\",\n        minFractionDigits: \"minFractionDigits\",\n        maxFractionDigits: \"maxFractionDigits\",\n        prefix: \"prefix\",\n        suffix: \"suffix\",\n        inputStyle: \"inputStyle\",\n        inputStyleClass: \"inputStyleClass\",\n        showClear: \"showClear\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        onInput: \"onInput\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onKeyDown: \"onKeyDown\",\n        onClear: \"onClear\"\n      },\n      features: [i0.ɵɵProvidersFeature([INPUTNUMBER_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature],\n      decls: 7,\n      vars: 39,\n      consts: [[\"input\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"pInputText\", \"\", \"role\", \"spinbutton\", \"inputmode\", \"decimal\", 3, \"input\", \"keydown\", \"keypress\", \"paste\", \"click\", \"focus\", \"blur\", \"ngClass\", \"ngStyle\", \"value\", \"disabled\", \"readonly\"], [4, \"ngIf\"], [\"class\", \"p-inputnumber-button-group\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-button-icon-only\", \"tabindex\", \"-1\", 3, \"ngClass\", \"class\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", 4, \"ngIf\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-inputnumber-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"ngClass\"], [1, \"p-inputnumber-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [1, \"p-inputnumber-button-group\"], [\"type\", \"button\", \"pButton\", \"\", \"tabindex\", \"-1\", 1, \"p-button-icon-only\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", \"ngClass\", \"disabled\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n      template: function InputNumber_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n          i0.ɵɵlistener(\"input\", function InputNumber_Template_input_input_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onUserInput($event));\n          })(\"keydown\", function InputNumber_Template_input_keydown_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputKeyDown($event));\n          })(\"keypress\", function InputNumber_Template_input_keypress_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputKeyPress($event));\n          })(\"paste\", function InputNumber_Template_input_paste_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPaste($event));\n          })(\"click\", function InputNumber_Template_input_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputClick());\n          })(\"focus\", function InputNumber_Template_input_focus_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"blur\", function InputNumber_Template_input_blur_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, InputNumber_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, InputNumber_span_4_Template, 7, 19, \"span\", 4)(5, InputNumber_button_5_Template, 3, 9, \"button\", 5)(6, InputNumber_button_6_Template, 3, 9, \"button\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(35, _c1, ctx.showButtons && ctx.buttonLayout === \"stacked\", ctx.showButtons && ctx.buttonLayout === \"horizontal\", ctx.showButtons && ctx.buttonLayout === \"vertical\"))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"inputnumber\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.inputStyleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-input\")(\"ngStyle\", ctx.inputStyle)(\"value\", ctx.formattedValue())(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-valuemin\", ctx.min)(\"aria-valuemax\", ctx.max)(\"aria-valuenow\", ctx.value)(\"placeholder\", ctx.placeholder)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"title\", ctx.title)(\"size\", ctx.size)(\"name\", ctx.name)(\"autocomplete\", ctx.autocomplete)(\"maxlength\", ctx.maxlength)(\"tabindex\", ctx.tabindex)(\"aria-required\", ctx.ariaRequired)(\"required\", ctx.required)(\"min\", ctx.min)(\"max\", ctx.max)(\"data-pc-section\", \"input\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.buttonLayout != \"vertical\" && ctx.showClear && ctx.value);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout === \"stacked\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.InputText, i3.ButtonDirective, TimesIcon, AngleUpIcon, AngleDownIcon],\n      styles: [\"@layer primeng{p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return InputNumber;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputNumberModule = /*#__PURE__*/(() => {\n  class InputNumberModule {\n    static ɵfac = function InputNumberModule_Factory(t) {\n      return new (t || InputNumberModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputNumberModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, InputTextModule, ButtonModule, TimesIcon, AngleUpIcon, AngleDownIcon, SharedModule]\n    });\n  }\n  return InputNumberModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };\n//# sourceMappingURL=primeng-inputnumber.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}