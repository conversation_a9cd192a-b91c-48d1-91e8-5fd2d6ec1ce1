{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { stringify } from 'qs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"src/app/store/services/service-ticket.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/progressspinner\";\nimport * as i9 from \"primeng/multiselect\";\nfunction AccountTicketsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_i_4_Template, 1, 1, \"i\", 16)(5, AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function AccountTicketsComponent_p_table_8_ng_template_2_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"id\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3, \" Ticket # \");\n    i0.ɵɵtemplate(4, AccountTicketsComponent_p_table_8_ng_template_2_i_4_Template, 1, 1, \"i\", 16)(5, AccountTicketsComponent_p_table_8_ng_template_2_i_5_Template, 1, 0, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountTicketsComponent_p_table_8_ng_template_2_ng_container_6_Template, 6, 4, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ticket_r6.support_team, \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ticket_r6.assigned_to_name, \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ticket_r6.priority, \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ticket_r6.subject, \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ticket_r6.status_id || \"\").toLowerCase(), \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, ticket_r6.createdAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 26);\n    i0.ɵɵtemplate(3, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 27)(4, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 27)(5, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 27)(6, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 27)(7, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_7_Template, 2, 1, \"ng-container\", 28)(8, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_ng_container_8_Template, 3, 4, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"support_team\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"assigned_to_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"priority\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"status_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n  }\n}\nfunction AccountTicketsComponent_p_table_8_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23)(2, \"div\", 24)(3, \"a\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, AccountTicketsComponent_p_table_8_ng_template_3_ng_container_5_Template, 9, 7, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ticket_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pSelectableRow\", ticket_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/service-ticket-details/\" + ((ticket_r6 == null ? null : ticket_r6.id) || \"-\") + \"/\" + ((ticket_r6 == null ? null : ticket_r6.documentId) || \"-\") + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ticket_r6 == null ? null : ticket_r6.id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountTicketsComponent_p_table_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 11, 0);\n    i0.ɵɵlistener(\"sortFunction\", function AccountTicketsComponent_p_table_8_Template_p_table_sortFunction_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort($event));\n    })(\"onRowSelect\", function AccountTicketsComponent_p_table_8_Template_p_table_onRowSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToTicket($event));\n    })(\"onColReorder\", function AccountTicketsComponent_p_table_8_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(2, AccountTicketsComponent_p_table_8_ng_template_2_Template, 7, 3, \"ng-template\", 12)(3, AccountTicketsComponent_p_table_8_ng_template_3_Template, 6, 4, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.tickets)(\"rows\", 10)(\"rowHover\", true)(\"loading\", ctx_r1.loading)(\"paginator\", true)(\"customSort\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction AccountTicketsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(\"No records found.\");\n  }\n}\nexport let AccountTicketsComponent = /*#__PURE__*/(() => {\n  class AccountTicketsComponent {\n    constructor(accountservice, ticketService, router) {\n      this.accountservice = accountservice;\n      this.ticketService = ticketService;\n      this.router = router;\n      this.unsubscribe$ = new Subject();\n      this.tickets = [];\n      this.loading = false;\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'support_team',\n        header: 'Support Team'\n      }, {\n        field: 'assigned_to_name',\n        header: 'Assigned To'\n      }, {\n        field: 'priority',\n        header: 'Priority'\n      }, {\n        field: 'subject',\n        header: 'Subject'\n      }, {\n        field: 'status',\n        header: 'Status'\n      }, {\n        field: 'createdAt',\n        header: 'Created At'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n      this.isSidebarHidden = false;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.tickets.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loading = true;\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.loadInitialData(response.bp_id);\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    loadInitialData(id) {\n      this.ticketService.getByAccountId(id).subscribe(response => {\n        this.loading = false;\n        this.tickets = response?.data || [];\n        // Get unique assigned_to values\n        const uniqueAssignedTo = Array.from(new Set(this.tickets.map(ticket => ticket.assigned_to)));\n        this.searchBps(uniqueAssignedTo).pipe(takeUntil(this.unsubscribe$)).subscribe(res => {\n          if (res?.length) {\n            this.tickets = this.tickets.map(ticket => {\n              const found = res.find(item => item.bp_id === ticket.assigned_to);\n              if (found) {\n                ticket.assigned_to_name = found.bp_full_name;\n              }\n              return ticket;\n            });\n          }\n        });\n      }, () => {\n        this.loading = false;\n      });\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    goToTicket(event) {\n      this.searchBps([event.data.account_id]).pipe(takeUntil(this.unsubscribe$)).subscribe(res => {\n        if (res?.length) {\n          this.router.navigate(['/store/service-ticket-details', event.data.id, res[0].documentId]);\n        }\n      });\n    }\n    searchBps(bpIds) {\n      const params = stringify({\n        filters: {\n          $and: [{\n            bp_id: {\n              $in: bpIds\n            }\n          }]\n        }\n      });\n      return this.accountservice.search(params);\n    }\n    static {\n      this.ɵfac = function AccountTicketsComponent_Factory(t) {\n        return new (t || AccountTicketsComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.ServiceTicketService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountTicketsComponent,\n        selectors: [[\"app-account-tickets\"]],\n        decls: 10,\n        vars: 6,\n        consts: [[\"myTab\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"single\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\", \"reorderableColumns\", \"sortFunction\", \"onRowSelect\", \"onColReorder\", 4, \"ngIf\"], [\"class\", \"w-100\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"single\", 1, \"scrollable-table\", 3, \"sortFunction\", \"onRowSelect\", \"onColReorder\", \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"customSort\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [3, \"pSelectableRow\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-semibold\", \"border-round-left-lg\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"capitalize\", 4, \"ngSwitchCase\"], [\"class\", \"border-round-right-lg\", 4, \"ngSwitchCase\"], [1, \"capitalize\"], [1, \"border-round-right-lg\"], [1, \"w-100\"]],\n        template: function AccountTicketsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n            i0.ɵɵtext(3, \"Tickets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountTicketsComponent_Template_p_multiSelect_ngModelChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(6, \"div\", 6);\n            i0.ɵɵtemplate(7, AccountTicketsComponent_div_7_Template, 2, 0, \"div\", 7)(8, AccountTicketsComponent_p_table_8_Template, 4, 7, \"p-table\", 8)(9, AccountTicketsComponent_div_9_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.tickets.length);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.tickets.length);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.PrimeTemplate, i6.Table, i6.SortableColumn, i6.FrozenColumn, i6.SelectableRow, i6.ReorderableColumn, i7.NgControlStatus, i7.NgModel, i8.ProgressSpinner, i9.MultiSelect, i4.DatePipe]\n      });\n    }\n  }\n  return AccountTicketsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}