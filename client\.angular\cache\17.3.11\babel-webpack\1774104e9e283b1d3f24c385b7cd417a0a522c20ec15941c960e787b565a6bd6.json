{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ContactsService = /*#__PURE__*/(() => {\n  class ContactsService {\n    constructor(http) {\n      this.http = http;\n      this.contactSubject = new BehaviorSubject(null);\n      this.contact = this.contactSubject.asObservable();\n    }\n    createNote(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n        data\n      });\n    }\n    createContact(data) {\n      return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\n    }\n    updateContact(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n    }\n    updateNote(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n        data\n      });\n    }\n    getContacts(page, pageSize, sortField, sortOrder, searchTerm, obsolete) {\n      let params = new HttpParams().set('pagination[page]', Math.max(page, 1).toString()).set('pagination[pageSize]', Math.max(pageSize, 1).toString()).set('fields', 'bp_company_id,bp_person_id').set('filters[business_partner_person][roles][bp_role][$in][0]', 'BUP001').set('populate[business_partner_company][fields][0]', 'bp_full_name').set('populate[business_partner_person][fields][0]', 'bp_full_name').set('populate[business_partner_person][fields][1]', 'is_marked_for_archiving').set('populate[business_partner_person][populate]populate[addresses][fields][0]', 'house_number').set('populate[business_partner_person][populate]populate[addresses][fields][1]', 'street_name').set('populate[business_partner_person][populate]populate[addresses][fields][2]', 'city_name').set('populate[business_partner_person][populate]populate[addresses][fields][3]', 'region').set('populate[business_partner_person][populate]populate[addresses][fields][4]', 'country').set('populate[business_partner_person][populate]populate[addresses][fields][5]', 'postal_code').set('populate[business_partner_person][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number_type').set('populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number').set('populate[business_partner_person][populate][bp_extension][fields][0]', 'web_registered').set('populate[business_partner_person][populate][bp_extension][fields][1]', 'business_department').set('populate[person_func_and_dept][fields][0]', 'contact_person_department');\n      if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm && searchTerm.trim() !== '') {\n        params = params.set('filters[$or][0][bp_person_id][$containsi]', searchTerm).set('filters[$or][1][bp_company_id][$containsi]', searchTerm).set('filters[$or][2][business_partner_person][bp_full_name][$containsi]', searchTerm).set('filters[$or][3][business_partner_company][bp_full_name][$containsi]', searchTerm).set('filters[$or][4][business_partner_person][addresses][phone_numbers][phone_number][$containsi]', searchTerm).set('filters[$or][5][business_partner_person][addresses][emails][email_address][$containsi]', searchTerm);\n      }\n      if (obsolete) {\n        params = params.set('filters[$and][0][business_partner_person][is_marked_for_archiving][$eq]', 'true');\n      }\n      return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n        params\n      });\n    }\n    getContactByID(contactId) {\n      const params = new HttpParams().set('filters[documentId][$eq]', contactId).set('populate[business_partner_company][fields][0]', 'bp_full_name').set('populate[business_partner_person][populate][bp_extension][populate]', '*').set('populate[business_partner_person][populate][addresses][populate]', '*').set('populate[business_partner_person][populate][addresses][populate]', '*').set('populate[business_partner_person][populate][contact_person_func_and_depts][fields][0]', 'contact_person_department').set('populate[business_partner_person][populate][contact_person_func_and_depts][fields][1]', 'contact_person_department_name').set('populate[business_partner_person][populate][notes][populate]', '*').set('populate[person_func_and_dept][fields][0]', 'contact_person_department');\n      return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n        params\n      }).pipe(map(response => {\n        const contactDetails = response?.data[0] || null;\n        this.contactSubject.next(contactDetails);\n        return response;\n      }));\n    }\n    getCPDepartment() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getCPDepartmentLabel(code) {\n      const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS').set('filters[code][$eq]', code);\n      console.log(code);\n      return this.http.get(CMS_APIContstant.CONFIG_DATA, {\n        params\n      });\n    }\n    getCommunicationType() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'PRFRD_COMM_MEDIUM_TYPE');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getCPFunction() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getAccounts(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      }).pipe(map(response => (response?.data || []).map(item => {\n        return {\n          bp_id: item?.bp_id || '',\n          bp_full_name: item?.bp_full_name || ''\n        };\n      })));\n    }\n    deleteNote(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n    }\n    static {\n      this.ɵfac = function ContactsService_Factory(t) {\n        return new (t || ContactsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ContactsService,\n        factory: ContactsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ContactsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}