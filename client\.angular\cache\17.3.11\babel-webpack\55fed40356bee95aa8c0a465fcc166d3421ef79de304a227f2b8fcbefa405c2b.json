{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let SessionsyncService = /*#__PURE__*/(() => {\n  class SessionsyncService {\n    constructor() {\n      this.initSessionSync();\n    }\n    initSessionSync() {\n      sessionStorage.removeItem('session_loaded_once');\n      localStorage.removeItem('sync_in_progress');\n      const hasSession = !!sessionStorage.getItem('userInfo') && !!sessionStorage.getItem('jwtToken');\n      if (!hasSession) {\n        localStorage.setItem('getSessionStorage', Date.now().toString());\n        localStorage.setItem('sync_in_progress', 'true');\n      }\n      window.addEventListener('storage', event => {\n        if (event.key === 'getSessionStorage') {\n          const userInfo = sessionStorage.getItem('userInfo');\n          const jwtToken = sessionStorage.getItem('jwtToken');\n          if (userInfo && jwtToken) {\n            const payload = JSON.stringify({\n              userInfo,\n              jwtToken\n            });\n            localStorage.setItem('sessionStorageData', payload);\n            localStorage.removeItem('sessionStorageData');\n          }\n        }\n        if (event.key === 'sessionStorageData' && event.newValue) {\n          const data = JSON.parse(event.newValue || '{}');\n          if (data?.userInfo && data?.jwtToken) {\n            sessionStorage.setItem('userInfo', data.userInfo);\n            sessionStorage.setItem('jwtToken', data.jwtToken);\n            if (!sessionStorage.getItem('session_loaded_once')) {\n              sessionStorage.setItem('session_loaded_once', 'true');\n              setTimeout(() => location.reload(), 200);\n            }\n          }\n        }\n      });\n    }\n    static {\n      this.ɵfac = function SessionsyncService_Factory(t) {\n        return new (t || SessionsyncService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SessionsyncService,\n        factory: SessionsyncService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SessionsyncService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}