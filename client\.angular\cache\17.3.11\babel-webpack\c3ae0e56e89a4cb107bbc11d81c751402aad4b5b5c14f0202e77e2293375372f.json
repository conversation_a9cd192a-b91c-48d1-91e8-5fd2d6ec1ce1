{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../activities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = () => [\"/auth/signup\"];\nfunction TasksComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 21)(4, \"div\", 22);\n    i0.ɵɵtext(5, \" Subject \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 24)(8, \"div\", 22);\n    i0.ɵɵtext(9, \" Status \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 26)(12, \"div\", 22);\n    i0.ɵɵtext(13, \" Start Date/Time \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 28)(16, \"div\", 22);\n    i0.ɵɵtext(17, \" End Date/Time \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 30)(24, \"div\", 22);\n    i0.ɵɵtext(25, \" Category \");\n    i0.ɵɵelement(26, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\");\n    i0.ɵɵtext(28, \"Notes\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 32)(1, \"td\", 20);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", task_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/tasks/\" + task_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.description) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.start_date) ? i0.ɵɵpipeBind3(9, 10, task_r3.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.end_date) ? i0.ɵɵpipeBind3(12, 14, task_r3.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.account) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.contact) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.category) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (task_r3 == null ? null : task_r3.notes) || \"-\", \" \");\n  }\n}\nfunction TasksComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"No tasks found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"Loading tasks data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TasksComponent = /*#__PURE__*/(() => {\n  class TasksComponent {\n    constructor(activitiesservice) {\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.tasks = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n    }\n    ngOnInit() {\n      this.breadcrumbitems = [{\n        label: 'Tasks',\n        routerLink: ['/store/activities']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'All',\n        code: 'ALL'\n      }, {\n        name: 'My Appointments',\n        code: 'MA'\n      }, {\n        name: 'My Appointments This Month',\n        code: 'MAM'\n      }, {\n        name: 'My Appointments This Week',\n        code: 'MAW'\n      }, {\n        name: 'My Appointments Today',\n        code: 'MAT'\n      }, {\n        name: 'My Completed Appointments',\n        code: 'MCA'\n      }];\n    }\n    loadActivities(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      this.activitiesservice.getActivities(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n        next: response => {\n          this.tasks = response?.data || [];\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching tasks', error);\n          this.loading = false;\n        }\n      });\n    }\n    onGlobalFilter(table, event) {\n      this.loadActivities({\n        first: 0,\n        rows: 10\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function TasksComponent_Factory(t) {\n        return new (t || TasksComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TasksComponent,\n        selectors: [[\"app-tasks\"]],\n        decls: 22,\n        vars: 15,\n        consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Prospects\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", \"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"description\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"description\"], [\"pSortableColumn\", \"activity_status\"], [\"field\", \"activity_status\"], [\"pSortableColumn\", \"start_date\"], [\"field\", \"start_date\"], [\"pSortableColumn\", \"end_date\"], [\"field\", \"end_date\"], [\"pSortableColumn\", \"category\"], [\"field\", \"category\"], [1, \"cursor-pointer\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\", \"pl-3\"]],\n        template: function TasksComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TasksComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function TasksComponent_Template_input_input_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              const dt1_r2 = i0.ɵɵreference(17);\n              return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"i\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TasksComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 12)(12, \"span\", 13);\n            i0.ɵɵtext(13, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(14, \" Create \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 14)(16, \"p-table\", 15, 1);\n            i0.ɵɵlistener(\"onLazyLoad\", function TasksComponent_Template_p_table_onLazyLoad_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadActivities($event));\n            });\n            i0.ɵɵtemplate(18, TasksComponent_ng_template_18_Template, 29, 0, \"ng-template\", 16)(19, TasksComponent_ng_template_19_Template, 21, 18, \"ng-template\", 17)(20, TasksComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18)(21, TasksComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c0));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"value\", ctx.tasks)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n          }\n        },\n        dependencies: [i2.RouterLink, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.Table, i5.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i4.TableCheckbox, i4.TableHeaderCheckbox, i6.Dropdown, i7.Breadcrumb, i8.DatePipe]\n      });\n    }\n  }\n  return TasksComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}