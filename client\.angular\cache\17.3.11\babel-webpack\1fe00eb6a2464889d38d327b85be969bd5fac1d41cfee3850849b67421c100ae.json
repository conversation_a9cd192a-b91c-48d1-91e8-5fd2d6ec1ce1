{"ast": null, "code": "import { fork<PERSON><PERSON>n, Subject, takeUntil } from 'rxjs';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/progressspinner\";\nimport * as i8 from \"primeng/multiselect\";\nfunction AccountReturnsComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function AccountReturnsComponent_p_table_7_ng_template_2_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"SD_DOC\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Return Order # \");\n    i0.ɵɵtemplate(4, AccountReturnsComponent_p_table_7_ng_template_2_i_4_Template, 1, 1, \"i\", 15)(5, AccountReturnsComponent_p_table_7_ng_template_2_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountReturnsComponent_p_table_7_ng_template_2_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const order_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", order_r6.REF_SD_DOC, \" \");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const order_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.moment(order_r6.DOC_DATE).format(\"MM/DD/YYYY\"), \" \");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 24);\n    i0.ɵɵtemplate(3, AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 25)(4, AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"REF_SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_DATE\");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountReturnsComponent_p_table_7_ng_template_3_ng_container_3_Template, 5, 3, \"ng-container\", 17);\n    i0.ɵɵelementStart(4, \"td\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const order_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pSelectableRow\", order_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r6.SD_DOC, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusName(order_r6.DOC_STATUS), \" \");\n  }\n}\nfunction AccountReturnsComponent_p_table_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 10, 0);\n    i0.ɵɵlistener(\"onRowSelect\", function AccountReturnsComponent_p_table_7_Template_p_table_onRowSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToRetrunOrder($event));\n    })(\"onColReorder\", function AccountReturnsComponent_p_table_7_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(2, AccountReturnsComponent_p_table_7_ng_template_2_Template, 9, 3, \"ng-template\", 11)(3, AccountReturnsComponent_p_table_7_ng_template_3_Template, 6, 4, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.returns)(\"rows\", 10)(\"rowHover\", true)(\"loading\", ctx_r1.loading)(\"paginator\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction AccountReturnsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(\"No records found.\");\n  }\n}\nexport let AccountReturnsComponent = /*#__PURE__*/(() => {\n  class AccountReturnsComponent {\n    constructor(accountservice, router, route) {\n      this.accountservice = accountservice;\n      this.router = router;\n      this.route = route;\n      this.unsubscribe$ = new Subject();\n      this.moment = moment;\n      this.returns = [];\n      this.loading = false;\n      this.customer = {};\n      this.statuses = [];\n      this.statusString = '';\n      this.loadingPdf = false;\n      this.typeByCode = {};\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'REF_SD_DOC',\n        header: 'Ref. Sales Order #'\n      }, {\n        field: 'DOC_DATE',\n        header: 'Date Placed'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n      this.isSidebarHidden = false;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.returns.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loading = true;\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.loadInitialData(response.customer.customer_id);\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    loadInitialData(soldToParty) {\n      forkJoin({\n        partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\n        statuses: this.accountservice.fetchOrderStatuses({\n          'filters[type][$eq]': 'RETURN_STATUS'\n        })\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: ({\n          partnerFunction,\n          statuses\n        }) => {\n          this.statusString = (statuses?.data || []).map(val => val.code).join(';');\n          this.statuses = statuses?.data || [];\n          this.customer = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n          if (this.customer) {\n            this.fetchData();\n          }\n        },\n        error: error => {\n          console.error('Error fetching initial data:', error);\n        }\n      });\n    }\n    fetchData() {\n      this.accountservice.getReturns({\n        SD_DOC: '',\n        DOC_TYPE: \"CBAR\",\n        DOC_STATUS: this.statusString,\n        SOLDTO: this.customer?.customer_id,\n        VKORG: this.customer?.sales_organization,\n        COUNT: 1000,\n        DOCUMENT_DATE: '',\n        DOCUMENT_DATE_TO: ''\n      }).subscribe(response => {\n        this.loading = false;\n        this.returns = response?.RETURNORDERS || [];\n      }, () => {\n        this.loading = false;\n      });\n    }\n    formatDate(input) {\n      return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    // customSort(event: SortEvent) {\n    //   const sort = {\n    //     All: (a: any, b: any) => {\n    //       if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);\n    //       if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);\n    //       return 0;\n    //     },\n    //     Support_Team: (a: any, b: any) => {\n    //       const field = event.field || '';\n    //       const aValue = a[field] ?? '';\n    //       const bValue = b[field] ?? '';\n    //       return aValue.toString().localeCompare(bValue.toString()) * (event.order || 1);\n    //     }\n    //   };\n    //   event.data?.sort(event.field == \"support_team\" || event.field == \"assigned_to\" || event.field == \"subject\" ? sort.Support_Team : sort.All);\n    // }\n    getStatusName(code) {\n      const status = this.statuses.find(o => o.code === code);\n      if (status) {\n        return status.description;\n      }\n      return \"\";\n    }\n    goToRetrunOrder(event) {\n      this.router.navigate([`../return-order/${event.data.SD_DOC}/${event.data.REF_SD_DOC}`], {\n        relativeTo: this.route\n      });\n    }\n    static {\n      this.ɵfac = function AccountReturnsComponent_Factory(t) {\n        return new (t || AccountReturnsComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountReturnsComponent,\n        selectors: [[\"app-account-returns\"]],\n        decls: 9,\n        vars: 6,\n        consts: [[\"myTab\", \"\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"single\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"reorderableColumns\", \"onRowSelect\", \"onColReorder\", 4, \"ngIf\"], [\"class\", \"w-100\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"selectionMode\", \"single\", 1, \"scrollable-table\", 3, \"onRowSelect\", \"onColReorder\", \"value\", \"rows\", \"rowHover\", \"loading\", \"paginator\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [3, \"pSelectableRow\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-semibold\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"w-100\"]],\n        template: function AccountReturnsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n            i0.ɵɵtext(3, \"Returns\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-multiSelect\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountReturnsComponent_Template_p_multiSelect_ngModelChange_4_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5);\n            i0.ɵɵtemplate(6, AccountReturnsComponent_div_6_Template, 2, 0, \"div\", 6)(7, AccountReturnsComponent_p_table_7_Template, 4, 6, \"p-table\", 7)(8, AccountReturnsComponent_div_8_Template, 2, 1, \"div\", 8);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.returns.length);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.returns.length);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i4.PrimeTemplate, i5.Table, i5.SortableColumn, i5.FrozenColumn, i5.SelectableRow, i5.ReorderableColumn, i6.NgControlStatus, i6.NgModel, i7.ProgressSpinner, i8.MultiSelect]\n      });\n    }\n  }\n  return AccountReturnsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}