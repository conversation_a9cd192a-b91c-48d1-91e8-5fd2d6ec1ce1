{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../customer.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/tabview\";\nimport * as i7 from \"primeng/autocomplete\";\nimport * as i8 from \"./customer-info/customer-info.component\";\nimport * as i9 from \"./customer-companies/customer-companies.component\";\nimport * as i10 from \"./customer-partner/customer-partner.component\";\nimport * as i11 from \"./customer-sales-area/customer-sales-area.component\";\nimport * as i12 from \"./customer-sales-texts/customer-sales-texts.component\";\nfunction CustomerDetailsComponent_h5_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.business_partner == null ? null : ctx_r0.customerDetails.business_partner.bp_id, \" - \", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.business_partner == null ? null : ctx_r0.customerDetails.business_partner.bp_full_name, \" \");\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-customer-companies\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"companies\", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.companies);\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-customer-partner\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"partner_functions\", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.partner_functions);\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-customer-sales-area\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"sales_area\", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.sales_areas);\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-customer-sales-texts\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"sales_text\", ctx_r0.customerDetails == null ? null : ctx_r0.customerDetails.customer_texts);\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-customer-info\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customerDetails\", ctx_r0.customerDetails);\n  }\n}\nfunction CustomerDetailsComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 6);\n    i0.ɵɵelementContainerStart(1, 7);\n    i0.ɵɵtemplate(2, CustomerDetailsComponent_p_tabPanel_8_ng_container_2_Template, 2, 1, \"ng-container\", 8)(3, CustomerDetailsComponent_p_tabPanel_8_ng_container_3_Template, 2, 1, \"ng-container\", 8)(4, CustomerDetailsComponent_p_tabPanel_8_ng_container_4_Template, 2, 1, \"ng-container\", 8)(5, CustomerDetailsComponent_p_tabPanel_8_ng_container_5_Template, 2, 1, \"ng-container\", 8)(6, CustomerDetailsComponent_p_tabPanel_8_ng_container_6_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"header\", tab_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", tab_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"COMPANIES\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"PARTNER_FUNCTION\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"SALES_AREA\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"TEXTS\");\n  }\n}\nexport let CustomerDetailsComponent = /*#__PURE__*/(() => {\n  class CustomerDetailsComponent {\n    constructor(customerService, route, router) {\n      this.customerService = customerService;\n      this.route = route;\n      this.router = router;\n      this.tabs = [{\n        title: 'General',\n        value: 'GENERAL'\n      }, {\n        title: 'Companies',\n        value: 'COMPANIES'\n      }, {\n        title: 'Partner Function',\n        value: 'PARTNER_FUNCTION'\n      }, {\n        title: 'Sales Area',\n        value: 'SALES_AREA'\n      }, {\n        title: 'Texts',\n        value: 'TEXTS'\n      }];\n      this.customerDetails = null;\n      this.filteredCustomers = [];\n      this.isExpanded = false;\n      this.expandedRows = {};\n      this.bp_id = '';\n    }\n    ngOnInit() {\n      this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n      this.route.paramMap.subscribe(params => {\n        const customerId = params.get('id');\n        if (customerId) {\n          this.loadCustomerData(customerId);\n        }\n      });\n    }\n    loadCustomerData(customerId) {\n      this.customerService.getCustomerByID(customerId).subscribe({\n        next: response => {\n          this.customerDetails = response?.data[0] || null;\n          console.log('API Data:', this.customerDetails);\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    searchCustomers(event) {\n      const query = event.query.toLowerCase();\n      this.customerService.getCustomerByIDName(query).subscribe(response => {\n        this.filteredCustomers = response.data.map(customer => ({\n          id: customer.customer_id,\n          name: customer.customer_name,\n          searchword: customer.customer_id + \" - \" + customer.customer_name\n        }));\n      });\n    }\n    onCustomerSelect(customer) {\n      const customerId = customer.value.id;\n      if (customerId) {\n        this.router.navigate(['/backoffice/customer/', customerId]);\n      } else {\n        console.error('Customer ID is undefined or null');\n      }\n    }\n    goToBack() {\n      this.router.navigate(['/backoffice/customer']);\n    }\n    static {\n      this.ɵfac = function CustomerDetailsComponent_Factory(t) {\n        return new (t || CustomerDetailsComponent)(i0.ɵɵdirectiveInject(i1.CustomerService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerDetailsComponent,\n        selectors: [[\"app-customer-details\"]],\n        decls: 9,\n        vars: 5,\n        consts: [[1, \"grid\"], [1, \"field\", \"col-12\", \"md:col-4\"], [4, \"ngIf\"], [\"field\", \"searchword\", \"placeholder\", \"Search Customer by ID or Name\", 1, \"p-fluid\", 3, \"ngModelChange\", \"completeMethod\", \"onSelect\", \"ngModel\", \"suggestions\", \"dropdown\"], [\"icon\", \"pi pi-arrow-left\", \"label\", \"Back\", 1, \"p-button-primary\", \"p-back-button\", 3, \"onClick\"], [3, \"header\", 4, \"ngFor\", \"ngForOf\"], [3, \"header\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [3, \"companies\"], [3, \"partner_functions\"], [3, \"sales_area\"], [3, \"sales_text\"], [3, \"customerDetails\"]],\n        template: function CustomerDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, CustomerDetailsComponent_h5_2_Template, 2, 2, \"h5\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 1)(4, \"p-autoComplete\", 3);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerDetailsComponent_Template_p_autoComplete_ngModelChange_4_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCustomer, $event) || (ctx.selectedCustomer = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"completeMethod\", function CustomerDetailsComponent_Template_p_autoComplete_completeMethod_4_listener($event) {\n              return ctx.searchCustomers($event);\n            })(\"onSelect\", function CustomerDetailsComponent_Template_p_autoComplete_onSelect_4_listener($event) {\n              return ctx.onCustomerSelect($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 1)(6, \"p-button\", 4);\n            i0.ɵɵlistener(\"onClick\", function CustomerDetailsComponent_Template_p_button_onClick_6_listener() {\n              return ctx.goToBack();\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"p-tabView\");\n            i0.ɵɵtemplate(8, CustomerDetailsComponent_p_tabPanel_8_Template, 7, 6, \"p-tabPanel\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_id) && (ctx.customerDetails == null ? null : ctx.customerDetails.business_partner == null ? null : ctx.customerDetails.business_partner.bp_full_name));\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCustomer);\n            i0.ɵɵproperty(\"suggestions\", ctx.filteredCustomers)(\"dropdown\", false);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i4.NgControlStatus, i4.NgModel, i5.Button, i6.TabView, i6.TabPanel, i7.AutoComplete, i8.CustomerInfoComponent, i9.CustomerCompaniesComponent, i10.CustomerPartnerComponent, i11.CustomerSalesAreaComponent, i12.CustomerSalesTextsComponent],\n        styles: [\".p-back-button[_ngcontent-%COMP%]{float:right}\"]\n      });\n    }\n  }\n  return CustomerDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}