{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Central Atlas Tamazight [tzm]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tzm = moment.defineLocale('tzm', {\n    months: 'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split('_'),\n    monthsShort: 'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split('_'),\n    weekdays: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    weekdaysShort: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    weekdaysMin: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[ⴰⵙⴷⵅ ⴴ] LT',\n      nextDay: '[ⴰⵙⴽⴰ ⴴ] LT',\n      nextWeek: 'dddd [ⴴ] LT',\n      lastDay: '[ⴰⵚⴰⵏⵜ ⴴ] LT',\n      lastWeek: 'dddd [ⴴ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s',\n      past: 'ⵢⴰⵏ %s',\n      s: 'ⵉⵎⵉⴽ',\n      ss: '%d ⵉⵎⵉⴽ',\n      m: 'ⵎⵉⵏⵓⴺ',\n      mm: '%d ⵎⵉⵏⵓⴺ',\n      h: 'ⵙⴰⵄⴰ',\n      hh: '%d ⵜⴰⵙⵙⴰⵄⵉⵏ',\n      d: 'ⴰⵙⵙ',\n      dd: '%d oⵙⵙⴰⵏ',\n      M: 'ⴰⵢoⵓⵔ',\n      MM: '%d ⵉⵢⵢⵉⵔⵏ',\n      y: 'ⴰⵙⴳⴰⵙ',\n      yy: '%d ⵉⵙⴳⴰⵙⵏ'\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return tzm;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}