import { Component, OnInit } from '@angular/core';
import { OrganizationalService } from '../../organizational.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ActivatedRoute } from '@angular/router';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-employees',
  templateUrl: './employees.component.html',
  styleUrl: './employees.component.scss',
})
export class EmployeesComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public employeeDetails: any[] = [];
  public employees$?: Observable<any[]>;
  public employeeLoading = false;
  public employeeInput$ = new Subject<string>();
  public managerDetails: any[] = [];
  public organisational_unit_id: string = '';
  public addEmployeeDialogVisible: boolean = false;
  public addManagerDialogVisible: boolean = false;
  public visible: boolean = false;
  public position: string = 'right';
  public submitted = false;
  public editid: string = '';
  public editManagerid: string = '';
  public saving = false;
  private defaultOptions: any = [];

  public EmployeeForm: FormGroup = this.formBuilder.group({
    start_date: ['', [Validators.required]],
    end_date: ['', [Validators.required]],
    business_partner_internal_id: [''],
  });

  public ManagerForm: FormGroup = this.formBuilder.group({
    start_date: ['', [Validators.required]],
    end_date: ['', [Validators.required]],
    business_partner_internal_id: [''],
  });

  constructor(
    private route: ActivatedRoute,
    private organizationalservice: OrganizationalService,
    private formBuilder: FormBuilder,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedColumnsMap: { [key: string]: Column[] } = {
    employee: [],
    manager: [],
  };

  public cols: Column[] = [
    { field: 'end_date', header: 'Valid To' },
    { field: 'business_partner.bp_full_name', header: 'Employee' },
    { field: 'job_id', header: 'Job' },
  ];

  public colsmanager: Column[] = [
    { field: 'end_date', header: 'Valid To' },
    { field: 'business_partner.bp_full_name', header: 'Manager' },
  ];

  // Separate sort field/order for employee and manager
  sortFieldMap: { [key: string]: string } = {
    employee: '',
    manager: '',
  };
  sortOrderMap: { [key: string]: number } = {
    employee: 1,
    manager: 1,
  };

  // Sorting method
  customSort(field: string, module: 'employee' | 'manager'): void {
    let sortdetails;
    if (module === 'employee') {
      sortdetails = this.employeeDetails;
    } else if (module === 'manager') {
      sortdetails = this.managerDetails;
    } else {
      console.warn('Unknown module:', module);
      return;
    }

    let currentField = this.sortFieldMap[module];
    let currentOrder = this.sortOrderMap[module];

    if (currentField === field) {
      currentOrder = -currentOrder;
    } else {
      currentField = field;
      currentOrder = 1;
    }

    this.sortFieldMap[module] = currentField;
    this.sortOrderMap[module] = currentOrder;

    sortdetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;
      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string') {
        result = value1.localeCompare(value2);
      } else {
        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;
      }

      return currentOrder * result;
    });
  }

  // Utility to resolve nested field values
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    return field.indexOf('.') === -1
      ? data[field]
      : field.split('.').reduce((obj, key) => obj?.[key], data);
  }

  // Dynamic selected columns getter/setter
  getSelectedColumns(module: 'employee' | 'manager'): Column[] {
    return this._selectedColumnsMap[module];
  }

  setSelectedColumns(module: 'employee' | 'manager', val: Column[]) {
    const baseCols = module === 'employee' ? this.cols : this.colsmanager;
    this._selectedColumnsMap[module] = baseCols.filter((col) =>
      val.includes(col)
    );
  }

  // Column reorder handler (per module)
  onColumnReorder(event: any, module: 'employee' | 'manager') {
    const draggedCol = this._selectedColumnsMap[module][event.dragIndex];
    this._selectedColumnsMap[module].splice(event.dragIndex, 1);
    this._selectedColumnsMap[module].splice(event.dropIndex, 0, draggedCol);
  }

  ngOnInit(): void {
    this.loadEmployee();
    this.organisational_unit_id =
      this.route.parent?.snapshot.paramMap.get('id') || '';
    this.organizationalservice.organizational
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.employeeDetails = response?.crm_org_unit_employees || [];
          this.managerDetails = response?.crm_org_unit_managers || [];
        }
      });

    this._selectedColumnsMap['employee'] = this.cols;
    this._selectedColumnsMap['manager'] = this.colsmanager;
  }

  private loadEmployee(): void {
    this.employees$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.employeeInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.employeeLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'BUP003',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.organizationalservice.getEmployees(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Employee fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.employeeLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  editEmployee(employee: any) {
    this.addEmployeeDialogVisible = true;
    this.editid = employee?.documentId;

    this.defaultOptions = [];
    this.defaultOptions.push({
      bp_full_name: employee?.business_partner?.bp_full_name,
      bp_id: employee?.business_partner_internal_id,
    });
    this.loadEmployee();

    this.EmployeeForm.patchValue({
      start_date: employee?.start_date ? new Date(employee?.start_date) : null,
      end_date: employee?.end_date ? new Date(employee?.end_date) : null,
      business_partner_internal_id: employee?.business_partner_internal_id,
    });
  }

  editManager(manager: any) {
    this.addManagerDialogVisible = true;
    this.editManagerid = manager?.documentId;

    this.defaultOptions = [];
    this.defaultOptions.push({
      bp_full_name: manager?.business_partner?.bp_full_name,
      bp_id: manager?.business_partner_internal_id,
    });

    this.loadEmployee();

    this.ManagerForm.patchValue({
      start_date: manager?.start_date ? new Date(manager?.start_date) : null,
      end_date: manager?.end_date ? new Date(manager?.end_date) : null,
      business_partner_internal_id: manager?.business_partner_internal_id,
    });
  }

  async onSubmit() {
    this.submitted = true;
    this.visible = true;

    if (this.EmployeeForm.invalid) {
      console.log('Form is invalid:', this.EmployeeForm.errors);
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.EmployeeForm.value };

    const data = {
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      business_partner_internal_id: value?.business_partner_internal_id,
      organisational_unit_id: this.organisational_unit_id,
    };

    let employeeRequest$: Observable<any>;

    if (this.editid) {
      employeeRequest$ = this.organizationalservice.updateEmployee(
        this.editid,
        data
      );
    } else {
      employeeRequest$ = this.organizationalservice.createEmployee(data);
    }

    employeeRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({
      complete: () => {
        this.saving = false;
        this.addEmployeeDialogVisible = false;
        this.EmployeeForm.reset();
        this.messageservice.add({
          severity: 'success',
          detail: this.editid
            ? 'Employee updated successfully!'
            : 'Employee created successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.saving = false;
        this.addEmployeeDialogVisible = false;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  async onSubmitManager() {
    this.submitted = true;
    this.visible = true;

    if (this.ManagerForm.invalid) {
      console.log('Form is invalid:', this.ManagerForm.errors);
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.ManagerForm.value };

    const data = {
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      business_partner_internal_id: value?.business_partner_internal_id,
      organisational_unit_id: this.organisational_unit_id,
    };

    let managerRequest$: Observable<any>;

    if (this.editManagerid) {
      managerRequest$ = this.organizationalservice.updateManager(
        this.editManagerid,
        data
      );
    } else {
      managerRequest$ = this.organizationalservice.createManager(data);
    }

    managerRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({
      complete: () => {
        this.saving = false;
        this.addManagerDialogVisible = false;
        this.ManagerForm.reset();
        this.messageservice.add({
          severity: 'success',
          detail: this.editManagerid
            ? 'Manager updated successfully!'
            : 'Manager created successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.saving = false;
        this.addManagerDialogVisible = false;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  confirmRemove(item: any, module: string) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item, module);
      },
    });
  }

  remove(item: any, module: string) {
    let deleteObservable;

    if (module === 'employee') {
      deleteObservable = this.organizationalservice.deleteEmployee(
        item.documentId
      );
    } else if (module === 'manager') {
      deleteObservable = this.organizationalservice.deleteManager(
        item.documentId
      );
    } else {
      console.warn('Unknown module:', module);
      return;
    }
    deleteObservable.pipe(takeUntil(this.unsubscribe$)).subscribe({
      next: () => {
        this.messageservice.add({
          severity: 'success',
          detail: 'Record Deleted Successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  showNewDialog(position: string, dialog: string) {
    this.position = position;
    this.submitted = false;

    if (dialog === 'employee') {
      this.addEmployeeDialogVisible = true;
      this.EmployeeForm.reset();
    } else if (dialog === 'manager') {
      this.addManagerDialogVisible = true;
      this.ManagerForm.reset();
    }
  }

  get f(): any {
    return this.EmployeeForm.controls;
  }

  get fManager(): any {
    return this.ManagerForm.controls;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
