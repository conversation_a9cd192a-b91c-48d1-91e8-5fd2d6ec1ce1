{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Macedonian [mk]\n//! author : <PERSON><PERSON> : https://github.com/B0k0\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bkyceh\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var mk = moment.defineLocale('mk', {\n    months: 'јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември'.split('_'),\n    monthsShort: 'јан_фев_мар_апр_м<PERSON><PERSON>_јун_јул_авг_сеп_окт_ное_дек'.split('_'),\n    weekdays: 'недела_понеделник_вторник_среда_четврток_петок_сабота'.split('_'),\n    weekdaysShort: 'нед_пон_вто_сре_чет_пет_саб'.split('_'),\n    weekdaysMin: 'нe_пo_вт_ср_че_пе_сa'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Денес во] LT',\n      nextDay: '[Утре во] LT',\n      nextWeek: '[Во] dddd [во] LT',\n      lastDay: '[Вчера во] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 6:\n            return '[Изминатата] dddd [во] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[Изминатиот] dddd [во] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: 'пред %s',\n      s: 'неколку секунди',\n      ss: '%d секунди',\n      m: 'една минута',\n      mm: '%d минути',\n      h: 'еден час',\n      hh: '%d часа',\n      d: 'еден ден',\n      dd: '%d дена',\n      M: 'еден месец',\n      MM: '%d месеци',\n      y: 'една година',\n      yy: '%d години'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n    ordinal: function (number) {\n      var lastDigit = number % 10,\n        last2Digits = number % 100;\n      if (number === 0) {\n        return number + '-ев';\n      } else if (last2Digits === 0) {\n        return number + '-ен';\n      } else if (last2Digits > 10 && last2Digits < 20) {\n        return number + '-ти';\n      } else if (lastDigit === 1) {\n        return number + '-ви';\n      } else if (lastDigit === 2) {\n        return number + '-ри';\n      } else if (lastDigit === 7 || lastDigit === 8) {\n        return number + '-ми';\n      } else {\n        return number + '-ти';\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return mk;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "mk", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "lastDigit", "last2Digits", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/mk.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Macedonian [mk]\n//! author : <PERSON><PERSON> : https://github.com/B0k0\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bkyceh\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var mk = moment.defineLocale('mk', {\n        months: 'јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември'.split(\n            '_'\n        ),\n        monthsShort: 'јан_фев_мар_апр_ма<PERSON>_јун_јул_авг_сеп_окт_ное_дек'.split('_'),\n        weekdays: 'недела_понеделник_вторник_среда_четврток_петок_сабота'.split(\n            '_'\n        ),\n        weekdaysShort: 'нед_пон_вто_сре_чет_пет_саб'.split('_'),\n        weekdaysMin: 'нe_пo_вт_ср_че_пе_сa'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'D.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY H:mm',\n            LLLL: 'dddd, D MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[Денес во] LT',\n            nextDay: '[Утре во] LT',\n            nextWeek: '[Во] dddd [во] LT',\n            lastDay: '[Вчера во] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                    case 3:\n                    case 6:\n                        return '[Изминатата] dddd [во] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[Изминатиот] dddd [во] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'за %s',\n            past: 'пред %s',\n            s: 'неколку секунди',\n            ss: '%d секунди',\n            m: 'една минута',\n            mm: '%d минути',\n            h: 'еден час',\n            hh: '%d часа',\n            d: 'еден ден',\n            dd: '%d дена',\n            M: 'еден месец',\n            MM: '%d месеци',\n            y: 'една година',\n            yy: '%d години',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n        ordinal: function (number) {\n            var lastDigit = number % 10,\n                last2Digits = number % 100;\n            if (number === 0) {\n                return number + '-ев';\n            } else if (last2Digits === 0) {\n                return number + '-ен';\n            } else if (last2Digits > 10 && last2Digits < 20) {\n                return number + '-ти';\n            } else if (lastDigit === 1) {\n                return number + '-ви';\n            } else if (lastDigit === 2) {\n                return number + '-ри';\n            } else if (lastDigit === 7 || lastDigit === 8) {\n                return number + '-ми';\n            } else {\n                return number + '-ти';\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return mk;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,sFAAsF,CAACC,KAAK,CAChG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,uDAAuD,CAACF,KAAK,CACnE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,mBAAmB;MAC7BC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,2BAA2B;UACtC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,2BAA2B;QAC1C;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,6BAA6B;IACrDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,SAAS,GAAGD,MAAM,GAAG,EAAE;QACvBE,WAAW,GAAGF,MAAM,GAAG,GAAG;MAC9B,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAOA,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIE,WAAW,KAAK,CAAC,EAAE;QAC1B,OAAOF,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIE,WAAW,GAAG,EAAE,IAAIA,WAAW,GAAG,EAAE,EAAE;QAC7C,OAAOF,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,EAAE;QACxB,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,EAAE;QACxB,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC,EAAE;QAC3C,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM;QACH,OAAOA,MAAM,GAAG,KAAK;MACzB;IACJ,CAAC;IACDG,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}