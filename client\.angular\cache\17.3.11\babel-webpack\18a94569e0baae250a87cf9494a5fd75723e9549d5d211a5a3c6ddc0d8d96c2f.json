{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Galician [gl]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var gl = moment.defineLocale('gl', {\n    months: 'xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro'.split('_'),\n    monthsShort: 'xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'domingo_luns_martes_mércores_xoves_venres_sábado'.split('_'),\n    weekdaysShort: 'dom._lun._mar._mér._xov._ven._sáb.'.split('_'),\n    weekdaysMin: 'do_lu_ma_mé_xo_ve_sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [de] MMMM [de] YYYY',\n      LLL: 'D [de] MMMM [de] YYYY H:mm',\n      LLLL: 'dddd, D [de] MMMM [de] YYYY H:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[hoxe ' + (this.hours() !== 1 ? 'ás' : 'á') + '] LT';\n      },\n      nextDay: function () {\n        return '[mañá ' + (this.hours() !== 1 ? 'ás' : 'á') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [' + (this.hours() !== 1 ? 'ás' : 'a') + '] LT';\n      },\n      lastDay: function () {\n        return '[onte ' + (this.hours() !== 1 ? 'á' : 'a') + '] LT';\n      },\n      lastWeek: function () {\n        return '[o] dddd [pasado ' + (this.hours() !== 1 ? 'ás' : 'a') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (str) {\n        if (str.indexOf('un') === 0) {\n          return 'n' + str;\n        }\n        return 'en ' + str;\n      },\n      past: 'hai %s',\n      s: 'uns segundos',\n      ss: '%d segundos',\n      m: 'un minuto',\n      mm: '%d minutos',\n      h: 'unha hora',\n      hh: '%d horas',\n      d: 'un día',\n      dd: '%d días',\n      M: 'un mes',\n      MM: '%d meses',\n      y: 'un ano',\n      yy: '%d anos'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return gl;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "gl", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "hours", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "str", "indexOf", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/gl.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Galician [gl]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var gl = moment.defineLocale('gl', {\n        months: 'xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro'.split(\n            '_'\n        ),\n        monthsShort:\n            'xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'domingo_luns_martes_mércores_xoves_venres_sábado'.split('_'),\n        weekdaysShort: 'dom._lun._mar._mér._xov._ven._sáb.'.split('_'),\n        weekdaysMin: 'do_lu_ma_mé_xo_ve_sá'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D [de] MMMM [de] YYYY',\n            LLL: 'D [de] MMMM [de] YYYY H:mm',\n            LLLL: 'dddd, D [de] MMMM [de] YYYY H:mm',\n        },\n        calendar: {\n            sameDay: function () {\n                return '[hoxe ' + (this.hours() !== 1 ? 'ás' : 'á') + '] LT';\n            },\n            nextDay: function () {\n                return '[mañá ' + (this.hours() !== 1 ? 'ás' : 'á') + '] LT';\n            },\n            nextWeek: function () {\n                return 'dddd [' + (this.hours() !== 1 ? 'ás' : 'a') + '] LT';\n            },\n            lastDay: function () {\n                return '[onte ' + (this.hours() !== 1 ? 'á' : 'a') + '] LT';\n            },\n            lastWeek: function () {\n                return (\n                    '[o] dddd [pasado ' + (this.hours() !== 1 ? 'ás' : 'a') + '] LT'\n                );\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: function (str) {\n                if (str.indexOf('un') === 0) {\n                    return 'n' + str;\n                }\n                return 'en ' + str;\n            },\n            past: 'hai %s',\n            s: 'uns segundos',\n            ss: '%d segundos',\n            m: 'un minuto',\n            mm: '%d minutos',\n            h: 'unha hora',\n            hh: '%d horas',\n            d: 'un día',\n            dd: '%d días',\n            M: 'un mes',\n            MM: '%d meses',\n            y: 'un ano',\n            yy: '%d anos',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return gl;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wFAAwF,CAACC,KAAK,CAClG,GACJ,CAAC;IACDC,WAAW,EACP,6DAA6D,CAACD,KAAK,CAC/D,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,kDAAkD,CAACH,KAAK,CAAC,GAAG,CAAC;IACvEI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,4BAA4B;MACjCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,QAAQ,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM;MAChE,CAAC;MACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,QAAQ,IAAI,IAAI,CAACD,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM;MAChE,CAAC;MACDE,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,QAAQ,IAAI,IAAI,CAACF,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM;MAChE,CAAC;MACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,QAAQ,IAAI,IAAI,CAACH,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM;MAC/D,CAAC;MACDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OACI,mBAAmB,IAAI,IAAI,CAACJ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM;MAExE,CAAC;MACDK,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACnB,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;UACzB,OAAO,GAAG,GAAGD,GAAG;QACpB;QACA,OAAO,KAAK,GAAGA,GAAG;MACtB,CAAC;MACDE,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}