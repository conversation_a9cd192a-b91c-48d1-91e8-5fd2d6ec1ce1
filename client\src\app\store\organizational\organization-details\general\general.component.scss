.invalid-feedback,
.p-inputtext:invalid,
.is-checkbox-invalid,
.p-inputtext.is-invalid {
    color: var(--red-500);
    right: 10px;
}

::ng-deep {
    .opportunity-contact-popup {
        .p-dialog {
            margin-right: 50px;

            .p-dialog-header {
                background: var(--surface-0);
                border-bottom: 1px solid var(--surface-100);

                h4 {
                    margin: 0;
                }
            }

            .p-dialog-content {
                background: var(--surface-0);
                padding: 1.714rem;
            }
        }
    }
}