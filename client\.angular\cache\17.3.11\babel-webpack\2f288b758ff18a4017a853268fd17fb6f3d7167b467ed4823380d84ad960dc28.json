{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nimport { UniqueComponentId } from 'primeng/utils';\nlet SortAmountDownIcon = /*#__PURE__*/(() => {\n  class SortAmountDownIcon extends BaseIcon {\n    pathId;\n    ngOnInit() {\n      this.pathId = 'url(#' + UniqueComponentId() + ')';\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵSortAmountDownIcon_BaseFactory;\n      return function SortAmountDownIcon_Factory(t) {\n        return (ɵSortAmountDownIcon_BaseFactory || (ɵSortAmountDownIcon_BaseFactory = i0.ɵɵgetInheritedFactory(SortAmountDownIcon)))(t || SortAmountDownIcon);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SortAmountDownIcon,\n      selectors: [[\"SortAmountDownIcon\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M4.93953 10.5858L3.83759 11.6877V0.677419C3.83759 0.307097 3.53049 0 3.16017 0C2.78985 0 2.48275 0.307097 2.48275 0.677419V11.6877L1.38082 10.5858C1.11888 10.3239 0.685331 10.3239 0.423396 10.5858C0.16146 10.8477 0.16146 11.2813 0.423396 11.5432L2.68146 13.8013C2.74469 13.8645 2.81694 13.9097 2.89823 13.9458C2.97952 13.9819 3.06985 14 3.16017 14C3.25049 14 3.33178 13.9819 3.42211 13.9458C3.5034 13.9097 3.57565 13.8645 3.63888 13.8013L5.89694 11.5432C6.15888 11.2813 6.15888 10.8477 5.89694 10.5858C5.63501 10.3239 5.20146 10.3239 4.93953 10.5858ZM13.0957 0H7.22468C6.85436 0 6.54726 0.307097 6.54726 0.677419C6.54726 1.04774 6.85436 1.35484 7.22468 1.35484H13.0957C13.466 1.35484 13.7731 1.04774 13.7731 0.677419C13.7731 0.307097 13.466 0 13.0957 0ZM7.22468 5.41935H9.48275C9.85307 5.41935 10.1602 5.72645 10.1602 6.09677C10.1602 6.4671 9.85307 6.77419 9.48275 6.77419H7.22468C6.85436 6.77419 6.54726 6.4671 6.54726 6.09677C6.54726 5.72645 6.85436 5.41935 7.22468 5.41935ZM7.6763 8.12903H7.22468C6.85436 8.12903 6.54726 8.43613 6.54726 8.80645C6.54726 9.17677 6.85436 9.48387 7.22468 9.48387H7.6763C8.04662 9.48387 8.35372 9.17677 8.35372 8.80645C8.35372 8.43613 8.04662 8.12903 7.6763 8.12903ZM7.22468 2.70968H11.2892C11.6595 2.70968 11.9666 3.01677 11.9666 3.3871C11.9666 3.75742 11.6595 4.06452 11.2892 4.06452H7.22468C6.85436 4.06452 6.54726 3.75742 6.54726 3.3871C6.54726 3.01677 6.85436 2.70968 7.22468 2.70968Z\", \"fill\", \"currentColor\"], [3, \"id\"], [\"width\", \"14\", \"height\", \"14\", \"fill\", \"white\"]],\n      template: function SortAmountDownIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\");\n          i0.ɵɵelement(2, \"path\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"defs\")(4, \"clipPath\", 2);\n          i0.ɵɵelement(5, \"rect\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getClassNames());\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"clip-path\", ctx.pathId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"id\", ctx.pathId);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return SortAmountDownIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SortAmountDownIcon };\n//# sourceMappingURL=primeng-icons-sortamountdown.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}