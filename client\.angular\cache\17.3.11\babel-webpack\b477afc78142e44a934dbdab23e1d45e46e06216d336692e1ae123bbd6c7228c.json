{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ProspectsRoutingModule } from './prospects-routing.module';\nimport { ProspectsComponent } from './prospects.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EditorModule } from 'primeng/editor';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { TableModule } from 'primeng/table';\nimport { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';\nimport { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';\nimport { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';\nimport { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';\nimport { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';\nimport { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';\nimport { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';\nimport { AddProspectComponent } from './add-prospect/add-prospect.component';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DialogModule } from 'primeng/dialog';\nimport { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';\nimport { EmployeeSelectComponent } from './employee-select/employee-select.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class ProspectsModule {\n  static {\n    this.ɵfac = function ProspectsModule_Factory(t) {\n      return new (t || ProspectsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProspectsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, NgSelectModule, ProspectsRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, ToastModule, EditorModule, InputTextModule, ConfirmDialogModule, DialogModule, CheckboxModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProspectsModule, {\n    declarations: [ProspectsComponent, ProspectsDetailsComponent, ProspectsOverviewComponent, ProspectsContactsComponent, ProspectsSalesTeamComponent, ProspectsAiInsightsComponent, ProspectsOrganizationDataComponent, ProspectsAttachmentsComponent, ProspectsNotesComponent, AddProspectComponent, ProspectActivitiesComponent, EmployeeSelectComponent],\n    imports: [CommonModule, NgSelectModule, ProspectsRoutingModule, FormsModule, TableModule, ReactiveFormsModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, ToastModule, EditorModule, InputTextModule, ConfirmDialogModule, DialogModule, CheckboxModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgSelectModule", "ProspectsRoutingModule", "ProspectsComponent", "FormsModule", "ReactiveFormsModule", "BreadcrumbModule", "CalendarModule", "EditorModule", "DropdownModule", "CheckboxModule", "TableModule", "ProspectsDetailsComponent", "ProspectsOverviewComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "ToastModule", "MessageService", "ConfirmationService", "ProspectsAiInsightsComponent", "ProspectsAttachmentsComponent", "ProspectsContactsComponent", "ProspectsNotesComponent", "ProspectsOrganizationDataComponent", "ProspectsSalesTeamComponent", "AddProspectComponent", "ConfirmDialogModule", "DialogModule", "ProspectActivitiesComponent", "EmployeeSelectComponent", "SharedModule", "ProspectsModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ProspectsRoutingModule } from './prospects-routing.module';\r\nimport { ProspectsComponent } from './prospects.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';\r\nimport { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';\r\nimport { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';\r\nimport { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';\r\nimport { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';\r\nimport { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';\r\nimport { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';\r\nimport { AddProspectComponent } from './add-prospect/add-prospect.component';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';\r\nimport { EmployeeSelectComponent } from './employee-select/employee-select.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProspectsComponent,\r\n    ProspectsDetailsComponent,\r\n    ProspectsOverviewComponent,\r\n    ProspectsContactsComponent,\r\n    ProspectsSalesTeamComponent,\r\n    ProspectsAiInsightsComponent,\r\n    ProspectsOrganizationDataComponent,\r\n    ProspectsAttachmentsComponent,\r\n    ProspectsNotesComponent,\r\n    AddProspectComponent,\r\n    ProspectActivitiesComponent,\r\n    EmployeeSelectComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    NgSelectModule,\r\n    ProspectsRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ReactiveFormsModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    ToastModule,\r\n    EditorModule,\r\n    InputTextModule,\r\n    ConfirmDialogModule,\r\n    DialogModule,\r\n    CheckboxModule,\r\n    SharedModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class ProspectsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,4BAA4B,QAAQ,2EAA2E;AACxH,SAASC,6BAA6B,QAAQ,2EAA2E;AACzH,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,kCAAkC,QAAQ,uFAAuF;AAC1I,SAASC,2BAA2B,QAAQ,yEAAyE;AACrH,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,YAAY,QAAQ,8BAA8B;;AAwC3D,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBAFf,CAACd,cAAc,EAAEC,mBAAmB,CAAC;MAAAc,OAAA,GApB9ClC,YAAY,EACZC,cAAc,EACdC,sBAAsB,EACtBE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBU,YAAY,EACZN,cAAc,EACdQ,aAAa,EACbH,kBAAkB,EAClBR,gBAAgB,EAChBC,cAAc,EACdW,WAAW,EACXV,YAAY,EACZQ,eAAe,EACfY,mBAAmB,EACnBC,YAAY,EACZnB,cAAc,EACdsB,YAAY;IAAA;EAAA;;;2EAIHC,eAAe;IAAAE,YAAA,GApCxBhC,kBAAkB,EAClBS,yBAAyB,EACzBC,0BAA0B,EAC1BU,0BAA0B,EAC1BG,2BAA2B,EAC3BL,4BAA4B,EAC5BI,kCAAkC,EAClCH,6BAA6B,EAC7BE,uBAAuB,EACvBG,oBAAoB,EACpBG,2BAA2B,EAC3BC,uBAAuB;IAAAG,OAAA,GAGvBlC,YAAY,EACZC,cAAc,EACdC,sBAAsB,EACtBE,WAAW,EACXO,WAAW,EACXN,mBAAmB,EACnBU,YAAY,EACZN,cAAc,EACdQ,aAAa,EACbH,kBAAkB,EAClBR,gBAAgB,EAChBC,cAAc,EACdW,WAAW,EACXV,YAAY,EACZQ,eAAe,EACfY,mBAAmB,EACnBC,YAAY,EACZnB,cAAc,EACdsB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}