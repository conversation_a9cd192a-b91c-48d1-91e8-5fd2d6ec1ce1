{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BackofficeRoutingModule } from './backoffice-routing.module';\nimport { BackofficeComponent } from './backoffice.component';\nimport * as i0 from \"@angular/core\";\nexport class BackofficeModule {\n  static {\n    this.ɵfac = function BackofficeModule_Factory(t) {\n      return new (t || BackofficeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BackofficeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, BackofficeRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BackofficeModule, {\n    declarations: [BackofficeComponent],\n    imports: [CommonModule, BackofficeRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "BackofficeRoutingModule", "BackofficeComponent", "BackofficeModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\backoffice.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { BackofficeRoutingModule } from './backoffice-routing.module';\r\nimport { BackofficeComponent } from './backoffice.component';\r\n\r\n@NgModule({\r\n  declarations: [BackofficeComponent],\r\n  imports: [CommonModule, BackofficeRoutingModule],\r\n})\r\nexport class BackofficeModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,mBAAmB,QAAQ,wBAAwB;;AAM5D,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAFjBH,YAAY,EAAEC,uBAAuB;IAAA;EAAA;;;2EAEpCE,gBAAgB;IAAAC,YAAA,GAHZF,mBAAmB;IAAAG,OAAA,GACxBL,YAAY,EAAEC,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}