{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ExportService {\n  constructor(http) {\n    this.http = http;\n  }\n  download(exportUrl) {\n    return this.http.get(exportUrl, {\n      responseType: 'blob',\n      observe: 'events',\n      reportProgress: true\n    }).pipe(catchError(err => {\n      console.error('Download error:', err);\n      return throwError(() => new Error('Download failed'));\n    }));\n  }\n  save(blob, filename) {\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = filename;\n    a.click();\n    URL.revokeObjectURL(url);\n  }\n  static {\n    this.ɵfac = function ExportService_Factory(t) {\n      return new (t || ExportService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ExportService,\n      factory: ExportService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "ExportService", "constructor", "http", "download", "exportUrl", "get", "responseType", "observe", "reportProgress", "pipe", "err", "console", "error", "Error", "save", "blob", "filename", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "click", "revokeObjectURL", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\export\\export.service.ts"], "sourcesContent": ["import { HttpClient, HttpEvent } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ExportService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  download(exportUrl: string): Observable<HttpEvent<Blob>> {\r\n    return this.http\r\n      .get(exportUrl, {\r\n        responseType: 'blob',\r\n        observe: 'events',\r\n        reportProgress: true,\r\n      })\r\n      .pipe(\r\n        catchError((err) => {\r\n          console.error('Download error:', err);\r\n          return throwError(() => new Error('Download failed'));\r\n        })\r\n      );\r\n  }\r\n\r\n  save(blob: Blob, filename: string): void {\r\n    const url = URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = filename;\r\n    a.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAa,gBAAgB;;;AAKhD,OAAM,MAAOC,aAAa;EACxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,QAAQA,CAACC,SAAiB;IACxB,OAAO,IAAI,CAACF,IAAI,CACbG,GAAG,CAACD,SAAS,EAAE;MACdE,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,QAAQ;MACjBC,cAAc,EAAE;KACjB,CAAC,CACDC,IAAI,CACHV,UAAU,CAAEW,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAEF,GAAG,CAAC;MACrC,OAAOZ,UAAU,CAAC,MAAM,IAAIe,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACvD,CAAC,CAAC,CACH;EACL;EAEAC,IAAIA,CAACC,IAAU,EAAEC,QAAgB;IAC/B,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACjB,QAAQ,GAAGa,QAAQ;IACrBI,CAAC,CAACI,KAAK,EAAE;IACTN,GAAG,CAACO,eAAe,CAACR,GAAG,CAAC;EAC1B;;;uBAzBWjB,aAAa,EAAA0B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb7B,aAAa;MAAA8B,OAAA,EAAb9B,aAAa,CAAA+B,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}