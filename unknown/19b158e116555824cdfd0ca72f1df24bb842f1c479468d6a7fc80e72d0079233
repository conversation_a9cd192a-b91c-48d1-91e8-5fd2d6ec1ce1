{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BackofficeRoutingModule } from './backoffice-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let BackofficeModule = /*#__PURE__*/(() => {\n  class BackofficeModule {\n    static {\n      this.ɵfac = function BackofficeModule_Factory(t) {\n        return new (t || BackofficeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: BackofficeModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, BackofficeRoutingModule]\n      });\n    }\n  }\n  return BackofficeModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}