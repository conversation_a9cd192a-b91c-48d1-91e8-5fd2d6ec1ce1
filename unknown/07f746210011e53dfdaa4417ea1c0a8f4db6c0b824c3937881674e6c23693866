{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nvar $apply = require('./functionApply');\nvar actualApply = require('./actualApply');\n\n/** @type {import('./applyBind')} */\nmodule.exports = function applyBind() {\n  return actualApply(bind, $apply, arguments);\n};", "map": {"version": 3, "names": ["bind", "require", "$apply", "actualApply", "module", "exports", "applyBind", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/call-bind-apply-helpers/applyBind.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\nvar $apply = require('./functionApply');\nvar actualApply = require('./actualApply');\n\n/** @type {import('./applyBind')} */\nmodule.exports = function applyBind() {\n\treturn actualApply(bind, $apply, arguments);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIC,MAAM,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AACvC,IAAIE,WAAW,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACAG,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAAA,EAAG;EACrC,OAAOH,WAAW,CAACH,IAAI,EAAEE,MAAM,EAAEK,SAAS,CAAC;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}