{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { TableModule } from 'primeng/table';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\nimport { ActivitiesItemDetailComponent } from './activities-item-detail/activities-item-detail.component';\nimport { EditorModule } from 'primeng/editor';\nimport * as i0 from \"@angular/core\";\nexport class CommonFormModule {\n  static {\n    this.ɵfac = function CommonFormModule_Factory(t) {\n      return new (t || CommonFormModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CommonFormModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, EditorModule, DropdownModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CommonFormModule, {\n    declarations: [ActivitiesFormComponent, ActivitiesItemDetailComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, EditorModule, DropdownModule, CalendarModule],\n    exports: [ActivitiesFormComponent, ActivitiesItemDetailComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "DialogModule", "TableModule", "NgSelectModule", "ButtonModule", "DropdownModule", "CalendarModule", "ActivitiesFormComponent", "ActivitiesItemDetailComponent", "EditorModule", "CommonFormModule", "declarations", "imports", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\common-form\\common-form.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { TableModule } from 'primeng/table';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\r\nimport { ActivitiesItemDetailComponent } from './activities-item-detail/activities-item-detail.component';\r\nimport { EditorModule } from 'primeng/editor';\r\n\r\n@NgModule({\r\n  declarations: [ActivitiesFormComponent, ActivitiesItemDetailComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DialogModule,\r\n    TableModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    EditorModule,\r\n    DropdownModule,\r\n    CalendarModule,\r\n  ],\r\n  exports: [ActivitiesFormComponent, ActivitiesItemDetailComponent],\r\n})\r\nexport class CommonFormModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,YAAY,QAAQ,gBAAgB;;AAkB7C,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAbzBZ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZK,YAAY,EACZJ,cAAc,EACdC,cAAc;IAAA;EAAA;;;2EAILI,gBAAgB;IAAAC,YAAA,GAfZJ,uBAAuB,EAAEC,6BAA6B;IAAAI,OAAA,GAEnEd,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZK,YAAY,EACZJ,cAAc,EACdC,cAAc;IAAAO,OAAA,GAENN,uBAAuB,EAAEC,6BAA6B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}