{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SalesQuotesRoutingModule } from './sales-quotes-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let SalesQuotesModule = /*#__PURE__*/(() => {\n  class SalesQuotesModule {\n    static {\n      this.ɵfac = function SalesQuotesModule_Factory(t) {\n        return new (t || SalesQuotesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SalesQuotesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, SalesQuotesRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule, MultiSelectModule]\n      });\n    }\n  }\n  return SalesQuotesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}