{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class BackofficeComponent {\n  constructor(renderer) {\n    this.renderer = renderer;\n  }\n  ngOnInit() {\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-dark/magenta/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function BackofficeComponent_Factory(t) {\n      return new (t || BackofficeComponent)(i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BackofficeComponent,\n      selectors: [[\"app-backoffice\"]],\n      decls: 1,\n      vars: 0,\n      template: function BackofficeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BackofficeComponent", "constructor", "renderer", "ngOnInit", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "Renderer2", "selectors", "decls", "vars", "template", "BackofficeComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\backoffice.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\backoffice.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-backoffice',\r\n  templateUrl: './backoffice.component.html',\r\n  styleUrl: './backoffice.component.scss',\r\n})\r\nexport class BackofficeComponent {\r\n  constructor(private renderer: Renderer2) {}\r\n\r\n  ngOnInit(): void {\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-dark/magenta/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAoBC,QAAmB;IAAnB,KAAAA,QAAQ,GAARA,QAAQ;EAAc;EAE1CC,QAAQA,CAAA;IACN;IACA,MAAMC,IAAI,GAAG,yDAAyD;IACtE,MAAMC,IAAI,GAAG,IAAI,CAACH,QAAQ,CAACI,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACF,QAAQ,CAACM,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;EAChD;EAEAM,WAAWA,CAAA;IACT;IACA,MAAMN,IAAI,GAAGI,QAAQ,CAACG,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIP,IAAI,EAAE;MACRA,IAAI,CAACQ,MAAM,EAAE;IACf;EACF;;;uBAtBWb,mBAAmB,EAAAc,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,SAAA;IAAA;EAAA;;;YAAnBhB,mBAAmB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCR,EAAA,CAAAU,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}