{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccountComponent } from './account.component';\nimport { AccountDetailsComponent } from './account-details/account-details.component';\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\nimport { AccountSalesQuoteDetailsComponent } from './account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesOrderDetailsComponent } from './account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { AccountInvoicesComponent } from './account-details/account-invoices/account-invoices.component';\nimport { AccountReturnsComponent } from './account-details/account-returns/account-returns.component';\nimport { ReturnOrderDetailsComponent } from './account-details/account-returns/return-order-details/return-order-details.component';\nimport { AccountCreditMemoComponent } from './account-details/account-credit-memo/account-credit-memo.component';\nimport { ActivitiesItemDetailComponent } from '../common-form/activities-item-detail/activities-item-detail.component';\nimport { OpportunityItemDetailComponent } from './account-details/account-opportunities/opportunity-item-detail/opportunity-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AccountComponent\n}, {\n  path: ':id',\n  component: AccountDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: AccountOverviewComponent\n  }, {\n    path: 'contacts',\n    component: AccountContactsComponent\n  }, {\n    path: 'sales-team',\n    component: AccountSalesTeamComponent\n  }, {\n    path: 'ai-insights',\n    component: AccountAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: AccountOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: AccountAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: AccountNotesComponent\n  }, {\n    path: 'activities',\n    component: AccountActivitiesComponent\n  }, {\n    path: 'activities/detail/:id',\n    component: ActivitiesItemDetailComponent\n  }, {\n    path: 'opportunities',\n    component: AccountOpportunitiesComponent\n  }, {\n    path: 'opportunities/detail/:id',\n    component: OpportunityItemDetailComponent\n  }, {\n    path: 'relationships',\n    component: AccountRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: AccountTicketsComponent\n  }, {\n    path: 'sales-quotes',\n    component: AccountSalesQuotesComponent\n  }, {\n    path: 'sales-quotes/:id',\n    component: AccountSalesQuoteDetailsComponent\n  }, {\n    path: 'sales-orders',\n    component: AccountSalesOrdersComponent\n  }, {\n    path: 'sales-orders/:id',\n    component: AccountSalesOrderDetailsComponent\n  }, {\n    path: 'invoices',\n    component: AccountInvoicesComponent\n  }, {\n    path: 'returns',\n    component: AccountReturnsComponent\n  }, {\n    path: 'return-order/:returnOrderId/:refDocId',\n    component: ReturnOrderDetailsComponent\n  }, {\n    path: 'credit-memos',\n    component: AccountCreditMemoComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport let AccountRoutingModule = /*#__PURE__*/(() => {\n  class AccountRoutingModule {\n    static {\n      this.ɵfac = function AccountRoutingModule_Factory(t) {\n        return new (t || AccountRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AccountRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AccountRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}