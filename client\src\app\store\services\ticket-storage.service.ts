import { Injectable } from '@angular/core';

export interface TicketFormData {
  invoice_no: string;
  order_no: string;
  credit_memo_no: string;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class TicketStorageService {

  private readonly STORAGE_PREFIX = 'ticket_form_data_';

  constructor() { }

  /**
   * Save ticket form data to local storage using ticket ID as key
   * @param ticketId - The unique ticket ID to use as storage key
   * @param formData - The form data containing invoice_no, order_no, and credit_memo_no
   */
  saveTicketFormData(ticketId: string, formData: Partial<TicketFormData>): void {
    if (!ticketId) {
      console.warn('Ticket ID is required to save form data');
      return;
    }
    
    const ticketFormData: TicketFormData = {
      invoice_no: formData.invoice_no || '',
      order_no: formData.order_no || '',
      credit_memo_no: formData.credit_memo_no || '',
      timestamp: new Date().toISOString()
    };
    
    try {
      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;
      localStorage.setItem(storageKey, JSON.stringify(ticketFormData));
    } catch (error) {
      console.error('Error saving ticket form data to local storage:', error);
    }
  }

  /**
   * Retrieve ticket form data from local storage by ticket ID
   * @param ticketId - The unique ticket ID used as storage key
   * @returns The stored ticket form data or null if not found
   */
  getTicketFormData(ticketId: string): TicketFormData | null {
    if (!ticketId) {
      console.warn('Ticket ID is required to retrieve form data');
      return null;
    }
    
    try {
      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;
      const storedData = localStorage.getItem(storageKey);
      return storedData ? JSON.parse(storedData) : null;
    } catch (error) {
      console.error('Error retrieving ticket form data from local storage:', error);
      return null;
    }
  }

  /**
   * Clear ticket form data from local storage by ticket ID
   * @param ticketId - The unique ticket ID used as storage key
   */
  clearTicketFormData(ticketId: string): void {
    if (!ticketId) {
      console.warn('Ticket ID is required to clear form data');
      return;
    }
    
    try {
      const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Error clearing ticket form data from local storage:', error);
    }
  }

  /**
   * Get all ticket form data keys from local storage
   * @returns Array of ticket IDs that have stored form data
   */
  getAllTicketFormDataKeys(): string[] {
    try {
      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          keys.push(key.replace(this.STORAGE_PREFIX, ''));
        }
      }
      return keys;
    } catch (error) {
      console.error('Error getting ticket form data keys from local storage:', error);
      return [];
    }
  }

  /**
   * Clear all ticket form data from local storage
   * Useful for cleanup operations
   */
  clearAllTicketFormData(): void {
    try {
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          keysToRemove.push(key);
        }
      }
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Error clearing all ticket form data from local storage:', error);
    }
  }

  /**
   * Clear old ticket form data (older than specified days)
   * @param daysOld - Number of days after which data should be considered old
   */
  clearOldTicketFormData(daysOld: number = 30): void {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          const storedData = localStorage.getItem(key);
          if (storedData) {
            try {
              const parsedData: TicketFormData = JSON.parse(storedData);
              const dataDate = new Date(parsedData.timestamp);
              if (dataDate < cutoffDate) {
                keysToRemove.push(key);
              }
            } catch (parseError) {
              // If we can't parse the data, consider it old and remove it
              keysToRemove.push(key);
            }
          }
        }
      }
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      if (keysToRemove.length > 0) {
        console.log(`Cleared ${keysToRemove.length} old ticket form data entries`);
      }
    } catch (error) {
      console.error('Error clearing old ticket form data from local storage:', error);
    }
  }
}
