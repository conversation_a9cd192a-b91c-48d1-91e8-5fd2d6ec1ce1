{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport { forkJoin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"primeng/checkbox\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"38rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  width: \"50rem\"\n});\nfunction AccountContactsComponent_ng_template_11_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountContactsComponent_ng_template_11_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction AccountContactsComponent_ng_template_11_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountContactsComponent_ng_template_11_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction AccountContactsComponent_ng_template_11_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 55);\n    i0.ɵɵlistener(\"click\", function AccountContactsComponent_ng_template_11_ng_container_8_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountContactsComponent_ng_template_11_ng_container_8_i_4_Template, 1, 1, \"i\", 49)(5, AccountContactsComponent_ng_template_11_ng_container_8_i_5_Template, 1, 0, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountContactsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 46);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 47);\n    i0.ɵɵlistener(\"click\", function AccountContactsComponent_ng_template_11_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"full_name\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵtemplate(6, AccountContactsComponent_ng_template_11_i_6_Template, 1, 1, \"i\", 49)(7, AccountContactsComponent_ng_template_11_i_7_Template, 1, 0, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AccountContactsComponent_ng_template_11_ng_container_8_Template, 6, 4, \"ng-container\", 51);\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 52);\n    i0.ɵɵtext(11, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.job_title) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.phone_number) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.mobile) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.email_address) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.contact_person_function_name == null ? null : contact_r6.contact_person_function_name.name) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.contact_person_department_name == null ? null : contact_r6.contact_person_department_name.name) || \"-\", \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 65);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r6.web_registered)(\"disabled\", true);\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 65);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r6.contact_person_vip_type)(\"disabled\", true);\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 65);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r6.validity_end_date)(\"disabled\", true);\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const contact_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", contact_r6 == null ? null : contact_r6.communication_preference, \" \");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 63);\n    i0.ɵɵtemplate(3, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_3_Template, 2, 1, \"ng-container\", 64)(4, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_4_Template, 2, 1, \"ng-container\", 64)(5, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_5_Template, 2, 1, \"ng-container\", 64)(6, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_6_Template, 2, 1, \"ng-container\", 64)(7, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_7_Template, 2, 1, \"ng-container\", 64)(8, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_8_Template, 2, 1, \"ng-container\", 64)(9, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_9_Template, 2, 3, \"ng-container\", 64)(10, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_10_Template, 2, 3, \"ng-container\", 64)(11, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_11_Template, 2, 3, \"ng-container\", 64)(12, AccountContactsComponent_ng_template_12_ng_container_7_ng_container_12_Template, 2, 1, \"ng-container\", 64);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"job_title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_person_function_name.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_person_department_name.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"web_registered\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_person_vip_type\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"validity_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"communication_preference\");\n  }\n}\nfunction AccountContactsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 56)(1, \"td\", 57);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 59)(4, \"div\", 60)(5, \"a\", 61);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, AccountContactsComponent_ng_template_12_ng_container_7_Template, 13, 11, \"ng-container\", 51);\n    i0.ɵɵelementStart(8, \"td\")(9, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AccountContactsComponent_ng_template_12_Template_button_click_9_listener() {\n      const contact_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editContact(contact_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (contact_r6 == null ? null : contact_r6.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (contact_r6 == null ? null : contact_r6.full_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountContactsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 66);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountContactsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 66);\n    i0.ɵɵtext(2, \" Loading contacts data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountContactsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_27_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"first_name\"].errors[\"required\"]);\n  }\n}\nfunction AccountContactsComponent_div_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_44_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"last_name\"].errors[\"required\"]);\n  }\n}\nfunction AccountContactsComponent_div_75_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_75_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_75_div_1_Template, 2, 0, \"div\", 37)(2, AccountContactsComponent_div_75_div_2_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"email_address\"].errors && ctx_r1.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction AccountContactsComponent_div_83_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_83_div_1_Template, 2, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.ContactForm.get(\"phone_number\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AccountContactsComponent_div_93_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mobile is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_93_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"mobile\"].errors[\"required\"]);\n  }\n}\nfunction AccountContactsComponent_div_94_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \" Please enter a valid Mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AccountContactsComponent_div_94_div_1_Template, 2, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.ContactForm.get(\"mobile\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AccountContactsComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 16)(2, \"label\", 70)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"remove_circle_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \"DeActivate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵelement(7, \"p-checkbox\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 16)(9, \"label\", 72)(10, \"span\", 18);\n    i0.ɵɵtext(11, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \"VIP Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 20);\n    i0.ɵɵelement(14, \"p-checkbox\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction AccountContactsComponent_ng_template_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountContactsComponent_ng_template_110_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.bp_full_name, \"\");\n  }\n}\nfunction AccountContactsComponent_ng_template_110_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.email, \"\");\n  }\n}\nfunction AccountContactsComponent_ng_template_110_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r8.phone, \"\");\n  }\n}\nfunction AccountContactsComponent_ng_template_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AccountContactsComponent_ng_template_110_span_2_Template, 2, 1, \"span\", 37)(3, AccountContactsComponent_ng_template_110_span_3_Template, 2, 1, \"span\", 37)(4, AccountContactsComponent_ng_template_110_span_4_Template, 2, 1, \"span\", 37);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.phone);\n  }\n}\nexport let AccountContactsComponent = /*#__PURE__*/(() => {\n  class AccountContactsComponent {\n    constructor(accountservice, formBuilder, messageservice) {\n      this.accountservice = accountservice;\n      this.formBuilder = formBuilder;\n      this.messageservice = messageservice;\n      this.unsubscribe$ = new Subject();\n      this.contactDetails = [];\n      this.id = '';\n      this.departments = null;\n      this.functions = null;\n      this.addDialogVisible = false;\n      this.existingDialogVisible = false;\n      this.visible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.editid = '';\n      this.documentId = '';\n      this.saving = false;\n      this.cpDepartments = [];\n      this.cpFunctions = [];\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.selectedContacts = [];\n      this.ContactForm = this.formBuilder.group({\n        first_name: ['', [Validators.required]],\n        middle_name: [''],\n        last_name: ['', [Validators.required]],\n        job_title: [''],\n        contact_person_function_name: [''],\n        contact_person_department_name: [''],\n        email_address: ['', [Validators.required, Validators.email]],\n        phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        contact_person_vip_type: [''],\n        validity_end_date: [''],\n        contactexisting: ['']\n      });\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'job_title',\n        header: 'Job Title'\n      }, {\n        field: 'phone_number',\n        header: 'Phone'\n      }, {\n        field: 'mobile',\n        header: 'Mobile'\n      }, {\n        field: 'email_address',\n        header: 'E-Mail'\n      }, {\n        field: 'contact_person_function_name.name',\n        header: 'Function'\n      }, {\n        field: 'contact_person_department_name.name',\n        header: 'Department'\n      }, {\n        field: 'web_registered',\n        header: 'Web Registered'\n      }, {\n        field: 'vip_contact',\n        header: 'VIP Contact'\n      }, {\n        field: 'deactivate',\n        header: 'Deactivate'\n      }, {\n        field: 'communication_preference',\n        header: 'Comm. Preference'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.contactDetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadContacts();\n      forkJoin({\n        departments: this.accountservice.getCPDepartment(),\n        functions: this.accountservice.getCPFunction()\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n        departments,\n        functions\n      }) => {\n        // Load departments\n        this.cpDepartments = (departments?.data || []).map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n        // Load functions\n        this.cpFunctions = (functions?.data || []).map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n        this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n          if (response) {\n            this.id = response?.bp_id;\n            this.documentId = response?.documentId;\n            this.contactDetails = response?.contact_companies || [];\n            this.contactDetails = this.contactDetails.map(contact => {\n              return {\n                ...contact,\n                full_name: [contact?.business_partner_person?.first_name, contact?.business_partner_person?.middle_name, contact?.business_partner_person?.last_name].filter(Boolean).join(' '),\n                first_name: contact?.business_partner_person?.first_name || '',\n                middle_name: contact?.business_partner_person?.middle_name || '',\n                last_name: contact?.business_partner_person?.last_name || '',\n                email_address: contact?.business_partner_person?.addresses?.[0]?.emails?.[0]?.email_address || '',\n                phone_number: (contact?.business_partner_person?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number,\n                mobile: (contact?.business_partner_person?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '3')?.phone_number,\n                // Ensure department & function values are set correctly\n                contact_person_department_name: this.cpDepartments?.find(d => d.value === contact?.person_func_and_dept?.contact_person_department) || null,\n                // Default value if not found\n                contact_person_function_name: this.cpFunctions?.find(f => f.value === contact?.person_func_and_dept?.contact_person_function) || null,\n                // Default value if not found\n                job_title: contact?.business_partner_person?.bp_extension?.job_title || '',\n                contact_person_vip_type: contact?.person_func_and_dept?.contact_person_vip_type ? true : false,\n                web_registered: contact?.business_partner_person?.bp_extension?.web_registered ? true : false,\n                communication_preference: contact?.business_partner_person?.addresses?.[0]?.prfrd_comm_medium_type || '-',\n                validity_end_date: new Date().toISOString().split('T')[0] < contact?.validity_end_date?.split('T')[0] ? false : true\n              };\n            });\n          }\n        });\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    reactivateSelectedContacts() {\n      if (!this.selectedContacts || this.selectedContacts.length === 0) {\n        return;\n      }\n      const reactivateRequests = this.selectedContacts.map(contact => this.accountservice.updateReactivate(contact).toPromise());\n      Promise.all(reactivateRequests).then(() => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Contacts Reactivated successfully!.'\n        });\n        this.accountservice.getAccountByID(this.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        this.selectedContacts = [];\n      }).catch(error => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error during bulk update :' + error\n        });\n      });\n    }\n    loadContacts() {\n      this.contacts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP001',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.accountservice.getContacts(params).pipe(map(data => {\n          return data || []; // Make sure to return correct data structure\n        }), tap(() => this.contactLoading = false), catchError(error => {\n          this.contactLoading = false;\n          return of([]);\n        }));\n      })));\n    }\n    editContact(contact) {\n      this.addDialogVisible = true;\n      this.editid = contact?.documentId;\n      this.ContactForm.patchValue({\n        first_name: contact.first_name,\n        middle_name: contact.middle_name,\n        last_name: contact.last_name,\n        job_title: contact.job_title,\n        email_address: contact.email_address,\n        phone_number: contact.phone_number,\n        mobile: contact.mobile,\n        validity_end_date: contact.validity_end_date,\n        contact_person_vip_type: contact.contact_person_vip_type,\n        contactexisting: '',\n        // Ensure department & function are set correctly\n        contact_person_function_name: this.cpFunctions.find(f => f.value === contact?.contact_person_function_name?.value) || null,\n        contact_person_department_name: this.cpDepartments.find(d => d.value === contact?.contact_person_department_name?.value) || null\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        _this.visible = true;\n        if (_this.ContactForm.value?.contactexisting) {\n          const existing = _this.ContactForm.value.contactexisting;\n          const data = {\n            bp_person_id: existing?.bp_id,\n            bp_id: _this.id\n          };\n          _this.saving = true;\n          _this.accountservice.createExistingContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.existingDialogVisible = false;\n              _this.ContactForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Contact Added successfully!.'\n              });\n              _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: () => {\n              _this.saving = false;\n              _this.addDialogVisible = false;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n          // Skip rest of logic for new contact\n          return;\n        }\n        if (_this.ContactForm.invalid) {\n          console.log('Form is invalid:', _this.ContactForm.errors);\n          _this.visible = true;\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.ContactForm.value\n        };\n        const data = {\n          bp_id: _this.id,\n          first_name: value?.first_name || '',\n          middle_name: value?.middle_name,\n          last_name: value?.last_name || '',\n          job_title: value?.job_title || '',\n          contact_person_function_name: value?.contact_person_function_name?.name || '',\n          contact_person_function: value?.contact_person_function_name?.value || '',\n          contact_person_department_name: value?.contact_person_department_name?.name || '',\n          contact_person_department: value?.contact_person_department_name?.value || '',\n          email_address: value?.email_address,\n          phone_number: value?.phone_number,\n          mobile: value?.mobile,\n          contact_person_vip_type: value?.contact_person_vip_type,\n          validity_end_date: value?.validity_end_date ? new Date().toISOString().split('T')[0] : '9999-12-29'\n        };\n        if (_this.editid) {\n          _this.accountservice.updateContact(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.addDialogVisible = false;\n              _this.ContactForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Contact Updated successfully!.'\n              });\n              _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.addDialogVisible = false;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.accountservice.createContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.addDialogVisible = false;\n              _this.existingDialogVisible = false;\n              _this.ContactForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Contact created successfully!.'\n              });\n              _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.addDialogVisible = false;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    showNewDialog(position) {\n      this.position = position;\n      this.addDialogVisible = true;\n      this.submitted = false;\n      this.ContactForm.reset();\n    }\n    showExistingDialog(position) {\n      this.position = position;\n      this.existingDialogVisible = true;\n    }\n    get f() {\n      return this.ContactForm.controls;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountContactsComponent_Factory(t) {\n        return new (t || AccountContactsComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountContactsComponent,\n        selectors: [[\"app-account-contacts\"]],\n        decls: 114,\n        vars: 66,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Reactivate\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"font-semibold\", 3, \"click\", \"rounded\", \"styleClass\", \"disabled\"], [\"label\", \"Add New\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"selectionChange\", \"onColReorder\", \"value\", \"selection\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"account-contact-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"First Name \", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"first_name\", \"formControlName\", \"first_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [\"for\", \"Middle Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"middle_name\", \"formControlName\", \"middle_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Last Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"last_name\", \"formControlName\", \"last_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Job Title\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"job_title\", \"formControlName\", \"job_title\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Function\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_function_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Function\", 3, \"options\", \"styleClass\"], [\"for\", \"Department\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_department_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Department\", 3, \"options\", \"styleClass\"], [\"for\", \"Email\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"email\", \"id\", \"email_address\", \"formControlName\", \"email_address\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Phone\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"phone_number\", \"formControlName\", \"phone_number\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"for\", \"Mobile\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"mobile\", \"formControlName\", \"mobile\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"font-medium\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"binary\", \"ngModel\", \"disabled\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"p-error\"], [\"for\", \"DeActivate\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"validity_end_date\", \"formControlName\", \"validity_end_date\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"], [\"for\", \"VIP Contact\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"contact_person_vip_type\", \"formControlName\", \"contact_person_vip_type\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"]],\n        template: function AccountContactsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Contacts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_p_button_click_5_listener() {\n              return ctx.reactivateSelectedContacts();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-button\", 5);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_p_button_click_6_listener() {\n              return ctx.showNewDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p-button\", 6);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_p_button_click_7_listener() {\n              return ctx.showExistingDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p-multiSelect\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountContactsComponent_Template_p_multiSelect_ngModelChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"p-table\", 9);\n            i0.ɵɵtwoWayListener(\"selectionChange\", function AccountContactsComponent_Template_p_table_selectionChange_10_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedContacts, $event) || (ctx.selectedContacts = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onColReorder\", function AccountContactsComponent_Template_p_table_onColReorder_10_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(11, AccountContactsComponent_ng_template_11_Template, 12, 3, \"ng-template\", 10)(12, AccountContactsComponent_ng_template_12_Template, 10, 4, \"ng-template\", 11)(13, AccountContactsComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12)(14, AccountContactsComponent_ng_template_14_Template, 3, 0, \"ng-template\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"p-dialog\", 14);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AccountContactsComponent_Template_p_dialog_visibleChange_15_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(16, AccountContactsComponent_ng_template_16_Template, 2, 0, \"ng-template\", 10);\n            i0.ɵɵelementStart(17, \"form\", 15)(18, \"div\", 16)(19, \"label\", 17)(20, \"span\", 18);\n            i0.ɵɵtext(21, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(22, \"First Name \");\n            i0.ɵɵelementStart(23, \"span\", 19);\n            i0.ɵɵtext(24, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 20);\n            i0.ɵɵelement(26, \"input\", 21);\n            i0.ɵɵtemplate(27, AccountContactsComponent_div_27_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 23)(30, \"span\", 18);\n            i0.ɵɵtext(31, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32, \"Middle Name \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 20);\n            i0.ɵɵelement(34, \"input\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 16)(36, \"label\", 25)(37, \"span\", 18);\n            i0.ɵɵtext(38, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(39, \"Last Name \");\n            i0.ɵɵelementStart(40, \"span\", 19);\n            i0.ɵɵtext(41, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 20);\n            i0.ɵɵelement(43, \"input\", 26);\n            i0.ɵɵtemplate(44, AccountContactsComponent_div_44_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 16)(46, \"label\", 27)(47, \"span\", 18);\n            i0.ɵɵtext(48, \"work\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(49, \"Job Title \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 20);\n            i0.ɵɵelement(51, \"input\", 28);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 16)(53, \"label\", 29)(54, \"span\", 18);\n            i0.ɵɵtext(55, \"functions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(56, \"Function \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 20);\n            i0.ɵɵelement(58, \"p-dropdown\", 30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\", 16)(60, \"label\", 31)(61, \"span\", 18);\n            i0.ɵɵtext(62, \"inbox_text_person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(63, \"Department \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"div\", 20);\n            i0.ɵɵelement(65, \"p-dropdown\", 32);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"div\", 16)(67, \"label\", 33)(68, \"span\", 18);\n            i0.ɵɵtext(69, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(70, \"Email\");\n            i0.ɵɵelementStart(71, \"span\", 19);\n            i0.ɵɵtext(72, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"div\", 20);\n            i0.ɵɵelement(74, \"input\", 34);\n            i0.ɵɵtemplate(75, AccountContactsComponent_div_75_Template, 3, 2, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"div\", 16)(77, \"label\", 35)(78, \"span\", 18);\n            i0.ɵɵtext(79, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(80, \"Phone \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"div\", 20);\n            i0.ɵɵelement(82, \"input\", 36);\n            i0.ɵɵtemplate(83, AccountContactsComponent_div_83_Template, 2, 1, \"div\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(84, \"div\", 16)(85, \"label\", 38)(86, \"span\", 18);\n            i0.ɵɵtext(87, \"smartphone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(88, \"Mobile # \");\n            i0.ɵɵelementStart(89, \"span\", 19);\n            i0.ɵɵtext(90, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(91, \"div\", 20);\n            i0.ɵɵelement(92, \"input\", 39);\n            i0.ɵɵtemplate(93, AccountContactsComponent_div_93_Template, 2, 1, \"div\", 22)(94, AccountContactsComponent_div_94_Template, 2, 1, \"div\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(95, AccountContactsComponent_div_95_Template, 15, 2, \"div\", 37);\n            i0.ɵɵelementStart(96, \"div\", 40)(97, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_button_click_97_listener() {\n              return ctx.addDialogVisible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_button_click_98_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(99, \"p-dialog\", 14);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AccountContactsComponent_Template_p_dialog_visibleChange_99_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(100, AccountContactsComponent_ng_template_100_Template, 2, 0, \"ng-template\", 10);\n            i0.ɵɵelementStart(101, \"form\", 15)(102, \"div\", 16)(103, \"label\", 43)(104, \"span\", 18);\n            i0.ɵɵtext(105, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(106, \"Contacts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"div\", 20)(108, \"ng-select\", 44);\n            i0.ɵɵpipe(109, \"async\");\n            i0.ɵɵtemplate(110, AccountContactsComponent_ng_template_110_Template, 5, 4, \"ng-template\", 45);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(111, \"div\", 40)(112, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_button_click_112_listener() {\n              return ctx.existingDialogVisible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function AccountContactsComponent_Template_button_click_113_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_33_0;\n            let tmp_36_0;\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"px-3\")(\"disabled\", !ctx.selectedContacts || ctx.selectedContacts.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.contactDetails);\n            i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedContacts);\n            i0.ɵɵproperty(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(56, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c1, ctx.submitted && ctx.f[\"first_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"first_name\"].errors);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c1, ctx.submitted && ctx.f[\"last_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"last_name\"].errors);\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"options\", ctx.cpFunctions)(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c1, ctx.submitted && ctx.f[\"email_address\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_33_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_33_0.touched) && ((tmp_33_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_33_0.invalid));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c1, ctx.submitted && ctx.f[\"mobile\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"mobile\"].errors);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_36_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_36_0.touched) && ((tmp_36_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_36_0.invalid));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.editid && ctx.ContactForm.value.first_name);\n            i0.ɵɵadvance(4);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(65, _c2));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(109, 54, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.Tooltip, i3.PrimeTemplate, i6.Dropdown, i7.Table, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i7.TableCheckbox, i7.TableHeaderCheckbox, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i8.ButtonDirective, i8.Button, i9.NgSelectComponent, i9.NgOptionTemplateDirective, i10.Checkbox, i11.InputText, i12.Dialog, i13.MultiSelect, i4.AsyncPipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .account-contact-popup .p-dialog{margin-right:50px}  .account-contact-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .account-contact-popup .p-dialog .p-dialog-header h4{margin:0}  .account-contact-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return AccountContactsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}