{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/account/account.service\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"primeng/table\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/button\";\nfunction OpportunitiesSalesTeamComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \" First Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 13)(6, \"div\", 11);\n    i0.ɵɵtext(7, \" Last Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 15)(10, \"div\", 11);\n    i0.ɵɵtext(11, \" Role \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 17)(14, \"div\", 11);\n    i0.ɵɵtext(15, \" Email \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction OpportunitiesSalesTeamComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const team_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.business_partner == null ? null : team_r1.business_partner.first_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.business_partner == null ? null : team_r1.business_partner.last_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.partner_role) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r1 == null ? null : team_r1.business_partner == null ? null : team_r1.business_partner.addresses == null ? null : team_r1.business_partner.addresses[0] == null ? null : team_r1.business_partner.addresses[0].emails == null ? null : team_r1.business_partner.addresses[0].emails[0] == null ? null : team_r1.business_partner.addresses[0].emails[0].email_address) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesSalesTeamComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 20);\n    i0.ɵɵtext(2, \"No Sales Teams found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesSalesTeamComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 20);\n    i0.ɵɵtext(2, \"Loading Sales Teams data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OpportunitiesSalesTeamComponent {\n  constructor(accountservice, opportunitiesservice) {\n    this.accountservice = accountservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.salesteamDetails = null;\n    this.id = '';\n    this.partnerfunction = [];\n    this.partnerLoading = false;\n  }\n  ngOnInit() {\n    this.loadPartners();\n  }\n  loadPartners() {\n    this.partnerLoading = true;\n    this.accountservice.getCRMPartner().pipe(takeUntil(this.unsubscribe$), switchMap(partners => {\n      this.partnerfunction = partners || [];\n      return this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$));\n    })).subscribe({\n      next: response => {\n        if (!response) return;\n        this.id = response?.customer?.customer_id;\n        const filteredPartners = response.customer.partner_functions.filter(pf => this.partnerfunction.some(partner => partner?.value === pf.partner_function));\n        if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\n          this.salesteamDetails = filteredPartners.map(pf => {\n            const matchedPartner = this.partnerfunction.find(partner => partner?.value === pf.partner_function);\n            return {\n              ...pf,\n              partner_role: matchedPartner ? matchedPartner?.label : null,\n              // Adding partner label\n              addresses: this.filterXXDefaultAddresses(response?.customer?.partner_functions?.business_partner?.addresses || [])\n            };\n          });\n        } else {\n          this.salesteamDetails = [];\n        }\n        this.partnerLoading = false;\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n        this.partnerfunction = [];\n        this.salesteamDetails = [];\n        this.partnerLoading = false;\n      },\n      complete: () => {\n        console.log('Partner function and employee details loaded successfully.');\n      }\n    });\n  }\n  filterXXDefaultAddresses(addresses) {\n    return addresses.filter(address => address.address_usages && address.address_usages.some(usage => usage.address_usage === 'XXDEFAULT'));\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesSalesTeamComponent_Factory(t) {\n      return new (t || OpportunitiesSalesTeamComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.OpportunitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesSalesTeamComponent,\n      selectors: [[\"app-opportunities-sales-team\"]],\n      decls: 11,\n      vars: 6,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"icon\", \"pi pi-angle-left\", 1, \"-ml-5\", 3, \"rounded\", \"outlined\", \"styleClass\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"business_partner.first_name\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"business_partner.first_name\"], [\"pSortableColumn\", \"business_partner.last_name\"], [\"field\", \"business_partner.last_name\"], [\"pSortableColumn\", \"partner_role\"], [\"field\", \"partner_role\"], [\"pSortableColumn\", \"address.email_address\"], [\"field\", \"address.email_address\"], [1, \"border-round-left-lg\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"]],\n      template: function OpportunitiesSalesTeamComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"p-button\", 2);\n          i0.ɵɵelementStart(3, \"h4\", 3);\n          i0.ɵɵtext(4, \"Sales Team\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, OpportunitiesSalesTeamComponent_ng_template_7_Template, 17, 0, \"ng-template\", 6)(8, OpportunitiesSalesTeamComponent_ng_template_8_Template, 9, 4, \"ng-template\", 7)(9, OpportunitiesSalesTeamComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, OpportunitiesSalesTeamComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.salesteamDetails)(\"rows\", 8)(\"paginator\", true);\n        }\n      },\n      dependencies: [i3.Table, i4.PrimeTemplate, i3.SortableColumn, i3.SortIcon, i5.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "switchMap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "team_r1", "business_partner", "first_name", "last_name", "partner_role", "addresses", "emails", "email_address", "OpportunitiesSalesTeamComponent", "constructor", "accountservice", "opportunitiesservice", "unsubscribe$", "salesteamDetails", "id", "partnerfunction", "partner<PERSON><PERSON><PERSON>", "ngOnInit", "loadPartners", "get<PERSON><PERSON><PERSON><PERSON>", "pipe", "partners", "opportunity", "subscribe", "next", "response", "customer", "customer_id", "filteredPartners", "partner_functions", "filter", "pf", "some", "partner", "value", "partner_function", "Array", "isArray", "length", "map", "<PERSON><PERSON><PERSON><PERSON>", "find", "label", "filterXXDefaultAddresses", "error", "console", "complete", "log", "address", "address_usages", "usage", "address_usage", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "OpportunitiesService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesSalesTeamComponent_Template", "rf", "ctx", "ɵɵtemplate", "OpportunitiesSalesTeamComponent_ng_template_7_Template", "OpportunitiesSalesTeamComponent_ng_template_8_Template", "OpportunitiesSalesTeamComponent_ng_template_9_Template", "OpportunitiesSalesTeamComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-sales-team\\opportunities-sales-team.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-sales-team\\opportunities-sales-team.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AccountService } from 'src/app/store/account/account.service';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { switchMap } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-sales-team',\r\n  templateUrl: './opportunities-sales-team.component.html',\r\n  styleUrl: './opportunities-sales-team.component.scss',\r\n})\r\nexport class OpportunitiesSalesTeamComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public salesteamDetails: any = null;\r\n  public id: string = '';\r\n  public partnerfunction: { label: string; value: string }[] = [];\r\n  public partnerLoading = false;\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private opportunitiesservice: OpportunitiesService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadPartners();\r\n  }\r\n\r\n  private loadPartners(): void {\r\n    this.partnerLoading = true;\r\n\r\n    this.accountservice\r\n      .getCRMPartner()\r\n      .pipe(\r\n        takeUntil(this.unsubscribe$),\r\n        switchMap((partners: any) => {\r\n          this.partnerfunction = partners || [];\r\n\r\n          return this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$));\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (!response) return;\r\n\r\n          this.id = response?.customer?.customer_id;\r\n          const filteredPartners = response.customer.partner_functions.filter(\r\n            (pf: any) =>\r\n              this.partnerfunction.some(\r\n                (partner: any) => partner?.value === pf.partner_function\r\n              )\r\n          );\r\n\r\n          if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\r\n            this.salesteamDetails = filteredPartners.map((pf: any) => {\r\n              const matchedPartner = this.partnerfunction.find(\r\n                (partner: any) => partner?.value === pf.partner_function\r\n              );\r\n\r\n              return {\r\n                ...pf,\r\n                partner_role: matchedPartner ? matchedPartner?.label : null, // Adding partner label\r\n                addresses: this.filterXXDefaultAddresses(\r\n                  response?.customer?.partner_functions?.business_partner\r\n                    ?.addresses || []\r\n                ),\r\n              };\r\n            });\r\n          } else {\r\n            this.salesteamDetails = [];\r\n          }\r\n\r\n          this.partnerLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching data:', error);\r\n          this.partnerfunction = [];\r\n          this.salesteamDetails = [];\r\n          this.partnerLoading = false;\r\n        },\r\n        complete: () => {\r\n          console.log(\r\n            'Partner function and employee details loaded successfully.'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  private filterXXDefaultAddresses(addresses: any[]): any[] {\r\n    return addresses.filter(\r\n      (address: any) =>\r\n        address.address_usages &&\r\n        address.address_usages.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n    );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n            [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\" class=\"-ml-5\" />\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Team</h4>\r\n        <!-- <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [outlined]=\"true\" class=\"ml-auto\"\r\n            [styleClass]=\"'w-5rem font-semibold'\" /> -->\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"salesteamDetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <!-- <th class=\"border-round-left-lg\" pSortableColumn=\"id\">\r\n                        <div class=\"flex justify-content-between align-items-center\">\r\n                            ID #\r\n                            <div class=\"flex align-items-center\">\r\n                                <p-sortIcon field=\"id\"></p-sortIcon>\r\n                            </div>\r\n                        </div>\r\n                    </th> -->\r\n                    <th pSortableColumn=\"business_partner.first_name\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            First Name\r\n                            <p-sortIcon field=\"business_partner.first_name\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"business_partner.last_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Last Name\r\n                            <p-sortIcon field=\"business_partner.last_name\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"partner_role\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Role\r\n                            <p-sortIcon field=\"partner_role\" />\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"address.email_address\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Email\r\n                            <p-sortIcon field=\"address.email_address\" />\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-team>\r\n                <tr>\r\n                    <!-- <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ team?.id || '-'}}\r\n                    </td> -->\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ team?.business_partner?.first_name || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.business_partner?.last_name || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.partner_role || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ team?.business_partner?.addresses?.[0]?.emails?.[0]?.email_address ||\r\n                        '-'}}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">No Sales Teams found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">Loading Sales Teams data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;ICoBlBC,EAVR,CAAAC,cAAA,SAAI,aAS+E,cAChC;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAAG,SAAA,qBAAkD;IAE1DH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAiD,cACF;IACvCD,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAG,SAAA,qBAAiD;IAEzDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAmC,eACY;IACvCD,EAAA,CAAAE,MAAA,cACA;IAAAF,EAAA,CAAAG,SAAA,sBAAmC;IAE3CH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,cAA4C,eACG;IACvCD,EAAA,CAAAE,MAAA,eACA;IAAAF,EAAA,CAAAG,SAAA,sBAA4C;IAGxDH,EAFQ,CAAAI,YAAA,EAAM,EACL,EACJ;;;;;IAQDJ,EAJJ,CAAAC,cAAA,SAAI,aAIiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAEJ;IACJF,EADI,CAAAI,YAAA,EAAK,EACJ;;;;IAZGJ,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAC,UAAA,cACJ;IAEIT,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAE,SAAA,cACJ;IAEIV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAI,YAAA,cACJ;IAEIX,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,kBAAA,OAAAC,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAI,SAAA,kBAAAL,OAAA,CAAAC,gBAAA,CAAAI,SAAA,qBAAAL,OAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,kBAAAN,OAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,qBAAAN,OAAA,CAAAC,gBAAA,CAAAI,SAAA,IAAAC,MAAA,IAAAC,aAAA,cAEJ;;;;;IAKAd,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IACtEF,EADsE,CAAAI,YAAA,EAAK,EACtE;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IACzFF,EADyF,CAAAI,YAAA,EAAK,EACzF;;;ADnErB,OAAM,MAAOW,+BAA+B;EAO1CC,YACUC,cAA8B,EAC9BC,oBAA0C;IAD1C,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IARtB,KAAAC,YAAY,GAAG,IAAItB,OAAO,EAAQ;IACnC,KAAAuB,gBAAgB,GAAQ,IAAI;IAC5B,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,eAAe,GAAuC,EAAE;IACxD,KAAAC,cAAc,GAAG,KAAK;EAK1B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACF,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACN,cAAc,CAChBS,aAAa,EAAE,CACfC,IAAI,CACH7B,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,EAC5BpB,SAAS,CAAE6B,QAAa,IAAI;MAC1B,IAAI,CAACN,eAAe,GAAGM,QAAQ,IAAI,EAAE;MAErC,OAAO,IAAI,CAACV,oBAAoB,CAACW,WAAW,CAACF,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC;IACjF,CAAC,CAAC,CACH,CACAW,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,EAAE;QAEf,IAAI,CAACX,EAAE,GAAGW,QAAQ,EAAEC,QAAQ,EAAEC,WAAW;QACzC,MAAMC,gBAAgB,GAAGH,QAAQ,CAACC,QAAQ,CAACG,iBAAiB,CAACC,MAAM,CAChEC,EAAO,IACN,IAAI,CAAChB,eAAe,CAACiB,IAAI,CACtBC,OAAY,IAAKA,OAAO,EAAEC,KAAK,KAAKH,EAAE,CAACI,gBAAgB,CACzD,CACJ;QAED,IAAIC,KAAK,CAACC,OAAO,CAACT,gBAAgB,CAAC,IAAIA,gBAAgB,CAACU,MAAM,GAAG,CAAC,EAAE;UAClE,IAAI,CAACzB,gBAAgB,GAAGe,gBAAgB,CAACW,GAAG,CAAER,EAAO,IAAI;YACvD,MAAMS,cAAc,GAAG,IAAI,CAACzB,eAAe,CAAC0B,IAAI,CAC7CR,OAAY,IAAKA,OAAO,EAAEC,KAAK,KAAKH,EAAE,CAACI,gBAAgB,CACzD;YAED,OAAO;cACL,GAAGJ,EAAE;cACL3B,YAAY,EAAEoC,cAAc,GAAGA,cAAc,EAAEE,KAAK,GAAG,IAAI;cAAE;cAC7DrC,SAAS,EAAE,IAAI,CAACsC,wBAAwB,CACtClB,QAAQ,EAAEC,QAAQ,EAAEG,iBAAiB,EAAE5B,gBAAgB,EACnDI,SAAS,IAAI,EAAE;aAEtB;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACQ,gBAAgB,GAAG,EAAE;QAC5B;QAEA,IAAI,CAACG,cAAc,GAAG,KAAK;MAC7B,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC7B,eAAe,GAAG,EAAE;QACzB,IAAI,CAACF,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACG,cAAc,GAAG,KAAK;MAC7B,CAAC;MACD8B,QAAQ,EAAEA,CAAA,KAAK;QACbD,OAAO,CAACE,GAAG,CACT,4DAA4D,CAC7D;MACH;KACD,CAAC;EACN;EAEQJ,wBAAwBA,CAACtC,SAAgB;IAC/C,OAAOA,SAAS,CAACyB,MAAM,CACpBkB,OAAY,IACXA,OAAO,CAACC,cAAc,IACtBD,OAAO,CAACC,cAAc,CAACjB,IAAI,CACxBkB,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACJ;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxC,YAAY,CAACY,IAAI,EAAE;IACxB,IAAI,CAACZ,YAAY,CAACkC,QAAQ,EAAE;EAC9B;;;uBAzFWtC,+BAA+B,EAAAf,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA/BjD,+BAA+B;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVxCvE,EADJ,CAAAC,cAAA,aAA2D,aAC4B;UAC/ED,EAAA,CAAAG,SAAA,kBAC0E;UAC1EH,EAAA,CAAAC,cAAA,YAA+C;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAG7DF,EAH6D,CAAAI,YAAA,EAAK,EAG5D;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAgE1BD,EA9DA,CAAAyE,UAAA,IAAAC,sDAAA,0BAAgC,IAAAC,sDAAA,yBAqCO,IAAAC,sDAAA,yBAoBD,KAAAC,uDAAA,yBAKD;UAOjD7E,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;;;UAhFoCJ,EAAA,CAAAK,SAAA,GAAgB;UAC9CL,EAD8B,CAAA8E,UAAA,iBAAgB,kBAAkB,sDACX;UAOhD9E,EAAA,CAAAK,SAAA,GAA0B;UAAuCL,EAAjE,CAAA8E,UAAA,UAAAN,GAAA,CAAApD,gBAAA,CAA0B,WAAwB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}