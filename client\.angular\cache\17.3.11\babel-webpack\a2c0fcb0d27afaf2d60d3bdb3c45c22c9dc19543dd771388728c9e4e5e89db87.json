{"ast": null, "code": "import { concat, takeUntil } from 'rxjs';\nimport { Subject } from 'rxjs';\nimport { switchMap, tap, distinctUntilChanged, map } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"../prospects/prospects.service\";\nimport * as i4 from \"../account/account.service\";\nimport * as i5 from \"../services/service-ticket.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/calendar\";\nimport * as i13 from \"primeng/toast\";\nimport * as i14 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  \"padding-top\": \"10px\"\n});\nfunction ServiceTicketsComponent_ng_template_47_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r1.bp_full_name, \"\");\n  }\n}\nfunction ServiceTicketsComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ServiceTicketsComponent_ng_template_47_span_2_Template, 2, 1, \"span\", 40);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.bp_full_name);\n  }\n}\nexport let ServiceTicketsComponent = /*#__PURE__*/(() => {\n  class ServiceTicketsComponent {\n    constructor(renderer, route, messageService, confirmationService, prospectsservice, accountService, serviceTicketService, fb) {\n      this.renderer = renderer;\n      this.route = route;\n      this.messageService = messageService;\n      this.confirmationService = confirmationService;\n      this.prospectsservice = prospectsservice;\n      this.accountService = accountService;\n      this.serviceTicketService = serviceTicketService;\n      this.fb = fb;\n      this.bodyClass = 'service-ticket-body';\n      this.items = [{\n        label: 'Service Ticket',\n        routerLink: ['/store/service-tickets']\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: ['/']\n      };\n      this.id = '';\n      this.ticketDetails = null;\n      this.ticketStatuses = [];\n      this.accountDetails = null;\n      this.unsubscribe$ = new Subject();\n      this.employeeLoading = false;\n      this.employeeInput$ = new Subject();\n      this.priorityOptions = [{\n        label: 'Low',\n        value: 'Low'\n      }, {\n        label: 'Medium',\n        value: 'Medium'\n      }, {\n        label: 'High',\n        value: 'High'\n      }];\n      this.submitting = false;\n      this.closing = false;\n      this.ticketForm = this.fb.group({\n        id: [{\n          value: '',\n          disabled: true\n        }],\n        support_team: [''],\n        status_id: [''],\n        priority: ['Low'],\n        subject: [''],\n        account_id: [{\n          value: '',\n          disabled: true\n        }],\n        contact_id: [''],\n        assigned_to: [''],\n        description: ['', [Validators.required]],\n        scheduled_date: ['']\n      });\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('ticket-id') || '';\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        if (params.get('ticket-id') !== this.id) {\n          this.id = params.get('ticket-id') || '';\n          this.getTicketDetails();\n        }\n      });\n      this.renderer.addClass(document.body, this.bodyClass);\n      if (this.id) {\n        this.getTicketDetails();\n      }\n      this.getAllStatus();\n      this.loadEmployees();\n    }\n    getTicketDetails() {\n      this.serviceTicketService.getById(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.ticketDetails = response?.data?.[0] || null;\n          if (this.ticketDetails) {\n            this.ticketForm.patchValue({\n              id: this.ticketDetails.id || '',\n              support_team: this.ticketDetails.support_team || '',\n              status_id: this.ticketDetails.status_id || '',\n              priority: this.ticketDetails.priority || 'Low',\n              subject: this.ticketDetails.subject || '',\n              account_id: this.ticketDetails.account_id || '',\n              contact_id: this.ticketDetails.contact_id || '',\n              assigned_to: this.ticketDetails.assigned_to || '',\n              description: this.ticketDetails.description || '',\n              scheduled_date: this.ticketDetails.scheduled_date || null\n            });\n            this.employeeInput$.next(this.ticketDetails.assigned_to || '');\n          }\n          this.getBPDetails();\n          this.getContactDetails();\n        },\n        error: err => {\n          console.error('Error fetching ticket:', err);\n        }\n      });\n    }\n    getBPDetails() {\n      if (!this.ticketDetails) return;\n      this.accountService.getAccountByID(this.ticketDetails?.account_id, true).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.accountDetails = response?.data?.[0] || null;\n        },\n        error: err => {\n          console.error('Error fetching business partner details:', err);\n        }\n      });\n    }\n    getContactDetails() {\n      if (!this.ticketDetails) return;\n      this.accountService.getContactByID(this.ticketDetails?.contact_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.accountDetails = response?.data?.[0] || null;\n        },\n        error: err => {\n          console.error('Error fetching business partner details:', err);\n        }\n      });\n    }\n    getAllStatus() {\n      this.serviceTicketService.getAllTicketStatus().pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.ticketStatuses = response?.data || [];\n        },\n        error: err => {\n          console.error('Error fetching ticket statuses:', err);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    confirmClose() {\n      this.confirmationService.confirm({\n        message: 'Are you sure you want to close this ticket? This action will mark the ticket as completed.',\n        header: 'Confirm Close Ticket',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.close();\n        }\n      });\n    }\n    close() {\n      if (this.id) {\n        this.closing = true;\n        this.serviceTicketService.updateTicket(this.ticketDetails.documentId, {\n          data: {\n            status_id: 'COMPLETED'\n          }\n        }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n          next: response => {\n            this.messageService.add({\n              severity: 'success',\n              detail: 'Ticket Closed Successfully!'\n            });\n            this.closing = false;\n            this.ticketDetails = response?.data || null;\n            if (this.ticketDetails) {\n              this.ticketForm.patchValue({\n                id: this.ticketDetails.id || '',\n                status_id: this.ticketDetails.status_id || ''\n              });\n            }\n          },\n          error: err => {\n            this.messageService.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n            this.submitting = false;\n            console.error('Error updating ticket:', err);\n          }\n        });\n      }\n    }\n    onSubmit() {\n      if (this.ticketForm.valid && this.id) {\n        const payload = this.ticketForm.value;\n        this.submitting = true;\n        this.serviceTicketService.updateTicket(this.ticketDetails.documentId, {\n          data: payload\n        }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n          next: response => {\n            this.messageService.add({\n              severity: 'success',\n              detail: 'Ticket Updated Successfully!'\n            });\n            this.submitting = false;\n            this.ticketDetails = response?.data || null;\n            if (this.ticketDetails) {\n              this.ticketForm.patchValue({\n                id: this.ticketDetails.id || '',\n                support_team: this.ticketDetails.support_team || '',\n                status_id: this.ticketDetails.status_id || '',\n                priority: this.ticketDetails.priority || 'Low',\n                subject: this.ticketDetails.subject || '',\n                account_id: this.ticketDetails.account_id || '',\n                contact_id: this.ticketDetails.contact_id || '',\n                assigned_to: this.ticketDetails.assigned_to || '',\n                description: this.ticketDetails.description || ''\n              });\n            }\n          },\n          error: err => {\n            this.messageService.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n            this.submitting = false;\n            console.error('Error updating ticket:', err);\n          }\n        });\n      }\n    }\n    loadEmployees() {\n      this.employees$ = concat(this.employeeInput$.pipe(distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq]`]: 'BUP003',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.prospectsservice.getEmployee(params).pipe(map(data => {\n          return data;\n        }), tap(() => this.employeeLoading = false));\n      })));\n    }\n    static {\n      this.ɵfac = function ServiceTicketsComponent_Factory(t) {\n        return new (t || ServiceTicketsComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i3.ProspectsService), i0.ɵɵdirectiveInject(i4.AccountService), i0.ɵɵdirectiveInject(i5.ServiceTicketService), i0.ɵɵdirectiveInject(i6.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ServiceTicketsComponent,\n        selectors: [[\"app-service-tickets\"]],\n        decls: 76,\n        vars: 27,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"account-sec\", \"w-full\", \"border-none\", \"mb-3\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"acc-title\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"header-title\", \"m-0\", \"pl-3\", \"relative\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", \"border-none\", \"bg-red-100\", \"text-red-500\", 3, \"click\", \"disabled\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"border-none\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\", \"disabled\"], [1, \"account-p-tabs\", \"relative\", \"flex\", \"gap-3\", \"flex-column\"], [1, \"acc-tab-info\", \"pb-2\"], [1, \"grid\", \"mt-0\", 3, \"formGroup\"], [1, \"col-12\", \"lg:col-2\", \"md:col-2\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"id\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"for\", \"account_id\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"account_id\", \"formControlName\", \"account_id\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"readonly\", \"\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\", 3, \"value\"], [\"for\", \"support_team\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"support_team\", \"formControlName\", \"support_team\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"for\", \"assigned_to\", 1, \"text-500\", \"font-medium\"], [\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"assigned_to\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [\"for\", \"status_id\", 1, \"text-500\", \"font-medium\"], [\"id\", \"status_id\", \"formControlName\", \"status_id\", \"optionLabel\", \"description\", \"optionValue\", \"code\", \"placeholder\", \"Choose status\", 3, \"options\", \"styleClass\"], [1, \"col-12\", \"lg:col-2\", \"md:col-2\", \"pt-0\"], [\"for\", \"priority\", 1, \"text-500\", \"font-medium\"], [\"id\", \"priority\", \"formControlName\", \"priority\", \"placeholder\", \"Choose here\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"styleClass\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"pt-0\"], [\"for\", \"subject\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"subject\", \"formControlName\", \"subject\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"for\", \"description\", 1, \"text-500\", \"font-medium\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"for\", \"scheduled_date\", 1, \"text-500\", \"font-medium\"], [\"id\", \"scheduled_date\", \"formControlName\", \"scheduled_date\", \"styleClass\", \"h-2-8rem w-full font-semibold text-500\"], [4, \"ngIf\"]],\n        template: function ServiceTicketsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"h5\", 7);\n            i0.ɵɵtext(8, \"Service Ticket\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function ServiceTicketsComponent_Template_button_click_10_listener() {\n              return ctx.confirmClose();\n            });\n            i0.ɵɵelementStart(11, \"i\", 10);\n            i0.ɵɵtext(12, \"close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function ServiceTicketsComponent_Template_button_click_14_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(15, \"i\", 10);\n            i0.ɵɵtext(16, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(17);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 12)(19, \"div\", 13)(20, \"form\", 14)(21, \"div\", 15)(22, \"div\", 16)(23, \"label\", 17);\n            i0.ɵɵtext(24, \"Ticket ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 15)(27, \"div\", 16)(28, \"label\", 19);\n            i0.ɵɵtext(29, \"Account ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 15)(32, \"div\", 16)(33, \"label\", 17);\n            i0.ɵɵtext(34, \"Account Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 15)(37, \"div\", 16)(38, \"label\", 22);\n            i0.ɵɵtext(39, \"Support Team\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 15)(42, \"div\", 16)(43, \"label\", 24);\n            i0.ɵɵtext(44, \"Assigned To\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"ng-select\", 25);\n            i0.ɵɵpipe(46, \"async\");\n            i0.ɵɵtemplate(47, ServiceTicketsComponent_ng_template_47_Template, 3, 2, \"ng-template\", 26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 15)(49, \"div\", 16)(50, \"label\", 27);\n            i0.ɵɵtext(51, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(52, \"p-dropdown\", 28);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"div\", 29)(54, \"div\", 16)(55, \"label\", 30);\n            i0.ɵɵtext(56, \"Priority\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(57, \"p-dropdown\", 31);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"div\", 32)(59, \"div\", 16)(60, \"label\", 33);\n            i0.ɵɵtext(61, \"Subject\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(62, \"input\", 34);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 32)(64, \"div\", 16)(65, \"label\", 35);\n            i0.ɵɵtext(66, \"Description \");\n            i0.ɵɵelementStart(67, \"span\", 36);\n            i0.ɵɵtext(68, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(69, \"textarea\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(70, \"div\", 29)(71, \"div\", 16)(72, \"label\", 38);\n            i0.ɵɵtext(73, \"Scheduled Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(74, \"p-calendar\", 39);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelement(75, \"router-outlet\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.closing || ctx.ticketForm.invalid);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.closing ? \"Closing...\" : \"Close\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.submitting || ctx.ticketForm.invalid);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.submitting ? \"Submitting\" : \"Submit\", \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"formGroup\", ctx.ticketForm);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"value\", ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name);\n            i0.ɵɵadvance(10);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(46, 24, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 1);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.ticketStatuses)(\"styleClass\", \"w-full h-2-8rem w-full font-semibold text-500\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.priorityOptions)(\"styleClass\", \"w-full h-2-8rem w-full font-semibold text-500\");\n            i0.ɵɵadvance(12);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c0));\n          }\n        },\n        dependencies: [i7.NgIf, i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.FormGroupDirective, i6.FormControlName, i8.NgSelectComponent, i8.NgOptionTemplateDirective, i1.RouterOutlet, i9.Breadcrumb, i10.ButtonDirective, i11.Dropdown, i12.Calendar, i13.Toast, i14.InputText, i7.AsyncPipe],\n        styles: [\".service-ticket-body .topbar-start h1{display:none}  .test{padding-top:10px}\"]\n      });\n    }\n  }\n  return ServiceTicketsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}