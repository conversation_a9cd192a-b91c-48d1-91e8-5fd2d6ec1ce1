{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { AuthService } from '../authentication/auth.service';\nexport function initializeApp(service) {\n  return () => service.checkAdminUser();\n}\nexport const appInitializerProviders = [{\n  provide: APP_INITIALIZER,\n  useFactory: initializeApp,\n  deps: [AuthService],\n  multi: true\n}];", "map": {"version": 3, "names": ["APP_INITIALIZER", "AuthService", "initializeApp", "service", "checkAdminUser", "appInitializerProviders", "provide", "useFactory", "deps", "multi"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\bootstrap\\initializers.ts"], "sourcesContent": ["import { APP_INITIALIZER } from '@angular/core';\r\nimport { AuthService } from '../authentication/auth.service';\r\n\r\nexport function initializeApp(service: AuthService) {\r\n  return () => service.checkAdminUser();\r\n}\r\n\r\nexport const appInitializerProviders = [\r\n  {\r\n    provide: APP_INITIALIZER,\r\n    useFactory: initializeApp,\r\n    deps: [AuthService],\r\n    multi: true,\r\n  },\r\n];\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,eAAe;AAC/C,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,OAAM,SAAUC,aAAaA,CAACC,OAAoB;EAChD,OAAO,MAAMA,OAAO,CAACC,cAAc,EAAE;AACvC;AAEA,OAAO,MAAMC,uBAAuB,GAAG,CACrC;EACEC,OAAO,EAAEN,eAAe;EACxBO,UAAU,EAAEL,aAAa;EACzBM,IAAI,EAAE,CAACP,WAAW,CAAC;EACnBQ,KAAK,EAAE;CACR,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}