{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { OrganizationalComponent } from './organizational.component';\nimport { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';\nimport { EmployeesComponent } from './organization-details/employees/employees.component';\nimport { FunctionsComponent } from './organization-details/functions/functions.component';\nimport { GeneralComponent } from './organization-details/general/general.component';\nimport { OrganizationDetailsComponent } from './organization-details/organization-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: OrganizationalComponent\n}, {\n  path: 'create',\n  component: AddOrgUnitComponent\n}, {\n  path: ':id',\n  component: OrganizationDetailsComponent,\n  children: [{\n    path: 'general',\n    component: GeneralComponent\n  }, {\n    path: 'functions',\n    component: FunctionsComponent\n  }, {\n    path: 'employees',\n    component: EmployeesComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport let OrganizationalRoutingModule = /*#__PURE__*/(() => {\n  class OrganizationalRoutingModule {\n    static {\n      this.ɵfac = function OrganizationalRoutingModule_Factory(t) {\n        return new (t || OrganizationalRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: OrganizationalRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return OrganizationalRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}