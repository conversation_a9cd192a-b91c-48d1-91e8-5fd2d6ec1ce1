{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { catchError, of, tap, throwError } from 'rxjs';\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n    this.escapeTokenUrls = [CMS_APIContstant.SINGIN, CMS_APIContstant.RESET_PASSWORD_REQUEST, CMS_APIContstant.RESET_PASSWORD, CMS_APIContstant.STORE_DESIGN, CMS_APIContstant.MAIN_MENU_API_DETAILS];\n  }\n  intercept(req, next) {\n    if (this.authService.isLoggedIn) {\n      if (req.url.includes(environment.apiEndpoint) || !this.startsWithAnyUrl(req.url, this.escapeTokenUrls)) {\n        const headers = {\n          Authorization: 'Bearer ' + this.getAuthToken(req.url)\n        };\n        if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {\n          const details = this.authService.userDetail;\n          headers.documentId = details.documentId;\n        }\n        req = req.clone({\n          setHeaders: headers\n        });\n      }\n    }\n    return next.handle(req).pipe(tap(event => {\n      if (event instanceof HttpResponse) {\n        const newToken = event.headers.get('refreshtoken');\n        const auth = this.authService.getAuth();\n        if (newToken && auth) {\n          this.authService.setAuth(newToken, auth[this.authService.UserDetailsKey], this.authService.isRememberMeSelected());\n        }\n      }\n    })).pipe(catchError(x => this.handleAuthError(x)));\n  }\n  getAuthToken(url) {\n    const authToken = this.authService.getToken();\n    // const isAdmin = this.authService.userDetail?.isAdmin;\n    // if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {\n    //   return environment.cmsApiToken;\n    // }\n    return authToken;\n  }\n  startsWithAnyUrl(newUrl, urls) {\n    return urls.some(url => newUrl.startsWith(url));\n  }\n  handleAuthError(err) {\n    if (err.status === 401 || err.status === 403) {\n      this.authService.removeAuthToken();\n      window.location.href = '#/auth/login';\n      window.location.reload();\n      return of(err.message);\n    }\n    return throwError(() => err);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpResponse", "catchError", "of", "tap", "throwError", "ApiConstant", "CMS_APIContstant", "environment", "AuthInterceptor", "constructor", "authService", "escapeTokenUrls", "SINGIN", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "STORE_DESIGN", "MAIN_MENU_API_DETAILS", "intercept", "req", "next", "isLoggedIn", "url", "includes", "apiEndpoint", "startsWithAnyUrl", "headers", "Authorization", "getAuthToken", "Object", "values", "details", "userDetail", "documentId", "clone", "setHeaders", "handle", "pipe", "event", "newToken", "get", "auth", "getAuth", "setAuth", "UserDetailsKey", "isRememberMeSelected", "x", "handleAuthError", "authToken", "getToken", "newUrl", "urls", "some", "startsWith", "err", "status", "removeAuthToken", "window", "location", "href", "reload", "message", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\authentication\\auth.intreceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  HttpInterceptor,\r\n  HttpRequest,\r\n  HttpHandler,\r\n  HttpErrorResponse,\r\n  HttpResponse,\r\n} from '@angular/common/http';\r\nimport { AuthService } from './auth.service';\r\nimport { catchError, Observable, of, tap, throwError } from 'rxjs';\r\nimport { ApiConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n  private escapeTokenUrls = [\r\n    CMS_APIContstant.SINGIN,\r\n    CMS_APIContstant.RESET_PASSWORD_REQUEST,\r\n    CMS_APIContstant.RESET_PASSWORD,\r\n    CMS_APIContstant.STORE_DESIGN,\r\n    CMS_APIContstant.MAIN_MENU_API_DETAILS,\r\n  ];\r\n\r\n  constructor(private authService: AuthService) {}\r\n\r\n  intercept(req: HttpRequest<any>, next: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {\r\n    if (this.authService.isLoggedIn) {\r\n      if (\r\n        req.url.includes(environment.apiEndpoint) ||\r\n        !this.startsWithAnyUrl(req.url, this.escapeTokenUrls)\r\n      ) {\r\n        const headers: any = {\r\n          Authorization: 'Bearer ' + this.getAuthToken(req.url),\r\n        };\r\n        if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {\r\n          const details = this.authService.userDetail;\r\n          headers.documentId = details.documentId;\r\n        }\r\n        req = req.clone({\r\n          setHeaders: headers,\r\n        });\r\n      }\r\n    }\r\n    return next\r\n      .handle(req)\r\n      .pipe(\r\n        tap((event: any) => {\r\n          if (event instanceof HttpResponse) {\r\n            const newToken = event.headers.get('refreshtoken');\r\n            const auth = this.authService.getAuth();\r\n            if (newToken && auth) {\r\n              this.authService.setAuth(\r\n                newToken,\r\n                auth[this.authService.UserDetailsKey],\r\n                this.authService.isRememberMeSelected()\r\n              );\r\n            }\r\n          }\r\n        })\r\n      )\r\n      .pipe(catchError((x) => this.handleAuthError(x)));\r\n  }\r\n\r\n  private getAuthToken(url: string) {\r\n    const authToken = this.authService.getToken();\r\n    // const isAdmin = this.authService.userDetail?.isAdmin;\r\n    // if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {\r\n    //   return environment.cmsApiToken;\r\n    // }\r\n    return authToken;\r\n  }\r\n\r\n  private startsWithAnyUrl(newUrl: string, urls: string[]) {\r\n    return urls.some((url) => newUrl.startsWith(url));\r\n  }\r\n\r\n  private handleAuthError(err: HttpErrorResponse): Observable<any> {\r\n    if (err.status === 401 || err.status === 403) {\r\n      this.authService.removeAuthToken();\r\n      window.location.href = '#/auth/login';\r\n      window.location.reload();\r\n\r\n      return of(err.message);\r\n    }\r\n    return throwError(() => err);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAKEA,YAAY,QACP,sBAAsB;AAE7B,SAASC,UAAU,EAAcC,EAAE,EAAEC,GAAG,EAAEC,UAAU,QAAQ,MAAM;AAClE,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,OAAM,MAAOC,eAAe;EAS1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IARvB,KAAAC,eAAe,GAAG,CACxBL,gBAAgB,CAACM,MAAM,EACvBN,gBAAgB,CAACO,sBAAsB,EACvCP,gBAAgB,CAACQ,cAAc,EAC/BR,gBAAgB,CAACS,YAAY,EAC7BT,gBAAgB,CAACU,qBAAqB,CACvC;EAE8C;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,IAAI,IAAI,CAACT,WAAW,CAACU,UAAU,EAAE;MAC/B,IACEF,GAAG,CAACG,GAAG,CAACC,QAAQ,CAACf,WAAW,CAACgB,WAAW,CAAC,IACzC,CAAC,IAAI,CAACC,gBAAgB,CAACN,GAAG,CAACG,GAAG,EAAE,IAAI,CAACV,eAAe,CAAC,EACrD;QACA,MAAMc,OAAO,GAAQ;UACnBC,aAAa,EAAE,SAAS,GAAG,IAAI,CAACC,YAAY,CAACT,GAAG,CAACG,GAAG;SACrD;QACD,IAAI,IAAI,CAACG,gBAAgB,CAACN,GAAG,CAACG,GAAG,EAAE,CAAC,GAAGO,MAAM,CAACC,MAAM,CAACxB,WAAW,CAAC,CAAC,CAAC,EAAE;UACnE,MAAMyB,OAAO,GAAG,IAAI,CAACpB,WAAW,CAACqB,UAAU;UAC3CN,OAAO,CAACO,UAAU,GAAGF,OAAO,CAACE,UAAU;QACzC;QACAd,GAAG,GAAGA,GAAG,CAACe,KAAK,CAAC;UACdC,UAAU,EAAET;SACb,CAAC;MACJ;IACF;IACA,OAAON,IAAI,CACRgB,MAAM,CAACjB,GAAG,CAAC,CACXkB,IAAI,CACHjC,GAAG,CAAEkC,KAAU,IAAI;MACjB,IAAIA,KAAK,YAAYrC,YAAY,EAAE;QACjC,MAAMsC,QAAQ,GAAGD,KAAK,CAACZ,OAAO,CAACc,GAAG,CAAC,cAAc,CAAC;QAClD,MAAMC,IAAI,GAAG,IAAI,CAAC9B,WAAW,CAAC+B,OAAO,EAAE;QACvC,IAAIH,QAAQ,IAAIE,IAAI,EAAE;UACpB,IAAI,CAAC9B,WAAW,CAACgC,OAAO,CACtBJ,QAAQ,EACRE,IAAI,CAAC,IAAI,CAAC9B,WAAW,CAACiC,cAAc,CAAC,EACrC,IAAI,CAACjC,WAAW,CAACkC,oBAAoB,EAAE,CACxC;QACH;MACF;IACF,CAAC,CAAC,CACH,CACAR,IAAI,CAACnC,UAAU,CAAE4C,CAAC,IAAK,IAAI,CAACC,eAAe,CAACD,CAAC,CAAC,CAAC,CAAC;EACrD;EAEQlB,YAAYA,CAACN,GAAW;IAC9B,MAAM0B,SAAS,GAAG,IAAI,CAACrC,WAAW,CAACsC,QAAQ,EAAE;IAC7C;IACA;IACA;IACA;IACA,OAAOD,SAAS;EAClB;EAEQvB,gBAAgBA,CAACyB,MAAc,EAAEC,IAAc;IACrD,OAAOA,IAAI,CAACC,IAAI,CAAE9B,GAAG,IAAK4B,MAAM,CAACG,UAAU,CAAC/B,GAAG,CAAC,CAAC;EACnD;EAEQyB,eAAeA,CAACO,GAAsB;IAC5C,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;MAC5C,IAAI,CAAC5C,WAAW,CAAC6C,eAAe,EAAE;MAClCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;MAExB,OAAOzD,EAAE,CAACmD,GAAG,CAACO,OAAO,CAAC;IACxB;IACA,OAAOxD,UAAU,CAAC,MAAMiD,GAAG,CAAC;EAC9B;;;uBAvEW7C,eAAe,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfxD,eAAe;MAAAyD,OAAA,EAAfzD,eAAe,CAAA0D;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}