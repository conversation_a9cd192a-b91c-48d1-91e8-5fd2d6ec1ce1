{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of, forkJoin } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/account/account.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/tooltip\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/checkbox\";\nconst _c0 = () => ({\n  width: \"38rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  width: \"45rem\"\n});\nfunction OpportunitiesContactsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 43);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 44)(4, \"div\", 45);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 47)(8, \"div\", 45);\n    i0.ɵɵtext(9, \"Job Title\");\n    i0.ɵɵelement(10, \"p-sortIcon\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 49)(12, \"div\", 45);\n    i0.ɵɵtext(13, \" Phone \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 51)(16, \"div\", 45);\n    i0.ɵɵtext(17, \" Mobile \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\", 53)(20, \"div\", 45);\n    i0.ɵɵtext(21, \" E-Mail \");\n    i0.ɵɵelement(22, \"p-sortIcon\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"th\", 55)(24, \"div\", 45);\n    i0.ɵɵtext(25, \"Function\");\n    i0.ɵɵelement(26, \"p-sortIcon\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\", 57)(28, \"div\", 45);\n    i0.ɵɵtext(29, \" Department \");\n    i0.ɵɵelement(30, \"p-sortIcon\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"th\", 58);\n    i0.ɵɵtext(32, \"Web Registered\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 59);\n    i0.ɵɵtext(34, \"VIP Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\")(36, \"div\", 45);\n    i0.ɵɵtext(37, \"Deactivate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"th\", 60);\n    i0.ɵɵtext(39, \"Comm. Preference\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\", 60);\n    i0.ɵɵtext(41, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 61);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵelement(18, \"p-checkbox\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵelement(20, \"p-checkbox\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵelement(22, \"p-checkbox\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\")(26, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_ng_template_9_Template_button_click_26_listener() {\n      const contact_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editContact(contact_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.business_partner == null ? null : contact_r2.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.job_title) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.mobile) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.email_address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.contact_person_function_name == null ? null : contact_r2.contact_person_function_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.contact_person_department_name == null ? null : contact_r2.contact_person_department_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r2.web_registered)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r2.contact_person_vip_type)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", contact_r2.validity_end_date)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", contact_r2 == null ? null : contact_r2.communication_preference, \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 66);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 66);\n    i0.ɵɵtext(2, \" Loading contacts data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_24_div_1_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"first_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_41_div_1_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"last_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_72_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_72_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_72_div_1_Template, 2, 0, \"div\", 34)(2, OpportunitiesContactsComponent_div_72_div_2_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitted && ctx_r2.f[\"email_address\"].errors && ctx_r2.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_80_div_1_Template, 2, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.ContactForm.get(\"phone_number\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mobile is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_90_div_1_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"mobile\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_91_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \" Please enter a valid Mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_91_div_1_Template, 2, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.ContactForm.get(\"mobile\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"label\", 70)(3, \"span\", 15);\n    i0.ɵɵtext(4, \"remove_circle_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \"DeActivate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 17);\n    i0.ɵɵelement(7, \"p-checkbox\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 13)(9, \"label\", 72)(10, \"span\", 15);\n    i0.ɵɵtext(11, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \"VIP Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 17);\n    i0.ɵɵelement(14, \"p-checkbox\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_107_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_107_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_107_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.phone, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesContactsComponent_ng_template_107_span_2_Template, 2, 1, \"span\", 34)(3, OpportunitiesContactsComponent_ng_template_107_span_3_Template, 2, 1, \"span\", 34)(4, OpportunitiesContactsComponent_ng_template_107_span_4_Template, 2, 1, \"span\", 34);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.phone);\n  }\n}\nexport class OpportunitiesContactsComponent {\n  constructor(route, opportunitiesservice, accountservice, formBuilder, messageservice) {\n    this.route = route;\n    this.opportunitiesservice = opportunitiesservice;\n    this.accountservice = accountservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.contactDetails = null;\n    this.id = '';\n    this.opportunity_id = '';\n    this.departments = null;\n    this.functions = null;\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.visible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.editid = '';\n    this.documentId = '';\n    this.saving = false;\n    this.cpDepartments = [];\n    this.cpFunctions = [];\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.selectedContacts = [];\n    this.ContactForm = this.formBuilder.group({\n      first_name: ['', [Validators.required]],\n      middle_name: [''],\n      last_name: ['', [Validators.required]],\n      job_title: [''],\n      contact_person_function_name: [''],\n      contact_person_department_name: [''],\n      email_address: ['', [Validators.required, Validators.email]],\n      phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      contact_person_vip_type: [''],\n      validity_end_date: [''],\n      contactexisting: ['']\n    });\n  }\n  ngOnInit() {\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.loadContacts();\n    forkJoin({\n      departments: this.accountservice.getCPDepartment(),\n      functions: this.accountservice.getCPFunction()\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n      departments,\n      functions\n    }) => {\n      // Load departments\n      this.cpDepartments = (departments?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      // Load functions\n      this.cpFunctions = (functions?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.id = response?.opportunity_id;\n          this.documentId = response?.documentId;\n          this.contactDetails = response?.opportunity_prospect_contact_parties || [];\n          this.contactDetails = this.contactDetails.map(contact => {\n            return {\n              ...contact,\n              full_name: [contact?.business_partner_person?.first_name, contact?.business_partner_person?.middle_name, contact?.business_partner_person?.last_name].filter(Boolean).join(' '),\n              first_name: contact?.business_partner_person?.first_name || '',\n              middle_name: contact?.business_partner_person?.middle_name || '',\n              last_name: contact?.business_partner_person?.last_name || '',\n              email_address: contact?.business_partner_person?.addresses?.[0]?.emails?.[0]?.email_address || '',\n              phone_number: (contact?.business_partner_person?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number,\n              mobile: (contact?.business_partner_person?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '3')?.phone_number,\n              // Ensure department & function values are set correctly\n              contact_person_department_name: this.cpDepartments?.find(d => d.value === contact?.person_func_and_dept?.contact_person_department) || null,\n              // Default value if not found\n              contact_person_function_name: this.cpFunctions?.find(f => f.value === contact?.person_func_and_dept?.contact_person_function) || null,\n              // Default value if not found\n              job_title: contact?.business_partner_person?.bp_extension?.job_title || '',\n              contact_person_vip_type: contact?.person_func_and_dept?.contact_person_vip_type ? true : false,\n              web_registered: contact?.business_partner_person?.bp_extension?.web_registered ? true : false,\n              communication_preference: contact?.business_partner_person?.addresses?.[0]?.prfrd_comm_medium_type || '-',\n              validity_end_date: new Date().toISOString().split('T')[0] < contact?.validity_end_date?.split('T')[0] ? false : true\n            };\n          });\n        }\n      });\n    });\n  }\n  reactivateSelectedContacts() {\n    if (!this.selectedContacts || this.selectedContacts.length === 0) {\n      return;\n    }\n    const reactivateRequests = this.selectedContacts.map(contact => this.accountservice.updateReactivate(contact).toPromise());\n    Promise.all(reactivateRequests).then(() => {\n      this.messageservice.add({\n        severity: 'success',\n        detail: 'Contacts Reactivated successfully!.'\n      });\n      this.opportunitiesservice.getOpportunityByID(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      this.selectedContacts = [];\n    }).catch(error => {\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Error during bulk update :' + error\n      });\n    });\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.accountservice.getContacts(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.contactLoading = false), catchError(error => {\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  editContact(contact) {\n    this.addDialogVisible = true;\n    this.editid = contact?.documentId;\n    this.ContactForm.patchValue({\n      first_name: contact.first_name,\n      middle_name: contact.middle_name,\n      last_name: contact.last_name,\n      job_title: contact.job_title,\n      email_address: contact.email_address,\n      phone_number: contact.phone_number,\n      mobile: contact.mobile,\n      validity_end_date: contact.validity_end_date,\n      contact_person_vip_type: contact.contact_person_vip_type,\n      contactexisting: '',\n      // Ensure department & function are set correctly\n      contact_person_function_name: this.cpFunctions.find(f => f.value === contact?.contact_person_function_name?.value) || null,\n      contact_person_department_name: this.cpDepartments.find(d => d.value === contact?.contact_person_department_name?.value) || null\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.visible = true;\n      if (_this.ContactForm.value?.contactexisting) {\n        const existing = _this.ContactForm.value.contactexisting;\n        const data = {\n          opportunity_party_contact_party_id: existing?.bp_id,\n          opportunity_id: _this.opportunity_id,\n          role_code: ''\n        };\n        _this.saving = true;\n        _this.opportunitiesservice.createExistingContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.existingDialogVisible = false;\n            _this.ContactForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Contact Added successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n        // Skip rest of logic for new contact\n        return;\n      }\n      if (_this.ContactForm.invalid) {\n        console.log('Form is invalid:', _this.ContactForm.errors);\n        _this.visible = true;\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.ContactForm.value\n      };\n      const data = {\n        bp_id: _this.id,\n        first_name: value?.first_name || '',\n        middle_name: value?.middle_name,\n        last_name: value?.last_name || '',\n        job_title: value?.job_title || '',\n        contact_person_function_name: value?.contact_person_function_name?.name || '',\n        contact_person_function: value?.contact_person_function_name?.value || '',\n        contact_person_department_name: value?.contact_person_department_name?.name || '',\n        contact_person_department: value?.contact_person_department_name?.value || '',\n        email_address: value?.email_address,\n        phone_number: value?.phone_number,\n        mobile: value?.mobile,\n        contact_person_vip_type: value?.contact_person_vip_type,\n        validity_end_date: value?.validity_end_date ? new Date().toISOString().split('T')[0] : '9999-12-29'\n      };\n      if (_this.editid) {\n        _this.accountservice.updateContact(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.ContactForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Contact Updated successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: res => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.accountservice.createContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.existingDialogVisible = false;\n            _this.ContactForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Contact created successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.ContactForm.reset();\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  get f() {\n    return this.ContactForm.controls;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesContactsComponent_Factory(t) {\n      return new (t || OpportunitiesContactsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesContactsComponent,\n      selectors: [[\"app-opportunities-contacts\"]],\n      decls: 111,\n      vars: 54,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"selectionChange\", \"value\", \"selection\", \"rows\", \"paginator\", \"scrollable\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-contact-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"First Name \", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"first_name\", \"formControlName\", \"first_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [\"for\", \"Middle Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"middle_name\", \"formControlName\", \"middle_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Last Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"last_name\", \"formControlName\", \"last_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Job Title\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"job_title\", \"formControlName\", \"job_title\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Function\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_function_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Function\", 3, \"options\", \"styleClass\"], [\"for\", \"Department\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_department_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Department\", 3, \"options\", \"styleClass\"], [\"for\", \"Email\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"email\", \"id\", \"email_address\", \"formControlName\", \"email_address\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Phone\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"phone_number\", \"formControlName\", \"phone_number\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"for\", \"Mobile\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"mobile\", \"formControlName\", \"mobile\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"table-checkbox\", \"text-center\"], [\"pFrozenColumn\", \"\", \"pSortableColumn\", \"full_name\", 2, \"width\", \"10%\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"full_name\"], [\"pSortableColumn\", \"job_title\", 2, \"width\", \"10%\"], [\"field\", \"job_title\"], [\"pSortableColumn\", \"phone_number\", 2, \"width\", \"10%\"], [\"field\", \"phone_number\"], [\"pSortableColumn\", \"mobile\", 2, \"width\", \"10%\"], [\"field\", \"mobile\"], [\"pSortableColumn\", \"email_address\", 2, \"width\", \"15%\"], [\"field\", \"email_address\"], [\"pSortableColumn\", \"contact_person_function_name\", 2, \"width\", \"10%\"], [\"field\", \"contact_person_department_name\"], [\"pSortableColumn\", \"contact_person_department_name\", 2, \"width\", \"10%\"], [2, \"width\", \"10%\"], [2, \"width\", \"7%\"], [2, \"width\", \"8%\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\"], [3, \"binary\", \"ngModel\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"p-error\"], [\"for\", \"DeActivate\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"validity_end_date\", \"formControlName\", \"validity_end_date\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"], [\"for\", \"VIP Contact\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"contact_person_vip_type\", \"formControlName\", \"contact_person_vip_type\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"]],\n      template: function OpportunitiesContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Contacts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_p_button_click_5_listener() {\n            return ctx.showExistingDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-table\", 6);\n          i0.ɵɵtwoWayListener(\"selectionChange\", function OpportunitiesContactsComponent_Template_p_table_selectionChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedContacts, $event) || (ctx.selectedContacts = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(8, OpportunitiesContactsComponent_ng_template_8_Template, 42, 0, \"ng-template\", 7)(9, OpportunitiesContactsComponent_ng_template_9_Template, 27, 18, \"ng-template\", 8)(10, OpportunitiesContactsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9)(11, OpportunitiesContactsComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"p-dialog\", 11);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(13, OpportunitiesContactsComponent_ng_template_13_Template, 2, 0, \"ng-template\", 7);\n          i0.ɵɵelementStart(14, \"form\", 12)(15, \"div\", 13)(16, \"label\", 14)(17, \"span\", 15);\n          i0.ɵɵtext(18, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \"First Name \");\n          i0.ɵɵelementStart(20, \"span\", 16);\n          i0.ɵɵtext(21, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 17);\n          i0.ɵɵelement(23, \"input\", 18);\n          i0.ɵɵtemplate(24, OpportunitiesContactsComponent_div_24_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 20)(27, \"span\", 15);\n          i0.ɵɵtext(28, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(29, \"Middle Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 17);\n          i0.ɵɵelement(31, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"label\", 22)(34, \"span\", 15);\n          i0.ɵɵtext(35, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \"Last Name \");\n          i0.ɵɵelementStart(37, \"span\", 16);\n          i0.ɵɵtext(38, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 17);\n          i0.ɵɵelement(40, \"input\", 23);\n          i0.ɵɵtemplate(41, OpportunitiesContactsComponent_div_41_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 13)(43, \"label\", 24)(44, \"span\", 15);\n          i0.ɵɵtext(45, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \"Job Title \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 17);\n          i0.ɵɵelement(48, \"input\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"label\", 26)(51, \"span\", 15);\n          i0.ɵɵtext(52, \"functions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \"Function \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 17);\n          i0.ɵɵelement(55, \"p-dropdown\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 13)(57, \"label\", 28)(58, \"span\", 15);\n          i0.ɵɵtext(59, \"inbox_text_person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \"Department \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 17);\n          i0.ɵɵelement(62, \"p-dropdown\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 13)(64, \"label\", 30)(65, \"span\", 15);\n          i0.ɵɵtext(66, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \"Email\");\n          i0.ɵɵelementStart(68, \"span\", 16);\n          i0.ɵɵtext(69, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 17);\n          i0.ɵɵelement(71, \"input\", 31);\n          i0.ɵɵtemplate(72, OpportunitiesContactsComponent_div_72_Template, 3, 2, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 13)(74, \"label\", 32)(75, \"span\", 15);\n          i0.ɵɵtext(76, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \"Phone \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"div\", 17);\n          i0.ɵɵelement(79, \"input\", 33);\n          i0.ɵɵtemplate(80, OpportunitiesContactsComponent_div_80_Template, 2, 1, \"div\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 13)(82, \"label\", 35)(83, \"span\", 15);\n          i0.ɵɵtext(84, \"smartphone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(85, \"Mobile # \");\n          i0.ɵɵelementStart(86, \"span\", 16);\n          i0.ɵɵtext(87, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 17);\n          i0.ɵɵelement(89, \"input\", 36);\n          i0.ɵɵtemplate(90, OpportunitiesContactsComponent_div_90_Template, 2, 1, \"div\", 19)(91, OpportunitiesContactsComponent_div_91_Template, 2, 1, \"div\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(92, OpportunitiesContactsComponent_div_92_Template, 15, 2, \"div\", 34);\n          i0.ɵɵelementStart(93, \"div\", 37)(94, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_94_listener() {\n            return ctx.addDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_95_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(96, \"p-dialog\", 11);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_96_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(97, OpportunitiesContactsComponent_ng_template_97_Template, 2, 0, \"ng-template\", 7);\n          i0.ɵɵelementStart(98, \"form\", 12)(99, \"div\", 13)(100, \"label\", 40)(101, \"span\", 15);\n          i0.ɵɵtext(102, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(103, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 17)(105, \"ng-select\", 41);\n          i0.ɵɵpipe(106, \"async\");\n          i0.ɵɵtemplate(107, OpportunitiesContactsComponent_ng_template_107_Template, 5, 4, \"ng-template\", 42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(108, \"div\", 37)(109, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_109_listener() {\n            return ctx.existingDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_110_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_23_0;\n          let tmp_26_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.contactDetails);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedContacts);\n          i0.ɵɵproperty(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(44, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c1, ctx.submitted && ctx.f[\"first_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"first_name\"].errors);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(47, _c1, ctx.submitted && ctx.f[\"last_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"last_name\"].errors);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", ctx.cpFunctions)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"options\", ctx.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c1, ctx.submitted && ctx.f[\"email_address\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_23_0.touched) && ((tmp_23_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_23_0.invalid));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c1, ctx.submitted && ctx.f[\"mobile\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"mobile\"].errors);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_26_0.touched) && ((tmp_26_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_26_0.invalid));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editid && ctx.ContactForm.value.first_name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(53, _c2));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(106, 42, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.FormGroupDirective, i4.FormControlName, i7.Table, i5.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.Tooltip, i13.InputText, i14.Checkbox, i6.AsyncPipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .opportunity-contact-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1jb250YWN0cy9vcHBvcnR1bml0aWVzLWNvbnRhY3RzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUUscUJBQUE7RUFDQSxXQUFBO0FBQ0Y7O0FBSUk7RUFDRSxrQkFBQTtBQUROO0FBR007RUFDRSw0QkFBQTtFQUNBLDJDQUFBO0FBRFI7QUFHUTtFQUNFLFNBQUE7QUFEVjtBQUtNO0VBQ0UsNEJBQUE7RUFDQSxpQkFBQTtBQUhSIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICByaWdodDogMTBweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIHtcclxuICAub3Bwb3J0dW5pdHktY29udGFjdC1wb3B1cCB7XHJcbiAgICAucC1kaWFsb2cge1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgIGg0IHtcclxuICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "fork<PERSON><PERSON>n", "distinctUntilChanged", "switchMap", "tap", "catchError", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "OpportunitiesContactsComponent_ng_template_9_Template_button_click_26_listener", "contact_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "editContact", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "business_partner", "bp_full_name", "job_title", "phone_number", "mobile", "email_address", "contact_person_function_name", "name", "contact_person_department_name", "web_registered", "contact_person_vip_type", "validity_end_date", "communication_preference", "ɵɵtemplate", "OpportunitiesContactsComponent_div_24_div_1_Template", "f", "errors", "OpportunitiesContactsComponent_div_41_div_1_Template", "OpportunitiesContactsComponent_div_72_div_1_Template", "OpportunitiesContactsComponent_div_72_div_2_Template", "submitted", "OpportunitiesContactsComponent_div_80_div_1_Template", "tmp_1_0", "ContactForm", "get", "OpportunitiesContactsComponent_div_90_div_1_Template", "OpportunitiesContactsComponent_div_91_div_1_Template", "item_r4", "email", "phone", "OpportunitiesContactsComponent_ng_template_107_span_2_Template", "OpportunitiesContactsComponent_ng_template_107_span_3_Template", "OpportunitiesContactsComponent_ng_template_107_span_4_Template", "ɵɵtextInterpolate", "bp_id", "OpportunitiesContactsComponent", "constructor", "route", "opportunitiesservice", "accountservice", "formBuilder", "messageservice", "unsubscribe$", "contactDetails", "id", "opportunity_id", "departments", "functions", "addDialogVisible", "existingDialogVisible", "visible", "position", "editid", "documentId", "saving", "cpDepartments", "cpFunctions", "contactLoading", "contactInput$", "defaultOptions", "selectedContacts", "group", "first_name", "required", "middle_name", "last_name", "pattern", "contactexisting", "ngOnInit", "parent", "snapshot", "paramMap", "loadContacts", "getCPDepartment", "getCPFunction", "pipe", "subscribe", "data", "item", "description", "value", "code", "opportunity", "response", "opportunity_prospect_contact_parties", "contact", "full_name", "business_partner_person", "filter", "Boolean", "join", "addresses", "emails", "phone_numbers", "find", "phone_number_type", "d", "person_func_and_dept", "contact_person_department", "contact_person_function", "bp_extension", "prfrd_comm_medium_type", "Date", "toISOString", "split", "reactivateSelectedContacts", "length", "reactivateRequests", "updateReactivate", "to<PERSON>romise", "Promise", "all", "then", "add", "severity", "detail", "getOpportunityByID", "catch", "error", "contacts$", "term", "params", "getContacts", "patchValue", "onSubmit", "_this", "_asyncToGenerator", "existing", "opportunity_party_contact_party_id", "role_code", "createExistingContact", "complete", "reset", "invalid", "console", "log", "updateContact", "res", "createContact", "showNewDialog", "showExistingDialog", "controls", "ngOnDestroy", "next", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "OpportunitiesService", "i3", "AccountService", "i4", "FormBuilder", "i5", "MessageService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesContactsComponent_Template", "rf", "ctx", "OpportunitiesContactsComponent_Template_p_button_click_5_listener", "ɵɵtwoWayListener", "OpportunitiesContactsComponent_Template_p_table_selectionChange_7_listener", "$event", "ɵɵtwoWayBindingSet", "OpportunitiesContactsComponent_ng_template_8_Template", "OpportunitiesContactsComponent_ng_template_9_Template", "OpportunitiesContactsComponent_ng_template_10_Template", "OpportunitiesContactsComponent_ng_template_11_Template", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_12_listener", "OpportunitiesContactsComponent_ng_template_13_Template", "OpportunitiesContactsComponent_div_24_Template", "OpportunitiesContactsComponent_div_41_Template", "OpportunitiesContactsComponent_div_72_Template", "OpportunitiesContactsComponent_div_80_Template", "OpportunitiesContactsComponent_div_90_Template", "OpportunitiesContactsComponent_div_91_Template", "OpportunitiesContactsComponent_div_92_Template", "OpportunitiesContactsComponent_Template_button_click_94_listener", "OpportunitiesContactsComponent_Template_button_click_95_listener", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_96_listener", "OpportunitiesContactsComponent_ng_template_97_Template", "OpportunitiesContactsComponent_ng_template_107_Template", "OpportunitiesContactsComponent_Template_button_click_109_listener", "OpportunitiesContactsComponent_Template_button_click_110_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "tmp_23_0", "touched", "tmp_26_0", "_c2", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { AccountService } from 'src/app/store/account/account.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport {\r\n  Subject,\r\n  takeUntil,\r\n  Observable,\r\n  concat,\r\n  map,\r\n  of,\r\n  forkJoin,\r\n} from 'rxjs';\r\nimport { MessageService } from 'primeng/api';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n} from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-contacts',\r\n  templateUrl: './opportunities-contacts.component.html',\r\n  styleUrl: './opportunities-contacts.component.scss',\r\n})\r\nexport class OpportunitiesContactsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contactDetails: any = null;\r\n  public id: string = '';\r\n  public opportunity_id: string = '';\r\n  public departments: any = null;\r\n  public functions: any = null;\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  public visible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public editid: string = '';\r\n  public documentId: string = '';\r\n  public saving = false;\r\n  public cpDepartments: { name: string; value: string }[] = [];\r\n  public cpFunctions: { name: string; value: string }[] = [];\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public selectedContacts = [];\r\n\r\n  public ContactForm: FormGroup = this.formBuilder.group({\r\n    first_name: ['', [Validators.required]],\r\n    middle_name: [''],\r\n    last_name: ['', [Validators.required]],\r\n    job_title: [''],\r\n    contact_person_function_name: [''],\r\n    contact_person_department_name: [''],\r\n    email_address: ['', [Validators.required, Validators.email]],\r\n    phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    contact_person_vip_type: [''],\r\n    validity_end_date: [''],\r\n    contactexisting: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private accountservice: AccountService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.loadContacts();\r\n    forkJoin({\r\n      departments: this.accountservice.getCPDepartment(),\r\n      functions: this.accountservice.getCPFunction(),\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(({ departments, functions }) => {\r\n        // Load departments\r\n        this.cpDepartments = (departments?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n\r\n        // Load functions\r\n        this.cpFunctions = (functions?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n        this.opportunitiesservice.opportunity\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe((response: any) => {\r\n            if (response) {\r\n              this.id = response?.opportunity_id;\r\n              this.documentId = response?.documentId;\r\n              this.contactDetails =\r\n                response?.opportunity_prospect_contact_parties || [];\r\n\r\n              this.contactDetails = this.contactDetails.map((contact: any) => {\r\n                return {\r\n                  ...contact,\r\n                  full_name: [\r\n                    contact?.business_partner_person?.first_name,\r\n                    contact?.business_partner_person?.middle_name,\r\n                    contact?.business_partner_person?.last_name,\r\n                  ]\r\n                    .filter(Boolean)\r\n                    .join(' '),\r\n\r\n                  first_name:\r\n                    contact?.business_partner_person?.first_name || '',\r\n                  middle_name:\r\n                    contact?.business_partner_person?.middle_name || '',\r\n                  last_name: contact?.business_partner_person?.last_name || '',\r\n                  email_address:\r\n                    contact?.business_partner_person?.addresses?.[0]\r\n                      ?.emails?.[0]?.email_address || '',\r\n                  phone_number: (\r\n                    contact?.business_partner_person?.addresses?.[0]\r\n                      ?.phone_numbers || []\r\n                  ).find((item: any) => item.phone_number_type === '1')\r\n                    ?.phone_number,\r\n                  mobile: (\r\n                    contact?.business_partner_person?.addresses?.[0]\r\n                      ?.phone_numbers || []\r\n                  ).find((item: any) => item.phone_number_type === '3')\r\n                    ?.phone_number,\r\n\r\n                  // Ensure department & function values are set correctly\r\n                  contact_person_department_name:\r\n                    this.cpDepartments?.find(\r\n                      (d: any) =>\r\n                        d.value ===\r\n                        contact?.person_func_and_dept?.contact_person_department\r\n                    ) || null, // Default value if not found\r\n\r\n                  contact_person_function_name:\r\n                    this.cpFunctions?.find(\r\n                      (f: any) =>\r\n                        f.value ===\r\n                        contact?.person_func_and_dept?.contact_person_function\r\n                    ) || null, // Default value if not found\r\n                  job_title:\r\n                    contact?.business_partner_person?.bp_extension?.job_title ||\r\n                    '',\r\n                  contact_person_vip_type: contact?.person_func_and_dept\r\n                    ?.contact_person_vip_type\r\n                    ? true\r\n                    : false,\r\n                  web_registered: contact?.business_partner_person?.bp_extension\r\n                    ?.web_registered\r\n                    ? true\r\n                    : false,\r\n                  communication_preference:\r\n                    contact?.business_partner_person?.addresses?.[0]\r\n                      ?.prfrd_comm_medium_type || '-',\r\n                  validity_end_date:\r\n                    new Date().toISOString().split('T')[0] <\r\n                    contact?.validity_end_date?.split('T')[0]\r\n                      ? false\r\n                      : true,\r\n                };\r\n              });\r\n            }\r\n          });\r\n      });\r\n  }\r\n\r\n  public reactivateSelectedContacts() {\r\n    if (!this.selectedContacts || this.selectedContacts.length === 0) {\r\n      return;\r\n    }\r\n    const reactivateRequests = this.selectedContacts.map((contact) =>\r\n      this.accountservice.updateReactivate(contact).toPromise()\r\n    );\r\n    Promise.all(reactivateRequests)\r\n      .then(() => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Contacts Reactivated successfully!.',\r\n        });\r\n        this.opportunitiesservice\r\n          .getOpportunityByID(this.id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n        this.selectedContacts = [];\r\n      })\r\n      .catch((error) => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error during bulk update :' + error,\r\n        });\r\n      });\r\n  }\r\n\r\n  private loadContacts() {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.contactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.accountservice.getContacts(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  editContact(contact: any) {\r\n    this.addDialogVisible = true;\r\n    this.editid = contact?.documentId;\r\n\r\n    this.ContactForm.patchValue({\r\n      first_name: contact.first_name,\r\n      middle_name: contact.middle_name,\r\n      last_name: contact.last_name,\r\n      job_title: contact.job_title,\r\n      email_address: contact.email_address,\r\n      phone_number: contact.phone_number,\r\n      mobile: contact.mobile,\r\n      validity_end_date: contact.validity_end_date,\r\n      contact_person_vip_type: contact.contact_person_vip_type,\r\n      contactexisting: '',\r\n\r\n      // Ensure department & function are set correctly\r\n      contact_person_function_name:\r\n        this.cpFunctions.find(\r\n          (f) => f.value === contact?.contact_person_function_name?.value\r\n        ) || null,\r\n      contact_person_department_name:\r\n        this.cpDepartments.find(\r\n          (d) => d.value === contact?.contact_person_department_name?.value\r\n        ) || null,\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n    if (this.ContactForm.value?.contactexisting) {\r\n      const existing = this.ContactForm.value.contactexisting;\r\n\r\n      const data = {\r\n        opportunity_party_contact_party_id: existing?.bp_id,\r\n        opportunity_id: this.opportunity_id,\r\n        role_code: '',\r\n      };\r\n\r\n      this.saving = true;\r\n\r\n      this.opportunitiesservice\r\n        .createExistingContact(data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.existingDialogVisible = false;\r\n            this.ContactForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Contact Added successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n\r\n      // Skip rest of logic for new contact\r\n      return;\r\n    }\r\n\r\n    if (this.ContactForm.invalid) {\r\n      console.log('Form is invalid:', this.ContactForm.errors);\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.ContactForm.value };\r\n\r\n    const data = {\r\n      bp_id: this.id,\r\n      first_name: value?.first_name || '',\r\n      middle_name: value?.middle_name,\r\n      last_name: value?.last_name || '',\r\n      job_title: value?.job_title || '',\r\n      contact_person_function_name:\r\n        value?.contact_person_function_name?.name || '',\r\n      contact_person_function: value?.contact_person_function_name?.value || '',\r\n      contact_person_department_name:\r\n        value?.contact_person_department_name?.name || '',\r\n      contact_person_department:\r\n        value?.contact_person_department_name?.value || '',\r\n      email_address: value?.email_address,\r\n      phone_number: value?.phone_number,\r\n      mobile: value?.mobile,\r\n      contact_person_vip_type: value?.contact_person_vip_type,\r\n      validity_end_date: value?.validity_end_date\r\n        ? new Date().toISOString().split('T')[0]\r\n        : '9999-12-29',\r\n    };\r\n\r\n    if (this.editid) {\r\n      this.accountservice\r\n        .updateContact(this.editid, data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.ContactForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Contact Updated successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.documentId)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.accountservice\r\n        .createContact(data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.existingDialogVisible = false;\r\n            this.ContactForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Contact created successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.ContactForm.reset();\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.ContactForm.controls;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Contacts</h4>\r\n        <div class=\"flex gap-3 ml-auto\">\r\n            <!-- <p-button label=\"Reactivate\" icon=\"pi pi-check\" iconPos=\"right\" class=\"font-semibold\" [rounded]=\"true\"\r\n                [styleClass]=\"'px-3'\" (click)=\"reactivateSelectedContacts()\"\r\n                [disabled]=\"!selectedContacts || selectedContacts.length === 0\" /> -->\r\n            <!-- <p-button label=\"Add New\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" /> -->\r\n            <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\" iconPos=\"right\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"contactDetails\" [(selection)]=\"selectedContacts\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem table-checkbox text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn pSortableColumn=\"full_name\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Name\r\n                            <p-sortIcon field=\"full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"job_title\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">Job Title<p-sortIcon field=\"job_title\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"phone_number\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Phone\r\n                            <p-sortIcon field=\"phone_number\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"mobile\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Mobile\r\n                            <p-sortIcon field=\"mobile\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"email_address\" style=\"width: 15%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            E-Mail\r\n                            <p-sortIcon field=\"email_address\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"contact_person_function_name\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">Function<p-sortIcon\r\n                                field=\"contact_person_department_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"contact_person_department_name\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Department\r\n                            <p-sortIcon field=\"contact_person_department_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th style=\"width: 10%\">Web Registered</th>\r\n                    <th style=\"width: 7%\">VIP Contact</th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center gap-2\">Deactivate</div>\r\n                    </th>\r\n                    <th style=\"width: 8%\">Comm. Preference</th>\r\n                    <th style=\"width: 8%\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact>\r\n                <tr>\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td pFrozenColumn>\r\n                        {{ contact?.business_partner?.bp_full_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.job_title || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.phone_number || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.mobile || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.email_address || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.contact_person_function_name?.name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.contact_person_department_name?.name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        <p-checkbox [binary]=\"true\" [ngModel]=\"contact.web_registered\" [disabled]=\"true\"></p-checkbox>\r\n                    </td>\r\n                    <td>\r\n                        <p-checkbox [binary]=\"true\" [ngModel]=\"contact.contact_person_vip_type\"\r\n                            [disabled]=\"true\"></p-checkbox>\r\n                    </td>\r\n                    <td>\r\n                        <p-checkbox [binary]=\"true\" [ngModel]=\"contact.validity_end_date\"\r\n                            [disabled]=\"true\"></p-checkbox>\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.communication_preference}}\r\n                    </td>\r\n                    <td>\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editContact(contact)\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" colspan=\"13\">No contacts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"13\" class=\"border-round-left-lg\">\r\n                        Loading contacts data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '38rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"First Name \">\r\n                <span class=\"material-symbols-rounded\">person</span>First Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"first_name\" formControlName=\"first_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['first_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['first_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['first_name'].errors['required']\">\r\n                        First Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Middle Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Middle Name\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"middle_name\" formControlName=\"middle_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Last Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Last Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"last_name\" formControlName=\"last_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['last_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['last_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['last_name'].errors['required']\">\r\n                        Last Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Job Title\">\r\n                <span class=\"material-symbols-rounded\">work</span>Job Title\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"job_title\" formControlName=\"job_title\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Function\">\r\n                <span class=\"material-symbols-rounded\">functions</span>Function\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpFunctions\" formControlName=\"contact_person_function_name\" optionLabel=\"name\"\r\n                    dataKey=\"value\" placeholder=\"Select Function\" [styleClass]=\"'h-3rem w-full'\"></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Department\">\r\n                <span class=\"material-symbols-rounded\">inbox_text_person</span>Department\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpDepartments\" formControlName=\"contact_person_department_name\"\r\n                    optionLabel=\"name\" dataKey=\"value\" placeholder=\"Select Department\" [styleClass]=\"'h-3rem w-full'\">\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Email\">\r\n                <span class=\"material-symbols-rounded\">mail</span>Email<span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"email\" id=\"email_address\" formControlName=\"email_address\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['email_address'].errors }\" />\r\n                <div *ngIf=\"submitted && f['email_address'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"\r\n              submitted &&\r\n              f['email_address'].errors &&\r\n              f['email_address'].errors['required']\r\n            \">\r\n                        Email is required.\r\n                    </div>\r\n                    <div *ngIf=\"f['email_address'].errors['email']\">\r\n                        Email is invalid.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Phone\">\r\n                <span class=\"material-symbols-rounded\">phone_in_talk</span>Phone\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"phone_number\" formControlName=\"phone_number\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n                <div *ngIf=\"ContactForm.get('phone_number')?.touched && ContactForm.get('phone_number')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('phone_number')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Phone number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Mobile\">\r\n                <span class=\"material-symbols-rounded\">smartphone</span>Mobile #\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"mobile\" formControlName=\"mobile\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['mobile'].errors }\" />\r\n                <div *ngIf=\"submitted && f['mobile'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['mobile'].errors['required']\">\r\n                        Mobile is required.\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"ContactForm.get('mobile')?.touched && ContactForm.get('mobile')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('mobile')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Mobile number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"editid && ContactForm.value.first_name\">\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"DeActivate\">\r\n                    <span class=\"material-symbols-rounded\">remove_circle_outline</span>DeActivate\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"validity_end_date\" formControlName=\"validity_end_date\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"VIP Contact\">\r\n                    <span class=\"material-symbols-rounded\">star</span>VIP Contact\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"contact_person_vip_type\" formControlName=\"contact_person_vip_type\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                <span class=\"material-symbols-rounded\">person</span>Contacts\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" [hideSelected]=\"true\"\r\n                    [loading]=\"contactLoading\" [minTermLength]=\"0\" formControlName=\"contactexisting\"\r\n                    [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\" appendTo=\"body\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.phone\"> : {{ item.phone }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"existingDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AAGA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SACEC,OAAO,EACPC,SAAS,EAETC,MAAM,EACNC,GAAG,EACHC,EAAE,EACFC,QAAQ,QACH,MAAM;AAEb,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,QACL,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAHC,EADJ,CAAAC,cAAA,SAAI,aACsF;IAClFD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAiE,cAClB;IACvCD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA2C;IAEnDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAmD,cACJ;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAE,SAAA,sBAA2C;IAEnGF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAsD,eACP;IACvCD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAgD,eACD;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAwC;IAEhDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAuD,eACR;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA+C;IAEvDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAsE,eACvB;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAE,SAAA,sBACS;IAEhEF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAwE,eACzB;IACvCD,EAAA,CAAAI,MAAA,oBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAgE;IAExEF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAElCH,EADJ,CAAAC,cAAA,UAAI,eAC2C;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IACzDJ,EADyD,CAAAG,YAAA,EAAM,EAC1D;IACLH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAChCJ,EADgC,CAAAG,YAAA,EAAK,EAChC;;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACuE;IACnED,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAkB;IACdD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,SAAA,sBAA8F;IAClGF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,SAAA,sBACmC;IACvCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,SAAA,sBACmC;IACvCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,UAAI,kBAEmC;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,+EAAA;MAAA,MAAAC,UAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,UAAA,CAAoB;IAAA,EAAC;IAE1CP,EAF2C,CAAAG,YAAA,EAAS,EAC3C,EACJ;;;;IAzCoBH,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAgB,UAAA,UAAAT,UAAA,CAAiB;IAGlCP,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAW,gBAAA,kBAAAX,UAAA,CAAAW,gBAAA,CAAAC,YAAA,cACJ;IAEInB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAa,SAAA,cACJ;IAEIpB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAc,YAAA,cACJ;IAEIrB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAe,MAAA,cACJ;IAEItB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAgB,aAAA,cACJ;IAEIvB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAiB,4BAAA,kBAAAjB,UAAA,CAAAiB,4BAAA,CAAAC,IAAA,cACJ;IAEIzB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,OAAAV,UAAA,kBAAAA,UAAA,CAAAmB,8BAAA,kBAAAnB,UAAA,CAAAmB,8BAAA,CAAAD,IAAA,cACJ;IAEgBzB,EAAA,CAAAe,SAAA,GAAe;IAAoCf,EAAnD,CAAAgB,UAAA,gBAAe,YAAAT,UAAA,CAAAoB,cAAA,CAAmC,kBAAkB;IAGpE3B,EAAA,CAAAe,SAAA,GAAe;IACvBf,EADQ,CAAAgB,UAAA,gBAAe,YAAAT,UAAA,CAAAqB,uBAAA,CAA4C,kBAClD;IAGT5B,EAAA,CAAAe,SAAA,GAAe;IACvBf,EADQ,CAAAgB,UAAA,gBAAe,YAAAT,UAAA,CAAAsB,iBAAA,CAAsC,kBAC5C;IAGrB7B,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAiB,kBAAA,MAAAV,UAAA,kBAAAA,UAAA,CAAAuB,wBAAA,MACJ;;;;;IASA9B,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACpEJ,EADoE,CAAAG,YAAA,EAAK,EACpE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAC1CD,EAAA,CAAAI,MAAA,8CACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IAchBH,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAI,MAAA,gCACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA+B,UAAA,IAAAC,oDAAA,kBAAgD;IAGpDhC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAAsB,CAAA,eAAAC,MAAA,aAAwC;;;;;IAyB9ClC,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAI,MAAA,+BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA+B,UAAA,IAAAI,oDAAA,kBAA+C;IAGnDnC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAe,SAAA,EAAuC;IAAvCf,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAAsB,CAAA,cAAAC,MAAA,aAAuC;;;;;IA2C7ClC,EAAA,CAAAC,cAAA,UAIN;IACUD,EAAA,CAAAI,MAAA,2BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAI,MAAA,0BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAXVH,EAAA,CAAAC,cAAA,cACmE;IAQ/DD,EAPA,CAAA+B,UAAA,IAAAK,oDAAA,kBAIN,IAAAC,oDAAA,kBAGsD;IAGpDrC,EAAA,CAAAG,YAAA,EAAM;;;;IAVIH,EAAA,CAAAe,SAAA,EAIf;IAJef,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAA2B,SAAA,IAAA3B,MAAA,CAAAsB,CAAA,kBAAAC,MAAA,IAAAvB,MAAA,CAAAsB,CAAA,kBAAAC,MAAA,aAIf;IAGelC,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAAsB,CAAA,kBAAAC,MAAA,UAAwC;;;;;IAc9ClC,EAAA,CAAAC,cAAA,cAAkF;IAC9ED,EAAA,CAAAI,MAAA,2CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAkG;IAC9FD,EAAA,CAAA+B,UAAA,IAAAQ,oDAAA,kBAAkF;IAGtFvC,EAAA,CAAAG,YAAA,EAAM;;;;;IAHIH,EAAA,CAAAe,SAAA,EAA0D;IAA1Df,EAAA,CAAAgB,UAAA,UAAAwB,OAAA,GAAA7B,MAAA,CAAA8B,WAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAA0D;;;;;IAgBhElC,EAAA,CAAAC,cAAA,UAA4C;IACxCD,EAAA,CAAAI,MAAA,4BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA+B,UAAA,IAAAY,oDAAA,kBAA4C;IAGhD3C,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAe,SAAA,EAAoC;IAApCf,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAAsB,CAAA,WAAAC,MAAA,aAAoC;;;;;IAK1ClC,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,MAAA,4CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAsF;IAClFD,EAAA,CAAA+B,UAAA,IAAAa,oDAAA,kBAA4E;IAGhF5C,EAAA,CAAAG,YAAA,EAAM;;;;;IAHIH,EAAA,CAAAe,SAAA,EAAoD;IAApDf,EAAA,CAAAgB,UAAA,UAAAwB,OAAA,GAAA7B,MAAA,CAAA8B,WAAA,CAAAC,GAAA,6BAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAAoD;;;;;IAS1DlC,EAHZ,CAAAC,cAAA,UAAoD,cACK,gBACiD,eACvD;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAI,MAAA,kBACvE;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAwC;IACpCD,EAAA,CAAAE,SAAA,qBACuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,cAAqD,gBACkD,gBACxD;IAAAD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAI,MAAA,oBACtD;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACpCD,EAAA,CAAAE,SAAA,sBACuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;IAb6EH,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;IASHhB,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;;;;;IAiB9GhB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IAcZH,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAe,SAAA,EAAyB;IAAzBf,EAAA,CAAAiB,kBAAA,QAAA4B,OAAA,CAAA1B,YAAA,KAAyB;;;;;IAC1DnB,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAe,SAAA,EAAkB;IAAlBf,EAAA,CAAAiB,kBAAA,QAAA4B,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5C9C,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAe,SAAA,EAAkB;IAAlBf,EAAA,CAAAiB,kBAAA,QAAA4B,OAAA,CAAAE,KAAA,KAAkB;;;;;IAH5C/C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAFA,CAAA+B,UAAA,IAAAiB,8DAAA,mBAAgC,IAAAC,8DAAA,mBACP,IAAAC,8DAAA,mBACA;;;;IAHnBlD,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAmD,iBAAA,CAAAN,OAAA,CAAAO,KAAA,CAAgB;IACfpD,EAAA,CAAAe,SAAA,EAAuB;IAAvBf,EAAA,CAAAgB,UAAA,SAAA6B,OAAA,CAAA1B,YAAA,CAAuB;IACvBnB,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAgB,UAAA,SAAA6B,OAAA,CAAAC,KAAA,CAAgB;IAChB9C,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAgB,UAAA,SAAA6B,OAAA,CAAAE,KAAA,CAAgB;;;AD9R/C,OAAM,MAAOM,8BAA8B;EAsCzCC,YACUC,KAAqB,EACrBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,WAAwB,EACxBC,cAA8B;IAJ9B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IA1ChB,KAAAC,YAAY,GAAG,IAAItE,OAAO,EAAQ;IACnC,KAAAuE,cAAc,GAAQ,IAAI;IAC1B,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAA/B,SAAS,GAAG,KAAK;IACjB,KAAAgC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,aAAa,GAAsC,EAAE;IACrD,KAAAC,WAAW,GAAsC,EAAE;IAEnD,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAItF,OAAO,EAAU;IACpC,KAAAuF,cAAc,GAAQ,EAAE;IACzB,KAAAC,gBAAgB,GAAG,EAAE;IAErB,KAAArC,WAAW,GAAc,IAAI,CAACiB,WAAW,CAACqB,KAAK,CAAC;MACrDC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MACvCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAAC4F,QAAQ,CAAC,CAAC;MACtC7D,SAAS,EAAE,CAAC,EAAE,CAAC;MACfI,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClCE,8BAA8B,EAAE,CAAC,EAAE,CAAC;MACpCH,aAAa,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC4F,QAAQ,EAAE5F,UAAU,CAACyD,KAAK,CAAC,CAAC;MAC5DzB,YAAY,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC+F,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACzD9D,MAAM,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAAC4F,QAAQ,EAAE5F,UAAU,CAAC+F,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACxExD,uBAAuB,EAAE,CAAC,EAAE,CAAC;MAC7BC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBwD,eAAe,EAAE,CAAC,EAAE;KACrB,CAAC;EAQC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACvB,cAAc,GAAG,IAAI,CAACR,KAAK,CAACgC,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAAC/C,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1E,IAAI,CAACgD,YAAY,EAAE;IACnB/F,QAAQ,CAAC;MACPqE,WAAW,EAAE,IAAI,CAACP,cAAc,CAACkC,eAAe,EAAE;MAClD1B,SAAS,EAAE,IAAI,CAACR,cAAc,CAACmC,aAAa;KAC7C,CAAC,CACCC,IAAI,CAACtG,SAAS,CAAC,IAAI,CAACqE,YAAY,CAAC,CAAC,CAClCkC,SAAS,CAAC,CAAC;MAAE9B,WAAW;MAAEC;IAAS,CAAE,KAAI;MACxC;MACA,IAAI,CAACQ,aAAa,GAAG,CAACT,WAAW,EAAE+B,IAAI,IAAI,EAAE,EAAEtG,GAAG,CAAEuG,IAAS,KAAM;QACjEvE,IAAI,EAAEuE,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MAEH;MACA,IAAI,CAACzB,WAAW,GAAG,CAACT,SAAS,EAAE8B,IAAI,IAAI,EAAE,EAAEtG,GAAG,CAAEuG,IAAS,KAAM;QAC7DvE,IAAI,EAAEuE,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MACH,IAAI,CAAC3C,oBAAoB,CAAC4C,WAAW,CAClCP,IAAI,CAACtG,SAAS,CAAC,IAAI,CAACqE,YAAY,CAAC,CAAC,CAClCkC,SAAS,CAAEO,QAAa,IAAI;QAC3B,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACvC,EAAE,GAAGuC,QAAQ,EAAEtC,cAAc;UAClC,IAAI,CAACQ,UAAU,GAAG8B,QAAQ,EAAE9B,UAAU;UACtC,IAAI,CAACV,cAAc,GACjBwC,QAAQ,EAAEC,oCAAoC,IAAI,EAAE;UAEtD,IAAI,CAACzC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACpE,GAAG,CAAE8G,OAAY,IAAI;YAC7D,OAAO;cACL,GAAGA,OAAO;cACVC,SAAS,EAAE,CACTD,OAAO,EAAEE,uBAAuB,EAAEzB,UAAU,EAC5CuB,OAAO,EAAEE,uBAAuB,EAAEvB,WAAW,EAC7CqB,OAAO,EAAEE,uBAAuB,EAAEtB,SAAS,CAC5C,CACEuB,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;cAEZ5B,UAAU,EACRuB,OAAO,EAAEE,uBAAuB,EAAEzB,UAAU,IAAI,EAAE;cACpDE,WAAW,EACTqB,OAAO,EAAEE,uBAAuB,EAAEvB,WAAW,IAAI,EAAE;cACrDC,SAAS,EAAEoB,OAAO,EAAEE,uBAAuB,EAAEtB,SAAS,IAAI,EAAE;cAC5D5D,aAAa,EACXgF,OAAO,EAAEE,uBAAuB,EAAEI,SAAS,GAAG,CAAC,CAAC,EAC5CC,MAAM,GAAG,CAAC,CAAC,EAAEvF,aAAa,IAAI,EAAE;cACtCF,YAAY,EAAE,CACZkF,OAAO,EAAEE,uBAAuB,EAAEI,SAAS,GAAG,CAAC,CAAC,EAC5CE,aAAa,IAAI,EAAE,EACvBC,IAAI,CAAEhB,IAAS,IAAKA,IAAI,CAACiB,iBAAiB,KAAK,GAAG,CAAC,EACjD5F,YAAY;cAChBC,MAAM,EAAE,CACNiF,OAAO,EAAEE,uBAAuB,EAAEI,SAAS,GAAG,CAAC,CAAC,EAC5CE,aAAa,IAAI,EAAE,EACvBC,IAAI,CAAEhB,IAAS,IAAKA,IAAI,CAACiB,iBAAiB,KAAK,GAAG,CAAC,EACjD5F,YAAY;cAEhB;cACAK,8BAA8B,EAC5B,IAAI,CAAC+C,aAAa,EAAEuC,IAAI,CACrBE,CAAM,IACLA,CAAC,CAAChB,KAAK,KACPK,OAAO,EAAEY,oBAAoB,EAAEC,yBAAyB,CAC3D,IAAI,IAAI;cAAE;cAEb5F,4BAA4B,EAC1B,IAAI,CAACkD,WAAW,EAAEsC,IAAI,CACnB/E,CAAM,IACLA,CAAC,CAACiE,KAAK,KACPK,OAAO,EAAEY,oBAAoB,EAAEE,uBAAuB,CACzD,IAAI,IAAI;cAAE;cACbjG,SAAS,EACPmF,OAAO,EAAEE,uBAAuB,EAAEa,YAAY,EAAElG,SAAS,IACzD,EAAE;cACJQ,uBAAuB,EAAE2E,OAAO,EAAEY,oBAAoB,EAClDvF,uBAAuB,GACvB,IAAI,GACJ,KAAK;cACTD,cAAc,EAAE4E,OAAO,EAAEE,uBAAuB,EAAEa,YAAY,EAC1D3F,cAAc,GACd,IAAI,GACJ,KAAK;cACTG,wBAAwB,EACtByE,OAAO,EAAEE,uBAAuB,EAAEI,SAAS,GAAG,CAAC,CAAC,EAC5CU,sBAAsB,IAAI,GAAG;cACnC1F,iBAAiB,EACf,IAAI2F,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACtCnB,OAAO,EAAE1E,iBAAiB,EAAE6F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACrC,KAAK,GACL;aACP;UACH,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEOC,0BAA0BA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAAC7C,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC8C,MAAM,KAAK,CAAC,EAAE;MAChE;IACF;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAC/C,gBAAgB,CAACrF,GAAG,CAAE8G,OAAO,IAC3D,IAAI,CAAC9C,cAAc,CAACqE,gBAAgB,CAACvB,OAAO,CAAC,CAACwB,SAAS,EAAE,CAC1D;IACDC,OAAO,CAACC,GAAG,CAACJ,kBAAkB,CAAC,CAC5BK,IAAI,CAAC,MAAK;MACT,IAAI,CAACvE,cAAc,CAACwE,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAAC7E,oBAAoB,CACtB8E,kBAAkB,CAAC,IAAI,CAACxE,EAAE,CAAC,CAC3B+B,IAAI,CAACtG,SAAS,CAAC,IAAI,CAACqE,YAAY,CAAC,CAAC,CAClCkC,SAAS,EAAE;MACd,IAAI,CAAChB,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACDyD,KAAK,CAAEC,KAAK,IAAI;MACf,IAAI,CAAC7E,cAAc,CAACwE,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,4BAA4B,GAAGG;OACxC,CAAC;IACJ,CAAC,CAAC;EACN;EAEQ9C,YAAYA,CAAA;IAClB,IAAI,CAAC+C,SAAS,GAAGjJ,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACmF,cAAc,CAAC;IAAE;IACzB,IAAI,CAACD,aAAa,CAACiB,IAAI,CACrBjG,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC6E,cAAc,GAAG,IAAK,CAAC,EACvC9E,SAAS,CAAE6I,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAACjF,cAAc,CAACmF,WAAW,CAACD,MAAM,CAAC,CAAC9C,IAAI,CACjDpG,GAAG,CAAEsG,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFjG,GAAG,CAAC,MAAO,IAAI,CAAC6E,cAAc,GAAG,KAAM,CAAC,EACxC5E,UAAU,CAAEyI,KAAK,IAAI;QACnB,IAAI,CAAC7D,cAAc,GAAG,KAAK;QAC3B,OAAOjF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAoB,WAAWA,CAACyF,OAAY;IACtB,IAAI,CAACrC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACI,MAAM,GAAGiC,OAAO,EAAEhC,UAAU;IAEjC,IAAI,CAAC9B,WAAW,CAACoG,UAAU,CAAC;MAC1B7D,UAAU,EAAEuB,OAAO,CAACvB,UAAU;MAC9BE,WAAW,EAAEqB,OAAO,CAACrB,WAAW;MAChCC,SAAS,EAAEoB,OAAO,CAACpB,SAAS;MAC5B/D,SAAS,EAAEmF,OAAO,CAACnF,SAAS;MAC5BG,aAAa,EAAEgF,OAAO,CAAChF,aAAa;MACpCF,YAAY,EAAEkF,OAAO,CAAClF,YAAY;MAClCC,MAAM,EAAEiF,OAAO,CAACjF,MAAM;MACtBO,iBAAiB,EAAE0E,OAAO,CAAC1E,iBAAiB;MAC5CD,uBAAuB,EAAE2E,OAAO,CAAC3E,uBAAuB;MACxDyD,eAAe,EAAE,EAAE;MAEnB;MACA7D,4BAA4B,EAC1B,IAAI,CAACkD,WAAW,CAACsC,IAAI,CAClB/E,CAAC,IAAKA,CAAC,CAACiE,KAAK,KAAKK,OAAO,EAAE/E,4BAA4B,EAAE0E,KAAK,CAChE,IAAI,IAAI;MACXxE,8BAA8B,EAC5B,IAAI,CAAC+C,aAAa,CAACuC,IAAI,CACpBE,CAAC,IAAKA,CAAC,CAAChB,KAAK,KAAKK,OAAO,EAAE7E,8BAA8B,EAAEwE,KAAK,CAClE,IAAI;KACR,CAAC;EACJ;EAEM4C,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACzG,SAAS,GAAG,IAAI;MACrByG,KAAI,CAAC3E,OAAO,GAAG,IAAI;MACnB,IAAI2E,KAAI,CAACtG,WAAW,CAACyD,KAAK,EAAEb,eAAe,EAAE;QAC3C,MAAM4D,QAAQ,GAAGF,KAAI,CAACtG,WAAW,CAACyD,KAAK,CAACb,eAAe;QAEvD,MAAMU,IAAI,GAAG;UACXmD,kCAAkC,EAAED,QAAQ,EAAE7F,KAAK;UACnDW,cAAc,EAAEgF,KAAI,CAAChF,cAAc;UACnCoF,SAAS,EAAE;SACZ;QAEDJ,KAAI,CAACvE,MAAM,GAAG,IAAI;QAElBuE,KAAI,CAACvF,oBAAoB,CACtB4F,qBAAqB,CAACrD,IAAI,CAAC,CAC3BF,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,CAAC;UACTuD,QAAQ,EAAEA,CAAA,KAAK;YACbN,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC5E,qBAAqB,GAAG,KAAK;YAClC4E,KAAI,CAACtG,WAAW,CAAC6G,KAAK,EAAE;YACxBP,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFU,KAAI,CAACvF,oBAAoB,CACtB8E,kBAAkB,CAACS,KAAI,CAACjF,EAAE,CAAC,CAC3B+B,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,EAAE;UAChB,CAAC;UACD0C,KAAK,EAAEA,CAAA,KAAK;YACVO,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC7E,gBAAgB,GAAG,KAAK;YAC7B6E,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;QAEJ;QACA;MACF;MAEA,IAAIU,KAAI,CAACtG,WAAW,CAAC8G,OAAO,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEV,KAAI,CAACtG,WAAW,CAACP,MAAM,CAAC;QACxD6G,KAAI,CAAC3E,OAAO,GAAG,IAAI;QACnB;MACF;MAEA2E,KAAI,CAACvE,MAAM,GAAG,IAAI;MAClB,MAAM0B,KAAK,GAAG;QAAE,GAAG6C,KAAI,CAACtG,WAAW,CAACyD;MAAK,CAAE;MAE3C,MAAMH,IAAI,GAAG;QACX3C,KAAK,EAAE2F,KAAI,CAACjF,EAAE;QACdkB,UAAU,EAAEkB,KAAK,EAAElB,UAAU,IAAI,EAAE;QACnCE,WAAW,EAAEgB,KAAK,EAAEhB,WAAW;QAC/BC,SAAS,EAAEe,KAAK,EAAEf,SAAS,IAAI,EAAE;QACjC/D,SAAS,EAAE8E,KAAK,EAAE9E,SAAS,IAAI,EAAE;QACjCI,4BAA4B,EAC1B0E,KAAK,EAAE1E,4BAA4B,EAAEC,IAAI,IAAI,EAAE;QACjD4F,uBAAuB,EAAEnB,KAAK,EAAE1E,4BAA4B,EAAE0E,KAAK,IAAI,EAAE;QACzExE,8BAA8B,EAC5BwE,KAAK,EAAExE,8BAA8B,EAAED,IAAI,IAAI,EAAE;QACnD2F,yBAAyB,EACvBlB,KAAK,EAAExE,8BAA8B,EAAEwE,KAAK,IAAI,EAAE;QACpD3E,aAAa,EAAE2E,KAAK,EAAE3E,aAAa;QACnCF,YAAY,EAAE6E,KAAK,EAAE7E,YAAY;QACjCC,MAAM,EAAE4E,KAAK,EAAE5E,MAAM;QACrBM,uBAAuB,EAAEsE,KAAK,EAAEtE,uBAAuB;QACvDC,iBAAiB,EAAEqE,KAAK,EAAErE,iBAAiB,GACvC,IAAI2F,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACtC;OACL;MAED,IAAIqB,KAAI,CAACzE,MAAM,EAAE;QACfyE,KAAI,CAACtF,cAAc,CAChBiG,aAAa,CAACX,KAAI,CAACzE,MAAM,EAAEyB,IAAI,CAAC,CAChCF,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,CAAC;UACTuD,QAAQ,EAAEA,CAAA,KAAK;YACbN,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC7E,gBAAgB,GAAG,KAAK;YAC7B6E,KAAI,CAACtG,WAAW,CAAC6G,KAAK,EAAE;YACxBP,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFU,KAAI,CAACvF,oBAAoB,CACtB8E,kBAAkB,CAACS,KAAI,CAACxE,UAAU,CAAC,CACnCsB,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,EAAE;UAChB,CAAC;UACD0C,KAAK,EAAGmB,GAAQ,IAAI;YAClBZ,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC7E,gBAAgB,GAAG,KAAK;YAC7B6E,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACLU,KAAI,CAACtF,cAAc,CAChBmG,aAAa,CAAC7D,IAAI,CAAC,CACnBF,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,CAAC;UACTuD,QAAQ,EAAEA,CAAA,KAAK;YACbN,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC7E,gBAAgB,GAAG,KAAK;YAC7B6E,KAAI,CAAC5E,qBAAqB,GAAG,KAAK;YAClC4E,KAAI,CAACtG,WAAW,CAAC6G,KAAK,EAAE;YACxBP,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFU,KAAI,CAACvF,oBAAoB,CACtB8E,kBAAkB,CAACS,KAAI,CAACjF,EAAE,CAAC,CAC3B+B,IAAI,CAACtG,SAAS,CAACwJ,KAAI,CAACnF,YAAY,CAAC,CAAC,CAClCkC,SAAS,EAAE;UAChB,CAAC;UACD0C,KAAK,EAAEA,CAAA,KAAK;YACVO,KAAI,CAACvE,MAAM,GAAG,KAAK;YACnBuE,KAAI,CAAC7E,gBAAgB,GAAG,KAAK;YAC7B6E,KAAI,CAACpF,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEAwB,aAAaA,CAACxF,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC5B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,WAAW,CAAC6G,KAAK,EAAE;EAC1B;EAEAQ,kBAAkBA,CAACzF,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACF,qBAAqB,GAAG,IAAI;EACnC;EAEA,IAAIlC,CAACA,CAAA;IACH,OAAO,IAAI,CAACQ,WAAW,CAACsH,QAAQ;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpG,YAAY,CAACqG,IAAI,EAAE;IACxB,IAAI,CAACrG,YAAY,CAACyF,QAAQ,EAAE;EAC9B;;;uBApYWhG,8BAA8B,EAAArD,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAtK,EAAA,CAAAkK,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxK,EAAA,CAAAkK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA1K,EAAA,CAAAkK,iBAAA,CAAAS,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA9BvH,8BAA8B;MAAAwH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBnCnL,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAI,MAAA,eAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAOxDH,EANJ,CAAAC,cAAA,aAAgC,kBAO+B;UADxBD,EAAA,CAAAK,UAAA,mBAAAgL,kEAAA;YAAA,OAASD,GAAA,CAAAtB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAGhF9J,EAHQ,CAAAG,YAAA,EAC2D,EACzD,EACJ;UAGFH,EADJ,CAAAC,cAAA,aAAuB,iBAEwD;UADzCD,EAAA,CAAAsL,gBAAA,6BAAAC,2EAAAC,MAAA;YAAAxL,EAAA,CAAAyL,kBAAA,CAAAL,GAAA,CAAAtG,gBAAA,EAAA0G,MAAA,MAAAJ,GAAA,CAAAtG,gBAAA,GAAA0G,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UA2G9DxL,EAzGA,CAAA+B,UAAA,IAAA2J,qDAAA,0BAAgC,IAAAC,qDAAA,2BAsDU,KAAAC,sDAAA,yBA8CJ,KAAAC,sDAAA,0BAKD;UASjD7L,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC0D;UADjCD,EAAA,CAAAsL,gBAAA,2BAAAQ,2EAAAN,MAAA;YAAAxL,EAAA,CAAAyL,kBAAA,CAAAL,GAAA,CAAAlH,gBAAA,EAAAsH,MAAA,MAAAJ,GAAA,CAAAlH,gBAAA,GAAAsH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAEnDxL,EAAA,CAAA+B,UAAA,KAAAgK,sDAAA,yBAAgC;UAOpB/L,EAHZ,CAAAC,cAAA,gBAAwE,eACf,iBACkD,gBACxD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,mBACpD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC2F;UAC3FF,EAAA,CAAA+B,UAAA,KAAAiK,8CAAA,kBACmE;UAM3EhM,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACkD,gBACxD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,oBACxD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UAEjCF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgD,gBACtD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACpD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC0F;UAC1FF,EAAA,CAAA+B,UAAA,KAAAkK,8CAAA,kBACmE;UAM3EjM,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgD,gBACtD;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACtD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UAEjCF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC+C,gBACrD;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,iBAC3D;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,sBAC8F;UAEtGF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACiD,gBACvD;UAAAD,EAAA,CAAAI,MAAA,yBAAiB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,mBACnE;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,sBAEa;UAErBF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4C,gBAClD;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UACvFJ,EADuF,CAAAG,YAAA,EAAO,EACtF;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC8F;UAC9FF,EAAA,CAAA+B,UAAA,KAAAmK,8CAAA,kBACmE;UAa3ElM,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4C,gBAClD;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,cAC/D;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UACzBF,EAAA,CAAA+B,UAAA,KAAAoK,8CAAA,kBAAkG;UAM1GnM,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC6C,gBACnD;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,iBACxD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACuF;UAOvFF,EANA,CAAA+B,UAAA,KAAAqK,8CAAA,kBACmE,KAAAC,8CAAA,kBAKmB;UAM9FrM,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAA+B,UAAA,KAAAuK,8CAAA,mBAAoD;UAqBhDtM,EADJ,CAAAC,cAAA,eAAoD,kBAGT;UAAnCD,EAAA,CAAAK,UAAA,mBAAAkM,iEAAA;YAAA,OAAAnB,GAAA,CAAAlH,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAClE,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAAmM,iEAAA;YAAA,OAASpB,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC;UAGpC9I,EAHqC,CAAAG,YAAA,EAAS,EAChC,EACH,EACA;UACXH,EAAA,CAAAC,cAAA,oBAC0D;UADjCD,EAAA,CAAAsL,gBAAA,2BAAAmB,2EAAAjB,MAAA;YAAAxL,EAAA,CAAAyL,kBAAA,CAAAL,GAAA,CAAAjH,qBAAA,EAAAqH,MAAA,MAAAJ,GAAA,CAAAjH,qBAAA,GAAAqH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAExDxL,EAAA,CAAA+B,UAAA,KAAA2K,sDAAA,yBAAgC;UAOpB1M,EAHZ,CAAAC,cAAA,gBAAwE,eACf,kBAC+C,iBACrD;UAAAD,EAAA,CAAAI,MAAA,eAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACxD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAEJH,EADJ,CAAAC,cAAA,gBAAwC,sBAGoC;;UACpED,EAAA,CAAA+B,UAAA,MAAA4K,uDAAA,0BAA2C;UAQvD3M,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;UAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAGJ;UAAxCD,EAAA,CAAAK,UAAA,mBAAAuM,kEAAA;YAAA,OAAAxB,GAAA,CAAAjH,qBAAA,GAAiC,KAAK;UAAA,EAAC;UAACnE,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAAwM,kEAAA;YAAA,OAASzB,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC;UAGpC9I,EAHqC,CAAAG,YAAA,EAAS,EAChC,EACH,EACA;;;;;UA5TKH,EAAA,CAAAe,SAAA,GAAmC;UAACf,EAApC,CAAAgB,UAAA,oCAAmC,iBAAiB;UAKnDhB,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAAgB,UAAA,UAAAoK,GAAA,CAAAvH,cAAA,CAAwB;UAAC7D,EAAA,CAAA8M,gBAAA,cAAA1B,GAAA,CAAAtG,gBAAA,CAAgC;UACpC9E,EADkD,CAAAgB,UAAA,YAAW,mBAAmB,oBAC7D;UAoHDhB,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAA+M,UAAA,CAAA/M,EAAA,CAAAgN,eAAA,KAAAC,GAAA,EAA4B;UAA1EjN,EAAA,CAAAgB,UAAA,eAAc;UAAChB,EAAA,CAAA8M,gBAAA,YAAA1B,GAAA,CAAAlH,gBAAA,CAA8B;UACnDlE,EADiF,CAAAgB,UAAA,qBAAoB,oBAClF;UAKbhB,EAAA,CAAAe,SAAA,GAAyB;UAAzBf,EAAA,CAAAgB,UAAA,cAAAoK,GAAA,CAAA3I,WAAA,CAAyB;UAQIzC,EAAA,CAAAe,SAAA,GAAiE;UAAjEf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkN,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,eAAAC,MAAA,EAAiE;UAClFlC,EAAA,CAAAe,SAAA,EAAyC;UAAzCf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,eAAAC,MAAA,CAAyC;UAwBxBlC,EAAA,CAAAe,SAAA,IAAgE;UAAhEf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkN,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,cAAAC,MAAA,EAAgE;UACjFlC,EAAA,CAAAe,SAAA,EAAwC;UAAxCf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,cAAAC,MAAA,CAAwC;UAsBlClC,EAAA,CAAAe,SAAA,IAAuB;UACef,EADtC,CAAAgB,UAAA,YAAAoK,GAAA,CAAA1G,WAAA,CAAuB,+BAC6C;UAQpE1E,EAAA,CAAAe,SAAA,GAAyB;UACkCf,EAD3D,CAAAgB,UAAA,YAAAoK,GAAA,CAAA3G,aAAA,CAAyB,+BACgE;UAU9EzE,EAAA,CAAAe,SAAA,GAAoE;UAApEf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkN,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,kBAAAC,MAAA,EAAoE;UACrFlC,EAAA,CAAAe,SAAA,EAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,kBAAAC,MAAA,CAA4C;UAsB5ClC,EAAA,CAAAe,SAAA,GAA0F;UAA1Ff,EAAA,CAAAgB,UAAA,WAAAoM,QAAA,GAAAhC,GAAA,CAAA3I,WAAA,CAAAC,GAAA,mCAAA0K,QAAA,CAAAC,OAAA,OAAAD,QAAA,GAAAhC,GAAA,CAAA3I,WAAA,CAAAC,GAAA,mCAAA0K,QAAA,CAAA7D,OAAA,EAA0F;UAczEvJ,EAAA,CAAAe,SAAA,GAA6D;UAA7Df,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkN,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,WAAAC,MAAA,EAA6D;UAC9ElC,EAAA,CAAAe,SAAA,EAAqC;UAArCf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAnJ,CAAA,WAAAC,MAAA,CAAqC;UAMrClC,EAAA,CAAAe,SAAA,EAA8E;UAA9Ef,EAAA,CAAAgB,UAAA,WAAAsM,QAAA,GAAAlC,GAAA,CAAA3I,WAAA,CAAAC,GAAA,6BAAA4K,QAAA,CAAAD,OAAA,OAAAC,QAAA,GAAAlC,GAAA,CAAA3I,WAAA,CAAAC,GAAA,6BAAA4K,QAAA,CAAA/D,OAAA,EAA8E;UAOtFvJ,EAAA,CAAAe,SAAA,EAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAA9G,MAAA,IAAA8G,GAAA,CAAA3I,WAAA,CAAAyD,KAAA,CAAAlB,UAAA,CAA4C;UA6BGhF,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAA+M,UAAA,CAAA/M,EAAA,CAAAgN,eAAA,KAAAO,GAAA,EAA4B;UAA/EvN,EAAA,CAAAgB,UAAA,eAAc;UAAChB,EAAA,CAAA8M,gBAAA,YAAA1B,GAAA,CAAAjH,qBAAA,CAAmC;UACxDnE,EADsF,CAAAgB,UAAA,qBAAoB,oBACvF;UAKbhB,EAAA,CAAAe,SAAA,GAAyB;UAAzBf,EAAA,CAAAgB,UAAA,cAAAoK,GAAA,CAAA3I,WAAA,CAAyB;UAMGzC,EAAA,CAAAe,SAAA,GAA2B;UAEjBf,EAFV,CAAAgB,UAAA,UAAAhB,EAAA,CAAAwN,WAAA,UAAApC,GAAA,CAAA3C,SAAA,EAA2B,sBAA+C,YAAA2C,GAAA,CAAAzG,cAAA,CAClE,oBAAoB,cAAAyG,GAAA,CAAAxG,aAAA,CACnB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}