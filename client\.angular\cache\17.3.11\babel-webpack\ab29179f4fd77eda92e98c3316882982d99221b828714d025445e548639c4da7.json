{"ast": null, "code": "'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\n// eslint-disable-next-line no-extra-parens, no-proto\nvar hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */[].__proto__ === Array.prototype;\n\n// eslint-disable-next-line no-extra-parens\nvar desc = hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */'__proto__');\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function' ? callBind([desc.get]) : typeof $getPrototypeOf === 'function' ? /** @type {import('./get')} */function getDunder(value) {\n  // eslint-disable-next-line eqeqeq\n  return $getPrototypeOf(value == null ? value : $Object(value));\n} : false;", "map": {"version": 3, "names": ["callBind", "require", "gOPD", "hasProtoAccessor", "__proto__", "Array", "prototype", "desc", "Object", "$Object", "$getPrototypeOf", "getPrototypeOf", "module", "exports", "get", "getDunder", "value"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/dunder-proto/get.js"], "sourcesContent": ["'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\n// eslint-disable-next-line no-extra-parens, no-proto\nvar hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */ ([]).__proto__ === Array.prototype;\n\n// eslint-disable-next-line no-extra-parens\nvar desc = hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */ ('__proto__'));\n\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function'\n\t? callBind([desc.get])\n\t: typeof $getPrototypeOf === 'function'\n\t\t? /** @type {import('./get')} */ function getDunder(value) {\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\treturn $getPrototypeOf(value == null ? value : $Object(value));\n\t\t}\n\t\t: false;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACjD,IAAIC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;;AAE1B;AACA,IAAIE,gBAAgB,GAAG,qDAAuD,EAAE,CAAEC,SAAS,KAAKC,KAAK,CAACC,SAAS;;AAE/G;AACA,IAAIC,IAAI,GAAGJ,gBAAgB,IAAID,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACF,SAAS,EAAE,4CAA8C,WAAY,CAAC;AAEzH,IAAIG,OAAO,GAAGD,MAAM;AACpB,IAAIE,eAAe,GAAGD,OAAO,CAACE,cAAc;;AAE5C;AACAC,MAAM,CAACC,OAAO,GAAGN,IAAI,IAAI,OAAOA,IAAI,CAACO,GAAG,KAAK,UAAU,GACpDd,QAAQ,CAAC,CAACO,IAAI,CAACO,GAAG,CAAC,CAAC,GACpB,OAAOJ,eAAe,KAAK,UAAU,GACpC,8BAA+B,SAASK,SAASA,CAACC,KAAK,EAAE;EAC1D;EACA,OAAON,eAAe,CAACM,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGP,OAAO,CAACO,KAAK,CAAC,CAAC;AAC/D,CAAC,GACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}