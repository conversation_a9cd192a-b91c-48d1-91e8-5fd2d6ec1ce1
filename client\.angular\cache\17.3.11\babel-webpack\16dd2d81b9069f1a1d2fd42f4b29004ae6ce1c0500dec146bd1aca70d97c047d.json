{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/button\";\nfunction ContactsRelationshipsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 8);\n    i0.ɵɵtext(2, \"Relationship Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Business Partner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"% Shareholder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Main\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 9);\n    i0.ɵɵtext(12, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsRelationshipsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 9)(12, \"button\", 11)(13, \"i\", 12);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.RelationshipType, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.BusinessPartner, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Shareholder, \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Address, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Main, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tableinfo_r1.Action);\n  }\n}\nexport let ContactsRelationshipsComponent = /*#__PURE__*/(() => {\n  class ContactsRelationshipsComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.tableData = [{\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }, {\n        RelationshipType: 'Has Activity Partner',\n        BusinessPartner: 'Mrugesh Amin',\n        Shareholder: '50',\n        Address: 'NY 10003, USA. 2nd Street Dorm.',\n        Main: 'Development partner',\n        Action: 'Delete'\n      }];\n    }\n    static {\n      this.ɵfac = function ContactsRelationshipsComponent_Factory(t) {\n        return new (t || ContactsRelationshipsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContactsRelationshipsComponent,\n        selectors: [[\"app-contacts-relationships\"]],\n        decls: 9,\n        vars: 5,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"]],\n        template: function ContactsRelationshipsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Relationships\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"p-button\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n            i0.ɵɵtemplate(7, ContactsRelationshipsComponent_ng_template_7_Template, 13, 0, \"ng-template\", 6)(8, ContactsRelationshipsComponent_ng_template_8_Template, 15, 6, \"ng-template\", 7);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.PrimeTemplate, i2.Table, i3.Button]\n      });\n    }\n  }\n  return ContactsRelationshipsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}