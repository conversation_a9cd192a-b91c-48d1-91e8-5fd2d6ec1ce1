{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceDashboardComponent } from './service-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ServiceDashboardComponent\n}];\nexport class ServiceDashboardRoutingModule {\n  static {\n    this.ɵfac = function ServiceDashboardRoutingModule_Factory(t) {\n      return new (t || ServiceDashboardRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceDashboardRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceDashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceDashboardComponent", "routes", "path", "component", "ServiceDashboardRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-dashboard\\service-dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceDashboardComponent } from './service-dashboard.component';\r\n\r\nconst routes: Routes = [{ path: '', component: ServiceDashboardComponent }];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceDashboardRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,yBAAyB,QAAQ,+BAA+B;;;AAEzE,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAyB,CAAE,CAAC;AAM3E,OAAM,MAAOI,6BAA6B;;;uBAA7BA,6BAA6B;IAAA;EAAA;;;YAA7BA;IAA6B;EAAA;;;gBAH9BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,6BAA6B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF9BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}