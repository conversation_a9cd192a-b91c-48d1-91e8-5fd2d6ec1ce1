{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Slovenian [sl]\n//! author : <PERSON> : https://github.com/sedovsek\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        return withoutSuffix || isFuture ? 'nekaj sekund' : 'nekaj sekundami';\n      case 'ss':\n        if (number === 1) {\n          result += withoutSuffix ? 'sekundo' : 'sekundi';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'sekundi' : 'sekundah';\n        } else if (number < 5) {\n          result += withoutSuffix || isFuture ? 'sekunde' : 'sekundah';\n        } else {\n          result += 'sekund';\n        }\n        return result;\n      case 'm':\n        return withoutSuffix ? 'ena minuta' : 'eno minuto';\n      case 'mm':\n        if (number === 1) {\n          result += withoutSuffix ? 'minuta' : 'minuto';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'minuti' : 'minutama';\n        } else if (number < 5) {\n          result += withoutSuffix || isFuture ? 'minute' : 'minutami';\n        } else {\n          result += withoutSuffix || isFuture ? 'minut' : 'minutami';\n        }\n        return result;\n      case 'h':\n        return withoutSuffix ? 'ena ura' : 'eno uro';\n      case 'hh':\n        if (number === 1) {\n          result += withoutSuffix ? 'ura' : 'uro';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'uri' : 'urama';\n        } else if (number < 5) {\n          result += withoutSuffix || isFuture ? 'ure' : 'urami';\n        } else {\n          result += withoutSuffix || isFuture ? 'ur' : 'urami';\n        }\n        return result;\n      case 'd':\n        return withoutSuffix || isFuture ? 'en dan' : 'enim dnem';\n      case 'dd':\n        if (number === 1) {\n          result += withoutSuffix || isFuture ? 'dan' : 'dnem';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'dni' : 'dnevoma';\n        } else {\n          result += withoutSuffix || isFuture ? 'dni' : 'dnevi';\n        }\n        return result;\n      case 'M':\n        return withoutSuffix || isFuture ? 'en mesec' : 'enim mesecem';\n      case 'MM':\n        if (number === 1) {\n          result += withoutSuffix || isFuture ? 'mesec' : 'mesecem';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'meseca' : 'mesecema';\n        } else if (number < 5) {\n          result += withoutSuffix || isFuture ? 'mesece' : 'meseci';\n        } else {\n          result += withoutSuffix || isFuture ? 'mesecev' : 'meseci';\n        }\n        return result;\n      case 'y':\n        return withoutSuffix || isFuture ? 'eno leto' : 'enim letom';\n      case 'yy':\n        if (number === 1) {\n          result += withoutSuffix || isFuture ? 'leto' : 'letom';\n        } else if (number === 2) {\n          result += withoutSuffix || isFuture ? 'leti' : 'letoma';\n        } else if (number < 5) {\n          result += withoutSuffix || isFuture ? 'leta' : 'leti';\n        } else {\n          result += withoutSuffix || isFuture ? 'let' : 'leti';\n        }\n        return result;\n    }\n  }\n  var sl = moment.defineLocale('sl', {\n    months: 'januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december'.split('_'),\n    monthsShort: 'jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota'.split('_'),\n    weekdaysShort: 'ned._pon._tor._sre._čet._pet._sob.'.split('_'),\n    weekdaysMin: 'ne_po_to_sr_če_pe_so'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD. MM. YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[danes ob] LT',\n      nextDay: '[jutri ob] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[v] [nedeljo] [ob] LT';\n          case 3:\n            return '[v] [sredo] [ob] LT';\n          case 6:\n            return '[v] [soboto] [ob] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[v] dddd [ob] LT';\n        }\n      },\n      lastDay: '[včeraj ob] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[prejšnjo] [nedeljo] [ob] LT';\n          case 3:\n            return '[prejšnjo] [sredo] [ob] LT';\n          case 6:\n            return '[prejšnjo] [soboto] [ob] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[prejšnji] dddd [ob] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'čez %s',\n      past: 'pred %s',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return sl;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}