<form [formGroup]="OrganizationForm">
    <div class="card shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50">
        <h3 class="mb-2 flex align-items-center h-3rem">Create Organization</h3>
        <div class="p-fluid p-formgrid grid mt-0">
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">badge</span>
                        Name
                    </label>
                    <input pInputText id="name" type="text" formControlName="name" placeholder="Name"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">event</span>
                        Valid From <span class="text-red-500">*</span>
                    </label>
                    <p-calendar formControlName="valid_from" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                        [showIcon]="true" styleClass="h-3rem w-full" placeholder="Valid From"
                        [ngClass]="{ 'is-invalid': submitted && f['valid_from'].errors }" />
                    <div *ngIf="submitted && f['valid_from'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['valid_from'].errors &&
                                f['valid_from'].errors['required']
                              ">
                            Valid From is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">event</span>
                        Valid To <span class="text-red-500">*</span>
                    </label>
                    <p-calendar formControlName="valid_to" inputId="calendar-12h" [showTime]="true" hourFormat="12"
                        [showIcon]="true" styleClass="h-3rem w-full" placeholder="Valid To"
                        [ngClass]="{ 'is-invalid': submitted && f['valid_to'].errors }" />
                    <div *ngIf="submitted && f['valid_to'].errors" class="p-error">
                        <div *ngIf="
                                submitted &&
                                f['valid_to'].errors &&
                                f['valid_to'].errors['required']
                              ">
                            Valid To is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">supervisor_account</span>
                        Parent Unit
                    </label>
                    <ng-select pInputText [items]="units$ | async" bindLabel="name" bindValue="organisational_unit_id"
                        [hideSelected]="true" [loading]="unitLoading" [minTermLength]="0"
                        formControlName="parent_organisational_unit_id" [typeahead]="unitInput$" [maxSelectedItems]="10"
                        appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                        placeholder="Search for a parent unit">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.organisational_unit_id }}</span>
                            <span *ngIf="item.name"> : {{ item.name }}</span>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">business</span>
                        Company Name
                    </label>
                    <input pInputText id="company_name" type="text" formControlName="company_name"
                        placeholder="Company Name" class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">map</span>
                        Country <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode"
                        [(ngModel)]="selectedCountry" (onChange)="onCountryChange()" [filter]="true"
                        formControlName="country_code" [styleClass]="'h-3rem w-full'" placeholder="Select Country"
                        [ngClass]="{ 'is-invalid': submitted && f['country_code'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['country_code'].errors" class="p-error">
                        <div *ngIf="
                submitted &&
                f['country_code'].errors &&
                f['country_code'].errors['required']
              ">
                            Country is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">location_on</span>
                        State <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="states" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedState"
                        formControlName="state" placeholder="Select State" [disabled]="!selectedCountry"
                        [styleClass]="'h-3rem w-full'" [ngClass]="{ 'is-invalid': submitted && f['state'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['state'].errors" class="p-error">
                        <div *ngIf="
                submitted &&
                f['state'].errors &&
                f['state'].errors['required']
              ">
                            State is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">location_city</span>
                        City
                    </label>
                    <input pInputText id="city" type="text" formControlName="city" placeholder="City"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">home</span>
                        House Number
                    </label>
                    <input pInputText id="house_number" type="text" formControlName="house_number"
                        placeholder="House Number" class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">map</span>
                        Street
                    </label>
                    <input pInputText id="street" type="text" formControlName="street" placeholder="Street"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">local_post_office</span>
                        Postal Code
                    </label>
                    <input pInputText id="postal_code" type="text" formControlName="postal_code"
                        placeholder="Postal Code" class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">bar_chart</span>
                        Sales
                    </label>
                    <p-toggleButton formControlName="sales_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">bar_chart</span>
                        Sales Organization
                    </label>
                    <p-toggleButton formControlName="sales_organisation_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">build</span>
                        Service
                    </label>
                    <p-toggleButton formControlName="service_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">build</span>
                        Service Organization
                    </label>
                    <p-toggleButton formControlName="service_organisation_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">campaign</span>
                        Marketing
                    </label>
                    <p-toggleButton formControlName="marketing_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">schema</span>
                        Reporting Line
                    </label>
                    <p-toggleButton formControlName="reporting_line_indicator" onLabel="On" offLabel="Off"
                        styleClass="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">manage_accounts</span>
                        Manager
                    </label>
                    <ng-select pInputText [items]="managers$ | async" bindLabel="bp_full_name"
                        bindValue="business_partner_internal_id" [hideSelected]="true" [loading]="managerLoading"
                        [minTermLength]="0" formControlName="business_partner_internal_id" [typeahead]="managerInput$"
                        [maxSelectedItems]="10" appendTo="body"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                        placeholder="Search for a manager">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.business_partner_internal_id }}</span>
                            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
        </div>
    </div>
    <div class="flex align-items-center gap-3 mt-4 ml-auto">
        <button pButton type="button" label="Cancel"
            class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
            (click)="onCancel()"></button>
        <button pButton type="submit" label="Create" class="p-button-rounded justify-content-center w-9rem h-3rem"
            (click)="onSubmit()"></button>
    </div>
</form>