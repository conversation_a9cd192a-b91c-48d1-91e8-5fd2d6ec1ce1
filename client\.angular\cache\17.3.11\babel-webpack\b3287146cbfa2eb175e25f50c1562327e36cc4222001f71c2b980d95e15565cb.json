{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError, shareReplay, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"partySelect\"];\nconst _c1 = () => ({\n  width: \"48rem\"\n});\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 34);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 35);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_4_Template, 1, 1, \"i\", 29)(5, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 27);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_10_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"role_code\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵtext(3, \" Role \");\n    i0.ɵɵtemplate(4, SalesCallInvolvedPartiesComponent_ng_template_10_i_4_Template, 1, 1, \"i\", 29)(5, SalesCallInvolvedPartiesComponent_ng_template_10_i_5_Template, 1, 0, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 31);\n    i0.ɵɵelementStart(7, \"th\", 32);\n    i0.ɵɵtext(8, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"role_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"role_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.mobile) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.phone_number) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.email_address) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const partie_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.address) || \"-\", \" \");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 39);\n    i0.ɵɵtemplate(3, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 40)(4, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 40)(5, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 40)(6, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_6_Template, 2, 1, \"ng-container\", 40)(7, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_7_Template, 2, 1, \"ng-container\", 40);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mobile\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"address\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_Template, 8, 6, \"ng-container\", 31);\n    i0.ɵɵelementStart(4, \"td\", 32)(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_ng_template_11_Template_button_click_5_listener($event) {\n      const partie_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(partie_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partie_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (partie_r7 == null ? null : partie_r7.role_code) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41);\n    i0.ɵɵtext(2, \"No involved parties found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41);\n    i0.ɵɵtext(2, \"Loading involved parties data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Involved Parties\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.bp_full_name, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction SalesCallInvolvedPartiesComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallInvolvedPartiesComponent_ng_template_34_span_2_Template, 2, 1, \"span\", 42)(3, SalesCallInvolvedPartiesComponent_ng_template_34_span_3_Template, 2, 1, \"span\", 42)(4, SalesCallInvolvedPartiesComponent_ng_template_34_span_4_Template, 2, 1, \"span\", 42)(5, SalesCallInvolvedPartiesComponent_ng_template_34_span_5_Template, 2, 1, \"span\", 42)(6, SalesCallInvolvedPartiesComponent_ng_template_34_span_6_Template, 2, 1, \"span\", 42);\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email && item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email && !item_r9.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile && (item_r9.email || item_r9.bp_full_name));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile && !item_r9.email && !item_r9.bp_full_name);\n  }\n}\nexport class SalesCallInvolvedPartiesComponent {\n  constructor(activitiesservice, formBuilder, messageservice, confirmationservice) {\n    this.activitiesservice = activitiesservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.involvedpartiesdetails = [];\n    this.activity_id = '';\n    this.addDialogVisible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.saving = false;\n    this.partyDataLoading = false;\n    this.partyInput$ = new Subject();\n    this.InvolvedPartiesForm = this.formBuilder.group({\n      role_code: [''],\n      party_id: ['']\n    });\n    this.role = [{\n      label: 'Account',\n      value: 'FLCU01'\n    }, {\n      label: 'Contact',\n      value: 'BUP001'\n    }, {\n      label: 'Created By',\n      value: 'YC'\n    }, {\n      label: 'Inside Sales Rep',\n      value: 'YI'\n    }, {\n      label: 'Outside Sales Rep',\n      value: 'YO'\n    }];\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'bp_full_name',\n      header: 'Name'\n    }, {\n      field: 'mobile',\n      header: 'Mobile'\n    }, {\n      field: 'phone_number',\n      header: 'Phone'\n    }, {\n      field: 'email_address',\n      header: 'E-Mail'\n    }, {\n      field: 'address',\n      header: 'Address'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.involvedpartiesdetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.loadPartyDataOnRoleChange();\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response.activity_id;\n        const involvedParties = response?.involved_parties || [];\n        const roleMap = {\n          YI: 'Inside Sales Rep',\n          //YC: 'Created By',\n          YO: 'Outside Sales Rep'\n        };\n        const allowedPartnerFunctions = ['YI', 'YO'];\n        this.involvedpartiesdetails = involvedParties.map(party => {\n          const addresses = party?.business_partner?.addresses || [];\n          const address = addresses.find(addr => addr?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT'));\n          const processedAddress = address ? {\n            email_address: address?.emails?.[0]?.email_address || '-',\n            mobile: address?.phone_numbers?.find(item => item.phone_number_type === '3')?.phone_number || '-',\n            phone_number: address?.phone_numbers?.find(item => item.phone_number_type === '1')?.phone_number || '-',\n            address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', ')\n          } : {\n            email_address: '-',\n            mobile: '-',\n            phone_number: '-',\n            address: '-'\n          };\n          let roleMatch = '-';\n          if (party?.role_code === 'FLCU01' || party?.role_code === 'BUP001') {\n            roleMatch = this.role.find(r => r.value === party?.role_code || r.value === party?.bp_role)?.label || '-';\n          } else {\n            const partnerFn = party?.business_partner?.customer?.partner_functions?.find(p => allowedPartnerFunctions.includes(p?.partner_function));\n            if (!partnerFn && party?.role_code === 'BUP003' && !['YI', 'YO'].includes(party?.function_code)) {\n              roleMatch = 'Created By';\n            } else if (partnerFn) {\n              roleMatch = roleMap[partnerFn.partner_function] || '-';\n            }\n          }\n          return {\n            ...party,\n            bp_full_name: party?.business_partner?.bp_full_name || '-',\n            role_code: roleMatch,\n            ...processedAddress\n          };\n        });\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadPartyDataOnRoleChange() {\n    // Use valueChanges to watch for role_code changes\n    this.partyData$ = this.InvolvedPartiesForm.get('role_code').valueChanges.pipe(tap(() => {\n      this.clearPartyData(); // Extracted clearing logic\n    }), switchMap(role => {\n      if (!role) return of([]); // Return empty array if no role selected\n      return this.partyInput$.pipe(distinctUntilChanged(),\n      // Only trigger when input changes\n      debounceTime(300),\n      // Prevent rapid search calls\n      tap(() => this.partyDataLoading = true), switchMap(term => this.getPartnersByRoleAndSearch(role, term)));\n    }), shareReplay(1) // Ensure multiple subscribers get the same data\n    );\n  }\n  // Extracted method to clear party data\n  clearPartyData() {\n    this.InvolvedPartiesForm.get('party_id')?.reset();\n    this.partyInput$.next(''); // Clear the search term\n    if (this.partySelect) {\n      this.partySelect.clearModel(); // Clear the search input\n    }\n  }\n  // Method to get partners based on the role and search term\n  getPartnersByRoleAndSearch(role, term) {\n    const params = this.getParamsByRole(role, term);\n    if (!params) return of([]);\n    return this.activitiesservice.getPartners(params).pipe(map(res => res || []), catchError(error => {\n      console.error('Error fetching partners:', error); // Log error for debugging\n      this.partyDataLoading = false;\n      return of([]); // Return empty array in case of error\n    }), tap(() => this.partyDataLoading = false) // Reset loading flag after fetching\n    );\n  }\n  getParamsByRole(role, term) {\n    if (!role) return null;\n    const filters = {\n      'filters[$or][0][bp_full_name][$containsi]': term,\n      'filters[$or][1][bp_id][$containsi]': term,\n      'filters[$or][2][first_name][$containsi]': term,\n      'filters[$or][3][last_name][$containsi]': term\n    };\n    // Define roleFilters with string keys and object values\n    const roleFilters = {\n      FLCU01: {\n        'filters[roles][bp_role][$eq][0]': 'FLCU01',\n        'filters[roles][bp_role][$eq][1]': 'FLCU00'\n      },\n      BUP001: {\n        'filters[roles][bp_role][$eq]': 'BUP001'\n      },\n      YI: {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'filters[customer][partner_functions][partner_function][$eq]': 'YI'\n      },\n      YO: {\n        'filters[roles][bp_role][$eq]': 'BUP003',\n        'filters[customer][partner_functions][partner_function][$eq]': 'YO'\n      },\n      YC: {\n        'filters[roles][bp_role][$eq]': 'BUP003'\n        //'filters[customer][partner_functions][partner_function][$eq]': 'YC',\n      }\n    };\n    // Use the roleFilters map to get the filters for the specific role\n    const roleSpecificFilters = roleFilters[role];\n    if (roleSpecificFilters) {\n      return {\n        ...roleSpecificFilters,\n        ...filters // Merge common filters\n      };\n    }\n    return null; // Return null if no filters are found for the given role\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.InvolvedPartiesForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.InvolvedPartiesForm.value\n      };\n      let role_code = value?.role_code;\n      if (['YI', 'YO', 'YC'].includes(role_code)) {\n        role_code = 'BUP003';\n      }\n      const data = {\n        activity_id: _this.activity_id,\n        role_code: role_code,\n        party_id: value?.party_id\n      };\n      _this.activitiesservice.createInvolvedParty(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.InvolvedPartiesForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Involved Party Added successFully!'\n          });\n          _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: res => {\n          _this.saving = false;\n          _this.addDialogVisible = true;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteInvolvedParty(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.InvolvedPartiesForm.reset();\n    if (this.role?.length > 0) {\n      this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallInvolvedPartiesComponent_Factory(t) {\n      return new (t || SalesCallInvolvedPartiesComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallInvolvedPartiesComponent,\n      selectors: [[\"app-sales-call-involved-parties\"]],\n      viewQuery: function SalesCallInvolvedPartiesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.partySelect = _t.first);\n        }\n      },\n      decls: 38,\n      vars: 27,\n      consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"party-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"Role\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"role_code\", \"placeholder\", \"Select a Role\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Involved Party\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"party_id\", \"appendTo\", \"body\", 1, \"w-full\", \"h-3rem\", 3, \"search\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\"], [\"ng-option-tmp\", \"\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"font-medium\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"7\", 1, \"border-round-left-lg\"], [4, \"ngIf\"]],\n      template: function SalesCallInvolvedPartiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Involved Parties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_p_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showNewDialog(\"right\"));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallInvolvedPartiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"p-table\", 8, 0);\n          i0.ɵɵlistener(\"onColReorder\", function SalesCallInvolvedPartiesComponent_Template_p_table_onColReorder_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(10, SalesCallInvolvedPartiesComponent_ng_template_10_Template, 9, 3, \"ng-template\", 9)(11, SalesCallInvolvedPartiesComponent_ng_template_11_Template, 6, 2, \"ng-template\", 10)(12, SalesCallInvolvedPartiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11)(13, SalesCallInvolvedPartiesComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"p-dialog\", 13);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallInvolvedPartiesComponent_Template_p_dialog_visibleChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(15, SalesCallInvolvedPartiesComponent_ng_template_15_Template, 2, 0, \"ng-template\", 9);\n          i0.ɵɵelementStart(16, \"form\", 14)(17, \"div\", 15)(18, \"label\", 16)(19, \"span\", 17);\n          i0.ɵɵtext(20, \"supervisor_account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \"Role \");\n          i0.ɵɵelementStart(22, \"span\", 18);\n          i0.ɵɵtext(23, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵelement(25, \"p-dropdown\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 15)(27, \"label\", 21)(28, \"span\", 17);\n          i0.ɵɵtext(29, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \"Involved Party \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 19)(32, \"ng-select\", 22);\n          i0.ɵɵpipe(33, \"async\");\n          i0.ɵɵlistener(\"search\", function SalesCallInvolvedPartiesComponent_Template_ng_select_search_32_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.partyInput$.next($event.term));\n          });\n          i0.ɵɵtemplate(34, SalesCallInvolvedPartiesComponent_ng_template_34_Template, 7, 6, \"ng-template\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 24)(36, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_36_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addDialogVisible = false);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function SalesCallInvolvedPartiesComponent_Template_button_click_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.involvedpartiesdetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c1));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.InvolvedPartiesForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.role);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(33, 24, ctx.partyData$))(\"hideSelected\", true)(\"loading\", ctx.partyDataLoading)(\"minTermLength\", 3)(\"typeahead\", ctx.partyInput$);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Dialog, i11.MultiSelect, i4.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "map", "of", "distinctUntilChanged", "switchMap", "tap", "catchError", "shareReplay", "debounceTime", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_4_Template", "SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "SalesCallInvolvedPartiesComponent_ng_template_10_Template_th_click_1_listener", "_r2", "SalesCallInvolvedPartiesComponent_ng_template_10_i_4_Template", "SalesCallInvolvedPartiesComponent_ng_template_10_i_5_Template", "SalesCallInvolvedPartiesComponent_ng_template_10_ng_container_6_Template", "selectedColumns", "partie_r7", "bp_full_name", "mobile", "phone_number", "email_address", "address", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_3_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_4_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_5_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_6_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_ng_container_7_Template", "col_r8", "SalesCallInvolvedPartiesComponent_ng_template_11_ng_container_3_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_Template_button_click_5_listener", "$event", "_r6", "stopPropagation", "confirmRemove", "role_code", "item_r9", "email", "SalesCallInvolvedPartiesComponent_ng_template_34_span_2_Template", "SalesCallInvolvedPartiesComponent_ng_template_34_span_3_Template", "SalesCallInvolvedPartiesComponent_ng_template_34_span_4_Template", "SalesCallInvolvedPartiesComponent_ng_template_34_span_5_Template", "SalesCallInvolvedPartiesComponent_ng_template_34_span_6_Template", "ɵɵtextInterpolate", "bp_id", "SalesCallInvolvedPartiesComponent", "constructor", "activitiesservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "involvedpartiesdetails", "activity_id", "addDialogVisible", "position", "submitted", "saving", "partyDataLoading", "partyInput$", "InvolvedPartiesForm", "group", "party_id", "role", "label", "value", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "loadPartyDataOnRoleChange", "activity", "pipe", "subscribe", "response", "involvedParties", "involved_parties", "roleMap", "YI", "YO", "allowedPartnerFunctions", "party", "addresses", "business_partner", "find", "addr", "address_usages", "some", "usage", "address_usage", "processedAddress", "emails", "phone_numbers", "item", "phone_number_type", "house_number", "street_name", "city_name", "region", "country", "postal_code", "filter", "Boolean", "join", "roleMatch", "r", "bp_role", "partnerFn", "customer", "partner_functions", "p", "includes", "partner_function", "function_code", "val", "col", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "partyData$", "get", "valueChanges", "clearPartyData", "term", "getPartnersByRoleAndSearch", "reset", "next", "partySelect", "clearModel", "params", "getParamsByRole", "getPartners", "res", "error", "console", "filters", "roleFilters", "FLCU01", "BUP001", "YC", "roleSpecificFilters", "onSubmit", "_this", "_asyncToGenerator", "invalid", "createInvolvedParty", "add", "severity", "detail", "getActivityByID", "confirm", "message", "icon", "accept", "remove", "deleteInvolvedParty", "documentId", "showNewDialog", "length", "setValue", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "FormBuilder", "i3", "MessageService", "ConfirmationService", "selectors", "viewQuery", "SalesCallInvolvedPartiesComponent_Query", "rf", "ctx", "SalesCallInvolvedPartiesComponent_Template_p_button_click_5_listener", "_r1", "ɵɵtwoWayListener", "SalesCallInvolvedPartiesComponent_Template_p_multiSelect_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "SalesCallInvolvedPartiesComponent_Template_p_table_onColReorder_8_listener", "SalesCallInvolvedPartiesComponent_ng_template_10_Template", "SalesCallInvolvedPartiesComponent_ng_template_11_Template", "SalesCallInvolvedPartiesComponent_ng_template_12_Template", "SalesCallInvolvedPartiesComponent_ng_template_13_Template", "SalesCallInvolvedPartiesComponent_Template_p_dialog_visibleChange_14_listener", "SalesCallInvolvedPartiesComponent_ng_template_15_Template", "SalesCallInvolvedPartiesComponent_Template_ng_select_search_32_listener", "SalesCallInvolvedPartiesComponent_ng_template_34_Template", "SalesCallInvolvedPartiesComponent_Template_button_click_36_listener", "SalesCallInvolvedPartiesComponent_Template_button_click_37_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-involved-parties\\sales-call-involved-parties.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-involved-parties\\sales-call-involved-parties.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n  shareReplay,\r\n  debounceTime,\r\n} from 'rxjs/operators';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { NgSelectComponent } from '@ng-select/ng-select';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-call-involved-parties',\r\n  templateUrl: './sales-call-involved-parties.component.html',\r\n  styleUrl: './sales-call-involved-parties.component.scss',\r\n})\r\nexport class SalesCallInvolvedPartiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('partySelect') partySelect!: NgSelectComponent;\r\n  public involvedpartiesdetails: any[] = [];\r\n  public activity_id: string = '';\r\n  public addDialogVisible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public saving = false;\r\n  public partyData$?: Observable<any[]>;\r\n  public partyDataLoading = false;\r\n  public partyInput$ = new Subject<string>();\r\n  public InvolvedPartiesForm: FormGroup = this.formBuilder.group({\r\n    role_code: [''],\r\n    party_id: [''],\r\n  });\r\n\r\n  public role = [\r\n    { label: 'Account', value: 'FLCU01' },\r\n    { label: 'Contact', value: 'BUP001' },\r\n    { label: 'Created By', value: 'YC' },\r\n    { label: 'Inside Sales Rep', value: 'YI' },\r\n    { label: 'Outside Sales Rep', value: 'YO' },\r\n  ];\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'bp_full_name', header: 'Name' },\r\n    { field: 'mobile', header: 'Mobile' },\r\n    { field: 'phone_number', header: 'Phone' },\r\n    { field: 'email_address', header: 'E-Mail' },\r\n    { field: 'address', header: 'Address' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.involvedpartiesdetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadPartyDataOnRoleChange();\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response.activity_id;\r\n          const involvedParties = response?.involved_parties || [];\r\n\r\n          const roleMap: any = {\r\n            YI: 'Inside Sales Rep',\r\n            //YC: 'Created By',\r\n            YO: 'Outside Sales Rep',\r\n          };\r\n\r\n          const allowedPartnerFunctions = ['YI', 'YO'];\r\n\r\n          this.involvedpartiesdetails = involvedParties.map((party: any) => {\r\n            const addresses = party?.business_partner?.addresses || [];\r\n\r\n            const address = addresses.find((addr: any) =>\r\n              addr?.address_usages?.some(\r\n                (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n              )\r\n            );\r\n\r\n            const processedAddress = address\r\n              ? {\r\n                email_address: address?.emails?.[0]?.email_address || '-',\r\n                mobile:\r\n                  address?.phone_numbers?.find(\r\n                    (item: any) => item.phone_number_type === '3'\r\n                  )?.phone_number || '-',\r\n                phone_number:\r\n                  address?.phone_numbers?.find(\r\n                    (item: any) => item.phone_number_type === '1'\r\n                  )?.phone_number || '-',\r\n                address: [\r\n                  address?.house_number,\r\n                  address?.street_name,\r\n                  address?.city_name,\r\n                  address?.region,\r\n                  address?.country,\r\n                  address?.postal_code,\r\n                ]\r\n                  .filter(Boolean)\r\n                  .join(', '),\r\n              }\r\n              : {\r\n                email_address: '-',\r\n                mobile: '-',\r\n                phone_number: '-',\r\n                address: '-',\r\n              };\r\n\r\n            let roleMatch = '-';\r\n\r\n            if (\r\n              party?.role_code === 'FLCU01' ||\r\n              party?.role_code === 'BUP001'\r\n            ) {\r\n              roleMatch =\r\n                this.role.find(\r\n                  (r) =>\r\n                    r.value === party?.role_code || r.value === party?.bp_role\r\n                )?.label || '-';\r\n            } else {\r\n              const partnerFn =\r\n                party?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) =>\r\n                    allowedPartnerFunctions.includes(p?.partner_function)\r\n                );\r\n              if (\r\n                !partnerFn &&\r\n                party?.role_code === 'BUP003' &&\r\n                !['YI', 'YO'].includes(party?.function_code)\r\n              ) {\r\n                roleMatch = 'Created By';\r\n              } else if (partnerFn) {\r\n                roleMatch = roleMap[partnerFn.partner_function] || '-';\r\n              }\r\n            }\r\n\r\n            return {\r\n              ...party,\r\n              bp_full_name: party?.business_partner?.bp_full_name || '-',\r\n              role_code: roleMatch,\r\n              ...processedAddress,\r\n            };\r\n          });\r\n        }\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadPartyDataOnRoleChange(): void {\r\n    // Use valueChanges to watch for role_code changes\r\n    this.partyData$ = this.InvolvedPartiesForm.get(\r\n      'role_code'\r\n    )!.valueChanges.pipe(\r\n      tap(() => {\r\n        this.clearPartyData(); // Extracted clearing logic\r\n      }),\r\n      switchMap((role: string) => {\r\n        if (!role) return of([]); // Return empty array if no role selected\r\n\r\n        return this.partyInput$.pipe(\r\n          distinctUntilChanged(), // Only trigger when input changes\r\n          debounceTime(300), // Prevent rapid search calls\r\n          tap(() => (this.partyDataLoading = true)),\r\n          switchMap((term: string) =>\r\n            this.getPartnersByRoleAndSearch(role, term)\r\n          )\r\n        );\r\n      }),\r\n      shareReplay(1) // Ensure multiple subscribers get the same data\r\n    );\r\n  }\r\n\r\n  // Extracted method to clear party data\r\n  private clearPartyData(): void {\r\n    this.InvolvedPartiesForm.get('party_id')?.reset();\r\n    this.partyInput$.next(''); // Clear the search term\r\n    if (this.partySelect) {\r\n      this.partySelect.clearModel(); // Clear the search input\r\n    }\r\n  }\r\n\r\n  // Method to get partners based on the role and search term\r\n  private getPartnersByRoleAndSearch(role: string, term: string) {\r\n    const params = this.getParamsByRole(role, term);\r\n    if (!params) return of([]);\r\n\r\n    return this.activitiesservice.getPartners(params).pipe(\r\n      map((res: any) => res || []),\r\n      catchError((error) => {\r\n        console.error('Error fetching partners:', error); // Log error for debugging\r\n        this.partyDataLoading = false;\r\n        return of([]); // Return empty array in case of error\r\n      }),\r\n      tap(() => (this.partyDataLoading = false)) // Reset loading flag after fetching\r\n    );\r\n  }\r\n\r\n  private getParamsByRole(role: string, term: string): any | null {\r\n    if (!role) return null;\r\n\r\n    const filters: any = {\r\n      'filters[$or][0][bp_full_name][$containsi]': term,\r\n      'filters[$or][1][bp_id][$containsi]': term,\r\n      'filters[$or][2][first_name][$containsi]': term,\r\n      'filters[$or][3][last_name][$containsi]': term,\r\n    };\r\n\r\n    // Define roleFilters with string keys and object values\r\n    const roleFilters: Record<string, any> = {\r\n      FLCU01: {\r\n        'filters[roles][bp_role][$eq][0]': 'FLCU01',\r\n        'filters[roles][bp_role][$eq][1]': 'FLCU00',\r\n      },\r\n      BUP001: {\r\n        'filters[roles][bp_role][$eq]': 'BUP001',\r\n      },\r\n      YI: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        'filters[customer][partner_functions][partner_function][$eq]': 'YI',\r\n      },\r\n      YO: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        'filters[customer][partner_functions][partner_function][$eq]': 'YO',\r\n      },\r\n      YC: {\r\n        'filters[roles][bp_role][$eq]': 'BUP003',\r\n        //'filters[customer][partner_functions][partner_function][$eq]': 'YC',\r\n      },\r\n    };\r\n\r\n    // Use the roleFilters map to get the filters for the specific role\r\n    const roleSpecificFilters = roleFilters[role];\r\n\r\n    if (roleSpecificFilters) {\r\n      return {\r\n        ...roleSpecificFilters,\r\n        ...filters, // Merge common filters\r\n      };\r\n    }\r\n\r\n    return null; // Return null if no filters are found for the given role\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.InvolvedPartiesForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.InvolvedPartiesForm.value };\r\n\r\n    let role_code = value?.role_code;\r\n    if (['YI', 'YO', 'YC'].includes(role_code)) {\r\n      role_code = 'BUP003';\r\n    }\r\n\r\n    const data = {\r\n      activity_id: this.activity_id,\r\n      role_code: role_code,\r\n      party_id: value?.party_id,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createInvolvedParty(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.InvolvedPartiesForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Involved Party Added successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.addDialogVisible = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteInvolvedParty(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.InvolvedPartiesForm.reset();\r\n    if (this.role?.length > 0) {\r\n      this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Involved Parties</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"involvedpartiesdetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('role_code')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Role\r\n                            <i *ngIf=\"sortField === 'role_code'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'role_code'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-partie let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg font-medium\">\r\n                        {{ partie?.role_code || \"-\" }}\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'bp_full_name'\">\r\n                                    {{ partie?.bp_full_name || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'mobile'\">\r\n                                    {{ partie?.mobile || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'phone_number'\">\r\n                                    {{ partie?.phone_number || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'email_address'\">\r\n                                    {{ partie?.email_address || \"-\" }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'address'\">\r\n                                    {{ partie?.address || \"-\" }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(partie)\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">No involved parties found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">Loading involved parties data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '48rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"party-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Involved Parties</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"InvolvedPartiesForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Role\">\r\n                <span class=\"material-symbols-rounded\">supervisor_account</span>Role\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"role\" formControlName=\"role_code\" placeholder=\"Select a Role\" optionLabel=\"label\"\r\n                    optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Involved Party\">\r\n                <span class=\"material-symbols-rounded\">person</span>Involved Party\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select [items]=\"partyData$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\" [hideSelected]=\"true\"\r\n                    [loading]=\"partyDataLoading\" [minTermLength]=\"3\" [typeahead]=\"partyInput$\"\r\n                    formControlName=\"party_id\" appendTo=\"body\" class=\"w-full h-3rem\"\r\n                    (search)=\"partyInput$.next($event.term)\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        <span *ngIf=\"item.email && item.bp_full_name\">\r\n                            : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.email && !item.bp_full_name\">\r\n                            : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.mobile && (item.email || item.bp_full_name)\">\r\n                            : {{ item.mobile }}</span>\r\n                        <span *ngIf=\"item.mobile && !item.email && !item.bp_full_name\">\r\n                            : {{ item.mobile }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,EAAcC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAG9D,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,YAAY,QACP,gBAAgB;;;;;;;;;;;;;;;;;;;ICeKC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAiE;;;;;IAO7DD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,4EAAA,gBACkF,IAAAC,4EAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAhB7ChB,EADJ,CAAAM,cAAA,SAAI,aACiF;IAA/DN,EAAA,CAAAO,UAAA,mBAAAmB,8EAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IAC/Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,aACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,6DAAA,gBACkF,IAAAC,6DAAA,gBAErB;IAErE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,wEAAA,2BAAkD;IAWlD9B,EAAA,CAAAM,cAAA,aAAkC;IAAAN,EAAA,CAAAiB,MAAA,aAAM;IAC5CjB,EAD4C,CAAAqB,YAAA,EAAK,EAC5C;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,iBAA+B;IAG/BzB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,iBAA+B;IAGbzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAuBpC/B,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAC,YAAA,cACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAAuC;IACnCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAE,MAAA,cACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAG,YAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAI,aAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAAwC;IACpCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAK,OAAA,cACJ;;;;;IArBZrC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAiBjCL,EAhBA,CAAAkB,UAAA,IAAAoB,uFAAA,2BAA6C,IAAAC,uFAAA,2BAIN,IAAAC,uFAAA,2BAIM,IAAAC,uFAAA,2BAIC,IAAAC,uFAAA,2BAIN;;IAKhD1C,EAAA,CAAAqB,YAAA,EAAK;;;;;IAtBarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAyC,MAAA,CAAA3B,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAsB,SAAA,EAAsB;IAAtBtB,EAAA,CAAAE,UAAA,0BAAsB;IAItBF,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,iCAA6B;IAI7BF,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,2BAAuB;;;;;;IAtBlDF,EADJ,CAAAM,cAAA,aAA2B,aACoC;IACvDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAkB,UAAA,IAAA0B,wEAAA,2BAAkD;IA2B9C5C,EADJ,CAAAM,cAAA,aAAkC,iBAEgC;IAA1DN,EAAA,CAAAO,UAAA,mBAAAsC,kFAAAC,MAAA;MAAA,MAAAd,SAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAAqC,GAAA,EAAAnC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAASiC,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAAhD,EAAA,CAAAc,WAAA,CAAEX,MAAA,CAAA8C,aAAA,CAAAjB,SAAA,CAAqB;IAAA,EAAC;IAErEhC,EAFsE,CAAAqB,YAAA,EAAS,EACtE,EACJ;;;;;IAhCGrB,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,SAAA,kBAAAA,SAAA,CAAAkB,SAAA,cACJ;IAC8BlD,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAmChD/B,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,iCAA0B;IAC3EjB,EAD2E,CAAAqB,YAAA,EAAK,EAC3E;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,kDAA2C;IAC5FjB,EAD4F,CAAAqB,YAAA,EAAK,EAC5F;;;;;IAQbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,uBAAgB;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IA0BTrB,EAAA,CAAAM,cAAA,WAAgC;IAACN,EAAA,CAAAiB,MAAA,GAAyB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAhCrB,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAuB,kBAAA,QAAA4B,OAAA,CAAAlB,YAAA,KAAyB;;;;;IAC1DjC,EAAA,CAAAM,cAAA,WAA8C;IAC1CN,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAzBrB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAuB,kBAAA,QAAA4B,OAAA,CAAAC,KAAA,KAAkB;;;;;IACtBpD,EAAA,CAAAM,cAAA,WAA+C;IAC3CN,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAzBrB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAuB,kBAAA,QAAA4B,OAAA,CAAAC,KAAA,KAAkB;;;;;IACtBpD,EAAA,CAAAM,cAAA,WAA+D;IAC3DN,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAA1BrB,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAuB,kBAAA,QAAA4B,OAAA,CAAAjB,MAAA,KAAmB;;;;;IACvBlC,EAAA,CAAAM,cAAA,WAA+D;IAC3DN,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAA1BrB,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAuB,kBAAA,QAAA4B,OAAA,CAAAjB,MAAA,KAAmB;;;;;IATvBlC,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAiB,MAAA,GAAgB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAQ7BrB,EAPA,CAAAkB,UAAA,IAAAmC,gEAAA,mBAAgC,IAAAC,gEAAA,mBACc,IAAAC,gEAAA,mBAEC,IAAAC,gEAAA,mBAEgB,IAAAC,gEAAA,mBAEA;;;;IARzDzD,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAA0D,iBAAA,CAAAP,OAAA,CAAAQ,KAAA,CAAgB;IACf3D,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,SAAAiD,OAAA,CAAAlB,YAAA,CAAuB;IACvBjC,EAAA,CAAAsB,SAAA,EAAqC;IAArCtB,EAAA,CAAAE,UAAA,SAAAiD,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAlB,YAAA,CAAqC;IAErCjC,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,SAAAiD,OAAA,CAAAC,KAAA,KAAAD,OAAA,CAAAlB,YAAA,CAAsC;IAEtCjC,EAAA,CAAAsB,SAAA,EAAsD;IAAtDtB,EAAA,CAAAE,UAAA,SAAAiD,OAAA,CAAAjB,MAAA,KAAAiB,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAlB,YAAA,EAAsD;IAEtDjC,EAAA,CAAAsB,SAAA,EAAsD;IAAtDtB,EAAA,CAAAE,UAAA,SAAAiD,OAAA,CAAAjB,MAAA,KAAAiB,OAAA,CAAAC,KAAA,KAAAD,OAAA,CAAAlB,YAAA,CAAsD;;;AD7GrF,OAAM,MAAO2B,iCAAiC;EAyB5CC,YACUC,iBAAoC,EACpCC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA5BrB,KAAAC,YAAY,GAAG,IAAI5E,OAAO,EAAQ;IAEnC,KAAA6E,sBAAsB,GAAU,EAAE;IAClC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,IAAIpF,OAAO,EAAU;IACnC,KAAAqF,mBAAmB,GAAc,IAAI,CAACZ,WAAW,CAACa,KAAK,CAAC;MAC7D1B,SAAS,EAAE,CAAC,EAAE,CAAC;MACf2B,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;IAEK,KAAAC,IAAI,GAAG,CACZ;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACrC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACrC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAI,CAAE,EACpC;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC1C;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC5C;IASO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAElE,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACzC;MAAER,KAAK,EAAE,QAAQ;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrC;MAAER,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAC1C;MAAER,KAAK,EAAE,eAAe;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EAC5C;MAAER,KAAK,EAAE,SAAS;MAAEQ,MAAM,EAAE;IAAS,CAAE,CACxC;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAbjB;EAeJW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC+D,sBAAsB,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACxC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEpE,KAAK,CAAC;MAC9C,MAAMwE,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAErE,KAAK,CAAC;MAE9C,IAAIyE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACpF,SAAS,GAAGqF,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE3E,KAAa;IACvC,IAAI,CAAC2E,IAAI,IAAI,CAAC3E,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC4E,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC3E,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACpC,iBAAiB,CAACqC,QAAQ,CAC5BC,IAAI,CAAC7G,SAAS,CAAC,IAAI,CAAC2E,YAAY,CAAC,CAAC,CAClCmC,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAClC,WAAW,GAAGkC,QAAQ,CAAClC,WAAW;QACvC,MAAMmC,eAAe,GAAGD,QAAQ,EAAEE,gBAAgB,IAAI,EAAE;QAExD,MAAMC,OAAO,GAAQ;UACnBC,EAAE,EAAE,kBAAkB;UACtB;UACAC,EAAE,EAAE;SACL;QAED,MAAMC,uBAAuB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;QAE5C,IAAI,CAACzC,sBAAsB,GAAGoC,eAAe,CAAC/G,GAAG,CAAEqH,KAAU,IAAI;UAC/D,MAAMC,SAAS,GAAGD,KAAK,EAAEE,gBAAgB,EAAED,SAAS,IAAI,EAAE;UAE1D,MAAMzE,OAAO,GAAGyE,SAAS,CAACE,IAAI,CAAEC,IAAS,IACvCA,IAAI,EAAEC,cAAc,EAAEC,IAAI,CACvBC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF;UAED,MAAMC,gBAAgB,GAAGjF,OAAO,GAC5B;YACAD,aAAa,EAAEC,OAAO,EAAEkF,MAAM,GAAG,CAAC,CAAC,EAAEnF,aAAa,IAAI,GAAG;YACzDF,MAAM,EACJG,OAAO,EAAEmF,aAAa,EAAER,IAAI,CACzBS,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAK,GAAG,CAC9C,EAAEvF,YAAY,IAAI,GAAG;YACxBA,YAAY,EACVE,OAAO,EAAEmF,aAAa,EAAER,IAAI,CACzBS,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAK,GAAG,CAC9C,EAAEvF,YAAY,IAAI,GAAG;YACxBE,OAAO,EAAE,CACPA,OAAO,EAAEsF,YAAY,EACrBtF,OAAO,EAAEuF,WAAW,EACpBvF,OAAO,EAAEwF,SAAS,EAClBxF,OAAO,EAAEyF,MAAM,EACfzF,OAAO,EAAE0F,OAAO,EAChB1F,OAAO,EAAE2F,WAAW,CACrB,CACEC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI;WACb,GACC;YACA/F,aAAa,EAAE,GAAG;YAClBF,MAAM,EAAE,GAAG;YACXC,YAAY,EAAE,GAAG;YACjBE,OAAO,EAAE;WACV;UAEH,IAAI+F,SAAS,GAAG,GAAG;UAEnB,IACEvB,KAAK,EAAE3D,SAAS,KAAK,QAAQ,IAC7B2D,KAAK,EAAE3D,SAAS,KAAK,QAAQ,EAC7B;YACAkF,SAAS,GACP,IAAI,CAACtD,IAAI,CAACkC,IAAI,CACXqB,CAAC,IACAA,CAAC,CAACrD,KAAK,KAAK6B,KAAK,EAAE3D,SAAS,IAAImF,CAAC,CAACrD,KAAK,KAAK6B,KAAK,EAAEyB,OAAO,CAC7D,EAAEvD,KAAK,IAAI,GAAG;UACnB,CAAC,MAAM;YACL,MAAMwD,SAAS,GACb1B,KAAK,EAAEE,gBAAgB,EAAEyB,QAAQ,EAAEC,iBAAiB,EAAEzB,IAAI,CACvD0B,CAAM,IACL9B,uBAAuB,CAAC+B,QAAQ,CAACD,CAAC,EAAEE,gBAAgB,CAAC,CACxD;YACH,IACE,CAACL,SAAS,IACV1B,KAAK,EAAE3D,SAAS,KAAK,QAAQ,IAC7B,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAACyF,QAAQ,CAAC9B,KAAK,EAAEgC,aAAa,CAAC,EAC5C;cACAT,SAAS,GAAG,YAAY;YAC1B,CAAC,MAAM,IAAIG,SAAS,EAAE;cACpBH,SAAS,GAAG3B,OAAO,CAAC8B,SAAS,CAACK,gBAAgB,CAAC,IAAI,GAAG;YACxD;UACF;UAEA,OAAO;YACL,GAAG/B,KAAK;YACR5E,YAAY,EAAE4E,KAAK,EAAEE,gBAAgB,EAAE9E,YAAY,IAAI,GAAG;YAC1DiB,SAAS,EAAEkF,SAAS;YACpB,GAAGd;WACJ;QACH,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEJ,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAInD,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACkD,gBAAgB;EAC9B;EAEA,IAAIlD,eAAeA,CAAC+G,GAAU;IAC5B,IAAI,CAAC7D,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC+C,MAAM,CAACc,GAAG,IAAID,GAAG,CAACH,QAAQ,CAACI,GAAG,CAAC,CAAC;EACpE;EAEAC,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACjE,gBAAgB,CAACgE,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAAClE,gBAAgB,CAACmE,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAAClE,gBAAgB,CAACmE,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAhD,yBAAyBA,CAAA;IACvB;IACA,IAAI,CAACoD,UAAU,GAAG,IAAI,CAAC3E,mBAAmB,CAAC4E,GAAG,CAC5C,WAAW,CACX,CAACC,YAAY,CAACpD,IAAI,CAClBxG,GAAG,CAAC,MAAK;MACP,IAAI,CAAC6J,cAAc,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC,EACF9J,SAAS,CAAEmF,IAAY,IAAI;MACzB,IAAI,CAACA,IAAI,EAAE,OAAOrF,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAE1B,OAAO,IAAI,CAACiF,WAAW,CAAC0B,IAAI,CAC1B1G,oBAAoB,EAAE;MAAE;MACxBK,YAAY,CAAC,GAAG,CAAC;MAAE;MACnBH,GAAG,CAAC,MAAO,IAAI,CAAC6E,gBAAgB,GAAG,IAAK,CAAC,EACzC9E,SAAS,CAAE+J,IAAY,IACrB,IAAI,CAACC,0BAA0B,CAAC7E,IAAI,EAAE4E,IAAI,CAAC,CAC5C,CACF;IACH,CAAC,CAAC,EACF5J,WAAW,CAAC,CAAC,CAAC,CAAC;KAChB;EACH;EAEA;EACQ2J,cAAcA,CAAA;IACpB,IAAI,CAAC9E,mBAAmB,CAAC4E,GAAG,CAAC,UAAU,CAAC,EAAEK,KAAK,EAAE;IACjD,IAAI,CAAClF,WAAW,CAACmF,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACC,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACC,UAAU,EAAE,CAAC,CAAC;IACjC;EACF;EAEA;EACQJ,0BAA0BA,CAAC7E,IAAY,EAAE4E,IAAY;IAC3D,MAAMM,MAAM,GAAG,IAAI,CAACC,eAAe,CAACnF,IAAI,EAAE4E,IAAI,CAAC;IAC/C,IAAI,CAACM,MAAM,EAAE,OAAOvK,EAAE,CAAC,EAAE,CAAC;IAE1B,OAAO,IAAI,CAACqE,iBAAiB,CAACoG,WAAW,CAACF,MAAM,CAAC,CAAC5D,IAAI,CACpD5G,GAAG,CAAE2K,GAAQ,IAAKA,GAAG,IAAI,EAAE,CAAC,EAC5BtK,UAAU,CAAEuK,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC,CAAC,CAAC;MAClD,IAAI,CAAC3F,gBAAgB,GAAG,KAAK;MAC7B,OAAOhF,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,EACFG,GAAG,CAAC,MAAO,IAAI,CAAC6E,gBAAgB,GAAG,KAAM,CAAC,CAAC;KAC5C;EACH;EAEQwF,eAAeA,CAACnF,IAAY,EAAE4E,IAAY;IAChD,IAAI,CAAC5E,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMwF,OAAO,GAAQ;MACnB,2CAA2C,EAAEZ,IAAI;MACjD,oCAAoC,EAAEA,IAAI;MAC1C,yCAAyC,EAAEA,IAAI;MAC/C,wCAAwC,EAAEA;KAC3C;IAED;IACA,MAAMa,WAAW,GAAwB;MACvCC,MAAM,EAAE;QACN,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE;OACpC;MACDC,MAAM,EAAE;QACN,8BAA8B,EAAE;OACjC;MACD/D,EAAE,EAAE;QACF,8BAA8B,EAAE,QAAQ;QACxC,6DAA6D,EAAE;OAChE;MACDC,EAAE,EAAE;QACF,8BAA8B,EAAE,QAAQ;QACxC,6DAA6D,EAAE;OAChE;MACD+D,EAAE,EAAE;QACF,8BAA8B,EAAE;QAChC;;KAEH;IAED;IACA,MAAMC,mBAAmB,GAAGJ,WAAW,CAACzF,IAAI,CAAC;IAE7C,IAAI6F,mBAAmB,EAAE;MACvB,OAAO;QACL,GAAGA,mBAAmB;QACtB,GAAGL,OAAO,CAAE;OACb;IACH;IAEA,OAAO,IAAI,CAAC,CAAC;EACf;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACtG,SAAS,GAAG,IAAI;MAErB,IAAIsG,KAAI,CAAClG,mBAAmB,CAACoG,OAAO,EAAE;QACpC;MACF;MAEAF,KAAI,CAACrG,MAAM,GAAG,IAAI;MAClB,MAAMQ,KAAK,GAAG;QAAE,GAAG6F,KAAI,CAAClG,mBAAmB,CAACK;MAAK,CAAE;MAEnD,IAAI9B,SAAS,GAAG8B,KAAK,EAAE9B,SAAS;MAChC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACyF,QAAQ,CAACzF,SAAS,CAAC,EAAE;QAC1CA,SAAS,GAAG,QAAQ;MACtB;MAEA,MAAMyC,IAAI,GAAG;QACXvB,WAAW,EAAEyG,KAAI,CAACzG,WAAW;QAC7BlB,SAAS,EAAEA,SAAS;QACpB2B,QAAQ,EAAEG,KAAK,EAAEH;OAClB;MAEDgG,KAAI,CAAC/G,iBAAiB,CACnBkH,mBAAmB,CAACrF,IAAI,CAAC,CACzBS,IAAI,CAAC7G,SAAS,CAACsL,KAAI,CAAC3G,YAAY,CAAC,CAAC,CAClCmC,SAAS,CAAC;QACTwD,IAAI,EAAGvD,QAAa,IAAI;UACtBuE,KAAI,CAACrG,MAAM,GAAG,KAAK;UACnBqG,KAAI,CAACxG,gBAAgB,GAAG,KAAK;UAC7BwG,KAAI,CAAClG,mBAAmB,CAACiF,KAAK,EAAE;UAChCiB,KAAI,CAAC7G,cAAc,CAACiH,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFN,KAAI,CAAC/G,iBAAiB,CACnBsH,eAAe,CAACP,KAAI,CAACzG,WAAW,CAAC,CACjCgC,IAAI,CAAC7G,SAAS,CAACsL,KAAI,CAAC3G,YAAY,CAAC,CAAC,CAClCmC,SAAS,EAAE;QAChB,CAAC;QACD+D,KAAK,EAAGD,GAAQ,IAAI;UAClBU,KAAI,CAACrG,MAAM,GAAG,KAAK;UACnBqG,KAAI,CAACxG,gBAAgB,GAAG,IAAI;UAC5BwG,KAAI,CAAC7G,cAAc,CAACiH,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAlI,aAAaA,CAACwE,IAAS;IACrB,IAAI,CAACxD,mBAAmB,CAACoH,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChE9J,MAAM,EAAE,SAAS;MACjB+J,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAChE,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAgE,MAAMA,CAAChE,IAAS;IACd,IAAI,CAAC3D,iBAAiB,CACnB4H,mBAAmB,CAACjE,IAAI,CAACkE,UAAU,CAAC,CACpCvF,IAAI,CAAC7G,SAAS,CAAC,IAAI,CAAC2E,YAAY,CAAC,CAAC,CAClCmC,SAAS,CAAC;MACTwD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7F,cAAc,CAACiH,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACrH,iBAAiB,CACnBsH,eAAe,CAAC,IAAI,CAAChH,WAAW,CAAC,CACjCgC,IAAI,CAAC7G,SAAS,CAAC,IAAI,CAAC2E,YAAY,CAAC,CAAC,CAClCmC,SAAS,EAAE;MAChB,CAAC;MACD+D,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACpG,cAAc,CAACiH,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAS,aAAaA,CAACtH,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,mBAAmB,CAACiF,KAAK,EAAE;IAChC,IAAI,IAAI,CAAC9E,IAAI,EAAE+G,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAClH,mBAAmB,CAAC4E,GAAG,CAAC,WAAW,CAAC,EAAEuC,QAAQ,CAAC,IAAI,CAAChH,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;IACzE;EACF;EAEA+G,WAAWA,CAAA;IACT,IAAI,CAAC7H,YAAY,CAAC2F,IAAI,EAAE;IACxB,IAAI,CAAC3F,YAAY,CAAC8H,QAAQ,EAAE;EAC9B;;;uBA9XWpI,iCAAiC,EAAA5D,EAAA,CAAAiM,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAnM,EAAA,CAAAiM,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArM,EAAA,CAAAiM,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvM,EAAA,CAAAiM,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAjC5I,iCAAiC;MAAA6I,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCtBtC5M,EAHR,CAAAM,cAAA,aAA2D,aAEyC,YAC7C;UAAAN,EAAA,CAAAiB,MAAA,uBAAgB;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAGhErB,EADJ,CAAAM,cAAA,aAA2C,kBAEoC;UADrDN,EAAA,CAAAO,UAAA,mBAAAuM,qEAAA;YAAA9M,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA,OAAA/M,EAAA,CAAAc,WAAA,CAAS+L,GAAA,CAAAjB,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAAtD5L,EAAA,CAAAqB,YAAA,EAC2E;UAE3ErB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAAgN,gBAAA,2BAAAC,kFAAAnK,MAAA;YAAA9C,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA/M,EAAA,CAAAkN,kBAAA,CAAAL,GAAA,CAAA9K,eAAA,EAAAe,MAAA,MAAA+J,GAAA,CAAA9K,eAAA,GAAAe,MAAA;YAAA,OAAA9C,EAAA,CAAAc,WAAA,CAAAgC,MAAA;UAAA,EAA6B;UAKrE9C,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,oBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAA4M,2EAAArK,MAAA;YAAA9C,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA,OAAA/M,EAAA,CAAAc,WAAA,CAAgB+L,GAAA,CAAA7D,eAAA,CAAAlG,MAAA,CAAuB;UAAA,EAAC;UAuExC9C,EArEA,CAAAkB,UAAA,KAAAkM,yDAAA,yBAAgC,KAAAC,yDAAA,0BA0B+B,KAAAC,yDAAA,0BAsCzB,KAAAC,yDAAA,0BAKD;UAOjDvN,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAM,cAAA,oBAC4C;UADnBN,EAAA,CAAAgN,gBAAA,2BAAAQ,8EAAA1K,MAAA;YAAA9C,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA/M,EAAA,CAAAkN,kBAAA,CAAAL,GAAA,CAAAxI,gBAAA,EAAAvB,MAAA,MAAA+J,GAAA,CAAAxI,gBAAA,GAAAvB,MAAA;YAAA,OAAA9C,EAAA,CAAAc,WAAA,CAAAgC,MAAA;UAAA,EAA8B;UAEnD9C,EAAA,CAAAkB,UAAA,KAAAuM,yDAAA,yBAAgC;UAOpBzN,EAHZ,CAAAM,cAAA,gBAAgF,eACvB,iBAC2C,gBACjD;UAAAN,EAAA,CAAAiB,MAAA,0BAAkB;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,aAChE;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,eAAwC;UACpCN,EAAA,CAAAC,SAAA,sBAEa;UAErBD,EADI,CAAAqB,YAAA,EAAM,EACJ;UAGErB,EAFR,CAAAM,cAAA,eAAqD,iBACqD,gBAC3D;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,uBACxD;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UAEJrB,EADJ,CAAAM,cAAA,eAAwC,qBAIS;;UAAzCN,EAAA,CAAAO,UAAA,oBAAAmN,wEAAA5K,MAAA;YAAA9C,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA,OAAA/M,EAAA,CAAAc,WAAA,CAAU+L,GAAA,CAAAnI,WAAA,CAAAmF,IAAA,CAAA/G,MAAA,CAAA4G,IAAA,CAA6B;UAAA,EAAC;UACxC1J,EAAA,CAAAkB,UAAA,KAAAyM,yDAAA,0BAA2C;UAcvD3N,EAFQ,CAAAqB,YAAA,EAAY,EACV,EACJ;UAEFrB,EADJ,CAAAM,cAAA,eAAoD,kBAGT;UAAnCN,EAAA,CAAAO,UAAA,mBAAAqN,oEAAA;YAAA5N,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA,OAAA/M,EAAA,CAAAc,WAAA,CAAA+L,GAAA,CAAAxI,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAACrE,EAAA,CAAAqB,YAAA,EAAS;UAChDrB,EAAA,CAAAM,cAAA,kBACyB;UAArBN,EAAA,CAAAO,UAAA,mBAAAsN,oEAAA;YAAA7N,EAAA,CAAAU,aAAA,CAAAqM,GAAA;YAAA,OAAA/M,EAAA,CAAAc,WAAA,CAAS+L,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAGpC5K,EAHqC,CAAAqB,YAAA,EAAS,EAChC,EACH,EACA;;;UA7IqBrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzDF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAA2M,GAAA,CAAA3H,IAAA,CAAgB;UAAClF,EAAA,CAAA8N,gBAAA,YAAAjB,GAAA,CAAA9K,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMpIF,EAAA,CAAAsB,SAAA,GAAgC;UAC6BtB,EAD7D,CAAAE,UAAA,UAAA2M,GAAA,CAAA1I,sBAAA,CAAgC,YAAyB,mBAAmB,cAAc,oBACvD,4BAAqD;UAgFtDnE,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAA+N,UAAA,CAAA/N,EAAA,CAAAgO,eAAA,KAAAC,GAAA,EAA4B;UAA1EjO,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAA8N,gBAAA,YAAAjB,GAAA,CAAAxI,gBAAA,CAA8B;UACnDrE,EADiF,CAAAE,UAAA,qBAAoB,oBAClF;UAKbF,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAE,UAAA,cAAA2M,GAAA,CAAAlI,mBAAA,CAAiC;UAOf3E,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAA2M,GAAA,CAAA/H,IAAA,CAAgB;UAUjB9E,EAAA,CAAAsB,SAAA,GAA4B;UACctB,EAD1C,CAAAE,UAAA,UAAAF,EAAA,CAAAkO,WAAA,SAAArB,GAAA,CAAAvD,UAAA,EAA4B,sBAAiE,YAAAuD,GAAA,CAAApI,gBAAA,CACxE,oBAAoB,cAAAoI,GAAA,CAAAnI,WAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}