{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MenuService {\n  constructor() {\n    this.menuSource = new Subject();\n    this.resetSource = new Subject();\n    this.menuSource$ = this.menuSource.asObservable();\n    this.resetSource$ = this.resetSource.asObservable();\n    this.activeMenuSubject = new BehaviorSubject('');\n    this.activeMenu$ = this.activeMenuSubject.asObservable();\n  }\n  onMenuStateChange(event) {\n    this.menuSource.next(event);\n  }\n  reset() {\n    this.resetSource.next(true);\n  }\n  setActiveMenu(menuName) {\n    this.activeMenuSubject.next(menuName);\n  }\n  getActiveMenu() {\n    return this.activeMenuSubject.value;\n  }\n  static {\n    this.ɵfac = function MenuService_Factory(t) {\n      return new (t || MenuService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MenuService,\n      factory: MenuService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Subject", "MenuService", "constructor", "menuSource", "resetSource", "menuSource$", "asObservable", "resetSource$", "activeMenuSubject", "activeMenu$", "onMenuStateChange", "event", "next", "reset", "setActiveMenu", "menuName", "getActiveMenu", "value", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\app.menu.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Subject } from 'rxjs';\r\nimport { MenuChangeEvent } from './api/menuchangeevent';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class MenuService {\r\n\r\n    private menuSource = new Subject<MenuChangeEvent>();\r\n    private resetSource = new Subject();\r\n\r\n    menuSource$ = this.menuSource.asObservable();\r\n    resetSource$ = this.resetSource.asObservable();\r\n\r\n    onMenuStateChange(event: MenuChangeEvent) {\r\n        this.menuSource.next(event);\r\n    }\r\n\r\n    reset() {\r\n        this.resetSource.next(true);\r\n    }\r\n\r\n    private activeMenuSubject = new BehaviorSubject<string>('');\r\n    activeMenu$ = this.activeMenuSubject.asObservable();\r\n\r\n    setActiveMenu(menuName: string): void {\r\n        this.activeMenuSubject.next(menuName);\r\n    }\r\n\r\n    getActiveMenu(): string {\r\n        return this.activeMenuSubject.value;\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAEC,OAAO,QAAQ,MAAM;;AAM/C,OAAM,MAAOC,WAAW;EAHxBC,YAAA;IAKY,KAAAC,UAAU,GAAG,IAAIH,OAAO,EAAmB;IAC3C,KAAAI,WAAW,GAAG,IAAIJ,OAAO,EAAE;IAEnC,KAAAK,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;IAUtC,KAAAE,iBAAiB,GAAG,IAAIT,eAAe,CAAS,EAAE,CAAC;IAC3D,KAAAU,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACF,YAAY,EAAE;;EATnDI,iBAAiBA,CAACC,KAAsB;IACpC,IAAI,CAACR,UAAU,CAACS,IAAI,CAACD,KAAK,CAAC;EAC/B;EAEAE,KAAKA,CAAA;IACD,IAAI,CAACT,WAAW,CAACQ,IAAI,CAAC,IAAI,CAAC;EAC/B;EAKAE,aAAaA,CAACC,QAAgB;IAC1B,IAAI,CAACP,iBAAiB,CAACI,IAAI,CAACG,QAAQ,CAAC;EACzC;EAEAC,aAAaA,CAAA;IACT,OAAO,IAAI,CAACR,iBAAiB,CAACS,KAAK;EACvC;;;uBAzBShB,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAiB,OAAA,EAAXjB,WAAW,CAAAkB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}