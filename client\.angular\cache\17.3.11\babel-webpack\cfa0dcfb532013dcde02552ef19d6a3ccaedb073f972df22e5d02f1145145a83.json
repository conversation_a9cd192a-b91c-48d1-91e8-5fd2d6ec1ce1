{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AddOpportunitieComponent {\n  static {\n    this.ɵfac = function AddOpportunitieComponent_Factory(t) {\n      return new (t || AddOpportunitieComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddOpportunitieComponent,\n      selectors: [[\"app-add-opportunitie\"]],\n      decls: 2,\n      vars: 0,\n      template: function AddOpportunitieComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"add-opportunitie works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AddOpportunitieComponent", "selectors", "decls", "vars", "template", "AddOpportunitieComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\add-opportunitie\\add-opportunitie.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\add-opportunitie\\add-opportunitie.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-add-opportunitie',\r\n  templateUrl: './add-opportunitie.component.html',\r\n  styleUrl: './add-opportunitie.component.scss'\r\n})\r\nexport class AddOpportunitieComponent {\r\n\r\n}\r\n", "<p>add-opportunitie works!</p>\r\n"], "mappings": ";AAOA,OAAM,MAAOA,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPrCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}