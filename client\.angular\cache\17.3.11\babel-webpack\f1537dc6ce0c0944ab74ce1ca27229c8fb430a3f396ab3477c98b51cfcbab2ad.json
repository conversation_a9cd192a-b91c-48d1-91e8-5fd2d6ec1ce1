{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccountComponent } from './account.component';\nimport { AccountDetailsComponent } from './account-details/account-details.component';\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\nimport { AccountSalesQuoteDetailsComponent } from './account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesOrderDetailsComponent } from './account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { AccountInvoicesComponent } from './account-details/account-invoices/account-invoices.component';\nimport { AccountReturnsComponent } from './account-details/account-returns/account-returns.component';\nimport { ReturnOrderDetailsComponent } from './account-details/account-returns/return-order-details/return-order-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AccountComponent\n}, {\n  path: ':id',\n  component: AccountDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: AccountOverviewComponent\n  }, {\n    path: 'contacts',\n    component: AccountContactsComponent\n  }, {\n    path: 'sales-team',\n    component: AccountSalesTeamComponent\n  }, {\n    path: 'opportunities',\n    component: AccountOpportunitiesComponent\n  }, {\n    path: 'ai-insights',\n    component: AccountAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: AccountOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: AccountAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: AccountNotesComponent\n  }, {\n    path: 'activities',\n    component: AccountActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: AccountRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: AccountTicketsComponent\n  }, {\n    path: 'sales-quotes',\n    component: AccountSalesQuotesComponent\n  }, {\n    path: 'sales-quotes/:id',\n    component: AccountSalesQuoteDetailsComponent\n  }, {\n    path: 'sales-orders',\n    component: AccountSalesOrdersComponent\n  }, {\n    path: 'sales-orders/:id',\n    component: AccountSalesOrderDetailsComponent\n  }, {\n    path: 'invoices',\n    component: AccountInvoicesComponent\n  }, {\n    path: 'returns',\n    component: AccountReturnsComponent\n  }, {\n    path: \"return-order/:returnOrderId/:refDocId\",\n    component: ReturnOrderDetailsComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class AccountRoutingModule {\n  static {\n    this.ɵfac = function AccountRoutingModule_Factory(t) {\n      return new (t || AccountRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccountRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccountComponent", "AccountDetailsComponent", "AccountOverviewComponent", "AccountContactsComponent", "AccountSalesTeamComponent", "AccountAiInsightsComponent", "AccountOrganizationDataComponent", "AccountAttachmentsComponent", "AccountNotesComponent", "AccountOpportunitiesComponent", "AccountActivitiesComponent", "AccountRelationshipsComponent", "AccountTicketsComponent", "AccountSalesQuotesComponent", "AccountSalesOrdersComponent", "AccountSalesQuoteDetailsComponent", "AccountSalesOrderDetailsComponent", "AccountInvoicesComponent", "AccountReturnsComponent", "ReturnOrderDetailsComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "AccountRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AccountComponent } from './account.component';\r\nimport { AccountDetailsComponent } from './account-details/account-details.component';\r\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\r\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\r\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\r\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\r\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\r\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\r\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\r\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\r\nimport { AccountSalesQuoteDetailsComponent } from './account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\r\nimport { AccountSalesOrderDetailsComponent } from './account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\r\nimport { AccountInvoicesComponent } from './account-details/account-invoices/account-invoices.component';\r\nimport { AccountReturnsComponent } from './account-details/account-returns/account-returns.component';\r\nimport { ReturnOrderDetailsComponent } from './account-details/account-returns/return-order-details/return-order-details.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: AccountComponent },\r\n  {\r\n    path: ':id',\r\n    component: AccountDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: AccountOverviewComponent },\r\n      { path: 'contacts', component: AccountContactsComponent },\r\n      { path: 'sales-team', component: AccountSalesTeamComponent },\r\n      { path: 'opportunities', component: AccountOpportunitiesComponent },\r\n      { path: 'ai-insights', component: AccountAiInsightsComponent },\r\n      {\r\n        path: 'organization-data',\r\n        component: AccountOrganizationDataComponent,\r\n      },\r\n      { path: 'attachments', component: AccountAttachmentsComponent },\r\n      { path: 'notes', component: AccountNotesComponent },\r\n      { path: 'activities', component: AccountActivitiesComponent },\r\n      { path: 'relationships', component: AccountRelationshipsComponent },\r\n      { path: 'tickets', component: AccountTicketsComponent },\r\n      { path: 'sales-quotes', component: AccountSalesQuotesComponent },\r\n      {\r\n        path: 'sales-quotes/:id',\r\n        component: AccountSalesQuoteDetailsComponent,\r\n      },\r\n      { path: 'sales-orders', component: AccountSalesOrdersComponent },\r\n      {\r\n        path: 'sales-orders/:id',\r\n        component: AccountSalesOrderDetailsComponent,\r\n      },\r\n      {\r\n        path: 'invoices',\r\n        component: AccountInvoicesComponent,\r\n      },\r\n      {\r\n        path: 'returns',\r\n        component: AccountReturnsComponent,\r\n      },\r\n      {\r\n        path: \"return-order/:returnOrderId/:refDocId\",\r\n        component: ReturnOrderDetailsComponent\r\n      },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AccountRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,gCAAgC,QAAQ,iFAAiF;AAClI,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,iCAAiC,QAAQ,0GAA0G;AAC5J,SAASC,iCAAiC,QAAQ,0GAA0G;AAC5J,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,2BAA2B,QAAQ,uFAAuF;;;AAEnI,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEtB;AAAgB,CAAE,EACzC;EACEqB,IAAI,EAAE,KAAK;EACXC,SAAS,EAAErB,uBAAuB;EAClCsB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEpB;EAAwB,CAAE,EACzD;IAAEmB,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEnB;EAAwB,CAAE,EACzD;IAAEkB,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAElB;EAAyB,CAAE,EAC5D;IAAEiB,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEb;EAA6B,CAAE,EACnE;IAAEY,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEjB;EAA0B,CAAE,EAC9D;IACEgB,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEhB;GACZ,EACD;IAAEe,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEf;EAA2B,CAAE,EAC/D;IAAEc,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEd;EAAqB,CAAE,EACnD;IAAEa,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEZ;EAA0B,CAAE,EAC7D;IAAEW,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEX;EAA6B,CAAE,EACnE;IAAEU,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEV;EAAuB,CAAE,EACvD;IAAES,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAET;EAA2B,CAAE,EAChE;IACEQ,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEP;GACZ,EACD;IAAEM,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAER;EAA2B,CAAE,EAChE;IACEO,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEN;GACZ,EACD;IACEK,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEL;GACZ,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEJ;GACZ,EACD;IACEG,IAAI,EAAE,uCAAuC;IAC7CC,SAAS,EAAEH;GACZ,EACD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrB3B,YAAY,CAAC4B,QAAQ,CAACP,MAAM,CAAC,EAC7BrB,YAAY;IAAA;EAAA;;;2EAEX2B,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAA9B,YAAA;IAAA+B,OAAA,GAFrB/B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}