{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { BadgeModule } from 'primeng/badge';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { RippleModule } from 'primeng/ripple';\nimport { AppConfigModule } from './config/app.config.module';\nimport { AppLayoutComponent } from './app.layout.component';\nimport { AppBreadcrumbComponent } from './app.breadcrumb.component';\nimport { AppSidebarComponent } from './app.sidebar.component';\nimport { AppTopbarComponent } from './app.topbar.component';\nimport { AppProfileSidebarComponent } from './app.profilesidebar.component';\nimport { AppMenuComponent } from './app.menu.component';\nimport { AppMenuitemComponent } from './app.menuitem.component';\nimport { RouterModule } from '@angular/router';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nexport class AppLayoutModule {\n  static {\n    this.ɵfac = function AppLayoutModule_Factory(t) {\n      return new (t || AppLayoutModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppLayoutModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, ButtonModule, TooltipModule, RippleModule, RouterModule, AppConfigModule, StyleClassModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppLayoutModule, {\n    declarations: [AppLayoutComponent, AppBreadcrumbComponent, AppSidebarComponent, AppTopbarComponent, AppProfileSidebarComponent, AppMenuComponent, AppMenuitemComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, ButtonModule, TooltipModule, RippleModule, RouterModule, AppConfigModule, StyleClassModule, CalendarModule]\n  });\n})();\ni0.ɵɵsetComponentScope(AppMenuComponent, [i1.NgForOf, i1.NgIf, AppMenuitemComponent], []);", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "HttpClientModule", "BrowserAnimationsModule", "InputTextModule", "SidebarModule", "BadgeModule", "RadioButtonModule", "InputSwitchModule", "TooltipModule", "RippleModule", "AppConfigModule", "AppLayoutComponent", "AppBreadcrumbComponent", "AppSidebarComponent", "AppTopbarComponent", "AppProfileSidebarComponent", "AppMenuComponent", "AppMenuitemComponent", "RouterModule", "ButtonModule", "StyleClassModule", "CalendarModule", "AppLayoutModule", "declarations", "imports", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\app.layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { AppConfigModule } from './config/app.config.module';\r\nimport { AppLayoutComponent } from './app.layout.component';\r\nimport { AppBreadcrumbComponent } from './app.breadcrumb.component';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AppTopbarComponent } from './app.topbar.component';\r\nimport { AppProfileSidebarComponent } from './app.profilesidebar.component';\r\nimport { AppMenuComponent } from './app.menu.component';\r\nimport { AppMenuitemComponent } from './app.menuitem.component';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { CalendarModule } from 'primeng/calendar';\r\n\r\n@NgModule({\r\n    declarations: [\r\n        AppLayoutComponent,\r\n        AppBreadcrumbComponent,\r\n        AppSidebarComponent,\r\n        AppTopbarComponent,\r\n        AppProfileSidebarComponent,\r\n        AppMenuComponent,\r\n        AppMenuitemComponent\r\n    ],\r\n    imports: [\r\n        BrowserModule,\r\n        FormsModule,\r\n        HttpClientModule,\r\n        BrowserAnimationsModule,\r\n        InputTextModule,\r\n        SidebarModule,\r\n        BadgeModule,\r\n        RadioButtonModule,\r\n        InputSwitchModule,\r\n        ButtonModule,\r\n        TooltipModule,\r\n        RippleModule,\r\n        RouterModule,\r\n        AppConfigModule,\r\n        StyleClassModule,\r\n        CalendarModule,\r\n        \r\n    ]\r\n})\r\nexport class AppLayoutModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;;;AAgCjD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAnBpBvB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,eAAe,EACfC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EACjBY,YAAY,EACZX,aAAa,EACbC,YAAY,EACZS,YAAY,EACZR,eAAe,EACfU,gBAAgB,EAChBC,cAAc;IAAA;EAAA;;;2EAITC,eAAe;IAAAC,YAAA,GA5BpBZ,kBAAkB,EAClBC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,oBAAoB;IAAAO,OAAA,GAGpBzB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,eAAe,EACfC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EACjBY,YAAY,EACZX,aAAa,EACbC,YAAY,EACZS,YAAY,EACZR,eAAe,EACfU,gBAAgB,EAChBC,cAAc;EAAA;AAAA;uBAnBdL,gBAAgB,GAAAS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAChBV,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}