{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ProfileRoutingModule } from './profile-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {\n    static {\n      this.ɵfac = function ProfileModule_Factory(t) {\n        return new (t || ProfileModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ProfileModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, ReactiveFormsModule, ProfileRoutingModule, FormsModule]\n      });\n    }\n  }\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}