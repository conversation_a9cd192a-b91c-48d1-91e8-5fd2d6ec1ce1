{"ast": null, "code": "export const findEntryByCode = (source, code) => {\n  if (code && source != null) {\n    const codex = source.findIndex(c => {\n      return c.isoCode === code;\n    });\n    return codex !== -1 ? source[codex] : undefined;\n  }\n  return undefined;\n};\nexport const findStateByCodeAndCountryCode = (source, code, countryCode) => {\n  if (code && countryCode && source != null) {\n    const codex = source.findIndex(c => {\n      return c.isoCode === code && c.countryCode === countryCode;\n    });\n    return codex !== -1 ? source[codex] : undefined;\n  }\n  return undefined;\n};\nexport function defaultKeyToCompare(entity) {\n  return entity.name;\n}\nexport const compare = (a, b,\n// eslint-disable-next-line no-unused-vars\nkeyToCompare = defaultKeyToCompare) => {\n  if (keyToCompare(a) < keyToCompare(b)) return -1;\n  if (keyToCompare(a) > keyToCompare(b)) return 1;\n  return 0;\n};\nexport const convertArrayToObject = (keys, arr) => {\n  const result = arr.map(subArr => {\n    return Object.fromEntries(keys.map((key, index) => [key, subArr[index]]));\n  });\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}