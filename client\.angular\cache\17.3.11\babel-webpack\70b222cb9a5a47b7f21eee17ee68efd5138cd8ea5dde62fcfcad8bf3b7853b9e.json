{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ActivitiesRoutingModule } from './activities-routing.module';\nimport { ActivitiesComponent } from './activities.component';\nimport { ActivitiesDetailsComponent } from './activities-details/activities-details.component';\nimport { ActivitiesOverviewComponent } from './activities-details/activities-overview/activities-overview.component';\nimport { ActivitiesAiInsightsComponent } from './activities-details/activities-ai-insights/activities-ai-insights.component';\nimport { ActivitiesAttachmentsComponent } from './activities-details/activities-attachments/activities-attachments.component';\nimport { ActivitiesContactsComponent } from './activities-details/activities-contacts/activities-contacts.component';\nimport { ActivitiesNotesComponent } from './activities-details/activities-notes/activities-notes.component';\nimport { ActivitiesOrganizationDataComponent } from './activities-details/activities-organization-data/activities-organization-data.component';\nimport { ActivitiesSalesTeamComponent } from './activities-details/activities-sales-team/activities-sales-team.component';\nimport { FormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport class ActivitiesModule {\n  static {\n    this.ɵfac = function ActivitiesModule_Factory(t) {\n      return new (t || ActivitiesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ActivitiesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ActivitiesRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ActivitiesModule, {\n    declarations: [ActivitiesComponent, ActivitiesDetailsComponent, ActivitiesOverviewComponent, ActivitiesContactsComponent, ActivitiesSalesTeamComponent, ActivitiesAiInsightsComponent, ActivitiesOrganizationDataComponent, ActivitiesAttachmentsComponent, ActivitiesNotesComponent],\n    imports: [CommonModule, ActivitiesRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ActivitiesRoutingModule", "ActivitiesComponent", "ActivitiesDetailsComponent", "ActivitiesOverviewComponent", "ActivitiesAiInsightsComponent", "ActivitiesAttachmentsComponent", "ActivitiesContactsComponent", "ActivitiesNotesComponent", "ActivitiesOrganizationDataComponent", "ActivitiesSalesTeamComponent", "FormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "CalendarModule", "DropdownModule", "InputTextModule", "TableModule", "TabViewModule", "ActivitiesModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ActivitiesRoutingModule } from './activities-routing.module';\r\nimport { ActivitiesComponent } from './activities.component';\r\nimport { ActivitiesDetailsComponent } from './activities-details/activities-details.component';\r\nimport { ActivitiesOverviewComponent } from './activities-details/activities-overview/activities-overview.component';\r\nimport { ActivitiesAiInsightsComponent } from './activities-details/activities-ai-insights/activities-ai-insights.component';\r\nimport { ActivitiesAttachmentsComponent } from './activities-details/activities-attachments/activities-attachments.component';\r\nimport { ActivitiesContactsComponent } from './activities-details/activities-contacts/activities-contacts.component';\r\nimport { ActivitiesNotesComponent } from './activities-details/activities-notes/activities-notes.component';\r\nimport { ActivitiesOrganizationDataComponent } from './activities-details/activities-organization-data/activities-organization-data.component';\r\nimport { ActivitiesSalesTeamComponent } from './activities-details/activities-sales-team/activities-sales-team.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ActivitiesComponent,\r\n    ActivitiesDetailsComponent,\r\n    ActivitiesOverviewComponent,\r\n    ActivitiesContactsComponent,\r\n    ActivitiesSalesTeamComponent,\r\n    ActivitiesAiInsightsComponent,\r\n    ActivitiesOrganizationDataComponent,\r\n    ActivitiesAttachmentsComponent,\r\n    ActivitiesNotesComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ActivitiesRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule\r\n  ]\r\n})\r\nexport class ActivitiesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,6BAA6B,QAAQ,8EAA8E;AAC5H,SAASC,8BAA8B,QAAQ,8EAA8E;AAC7H,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,wBAAwB,QAAQ,kEAAkE;AAC3G,SAASC,mCAAmC,QAAQ,0FAA0F;AAC9I,SAASC,4BAA4B,QAAQ,4EAA4E;AACzH,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;;AA6B/C,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAbzBpB,YAAY,EACZC,uBAAuB,EACvBU,WAAW,EACXO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdG,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;IAAA;EAAA;;;2EAGNG,gBAAgB;IAAAC,YAAA,GAxBzBnB,mBAAmB,EACnBC,0BAA0B,EAC1BC,2BAA2B,EAC3BG,2BAA2B,EAC3BG,4BAA4B,EAC5BL,6BAA6B,EAC7BI,mCAAmC,EACnCH,8BAA8B,EAC9BE,wBAAwB;IAAAc,OAAA,GAGxBtB,YAAY,EACZC,uBAAuB,EACvBU,WAAW,EACXO,WAAW,EACXJ,YAAY,EACZE,cAAc,EACdG,aAAa,EACbP,kBAAkB,EAClBC,gBAAgB,EAChBE,cAAc,EACdE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}