{"ast": null, "code": "import { environment } from '../../environments/environment';\nexport const ApiConstant = {\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\n  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\n  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\n  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\n  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\n  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\n  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\n  USERS: `${environment.apiEndpoint}/users`,\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\n  PARTNERS: `${environment.apiEndpoint}/api/business-partners`,\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\n  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\n  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\n  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\n  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\n  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\n  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\n  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\n  IMAGES: `${environment.apiEndpoint}/media`,\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\n  SALES_ORDER: `${environment.apiEndpoint}/api/sales-orders`,\n  SALES_ORDER_GENERIC: `${environment.apiEndpoint}/api/sales-orders/generic`,\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\n  CARTS: `${environment.apiEndpoint}/carts`,\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\n  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\n  SETTINGS: `${environment.apiEndpoint}/settings`,\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\n  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\n  TICKETS: `${environment.apiEndpoint}/tickets`,\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\n  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\n  SALES_QUOTE_GENERIC: `${environment.apiEndpoint}/api/sales-quotes/generic`,\n  QUOTE: `${environment.apiEndpoint}/quote-statuses`,\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\n  RETURN_ORDER: `${environment.apiEndpoint}/api/return-orders`,\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\n  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  BANNER: `${environment.apiEndpoint}/banner`,\n  BUSINESS_PARTNER: `${environment.apiEndpoint}/api/business-partners`\n};\nexport const CMS_APIContstant = {\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\n  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\n  TICKET: `${environment.cmsApiEndpoint}/api/tickets`,\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\n  PARTNERS_CONTACTS: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\n  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\n  CONFIG_DATA: `${environment.cmsApiEndpoint}/api/configurations`,\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\n  ACCOUNT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\n  PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner`,\n  REGISTER_PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner/registration`,\n  PROSPECT_ADDRESS_REGISTER: `${environment.cmsApiEndpoint}/api/business-partner-address/registration`,\n  PROSPECT_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-address`,\n  CREATE_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/registration`,\n  EXISTING_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/mapping`,\n  PROSPECT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact`,\n  CRM_NOTE: `${environment.cmsApiEndpoint}/api/crm-notes`,\n  CRM_ACTIVITY: `${environment.cmsApiEndpoint}/api/crm-activities`,\n  CRM_ACTIVITY_PHONE_CALL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-activity/registration/phone-call`,\n  CRM_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-item/registration`,\n  CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/activity-registration`,\n  CRM_INVOLVED_PARTIES: `${environment.cmsApiEndpoint}/api/crm-involved-parties`,\n  CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-items`,\n  CRM_OPPORTUNITY_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity/registration`,\n  CRM_OPPORTUNITY_FOLLOWUP: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-documents`,\n  CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/opportunity-registration`,\n  CRM_OPPORTUNITY_CONTACT: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-parties`,\n  CRM_OPPORTUNITY_CONTACT_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-party/registration`,\n  CRM_OPPORTUNITY: `${environment.cmsApiEndpoint}/api/crm-opportunities`,\n  CRM_OPPORTUNITY_SALES_TEAM: `${environment.cmsApiEndpoint}/api/crm-opportunity-sales-team-parties`,\n  CRM_COMPETITORS: `${environment.cmsApiEndpoint}/api/crm-competitors`,\n  REGISTER_PROSPECT_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function/registration`,\n  SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function`,\n  BP_EXTENSIONS: `${environment.cmsApiEndpoint}/api/business-partner-extensions`,\n  DELETE_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\n  MARKETING_ATTRIBUTES: `${environment.cmsApiEndpoint}/api/bp-marketing-attributes`,\n  CONTENT_CRM: `${environment.cmsApiEndpoint}/api/content-crms`\n};\nexport const AppConstant = {\n  SESSION_TIMEOUT: 3600 * 1000,\n  // in MS\n  PRODUCT_IMAGE_FALLBACK: 'assets/layout/images/demo-product.jpeg'\n};\nexport const Permission = {\n  VIEW_INVOICE: 'P0003'\n};", "map": {"version": 3, "names": ["environment", "ApiConstant", "FETCH_TOKEN", "apiEndpoint", "ORDER_HISTORY", "GET_ALL_PRODUCTS", "GET_PRODUCT_IMAGES", "PRODUCT_IMAGE", "PRODUCT_PLANT", "PRODUCT_DESCRIPTION", "USERS", "ADMIN_USERS", "SINGOUT", "PARTNERS", "CUSTOMER_COMPANIES", "CUSTOMER_TEXT", "CUSTOMER_PARTNER_FUNCTION", "CUSTOMER_SALES_AREA", "GET_ORDER_STATUSES", "GET_INVOICE_FORM_TYPES", "RELATIONSHIP_TYPES", "CONDITIONS", "GET_CATALOGS", "PRODUCT_CATEGORIES", "PRODUCT_CATALOGS", "GET_INVOICE_STATUSES", "GET_SALE_ORDER_STATUSES", "GET_INVOICE_TYPES", "GET_ORDER_TYPES", "ORDER_DETAILS", "SCHEDULED_ORDER_DETAILS", "INVOICE", "IMAGES", "SALES_ORDER_SIMULATION", "SALES_ORDER_CREATION", "SALES_ORDER", "SALES_ORDER_GENERIC", "ADD_SHIPPING_ADDRESS", "CARTS", "GET_MATERIAL_STOCK", "GET_SALES_PRICE", "CUSTOMERS", "SETTINGS", "COMPANY_LOGO", "SIMILAR_PRODUCTS", "TICKET_LIST", "TICKETS", "TICKET_STATUSES", "SALES_QUOTE_CREATION", "SALES_QUOTE", "SALES_QUOTE_GENERIC", "QUOTE", "RETURN_STATUS", "RETURN_REASON", "REFUND_PROGRESS", "RETURN_ORDER", "NOTIFICATION", "PRODUCT_REGISTER", "BULK_UPLOAD_STATUS", "CUSTOMER_TEXT_DESCR", "SALES_ORDER_SCHEDULER_CREATION", "SALES_ORDER_SCHEDULER", "BANNER", "BUSINESS_PARTNER", "CMS_APIContstant", "USER_ROLES", "cmsApiEndpoint", "STORE_DESIGN", "TICKET", "MAIN_MENU_API_DETAILS", "PARTNERS_ADDRESS", "PARTNERS_CONTACTS", "SINGIN", "USER_DETAILS", "USER_CART", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "USER_PERMISSIONS", "GET_CATEGORIES", "CONFIG_DATA", "PRODUCT_MDEIA", "ACCOUNT_CONTACT", "PROSPECTS", "REGISTER_PROSPECTS", "PROSPECT_ADDRESS_REGISTER", "PROSPECT_ADDRESS", "CREATE_CONTACT", "EXISTING_CONTACT", "PROSPECT_CONTACT", "CRM_NOTE", "CRM_ACTIVITY", "CRM_ACTIVITY_PHONE_CALL_REGISTRATION", "CRM_ACTIVITY_FOLLOW_UP_REGISTRATION", "CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION", "CRM_INVOLVED_PARTIES", "CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS", "CRM_OPPORTUNITY_REGISTRATION", "CRM_OPPORTUNITY_FOLLOWUP", "CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION", "CRM_OPPORTUNITY_CONTACT", "CRM_OPPORTUNITY_CONTACT_REGISTRATION", "CRM_OPPORTUNITY", "CRM_OPPORTUNITY_SALES_TEAM", "CRM_COMPETITORS", "REGISTER_PROSPECT_SALES_TEAM", "SALES_TEAM", "BP_EXTENSIONS", "DELETE_SALES_TEAM", "MARKETING_ATTRIBUTES", "CONTENT_CRM", "AppConstant", "SESSION_TIMEOUT", "PRODUCT_IMAGE_FALLBACK", "Permission", "VIEW_INVOICE"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\constants\\api.constants.ts"], "sourcesContent": ["import { environment } from '../../environments/environment';\r\n\r\nexport const ApiConstant = {\r\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\r\n  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\r\n  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\r\n  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\r\n  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\r\n  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\r\n  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\r\n  USERS: `${environment.apiEndpoint}/users`,\r\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\r\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\r\n  PARTNERS: `${environment.apiEndpoint}/api/business-partners`,\r\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\r\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\r\n  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\r\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\r\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\r\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\r\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\r\n  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\r\n  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\r\n  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\r\n  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\r\n  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\r\n  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\r\n  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\r\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\r\n  IMAGES: `${environment.apiEndpoint}/media`,\r\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\r\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\r\n  SALES_ORDER: `${environment.apiEndpoint}/api/sales-orders`,\r\n  SALES_ORDER_GENERIC: `${environment.apiEndpoint}/api/sales-orders/generic`,\r\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\r\n  CARTS: `${environment.apiEndpoint}/carts`,\r\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\r\n  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\r\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\r\n  SETTINGS: `${environment.apiEndpoint}/settings`,\r\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\r\n  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\r\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\r\n  TICKETS: `${environment.apiEndpoint}/tickets`,\r\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\r\n  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\r\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\r\n  SALES_QUOTE_GENERIC: `${environment.apiEndpoint}/api/sales-quotes/generic`,\r\n  QUOTE: `${environment.apiEndpoint}/quote-statuses`,\r\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\r\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\r\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\r\n  RETURN_ORDER: `${environment.apiEndpoint}/api/return-orders`,\r\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\r\n  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\r\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\r\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\r\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  BANNER: `${environment.apiEndpoint}/banner`,\r\n  BUSINESS_PARTNER: `${environment.apiEndpoint}/api/business-partners`,\r\n};\r\n\r\nexport const CMS_APIContstant = {\r\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\r\n  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\r\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\r\n  TICKET: `${environment.cmsApiEndpoint}/api/tickets`,\r\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\r\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\r\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\r\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\r\n  PARTNERS_CONTACTS: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\r\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\r\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\r\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\r\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\r\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\r\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\r\n  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\r\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\r\n  CONFIG_DATA: `${environment.cmsApiEndpoint}/api/configurations`,\r\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\r\n  ACCOUNT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\r\n  PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner`,\r\n  REGISTER_PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner/registration`,\r\n  PROSPECT_ADDRESS_REGISTER: `${environment.cmsApiEndpoint}/api/business-partner-address/registration`,\r\n  PROSPECT_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-address`,\r\n  CREATE_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/registration`,\r\n  EXISTING_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/mapping`,\r\n  PROSPECT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact`,\r\n  CRM_NOTE: `${environment.cmsApiEndpoint}/api/crm-notes`,\r\n  CRM_ACTIVITY: `${environment.cmsApiEndpoint}/api/crm-activities`,\r\n  CRM_ACTIVITY_PHONE_CALL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-activity/registration/phone-call`,\r\n  CRM_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-item/registration`,\r\n  CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/activity-registration`,\r\n  CRM_INVOLVED_PARTIES: `${environment.cmsApiEndpoint}/api/crm-involved-parties`,\r\n  CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-items`,\r\n  CRM_OPPORTUNITY_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity/registration`,\r\n  CRM_OPPORTUNITY_FOLLOWUP: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-documents`,\r\n  CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/opportunity-registration`,\r\n  CRM_OPPORTUNITY_CONTACT:`${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-parties`,\r\n  CRM_OPPORTUNITY_CONTACT_REGISTRATION:`${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-party/registration`,\r\n  CRM_OPPORTUNITY: `${environment.cmsApiEndpoint}/api/crm-opportunities`,\r\n  CRM_OPPORTUNITY_SALES_TEAM: `${environment.cmsApiEndpoint}/api/crm-opportunity-sales-team-parties`,\r\n  CRM_COMPETITORS: `${environment.cmsApiEndpoint}/api/crm-competitors`,\r\n  REGISTER_PROSPECT_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function/registration`,\r\n  SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function`,\r\n  BP_EXTENSIONS: `${environment.cmsApiEndpoint}/api/business-partner-extensions`,\r\n  DELETE_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\r\n  MARKETING_ATTRIBUTES: `${environment.cmsApiEndpoint}/api/bp-marketing-attributes`,\r\n  CONTENT_CRM: `${environment.cmsApiEndpoint}/api/content-crms`,\r\n};\r\n\r\nexport const AppConstant = {\r\n  SESSION_TIMEOUT: 3600 * 1000, // in MS\r\n  PRODUCT_IMAGE_FALLBACK: 'assets/layout/images/demo-product.jpeg',\r\n};\r\n\r\nexport const Permission = {\r\n  VIEW_INVOICE: 'P0003',\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gCAAgC;AAE5D,OAAO,MAAMC,WAAW,GAAG;EACzBC,WAAW,EAAE,GAAGF,WAAW,CAACG,WAAW,iBAAiB;EACxDC,aAAa,EAAE,GAAGJ,WAAW,CAACG,WAAW,SAAS;EAClDE,gBAAgB,EAAE,GAAGL,WAAW,CAACG,WAAW,WAAW;EACvDG,kBAAkB,EAAE,GAAGN,WAAW,CAACG,WAAW,sBAAsB;EACpEI,aAAa,EAAE,GAAGP,WAAW,CAACG,WAAW,QAAQ;EACjDK,aAAa,EAAE,GAAGR,WAAW,CAACG,WAAW,iBAAiB;EAC1DM,mBAAmB,EAAE,GAAGT,WAAW,CAACG,WAAW,uBAAuB;EACtEO,KAAK,EAAE,GAAGV,WAAW,CAACG,WAAW,QAAQ;EACzCQ,WAAW,EAAE,GAAGX,WAAW,CAACG,WAAW,kBAAkB;EACzDS,OAAO,EAAE,GAAGZ,WAAW,CAACG,WAAW,cAAc;EACjDU,QAAQ,EAAE,GAAGb,WAAW,CAACG,WAAW,wBAAwB;EAC5DW,kBAAkB,EAAE,GAAGd,WAAW,CAACG,WAAW,qBAAqB;EACnEY,aAAa,EAAE,GAAGf,WAAW,CAACG,WAAW,gBAAgB;EACzDa,yBAAyB,EAAE,GAAGhB,WAAW,CAACG,WAAW,6BAA6B;EAClFc,mBAAmB,EAAE,GAAGjB,WAAW,CAACG,WAAW,uBAAuB;EACtEe,kBAAkB,EAAE,GAAGlB,WAAW,CAACG,WAAW,sBAAsB;EACpEgB,sBAAsB,EAAE,GAAGnB,WAAW,CAACG,WAAW,qBAAqB;EACvEiB,kBAAkB,EAAE,GAAGpB,WAAW,CAACG,WAAW,qBAAqB;EACnEkB,UAAU,EAAE,GAAGrB,WAAW,CAACG,WAAW,aAAa;EACnDmB,YAAY,EAAE,GAAGtB,WAAW,CAACG,WAAW,mBAAmB;EAC3DoB,kBAAkB,EAAE,GAAGvB,WAAW,CAACG,WAAW,mCAAmC;EACjFqB,gBAAgB,EAAE,GAAGxB,WAAW,CAACG,WAAW,iCAAiC;EAC7EsB,oBAAoB,EAAE,GAAGzB,WAAW,CAACG,WAAW,mBAAmB;EACnEuB,uBAAuB,EAAE,GAAG1B,WAAW,CAACG,WAAW,sBAAsB;EACzEwB,iBAAiB,EAAE,GAAG3B,WAAW,CAACG,WAAW,gBAAgB;EAC7DyB,eAAe,EAAE,GAAG5B,WAAW,CAACG,WAAW,cAAc;EACzD0B,aAAa,EAAE,GAAG7B,WAAW,CAACG,WAAW,SAAS;EAClD2B,uBAAuB,EAAE,GAAG9B,WAAW,CAACG,WAAW,yBAAyB;EAC5E4B,OAAO,EAAE,GAAG/B,WAAW,CAACG,WAAW,eAAe;EAClD6B,MAAM,EAAE,GAAGhC,WAAW,CAACG,WAAW,QAAQ;EAC1C8B,sBAAsB,EAAE,GAAGjC,WAAW,CAACG,WAAW,yBAAyB;EAC3E+B,oBAAoB,EAAE,GAAGlC,WAAW,CAACG,WAAW,uBAAuB;EACvEgC,WAAW,EAAE,GAAGnC,WAAW,CAACG,WAAW,mBAAmB;EAC1DiC,mBAAmB,EAAE,GAAGpC,WAAW,CAACG,WAAW,2BAA2B;EAC1EkC,oBAAoB,EAAE,GAAGrC,WAAW,CAACG,WAAW,mCAAmC;EACnFmC,KAAK,EAAE,GAAGtC,WAAW,CAACG,WAAW,QAAQ;EACzCoC,kBAAkB,EAAE,GAAGvC,WAAW,CAACG,WAAW,+BAA+B;EAC7EqC,eAAe,EAAE,GAAGxC,WAAW,CAACG,WAAW,cAAc;EACzDsC,SAAS,EAAE,GAAGzC,WAAW,CAACG,WAAW,YAAY;EACjDuC,QAAQ,EAAE,GAAG1C,WAAW,CAACG,WAAW,WAAW;EAC/CwC,YAAY,EAAE,GAAG3C,WAAW,CAACG,WAAW,eAAe;EACvDyC,gBAAgB,EAAE,GAAG5C,WAAW,CAACG,WAAW,mBAAmB;EAC/D0C,WAAW,EAAE,GAAG7C,WAAW,CAACG,WAAW,eAAe;EACtD2C,OAAO,EAAE,GAAG9C,WAAW,CAACG,WAAW,UAAU;EAC7C4C,eAAe,EAAE,GAAG/C,WAAW,CAACG,WAAW,kBAAkB;EAC7D6C,oBAAoB,EAAE,GAAGhD,WAAW,CAACG,WAAW,qBAAqB;EACrE8C,WAAW,EAAE,GAAGjD,WAAW,CAACG,WAAW,mBAAmB;EAC1D+C,mBAAmB,EAAE,GAAGlD,WAAW,CAACG,WAAW,2BAA2B;EAC1EgD,KAAK,EAAE,GAAGnD,WAAW,CAACG,WAAW,iBAAiB;EAClDiD,aAAa,EAAE,GAAGpD,WAAW,CAACG,WAAW,kBAAkB;EAC3DkD,aAAa,EAAE,GAAGrD,WAAW,CAACG,WAAW,gBAAgB;EACzDmD,eAAe,EAAE,GAAGtD,WAAW,CAACG,WAAW,kBAAkB;EAC7DoD,YAAY,EAAE,GAAGvD,WAAW,CAACG,WAAW,oBAAoB;EAC5DqD,YAAY,EAAE,GAAGxD,WAAW,CAACG,WAAW,eAAe;EACvDsD,gBAAgB,EAAE,GAAGzD,WAAW,CAACG,WAAW,mBAAmB;EAC/DuD,kBAAkB,EAAE,GAAG1D,WAAW,CAACG,WAAW,sBAAsB;EACpEwD,mBAAmB,EAAE,GAAG3D,WAAW,CAACG,WAAW,4BAA4B;EAC3EyD,8BAA8B,EAAE,GAAG5D,WAAW,CAACG,WAAW,yBAAyB;EACnF0D,qBAAqB,EAAE,GAAG7D,WAAW,CAACG,WAAW,yBAAyB;EAC1E2D,MAAM,EAAE,GAAG9D,WAAW,CAACG,WAAW,SAAS;EAC3C4D,gBAAgB,EAAE,GAAG/D,WAAW,CAACG,WAAW;CAC7C;AAED,OAAO,MAAM6D,gBAAgB,GAAG;EAC9BC,UAAU,EAAE,GAAGjE,WAAW,CAACkE,cAAc,8BAA8B;EACvEC,YAAY,EAAE,GAAGnE,WAAW,CAACkE,cAAc,4BAA4B;EACvElD,yBAAyB,EAAE,GAAGhB,WAAW,CAACkE,cAAc,iCAAiC;EACzFE,MAAM,EAAE,GAAGpE,WAAW,CAACkE,cAAc,cAAc;EACnDxB,QAAQ,EAAE,GAAG1C,WAAW,CAACkE,cAAc,eAAe;EACtDG,qBAAqB,EAAE,GAAGrE,WAAW,CAACkE,cAAc,oBAAoB;EACxErD,QAAQ,EAAE,GAAGb,WAAW,CAACkE,cAAc,wBAAwB;EAC/DI,gBAAgB,EAAE,GAAGtE,WAAW,CAACkE,cAAc,iCAAiC;EAChFK,iBAAiB,EAAE,GAAGvE,WAAW,CAACkE,cAAc,gCAAgC;EAChFM,MAAM,EAAE,GAAGxE,WAAW,CAACkE,cAAc,iBAAiB;EACtDO,YAAY,EAAE,GAAGzE,WAAW,CAACkE,cAAc,YAAY;EACvDQ,SAAS,EAAE,GAAG1E,WAAW,CAACkE,cAAc,YAAY;EACpDS,sBAAsB,EAAE,GAAG3E,WAAW,CAACkE,cAAc,2BAA2B;EAChFU,cAAc,EAAE,GAAG5E,WAAW,CAACkE,cAAc,0BAA0B;EACvEzB,SAAS,EAAE,GAAGzC,WAAW,CAACkE,cAAc,gBAAgB;EACxDW,gBAAgB,EAAE,GAAG7E,WAAW,CAACkE,cAAc,6BAA6B;EAC5EY,cAAc,EAAE,GAAG9E,WAAW,CAACkE,cAAc,yBAAyB;EACtEa,WAAW,EAAE,GAAG/E,WAAW,CAACkE,cAAc,qBAAqB;EAC/Dc,aAAa,EAAE,GAAGhF,WAAW,CAACkE,cAAc,qBAAqB;EACjEe,eAAe,EAAE,GAAGjF,WAAW,CAACkE,cAAc,gCAAgC;EAC9EgB,SAAS,EAAE,GAAGlF,WAAW,CAACkE,cAAc,uBAAuB;EAC/DiB,kBAAkB,EAAE,GAAGnF,WAAW,CAACkE,cAAc,oCAAoC;EACrFkB,yBAAyB,EAAE,GAAGpF,WAAW,CAACkE,cAAc,4CAA4C;EACpGmB,gBAAgB,EAAE,GAAGrF,WAAW,CAACkE,cAAc,+BAA+B;EAC9EoB,cAAc,EAAE,GAAGtF,WAAW,CAACkE,cAAc,4CAA4C;EACzFqB,gBAAgB,EAAE,GAAGvF,WAAW,CAACkE,cAAc,uCAAuC;EACtFsB,gBAAgB,EAAE,GAAGxF,WAAW,CAACkE,cAAc,+BAA+B;EAC9EuB,QAAQ,EAAE,GAAGzF,WAAW,CAACkE,cAAc,gBAAgB;EACvDwB,YAAY,EAAE,GAAG1F,WAAW,CAACkE,cAAc,qBAAqB;EAChEyB,oCAAoC,EAAE,GAAG3F,WAAW,CAACkE,cAAc,2CAA2C;EAC9G0B,mCAAmC,EAAE,GAAG5F,WAAW,CAACkE,cAAc,kDAAkD;EACpH2B,+CAA+C,EAAE,GAAG7F,WAAW,CAACkE,cAAc,6EAA6E;EAC3J4B,oBAAoB,EAAE,GAAG9F,WAAW,CAACkE,cAAc,2BAA2B;EAC9E6B,mCAAmC,EAAE,GAAG/F,WAAW,CAACkE,cAAc,sCAAsC;EACxG8B,4BAA4B,EAAE,GAAGhG,WAAW,CAACkE,cAAc,mCAAmC;EAC9F+B,wBAAwB,EAAE,GAAGjG,WAAW,CAACkE,cAAc,wDAAwD;EAC/GgC,sCAAsC,EAAE,GAAGlG,WAAW,CAACkE,cAAc,gFAAgF;EACrJiC,uBAAuB,EAAC,GAAGnG,WAAW,CAACkE,cAAc,4CAA4C;EACjGkC,oCAAoC,EAAC,GAAGpG,WAAW,CAACkE,cAAc,uDAAuD;EACzHmC,eAAe,EAAE,GAAGrG,WAAW,CAACkE,cAAc,wBAAwB;EACtEoC,0BAA0B,EAAE,GAAGtG,WAAW,CAACkE,cAAc,yCAAyC;EAClGqC,eAAe,EAAE,GAAGvG,WAAW,CAACkE,cAAc,sBAAsB;EACpEsC,4BAA4B,EAAE,GAAGxG,WAAW,CAACkE,cAAc,6CAA6C;EACxGuC,UAAU,EAAE,GAAGzG,WAAW,CAACkE,cAAc,gCAAgC;EACzEwC,aAAa,EAAE,GAAG1G,WAAW,CAACkE,cAAc,kCAAkC;EAC9EyC,iBAAiB,EAAE,GAAG3G,WAAW,CAACkE,cAAc,iCAAiC;EACjF0C,oBAAoB,EAAE,GAAG5G,WAAW,CAACkE,cAAc,8BAA8B;EACjF2C,WAAW,EAAE,GAAG7G,WAAW,CAACkE,cAAc;CAC3C;AAED,OAAO,MAAM4C,WAAW,GAAG;EACzBC,eAAe,EAAE,IAAI,GAAG,IAAI;EAAE;EAC9BC,sBAAsB,EAAE;CACzB;AAED,OAAO,MAAMC,UAAU,GAAG;EACxBC,YAAY,EAAE;CACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}