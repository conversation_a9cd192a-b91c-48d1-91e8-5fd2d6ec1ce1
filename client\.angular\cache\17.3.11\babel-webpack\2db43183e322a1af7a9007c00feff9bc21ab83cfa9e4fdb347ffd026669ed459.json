{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport * as moment from 'moment';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\nlet ReturnOrderDetailsComponent = class ReturnOrderDetailsComponent {\n  constructor(activatedRoute, productReturnService, service, authService) {\n    this.activatedRoute = activatedRoute;\n    this.productReturnService = productReturnService;\n    this.service = service;\n    this.authService = authService;\n    this.items = [{\n      label: 'Return Order Details',\n      routerLink: ['/store/return-order-details']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.ngUnsubscribe = new Subject();\n    this.moment = moment;\n    this.loading = false;\n    this.returnOrderId = null;\n    this.refDocId = null;\n    this.returnOrderDetail = null;\n    this.sellerDetails = {};\n    this.statuses = [];\n    this.returnReason = [];\n    this.sellerDetails = {\n      ...this.authService.partnerFunction\n    };\n  }\n  ngOnInit() {\n    this.getReasonReturn();\n    this.activatedRoute.paramMap.subscribe(params => {\n      const id = params.get(\"returnOrderId\");\n      const refDocId = params.get(\"refDocId\");\n      if (id && refDocId) {\n        this.returnOrderId = id;\n        this.refDocId = refDocId;\n        this.loading = true;\n        this.getOrderDetails();\n      }\n    });\n  }\n  getReasonReturn() {\n    this.productReturnService.getAllReturnReason().pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: res => {\n        this.returnReason = res?.data || [];\n      },\n      error: err => {\n        this._snackBar.open(\"Error while processing your request.\", {\n          type: 'Error'\n        });\n      }\n    });\n  }\n  getOrderDetails() {\n    const payload = {\n      SD_DOC: this.returnOrderId,\n      DOC_TYPE: \"CBAR\",\n      REF_SD_DOC: this.refDocId\n    };\n    const status$ = this.service.getAllStatus();\n    const details$ = this.service.getRetrunOrderDetails(payload);\n    forkJoin([status$, details$]).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: result => {\n        this.statuses = result[0]?.data || [];\n        this.returnOrderDetail = result[1]?.RETURNORDER || null;\n        this.loading = false;\n      },\n      error: () => {\n        this.loading = false;\n        this._snackBar.open(\"Error while processing your request.\", {\n          type: 'Error'\n        });\n      }\n    });\n  }\n  getStatusName(code) {\n    const status = this.statuses.find(o => o.code === code);\n    if (status) {\n      return status.description;\n    }\n    return \"\";\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n};\nReturnOrderDetailsComponent = __decorate([Component({\n  selector: 'app-return-order-details',\n  templateUrl: './return-order-details.component.html',\n  styleUrls: ['./return-order-details.component.scss']\n})], ReturnOrderDetailsComponent);\nexport { ReturnOrderDetailsComponent };", "map": {"version": 3, "names": ["Component", "moment", "Subject", "takeUntil", "fork<PERSON><PERSON>n", "ReturnOrderDetailsComponent", "constructor", "activatedRoute", "productReturnService", "service", "authService", "items", "label", "routerLink", "home", "icon", "ngUnsubscribe", "loading", "returnOrderId", "refDocId", "returnOrderDetail", "sellerDetails", "statuses", "returnReason", "partnerFunction", "ngOnInit", "getReasonReturn", "paramMap", "subscribe", "params", "id", "get", "getOrderDetails", "getAllReturnReason", "pipe", "next", "res", "data", "error", "err", "_snackBar", "open", "type", "payload", "SD_DOC", "DOC_TYPE", "REF_SD_DOC", "status$", "getAllStatus", "details$", "getRetrunOrderDetails", "result", "RETURNORDER", "getStatusName", "code", "status", "find", "o", "description", "ngOnDestroy", "complete", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-returns\\return-order-details\\return-order-details.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-return-order-details',\r\n  templateUrl: './return-order-details.component.html',\r\n  styleUrls: ['./return-order-details.component.scss']\r\n})\r\nexport class ReturnOrderDetailsComponent {\r\n\r\n  items: MenuItem[] | any = [\r\n    { label: 'Return Order Details', routerLink: ['/store/return-order-details'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public moment = moment;\r\n  public loading = false;\r\n  public returnOrderId: any = null;\r\n  public refDocId: any = null;\r\n  public returnOrderDetail: any = null;\r\n  public sellerDetails: any = {};\r\n  public statuses: any = [];\r\n  public returnReason: any = [];\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    public productReturnService: AccountService,\r\n    public service: ReturnOrderService,\r\n    public authService: AuthService\r\n  ) {\r\n    this.sellerDetails = {\r\n      ...this.authService.partnerFunction\r\n    };\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.getReasonReturn();\r\n    this.activatedRoute.paramMap.subscribe((params) => {\r\n      const id = params.get(\"returnOrderId\");\r\n      const refDocId = params.get(\"refDocId\");\r\n      if (id && refDocId) {\r\n        this.returnOrderId = id;\r\n        this.refDocId = refDocId;\r\n        this.loading = true;\r\n        this.getOrderDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  getReasonReturn() {\r\n    this.productReturnService\r\n      .getAllReturnReason()\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.returnReason = res?.data || [];\r\n        },\r\n        error: (err) => {\r\n          this._snackBar.open(\"Error while processing your request.\", { type: 'Error' });\r\n        },\r\n      });\r\n  }\r\n\r\n  getOrderDetails() {\r\n    const payload: any = {\r\n      SD_DOC: this.returnOrderId,\r\n      DOC_TYPE: \"CBAR\",\r\n      REF_SD_DOC: this.refDocId\r\n    };\r\n    const status$ = this.service.getAllStatus();\r\n    const details$ = this.service.getRetrunOrderDetails(payload);\r\n    forkJoin([status$, details$])\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (result: any) => {\r\n          this.statuses = result[0]?.data || [];\r\n          this.returnOrderDetail = result[1]?.RETURNORDER || null;\r\n          this.loading = false;\r\n        },\r\n        error: () => {\r\n          this.loading = false;\r\n          this._snackBar.open(\"Error while processing your request.\", { type: 'Error' });\r\n        },\r\n      });\r\n  }\r\n\r\n  getStatusName(code: string) {\r\n    const status = this.statuses.find((o: any) => o.code === code);\r\n    if (status) {\r\n      return status.description;\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAEzC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAEhC,SAASC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AAQ5C,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAiBtCC,YACUC,cAA8B,EAC/BC,oBAAoC,EACpCC,OAA2B,EAC3BC,WAAwB;IAHvB,KAAAH,cAAc,GAAdA,cAAc;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IAnBpB,KAAAC,KAAK,GAAqB,CACxB;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,UAAU,EAAE,CAAC,6BAA6B;IAAC,CAAE,CAC/E;IACD,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAExD,KAAAG,aAAa,GAAG,IAAId,OAAO,EAAQ;IACpC,KAAAD,MAAM,GAAGA,MAAM;IACf,KAAAgB,OAAO,GAAG,KAAK;IACf,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,QAAQ,GAAQ,IAAI;IACpB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,aAAa,GAAQ,EAAE;IACvB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,YAAY,GAAQ,EAAE;IAQ3B,IAAI,CAACF,aAAa,GAAG;MACnB,GAAG,IAAI,CAACX,WAAW,CAACc;KACrB;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACnB,cAAc,CAACoB,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MAChD,MAAMC,EAAE,GAAGD,MAAM,CAACE,GAAG,CAAC,eAAe,CAAC;MACtC,MAAMZ,QAAQ,GAAGU,MAAM,CAACE,GAAG,CAAC,UAAU,CAAC;MACvC,IAAID,EAAE,IAAIX,QAAQ,EAAE;QAClB,IAAI,CAACD,aAAa,GAAGY,EAAE;QACvB,IAAI,CAACX,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACF,OAAO,GAAG,IAAI;QACnB,IAAI,CAACe,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAEAN,eAAeA,CAAA;IACb,IAAI,CAAClB,oBAAoB,CACtByB,kBAAkB,EAAE,CACpBC,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACa,aAAa,CAAC,CAAC,CACnCY,SAAS,CAAC;MACTO,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACb,YAAY,GAAGa,GAAG,EAAEC,IAAI,IAAI,EAAE;MACrC,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,sCAAsC,EAAE;UAAEC,IAAI,EAAE;QAAO,CAAE,CAAC;MAChF;KACD,CAAC;EACN;EAEAV,eAAeA,CAAA;IACb,MAAMW,OAAO,GAAQ;MACnBC,MAAM,EAAE,IAAI,CAAC1B,aAAa;MAC1B2B,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,IAAI,CAAC3B;KAClB;IACD,MAAM4B,OAAO,GAAG,IAAI,CAACtC,OAAO,CAACuC,YAAY,EAAE;IAC3C,MAAMC,QAAQ,GAAG,IAAI,CAACxC,OAAO,CAACyC,qBAAqB,CAACP,OAAO,CAAC;IAC5DvC,QAAQ,CAAC,CAAC2C,OAAO,EAAEE,QAAQ,CAAC,CAAC,CAC1Bf,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACa,aAAa,CAAC,CAAC,CACnCY,SAAS,CAAC;MACTO,IAAI,EAAGgB,MAAW,IAAI;QACpB,IAAI,CAAC7B,QAAQ,GAAG6B,MAAM,CAAC,CAAC,CAAC,EAAEd,IAAI,IAAI,EAAE;QACrC,IAAI,CAACjB,iBAAiB,GAAG+B,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,IAAI,IAAI;QACvD,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACrB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACuB,SAAS,CAACC,IAAI,CAAC,sCAAsC,EAAE;UAAEC,IAAI,EAAE;QAAO,CAAE,CAAC;MAChF;KACD,CAAC;EACN;EAEAW,aAAaA,CAACC,IAAY;IACxB,MAAMC,MAAM,GAAG,IAAI,CAACjC,QAAQ,CAACkC,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAC9D,IAAIC,MAAM,EAAE;MACV,OAAOA,MAAM,CAACG,WAAW;IAC3B;IACA,OAAO,EAAE;EACX;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3C,aAAa,CAACmB,IAAI,EAAE;IACzB,IAAI,CAACnB,aAAa,CAAC4C,QAAQ,EAAE;EAC/B;CACD;AA3FYvD,2BAA2B,GAAAwD,UAAA,EALvC7D,SAAS,CAAC;EACT8D,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC;CACpD,CAAC,C,EACW3D,2BAA2B,CA2FvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}