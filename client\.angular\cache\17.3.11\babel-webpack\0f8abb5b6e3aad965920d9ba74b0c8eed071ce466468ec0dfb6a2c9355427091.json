{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/layout/service/app.layout.service\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"./app.menu.service\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/ripple\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"primeng/styleclass\";\nimport * as i8 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = () => [\"/store/profile\"];\nexport let AppTopbarComponent = /*#__PURE__*/(() => {\n  class AppTopbarComponent {\n    constructor(layoutService, el, authService, menuService) {\n      this.layoutService = layoutService;\n      this.el = el;\n      this.authService = authService;\n      this.menuService = menuService;\n      this.searchActive = false;\n      this.activeMenu = '';\n    }\n    activateSearch() {\n      this.searchActive = true;\n      setTimeout(() => {\n        this.searchInput.nativeElement.focus();\n      }, 100);\n    }\n    deactivateSearch() {\n      this.searchActive = false;\n    }\n    onMenuButtonClick() {\n      this.layoutService.onMenuToggle();\n    }\n    onConfigButtonClick() {\n      this.layoutService.showConfigSidebar();\n    }\n    onSidebarButtonClick() {\n      this.layoutService.showSidebar();\n    }\n    logout() {\n      this.authService.doLogout();\n    }\n    ngOnInit() {\n      this.menuService.activeMenu$.subscribe(menuName => {\n        this.activeMenu = menuName;\n      });\n    }\n    static {\n      this.ɵfac = function AppTopbarComponent_Factory(t) {\n        return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MenuService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppTopbarComponent,\n        selectors: [[\"app-topbar\"]],\n        viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(AppSidebarComponent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n          }\n        },\n        decls: 28,\n        vars: 4,\n        consts: [[\"menubutton\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\", \"gap-6\"], [1, \"app-logo-normal\", \"w-12rem\"], [3, \"src\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [1, \"ml-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cog\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"profile-item\", \"topbar-item\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"p-4\", \"w-15rem\", \"z-5\", \"ng-hidden\", \"border-round\", \"hadow-1\", \"border-1\", \"border-bluegray-100\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-3\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"routerLink\"], [1, \"pi\", \"pi-fw\", \"pi-user\", \"mr-2\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mt-3\", \"pt-3\", \"border-top-1\", \"logout-btn\"], [\"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", \"font-medium\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"]],\n        template: function AppTopbarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵelement(3, \"img\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 6, 0);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onMenuButtonClick());\n            });\n            i0.ɵɵelement(6, \"i\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 8);\n            i0.ɵɵelement(8, \"app-sidebar\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 9)(10, \"ul\", 10)(11, \"li\", 11)(12, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onConfigButtonClick());\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"li\", 13, 1)(15, \"a\", 14);\n            i0.ɵɵelement(16, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"ul\", 16)(18, \"li\", 17)(19, \"a\", 18);\n            i0.ɵɵelement(20, \"i\", 19);\n            i0.ɵɵelementStart(21, \"span\");\n            i0.ɵɵtext(22, \"Profile\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"li\", 20)(24, \"a\", 21);\n            i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_24_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.logout());\n            });\n            i0.ɵɵelement(25, \"i\", 22);\n            i0.ɵɵelementStart(26, \"span\");\n            i0.ɵɵtext(27, \"Logout\");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"dark\" ? \"light\" : \"dark\") + \".svg\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c2));\n          }\n        },\n        dependencies: [i4.ButtonDirective, i5.Ripple, i6.RouterLink, i7.StyleClass, i8.AppSidebarComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppTopbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}