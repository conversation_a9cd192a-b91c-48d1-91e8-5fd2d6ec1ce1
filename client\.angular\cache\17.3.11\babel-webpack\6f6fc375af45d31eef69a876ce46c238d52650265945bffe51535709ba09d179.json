{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/content-vendor.service\";\nimport * as i4 from \"./app.menu.component\";\nconst _c0 = [\"menuContainer\"];\nconst _c1 = () => [\"/\"];\nexport let AppSidebarComponent = /*#__PURE__*/(() => {\n  class AppSidebarComponent {\n    constructor(layoutService, el, route, CMSservice) {\n      this.layoutService = layoutService;\n      this.el = el;\n      this.route = route;\n      this.CMSservice = CMSservice;\n      this.timeout = null;\n      this.logo = '';\n    }\n    ngOnInit() {\n      const commonContent = this.route.snapshot.data['commonContent'];\n      const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"crm.logo\");\n      if (logoComponent?.length) {\n        this.logo = logoComponent[0].Logo?.url || '';\n      }\n    }\n    onMouseEnter() {\n      if (!this.layoutService.state.anchored) {\n        if (this.timeout) {\n          clearTimeout(this.timeout);\n          this.timeout = null;\n        }\n        this.layoutService.state.sidebarActive = true;\n      }\n    }\n    onMouseLeave() {\n      if (!this.layoutService.state.anchored) {\n        if (!this.timeout) {\n          this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\n        }\n      }\n    }\n    anchor() {\n      this.layoutService.state.anchored = !this.layoutService.state.anchored;\n    }\n    static {\n      this.ɵfac = function AppSidebarComponent_Factory(t) {\n        return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentVendorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppSidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        viewQuery: function AppSidebarComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuContainer = _t.first);\n          }\n        },\n        decls: 11,\n        vars: 4,\n        consts: [[\"menuContainer\", \"\"], [1, \"layout-sidebar\", 3, \"mouseenter\", \"mouseleave\"], [1, \"sidebar-header\"], [1, \"app-logo\", 3, \"routerLink\"], [1, \"app-logo-small\", \"h-2rem\"], [3, \"src\"], [1, \"app-logo-normal\"], [1, \"h-2rem\", 3, \"src\"], [\"type\", \"button\", 1, \"layout-sidebar-anchor\", \"p-link\", \"z-2\", 3, \"click\"], [1, \"layout-menu-container\"]],\n        template: function AppSidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1);\n            i0.ɵɵlistener(\"mouseenter\", function AppSidebarComponent_Template_div_mouseenter_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onMouseEnter());\n            })(\"mouseleave\", function AppSidebarComponent_Template_div_mouseleave_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onMouseLeave());\n            });\n            i0.ɵɵelementStart(1, \"div\", 2)(2, \"a\", 3)(3, \"div\", 4);\n            i0.ɵɵelement(4, \"img\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 6);\n            i0.ɵɵelement(6, \"img\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function AppSidebarComponent_Template_button_click_7_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.anchor());\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 9, 0);\n            i0.ɵɵelement(10, \"app-menu\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n          }\n        },\n        dependencies: [i2.RouterLink, i4.AppMenuComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppSidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}