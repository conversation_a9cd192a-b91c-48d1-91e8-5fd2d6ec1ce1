{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { OpportunitiesComponent } from './opportunities.component';\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\nimport { OpportunitiesFollowUpComponent } from './opportunities-details/opportunities-follow-up/opportunities-follow-up.component';\nimport { OpportunitiesHierarchyComponent } from './opportunities-details/opportunities-hierarchy/opportunities-hierarchy.component';\nimport { OpportunitiesDocumentFlowComponent } from './opportunities-details/opportunities-document-flow/opportunities-document-flow.component';\nimport { SalesCallFollowItemDetailComponent } from './opportunities-details/opportunities-follow-up/sales-call-follow-item-detail/sales-call-follow-item-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: OpportunitiesComponent\n}, {\n  path: 'create',\n  component: AddOpportunitieComponent\n}, {\n  path: ':id',\n  component: OpportunitiesDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: OpportunitiesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: OpportunitiesContactsComponent\n  }, {\n    path: 'sales-team',\n    component: OpportunitiesSalesTeamComponent\n  }, {\n    path: 'follow-up',\n    component: OpportunitiesFollowUpComponent\n  }, {\n    path: 'follow-up/:id',\n    component: SalesCallFollowItemDetailComponent\n  }, {\n    path: 'hierarchy',\n    component: OpportunitiesHierarchyComponent\n  }, {\n    path: 'document-flow',\n    component: OpportunitiesDocumentFlowComponent\n  }, {\n    path: 'ai-insights',\n    component: OpportunitiesAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: OpportunitiesOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: OpportunitiesAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: OpportunitiesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class OpportunitiesRoutingModule {\n  static {\n    this.ɵfac = function OpportunitiesRoutingModule_Factory(t) {\n      return new (t || OpportunitiesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OpportunitiesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OpportunitiesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "OpportunitiesComponent", "OpportunitiesDetailsComponent", "OpportunitiesOverviewComponent", "OpportunitiesContactsComponent", "OpportunitiesSalesTeamComponent", "OpportunitiesAiInsightsComponent", "OpportunitiesOrganizationDataComponent", "OpportunitiesAttachmentsComponent", "OpportunitiesNotesComponent", "AddOpportunitieComponent", "OpportunitiesFollowUpComponent", "OpportunitiesHierarchyComponent", "OpportunitiesDocumentFlowComponent", "SalesCallFollowItemDetailComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "OpportunitiesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { OpportunitiesComponent } from './opportunities.component';\r\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\r\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\r\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\r\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\r\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\r\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\r\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\r\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\r\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\r\nimport { OpportunitiesFollowUpComponent } from './opportunities-details/opportunities-follow-up/opportunities-follow-up.component';\r\nimport { OpportunitiesHierarchyComponent } from './opportunities-details/opportunities-hierarchy/opportunities-hierarchy.component';\r\nimport { OpportunitiesDocumentFlowComponent } from './opportunities-details/opportunities-document-flow/opportunities-document-flow.component';\r\nimport { SalesCallFollowItemDetailComponent } from './opportunities-details/opportunities-follow-up/sales-call-follow-item-detail/sales-call-follow-item-detail.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: OpportunitiesComponent },\r\n  { path: 'create', component: AddOpportunitieComponent },\r\n  {\r\n    path: ':id',\r\n    component: OpportunitiesDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: OpportunitiesOverviewComponent },\r\n      { path: 'contacts', component: OpportunitiesContactsComponent },\r\n      { path: 'sales-team', component: OpportunitiesSalesTeamComponent },\r\n      { path: 'follow-up', component: OpportunitiesFollowUpComponent },\r\n      {\r\n        path: 'follow-up/:id',\r\n        component: SalesCallFollowItemDetailComponent,\r\n      },\r\n      { path: 'hierarchy', component: OpportunitiesHierarchyComponent },\r\n      { path: 'document-flow', component: OpportunitiesDocumentFlowComponent },\r\n      { path: 'ai-insights', component: OpportunitiesAiInsightsComponent },\r\n      {\r\n        path: 'organization-data',\r\n        component: OpportunitiesOrganizationDataComponent,\r\n      },\r\n      { path: 'attachments', component: OpportunitiesAttachmentsComponent },\r\n      { path: 'notes', component: OpportunitiesNotesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class OpportunitiesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,6BAA6B,QAAQ,yDAAyD;AACvG,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,gCAAgC,QAAQ,uFAAuF;AACxI,SAASC,sCAAsC,QAAQ,mGAAmG;AAC1J,SAASC,iCAAiC,QAAQ,uFAAuF;AACzI,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,8BAA8B,QAAQ,mFAAmF;AAClI,SAASC,+BAA+B,QAAQ,mFAAmF;AACnI,SAASC,kCAAkC,QAAQ,2FAA2F;AAC9I,SAASC,kCAAkC,QAAQ,uHAAuH;;;AAE1K,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEhB;AAAsB,CAAE,EAC/C;EAAEe,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEP;AAAwB,CAAE,EACvD;EACEM,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEf,6BAA6B;EACxCgB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEd;EAA8B,CAAE,EAC/D;IAAEa,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEb;EAA8B,CAAE,EAC/D;IAAEY,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEZ;EAA+B,CAAE,EAClE;IAAEW,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEN;EAA8B,CAAE,EAChE;IACEK,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEH;GACZ,EACD;IAAEE,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEL;EAA+B,CAAE,EACjE;IAAEI,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEJ;EAAkC,CAAE,EACxE;IAAEG,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEX;EAAgC,CAAE,EACpE;IACEU,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEV;GACZ,EACD;IAAES,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAET;EAAiC,CAAE,EACrE;IAAEQ,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAA2B,CAAE,EACzD;IAAEO,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BrB,YAAY,CAACsB,QAAQ,CAACP,MAAM,CAAC,EAC7Bf,YAAY;IAAA;EAAA;;;2EAEXqB,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAxB,YAAA;IAAAyB,OAAA,GAF3BzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}