{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../account.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"../../../shared/initials.pipe\";\nfunction AccountDetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"p-breadcrumb\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"model\", ctx_r0.breadcrumbitems)(\"home\", ctx_r0.home)(\"styleClass\", \"py-2 px-0 border-none\");\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r2.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r2.label);\n  }\n}\nfunction AccountDetailsComponent_p_tabPanel_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 37);\n    i0.ɵɵtemplate(1, AccountDetailsComponent_p_tabPanel_6_ng_template_1_Template, 2, 2, \"ng-template\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class AccountDetailsComponent {\n  constructor(route, router, formBuilder, accountservice, messageservice, confirmationservice) {\n    this.route = route;\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.accountservice = accountservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.accountDetails = null;\n    this.sidebarDetails = null;\n    this.NoteDetails = null;\n    this.activeItem = {};\n    this.bp_id = '';\n    this.bp_status = '';\n    this.bp_doc_id = '';\n    this.partner_id = '';\n    this.partner_role = '';\n    this.loading = false;\n    this.activeIndex = 0;\n    this.isSidebarHidden = false;\n    this.submitted = false;\n    this.saving = false;\n    this.Actions = [];\n    this.hideBreadCrumbs = false;\n    this.showInvoices = false;\n    this.showReturns = false;\n    this.GlobalNoteForm = this.formBuilder.group({\n      note: ['']\n    });\n  }\n  ngOnInit() {\n    this.hideBreadCrumbs = this.route.snapshot.data['hideBreadCrumbs'] || false;\n    this.showInvoices = this.route.snapshot.data['showInvoices'] || false;\n    this.showReturns = this.route.snapshot.data['showReturns'] || false;\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      this.partner_id = response?.bp_id;\n      if (this.partner_id) {\n        this.accountservice.getGlobalNote(this.partner_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n          next: noteResponse => {\n            this.NoteDetails = noteResponse?.data[0] || [];\n            this.GlobalNoteForm.patchValue({\n              note: noteResponse?.data[0]?.note\n            });\n          },\n          error: error => {\n            console.error('Error fetching global note:', error);\n          }\n        });\n      }\n    });\n    this.makeMenuItems(this.bp_id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const accountId = params.get('id');\n      if (accountId) {\n        this.loadAccountData(accountId);\n      }\n    });\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(bp_id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `sales-team`\n    }, {\n      label: 'Relationships',\n      routerLink: `relationships`\n    },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `ai-insights`,\n    // },\n    {\n      label: 'Organization Data',\n      routerLink: `organization-data`\n    }, {\n      label: 'Attachments',\n      routerLink: `attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `notes`\n    }, {\n      label: 'Activities',\n      routerLink: `activities`\n    }, {\n      label: 'Opportunities',\n      routerLink: `opportunities`\n    }, {\n      label: 'Tickets',\n      routerLink: `tickets`\n    }, {\n      label: 'Sales Quotes',\n      routerLink: `sales-quotes`\n    }, {\n      label: 'Sales Orders',\n      routerLink: `sales-orders`\n    }];\n    if (this.showInvoices) {\n      this.items.push({\n        label: 'Invoices',\n        routerLink: `invoices`\n      });\n    }\n    if (this.showReturns) {\n      this.items.push({\n        label: 'Returns',\n        routerLink: `returns`\n      });\n    }\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Accounts',\n      routerLink: ['/store/account']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigate([selectedTab.routerLink], {\n        relativeTo: this.route\n      });\n    }\n  }\n  loadAccountData(accountId) {\n    this.loading = true;\n    this.accountservice.getAccountByID(accountId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response) {\n          this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\n          this.bp_doc_id = response?.data?.[0]?.bp_extension?.documentId;\n          this.Actions = [{\n            name: this.bp_status === 'OBSOLETE' ? 'Set As Active' : 'Set As Obsolete',\n            code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO'\n          }];\n          const partner_role = response?.data?.[0]?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n          this.partner_role = partner_role?.bp_identification?.business_partner?.bp_full_name || null;\n          this.accountDetails = response?.data?.[0] || null;\n          this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.addresses || []);\n          this.loading = false;\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      is_marked_for_archiving: status\n    };\n    this.accountservice.updateBpStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.GlobalNoteForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.GlobalNoteForm.value\n      };\n      const data = {\n        note: value?.note,\n        bp_id: _this?.partner_id,\n        ...(!_this.NoteDetails.documentId ? {\n          is_global_note: true\n        } : {})\n      };\n      const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.accountservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n      : _this.accountservice.createNote(data); // Create if not exists\n      apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Account Note Updated successFully!'\n          });\n          _this.accountservice.getAccountByID(_this.partner_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  goToBack() {\n    this.router.navigate(['/store/account']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountDetailsComponent_Factory(t) {\n      return new (t || AccountDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountDetailsComponent,\n      selectors: [[\"app-account-details\"]],\n      decls: 81,\n      vars: 25,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [\"class\", \"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\", 4, \"ngIf\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"onChange\", \"activeIndexChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function AccountDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, AccountDetailsComponent_div_2_Template, 3, 3, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"p-tabView\", 5);\n          i0.ɵɵlistener(\"onChange\", function AccountDetailsComponent_Template_p_tabView_onChange_5_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function AccountDetailsComponent_Template_p_tabView_activeIndexChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(6, AccountDetailsComponent_p_tabPanel_6_Template, 2, 1, \"p-tabPanel\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"h5\", 14);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"h5\", 16);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"ul\", 17)(21, \"li\", 18)(22, \"span\", 19);\n          i0.ɵɵtext(23, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"li\", 18)(26, \"span\", 19);\n          i0.ɵɵtext(27, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"li\", 18)(30, \"span\", 19);\n          i0.ɵɵtext(31, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 20)(34, \"ul\", 21)(35, \"li\", 22)(36, \"span\", 23)(37, \"i\", 24);\n          i0.ɵɵtext(38, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"span\", 25);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"li\", 22)(43, \"span\", 23)(44, \"i\", 24);\n          i0.ɵɵtext(45, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"span\", 25);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"li\", 22)(50, \"span\", 23)(51, \"i\", 24);\n          i0.ɵɵtext(52, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 25);\n          i0.ɵɵtext(55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"li\", 22)(57, \"span\", 23)(58, \"i\", 24);\n          i0.ɵɵtext(59, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"span\", 25);\n          i0.ɵɵtext(62);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"li\", 22)(64, \"span\", 23)(65, \"i\", 24);\n          i0.ɵɵtext(66, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\", 25);\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"form\", 26)(71, \"div\", 27)(72, \"h4\", 28);\n          i0.ɵɵtext(73, \"Global Note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 29);\n          i0.ɵɵelement(75, \"textarea\", 30);\n          i0.ɵɵelementStart(76, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_button_click_76_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(77, \"div\", 32)(78, \"p-button\", 33);\n          i0.ɵɵlistener(\"click\", function AccountDetailsComponent_Template_p_button_click_78_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(79, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(80, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideBreadCrumbs);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 23, ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.accountDetails == null ? null : ctx.accountDetails.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.accountDetails == null ? null : ctx.accountDetails.contact_companies == null ? null : ctx.accountDetails.contact_companies[0] == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person == null ? null : ctx.accountDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.Breadcrumb, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.PrimeTemplate, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i7.ButtonDirective, i7.Button, i8.TabView, i8.TabPanel, i9.Toast, i10.ConfirmDialog, i11.InitialsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "breadcrumbitems", "home", "ɵɵtext", "tab_r2", "routerLink", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "AccountDetailsComponent_p_tabPanel_6_ng_template_1_Template", "AccountDetailsComponent", "constructor", "route", "router", "formBuilder", "accountservice", "messageservice", "confirmationservice", "unsubscribe$", "accountDetails", "sidebarDetails", "NoteDetails", "activeItem", "bp_id", "bp_status", "bp_doc_id", "partner_id", "partner_role", "loading", "activeIndex", "isSidebarHidden", "submitted", "saving", "Actions", "hideBreadCrumbs", "showInvoices", "showReturns", "GlobalNoteForm", "group", "note", "ngOnInit", "snapshot", "data", "icon", "paramMap", "get", "account", "pipe", "subscribe", "response", "getGlobalNote", "next", "noteResponse", "patchValue", "error", "console", "makeMenuItems", "items", "length", "setActiveTabFromURL", "params", "accountId", "loadAccountData", "events", "push", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigate", "relativeTo", "getAccountByID", "bp_extension", "documentId", "name", "code", "customer", "partner_functions", "find", "p", "partner_function", "bp_identification", "business_partner", "bp_full_name", "formatSidebarDetails", "addresses", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone_numbers", "website_url", "home_page_urls", "onActionChange", "actionCode", "value", "actionsMap", "SAA", "UpdateStatus", "SAO", "action", "confirm", "message", "header", "accept", "docid", "status", "is_marked_for_archiving", "updateBpStatus", "add", "severity", "detail", "setTimeout", "window", "location", "reload", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "is_global_note", "apiCall", "updateNote", "createNote", "goToBack", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "FormBuilder", "i3", "AccountService", "i4", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "AccountDetailsComponent_Template", "rf", "ctx", "AccountDetailsComponent_div_2_Template", "ɵɵlistener", "AccountDetailsComponent_Template_p_tabView_onChange_5_listener", "$event", "ɵɵtwoWayListener", "AccountDetailsComponent_Template_p_tabView_activeIndexChange_5_listener", "ɵɵtwoWayBindingSet", "AccountDetailsComponent_p_tabPanel_6_Template", "AccountDetailsComponent_Template_button_click_76_listener", "AccountDetailsComponent_Template_p_button_click_78_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "contact_companies", "business_partner_person", "first_name", "last_name"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AccountService } from '../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n@Component({\r\n  selector: 'app-account-details',\r\n  templateUrl: './account-details.component.html',\r\n  styleUrl: './account-details.component.scss',\r\n})\r\nexport class AccountDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public accountDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public NoteDetails: any = null;\r\n  public items: MenuItem[] | any;\r\n  public activeItem: MenuItem = {};\r\n  public home: MenuItem | any;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public bp_id: string = '';\r\n  public bp_status: string = '';\r\n  public bp_doc_id: string = '';\r\n  public partner_id: string = '';\r\n  public partner_role: string = '';\r\n  public loading: boolean = false;\r\n  public activeIndex: number = 0;\r\n  public isSidebarHidden = false;\r\n  public submitted = false;\r\n  public saving = false;\r\n  public Actions: Actions[] = [];\r\n  public hideBreadCrumbs = false;\r\n  public showInvoices = false;\r\n  public showReturns = false\r\n\r\n  public GlobalNoteForm: FormGroup = this.formBuilder.group({\r\n    note: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private formBuilder: FormBuilder,\r\n    private accountservice: AccountService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.hideBreadCrumbs = this.route.snapshot.data['hideBreadCrumbs'] || false;\r\n    this.showInvoices = this.route.snapshot.data['showInvoices'] || false;\r\n    this.showReturns = this.route.snapshot.data['showReturns'] || false;\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        this.partner_id = response?.bp_id;\r\n\r\n        if (this.partner_id) {\r\n          this.accountservice\r\n            .getGlobalNote(this.partner_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe({\r\n              next: (noteResponse) => {\r\n                this.NoteDetails = noteResponse?.data[0] || [];\r\n\r\n                this.GlobalNoteForm.patchValue({\r\n                  note: noteResponse?.data[0]?.note,\r\n                });\r\n              },\r\n              error: (error) => {\r\n                console.error('Error fetching global note:', error);\r\n              },\r\n            });\r\n        }\r\n      });\r\n\r\n    this.makeMenuItems(this.bp_id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const accountId = params.get('id');\r\n        if (accountId) {\r\n          this.loadAccountData(accountId);\r\n        }\r\n      });\r\n\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(bp_id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `overview`,\r\n      },\r\n      {\r\n        label: 'Contacts',\r\n        routerLink: `contacts`,\r\n      },\r\n      {\r\n        label: 'Sales Team',\r\n        routerLink: `sales-team`,\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        routerLink: `relationships`,\r\n      },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `ai-insights`,\r\n      // },\r\n      {\r\n        label: 'Organization Data',\r\n        routerLink: `organization-data`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `attachments`,\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        routerLink: `notes`,\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        routerLink: `activities`,\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        routerLink: `opportunities`,\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        routerLink: `tickets`,\r\n      },\r\n      {\r\n        label: 'Sales Quotes',\r\n        routerLink: `sales-quotes`,\r\n      },\r\n      {\r\n        label: 'Sales Orders',\r\n        routerLink: `sales-orders`,\r\n      },\r\n    ];\r\n    if(this.showInvoices) {\r\n      this.items.push({\r\n        label: 'Invoices',\r\n        routerLink: `invoices`,\r\n      });\r\n    }\r\n    if(this.showReturns) {\r\n      this.items.push({\r\n        label: 'Returns',\r\n        routerLink: `returns`,\r\n      });\r\n    }\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Accounts', routerLink: ['/store/account'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigate([selectedTab.routerLink], { relativeTo: this.route });\r\n    }\r\n  }\r\n\r\n  private loadAccountData(accountId: string): void {\r\n    this.loading = true;\r\n    this.accountservice\r\n      .getAccountByID(accountId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response) {\r\n            this.bp_status = response?.data?.[0]?.bp_extension?.bp_status;\r\n            this.bp_doc_id = response?.data?.[0]?.bp_extension?.documentId;\r\n            this.Actions = [\r\n              {\r\n                name:\r\n                  this.bp_status === 'OBSOLETE'\r\n                    ? 'Set As Active'\r\n                    : 'Set As Obsolete',\r\n                code: this.bp_status === 'OBSOLETE' ? 'SAA' : 'SAO',\r\n              },\r\n            ];\r\n            const partner_role =\r\n              response?.data?.[0]?.customer?.partner_functions?.find(\r\n                (p: any) => p.partner_function === 'YI'\r\n              );\r\n            this.partner_role =\r\n              partner_role?.bp_identification?.business_partner?.bp_full_name ||\r\n              null;\r\n            this.accountDetails = response?.data?.[0] || null;\r\n            this.sidebarDetails = this.formatSidebarDetails(\r\n              response?.data[0]?.addresses || []\r\n            );\r\n            this.loading = false;\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\r\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      is_marked_for_archiving: status,\r\n    };\r\n    this.accountservice\r\n      .updateBpStatus(docid, data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Action Updated Successfully!',\r\n          });\r\n\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 1000);\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.GlobalNoteForm.invalid) {\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    const value = { ...this.GlobalNoteForm.value };\r\n\r\n    const data = {\r\n      note: value?.note,\r\n      bp_id: this?.partner_id,\r\n      ...(!this.NoteDetails.documentId ? { is_global_note: true } : {}),\r\n    };\r\n\r\n    const apiCall =\r\n      this.NoteDetails && this.NoteDetails.documentId\r\n        ? this.accountservice.updateNote(this.NoteDetails.documentId, data) // Update if exists\r\n        : this.accountservice.createNote(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Account Note Updated successFully!',\r\n        });\r\n        this.accountservice\r\n          .getAccountByID(this.partner_id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/account']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\" *ngIf=\"!hideBreadCrumbs\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <!-- <p-dropdown [options]=\"Actions\" (onChange)=\"onActionChange($event)\" optionLabel=\"name\" placeholder=\"Action\"\r\n        [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" /> -->\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" (onChange)=\"onTabChange($event)\" [(activeIndex)]=\"activeIndex\">\r\n                <p-tabPanel *ngFor=\"let tab of items;let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative flex-nowrap\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">{{ accountDetails?.bp_full_name | initials }}</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{accountDetails?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">CRM ID</span> :\r\n                                            {{accountDetails?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Account Owner </span> :\r\n                                            {{partner_role || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">Main Contact</span> :\r\n                                            {{\r\n                                            (accountDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (accountDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{sidebarDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                    <form [formGroup]=\"GlobalNoteForm\">\r\n                        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden mt-5 p-3\">\r\n                            <h4 class=\"mb-3 font-semibold text-color\">Global Note</h4>\r\n                            <div class=\"flex flex-column gap-3\">\r\n                                <textarea formControlName=\"note\" rows=\"4\"\r\n                                    class=\"w-full h-8rem p-2 border-1 border-round\"\r\n                                    placeholder=\"Enter your note here...\"></textarea>\r\n                                <button pButton type=\"button\" (click)=\"onNoteSubmit()\" label=\"Save Note\"\r\n                                    class=\"p-button-rounded justify-content-center w-9rem h-3rem\"></button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": ";AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICDjCC,EADJ,CAAAC,cAAA,cAAmH,cACrD;IAGtDD,EAAA,CAAAE,SAAA,uBAA+F;IAIvGF,EAHI,CAAAG,YAAA,EAAM,EAGJ;;;;IAJgBH,EAAA,CAAAI,SAAA,GAAyB;IAAeJ,EAAxC,CAAAK,UAAA,UAAAC,MAAA,CAAAC,eAAA,CAAyB,SAAAD,MAAA,CAAAE,IAAA,CAAc,uCAAuC;;;;;IAWhFR,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAS,MAAA,GACvE;IAAAT,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAK,UAAA,eAAAK,MAAA,CAAAC,UAAA,CAA6B;IACuDX,EAAA,CAAAI,SAAA,EACvE;IADuEJ,EAAA,CAAAY,iBAAA,CAAAF,MAAA,CAAAG,KAAA,CACvE;;;;;IAJxBb,EAAA,CAAAC,cAAA,qBAAmF;IAC/ED,EAAA,CAAAc,UAAA,IAAAC,2DAAA,0BAAgC;IAKpCf,EAAA,CAAAG,YAAA,EAAa;;;IANuCH,EAAA,CAAAK,UAAA,+BAA8B;;;ADElG,OAAM,MAAOW,uBAAuB;EA4BlCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,cAA8B,EAC9BC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAjCrB,KAAAC,YAAY,GAAG,IAAI1B,OAAO,EAAQ;IACnC,KAAA2B,cAAc,GAAQ,IAAI;IAC1B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAAC,UAAU,GAAa,EAAE;IAGzB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,OAAO,GAAc,EAAE;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,WAAW,GAAG,KAAK;IAEnB,KAAAC,cAAc,GAAc,IAAI,CAACvB,WAAW,CAACwB,KAAK,CAAC;MACxDC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EASE;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACN,eAAe,GAAG,IAAI,CAACtB,KAAK,CAAC6B,QAAQ,CAACC,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK;IAC3E,IAAI,CAACP,YAAY,GAAG,IAAI,CAACvB,KAAK,CAAC6B,QAAQ,CAACC,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK;IACrE,IAAI,CAACN,WAAW,GAAG,IAAI,CAACxB,KAAK,CAAC6B,QAAQ,CAACC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK;IACnE,IAAI,CAACxC,IAAI,GAAG;MAAEyC,IAAI,EAAE,oBAAoB;MAAEtC,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACkB,KAAK,GAAG,IAAI,CAACX,KAAK,CAAC6B,QAAQ,CAACG,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAAC9B,cAAc,CAAC+B,OAAO,CACxBC,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAI,CAACvB,UAAU,GAAGuB,QAAQ,EAAE1B,KAAK;MAEjC,IAAI,IAAI,CAACG,UAAU,EAAE;QACnB,IAAI,CAACX,cAAc,CAChBmC,aAAa,CAAC,IAAI,CAACxB,UAAU,CAAC,CAC9BqB,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;UACTG,IAAI,EAAGC,YAAY,IAAI;YACrB,IAAI,CAAC/B,WAAW,GAAG+B,YAAY,EAAEV,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YAE9C,IAAI,CAACL,cAAc,CAACgB,UAAU,CAAC;cAC7Bd,IAAI,EAAEa,YAAY,EAAEV,IAAI,CAAC,CAAC,CAAC,EAAEH;aAC9B,CAAC;UACJ,CAAC;UACDe,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACrD;SACD,CAAC;MACN;IACF,CAAC,CAAC;IAEJ,IAAI,CAACE,aAAa,CAAC,IAAI,CAACjC,KAAK,CAAC;IAC9B,IAAI,IAAI,CAACkC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACpC,UAAU,GAAG,IAAI,CAACmC,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACE,mBAAmB,EAAE;IAC1B,IAAI,CAAC/C,KAAK,CAACgC,QAAQ,CAChBG,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAEY,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAACf,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIgB,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;IAEJ,IAAI,CAAChD,MAAM,CAACkD,MAAM,CAAChB,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAAC8B,SAAS,CAAC,MAAK;MACnE,IAAI,CAACW,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAH,aAAaA,CAACjC,KAAa;IACzB,IAAI,CAACkC,KAAK,GAAG,CACX;MACElD,KAAK,EAAE,UAAU;MACjBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,UAAU;MACjBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,YAAY;MACnBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,eAAe;MACtBF,UAAU,EAAE;KACb;IACD;IACA;IACA;IACA;IACA;MACEE,KAAK,EAAE,mBAAmB;MAC1BF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,aAAa;MACpBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,OAAO;MACdF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,YAAY;MACnBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,eAAe;MACtBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,SAAS;MAChBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,cAAc;MACrBF,UAAU,EAAE;KACb,EACD;MACEE,KAAK,EAAE,cAAc;MACrBF,UAAU,EAAE;KACb,CACF;IACD,IAAG,IAAI,CAAC8B,YAAY,EAAE;MACpB,IAAI,CAACsB,KAAK,CAACO,IAAI,CAAC;QACdzD,KAAK,EAAE,UAAU;QACjBF,UAAU,EAAE;OACb,CAAC;IACJ;IACA,IAAG,IAAI,CAAC+B,WAAW,EAAE;MACnB,IAAI,CAACqB,KAAK,CAACO,IAAI,CAAC;QACdzD,KAAK,EAAE,SAAS;QAChBF,UAAU,EAAE;OACb,CAAC;IACJ;EACF;EAEAsD,mBAAmBA,CAAA;IACjB,MAAMM,QAAQ,GAAG,IAAI,CAACpD,MAAM,CAACqD,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAACZ,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMY,UAAU,GAAG,IAAI,CAACb,KAAK,CAACc,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAACnE,UAAU,CAACoE,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAACtC,WAAW,GAAGyC,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAAChD,UAAU,GAAG,IAAI,CAACmC,KAAK,CAAC,IAAI,CAAC5B,WAAW,CAAC,IAAI,IAAI,CAAC4B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACiB,gBAAgB,CAAC,IAAI,CAACpD,UAAU,EAAEf,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEAmE,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC1E,eAAe,GAAG,CACrB;MAAEM,KAAK,EAAE,UAAU;MAAEF,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,EACrD;MAAEE,KAAK,EAAEoE,SAAS;MAAEtE,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAuE,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACpB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAAC7B,WAAW,GAAGgD,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC5B,WAAW,CAAC;IAEhD,IAAIkD,WAAW,EAAE1E,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAACmE,QAAQ,CAAC,CAACD,WAAW,CAAC1E,UAAU,CAAC,EAAE;QAAE4E,UAAU,EAAE,IAAI,CAACrE;MAAK,CAAE,CAAC;IAC5E;EACF;EAEQkD,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAACjC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACb,cAAc,CAChBmE,cAAc,CAACrB,SAAS,CAAC,CACzBd,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;MACTG,IAAI,EAAGF,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACzB,SAAS,GAAGyB,QAAQ,EAAEP,IAAI,GAAG,CAAC,CAAC,EAAEyC,YAAY,EAAE3D,SAAS;UAC7D,IAAI,CAACC,SAAS,GAAGwB,QAAQ,EAAEP,IAAI,GAAG,CAAC,CAAC,EAAEyC,YAAY,EAAEC,UAAU;UAC9D,IAAI,CAACnD,OAAO,GAAG,CACb;YACEoD,IAAI,EACF,IAAI,CAAC7D,SAAS,KAAK,UAAU,GACzB,eAAe,GACf,iBAAiB;YACvB8D,IAAI,EAAE,IAAI,CAAC9D,SAAS,KAAK,UAAU,GAAG,KAAK,GAAG;WAC/C,CACF;UACD,MAAMG,YAAY,GAChBsB,QAAQ,EAAEP,IAAI,GAAG,CAAC,CAAC,EAAE6C,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CACnDC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;UACH,IAAI,CAAChE,YAAY,GACfA,YAAY,EAAEiE,iBAAiB,EAAEC,gBAAgB,EAAEC,YAAY,IAC/D,IAAI;UACN,IAAI,CAAC3E,cAAc,GAAG8B,QAAQ,EAAEP,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI;UACjD,IAAI,CAACtB,cAAc,GAAG,IAAI,CAAC2E,oBAAoB,CAC7C9C,QAAQ,EAAEP,IAAI,CAAC,CAAC,CAAC,EAAEsD,SAAS,IAAI,EAAE,CACnC;UACD,IAAI,CAACpE,OAAO,GAAG,KAAK;QACtB;MACF,CAAC;MACD0B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEQmE,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbC,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAEhB,OAAO,EAAEiB,aAAa,GAAG,CAAC,CAAC,EAAED,YAAY,IAAI,GAAG;MAC9DE,WAAW,EAAElB,OAAO,EAAEmB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,cAAcA,CAACzC,KAAU;IACvB,MAAM0C,UAAU,GAAG1C,KAAK,CAAC2C,KAAK,EAAElC,IAAI;IAEpC,MAAMmC,UAAU,GAAkC;MAChDC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAAClG,SAAS,EAAE,OAAO,CAAC;MACrDmG,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,IAAI,CAAClG,SAAS,EAAE,MAAM;KACpD;IAED,MAAMoG,MAAM,GAAGJ,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIM,MAAM,EAAE;MACV,IAAI,CAAC5G,mBAAmB,CAAC6G,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjBrF,IAAI,EAAE,4BAA4B;QAClCsF,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAF,YAAYA,CAACO,KAAU,EAAEC,MAAW;IAClC,MAAMzF,IAAI,GAAG;MACX0F,uBAAuB,EAAED;KAC1B;IACD,IAAI,CAACpH,cAAc,CAChBsH,cAAc,CAACH,KAAK,EAAExF,IAAI,CAAC,CAC3BK,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnC,cAAc,CAACsH,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEFC,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDtF,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACtC,cAAc,CAACsH,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEMK,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC/G,SAAS,GAAG,IAAI;MAErB,IAAI+G,KAAI,CAACzG,cAAc,CAAC2G,OAAO,EAAE;QAC/B;MACF;MACAF,KAAI,CAAC9G,MAAM,GAAG,IAAI;MAClB,MAAMwF,KAAK,GAAG;QAAE,GAAGsB,KAAI,CAACzG,cAAc,CAACmF;MAAK,CAAE;MAE9C,MAAM9E,IAAI,GAAG;QACXH,IAAI,EAAEiF,KAAK,EAAEjF,IAAI;QACjBhB,KAAK,EAAEuH,KAAI,EAAEpH,UAAU;QACvB,IAAI,CAACoH,KAAI,CAACzH,WAAW,CAAC+D,UAAU,GAAG;UAAE6D,cAAc,EAAE;QAAI,CAAE,GAAG,EAAE;OACjE;MAED,MAAMC,OAAO,GACXJ,KAAI,CAACzH,WAAW,IAAIyH,KAAI,CAACzH,WAAW,CAAC+D,UAAU,GAC3C0D,KAAI,CAAC/H,cAAc,CAACoI,UAAU,CAACL,KAAI,CAACzH,WAAW,CAAC+D,UAAU,EAAE1C,IAAI,CAAC,CAAC;MAAA,EAClEoG,KAAI,CAAC/H,cAAc,CAACqI,UAAU,CAAC1G,IAAI,CAAC,CAAC,CAAC;MAC5CwG,OAAO,CAACnG,IAAI,CAACtD,SAAS,CAACqJ,KAAI,CAAC5H,YAAY,CAAC,CAAC,CAAC8B,SAAS,CAAC;QACnDG,IAAI,EAAEA,CAAA,KAAK;UACT2F,KAAI,CAAC9H,cAAc,CAACsH,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFM,KAAI,CAAC/H,cAAc,CAChBmE,cAAc,CAAC4D,KAAI,CAACpH,UAAU,CAAC,CAC/BqB,IAAI,CAACtD,SAAS,CAACqJ,KAAI,CAAC5H,YAAY,CAAC,CAAC,CAClC8B,SAAS,EAAE;QAChB,CAAC;QACDM,KAAK,EAAEA,CAAA,KAAK;UACVwF,KAAI,CAAC9G,MAAM,GAAG,KAAK;UACnB8G,KAAI,CAAC9H,cAAc,CAACsH,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEAa,QAAQA,CAAA;IACN,IAAI,CAACxI,MAAM,CAACmE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAsE,aAAaA,CAAA;IACX,IAAI,CAACxH,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAyH,WAAWA,CAAA;IACT,IAAI,CAACrI,YAAY,CAACiC,IAAI,EAAE;IACxB,IAAI,CAACjC,YAAY,CAACsI,QAAQ,EAAE;EAC9B;;;uBAhWW9I,uBAAuB,EAAAhB,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAlK,EAAA,CAAA+J,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAApK,EAAA,CAAA+J,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAtK,EAAA,CAAA+J,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAxK,EAAA,CAAA+J,iBAAA,CAAAQ,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAvBzJ,uBAAuB;MAAA0J,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBpChL,EAAA,CAAAE,SAAA,iBAAuD;UACvDF,EAAA,CAAAC,cAAA,aAA8D;UAC1DD,EAAA,CAAAc,UAAA,IAAAoK,sCAAA,iBAAmH;UAY3GlL,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAmL,UAAA,sBAAAC,+DAAAC,MAAA;YAAA,OAAYJ,GAAA,CAAA/F,WAAA,CAAAmG,MAAA,CAAmB;UAAA,EAAC;UAACrL,EAAA,CAAAsL,gBAAA,+BAAAC,wEAAAF,MAAA;YAAArL,EAAA,CAAAwL,kBAAA,CAAAP,GAAA,CAAA9I,WAAA,EAAAkJ,MAAA,MAAAJ,GAAA,CAAA9I,WAAA,GAAAkJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UACzFrL,EAAA,CAAAc,UAAA,IAAA2K,6CAAA,wBAAmF;UAQ3FzL,EADI,CAAAG,YAAA,EAAY,EACV;UASsBH,EAR5B,CAAAC,cAAA,aAAqD,aACL,aACmD,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAS,MAAA,IAA6C;;UAC5FT,EAD4F,CAAAG,YAAA,EAAK,EAC3F;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAS,MAAA,IACJ;UAAAT,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACE;UAAAD,EAAA,CAAAS,MAAA,cAAM;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAS,MAAA,IAE1D;UAAAT,EAAA,CAAAG,YAAA,EAAK;UAKDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAS,MAAA,sBAAc;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAS,MAAA,IAElE;UAAAT,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADJ,CAAAC,cAAA,cAA0C,gBACE;UAAAD,EAAA,CAAAS,MAAA,oBAAY;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAS,MAAA,IAMhE;UAIhBT,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAS,MAAA,mBAAW;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAS,MAAA,gBAAO;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAS,MAAA,IAAuC;UAChET,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAS,MAAA,qBAAa;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAS,MAAA,cAAK;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAS,MAAA,IAA4C;UACrET,EADqE,CAAAG,YAAA,EAAO,EACvE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAS,MAAA,qBAAa;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAS,MAAA,qBAChD;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBACmB;UAAAD,EAAA,CAAAS,MAAA,IACP;UAChBT,EADgB,CAAAG,YAAA,EAAO,EAClB;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAS,MAAA,YAAI;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAS,MAAA,cAAK;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAS,MAAA,IAA6C;UACtET,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAS,MAAA,gBAAQ;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAS,MAAA,gBAAO;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAS,MAAA,IAA2C;UAIhFT,EAJgF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,eAC6C,cAC9B;UAAAD,EAAA,CAAAS,MAAA,mBAAW;UAAAT,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAC,cAAA,eAAoC;UAChCD,EAAA,CAAAE,SAAA,oBAEqD;UACrDF,EAAA,CAAAC,cAAA,kBACkE;UADpCD,EAAA,CAAAmL,UAAA,mBAAAO,0DAAA;YAAA,OAAST,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAKtEnJ,EAJkF,CAAAG,YAAA,EAAS,EACzE,EACJ,EACH,EACL;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAAmL,UAAA,mBAAAQ,4DAAA;YAAA,OAASV,GAAA,CAAArB,aAAA,EAAe;UAAA,EAAC;UAH7B5J,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAAE,SAAA,qBAA+B;UAKnDF,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAE,SAAA,uBAAmC;;;UA1HJF,EAAA,CAAAK,UAAA,cAAa;UAEmDL,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAK,UAAA,UAAA4K,GAAA,CAAAzI,eAAA,CAAsB;UAY9FxC,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAK,UAAA,oBAAmB;UAAkCL,EAAA,CAAA4L,gBAAA,gBAAAX,GAAA,CAAA9I,WAAA,CAA6B;UAC7DnC,EAAA,CAAAI,SAAA,EAAS;UAATJ,EAAA,CAAAK,UAAA,YAAA4K,GAAA,CAAAlH,KAAA,CAAS;UAWe/D,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAA6L,WAAA,iBAAAZ,GAAA,CAAA7I,eAAA,CAAsC;UAM3BpC,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA8L,WAAA,SAAAb,GAAA,CAAAxJ,cAAA,kBAAAwJ,GAAA,CAAAxJ,cAAA,CAAA2E,YAAA,EAA6C;UAIpFpG,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAA+L,kBAAA,OAAAd,GAAA,CAAAxJ,cAAA,kBAAAwJ,GAAA,CAAAxJ,cAAA,CAAA2E,YAAA,cACJ;UAG8DpG,EAAA,CAAAI,SAAA,GAE1D;UAF0DJ,EAAA,CAAA+L,kBAAA,SAAAd,GAAA,CAAAxJ,cAAA,kBAAAwJ,GAAA,CAAAxJ,cAAA,CAAAI,KAAA,cAE1D;UAKkE7B,EAAA,CAAAI,SAAA,GAElE;UAFkEJ,EAAA,CAAA+L,kBAAA,QAAAd,GAAA,CAAAhJ,YAAA,aAElE;UAEgEjC,EAAA,CAAAI,SAAA,GAMhE;UANgEJ,EAAA,CAAA+L,kBAAA,UAAAd,GAAA,CAAAxJ,cAAA,kBAAAwJ,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,kBAAAf,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,qBAAAf,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,IAAAC,uBAAA,CAAAC,UAAA,oBAAAjB,GAAA,CAAAxJ,cAAA,kBAAAwJ,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,kBAAAf,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,qBAAAf,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAxJ,cAAA,CAAAuK,iBAAA,IAAAC,uBAAA,CAAAE,SAAA,eAMhE;UAWiBnM,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,iBAAA,EAAAqK,GAAA,CAAAvJ,cAAA,kBAAAuJ,GAAA,CAAAvJ,cAAA,qBAAAuJ,GAAA,CAAAvJ,cAAA,IAAA8E,OAAA,SAAuC;UAMvCxG,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAY,iBAAA,EAAAqK,GAAA,CAAAvJ,cAAA,kBAAAuJ,GAAA,CAAAvJ,cAAA,qBAAAuJ,GAAA,CAAAvJ,cAAA,IAAA8F,YAAA,SAA4C;UAO9CxH,EAAA,CAAAI,SAAA,GACP;UADOJ,EAAA,CAAAY,iBAAA,EAAAqK,GAAA,CAAAvJ,cAAA,kBAAAuJ,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,kBAAAf,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,qBAAAf,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,kBAAAhB,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,CAAA3F,SAAA,kBAAA2E,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,CAAA3F,SAAA,qBAAA2E,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,CAAA3F,SAAA,IAAAmB,aAAA,kBAAAwD,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,CAAA3F,SAAA,IAAAmB,aAAA,qBAAAwD,GAAA,CAAAvJ,cAAA,CAAAsK,iBAAA,IAAAC,uBAAA,CAAA3F,SAAA,IAAAmB,aAAA,IAAAD,YAAA,SACP;UAKSxH,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAY,iBAAA,EAAAqK,GAAA,CAAAvJ,cAAA,kBAAAuJ,GAAA,CAAAvJ,cAAA,qBAAAuJ,GAAA,CAAAvJ,cAAA,IAAA4F,aAAA,SAA6C;UAM7CtH,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAY,iBAAA,EAAAqK,GAAA,CAAAvJ,cAAA,kBAAAuJ,GAAA,CAAAvJ,cAAA,qBAAAuJ,GAAA,CAAAvJ,cAAA,IAAAgG,WAAA,SAA2C;UAK1E1H,EAAA,CAAAI,SAAA,EAA4B;UAA5BJ,EAAA,CAAAK,UAAA,cAAA4K,GAAA,CAAAtI,cAAA,CAA4B;UAiBJ3C,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA6L,WAAA,gBAAAZ,GAAA,CAAA7I,eAAA,CAAqC;UAF/DpC,EAD8B,CAAAK,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}