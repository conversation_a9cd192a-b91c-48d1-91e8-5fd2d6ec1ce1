{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/activities/activities.service\";\nimport * as i2 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nexport class OpportunityFollowItemDetailComponent {\n  constructor(activitiesservice, opportunitiesservice, router) {\n    this.activitiesservice = activitiesservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.router = router;\n    this.followupDetail = null;\n    this.unsubscribe$ = new Subject();\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n  }\n  ngOnInit() {\n    this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    this.followupdata = history.state.followupdata;\n    this.opportunitiesservice.getOpportunityByID(this.followupdata?.opportunity?.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (!response?.data?.length) return;\n      this.followupDetail = response.data[0];\n    });\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  goToBack() {\n    this.activitiesservice.getActivityByID(this.followupdata?.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.router.navigate(['/store/activities/calls', this.followupdata.activity_id, 'follow-items']);\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunityFollowItemDetailComponent_Factory(t) {\n      return new (t || OpportunityFollowItemDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunityFollowItemDetailComponent,\n      selectors: [[\"app-opportunity-follow-item-detail\"]],\n      decls: 219,\n      vars: 31,\n      consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"pt-0\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"filter-sec\", \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"pt-0\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mb-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"]],\n      template: function OpportunityFollowItemDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n          i0.ɵɵtext(5, \"Follow Up Opportunities Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function OpportunityFollowItemDetailComponent_Template_button_click_7_listener() {\n            return ctx.goToBack();\n          });\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Back \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 2)(13, \"div\", 9)(14, \"h4\", 4);\n          i0.ɵɵtext(15, \"Follow Up Opportunities Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"ul\", 11)(18, \"li\", 12)(19, \"div\", 13)(20, \"i\", 14);\n          i0.ɵɵtext(21, \"tag\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 15)(23, \"h6\", 16);\n          i0.ɵɵtext(24, \"Opportunity ID #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 17);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"li\", 12)(28, \"div\", 13)(29, \"i\", 14);\n          i0.ɵɵtext(30, \"badge\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"h6\", 16);\n          i0.ɵɵtext(33, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 17);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 12)(37, \"div\", 13)(38, \"i\", 14);\n          i0.ɵɵtext(39, \"attach_money\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 15)(41, \"h6\", 16);\n          i0.ɵɵtext(42, \"Expected Value\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\", 17);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"li\", 12)(46, \"div\", 13)(47, \"i\", 14);\n          i0.ɵɵtext(48, \"event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"h6\", 16);\n          i0.ɵɵtext(51, \"Expected Decision Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\", 17);\n          i0.ɵɵtext(53);\n          i0.ɵɵpipe(54, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"li\", 12)(56, \"div\", 13)(57, \"i\", 14);\n          i0.ɵɵtext(58, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 15)(60, \"h6\", 16);\n          i0.ɵɵtext(61, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\", 17);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"li\", 12)(65, \"div\", 13)(66, \"i\", 14);\n          i0.ɵɵtext(67, \"contact_phone\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 15)(69, \"h6\", 16);\n          i0.ɵɵtext(70, \"Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\", 17);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"li\", 12)(74, \"div\", 13)(75, \"i\", 14);\n          i0.ɵɵtext(76, \"category\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 15)(78, \"h6\", 16);\n          i0.ɵɵtext(79, \"Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"p\", 17);\n          i0.ɵɵtext(81);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"li\", 12)(83, \"div\", 13)(84, \"i\", 14);\n          i0.ɵɵtext(85, \"account_tree\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 15)(87, \"h6\", 16);\n          i0.ɵɵtext(88, \"Parent Opportunity\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"p\", 17);\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(91, \"li\", 12)(92, \"div\", 13)(93, \"i\", 14);\n          i0.ɵɵtext(94, \"source\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 15)(96, \"h6\", 16);\n          i0.ɵɵtext(97, \"Source\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"p\", 17);\n          i0.ɵɵtext(99);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(100, \"li\", 12)(101, \"div\", 13)(102, \"i\", 14);\n          i0.ɵɵtext(103, \"scale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"div\", 15)(105, \"h6\", 16);\n          i0.ɵɵtext(106, \"Weighted Value\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"p\", 17);\n          i0.ɵɵtext(108);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(109, \"li\", 12)(110, \"div\", 13)(111, \"i\", 14);\n          i0.ɵɵtext(112, \"lens\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 15)(114, \"h6\", 16);\n          i0.ɵɵtext(115, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p\", 17);\n          i0.ɵɵtext(117);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(118, \"li\", 12)(119, \"div\", 13)(120, \"i\", 14);\n          i0.ɵɵtext(121, \"fact_check\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(122, \"div\", 15)(123, \"h6\", 16);\n          i0.ɵɵtext(124, \"Reason for Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"p\", 17);\n          i0.ɵɵtext(126);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(127, \"li\", 12)(128, \"div\", 13)(129, \"i\", 14);\n          i0.ɵɵtext(130, \"track_changes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(131, \"div\", 15)(132, \"h6\", 16);\n          i0.ɵɵtext(133, \"Days in Sale Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"p\", 17);\n          i0.ɵɵtext(135);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(136, \"li\", 12)(137, \"div\", 13)(138, \"i\", 14);\n          i0.ɵɵtext(139, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(140, \"div\", 15)(141, \"h6\", 16);\n          i0.ɵɵtext(142, \"Probability\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"p\", 17);\n          i0.ɵɵtext(144);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(145, \"li\", 12)(146, \"div\", 13)(147, \"i\", 14);\n          i0.ɵɵtext(148, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(149, \"div\", 15)(150, \"h6\", 16);\n          i0.ɵɵtext(151, \"Owner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"p\", 17);\n          i0.ɵɵtext(153);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(154, \"li\", 12)(155, \"div\", 13)(156, \"i\", 14);\n          i0.ɵɵtext(157, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(158, \"div\", 15)(159, \"h6\", 16);\n          i0.ɵɵtext(160, \"Sales Organization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"p\", 17);\n          i0.ɵɵtext(162);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(163, \"li\", 12)(164, \"div\", 13)(165, \"i\", 14);\n          i0.ɵɵtext(166, \"sell\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(167, \"div\", 15)(168, \"h6\", 16);\n          i0.ɵɵtext(169, \"Sales Unit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"p\", 17);\n          i0.ɵɵtext(171);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(172, \"li\", 12)(173, \"div\", 13)(174, \"i\", 14);\n          i0.ɵɵtext(175, \"event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(176, \"div\", 15)(177, \"h6\", 16);\n          i0.ɵɵtext(178, \"Create Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(179, \"p\", 17);\n          i0.ɵɵtext(180);\n          i0.ɵɵpipe(181, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(182, \"li\", 12)(183, \"div\", 13)(184, \"i\", 14);\n          i0.ɵɵtext(185, \"event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(186, \"div\", 15)(187, \"h6\", 16);\n          i0.ɵɵtext(188, \"Last Updated Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(189, \"p\", 17);\n          i0.ɵɵtext(190);\n          i0.ɵɵpipe(191, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(192, \"li\", 12)(193, \"div\", 13)(194, \"i\", 14);\n          i0.ɵɵtext(195, \"update\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"div\", 15)(197, \"h6\", 16);\n          i0.ɵɵtext(198, \"Last Updated By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(199, \"p\", 17);\n          i0.ɵɵtext(200);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(201, \"li\", 12)(202, \"div\", 13)(203, \"i\", 14);\n          i0.ɵɵtext(204, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(205, \"div\", 15)(206, \"h6\", 16);\n          i0.ɵɵtext(207, \"Progress\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(208, \"p\", 17);\n          i0.ɵɵtext(209);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(210, \"li\", 12)(211, \"div\", 13)(212, \"i\", 14);\n          i0.ɵɵtext(213, \"help\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(214, \"div\", 15)(215, \"h6\", 16);\n          i0.ɵɵtext(216, \"Need Help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(217, \"p\", 17);\n          i0.ɵɵtext(218);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.opportunity_id) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.name) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.expected_revenue_amount) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.expected_revenue_end_date) ? i0.ɵɵpipeBind2(54, 22, ctx.followupDetail.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.business_partner == null ? null : ctx.followupDetail.business_partner.bp_full_name) || \"-\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.business_partner_contact == null ? null : ctx.followupDetail.business_partner_contact.bp_full_name) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunityCategory\", ctx.followupDetail == null ? null : ctx.followupDetail.group_code) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.parent_opportunity) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunitySource\", ctx.followupDetail == null ? null : ctx.followupDetail.origin_type_code) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.weighted_expected_net_amount) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"opportunityStatus\", ctx.followupDetail == null ? null : ctx.followupDetail.life_cycle_status_code) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.result_reason_code) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.days_in_sales_status) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.probability_percent) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.business_partner_owner == null ? null : ctx.followupDetail.business_partner_owner.bp_full_name) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.sales_organisation_id) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.sales_unit_party_id) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.followupDetail == null ? null : ctx.followupDetail.expected_revenue_start_date) ? i0.ɵɵpipeBind2(181, 25, ctx.followupDetail.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.last_change_date) ? i0.ɵɵpipeBind2(191, 28, ctx.followupDetail.last_change_date, \"MM/dd/yyyy hh:mm a\") : \"-\", \"\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.last_changed_by) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.progress) || \"-\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.followupDetail == null ? null : ctx.followupDetail.need_help) || \"-\", \"\");\n        }\n      },\n      dependencies: [i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "OpportunityFollowItemDetailComponent", "constructor", "activitiesservice", "opportunitiesservice", "router", "followupDetail", "unsubscribe$", "dropdowns", "opportunityCategory", "opportunityStatus", "opportunitySource", "ngOnInit", "loadOpportunityDropDown", "followupdata", "history", "state", "getOpportunityByID", "opportunity", "opportunity_id", "pipe", "subscribe", "response", "data", "length", "target", "type", "getOpportunityDropdownOptions", "res", "map", "attr", "label", "description", "value", "code", "getLabelFromDropdown", "dropdownKey", "item", "find", "opt", "goToBack", "getActivityByID", "activity_id", "navigate", "ngOnDestroy", "next", "complete", "i0", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "OpportunitiesService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "OpportunityFollowItemDetailComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "OpportunityFollowItemDetailComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "expected_revenue_amount", "expected_revenue_end_date", "ɵɵpipeBind2", "business_partner", "bp_full_name", "business_partner_contact", "group_code", "parent_opportunity", "origin_type_code", "weighted_expected_net_amount", "life_cycle_status_code", "result_reason_code", "days_in_sales_status", "probability_percent", "business_partner_owner", "sales_organisation_id", "sales_unit_party_id", "expected_revenue_start_date", "last_change_date", "last_changed_by", "progress", "need_help"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\opportunity-follow-item-detail\\opportunity-follow-item-detail.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\opportunity-follow-item-detail\\opportunity-follow-item-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-opportunity-follow-item-detail',\r\n  templateUrl: './opportunity-follow-item-detail.component.html',\r\n  styleUrl: './opportunity-follow-item-detail.component.scss',\r\n})\r\nexport class OpportunityFollowItemDetailComponent implements OnInit {\r\n  public followupDetail: any = null;\r\n  public followupdata: any;\r\n  private unsubscribe$ = new Subject<void>();\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadOpportunityDropDown(\r\n      'opportunityCategory',\r\n      'CRM_OPPORTUNITY_GROUP'\r\n    );\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.loadOpportunityDropDown(\r\n      'opportunitySource',\r\n      'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n    );\r\n    this.followupdata = history.state.followupdata;\r\n    this.opportunitiesservice\r\n      .getOpportunityByID(this.followupdata?.opportunity?.opportunity_id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response) => {\r\n        if (!response?.data?.length) return;\r\n        this.followupDetail = response.data[0];\r\n      });\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  goToBack() {\r\n    this.activitiesservice\r\n      .getActivityByID(this.followupdata?.activity_id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(() => {\r\n        this.router.navigate([\r\n          '/store/activities/calls',\r\n          this.followupdata.activity_id,\r\n          'follow-items',\r\n        ]);\r\n      });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"grid mt-0 relative\">\r\n    <div class=\"col-12 pt-0\">\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-between gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Follow Up Opportunities Details</h4>\r\n                <div class=\"filter-sec flex align-items-center gap-2\">\r\n                    <button type=\"button\" (click)=\"goToBack()\"\r\n                        class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                        <span class=\"material-symbols-rounded text-2xl\">arrow_back</span> Back\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:flex-1 md:flex-1 pt-0\">\r\n        <div class=\"p-3 mb-4 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <h4 class=\"m-0 ml-0 pl-3 left-border relative flex\">Follow Up Opportunities Information</h4>\r\n            </div>\r\n\r\n            <div class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <ul class=\"order-details-list m-0 p-0 d-grid gap-5\">\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">tag</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Opportunity ID #</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ followupDetail?.opportunity_id || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">badge</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Name</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ followupDetail?.name || \"-\" }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">attach_money</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Expected Value</h6>\r\n                            <p class=\"m-0 font-medium text-400\">\r\n                                {{ followupDetail?.expected_revenue_amount || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">event</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Expected Decision Date</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ followupDetail?.expected_revenue_end_date ?\r\n                                (followupDetail.expected_revenue_end_date | date: 'MM/dd/yyyy') : '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">account_circle</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Account</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ followupDetail?.business_partner?.bp_full_name || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">contact_phone</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Contact</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                followupDetail?.business_partner_contact?.bp_full_name || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">category</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Category</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                getLabelFromDropdown('opportunityCategory',followupDetail?.group_code)\r\n                                || '-'}}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">account_tree</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Parent Opportunity</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.parent_opportunity || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">source</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Source</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ getLabelFromDropdown('opportunitySource',\r\n                                followupDetail?.origin_type_code) || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">scale</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Weighted Value</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.weighted_expected_net_amount || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">lens</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Status</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ getLabelFromDropdown('opportunityStatus',\r\n                                followupDetail?.life_cycle_status_code) || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">fact_check</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Reason for Status</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.result_reason_code || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">track_changes</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Days in Sale Status</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.days_in_sales_status || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">trending_up</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Probability</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.probability_percent || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">account_circle</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Owner</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                followupDetail?.business_partner_owner?.bp_full_name || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">business</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Sales Organization</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                followupDetail?.sales_organisation_id || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">sell</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Sales Unit</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                followupDetail?.sales_unit_party_id || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">event</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Create Date</h6>\r\n                            <p class=\"m-0 font-medium text-400\">{{ followupDetail?.expected_revenue_start_date ?\r\n                                (followupDetail.expected_revenue_start_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">event</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Last Updated Date</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.last_change_date ?\r\n                                (followupDetail.last_change_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">update</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Last Updated By</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.last_changed_by || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">trending_up</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Progress</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{ followupDetail?.progress || '-' }}</p>\r\n                        </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center gap-3 mb-3\">\r\n                        <div\r\n                            class=\"icon flex align-items-center justify-content-center w-3rem h-3rem border-circle bg-blue-200\">\r\n                            <i class=\"material-symbols-rounded\">help</i>\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            <h6 class=\"m-0\">Need Help</h6>\r\n                            <p class=\"m-0 font-medium text-400\"> {{\r\n                                followupDetail?.need_help || '-'\r\n                                }}</p>\r\n                        </div>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;AAUzC,OAAM,MAAOC,oCAAoC;EAW/CC,YACUC,iBAAoC,EACpCC,oBAA0C,EAC1CC,MAAc;IAFd,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;IAbT,KAAAC,cAAc,GAAQ,IAAI;IAEzB,KAAAC,YAAY,GAAG,IAAIR,OAAO,EAAQ;IAEnC,KAAAS,SAAS,GAA0B;MACxCC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;IACD,IAAI,CAACA,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACD,IAAI,CAACC,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACF,YAAY;IAC9C,IAAI,CAACV,oBAAoB,CACtBa,kBAAkB,CAAC,IAAI,CAACH,YAAY,EAAEI,WAAW,EAAEC,cAAc,CAAC,CAClEC,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACO,YAAY,CAAC,CAAC,CAClCc,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACA,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAE;MAC7B,IAAI,CAAClB,cAAc,GAAGgB,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;EACN;EAEAV,uBAAuBA,CAACY,MAAc,EAAEC,IAAY;IAClD,IAAI,CAACtB,oBAAoB,CACtBuB,6BAA6B,CAACD,IAAI,CAAC,CACnCL,SAAS,CAAEO,GAAQ,IAAI;MACtB,IAAI,CAACpB,SAAS,CAACiB,MAAM,CAAC,GACpBG,GAAG,EAAEL,IAAI,EAAEM,GAAG,CAAEC,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAC,oBAAoBA,CAACC,WAAmB,EAAEH,KAAa;IACrD,MAAMI,IAAI,GAAG,IAAI,CAAC7B,SAAS,CAAC4B,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACN,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOI,IAAI,EAAEN,KAAK,IAAIE,KAAK;EAC7B;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACrC,iBAAiB,CACnBsC,eAAe,CAAC,IAAI,CAAC3B,YAAY,EAAE4B,WAAW,CAAC,CAC/CtB,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACO,YAAY,CAAC,CAAC,CAClCc,SAAS,CAAC,MAAK;MACd,IAAI,CAAChB,MAAM,CAACsC,QAAQ,CAAC,CACnB,yBAAyB,EACzB,IAAI,CAAC7B,YAAY,CAAC4B,WAAW,EAC7B,cAAc,CACf,CAAC;IACJ,CAAC,CAAC;EACN;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACrC,YAAY,CAACsC,IAAI,EAAE;IACxB,IAAI,CAACtC,YAAY,CAACuC,QAAQ,EAAE;EAC9B;;;uBAxEW7C,oCAAoC,EAAA8C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApCrD,oCAAoC;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPjCd,EAJhB,CAAAgB,cAAA,aAAgC,aACH,aACuC,aACwB,YACxB;UAAAhB,EAAA,CAAAiB,MAAA,sCAA+B;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAEpFlB,EADJ,CAAAgB,cAAA,aAAsD,gBAEwF;UADpHhB,EAAA,CAAAmB,UAAA,mBAAAC,sEAAA;YAAA,OAASL,GAAA,CAAAtB,QAAA,EAAU;UAAA,EAAC;UAEtCO,EAAA,CAAAgB,cAAA,cAAgD;UAAAhB,EAAA,CAAAiB,MAAA,iBAAU;UAAAjB,EAAA,CAAAkB,YAAA,EAAO;UAAClB,EAAA,CAAAiB,MAAA,cACtE;UAIhBjB,EAJgB,CAAAkB,YAAA,EAAS,EACP,EACJ,EACJ,EACJ;UAIMlB,EAHZ,CAAAgB,cAAA,cAA6C,cACmB,cACsB,aACtB;UAAAhB,EAAA,CAAAiB,MAAA,2CAAmC;UAC3FjB,EAD2F,CAAAkB,YAAA,EAAK,EAC1F;UAOUlB,EALhB,CAAAgB,cAAA,eAAiG,cACzC,cACD,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,WAAG;UAC3CjB,EAD2C,CAAAkB,YAAA,EAAI,EACzC;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,wBAAgB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACrClB,EAAA,CAAAgB,cAAA,aAAoC;UAAAhB,EAAA,CAAAiB,MAAA,IAA2C;UAEvFjB,EAFuF,CAAAkB,YAAA,EAAI,EACjF,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,aAAK;UAC7CjB,EAD6C,CAAAkB,YAAA,EAAI,EAC3C;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,YAAI;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACzBlB,EAAA,CAAAgB,cAAA,aAAoC;UAAAhB,EAAA,CAAAiB,MAAA,IAAiC;UAE7EjB,EAF6E,CAAAkB,YAAA,EAAI,EACvE,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAY;UACpDjB,EADoD,CAAAkB,YAAA,EAAI,EAClD;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,sBAAc;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACnClB,EAAA,CAAAgB,cAAA,aAAoC;UAChChB,EAAA,CAAAiB,MAAA,IAAoD;UAEhEjB,EAFgE,CAAAkB,YAAA,EAAI,EAC1D,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,aAAK;UAC7CjB,EAD6C,CAAAkB,YAAA,EAAI,EAC3C;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,8BAAsB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC3ClB,EAAA,CAAAgB,cAAA,aAAoC;UAAAhB,EAAA,CAAAiB,MAAA,IAE9B;;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,sBAAc;UACtDjB,EADsD,CAAAkB,YAAA,EAAI,EACpD;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC5BlB,EAAA,CAAAgB,cAAA,aAAoC;UAAAhB,EAAA,CAAAiB,MAAA,IAC9B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,qBAAa;UACrDjB,EADqD,CAAAkB,YAAA,EAAI,EACnD;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC5BlB,EAAA,CAAAgB,cAAA,aAAoC;UAAChB,EAAA,CAAAiB,MAAA,IAC+B;UAE5EjB,EAF4E,CAAAkB,YAAA,EAAI,EACtE,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,gBAAQ;UAChDjB,EADgD,CAAAkB,YAAA,EAAI,EAC9C;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC7BlB,EAAA,CAAAgB,cAAA,aAAoC;UAAChB,EAAA,CAAAiB,MAAA,IAEzB;UAEpBjB,EAFoB,CAAAkB,YAAA,EAAI,EACd,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAY;UACpDjB,EADoD,CAAAkB,YAAA,EAAI,EAClD;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,0BAAkB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACvClB,EAAA,CAAAgB,cAAA,aAAoC;UAAChB,EAAA,CAAAiB,MAAA,IAC/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,cAA+C,eAE6D,aAChE;UAAAhB,EAAA,CAAAiB,MAAA,cAAM;UAC9CjB,EAD8C,CAAAkB,YAAA,EAAI,EAC5C;UAEFlB,EADJ,CAAAgB,cAAA,eAAkB,cACE;UAAAhB,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC3BlB,EAAA,CAAAgB,cAAA,aAAoC;UAAChB,EAAA,CAAAiB,MAAA,IACU;UAEvDjB,EAFuD,CAAAkB,YAAA,EAAI,EACjD,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,cAAK;UAC7CjB,EAD6C,CAAAkB,YAAA,EAAI,EAC3C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,uBAAc;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACnClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAC/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,aAAI;UAC5CjB,EAD4C,CAAAkB,YAAA,EAAI,EAC1C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,eAAM;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC3BlB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KACgB;UAE7DjB,EAF6D,CAAAkB,YAAA,EAAI,EACvD,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,mBAAU;UAClDjB,EADkD,CAAAkB,YAAA,EAAI,EAChD;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,0BAAiB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACtClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAC/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,sBAAa;UACrDjB,EADqD,CAAAkB,YAAA,EAAI,EACnD;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,4BAAmB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACxClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAC/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAW;UACnDjB,EADmD,CAAAkB,YAAA,EAAI,EACjD;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAW;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAChClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAC/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,uBAAc;UACtDjB,EADsD,CAAAkB,YAAA,EAAI,EACpD;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,cAAK;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC1BlB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAE/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,iBAAQ;UAChDjB,EADgD,CAAAkB,YAAA,EAAI,EAC9C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,2BAAkB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACvClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAE/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,aAAI;UAC5CjB,EAD4C,CAAAkB,YAAA,EAAI,EAC1C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,mBAAU;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC/BlB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAE/B;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,cAAK;UAC7CjB,EAD6C,CAAAkB,YAAA,EAAI,EAC3C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAW;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAChClB,EAAA,CAAAgB,cAAA,cAAoC;UAAAhB,EAAA,CAAAiB,MAAA,KAE9B;;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,cAAK;UAC7CjB,EAD6C,CAAAkB,YAAA,EAAI,EAC3C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,0BAAiB;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACtClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAE/B;;UAEdjB,EAFc,CAAAkB,YAAA,EAAI,EACR,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,eAAM;UAC9CjB,EAD8C,CAAAkB,YAAA,EAAI,EAC5C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,wBAAe;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UACpClB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAA4C;UAEzFjB,EAFyF,CAAAkB,YAAA,EAAI,EACnF,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,oBAAW;UACnDjB,EADmD,CAAAkB,YAAA,EAAI,EACjD;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,iBAAQ;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC7BlB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAAqC;UAElFjB,EAFkF,CAAAkB,YAAA,EAAI,EAC5E,EACL;UAIGlB,EAHR,CAAAgB,cAAA,eAA+C,gBAE6D,cAChE;UAAAhB,EAAA,CAAAiB,MAAA,aAAI;UAC5CjB,EAD4C,CAAAkB,YAAA,EAAI,EAC1C;UAEFlB,EADJ,CAAAgB,cAAA,gBAAkB,eACE;UAAAhB,EAAA,CAAAiB,MAAA,kBAAS;UAAAjB,EAAA,CAAAkB,YAAA,EAAK;UAC9BlB,EAAA,CAAAgB,cAAA,cAAoC;UAAChB,EAAA,CAAAiB,MAAA,KAE/B;UAOlCjB,EAPkC,CAAAkB,YAAA,EAAI,EACR,EACL,EACJ,EACH,EACJ,EACJ,EACJ;;;UAnP0DlB,EAAA,CAAAqB,SAAA,IAA2C;UAA3CrB,EAAA,CAAAsB,iBAAA,EAAAP,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAa,cAAA,SAA2C;UAU3C4B,EAAA,CAAAqB,SAAA,GAAiC;UAAjCrB,EAAA,CAAAsB,iBAAA,EAAAP,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAgE,IAAA,SAAiC;UAWjEvB,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAkE,uBAAA,aAAoD;UAUpBzB,EAAA,CAAAqB,SAAA,GAE9B;UAF8BrB,EAAA,CAAAsB,iBAAA,EAAAP,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAmE,yBAAA,IAAA1B,EAAA,CAAA2B,WAAA,SAAAZ,GAAA,CAAAxD,cAAA,CAAAmE,yBAAA,sBAE9B;UAU8B1B,EAAA,CAAAqB,SAAA,IAC9B;UAD8BrB,EAAA,CAAAsB,iBAAA,EAAAP,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAqE,gBAAA,kBAAAb,GAAA,CAAAxD,cAAA,CAAAqE,gBAAA,CAAAC,YAAA,SAC9B;UAU+B7B,EAAA,CAAAqB,SAAA,GAC+B;UAD/BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAuE,wBAAA,kBAAAf,GAAA,CAAAxD,cAAA,CAAAuE,wBAAA,CAAAD,YAAA,aAC+B;UAU/B7B,EAAA,CAAAqB,SAAA,GAEzB;UAFyBrB,EAAA,CAAAwB,kBAAA,MAAAT,GAAA,CAAA3B,oBAAA,wBAAA2B,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAwE,UAAA,aAEzB;UAUyB/B,EAAA,CAAAqB,SAAA,GAC/B;UAD+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAyE,kBAAA,aAC/B;UAU+BhC,EAAA,CAAAqB,SAAA,GACU;UADVrB,EAAA,CAAAwB,kBAAA,MAAAT,GAAA,CAAA3B,oBAAA,sBAAA2B,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA0E,gBAAA,aACU;UAUVjC,EAAA,CAAAqB,SAAA,GAC/B;UAD+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA2E,4BAAA,aAC/B;UAU+BlC,EAAA,CAAAqB,SAAA,GACgB;UADhBrB,EAAA,CAAAwB,kBAAA,MAAAT,GAAA,CAAA3B,oBAAA,sBAAA2B,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA4E,sBAAA,aACgB;UAUhBnC,EAAA,CAAAqB,SAAA,GAC/B;UAD+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA6E,kBAAA,aAC/B;UAU+BpC,EAAA,CAAAqB,SAAA,GAC/B;UAD+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA8E,oBAAA,aAC/B;UAU+BrC,EAAA,CAAAqB,SAAA,GAC/B;UAD+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAA+E,mBAAA,aAC/B;UAU+BtC,EAAA,CAAAqB,SAAA,GAE/B;UAF+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAgF,sBAAA,kBAAAxB,GAAA,CAAAxD,cAAA,CAAAgF,sBAAA,CAAAV,YAAA,aAE/B;UAU+B7B,EAAA,CAAAqB,SAAA,GAE/B;UAF+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAiF,qBAAA,aAE/B;UAU+BxC,EAAA,CAAAqB,SAAA,GAE/B;UAF+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAkF,mBAAA,aAE/B;UAU8BzC,EAAA,CAAAqB,SAAA,GAE9B;UAF8BrB,EAAA,CAAAsB,iBAAA,EAAAP,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAmF,2BAAA,IAAA1C,EAAA,CAAA2B,WAAA,UAAAZ,GAAA,CAAAxD,cAAA,CAAAmF,2BAAA,8BAE9B;UAU+B1C,EAAA,CAAAqB,SAAA,IAE/B;UAF+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAoF,gBAAA,IAAA3C,EAAA,CAAA2B,WAAA,UAAAZ,GAAA,CAAAxD,cAAA,CAAAoF,gBAAA,kCAE/B;UAU+B3C,EAAA,CAAAqB,SAAA,IAA4C;UAA5CrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAqF,eAAA,aAA4C;UAU5C5C,EAAA,CAAAqB,SAAA,GAAqC;UAArCrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAsF,QAAA,aAAqC;UAUrC7C,EAAA,CAAAqB,SAAA,GAE/B;UAF+BrB,EAAA,CAAAwB,kBAAA,OAAAT,GAAA,CAAAxD,cAAA,kBAAAwD,GAAA,CAAAxD,cAAA,CAAAuF,SAAA,aAE/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}