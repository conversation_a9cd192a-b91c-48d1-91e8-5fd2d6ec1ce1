{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccountComponent } from './account.component';\nimport { AccountDetailsComponent } from './account-details/account-details.component';\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\nimport { AccountPartnersComponent } from './account-details/account-partners/account-partners.component';\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AccountComponent\n}, {\n  path: ':id',\n  component: AccountDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: AccountOverviewComponent\n  }, {\n    path: 'contacts',\n    component: AccountContactsComponent\n  }, {\n    path: 'partners',\n    component: AccountPartnersComponent\n  }, {\n    path: 'sales-team',\n    component: AccountSalesTeamComponent\n  }, {\n    path: 'opportunities',\n    component: AccountOpportunitiesComponent\n  }, {\n    path: 'ai-insights',\n    component: AccountAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: AccountOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: AccountAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: AccountNotesComponent\n  }, {\n    path: 'activities',\n    component: AccountActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: AccountRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: AccountTicketsComponent\n  }, {\n    path: 'sales-quotes',\n    component: AccountSalesQuotesComponent\n  }, {\n    path: 'sales-orders',\n    component: AccountSalesOrdersComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class AccountRoutingModule {\n  static {\n    this.ɵfac = function AccountRoutingModule_Factory(t) {\n      return new (t || AccountRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccountRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccountComponent", "AccountDetailsComponent", "AccountOverviewComponent", "AccountContactsComponent", "AccountSalesTeamComponent", "AccountAiInsightsComponent", "AccountOrganizationDataComponent", "AccountAttachmentsComponent", "AccountNotesComponent", "AccountPartnersComponent", "AccountOpportunitiesComponent", "AccountActivitiesComponent", "AccountRelationshipsComponent", "AccountTicketsComponent", "AccountSalesQuotesComponent", "AccountSalesOrdersComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "AccountRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AccountComponent } from './account.component';\r\nimport { AccountDetailsComponent } from './account-details/account-details.component';\r\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\r\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\r\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\r\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\r\nimport { AccountPartnersComponent } from './account-details/account-partners/account-partners.component';\r\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\r\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\r\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\r\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: AccountComponent },\r\n  {\r\n    path: ':id',\r\n    component: AccountDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: AccountOverviewComponent },\r\n      { path: 'contacts', component: AccountContactsComponent },\r\n      { path: 'partners', component: AccountPartnersComponent },\r\n      { path: 'sales-team', component: AccountSalesTeamComponent },\r\n      { path: 'opportunities', component: AccountOpportunitiesComponent },\r\n      { path: 'ai-insights', component: AccountAiInsightsComponent },\r\n      { path: 'organization-data', component: AccountOrganizationDataComponent },\r\n      { path: 'attachments', component: AccountAttachmentsComponent },\r\n      { path: 'notes', component: AccountNotesComponent },\r\n      { path: 'activities', component: AccountActivitiesComponent },\r\n      { path: 'relationships', component: AccountRelationshipsComponent },\r\n      { path: 'tickets', component: AccountTicketsComponent },\r\n      { path: 'sales-quotes', component: AccountSalesQuotesComponent },\r\n      { path: 'sales-orders', component: AccountSalesOrdersComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AccountRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,gCAAgC,QAAQ,iFAAiF;AAClI,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,2BAA2B,QAAQ,uEAAuE;;;AAEnH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAElB;AAAgB,CAAE,EACzC;EACEiB,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEjB,uBAAuB;EAClCkB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEhB;EAAwB,CAAE,EACzD;IAAEe,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEf;EAAwB,CAAE,EACzD;IAAEc,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAAwB,CAAE,EACzD;IAAEQ,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEd;EAAyB,CAAE,EAC5D;IAAEa,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAER;EAA6B,CAAE,EACnE;IAAEO,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEb;EAA0B,CAAE,EAC9D;IAAEY,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEZ;EAAgC,CAAE,EAC1E;IAAEW,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEX;EAA2B,CAAE,EAC/D;IAAEU,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEV;EAAqB,CAAE,EACnD;IAAES,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA0B,CAAE,EAC7D;IAAEM,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEN;EAA6B,CAAE,EACnE;IAAEK,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEL;EAAuB,CAAE,EACvD;IAAEI,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEJ;EAA2B,CAAE,EAChE;IAAEG,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEH;EAA2B,CAAE,EAChE;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBvB,YAAY,CAACwB,QAAQ,CAACP,MAAM,CAAC,EAC7BjB,YAAY;IAAA;EAAA;;;2EAEXuB,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFrB3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}