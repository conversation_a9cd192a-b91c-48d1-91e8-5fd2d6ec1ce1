{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nconst _c0 = (a0, a1, a2) => ({\n  \"p-button p-togglebutton p-component\": true,\n  \"p-button-icon-only\": a0,\n  \"p-highlight\": a1,\n  \"p-disabled\": a2\n});\nconst _c1 = (a0, a1) => ({\n  \"p-button-icon\": true,\n  \"p-button-icon-left\": a0,\n  \"p-button-icon-right\": a1\n});\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction ToggleButton_Conditional_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r0.checked ? ctx_r0.onIcon : ctx_r0.offIcon);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c1, ctx_r0.iconPos === \"left\", ctx_r0.iconPos === \"right\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction ToggleButton_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ToggleButton_Conditional_1_span_0_Template, 1, 7, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.onIcon || ctx_r0.offIcon);\n  }\n}\nfunction ToggleButton_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ToggleButton_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ToggleButton_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r0.checked));\n  }\n}\nfunction ToggleButton_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.checked ? ctx_r0.hasOnLabel ? ctx_r0.onLabel : \"\" : ctx_r0.hasOffLabel ? ctx_r0.offLabel : \"\");\n  }\n}\nconst TOGGLEBUTTON_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => ToggleButton),\n  multi: true\n};\n/**\n * ToggleButton is used to select a boolean value using a button.\n * @group Components\n */\nlet ToggleButton = /*#__PURE__*/(() => {\n  class ToggleButton {\n    cd;\n    /**\n     * Label for the on state.\n     * @group Props\n     */\n    onLabel;\n    /**\n     * Label for the off state.\n     * @group Props\n     */\n    offLabel;\n    /**\n     * Icon for the on state.\n     * @group Props\n     */\n    onIcon;\n    /**\n     * Icon for the off state.\n     * @group Props\n     */\n    offIcon;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Callback to invoke on value change.\n     * @param {ToggleButtonChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    templates;\n    iconTemplate;\n    checked = false;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    constructor(cd) {\n      this.cd = cd;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          default:\n            this.iconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    toggle(event) {\n      if (!this.disabled) {\n        this.checked = !this.checked;\n        this.onModelChange(this.checked);\n        this.onModelTouched();\n        this.onChange.emit({\n          originalEvent: event,\n          checked: this.checked\n        });\n        this.cd.markForCheck();\n      }\n    }\n    onKeyDown(event) {\n      switch (event.code) {\n        case 'Enter':\n          this.toggle(event);\n          event.preventDefault();\n          break;\n        case 'Space':\n          this.toggle(event);\n          event.preventDefault();\n          break;\n      }\n    }\n    onBlur() {\n      this.onModelTouched();\n    }\n    writeValue(value) {\n      this.checked = value;\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    get hasOnLabel() {\n      return this.onLabel && this.onLabel.length > 0;\n    }\n    get hasOffLabel() {\n      return this.onLabel && this.onLabel.length > 0;\n    }\n    static ɵfac = function ToggleButton_Factory(t) {\n      return new (t || ToggleButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ToggleButton,\n      selectors: [[\"p-toggleButton\"]],\n      contentQueries: function ToggleButton_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        onLabel: \"onLabel\",\n        offLabel: \"offLabel\",\n        onIcon: \"onIcon\",\n        offIcon: \"offIcon\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        disabled: \"disabled\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        inputId: \"inputId\",\n        tabindex: \"tabindex\",\n        iconPos: \"iconPos\"\n      },\n      outputs: {\n        onChange: \"onChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([TOGGLEBUTTON_VALUE_ACCESSOR])],\n      decls: 4,\n      vars: 16,\n      consts: [[\"role\", \"switch\", \"pRipple\", \"\", 3, \"click\", \"keydown\", \"ngClass\", \"ngStyle\"], [3, \"class\", \"ngClass\"], [\"class\", \"p-button-label\", 4, \"ngIf\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-button-label\"]],\n      template: function ToggleButton_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function ToggleButton_Template_div_click_0_listener($event) {\n            return ctx.toggle($event);\n          })(\"keydown\", function ToggleButton_Template_div_keydown_0_listener($event) {\n            return ctx.onKeyDown($event);\n          });\n          i0.ɵɵtemplate(1, ToggleButton_Conditional_1_Template, 1, 1, \"span\", 1)(2, ToggleButton_Conditional_2_Template, 1, 4)(3, ToggleButton_span_3_Template, 2, 2, \"span\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.onIcon && ctx.offIcon && !ctx.hasOnLabel && !ctx.hasOffLabel, ctx.checked, ctx.disabled))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : \"0\")(\"aria-checked\", ctx.checked)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"data-pc-name\", \"togglebutton\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, !ctx.iconTemplate ? 1 : 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.onLabel || ctx.offLabel);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n      styles: [\"@layer primeng{.p-button[_ngcontent-%COMP%]{margin:0;display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;align-items:center;vertical-align:bottom;text-align:center;overflow:hidden;position:relative}.p-button-label[_ngcontent-%COMP%]{flex:1 1 auto}.p-button-icon-right[_ngcontent-%COMP%]{order:1}.p-button[_ngcontent-%COMP%]:disabled{cursor:default;pointer-events:none}.p-button-icon-only[_ngcontent-%COMP%]{justify-content:center}.p-button-icon-only[_ngcontent-%COMP%]:after{content:\\\"p\\\";visibility:hidden;clip:rect(0 0 0 0);width:0}.p-button-vertical[_ngcontent-%COMP%]{flex-direction:column}.p-button-icon-bottom[_ngcontent-%COMP%]{order:2}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]{margin:0}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:not(:last-child){border-right:0 none}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:not(:first-of-type):not(:last-of-type){border-radius:0}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:first-of-type{border-top-right-radius:0;border-bottom-right-radius:0}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:last-of-type{border-top-left-radius:0;border-bottom-left-radius:0}.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:focus{position:relative;z-index:1}p-button[iconpos=right][_ngcontent-%COMP%]   spinnericon[_ngcontent-%COMP%]{order:1}}\"],\n      changeDetection: 0\n    });\n  }\n  return ToggleButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ToggleButtonModule = /*#__PURE__*/(() => {\n  class ToggleButtonModule {\n    static ɵfac = function ToggleButtonModule_Factory(t) {\n      return new (t || ToggleButtonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ToggleButtonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, SharedModule, SharedModule]\n    });\n  }\n  return ToggleButtonModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TOGGLEBUTTON_VALUE_ACCESSOR, ToggleButton, ToggleButtonModule };\n//# sourceMappingURL=primeng-togglebutton.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}