{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/table\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/multiselect\";\nfunction ProspectsAttachmentsComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 17);\n    i0.ɵɵlistener(\"click\", function ProspectsAttachmentsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 12)(5, ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10);\n    i0.ɵɵlistener(\"click\", function ProspectsAttachmentsComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"Title\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 11);\n    i0.ɵɵtext(3, \" File Name \");\n    i0.ɵɵtemplate(4, ProspectsAttachmentsComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 12)(5, ProspectsAttachmentsComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProspectsAttachmentsComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 14);\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \" Actions \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"Title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"Title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r5.Type, \" \");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r5.ChangedOn, \" \");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r5.ChangedBy, \" \");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 24);\n    i0.ɵɵtemplate(3, ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_3_Template, 2, 1, \"ng-container\", 25)(4, ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_4_Template, 2, 1, \"ng-container\", 25)(5, ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_5_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Type\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ChangedOn\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ChangedBy\");\n  }\n}\nfunction ProspectsAttachmentsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\", 19)(2, \"div\", 20)(3, \"i\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProspectsAttachmentsComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 14);\n    i0.ɵɵelementStart(7, \"td\")(8, \"button\", 22)(9, \"i\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tableinfo_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tableinfo_r5.FileIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r5.Title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tableinfo_r5.Action);\n  }\n}\nexport class ProspectsAttachmentsComponent {\n  constructor() {\n    this.tableData = [];\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'Type',\n      header: 'Type'\n    }, {\n      field: 'ChangedOn',\n      header: 'Changed On'\n    }, {\n      field: 'ChangedBy',\n      header: 'Changed By'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.tableData.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.tableData = [{\n      FileIcon: 'perm_media',\n      Title: 'logo-original.jpg',\n      Type: 'Standard Attachment',\n      ChangedOn: '11/28/2024 1:12 PM',\n      ChangedBy: 'Amit Asar',\n      Action: 'delete'\n    }, {\n      FileIcon: 'perm_media',\n      Title: 'logo-or.jpg',\n      Type: 'Standard File',\n      ChangedOn: '11/28/2024 1:12 PM',\n      ChangedBy: 'Amit Asar Digital',\n      Action: 'delete'\n    }, {\n      FileIcon: 'perm_media',\n      Title: 'logo-file',\n      Type: 'Standard Files',\n      ChangedOn: '11/28/2024 1:12 PM',\n      ChangedBy: 'Amit Singh',\n      Action: 'delete'\n    }];\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  static {\n    this.ɵfac = function ProspectsAttachmentsComponent_Factory(t) {\n      return new (t || ProspectsAttachmentsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsAttachmentsComponent,\n      selectors: [[\"app-prospects-attachments\"]],\n      decls: 11,\n      vars: 11,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"rounded\", \"styleClass\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\", \"text-primary\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"]],\n      template: function ProspectsAttachmentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Attachments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"p-button\", 4);\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsAttachmentsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function ProspectsAttachmentsComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, ProspectsAttachmentsComponent_ng_template_9_Template, 9, 3, \"ng-template\", 8)(10, ProspectsAttachmentsComponent_ng_template_10_Template, 11, 4, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"font-semibold px-3\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgSwitch, i1.NgSwitchCase, i2.NgControlStatus, i2.NgModel, i3.Table, i4.PrimeTemplate, i3.SortableColumn, i3.FrozenColumn, i3.ReorderableColumn, i5.Button, i6.MultiSelect],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "ProspectsAttachmentsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_4_Template", "ProspectsAttachmentsComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "ProspectsAttachmentsComponent_ng_template_9_Template_th_click_1_listener", "_r1", "ProspectsAttachmentsComponent_ng_template_9_i_4_Template", "ProspectsAttachmentsComponent_ng_template_9_i_5_Template", "ProspectsAttachmentsComponent_ng_template_9_ng_container_6_Template", "selectedColumns", "tableinfo_r5", "Type", "ChangedOn", "ChangedBy", "ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_3_Template", "ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_4_Template", "ProspectsAttachmentsComponent_ng_template_10_ng_container_6_ng_container_5_Template", "col_r6", "ProspectsAttachmentsComponent_ng_template_10_ng_container_6_Template", "ɵɵtextInterpolate", "FileIcon", "Title", "Action", "ProspectsAttachmentsComponent", "constructor", "tableData", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "selectors", "decls", "vars", "consts", "template", "ProspectsAttachmentsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ProspectsAttachmentsComponent_Template_p_multiSelect_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "ProspectsAttachmentsComponent_Template_p_table_onColReorder_8_listener", "ProspectsAttachmentsComponent_ng_template_9_Template", "ProspectsAttachmentsComponent_ng_template_10_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-attachments\\prospects-attachments.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-attachments\\prospects-attachments.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\ninterface AccountTableData {\r\n  FileIcon?: string;\r\n  Title?: string;\r\n  Type?: string;\r\n  ChangedOn?: string;\r\n  ChangedBy?: string;\r\n  Action?: string;\r\n}\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects-attachments',\r\n  templateUrl: './prospects-attachments.component.html',\r\n  styleUrl: './prospects-attachments.component.scss'\r\n})\r\nexport class ProspectsAttachmentsComponent {\r\n\r\n  tableData: AccountTableData[] = [];\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'Type', header: 'Type' },\r\n    { field: 'ChangedOn', header: 'Changed On' },\r\n    { field: 'ChangedBy', header: 'Changed By' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.tableData.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.tableData = [\r\n      {\r\n        FileIcon: 'perm_media',\r\n        Title: 'logo-original.jpg',\r\n        Type: 'Standard Attachment',\r\n        ChangedOn: '11/28/2024 1:12 PM',\r\n        ChangedBy: 'Amit Asar',\r\n        Action: 'delete',\r\n      },\r\n      {\r\n        FileIcon: 'perm_media',\r\n        Title: 'logo-or.jpg',\r\n        Type: 'Standard File',\r\n        ChangedOn: '11/28/2024 1:12 PM',\r\n        ChangedBy: 'Amit Asar Digital',\r\n        Action: 'delete',\r\n      },\r\n      {\r\n        FileIcon: 'perm_media',\r\n        Title: 'logo-file',\r\n        Type: 'Standard Files',\r\n        ChangedOn: '11/28/2024 1:12 PM',\r\n        ChangedBy: 'Amit Singh',\r\n        Action: 'delete',\r\n      },\r\n\r\n    ];\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Attachments</h4>\r\n\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" [rounded]=\"true\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"tableData\" dataKey=\"id\" [rows]=\"8\" [paginator]=\"true\" [lazy]=\"true\" responsiveLayout=\"scroll\"\r\n            [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('Title')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            File Name\r\n                            <i *ngIf=\"sortField === 'Title'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'Title'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th>\r\n                        Actions\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-tableinfo let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <i class=\"material-symbols-rounded text-primary\">{{ tableinfo.FileIcon }}</i> {{\r\n                            tableinfo.Title\r\n                            }}\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'Type'\">\r\n                                    {{ tableinfo.Type }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'ChangedOn'\">\r\n                                    {{ tableinfo.ChangedOn }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'ChangedBy'\">\r\n                                    {{ tableinfo.ChangedBy }}\r\n                                </ng-container>\r\n                                \r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td>\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\">\r\n                            <i class=\"material-symbols-rounded text-red-500\">{{ tableinfo.Action }}</i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;ICyB4BA,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA6D;;;;;IAOzDD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,wFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,uEAAA,gBACkF,IAAAC,uEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAhB7ChB,EADJ,CAAAM,cAAA,SAAI,aAC6E;IAA3DN,EAAA,CAAAO,UAAA,mBAAAmB,yEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,OAAO,CAAC;IAAA,EAAC;IAC3Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,kBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,wDAAA,gBACkF,IAAAC,wDAAA,gBAEzB;IAEjE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,mEAAA,2BAAkD;IAWlD9B,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAiB,MAAA,gBACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;IApBWrB,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,aAA2B;IAG3BzB,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,aAA2B;IAGTzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA6BpC/B,EAAA,CAAAK,uBAAA,GAAqC;IACjCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAC,IAAA,MACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAE,SAAA,MACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAG,SAAA,MACJ;;;;;IAbZnC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAkB,UAAA,IAAAkB,mFAAA,2BAAqC,IAAAC,mFAAA,2BAIK,IAAAC,mFAAA,2BAIA;;IAKlDtC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAdarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAqC,MAAA,CAAAvB,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,wBAAoB;IAIpBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;;;;;IAhB5CF,EAHZ,CAAAM,cAAA,aAA2B,aACwB,cACA,YACU;IAAAN,EAAA,CAAAiB,MAAA,GAAwB;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IAACrB,EAAA,CAAAiB,MAAA,GAGlF;IACJjB,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAsB,oEAAA,2BAAkD;IAqB1CxC,EAHR,CAAAM,cAAA,SAAI,iBAEkG,YAC7C;IAAAN,EAAA,CAAAiB,MAAA,IAAsB;IAGnFjB,EAHmF,CAAAqB,YAAA,EAAI,EACtE,EACR,EACJ;;;;;IA7BwDrB,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAyC,iBAAA,CAAAT,YAAA,CAAAU,QAAA,CAAwB;IAAK1C,EAAA,CAAAsB,SAAA,EAGlF;IAHkFtB,EAAA,CAAAuB,kBAAA,MAAAS,YAAA,CAAAW,KAAA,MAGlF;IAE0B3C,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;IAqBS/B,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAyC,iBAAA,CAAAT,YAAA,CAAAY,MAAA,CAAsB;;;ADzDnG,OAAM,MAAOC,6BAA6B;EAL1CC,YAAA;IAOE,KAAAC,SAAS,GAAuB,EAAE;IAE1B,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEjC,KAAK,EAAE,MAAM;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACjC;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC5C;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;;EAErBW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC2C,SAAS,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEnC,KAAK,CAAC;MAC9C,MAAMuC,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEpC,KAAK,CAAC;MAE9C,IAAIwC,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACnD,SAAS,GAAGoD,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE1C,KAAa;IACvC,IAAI,CAAC0C,IAAI,IAAI,CAAC1C,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC2C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC1C,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACjB,SAAS,GAAG,CACf;MACEL,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,mBAAmB;MAC1BV,IAAI,EAAE,qBAAqB;MAC3BC,SAAS,EAAE,oBAAoB;MAC/BC,SAAS,EAAE,WAAW;MACtBS,MAAM,EAAE;KACT,EACD;MACEF,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,aAAa;MACpBV,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE,oBAAoB;MAC/BC,SAAS,EAAE,mBAAmB;MAC9BS,MAAM,EAAE;KACT,EACD;MACEF,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,WAAW;MAClBV,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAE,oBAAoB;MAC/BC,SAAS,EAAE,YAAY;MACvBS,MAAM,EAAE;KACT,CAEF;IAED,IAAI,CAACI,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAIlB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACiB,gBAAgB;EAC9B;EAEA,IAAIjB,eAAeA,CAACkC,GAAU;IAC5B,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACiB,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACvB,gBAAgB,CAACsB,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACxB,gBAAgB,CAACyB,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACxB,gBAAgB,CAACyB,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;;;uBA9FW1B,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBlCjF,EAFR,CAAAM,cAAA,aAAuD,aAC2C,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,kBAAW;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAE/DrB,EAAA,CAAAM,cAAA,aAAmD;UAC/CN,EAAA,CAAAC,SAAA,kBAC0C;UAE1CD,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAAmF,gBAAA,2BAAAC,8EAAAC,MAAA;YAAArF,EAAA,CAAAsF,kBAAA,CAAAJ,GAAA,CAAAnD,eAAA,EAAAsD,MAAA,MAAAH,GAAA,CAAAnD,eAAA,GAAAsD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrErF,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAAgF,uEAAAF,MAAA;YAAA,OAAgBH,GAAA,CAAAb,eAAA,CAAAgB,MAAA,CAAuB;UAAA,EAAC;UA8BxCrF,EA5BA,CAAAkB,UAAA,IAAAsE,oDAAA,yBAAgC,KAAAC,qDAAA,0BA4BkC;UAqC9EzF,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UAhFqErB,EAAA,CAAAsB,SAAA,GAAgB;UAC3EtB,EAD2D,CAAAE,UAAA,iBAAgB,oCACxC;UAExBF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAgF,GAAA,CAAAjC,IAAA,CAAgB;UAACjD,EAAA,CAAA0F,gBAAA,YAAAR,GAAA,CAAAnD,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAAmB;UACqBtB,EADxC,CAAAE,UAAA,UAAAgF,GAAA,CAAAnC,SAAA,CAAmB,WAAwB,mBAAmB,cAAc,oBAC9D,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}