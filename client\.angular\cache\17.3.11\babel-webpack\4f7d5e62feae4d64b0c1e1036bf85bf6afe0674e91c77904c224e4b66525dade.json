{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar gOPD = require('gopd');\nvar $TypeError = require('es-errors/type');\nvar $floor = GetIntrinsic('%Math.floor%');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionLength(fn, length) {\n  if (typeof fn !== 'function') {\n    throw new $TypeError('`fn` is not a function');\n  }\n  if (typeof length !== 'number' || length < 0 || length > 0xFFFFFFFF || $floor(length) !== length) {\n    throw new $TypeError('`length` must be a positive 32-bit integer');\n  }\n  var loose = arguments.length > 2 && !!arguments[2];\n  var functionLengthIsConfigurable = true;\n  var functionLengthIsWritable = true;\n  if ('length' in fn && gOPD) {\n    var desc = gOPD(fn, 'length');\n    if (desc && !desc.configurable) {\n      functionLengthIsConfigurable = false;\n    }\n    if (desc && !desc.writable) {\n      functionLengthIsWritable = false;\n    }\n  }\n  if (functionLengthIsConfigurable || functionLengthIsWritable || !loose) {\n    if (hasDescriptors) {\n      define( /** @type {Parameters<define>[0]} */fn, 'length', length, true, true);\n    } else {\n      define( /** @type {Parameters<define>[0]} */fn, 'length', length);\n    }\n  }\n  return fn;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "define", "hasDescriptors", "gOPD", "$TypeError", "$floor", "module", "exports", "setFunctionLength", "fn", "length", "loose", "arguments", "functionLengthIsConfigurable", "functionLengthIsWritable", "desc", "configurable", "writable"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/set-function-length/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar gOPD = require('gopd');\n\nvar $TypeError = require('es-errors/type');\nvar $floor = GetIntrinsic('%Math.floor%');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionLength(fn, length) {\n\tif (typeof fn !== 'function') {\n\t\tthrow new $TypeError('`fn` is not a function');\n\t}\n\tif (typeof length !== 'number' || length < 0 || length > 0xFFFFFFFF || $floor(length) !== length) {\n\t\tthrow new $TypeError('`length` must be a positive 32-bit integer');\n\t}\n\n\tvar loose = arguments.length > 2 && !!arguments[2];\n\n\tvar functionLengthIsConfigurable = true;\n\tvar functionLengthIsWritable = true;\n\tif ('length' in fn && gOPD) {\n\t\tvar desc = gOPD(fn, 'length');\n\t\tif (desc && !desc.configurable) {\n\t\t\tfunctionLengthIsConfigurable = false;\n\t\t}\n\t\tif (desc && !desc.writable) {\n\t\t\tfunctionLengthIsWritable = false;\n\t\t}\n\t}\n\n\tif (functionLengthIsConfigurable || functionLengthIsWritable || !loose) {\n\t\tif (hasDescriptors) {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'length', length, true, true);\n\t\t} else {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'length', length);\n\t\t}\n\t}\n\treturn fn;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,MAAM,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC5C,IAAIE,cAAc,GAAGF,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AAC1D,IAAIG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAE1B,IAAII,UAAU,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAIK,MAAM,GAAGN,YAAY,CAAC,cAAc,CAAC;;AAEzC;AACAO,MAAM,CAACC,OAAO,GAAG,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,MAAM,EAAE;EACvD,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC7B,MAAM,IAAIL,UAAU,CAAC,wBAAwB,CAAC;EAC/C;EACA,IAAI,OAAOM,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,UAAU,IAAIL,MAAM,CAACK,MAAM,CAAC,KAAKA,MAAM,EAAE;IACjG,MAAM,IAAIN,UAAU,CAAC,4CAA4C,CAAC;EACnE;EAEA,IAAIO,KAAK,GAAGC,SAAS,CAACF,MAAM,GAAG,CAAC,IAAI,CAAC,CAACE,SAAS,CAAC,CAAC,CAAC;EAElD,IAAIC,4BAA4B,GAAG,IAAI;EACvC,IAAIC,wBAAwB,GAAG,IAAI;EACnC,IAAI,QAAQ,IAAIL,EAAE,IAAIN,IAAI,EAAE;IAC3B,IAAIY,IAAI,GAAGZ,IAAI,CAACM,EAAE,EAAE,QAAQ,CAAC;IAC7B,IAAIM,IAAI,IAAI,CAACA,IAAI,CAACC,YAAY,EAAE;MAC/BH,4BAA4B,GAAG,KAAK;IACrC;IACA,IAAIE,IAAI,IAAI,CAACA,IAAI,CAACE,QAAQ,EAAE;MAC3BH,wBAAwB,GAAG,KAAK;IACjC;EACD;EAEA,IAAID,4BAA4B,IAAIC,wBAAwB,IAAI,CAACH,KAAK,EAAE;IACvE,IAAIT,cAAc,EAAE;MACnBD,MAAM,EAAC,oCAAsCQ,EAAE,EAAG,QAAQ,EAAEC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAChF,CAAC,MAAM;MACNT,MAAM,EAAC,oCAAsCQ,EAAE,EAAG,QAAQ,EAAEC,MAAM,CAAC;IACpE;EACD;EACA,OAAOD,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}