{"ast": null, "code": "export const findEntryByCode = (source, code) => {\n  if (code && source != null) {\n    const codex = source.findIndex(c => {\n      return c.isoCode === code;\n    });\n    return codex !== -1 ? source[codex] : undefined;\n  }\n  return undefined;\n};\nexport const findStateByCodeAndCountryCode = (source, code, countryCode) => {\n  if (code && countryCode && source != null) {\n    const codex = source.findIndex(c => {\n      return c.isoCode === code && c.countryCode === countryCode;\n    });\n    return codex !== -1 ? source[codex] : undefined;\n  }\n  return undefined;\n};\nexport function defaultKeyToCompare(entity) {\n  return entity.name;\n}\nexport const compare = (a, b,\n// eslint-disable-next-line no-unused-vars\nkeyToCompare = defaultKeyToCompare) => {\n  if (keyToCompare(a) < keyToCompare(b)) return -1;\n  if (keyToCompare(a) > keyToCompare(b)) return 1;\n  return 0;\n};\nexport const convertArrayToObject = (keys, arr) => {\n  const result = arr.map(subArr => {\n    return Object.fromEntries(keys.map((key, index) => [key, subArr[index]]));\n  });\n  return result;\n};", "map": {"version": 3, "names": ["findEntryByCode", "source", "code", "codex", "findIndex", "c", "isoCode", "undefined", "findStateByCodeAndCountryCode", "countryCode", "defaultKeyToCompare", "entity", "name", "compare", "a", "b", "keyToCompare", "convertArrayToObject", "keys", "arr", "result", "map", "subArr", "Object", "fromEntries", "key", "index"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/country-state-city/lib/utils/index.js"], "sourcesContent": ["export const findEntryByCode = (source, code) => {\n    if (code && source != null) {\n        const codex = source.findIndex((c) => {\n            return c.isoCode === code;\n        });\n        return codex !== -1 ? source[codex] : undefined;\n    }\n    return undefined;\n};\nexport const findStateByCodeAndCountryCode = (source, code, countryCode) => {\n    if (code && countryCode && source != null) {\n        const codex = source.findIndex((c) => {\n            return c.isoCode === code && c.countryCode === countryCode;\n        });\n        return codex !== -1 ? source[codex] : undefined;\n    }\n    return undefined;\n};\nexport function defaultKeyToCompare(entity) {\n    return entity.name;\n}\nexport const compare = (a, b, \n// eslint-disable-next-line no-unused-vars\nkeyToCompare = defaultKeyToCompare) => {\n    if (keyToCompare(a) < keyToCompare(b))\n        return -1;\n    if (keyToCompare(a) > keyToCompare(b))\n        return 1;\n    return 0;\n};\nexport const convertArrayToObject = (keys, arr) => {\n    const result = arr.map((subArr) => {\n        return Object.fromEntries(keys.map((key, index) => [key, subArr[index]]));\n    });\n    return result;\n};\n"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;EAC7C,IAAIA,IAAI,IAAID,MAAM,IAAI,IAAI,EAAE;IACxB,MAAME,KAAK,GAAGF,MAAM,CAACG,SAAS,CAAEC,CAAC,IAAK;MAClC,OAAOA,CAAC,CAACC,OAAO,KAAKJ,IAAI;IAC7B,CAAC,CAAC;IACF,OAAOC,KAAK,KAAK,CAAC,CAAC,GAAGF,MAAM,CAACE,KAAK,CAAC,GAAGI,SAAS;EACnD;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,OAAO,MAAMC,6BAA6B,GAAGA,CAACP,MAAM,EAAEC,IAAI,EAAEO,WAAW,KAAK;EACxE,IAAIP,IAAI,IAAIO,WAAW,IAAIR,MAAM,IAAI,IAAI,EAAE;IACvC,MAAME,KAAK,GAAGF,MAAM,CAACG,SAAS,CAAEC,CAAC,IAAK;MAClC,OAAOA,CAAC,CAACC,OAAO,KAAKJ,IAAI,IAAIG,CAAC,CAACI,WAAW,KAAKA,WAAW;IAC9D,CAAC,CAAC;IACF,OAAON,KAAK,KAAK,CAAC,CAAC,GAAGF,MAAM,CAACE,KAAK,CAAC,GAAGI,SAAS;EACnD;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,OAAO,SAASG,mBAAmBA,CAACC,MAAM,EAAE;EACxC,OAAOA,MAAM,CAACC,IAAI;AACtB;AACA,OAAO,MAAMC,OAAO,GAAGA,CAACC,CAAC,EAAEC,CAAC;AAC5B;AACAC,YAAY,GAAGN,mBAAmB,KAAK;EACnC,IAAIM,YAAY,CAACF,CAAC,CAAC,GAAGE,YAAY,CAACD,CAAC,CAAC,EACjC,OAAO,CAAC,CAAC;EACb,IAAIC,YAAY,CAACF,CAAC,CAAC,GAAGE,YAAY,CAACD,CAAC,CAAC,EACjC,OAAO,CAAC;EACZ,OAAO,CAAC;AACZ,CAAC;AACD,OAAO,MAAME,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC/C,MAAMC,MAAM,GAAGD,GAAG,CAACE,GAAG,CAAEC,MAAM,IAAK;IAC/B,OAAOC,MAAM,CAACC,WAAW,CAACN,IAAI,CAACG,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,KAAK,CAACD,GAAG,EAAEH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7E,CAAC,CAAC;EACF,OAAON,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}