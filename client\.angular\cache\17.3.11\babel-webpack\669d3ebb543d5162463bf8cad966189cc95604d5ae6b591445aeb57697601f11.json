{"ast": null, "code": "import { Subject, of } from 'rxjs';\nimport { takeUntil, tap, switchMap, map, catchError, shareReplay } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dialog\";\nconst _c0 = [\"TypeSelect\"];\nconst _c1 = [\"dt\"];\nconst _c2 = () => ({\n  width: \"70vw\",\n  minWidth: \"400px\"\n});\nconst _c3 = () => ({\n  \"640px\": \"95vw\",\n  \"1024px\": \"75vw\"\n});\nconst _c4 = () => ({\n  height: \"3rem\"\n});\nconst _c5 = () => [];\nconst _c6 = () => [5, 10, 20];\nconst _c7 = () => [\"activity_id\", \"document_type\", \"subject\", \"main_account_party_id\"];\nconst _c8 = a0 => ({\n  \"row-selected\": a0\n});\nfunction SalesCallRelatedItemsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 31)(6, \"div\", 29);\n    i0.ɵɵtext(7, \" Type \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 33)(10, \"div\", 29);\n    i0.ɵɵtext(11, \" Responsible \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 35);\n    i0.ɵɵtext(14, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36);\n    i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_ng_template_8_Template_tr_click_0_listener() {\n      const related_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.navigateToRelatedItemDetail(related_r3));\n    });\n    i0.ɵɵelementStart(1, \"td\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 35)(8, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_ng_template_8_Template_button_click_8_listener($event) {\n      const related_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r3.confirmRemove(related_r3));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const related_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r3 == null ? null : related_r3.activity_transaction == null ? null : related_r3.activity_transaction.subject) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityDocumentType\", related_r3 == null ? null : related_r3.type_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (related_r3 == null ? null : related_r3.partner_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"No related items found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"Loading related items data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_30_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"p-sortIcon\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"field\", col_r5.field);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_30_th_1_Template, 3, 3, \"th\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.displayedColumns);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    const rowData_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityDocumentType\", rowData_r7[col_r6.field]), \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    const rowData_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", rowData_r7[col_r6.field], \" \");\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_container_1_Template, 2, 1, \"ng-container\", 45)(2, SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const normalCell_r8 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r6.field === \"document_type\")(\"ngIfElse\", normalCell_r8);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 43);\n    i0.ɵɵtemplate(1, SalesCallRelatedItemsComponent_ng_template_31_td_1_Template, 4, 2, \"td\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowData_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"pSelectableRow\", rowData_r7)(\"ngClass\", i0.ɵɵpureFunction1(3, _c8, (ctx_r3.selectedItem == null ? null : ctx_r3.selectedItem.activity_id) === rowData_r7.activity_id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.displayedColumns);\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 46);\n    i0.ɵɵtext(2, \"No items found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallRelatedItemsComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 46);\n    i0.ɵɵtext(2, \"Loading items data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SalesCallRelatedItemsComponent {\n  constructor(activitiesservice, formBuilder, router, route, cdr, messageservice, confirmationservice) {\n    this.activitiesservice = activitiesservice;\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.route = route;\n    this.cdr = cdr;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.relateditemsdetails = null;\n    this.activity_id = '';\n    this.account_id = '';\n    this.addDialogVisible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.saving = false;\n    this.ItemDataLoading = false;\n    this.ItemInput$ = new Subject();\n    this.selectedItem = null;\n    this.displayedColumns = [];\n    this.dropdowns = {\n      activityDocumentType: []\n    };\n    this.RelatedItemsForm = this.formBuilder.group({\n      type_code: ['', Validators.required],\n      activity_transaction_id: [null]\n    });\n  }\n  ngOnInit() {\n    this.loadItemDataOnTypeChange();\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM');\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response.activity_id || '';\n        this.account_id = response.business_partner?.bp_id || '';\n        const allItems = response.follow_up_and_related_items || [];\n        this.relateditemsdetails = allItems.filter(item => item.btd_role_code === '1').map(item => {\n          const partnerFn = item.activity_transaction?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n          const partnerName = partnerFn?.bp_full_name || null;\n          return {\n            ...item,\n            partner_name: partnerName\n          };\n        });\n      }\n    });\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadItemDataOnTypeChange() {\n    this.ItemData$ = this.RelatedItemsForm.get('type_code').valueChanges.pipe(tap(type => {\n      this.RelatedItemsForm.get('activity_transaction_id')?.reset();\n      this.setDisplayedColumns(type);\n      if (this.TypeSelect) {\n        this.TypeSelect.clearModel();\n      }\n      this.ItemDataLoading = true;\n    }), switchMap(type => {\n      if (!type) {\n        this.ItemDataLoading = false;\n        return of([]);\n      }\n      const params = this.getParamsByRole(type);\n      if (!params) {\n        this.ItemDataLoading = false;\n        return of([]);\n      }\n      return this.activitiesservice.getActivityCodeWise(params).pipe(map(res => res.data || []),\n      // <-- Extract array here\n      catchError(() => {\n        this.ItemDataLoading = false;\n        return of([]);\n      }), tap(() => this.ItemDataLoading = false));\n    }), shareReplay(1));\n  }\n  setDisplayedColumns(type) {\n    switch (type) {\n      case '0006':\n        // Phone Call\n        this.displayedColumns = [{\n          field: 'activity_id',\n          header: 'Reference ID'\n        }, {\n          field: 'document_type',\n          header: 'Task Type'\n        }, {\n          field: 'subject',\n          header: 'Description'\n        }, {\n          field: 'main_account_party_id',\n          header: 'Account ID'\n        }];\n        break;\n      case '0002':\n        // Activity Task\n        this.displayedColumns = [{\n          field: 'activity_id',\n          header: 'Reference ID'\n        }, {\n          field: 'document_type',\n          header: 'Task Type'\n        }, {\n          field: 'subject',\n          header: 'Description'\n        }, {\n          field: 'main_account_party_id',\n          header: 'Account ID'\n        }];\n        break;\n      case '0005':\n        // Opportunity\n        this.displayedColumns = [{\n          field: 'activity_id',\n          header: 'Reference ID'\n        }, {\n          field: 'document_type',\n          header: 'Task Type'\n        }, {\n          field: 'subject',\n          header: 'Description'\n        }, {\n          field: 'main_account_party_id',\n          header: 'Account ID'\n        }];\n        break;\n      default:\n        this.displayedColumns = [];\n    }\n  }\n  onSelectReference() {\n    if (!this.selectedItem) {\n      this.messageservice.add({\n        severity: 'warn',\n        detail: 'Please select a record to continue.'\n      });\n      return;\n    }\n    this.RelatedItemsForm.patchValue({\n      activity_transaction_id: this.selectedItem.activity_id\n    });\n    this.onSubmit();\n  }\n  getParamsByRole(type) {\n    if (!type) return null;\n    switch (type) {\n      case '0006':\n        return {\n          'filters[document_type][$eq]': '0006',\n          'filters[main_account_party_id][$eq]': this.account_id\n        };\n      case '0002':\n        return {\n          'filters[document_type][$eq]': '0002',\n          'filters[main_account_party_id][$eq]': this.account_id\n        };\n      case '0005':\n        return {\n          'filters[document_type][$eq]': '0005',\n          'filters[main_account_party_id][$eq]': this.account_id\n        };\n      default:\n        return null;\n    }\n  }\n  onSelectionChange(selected) {\n    this.selectedItem = selected;\n    if (selected) {\n      this.RelatedItemsForm.patchValue({\n        activity_transaction_id: this.selectedItem.activity_id || null\n      });\n    } else {\n      this.RelatedItemsForm.patchValue({\n        activity_transaction_id: null\n      });\n    }\n  }\n  onSubmit() {\n    this.submitted = true;\n    if (this.RelatedItemsForm.invalid) {\n      return;\n    }\n    this.saving = true;\n    const value = {\n      ...this.RelatedItemsForm.value\n    };\n    const data = {\n      activity_id: this.activity_id,\n      activity_transaction_id: value.activity_transaction_id,\n      type_code: value.type_code,\n      btd_role_code: '1'\n    };\n    this.activitiesservice.createRelatedItem(data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.saving = false;\n        this.addDialogVisible = false;\n        this.RelatedItemsForm.reset();\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Related Item Added successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.saving = false;\n        this.addDialogVisible = true;\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => this.remove(item)\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  get f() {\n    return this.RelatedItemsForm.controls;\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.RelatedItemsForm.reset();\n    const options = this.dropdowns['activityDocumentType'];\n    if (options?.length > 0) {\n      this.RelatedItemsForm.get('type_code')?.setValue(options[0].value);\n    }\n  }\n  onGlobalFilter(event) {\n    const value = event.target.value;\n    this.table.filterGlobal(value, 'contains');\n    // Ensure the view updates immediately\n    this.cdr.detectChanges();\n  }\n  navigateToRelatedItemDetail(item) {\n    this.router.navigate([item.activity_transaction?.activity_id], {\n      relativeTo: this.route,\n      state: {\n        relateditemdata: item\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallRelatedItemsComponent_Factory(t) {\n      return new (t || SalesCallRelatedItemsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallRelatedItemsComponent,\n      selectors: [[\"app-sales-call-related-items\"]],\n      viewQuery: function SalesCallRelatedItemsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.TypeSelect = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n        }\n      },\n      decls: 37,\n      vars: 38,\n      consts: [[\"dt\", \"\"], [\"normalCell\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"header\", \"Add Reference\", 3, \"visibleChange\", \"visible\", \"modal\", \"closable\", \"resizable\", \"draggable\", \"breakpoints\"], [1, \"p-fluid\", 3, \"formGroup\"], [1, \"flex\", \"gap-3\", \"mb-3\"], [1, \"w-8\", \"flex\", \"flex-column\"], [\"for\", \"type_code\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"mb-1\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"formControlName\", \"type_code\", \"placeholder\", \"Select Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"w-full text-sm\", 3, \"options\"], [1, \"w-4\", \"flex\", \"flex-column\"], [\"for\", \"search\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"mb-2\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"search\", \"styleClass\", \"w-full text-sm\", \"placeholder\", \"Search...\", 3, \"input\"], [1, \"p-mb-4\"], [\"selectionMode\", \"single\", \"dataKey\", \"activity_id\", \"scrollHeight\", \"280px\", \"styleClass\", \"p-datatable-gridlines\", 3, \"selectionChange\", \"value\", \"paginator\", \"rows\", \"rowsPerPageOptions\", \"responsiveLayout\", \"selection\", \"loading\", \"scrollable\", \"rowHover\", \"globalFilterFields\"], [1, \"flex\", \"justify-content-end\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-sm\", \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-sm\", \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\", \"disabled\"], [\"pSortableColumn\", \"activity_transaction.subject\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"activity_transaction.subject\"], [\"pSortableColumn\", \"type_code\"], [\"field\", \"type_code\"], [\"pSortableColumn\", \"partner_name\"], [\"field\", \"partner_name\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"4\", 1, \"border-round-left-lg\"], [3, \"pSortableColumn\", 4, \"ngFor\", \"ngForOf\"], [3, \"pSortableColumn\"], [3, \"field\"], [3, \"pSelectableRow\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\", \"ngIfElse\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\"]],\n      template: function SalesCallRelatedItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h4\", 4);\n          i0.ɵɵtext(3, \"Related Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_p_button_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showNewDialog(\"right\"));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"p-table\", 7);\n          i0.ɵɵtemplate(7, SalesCallRelatedItemsComponent_ng_template_7_Template, 15, 0, \"ng-template\", 8)(8, SalesCallRelatedItemsComponent_ng_template_8_Template, 9, 3, \"ng-template\", 9)(9, SalesCallRelatedItemsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10)(10, SalesCallRelatedItemsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallRelatedItemsComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(12, \"form\", 13)(13, \"div\", 14)(14, \"div\", 15)(15, \"label\", 16)(16, \"span\", 17);\n          i0.ɵɵtext(17, \"supervisor_account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Type \");\n          i0.ɵɵelementStart(19, \"span\", 18);\n          i0.ɵɵtext(20, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(21, \"p-dropdown\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 20)(23, \"label\", 21);\n          i0.ɵɵtext(24, \" Search \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"input\", 22);\n          i0.ɵɵlistener(\"input\", function SalesCallRelatedItemsComponent_Template_input_input_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onGlobalFilter($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 23)(27, \"p-table\", 24, 0);\n          i0.ɵɵpipe(29, \"async\");\n          i0.ɵɵtwoWayListener(\"selectionChange\", function SalesCallRelatedItemsComponent_Template_p_table_selectionChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedItem, $event) || (ctx.selectedItem = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(30, SalesCallRelatedItemsComponent_ng_template_30_Template, 2, 1, \"ng-template\", 8)(31, SalesCallRelatedItemsComponent_ng_template_31_Template, 2, 5, \"ng-template\", 9)(32, SalesCallRelatedItemsComponent_ng_template_32_Template, 3, 0, \"ng-template\", 10)(33, SalesCallRelatedItemsComponent_ng_template_33_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 25)(35, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_35_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addDialogVisible = false);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function SalesCallRelatedItemsComponent_Template_button_click_36_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectReference());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.relateditemsdetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(32, _c2));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"modal\", true)(\"closable\", true)(\"resizable\", true)(\"draggable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(33, _c3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.RelatedItemsForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(34, _c4));\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"activityDocumentType\"]);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleProp(\"height\", \"3rem\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind1(29, 30, ctx.ItemData$) || i0.ɵɵpureFunction0(35, _c5))(\"paginator\", true)(\"rows\", 5)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(36, _c6))(\"responsiveLayout\", \"scroll\");\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedItem);\n          i0.ɵɵproperty(\"loading\", ctx.ItemDataLoading)(\"scrollable\", true)(\"rowHover\", false)(\"globalFilterFields\", i0.ɵɵpureFunction0(37, _c7));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedItem);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i6.Table, i4.PrimeTemplate, i6.SortableColumn, i6.SelectableRow, i6.SortIcon, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.InputText, i11.Dialog, i5.AsyncPipe],\n      styles: [\"[_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr.row-selected {\\n  background-color: #f0f0f0 !important; \\n\\n  color: #000 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy9zYWxlcy1jYWxsL3NhbGVzLWNhbGwtZGV0YWlscy9zYWxlcy1jYWxsLXJlbGF0ZWQtaXRlbXMvc2FsZXMtY2FsbC1yZWxhdGVkLWl0ZW1zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usb0NBQUEsRUFBQSxxQ0FBQTtFQUNBLHNCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCA6Om5nLWRlZXAgLnAtZGF0YXRhYmxlIC5wLWRhdGF0YWJsZS10Ym9keSA+IHRyLnJvdy1zZWxlY3RlZCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjBmMCAhaW1wb3J0YW50OyAvKiBMaWdodCBncmF5IG9yIHlvdXIgZGVzaXJlZCBjb2xvciAqL1xyXG4gIGNvbG9yOiAjMDAwICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "of", "takeUntil", "tap", "switchMap", "map", "catchError", "shareReplay", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "SalesCallRelatedItemsComponent_ng_template_8_Template_tr_click_0_listener", "related_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "navigateToRelatedItemDetail", "SalesCallRelatedItemsComponent_ng_template_8_Template_button_click_8_listener", "$event", "stopPropagation", "confirmRemove", "ɵɵadvance", "ɵɵtextInterpolate1", "activity_transaction", "subject", "getLabelFromDropdown", "type_code", "partner_name", "ɵɵpropertyInterpolate", "col_r5", "field", "header", "ɵɵproperty", "ɵɵtemplate", "SalesCallRelatedItemsComponent_ng_template_30_th_1_Template", "displayedColumns", "ɵɵelementContainerStart", "rowData_r7", "col_r6", "SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_container_1_Template", "SalesCallRelatedItemsComponent_ng_template_31_td_1_ng_template_2_Template", "ɵɵtemplateRefExtractor", "normalCell_r8", "SalesCallRelatedItemsComponent_ng_template_31_td_1_Template", "ɵɵpureFunction1", "_c8", "selectedItem", "activity_id", "SalesCallRelatedItemsComponent", "constructor", "activitiesservice", "formBuilder", "router", "route", "cdr", "messageservice", "confirmationservice", "unsubscribe$", "relateditemsdetails", "account_id", "addDialogVisible", "position", "submitted", "saving", "ItemDataLoading", "ItemInput$", "dropdowns", "activityDocumentType", "RelatedItemsForm", "group", "required", "activity_transaction_id", "ngOnInit", "loadItemDataOnTypeChange", "loadActivityDropDown", "activity", "pipe", "subscribe", "response", "business_partner", "bp_id", "allItems", "follow_up_and_related_items", "filter", "item", "btd_role_code", "partnerFn", "customer", "partner_functions", "find", "p", "partner_function", "partner<PERSON>ame", "bp_full_name", "target", "type", "getActivityDropdownOptions", "res", "data", "attr", "label", "description", "value", "code", "dropdownKey", "opt", "ItemData$", "get", "valueChanges", "reset", "setDisplayedColumns", "TypeSelect", "clearModel", "params", "getParamsByRole", "getActivityCodeWise", "onSelectReference", "add", "severity", "detail", "patchValue", "onSubmit", "onSelectionChange", "selected", "invalid", "createRelatedItem", "next", "getActivityByID", "error", "confirm", "message", "icon", "accept", "remove", "deleteFollowupItem", "documentId", "f", "controls", "showNewDialog", "options", "length", "setValue", "onGlobalFilter", "event", "table", "filterGlobal", "detectChanges", "navigate", "relativeTo", "state", "relateditemdata", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "FormBuilder", "i3", "Router", "ActivatedRoute", "ChangeDetectorRef", "i4", "MessageService", "ConfirmationService", "selectors", "viewQuery", "SalesCallRelatedItemsComponent_Query", "rf", "ctx", "SalesCallRelatedItemsComponent_Template_p_button_click_4_listener", "_r1", "SalesCallRelatedItemsComponent_ng_template_7_Template", "SalesCallRelatedItemsComponent_ng_template_8_Template", "SalesCallRelatedItemsComponent_ng_template_9_Template", "SalesCallRelatedItemsComponent_ng_template_10_Template", "ɵɵtwoWayListener", "SalesCallRelatedItemsComponent_Template_p_dialog_visibleChange_11_listener", "ɵɵtwoWayBindingSet", "SalesCallRelatedItemsComponent_Template_input_input_25_listener", "SalesCallRelatedItemsComponent_Template_p_table_selectionChange_27_listener", "SalesCallRelatedItemsComponent_ng_template_30_Template", "SalesCallRelatedItemsComponent_ng_template_31_Template", "SalesCallRelatedItemsComponent_ng_template_32_Template", "SalesCallRelatedItemsComponent_ng_template_33_Template", "SalesCallRelatedItemsComponent_Template_button_click_35_listener", "SalesCallRelatedItemsComponent_Template_button_click_36_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c2", "ɵɵtwoWayProperty", "_c3", "_c4", "ɵɵstyleProp", "ɵɵpipeBind1", "_c5", "_c6", "_c7"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-related-items\\sales-call-related-items.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-related-items\\sales-call-related-items.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';\r\nimport { Subject, Observable, of } from 'rxjs';\r\nimport {\r\n  takeUntil,\r\n  tap,\r\n  switchMap,\r\n  map,\r\n  catchError,\r\n  shareReplay,\r\n} from 'rxjs/operators';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { NgSelectComponent } from '@ng-select/ng-select';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { Table } from 'primeng/table';\r\nimport { ChangeDetectorRef } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-sales-call-related-items',\r\n  templateUrl: './sales-call-related-items.component.html',\r\n  styleUrls: ['./sales-call-related-items.component.scss'],\r\n})\r\nexport class SalesCallRelatedItemsComponent implements OnInit, OnDestroy {\r\n  private unsubscribe$ = new Subject<void>();\r\n\r\n  @ViewChild('TypeSelect') TypeSelect!: NgSelectComponent;\r\n  @ViewChild('dt') table!: Table;\r\n\r\n  public relateditemsdetails: any = null;\r\n  public activity_id: string = '';\r\n  public account_id: string = '';\r\n  public addDialogVisible = false;\r\n  public position = 'right';\r\n  public submitted = false;\r\n  public saving = false;\r\n\r\n  public ItemData$?: Observable<any[]>;\r\n  public ItemDataLoading = false;\r\n  public ItemInput$ = new Subject<string>();\r\n\r\n  selectedItem: any = null;\r\n\r\n  displayedColumns: any[] = [];\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n  };\r\n\r\n  public RelatedItemsForm: FormGroup;\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private formBuilder: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private cdr: ChangeDetectorRef,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {\r\n    this.RelatedItemsForm = this.formBuilder.group({\r\n      type_code: ['', Validators.required],\r\n      activity_transaction_id: [null],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadItemDataOnTypeChange();\r\n\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM'\r\n    );\r\n\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response.activity_id || '';\r\n          this.account_id = response.business_partner?.bp_id || '';\r\n\r\n          const allItems = response.follow_up_and_related_items || [];\r\n\r\n          this.relateditemsdetails = allItems\r\n            .filter((item: any) => item.btd_role_code === '1')\r\n            .map((item: any) => {\r\n              const partnerFn =\r\n                item.activity_transaction?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) => p.partner_function === 'YI'\r\n                );\r\n              const partnerName = partnerFn?.bp_full_name || null;\r\n              return {\r\n                ...item,\r\n                partner_name: partnerName,\r\n              };\r\n            });\r\n        }\r\n      });\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  loadItemDataOnTypeChange(): void {\r\n    this.ItemData$ = this.RelatedItemsForm.get('type_code')!.valueChanges.pipe(\r\n      tap((type) => {\r\n        this.RelatedItemsForm.get('activity_transaction_id')?.reset();\r\n        this.setDisplayedColumns(type);\r\n        if (this.TypeSelect) {\r\n          this.TypeSelect.clearModel();\r\n        }\r\n        this.ItemDataLoading = true;\r\n      }),\r\n      switchMap((type: string) => {\r\n        if (!type) {\r\n          this.ItemDataLoading = false;\r\n          return of([]);\r\n        }\r\n\r\n        const params = this.getParamsByRole(type);\r\n        if (!params) {\r\n          this.ItemDataLoading = false;\r\n          return of([]);\r\n        }\r\n\r\n        return this.activitiesservice.getActivityCodeWise(params).pipe(\r\n          map((res: any) => res.data || []), // <-- Extract array here\r\n          catchError(() => {\r\n            this.ItemDataLoading = false;\r\n            return of([]);\r\n          }),\r\n          tap(() => (this.ItemDataLoading = false))\r\n        );\r\n      }),\r\n      shareReplay(1)\r\n    );\r\n  }\r\n\r\n  setDisplayedColumns(type: string) {\r\n    switch (type) {\r\n      case '0006': // Phone Call\r\n        this.displayedColumns = [\r\n          { field: 'activity_id', header: 'Reference ID' },\r\n          { field: 'document_type', header: 'Task Type' },\r\n          { field: 'subject', header: 'Description' },\r\n          { field: 'main_account_party_id', header: 'Account ID' },\r\n        ];\r\n        break;\r\n      case '0002': // Activity Task\r\n        this.displayedColumns = [\r\n          { field: 'activity_id', header: 'Reference ID' },\r\n          { field: 'document_type', header: 'Task Type' },\r\n          { field: 'subject', header: 'Description' },\r\n          { field: 'main_account_party_id', header: 'Account ID' },\r\n        ];\r\n        break;\r\n      case '0005': // Opportunity\r\n        this.displayedColumns = [\r\n          { field: 'activity_id', header: 'Reference ID' },\r\n          { field: 'document_type', header: 'Task Type' },\r\n          { field: 'subject', header: 'Description' },\r\n          { field: 'main_account_party_id', header: 'Account ID' },\r\n        ];\r\n        break;\r\n      default:\r\n        this.displayedColumns = [];\r\n    }\r\n  }\r\n\r\n  onSelectReference() {\r\n    if (!this.selectedItem) {\r\n      this.messageservice.add({\r\n        severity: 'warn',\r\n        detail: 'Please select a record to continue.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.RelatedItemsForm.patchValue({\r\n      activity_transaction_id: this.selectedItem.activity_id,\r\n    });\r\n\r\n    this.onSubmit();\r\n  }\r\n\r\n  private getParamsByRole(type: string): any | null {\r\n    if (!type) return null;\r\n\r\n    switch (type) {\r\n      case '0006':\r\n        return {\r\n          'filters[document_type][$eq]': '0006',\r\n          'filters[main_account_party_id][$eq]': this.account_id,\r\n        };\r\n      case '0002':\r\n        return {\r\n          'filters[document_type][$eq]': '0002',\r\n          'filters[main_account_party_id][$eq]': this.account_id,\r\n        };\r\n      case '0005':\r\n        return {\r\n          'filters[document_type][$eq]': '0005',\r\n          'filters[main_account_party_id][$eq]': this.account_id,\r\n        };\r\n      default:\r\n        return null;\r\n    }\r\n  }\r\n\r\n  onSelectionChange(selected: any): void {\r\n    this.selectedItem = selected;\r\n    if (selected) {\r\n      this.RelatedItemsForm.patchValue({\r\n        activity_transaction_id: this.selectedItem.activity_id || null,\r\n      });\r\n    } else {\r\n      this.RelatedItemsForm.patchValue({\r\n        activity_transaction_id: null,\r\n      });\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.RelatedItemsForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.RelatedItemsForm.value };\r\n\r\n    const data = {\r\n      activity_id: this.activity_id,\r\n      activity_transaction_id: value.activity_transaction_id,\r\n      type_code: value.type_code,\r\n      btd_role_code: '1',\r\n    };\r\n\r\n    this.activitiesservice\r\n      .createRelatedItem(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.RelatedItemsForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Related Item Added successfully!',\r\n          });\r\n\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => this.remove(item),\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteFollowupItem(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n\r\n          this.activitiesservice\r\n            .getActivityByID(this.activity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  get f() {\r\n    return this.RelatedItemsForm.controls;\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.RelatedItemsForm.reset();\r\n\r\n    const options = this.dropdowns['activityDocumentType'];\r\n    if (options?.length > 0) {\r\n      this.RelatedItemsForm.get('type_code')?.setValue(options[0].value);\r\n    }\r\n  }\r\n\r\n  onGlobalFilter(event: any) {\r\n    const value = event.target.value;\r\n    this.table.filterGlobal(value, 'contains');\r\n\r\n    // Ensure the view updates immediately\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  navigateToRelatedItemDetail(item: any) {\r\n    this.router.navigate([item.activity_transaction?.activity_id], {\r\n      relativeTo: this.route,\r\n      state: { relateditemdata: item },\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Related Items</h4>\r\n        <p-button label=\"Add\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\"></p-button>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"relateditemsdetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"activity_transaction.subject\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Name\r\n                            <p-sortIcon field=\"activity_transaction.subject\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"type_code\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Type\r\n                            <p-sortIcon field=\"type_code\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"partner_name\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Responsible\r\n                            <p-sortIcon field=\"partner_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg text-center\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-related>\r\n                <tr (click)=\"navigateToRelatedItemDetail(related)\" class=\"cursor-pointer\">\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ related?.activity_transaction?.subject || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ getLabelFromDropdown('activityDocumentType', related?.type_code) || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ related?.partner_name || '-' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(related)\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"4\" class=\"border-round-left-lg\">No related items found.</td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"4\" class=\"border-round-left-lg\">Loading related items data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n<p-dialog header=\"Add Reference\" [(visible)]=\"addDialogVisible\" [modal]=\"true\"\r\n    [style]=\"{ width: '70vw', minWidth: '400px' }\" [closable]=\"true\" [resizable]=\"true\" [draggable]=\"false\"\r\n    [breakpoints]=\"{ '640px': '95vw', '1024px': '75vw' }\">\r\n    <form [formGroup]=\"RelatedItemsForm\" class=\"p-fluid\">\r\n\r\n        <div class=\"flex gap-3 mb-3\">\r\n            <!-- Dropdown: 70% width -->\r\n            <div class=\"w-8 flex flex-column\">\r\n                <label for=\"type_code\" class=\"flex align-items-center font-semibold mb-1 gap-1\">\r\n                    <span class=\"material-symbols-rounded\">supervisor_account</span>\r\n                    Type <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown formControlName=\"type_code\" [options]=\"dropdowns['activityDocumentType']\"\r\n                    placeholder=\"Select Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"w-full text-sm\"\r\n                    [style]=\"{ height: '3rem' }\"></p-dropdown>\r\n            </div>\r\n\r\n            <div class=\"w-4 flex flex-column\">\r\n                <label for=\"search\" class=\"flex align-items-center font-semibold mb-2 gap-1\">\r\n                    Search\r\n                </label>\r\n                <input pInputText type=\"text\" id=\"search\" styleClass=\"w-full text-sm\" placeholder=\"Search...\"\r\n                    [style.height]=\"'3rem'\" (input)=\"onGlobalFilter($event)\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"p-mb-4\">\r\n            <p-table #dt [value]=\"(ItemData$ | async) || []\" [paginator]=\"true\" [rows]=\"5\"\r\n                [rowsPerPageOptions]=\"[5, 10, 20]\" [responsiveLayout]=\"'scroll'\" selectionMode=\"single\"\r\n                [(selection)]=\"selectedItem\" dataKey=\"activity_id\" [loading]=\"ItemDataLoading\"\r\n                [scrollable]=\"true\" [rowHover]=\"false\" scrollHeight=\"280px\" styleClass=\"p-datatable-gridlines\"\r\n                [globalFilterFields]=\"['activity_id', 'document_type','subject','main_account_party_id']\">\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th *ngFor=\"let col of displayedColumns\" pSortableColumn=\"{{ col.field }}\">\r\n                            {{ col.header }}\r\n                            <p-sortIcon [field]=\"col.field\"></p-sortIcon>\r\n                        </th>\r\n                    </tr>\r\n                </ng-template>\r\n\r\n                <ng-template pTemplate=\"body\" let-rowData>\r\n                    <tr [pSelectableRow]=\"rowData\" [ngClass]=\"{ 'row-selected': selectedItem?.activity_id === rowData.activity_id }\">\r\n                        <td *ngFor=\"let col of displayedColumns\">\r\n                            <ng-container *ngIf=\"col.field === 'document_type'; else normalCell\">\r\n                                {{ getLabelFromDropdown('activityDocumentType', rowData[col.field]) }}\r\n                            </ng-container>\r\n                            <ng-template #normalCell>\r\n                                {{ rowData[col.field] }}\r\n                            </ng-template>\r\n                        </td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"emptymessage\">\r\n                    <tr>\r\n                        <td class=\"border-round-left-lg\" colspan=\"10\">No items found.</td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"loadingbody\">\r\n                    <tr>\r\n                        <td colspan=\"10\" class=\"border-round-left-lg\">Loading items data. Please wait...</td>\r\n                    </tr>\r\n                </ng-template>\r\n            </p-table>\r\n        </div>\r\n\r\n        <div class=\"flex justify-content-end p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-sm p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\"\r\n                class=\"p-button-sm p-button-rounded justify-content-center w-9rem h-3rem\" [disabled]=\"!selectedItem\"\r\n                (click)=\"onSelectReference()\"></button>\r\n        </div>\r\n\r\n    </form>\r\n</p-dialog>"], "mappings": "AACA,SAASA,OAAO,EAAcC,EAAE,QAAQ,MAAM;AAC9C,SACEC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,WAAW,QACN,gBAAgB;AAEvB,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICC3CC,EAFR,CAAAC,cAAA,SAAI,aACgF,cACjC;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAG,SAAA,qBAA8D;IAEtEH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAgC,cACe;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAG,SAAA,qBAA2C;IAEnDH,EADI,CAAAI,YAAA,EAAM,EACL;IAEDJ,EADJ,CAAAC,cAAA,aAAmC,eACY;IACvCD,EAAA,CAAAE,MAAA,qBACA;IAAAF,EAAA,CAAAG,SAAA,sBAA8C;IAEtDH,EADI,CAAAI,YAAA,EAAM,EACL;IACLJ,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACxDF,EADwD,CAAAI,YAAA,EAAK,EACxD;;;;;;IAILJ,EAAA,CAAAC,cAAA,aAA0E;IAAtED,EAAA,CAAAK,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,UAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,2BAAA,CAAAP,UAAA,CAAoC;IAAA,EAAC;IAC9CP,EAAA,CAAAC,cAAA,aAAsF;IAClFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAEDJ,EADJ,CAAAC,cAAA,aAA8C,iBAEqB;IAA3DD,EAAA,CAAAK,UAAA,mBAAAU,8EAAAC,MAAA;MAAA,MAAAT,UAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAASI,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAEF,MAAA,CAAAO,aAAA,CAAAX,UAAA,CAAsB;IAAA,EAAC;IAEtEP,EAFuE,CAAAI,YAAA,EAAS,EACvE,EACJ;;;;;IAZGJ,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,OAAAb,UAAA,kBAAAA,UAAA,CAAAc,oBAAA,kBAAAd,UAAA,CAAAc,oBAAA,CAAAC,OAAA,cACJ;IAEItB,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,MAAAT,MAAA,CAAAY,oBAAA,yBAAAhB,UAAA,kBAAAA,UAAA,CAAAiB,SAAA,cACJ;IAEIxB,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,OAAAb,UAAA,kBAAAA,UAAA,CAAAkB,YAAA,cACJ;;;;;IAUAzB,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IACxEF,EADwE,CAAAI,YAAA,EAAK,EACxE;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IACzFF,EADyF,CAAAI,YAAA,EAAK,EACzF;;;;;IAuCGJ,EAAA,CAAAC,cAAA,aAA2E;IACvED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAG,SAAA,qBAA6C;IACjDH,EAAA,CAAAI,YAAA,EAAK;;;;IAHoCJ,EAAA,CAAA0B,qBAAA,oBAAAC,MAAA,CAAAC,KAAA,CAAiC;IACtE5B,EAAA,CAAAmB,SAAA,EACA;IADAnB,EAAA,CAAAoB,kBAAA,MAAAO,MAAA,CAAAE,MAAA,MACA;IAAY7B,EAAA,CAAAmB,SAAA,EAAmB;IAAnBnB,EAAA,CAAA8B,UAAA,UAAAH,MAAA,CAAAC,KAAA,CAAmB;;;;;IAHvC5B,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA+B,UAAA,IAAAC,2DAAA,iBAA2E;IAI/EhC,EAAA,CAAAI,YAAA,EAAK;;;;IAJmBJ,EAAA,CAAAmB,SAAA,EAAmB;IAAnBnB,EAAA,CAAA8B,UAAA,YAAAnB,MAAA,CAAAsB,gBAAA,CAAmB;;;;;IAUnCjC,EAAA,CAAAkC,uBAAA,GAAqE;IACjElC,EAAA,CAAAE,MAAA,GACJ;;;;;;;IADIF,EAAA,CAAAmB,SAAA,EACJ;IADInB,EAAA,CAAAoB,kBAAA,MAAAT,MAAA,CAAAY,oBAAA,yBAAAY,UAAA,CAAAC,MAAA,CAAAR,KAAA,QACJ;;;;;IAEI5B,EAAA,CAAAE,MAAA,GACJ;;;;;IADIF,EAAA,CAAAoB,kBAAA,MAAAe,UAAA,CAAAC,MAAA,CAAAR,KAAA,OACJ;;;;;IANJ5B,EAAA,CAAAC,cAAA,SAAyC;IAIrCD,EAHA,CAAA+B,UAAA,IAAAM,0EAAA,2BAAqE,IAAAC,yEAAA,gCAAAtC,EAAA,CAAAuC,sBAAA,CAG5C;IAG7BvC,EAAA,CAAAI,YAAA,EAAK;;;;;IANcJ,EAAA,CAAAmB,SAAA,EAAqC;IAAAnB,EAArC,CAAA8B,UAAA,SAAAM,MAAA,CAAAR,KAAA,qBAAqC,aAAAY,aAAA,CAAe;;;;;IAF3ExC,EAAA,CAAAC,cAAA,aAAiH;IAC7GD,EAAA,CAAA+B,UAAA,IAAAU,2DAAA,iBAAyC;IAQ7CzC,EAAA,CAAAI,YAAA,EAAK;;;;;IAT0BJ,EAA3B,CAAA8B,UAAA,mBAAAK,UAAA,CAA0B,YAAAnC,EAAA,CAAA0C,eAAA,IAAAC,GAAA,GAAAhC,MAAA,CAAAiC,YAAA,kBAAAjC,MAAA,CAAAiC,YAAA,CAAAC,WAAA,MAAAV,UAAA,CAAAU,WAAA,EAAkF;IACxF7C,EAAA,CAAAmB,SAAA,EAAmB;IAAnBnB,EAAA,CAAA8B,UAAA,YAAAnB,MAAA,CAAAsB,gBAAA,CAAmB;;;;;IAYvCjC,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACjEF,EADiE,CAAAI,YAAA,EAAK,EACjE;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACpFF,EADoF,CAAAI,YAAA,EAAK,EACpF;;;ADvGzB,OAAM,MAAO0C,8BAA8B;EA4BzCC,YACUC,iBAAoC,EACpCC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,GAAsB,EACtBC,cAA8B,EAC9BC,mBAAwC;IANxC,KAAAN,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAlCrB,KAAAC,YAAY,GAAG,IAAIhE,OAAO,EAAQ;IAKnC,KAAAiE,mBAAmB,GAAQ,IAAI;IAC/B,KAAAX,WAAW,GAAW,EAAE;IACxB,KAAAY,UAAU,GAAW,EAAE;IACvB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,OAAO;IAClB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAGd,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,UAAU,GAAG,IAAIxE,OAAO,EAAU;IAEzC,KAAAqD,YAAY,GAAQ,IAAI;IAExB,KAAAX,gBAAgB,GAAU,EAAE;IAErB,KAAA+B,SAAS,GAA0B;MACxCC,oBAAoB,EAAE;KACvB;IAaC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACjB,WAAW,CAACkB,KAAK,CAAC;MAC7C3C,SAAS,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACqE,QAAQ,CAAC;MACpCC,uBAAuB,EAAE,CAAC,IAAI;KAC/B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,wBAAwB,EAAE;IAE/B,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,6CAA6C,CAC9C;IAED,IAAI,CAACxB,iBAAiB,CAACyB,QAAQ,CAC5BC,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC/B,WAAW,GAAG+B,QAAQ,CAAC/B,WAAW,IAAI,EAAE;QAC7C,IAAI,CAACY,UAAU,GAAGmB,QAAQ,CAACC,gBAAgB,EAAEC,KAAK,IAAI,EAAE;QAExD,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,2BAA2B,IAAI,EAAE;QAE3D,IAAI,CAACxB,mBAAmB,GAAGuB,QAAQ,CAChCE,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAACC,aAAa,KAAK,GAAG,CAAC,CACjDvF,GAAG,CAAEsF,IAAS,IAAI;UACjB,MAAME,SAAS,GACbF,IAAI,CAAC7D,oBAAoB,EAAEwD,gBAAgB,EAAEQ,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC3EC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;UACH,MAAMC,WAAW,GAAGN,SAAS,EAAEO,YAAY,IAAI,IAAI;UACnD,OAAO;YACL,GAAGT,IAAI;YACPzD,YAAY,EAAEiE;WACf;QACH,CAAC,CAAC;MACN;IACF,CAAC,CAAC;EACN;EAEAlB,oBAAoBA,CAACoB,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAC7C,iBAAiB,CACnB8C,0BAA0B,CAACD,IAAI,CAAC,CAChClB,SAAS,CAAEoB,GAAQ,IAAI;MACtB,IAAI,CAAC/B,SAAS,CAAC4B,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAEpG,GAAG,CAAEqG,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEA9E,oBAAoBA,CAAC+E,WAAmB,EAAEF,KAAa;IACrD,MAAMlB,IAAI,GAAG,IAAI,CAAClB,SAAS,CAACsC,WAAW,CAAC,EAAEf,IAAI,CAC3CgB,GAAG,IAAKA,GAAG,CAACH,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOlB,IAAI,EAAEgB,KAAK,IAAIE,KAAK;EAC7B;EAEA7B,wBAAwBA,CAAA;IACtB,IAAI,CAACiC,SAAS,GAAG,IAAI,CAACtC,gBAAgB,CAACuC,GAAG,CAAC,WAAW,CAAE,CAACC,YAAY,CAAChC,IAAI,CACxEhF,GAAG,CAAEmG,IAAI,IAAI;MACX,IAAI,CAAC3B,gBAAgB,CAACuC,GAAG,CAAC,yBAAyB,CAAC,EAAEE,KAAK,EAAE;MAC7D,IAAI,CAACC,mBAAmB,CAACf,IAAI,CAAC;MAC9B,IAAI,IAAI,CAACgB,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,UAAU,EAAE;MAC9B;MACA,IAAI,CAAChD,eAAe,GAAG,IAAI;IAC7B,CAAC,CAAC,EACFnE,SAAS,CAAEkG,IAAY,IAAI;MACzB,IAAI,CAACA,IAAI,EAAE;QACT,IAAI,CAAC/B,eAAe,GAAG,KAAK;QAC5B,OAAOtE,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,MAAMuH,MAAM,GAAG,IAAI,CAACC,eAAe,CAACnB,IAAI,CAAC;MACzC,IAAI,CAACkB,MAAM,EAAE;QACX,IAAI,CAACjD,eAAe,GAAG,KAAK;QAC5B,OAAOtE,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,OAAO,IAAI,CAACwD,iBAAiB,CAACiE,mBAAmB,CAACF,MAAM,CAAC,CAACrC,IAAI,CAC5D9E,GAAG,CAAEmG,GAAQ,IAAKA,GAAG,CAACC,IAAI,IAAI,EAAE,CAAC;MAAE;MACnCnG,UAAU,CAAC,MAAK;QACd,IAAI,CAACiE,eAAe,GAAG,KAAK;QAC5B,OAAOtE,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,EACFE,GAAG,CAAC,MAAO,IAAI,CAACoE,eAAe,GAAG,KAAM,CAAC,CAC1C;IACH,CAAC,CAAC,EACFhE,WAAW,CAAC,CAAC,CAAC,CACf;EACH;EAEA8G,mBAAmBA,CAACf,IAAY;IAC9B,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE;QACX,IAAI,CAAC5D,gBAAgB,GAAG,CACtB;UAAEL,KAAK,EAAE,aAAa;UAAEC,MAAM,EAAE;QAAc,CAAE,EAChD;UAAED,KAAK,EAAE,eAAe;UAAEC,MAAM,EAAE;QAAW,CAAE,EAC/C;UAAED,KAAK,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAa,CAAE,EAC3C;UAAED,KAAK,EAAE,uBAAuB;UAAEC,MAAM,EAAE;QAAY,CAAE,CACzD;QACD;MACF,KAAK,MAAM;QAAE;QACX,IAAI,CAACI,gBAAgB,GAAG,CACtB;UAAEL,KAAK,EAAE,aAAa;UAAEC,MAAM,EAAE;QAAc,CAAE,EAChD;UAAED,KAAK,EAAE,eAAe;UAAEC,MAAM,EAAE;QAAW,CAAE,EAC/C;UAAED,KAAK,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAa,CAAE,EAC3C;UAAED,KAAK,EAAE,uBAAuB;UAAEC,MAAM,EAAE;QAAY,CAAE,CACzD;QACD;MACF,KAAK,MAAM;QAAE;QACX,IAAI,CAACI,gBAAgB,GAAG,CACtB;UAAEL,KAAK,EAAE,aAAa;UAAEC,MAAM,EAAE;QAAc,CAAE,EAChD;UAAED,KAAK,EAAE,eAAe;UAAEC,MAAM,EAAE;QAAW,CAAE,EAC/C;UAAED,KAAK,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAa,CAAE,EAC3C;UAAED,KAAK,EAAE,uBAAuB;UAAEC,MAAM,EAAE;QAAY,CAAE,CACzD;QACD;MACF;QACE,IAAI,CAACI,gBAAgB,GAAG,EAAE;IAC9B;EACF;EAEAiF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACtE,YAAY,EAAE;MACtB,IAAI,CAACS,cAAc,CAAC8D,GAAG,CAAC;QACtBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,IAAI,CAACnD,gBAAgB,CAACoD,UAAU,CAAC;MAC/BjD,uBAAuB,EAAE,IAAI,CAACzB,YAAY,CAACC;KAC5C,CAAC;IAEF,IAAI,CAAC0E,QAAQ,EAAE;EACjB;EAEQP,eAAeA,CAACnB,IAAY;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,qCAAqC,EAAE,IAAI,CAACpC;SAC7C;MACH,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,qCAAqC,EAAE,IAAI,CAACA;SAC7C;MACH,KAAK,MAAM;QACT,OAAO;UACL,6BAA6B,EAAE,MAAM;UACrC,qCAAqC,EAAE,IAAI,CAACA;SAC7C;MACH;QACE,OAAO,IAAI;IACf;EACF;EAEA+D,iBAAiBA,CAACC,QAAa;IAC7B,IAAI,CAAC7E,YAAY,GAAG6E,QAAQ;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACvD,gBAAgB,CAACoD,UAAU,CAAC;QAC/BjD,uBAAuB,EAAE,IAAI,CAACzB,YAAY,CAACC,WAAW,IAAI;OAC3D,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACqB,gBAAgB,CAACoD,UAAU,CAAC;QAC/BjD,uBAAuB,EAAE;OAC1B,CAAC;IACJ;EACF;EAEAkD,QAAQA,CAAA;IACN,IAAI,CAAC3D,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACM,gBAAgB,CAACwD,OAAO,EAAE;MACjC;IACF;IAEA,IAAI,CAAC7D,MAAM,GAAG,IAAI;IAClB,MAAMuC,KAAK,GAAG;MAAE,GAAG,IAAI,CAAClC,gBAAgB,CAACkC;IAAK,CAAE;IAEhD,MAAMJ,IAAI,GAAG;MACXnD,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BwB,uBAAuB,EAAE+B,KAAK,CAAC/B,uBAAuB;MACtD7C,SAAS,EAAE4E,KAAK,CAAC5E,SAAS;MAC1B2D,aAAa,EAAE;KAChB;IAED,IAAI,CAACnC,iBAAiB,CACnB2E,iBAAiB,CAAC3B,IAAI,CAAC,CACvBtB,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAC;MACTiD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/D,MAAM,GAAG,KAAK;QACnB,IAAI,CAACH,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACQ,gBAAgB,CAACyC,KAAK,EAAE;QAC7B,IAAI,CAACtD,cAAc,CAAC8D,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEF,IAAI,CAACrE,iBAAiB,CACnB6E,eAAe,CAAC,IAAI,CAAChF,WAAW,CAAC,CACjC6B,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAClCoB,SAAS,EAAE;MAChB,CAAC;MACDmD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACjE,MAAM,GAAG,KAAK;QACnB,IAAI,CAACH,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACL,cAAc,CAAC8D,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAnG,aAAaA,CAACgE,IAAS;IACrB,IAAI,CAAC5B,mBAAmB,CAACyE,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEnG,MAAM,EAAE,SAAS;MACjBoG,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,MAAM,CAACjD,IAAI;KAC/B,CAAC;EACJ;EAEAiD,MAAMA,CAACjD,IAAS;IACd,IAAI,CAAClC,iBAAiB,CACnBoF,kBAAkB,CAAClD,IAAI,CAACmD,UAAU,CAAC,CACnC3D,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAClCoB,SAAS,CAAC;MACTiD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvE,cAAc,CAAC8D,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEF,IAAI,CAACrE,iBAAiB,CACnB6E,eAAe,CAAC,IAAI,CAAChF,WAAW,CAAC,CACjC6B,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAClCoB,SAAS,EAAE;MAChB,CAAC;MACDmD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACzE,cAAc,CAAC8D,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA,IAAIiB,CAACA,CAAA;IACH,OAAO,IAAI,CAACpE,gBAAgB,CAACqE,QAAQ;EACvC;EAEAC,aAAaA,CAAC7E,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACM,gBAAgB,CAACyC,KAAK,EAAE;IAE7B,MAAM8B,OAAO,GAAG,IAAI,CAACzE,SAAS,CAAC,sBAAsB,CAAC;IACtD,IAAIyE,OAAO,EAAEC,MAAM,GAAG,CAAC,EAAE;MACvB,IAAI,CAACxE,gBAAgB,CAACuC,GAAG,CAAC,WAAW,CAAC,EAAEkC,QAAQ,CAACF,OAAO,CAAC,CAAC,CAAC,CAACrC,KAAK,CAAC;IACpE;EACF;EAEAwC,cAAcA,CAACC,KAAU;IACvB,MAAMzC,KAAK,GAAGyC,KAAK,CAACjD,MAAM,CAACQ,KAAK;IAChC,IAAI,CAAC0C,KAAK,CAACC,YAAY,CAAC3C,KAAK,EAAE,UAAU,CAAC;IAE1C;IACA,IAAI,CAAChD,GAAG,CAAC4F,aAAa,EAAE;EAC1B;EAEAlI,2BAA2BA,CAACoE,IAAS;IACnC,IAAI,CAAChC,MAAM,CAAC+F,QAAQ,CAAC,CAAC/D,IAAI,CAAC7D,oBAAoB,EAAEwB,WAAW,CAAC,EAAE;MAC7DqG,UAAU,EAAE,IAAI,CAAC/F,KAAK;MACtBgG,KAAK,EAAE;QAAEC,eAAe,EAAElE;MAAI;KAC/B,CAAC;EACJ;EAEAmE,WAAWA,CAAA;IACT,IAAI,CAAC9F,YAAY,CAACqE,IAAI,EAAE;IACxB,IAAI,CAACrE,YAAY,CAAC+F,QAAQ,EAAE;EAC9B;;;uBAzUWxG,8BAA8B,EAAA9C,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAzJ,EAAA,CAAAuJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3J,EAAA,CAAAuJ,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA7J,EAAA,CAAAuJ,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA9J,EAAA,CAAAuJ,iBAAA,CAAAvJ,EAAA,CAAA+J,iBAAA,GAAA/J,EAAA,CAAAuJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAAuJ,iBAAA,CAAAS,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAA9BpH,8BAA8B;MAAAqH,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCrBnCtK,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACjEJ,EAAA,CAAAC,cAAA,kBACyD;UADnCD,EAAA,CAAAK,UAAA,mBAAAmK,kEAAA;YAAAxK,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAA,OAAAzK,EAAA,CAAAa,WAAA,CAAS0J,GAAA,CAAA/B,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAE1DxI,EAD6D,CAAAI,YAAA,EAAW,EAClE;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAC0F;UAiDzGD,EAhDA,CAAA+B,UAAA,IAAA2I,qDAAA,0BAAgC,IAAAC,qDAAA,yBAwBU,IAAAC,qDAAA,0BAkBJ,KAAAC,sDAAA,0BAMD;UAOjD7K,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UAENJ,EAAA,CAAAC,cAAA,oBAE0D;UAFzBD,EAAA,CAAA8K,gBAAA,2BAAAC,2EAAA/J,MAAA;YAAAhB,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAAzK,EAAA,CAAAgL,kBAAA,CAAAT,GAAA,CAAA7G,gBAAA,EAAA1C,MAAA,MAAAuJ,GAAA,CAAA7G,gBAAA,GAAA1C,MAAA;YAAA,OAAAhB,EAAA,CAAAa,WAAA,CAAAG,MAAA;UAAA,EAA8B;UAS3ChB,EANhB,CAAAC,cAAA,gBAAqD,eAEpB,eAES,iBACkD,gBACrC;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAChEJ,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACrCF,EADqC,CAAAI,YAAA,EAAO,EACpC;UACRJ,EAAA,CAAAG,SAAA,sBAE8C;UAClDH,EAAA,CAAAI,YAAA,EAAM;UAGFJ,EADJ,CAAAC,cAAA,eAAkC,iBAC+C;UACzED,EAAA,CAAAE,MAAA,gBACJ;UAAAF,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,iBAC+D;UAAnCD,EAAA,CAAAK,UAAA,mBAAA4K,gEAAAjK,MAAA;YAAAhB,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAA,OAAAzK,EAAA,CAAAa,WAAA,CAAS0J,GAAA,CAAA3B,cAAA,CAAA5H,MAAA,CAAsB;UAAA,EAAC;UAEpEhB,EAHQ,CAAAI,YAAA,EAC+D,EAC7D,EACJ;UAEFJ,EADJ,CAAAC,cAAA,eAAoB,sBAK8E;;UAF1FD,EAAA,CAAA8K,gBAAA,6BAAAI,4EAAAlK,MAAA;YAAAhB,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAAzK,EAAA,CAAAgL,kBAAA,CAAAT,GAAA,CAAA3H,YAAA,EAAA5B,MAAA,MAAAuJ,GAAA,CAAA3H,YAAA,GAAA5B,MAAA;YAAA,OAAAhB,EAAA,CAAAa,WAAA,CAAAG,MAAA;UAAA,EAA4B;UA6B5BhB,EA1BA,CAAA+B,UAAA,KAAAoJ,sDAAA,yBAAgC,KAAAC,sDAAA,yBASU,KAAAC,sDAAA,0BAYJ,KAAAC,sDAAA,0BAKD;UAM7CtL,EADI,CAAAI,YAAA,EAAU,EACR;UAGFJ,EADJ,CAAAC,cAAA,eAAqD,kBAGV;UAAnCD,EAAA,CAAAK,UAAA,mBAAAkL,iEAAA;YAAAvL,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAA,OAAAzK,EAAA,CAAAa,WAAA,CAAA0J,GAAA,CAAA7G,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAC1D,EAAA,CAAAI,YAAA,EAAS;UAChDJ,EAAA,CAAAC,cAAA,kBAEkC;UAA9BD,EAAA,CAAAK,UAAA,mBAAAmL,iEAAA;YAAAxL,EAAA,CAAAQ,aAAA,CAAAiK,GAAA;YAAA,OAAAzK,EAAA,CAAAa,WAAA,CAAS0J,GAAA,CAAArD,iBAAA,EAAmB;UAAA,EAAC;UAI7ClH,EAJ8C,CAAAI,YAAA,EAAS,EACzC,EAEH,EACA;;;UAzICJ,EAAA,CAAAmB,SAAA,GAAmC;UAACnB,EAApC,CAAA8B,UAAA,oCAAmC,iBAAiB;UAI/C9B,EAAA,CAAAmB,SAAA,GAA6B;UAA0BnB,EAAvD,CAAA8B,UAAA,UAAAyI,GAAA,CAAA/G,mBAAA,CAA6B,YAAyB,mBAAmB;UA2DtFxD,EAAA,CAAAmB,SAAA,GAA8C;UAA9CnB,EAAA,CAAAyL,UAAA,CAAAzL,EAAA,CAAA0L,eAAA,KAAAC,GAAA,EAA8C;UADjB3L,EAAA,CAAA4L,gBAAA,YAAArB,GAAA,CAAA7G,gBAAA,CAA8B;UAE3D1D,EAF4D,CAAA8B,UAAA,eAAc,kBACV,mBAAmB,oBAAoB,gBAAA9B,EAAA,CAAA0L,eAAA,KAAAG,GAAA,EAClD;UAC/C7L,EAAA,CAAAmB,SAAA,EAA8B;UAA9BnB,EAAA,CAAA8B,UAAA,cAAAyI,GAAA,CAAArG,gBAAA,CAA8B;UAWpBlE,EAAA,CAAAmB,SAAA,GAA4B;UAA5BnB,EAAA,CAAAyL,UAAA,CAAAzL,EAAA,CAAA0L,eAAA,KAAAI,GAAA,EAA4B;UAFQ9L,EAAA,CAAA8B,UAAA,YAAAyI,GAAA,CAAAvG,SAAA,yBAA6C;UAUjFhE,EAAA,CAAAmB,SAAA,GAAuB;UAAvBnB,EAAA,CAAA+L,WAAA,kBAAuB;UAIlB/L,EAAA,CAAAmB,SAAA,GAAmC;UACTnB,EAD1B,CAAA8B,UAAA,UAAA9B,EAAA,CAAAgM,WAAA,SAAAzB,GAAA,CAAA/D,SAAA,KAAAxG,EAAA,CAAA0L,eAAA,KAAAO,GAAA,EAAmC,mBAAmB,WAAW,uBAAAjM,EAAA,CAAA0L,eAAA,KAAAQ,GAAA,EACxC,8BAA8B;UAChElM,EAAA,CAAA4L,gBAAA,cAAArB,GAAA,CAAA3H,YAAA,CAA4B;UAE5B5C,EAFmD,CAAA8B,UAAA,YAAAyI,GAAA,CAAAzG,eAAA,CAA2B,oBAC3D,mBAAmB,uBAAA9D,EAAA,CAAA0L,eAAA,KAAAS,GAAA,EACmD;UAwCfnM,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAA8B,UAAA,cAAAyI,GAAA,CAAA3H,YAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}