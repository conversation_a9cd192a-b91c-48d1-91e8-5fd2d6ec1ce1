{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime = 0, intervalOrScheduler, scheduler = asyncScheduler) {\n  let intervalDuration = -1;\n  if (intervalOrScheduler != null) {\n    if (isScheduler(intervalOrScheduler)) {\n      scheduler = intervalOrScheduler;\n    } else {\n      intervalDuration = intervalOrScheduler;\n    }\n  }\n  return new Observable(subscriber => {\n    let due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n    if (due < 0) {\n      due = 0;\n    }\n    let n = 0;\n    return scheduler.schedule(function () {\n      if (!subscriber.closed) {\n        subscriber.next(n++);\n        if (0 <= intervalDuration) {\n          this.schedule(undefined, intervalDuration);\n        } else {\n          subscriber.complete();\n        }\n      }\n    }, due);\n  });\n}\n//# sourceMappingURL=timer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}