{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/multiselect\";\nfunction AccountSalesTeamComponent_ng_template_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 17);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 17);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 18);\n    i0.ɵɵlistener(\"click\", function AccountSalesTeamComponent_ng_template_8_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountSalesTeamComponent_ng_template_8_ng_container_6_i_4_Template, 1, 1, \"i\", 13)(5, AccountSalesTeamComponent_ng_template_8_ng_container_6_i_5_Template, 1, 0, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 11);\n    i0.ɵɵlistener(\"click\", function AccountSalesTeamComponent_ng_template_8_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"business_partner.first_name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 12);\n    i0.ɵɵtext(3, \" First Name \");\n    i0.ɵɵtemplate(4, AccountSalesTeamComponent_ng_template_8_i_4_Template, 1, 1, \"i\", 13)(5, AccountSalesTeamComponent_ng_template_8_i_5_Template, 1, 0, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountSalesTeamComponent_ng_template_8_ng_container_6_Template, 6, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"business_partner.first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"business_partner.first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.last_name) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.partner_role) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.addresses == null ? null : team_r5.business_partner.addresses[0] == null ? null : team_r5.business_partner.addresses[0].emails == null ? null : team_r5.business_partner.addresses[0].emails[0] == null ? null : team_r5.business_partner.addresses[0].emails[0].email_address) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 21);\n    i0.ɵɵtemplate(3, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 22)(4, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 22)(5, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.last_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_role\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"address.email_address\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 19)(1, \"td\", 20);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSalesTeamComponent_ng_template_9_ng_container_3_Template, 6, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.first_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 23);\n    i0.ɵɵtext(2, \"No Sales Teams found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 23);\n    i0.ɵɵtext(2, \"Loading Sales Teams data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AccountSalesTeamComponent = /*#__PURE__*/(() => {\n  class AccountSalesTeamComponent {\n    constructor(accountservice) {\n      this.accountservice = accountservice;\n      this.unsubscribe$ = new Subject();\n      this.salesteamDetails = [];\n      this.id = '';\n      this.partnerfunction = [];\n      this.partnerLoading = false;\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'business_partner.last_name',\n        header: 'Last Name'\n      }, {\n        field: 'partner_role',\n        header: 'Role'\n      }, {\n        field: 'address.email_address',\n        header: 'Email'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.salesteamDetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadPartners();\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadPartners() {\n      this.partnerLoading = true;\n      this.accountservice.getCRMPartner().pipe(takeUntil(this.unsubscribe$), switchMap(partners => {\n        this.partnerfunction = partners || [];\n        return this.accountservice.account.pipe(takeUntil(this.unsubscribe$));\n      })).subscribe({\n        next: response => {\n          if (!response) return;\n          this.id = response?.customer?.customer_id;\n          const filteredPartners = response.customer.partner_functions.filter(pf => this.partnerfunction.some(partner => partner?.value === pf.partner_function));\n          if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\n            this.salesteamDetails = filteredPartners.map(pf => {\n              const matchedPartner = this.partnerfunction.find(partner => partner?.value === pf.partner_function);\n              return {\n                ...pf,\n                partner_role: matchedPartner ? matchedPartner?.label : null,\n                // Adding partner label\n                addresses: this.filterXXDefaultAddresses(response?.customer?.partner_functions?.business_partner?.addresses || [])\n              };\n            });\n          } else {\n            this.salesteamDetails = [];\n          }\n          this.partnerLoading = false;\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n          this.partnerfunction = [];\n          this.salesteamDetails = [];\n          this.partnerLoading = false;\n        },\n        complete: () => {\n          console.log('Partner function and employee details loaded successfully.');\n        }\n      });\n    }\n    filterXXDefaultAddresses(addresses) {\n      return addresses.filter(address => address.address_usages && address.address_usages.some(usage => usage.address_usage === 'XXDEFAULT'));\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountSalesTeamComponent_Factory(t) {\n        return new (t || AccountSalesTeamComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountSalesTeamComponent,\n        selectors: [[\"app-account-sales-team\"]],\n        decls: 12,\n        vars: 9,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"font-medium\", \"border-round-left-lg\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"]],\n        template: function AccountSalesTeamComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Sales Team\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-multiSelect\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSalesTeamComponent_Template_p_multiSelect_ngModelChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-table\", 6);\n            i0.ɵɵlistener(\"onColReorder\", function AccountSalesTeamComponent_Template_p_table_onColReorder_7_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(8, AccountSalesTeamComponent_ng_template_8_Template, 7, 3, \"ng-template\", 7)(9, AccountSalesTeamComponent_ng_template_9_Template, 4, 2, \"ng-template\", 8)(10, AccountSalesTeamComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9)(11, AccountSalesTeamComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.salesteamDetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.PrimeTemplate, i4.Table, i4.SortableColumn, i4.FrozenColumn, i4.ReorderableColumn, i5.NgControlStatus, i5.NgModel, i6.MultiSelect]\n      });\n    }\n  }\n  return AccountSalesTeamComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}