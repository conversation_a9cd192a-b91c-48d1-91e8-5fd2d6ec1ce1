{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DashboardComponent {\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"dashboard works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\dashboard\\dashboard.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrl: './dashboard.component.scss'\r\n})\r\nexport class DashboardComponent {\r\n\r\n}\r\n", "<p>dashboard works!</p>\r\n"], "mappings": ";AASA,OAAM,MAAOA,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BN,EAAA,CAAAQ,cAAA,QAAG;UAAAR,EAAA,CAAAS,MAAA,uBAAgB;UAAAT,EAAA,CAAAU,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}