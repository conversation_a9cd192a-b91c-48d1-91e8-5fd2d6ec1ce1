{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../opportunities/opportunities.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/tabview\";\nimport * as i7 from \"primeng/toast\";\nimport * as i8 from \"primeng/confirmdialog\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"../../../shared/initials.pipe\";\nfunction OrganizationDetailsComponent_p_tabPanel_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction OrganizationDetailsComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 30);\n    i0.ɵɵtemplate(1, OrganizationDetailsComponent_p_tabPanel_8_ng_template_1_Template, 2, 2, \"ng-template\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let OrganizationDetailsComponent = /*#__PURE__*/(() => {\n  class OrganizationDetailsComponent {\n    constructor(router, route, opportunitiesservice) {\n      this.router = router;\n      this.route = route;\n      this.opportunitiesservice = opportunitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.opportunityDetails = null;\n      this.sidebarDetails = null;\n      this.items = [];\n      this.id = '';\n      this.partner_role = '';\n      this.breadcrumbitems = [];\n      this.activeItem = null;\n      this.isSidebarHidden = false;\n      this.activeIndex = 0;\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const opportunityId = params.get('id');\n        if (opportunityId) {\n          this.loadOpportunityData(opportunityId);\n        }\n      });\n      // Listen for route changes to keep active tab in sync\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        const partner_role = response?.business_partner?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n        this.partner_role = partner_role?.bp_full_name || null;\n        this.opportunityDetails = response || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.business_partner?.addresses || []);\n      });\n    }\n    formatSidebarDetails(addresses) {\n      return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n        ...address,\n        address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n        email_address: address?.emails?.[0]?.email_address || '-',\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n        website_url: address?.home_page_urls?.[0]?.website_url || '-'\n      }));\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'General',\n        routerLink: `/store/organization/${id}/general`\n      }, {\n        label: 'Functions',\n        routerLink: `/store/organization/${id}/functions`\n      }, {\n        label: 'Employees',\n        routerLink: `/store/organization/${id}/employees`\n      }];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'general';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'General');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Organization',\n        routerLink: ['/store/organization']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    loadOpportunityData(activityId) {\n      this.opportunitiesservice.getOpportunityByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.opportunityDetails = response?.data[0] || null;\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    goToBack() {\n      this.router.navigate(['/store/organization']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OrganizationDetailsComponent_Factory(t) {\n        return new (t || OrganizationDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OrganizationDetailsComponent,\n        selectors: [[\"app-organization-details\"]],\n        decls: 76,\n        vars: 26,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\", \"sidebar-c-details\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function OrganizationDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"p-tabView\", 7);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function OrganizationDetailsComponent_Template_p_tabView_activeIndexChange_7_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function OrganizationDetailsComponent_Template_p_tabView_onChange_7_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(8, OrganizationDetailsComponent_p_tabPanel_8_Template, 2, 1, \"p-tabPanel\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"h5\", 16);\n            i0.ɵɵtext(17);\n            i0.ɵɵpipe(18, \"initials\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 17)(20, \"h5\", 18);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"ul\", 19)(23, \"li\", 20)(24, \"span\", 21);\n            i0.ɵɵtext(25, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"li\", 20)(28, \"span\", 21);\n            i0.ɵɵtext(29, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"li\", 20)(32, \"span\", 21);\n            i0.ɵɵtext(33, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(35, \"div\", 22)(36, \"ul\", 23)(37, \"li\", 24)(38, \"span\", 25)(39, \"i\", 26);\n            i0.ɵɵtext(40, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(41, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"span\", 27);\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"li\", 24)(45, \"span\", 25)(46, \"i\", 26);\n            i0.ɵɵtext(47, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(48, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\", 27);\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"li\", 24)(52, \"span\", 25)(53, \"i\", 26);\n            i0.ɵɵtext(54, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(55, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"span\", 27);\n            i0.ɵɵtext(57);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"li\", 24)(59, \"span\", 25)(60, \"i\", 26);\n            i0.ɵɵtext(61, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(62, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"span\", 27);\n            i0.ɵɵtext(64);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"li\", 24)(66, \"span\", 25)(67, \"i\", 26);\n            i0.ɵɵtext(68, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(69, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"span\", 27);\n            i0.ɵɵtext(71);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(72, \"div\", 28)(73, \"p-button\", 29);\n            i0.ɵɵlistener(\"click\", function OrganizationDetailsComponent_Template_p_button_click_73_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(74, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(75, \"p-confirmDialog\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 24, ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_full_name));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" : \", (ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ((ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.contact_companies == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0] == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.opportunityDetails == null ? null : ctx.opportunityDetails.business_partner == null ? null : ctx.opportunityDetails.business_partner.contact_companies == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0] == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person == null ? null : ctx.opportunityDetails.business_partner.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails.contact_companies == null ? null : ctx.sidebarDetails.contact_companies[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.sidebarDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i3.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.Button, i5.PrimeTemplate, i6.TabView, i6.TabPanel, i7.Toast, i8.ConfirmDialog, i9.Breadcrumb, i10.InitialsPipe]\n      });\n    }\n  }\n  return OrganizationDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}