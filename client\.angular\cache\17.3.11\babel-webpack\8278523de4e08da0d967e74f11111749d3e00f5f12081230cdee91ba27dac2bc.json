{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ImportRoutingModule } from './import-routing.module';\nimport { ImportComponent } from './import.component';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { TableModule } from 'primeng/table';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { DropdownModule } from 'primeng/dropdown';\nimport * as i0 from \"@angular/core\";\nexport class ImportModule {\n  static {\n    this.ɵfac = function ImportModule_Factory(t) {\n      return new (t || ImportModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ImportModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ConfirmationService, MessageService],\n      imports: [CommonModule, ConfirmDialogModule, TabMenuModule, TableModule, ProgressBarModule, DropdownModule, ToastModule, BreadcrumbModule, ImportRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ImportModule, {\n    declarations: [ImportComponent],\n    imports: [CommonModule, ConfirmDialogModule, TabMenuModule, TableModule, ProgressBarModule, DropdownModule, ToastModule, BreadcrumbModule, ImportRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ImportRoutingModule", "ImportComponent", "ConfirmDialogModule", "TabMenuModule", "TableModule", "ProgressBarModule", "ToastModule", "ConfirmationService", "MessageService", "BreadcrumbModule", "DropdownModule", "ImportModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\import\\import.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImportRoutingModule } from './import-routing.module';\r\nimport { ImportComponent } from './import.component';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ProgressBarModule } from 'primeng/progressbar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ImportComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ConfirmDialogModule,\r\n    TabMenuModule,\r\n    TableModule,\r\n    ProgressBarModule,\r\n    DropdownModule,\r\n    ToastModule,\r\n    BreadcrumbModule,\r\n    ImportRoutingModule,\r\n  ],\r\n  providers: [\r\n    ConfirmationService,\r\n    MessageService\r\n  ],\r\n})\r\nexport class ImportModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;;AAuBjD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;iBALZ,CACTJ,mBAAmB,EACnBC,cAAc,CACf;MAAAI,OAAA,GAbCb,YAAY,EACZG,mBAAmB,EACnBC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBK,cAAc,EACdJ,WAAW,EACXG,gBAAgB,EAChBT,mBAAmB;IAAA;EAAA;;;2EAOVW,YAAY;IAAAE,YAAA,GAlBrBZ,eAAe;IAAAW,OAAA,GAGfb,YAAY,EACZG,mBAAmB,EACnBC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBK,cAAc,EACdJ,WAAW,EACXG,gBAAgB,EAChBT,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}