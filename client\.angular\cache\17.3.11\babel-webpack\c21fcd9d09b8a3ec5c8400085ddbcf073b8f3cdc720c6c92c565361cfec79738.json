{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsListingComponent } from './service-tickets-listing/service-tickets-listing.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ServiceTicketsListingComponent\n}];\nexport let ServiceTicketsListingRoutingModule = /*#__PURE__*/(() => {\n  class ServiceTicketsListingRoutingModule {\n    static {\n      this.ɵfac = function ServiceTicketsListingRoutingModule_Factory(t) {\n        return new (t || ServiceTicketsListingRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ServiceTicketsListingRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ServiceTicketsListingRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}