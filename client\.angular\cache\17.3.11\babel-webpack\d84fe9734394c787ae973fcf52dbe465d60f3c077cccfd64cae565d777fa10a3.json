{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, signal, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { BanIcon } from 'primeng/icons/ban';\nimport { StarIcon } from 'primeng/icons/star';\nimport { StarFillIcon } from 'primeng/icons/starfill';\nimport { DomHandler } from 'primeng/dom';\nimport { UniqueComponentId } from 'primeng/utils';\nconst _c0 = (a0, a1) => ({\n  \"p-readonly\": a0,\n  \"p-disabled\": a1\n});\nconst _c1 = a0 => ({\n  \"p-focus\": a0\n});\nconst _c2 = (a0, a1) => ({\n  \"p-rating-item-active\": a0,\n  \"p-focus\": a1\n});\nfunction Rating_ng_container_1_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.iconCancelClass)(\"ngStyle\", ctx_r1.iconCancelStyle);\n  }\n}\nfunction Rating_ng_container_1_div_1_BanIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BanIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"styleClass\", \"p-rating-icon p-rating-cancel\")(\"ngStyle\", ctx_r1.iconCancelStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"cancelIcon\");\n  }\n}\nfunction Rating_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function Rating_ng_container_1_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionClick($event, 0));\n    });\n    i0.ɵɵelementStart(1, \"span\", 6)(2, \"input\", 7);\n    i0.ɵɵlistener(\"focus\", function Rating_ng_container_1_div_1_Template_input_focus_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInputFocus($event, 0));\n    })(\"blur\", function Rating_ng_container_1_div_1_Template_input_blur_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInputBlur($event));\n    })(\"change\", function Rating_ng_container_1_div_1_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChange($event, 0));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(3, Rating_ng_container_1_div_1_span_3_Template, 1, 2, \"span\", 8)(4, Rating_ng_container_1_div_1_BanIcon_4_Template, 1, 3, \"BanIcon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c1, ctx_r1.focusedOptionIndex() === 0 && ctx_r1.isFocusVisible));\n    i0.ɵɵattribute(\"data-pc-section\", \"cancelItem\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.name)(\"checked\", ctx_r1.value === 0)(\"disabled\", ctx_r1.disabled)(\"readonly\", ctx_r1.readonly);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.cancelAriaLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.iconCancelClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.iconCancelClass);\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.iconOffStyle)(\"ngClass\", ctx_r1.iconOffClass);\n    i0.ɵɵattribute(\"data-pc-section\", \"offIcon\");\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_3_StarIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"StarIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.iconOffStyle)(\"styleClass\", \"p-rating-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"offIcon\");\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Rating_ng_container_1_ng_template_2_ng_container_3_span_1_Template, 1, 3, \"span\", 14)(2, Rating_ng_container_1_ng_template_2_ng_container_3_StarIcon_2_Template, 1, 3, \"StarIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.iconOffClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.iconOffClass);\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.iconOnStyle)(\"ngClass\", ctx_r1.iconOnClass);\n    i0.ɵɵattribute(\"data-pc-section\", \"onIcon\");\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_4_StarFillIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"StarFillIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.iconOnStyle)(\"styleClass\", \"p-rating-icon p-rating-icon-active\");\n    i0.ɵɵattribute(\"data-pc-section\", \"onIcon\");\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Rating_ng_container_1_ng_template_2_ng_container_4_span_1_Template, 1, 3, \"span\", 18)(2, Rating_ng_container_1_ng_template_2_ng_container_4_StarFillIcon_2_Template, 1, 3, \"StarFillIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.iconOnClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.iconOnClass);\n  }\n}\nfunction Rating_ng_container_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function Rating_ng_container_1_ng_template_2_Template_div_click_0_listener($event) {\n      const star_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionClick($event, star_r4 + 1));\n    });\n    i0.ɵɵelementStart(1, \"span\", 6)(2, \"input\", 7);\n    i0.ɵɵlistener(\"focus\", function Rating_ng_container_1_ng_template_2_Template_input_focus_2_listener($event) {\n      const star_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInputFocus($event, star_r4 + 1));\n    })(\"blur\", function Rating_ng_container_1_ng_template_2_Template_input_blur_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInputBlur($event));\n    })(\"change\", function Rating_ng_container_1_ng_template_2_Template_input_change_2_listener($event) {\n      const star_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChange($event, star_r4 + 1));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(3, Rating_ng_container_1_ng_template_2_ng_container_3_Template, 3, 2, \"ng-container\", 13)(4, Rating_ng_container_1_ng_template_2_ng_container_4_Template, 3, 2, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c2, star_r4 + 1 <= ctx_r1.value, star_r4 + 1 === ctx_r1.focusedOptionIndex() && ctx_r1.isFocusVisible));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.name)(\"checked\", ctx_r1.value === 0)(\"disabled\", ctx_r1.disabled)(\"readonly\", ctx_r1.readonly);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.starAriaLabel(star_r4 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.value || i_r5 >= ctx_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.value && i_r5 < ctx_r1.value);\n  }\n}\nfunction Rating_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Rating_ng_container_1_div_1_Template, 5, 12, \"div\", 3)(2, Rating_ng_container_1_ng_template_2_Template, 5, 12, \"ng-template\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cancel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.starsArray);\n  }\n}\nfunction Rating_ng_template_2_span_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Rating_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵlistener(\"click\", function Rating_ng_template_2_span_0_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionClick($event, 0));\n    });\n    i0.ɵɵtemplate(1, Rating_ng_template_2_span_0_ng_container_1_Template, 1, 0, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.iconCancelStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"cancelIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.cancelIconTemplate);\n  }\n}\nfunction Rating_ng_template_2_span_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Rating_ng_template_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵlistener(\"click\", function Rating_ng_template_2_span_1_Template_span_click_0_listener($event) {\n      const star_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOptionClick($event, star_r8 + 1));\n    });\n    i0.ɵɵtemplate(1, Rating_ng_template_2_span_1_ng_container_1_Template, 1, 0, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"onIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.getIconTemplate(i_r9));\n  }\n}\nfunction Rating_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Rating_ng_template_2_span_0_Template, 2, 3, \"span\", 20)(1, Rating_ng_template_2_span_1_Template, 2, 2, \"span\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cancel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.starsArray);\n  }\n}\nconst RATING_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Rating),\n  multi: true\n};\n/**\n * Rating is an extension to standard radio button element with theming.\n * @group Components\n */\nlet Rating = /*#__PURE__*/(() => {\n  class Rating {\n    cd;\n    config;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When present, changing the value is not possible.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Number of stars.\n     * @group Props\n     */\n    stars = 5;\n    /**\n     * When specified a cancel icon is displayed to allow removing the value.\n     * @group Props\n     */\n    cancel = true;\n    /**\n     * Style class of the on icon.\n     * @group Props\n     */\n    iconOnClass;\n    /**\n     * Inline style of the on icon.\n     * @group Props\n     */\n    iconOnStyle;\n    /**\n     * Style class of the off icon.\n     * @group Props\n     */\n    iconOffClass;\n    /**\n     * Inline style of the off icon.\n     * @group Props\n     */\n    iconOffStyle;\n    /**\n     * Style class of the cancel icon.\n     * @group Props\n     */\n    iconCancelClass;\n    /**\n     * Inline style of the cancel icon.\n     * @group Props\n     */\n    iconCancelStyle;\n    /**\n     * Emitted on value change.\n     * @param {RatingRateEvent} value - Custom rate event.\n     * @group Emits\n     */\n    onRate = new EventEmitter();\n    /**\n     * Emitted when the rating is cancelled.\n     * @param {Event} value - Browser event.\n     * @group Emits\n     */\n    onCancel = new EventEmitter();\n    /**\n     * Emitted when the rating receives focus.\n     * @param {Event} value - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Emitted when the rating loses focus.\n     * @param {Event} value - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    templates;\n    onIconTemplate;\n    offIconTemplate;\n    cancelIconTemplate;\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    starsArray;\n    isFocusVisibleItem = true;\n    focusedOptionIndex = signal(-1);\n    name;\n    constructor(cd, config) {\n      this.cd = cd;\n      this.config = config;\n    }\n    ngOnInit() {\n      this.name = this.name || UniqueComponentId();\n      this.starsArray = [];\n      for (let i = 0; i < this.stars; i++) {\n        this.starsArray[i] = i;\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'onicon':\n            this.onIconTemplate = item.template;\n            break;\n          case 'officon':\n            this.offIconTemplate = item.template;\n            break;\n          case 'cancelicon':\n            this.cancelIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onOptionClick(event, value) {\n      if (!this.readonly && !this.disabled) {\n        this.onOptionSelect(event, value);\n        this.isFocusVisibleItem = false;\n        const firstFocusableEl = DomHandler.getFirstFocusableElement(event.currentTarget, '');\n        firstFocusableEl && DomHandler.focus(firstFocusableEl);\n      }\n    }\n    onOptionSelect(event, value) {\n      this.focusedOptionIndex.set(value);\n      this.updateModel(event, value || null);\n    }\n    onChange(event, value) {\n      this.onOptionSelect(event, value);\n      this.isFocusVisibleItem = true;\n    }\n    onInputBlur(event) {\n      this.focusedOptionIndex.set(-1);\n      this.onBlur.emit(event);\n    }\n    onInputFocus(event, value) {\n      this.focusedOptionIndex.set(value);\n      this.onFocus.emit(event);\n    }\n    updateModel(event, value) {\n      this.value = value;\n      this.onModelChange(this.value);\n      this.onModelTouched();\n      if (!value) {\n        this.onCancel.emit();\n      } else {\n        this.onRate.emit({\n          originalEvent: event,\n          value\n        });\n      }\n    }\n    cancelAriaLabel() {\n      return this.config.translation.clear;\n    }\n    starAriaLabel(value) {\n      return value === 1 ? this.config.translation.aria.star : this.config.translation.aria.stars.replace(/{star}/g, value);\n    }\n    getIconTemplate(i) {\n      return !this.value || i >= this.value ? this.offIconTemplate : this.onIconTemplate;\n    }\n    writeValue(value) {\n      this.value = value;\n      this.cd.detectChanges();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    get isCustomIcon() {\n      return this.templates && this.templates.length > 0;\n    }\n    static ɵfac = function Rating_Factory(t) {\n      return new (t || Rating)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Rating,\n      selectors: [[\"p-rating\"]],\n      contentQueries: function Rating_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: \"disabled\",\n        readonly: \"readonly\",\n        stars: \"stars\",\n        cancel: \"cancel\",\n        iconOnClass: \"iconOnClass\",\n        iconOnStyle: \"iconOnStyle\",\n        iconOffClass: \"iconOffClass\",\n        iconOffStyle: \"iconOffStyle\",\n        iconCancelClass: \"iconCancelClass\",\n        iconCancelStyle: \"iconCancelStyle\"\n      },\n      outputs: {\n        onRate: \"onRate\",\n        onCancel: \"onCancel\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      features: [i0.ɵɵProvidersFeature([RATING_VALUE_ACCESSOR])],\n      decls: 4,\n      vars: 8,\n      consts: [[\"customTemplate\", \"\"], [1, \"p-rating\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-rating-item p-rating-cancel-item\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"p-rating-item\", \"p-rating-cancel-item\", 3, \"click\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"radio\", \"value\", \"0\", 3, \"focus\", \"blur\", \"change\", \"name\", \"checked\", \"disabled\", \"readonly\"], [\"class\", \"p-rating-icon p-rating-cancel\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"styleClass\", \"ngStyle\", 4, \"ngIf\"], [1, \"p-rating-icon\", \"p-rating-cancel\", 3, \"ngClass\", \"ngStyle\"], [3, \"styleClass\", \"ngStyle\"], [1, \"p-rating-item\", 3, \"click\", \"ngClass\"], [4, \"ngIf\"], [\"class\", \"p-rating-icon\", 3, \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [3, \"ngStyle\", \"styleClass\", 4, \"ngIf\"], [1, \"p-rating-icon\", 3, \"ngStyle\", \"ngClass\"], [3, \"ngStyle\", \"styleClass\"], [\"class\", \"p-rating-icon p-rating-icon-active\", 3, \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [1, \"p-rating-icon\", \"p-rating-icon-active\", 3, \"ngStyle\", \"ngClass\"], [\"class\", \"p-rating-icon p-rating-cancel\", 3, \"ngStyle\", \"click\", 4, \"ngIf\"], [\"class\", \"p-rating-icon\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-rating-icon\", \"p-rating-cancel\", 3, \"click\", \"ngStyle\"], [4, \"ngTemplateOutlet\"], [1, \"p-rating-icon\", 3, \"click\"]],\n      template: function Rating_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, Rating_ng_container_1_Template, 3, 2, \"ng-container\", 2)(2, Rating_ng_template_2_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const customTemplate_r10 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, ctx.readonly, ctx.disabled));\n          i0.ɵɵattribute(\"data-pc-name\", \"rating\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isCustomIcon)(\"ngIfElse\", customTemplate_r10);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, StarFillIcon, StarIcon, BanIcon],\n      styles: [\"@layer primeng{.p-rating{display:inline-flex}.p-rating-icon{cursor:pointer}.p-rating.p-rating-readonly .p-rating-icon{cursor:default}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Rating;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RatingModule = /*#__PURE__*/(() => {\n  class RatingModule {\n    static ɵfac = function RatingModule_Factory(t) {\n      return new (t || RatingModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RatingModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, StarFillIcon, StarIcon, BanIcon, SharedModule]\n    });\n  }\n  return RatingModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RATING_VALUE_ACCESSOR, Rating, RatingModule };\n//# sourceMappingURL=primeng-rating.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}