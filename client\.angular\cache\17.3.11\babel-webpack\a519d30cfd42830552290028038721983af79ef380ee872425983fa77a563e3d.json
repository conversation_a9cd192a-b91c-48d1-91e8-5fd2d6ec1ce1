{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/calendar\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/toast\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction AddAppointmentsComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddAppointmentsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, AddAppointmentsComponent_div_15_div_1_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"bp_full_name\"].errors && ctx_r0.f[\"bp_full_name\"].errors[\"required\"]);\n  }\n}\nfunction AddAppointmentsComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddAppointmentsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, AddAppointmentsComponent_div_23_div_1_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nexport let AddAppointmentsComponent = /*#__PURE__*/(() => {\n  class AddAppointmentsComponent {\n    constructor(formBuilder, activitiesservice, messageservice, router) {\n      this.formBuilder = formBuilder;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.router = router;\n      this.ngUnsubscribe = new Subject();\n      this.AppointmentForm = this.formBuilder.group({\n        bp_full_name: ['', [Validators.required]],\n        email_address: ['', [Validators.email]],\n        website_url: [''],\n        owner: [''],\n        additional_street_prefix_name: [''],\n        additional_street_suffix_name: [''],\n        house_number: [''],\n        street_name: [''],\n        city_name: [''],\n        region: ['', [Validators.required]],\n        country: ['', [Validators.required]],\n        postal_code: [''],\n        fax_number: [''],\n        phone_number: ['']\n      });\n      this.submitted = false;\n      this.saving = false;\n      this.existingMessage = '';\n      this.partnerfunction = [];\n      this.partnerLoading = false;\n      this.cpDepartments = [];\n    }\n    ngOnInit() {}\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.AppointmentForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.AppointmentForm.value\n        };\n        const data = {\n          bp_full_name: value?.bp_full_name,\n          email_address: value?.email_address,\n          fax_number: value?.fax_number,\n          website_url: value?.website_url,\n          phone_number: value?.phone_number,\n          house_number: value?.house_number,\n          additional_street_prefix_name: value?.additional_street_prefix_name,\n          additional_street_suffix_name: value?.additional_street_suffix_name,\n          street_name: value?.street_name,\n          city_name: value?.city_name,\n          postal_code: value?.postal_code,\n          region: value?.region,\n          contacts: Array.isArray(value.contacts) ? value.contacts : [],\n          // Ensures contacts is an array\n          employees: value.employees\n        };\n        // this.activitiesservice\n        //   .createProspect(data)\n        //   .pipe(takeUntil(this.ngUnsubscribe))\n        //   .subscribe({\n        //     next: (response: any) => {\n        //       if (response?.data?.documentId) {\n        //         sessionStorage.setItem(\n        //           'prospectMessage',\n        //           'Prospect created successfully!'\n        //         );\n        //         window.location.href = `${window.location.origin}#/store/prospects/${response?.data?.bp_id}/overview`;\n        //       } else {\n        //         console.error('Missing documentId in response:', response);\n        //       }\n        //     },\n        //     error: (res: any) => {\n        //       this.saving = false;\n        //       const msg: any = res?.error?.message || null;\n        //       if (msg) {\n        //         if (\n        //           msg &&\n        //           msg.includes('unique constraint violated') &&\n        //           msg.includes(\"constraint='EMAIL'\")\n        //         ) {\n        //           this.messageservice.add({\n        //             severity: 'error',\n        //             detail: 'Given email address already in use.',\n        //           });\n        //         } else {\n        //           this.messageservice.add({\n        //             severity: 'error',\n        //             detail: res?.error?.message,\n        //           });\n        //         }\n        //       } else {\n        //         this.messageservice.add({\n        //           severity: 'error',\n        //           detail: 'Error while processing your request.',\n        //         });\n        //       }\n        //     },\n        //   });\n      })();\n    }\n    addNewContact() {\n      this.contacts.push(this.createContactFormGroup());\n    }\n    addNewEmployee() {\n      this.employees.push(this.createEmployeeFormGroup());\n    }\n    createContactFormGroup() {\n      return this.formBuilder.group({\n        first_name: ['', Validators.required],\n        last_name: ['', Validators.required],\n        contact_person_department_name: ['', Validators.required],\n        contact_person_department: ['', Validators.required],\n        email_address: ['', Validators.required],\n        phone_number: ['', Validators.required]\n      });\n    }\n    createEmployeeFormGroup() {\n      return this.formBuilder.group({\n        partner_function: [null, Validators.required],\n        bp_customer_number: [null, Validators.required]\n      });\n    }\n    deleteContact(index) {\n      if (this.contacts.length > 1) {\n        this.contacts.removeAt(index);\n      }\n    }\n    deleteEmployee(index) {\n      if (this.employees.length > 1) {\n        this.employees.removeAt(index);\n      }\n    }\n    isFieldInvalid(index, field, arrayName) {\n      const control = this.AppointmentForm.get(arrayName).at(index).get(field);\n      return control?.invalid && (control?.touched || this.submitted);\n    }\n    get f() {\n      return this.AppointmentForm.controls;\n    }\n    get contacts() {\n      return this.AppointmentForm.get('contacts');\n    }\n    get employees() {\n      return this.AppointmentForm.get('employees');\n    }\n    onCancel() {\n      this.router.navigate(['/store/activities/appointments']);\n    }\n    onReset() {\n      this.submitted = false;\n      this.AppointmentForm.reset();\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function AddAppointmentsComponent_Factory(t) {\n        return new (t || AddAppointmentsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddAppointmentsComponent,\n        selectors: [[\"app-add-appointments\"]],\n        decls: 79,\n        vars: 15,\n        consts: [[\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"bp_full_name\", \"type\", \"text\", \"formControlName\", \"bp_full_name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"pInputText\", \"\", \"id\", \"email_address\", \"type\", \"text\", \"formControlName\", \"email_address\", \"placeholder\", \"Email Address\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"website_url\", \"type\", \"text\", \"formControlName\", \"website_url\", \"placeholder\", \"Website\", 1, \"h-3rem\", \"w-full\"], [\"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"formControlName\", \"country\", \"placeholder\", \"Select Country\", 3, \"styleClass\"], [3, \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"CANCEL\", 1, \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"CREATE\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"]],\n        template: function AddAppointmentsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"form\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n            i0.ɵɵtext(4, \"Create Appointment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7)(9, \"span\", 8);\n            i0.ɵɵtext(10, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Subject \");\n            i0.ɵɵelementStart(12, \"span\", 9);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(14, \"input\", 10);\n            i0.ɵɵtemplate(15, AddAppointmentsComponent_div_15_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 5)(17, \"div\", 6)(18, \"label\", 7)(19, \"span\", 12);\n            i0.ɵɵtext(20, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" Account \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 13);\n            i0.ɵɵtemplate(23, AddAppointmentsComponent_div_23_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 5)(25, \"div\", 6)(26, \"label\", 7)(27, \"span\", 12);\n            i0.ɵɵtext(28, \"globe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(29, \" Contact \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 5)(32, \"div\", 6)(33, \"label\", 7)(34, \"span\", 12);\n            i0.ɵɵtext(35, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(36, \" Category \");\n            i0.ɵɵelementStart(37, \"span\", 9);\n            i0.ɵɵtext(38, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(39, \"p-dropdown\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 5)(41, \"div\", 6)(42, \"label\", 7)(43, \"span\", 12);\n            i0.ɵɵtext(44, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(45, \" Disposition Code \");\n            i0.ɵɵelementStart(46, \"span\", 9);\n            i0.ɵɵtext(47, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(48, \"p-dropdown\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 5)(50, \"div\", 6)(51, \"label\", 7)(52, \"span\", 12);\n            i0.ɵɵtext(53, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(54, \" Start Date/Time \");\n            i0.ɵɵelementStart(55, \"span\", 9);\n            i0.ɵɵtext(56, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(57, \"p-calendar\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"div\", 5)(59, \"div\", 6)(60, \"label\", 7)(61, \"span\", 12);\n            i0.ɵɵtext(62, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(63, \" End Date/Time \");\n            i0.ɵɵelementStart(64, \"span\", 9);\n            i0.ɵɵtext(65, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(66, \"p-calendar\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 5)(68, \"div\", 6)(69, \"label\", 7)(70, \"span\", 12);\n            i0.ɵɵtext(71, \"map\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(72, \" Priority \");\n            i0.ɵɵelementStart(73, \"span\", 9);\n            i0.ɵɵtext(74, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(75, \"p-dropdown\", 15);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(76, \"div\", 17)(77, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function AddAppointmentsComponent_Template_button_click_77_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function AddAppointmentsComponent_Template_button_click_78_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.AppointmentForm);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.submitted && ctx.f[\"bp_full_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"bp_full_name\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx.submitted && ctx.f[\"email_address\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"styleClass\", \"h-3rem w-full\");\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.ButtonDirective, i7.Dropdown, i8.Calendar, i9.InputText, i10.Toast]\n      });\n    }\n  }\n  return AddAppointmentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}