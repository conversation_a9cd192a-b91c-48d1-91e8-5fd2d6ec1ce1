{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/multiselect\";\nfunction AccountSalesTeamComponent_ng_template_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 17);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 17);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 18);\n    i0.ɵɵlistener(\"click\", function AccountSalesTeamComponent_ng_template_8_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountSalesTeamComponent_ng_template_8_ng_container_6_i_4_Template, 1, 1, \"i\", 13)(5, AccountSalesTeamComponent_ng_template_8_ng_container_6_i_5_Template, 1, 0, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 11);\n    i0.ɵɵlistener(\"click\", function AccountSalesTeamComponent_ng_template_8_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"business_partner.first_name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 12);\n    i0.ɵɵtext(3, \" First Name \");\n    i0.ɵɵtemplate(4, AccountSalesTeamComponent_ng_template_8_i_4_Template, 1, 1, \"i\", 13)(5, AccountSalesTeamComponent_ng_template_8_i_5_Template, 1, 0, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountSalesTeamComponent_ng_template_8_ng_container_6_Template, 6, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"business_partner.first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"business_partner.first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.last_name) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.partner_role) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.addresses == null ? null : team_r5.business_partner.addresses[0] == null ? null : team_r5.business_partner.addresses[0].emails == null ? null : team_r5.business_partner.addresses[0].emails[0] == null ? null : team_r5.business_partner.addresses[0].emails[0].email_address) || \"-\", \" \");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 21);\n    i0.ɵɵtemplate(3, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 22)(4, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 22)(5, AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.last_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"partner_role\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"address.email_address\");\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 19)(1, \"td\", 20);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSalesTeamComponent_ng_template_9_ng_container_3_Template, 6, 4, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const team_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (team_r5 == null ? null : team_r5.business_partner == null ? null : team_r5.business_partner.first_name) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 23);\n    i0.ɵɵtext(2, \"No Sales Teams found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesTeamComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 23);\n    i0.ɵɵtext(2, \"Loading Sales Teams data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountSalesTeamComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.salesteamDetails = [];\n    this.id = '';\n    this.partnerfunction = [];\n    this.partnerLoading = false;\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'business_partner.last_name',\n      header: 'Last Name'\n    }, {\n      field: 'partner_role',\n      header: 'Role'\n    }, {\n      field: 'address.email_address',\n      header: 'Email'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.salesteamDetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.loadPartners();\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadPartners() {\n    this.partnerLoading = true;\n    this.accountservice.getCRMPartner().pipe(takeUntil(this.unsubscribe$), switchMap(partners => {\n      this.partnerfunction = partners || [];\n      return this.accountservice.account.pipe(takeUntil(this.unsubscribe$));\n    })).subscribe({\n      next: response => {\n        if (!response) return;\n        this.id = response?.customer?.customer_id;\n        const filteredPartners = response.customer.partner_functions.filter(pf => this.partnerfunction.some(partner => partner?.value === pf.partner_function));\n        if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\n          this.salesteamDetails = filteredPartners.map(pf => {\n            const matchedPartner = this.partnerfunction.find(partner => partner?.value === pf.partner_function);\n            return {\n              ...pf,\n              partner_role: matchedPartner ? matchedPartner?.label : null,\n              // Adding partner label\n              addresses: this.filterXXDefaultAddresses(response?.customer?.partner_functions?.business_partner?.addresses || [])\n            };\n          });\n        } else {\n          this.salesteamDetails = [];\n        }\n        this.partnerLoading = false;\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n        this.partnerfunction = [];\n        this.salesteamDetails = [];\n        this.partnerLoading = false;\n      },\n      complete: () => {\n        console.log('Partner function and employee details loaded successfully.');\n      }\n    });\n  }\n  filterXXDefaultAddresses(addresses) {\n    return addresses.filter(address => address.address_usages && address.address_usages.some(usage => usage.address_usage === 'XXDEFAULT'));\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountSalesTeamComponent_Factory(t) {\n      return new (t || AccountSalesTeamComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSalesTeamComponent,\n      selectors: [[\"app-account-sales-team\"]],\n      decls: 12,\n      vars: 9,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"font-medium\", \"border-round-left-lg\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"]],\n      template: function AccountSalesTeamComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Sales Team\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-multiSelect\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSalesTeamComponent_Template_p_multiSelect_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-table\", 6);\n          i0.ɵɵlistener(\"onColReorder\", function AccountSalesTeamComponent_Template_p_table_onColReorder_7_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(8, AccountSalesTeamComponent_ng_template_8_Template, 7, 3, \"ng-template\", 7)(9, AccountSalesTeamComponent_ng_template_9_Template, 4, 2, \"ng-template\", 8)(10, AccountSalesTeamComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9)(11, AccountSalesTeamComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.salesteamDetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.PrimeTemplate, i4.Table, i4.SortableColumn, i4.FrozenColumn, i4.ReorderableColumn, i5.NgControlStatus, i5.NgModel, i6.MultiSelect],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "switchMap", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "AccountSalesTeamComponent_ng_template_8_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "AccountSalesTeamComponent_ng_template_8_ng_container_6_i_4_Template", "AccountSalesTeamComponent_ng_template_8_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "AccountSalesTeamComponent_ng_template_8_Template_th_click_1_listener", "_r1", "AccountSalesTeamComponent_ng_template_8_i_4_Template", "AccountSalesTeamComponent_ng_template_8_i_5_Template", "AccountSalesTeamComponent_ng_template_8_ng_container_6_Template", "selectedColumns", "team_r5", "business_partner", "last_name", "partner_role", "addresses", "emails", "email_address", "AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_3_Template", "AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_4_Template", "AccountSalesTeamComponent_ng_template_9_ng_container_3_ng_container_5_Template", "col_r6", "AccountSalesTeamComponent_ng_template_9_ng_container_3_Template", "first_name", "AccountSalesTeamComponent", "constructor", "accountservice", "unsubscribe$", "salesteamDetails", "id", "partnerfunction", "partner<PERSON><PERSON><PERSON>", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "loadPartners", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "get<PERSON><PERSON><PERSON><PERSON>", "pipe", "partners", "account", "subscribe", "next", "response", "customer", "customer_id", "filteredPartners", "partner_functions", "pf", "some", "partner", "value", "partner_function", "Array", "isArray", "length", "map", "<PERSON><PERSON><PERSON><PERSON>", "find", "label", "filterXXDefaultAddresses", "error", "console", "complete", "log", "address", "address_usages", "usage", "address_usage", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountSalesTeamComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "AccountSalesTeamComponent_Template_p_multiSelect_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "AccountSalesTeamComponent_Template_p_table_onColReorder_7_listener", "AccountSalesTeamComponent_ng_template_8_Template", "AccountSalesTeamComponent_ng_template_9_Template", "AccountSalesTeamComponent_ng_template_10_Template", "AccountSalesTeamComponent_ng_template_11_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-team\\account-sales-team.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-team\\account-sales-team.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AccountService } from '../../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { switchMap } from 'rxjs/operators';\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n@Component({\r\n  selector: 'app-account-sales-team',\r\n  templateUrl: './account-sales-team.component.html',\r\n  styleUrl: './account-sales-team.component.scss',\r\n})\r\nexport class AccountSalesTeamComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public salesteamDetails: any[] = [];\r\n  public id: string = '';\r\n  public partnerfunction: { label: string; value: string }[] = [];\r\n  public partnerLoading = false;\r\n\r\n  constructor(private accountservice: AccountService) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'business_partner.last_name', header: 'Last Name' },\r\n    { field: 'partner_role', header: 'Role' },\r\n    { field: 'address.email_address', header: 'Email' }\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.salesteamDetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadPartners();\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  private loadPartners(): void {\r\n    this.partnerLoading = true;\r\n\r\n    this.accountservice\r\n      .getCRMPartner()\r\n      .pipe(\r\n        takeUntil(this.unsubscribe$),\r\n        switchMap((partners: any) => {\r\n          this.partnerfunction = partners || [];\r\n\r\n          return this.accountservice.account.pipe(takeUntil(this.unsubscribe$));\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (!response) return;\r\n\r\n          this.id = response?.customer?.customer_id;\r\n          const filteredPartners = response.customer.partner_functions.filter(\r\n            (pf: any) =>\r\n              this.partnerfunction.some(\r\n                (partner: any) => partner?.value === pf.partner_function\r\n              )\r\n          );\r\n\r\n          if (Array.isArray(filteredPartners) && filteredPartners.length > 0) {\r\n            this.salesteamDetails = filteredPartners.map((pf: any) => {\r\n              const matchedPartner = this.partnerfunction.find(\r\n                (partner: any) => partner?.value === pf.partner_function\r\n              );\r\n\r\n              return {\r\n                ...pf,\r\n                partner_role: matchedPartner ? matchedPartner?.label : null, // Adding partner label\r\n                addresses: this.filterXXDefaultAddresses(\r\n                  response?.customer?.partner_functions?.business_partner\r\n                    ?.addresses || []\r\n                ),\r\n              };\r\n            });\r\n          } else {\r\n            this.salesteamDetails = [];\r\n          }\r\n\r\n          this.partnerLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching data:', error);\r\n          this.partnerfunction = [];\r\n          this.salesteamDetails = [];\r\n          this.partnerLoading = false;\r\n        },\r\n        complete: () => {\r\n          console.log(\r\n            'Partner function and employee details loaded successfully.'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  private filterXXDefaultAddresses(addresses: any[]): any[] {\r\n    return addresses.filter(\r\n      (address: any) =>\r\n        address.address_usages &&\r\n        address.address_usages.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n    );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Team</h4>\r\n\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <!-- <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" /> -->\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"salesteamDetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('business_partner.first_name')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            First Name\r\n                            <i *ngIf=\"sortField === 'business_partner.first_name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'business_partner.first_name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-team let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"font-medium border-round-left-lg\">\r\n                        {{ team?.business_partner?.first_name || '-'}}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner.last_name'\">\r\n                                    {{ team?.business_partner?.last_name || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'partner_role'\">\r\n                                    {{ team?.partner_role || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'address.email_address'\">\r\n                                    {{ team?.business_partner?.addresses?.[0]?.emails?.[0]?.email_address || '-'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">No Sales Teams found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">Loading Sales Teams data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;ICsBdC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAmF;;;;;IAQ/ED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,oFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,mEAAA,gBACkF,IAAAC,mEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAjB7ChB,EADJ,CAAAM,cAAA,SAAI,aACmG;IAAjFN,EAAA,CAAAO,UAAA,mBAAAmB,qEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,6BAA6B,CAAC;IAAA,EAAC;IACjEf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,mBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,oDAAA,gBACkF,IAAAC,oDAAA,gBAEH;IAEvF7B,EADI,CAAAqB,YAAA,EAAM,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAY,+DAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,mCAAiD;IAGjDzB,EAAA,CAAAsB,SAAA,EAAiD;IAAjDtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,mCAAiD;IAI/BzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAwBpC/B,EAAA,CAAAK,uBAAA,GAA2D;IACvDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAC,SAAA,cACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAG,YAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAAsD;IAClDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAG,SAAA,kBAAAJ,OAAA,CAAAC,gBAAA,CAAAG,SAAA,qBAAAJ,OAAA,CAAAC,gBAAA,CAAAG,SAAA,IAAAC,MAAA,kBAAAL,OAAA,CAAAC,gBAAA,CAAAG,SAAA,IAAAC,MAAA,qBAAAL,OAAA,CAAAC,gBAAA,CAAAG,SAAA,IAAAC,MAAA,IAAAC,aAAA,cACJ;;;;;IAdZtC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAUjCL,EARA,CAAAkB,UAAA,IAAAqB,8EAAA,2BAA2D,IAAAC,8EAAA,2BAId,IAAAC,8EAAA,2BAIS;;IAK9DzC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAfarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAwC,MAAA,CAAA1B,KAAA,CAAsB;IAEjBhB,EAAA,CAAAsB,SAAA,EAA0C;IAA1CtB,EAAA,CAAAE,UAAA,8CAA0C;IAI1CF,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAI5BF,EAAA,CAAAsB,SAAA,EAAqC;IAArCtB,EAAA,CAAAE,UAAA,yCAAqC;;;;;IAhBhEF,EADJ,CAAAM,cAAA,aAA2B,aACoC;IACvDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAyB,+DAAA,2BAAkD;IAmBtD3C,EAAA,CAAAqB,YAAA,EAAK;;;;;IAtBGrB,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,OAAA,kBAAAA,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAW,UAAA,cACJ;IAE8B5C,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAwBhD/B,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,4BAAqB;IACtEjB,EADsE,CAAAqB,YAAA,EAAK,EACtE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,+CAAwC;IACzFjB,EADyF,CAAAqB,YAAA,EAAK,EACzF;;;ADrErB,OAAM,MAAOwB,yBAAyB;EAOpCC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAN1B,KAAAC,YAAY,GAAG,IAAInD,OAAO,EAAQ;IACnC,KAAAoD,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,eAAe,GAAuC,EAAE;IACxD,KAAAC,cAAc,GAAG,KAAK;IAIrB,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEtC,KAAK,EAAE,4BAA4B;MAAEQ,MAAM,EAAE;IAAW,CAAE,EAC5D;MAAER,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACzC;MAAER,KAAK,EAAE,uBAAuB;MAAEQ,MAAM,EAAE;IAAO,CAAE,CACpD;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAXiC;EAatDW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC6C,gBAAgB,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAClC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAExC,KAAK,CAAC;MAC9C,MAAM4C,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEzC,KAAK,CAAC;MAE9C,IAAI6C,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACxD,SAAS,GAAGyD,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE/C,KAAa;IACvC,IAAI,CAAC+C,IAAI,IAAI,CAAC/C,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACgD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC/C,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACiD,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAIvB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACsB,gBAAgB;EAC9B;EAEA,IAAItB,eAAeA,CAACwC,GAAU;IAC5B,IAAI,CAAClB,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACkB,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACxB,gBAAgB,CAACuB,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEQP,YAAYA,CAAA;IAClB,IAAI,CAAClB,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACL,cAAc,CAChBkC,aAAa,EAAE,CACfC,IAAI,CACHpF,SAAS,CAAC,IAAI,CAACkD,YAAY,CAAC,EAC5BjD,SAAS,CAAEoF,QAAa,IAAI;MAC1B,IAAI,CAAChC,eAAe,GAAGgC,QAAQ,IAAI,EAAE;MAErC,OAAO,IAAI,CAACpC,cAAc,CAACqC,OAAO,CAACF,IAAI,CAACpF,SAAS,CAAC,IAAI,CAACkD,YAAY,CAAC,CAAC;IACvE,CAAC,CAAC,CACH,CACAqC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,EAAE;QAEf,IAAI,CAACrC,EAAE,GAAGqC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW;QACzC,MAAMC,gBAAgB,GAAGH,QAAQ,CAACC,QAAQ,CAACG,iBAAiB,CAACnB,MAAM,CAChEoB,EAAO,IACN,IAAI,CAACzC,eAAe,CAAC0C,IAAI,CACtBC,OAAY,IAAKA,OAAO,EAAEC,KAAK,KAAKH,EAAE,CAACI,gBAAgB,CACzD,CACJ;QAED,IAAIC,KAAK,CAACC,OAAO,CAACR,gBAAgB,CAAC,IAAIA,gBAAgB,CAACS,MAAM,GAAG,CAAC,EAAE;UAClE,IAAI,CAAClD,gBAAgB,GAAGyC,gBAAgB,CAACU,GAAG,CAAER,EAAO,IAAI;YACvD,MAAMS,cAAc,GAAG,IAAI,CAAClD,eAAe,CAACmD,IAAI,CAC7CR,OAAY,IAAKA,OAAO,EAAEC,KAAK,KAAKH,EAAE,CAACI,gBAAgB,CACzD;YAED,OAAO;cACL,GAAGJ,EAAE;cACLzD,YAAY,EAAEkE,cAAc,GAAGA,cAAc,EAAEE,KAAK,GAAG,IAAI;cAAE;cAC7DnE,SAAS,EAAE,IAAI,CAACoE,wBAAwB,CACtCjB,QAAQ,EAAEC,QAAQ,EAAEG,iBAAiB,EAAE1D,gBAAgB,EACnDG,SAAS,IAAI,EAAE;aAEtB;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACa,gBAAgB,GAAG,EAAE;QAC5B;QAEA,IAAI,CAACG,cAAc,GAAG,KAAK;MAC7B,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACtD,eAAe,GAAG,EAAE;QACzB,IAAI,CAACF,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACG,cAAc,GAAG,KAAK;MAC7B,CAAC;MACDuD,QAAQ,EAAEA,CAAA,KAAK;QACbD,OAAO,CAACE,GAAG,CACT,4DAA4D,CAC7D;MACH;KACD,CAAC;EACN;EAEQJ,wBAAwBA,CAACpE,SAAgB;IAC/C,OAAOA,SAAS,CAACoC,MAAM,CACpBqC,OAAY,IACXA,OAAO,CAACC,cAAc,IACtBD,OAAO,CAACC,cAAc,CAACjB,IAAI,CACxBkB,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACJ;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjE,YAAY,CAACsC,IAAI,EAAE;IACxB,IAAI,CAACtC,YAAY,CAAC2D,QAAQ,EAAE;EAC9B;;;uBApJW9D,yBAAyB,EAAA7C,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAzBvE,yBAAyB;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9B3H,EAFR,CAAAM,cAAA,aAA2D,aACuC,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,iBAAU;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAM1DrB,EAJJ,CAAAM,cAAA,aAAmD,uBAMgG;UAF/GN,EAAA,CAAA6H,gBAAA,2BAAAC,0EAAAC,MAAA;YAAA/H,EAAA,CAAAgI,kBAAA,CAAAJ,GAAA,CAAA7F,eAAA,EAAAgG,MAAA,MAAAH,GAAA,CAAA7F,eAAA,GAAAgG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE/H,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAA0H,mEAAAF,MAAA;YAAA,OAAgBH,GAAA,CAAAjD,eAAA,CAAAoD,MAAA,CAAuB;UAAA,EAAC;UA6DxC/H,EA3DA,CAAAkB,UAAA,IAAAgH,gDAAA,yBAAgC,IAAAC,gDAAA,yBA0B6B,KAAAC,iDAAA,yBA4BvB,KAAAC,iDAAA,0BAKD;UAOjDrI,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UA9EqBrB,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAA0H,GAAA,CAAAtE,IAAA,CAAgB;UAACtD,EAAA,CAAAsI,gBAAA,YAAAV,GAAA,CAAA7F,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAA0B;UACwCtB,EADlE,CAAAE,UAAA,UAAA0H,GAAA,CAAA3E,gBAAA,CAA0B,YAAyB,mBAAmB,cAAc,oBAC5C,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}