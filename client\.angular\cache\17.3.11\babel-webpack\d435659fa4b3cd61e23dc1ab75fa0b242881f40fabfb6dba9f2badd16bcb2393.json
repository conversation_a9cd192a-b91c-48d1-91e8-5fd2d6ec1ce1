{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DashboardComponent } from './dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DashboardComponent\n}];\nexport let DashboardRoutingModule = /*#__PURE__*/(() => {\n  class DashboardRoutingModule {\n    static {\n      this.ɵfac = function DashboardRoutingModule_Factory(t) {\n        return new (t || DashboardRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: DashboardRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return DashboardRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}