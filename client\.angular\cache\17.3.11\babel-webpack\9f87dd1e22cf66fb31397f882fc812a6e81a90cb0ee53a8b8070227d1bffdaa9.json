{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ContactsComponent } from './contacts.component';\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\nimport { ContactsContactsComponent } from './contacts-details/contacts-contacts/contacts-contacts.component';\nimport { ContactsPartnersComponent } from './contacts-details/contacts-partners/contacts-partners.component';\nimport { ContactsSalesTeamComponent } from './contacts-details/contacts-sales-team/contacts-sales-team.component';\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\nimport { ContactsAiInsightsComponent } from './contacts-details/contacts-ai-insights/contacts-ai-insights.component';\nimport { ContactsOrganizationDataComponent } from './contacts-details/contacts-organization-data/contacts-organization-data.component';\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\nimport { ContactsSalesQuotesComponent } from './contacts-details/contacts-sales-quotes/contacts-sales-quotes.component';\nimport { ContactsSalesOrdersComponent } from './contacts-details/contacts-sales-orders/contacts-sales-orders.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ContactsComponent\n}, {\n  path: '',\n  component: ContactsDetailsComponent,\n  children: [{\n    path: 'overview',\n    component: ContactsOverviewComponent\n  }, {\n    path: 'contacts',\n    component: ContactsContactsComponent\n  }, {\n    path: 'partners',\n    component: ContactsPartnersComponent\n  }, {\n    path: 'sales-team',\n    component: ContactsSalesTeamComponent\n  }, {\n    path: 'opportunities',\n    component: ContactsOpportunitiesComponent\n  }, {\n    path: 'ai-insights',\n    component: ContactsAiInsightsComponent\n  }, {\n    path: 'organization-data',\n    component: ContactsOrganizationDataComponent\n  }, {\n    path: 'attachments',\n    component: ContactsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ContactsNotesComponent\n  }, {\n    path: 'activities',\n    component: ContactsActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: ContactsRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: ContactsTicketsComponent\n  }, {\n    path: 'sales-quotes',\n    component: ContactsSalesQuotesComponent\n  }, {\n    path: 'sales-orders',\n    component: ContactsSalesOrdersComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class ContactsRoutingModule {\n  static {\n    this.ɵfac = function ContactsRoutingModule_Factory(t) {\n      return new (t || ContactsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ContactsComponent", "ContactsDetailsComponent", "ContactsOverviewComponent", "ContactsContactsComponent", "ContactsPartnersComponent", "ContactsSalesTeamComponent", "ContactsOpportunitiesComponent", "ContactsAiInsightsComponent", "ContactsOrganizationDataComponent", "ContactsAttachmentsComponent", "ContactsNotesComponent", "ContactsActivitiesComponent", "ContactsRelationshipsComponent", "ContactsTicketsComponent", "ContactsSalesQuotesComponent", "ContactsSalesOrdersComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ContactsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ContactsComponent } from './contacts.component';\r\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\r\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\r\nimport { ContactsContactsComponent } from './contacts-details/contacts-contacts/contacts-contacts.component';\r\nimport { ContactsPartnersComponent } from './contacts-details/contacts-partners/contacts-partners.component';\r\nimport { ContactsSalesTeamComponent } from './contacts-details/contacts-sales-team/contacts-sales-team.component';\r\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\r\nimport { ContactsAiInsightsComponent } from './contacts-details/contacts-ai-insights/contacts-ai-insights.component';\r\nimport { ContactsOrganizationDataComponent } from './contacts-details/contacts-organization-data/contacts-organization-data.component';\r\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\r\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\r\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\r\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\r\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\r\nimport { ContactsSalesQuotesComponent } from './contacts-details/contacts-sales-quotes/contacts-sales-quotes.component';\r\nimport { ContactsSalesOrdersComponent } from './contacts-details/contacts-sales-orders/contacts-sales-orders.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ContactsComponent },\r\n  {\r\n    path: '',\r\n    component: ContactsDetailsComponent,\r\n    children: [\r\n      { path: 'overview', component: ContactsOverviewComponent },\r\n      { path: 'contacts', component: ContactsContactsComponent },\r\n      { path: 'partners', component: ContactsPartnersComponent },\r\n      { path: 'sales-team', component: ContactsSalesTeamComponent },\r\n      { path: 'opportunities', component: ContactsOpportunitiesComponent },\r\n      { path: 'ai-insights', component: ContactsAiInsightsComponent },\r\n      { path: 'organization-data', component: ContactsOrganizationDataComponent },\r\n      { path: 'attachments', component: ContactsAttachmentsComponent },\r\n      { path: 'notes', component: ContactsNotesComponent },\r\n      { path: 'activities', component: ContactsActivitiesComponent },\r\n      { path: 'relationships', component: ContactsRelationshipsComponent },\r\n      { path: 'tickets', component: ContactsTicketsComponent },\r\n      { path: 'sales-quotes', component: ContactsSalesQuotesComponent },\r\n      { path: 'sales-orders', component: ContactsSalesOrdersComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ContactsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,0BAA0B,QAAQ,sEAAsE;AACjH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,iCAAiC,QAAQ,oFAAoF;AACtI,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,2BAA2B,QAAQ,sEAAsE;AAClH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,4BAA4B,QAAQ,0EAA0E;AACvH,SAASC,4BAA4B,QAAQ,0EAA0E;;;AAEvH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAElB;AAAiB,CAAE,EAC1C;EACEiB,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEjB,wBAAwB;EACnCkB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEhB;EAAyB,CAAE,EAC1D;IAAEe,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEf;EAAyB,CAAE,EAC1D;IAAEc,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEd;EAAyB,CAAE,EAC1D;IAAEa,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEb;EAA0B,CAAE,EAC7D;IAAEY,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEZ;EAA8B,CAAE,EACpE;IAAEW,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEX;EAA2B,CAAE,EAC/D;IAAEU,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEV;EAAiC,CAAE,EAC3E;IAAES,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAET;EAA4B,CAAE,EAChE;IAAEQ,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAsB,CAAE,EACpD;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA2B,CAAE,EAC9D;IAAEM,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEN;EAA8B,CAAE,EACpE;IAAEK,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEL;EAAwB,CAAE,EACxD;IAAEI,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEJ;EAA4B,CAAE,EACjE;IAAEG,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEH;EAA4B,CAAE,EACjE;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBvB,YAAY,CAACwB,QAAQ,CAACP,MAAM,CAAC,EAC7BjB,YAAY;IAAA;EAAA;;;2EAEXuB,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFtB3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}