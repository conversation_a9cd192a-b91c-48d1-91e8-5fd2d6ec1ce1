{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./layout/service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nexport let StoreComponent = /*#__PURE__*/(() => {\n  class StoreComponent {\n    constructor(primengConfig, renderer, layoutService) {\n      this.primengConfig = primengConfig;\n      this.renderer = renderer;\n      this.layoutService = layoutService;\n    }\n    ngOnInit() {\n      // Inject theme \n      const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n      const link = this.renderer.createElement('link');\n      this.renderer.setAttribute(link, 'id', 'theme-link');\n      this.renderer.setAttribute(link, 'rel', 'stylesheet');\n      this.renderer.setAttribute(link, 'type', 'text/css');\n      this.renderer.setAttribute(link, 'href', href);\n      // Append the link tag to the head of the document\n      this.renderer.appendChild(document.head, link);\n      this.primengConfig.ripple = true; //enables core ripple functionality\n      //optional configuration with the default configuration\n      const config = {\n        ripple: false,\n        //toggles ripple on and off\n        menuMode: 'reveal',\n        //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\n        colorScheme: 'light',\n        //color scheme of the template, valid values are \"light\" and \"dark\"\n        theme: 'snjya',\n        //default component theme for PrimeNG\n        scale: 14 //size of the body font size to scale the whole application\n      };\n      this.layoutService.config.set(config);\n    }\n    ngOnDestroy() {\n      // Find and remove the link tag when the component is destroyed\n      const link = document.getElementById('theme-link');\n      if (link) {\n        link.remove();\n      }\n    }\n    static {\n      this.ɵfac = function StoreComponent_Factory(t) {\n        return new (t || StoreComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.LayoutService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: StoreComponent,\n        selectors: [[\"app-store\"]],\n        decls: 1,\n        vars: 0,\n        template: function StoreComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [i3.RouterOutlet],\n        styles: [\".bg-light-blue{background:#c3dbff!important}  .object-fit-cover{object-fit:cover}  .h-36rem{height:36rem!important}  .h-34rem{height:34rem!important}  .h-3-3rem{height:3.3rem}  .d-grid{display:grid!important}  .w-fit{width:-moz-fit-content;width:fit-content}  .min-w-3rem{min-width:3rem!important}  .min-w-16{min-width:16rem!important}  .min-w-14{min-width:14rem!important}  .min-h-14{min-height:14rem!important}  .min-h-18{min-height:18rem!important}  .min-h-30{min-height:30rem!important}  .font-900{font-weight:900!important}  .surface-b{background:var(--surface-b)!important}  .object-fit-contain{object-fit:contain}  .transition-03{transition:all .3s ease-in-out}  .p-datatable-wrapper thead p-sorticon svg{color:var(--white)}  .header-title:before,   .left-border:before{position:absolute;content:\\\"\\\";left:0;top:0;bottom:0;margin:auto;width:5px;height:24px;background:var(--primary-color);border-radius:50px}  .layout-sidebar{width:20rem}  .layout-content-wrapper{background:var(--surface-0)}  .bg-whight-light{background:#f6f7f9}  .all-overview-body{min-height:calc(100vh - 90px)}  .all-overview-body .p-galleria .p-galleria-item-nav{color:var(--text-color)!important;background:var(--surface-0)!important;width:3rem!important;height:3rem!important;transform:translateY(-50%);z-index:99}  .all-overview-body .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon{width:1.5rem;height:1.5rem}  .all-overview-body .card-list .v-details-list .v-details-box .text{min-width:120px}  .all-overview-body p-table table thead th{height:44px;background:var(--primary-color);color:var(--surface-0)}  .all-overview-body p-table table tbody td{height:44px}  .all-overview-body .v-details-list .v-details-box .text{min-width:182px}  .all-overview-body .order-details-list{grid-template-columns:repeat(auto-fill,minmax(300px,1fr))}  .p-inputtext{appearance:auto!important}  .border-left-5{border-left:5px solid var(--orange-200)}  .p-calendar{display:flex}  .p-calendar .p-button-icon-only{width:3rem}  .max-w-1200{max-width:1200px}  .text-shadow-l-blue{text-shadow:0 2px 6px rgba(0,63,147,.8)}  .h-32rem{height:32rem!important}  .h-2-8rem{height:2.8rem!important}  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text{color:var(--text-color);font-weight:600}  p-paginator .p-paginator{padding:20px 0;margin:1.5rem 0 0;border-top:1px solid var(--surface-d);border-radius:0}  p-paginator .p-paginator button{width:3rem;height:2rem;border-radius:.3rem;border:1px solid var(--surface-c)}  p-paginator .p-paginator p-dropdown{display:none}  .table-sec tbody:before{line-height:20px;content:\\\"_\\\";color:transparent;display:block}  .table-sec tbody tr:nth-child(odd) td{background:var(--surface-b)}  .table-sec thead th .p-checkbox .p-checkbox-box.p-highlight{border-color:var(--surface-0)}  .table-sec thead th:last-child{border-top-right-radius:.5rem!important;border-bottom-right-radius:.5rem!important}  .table-sec tbody td:last-child{border-top-right-radius:.5rem!important;border-bottom-right-radius:.5rem!important}  .p-datatable-scrollable>.p-datatable-wrapper{padding-bottom:12px}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav{padding:0 16px;border-color:var(--surface-100)}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li{position:relative}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li:before{content:\\\"\\\";position:absolute;right:-1px;top:0;bottom:0;margin:auto;background:var(--surface-50);width:1px;height:20px}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link{border:none;padding:0}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link{padding:8px 14px;min-height:40px;color:var(--gray-600);gap:0 6px;border-radius:10px 10px 0 0;cursor:pointer;font-weight:500}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link:hover{color:var(--primary-color)}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab{background:#f6f7f9;border:2px solid var(--surface-100);border-bottom:none;font-weight:600;color:var(--gray-800)}  .details-tabs-list .p-tabview-nav-content .p-tabview-nav .p-tabview-ink-bar{display:none!important}  .details-tabs-list .p-tabview-panels{display:none}  .details-tabs-list .p-tabview-nav-btn.p-link{background:var(--surface-0)}  .details-tabs-result{min-height:calc(100vh - 192px)}  .layout-sidebar .layout-menu li:nth-child(2) .layout-menuitem-root-text{margin:12px 0;padding:12px 0;border-top:1px solid var(--surface-c);font-size:14px;font-weight:600}  .card-heading h4{margin-left:10px!important;min-height:30px;align-items:center}  .sidebar-hide{display:none}  .arrow-btn{top:29px;left:0;z-index:99}  .arrow-round{transform:rotate(180deg)}  .layout-sidebar .sidebar-header .app-logo .arrow-icon{transform:rotateY(180deg);position:absolute;right:11px;top:14px;color:var(--primary-color)}  .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .arrow-icon{display:none!important}  .p-dropdown-label{display:flex;align-items:center}  .filter-sec p-dropdown .p-dropdown-label,   .filter-sec p-dropdown .p-dropdown-trigger{color:var(--primary-700)}  .filter-sec .table-multiselect-dropdown .p-overlay.p-component{left:-140px!important}  .filter-sec .table-multiselect-dropdown .p-multiselect-items-wrapper p-multiselectitem li{margin:0 0 2px!important;min-width:164px}  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label-container{display:none}  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label{color:var(--primary-700);align-items:center;display:flex;text-transform:capitalize}  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger{position:relative;width:1.5rem;height:1.5rem}  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger chevrondownicon{display:none}  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger:before{position:absolute;content:\\\"\\\\e5d4\\\";width:1.5rem;height:1.5rem;font-family:Material Symbols Rounded;font-size:1.5rem;color:var(--primary-700);line-height:22px}  .scrollable-table table thead th{min-width:150px;white-space:nowrap}  .scrollable-table table thead th.table-checkbox{min-width:32px}  .scrollable-table table tbody tr:nth-child(odd) td:first-child{background:#f2f2f5}  .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2){background:#f2f2f5}  .scrollable-table table tbody td{white-space:nowrap}  .all-page-details{width:calc(100% - 28rem)}  .layout-dark .bg-whight-light{background:var(--surface-0)}  .layout-dark .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab{background:var(--surface-0);color:var(--text-color)}  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:first-child{background:var(--surface-b)}  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2){background:var(--surface-b)}  .layout-dark .table-sec tbody tr:nth-child(odd) td{background:#ffffff0d}  .p-sortable-column-badge{display:none!important}  .note-text{max-width:320px;width:320px;white-space:normal!important}  .multiselect-dropdown{padding:0;height:3rem}  .multiselect-dropdown .ng-select-container{background:none!important;border:none;height:3rem!important;align-items:center}  .multiselect-dropdown .ng-select-container .ng-value-container{height:2rem}  .multiselect-dropdown .ng-select-container .ng-value-container .ng-input{top:0!important;bottom:0;margin:auto;display:flex;align-items:center}  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option input{min-width:20px}  .confirm-popup .p-dialog{margin-right:50px}  .confirm-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .confirm-popup .p-dialog .p-dialog-header h4{margin:0}  .confirm-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}  .confirm-popup .p-dialog .p-dialog-footer{background:var(--surface-0);padding:1.714rem}  .confirm-popup .p-dialog .p-dialog-footer .p-button{height:2.5rem!important;width:8rem!important;gap:.25rem!important;font-weight:500!important;border:none;border-radius:2rem;justify-content:center}  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-reject{color:var(--red-500)!important;background-color:var(--red-100)!important}  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-accept{color:var(--primary-700)!important;background-color:#c3dbff!important}  .confirm-popup .p-dialog .p-dialog-footer .p-button .p-button-label{flex:none!important}\"]\n      });\n    }\n  }\n  return StoreComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}