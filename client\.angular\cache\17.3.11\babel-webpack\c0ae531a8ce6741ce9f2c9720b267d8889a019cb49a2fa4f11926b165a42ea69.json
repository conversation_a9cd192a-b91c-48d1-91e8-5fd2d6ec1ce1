{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./opportunities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction OpportunitiesComponent_ng_template_19_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function OpportunitiesComponent_ng_template_19_ng_container_8_Template_th_click_1_listener() {\n      const col_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OpportunitiesComponent_ng_template_19_ng_container_8_i_4_Template, 1, 1, \"i\", 24)(5, OpportunitiesComponent_ng_template_19_ng_container_8_i_5_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== col_r6.field);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function OpportunitiesComponent_ng_template_19_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"opportunity_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" ID \");\n    i0.ɵɵtemplate(6, OpportunitiesComponent_ng_template_19_i_6_Template, 1, 1, \"i\", 24)(7, OpportunitiesComponent_ng_template_19_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, OpportunitiesComponent_ng_template_19_ng_container_8_Template, 6, 4, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === \"opportunity_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== \"opportunity_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/opportunities/\" + opportunity_r7.opportunity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.business_partner == null ? null : opportunity_r7.business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.business_partner_owner == null ? null : opportunity_r7.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_start_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.expected_revenue_start_date, \"MM/dd/yyyy\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.updatedAt) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.updatedAt, \"MM/dd/yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.last_changed_by) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_amount) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"opportunityStatus\", opportunity_r7 == null ? null : opportunity_r7.life_cycle_status_code) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.probability_percent) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.need_help) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 35)(4, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 35)(5, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 35)(6, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_6_Template, 3, 4, \"ng-container\", 35)(7, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_7_Template, 3, 4, \"ng-container\", 35)(8, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_8_Template, 3, 4, \"ng-container\", 35)(9, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 35)(10, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 35)(11, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 35)(12, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_12_Template, 2, 1, \"ng-container\", 35)(13, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_13_Template, 2, 1, \"ng-container\", 35);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"last_changed_by\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_amount\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"life_cycle_status_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"probability_percent\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"need_help\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, OpportunitiesComponent_ng_template_20_ng_container_5_Template, 14, 12, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", opportunity_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/opportunities/\" + opportunity_r7.opportunity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", opportunity_r7 == null ? null : opportunity_r7.opportunity_id, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction OpportunitiesComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 37);\n    i0.ɵɵtext(2, \"No opportunities found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 37);\n    i0.ɵɵtext(2, \"Loading opportunities data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let OpportunitiesComponent = /*#__PURE__*/(() => {\n  class OpportunitiesComponent {\n    constructor(opportunitiesservice, router) {\n      this.opportunitiesservice = opportunitiesservice;\n      this.router = router;\n      this.unsubscribe$ = new Subject();\n      this.opportunities = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n      this.dropdowns = {\n        opportunityStatus: []\n      };\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'name',\n        header: 'Name'\n      }, {\n        field: 'business_partner.bp_full_name',\n        header: 'Account'\n      }, {\n        field: 'business_partner_owner.bp_full_name',\n        header: 'Owner'\n      }, {\n        field: 'expected_revenue_start_date',\n        header: 'Start Date'\n      }, {\n        field: 'expected_revenue_end_date',\n        header: 'Close Date'\n      }, {\n        field: 'updatedAt',\n        header: 'Last Updated Date'\n      }, {\n        field: 'last_updated_by',\n        header: 'Last Updated By'\n      }, {\n        field: 'expected_value',\n        header: 'Expected Value'\n      }, {\n        field: 'life_cycle_status_code',\n        header: 'Status'\n      }, {\n        field: 'probability',\n        header: 'Probability'\n      }, {\n        field: 'need_help',\n        header: 'Need Help'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.opportunities.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.breadcrumbitems = [{\n        label: 'Opportunities',\n        routerLink: ['/store/opportunities']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'All',\n        code: 'ALL'\n      }, {\n        name: 'My Opportunities',\n        code: 'MO'\n      }];\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadOpportunities(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      const filter = this.selectedActions?.code;\n      this.opportunitiesservice.getOpportunities(page, pageSize, sortField, sortOrder, this.globalSearchTerm, filter).subscribe({\n        next: response => {\n          this.opportunities = response?.data || [];\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching opportunities', error);\n          this.loading = false;\n        }\n      });\n    }\n    onActionChange() {\n      // Re-trigger the lazy load with current dt1 state\n      const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n        first: 0,\n        rows: 15\n      };\n      this.loadOpportunities(dt1State);\n    }\n    onGlobalFilter(table, event) {\n      this.loadOpportunities({\n        first: 0,\n        rows: 10\n      });\n    }\n    signup() {\n      this.router.navigate(['/store/opportunities/create']);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OpportunitiesComponent_Factory(t) {\n        return new (t || OpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.OpportunitiesService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunitiesComponent,\n        selectors: [[\"app-opportunities\"]],\n        viewQuery: function OpportunitiesComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n          }\n        },\n        decls: 23,\n        vars: 18,\n        consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Opportunity\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"text-blue-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"]],\n        template: function OpportunitiesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function OpportunitiesComponent_Template_input_input_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              const dt1_r2 = i0.ɵɵreference(18);\n              return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"i\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"onChange\", function OpportunitiesComponent_Template_p_dropdown_onChange_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onActionChange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function OpportunitiesComponent_Template_button_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.signup());\n            });\n            i0.ɵɵelementStart(12, \"span\", 13);\n            i0.ɵɵtext(13, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(14, \" Create \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p-multiSelect\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_p_multiSelect_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 15)(17, \"p-table\", 16, 1);\n            i0.ɵɵlistener(\"onLazyLoad\", function OpportunitiesComponent_Template_p_table_onLazyLoad_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadOpportunities($event));\n            })(\"onColReorder\", function OpportunitiesComponent_Template_p_table_onColReorder_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(19, OpportunitiesComponent_ng_template_19_Template, 9, 3, \"ng-template\", 17)(20, OpportunitiesComponent_ng_template_20_Template, 6, 4, \"ng-template\", 18)(21, OpportunitiesComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19)(22, OpportunitiesComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.opportunities)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i2.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.MultiSelect, i3.DatePipe]\n      });\n    }\n  }\n  return OpportunitiesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}