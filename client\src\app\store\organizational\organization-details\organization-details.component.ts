import { Component, OnInit } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { OpportunitiesService } from '../../opportunities/opportunities.service';
import { OrganizationalService } from '../organizational.service';

@Component({
  selector: 'app-organization-details',
  templateUrl: './organization-details.component.html',
  styleUrl: './organization-details.component.scss',
})
export class OrganizationDetailsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public organizationDetails: any = null;
  public sidebarDetails: any = null;
  public items: MenuItem[] = [];
  public home: MenuItem | any;
  public id: string = '';
  public partner_role: string = '';
  public breadcrumbitems: MenuItem[] = [];
  public activeItem: MenuItem | null = null;
  public isSidebarHidden = false;
  public activeIndex: number = 0;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private opportunitiesservice: OpportunitiesService,
    private organizationalservice:OrganizationalService
  ) {}

  ngOnInit() {
    this.id = this.route.snapshot.paramMap.get('id') || '';
    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };

    this.makeMenuItems(this.id);
    if (this.items.length > 0) {
      this.activeItem = this.items[0];
    }

    this.setActiveTabFromURL();

    this.route.paramMap
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((params) => {
        const opportunityId = params.get('id');
        if (opportunityId) {
          this.loadOpportunityData(opportunityId);
        }
      });
    // Listen for route changes to keep active tab in sync
    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
      this.setActiveTabFromURL();
    });

    this.opportunitiesservice.opportunity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        const partner_role =
          response?.business_partner?.customer?.partner_functions?.find(
            (p: any) => p.partner_function === 'YI'
          );
        this.partner_role = partner_role?.bp_full_name || null;
        this.organizationDetails = response || null;
        this.sidebarDetails = this.formatSidebarDetails(
          response?.business_partner?.addresses || []
        );
      });
  }

  private formatSidebarDetails(addresses: any[]): any[] {
    return addresses
      .filter((address: any) =>
        address?.address_usages?.some(
          (usage: any) => usage.address_usage === 'XXDEFAULT'
        )
      )
      .map((address: any) => ({
        ...address,
        address: [
          address?.house_number,
          address?.street_name,
          address?.city_name,
          address?.region,
          address?.country,
          address?.postal_code,
        ]
          .filter(Boolean)
          .join(', '),
        email_address: address?.emails?.[0]?.email_address || '-',
        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',
        website_url: address?.home_page_urls?.[0]?.website_url || '-',
      }));
  }

  makeMenuItems(id: string) {
    this.items = [
      {
        label: 'General',
        routerLink: `/store/organization/${id}/general`,
      },
      {
        label: 'Functions',
        routerLink: `/store/organization/${id}/functions`,
      },
      {
        label: 'Employees',
        routerLink: `/store/organization/${id}/employees`,
      },
    ];
  }

  setActiveTabFromURL() {
    const fullPath = this.router.url;
    const currentTab = fullPath.split('/').pop() || 'general';

    if (this.items.length === 0) return;

    const foundIndex = this.items.findIndex((tab: any) =>
      tab.routerLink.endsWith(currentTab)
    );
    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;
    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;

    this.updateBreadcrumb(this.activeItem?.label || 'General');
  }

  updateBreadcrumb(activeTab: string) {
    this.breadcrumbitems = [
      { label: 'Organization', routerLink: ['/store/organization'] },
      { label: activeTab, routerLink: [] },
    ];
  }

  onTabChange(event: { index: number }) {
    if (this.items.length === 0) return;

    this.activeIndex = event.index;
    const selectedTab = this.items[this.activeIndex];

    if (selectedTab?.routerLink) {
      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs
    }
  }

  private loadOpportunityData(activityId: string): void {
    this.organizationalservice
      .getOrganizationByID(activityId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          this.organizationDetails = response?.data[0] || null;
        },
        error: (error: any) => {
          console.error('Error fetching data:', error);
        },
      });
  }

  goToBack() {
    this.router.navigate(['/store/organization']);
  }

  toggleSidebar() {
    this.isSidebarHidden = !this.isSidebarHidden;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
