{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/activities/activities.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/editor\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"primeng/inputswitch\";\nimport * as i14 from \"primeng/tooltip\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"primeng/inputtext\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"label\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Opportunity ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 25)(10, \"div\", 26)(11, \"label\", 27)(12, \"span\", 28);\n    i0.ɵɵtext(13, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"div\", 26)(19, \"label\", 27)(20, \"span\", 28);\n    i0.ɵɵtext(21, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Expected Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 29);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"label\", 27)(28, \"span\", 28);\n    i0.ɵɵtext(29, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 29);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 25)(35, \"div\", 26)(36, \"label\", 27)(37, \"span\", 28);\n    i0.ɵɵtext(38, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 29);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 25)(43, \"div\", 26)(44, \"label\", 27)(45, \"span\", 28);\n    i0.ɵɵtext(46, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47, \" Primary Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 29);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 25)(51, \"div\", 26)(52, \"label\", 27)(53, \"span\", 28);\n    i0.ɵɵtext(54, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 29);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 25)(59, \"div\", 26)(60, \"label\", 27)(61, \"span\", 28);\n    i0.ɵɵtext(62, \"account_tree\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(63, \" Parent Opportunity \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 29);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 25)(67, \"div\", 26)(68, \"label\", 27)(69, \"span\", 28);\n    i0.ɵɵtext(70, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(71, \" Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 29);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26)(76, \"label\", 27)(77, \"span\", 28);\n    i0.ɵɵtext(78, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 29);\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"div\", 25)(83, \"div\", 26)(84, \"label\", 27)(85, \"span\", 28);\n    i0.ɵɵtext(86, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"div\", 29);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 25)(91, \"div\", 26)(92, \"label\", 27)(93, \"span\", 28);\n    i0.ɵɵtext(94, \"fact_check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(95, \" Reason for Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 29);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(98, \"div\", 25)(99, \"div\", 26)(100, \"label\", 27)(101, \"span\", 28);\n    i0.ɵɵtext(102, \"track_changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(103, \" Days in Sales Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"div\", 29);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 25)(107, \"div\", 26)(108, \"label\", 27)(109, \"span\", 28);\n    i0.ɵɵtext(110, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(111, \" Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"div\", 29);\n    i0.ɵɵtext(113);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(114, \"div\", 25)(115, \"div\", 26)(116, \"label\", 27)(117, \"span\", 28);\n    i0.ɵɵtext(118, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(119, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"div\", 29);\n    i0.ɵɵtext(121);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(122, \"div\", 25)(123, \"div\", 26)(124, \"label\", 27)(125, \"span\", 28);\n    i0.ɵɵtext(126, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(127, \" Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(128, \"div\", 29);\n    i0.ɵɵtext(129);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(130, \"div\", 25)(131, \"div\", 26)(132, \"label\", 27)(133, \"span\", 28);\n    i0.ɵɵtext(134, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(135, \" Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"div\", 29);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(138, \"div\", 25)(139, \"div\", 26)(140, \"label\", 27)(141, \"span\", 28);\n    i0.ɵɵtext(142, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(143, \" Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(144, \"div\", 29);\n    i0.ɵɵtext(145);\n    i0.ɵɵpipe(146, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(147, \"div\", 25)(148, \"div\", 26)(149, \"label\", 27)(150, \"span\", 28);\n    i0.ɵɵtext(151, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(152, \" Last Updated Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(153, \"div\", 29);\n    i0.ɵɵtext(154);\n    i0.ɵɵpipe(155, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(156, \"div\", 25)(157, \"div\", 26)(158, \"label\", 27)(159, \"span\", 28);\n    i0.ɵɵtext(160, \"update\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(161, \" Last Updated By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(162, \"div\", 29);\n    i0.ɵɵtext(163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(164, \"div\", 25)(165, \"div\", 26)(166, \"label\", 27)(167, \"span\", 28);\n    i0.ɵɵtext(168, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(169, \" Progress \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"div\", 29);\n    i0.ɵɵtext(171);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(172, \"div\", 25)(173, \"div\", 26)(174, \"label\", 27)(175, \"span\", 28);\n    i0.ɵɵtext(176, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(177, \" Need Help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"div\", 29);\n    i0.ɵɵtext(179);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.opportunity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_end_date) ? i0.ɵɵpipeBind2(33, 22, ctx_r0.overviewDetails.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"opportunityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.group_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.parent_opportunity) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunitySource\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.origin_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.weighted_expected_net_amount) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"opportunityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.life_cycle_status_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.result_reason_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.days_in_sales_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.probability_percent) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_organisation_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.sales_unit_party_id) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.expected_revenue_start_date) ? i0.ɵɵpipeBind2(146, 25, ctx_r0.overviewDetails.expected_revenue_start_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_change_date) ? i0.ɵɵpipeBind2(155, 28, ctx_r0.overviewDetails.last_change_date, \"MM/dd/yyyy hh:mm a\") : \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.last_changed_by) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.progress) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.need_help) || \"-\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_11_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_23_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template, 2, 1, \"span\", 55);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_42_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"input\", 57);\n    i0.ɵɵlistener(\"change\", function OpportunitiesOverviewComponent_form_6_ng_template_53_Template_input_change_1_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).item;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleSelection(item_r5.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template, 2, 1, \"span\", 55)(5, OpportunitiesOverviewComponent_form_6_ng_template_53_span_5_Template, 2, 1, \"span\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.isSelected(item_r5.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_54_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_form_6_div_87_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 30)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"label\", 31)(5, \"span\", 32);\n    i0.ɵɵtext(6, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Name \");\n    i0.ɵɵelementStart(8, \"span\", 33);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"input\", 34);\n    i0.ɵɵtemplate(11, OpportunitiesOverviewComponent_form_6_div_11_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"div\", 26)(14, \"label\", 31)(15, \"span\", 32);\n    i0.ɵɵtext(16, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \"Expected Value \");\n    i0.ɵɵelementStart(18, \"span\", 33);\n    i0.ɵɵtext(19, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 37);\n    i0.ɵɵelement(21, \"input\", 38)(22, \"p-dropdown\", 39);\n    i0.ɵɵtemplate(23, OpportunitiesOverviewComponent_form_6_div_23_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"div\", 26)(26, \"label\", 31)(27, \"span\", 32);\n    i0.ɵɵtext(28, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \"Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p-calendar\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 25)(32, \"div\", 26)(33, \"label\", 31)(34, \"span\", 32);\n    i0.ɵɵtext(35, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \"Account \");\n    i0.ɵɵelementStart(37, \"span\", 33);\n    i0.ɵɵtext(38, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"ng-select\", 41);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵtemplate(41, OpportunitiesOverviewComponent_form_6_ng_template_41_Template, 3, 2, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpportunitiesOverviewComponent_form_6_div_42_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 25)(44, \"div\", 26)(45, \"label\", 31)(46, \"span\", 32);\n    i0.ɵɵtext(47, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \"Primary Contact \");\n    i0.ɵɵelementStart(49, \"span\", 33);\n    i0.ɵɵtext(50, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"ng-select\", 43);\n    i0.ɵɵpipe(52, \"async\");\n    i0.ɵɵtemplate(53, OpportunitiesOverviewComponent_form_6_ng_template_53_Template, 6, 5, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpportunitiesOverviewComponent_form_6_div_54_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 25)(56, \"div\", 26)(57, \"label\", 31)(58, \"span\", 32);\n    i0.ɵɵtext(59, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-dropdown\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 25)(63, \"div\", 26)(64, \"label\", 31)(65, \"span\", 32);\n    i0.ɵɵtext(66, \"source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Source \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-dropdown\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 36)(70, \"div\", 26)(71, \"label\", 31)(72, \"span\", 32);\n    i0.ɵɵtext(73, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Weighted Value \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 37);\n    i0.ɵɵelement(76, \"input\", 46)(77, \"p-dropdown\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"div\", 25)(79, \"div\", 26)(80, \"label\", 31)(81, \"span\", 32);\n    i0.ɵɵtext(82, \"lens\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \"Status \");\n    i0.ɵɵelementStart(84, \"span\", 33);\n    i0.ɵɵtext(85, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(86, \"p-dropdown\", 47);\n    i0.ɵɵtemplate(87, OpportunitiesOverviewComponent_form_6_div_87_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 25)(89, \"div\", 26)(90, \"label\", 31)(91, \"span\", 32);\n    i0.ɵɵtext(92, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Probability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 25)(96, \"div\", 26)(97, \"label\", 31)(98, \"span\", 32);\n    i0.ɵɵtext(99, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Sales Organization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(101, \"input\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 25)(103, \"div\", 26)(104, \"label\", 31)(105, \"span\", 32);\n    i0.ɵɵtext(106, \"sell\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(107, \"Sales Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(108, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"div\", 25)(110, \"div\", 26)(111, \"label\", 31)(112, \"span\", 32);\n    i0.ɵɵtext(113, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(114, \"Created Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(115, \"p-calendar\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 25)(117, \"div\", 26)(118, \"label\", 31)(119, \"span\", 32);\n    i0.ɵɵtext(120, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \"Need help \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(122, \"p-inputSwitch\", 52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(123, \"div\", 53)(124, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_form_6_Template_button_click_124_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.OpportunityOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c2, ctx_r0.submitted && ctx_r0.f[\"name\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c2, ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 37, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(45, _c2, ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(52, 39, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(47, _c2, ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityCategory\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunitySource\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.currencies);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"opportunityStatus\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(49, _c2, ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 58)(2, \"div\", 56);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 60)(6, \"div\", 56);\n    i0.ɵɵtext(7, \" Created At \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 62)(10, \"div\", 56);\n    i0.ɵɵtext(11, \" Updated At \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 64);\n    i0.ɵɵtext(14, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 66)(10, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_15_Template_button_click_10_listener() {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_ng_template_15_Template_button_click_11_listener($event) {\n      const notes_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.stripHtml(notes_r7 == null ? null : notes_r7.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 3, notes_r7 == null ? null : notes_r7.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 6, notes_r7 == null ? null : notes_r7.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 69);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 69);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesOverviewComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, OpportunitiesOverviewComponent_div_24_div_1_Template, 2, 0, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class OpportunitiesOverviewComponent {\n  constructor(formBuilder, opportunitiesservice, activitiesservice, messageservice, confirmationservice, router) {\n    this.formBuilder = formBuilder;\n    this.opportunitiesservice = opportunitiesservice;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.router = router;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.notedetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.opportunity_id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.currencies = [{\n      label: 'USD',\n      value: 'USD'\n    }];\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.defaultSelected = false;\n    this.OpportunityOverviewForm = this.formBuilder.group({\n      name: ['', [Validators.required]],\n      prospect_party_id: ['', [Validators.required]],\n      primary_contact_party_id: ['', [Validators.required]],\n      origin_type_code: [''],\n      expected_revenue_amount: ['', [Validators.required]],\n      weighted_expected_net_amount: [''],\n      expected_revenue_start_date: [''],\n      expected_revenue_end_date: [''],\n      life_cycle_status_code: ['', [Validators.required]],\n      probability_percent: [''],\n      group_code: [''],\n      sales_organisation_id: [''],\n      sales_unit_party_id: [''],\n      currency: ['USD'],\n      need_help: ['']\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n  }\n  ngOnInit() {\n    // Opportunities successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('opportunitiesMessage');\n      }\n    }, 100);\n    this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    this.OpportunityOverviewForm.get('prospect_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.loadAccounts();\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.opportunity_id = response?.opportunity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.accountInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'FLCU01',\n        'filters[roles][bp_role][$in][1]': 'FLCU00',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Account fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.accountLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n        const currentValue = this.OpportunityOverviewForm.get('primary_contact_party_id')?.value;\n        // Only set default once and only if no manual selection\n        if (contacts.length > 0 && !currentValue?.length && !this.defaultSelected) {\n          this.OpportunityOverviewForm.get('primary_contact_party_id')?.setValue([contacts[0].bp_id]);\n          this.defaultSelected = true; // Mark as default set\n        }\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  toggleSelection(id) {\n    const control = this.OpportunityOverviewForm.get('primary_contact_party_id');\n    let currentValue = control?.value || [];\n    if (currentValue.includes(id)) {\n      currentValue = currentValue.filter(v => v !== id);\n    } else {\n      currentValue = [...currentValue, id];\n    }\n    control?.setValue(currentValue);\n  }\n  isSelected(id) {\n    return this.OpportunityOverviewForm.get('primary_contact_party_id')?.value?.includes(id);\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(opportunity) {\n    this.existingopportunity = {\n      opportunity_id: opportunity?.opportunity_id,\n      name: opportunity?.name,\n      group_code: opportunity?.group_code,\n      origin_type_code: opportunity?.origin_type_code,\n      probability_percent: opportunity?.probability_percent,\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\n      prospect_party_id: opportunity?.prospect_party_id,\n      primary_contact_party_id: opportunity?.primary_contact_party_id,\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\n      main_employee_responsible_party_id: opportunity?.main_employee_responsible_party_id,\n      sales_organisation_id: opportunity?.sales_organisation_id,\n      sales_unit_party_id: opportunity?.sales_unit_party_id\n    };\n    this.editid = opportunity.documentId;\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        opportunity_id: _this.opportunity_id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.opportunitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.opportunitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onSubmit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.OpportunityOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.OpportunityOverviewForm.value\n      };\n      const data = {\n        name: value?.name,\n        expected_revenue_amount: value?.expected_revenue_amount,\n        expected_revenue_end_date: value?.expected_revenue_end_date ? _this2.formatDate(value.expected_revenue_end_date) : null,\n        prospect_party_id: value?.prospect_party_id,\n        primary_contact_party_id: value?.primary_contact_party_id,\n        group_code: value?.group_code,\n        origin_type_code: value?.origin_type_code,\n        weighted_expected_net_amount: value?.weighted_expected_net_amount,\n        life_cycle_status_code: value?.life_cycle_status_code,\n        probability_percent: value?.probability_percent,\n        main_employee_responsible_party_id: value?.main_employee_responsible_party_id,\n        sales_organisation_id: value?.sales_organisation_id,\n        sales_unit_party_id: value?.sales_unit_party_id,\n        expected_revenue_start_date: value?.expected_revenue_start_date ? _this2.formatDate(value.expected_revenue_start_date) : null\n      };\n      _this2.opportunitiesservice.updateOpportunity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Opportunity Updated successFully!'\n          });\n          _this2.opportunitiesservice.getOpportunityByID(_this2.opportunity_id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const d = date instanceof Date ? date : new Date(date);\n    if (isNaN(d.getTime())) return ''; // handle invalid date\n    const yyyy = d.getFullYear();\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\n    const dd = String(d.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  get f() {\n    return this.OpportunityOverviewForm.controls;\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  onReset() {\n    this.submitted = false;\n    this.OpportunityOverviewForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesOverviewComponent_Factory(t) {\n      return new (t || OpportunitiesOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.ActivitiesService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesOverviewComponent,\n      selectors: [[\"app-opportunities-overview\"]],\n      decls: 28,\n      vars: 26,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"outlined\", \"styleClass\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"font-medium\", \"text-700\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [1, \"flex\", \"align-items-center\", \"w-full\", \"gap-2\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"currency\", \"optionLabel\", \"label\", \"placeholder\", \"Currency\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"expected_revenue_end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Expected Decision Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select Category\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select Source\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"weighted_expected_net_amount\", \"placeholder\", \"Weighted Value\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select Status\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_organisation_id\", \"type\", \"text\", \"formControlName\", \"sales_organisation_id\", \"placeholder\", \"Sales Organization'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"sales_unit_party_id\", \"type\", \"text\", \"formControlName\", \"sales_unit_party_id\", \"placeholder\", \"Sales Unit\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"expected_revenue_start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Created Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"need_help\", 1, \"h-3rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"], [\"pSortableColumn\", \"note\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\"], [\"field\", \"note\"], [\"pSortableColumn\", \"createdAt\", 2, \"width\", \"10rem\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"updatedAt\", 2, \"width\", \"10rem\"], [\"field\", \"updatedAt\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function OpportunitiesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, OpportunitiesOverviewComponent_div_5_Template, 180, 31, \"div\", 4)(6, OpportunitiesOverviewComponent_form_6_Template, 125, 51, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-button\", 8);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_p_button_click_11_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"p-table\", 10);\n          i0.ɵɵtemplate(14, OpportunitiesOverviewComponent_ng_template_14_Template, 15, 0, \"ng-template\", 11)(15, OpportunitiesOverviewComponent_ng_template_15_Template, 12, 9, \"ng-template\", 12)(16, OpportunitiesOverviewComponent_ng_template_16_Template, 3, 0, \"ng-template\", 13)(17, OpportunitiesOverviewComponent_ng_template_17_Template, 3, 0, \"ng-template\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"p-dialog\", 15);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(19, OpportunitiesOverviewComponent_ng_template_19_Template, 2, 0, \"ng-template\", 11);\n          i0.ɵɵelementStart(20, \"form\", 16)(21, \"div\", 17)(22, \"div\", 18);\n          i0.ɵɵelement(23, \"p-editor\", 19);\n          i0.ɵɵtemplate(24, OpportunitiesOverviewComponent_div_24_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_26_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function OpportunitiesOverviewComponent_Template_button_click_27_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"outlined\", true)(\"styleClass\", \"w-5rem font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.Table, i4.PrimeTemplate, i7.SortableColumn, i7.SortIcon, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.Editor, i12.NgSelectComponent, i12.NgOptionTemplateDirective, i13.InputSwitch, i14.Tooltip, i15.Calendar, i16.InputText, i6.AsyncPipe, i6.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1vdmVydmlldy9vcHBvcnR1bml0aWVzLW92ZXJ2aWV3LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUkscUJBQUE7RUFDQSxXQUFBO0FBQ0o7O0FBSVE7RUFDSSxrQkFBQTtBQURaO0FBR1k7RUFDSSw0QkFBQTtFQUNBLDJDQUFBO0FBRGhCO0FBR2dCO0VBQ0ksU0FBQTtBQURwQjtBQUtZO0VBQ0ksNEJBQUE7RUFDQSxpQkFBQTtBQUhoQiIsInNvdXJjZXNDb250ZW50IjpbIi5pbnZhbGlkLWZlZWRiYWNrLFxyXG4ucC1pbnB1dHRleHQ6aW52YWxpZCxcclxuLmlzLWNoZWNrYm94LWludmFsaWQsXHJcbi5wLWlucHV0dGV4dC5pcy1pbnZhbGlkIHtcclxuICAgIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICAgIHJpZ2h0OiAxMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAge1xyXG4gICAgLm5vdGUtcG9wdXAge1xyXG4gICAgICAgIC5wLWRpYWxvZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNTBweDtcclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "opportunity_id", "name", "ɵɵtextInterpolate1", "expected_revenue_amount", "expected_revenue_end_date", "ɵɵpipeBind2", "business_partner", "bp_full_name", "business_partner_contact", "getLabelFromDropdown", "group_code", "parent_opportunity", "origin_type_code", "weighted_expected_net_amount", "life_cycle_status_code", "result_reason_code", "days_in_sales_status", "probability_percent", "business_partner_owner", "sales_organisation_id", "sales_unit_party_id", "expected_revenue_start_date", "last_change_date", "last_changed_by", "progress", "need_help", "ɵɵtemplate", "OpportunitiesOverviewComponent_form_6_div_11_div_1_Template", "ɵɵproperty", "submitted", "f", "errors", "OpportunitiesOverviewComponent_form_6_div_23_div_1_Template", "item_r3", "OpportunitiesOverviewComponent_form_6_ng_template_41_span_2_Template", "bp_id", "OpportunitiesOverviewComponent_form_6_div_42_div_1_Template", "item_r5", "email", "mobile", "ɵɵlistener", "OpportunitiesOverviewComponent_form_6_ng_template_53_Template_input_change_1_listener", "ɵɵrestoreView", "_r4", "item", "ɵɵnextContext", "ɵɵresetView", "toggleSelection", "OpportunitiesOverviewComponent_form_6_ng_template_53_span_4_Template", "OpportunitiesOverviewComponent_form_6_ng_template_53_span_5_Template", "isSelected", "ɵɵtextInterpolate2", "OpportunitiesOverviewComponent_form_6_div_54_div_1_Template", "OpportunitiesOverviewComponent_form_6_div_87_div_1_Template", "ɵɵelement", "OpportunitiesOverviewComponent_form_6_div_11_Template", "OpportunitiesOverviewComponent_form_6_div_23_Template", "OpportunitiesOverviewComponent_form_6_ng_template_41_Template", "OpportunitiesOverviewComponent_form_6_div_42_Template", "OpportunitiesOverviewComponent_form_6_ng_template_53_Template", "OpportunitiesOverviewComponent_form_6_div_54_Template", "OpportunitiesOverviewComponent_form_6_div_87_Template", "OpportunitiesOverviewComponent_form_6_Template_button_click_124_listener", "_r2", "onSubmit", "OpportunityOverviewForm", "ɵɵpureFunction1", "_c2", "currencies", "ɵɵpipeBind1", "accounts$", "accountLoading", "accountInput$", "contacts$", "contactLoading", "contactInput$", "dropdowns", "OpportunitiesOverviewComponent_ng_template_15_Template_button_click_10_listener", "notes_r7", "_r6", "$implicit", "editNote", "OpportunitiesOverviewComponent_ng_template_15_Template_button_click_11_listener", "$event", "stopPropagation", "confirmRemove", "stripHtml", "note", "createdAt", "updatedAt", "OpportunitiesOverviewComponent_div_24_div_1_Template", "fNote", "OpportunitiesOverviewComponent", "constructor", "formBuilder", "opportunitiesservice", "activitiesservice", "messageservice", "confirmationservice", "router", "ngUnsubscribe", "notedetails", "defaultOptions", "saving", "editid", "isEditMode", "label", "value", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "defaultSelected", "group", "required", "prospect_party_id", "primary_contact_party_id", "currency", "NoteForm", "opportunityCategory", "opportunityStatus", "opportunitySource", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadOpportunityDropDown", "get", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "err", "console", "error", "subscribe", "loadAccounts", "opportunity", "response", "notes", "fetchOverviewData", "target", "type", "getOpportunityDropdownOptions", "res", "data", "attr", "description", "code", "dropdownKey", "find", "opt", "term", "params", "getPartners", "bpId", "getPartnersContact", "contacts", "currentValue", "length", "setValue", "id", "control", "includes", "filter", "v", "documentId", "patchValue", "existingopportunity", "main_employee_responsible_party_id", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getOpportunityByID", "createNote", "_this2", "formatDate", "updateOpportunity", "next", "date", "d", "Date", "isNaN", "getTime", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "confirm", "message", "header", "icon", "accept", "remove", "deleteNote", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "showDialog", "position", "controls", "toggleEdit", "onReset", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "OpportunitiesService", "i3", "ActivitiesService", "i4", "MessageService", "ConfirmationService", "i5", "Router", "selectors", "decls", "vars", "consts", "template", "OpportunitiesOverviewComponent_Template", "rf", "ctx", "OpportunitiesOverviewComponent_Template_p_button_click_4_listener", "OpportunitiesOverviewComponent_div_5_Template", "OpportunitiesOverviewComponent_form_6_Template", "OpportunitiesOverviewComponent_Template_p_button_click_11_listener", "OpportunitiesOverviewComponent_ng_template_14_Template", "OpportunitiesOverviewComponent_ng_template_15_Template", "OpportunitiesOverviewComponent_ng_template_16_Template", "OpportunitiesOverviewComponent_ng_template_17_Template", "ɵɵtwoWayListener", "OpportunitiesOverviewComponent_Template_p_dialog_visibleChange_18_listener", "ɵɵtwoWayBindingSet", "OpportunitiesOverviewComponent_ng_template_19_Template", "OpportunitiesOverviewComponent_div_24_Template", "OpportunitiesOverviewComponent_Template_button_click_26_listener", "OpportunitiesOverviewComponent_Template_button_click_27_listener", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-overview',\r\n  templateUrl: './opportunities-overview.component.html',\r\n  styleUrl: './opportunities-overview.component.scss',\r\n})\r\nexport class OpportunitiesOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public notedetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingopportunity: any;\r\n  public opportunity_id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public currencies = [{ label: 'USD', value: 'USD' }];\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n  private defaultSelected = false;\r\n\r\n  public OpportunityOverviewForm: FormGroup = this.formBuilder.group({\r\n    name: ['', [Validators.required]],\r\n    prospect_party_id: ['', [Validators.required]],\r\n    primary_contact_party_id: ['', [Validators.required]],\r\n    origin_type_code: [''],\r\n    expected_revenue_amount: ['', [Validators.required]],\r\n    weighted_expected_net_amount: [''],\r\n    expected_revenue_start_date: [''],\r\n    expected_revenue_end_date: [''],\r\n    life_cycle_status_code: ['', [Validators.required]],\r\n    probability_percent: [''],\r\n    group_code: [''],\r\n    sales_organisation_id: [''],\r\n    sales_unit_party_id: [''],\r\n    currency: ['USD'],\r\n    need_help: [''],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Opportunities successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('opportunitiesMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('opportunitiesMessage');\r\n      }\r\n    }, 100);\r\n    this.loadOpportunityDropDown(\r\n      'opportunityCategory',\r\n      'CRM_OPPORTUNITY_GROUP'\r\n    );\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.loadOpportunityDropDown(\r\n      'opportunitySource',\r\n      'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n    );\r\n    this.OpportunityOverviewForm.get('prospect_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.ngUnsubscribe),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.loadAccounts();\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.opportunity_id = response?.opportunity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.accountInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'FLCU01',\r\n            'filters[roles][bp_role][$in][1]': 'FLCU00',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.opportunitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Account fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.accountLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n\r\n            const currentValue = this.OpportunityOverviewForm.get(\r\n              'primary_contact_party_id'\r\n            )?.value;\r\n\r\n            // Only set default once and only if no manual selection\r\n            if (\r\n              contacts.length > 0 &&\r\n              !currentValue?.length &&\r\n              !this.defaultSelected\r\n            ) {\r\n              this.OpportunityOverviewForm.get(\r\n                'primary_contact_party_id'\r\n              )?.setValue([contacts[0].bp_id]);\r\n              this.defaultSelected = true; // Mark as default set\r\n            }\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  toggleSelection(id: string): void {\r\n    const control = this.OpportunityOverviewForm.get(\r\n      'primary_contact_party_id'\r\n    );\r\n    let currentValue = control?.value || [];\r\n\r\n    if (currentValue.includes(id)) {\r\n      currentValue = currentValue.filter((v: any) => v !== id);\r\n    } else {\r\n      currentValue = [...currentValue, id];\r\n    }\r\n\r\n    control?.setValue(currentValue);\r\n  }\r\n\r\n  isSelected(id: string): boolean {\r\n    return this.OpportunityOverviewForm.get(\r\n      'primary_contact_party_id'\r\n    )?.value?.includes(id);\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(opportunity: any) {\r\n    this.existingopportunity = {\r\n      opportunity_id: opportunity?.opportunity_id,\r\n      name: opportunity?.name,\r\n      group_code: opportunity?.group_code,\r\n      origin_type_code: opportunity?.origin_type_code,\r\n      probability_percent: opportunity?.probability_percent,\r\n      expected_revenue_amount: opportunity?.expected_revenue_amount,\r\n      expected_revenue_start_date: opportunity?.expected_revenue_start_date,\r\n      expected_revenue_end_date: opportunity?.expected_revenue_end_date,\r\n      prospect_party_id: opportunity?.prospect_party_id,\r\n      primary_contact_party_id: opportunity?.primary_contact_party_id,\r\n      weighted_expected_net_amount: opportunity?.weighted_expected_net_amount,\r\n      life_cycle_status_code: opportunity?.life_cycle_status_code,\r\n      main_employee_responsible_party_id:\r\n        opportunity?.main_employee_responsible_party_id,\r\n      sales_organisation_id: opportunity?.sales_organisation_id,\r\n      sales_unit_party_id: opportunity?.sales_unit_party_id,\r\n    };\r\n    this.editid = opportunity.documentId;\r\n    this.OpportunityOverviewForm.patchValue(this.existingopportunity);\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      opportunity_id: this.opportunity_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.opportunitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.opportunitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.OpportunityOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.OpportunityOverviewForm.value };\r\n\r\n    const data = {\r\n      name: value?.name,\r\n      expected_revenue_amount: value?.expected_revenue_amount,\r\n      expected_revenue_end_date: value?.expected_revenue_end_date\r\n        ? this.formatDate(value.expected_revenue_end_date)\r\n        : null,\r\n      prospect_party_id: value?.prospect_party_id,\r\n      primary_contact_party_id: value?.primary_contact_party_id,\r\n      group_code: value?.group_code,\r\n      origin_type_code: value?.origin_type_code,\r\n      weighted_expected_net_amount: value?.weighted_expected_net_amount,\r\n      life_cycle_status_code: value?.life_cycle_status_code,\r\n      probability_percent: value?.probability_percent,\r\n      main_employee_responsible_party_id:\r\n        value?.main_employee_responsible_party_id,\r\n      sales_organisation_id: value?.sales_organisation_id,\r\n      sales_unit_party_id: value?.sales_unit_party_id,\r\n      expected_revenue_start_date: value?.expected_revenue_start_date\r\n        ? this.formatDate(value.expected_revenue_start_date)\r\n        : null,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .updateOpportunity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Opportunity Updated successFully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    if (!date) return '';\r\n    const d = date instanceof Date ? date : new Date(date);\r\n    if (isNaN(d.getTime())) return ''; // handle invalid date\r\n    const yyyy = d.getFullYear();\r\n    const mm = String(d.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(d.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.OpportunityOverviewForm.controls;\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.OpportunityOverviewForm.reset();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil':''\" iconPos=\"right\"\r\n            [outlined]=\"true\" class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold'\" (click)=\"toggleEdit()\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">tag</span> Opportunity ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.opportunity_id || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> Name\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.name || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">attach_money</span> Expected Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.expected_revenue_amount || '-'}}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Expected Decision\r\n                    Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_end_date ?\r\n                    (overviewDetails.expected_revenue_end_date | date: 'MM/dd/yyyy') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.business_partner?.bp_full_name\r\n                    || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">contact_phone</span> Primary Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_contact?.bp_full_name\r\n                    || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityCategory',\r\n                    overviewDetails?.group_code) || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_tree</span> Parent Opportunity\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.parent_opportunity || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">source</span> Source\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunitySource',\r\n                    overviewDetails?.origin_type_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">scale</span> Weighted Value\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.weighted_expected_net_amount ||\r\n                    '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">lens</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('opportunityStatus',\r\n                    overviewDetails?.life_cycle_status_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">fact_check</span> Reason for Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.result_reason_code || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">track_changes</span> Days in Sales\r\n                    Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.days_in_sales_status || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Probability\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.probability_percent || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Owner\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">business</span> Sales Organization\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_organisation_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">sell</span> Sales Unit\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.sales_unit_party_id || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Create Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.expected_revenue_start_date ?\r\n                    (overviewDetails.expected_revenue_start_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">event</span> Last Updated Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.last_change_date ?\r\n                    (overviewDetails.last_change_date | date: 'MM/dd/yyyy hh:mm a') : '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">update</span> Last Updated By\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.last_changed_by || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">trending_up</span> Progress\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.progress || '-' }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 font-medium text-700\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">help</span> Need Help\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.need_help || '-' }}</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"OpportunityOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">badge</span> Name\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"name\" type=\"text\" formControlName=\"name\" placeholder=\"Name\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['name'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['name'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['name'].errors &&\r\n                                f['name'].errors['required']\r\n                              \">\r\n                            Name is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">attach_money</span>Expected Value\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"expected_revenue_amount\"\r\n                            placeholder=\"Expected Value\" class=\"h-3rem w-full\"\r\n                            [ngClass]=\"{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"currency\" optionLabel=\"label\"\r\n                            placeholder=\"Currency\" styleClass=\"h-3rem w-full\"></p-dropdown>\r\n                        <div *ngIf=\"submitted && f['expected_revenue_amount'].errors\" class=\"p-error\">\r\n                            <div *ngIf=\"\r\n                                submitted &&\r\n                                f['expected_revenue_amount'].errors &&\r\n                                f['expected_revenue_amount'].errors['required']\r\n                              \">\r\n                                Expected Value is required.\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Expected Decision Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Expected Decision Date\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"prospect_party_id\" [typeahead]=\"accountInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [ngClass]=\"{ 'is-invalid': submitted && f['prospect_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['prospect_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['prospect_party_id'].errors &&\r\n                                f['prospect_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">contact_phone</span>Primary Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"primary_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <input type=\"checkbox\" [checked]=\"isSelected(item.bp_id)\"\r\n                                    (change)=\"toggleSelection(item.bp_id)\"\r\n                                    style=\"width: 20px; height: 20px; margin-right: 6px;\" />\r\n                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </div>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['primary_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['primary_contact_party_id'].errors &&\r\n                                f['primary_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityCategory']\" formControlName=\"group_code\"\r\n                        placeholder=\"Select Category\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">source</span>Source\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunitySource']\" formControlName=\"origin_type_code\"\r\n                        placeholder=\"Select Source\" [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-6 sm:col-12\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">scale</span>Weighted Value\r\n                    </label>\r\n\r\n                    <div class=\"flex align-items-center w-full gap-2\">\r\n                        <input pInputText type=\"text\" formControlName=\"weighted_expected_net_amount\"\r\n                            placeholder=\"Weighted Value\" class=\"h-3rem w-full\" />\r\n\r\n                        <p-dropdown [options]=\"currencies\" formControlName=\"currency\" optionLabel=\"label\"\r\n                            placeholder=\"Currency\" styleClass=\"h-3rem w-full\"></p-dropdown>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">lens</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['opportunityStatus']\" formControlName=\"life_cycle_status_code\"\r\n                        placeholder=\"Select Status\" [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['life_cycle_status_code'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['life_cycle_status_code'].errors &&\r\n                                f['life_cycle_status_code'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">trending_up</span>Probability\r\n                    </label>\r\n                    <input pInputText id=\"probability_percent\" type=\"text\" formControlName=\"probability_percent\"\r\n                        placeholder=\"Probability'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">business</span>Sales Organization\r\n                    </label>\r\n                    <input pInputText id=\"sales_organisation_id\" type=\"text\" formControlName=\"sales_organisation_id\"\r\n                        placeholder=\"Sales Organization'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">sell</span>Sales Unit\r\n                    </label>\r\n                    <input pInputText id=\"sales_unit_party_id\" type=\"text\" formControlName=\"sales_unit_party_id\"\r\n                        placeholder=\"Sales Unit\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">event</span>Created Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"expected_revenue_start_date\" [showButtonBar]=\"true\"\r\n                        dateFormat=\"yy-mm-dd\" placeholder=\"Created Date\" [showIcon]=\"true\"\r\n                        styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">help</span>Need help\r\n                    </label>\r\n                    <p-inputSwitch formControlName=\"need_help\" class=\"h-3rem w-full\"></p-inputSwitch>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n<div class=\"p-3 w-full bg-white border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n        <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"notedetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"note\" style=\"width: 50rem;\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Note\r\n                            <p-sortIcon field=\"note\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Created At\r\n                            <p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"updatedAt\" style=\"width: 10rem;\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Updated At\r\n                            <p-sortIcon field=\"updatedAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg\" style=\"width: 7rem;\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        {{ stripHtml(notes?.note) || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.createdAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AACtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAHC,EALhB,CAAAC,cAAA,cAA6D,cAEV,cACnB,gBACmD,eACN;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAEvGF,EAFuG,CAAAG,YAAA,EAAM,EACnG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,cAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gCAE9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,yBACtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAG/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IACX;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAC9C,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACmD,gBACN;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,2BACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,+BAEtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACc;IAE3EF,EAF2E,CAAAG,YAAA,EAAM,EACvE,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,6BACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAE/C;;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,0BAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAA6C;IAE1GF,EAF0G,CAAAG,YAAA,EAAM,EACtG,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAAsC;IAEnGF,EAFmG,CAAAG,YAAA,EAAM,EAC/F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACmD,iBACN;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,oBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAAuC;IAGxGF,EAHwG,CAAAG,YAAA,EAAM,EAChG,EACJ,EACJ;;;;IA1M2DH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,cAAA,SAA0C;IAQ1CR,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,IAAA,SAC/C;IAQ+CT,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,uBAAA,cACrD;IASqDX,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAK,yBAAA,IAAAZ,EAAA,CAAAa,WAAA,SAAAP,MAAA,CAAAC,eAAA,CAAAK,yBAAA,sBAE/C;IAQ+CZ,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAO,gBAAA,kBAAAR,MAAA,CAAAC,eAAA,CAAAO,gBAAA,CAAAC,YAAA,SAE/C;IAQ+Cf,EAAA,CAAAI,SAAA,GAG/C;IAH+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAS,wBAAA,kBAAAV,MAAA,CAAAC,eAAA,CAAAS,wBAAA,CAAAD,YAAA,SAG/C;IAQ+Cf,EAAA,CAAAI,SAAA,GACX;IADWJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAW,oBAAA,wBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,UAAA,SACX;IAQWlB,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAY,kBAAA,SAC/C;IAQ+CnB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAW,oBAAA,sBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAa,gBAAA,cAErD;IAQqDpB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAc,4BAAA,cAErD;IAQqDrB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAW,oBAAA,sBAAAX,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,sBAAA,cAErD;IAQqDtB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAgB,kBAAA,cAGrD;IASqDvB,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,oBAAA,cACrD;IAQqDxB,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,mBAAA,cACrD;IAQqDzB,EAAA,CAAAI,SAAA,GACc;IADdJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,sBAAA,kBAAApB,MAAA,CAAAC,eAAA,CAAAmB,sBAAA,CAAAX,YAAA,SACc;IAQdf,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAoB,qBAAA,cACrD;IAQqD3B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAqB,mBAAA,cACrD;IAQqD5B,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAsB,2BAAA,IAAA7B,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAAsB,2BAAA,8BAE/C;IAQ+C7B,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAuB,gBAAA,IAAA9B,EAAA,CAAAa,WAAA,UAAAP,MAAA,CAAAC,eAAA,CAAAuB,gBAAA,8BAE/C;IAQ+C9B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAwB,eAAA,SAA6C;IAQ7C/B,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAyB,QAAA,SAAsC;IAQtChC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA0B,SAAA,SAAuC;;;;;IAepFjC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAkC,UAAA,IAAAC,2DAAA,kBAIQ;IAGZnC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,SAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,SAAAC,MAAA,aAID;;;;;IAqBDvC,EAAA,CAAAC,cAAA,UAII;IACAD,EAAA,CAAAE,MAAA,oCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8E;IAC1ED,EAAA,CAAAkC,UAAA,IAAAM,2DAAA,kBAII;IAGRxC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIL;IAJKJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,4BAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,4BAAAC,MAAA,aAIL;;;;;IA8BDvC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,QAAA+B,OAAA,CAAA1B,YAAA,KAAyB;;;;;IAD1Df,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAkC,UAAA,IAAAQ,oEAAA,mBAAgC;;;;IAD1B1C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAE,KAAA,CAAgB;IACf3C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAoC,UAAA,SAAAK,OAAA,CAAA1B,YAAA,CAAuB;;;;;IAIlCf,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAwE;IACpED,EAAA,CAAAkC,UAAA,IAAAU,2DAAA,kBAIQ;IAGZ5C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,sBAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,sBAAAC,MAAA,aAID;;;;;IAuBGvC,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAU,kBAAA,QAAAmC,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5C9C,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAU,kBAAA,QAAAmC,OAAA,CAAAE,MAAA,KAAmB;;;;;;IAL9C/C,EADJ,CAAAC,cAAA,cAA2C,gBAGqB;IADxDD,EAAA,CAAAgD,UAAA,oBAAAC,sFAAA;MAAA,MAAAJ,OAAA,GAAA7C,EAAA,CAAAkD,aAAA,CAAAC,GAAA,EAAAC,IAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAqD,aAAA;MAAA,OAAArD,EAAA,CAAAsD,WAAA,CAAUhD,MAAA,CAAAiD,eAAA,CAAAV,OAAA,CAAAF,KAAA,CAA2B;IAAA,EAAC;IAD1C3C,EAAA,CAAAG,YAAA,EAE4D;IAC5DH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAkC,UAAA,IAAAsB,oEAAA,mBAAyB,IAAAC,oEAAA,mBACC;IAC9BzD,EAAA,CAAAG,YAAA,EAAM;;;;;IANqBH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAoD,UAAA,CAAAb,OAAA,CAAAF,KAAA,EAAkC;IAGnD3C,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAA2D,kBAAA,KAAAd,OAAA,CAAAF,KAAA,QAAAE,OAAA,CAAA9B,YAAA,KAAyC;IACxCf,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAoC,UAAA,SAAAS,OAAA,CAAAC,KAAA,CAAgB;IAChB9C,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAoC,UAAA,SAAAS,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhC/C,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAkC,UAAA,IAAA0B,2DAAA,kBAIQ;IAGZ5D,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,6BAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,6BAAAC,MAAA,aAID;;;;;IAoDLvC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA6E;IACzED,EAAA,CAAAkC,UAAA,IAAA2B,2DAAA,kBAIQ;IAGZ7D,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,2BAAAC,MAAA,IAAAjC,MAAA,CAAAgC,CAAA,2BAAAC,MAAA,aAID;;;;;;IAlKLvC,EALpB,CAAAC,cAAA,eAA+D,cAClB,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,aACtE;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAA8D,SAAA,iBACwF;IACxF9D,EAAA,CAAAkC,UAAA,KAAA6B,qDAAA,kBAA2D;IAUnE/D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAK9CD,EAJA,CAAA8D,SAAA,iBAEqF,sBAGlB;IACnE9D,EAAA,CAAAkC,UAAA,KAAA8B,qDAAA,kBAA8E;IAW1FhE,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,+BACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,sBACmG;IAE3G9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAC9E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAG6F;;IACzFD,EAAA,CAAAkC,UAAA,KAAA+B,6DAAA,0BAA2C;IAI/CjE,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAkC,UAAA,KAAAgC,qDAAA,kBAAwE;IAUhFlE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,wBAC7E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIoF;;IAChFD,EAAA,CAAAkC,UAAA,KAAAiC,6DAAA,0BAA2C;IAU/CnE,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAkC,UAAA,KAAAkC,qDAAA,kBAA+E;IAUvFpE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,sBAEa;IAErB9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC1E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,sBAEa;IAErB9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAgD,eACpB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAC,cAAA,eAAkD;IAI9CD,EAHA,CAAA8D,SAAA,iBACyD,sBAGU;IAG/E9D,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACpE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAA8D,SAAA,sBAGa;IACb9D,EAAA,CAAAkC,UAAA,KAAAmC,qDAAA,kBAA6E;IAUrFrE,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,iBACuD;IAE/D9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,4BAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,kBAC8D;IAEtE9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,kBACqD;IAE7D9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,sBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,uBAE4C;IAEpD9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,mBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA8D,SAAA,0BAAiF;IAG7F9D,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAEvB;IAArBD,EAAA,CAAAgD,UAAA,mBAAAsB,yEAAA;MAAAtE,EAAA,CAAAkD,aAAA,CAAAqB,GAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAqD,aAAA;MAAA,OAAArD,EAAA,CAAAsD,WAAA,CAAShD,MAAA,CAAAkE,QAAA,EAAU;IAAA,EAAC;IAEhCxE,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IAhOkBH,EAAA,CAAAoC,UAAA,cAAA9B,MAAA,CAAAmE,uBAAA,CAAqC;IASpBzE,EAAA,CAAAI,SAAA,IAA2D;IAA3DJ,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,SAAAC,MAAA,EAA2D;IAC/EvC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,SAAAC,MAAA,CAAmC;IAqBjCvC,EAAA,CAAAI,SAAA,IAA8E;IAA9EJ,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,4BAAAC,MAAA,EAA8E;IAEtEvC,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAsE,UAAA,CAAsB;IAE5B5E,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,4BAAAC,MAAA,CAAsD;IAkBRvC,EAAA,CAAAI,SAAA,GAAsB;IACrCJ,EADe,CAAAoC,UAAA,uBAAsB,kBACpB;IAUpCpC,EAAA,CAAAI,SAAA,GAA2B;IAG7BJ,EAHE,CAAAoC,UAAA,UAAApC,EAAA,CAAA6E,WAAA,SAAAvE,MAAA,CAAAwE,SAAA,EAA2B,sBACxB,YAAAxE,MAAA,CAAAyE,cAAA,CAA2B,oBAAoB,cAAAzE,MAAA,CAAA0E,aAAA,CACL,wBAAwB,YAAAhF,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,sBAAAC,MAAA,EACC;IAMtFvC,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,sBAAAC,MAAA,CAAgD;IAiBhCvC,EAAA,CAAAI,SAAA,GAA2B;IAI7CJ,EAJkB,CAAAoC,UAAA,UAAApC,EAAA,CAAA6E,WAAA,SAAAvE,MAAA,CAAA2E,SAAA,EAA2B,sBACxB,YAAA3E,MAAA,CAAA4E,cAAA,CAA2B,oBAAoB,cAAA5E,MAAA,CAAA6E,aAAA,CACE,wBAAwB,kBAC7D,wBAAwB,YAAAnF,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,6BAAAC,MAAA,EACsB;IAY7EvC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,6BAAAC,MAAA,CAAuD;IAgBjDvC,EAAA,CAAAI,SAAA,GAA4C;IACtBJ,EADtB,CAAAoC,UAAA,YAAA9B,MAAA,CAAA8E,SAAA,wBAA4C,+BACQ;IASpDpF,EAAA,CAAAI,SAAA,GAA0C;IACtBJ,EADpB,CAAAoC,UAAA,YAAA9B,MAAA,CAAA8E,SAAA,sBAA0C,+BACQ;IAc9CpF,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAsE,UAAA,CAAsB;IAW1B5E,EAAA,CAAAI,SAAA,GAA0C;IAElDJ,EAFQ,CAAAoC,UAAA,YAAA9B,MAAA,CAAA8E,SAAA,sBAA0C,+BACQ,YAAApF,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAArE,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,2BAAAC,MAAA,EACmB;IAE3EvC,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+B,SAAA,IAAA/B,MAAA,CAAAgC,CAAA,2BAAAC,MAAA,CAAqD;IA4CDvC,EAAA,CAAAI,SAAA,IAAsB;IAC3BJ,EADK,CAAAoC,UAAA,uBAAsB,kBACV;;;;;IAiClEpC,EAFR,CAAAC,cAAA,SAAI,aAC8E,cAC/B;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAA8D,SAAA,qBAAsC;IAE9C9D,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,cACP;IACvCD,EAAA,CAAAE,MAAA,mBACA;IAAAF,EAAA,CAAA8D,SAAA,qBAA2C;IAEnD9D,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAsD,eACP;IACvCD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAA8D,SAAA,sBAA2C;IAEnD9D,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAClEF,EADkE,CAAAG,YAAA,EAAK,EAClE;;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAkC,kBAEA;IAA1BD,EAAA,CAAAgD,UAAA,mBAAAqC,gFAAA;MAAA,MAAAC,QAAA,GAAAtF,EAAA,CAAAkD,aAAA,CAAAqC,GAAA,EAAAC,SAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAAqD,aAAA;MAAA,OAAArD,EAAA,CAAAsD,WAAA,CAAShD,MAAA,CAAAmF,QAAA,CAAAH,QAAA,CAAe;IAAA,EAAC;IAACtF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,kBAC8D;IAA1DD,EAAA,CAAAgD,UAAA,mBAAA0C,gFAAAC,MAAA;MAAA,MAAAL,QAAA,GAAAtF,EAAA,CAAAkD,aAAA,CAAAqC,GAAA,EAAAC,SAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAAqD,aAAA;MAASsC,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA5F,EAAA,CAAAsD,WAAA,CAAEhD,MAAA,CAAAuF,aAAA,CAAAP,QAAA,CAAoB;IAAA,EAAE;IAErEtF,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAdGH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAwF,SAAA,CAAAR,QAAA,kBAAAA,QAAA,CAAAS,IAAA,cACJ;IAEI/F,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAa,WAAA,OAAAyE,QAAA,kBAAAA,QAAA,CAAAU,SAAA,8BACJ;IAEIhG,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAa,WAAA,OAAAyE,QAAA,kBAAAA,QAAA,CAAAW,SAAA,8BACJ;;;;;IAWAjG,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACnCF,EADmC,CAAAG,YAAA,EAAK,EACnC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACtDF,EADsD,CAAAG,YAAA,EAAK,EACtD;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAAkC,UAAA,IAAAgE,oDAAA,kBAA8C;IAClDlG,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA6F,KAAA,SAAA5D,MAAA,aAAsC;;;ADpfhE,OAAM,MAAO6D,8BAA8B;EAqDzCC,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAwC,EACxCC,MAAc;IALd,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IA1DR,KAAAC,aAAa,GAAG,IAAIxH,OAAO,EAAQ;IACpC,KAAAmB,eAAe,GAAQ,IAAI;IAC3B,KAAAsG,WAAW,GAAQ,IAAI;IAEvB,KAAA9B,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI5F,OAAO,EAAU;IAErC,KAAA8F,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI/F,OAAO,EAAU;IACpC,KAAA0H,cAAc,GAAQ,EAAE;IACzB,KAAAzE,SAAS,GAAG,KAAK;IACjB,KAAA0E,MAAM,GAAG,KAAK;IAEd,KAAAvG,cAAc,GAAW,EAAE;IAC3B,KAAAwG,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAArC,UAAU,GAAG,CAAC;MAAEsC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAAC;IAC7C,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IACtB,KAAAC,eAAe,GAAG,KAAK;IAExB,KAAAhD,uBAAuB,GAAc,IAAI,CAAC6B,WAAW,CAACoB,KAAK,CAAC;MACjEjH,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACwI,QAAQ,CAAC,CAAC;MACjCC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACzI,UAAU,CAACwI,QAAQ,CAAC,CAAC;MAC9CE,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAAC1I,UAAU,CAACwI,QAAQ,CAAC,CAAC;MACrDvG,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBT,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACwI,QAAQ,CAAC,CAAC;MACpDtG,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClCQ,2BAA2B,EAAE,CAAC,EAAE,CAAC;MACjCjB,yBAAyB,EAAE,CAAC,EAAE,CAAC;MAC/BU,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACwI,QAAQ,CAAC,CAAC;MACnDlG,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBP,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBS,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBkG,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB7F,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;IAEK,KAAA8F,QAAQ,GAAc,IAAI,CAACzB,WAAW,CAACoB,KAAK,CAAC;MAClD3B,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC5G,UAAU,CAACwI,QAAQ,CAAC;KACjC,CAAC;IAEK,KAAAvC,SAAS,GAA0B;MACxC4C,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;EASE;EAEHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,sBAAsB,CAAC;MACrE,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC5B,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,sBAAsB,CAAC;MACnD;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;IACD,IAAI,CAACA,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACD,IAAI,CAACnE,uBAAuB,CAACoE,GAAG,CAAC,mBAAmB,CAAC,EACjDC,YAAY,CAACC,IAAI,CACjB1J,SAAS,CAAC,IAAI,CAACuH,aAAa,CAAC,EAC7BjH,GAAG,CAAEqJ,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAAC/D,SAAS,GAAGzF,EAAE,CAAC,IAAI,CAACsH,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACFjH,UAAU,CAAEqJ,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACjE,SAAS,GAAGzF,EAAE,CAAC,IAAI,CAACsH,cAAc,CAAC;MACxC,OAAOtH,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACA6J,SAAS,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAAC/C,oBAAoB,CAACgD,WAAW,CAClCR,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAACuH,aAAa,CAAC,CAAC,CACnCyC,SAAS,CAAEG,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAAChJ,cAAc,GAAGgJ,QAAQ,EAAEhJ,cAAc;MAC9C,IAAI,CAACD,eAAe,GAAGiJ,QAAQ;MAC/B,IAAI,CAAC3C,WAAW,GAAG2C,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAAClJ,eAAe,EAAE;QACxB,IAAI,CAACmJ,iBAAiB,CAAC,IAAI,CAACnJ,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEAqI,uBAAuBA,CAACe,MAAc,EAAEC,IAAY;IAClD,IAAI,CAACrD,oBAAoB,CACtBsD,6BAA6B,CAACD,IAAI,CAAC,CACnCP,SAAS,CAAES,GAAQ,IAAI;MACtB,IAAI,CAAC1E,SAAS,CAACuE,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAExK,GAAG,CAAEyK,IAAS,KAAM;QAC7B9C,KAAK,EAAE8C,IAAI,CAACC,WAAW;QACvB9C,KAAK,EAAE6C,IAAI,CAACE;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAjJ,oBAAoBA,CAACkJ,WAAmB,EAAEhD,KAAa;IACrD,MAAM/D,IAAI,GAAG,IAAI,CAACgC,SAAS,CAAC+E,WAAW,CAAC,EAAEC,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAAClD,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAO/D,IAAI,EAAE8D,KAAK,IAAIC,KAAK;EAC7B;EAEQmC,YAAYA,CAAA;IAClB,IAAI,CAACxE,SAAS,GAAGxF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACsH,cAAc,CAAC;IAAE;IACzB,IAAI,CAAC9B,aAAa,CAAC+D,IAAI,CACrBjJ,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACoF,cAAc,GAAG,IAAK,CAAC,EACvCrF,SAAS,CAAE4K,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC/D,oBAAoB,CAACiE,WAAW,CAACD,MAAM,CAAC,CAACxB,IAAI,CACvDxJ,GAAG,CAAEiK,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxC3J,UAAU,CAAEuJ,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAO5J,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFO,QAAQ,CAAC,MAAO,IAAI,CAACgF,cAAc,GAAG,KAAM,CAAC,CAAC;OAC/C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQkE,qBAAqBA,CAACwB,IAAY;IACxC,IAAI,CAACxF,SAAS,GAAG,IAAI,CAACE,aAAa,CAAC4D,IAAI,CACtCnJ,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACuF,cAAc,GAAG,IAAK,CAAC,EACvCxF,SAAS,CAAE4K,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEE,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIH,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAC9D,iBAAiB,CAACkE,kBAAkB,CAACH,MAAM,CAAC,CAACxB,IAAI,CAC3DxJ,GAAG,CAAEiK,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxC7J,GAAG,CAAEgL,QAAe,IAAI;QACtB,IAAI,CAACzF,cAAc,GAAG,KAAK;QAE3B,MAAM0F,YAAY,GAAG,IAAI,CAACnG,uBAAuB,CAACoE,GAAG,CACnD,0BAA0B,CAC3B,EAAE1B,KAAK;QAER;QACA,IACEwD,QAAQ,CAACE,MAAM,GAAG,CAAC,IACnB,CAACD,YAAY,EAAEC,MAAM,IACrB,CAAC,IAAI,CAACpD,eAAe,EACrB;UACA,IAAI,CAAChD,uBAAuB,CAACoE,GAAG,CAC9B,0BAA0B,CAC3B,EAAEiC,QAAQ,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAChI,KAAK,CAAC,CAAC;UAChC,IAAI,CAAC8E,eAAe,GAAG,IAAI,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC,EACF5H,UAAU,CAAEuJ,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAClE,cAAc,GAAG,KAAK;QAC3B,OAAO1F,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA+D,eAAeA,CAACwH,EAAU;IACxB,MAAMC,OAAO,GAAG,IAAI,CAACvG,uBAAuB,CAACoE,GAAG,CAC9C,0BAA0B,CAC3B;IACD,IAAI+B,YAAY,GAAGI,OAAO,EAAE7D,KAAK,IAAI,EAAE;IAEvC,IAAIyD,YAAY,CAACK,QAAQ,CAACF,EAAE,CAAC,EAAE;MAC7BH,YAAY,GAAGA,YAAY,CAACM,MAAM,CAAEC,CAAM,IAAKA,CAAC,KAAKJ,EAAE,CAAC;IAC1D,CAAC,MAAM;MACLH,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAEG,EAAE,CAAC;IACtC;IAEAC,OAAO,EAAEF,QAAQ,CAACF,YAAY,CAAC;EACjC;EAEAlH,UAAUA,CAACqH,EAAU;IACnB,OAAO,IAAI,CAACtG,uBAAuB,CAACoE,GAAG,CACrC,0BAA0B,CAC3B,EAAE1B,KAAK,EAAE8D,QAAQ,CAACF,EAAE,CAAC;EACxB;EAEAtF,QAAQA,CAACM,IAAS;IAChB,IAAI,CAACqB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGzB,IAAI,EAAEqF,UAAU;IAClC,IAAI,CAACrD,QAAQ,CAACsD,UAAU,CAACtF,IAAI,CAAC;EAChC;EAEA2D,iBAAiBA,CAACH,WAAgB;IAChC,IAAI,CAAC+B,mBAAmB,GAAG;MACzB9K,cAAc,EAAE+I,WAAW,EAAE/I,cAAc;MAC3CC,IAAI,EAAE8I,WAAW,EAAE9I,IAAI;MACvBS,UAAU,EAAEqI,WAAW,EAAErI,UAAU;MACnCE,gBAAgB,EAAEmI,WAAW,EAAEnI,gBAAgB;MAC/CK,mBAAmB,EAAE8H,WAAW,EAAE9H,mBAAmB;MACrDd,uBAAuB,EAAE4I,WAAW,EAAE5I,uBAAuB;MAC7DkB,2BAA2B,EAAE0H,WAAW,EAAE1H,2BAA2B;MACrEjB,yBAAyB,EAAE2I,WAAW,EAAE3I,yBAAyB;MACjEgH,iBAAiB,EAAE2B,WAAW,EAAE3B,iBAAiB;MACjDC,wBAAwB,EAAE0B,WAAW,EAAE1B,wBAAwB;MAC/DxG,4BAA4B,EAAEkI,WAAW,EAAElI,4BAA4B;MACvEC,sBAAsB,EAAEiI,WAAW,EAAEjI,sBAAsB;MAC3DiK,kCAAkC,EAChChC,WAAW,EAAEgC,kCAAkC;MACjD5J,qBAAqB,EAAE4H,WAAW,EAAE5H,qBAAqB;MACzDC,mBAAmB,EAAE2H,WAAW,EAAE3H;KACnC;IACD,IAAI,CAACoF,MAAM,GAAGuC,WAAW,CAAC6B,UAAU;IACpC,IAAI,CAAC3G,uBAAuB,CAAC4G,UAAU,CAAC,IAAI,CAACC,mBAAmB,CAAC;EACnE;EAEME,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACnE,aAAa,GAAG,IAAI;MACzBmE,KAAI,CAACrE,WAAW,GAAG,IAAI;MAEvB,IAAIqE,KAAI,CAAC1D,QAAQ,CAAC4D,OAAO,EAAE;QACzBF,KAAI,CAACrE,WAAW,GAAG,IAAI;QACvB;MACF;MAEAqE,KAAI,CAAClE,UAAU,GAAG,IAAI;MACtB,MAAMJ,KAAK,GAAG;QAAE,GAAGsE,KAAI,CAAC1D,QAAQ,CAACZ;MAAK,CAAE;MAExC,MAAM4C,IAAI,GAAG;QACXvJ,cAAc,EAAEiL,KAAI,CAACjL,cAAc;QACnCuF,IAAI,EAAEoB,KAAK,EAAEpB;OACd;MAED,IAAI0F,KAAI,CAACjE,UAAU,EAAE;QACnBiE,KAAI,CAAClF,oBAAoB,CACtBqF,UAAU,CAACH,KAAI,CAACjE,UAAU,EAAEuC,IAAI,CAAC,CACjChB,IAAI,CAAC1J,SAAS,CAACoM,KAAI,CAAC7E,aAAa,CAAC,CAAC,CACnCyC,SAAS,CAAC;UACTwC,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAAClE,UAAU,GAAG,KAAK;YACvBkE,KAAI,CAACrE,WAAW,GAAG,KAAK;YACxBqE,KAAI,CAAC1D,QAAQ,CAAC+D,KAAK,EAAE;YACrBL,KAAI,CAAChF,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF+C,KAAI,CAAClF,oBAAoB,CACtBwF,kBAAkB,CAACN,KAAI,CAACjL,cAAc,CAAC,CACvCuI,IAAI,CAAC1J,SAAS,CAACoM,KAAI,CAAC7E,aAAa,CAAC,CAAC,CACnCyC,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGU,GAAQ,IAAI;YAClB2B,KAAI,CAAClE,UAAU,GAAG,KAAK;YACvBkE,KAAI,CAACrE,WAAW,GAAG,IAAI;YACvBqE,KAAI,CAAChF,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACL+C,KAAI,CAAClF,oBAAoB,CACtByF,UAAU,CAACjC,IAAI,CAAC,CAChBhB,IAAI,CAAC1J,SAAS,CAACoM,KAAI,CAAC7E,aAAa,CAAC,CAAC,CACnCyC,SAAS,CAAC;UACTwC,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAAClE,UAAU,GAAG,KAAK;YACvBkE,KAAI,CAACrE,WAAW,GAAG,KAAK;YACxBqE,KAAI,CAAC1D,QAAQ,CAAC+D,KAAK,EAAE;YACrBL,KAAI,CAAChF,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF+C,KAAI,CAAClF,oBAAoB,CACtBwF,kBAAkB,CAACN,KAAI,CAACjL,cAAc,CAAC,CACvCuI,IAAI,CAAC1J,SAAS,CAACoM,KAAI,CAAC7E,aAAa,CAAC,CAAC,CACnCyC,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGU,GAAQ,IAAI;YAClB2B,KAAI,CAAClE,UAAU,GAAG,KAAK;YACvBkE,KAAI,CAACrE,WAAW,GAAG,IAAI;YACvBqE,KAAI,CAAChF,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEMlE,QAAQA,CAAA;IAAA,IAAAyH,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAAC5J,SAAS,GAAG,IAAI;MAErB,IAAI4J,MAAI,CAACxH,uBAAuB,CAACkH,OAAO,EAAE;QACxC;MACF;MAEAM,MAAI,CAAClF,MAAM,GAAG,IAAI;MAClB,MAAMI,KAAK,GAAG;QAAE,GAAG8E,MAAI,CAACxH,uBAAuB,CAAC0C;MAAK,CAAE;MAEvD,MAAM4C,IAAI,GAAG;QACXtJ,IAAI,EAAE0G,KAAK,EAAE1G,IAAI;QACjBE,uBAAuB,EAAEwG,KAAK,EAAExG,uBAAuB;QACvDC,yBAAyB,EAAEuG,KAAK,EAAEvG,yBAAyB,GACvDqL,MAAI,CAACC,UAAU,CAAC/E,KAAK,CAACvG,yBAAyB,CAAC,GAChD,IAAI;QACRgH,iBAAiB,EAAET,KAAK,EAAES,iBAAiB;QAC3CC,wBAAwB,EAAEV,KAAK,EAAEU,wBAAwB;QACzD3G,UAAU,EAAEiG,KAAK,EAAEjG,UAAU;QAC7BE,gBAAgB,EAAE+F,KAAK,EAAE/F,gBAAgB;QACzCC,4BAA4B,EAAE8F,KAAK,EAAE9F,4BAA4B;QACjEC,sBAAsB,EAAE6F,KAAK,EAAE7F,sBAAsB;QACrDG,mBAAmB,EAAE0F,KAAK,EAAE1F,mBAAmB;QAC/C8J,kCAAkC,EAChCpE,KAAK,EAAEoE,kCAAkC;QAC3C5J,qBAAqB,EAAEwF,KAAK,EAAExF,qBAAqB;QACnDC,mBAAmB,EAAEuF,KAAK,EAAEvF,mBAAmB;QAC/CC,2BAA2B,EAAEsF,KAAK,EAAEtF,2BAA2B,GAC3DoK,MAAI,CAACC,UAAU,CAAC/E,KAAK,CAACtF,2BAA2B,CAAC,GAClD;OACL;MAEDoK,MAAI,CAAC1F,oBAAoB,CACtB4F,iBAAiB,CAACF,MAAI,CAACjF,MAAM,EAAE+C,IAAI,CAAC,CACpChB,IAAI,CAAC1J,SAAS,CAAC4M,MAAI,CAACrF,aAAa,CAAC,CAAC,CACnCyC,SAAS,CAAC;QACT+C,IAAI,EAAG5C,QAAa,IAAI;UACtByC,MAAI,CAACxF,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFuD,MAAI,CAAC1F,oBAAoB,CACtBwF,kBAAkB,CAACE,MAAI,CAACzL,cAAc,CAAC,CACvCuI,IAAI,CAAC1J,SAAS,CAAC4M,MAAI,CAACrF,aAAa,CAAC,CAAC,CACnCyC,SAAS,EAAE;UACd4C,MAAI,CAAChF,UAAU,GAAG,KAAK;QACzB,CAAC;QACDmC,KAAK,EAAGU,GAAQ,IAAI;UAClBmC,MAAI,CAAClF,MAAM,GAAG,KAAK;UACnBkF,MAAI,CAACxF,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAwD,UAAUA,CAACG,IAAS;IAClB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,CAAC,GAAGD,IAAI,YAAYE,IAAI,GAAGF,IAAI,GAAG,IAAIE,IAAI,CAACF,IAAI,CAAC;IACtD,IAAIG,KAAK,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGJ,CAAC,CAACK,WAAW,EAAE;IAC5B,MAAMC,EAAE,GAAGC,MAAM,CAACP,CAAC,CAACQ,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMC,EAAE,GAAGH,MAAM,CAACP,CAAC,CAACW,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/C,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEAnH,aAAaA,CAACzC,IAAS;IACrB,IAAI,CAACsD,mBAAmB,CAACwG,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACnK,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAmK,MAAMA,CAACnK,IAAS;IACd,IAAI,CAACmD,oBAAoB,CACtBiH,UAAU,CAACpK,IAAI,CAACgI,UAAU,CAAC,CAC3BrC,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAACuH,aAAa,CAAC,CAAC,CACnCyC,SAAS,CAAC;MACT+C,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC3F,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACnC,oBAAoB,CACtBwF,kBAAkB,CAAC,IAAI,CAACvL,cAAc,CAAC,CACvCuI,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAACuH,aAAa,CAAC,CAAC,CACnCyC,SAAS,EAAE;MAChB,CAAC;MACDD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3C,cAAc,CAAC+B,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA5C,SAASA,CAAC2H,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAC5G,YAAY,GAAG4G,QAAQ;IAC5B,IAAI,CAAC7G,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACS,QAAQ,CAAC+D,KAAK,EAAE;EACvB;EAEA,IAAI3F,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC4B,QAAQ,CAACmG,QAAQ;EAC/B;EAEA,IAAI5L,CAACA,CAAA;IACH,OAAO,IAAI,CAACmC,uBAAuB,CAACyJ,QAAQ;EAC9C;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAClH,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAmH,OAAOA,CAAA;IACL,IAAI,CAAC/L,SAAS,GAAG,KAAK;IACtB,IAAI,CAACoC,uBAAuB,CAACqH,KAAK,EAAE;EACtC;EAEAuC,WAAWA,CAAA;IACT,IAAI,CAACzH,aAAa,CAACwF,IAAI,EAAE;IACzB,IAAI,CAACxF,aAAa,CAACiF,QAAQ,EAAE;EAC/B;;;uBAheWzF,8BAA8B,EAAApG,EAAA,CAAAsO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxO,EAAA,CAAAsO,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAA1O,EAAA,CAAAsO,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA5O,EAAA,CAAAsO,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9O,EAAA,CAAAsO,iBAAA,CAAAO,EAAA,CAAAE,mBAAA,GAAA/O,EAAA,CAAAsO,iBAAA,CAAAU,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9B7I,8BAA8B;MAAA8I,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBnCxP,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,kBACqG;UAAzBD,EAAA,CAAAgD,UAAA,mBAAA0M,kEAAA;YAAA,OAASD,GAAA,CAAAtB,UAAA,EAAY;UAAA,EAAC;UACtGnO,EAFI,CAAAG,YAAA,EACqG,EACnG;UAmNNH,EAlNA,CAAAkC,UAAA,IAAAyN,6CAAA,oBAA6D,IAAAC,8CAAA,qBAkNE;UAiOnE5P,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAA4D,aAC2B,YAChC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,mBAC2D;UADrCD,EAAA,CAAAgD,UAAA,mBAAA6M,mEAAA;YAAA,OAASJ,GAAA,CAAAzB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAEvDhO,EAFI,CAAAG,YAAA,EAC2D,EACzD;UAGFH,EADJ,CAAAC,cAAA,cAAuB,mBAEW;UAkD1BD,EAhDA,CAAAkC,UAAA,KAAA4N,sDAAA,2BAAgC,KAAAC,sDAAA,2BAwBQ,KAAAC,sDAAA,0BAmBF,KAAAC,sDAAA,0BAKD;UAOjDjQ,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAAkQ,gBAAA,2BAAAC,2EAAAxK,MAAA;YAAA3F,EAAA,CAAAoQ,kBAAA,CAAAX,GAAA,CAAArI,WAAA,EAAAzB,MAAA,MAAA8J,GAAA,CAAArI,WAAA,GAAAzB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9C3F,EAAA,CAAAkC,UAAA,KAAAmO,sDAAA,0BAAgC;UAMxBrQ,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAA8D,SAAA,oBAC0E;UAC1E9D,EAAA,CAAAkC,UAAA,KAAAoO,8CAAA,kBACmE;UAI3EtQ,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAAgD,UAAA,mBAAAuN,iEAAA;YAAA,OAAAd,GAAA,CAAArI,WAAA,GAAuB,KAAK;UAAA,EAAC;UAACpH,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAAgD,UAAA,mBAAAwN,iEAAA;YAAA,OAASf,GAAA,CAAAjE,YAAA,EAAc;UAAA,EAAC;UAIxCxL,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UAphBOH,EAAA,CAAAI,SAAA,GAAuC;UACXJ,EAD5B,CAAAoC,UAAA,UAAAqN,GAAA,CAAAxI,UAAA,oBAAuC,UAAAwI,GAAA,CAAAxI,UAAA,uBAAyC,kBACrE,sCAAsD;UAEzEjH,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAoC,UAAA,UAAAqN,GAAA,CAAAxI,UAAA,CAAiB;UAkNhBjH,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAoC,UAAA,SAAAqN,GAAA,CAAAxI,UAAA,CAAgB;UAsOfjH,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAoC,UAAA,oCAAmC,iBAAiB;UAI/CpC,EAAA,CAAAI,SAAA,GAAqB;UAAwCJ,EAA7D,CAAAoC,UAAA,UAAAqN,GAAA,CAAA5I,WAAA,CAAqB,YAAyB,mBAAiC;UA2D7C7G,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAyQ,UAAA,CAAAzQ,EAAA,CAAA0Q,eAAA,KAAAC,GAAA,EAA4B;UAArE3Q,EAAA,CAAAoC,UAAA,eAAc;UAACpC,EAAA,CAAA4Q,gBAAA,YAAAnB,GAAA,CAAArI,WAAA,CAAyB;UAAmDpH,EAArB,CAAAoC,UAAA,qBAAoB,oBAAoB;UAM9GpC,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAoC,UAAA,cAAAqN,GAAA,CAAA1H,QAAA,CAAsB;UAGkD/H,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAyQ,UAAA,CAAAzQ,EAAA,CAAA0Q,eAAA,KAAAG,GAAA,EAA6B;UAC3F7Q,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA0E,eAAA,KAAAC,GAAA,EAAA8K,GAAA,CAAAnI,aAAA,IAAAmI,GAAA,CAAAtJ,KAAA,SAAA5D,MAAA,EAAmE;UACjEvC,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAoC,UAAA,SAAAqN,GAAA,CAAAnI,aAAA,IAAAmI,GAAA,CAAAtJ,KAAA,SAAA5D,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}