{"ast": null, "code": "import { MessageService } from 'primeng/api';\nimport { stringify } from \"qs\";\nimport { map, of, switchMap } from 'rxjs';\nimport { Country, State, City } from 'country-state-city';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../account/account.service\";\nimport * as i5 from \"../services/service-ticket.service\";\nimport * as i6 from \"../services/ticket-storage.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"120px\"\n});\nfunction IdentifyAccountComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22)(3, \"h5\", 23)(4, \"i\", 9);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.first_name, \" \", ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.last_name, \" \");\n  }\n}\nfunction IdentifyAccountComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"form\", 25)(2, \"div\", 26)(3, \"div\", 20)(4, \"div\", 27)(5, \"div\", 28)(6, \"label\", 29)(7, \"span\", 30);\n    i0.ɵɵtext(8, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Account Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 27)(12, \"div\", 28)(13, \"label\", 29)(14, \"span\", 30);\n    i0.ɵɵtext(15, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Account ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 27)(19, \"div\", 28)(20, \"label\", 29)(21, \"span\", 30);\n    i0.ɵɵtext(22, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Street \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Country \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"p-dropdown\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 35)(33, \"div\", 28)(34, \"label\", 29)(35, \"span\", 30);\n    i0.ɵɵtext(36, \"my_location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" State \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"p-dropdown\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 35)(40, \"div\", 28)(41, \"label\", 29)(42, \"span\", 30);\n    i0.ɵɵtext(43, \"map\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44, \" City \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"p-dropdown\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 35)(47, \"div\", 28)(48, \"label\", 29)(49, \"span\", 30);\n    i0.ɵɵtext(50, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \" Zip Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 35)(54, \"div\", 28)(55, \"label\", 29)(56, \"span\", 30);\n    i0.ɵɵtext(57, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 35)(61, \"div\", 28)(62, \"label\", 29)(63, \"span\", 30);\n    i0.ɵɵtext(64, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \" Telephone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"input\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 35)(68, \"div\", 28)(69, \"label\", 29)(70, \"span\", 30);\n    i0.ɵɵtext(71, \"pin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \" Invoice # \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"input\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 35)(75, \"div\", 28)(76, \"label\", 42)(77, \"span\", 30);\n    i0.ɵɵtext(78, \"pin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" Order # \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(80, \"input\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 35)(82, \"div\", 28)(83, \"label\", 42)(84, \"span\", 30);\n    i0.ɵɵtext(85, \"pin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(86, \" Credit Memo # \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"input\", 44);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(88, \"div\", 45)(89, \"div\", 13)(90, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_90_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.search());\n    });\n    i0.ɵɵelementStart(91, \"i\", 16);\n    i0.ɵɵtext(92, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_94_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.clear());\n    });\n    i0.ɵɵelementStart(95, \"i\", 16);\n    i0.ɵɵtext(96, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_98_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.reset());\n    });\n    i0.ɵɵelementStart(99, \"i\", 16);\n    i0.ɵɵtext(100, \"rule_settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(101, \" Reset \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.filterForm);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"options\", ctx_r0.countries);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.states);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.cities);\n    i0.ɵɵadvance(45);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loading ? \"Searching...\" : \"Search\", \" \");\n  }\n}\nfunction IdentifyAccountComponent_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2, \"Rows per page: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-dropdown\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function IdentifyAccountComponent_ng_container_18_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.mainTableRows, $event) || (ctx_r0.mainTableRows = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function IdentifyAccountComponent_ng_container_18_Template_p_dropdown_onChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c0));\n    i0.ɵɵproperty(\"options\", ctx_r0.pageSizeOptions);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.mainTableRows);\n  }\n}\nfunction IdentifyAccountComponent_p_multiSelect_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-multiSelect\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function IdentifyAccountComponent_p_multiSelect_23_Template_p_multiSelect_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedColumns, $event) || (ctx_r0.selectedColumns = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"options\", ctx_r0.cols);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedColumns);\n    i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n  }\n}\nfunction IdentifyAccountComponent_p_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No records found.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 63);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_Template_th_click_1_listener() {\n      const col_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r8.field, ctx_r0.data));\n    });\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_i_4_Template, 1, 1, \"i\", 58)(5, IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_i_5_Template, 1, 0, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r8.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r8.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField === col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField !== col_r8.field);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 55);\n    i0.ɵɵelementStart(2, \"th\", 56);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_p_table_26_ng_template_1_Template_th_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(\"bp_full_name\", ctx_r0.data));\n    });\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵtext(4, \" Account Name \");\n    i0.ɵɵtemplate(5, IdentifyAccountComponent_p_table_26_ng_template_1_i_5_Template, 1, 1, \"i\", 58)(6, IdentifyAccountComponent_p_table_26_ng_template_1_i_6_Template, 1, 0, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, IdentifyAccountComponent_p_table_26_ng_template_1_ng_container_7_Template, 6, 4, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField === \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField !== \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedColumns);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_2_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r9 = ctx.$implicit;\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r10[col_r9.field], \" \");\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 55);\n    i0.ɵɵelement(2, \"button\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 65)(4, \"span\", 66)(5, \"span\", 67);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, IdentifyAccountComponent_p_table_26_ng_template_2_ng_container_9_Template, 3, 1, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const expanded_r11 = ctx.expanded;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", item_r10)(\"icon\", expanded_r11 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\")(\"disabled\", !item_r10.contacts || !item_r10.contacts.length);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getInitials(item_r10.bp_full_name), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r10.bp_full_name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedColumns);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 75);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_Template_th_click_1_listener() {\n      const col_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const item_r15 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r14.field, item_r15.contacts));\n    });\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_i_4_Template, 1, 1, \"i\", 58)(5, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_i_5_Template, 1, 0, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r14 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", col_r14.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField === col_r14.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortField !== col_r14.field);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 74);\n    i0.ɵɵtemplate(2, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_ng_container_2_Template, 6, 3, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedBottomColumns);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r18 = ctx.$implicit;\n    const tableinfo_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r17[col_r18.field]);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 74)(2, \"input\", 76);\n    i0.ɵɵlistener(\"change\", function IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_6_Template_input_change_2_listener() {\n      const tableinfo_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.selectedContact = tableinfo_r17);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(3, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_6_ng_container_3_Template, 3, 1, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r17 = ctx.$implicit;\n    const item_r15 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"contactSelection\", item_r15.bp_id, \"\");\n    i0.ɵɵproperty(\"value\", tableinfo_r17)(\"checked\", (ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.bp_id) === tableinfo_r17.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedBottomColumns);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 13)(3, \"p-multiSelect\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function IdentifyAccountComponent_p_table_26_ng_template_3_div_2_Template_p_multiSelect_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedBottomColumns, $event) || (ctx_r0.selectedBottomColumns = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"p-table\", 73);\n    i0.ɵɵlistener(\"onColReorder\", function IdentifyAccountComponent_p_table_26_ng_template_3_div_2_Template_p_table_onColReorder_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onContactColumnReorder($event));\n    });\n    i0.ɵɵtemplate(5, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_5_Template, 3, 1, \"ng-template\", 52)(6, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_ng_template_6_Template, 4, 5, \"ng-template\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r0.contactCols);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedBottomColumns);\n    i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", item_r15.contacts)(\"rows\", 5)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 69);\n    i0.ɵɵtemplate(2, IdentifyAccountComponent_p_table_26_ng_template_3_div_2_Template, 7, 8, \"div\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r15.contacts && item_r15.contacts.length);\n  }\n}\nfunction IdentifyAccountComponent_p_table_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 51);\n    i0.ɵɵlistener(\"onColReorder\", function IdentifyAccountComponent_p_table_26_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onOtherTableColumnReorder($event));\n    });\n    i0.ɵɵtemplate(1, IdentifyAccountComponent_p_table_26_ng_template_1_Template, 8, 3, \"ng-template\", 52)(2, IdentifyAccountComponent_p_table_26_ng_template_2_Template, 10, 6, \"ng-template\", 53)(3, IdentifyAccountComponent_p_table_26_ng_template_3_Template, 3, 1, \"ng-template\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r0.data)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n  }\n}\nexport let IdentifyAccountComponent = /*#__PURE__*/(() => {\n  class IdentifyAccountComponent {\n    onPageSizeChange(event) {\n      // Optionally handle page size change logic here\n      // For PrimeNG, just updating mainTableRows is enough\n    }\n    constructor(renderer, messageservice, router, fb, service, ticketService, ticketStorageService) {\n      this.renderer = renderer;\n      this.messageservice = messageservice;\n      this.router = router;\n      this.fb = fb;\n      this.service = service;\n      this.ticketService = ticketService;\n      this.ticketStorageService = ticketStorageService;\n      this.bodyClass = 'identify-account-body';\n      this.countries = (() => {\n        const allCountries = Country.getAllCountries();\n        const usaIndex = allCountries.findIndex(c => c.isoCode === 'US');\n        const canadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\n        const usa = usaIndex !== -1 ? allCountries.splice(usaIndex, 1)[0] : null;\n        // After removing USA, Canada index may shift if it was after USA\n        const newCanadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\n        const canada = newCanadaIndex !== -1 ? allCountries.splice(newCanadaIndex, 1)[0] : null;\n        const result = [];\n        if (usa) result.push(usa);\n        if (canada) result.push(canada);\n        return result.concat(allCountries);\n      })();\n      this.states = [];\n      this.cities = [];\n      this.items = [{\n        label: 'Identify Account',\n        routerLink: ['/store/identify-account']\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: ['/']\n      };\n      this.filterForm = this.fb.group({\n        bp_id: [''],\n        bp_name: [''],\n        credit_memo_no: [''],\n        street: [''],\n        city: [''],\n        state: [''],\n        zip_code: [''],\n        country: [''],\n        email: [''],\n        phone: [''],\n        invoice_no: [''],\n        order_no: ['']\n      });\n      // Track which of the three fields is active\n      this.creditInvoiceOrderDisabled = {\n        credit_memo_no: false,\n        invoice_no: false,\n        order_no: false\n      };\n      this.data = [];\n      this.loading = false;\n      this.selectedContact = null;\n      this.mainTableRows = 20;\n      this.pageSizeOptions = [{\n        label: '20',\n        value: 20\n      }, {\n        label: '50',\n        value: 50\n      }, {\n        label: '100',\n        value: 100\n      }];\n      this.expandedRows = {};\n      this._selectedColumns = [];\n      this._selectedBottomColumns = [];\n      this.cols = [{\n        field: 'bp_id',\n        header: 'Account Id'\n      }, {\n        field: 'email',\n        header: 'Email'\n      }, {\n        field: 'phoneNo',\n        header: 'Phone'\n      }, {\n        field: 'address',\n        header: 'Address'\n      }];\n      this.contactCols = [{\n        field: 'bp_id',\n        header: 'ID #'\n      }, {\n        field: 'first_name',\n        header: 'First name'\n      }, {\n        field: 'last_name',\n        header: 'Last name'\n      }, {\n        field: 'email',\n        header: 'Email Id'\n      }, {\n        field: 'phoneNo',\n        header: 'Phone no'\n      }, {\n        field: 'status',\n        header: 'Status'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n      this.checked = false;\n      this.showDiv = false;\n      this.creatingTicket = false;\n      this.filterForm.get('country')?.valueChanges.subscribe(countryCode => {\n        if (countryCode) {\n          this.states = State.getStatesOfCountry(countryCode);\n        } else {\n          this.states = [];\n        }\n        this.filterForm.get('state')?.setValue('');\n        this.cities = [];\n        this.filterForm.get('city')?.setValue('');\n      });\n      this.filterForm.get('state')?.valueChanges.subscribe(stateCode => {\n        const countryCode = this.filterForm.get('country')?.value;\n        if (countryCode && stateCode) {\n          this.cities = City.getCitiesOfState(countryCode, stateCode);\n        } else {\n          this.cities = [];\n        }\n        this.filterForm.get('city')?.setValue('');\n      });\n      // Add value change handlers for credit_memo_no, invoice_no, order_no\n      ['credit_memo_no', 'invoice_no', 'order_no'].forEach(field => {\n        this.filterForm.get(field)?.valueChanges.subscribe(val => {\n          this.handleCreditInvoiceOrderChange(field, val ?? '');\n        });\n      });\n    }\n    ngOnInit() {\n      this.renderer.addClass(document.body, this.bodyClass);\n      this.filterForm.get('country')?.valueChanges.subscribe(countryCode => {\n        if (countryCode) {\n          this.states = State.getStatesOfCountry(countryCode);\n        } else {\n          this.states = [];\n        }\n        this.filterForm.get('state')?.setValue('');\n      });\n      this._selectedColumns = this.cols;\n      this._selectedBottomColumns = this.contactCols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    get selectedBottomColumns() {\n      return this._selectedBottomColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    set selectedBottomColumns(val) {\n      this._selectedBottomColumns = this.contactCols.filter(col => val.includes(col));\n    }\n    onOtherTableColumnReorder(event) {\n      const draggedCol = this.cols[event.dragIndex];\n      this.cols.splice(event.dragIndex, 1);\n      this.cols.splice(event.dropIndex, 0, draggedCol);\n    }\n    onContactColumnReorder(event) {\n      const draggedCol = this.contactCols[event.dragIndex];\n      this.contactCols.splice(event.dragIndex, 1);\n      this.contactCols.splice(event.dropIndex, 0, draggedCol);\n    }\n    // Sorting logic\n    customSort(field, targetArray) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      targetArray.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Helper for nested object sorting\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      return field.indexOf('.') === -1 ? data[field] : field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n    search() {\n      this.loading = true;\n      this.data = [];\n      this.selectedContact = null;\n      const formValues = this.filterForm.value;\n      if (formValues.credit_memo_no || formValues.invoice_no || formValues.order_no) {\n        this.service.getAccountDetailsByCreditInvoiceOrder({\n          SalesOrder: formValues.order_no,\n          Invoice: formValues.invoice_no,\n          CreditMemo: formValues.credit_memo_no\n        }).pipe(map(res => {\n          return res.SOLDTOPARTY ? [res.SOLDTOPARTY] : [];\n        }), switchMap(bpIds => {\n          if (!bpIds.length) {\n            return of([]);\n          }\n          const params = stringify({\n            filters: {\n              $and: [{\n                bp_id: {\n                  $in: bpIds\n                }\n              }]\n            },\n            populate: {\n              address_usages: {\n                fields: ['address_usage'],\n                populate: {\n                  business_partner_address: {\n                    fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\n                    populate: {\n                      emails: {\n                        fields: ['email_address']\n                      },\n                      phone_numbers: {\n                        fields: ['phone_number']\n                      }\n                    }\n                  }\n                }\n              },\n              contact_companies: {\n                fields: ['bp_company_id'],\n                populate: {\n                  business_partner_person: {\n                    fields: ['bp_id', 'first_name', 'last_name', 'is_marked_for_archiving'],\n                    populate: {\n                      addresses: {\n                        fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\n                        populate: {\n                          emails: {\n                            fields: ['email_address']\n                          },\n                          phone_numbers: {\n                            fields: ['phone_number']\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          });\n          return this.service.search(params);\n        })).subscribe(res => {\n          this.data = this.formatData(res);\n          this.loading = false;\n        }, () => {\n          this.loading = false;\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        });\n        return;\n      }\n      const obj = {\n        populate: ['roles'],\n        filters: {\n          $and: [{\n            roles: {\n              bp_role: {\n                $in: ['FLCU00', 'FLCU01', 'BUP001']\n              }\n            }\n          }]\n        }\n      };\n      if (formValues.bp_id) {\n        obj.filters.$and.push({\n          'bp_id': {\n            $eqi: formValues.bp_id || ''\n          }\n        });\n      }\n      if (formValues.bp_name) {\n        obj.filters.$and.push({\n          'bp_full_name': {\n            $containsi: formValues.bp_name || ''\n          }\n        });\n      }\n      if (formValues.street) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              street_name: {\n                $containsi: formValues.street || ''\n              }\n            }\n          }\n        });\n      }\n      if (formValues.city) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              city_name: {\n                $containsi: formValues.city || ''\n              }\n            }\n          }\n        });\n      }\n      if (formValues.state) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              region: {\n                $containsi: formValues.state || ''\n              }\n            }\n          }\n        });\n      }\n      if (formValues.zip_code) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              postal_code: {\n                $containsi: formValues.zip_code || ''\n              }\n            }\n          }\n        });\n      }\n      if (formValues.country) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              country: {\n                $containsi: formValues.country || ''\n              }\n            }\n          }\n        });\n      }\n      if (formValues.email) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              emails: {\n                email_address: {\n                  $containsi: formValues.email || ''\n                }\n              }\n            }\n          }\n        });\n      }\n      if (formValues.phone) {\n        obj.filters.$and.push({\n          'address_usages': {\n            business_partner_address: {\n              phone_numbers: {\n                phone_number: {\n                  $containsi: formValues.phone || ''\n                }\n              }\n            }\n          }\n        });\n      }\n      const params = stringify(obj);\n      this.service.search(params).pipe(switchMap(res => {\n        if (res?.length) {\n          const bpIds = [];\n          const contactBPIs = [];\n          for (let i = 0; i < res.length; i++) {\n            const bp = res[i];\n            const contactRole = bp.roles.find(role => role.bp_role == 'BUP001');\n            if (contactRole) {\n              contactBPIs.push(bp.bp_id);\n            } else {\n              bpIds.push(bp.bp_id);\n            }\n          }\n          if (!contactBPIs.length) {\n            return of(bpIds);\n          }\n          const params = stringify({\n            filters: {\n              $and: [{\n                bp_person_id: {\n                  $in: contactBPIs\n                }\n              }]\n            }\n          });\n          return this.service.getAccountDetailsByContact(params).pipe(map(contactDetails => {\n            if (!contactDetails?.length) {\n              return bpIds;\n            }\n            for (let index = 0; index < contactDetails.length; index++) {\n              const element = contactDetails[index];\n              if (!bpIds.includes(element.bp_company_id)) {\n                bpIds.push(element.bp_company_id);\n              }\n            }\n            return bpIds;\n          }));\n        } else {\n          return of([]);\n        }\n      }), switchMap(bpIds => {\n        if (!bpIds.length) {\n          return of([]);\n        }\n        const params = stringify({\n          filters: {\n            $and: [{\n              bp_id: {\n                $in: bpIds\n              }\n            }]\n          },\n          populate: {\n            address_usages: {\n              fields: ['address_usage'],\n              populate: {\n                business_partner_address: {\n                  fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\n                  populate: {\n                    emails: {\n                      fields: ['email_address']\n                    },\n                    phone_numbers: {\n                      fields: ['phone_number']\n                    }\n                  }\n                }\n              }\n            },\n            contact_companies: {\n              fields: ['bp_company_id'],\n              populate: {\n                business_partner_person: {\n                  fields: ['bp_id', 'first_name', 'last_name', 'is_marked_for_archiving'],\n                  populate: {\n                    addresses: {\n                      fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\n                      populate: {\n                        emails: {\n                          fields: ['email_address']\n                        },\n                        phone_numbers: {\n                          fields: ['phone_number']\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        });\n        return this.service.search(params);\n      })).subscribe(res => {\n        this.data = this.formatData(res);\n        this.loading = false;\n      }, () => {\n        this.loading = false;\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      });\n    }\n    handleCreditInvoiceOrderChange(changedField, value) {\n      const fields = ['credit_memo_no', 'invoice_no', 'order_no'];\n      if (value && value.trim() !== '') {\n        fields.forEach(f => {\n          this.creditInvoiceOrderDisabled[f] = f !== changedField;\n          if (f !== changedField) {\n            this.filterForm.get(f)?.disable({\n              emitEvent: false\n            });\n          }\n        });\n      } else {\n        // If all three are empty, enable all\n        const anyFilled = fields.some(f => {\n          const v = this.filterForm.get(f)?.value;\n          return v && v.trim() !== '';\n        });\n        if (!anyFilled) {\n          fields.forEach(f => {\n            this.creditInvoiceOrderDisabled[f] = false;\n            this.filterForm.get(f)?.enable({\n              emitEvent: false\n            });\n          });\n        }\n      }\n    }\n    getContactDetails(addresses) {\n      if (!addresses?.length || !addresses[0].business_partner_address) {\n        return {\n          address: '',\n          phoneNo: '',\n          email: ''\n        };\n      }\n      const address = addresses[0].business_partner_address;\n      if (!address) {\n        return {\n          address: '',\n          phoneNo: '',\n          email: ''\n        };\n      }\n      return this.getAddress(address);\n    }\n    getAddress(address) {\n      if (!address) {\n        return {\n          address: '',\n          phoneNo: '',\n          email: ''\n        };\n      }\n      return {\n        address: `${address.street_name || ''}, ${address.postal_code || ''}, ${address.city_name || ''}, ${address.region || ''}, ${address.country || ''}`,\n        phoneNo: address?.phone_numbers?.length ? address.phone_numbers[0].phone_number : '',\n        email: address?.emails?.length ? address.emails[0].email_address : ''\n      };\n    }\n    getContact(contacts, companyName) {\n      const data = [];\n      for (let i = 0; i < contacts.length; i++) {\n        const contact = contacts[i];\n        if (contact.business_partner_person) {\n          const person = contact.business_partner_person;\n          if (person.is_marked_for_archiving === false) {\n            data.push({\n              bp_id: person.bp_id,\n              bp_company_id: contact.bp_company_id,\n              bp_compny_name: companyName,\n              first_name: person.first_name || '',\n              last_name: person.last_name || '',\n              status: 'ACTIVE',\n              ...this.getAddress(person.addresses[0])\n            });\n          }\n        }\n      }\n      return data;\n    }\n    formatData(data) {\n      return data.map(item => {\n        return {\n          bp_id: item.bp_id,\n          bp_full_name: item.bp_full_name,\n          ...this.getContactDetails(item.address_usages),\n          contacts: this.getContact(item.contact_companies || [], item.bp_full_name)\n        };\n      });\n    }\n    clear() {\n      this.filterForm.reset();\n      // Enable all three fields on clear\n      ['credit_memo_no', 'invoice_no', 'order_no'].forEach(f => {\n        this.creditInvoiceOrderDisabled[f] = false;\n        this.filterForm.get(f)?.enable({\n          emitEvent: false\n        });\n      });\n    }\n    reset() {\n      this.data = [];\n      this.selectedContact = null;\n    }\n    getInitials(name) {\n      return name.trim().split(/\\s+/) // split by spaces\n      .slice(0, 2) // only take first two words\n      .map(word => word[0].toUpperCase()).join('');\n    }\n    toggleDiv() {\n      this.showDiv = !this.showDiv;\n    }\n    createTicket() {\n      if (!this.selectedContact) {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Please select a contact.'\n        });\n        return;\n      }\n      const data = {\n        account_id: this.selectedContact.bp_company_id,\n        contact_id: this.selectedContact.bp_id,\n        status_id: 'IN_PROGRESS',\n        subject: `${this.selectedContact.bp_compny_name} (${this.selectedContact.bp_company_id}) - ${this.selectedContact.first_name} ${this.selectedContact.last_name} (${this.selectedContact.bp_id})`\n      };\n      this.creatingTicket = true;\n      this.ticketService.createTicket({\n        data\n      }).subscribe(response => {\n        this.creatingTicket = false;\n        if (response?.data?.documentId) {\n          // Save invoice_no, order_no, and credit_memo_no to local storage using ticket ID as key\n          this.saveTicketFormDataToLocalStorage(response?.data?.id);\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Ticket created successfully.'\n          });\n          const params = stringify({\n            filters: {\n              $and: [{\n                bp_id: {\n                  $in: [this.selectedContact.bp_company_id]\n                }\n              }]\n            }\n          });\n          this.service.search(params).subscribe(res => {\n            if (res?.length) {\n              this.router.navigate(['/store/service-ticket-details', response?.data?.id, res[0].documentId]);\n            }\n          });\n        }\n      }, () => {\n        this.creatingTicket = false;\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while creating the ticket.'\n        });\n      });\n    }\n    /**\n     * Save ticket form data to local storage using ticket ID as key\n     * @param ticketId - The unique ticket ID to use as storage key\n     */\n    saveTicketFormDataToLocalStorage(ticketId) {\n      if (!ticketId) return;\n      const formValues = this.filterForm.value;\n      this.ticketStorageService.saveTicketFormData(ticketId, {\n        invoice_no: formValues.invoice_no || '',\n        order_no: formValues.order_no || '',\n        credit_memo_no: formValues.credit_memo_no || ''\n      });\n    }\n    /**\n     * Retrieve ticket form data from local storage by ticket ID\n     * @param ticketId - The unique ticket ID used as storage key\n     * @returns The stored ticket form data or null if not found\n     */\n    getTicketFormDataFromLocalStorage(ticketId) {\n      return this.ticketStorageService.getTicketFormData(ticketId);\n    }\n    /**\n     * Clear ticket form data from local storage by ticket ID\n     * @param ticketId - The unique ticket ID used as storage key\n     */\n    clearTicketFormDataFromLocalStorage(ticketId) {\n      this.ticketStorageService.clearTicketFormData(ticketId);\n    }\n    static {\n      this.ɵfac = function IdentifyAccountComponent_Factory(t) {\n        return new (t || IdentifyAccountComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AccountService), i0.ɵɵdirectiveInject(i5.ServiceTicketService), i0.ɵɵdirectiveInject(i6.TicketStorageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: IdentifyAccountComponent,\n        selectors: [[\"app-identify-account\"]],\n        features: [i0.ɵɵProvidersFeature([MessageService])],\n        decls: 27,\n        vars: 12,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"mt-3\", \"flex\", \"flex-column\", \"gap-3\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [\"class\", \"grid mt-0\", 4, \"ngIf\"], [1, \"account-sec\", \"w-full\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-4\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\", \"mt-3\"], [1, \"acc-title\", \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"header-title\", \"m-0\", \"pl-3\", \"relative\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"mt-3 border-none border-top-1 border-solid border-gray-50\", 4, \"ngIf\"], [1, \"search-result\", \"mt-3\", \"w-full\"], [1, \"acc-title\", \"filter-sec\", \"mb-3\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-gray-50\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-green-100\", \"border-none\", \"text-green-600\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\", \"disabled\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", \"class\", \"table-multiselect-dropdown\", 3, \"options\", \"ngModel\", \"styleClass\", \"ngModelChange\", 4, \"ngIf\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\", \"onColReorder\", 4, \"ngIf\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\"], [1, \"identify-name-box\", \"px-3\", \"flex\", \"align-items-center\", \"w-full\", \"h-4rem\", \"surface-b\", \"border-round\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"gap-2\", \"text-lg\", \"font-semibold\", \"text-primary\"], [1, \"mt-3\", \"border-none\", \"border-top-1\", \"border-solid\", \"border-gray-50\"], [1, \"account-p-tabs\", \"relative\", 3, \"formGroup\"], [1, \"acc-tab-info\", \"pb-2\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-1\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_name\", \"placeholder\", \"Account Name\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_id\", \"placeholder\", \"CRM ID\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"street\", \"placeholder\", \"Street\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"formControlName\", \"country\", \"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"dataKey\", \"isoCode\", \"placeholder\", \"Select Country\", \"styleClass\", \"h-2-8rem w-full\", 3, \"options\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\", \"pt-0\"], [\"formControlName\", \"state\", \"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"dataKey\", \"isoCode\", \"styleClass\", \"h-2-8rem w-full\", \"placeholder\", \"Select State\", 3, \"options\"], [\"formControlName\", \"city\", \"optionLabel\", \"name\", \"optionValue\", \"name\", \"dataKey\", \"name\", \"styleClass\", \"h-2-8rem w-full\", \"placeholder\", \"Select City\", 3, \"options\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"zip_code\", \"placeholder\", \"Zip Code\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"phone\", \"placeholder\", \"Telephone\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"invoice_no\", \"placeholder\", \"Invoice #\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-1\", \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"order_no\", \"placeholder\", \"Order #\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"formControlName\", \"credit_memo_no\", \"placeholder\", \"Credit Memo #\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [1, \"acc-title\", \"flex\", \"align-items-center\", \"justify-content-between\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-red-100\", \"border-none\", \"text-red-500\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\"], [\"styleClass\", \"h-2-8rem\", \"placeholder\", \"Rows per page\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 2, \"min-width\", \"3rem\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [\"type\", \"button\", \"pButton\", \"\", \"pRowToggler\", \"\", 1, \"p-button-text\", \"p-button-plain\", \"p-button-sm\", 3, \"pRowToggler\", \"icon\", \"disabled\"], [\"pFrozenColumn\", \"\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-2rem\", \"h-2rem\", \"bg-blue-500\", \"border-circle\", \"font-semibold\", \"text-white\"], [1, \"m-0\", \"text-base\", \"text-900\", \"w-20rem\", \"text-overflow-ellipsis\"], [\"colspan\", \"6\", 1, \"border-round-right-lg\"], [\"class\", \"py-3\", 4, \"ngIf\"], [1, \"py-3\"], [1, \"filter-sec\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-end\"], [\"dataKey\", \"id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [1, \"border-round-left-lg\", 2, \"min-width\", \"2rem\", \"width\", \"2rem\"], [\"pReorderableColumn\", \"\", 3, \"click\"], [\"type\", \"radio\", 1, \"custom-ratio-btn\", 3, \"change\", \"name\", \"value\", \"checked\"]],\n        template: function IdentifyAccountComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, IdentifyAccountComponent_div_4_Template, 7, 2, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n            i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_div_click_6_listener() {\n              return ctx.toggleDiv();\n            });\n            i0.ɵɵelementStart(7, \"h5\", 7);\n            i0.ɵɵtext(8, \"Account\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"button\", 8)(10, \"span\", 9);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(12, IdentifyAccountComponent_div_12_Template, 102, 6, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"h5\", 7);\n            i0.ɵɵtext(16, \"Result List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵtemplate(18, IdentifyAccountComponent_ng_container_18_Template, 4, 5, \"ng-container\", 14);\n            i0.ɵɵelementStart(19, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_button_click_19_listener() {\n              return ctx.createTicket();\n            });\n            i0.ɵɵelementStart(20, \"i\", 16);\n            i0.ɵɵtext(21, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, IdentifyAccountComponent_p_multiSelect_23_Template, 1, 3, \"p-multiSelect\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(24, IdentifyAccountComponent_p_24_Template, 2, 0, \"p\", 14);\n            i0.ɵɵelementStart(25, \"div\", 18);\n            i0.ɵɵtemplate(26, IdentifyAccountComponent_p_table_26_Template, 4, 5, \"p-table\", 19);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedContact);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(!ctx.showDiv ? \"keyboard_arrow_down\" : \"keyboard_arrow_up\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.showDiv);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.length);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedContact || ctx.creatingTicket);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.creatingTicket ? \"Confirming...\" : \"Confirm\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.data.length);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.data.length && !ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.length);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i8.Breadcrumb, i1.PrimeTemplate, i9.Dropdown, i10.Table, i10.SortableColumn, i10.FrozenColumn, i10.RowToggler, i10.ReorderableColumn, i3.NgModel, i11.ButtonDirective, i12.InputText, i13.MultiSelect],\n        styles: [\".identify-account-body .topbar-start h1{display:none}  .min-width{min-width:18rem}  .custom-ratio-btn{width:16px;height:16px;accent-color:var(--primary-500)}  .search-result p-accordion p-accordiontab{margin:0 0 4px!important;display:flex}  .search-result p-accordion p-accordiontab:last-child{margin:0!important}  .search-result p-accordion p-accordiontab .p-accordion-tab{width:100%}  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link{border:none;flex-direction:row-reverse;width:100%;justify-content:space-between;border-radius:8px;min-height:48px}  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link span.p-accordion-toggle-icon{margin:0}  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link:hover{box-shadow:0 1px 3px var(--surface-100)}  .search-result p-accordion p-accordiontab .p-accordion-content{border-radius:8px;border:1px solid var(--surface-b)}  .search-result p-accordion p-accordiontab:nth-child(odd) .p-accordion-header .p-accordion-header-link{background:var(--surface-b)}  .search-result p-accordion p-accordiontab:nth-child(2n) .p-accordion-header .p-accordion-header-link{background:var(--surface-0)}\"]\n      });\n    }\n  }\n  return IdentifyAccountComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}