{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ContactsRoutingModule } from './contacts-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { ContactsComponent } from './contacts.component';\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\nimport { ContactsContactsComponent } from './contacts-details/contacts-contacts/contacts-contacts.component';\nimport { ContactsPartnersComponent } from './contacts-details/contacts-partners/contacts-partners.component';\nimport { ContactsSalesTeamComponent } from './contacts-details/contacts-sales-team/contacts-sales-team.component';\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\nimport { ContactsAiInsightsComponent } from './contacts-details/contacts-ai-insights/contacts-ai-insights.component';\nimport { ContactsOrganizationDataComponent } from './contacts-details/contacts-organization-data/contacts-organization-data.component';\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\nimport { ContactsSalesQuotesComponent } from './contacts-details/contacts-sales-quotes/contacts-sales-quotes.component';\nimport { ContactsSalesOrdersComponent } from './contacts-details/contacts-sales-orders/contacts-sales-orders.component';\nimport * as i0 from \"@angular/core\";\nexport class ContactsModule {\n  static {\n    this.ɵfac = function ContactsModule_Factory(t) {\n      return new (t || ContactsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ContactsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactsModule, {\n    declarations: [ContactsComponent, ContactsDetailsComponent, ContactsOverviewComponent, ContactsContactsComponent, ContactsPartnersComponent, ContactsSalesTeamComponent, ContactsOpportunitiesComponent, ContactsAiInsightsComponent, ContactsOrganizationDataComponent, ContactsAttachmentsComponent, ContactsNotesComponent, ContactsActivitiesComponent, ContactsRelationshipsComponent, ContactsTicketsComponent, ContactsSalesQuotesComponent, ContactsSalesOrdersComponent],\n    imports: [CommonModule, ContactsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, TabViewModule, AutoCompleteModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ContactsRoutingModule", "FormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "ContactsComponent", "ContactsDetailsComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "ContactsOverviewComponent", "ContactsContactsComponent", "ContactsPartnersComponent", "ContactsSalesTeamComponent", "ContactsOpportunitiesComponent", "ContactsAiInsightsComponent", "ContactsOrganizationDataComponent", "ContactsAttachmentsComponent", "ContactsNotesComponent", "ContactsActivitiesComponent", "ContactsRelationshipsComponent", "ContactsTicketsComponent", "ContactsSalesQuotesComponent", "ContactsSalesOrdersComponent", "ContactsModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ContactsRoutingModule } from './contacts-routing.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ContactsComponent } from './contacts.component';\r\nimport { ContactsDetailsComponent } from './contacts-details/contacts-details.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ContactsOverviewComponent } from './contacts-details/contacts-overview/contacts-overview.component';\r\nimport { ContactsContactsComponent } from './contacts-details/contacts-contacts/contacts-contacts.component';\r\nimport { ContactsPartnersComponent } from './contacts-details/contacts-partners/contacts-partners.component';\r\nimport { ContactsSalesTeamComponent } from './contacts-details/contacts-sales-team/contacts-sales-team.component';\r\nimport { ContactsOpportunitiesComponent } from './contacts-details/contacts-opportunities/contacts-opportunities.component';\r\nimport { ContactsAiInsightsComponent } from './contacts-details/contacts-ai-insights/contacts-ai-insights.component';\r\nimport { ContactsOrganizationDataComponent } from './contacts-details/contacts-organization-data/contacts-organization-data.component';\r\nimport { ContactsAttachmentsComponent } from './contacts-details/contacts-attachments/contacts-attachments.component';\r\nimport { ContactsNotesComponent } from './contacts-details/contacts-notes/contacts-notes.component';\r\nimport { ContactsActivitiesComponent } from './contacts-details/contacts-activities/contacts-activities.component';\r\nimport { ContactsRelationshipsComponent } from './contacts-details/contacts-relationships/contacts-relationships.component';\r\nimport { ContactsTicketsComponent } from './contacts-details/contacts-tickets/contacts-tickets.component';\r\nimport { ContactsSalesQuotesComponent } from './contacts-details/contacts-sales-quotes/contacts-sales-quotes.component';\r\nimport { ContactsSalesOrdersComponent } from './contacts-details/contacts-sales-orders/contacts-sales-orders.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ContactsComponent,\r\n    ContactsDetailsComponent,\r\n    ContactsOverviewComponent,\r\n    ContactsContactsComponent,\r\n    ContactsPartnersComponent,\r\n    ContactsSalesTeamComponent,\r\n    ContactsOpportunitiesComponent,\r\n    ContactsAiInsightsComponent,\r\n    ContactsOrganizationDataComponent,\r\n    ContactsAttachmentsComponent,\r\n    ContactsNotesComponent,\r\n    ContactsActivitiesComponent,\r\n    ContactsRelationshipsComponent,\r\n    ContactsTicketsComponent,\r\n    ContactsSalesQuotesComponent,\r\n    ContactsSalesOrdersComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ContactsRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    InputTextModule\r\n  ]\r\n})\r\nexport class ContactsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,yBAAyB,QAAQ,kEAAkE;AAC5G,SAASC,0BAA0B,QAAQ,sEAAsE;AACjH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,iCAAiC,QAAQ,oFAAoF;AACtI,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,2BAA2B,QAAQ,sEAAsE;AAClH,SAASC,8BAA8B,QAAQ,4EAA4E;AAC3H,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,4BAA4B,QAAQ,0EAA0E;AACvH,SAASC,4BAA4B,QAAQ,0EAA0E;;AAoCvH,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAbvB3B,YAAY,EACZC,qBAAqB,EACrBE,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXJ,WAAW,EACXE,cAAc,EACdM,YAAY,EACZE,aAAa,EACbH,kBAAkB,EAClBE,eAAe;IAAA;EAAA;;;2EAGNgB,cAAc;IAAAC,YAAA,GA/BvBrB,iBAAiB,EACjBC,wBAAwB,EACxBK,yBAAyB,EACzBC,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,iCAAiC,EACjCC,4BAA4B,EAC5BC,sBAAsB,EACtBC,2BAA2B,EAC3BC,8BAA8B,EAC9BC,wBAAwB,EACxBC,4BAA4B,EAC5BC,4BAA4B;IAAAG,OAAA,GAG5B7B,YAAY,EACZC,qBAAqB,EACrBE,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXJ,WAAW,EACXE,cAAc,EACdM,YAAY,EACZE,aAAa,EACbH,kBAAkB,EAClBE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}