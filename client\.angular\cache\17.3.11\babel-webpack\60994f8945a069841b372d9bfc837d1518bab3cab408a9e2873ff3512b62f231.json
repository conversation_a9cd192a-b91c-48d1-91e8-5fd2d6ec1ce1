{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\n\n/**\n * Tag component is used to categorize content.\n * @group Components\n */\nconst _c0 = [\"*\"];\nfunction Tag_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.icon);\n  }\n}\nfunction Tag_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Tag_ng_container_2_span_1_Template, 1, 1, \"span\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.icon);\n  }\n}\nfunction Tag_span_3_1_ng_template_0_Template(rf, ctx) {}\nfunction Tag_span_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tag_span_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Tag_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtemplate(1, Tag_span_3_1_Template, 1, 0, null, 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate);\n  }\n}\nlet Tag = /*#__PURE__*/(() => {\n  class Tag {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Severity type of the tag.\n     * @group Props\n     */\n    severity;\n    /**\n     * Value to display inside the tag.\n     * @group Props\n     */\n    value;\n    /**\n     * Icon of the tag to display next to the value.\n     * @group Props\n     * @deprecated since 15.4.2. Use 'icon' template.\n     */\n    icon;\n    /**\n     * Whether the corners of the tag are rounded.\n     * @group Props\n     */\n    rounded;\n    templates;\n    iconTemplate;\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    containerClass() {\n      return {\n        'p-tag p-component': true,\n        'p-tag-info': this.severity === 'info',\n        'p-tag-success': this.severity === 'success',\n        'p-tag-warning': this.severity === 'warning',\n        'p-tag-danger': this.severity === 'danger',\n        'p-tag-rounded': this.rounded\n      };\n    }\n    static ɵfac = function Tag_Factory(t) {\n      return new (t || Tag)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Tag,\n      selectors: [[\"p-tag\"]],\n      contentQueries: function Tag_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        severity: \"severity\",\n        value: \"value\",\n        icon: \"icon\",\n        rounded: \"rounded\"\n      },\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 7,\n      consts: [[3, \"ngClass\", \"ngStyle\"], [4, \"ngIf\"], [\"class\", \"p-tag-icon\", 4, \"ngIf\"], [1, \"p-tag-value\"], [\"class\", \"p-tag-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-tag-icon\", 3, \"ngClass\"], [1, \"p-tag-icon\"], [4, \"ngTemplateOutlet\"]],\n      template: function Tag_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, Tag_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, Tag_span_3_Template, 2, 1, \"span\", 2);\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.iconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.iconTemplate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.value);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n      styles: [\"@layer primeng{.p-tag{display:inline-flex;align-items:center;justify-content:center}.p-tag-icon,.p-tag-value,.p-tag-icon.pi{line-height:1.5}.p-tag.p-tag-rounded{border-radius:10rem}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Tag;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TagModule = /*#__PURE__*/(() => {\n  class TagModule {\n    static ɵfac = function TagModule_Factory(t) {\n      return new (t || TagModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TagModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, SharedModule]\n    });\n  }\n  return TagModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tag, TagModule };\n//# sourceMappingURL=primeng-tag.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}