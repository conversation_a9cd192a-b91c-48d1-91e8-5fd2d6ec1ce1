{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./account.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = [\"dt1\"];\nconst _c1 = a0 => ({\n  \"text-orange-600 cursor-pointer font-medium\": true,\n  underline: a0\n});\nconst _c2 = a0 => ({\n  \"text-blue-600 cursor-pointer font-medium\": true,\n  underline: a0\n});\nfunction AccountComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 18);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 19)(4, \"div\", 20);\n    i0.ɵɵtext(5, \" Account ID \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 22)(8, \"div\", 20);\n    i0.ɵɵtext(9, \" Name \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 24)(12, \"div\", 20);\n    i0.ɵɵtext(13, \" Address \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 26)(16, \"div\", 20);\n    i0.ɵɵtext(17, \" City \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\", 28)(20, \"div\", 20);\n    i0.ɵɵtext(21, \" State \");\n    i0.ɵɵelement(22, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"th\")(24, \"div\", 20);\n    i0.ɵɵtext(25, \" Size \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"th\")(27, \"div\", 20);\n    i0.ɵɵtext(28, \" Role \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 18);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16, \" Customer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const account_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", account_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, account_r2 == null ? null : account_r2.bp_id))(\"routerLink\", \"/store/account/\" + account_r2.documentId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c2, account_r2 == null ? null : account_r2.bp_full_name))(\"routerLink\", \"/store/account/\" + account_r2.documentId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.address) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.city_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.region) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (account_r2 == null ? null : account_r2.size) || \"-\", \" \");\n  }\n}\nfunction AccountComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \"No accounts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \"Loading accounts data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.accounts = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.searchInputChanged = new Subject();\n  }\n  ngOnInit() {\n    this.searchInputChanged.pipe(debounceTime(400),\n    // Adjust delay here (ms)\n    distinctUntilChanged()).subscribe(term => {\n      this.globalSearchTerm = term;\n      this.loadAccounts({\n        first: 0,\n        rows: 15\n      });\n    });\n    this.breadcrumbitems = [{\n      label: 'Account',\n      routerLink: ['/store/account']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Accounts',\n      code: 'MA'\n    }, {\n      name: 'Obsolete Accounts',\n      code: 'OA'\n    }];\n    this.selectedActions = {\n      name: 'All',\n      code: 'ALL'\n    };\n  }\n  loadAccounts(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    const obsolete = this.selectedActions?.code === 'OA';\n    const myaccount = this.selectedActions?.code === 'MA';\n    this.accountservice.getAccounts(page, pageSize, sortField, sortOrder, this.globalSearchTerm, obsolete, myaccount).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        let accounts = response?.data.map(account => {\n          const defaultAddress = account.addresses?.find(address => {\n            return address.address_usages.find(usage => usage.address_usage === 'XXDEFAULT');\n          });\n          return {\n            ...account,\n            address: [defaultAddress?.house_number, defaultAddress?.street_name, defaultAddress?.city_name, defaultAddress?.region, defaultAddress?.country, defaultAddress?.postal_code].filter(Boolean).join(', '),\n            city_name: defaultAddress?.city_name || '-',\n            region: defaultAddress?.region || '-'\n          };\n        }) || [];\n        this.accounts = accounts;\n        this.totalRecords = response?.meta?.pagination?.total || 0;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching accounts', error);\n        this.loading = false;\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 15\n    };\n    this.loadAccounts(dt1State);\n  }\n  onSearchInputChange(event) {\n    const input = event.target.value;\n    this.searchInputChanged.next(input);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountComponent_Factory(t) {\n      return new (t || AccountComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountComponent,\n      selectors: [[\"app-account\"]],\n      viewQuery: function AccountComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 18,\n      vars: 13,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Account\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [\"pSortableColumn\", \"bp_id\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"bp_id\"], [\"pSortableColumn\", \"bp_full_name\"], [\"field\", \"bp_full_name\"], [\"pSortableColumn\", \"addresses.house_number\"], [\"field\", \"addresses.house_number\"], [\"pSortableColumn\", \"addresses.city_name\"], [\"field\", \"addresses.city_name\"], [\"pSortableColumn\", \"addresses.region\"], [\"field\", \"addresses.region\"], [1, \"cursor-pointer\"], [3, \"value\"], [3, \"ngClass\", \"routerLink\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function AccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function AccountComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function AccountComponent_Template_p_dropdown_onChange_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 12)(12, \"p-table\", 13, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function AccountComponent_Template_p_table_onLazyLoad_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadAccounts($event));\n          });\n          i0.ɵɵtemplate(14, AccountComponent_ng_template_14_Template, 29, 0, \"ng-template\", 14)(15, AccountComponent_ng_template_15_Template, 17, 15, \"ng-template\", 15)(16, AccountComponent_ng_template_16_Template, 3, 0, \"ng-template\", 16)(17, AccountComponent_ng_template_17_Template, 3, 0, \"ng-template\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.accounts)(\"rows\", 15)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i3.RouterLink, i4.Breadcrumb, i5.PrimeTemplate, i6.Dropdown, i7.Table, i7.SortableColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "account_r2", "ɵɵpureFunction1", "_c1", "bp_id", "documentId", "ɵɵtextInterpolate1", "_c2", "bp_full_name", "address", "city_name", "region", "size", "AccountComponent", "constructor", "accountservice", "unsubscribe$", "accounts", "totalRecords", "loading", "globalSearchTerm", "searchInputChanged", "ngOnInit", "pipe", "subscribe", "term", "loadAccounts", "first", "rows", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "selectedActions", "event", "page", "pageSize", "sortField", "sortOrder", "obsolete", "myaccount", "getAccounts", "next", "response", "data", "map", "account", "defaultAddress", "addresses", "find", "address_usages", "usage", "address_usage", "house_number", "street_name", "country", "postal_code", "filter", "Boolean", "join", "meta", "pagination", "total", "error", "console", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "onSearchInputChange", "input", "target", "value", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "viewQuery", "AccountComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "AccountComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "AccountComponent_Template_input_input_7_listener", "AccountComponent_Template_p_dropdown_ngModelChange_10_listener", "AccountComponent_Template_p_dropdown_onChange_10_listener", "AccountComponent_Template_p_table_onLazyLoad_12_listener", "ɵɵtemplate", "AccountComponent_ng_template_14_Template", "AccountComponent_ng_template_15_Template", "AccountComponent_ng_template_16_Template", "AccountComponent_ng_template_17_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { AccountService } from './account.service';\r\nimport { Table } from 'primeng/table';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n@Component({\r\n  selector: 'app-account',\r\n  templateUrl: './account.component.html',\r\n  styleUrl: './account.component.scss',\r\n})\r\nexport class AccountComponent implements OnInit {\r\n  @ViewChild('dt1') dt1!: Table;\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n  public accounts: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public searchInputChanged: Subject<string> = new Subject<string>();\r\n\r\n  constructor(private accountservice: AccountService) {}\r\n\r\n  ngOnInit() {\r\n    this.searchInputChanged\r\n      .pipe(\r\n        debounceTime(400), // Adjust delay here (ms)\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe((term: string) => {\r\n        this.globalSearchTerm = term;\r\n        this.loadAccounts({ first: 0, rows: 15 });\r\n      });\r\n    this.breadcrumbitems = [\r\n      { label: 'Account', routerLink: ['/store/account'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Accounts', code: 'MA' },\r\n      { name: 'Obsolete Accounts', code: 'OA' },\r\n    ];\r\n    this.selectedActions = { name: 'All', code: 'ALL' };\r\n  }\r\n\r\n  loadAccounts(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    const obsolete = this.selectedActions?.code === 'OA';\r\n    const myaccount = this.selectedActions?.code === 'MA';\r\n\r\n    this.accountservice\r\n      .getAccounts(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm,\r\n        obsolete,\r\n        myaccount\r\n      )\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          let accounts =\r\n            response?.data.map((account: any) => {\r\n              const defaultAddress = account.addresses?.find((address: any) => {\r\n                return address.address_usages.find(\r\n                  (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n                );\r\n              });\r\n\r\n              return {\r\n                ...account,\r\n                address: [\r\n                  defaultAddress?.house_number,\r\n                  defaultAddress?.street_name,\r\n                  defaultAddress?.city_name,\r\n                  defaultAddress?.region,\r\n                  defaultAddress?.country,\r\n                  defaultAddress?.postal_code,\r\n                ]\r\n                  .filter(Boolean)\r\n                  .join(', '),\r\n                city_name: defaultAddress?.city_name || '-',\r\n                region: defaultAddress?.region || '-',\r\n              };\r\n            }) || [];\r\n\r\n          this.accounts = accounts;\r\n          this.totalRecords = response?.meta?.pagination?.total || 0;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching accounts', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 15,\r\n    };\r\n    this.loadAccounts(dt1State);\r\n  }\r\n\r\n  onSearchInputChange(event: Event) {\r\n    const input = (event.target as HTMLInputElement).value;\r\n    this.searchInputChanged.next(input);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onSearchInputChange($event)\"\r\n                        placeholder=\"Search Account\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange()\"\r\n                optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n        </div>\r\n\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"accounts\" dataKey=\"id\" [rows]=\"15\" (onLazyLoad)=\"loadAccounts($event)\"\r\n            [loading]=\"loading\" styleClass=\"\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_id\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Account ID\r\n                            <p-sortIcon field=\"bp_id\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"bp_full_name\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Name\r\n                            <p-sortIcon field=\"bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"addresses.house_number\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Address\r\n                            <p-sortIcon\r\n                                field=\"addresses.house_number\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"addresses.city_name\">\r\n                        <div class=\"flex align-items-center\">\r\n                            City\r\n                            <p-sortIcon\r\n                                field=\"addresses.city_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"addresses.region\">\r\n                        <div class=\"flex align-items-center\">\r\n                            State\r\n                            <p-sortIcon field=\"addresses.region\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Size\r\n                        </div>\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Role\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-account>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"account\" />\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-orange-600 cursor-pointer font-medium': true,\r\n                        underline: account?.bp_id\r\n                        }\" [routerLink]=\"'/store/account/' + account.documentId\">\r\n                        {{ account?.bp_id || \"-\" }}\r\n                    </td>\r\n                    <td [ngClass]=\"{\r\n                        'text-blue-600 cursor-pointer font-medium': true,\r\n                        underline: account?.bp_full_name\r\n                        }\" [routerLink]=\"'/store/account/' + account.documentId\">\r\n                        {{ account?.bp_full_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.address || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.city_name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.region || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ account?.size || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        Customer\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">No accounts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg pl-3\">Loading accounts data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;ICuB/CC,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAA4B,cACa;IACjCD,EAAA,CAAAI,MAAA,mBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAAuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAmC,cACM;IACjCD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA6C,eACJ;IACjCD,EAAA,CAAAI,MAAA,iBACA;IAAAJ,EAAA,CAAAE,SAAA,sBACgD;IAExDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA0C,eACD;IACjCD,EAAA,CAAAI,MAAA,cACA;IAAAJ,EAAA,CAAAE,SAAA,sBAC6C;IAErDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAuC,eACE;IACjCD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAkD;IAE1DF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,cACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,UAAI,eACqC;IACjCD,EAAA,CAAAI,MAAA,cACJ;IAERJ,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;IAKDH,EADJ,CAAAC,cAAA,aAA2B,aACkC;IACrDD,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAG6D;IACzDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAG6D;IACzDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,kBACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA7BoBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,UAAA,UAAAC,UAAA,CAAiB;IAElCP,EAAA,CAAAK,SAAA,EAGE;IAACL,EAHH,CAAAM,UAAA,YAAAN,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAF,UAAA,kBAAAA,UAAA,CAAAG,KAAA,EAGE,mCAAAH,UAAA,CAAAI,UAAA,CAAsD;IACxDX,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAG,KAAA,cACJ;IACIV,EAAA,CAAAK,SAAA,EAGE;IAACL,EAHH,CAAAM,UAAA,YAAAN,EAAA,CAAAQ,eAAA,KAAAK,GAAA,EAAAN,UAAA,kBAAAA,UAAA,CAAAO,YAAA,EAGE,mCAAAP,UAAA,CAAAI,UAAA,CAAsD;IACxDX,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAO,YAAA,cACJ;IAEId,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAQ,OAAA,cACJ;IAEIf,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAS,SAAA,cACJ;IAEIhB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAU,MAAA,cACJ;IAEIjB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,OAAAL,UAAA,kBAAAA,UAAA,CAAAW,IAAA,cACJ;;;;;IAQAlB,EADJ,CAAAC,cAAA,SAAI,aACkD;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACxEJ,EADwE,CAAAG,YAAA,EAAK,EACxE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACkD;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IACzFJ,EADyF,CAAAG,YAAA,EAAK,EACzF;;;ADtGrB,OAAM,MAAOgB,gBAAgB;EAa3BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAX1B,KAAAC,YAAY,GAAG,IAAI1B,OAAO,EAAQ;IAKnC,KAAA2B,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,kBAAkB,GAAoB,IAAI/B,OAAO,EAAU;EAEb;EAErDgC,QAAQA,CAAA;IACN,IAAI,CAACD,kBAAkB,CACpBE,IAAI,CACH/B,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CACvB,CACA+B,SAAS,CAAEC,IAAY,IAAI;MAC1B,IAAI,CAACL,gBAAgB,GAAGK,IAAI;MAC5B,IAAI,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE,CAAC;IAC3C,CAAC,CAAC;IACJ,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,SAAS;MAAEC,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,CACrD;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IACrD,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAI,CAAE,EACnC;MAAED,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC1C;IACD,IAAI,CAACC,eAAe,GAAG;MAAEF,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE;EACrD;EAEAV,YAAYA,CAACY,KAAU;IACrB,IAAI,CAACnB,OAAO,GAAG,IAAI;IACnB,MAAMoB,IAAI,GAAGD,KAAK,CAACX,KAAK,GAAGW,KAAK,CAACV,IAAI,GAAG,CAAC;IACzC,MAAMY,QAAQ,GAAGF,KAAK,CAACV,IAAI;IAC3B,MAAMa,SAAS,GAAGH,KAAK,CAACG,SAAS;IACjC,MAAMC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IACjC,MAAMC,QAAQ,GAAG,IAAI,CAACN,eAAe,EAAED,IAAI,KAAK,IAAI;IACpD,MAAMQ,SAAS,GAAG,IAAI,CAACP,eAAe,EAAED,IAAI,KAAK,IAAI;IAErD,IAAI,CAACrB,cAAc,CAChB8B,WAAW,CACVN,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAACtB,gBAAgB,EACrBuB,QAAQ,EACRC,SAAS,CACV,CACArB,IAAI,CAAChC,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC,CAClCQ,SAAS,CAAC;MACTsB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI9B,QAAQ,GACV8B,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAAEC,OAAY,IAAI;UAClC,MAAMC,cAAc,GAAGD,OAAO,CAACE,SAAS,EAAEC,IAAI,CAAE5C,OAAY,IAAI;YAC9D,OAAOA,OAAO,CAAC6C,cAAc,CAACD,IAAI,CAC/BE,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD;UACH,CAAC,CAAC;UAEF,OAAO;YACL,GAAGN,OAAO;YACVzC,OAAO,EAAE,CACP0C,cAAc,EAAEM,YAAY,EAC5BN,cAAc,EAAEO,WAAW,EAC3BP,cAAc,EAAEzC,SAAS,EACzByC,cAAc,EAAExC,MAAM,EACtBwC,cAAc,EAAEQ,OAAO,EACvBR,cAAc,EAAES,WAAW,CAC5B,CACEC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;YACbrD,SAAS,EAAEyC,cAAc,EAAEzC,SAAS,IAAI,GAAG;YAC3CC,MAAM,EAAEwC,cAAc,EAAExC,MAAM,IAAI;WACnC;QACH,CAAC,CAAC,IAAI,EAAE;QAEV,IAAI,CAACM,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACC,YAAY,GAAG6B,QAAQ,EAAEiB,IAAI,EAAEC,UAAU,EAAEC,KAAK,IAAI,CAAC;QAC1D,IAAI,CAAC/C,OAAO,GAAG,KAAK;MACtB,CAAC;MACDgD,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAChD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAkD,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvD7C,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACF,YAAY,CAAC4C,QAAQ,CAAC;EAC7B;EAEAG,mBAAmBA,CAACnC,KAAY;IAC9B,MAAMoC,KAAK,GAAIpC,KAAK,CAACqC,MAA2B,CAACC,KAAK;IACtD,IAAI,CAACvD,kBAAkB,CAACyB,IAAI,CAAC4B,KAAK,CAAC;EACrC;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAC7D,YAAY,CAAC8B,IAAI,EAAE;IACxB,IAAI,CAAC9B,YAAY,CAAC8D,QAAQ,EAAE;EAC9B;;;uBAjHWjE,gBAAgB,EAAAnB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhBpE,gBAAgB;MAAAqE,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCdrB3F,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAGgF;UAFlFD,EAAA,CAAA6F,gBAAA,2BAAAC,yDAAAC,MAAA;YAAA/F,EAAA,CAAAgG,aAAA,CAAAC,GAAA;YAAAjG,EAAA,CAAAkG,kBAAA,CAAAN,GAAA,CAAAlE,gBAAA,EAAAqE,MAAA,MAAAH,GAAA,CAAAlE,gBAAA,GAAAqE,MAAA;YAAA,OAAA/F,EAAA,CAAAmG,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UAAC/F,EAAA,CAAAoG,UAAA,mBAAAC,iDAAAN,MAAA;YAAA/F,EAAA,CAAAgG,aAAA,CAAAC,GAAA;YAAA,OAAAjG,EAAA,CAAAmG,WAAA,CAASP,GAAA,CAAAb,mBAAA,CAAAgB,MAAA,CAA2B;UAAA,EAAC;UAA/F/F,EAAA,CAAAG,YAAA,EAE6G;UAC7GH,EAAA,CAAAE,SAAA,YAAiD;UAEzDF,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAC,cAAA,sBAEyG;UAFzED,EAAA,CAAA6F,gBAAA,2BAAAS,+DAAAP,MAAA;YAAA/F,EAAA,CAAAgG,aAAA,CAAAC,GAAA;YAAAjG,EAAA,CAAAkG,kBAAA,CAAAN,GAAA,CAAAjD,eAAA,EAAAoD,MAAA,MAAAH,GAAA,CAAAjD,eAAA,GAAAoD,MAAA;YAAA,OAAA/F,EAAA,CAAAmG,WAAA,CAAAJ,MAAA;UAAA,EAA6B;UAAC/F,EAAA,CAAAoG,UAAA,sBAAAG,0DAAA;YAAAvG,EAAA,CAAAgG,aAAA,CAAAC,GAAA;YAAA,OAAAjG,EAAA,CAAAmG,WAAA,CAAYP,GAAA,CAAAjB,cAAA,EAAgB;UAAA,EAAC;UAKnG3E,EALQ,CAAAG,YAAA,EAEyG,EACvG,EAEJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAGW;UAF4BD,EAAA,CAAAoG,UAAA,wBAAAI,yDAAAT,MAAA;YAAA/F,EAAA,CAAAgG,aAAA,CAAAC,GAAA;YAAA,OAAAjG,EAAA,CAAAmG,WAAA,CAAcP,GAAA,CAAA5D,YAAA,CAAA+D,MAAA,CAAoB;UAAA,EAAC;UA4FzF/F,EAzFA,CAAAyG,UAAA,KAAAC,wCAAA,2BAAgC,KAAAC,wCAAA,4BAkDU,KAAAC,wCAAA,0BAkCJ,KAAAC,wCAAA,0BAKD;UAOjD7G,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UAvHoBH,EAAA,CAAAK,SAAA,GAAyB;UAAeL,EAAxC,CAAAM,UAAA,UAAAsF,GAAA,CAAAzD,eAAA,CAAyB,SAAAyD,GAAA,CAAAtD,IAAA,CAAc,uCAAuC;UAMzDtC,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA8G,gBAAA,YAAAlB,GAAA,CAAAlE,gBAAA,CAA8B;UAMrD1B,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAM,UAAA,YAAAsF,GAAA,CAAApD,OAAA,CAAmB;UAACxC,EAAA,CAAA8G,gBAAA,YAAAlB,GAAA,CAAAjD,eAAA,CAA6B;UAEzD3C,EAAA,CAAAM,UAAA,mGAAkG;UAM5FN,EAAA,CAAAK,SAAA,GAAkB;UACuDL,EADzE,CAAAM,UAAA,UAAAsF,GAAA,CAAArE,QAAA,CAAkB,YAAyB,YAAAqE,GAAA,CAAAnE,OAAA,CAClC,mBAAiC,iBAAAmE,GAAA,CAAApE,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}