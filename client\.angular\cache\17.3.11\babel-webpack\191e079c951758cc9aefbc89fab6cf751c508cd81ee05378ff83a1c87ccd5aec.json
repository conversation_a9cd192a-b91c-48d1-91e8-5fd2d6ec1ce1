{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { ApiConstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport let ProspectsService = /*#__PURE__*/(() => {\n  class ProspectsService {\n    constructor(http, authservice) {\n      this.http = http;\n      this.authservice = authservice;\n      this.prospectSubject = new BehaviorSubject(null);\n      this.prospect = this.prospectSubject.asObservable();\n    }\n    createProspect(data) {\n      return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECTS}`, data);\n    }\n    createMarketing(data) {\n      return this.http.post(`${CMS_APIContstant.MARKETING_ATTRIBUTES}`, {\n        data\n      });\n    }\n    createNote(data) {\n      return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n        data\n      });\n    }\n    createAddress(data) {\n      return this.http.post(`${CMS_APIContstant.PROSPECT_ADDRESS_REGISTER}`, data);\n    }\n    createContact(data) {\n      return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\n    }\n    createExistingContact(data) {\n      return this.http.post(`${CMS_APIContstant.EXISTING_CONTACT}`, data);\n    }\n    createEmployee(data) {\n      return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECT_SALES_TEAM}`, data);\n    }\n    updateProspect(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PROSPECTS}/${Id}/save`, data);\n    }\n    updateMarketing(Id, data) {\n      return this.http.put(`${CMS_APIContstant.MARKETING_ATTRIBUTES}/${Id}`, {\n        data\n      });\n    }\n    updateAddress(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PROSPECT_ADDRESS}/${Id}/save`, data);\n    }\n    updateContact(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n    }\n    updateReactivate(contactdata) {\n      const data = {\n        validity_end_date: '9999-12-29'\n      };\n      return this.http.put(`${CMS_APIContstant.PARTNERS_CONTACTS}/${contactdata.documentId}`, {\n        data\n      });\n    }\n    updateEmployee(Id, data) {\n      return this.http.put(`${CMS_APIContstant.SALES_TEAM}/${Id}/save`, data);\n    }\n    updateBpStatus(Id, data) {\n      return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, {\n        data\n      });\n    }\n    updateNote(Id, data) {\n      return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n        data\n      });\n    }\n    getProspects(page, pageSize, sortField, sortOrder, searchTerm, obsolete, myprospect) {\n      let params = new HttpParams().set('pagination[page]', Math.max(page, 1).toString()).set('pagination[pageSize]', Math.max(pageSize, 1).toString()).set('fields', 'bp_id,bp_full_name,is_marked_for_archiving').set('filters[roles][bp_role][$in][0]', 'PRO001').set('populate[addresses][fields][0]', 'house_number').set('populate[addresses][fields][1]', 'street_name').set('populate[addresses][fields][2]', 'city_name').set('populate[addresses][fields][3]', 'region').set('populate[addresses][fields][4]', 'country').set('populate[addresses][fields][5]', 'postal_code').set('populate[addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[addresses][populate][emails][fields][0]', 'email_address').set('populate[addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[customer][fields][0]', 'id').set('populate[customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[customer][populate][partner_functions][populate][business_partner][fields][0]', 'bp_full_name');\n      if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm && searchTerm.trim() !== '') {\n        params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][addresses][house_number][$containsi]', searchTerm).set('filters[$or][3][addresses][city_name][$containsi]', searchTerm).set('filters[$or][4][addresses][country][$containsi]', searchTerm).set('filters[$or][5][addresses][emails][email_address][$containsi]', searchTerm).set('filters[$or][6][addresses][phone_numbers][phone_number][$containsi]', searchTerm).set('filters[$or][7][contact_companies][business_partner_person][first_name][$containsi]', searchTerm).set('filters[$or][8][contact_companies][business_partner_person][last_name][$containsi]', searchTerm).set('filters[$or][9][customer][partner_functions][business_partner][bp_full_name][$containsi]', searchTerm);\n      }\n      // Combine obsolete and myprospect filters carefully to avoid index collision\n      if (obsolete || myprospect) {\n        let andIndex = 0;\n        if (obsolete) {\n          params = params.set(`filters[$and][${andIndex}][is_marked_for_archiving][$eq]`, 'true');\n          andIndex++;\n        }\n        if (myprospect) {\n          const email = this.authservice.getUserEmail();\n          if (email) {\n            params = params.set(`filters[$and][${andIndex}][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][${andIndex + 1}][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n          } else {\n            console.warn('No email found for the logged-in user');\n          }\n        }\n      }\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      });\n    }\n    getEmployee(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      }).pipe(map(response => {\n        return response.data || [];\n      }));\n    }\n    getContacts(params) {\n      return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`, {\n        params\n      }).pipe(map(response => (response?.data || []).map(item => {\n        const contact = item?.addresses?.[0];\n        const email = contact?.emails?.[0]?.email_address || '';\n        const mobile = (contact?.phone_numbers || []).filter(item => item.phone_number_type === '3').map(item => item.phone_number);\n        return {\n          bp_id: item?.bp_id || '',\n          bp_full_name: (item?.first_name ? item.first_name : '') + (item?.last_name ? ' ' + item.last_name : ''),\n          email: email,\n          mobile: mobile\n        };\n      })));\n    }\n    getPartnerfunction() {\n      let params = new HttpParams().set('filters[usage][$eq]', 'CRM').set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      }).pipe(map(response => {\n        let data = response.data || [];\n        return data.map(item => ({\n          label: item.description,\n          // Display text\n          value: item.code // Stored value\n        }));\n      }));\n    }\n    getCPFunction() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getSizeUnit() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'BPMA_SIZE_UNIT');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getChainScale() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'BPMA_STR_CHAIN_SCALE');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getCPDepartment() {\n      let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n      return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n        params\n      });\n    }\n    getGlobalNote(id) {\n      let params = new HttpParams().set('filters[is_global_note][$eq]', 'true').set('filters[bp_id][$eq]', id);\n      return this.http.get(`${CMS_APIContstant.CRM_NOTE}`, {\n        params\n      });\n    }\n    delete(id) {\n      return this.http.delete(`${CMS_APIContstant.PROSPECTS}/${id}`);\n    }\n    deleteContact(id) {\n      return this.http.delete(`${CMS_APIContstant.PARTNERS_CONTACTS}/${id}`);\n    }\n    deleteNote(id) {\n      return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n    }\n    deleteAddress(id) {\n      return this.http.delete(`${CMS_APIContstant.PARTNERS_ADDRESS}/${id}`);\n    }\n    deleteEmployee(id) {\n      return this.http.delete(`${CMS_APIContstant.DELETE_SALES_TEAM}/${id}`);\n    }\n    getProspectByID(prospectId) {\n      const params = new HttpParams().set('filters[bp_id][$eq]', prospectId).set('populate[addresses][populate]', '*').set('populate[customer][populate][partner_functions][populate][business_partner][populate][addresses][populate]', '*').set('populate[notes][populate]', '*').set('populate[bp_extension][populate]', '*').set('populate[marketing_attributes][populate]', '*').set('populate[contact_companies][populate][person_func_and_dept][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][addresses][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][bp_extension][populate]', '*').set('populate[address_usages][populate]', '*');\n      return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n        params\n      }).pipe(map(response => {\n        const prospectDetails = response?.data[0] || null;\n        this.prospectSubject.next(prospectDetails);\n        return response;\n      }));\n    }\n    bpCreation(body) {\n      return this.http.post(`${ApiConstant.BUSINESS_PARTNER}`, body);\n    }\n    static {\n      this.ɵfac = function ProspectsService_Factory(t) {\n        return new (t || ProspectsService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProspectsService,\n        factory: ProspectsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProspectsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}