{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/table\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/button\";\nfunction SalesOrdersAttachmentsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10);\n    i0.ɵɵtext(2, \"File Icon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Changed On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Changed By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 11);\n    i0.ɵɵtext(12, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesOrdersAttachmentsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 10)(2, \"i\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 11)(13, \"button\", 13)(14, \"i\", 14);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tableinfo_r1.FileIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Type, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ChangedOn, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ChangedBy, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tableinfo_r1.Action);\n  }\n}\nexport let SalesOrdersAttachmentsComponent = /*#__PURE__*/(() => {\n  class SalesOrdersAttachmentsComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.tableData = [{\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }, {\n        FileIcon: 'perm_media',\n        Title: 'logo-original.jpg',\n        Type: 'Standard Attachment',\n        ChangedOn: '11/28/2024 1:12 PM',\n        ChangedBy: 'Amit Asar',\n        Action: 'delete'\n      }];\n    }\n    static {\n      this.ɵfac = function SalesOrdersAttachmentsComponent_Factory(t) {\n        return new (t || SalesOrdersAttachmentsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesOrdersAttachmentsComponent,\n        selectors: [[\"app-sales-orders-attachments\"]],\n        decls: 11,\n        vars: 5,\n        consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", \"font-semibold\", 3, \"rounded\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\", \"text-center\"], [1, \"material-symbols-rounded\", \"text-primary\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"]],\n        template: function SalesOrdersAttachmentsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n            i0.ɵɵtext(5, \"Attachments\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"p-button\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵtemplate(9, SalesOrdersAttachmentsComponent_ng_template_9_Template, 13, 0, \"ng-template\", 8)(10, SalesOrdersAttachmentsComponent_ng_template_10_Template, 16, 6, \"ng-template\", 9);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"px-3\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 8)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.Table, i2.PrimeTemplate, i3.Button]\n      });\n    }\n  }\n  return SalesOrdersAttachmentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}