{"ast": null, "code": "import { HttpResponse } from \"@angular/common/http\"; // Import HttpClient\nimport { tap, switchMap, catchError, of, throwError } from \"rxjs\";\nimport { ApiConstant, CMS_APIContstant, ENDPOINT } from \"src/app/constants/api.constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"@angular/common/http\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService, http) {\n      this.authService = authService;\n      this.http = http;\n      this.escapeTokenUrls = [CMS_APIContstant.SINGIN, CMS_APIContstant.RESET_PASSWORD_REQUEST, CMS_APIContstant.RESET_PASSWORD, CMS_APIContstant.CONTENT_CRM, CMS_APIContstant.MAIN_MENU_API_DETAILS];\n    }\n    intercept(req, next) {\n      if (this.authService.isLoggedIn && (req.url.includes(ENDPOINT.NODE) || !this.startsWithAnyUrl(req.url, this.escapeTokenUrls))) {\n        return this.getAuthToken(req.url).pipe(tap(token => {\n          if (token) {\n            const headers = {\n              Authorization: \"Bearer \" + token\n            };\n            if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {\n              const details = this.authService.userDetail;\n              headers.documentId = details.documentId;\n            }\n            req = req.clone({\n              setHeaders: headers\n            });\n          }\n        }), switchMap(() => next.handle(req)), tap(event => {\n          if (event instanceof HttpResponse) {\n            const newToken = event.headers.get(\"refreshtoken\");\n            const auth = this.authService.getAuth();\n            if (newToken && auth) {\n              this.authService.setAuth(newToken, auth[this.authService.UserDetailsKey], this.authService.isRememberMeSelected());\n            }\n          }\n        }), catchError(x => this.handleAuthError(x)));\n      }\n      return next.handle(req).pipe(catchError(x => this.handleAuthError(x)));\n    }\n    getAuthToken(url) {\n      const authToken = this.authService.getToken();\n      const isAdmin = this.authService.userDetail?.isAdmin;\n      if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {\n        return this.authService.cmsToken.pipe(switchMap(token => {\n          if (token) {\n            return of(token);\n          }\n          return this.authService.getCMSToken();\n        }));\n      }\n      return of(authToken);\n    }\n    startsWithAnyUrl(newUrl, urls) {\n      return urls.some(url => newUrl.startsWith(url));\n    }\n    handleAuthError(err) {\n      if (err.status === 401 || err.status === 403) {\n        this.authService.removeAuthToken();\n        window.location.href = \"#/auth/login\";\n        window.location.reload();\n        return of(err.message);\n      }\n      return throwError(() => err);\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}