{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dialog\";\nimport * as i8 from \"primeng/editor\";\nimport * as i9 from \"primeng/tooltip\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction OpportunitiesNotesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 19)(2, \"div\", 20);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 22)(6, \"div\", 20);\n    i0.ɵɵtext(7, \" Created At \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 24)(10, \"div\", 20);\n    i0.ɵɵtext(11, \" Updated At \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 26);\n    i0.ɵɵtext(14, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 28)(10, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function OpportunitiesNotesComponent_ng_template_8_Template_button_click_10_listener() {\n      const notes_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editNote(notes_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function OpportunitiesNotesComponent_ng_template_8_Template_button_click_11_listener($event) {\n      const notes_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(notes_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.stripHtml(notes_r2 == null ? null : notes_r2.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 3, notes_r2 == null ? null : notes_r2.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 6, notes_r2 == null ? null : notes_r2.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesNotesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesNotesComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesNotesComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, OpportunitiesNotesComponent_div_17_div_1_Template, 2, 0, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport let OpportunitiesNotesComponent = /*#__PURE__*/(() => {\n  class OpportunitiesNotesComponent {\n    constructor(formBuilder, opportunitiesservice, messageservice, confirmationservice) {\n      this.formBuilder = formBuilder;\n      this.opportunitiesservice = opportunitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.notedetails = null;\n      this.visible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.opportunity_id = '';\n      this.editid = '';\n      this.NoteForm = this.formBuilder.group({\n        note: ['', [Validators.required]]\n      });\n    }\n    ngOnInit() {\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.opportunity_id = response?.opportunity_id;\n          this.notedetails = response?.notes;\n        }\n      });\n    }\n    editNote(note) {\n      this.visible = true;\n      this.editid = note?.documentId;\n      this.NoteForm.patchValue(note);\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        _this.visible = true;\n        if (_this.NoteForm.invalid) {\n          _this.visible = true;\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.NoteForm.value\n        };\n        const data = {\n          opportunity_id: _this.opportunity_id,\n          note: value?.note\n        };\n        if (_this.editid) {\n          _this.opportunitiesservice.updateNote(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Updated Successfully!.'\n              });\n              _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.opportunitiesservice.createNote(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Created Successfully!.'\n              });\n              _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.opportunitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    get f() {\n      return this.NoteForm.controls;\n    }\n    showDialog(position) {\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.NoteForm.reset();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OpportunitiesNotesComponent_Factory(t) {\n        return new (t || OpportunitiesNotesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunitiesNotesComponent,\n        selectors: [[\"app-opportunities-notes\"]],\n        decls: 21,\n        vars: 20,\n        consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pSortableColumn\", \"note\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"note\"], [\"pSortableColumn\", \"createdAt\", 2, \"width\", \"10rem\"], [\"field\", \"createdAt\"], [\"pSortableColumn\", \"updatedAt\", 2, \"width\", \"10rem\"], [\"field\", \"updatedAt\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"border-round-left-lg\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [4, \"ngIf\"]],\n        template: function OpportunitiesNotesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 3);\n            i0.ɵɵlistener(\"click\", function OpportunitiesNotesComponent_Template_p_button_click_4_listener() {\n              return ctx.showDialog(\"right\");\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n            i0.ɵɵtemplate(7, OpportunitiesNotesComponent_ng_template_7_Template, 15, 0, \"ng-template\", 6)(8, OpportunitiesNotesComponent_ng_template_8_Template, 12, 9, \"ng-template\", 7)(9, OpportunitiesNotesComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, OpportunitiesNotesComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"p-dialog\", 10);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesNotesComponent_Template_p_dialog_visibleChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(12, OpportunitiesNotesComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n            i0.ɵɵelementStart(13, \"form\", 11)(14, \"div\", 12)(15, \"div\", 13);\n            i0.ɵɵelement(16, \"p-editor\", 14);\n            i0.ɵɵtemplate(17, OpportunitiesNotesComponent_div_17_Template, 2, 1, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 16)(19, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function OpportunitiesNotesComponent_Template_button_click_19_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function OpportunitiesNotesComponent_Template_button_click_20_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(16, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(17, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c2, ctx.submitted && ctx.f[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.Table, i3.PrimeTemplate, i5.SortableColumn, i5.SortIcon, i6.ButtonDirective, i6.Button, i7.Dialog, i8.Editor, i9.Tooltip, i4.DatePipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .note-popup .p-dialog{margin-right:50px}  .note-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .note-popup .p-dialog .p-dialog-header h4{margin:0}  .note-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return OpportunitiesNotesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}