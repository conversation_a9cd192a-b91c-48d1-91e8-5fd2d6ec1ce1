{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ServiceTicketService = /*#__PURE__*/(() => {\n  class ServiceTicketService {\n    constructor(http) {\n      this.http = http;\n    }\n    getAll(query) {\n      return this.http.get(`${CMS_APIContstant.TICKET}?${query}`);\n    }\n    getById(id) {\n      return this.http.get(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);\n    }\n    getByAccountId(id) {\n      return this.http.get(`${CMS_APIContstant.TICKET}?filters[account_id]=${id}`);\n    }\n    createTicket(data) {\n      return this.http.post(`${CMS_APIContstant.TICKET}`, data);\n    }\n    updateTicket(id, data) {\n      return this.http.put(`${CMS_APIContstant.TICKET}/${id}`, data);\n    }\n    getAllTicketStatus() {\n      return this.http.get(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');\n    }\n    static {\n      this.ɵfac = function ServiceTicketService_Factory(t) {\n        return new (t || ServiceTicketService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ServiceTicketService,\n        factory: ServiceTicketService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ServiceTicketService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}