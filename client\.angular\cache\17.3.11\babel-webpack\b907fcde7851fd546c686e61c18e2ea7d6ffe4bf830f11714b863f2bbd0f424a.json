{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../contacts.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/editor\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ContactsNotesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsNotesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ContactsNotesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsNotesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ContactsNotesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 30);\n    i0.ɵɵlistener(\"click\", function ContactsNotesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ContactsNotesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 23)(5, ContactsNotesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction ContactsNotesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function ContactsNotesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"note\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, ContactsNotesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 23)(5, ContactsNotesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ContactsNotesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 25);\n    i0.ɵɵelementStart(7, \"th\", 26)(8, \"div\", 27);\n    i0.ɵɵtext(9, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ContactsNotesComponent_ng_template_10_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r6 == null ? null : notes_r6.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ContactsNotesComponent_ng_template_10_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r6 == null ? null : notes_r6.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ContactsNotesComponent_ng_template_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 35);\n    i0.ɵɵtemplate(3, ContactsNotesComponent_ng_template_10_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 36)(4, ContactsNotesComponent_ng_template_10_ng_container_2_ng_container_4_Template, 3, 4, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n  }\n}\nfunction ContactsNotesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 31);\n    i0.ɵɵtemplate(2, ContactsNotesComponent_ng_template_10_ng_container_2_Template, 5, 3, \"ng-container\", 25);\n    i0.ɵɵelementStart(3, \"td\", 32)(4, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ContactsNotesComponent_ng_template_10_Template_button_click_4_listener() {\n      const notes_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNote(notes_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ContactsNotesComponent_ng_template_10_Template_button_click_5_listener($event) {\n      const notes_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(notes_r6));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r6 == null ? null : notes_r6.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ContactsNotesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 37);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsNotesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 37);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsNotesComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactsNotesComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactsNotesComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, ContactsNotesComponent_div_19_div_1_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport let ContactsNotesComponent = /*#__PURE__*/(() => {\n  class ContactsNotesComponent {\n    constructor(formBuilder, contactsservice, messageservice, confirmationservice) {\n      this.formBuilder = formBuilder;\n      this.contactsservice = contactsservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.unsubscribe$ = new Subject();\n      this.notedetails = [];\n      this.visible = false;\n      this.position = 'right';\n      this.submitted = false;\n      this.saving = false;\n      this.bp_id = '';\n      this.document_id = '';\n      this.editid = '';\n      this.NoteForm = this.formBuilder.group({\n        note: ['', [Validators.required]]\n      });\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'updatedAt',\n        header: 'Last Updated On'\n      }, {\n        field: 'updatedBy',\n        header: 'Updated By'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.notedetails.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.contactsservice.contact.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.bp_id = response?.bp_person_id;\n          this.document_id = response?.documentId;\n          this.notedetails = response?.business_partner_person?.notes;\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    editNote(note) {\n      this.visible = true;\n      this.editid = note?.documentId;\n      this.NoteForm.patchValue(note);\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        _this.visible = true;\n        if (_this.NoteForm.invalid) {\n          _this.visible = true;\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.NoteForm.value\n        };\n        const data = {\n          bp_id: _this.bp_id,\n          note: value?.note\n        };\n        if (_this.editid) {\n          _this.contactsservice.updateNote(_this.editid, data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Updated Successfully!.'\n              });\n              _this.contactsservice.getContactByID(_this.document_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.contactsservice.createNote(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n            complete: () => {\n              _this.saving = false;\n              _this.visible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Created Successfully!.'\n              });\n              _this.contactsservice.getContactByID(_this.document_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            },\n            error: res => {\n              _this.saving = false;\n              _this.visible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.contactsservice.deleteNote(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.contactsservice.getContactByID(this.document_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    get f() {\n      return this.NoteForm.controls;\n    }\n    showDialog(position) {\n      this.position = position;\n      this.visible = true;\n      this.submitted = false;\n      this.NoteForm.reset();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ContactsNotesComponent_Factory(t) {\n        return new (t || ContactsNotesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ContactsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContactsNotesComponent,\n        selectors: [[\"app-contacts-notes\"]],\n        decls: 23,\n        vars: 26,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 2, \"width\", \"50rem\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\", 2, \"width\", \"7rem\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"font-medium\", \"note-text\", 3, \"innerHTML\"], [1, \"border-round-right-lg\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [4, \"ngIf\"]],\n        template: function ContactsNotesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n            i0.ɵɵlistener(\"click\", function ContactsNotesComponent_Template_p_button_click_5_listener() {\n              return ctx.showDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsNotesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n            i0.ɵɵlistener(\"onColReorder\", function ContactsNotesComponent_Template_p_table_onColReorder_8_listener($event) {\n              return ctx.onColumnReorder($event);\n            });\n            i0.ɵɵtemplate(9, ContactsNotesComponent_ng_template_9_Template, 10, 3, \"ng-template\", 8)(10, ContactsNotesComponent_ng_template_10_Template, 6, 2, \"ng-template\", 9)(11, ContactsNotesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, ContactsNotesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"p-dialog\", 12);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function ContactsNotesComponent_Template_p_dialog_visibleChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(14, ContactsNotesComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n            i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"div\", 15);\n            i0.ɵɵelement(18, \"p-editor\", 16);\n            i0.ɵɵtemplate(19, ContactsNotesComponent_div_19_Template, 2, 1, \"div\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 18)(21, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ContactsNotesComponent_Template_button_click_21_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function ContactsNotesComponent_Template_button_click_22_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c2, ctx.submitted && ctx.f[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.Tooltip, i3.PrimeTemplate, i6.Table, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i7.ButtonDirective, i7.Button, i8.Dialog, i9.Editor, i10.MultiSelect, i4.DatePipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .note-popup .p-dialog{margin-right:50px}  .note-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .note-popup .p-dialog .p-dialog-header h4{margin:0}  .note-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return ContactsNotesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}