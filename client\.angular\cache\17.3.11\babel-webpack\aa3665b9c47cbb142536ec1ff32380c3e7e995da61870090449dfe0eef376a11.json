{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { OpportunitiesRoutingModule } from './opportunities-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { DialogModule } from 'primeng/dialog';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { ToastModule } from 'primeng/toast';\nimport { TabViewModule } from 'primeng/tabview';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { EditorModule } from 'primeng/editor';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let OpportunitiesModule = /*#__PURE__*/(() => {\n  class OpportunitiesModule {\n    static {\n      this.ɵfac = function OpportunitiesModule_Factory(t) {\n        return new (t || OpportunitiesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: OpportunitiesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, OpportunitiesRoutingModule, FormsModule, ReactiveFormsModule, TableModule, ToastModule, ButtonModule, DropdownModule, DialogModule, EditorModule, NgSelectModule, InputSwitchModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, ConfirmDialogModule, CheckboxModule, MultiSelectModule, SharedModule]\n      });\n    }\n  }\n  return OpportunitiesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}