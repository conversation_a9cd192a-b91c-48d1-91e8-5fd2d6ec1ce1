{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountRoutingModule } from './account-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AccountComponent } from './account.component';\nimport { AccountDetailsComponent } from './account-details/account-details.component';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ToastModule } from 'primeng/toast';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { DialogModule } from 'primeng/dialog';\nimport { EditorModule } from 'primeng/editor';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { AccountSalesQuoteDetailsComponent } from './account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesOrderDetailsComponent } from './account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class AccountModule {\n  static {\n    this.ɵfac = function AccountModule_Factory(t) {\n      return new (t || AccountModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, NgSelectModule, ButtonModule, TabViewModule, ToastModule, CheckboxModule, ConfirmDialogModule, AutoCompleteModule, InputTextModule, ProgressSpinnerModule, SidebarModule, DialogModule, EditorModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccountModule, {\n    declarations: [AccountComponent, AccountDetailsComponent, AccountOverviewComponent, AccountContactsComponent, AccountSalesTeamComponent, AccountAiInsightsComponent, AccountOrganizationDataComponent, AccountAttachmentsComponent, AccountNotesComponent, AccountOpportunitiesComponent, AccountActivitiesComponent, AccountRelationshipsComponent, AccountTicketsComponent, AccountSalesQuotesComponent, AccountSalesOrdersComponent, AccountSalesQuoteDetailsComponent, AccountSalesOrderDetailsComponent],\n    imports: [CommonModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, NgSelectModule, ButtonModule, TabViewModule, ToastModule, CheckboxModule, ConfirmDialogModule, AutoCompleteModule, InputTextModule, ProgressSpinnerModule, SidebarModule, DialogModule, EditorModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgSelectModule", "AccountRoutingModule", "FormsModule", "ReactiveFormsModule", "BreadcrumbModule", "CalendarModule", "DropdownModule", "TableModule", "AccountComponent", "AccountDetailsComponent", "AutoCompleteModule", "ButtonModule", "InputTextModule", "TabViewModule", "ToastModule", "CheckboxModule", "SidebarModule", "DialogModule", "EditorModule", "MessageService", "ConfirmationService", "AccountOverviewComponent", "AccountContactsComponent", "AccountSalesTeamComponent", "AccountAiInsightsComponent", "AccountOrganizationDataComponent", "AccountAttachmentsComponent", "AccountNotesComponent", "AccountOpportunitiesComponent", "AccountActivitiesComponent", "AccountRelationshipsComponent", "AccountTicketsComponent", "AccountSalesQuotesComponent", "AccountSalesOrdersComponent", "ProgressSpinnerModule", "ConfirmDialogModule", "AccountSalesQuoteDetailsComponent", "AccountSalesOrderDetailsComponent", "SharedModule", "AccountModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { AccountRoutingModule } from './account-routing.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { TableModule } from 'primeng/table';\r\nimport { AccountComponent } from './account.component';\r\nimport { AccountDetailsComponent } from './account-details/account-details.component';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { AccountOverviewComponent } from './account-details/account-overview/account-overview.component';\r\nimport { AccountContactsComponent } from './account-details/account-contacts/account-contacts.component';\r\nimport { AccountSalesTeamComponent } from './account-details/account-sales-team/account-sales-team.component';\r\nimport { AccountAiInsightsComponent } from './account-details/account-ai-insights/account-ai-insights.component';\r\nimport { AccountOrganizationDataComponent } from './account-details/account-organization-data/account-organization-data.component';\r\nimport { AccountAttachmentsComponent } from './account-details/account-attachments/account-attachments.component';\r\nimport { AccountNotesComponent } from './account-details/account-notes/account-notes.component';\r\nimport { AccountOpportunitiesComponent } from './account-details/account-opportunities/account-opportunities.component';\r\nimport { AccountActivitiesComponent } from './account-details/account-activities/account-activities.component';\r\nimport { AccountRelationshipsComponent } from './account-details/account-relationships/account-relationships.component';\r\nimport { AccountTicketsComponent } from './account-details/account-tickets/account-tickets.component';\r\nimport { AccountSalesQuotesComponent } from './account-details/account-sales-quotes/account-sales-quotes.component';\r\nimport { AccountSalesOrdersComponent } from './account-details/account-sales-orders/account-sales-orders.component';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { AccountSalesQuoteDetailsComponent } from './account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\r\nimport { AccountSalesOrderDetailsComponent } from './account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n@NgModule({\r\n  declarations: [\r\n    AccountComponent,\r\n    AccountDetailsComponent,\r\n    AccountOverviewComponent,\r\n    AccountContactsComponent,\r\n    AccountSalesTeamComponent,\r\n    AccountAiInsightsComponent,\r\n    AccountOrganizationDataComponent,\r\n    AccountAttachmentsComponent,\r\n    AccountNotesComponent,\r\n    AccountOpportunitiesComponent,\r\n    AccountActivitiesComponent,\r\n    AccountRelationshipsComponent,\r\n    AccountTicketsComponent,\r\n    AccountSalesQuotesComponent,\r\n    AccountSalesOrdersComponent,\r\n    AccountSalesQuoteDetailsComponent,\r\n    AccountSalesOrderDetailsComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    AccountRoutingModule,\r\n    BreadcrumbModule,\r\n    DropdownModule,\r\n    TableModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    TabViewModule,\r\n    ToastModule,\r\n    CheckboxModule,\r\n    ConfirmDialogModule,\r\n    AutoCompleteModule,\r\n    InputTextModule,\r\n    ProgressSpinnerModule,\r\n    SidebarModule,\r\n    DialogModule,\r\n    EditorModule,\r\n    SharedModule\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class AccountModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,gCAAgC,QAAQ,iFAAiF;AAClI,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iCAAiC,QAAQ,0GAA0G;AAC5J,SAASC,iCAAiC,QAAQ,0GAA0G;AAC5J,SAASC,YAAY,QAAQ,8BAA8B;;AA8C3D,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAFb,CAACpB,cAAc,EAAEC,mBAAmB,CAAC;MAAAoB,OAAA,GAtB9CzC,YAAY,EACZE,oBAAoB,EACpBG,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXL,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdL,cAAc,EACdW,YAAY,EACZE,aAAa,EACbC,WAAW,EACXC,cAAc,EACdoB,mBAAmB,EACnBzB,kBAAkB,EAClBE,eAAe,EACfsB,qBAAqB,EACrBlB,aAAa,EACbC,YAAY,EACZC,YAAY,EACZoB,YAAY;IAAA;EAAA;;;2EAIHC,aAAa;IAAAE,YAAA,GA3CtBjC,gBAAgB,EAChBC,uBAAuB,EACvBY,wBAAwB,EACxBC,wBAAwB,EACxBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,2BAA2B,EAC3BC,qBAAqB,EACrBC,6BAA6B,EAC7BC,0BAA0B,EAC1BC,6BAA6B,EAC7BC,uBAAuB,EACvBC,2BAA2B,EAC3BC,2BAA2B,EAC3BG,iCAAiC,EACjCC,iCAAiC;IAAAG,OAAA,GAGjCzC,YAAY,EACZE,oBAAoB,EACpBG,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXL,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdL,cAAc,EACdW,YAAY,EACZE,aAAa,EACbC,WAAW,EACXC,cAAc,EACdoB,mBAAmB,EACnBzB,kBAAkB,EAClBE,eAAe,EACfsB,qBAAqB,EACrBlB,aAAa,EACbC,YAAY,EACZC,YAAY,EACZoB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}