import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject, takeUntil, Observable, map, of } from 'rxjs';
import { ActivitiesService } from '../../../activities.service';
import { FormGroup, FormBuilder } from '@angular/forms';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
  shareReplay,
  debounceTime,
} from 'rxjs/operators';
import { MessageService, ConfirmationService } from 'primeng/api';
import { NgSelectComponent } from '@ng-select/ng-select';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-task-involved-parties',
  templateUrl: './task-involved-parties.component.html',
  styleUrl: './task-involved-parties.component.scss',
})
export class TaskInvolvedPartiesComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @ViewChild('partySelect') partySelect!: NgSelectComponent;
  public involvedpartiesdetails: any[] = [];
  public activity_id: string = '';
  public addDialogVisible: boolean = false;
  public position: string = 'right';
  public submitted = false;
  public saving = false;
  public partyData$?: Observable<any[]>;
  public partyDataLoading = false;
  public partyInput$ = new Subject<string>();
  public InvolvedPartiesForm: FormGroup = this.formBuilder.group({
    role_code: [''],
    party_id: [''],
  });

  public role = [
    { label: 'Account', value: 'FLCU01' },
    { label: 'Contact', value: 'BUP001' },
    { label: 'Created By', value: 'YC' },
    { label: 'Inside Sales Rep', value: 'YI' },
    { label: 'Outside Sales Rep', value: 'YO' },
  ];

  constructor(
    private activitiesservice: ActivitiesService,
    private formBuilder: FormBuilder,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'bp_full_name', header: 'Name' },
    { field: 'mobile', header: 'Mobile' },
    { field: 'phone_number', header: 'Phone' },
    { field: 'email_address', header: 'E-Mail' },
    { field: 'address', header: 'Address' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.involvedpartiesdetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit(): void {
    this.loadPartyDataOnRoleChange();
    this.activitiesservice.activity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.activity_id = response.activity_id;
          const involvedParties = response?.involved_parties || [];

          const roleMap: any = {
            YI: 'Inside Sales Rep',
            //YC: 'Created By',
            YO: 'Outside Sales Rep',
          };

          const allowedPartnerFunctions = ['YI', 'YO'];

          this.involvedpartiesdetails = involvedParties.map((party: any) => {
            const addresses = party?.business_partner?.addresses || [];

            const address = addresses.find((addr: any) =>
              addr?.address_usages?.some(
                (usage: any) => usage.address_usage === 'XXDEFAULT'
              )
            );

            const processedAddress = address
              ? {
                  email_address: address?.emails?.[0]?.email_address || '-',
                  mobile:
                    address?.phone_numbers?.find(
                      (item: any) => item.phone_number_type === '3'
                    )?.phone_number || '-',
                  phone_number:
                    address?.phone_numbers?.find(
                      (item: any) => item.phone_number_type === '1'
                    )?.phone_number || '-',
                  address: [
                    address?.house_number,
                    address?.street_name,
                    address?.city_name,
                    address?.region,
                    address?.country,
                    address?.postal_code,
                  ]
                    .filter(Boolean)
                    .join(', '),
                }
              : {
                  email_address: '-',
                  mobile: '-',
                  phone_number: '-',
                  address: '-',
                };

            let roleMatch = '-';

            if (
              party?.role_code === 'FLCU01' ||
              party?.role_code === 'BUP001'
            ) {
              roleMatch =
                this.role.find(
                  (r) =>
                    r.value === party?.role_code || r.value === party?.bp_role
                )?.label || '-';
            } else {
              const partnerFn =
                party?.business_partner?.customer?.partner_functions?.find(
                  (p: any) =>
                    allowedPartnerFunctions.includes(p?.partner_function)
                );
              if (
                !partnerFn &&
                party?.role_code === 'BUP003' &&
                !['YI', 'YO'].includes(party?.function_code)
              ) {
                roleMatch = 'Created By';
              } else if (partnerFn) {
                roleMatch = roleMap[partnerFn.partner_function] || '-';
              }
            }

            return {
              ...party,
              bp_full_name: party?.business_partner?.bp_full_name || '-',
              role_code: roleMatch,
              ...processedAddress,
            };
          });
        }
      });

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }

  loadPartyDataOnRoleChange(): void {
    // Use valueChanges to watch for role_code changes
    this.partyData$ = this.InvolvedPartiesForm.get(
      'role_code'
    )!.valueChanges.pipe(
      tap(() => {
        this.clearPartyData(); // Extracted clearing logic
      }),
      switchMap((role: string) => {
        if (!role) return of([]); // Return empty array if no role selected

        return this.partyInput$.pipe(
          distinctUntilChanged(), // Only trigger when input changes
          debounceTime(300), // Prevent rapid search calls
          tap(() => (this.partyDataLoading = true)),
          switchMap((term: string) =>
            this.getPartnersByRoleAndSearch(role, term)
          )
        );
      }),
      shareReplay(1) // Ensure multiple subscribers get the same data
    );
  }

  // Extracted method to clear party data
  private clearPartyData(): void {
    this.InvolvedPartiesForm.get('party_id')?.reset();
    this.partyInput$.next(''); // Clear the search term
    if (this.partySelect) {
      this.partySelect.clearModel(); // Clear the search input
    }
  }

  // Method to get partners based on the role and search term
  private getPartnersByRoleAndSearch(role: string, term: string) {
    const params = this.getParamsByRole(role, term);
    if (!params) return of([]);

    return this.activitiesservice.getPartners(params).pipe(
      map((res: any) => res || []),
      catchError((error) => {
        console.error('Error fetching partners:', error); // Log error for debugging
        this.partyDataLoading = false;
        return of([]); // Return empty array in case of error
      }),
      tap(() => (this.partyDataLoading = false)) // Reset loading flag after fetching
    );
  }

  private getParamsByRole(role: string, term: string): any | null {
    if (!role) return null;

    const filters: any = {
      'filters[$or][0][bp_full_name][$containsi]': term,
      'filters[$or][1][bp_id][$containsi]': term,
      'filters[$or][2][first_name][$containsi]': term,
      'filters[$or][3][last_name][$containsi]': term,
    };

    // Define roleFilters with string keys and object values
    const roleFilters: Record<string, any> = {
      FLCU01: {
        'filters[roles][bp_role][$eq][0]': 'FLCU01',
        'filters[roles][bp_role][$eq][1]': 'FLCU00',
      },
      BUP001: {
        'filters[roles][bp_role][$eq]': 'BUP001',
      },
      YI: {
        'filters[roles][bp_role][$eq]': 'BUP003',
        'filters[customer][partner_functions][partner_function][$eq]': 'YI',
      },
      YO: {
        'filters[roles][bp_role][$eq]': 'BUP003',
        'filters[customer][partner_functions][partner_function][$eq]': 'YO',
      },
      YC: {
        'filters[roles][bp_role][$eq]': 'BUP003',
        //'filters[customer][partner_functions][partner_function][$eq]': 'YC',
      },
    };

    // Use the roleFilters map to get the filters for the specific role
    const roleSpecificFilters = roleFilters[role];

    if (roleSpecificFilters) {
      return {
        ...roleSpecificFilters,
        ...filters, // Merge common filters
      };
    }

    return null; // Return null if no filters are found for the given role
  }

  async onSubmit() {
    this.submitted = true;

    if (this.InvolvedPartiesForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.InvolvedPartiesForm.value };

    let role_code = value?.role_code;
    if (['YI', 'YO', 'YC'].includes(role_code)) {
      role_code = 'BUP003';
    }

    const data = {
      activity_id: this.activity_id,
      role_code: role_code,
      party_id: value?.party_id,
    };

    this.activitiesservice
      .createInvolvedParty(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          this.saving = false;
          this.addDialogVisible = false;
          this.InvolvedPartiesForm.reset();
          this.messageservice.add({
            severity: 'success',
            detail: 'Involved Party Added successFully!',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: (res: any) => {
          this.saving = false;
          this.addDialogVisible = true;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  confirmRemove(item: any) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item);
      },
    });
  }

  remove(item: any) {
    this.activitiesservice
      .deleteInvolvedParty(item.documentId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Record Deleted Successfully!',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  showNewDialog(position: string) {
    this.position = position;
    this.addDialogVisible = true;
    this.submitted = false;
    this.InvolvedPartiesForm.reset();
    if (this.role?.length > 0) {
      this.InvolvedPartiesForm.get('role_code')?.setValue(this.role[0].value);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
