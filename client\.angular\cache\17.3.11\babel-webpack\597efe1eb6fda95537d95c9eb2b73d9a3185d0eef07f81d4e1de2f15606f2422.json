{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InitialsPipe } from './initials.pipe';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [InitialsPipe],\n    imports: [CommonModule],\n    exports: [InitialsPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "InitialsPipe", "SharedModule", "declarations", "imports", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { InitialsPipe } from './initials.pipe';\r\n\r\n\r\n@NgModule({\r\n    declarations: [InitialsPipe],\r\n    imports: [CommonModule],\r\n    exports: [InitialsPipe]\r\n})\r\nexport class SharedModule { }"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;AAQ9C,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAHXF,YAAY;IAAA;EAAA;;;2EAGbE,YAAY;IAAAC,YAAA,GAJNF,YAAY;IAAAG,OAAA,GACjBJ,YAAY;IAAAK,OAAA,GACZJ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}