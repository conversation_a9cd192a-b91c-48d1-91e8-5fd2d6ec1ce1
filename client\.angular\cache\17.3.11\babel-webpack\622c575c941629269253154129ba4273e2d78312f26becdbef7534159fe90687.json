{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n  return symbolPrefix + name;\n}\nfunction initZone() {\n  const performance = global['performance'];\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n  mark('Zone');\n  class ZoneImpl {\n    // tslint:disable-next-line:require-internal-with-underscore\n    static {\n      this.__symbol__ = __symbol__;\n    }\n    static assertZonePatched() {\n      if (global['Promise'] !== patches['ZoneAwarePromise']) {\n        throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n      }\n    }\n    static get root() {\n      let zone = ZoneImpl.current;\n      while (zone.parent) {\n        zone = zone.parent;\n      }\n      return zone;\n    }\n    static get current() {\n      return _currentZoneFrame.zone;\n    }\n    static get currentTask() {\n      return _currentTask;\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    static __load_patch(name, fn, ignoreDuplicate = false) {\n      if (patches.hasOwnProperty(name)) {\n        // `checkDuplicate` option is defined from global variable\n        // so it works for all modules.\n        // `ignoreDuplicate` can work for the specified module\n        const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n        if (!ignoreDuplicate && checkDuplicate) {\n          throw Error('Already loaded patch: ' + name);\n        }\n      } else if (!global['__Zone_disable_' + name]) {\n        const perfName = 'Zone:' + name;\n        mark(perfName);\n        patches[name] = fn(global, ZoneImpl, _api);\n        performanceMeasure(perfName, perfName);\n      }\n    }\n    get parent() {\n      return this._parent;\n    }\n    get name() {\n      return this._name;\n    }\n    constructor(parent, zoneSpec) {\n      this._parent = parent;\n      this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n      this._properties = zoneSpec && zoneSpec.properties || {};\n      this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n    }\n    get(key) {\n      const zone = this.getZoneWith(key);\n      if (zone) return zone._properties[key];\n    }\n    getZoneWith(key) {\n      let current = this;\n      while (current) {\n        if (current._properties.hasOwnProperty(key)) {\n          return current;\n        }\n        current = current._parent;\n      }\n      return null;\n    }\n    fork(zoneSpec) {\n      if (!zoneSpec) throw new Error('ZoneSpec required!');\n      return this._zoneDelegate.fork(this, zoneSpec);\n    }\n    wrap(callback, source) {\n      if (typeof callback !== 'function') {\n        throw new Error('Expecting function got: ' + callback);\n      }\n      const _callback = this._zoneDelegate.intercept(this, callback, source);\n      const zone = this;\n      return function () {\n        return zone.runGuarded(_callback, this, arguments, source);\n      };\n    }\n    run(callback, applyThis, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runGuarded(callback, applyThis = null, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runTask(task, applyThis, applyArgs) {\n      if (task.zone != this) {\n        throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      }\n      const zoneTask = task;\n      // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n      // will run in notScheduled(canceled) state, we should not try to\n      // run such kind of task but just return\n      const {\n        type,\n        data: {\n          isPeriodic = false,\n          isRefreshable = false\n        } = {}\n      } = task;\n      if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n        return;\n      }\n      const reEntryGuard = task.state != running;\n      reEntryGuard && zoneTask._transitionTo(running, scheduled);\n      const previousTask = _currentTask;\n      _currentTask = zoneTask;\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n          task.cancelFn = undefined;\n        }\n        try {\n          return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        // if the task's state is notScheduled or unknown, then it has already been cancelled\n        // we should not reset the state to scheduled\n        const state = task.state;\n        if (state !== notScheduled && state !== unknown) {\n          if (type == eventTask || isPeriodic || isRefreshable && state === scheduling) {\n            reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n          } else {\n            const zoneDelegates = zoneTask._zoneDelegates;\n            this._updateTaskCount(zoneTask, -1);\n            reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n            if (isRefreshable) {\n              zoneTask._zoneDelegates = zoneDelegates;\n            }\n          }\n        }\n        _currentZoneFrame = _currentZoneFrame.parent;\n        _currentTask = previousTask;\n      }\n    }\n    scheduleTask(task) {\n      if (task.zone && task.zone !== this) {\n        // check if the task was rescheduled, the newZone\n        // should not be the children of the original zone\n        let newZone = this;\n        while (newZone) {\n          if (newZone === task.zone) {\n            throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n          }\n          newZone = newZone.parent;\n        }\n      }\n      task._transitionTo(scheduling, notScheduled);\n      const zoneDelegates = [];\n      task._zoneDelegates = zoneDelegates;\n      task._zone = this;\n      try {\n        task = this._zoneDelegate.scheduleTask(this, task);\n      } catch (err) {\n        // should set task's state to unknown when scheduleTask throw error\n        // because the err may from reschedule, so the fromState maybe notScheduled\n        task._transitionTo(unknown, scheduling, notScheduled);\n        // TODO: @JiaLiPassion, should we check the result from handleError?\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      if (task._zoneDelegates === zoneDelegates) {\n        // we have to check because internally the delegate can reschedule the task.\n        this._updateTaskCount(task, 1);\n      }\n      if (task.state == scheduling) {\n        task._transitionTo(scheduled, scheduling);\n      }\n      return task;\n    }\n    scheduleMicroTask(source, callback, data, customSchedule) {\n      return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n    }\n    scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n    }\n    scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n    }\n    cancelTask(task) {\n      if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      if (task.state !== scheduled && task.state !== running) {\n        return;\n      }\n      task._transitionTo(canceling, scheduled, running);\n      try {\n        this._zoneDelegate.cancelTask(this, task);\n      } catch (err) {\n        // if error occurs when cancelTask, transit the state to unknown\n        task._transitionTo(unknown, canceling);\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      this._updateTaskCount(task, -1);\n      task._transitionTo(notScheduled, canceling);\n      task.runCount = -1;\n      return task;\n    }\n    _updateTaskCount(task, count) {\n      const zoneDelegates = task._zoneDelegates;\n      if (count == -1) {\n        task._zoneDelegates = null;\n      }\n      for (let i = 0; i < zoneDelegates.length; i++) {\n        zoneDelegates[i]._updateTaskCount(task.type, count);\n      }\n    }\n  }\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n  class _ZoneDelegate {\n    get zone() {\n      return this._zone;\n    }\n    constructor(zone, parentDelegate, zoneSpec) {\n      this._taskCounts = {\n        'microTask': 0,\n        'macroTask': 0,\n        'eventTask': 0\n      };\n      this._zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = this._zone;\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this._zone;\n        }\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this._zone;\n        }\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this._zone;\n        }\n      }\n    }\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new ZoneImpl(targetZone, zoneSpec);\n    }\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        }\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n      return returnTask;\n    }\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n    cancelTask(targetZone, task) {\n      let value;\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n        value = task.cancelFn(task);\n      }\n      return value;\n    }\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this._zone, isEmpty);\n      }\n    }\n  }\n  class ZoneTask {\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zone = null;\n      this.runCount = 0;\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zoneDelegates = null;\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._state = 'notScheduled';\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n      this.callback = callback;\n      const self = this;\n      // TODO: @JiaLiPassion options should have interface\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n      _numberOfNestedTaskFrames++;\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n        _numberOfNestedTaskFrames--;\n      }\n    }\n    get zone() {\n      return this._zone;\n    }\n    get state() {\n      return this._state;\n    }\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n      }\n    }\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    }\n    // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const symbolSetTimeout = __symbol__('setTimeout');\n  const symbolPromise = __symbol__('Promise');\n  const symbolThen = __symbol__('then');\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n  function nativeScheduleMicroTask(func) {\n    if (!nativeMicroTaskQueuePromise) {\n      if (global[symbolPromise]) {\n        nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n      }\n    }\n    if (nativeMicroTaskQueuePromise) {\n      let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n      if (!nativeThen) {\n        // native Promise is not patchable, we need to use `then` directly\n        // issue 1078\n        nativeThen = nativeMicroTaskQueuePromise['then'];\n      }\n      nativeThen.call(nativeMicroTaskQueuePromise, func);\n    } else {\n      global[symbolSetTimeout](func, 0);\n    }\n  }\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      nativeScheduleMicroTask(drainMicroTaskQueue);\n    }\n    task && _microTaskQueue.push(task);\n  }\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n      _api.microtaskDrainDone();\n      _isDrainingMicrotaskQueue = false;\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n    scheduling = 'scheduling',\n    scheduled = 'scheduled',\n    running = 'running',\n    canceling = 'canceling',\n    unknown = 'unknown';\n  const microTask = 'microTask',\n    macroTask = 'macroTask',\n    eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop,\n    nativeScheduleMicroTask: nativeScheduleMicroTask\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new ZoneImpl(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n  function noop() {}\n  performanceMeasure('Zone', 'Zone');\n  return ZoneImpl;\n}\nfunction loadZone() {\n  // if global['Zone'] already exists (maybe zone.js was already loaded or\n  // some other lib also registered a global object named Zone), we may need\n  // to throw an error, but sometimes user may not want this error.\n  // For example,\n  // we have two web pages, page1 includes zone.js, page2 doesn't.\n  // and the 1st time user load page1 and page2, everything work fine,\n  // but when user load page2 again, error occurs because global['Zone'] already exists.\n  // so we add a flag to let user choose whether to throw this error or not.\n  // By default, if existing Zone is from zone.js, we will not throw the error.\n  const global = globalThis;\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n  if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n    throw new Error('Zone already loaded.');\n  }\n  // Initialize global `Zone` constant.\n  global['Zone'] ??= initZone();\n  return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n  return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n  const source = prototype.constructor['name'];\n  for (let i = 0; i < fnNames.length; i++) {\n    const name = fnNames[i];\n    const delegate = prototype[name];\n    if (delegate) {\n      const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n      if (!isPropertyWritable(prototypeDesc)) {\n        continue;\n      }\n      prototype[name] = (delegate => {\n        const patched = function () {\n          return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n        };\n        attachOriginToPatched(patched, delegate);\n        return patched;\n      })(delegate);\n    }\n  }\n}\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n  if (!event) {\n    return;\n  }\n  let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signature\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n    if (\n    // https://github.com/angular/angular/issues/47579\n    // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n    // This is the only specific case we should check for. The spec defines that the\n    // `returnValue` attribute represents the message to show the user. When the event\n    // is created, this attribute must be set to the empty string.\n    event.type === 'beforeunload' &&\n    // To prevent any breaking changes resulting from this change, given that\n    // it was already causing a significant number of failures in G3, we have hidden\n    // that behavior behind a global configuration flag. Consumers can enable this\n    // flag explicitly if they want the `beforeunload` event to be handled as defined\n    // in the specification.\n    _global[enableBeforeunloadSymbol] &&\n    // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n    // `typeof result` is a string.\n    typeof result === 'string') {\n      event.returnValue = result;\n    } else if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n  return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  }\n  // if the descriptor not exists or is not configurable\n  // just return\n  if (!desc || !desc.configurable) {\n    return;\n  }\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  }\n  // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set;\n  // slice(2) cuz 'onclick' -> 'click', etc\n  const eventName = prop.slice(2);\n  let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n  desc.set = function (newValue) {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return;\n    }\n    const previousValue = target[eventNameSymbol];\n    if (typeof previousValue === 'function') {\n      target.removeEventListener(eventName, wrapFn);\n    }\n    // issue #978, when onload handler was added before loading zone.js\n    // we should remove it with originalDescSet\n    originalDescSet && originalDescSet.call(target, null);\n    target[eventNameSymbol] = newValue;\n    if (typeof newValue === 'function') {\n      target.addEventListener(eventName, wrapFn, false);\n    }\n  };\n  // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return null;\n    }\n    const listener = target[eventNameSymbol];\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet.call(this);\n      if (value) {\n        desc.set.call(this, value);\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n        return value;\n      }\n    }\n    return null;\n  };\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n    for (const prop in obj) {\n      if (prop.slice(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n  const OriginalClass = _global[className];\n  if (!OriginalClass) return;\n  // keep original class in global\n  _global[zoneSymbol(className)] = OriginalClass;\n  _global[className] = function () {\n    const a = bindArguments(arguments, className);\n    switch (a.length) {\n      case 0:\n        this[originalInstanceKey] = new OriginalClass();\n        break;\n      case 1:\n        this[originalInstanceKey] = new OriginalClass(a[0]);\n        break;\n      case 2:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n        break;\n      case 3:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n        break;\n      case 4:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n        break;\n      default:\n        throw new Error('Arg list too long.');\n    }\n  };\n  // attach original delegate to patched function\n  attachOriginToPatched(_global[className], OriginalClass);\n  const instance = new OriginalClass(function () {});\n  let prop;\n  for (prop in instance) {\n    // https://bugs.webkit.org/show_bug.cgi?id=44721\n    if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n    (function (prop) {\n      if (typeof instance[prop] === 'function') {\n        _global[className].prototype[prop] = function () {\n          return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n        };\n      } else {\n        ObjectDefineProperty(_global[className].prototype, prop, {\n          set: function (fn) {\n            if (typeof fn === 'function') {\n              this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n              // keep callback in wrapped function so we can\n              // use it in Function.prototype.toString to return\n              // the native one.\n              attachOriginToPatched(this[originalInstanceKey][prop], fn);\n            } else {\n              this[originalInstanceKey][prop] = fn;\n            }\n          },\n          get: function () {\n            return this[originalInstanceKey][prop];\n          }\n        });\n      }\n    })(prop);\n  }\n  for (prop in OriginalClass) {\n    if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n      _global[className][prop] = OriginalClass[prop];\n    }\n  }\n}\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name];\n    // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n      attachOriginToPatched(proto[name], delegate);\n    }\n  }\n  return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n  try {\n    const ua = internalWindow.navigator.userAgent;\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n      return true;\n    }\n  } catch (error) {}\n  return false;\n}\nfunction isIEOrEdge() {\n  if (isDetectedIEOrEdge) {\n    return ieOrEdge;\n  }\n  isDetectedIEOrEdge = true;\n  try {\n    const ua = internalWindow.navigator.userAgent;\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n      ieOrEdge = true;\n    }\n  } catch (error) {}\n  return ieOrEdge;\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// Note that passive event listeners are now supported by most modern browsers,\n// including Chrome, Firefox, Safari, and Edge. There's a pending change that\n// would remove support for legacy browsers by zone.js. Removing `passiveSupported`\n// from the codebase will reduce the final code size for existing apps that still use zone.js.\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n  try {\n    const options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passiveSupported = true;\n      }\n    });\n    // Note: We pass the `options` object as the event handler too. This is not compatible with the\n    // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n    // without an actual handler.\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames[eventName] = {};\n  zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n    const delegate = task.callback;\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n      task.originalDelegate = delegate;\n    }\n    // invoke static task.invoke\n    // need to try/catch error here, otherwise, the error in one event listener\n    // will break the executions of the other event listeners. Also error will\n    // not remove the event listener when `once` options is true.\n    let error;\n    try {\n      task.invoke(task, target, [event]);\n    } catch (err) {\n      error = err;\n    }\n    const options = task.options;\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n    return error;\n  };\n  function globalCallback(context, event, isCapture) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n      return;\n    }\n    // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n    const target = context || event.target || _global;\n    const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n    if (tasks) {\n      const errors = [];\n      // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n      if (tasks.length === 1) {\n        const err = invokeTask(tasks[0], target, event);\n        err && errors.push(err);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n          const err = invokeTask(copyTasks[i], target, event);\n          err && errors.push(err);\n        }\n      }\n      // Since there is only one error, we don't need to schedule microTask\n      // to throw the error.\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        for (let i = 0; i < errors.length; i++) {\n          const err = errors[i];\n          api.nativeScheduleMicroTask(() => {\n            throw err;\n          });\n        }\n      }\n    }\n  }\n  // global shared zoneAwareCallback to handle all event callback with capture = false\n  const globalZoneAwareCallback = function (event) {\n    return globalCallback(this, event, false);\n  };\n  // global shared zoneAwareCallback to handle all event callback with capture = true\n  const globalZoneAwareCaptureCallback = function (event) {\n    return globalCallback(this, event, true);\n  };\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n    let useGlobalCallback = true;\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n    let returnTarget = false;\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n    let proto = obj;\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n    if (!proto) {\n      return false;\n    }\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n    const eventNameToString = patchOptions && patchOptions.eventNameToString;\n    // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n    // eliminating the need to create a new object solely for passing data.\n    // WARNING: This object has a static lifetime, meaning it is not created\n    // each time `addEventListener` is called. It is instantiated only once\n    // and captured by reference inside the `addEventListener` and\n    // `removeEventListener` functions. Do not add any new properties to this\n    // object, as doing so would necessitate maintaining the information\n    // between `addEventListener` calls.\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n    function buildEventListenerOptions(options, passive) {\n      if (!passiveSupported && typeof options === 'object' && options) {\n        // doesn't support passive but user want to pass an object as options.\n        // this will not work on some old browser, so we just pass a boolean\n        // as useCapture parameter\n        return !!options.capture;\n      }\n      if (!passiveSupported || !passive) {\n        return options;\n      }\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n      if (typeof options === 'object' && options.passive !== false) {\n        return {\n          ...options,\n          passive: true\n        };\n      }\n      return options;\n    }\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n    /**\n     * In the context of events and listeners, this function will be\n     * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n     * Cancelling a task is primarily used to remove event listeners from\n     * the task target.\n     */\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames[task.eventName];\n        let symbolEventName;\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n            if (existingTask === task) {\n              existingTasks.splice(i, 1);\n              // set isRemoved to data for faster invokeTask check\n              task.isRemoved = true;\n              if (task.removeAbortListener) {\n                task.removeAbortListener();\n                task.removeAbortListener = null;\n              }\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n              break;\n            }\n          }\n        }\n      }\n      // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n      if (!task.allRemoved) {\n        return;\n      }\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n    const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n    function copyEventListenerOptions(options) {\n      if (typeof options === 'object' && options !== null) {\n        // We need to destructure the target `options` object since it may\n        // be frozen or sealed (possibly provided implicitly by a third-party\n        // library), or its properties may be readonly.\n        const newOptions = {\n          ...options\n        };\n        // The `signal` option was recently introduced, which caused regressions in\n        // third-party scenarios where `AbortController` was directly provided to\n        // `addEventListener` as options. For instance, in cases like\n        // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n        // which is valid because `AbortController` includes a `signal` getter, spreading\n        // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n        // isn't feasible since `AbortController` is a built-in object type, and attempting\n        // to create a new object directly with it as the prototype might result in\n        // unexpected behavior.\n        if (options.signal) {\n          newOptions.signal = options.signal;\n        }\n        return newOptions;\n      }\n      return options;\n    }\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        let delegate = arguments[1];\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        }\n        // don't create the bind delegate function for handleEvent\n        // case here to improve addEventListener performance\n        // we will create the bind delegate when invoke\n        let isHandleEvent = false;\n        if (typeof delegate !== 'function') {\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n          isHandleEvent = true;\n        }\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n        const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n        const signal = options?.signal;\n        if (signal?.aborted) {\n          // the signal is an aborted one, just return without attaching the event listener.\n          return;\n        }\n        if (unpatchedEvents) {\n          // check unpatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames[eventName];\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames[eventName];\n        }\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        }\n        // In the code below, `options` should no longer be reassigned; instead, it\n        // should only be mutated. This is because we pass that object to the native\n        // `addEventListener`.\n        // It's generally recommended to use the same object reference for options.\n        // This ensures consistency and avoids potential issues.\n        taskData.options = options;\n        if (once) {\n          // When using `addEventListener` with the `once` option, we don't pass\n          // the `once` option directly to the native `addEventListener` method.\n          // Instead, we keep the `once` setting and handle it ourselves.\n          taskData.options.once = false;\n        }\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n        // keep taskData into data to allow onScheduleEventTask to access the task information\n        if (data) {\n          data.taskData = taskData;\n        }\n        if (signal) {\n          // When using `addEventListener` with the `signal` option, we don't pass\n          // the `signal` option directly to the native `addEventListener` method.\n          // Instead, we keep the `signal` setting and handle it ourselves.\n          taskData.options.signal = undefined;\n        }\n        // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n        // which in turn calls the native `addEventListener`. This is why `taskData.options`\n        // is updated before scheduling the task, as `customScheduleGlobal` uses\n        // `taskData.options` to pass it to the native `addEventListener`.\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n        if (signal) {\n          // after task is scheduled, we need to store the signal back to task.options\n          taskData.options.signal = signal;\n          // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n          // primarily used for preventing strong references cycles. `onAbort` is always reachable\n          // as it's an event listener, so its closure retains a strong reference to the `task`.\n          const onAbort = () => task.zone.cancelTask(task);\n          nativeListener.call(signal, 'abort', onAbort, {\n            once: true\n          });\n          // We need to remove the `abort` listener when the event listener is going to be removed,\n          // as it creates a closure that captures `task`. This closure retains a reference to the\n          // `task` object even after it goes out of scope, preventing `task` from being garbage\n          // collected.\n          task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n        }\n        // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n        taskData.target = null;\n        // need to clear up taskData because it is a global object\n        if (data) {\n          data.taskData = null;\n        }\n        // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n        if (once) {\n          taskData.options.once = true;\n        }\n        if (!(!passiveSupported && typeof task.options === 'boolean')) {\n          // if not support passive, and we pass an option object\n          // to addEventListener, we should save the options to task\n          task.options = options;\n        }\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n        if (isHandleEvent) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n      const symbolEventNames = zoneSymbolEventNames[eventName];\n      let symbolEventName;\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n      const existingTasks = symbolEventName && target[symbolEventName];\n      // `existingTasks` may not exist if the `addEventListener` was called before\n      // it was patched by zone.js. Please refer to the attached issue for\n      // clarification, particularly after the `if` condition, before calling\n      // the native `removeEventListener`.\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1);\n            // set isRemoved to data for faster invokeTask check\n            existingTask.isRemoved = true;\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null;\n              // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates with capture=false were removed\n              // https:// github.com/angular/angular/issues/31643\n              // https://github.com/angular/angular/issues/54581\n              if (!capture && typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            }\n            // In all other conditions, when `addEventListener` is called after being\n            // patched by zone.js, we would always find an event task on the `EventTarget`.\n            // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n            // which ultimately removes an event listener and cleans up the abort listener\n            // (if an `AbortSignal` was provided when scheduling a task).\n            existingTask.zone.cancelTask(existingTask);\n            if (returnTarget) {\n              return target;\n            }\n            return;\n          }\n        }\n      }\n      // https://github.com/angular/zone.js/issues/930\n      // We may encounter a situation where the `addEventListener` was\n      // called on the event target before zone.js is loaded, resulting\n      // in no task being stored on the event target due to its invocation\n      // of the native implementation. In this scenario, we simply need to\n      // invoke the native `removeEventListener`.\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n      return listeners;\n    };\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (!eventName) {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1];\n          // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        }\n        // remove removeListener listener finally\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        const symbolEventNames = zoneSymbolEventNames[eventName];\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n          if (tasks) {\n            const removeTasks = tasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n      if (returnTarget) {\n        return this;\n      }\n    };\n    // for native toString patch\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n    return true;\n  }\n  let results = [];\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n  return results;\n}\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n    return foundTasks;\n  }\n  let symbolEventName = zoneSymbolEventNames[eventName];\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames[eventName];\n  }\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\nfunction patchEventPrototype(global, api) {\n  const Event = global['Event'];\n  if (Event && Event.prototype) {\n    api.patchMethod(Event.prototype, 'stopImmediatePropagation', delegate => function (self, args) {\n      self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n      // we need to call the native stopImmediatePropagation\n      // in case in some hybrid application, some part of\n      // application will be controlled by zone, some are not\n      delegate && delegate.apply(self, args);\n    });\n  }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n    const handleOrId = setNative.apply(window, data.args);\n    // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n    // to this so that we do not cause potentally leaks when using `setTimeout`\n    // since this can be periodic when using `.refresh`.\n    if (isNumber(handleOrId)) {\n      data.handleId = handleOrId;\n    } else {\n      data.handle = handleOrId;\n      // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n      data.isRefreshable = isFunction(handleOrId.refresh);\n    }\n    return task;\n  }\n  function clearTask(task) {\n    const {\n      handle,\n      handleId\n    } = task.data;\n    return clearNative.call(window, handle ?? handleId);\n  }\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (isFunction(args[0])) {\n      const options = {\n        isRefreshable: false,\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          const {\n            handle,\n            handleId,\n            isPeriodic,\n            isRefreshable\n          } = options;\n          if (!isPeriodic && !isRefreshable) {\n            if (handleId) {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[handleId];\n            } else if (handle) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              handle[taskSymbol] = null;\n            }\n          }\n        }\n      };\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n      if (!task) {\n        return task;\n      }\n      // Node.js must additionally support the ref and unref functions.\n      const {\n        handleId,\n        handle,\n        isRefreshable,\n        isPeriodic\n      } = task.data;\n      if (handleId) {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handleId] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n        if (isRefreshable && !isPeriodic) {\n          const originalRefresh = handle.refresh;\n          handle.refresh = function () {\n            const {\n              zone,\n              state\n            } = task;\n            if (state === 'notScheduled') {\n              task._state = 'scheduled';\n              zone._updateTaskCount(task, 1);\n            } else if (state === 'running') {\n              task._state = 'scheduling';\n            }\n            return originalRefresh.call(this);\n          };\n        }\n      }\n      return handle ?? handleId ?? task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n    if (isNumber(id)) {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n      delete tasksByHandleId[id];\n    } else {\n      // nodejs env ?? other environments.\n      task = id?.[taskSymbol];\n      if (task) {\n        id[taskSymbol] = null;\n      } else {\n        task = id;\n      }\n    }\n    if (task?.type) {\n      if (task.cancelFn) {\n        // Do not cancel already canceled functions\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\nfunction patchCustomElements(_global, api) {\n  const {\n    isBrowser,\n    isMix\n  } = api.getGlobalObjects();\n  if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n    return;\n  }\n  // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n  const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback', 'formAssociatedCallback', 'formDisabledCallback', 'formResetCallback', 'formStateRestoreCallback'];\n  api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\nfunction eventTargetPatch(_global, api) {\n  if (Zone[api.symbol('patchEventTarget')]) {\n    // EventTarget is already patched.\n    return;\n  }\n  const {\n    eventNames,\n    zoneSymbolEventNames,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX\n  } = api.getGlobalObjects();\n  //  predefine all __zone_symbol__ + eventName + true/false string\n  for (let i = 0; i < eventNames.length; i++) {\n    const eventName = eventNames[i];\n    const falseEventName = eventName + FALSE_STR;\n    const trueEventName = eventName + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n  const EVENT_TARGET = _global['EventTarget'];\n  if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n    return;\n  }\n  api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n  return true;\n}\nfunction patchEvent(global, api) {\n  api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n  if (!ignoreProperties || ignoreProperties.length === 0) {\n    return onProperties;\n  }\n  const tip = ignoreProperties.filter(ip => ip.target === target);\n  if (!tip || tip.length === 0) {\n    return onProperties;\n  }\n  const targetIgnoreProperties = tip[0].ignoreProperties;\n  return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n  // check whether target is available, sometimes target will be undefined\n  // because different browser or some 3rd party plugin.\n  if (!target) {\n    return;\n  }\n  const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n  patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n  return Object.getOwnPropertyNames(target).filter(name => name.startsWith('on') && name.length > 2).map(name => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n  if (isNode && !isMix) {\n    return;\n  }\n  if (Zone[api.symbol('patchEvents')]) {\n    // events are already been patched by legacy patch.\n    return;\n  }\n  const ignoreProperties = _global['__Zone_ignore_on_properties'];\n  // for browsers that we can patch the descriptor:  Chrome & Firefox\n  let patchTargets = [];\n  if (isBrowser) {\n    const internalWindow = window;\n    patchTargets = patchTargets.concat(['Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement', 'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker']);\n    const ignoreErrorProperties = isIE() ? [{\n      target: internalWindow,\n      ignoreProperties: ['error']\n    }] : [];\n    // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n    // so we need to pass WindowPrototype to check onProp exist or not\n    patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n  }\n  patchTargets = patchTargets.concat(['XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest', 'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket']);\n  for (let i = 0; i < patchTargets.length; i++) {\n    const target = _global[patchTargets[i]];\n    target && target.prototype && patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n  }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n  Zone.__load_patch('legacy', global => {\n    const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n      legacyPatch();\n    }\n  });\n  Zone.__load_patch('timers', global => {\n    const set = 'set';\n    const clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n  });\n  Zone.__load_patch('requestAnimationFrame', global => {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n  });\n  Zone.__load_patch('blocking', (global, Zone) => {\n    const blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (let i = 0; i < blockingMethods.length; i++) {\n      const name = blockingMethods[i];\n      patchMethod(global, name, (delegate, symbol, name) => {\n        return function (s, args) {\n          return Zone.current.run(delegate, global, args, name);\n        };\n      });\n    }\n  });\n  Zone.__load_patch('EventTarget', (global, Zone, api) => {\n    patchEvent(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n      api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n  });\n  Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n  });\n  Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n    patchClass('IntersectionObserver');\n  });\n  Zone.__load_patch('FileReader', (global, Zone, api) => {\n    patchClass('FileReader');\n  });\n  Zone.__load_patch('on_property', (global, Zone, api) => {\n    propertyDescriptorPatch(api, global);\n  });\n  Zone.__load_patch('customElements', (global, Zone, api) => {\n    patchCustomElements(global, api);\n  });\n  Zone.__load_patch('XHR', (global, Zone) => {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    const XHR_TASK = zoneSymbol('xhrTask');\n    const XHR_SYNC = zoneSymbol('xhrSync');\n    const XHR_LISTENER = zoneSymbol('xhrListener');\n    const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    const XHR_URL = zoneSymbol('xhrURL');\n    const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n      const XMLHttpRequest = window['XMLHttpRequest'];\n      if (!XMLHttpRequest) {\n        // XMLHttpRequest is not available in service worker\n        return;\n      }\n      const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n      function findPendingTask(target) {\n        return target[XHR_TASK];\n      }\n      let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n      let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      if (!oriAddListener) {\n        const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget) {\n          const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n          oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n      }\n      const READY_STATE_CHANGE = 'readystatechange';\n      const SCHEDULED = 'scheduled';\n      function scheduleTask(task) {\n        const data = task.data;\n        const target = data.target;\n        target[XHR_SCHEDULED] = false;\n        target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n        // remove existing event listener\n        const listener = target[XHR_LISTENER];\n        if (!oriAddListener) {\n          oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n        if (listener) {\n          oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n        }\n        const newListener = target[XHR_LISTENER] = () => {\n          if (target.readyState === target.DONE) {\n            // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n            // readyState=4 multiple times, so we need to check task state here\n            if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n              // check whether the xhr has registered onload listener\n              // if that is the case, the task should invoke after all\n              // onload listeners finish.\n              // Also if the request failed without response (status = 0), the load event handler\n              // will not be triggered, in that case, we should also invoke the placeholder callback\n              // to close the XMLHttpRequest::send macroTask.\n              // https://github.com/angular/angular/issues/38795\n              const loadTasks = target[Zone.__symbol__('loadfalse')];\n              if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                const oriInvoke = task.invoke;\n                task.invoke = function () {\n                  // need to load the tasks again, because in other\n                  // load listener, they may remove themselves\n                  const loadTasks = target[Zone.__symbol__('loadfalse')];\n                  for (let i = 0; i < loadTasks.length; i++) {\n                    if (loadTasks[i] === task) {\n                      loadTasks.splice(i, 1);\n                    }\n                  }\n                  if (!data.aborted && task.state === SCHEDULED) {\n                    oriInvoke.call(task);\n                  }\n                };\n                loadTasks.push(task);\n              } else {\n                task.invoke();\n              }\n            } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n              // error occurs when xhr.send()\n              target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n            }\n          }\n        };\n        oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n        const storedTask = target[XHR_TASK];\n        if (!storedTask) {\n          target[XHR_TASK] = task;\n        }\n        sendNative.apply(target, data.args);\n        target[XHR_SCHEDULED] = true;\n        return task;\n      }\n      function placeholderCallback() {}\n      function clearTask(task) {\n        const data = task.data;\n        // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n        // to prevent it from firing. So instead, we store info for the event listener.\n        data.aborted = true;\n        return abortNative.apply(data.target, data.args);\n      }\n      const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n        self[XHR_SYNC] = args[2] == false;\n        self[XHR_URL] = args[1];\n        return openNative.apply(self, args);\n      });\n      const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n      const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n      const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n      const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n        if (Zone.current[fetchTaskScheduling] === true) {\n          // a fetch is scheduling, so we are using xhr to polyfill fetch\n          // and because we already schedule macroTask for fetch, we should\n          // not schedule a macroTask for xhr again\n          return sendNative.apply(self, args);\n        }\n        if (self[XHR_SYNC]) {\n          // if the XHR is sync there is no task to schedule, just execute the code.\n          return sendNative.apply(self, args);\n        } else {\n          const options = {\n            target: self,\n            url: self[XHR_URL],\n            isPeriodic: false,\n            args: args,\n            aborted: false\n          };\n          const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n          if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n            // xhr request throw error when send\n            // we should invoke task instead of leaving a scheduled\n            // pending macroTask\n            task.invoke();\n          }\n        }\n      });\n      const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n        const task = findPendingTask(self);\n        if (task && typeof task.type == 'string') {\n          // If the XHR has already completed, do nothing.\n          // If the XHR has already been aborted, do nothing.\n          // Fix #569, call abort multiple times before done will cause\n          // macroTask task count be negative number\n          if (task.cancelFn == null || task.data && task.data.aborted) {\n            return;\n          }\n          task.zone.cancelTask(task);\n        } else if (Zone.current[fetchTaskAborting] === true) {\n          // the abort is called from fetch polyfill, we need to call native abort of XHR.\n          return abortNative.apply(self, args);\n        }\n        // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n        // task\n        // to cancel. Do nothing.\n      });\n    }\n  });\n  Zone.__load_patch('geolocation', global => {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n      patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n  });\n  Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n      return function (e) {\n        const eventTasks = findEventTasks(global, evtName);\n        eventTasks.forEach(eventTask => {\n          // windows has added unhandledrejection event listener\n          // trigger the event listener\n          const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n          if (PromiseRejectionEvent) {\n            const evt = new PromiseRejectionEvent(evtName, {\n              promise: e.promise,\n              reason: e.rejection\n            });\n            eventTask.invoke(evt);\n          }\n        });\n      };\n    }\n    if (global['PromiseRejectionEvent']) {\n      Zone[zoneSymbol('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n      Zone[zoneSymbol('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n    }\n  });\n  Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    patchQueueMicrotask(global, api);\n  });\n}\nfunction patchPromise(Zone) {\n  Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n      if (obj && obj.toString === Object.prototype.toString) {\n        const className = obj.constructor && obj.constructor.name;\n        return (className ? className : '') + ': ' + JSON.stringify(obj);\n      }\n      return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    const creationTrace = '__creationTrace__';\n    api.onUnhandledError = e => {\n      if (api.showUncaughtError()) {\n        const rejection = e && e.rejection;\n        if (rejection) {\n          console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n        } else {\n          console.error(e);\n        }\n      }\n    };\n    api.microtaskDrainDone = () => {\n      while (_uncaughtPromiseErrors.length) {\n        const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n        try {\n          uncaughtPromiseError.zone.runGuarded(() => {\n            if (uncaughtPromiseError.throwOriginal) {\n              throw uncaughtPromiseError.rejection;\n            }\n            throw uncaughtPromiseError;\n          });\n        } catch (error) {\n          handleUnhandledRejection(error);\n        }\n      }\n    };\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n      api.onUnhandledError(e);\n      try {\n        const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n        if (typeof handler === 'function') {\n          handler.call(this, e);\n        }\n      } catch (err) {}\n    }\n    function isThenable(value) {\n      return value && value.then;\n    }\n    function forwardResolution(value) {\n      return value;\n    }\n    function forwardRejection(rejection) {\n      return ZoneAwarePromise.reject(rejection);\n    }\n    const symbolState = __symbol__('state');\n    const symbolValue = __symbol__('value');\n    const symbolFinally = __symbol__('finally');\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n      return v => {\n        try {\n          resolvePromise(promise, state, v);\n        } catch (err) {\n          resolvePromise(promise, false, err);\n        }\n        // Do not return value or you will break the Promise spec.\n      };\n    }\n    const once = function () {\n      let wasCalled = false;\n      return function wrapper(wrappedFunction) {\n        return function () {\n          if (wasCalled) {\n            return;\n          }\n          wasCalled = true;\n          wrappedFunction.apply(null, arguments);\n        };\n      };\n    };\n    const TYPE_ERROR = 'Promise resolved with itself';\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n      const onceWrapper = once();\n      if (promise === value) {\n        throw new TypeError(TYPE_ERROR);\n      }\n      if (promise[symbolState] === UNRESOLVED) {\n        // should only get value.then once based on promise spec.\n        let then = null;\n        try {\n          if (typeof value === 'object' || typeof value === 'function') {\n            then = value && value.then;\n          }\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n          return promise;\n        }\n        // if (value instanceof ZoneAwarePromise) {\n        if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n          clearRejectedNoCatch(value);\n          resolvePromise(promise, value[symbolState], value[symbolValue]);\n        } else if (state !== REJECTED && typeof then === 'function') {\n          try {\n            then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n          } catch (err) {\n            onceWrapper(() => {\n              resolvePromise(promise, false, err);\n            })();\n          }\n        } else {\n          promise[symbolState] = state;\n          const queue = promise[symbolValue];\n          promise[symbolValue] = value;\n          if (promise[symbolFinally] === symbolFinally) {\n            // the promise is generated by Promise.prototype.finally\n            if (state === RESOLVED) {\n              // the state is resolved, should ignore the value\n              // and use parent promise value\n              promise[symbolState] = promise[symbolParentPromiseState];\n              promise[symbolValue] = promise[symbolParentPromiseValue];\n            }\n          }\n          // record task information in value when error occurs, so we can\n          // do some additional work such as render longStackTrace\n          if (state === REJECTED && value instanceof Error) {\n            // check if longStackTraceZone is here\n            const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n            if (trace) {\n              // only keep the long stack trace into error when in longStackTraceZone\n              ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                configurable: true,\n                enumerable: false,\n                writable: true,\n                value: trace\n              });\n            }\n          }\n          for (let i = 0; i < queue.length;) {\n            scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n          }\n          if (queue.length == 0 && state == REJECTED) {\n            promise[symbolState] = REJECTED_NO_CATCH;\n            let uncaughtPromiseError = value;\n            try {\n              // Here we throws a new Error to print more readable error log\n              // and if the value is not an error, zone.js builds an `Error`\n              // Object here to attach the stack information.\n              throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n            } catch (err) {\n              uncaughtPromiseError = err;\n            }\n            if (isDisableWrappingUncaughtPromiseRejection) {\n              // If disable wrapping uncaught promise reject\n              // use the value instead of wrapping it.\n              uncaughtPromiseError.throwOriginal = true;\n            }\n            uncaughtPromiseError.rejection = value;\n            uncaughtPromiseError.promise = promise;\n            uncaughtPromiseError.zone = Zone.current;\n            uncaughtPromiseError.task = Zone.currentTask;\n            _uncaughtPromiseErrors.push(uncaughtPromiseError);\n            api.scheduleMicroTask(); // to make sure that it is running\n          }\n        }\n      }\n      // Resolving an already resolved promise is a noop.\n      return promise;\n    }\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n      if (promise[symbolState] === REJECTED_NO_CATCH) {\n        // if the promise is rejected no catch status\n        // and queue.length > 0, means there is a error handler\n        // here to handle the rejected promise, we should trigger\n        // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n        // eventHandler\n        try {\n          const handler = Zone[REJECTION_HANDLED_HANDLER];\n          if (handler && typeof handler === 'function') {\n            handler.call(this, {\n              rejection: promise[symbolValue],\n              promise: promise\n            });\n          }\n        } catch (err) {}\n        promise[symbolState] = REJECTED;\n        for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n          if (promise === _uncaughtPromiseErrors[i].promise) {\n            _uncaughtPromiseErrors.splice(i, 1);\n          }\n        }\n      }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n      clearRejectedNoCatch(promise);\n      const promiseState = promise[symbolState];\n      const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n      zone.scheduleMicroTask(source, () => {\n        try {\n          const parentPromiseValue = promise[symbolValue];\n          const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n          if (isFinallyPromise) {\n            // if the promise is generated from finally call, keep parent promise's state and value\n            chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n            chainPromise[symbolParentPromiseState] = promiseState;\n          }\n          // should not pass value to finally callback\n          const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n          resolvePromise(chainPromise, true, value);\n        } catch (error) {\n          // if error occurs, should always return this error\n          resolvePromise(chainPromise, false, error);\n        }\n      }, chainPromise);\n    }\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    const noop = function () {};\n    const AggregateError = global.AggregateError;\n    class ZoneAwarePromise {\n      static toString() {\n        return ZONE_AWARE_PROMISE_TO_STRING;\n      }\n      static resolve(value) {\n        if (value instanceof ZoneAwarePromise) {\n          return value;\n        }\n        return resolvePromise(new this(null), RESOLVED, value);\n      }\n      static reject(error) {\n        return resolvePromise(new this(null), REJECTED, error);\n      }\n      static withResolvers() {\n        const result = {};\n        result.promise = new ZoneAwarePromise((res, rej) => {\n          result.resolve = res;\n          result.reject = rej;\n        });\n        return result;\n      }\n      static any(values) {\n        if (!values || typeof values[Symbol.iterator] !== 'function') {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        const promises = [];\n        let count = 0;\n        try {\n          for (let v of values) {\n            count++;\n            promises.push(ZoneAwarePromise.resolve(v));\n          }\n        } catch (err) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        if (count === 0) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n        let finished = false;\n        const errors = [];\n        return new ZoneAwarePromise((resolve, reject) => {\n          for (let i = 0; i < promises.length; i++) {\n            promises[i].then(v => {\n              if (finished) {\n                return;\n              }\n              finished = true;\n              resolve(v);\n            }, err => {\n              errors.push(err);\n              count--;\n              if (count === 0) {\n                finished = true;\n                reject(new AggregateError(errors, 'All promises were rejected'));\n              }\n            });\n          }\n        });\n      }\n      static race(values) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n        function onResolve(value) {\n          resolve(value);\n        }\n        function onReject(error) {\n          reject(error);\n        }\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n          value.then(onResolve, onReject);\n        }\n        return promise;\n      }\n      static all(values) {\n        return ZoneAwarePromise.allWithCallback(values);\n      }\n      static allSettled(values) {\n        const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n        return P.allWithCallback(values, {\n          thenCallback: value => ({\n            status: 'fulfilled',\n            value\n          }),\n          errorCallback: err => ({\n            status: 'rejected',\n            reason: err\n          })\n        });\n      }\n      static allWithCallback(values, callback) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n        // Start at 2 to prevent prematurely resolving if .then is called immediately.\n        let unresolvedCount = 2;\n        let valueIndex = 0;\n        const resolvedValues = [];\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n          const curValueIndex = valueIndex;\n          try {\n            value.then(value => {\n              resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n              unresolvedCount--;\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }, err => {\n              if (!callback) {\n                reject(err);\n              } else {\n                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                unresolvedCount--;\n                if (unresolvedCount === 0) {\n                  resolve(resolvedValues);\n                }\n              }\n            });\n          } catch (thenErr) {\n            reject(thenErr);\n          }\n          unresolvedCount++;\n          valueIndex++;\n        }\n        // Make the unresolvedCount zero-based again.\n        unresolvedCount -= 2;\n        if (unresolvedCount === 0) {\n          resolve(resolvedValues);\n        }\n        return promise;\n      }\n      constructor(executor) {\n        const promise = this;\n        if (!(promise instanceof ZoneAwarePromise)) {\n          throw new Error('Must be an instanceof Promise.');\n        }\n        promise[symbolState] = UNRESOLVED;\n        promise[symbolValue] = []; // queue;\n        try {\n          const onceWrapper = once();\n          executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n        } catch (error) {\n          resolvePromise(promise, false, error);\n        }\n      }\n      get [Symbol.toStringTag]() {\n        return 'Promise';\n      }\n      get [Symbol.species]() {\n        return ZoneAwarePromise;\n      }\n      then(onFulfilled, onRejected) {\n        // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n        // may be an object without a prototype (created through `Object.create(null)`); thus\n        // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n        // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n        // object and copies promise properties into that object (within the `getOrCreateLoad`\n        // function). The zone.js then checks if the resolved value has the `then` method and\n        // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n        // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n        let C = this.constructor?.[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = this.constructor || ZoneAwarePromise;\n        }\n        const chainPromise = new C(noop);\n        const zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n        }\n        return chainPromise;\n      }\n      catch(onRejected) {\n        return this.then(null, onRejected);\n      }\n      finally(onFinally) {\n        // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n        let C = this.constructor?.[Symbol.species];\n        if (!C || typeof C !== 'function') {\n          C = ZoneAwarePromise;\n        }\n        const chainPromise = new C(noop);\n        chainPromise[symbolFinally] = symbolFinally;\n        const zone = Zone.current;\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n        }\n        return chainPromise;\n      }\n    }\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    const symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n      const proto = Ctor.prototype;\n      const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n      if (prop && (prop.writable === false || !prop.configurable)) {\n        // check Ctor.prototype.then propertyDescriptor is writable or not\n        // in meteor env, writable is false, we should ignore such case\n        return;\n      }\n      const originalThen = proto.then;\n      // Keep a reference to the original method.\n      proto[symbolThen] = originalThen;\n      Ctor.prototype.then = function (onResolve, onReject) {\n        const wrapped = new ZoneAwarePromise((resolve, reject) => {\n          originalThen.call(this, resolve, reject);\n        });\n        return wrapped.then(onResolve, onReject);\n      };\n      Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n      return function (self, args) {\n        let resultPromise = fn.apply(self, args);\n        if (resultPromise instanceof ZoneAwarePromise) {\n          return resultPromise;\n        }\n        let ctor = resultPromise.constructor;\n        if (!ctor[symbolThenPatched]) {\n          patchThen(ctor);\n        }\n        return resultPromise;\n      };\n    }\n    if (NativePromise) {\n      patchThen(NativePromise);\n      patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n  });\n}\nfunction patchToString(Zone) {\n  // override Function.prototype.toString to make zone.js patched function\n  // look like native function\n  Zone.__load_patch('toString', global => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n    const newFunctionToString = function toString() {\n      if (typeof this === 'function') {\n        const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n        if (originalDelegate) {\n          if (typeof originalDelegate === 'function') {\n            return originalFunctionToString.call(originalDelegate);\n          } else {\n            return Object.prototype.toString.call(originalDelegate);\n          }\n        }\n        if (this === Promise) {\n          const nativePromise = global[PROMISE_SYMBOL];\n          if (nativePromise) {\n            return originalFunctionToString.call(nativePromise);\n          }\n        }\n        if (this === Error) {\n          const nativeError = global[ERROR_SYMBOL];\n          if (nativeError) {\n            return originalFunctionToString.call(nativeError);\n          }\n        }\n      }\n      return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n      if (typeof Promise === 'function' && this instanceof Promise) {\n        return PROMISE_OBJECT_TO_STRING;\n      }\n      return originalObjectToString.call(this);\n    };\n  });\n}\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n  const symbol = Zone.__symbol__(method);\n  if (target[symbol]) {\n    return;\n  }\n  const nativeDelegate = target[symbol] = target[method];\n  target[method] = function (name, opts, options) {\n    if (opts && opts.prototype) {\n      callbacks.forEach(function (callback) {\n        const source = `${targetName}.${method}::` + callback;\n        const prototype = opts.prototype;\n        // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n        // `customElements.define`. We explicitly wrap the patching code into try-catch since\n        // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n        // make those properties non-writable. This means that patching callback will throw an error\n        // `cannot assign to read-only property`. See this code as an example:\n        // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n        // We don't want to stop the application rendering if we couldn't patch some\n        // callback, e.g. `attributeChangedCallback`.\n        try {\n          if (prototype.hasOwnProperty(callback)) {\n            const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n            if (descriptor && descriptor.value) {\n              descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n              api._redefineProperty(opts.prototype, callback, descriptor);\n            } else if (prototype[callback]) {\n              prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n            }\n          } else if (prototype[callback]) {\n            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n          }\n        } catch {\n          // Note: we leave the catch block empty since there's no way to handle the error related\n          // to non-writable property.\n        }\n      });\n    }\n    return nativeDelegate.call(target, name, opts, options);\n  };\n  api.attachOriginToPatched(target[method], nativeDelegate);\n}\nfunction patchUtil(Zone) {\n  Zone.__load_patch('util', (global, Zone, api) => {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    const eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n    // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n    // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n    // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n    // supported for backwards compatibility.\n    const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n      global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n      Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = () => ({\n      globalSources,\n      zoneSymbolEventNames,\n      eventNames,\n      isBrowser,\n      isMix,\n      isNode,\n      TRUE_STR,\n      FALSE_STR,\n      ZONE_SYMBOL_PREFIX,\n      ADD_EVENT_LISTENER_STR,\n      REMOVE_EVENT_LISTENER_STR\n    });\n  });\n}\nfunction patchCommon(Zone) {\n  patchPromise(Zone);\n  patchToString(Zone);\n  patchUtil(Zone);\n}\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);", "map": {"version": 3, "names": ["global", "globalThis", "__symbol__", "name", "symbolPrefix", "initZone", "performance", "mark", "performanceMeasure", "label", "ZoneImpl", "assertZonePatched", "patches", "Error", "root", "zone", "current", "parent", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "checkDuplicate", "perfName", "_api", "_parent", "_name", "constructor", "zoneSpec", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "zoneTask", "type", "data", "isPeriodic", "isRefreshable", "state", "notScheduled", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "previousTask", "cancelFn", "undefined", "invokeTask", "unknown", "scheduling", "zoneDelegates", "_zoneDelegates", "_updateTaskCount", "scheduleTask", "newZone", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "runCount", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "loadZone", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "Zone", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "window", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "enableBeforeunloadSymbol", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "returnValue", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "isFunction", "isNumber", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "api", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "originalDelegate", "once", "globalCallback", "context", "isCapture", "tasks", "errors", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "splice", "removeAbortListener", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "copyEventListenerOptions", "newOptions", "signal", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "aborted", "constructorName", "targetSource", "onAbort", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "patchQueueMicrotask", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "handleOrId", "handle", "refresh", "clearTask", "delay", "timer", "originalRefresh", "id", "patchCustomElements", "callbacks", "customElements", "eventTargetPatch", "eventNames", "EVENT_TARGET", "patchEvent", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "patchBrowser", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "loadTasks", "status", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "e", "eventTasks", "for<PERSON>ach", "PromiseRejectionEvent", "evt", "promise", "reason", "rejection", "patchPromise", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "v", "resolvePromise", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "withResolvers", "res", "rej", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "race", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "patchToString", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "targetName", "method", "nativeDelegate", "opts", "descriptor", "patchUtil", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "patchCommon", "Zone$1"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\nfunction initZone() {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    class ZoneImpl {\n        // tslint:disable-next-line:require-internal-with-underscore\n        static { this.__symbol__ = __symbol__; }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = ZoneImpl.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, ZoneImpl, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = (zoneSpec && zoneSpec.properties) || {};\n            this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            }\n            const zoneTask = task;\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            const { type, data: { isPeriodic = false, isRefreshable = false } = {} } = task;\n            if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && zoneTask._transitionTo(running, scheduled);\n            const previousTask = _currentTask;\n            _currentTask = zoneTask;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                const state = task.state;\n                if (state !== notScheduled && state !== unknown) {\n                    if (type == eventTask || isPeriodic || (isRefreshable && state === scheduling)) {\n                        reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n                    }\n                    else {\n                        const zoneDelegates = zoneTask._zoneDelegates;\n                        this._updateTaskCount(zoneTask, -1);\n                        reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n                        if (isRefreshable) {\n                            zoneTask._zoneDelegates = zoneDelegates;\n                        }\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = -1;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task),\n    };\n    class _ZoneDelegate {\n        get zone() {\n            return this._zone;\n        }\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = {\n                'microTask': 0,\n                'macroTask': 0,\n                'eventTask': 0,\n            };\n            this._zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt =\n                zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = this._zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this._zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS\n                ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec)\n                : new ZoneImpl(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS\n                ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source)\n                : callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS\n                ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source)\n                : callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS\n                ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error)\n                : true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS\n                ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs)\n                : task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = (counts[type] = prev + count);\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type,\n                };\n                this.hasTask(this._zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount,\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask,\n    };\n    let _currentZoneFrame = { parent: null, zone: new ZoneImpl(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return ZoneImpl;\n}\n\nfunction loadZone() {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    const global = globalThis;\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n        throw new Error('Zone already loaded.');\n    }\n    // Initialize global `Zone` constant.\n    global['Zone'] ??= initZone();\n    return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = (isWindowExists && internalWindow) || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) &&\n    typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]' &&\n    !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result =\n            listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (\n        // https://github.com/angular/angular/issues/47579\n        // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n        // This is the only specific case we should check for. The spec defines that the\n        // `returnValue` attribute represents the message to show the user. When the event\n        // is created, this attribute must be set to the empty string.\n        event.type === 'beforeunload' &&\n            // To prevent any breaking changes resulting from this change, given that\n            // it was already causing a significant number of failures in G3, we have hidden\n            // that behavior behind a global configuration flag. Consumers can enable this\n            // flag explicitly if they want the `beforeunload` event to be handled as defined\n            // in the specification.\n            _global[enableBeforeunloadSymbol] &&\n            // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n            // `typeof result` is a string.\n            typeof result === 'string') {\n            event.returnValue = result;\n        }\n        else if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    },\n                });\n            }\n        })(prop);\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) { }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) { }\n    return ieOrEdge;\n}\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nfunction isNumber(value) {\n    return typeof value === 'number';\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// Note that passive event listeners are now supported by most modern browsers,\n// including Chrome, Firefox, Safari, and Edge. There's a pending change that\n// would remove support for legacy browsers by zone.js. Removing `passiveSupported`\n// from the codebase will reduce the final code size for existing apps that still use zone.js.\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            },\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true,\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n        // eliminating the need to create a new object solely for passing data.\n        // WARNING: This object has a static lifetime, meaning it is not created\n        // each time `addEventListener` is called. It is instantiated only once\n        // and captured by reference inside the `addEventListener` and\n        // `removeEventListener` functions. Do not add any new properties to this\n        // object, as doing so would necessitate maintaining the information\n        // between `addEventListener` calls.\n        const taskData = {};\n        const nativeAddEventListener = (proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER]);\n        const nativeRemoveEventListener = (proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER]);\n        const nativeListeners = (proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER]);\n        const nativeRemoveAllListeners = (proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]);\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        /**\n         * In the context of events and listeners, this function will be\n         * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n         * Cancelling a task is primarily used to remove event listeners from\n         * the task target.\n         */\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (task.removeAbortListener) {\n                                task.removeAbortListener();\n                                task.removeAbortListener = null;\n                            }\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return ((typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate));\n        };\n        const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        function copyEventListenerOptions(options) {\n            if (typeof options === 'object' && options !== null) {\n                // We need to destructure the target `options` object since it may\n                // be frozen or sealed (possibly provided implicitly by a third-party\n                // library), or its properties may be readonly.\n                const newOptions = { ...options };\n                // The `signal` option was recently introduced, which caused regressions in\n                // third-party scenarios where `AbortController` was directly provided to\n                // `addEventListener` as options. For instance, in cases like\n                // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n                // which is valid because `AbortController` includes a `signal` getter, spreading\n                // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n                // isn't feasible since `AbortController` is a built-in object type, and attempting\n                // to create a new object directly with it as the prototype might result in\n                // unexpected behavior.\n                if (options.signal) {\n                    newOptions.signal = options.signal;\n                }\n                return newOptions;\n            }\n            return options;\n        }\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n                const signal = options?.signal;\n                if (signal?.aborted) {\n                    // the signal is an aborted one, just return without attaching the event listener.\n                    return;\n                }\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source =\n                        constructorName +\n                            addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // In the code below, `options` should no longer be reassigned; instead, it\n                // should only be mutated. This is because we pass that object to the native\n                // `addEventListener`.\n                // It's generally recommended to use the same object reference for options.\n                // This ensures consistency and avoids potential issues.\n                taskData.options = options;\n                if (once) {\n                    // When using `addEventListener` with the `once` option, we don't pass\n                    // the `once` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `once` setting and handle it ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                if (signal) {\n                    // When using `addEventListener` with the `signal` option, we don't pass\n                    // the `signal` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `signal` setting and handle it ourselves.\n                    taskData.options.signal = undefined;\n                }\n                // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n                // which in turn calls the native `addEventListener`. This is why `taskData.options`\n                // is updated before scheduling the task, as `customScheduleGlobal` uses\n                // `taskData.options` to pass it to the native `addEventListener`.\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                if (signal) {\n                    // after task is scheduled, we need to store the signal back to task.options\n                    taskData.options.signal = signal;\n                    // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n                    // primarily used for preventing strong references cycles. `onAbort` is always reachable\n                    // as it's an event listener, so its closure retains a strong reference to the `task`.\n                    const onAbort = () => task.zone.cancelTask(task);\n                    nativeListener.call(signal, 'abort', onAbort, { once: true });\n                    // We need to remove the `abort` listener when the event listener is going to be removed,\n                    // as it creates a closure that captures `task`. This closure retains a reference to the\n                    // `task` object even after it goes out of scope, preventing `task` from being garbage\n                    // collected.\n                    task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n                }\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    taskData.options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            // `existingTasks` may not exist if the `addEventListener` was called before\n            // it was patched by zone.js. Please refer to the attached issue for\n            // clarification, particularly after the `if` condition, before calling\n            // the native `removeEventListener`.\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates with capture=false were removed\n                            // https:// github.com/angular/angular/issues/31643\n                            // https://github.com/angular/angular/issues/54581\n                            if (!capture && typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        // In all other conditions, when `addEventListener` is called after being\n                        // patched by zone.js, we would always find an event task on the `EventTarget`.\n                        // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n                        // which ultimately removes an event listener and cleans up the abort listener\n                        // (if an `AbortSignal` was provided when scheduling a task).\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // https://github.com/angular/zone.js/issues/930\n            // We may encounter a situation where the `addEventListener` was\n            // called on the event target before zone.js is loaded, resulting\n            // in no task being stored on the event target due to its invocation\n            // of the native implementation. In this scenario, we simply need to\n            // invoke the native `removeEventListener`.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks\n            ? captureFalseTasks.concat(captureTrueTasks)\n            : captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        const handleOrId = setNative.apply(window, data.args);\n        // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n        // to this so that we do not cause potentally leaks when using `setTimeout`\n        // since this can be periodic when using `.refresh`.\n        if (isNumber(handleOrId)) {\n            data.handleId = handleOrId;\n        }\n        else {\n            data.handle = handleOrId;\n            // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n            data.isRefreshable = isFunction(handleOrId.refresh);\n        }\n        return task;\n    }\n    function clearTask(task) {\n        const { handle, handleId } = task.data;\n        return clearNative.call(window, handle ?? handleId);\n    }\n    setNative = patchMethod(window, setName, (delegate) => function (self, args) {\n        if (isFunction(args[0])) {\n            const options = {\n                isRefreshable: false,\n                isPeriodic: nameSuffix === 'Interval',\n                delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n                args: args,\n            };\n            const callback = args[0];\n            args[0] = function timer() {\n                try {\n                    return callback.apply(this, arguments);\n                }\n                finally {\n                    // issue-934, task will be cancelled\n                    // even it is a periodic task such as\n                    // setInterval\n                    // https://github.com/angular/angular/issues/40387\n                    // Cleanup tasksByHandleId should be handled before scheduleTask\n                    // Since some zoneSpec may intercept and doesn't trigger\n                    // scheduleFn(scheduleTask) provided here.\n                    const { handle, handleId, isPeriodic, isRefreshable } = options;\n                    if (!isPeriodic && !isRefreshable) {\n                        if (handleId) {\n                            // in non-nodejs env, we remove timerId\n                            // from local cache\n                            delete tasksByHandleId[handleId];\n                        }\n                        else if (handle) {\n                            // Node returns complex objects as handleIds\n                            // we remove task reference from timer object\n                            handle[taskSymbol] = null;\n                        }\n                    }\n                }\n            };\n            const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n            if (!task) {\n                return task;\n            }\n            // Node.js must additionally support the ref and unref functions.\n            const { handleId, handle, isRefreshable, isPeriodic } = task.data;\n            if (handleId) {\n                // for non nodejs env, we save handleId: task\n                // mapping in local cache for clearTimeout\n                tasksByHandleId[handleId] = task;\n            }\n            else if (handle) {\n                // for nodejs env, we save task\n                // reference in timerId Object for clearTimeout\n                handle[taskSymbol] = task;\n                if (isRefreshable && !isPeriodic) {\n                    const originalRefresh = handle.refresh;\n                    handle.refresh = function () {\n                        const { zone, state } = task;\n                        if (state === 'notScheduled') {\n                            task._state = 'scheduled';\n                            zone._updateTaskCount(task, 1);\n                        }\n                        else if (state === 'running') {\n                            task._state = 'scheduling';\n                        }\n                        return originalRefresh.call(this);\n                    };\n                }\n            }\n            return handle ?? handleId ?? task;\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(window, args);\n        }\n    });\n    clearNative = patchMethod(window, cancelName, (delegate) => function (self, args) {\n        const id = args[0];\n        let task;\n        if (isNumber(id)) {\n            // non nodejs env.\n            task = tasksByHandleId[id];\n            delete tasksByHandleId[id];\n        }\n        else {\n            // nodejs env ?? other environments.\n            task = id?.[taskSymbol];\n            if (task) {\n                id[taskSymbol] = null;\n            }\n            else {\n                task = id;\n            }\n        }\n        if (task?.type) {\n            if (task.cancelFn) {\n                // Do not cancel already canceled functions\n                task.zone.cancelTask(task);\n            }\n        }\n        else {\n            // cause an error by calling it directly.\n            delegate.apply(window, args);\n        }\n    });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n    const callbacks = [\n        'connectedCallback',\n        'disconnectedCallback',\n        'adoptedCallback',\n        'attributeChangedCallback',\n        'formAssociatedCallback',\n        'formDisabledCallback',\n        'formResetCallback',\n        'formStateRestoreCallback',\n    ];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter((ip) => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter((op) => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter((name) => name.startsWith('on') && name.length > 2)\n        .map((name) => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document',\n            'SVGElement',\n            'Element',\n            'HTMLElement',\n            'HTMLBodyElement',\n            'HTMLMediaElement',\n            'HTMLFrameSetElement',\n            'HTMLFrameElement',\n            'HTMLIFrameElement',\n            'HTMLMarqueeElement',\n            'Worker',\n        ]);\n        const ignoreErrorProperties = isIE()\n            ? [{ target: internalWindow, ignoreProperties: ['error'] }]\n            : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest',\n        'XMLHttpRequestEventTarget',\n        'IDBIndex',\n        'IDBRequest',\n        'IDBOpenDBRequest',\n        'IDBDatabase',\n        'IDBTransaction',\n        'IDBCursor',\n        'WebSocket',\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target &&\n            target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n    Zone.__load_patch('legacy', (global) => {\n        const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('timers', (global) => {\n        const set = 'set';\n        const clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', (global) => {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', (global, Zone) => {\n        const blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (let i = 0; i < blockingMethods.length; i++) {\n            const name = blockingMethods[i];\n            patchMethod(global, name, (delegate, symbol, name) => {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', (global, Zone, api) => {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', (global, Zone, api) => {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', (global, Zone, api) => {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', (global, Zone, api) => {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', (global, Zone) => {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        const XHR_TASK = zoneSymbol('xhrTask');\n        const XHR_SYNC = zoneSymbol('xhrSync');\n        const XHR_LISTENER = zoneSymbol('xhrListener');\n        const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n        const XHR_URL = zoneSymbol('xhrURL');\n        const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            const XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget) {\n                    const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            const READY_STATE_CHANGE = 'readystatechange';\n            const SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                const data = task.data;\n                const target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                const listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                const newListener = (target[XHR_LISTENER] = () => {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            const loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                const oriInvoke = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (let i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                });\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                const storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                const data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            });\n            const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n            const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n            const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    const options = {\n                        target: self,\n                        url: self[XHR_URL],\n                        isPeriodic: false,\n                        args: args,\n                        aborted: false,\n                    };\n                    const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self &&\n                        self[XHR_ERROR_BEFORE_SCHEDULED] === true &&\n                        !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            });\n            const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n                const task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            });\n        }\n    });\n    Zone.__load_patch('geolocation', (global) => {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                const eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach((eventTask) => {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        const evt = new PromiseRejectionEvent(evtName, {\n                            promise: e.promise,\n                            reason: e.rejection,\n                        });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n    Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n        patchQueueMicrotask(global, api);\n    });\n}\n\nfunction patchPromise(Zone) {\n    Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n        const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        const ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                const className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        const __symbol__ = api.symbol;\n        const _uncaughtPromiseErrors = [];\n        const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n        const symbolPromise = __symbol__('Promise');\n        const symbolThen = __symbol__('then');\n        const creationTrace = '__creationTrace__';\n        api.onUnhandledError = (e) => {\n            if (api.showUncaughtError()) {\n                const rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = () => {\n            while (_uncaughtPromiseErrors.length) {\n                const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(() => {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            }\n        };\n        const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) { }\n        }\n        function isThenable(value) {\n            return value && value.then;\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        const symbolState = __symbol__('state');\n        const symbolValue = __symbol__('value');\n        const symbolFinally = __symbol__('finally');\n        const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        const symbolParentPromiseState = __symbol__('parentPromiseState');\n        const source = 'Promise.then';\n        const UNRESOLVED = null;\n        const RESOLVED = true;\n        const REJECTED = false;\n        const REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return (v) => {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        const once = function () {\n            let wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        const TYPE_ERROR = 'Promise resolved with itself';\n        const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            const onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                let then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED &&\n                    value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) &&\n                    value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(() => {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    const queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        const trace = Zone.currentTask &&\n                            Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                                configurable: true,\n                                enumerable: false,\n                                writable: true,\n                                value: trace,\n                            });\n                        }\n                    }\n                    for (let i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        let uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' +\n                                readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    const handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) { }\n                promise[symbolState] = REJECTED;\n                for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            const promiseState = promise[symbolState];\n            const delegate = promiseState\n                ? typeof onFulfilled === 'function'\n                    ? onFulfilled\n                    : forwardResolution\n                : typeof onRejected === 'function'\n                    ? onRejected\n                    : forwardRejection;\n            zone.scheduleMicroTask(source, () => {\n                try {\n                    const parentPromiseValue = promise[symbolValue];\n                    const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution\n                        ? []\n                        : [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        const noop = function () { };\n        const AggregateError = global.AggregateError;\n        class ZoneAwarePromise {\n            static toString() {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            }\n            static resolve(value) {\n                if (value instanceof ZoneAwarePromise) {\n                    return value;\n                }\n                return resolvePromise(new this(null), RESOLVED, value);\n            }\n            static reject(error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            }\n            static withResolvers() {\n                const result = {};\n                result.promise = new ZoneAwarePromise((res, rej) => {\n                    result.resolve = res;\n                    result.reject = rej;\n                });\n                return result;\n            }\n            static any(values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                const promises = [];\n                let count = 0;\n                try {\n                    for (let v of values) {\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                let finished = false;\n                const errors = [];\n                return new ZoneAwarePromise((resolve, reject) => {\n                    for (let i = 0; i < promises.length; i++) {\n                        promises[i].then((v) => {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, (err) => {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            }\n            static race(values) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            }\n            static all(values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            }\n            static allSettled(values) {\n                const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: (value) => ({ status: 'fulfilled', value }),\n                    errorCallback: (err) => ({ status: 'rejected', reason: err }),\n                });\n            }\n            static allWithCallback(values, callback) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                let unresolvedCount = 2;\n                let valueIndex = 0;\n                const resolvedValues = [];\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    const curValueIndex = valueIndex;\n                    try {\n                        value.then((value) => {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, (err) => {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            }\n            constructor(executor) {\n                const promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    const onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            get [Symbol.toStringTag]() {\n                return 'Promise';\n            }\n            get [Symbol.species]() {\n                return ZoneAwarePromise;\n            }\n            then(onFulfilled, onRejected) {\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and\n                // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n                // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            }\n            catch(onRejected) {\n                return this.then(null, onRejected);\n            }\n            finally(onFinally) {\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            }\n        }\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        const NativePromise = (global[symbolPromise] = global['Promise']);\n        global['Promise'] = ZoneAwarePromise;\n        const symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            const proto = Ctor.prototype;\n            const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            const originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                    originalThen.call(this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                let resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                let ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', (delegate) => zoneify(delegate));\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n}\n\nfunction patchToString(Zone) {\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', (global) => {\n        // patch Func.prototype.toString to let them look like native\n        const originalFunctionToString = Function.prototype.toString;\n        const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n        const PROMISE_SYMBOL = zoneSymbol('Promise');\n        const ERROR_SYMBOL = zoneSymbol('Error');\n        const newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    const nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    const nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        const originalObjectToString = Object.prototype.toString;\n        const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = (target[symbol] = target[method]);\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n    Zone.__load_patch('util', (global, Zone, api) => {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        const eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n        // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n        // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n        // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n        // supported for backwards compatibility.\n        const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = () => ({\n            globalSources,\n            zoneSymbolEventNames,\n            eventNames,\n            isBrowser,\n            isMix,\n            isNode,\n            TRUE_STR,\n            FALSE_STR,\n            ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR,\n        });\n    });\n}\n\nfunction patchCommon(Zone) {\n    patchPromise(Zone);\n    patchToString(Zone);\n    patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA,MAAMA,MAAM,GAAGC,UAAU;AACzB;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,MAAMC,YAAY,GAAGJ,MAAM,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EACxE,OAAOI,YAAY,GAAGD,IAAI;AAC9B;AACA,SAASE,QAAQA,CAAA,EAAG;EAChB,MAAMC,WAAW,GAAGN,MAAM,CAAC,aAAa,CAAC;EACzC,SAASO,IAAIA,CAACJ,IAAI,EAAE;IAChBG,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACH,IAAI,CAAC;EACnE;EACA,SAASK,kBAAkBA,CAACL,IAAI,EAAEM,KAAK,EAAE;IACrCH,WAAW,IAAIA,WAAW,CAAC,SAAS,CAAC,IAAIA,WAAW,CAAC,SAAS,CAAC,CAACH,IAAI,EAAEM,KAAK,CAAC;EAChF;EACAF,IAAI,CAAC,MAAM,CAAC;EACZ,MAAMG,QAAQ,CAAC;IACX;IACA;MAAS,IAAI,CAACR,UAAU,GAAGA,UAAU;IAAE;IACvC,OAAOS,iBAAiBA,CAAA,EAAG;MACvB,IAAIX,MAAM,CAAC,SAAS,CAAC,KAAKY,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,uEAAuE,GACnF,yBAAyB,GACzB,+DAA+D,GAC/D,kFAAkF,GAClF,sDAAsD,CAAC;MAC/D;IACJ;IACA,WAAWC,IAAIA,CAAA,EAAG;MACd,IAAIC,IAAI,GAAGL,QAAQ,CAACM,OAAO;MAC3B,OAAOD,IAAI,CAACE,MAAM,EAAE;QAChBF,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,OAAOF,IAAI;IACf;IACA,WAAWC,OAAOA,CAAA,EAAG;MACjB,OAAOE,iBAAiB,CAACH,IAAI;IACjC;IACA,WAAWI,WAAWA,CAAA,EAAG;MACrB,OAAOC,YAAY;IACvB;IACA;IACA,OAAOC,YAAYA,CAAClB,IAAI,EAAEmB,EAAE,EAAEC,eAAe,GAAG,KAAK,EAAE;MACnD,IAAIX,OAAO,CAACY,cAAc,CAACrB,IAAI,CAAC,EAAE;QAC9B;QACA;QACA;QACA,MAAMsB,cAAc,GAAGzB,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;QAC7E,IAAI,CAACqB,eAAe,IAAIE,cAAc,EAAE;UACpC,MAAMZ,KAAK,CAAC,wBAAwB,GAAGV,IAAI,CAAC;QAChD;MACJ,CAAC,MACI,IAAI,CAACH,MAAM,CAAC,iBAAiB,GAAGG,IAAI,CAAC,EAAE;QACxC,MAAMuB,QAAQ,GAAG,OAAO,GAAGvB,IAAI;QAC/BI,IAAI,CAACmB,QAAQ,CAAC;QACdd,OAAO,CAACT,IAAI,CAAC,GAAGmB,EAAE,CAACtB,MAAM,EAAEU,QAAQ,EAAEiB,IAAI,CAAC;QAC1CnB,kBAAkB,CAACkB,QAAQ,EAAEA,QAAQ,CAAC;MAC1C;IACJ;IACA,IAAIT,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACW,OAAO;IACvB;IACA,IAAIzB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC0B,KAAK;IACrB;IACAC,WAAWA,CAACb,MAAM,EAAEc,QAAQ,EAAE;MAC1B,IAAI,CAACH,OAAO,GAAGX,MAAM;MACrB,IAAI,CAACY,KAAK,GAAGE,QAAQ,GAAGA,QAAQ,CAAC5B,IAAI,IAAI,SAAS,GAAG,QAAQ;MAC7D,IAAI,CAAC6B,WAAW,GAAID,QAAQ,IAAIA,QAAQ,CAACE,UAAU,IAAK,CAAC,CAAC;MAC1D,IAAI,CAACC,aAAa,GAAG,IAAIC,aAAa,CAAC,IAAI,EAAE,IAAI,CAACP,OAAO,IAAI,IAAI,CAACA,OAAO,CAACM,aAAa,EAAEH,QAAQ,CAAC;IACtG;IACAK,GAAGA,CAACC,GAAG,EAAE;MACL,MAAMtB,IAAI,GAAG,IAAI,CAACuB,WAAW,CAACD,GAAG,CAAC;MAClC,IAAItB,IAAI,EACJ,OAAOA,IAAI,CAACiB,WAAW,CAACK,GAAG,CAAC;IACpC;IACAC,WAAWA,CAACD,GAAG,EAAE;MACb,IAAIrB,OAAO,GAAG,IAAI;MAClB,OAAOA,OAAO,EAAE;QACZ,IAAIA,OAAO,CAACgB,WAAW,CAACR,cAAc,CAACa,GAAG,CAAC,EAAE;UACzC,OAAOrB,OAAO;QAClB;QACAA,OAAO,GAAGA,OAAO,CAACY,OAAO;MAC7B;MACA,OAAO,IAAI;IACf;IACAW,IAAIA,CAACR,QAAQ,EAAE;MACX,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,CAAC;MACzC,OAAO,IAAI,CAACqB,aAAa,CAACK,IAAI,CAAC,IAAI,EAAER,QAAQ,CAAC;IAClD;IACAS,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MACnB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAChC,MAAM,IAAI5B,KAAK,CAAC,0BAA0B,GAAG4B,QAAQ,CAAC;MAC1D;MACA,MAAME,SAAS,GAAG,IAAI,CAACT,aAAa,CAACU,SAAS,CAAC,IAAI,EAAEH,QAAQ,EAAEC,MAAM,CAAC;MACtE,MAAM3B,IAAI,GAAG,IAAI;MACjB,OAAO,YAAY;QACf,OAAOA,IAAI,CAAC8B,UAAU,CAACF,SAAS,EAAE,IAAI,EAAEG,SAAS,EAAEJ,MAAM,CAAC;MAC9D,CAAC;IACL;IACAK,GAAGA,CAACN,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACxCxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;MAClF,CAAC,SACO;QACJxB,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACA4B,UAAUA,CAACJ,QAAQ,EAAEO,SAAS,GAAG,IAAI,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACtDxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAI;UACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;QAClF,CAAC,CACD,OAAOS,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJjC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACAoC,OAAOA,CAACC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAChC,IAAIK,IAAI,CAACvC,IAAI,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIF,KAAK,CAAC,6DAA6D,GACzE,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAEpD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ;MACA,MAAMqD,QAAQ,GAAGF,IAAI;MACrB;MACA;MACA;MACA,MAAM;QAAEG,IAAI;QAAEC,IAAI,EAAE;UAAEC,UAAU,GAAG,KAAK;UAAEC,aAAa,GAAG;QAAM,CAAC,GAAG,CAAC;MAAE,CAAC,GAAGN,IAAI;MAC/E,IAAIA,IAAI,CAACO,KAAK,KAAKC,YAAY,KAAKL,IAAI,KAAKM,SAAS,IAAIN,IAAI,KAAKO,SAAS,CAAC,EAAE;QAC3E;MACJ;MACA,MAAMC,YAAY,GAAGX,IAAI,CAACO,KAAK,IAAIK,OAAO;MAC1CD,YAAY,IAAIT,QAAQ,CAACW,aAAa,CAACD,OAAO,EAAEE,SAAS,CAAC;MAC1D,MAAMC,YAAY,GAAGjD,YAAY;MACjCA,YAAY,GAAGoC,QAAQ;MACvBtC,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAI0C,IAAI,IAAIO,SAAS,IAAIV,IAAI,CAACI,IAAI,IAAI,CAACC,UAAU,IAAI,CAACC,aAAa,EAAE;UACjEN,IAAI,CAACgB,QAAQ,GAAGC,SAAS;QAC7B;QACA,IAAI;UACA,OAAO,IAAI,CAACrC,aAAa,CAACsC,UAAU,CAAC,IAAI,EAAEhB,QAAQ,EAAER,SAAS,EAAEC,SAAS,CAAC;QAC9E,CAAC,CACD,OAAOE,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJ;QACA;QACA,MAAMU,KAAK,GAAGP,IAAI,CAACO,KAAK;QACxB,IAAIA,KAAK,KAAKC,YAAY,IAAID,KAAK,KAAKY,OAAO,EAAE;UAC7C,IAAIhB,IAAI,IAAIM,SAAS,IAAIJ,UAAU,IAAKC,aAAa,IAAIC,KAAK,KAAKa,UAAW,EAAE;YAC5ET,YAAY,IAAIT,QAAQ,CAACW,aAAa,CAACC,SAAS,EAAEF,OAAO,EAAEQ,UAAU,CAAC;UAC1E,CAAC,MACI;YACD,MAAMC,aAAa,GAAGnB,QAAQ,CAACoB,cAAc;YAC7C,IAAI,CAACC,gBAAgB,CAACrB,QAAQ,EAAE,CAAC,CAAC,CAAC;YACnCS,YAAY,IAAIT,QAAQ,CAACW,aAAa,CAACL,YAAY,EAAEI,OAAO,EAAEJ,YAAY,CAAC;YAC3E,IAAIF,aAAa,EAAE;cACfJ,QAAQ,CAACoB,cAAc,GAAGD,aAAa;YAC3C;UACJ;QACJ;QACAzD,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;QAC5CG,YAAY,GAAGiD,YAAY;MAC/B;IACJ;IACAS,YAAYA,CAACxB,IAAI,EAAE;MACf,IAAIA,IAAI,CAACvC,IAAI,IAAIuC,IAAI,CAACvC,IAAI,KAAK,IAAI,EAAE;QACjC;QACA;QACA,IAAIgE,OAAO,GAAG,IAAI;QAClB,OAAOA,OAAO,EAAE;UACZ,IAAIA,OAAO,KAAKzB,IAAI,CAACvC,IAAI,EAAE;YACvB,MAAMF,KAAK,CAAE,8BAA6B,IAAI,CAACV,IAAK,8CAA6CmD,IAAI,CAACvC,IAAI,CAACZ,IAAK,EAAC,CAAC;UACtH;UACA4E,OAAO,GAAGA,OAAO,CAAC9D,MAAM;QAC5B;MACJ;MACAqC,IAAI,CAACa,aAAa,CAACO,UAAU,EAAEZ,YAAY,CAAC;MAC5C,MAAMa,aAAa,GAAG,EAAE;MACxBrB,IAAI,CAACsB,cAAc,GAAGD,aAAa;MACnCrB,IAAI,CAAC0B,KAAK,GAAG,IAAI;MACjB,IAAI;QACA1B,IAAI,GAAG,IAAI,CAACpB,aAAa,CAAC4C,YAAY,CAAC,IAAI,EAAExB,IAAI,CAAC;MACtD,CAAC,CACD,OAAO2B,GAAG,EAAE;QACR;QACA;QACA3B,IAAI,CAACa,aAAa,CAACM,OAAO,EAAEC,UAAU,EAAEZ,YAAY,CAAC;QACrD;QACA,IAAI,CAAC5B,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE6B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI3B,IAAI,CAACsB,cAAc,KAAKD,aAAa,EAAE;QACvC;QACA,IAAI,CAACE,gBAAgB,CAACvB,IAAI,EAAE,CAAC,CAAC;MAClC;MACA,IAAIA,IAAI,CAACO,KAAK,IAAIa,UAAU,EAAE;QAC1BpB,IAAI,CAACa,aAAa,CAACC,SAAS,EAAEM,UAAU,CAAC;MAC7C;MACA,OAAOpB,IAAI;IACf;IACA4B,iBAAiBA,CAACxC,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAE;MACtD,OAAO,IAAI,CAACL,YAAY,CAAC,IAAIM,QAAQ,CAACC,SAAS,EAAE3C,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEZ,SAAS,CAAC,CAAC;IACxG;IACAe,iBAAiBA,CAAC5C,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACT,YAAY,CAAC,IAAIM,QAAQ,CAACpB,SAAS,EAAEtB,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAC,iBAAiBA,CAAC9C,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACT,YAAY,CAAC,IAAIM,QAAQ,CAACrB,SAAS,EAAErB,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAE,UAAUA,CAACnC,IAAI,EAAE;MACb,IAAIA,IAAI,CAACvC,IAAI,IAAI,IAAI,EACjB,MAAM,IAAIF,KAAK,CAAC,mEAAmE,GAC/E,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAEpD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ,IAAImD,IAAI,CAACO,KAAK,KAAKO,SAAS,IAAId,IAAI,CAACO,KAAK,KAAKK,OAAO,EAAE;QACpD;MACJ;MACAZ,IAAI,CAACa,aAAa,CAACuB,SAAS,EAAEtB,SAAS,EAAEF,OAAO,CAAC;MACjD,IAAI;QACA,IAAI,CAAChC,aAAa,CAACuD,UAAU,CAAC,IAAI,EAAEnC,IAAI,CAAC;MAC7C,CAAC,CACD,OAAO2B,GAAG,EAAE;QACR;QACA3B,IAAI,CAACa,aAAa,CAACM,OAAO,EAAEiB,SAAS,CAAC;QACtC,IAAI,CAACxD,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE6B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI,CAACJ,gBAAgB,CAACvB,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/BA,IAAI,CAACa,aAAa,CAACL,YAAY,EAAE4B,SAAS,CAAC;MAC3CpC,IAAI,CAACqC,QAAQ,GAAG,CAAC,CAAC;MAClB,OAAOrC,IAAI;IACf;IACAuB,gBAAgBA,CAACvB,IAAI,EAAEsC,KAAK,EAAE;MAC1B,MAAMjB,aAAa,GAAGrB,IAAI,CAACsB,cAAc;MACzC,IAAIgB,KAAK,IAAI,CAAC,CAAC,EAAE;QACbtC,IAAI,CAACsB,cAAc,GAAG,IAAI;MAC9B;MACA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,aAAa,CAACmB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3ClB,aAAa,CAACkB,CAAC,CAAC,CAAChB,gBAAgB,CAACvB,IAAI,CAACG,IAAI,EAAEmC,KAAK,CAAC;MACvD;IACJ;EACJ;EACA,MAAMG,WAAW,GAAG;IAChB5F,IAAI,EAAE,EAAE;IACR6F,SAAS,EAAEA,CAACC,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEC,YAAY,KAAKH,QAAQ,CAACI,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IACxFE,cAAc,EAAEA,CAACL,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE7C,IAAI,KAAK2C,QAAQ,CAACnB,YAAY,CAACqB,MAAM,EAAE7C,IAAI,CAAC;IAClFiD,YAAY,EAAEA,CAACN,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE7C,IAAI,EAAEN,SAAS,EAAEC,SAAS,KAAKgD,QAAQ,CAACzB,UAAU,CAAC2B,MAAM,EAAE7C,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;IAC1HuD,YAAY,EAAEA,CAACP,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE7C,IAAI,KAAK2C,QAAQ,CAACR,UAAU,CAACU,MAAM,EAAE7C,IAAI;EACjF,CAAC;EACD,MAAMnB,aAAa,CAAC;IAChB,IAAIpB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACiE,KAAK;IACrB;IACAlD,WAAWA,CAACf,IAAI,EAAE0F,cAAc,EAAE1E,QAAQ,EAAE;MACxC,IAAI,CAAC2E,WAAW,GAAG;QACf,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,CAAC;QACd,WAAW,EAAE;MACjB,CAAC;MACD,IAAI,CAAC1B,KAAK,GAAGjE,IAAI;MACjB,IAAI,CAAC4F,eAAe,GAAGF,cAAc;MACrC,IAAI,CAACG,OAAO,GAAG7E,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC8E,MAAM,GAAG9E,QAAQ,GAAG0E,cAAc,CAACG,OAAO,CAAC;MAC5F,IAAI,CAACE,SAAS,GAAG/E,QAAQ,KAAKA,QAAQ,CAAC8E,MAAM,GAAGJ,cAAc,GAAGA,cAAc,CAACK,SAAS,CAAC;MAC1F,IAAI,CAACC,aAAa,GACdhF,QAAQ,KAAKA,QAAQ,CAAC8E,MAAM,GAAG,IAAI,CAAC7B,KAAK,GAAGyB,cAAc,CAACM,aAAa,CAAC;MAC7E,IAAI,CAACC,YAAY,GACbjF,QAAQ,KAAKA,QAAQ,CAACkF,WAAW,GAAGlF,QAAQ,GAAG0E,cAAc,CAACO,YAAY,CAAC;MAC/E,IAAI,CAACE,cAAc,GACfnF,QAAQ,KAAKA,QAAQ,CAACkF,WAAW,GAAGR,cAAc,GAAGA,cAAc,CAACS,cAAc,CAAC;MACvF,IAAI,CAACC,kBAAkB,GACnBpF,QAAQ,KAAKA,QAAQ,CAACkF,WAAW,GAAG,IAAI,CAACjC,KAAK,GAAGyB,cAAc,CAACU,kBAAkB,CAAC;MACvF,IAAI,CAACC,SAAS,GAAGrF,QAAQ,KAAKA,QAAQ,CAACsF,QAAQ,GAAGtF,QAAQ,GAAG0E,cAAc,CAACW,SAAS,CAAC;MACtF,IAAI,CAACE,WAAW,GACZvF,QAAQ,KAAKA,QAAQ,CAACsF,QAAQ,GAAGZ,cAAc,GAAGA,cAAc,CAACa,WAAW,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBxF,QAAQ,KAAKA,QAAQ,CAACsF,QAAQ,GAAG,IAAI,CAACrC,KAAK,GAAGyB,cAAc,CAACc,eAAe,CAAC;MACjF,IAAI,CAACC,cAAc,GACfzF,QAAQ,KAAKA,QAAQ,CAAC0F,aAAa,GAAG1F,QAAQ,GAAG0E,cAAc,CAACe,cAAc,CAAC;MACnF,IAAI,CAACE,gBAAgB,GACjB3F,QAAQ,KAAKA,QAAQ,CAAC0F,aAAa,GAAGhB,cAAc,GAAGA,cAAc,CAACiB,gBAAgB,CAAC;MAC3F,IAAI,CAACC,oBAAoB,GACrB5F,QAAQ,KAAKA,QAAQ,CAAC0F,aAAa,GAAG,IAAI,CAACzC,KAAK,GAAGyB,cAAc,CAACkB,oBAAoB,CAAC;MAC3F,IAAI,CAACC,eAAe,GAChB7F,QAAQ,KAAKA,QAAQ,CAACuE,cAAc,GAAGvE,QAAQ,GAAG0E,cAAc,CAACmB,eAAe,CAAC;MACrF,IAAI,CAACC,iBAAiB,GAClB9F,QAAQ,KAAKA,QAAQ,CAACuE,cAAc,GAAGG,cAAc,GAAGA,cAAc,CAACoB,iBAAiB,CAAC;MAC7F,IAAI,CAACC,qBAAqB,GACtB/F,QAAQ,KAAKA,QAAQ,CAACuE,cAAc,GAAG,IAAI,CAACtB,KAAK,GAAGyB,cAAc,CAACqB,qBAAqB,CAAC;MAC7F,IAAI,CAACC,aAAa,GACdhG,QAAQ,KAAKA,QAAQ,CAACwE,YAAY,GAAGxE,QAAQ,GAAG0E,cAAc,CAACsB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBjG,QAAQ,KAAKA,QAAQ,CAACwE,YAAY,GAAGE,cAAc,GAAGA,cAAc,CAACuB,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBlG,QAAQ,KAAKA,QAAQ,CAACwE,YAAY,GAAG,IAAI,CAACvB,KAAK,GAAGyB,cAAc,CAACwB,mBAAmB,CAAC;MACzF,IAAI,CAACC,aAAa,GACdnG,QAAQ,KAAKA,QAAQ,CAACyE,YAAY,GAAGzE,QAAQ,GAAG0E,cAAc,CAACyB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBpG,QAAQ,KAAKA,QAAQ,CAACyE,YAAY,GAAGC,cAAc,GAAGA,cAAc,CAAC0B,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBrG,QAAQ,KAAKA,QAAQ,CAACyE,YAAY,GAAG,IAAI,CAACxB,KAAK,GAAGyB,cAAc,CAAC2B,mBAAmB,CAAC;MACzF,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,MAAMC,eAAe,GAAG1G,QAAQ,IAAIA,QAAQ,CAACiE,SAAS;MACtD,MAAM0C,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAU;MACjE,IAAII,eAAe,IAAIC,aAAa,EAAE;QAClC;QACA;QACA,IAAI,CAACL,UAAU,GAAGI,eAAe,GAAG1G,QAAQ,GAAGgE,WAAW;QAC1D,IAAI,CAACuC,YAAY,GAAG7B,cAAc;QAClC,IAAI,CAAC8B,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACxD,KAAK;QAClC,IAAI,CAACjD,QAAQ,CAACuE,cAAc,EAAE;UAC1B,IAAI,CAACsB,eAAe,GAAG7B,WAAW;UAClC,IAAI,CAAC8B,iBAAiB,GAAGpB,cAAc;UACvC,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAAC9C,KAAK;QAC3C;QACA,IAAI,CAACjD,QAAQ,CAACwE,YAAY,EAAE;UACxB,IAAI,CAACwB,aAAa,GAAGhC,WAAW;UAChC,IAAI,CAACiC,eAAe,GAAGvB,cAAc;UACrC,IAAI,CAACwB,mBAAmB,GAAG,IAAI,CAACjD,KAAK;QACzC;QACA,IAAI,CAACjD,QAAQ,CAACyE,YAAY,EAAE;UACxB,IAAI,CAAC0B,aAAa,GAAGnC,WAAW;UAChC,IAAI,CAACoC,eAAe,GAAG1B,cAAc;UACrC,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAACpD,KAAK;QACzC;MACJ;IACJ;IACAzC,IAAIA,CAACoG,UAAU,EAAE5G,QAAQ,EAAE;MACvB,OAAO,IAAI,CAAC6E,OAAO,GACb,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC/F,IAAI,EAAE4H,UAAU,EAAE5G,QAAQ,CAAC,GACpE,IAAIrB,QAAQ,CAACiI,UAAU,EAAE5G,QAAQ,CAAC;IAC5C;IACAa,SAASA,CAAC+F,UAAU,EAAElG,QAAQ,EAAEC,MAAM,EAAE;MACpC,OAAO,IAAI,CAACsE,YAAY,GAClB,IAAI,CAACA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAEwB,UAAU,EAAElG,QAAQ,EAAEC,MAAM,CAAC,GACzGD,QAAQ;IAClB;IACAS,MAAMA,CAACyF,UAAU,EAAElG,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACvD,OAAO,IAAI,CAAC0E,SAAS,GACf,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAEoB,UAAU,EAAElG,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC,GACnHD,QAAQ,CAACmG,KAAK,CAAC5F,SAAS,EAAEC,SAAS,CAAC;IAC9C;IACAG,WAAWA,CAACuF,UAAU,EAAExF,KAAK,EAAE;MAC3B,OAAO,IAAI,CAACqE,cAAc,GACpB,IAAI,CAACA,cAAc,CAACC,aAAa,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,oBAAoB,EAAEgB,UAAU,EAAExF,KAAK,CAAC,GACtG,IAAI;IACd;IACA2B,YAAYA,CAAC6D,UAAU,EAAErF,IAAI,EAAE;MAC3B,IAAIuF,UAAU,GAAGvF,IAAI;MACrB,IAAI,IAAI,CAACsE,eAAe,EAAE;QACtB,IAAI,IAAI,CAACS,UAAU,EAAE;UACjBQ,UAAU,CAACjE,cAAc,CAACkE,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAAC;QAC1D;QACAM,UAAU,GAAG,IAAI,CAACjB,eAAe,CAACtB,cAAc,CAAC,IAAI,CAACuB,iBAAiB,EAAE,IAAI,CAACC,qBAAqB,EAAEa,UAAU,EAAErF,IAAI,CAAC;QACtH,IAAI,CAACuF,UAAU,EACXA,UAAU,GAAGvF,IAAI;MACzB,CAAC,MACI;QACD,IAAIA,IAAI,CAACyF,UAAU,EAAE;UACjBzF,IAAI,CAACyF,UAAU,CAACzF,IAAI,CAAC;QACzB,CAAC,MACI,IAAIA,IAAI,CAACG,IAAI,IAAI4B,SAAS,EAAE;UAC7BH,iBAAiB,CAAC5B,IAAI,CAAC;QAC3B,CAAC,MACI;UACD,MAAM,IAAIzC,KAAK,CAAC,6BAA6B,CAAC;QAClD;MACJ;MACA,OAAOgI,UAAU;IACrB;IACArE,UAAUA,CAACmE,UAAU,EAAErF,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAC/C,OAAO,IAAI,CAAC8E,aAAa,GACnB,IAAI,CAACA,aAAa,CAACxB,YAAY,CAAC,IAAI,CAACyB,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEU,UAAU,EAAErF,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC,GACvHK,IAAI,CAACb,QAAQ,CAACmG,KAAK,CAAC5F,SAAS,EAAEC,SAAS,CAAC;IACnD;IACAwC,UAAUA,CAACkD,UAAU,EAAErF,IAAI,EAAE;MACzB,IAAI0F,KAAK;MACT,IAAI,IAAI,CAACd,aAAa,EAAE;QACpBc,KAAK,GAAG,IAAI,CAACd,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC2B,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEO,UAAU,EAAErF,IAAI,CAAC;MAC7G,CAAC,MACI;QACD,IAAI,CAACA,IAAI,CAACgB,QAAQ,EAAE;UAChB,MAAMzD,KAAK,CAAC,wBAAwB,CAAC;QACzC;QACAmI,KAAK,GAAG1F,IAAI,CAACgB,QAAQ,CAAChB,IAAI,CAAC;MAC/B;MACA,OAAO0F,KAAK;IAChB;IACA3C,OAAOA,CAACsC,UAAU,EAAEM,OAAO,EAAE;MACzB;MACA;MACA,IAAI;QACA,IAAI,CAACZ,UAAU,IACX,IAAI,CAACA,UAAU,CAACrC,SAAS,CAAC,IAAI,CAACsC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAEG,UAAU,EAAEM,OAAO,CAAC;MAChG,CAAC,CACD,OAAOhE,GAAG,EAAE;QACR,IAAI,CAAC7B,WAAW,CAACuF,UAAU,EAAE1D,GAAG,CAAC;MACrC;IACJ;IACA;IACAJ,gBAAgBA,CAACpB,IAAI,EAAEmC,KAAK,EAAE;MAC1B,MAAMsD,MAAM,GAAG,IAAI,CAACxC,WAAW;MAC/B,MAAMyC,IAAI,GAAGD,MAAM,CAACzF,IAAI,CAAC;MACzB,MAAM2F,IAAI,GAAIF,MAAM,CAACzF,IAAI,CAAC,GAAG0F,IAAI,GAAGvD,KAAM;MAC1C,IAAIwD,IAAI,GAAG,CAAC,EAAE;QACV,MAAM,IAAIvI,KAAK,CAAC,0CAA0C,CAAC;MAC/D;MACA,IAAIsI,IAAI,IAAI,CAAC,IAAIC,IAAI,IAAI,CAAC,EAAE;QACxB,MAAMH,OAAO,GAAG;UACZ5D,SAAS,EAAE6D,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClClF,SAAS,EAAEkF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCnF,SAAS,EAAEmF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCG,MAAM,EAAE5F;QACZ,CAAC;QACD,IAAI,CAAC4C,OAAO,CAAC,IAAI,CAACrB,KAAK,EAAEiE,OAAO,CAAC;MACrC;IACJ;EACJ;EACA,MAAM7D,QAAQ,CAAC;IACXtD,WAAWA,CAAC2B,IAAI,EAAEf,MAAM,EAAED,QAAQ,EAAE6G,OAAO,EAAEP,UAAU,EAAEzE,QAAQ,EAAE;MAC/D;MACA,IAAI,CAACU,KAAK,GAAG,IAAI;MACjB,IAAI,CAACW,QAAQ,GAAG,CAAC;MACjB;MACA,IAAI,CAACf,cAAc,GAAG,IAAI;MAC1B;MACA,IAAI,CAAC2E,MAAM,GAAG,cAAc;MAC5B,IAAI,CAAC9F,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACf,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACgB,IAAI,GAAG4F,OAAO;MACnB,IAAI,CAACP,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACzE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC7B,QAAQ,EAAE;QACX,MAAM,IAAI5B,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;MACxB,MAAM+G,IAAI,GAAG,IAAI;MACjB;MACA,IAAI/F,IAAI,KAAKM,SAAS,IAAIuF,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;QAC/C,IAAI,CAACvG,MAAM,GAAGkC,QAAQ,CAACZ,UAAU;MACrC,CAAC,MACI;QACD,IAAI,CAACtB,MAAM,GAAG,YAAY;UACtB,OAAOkC,QAAQ,CAACZ,UAAU,CAACkF,IAAI,CAAC1J,MAAM,EAAEwJ,IAAI,EAAE,IAAI,EAAE1G,SAAS,CAAC;QAClE,CAAC;MACL;IACJ;IACA,OAAO0B,UAAUA,CAAClB,IAAI,EAAE6C,MAAM,EAAEwD,IAAI,EAAE;MAClC,IAAI,CAACrG,IAAI,EAAE;QACPA,IAAI,GAAG,IAAI;MACf;MACAsG,yBAAyB,EAAE;MAC3B,IAAI;QACAtG,IAAI,CAACqC,QAAQ,EAAE;QACf,OAAOrC,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE6C,MAAM,EAAEwD,IAAI,CAAC;MAChD,CAAC,SACO;QACJ,IAAIC,yBAAyB,IAAI,CAAC,EAAE;UAChCC,mBAAmB,CAAC,CAAC;QACzB;QACAD,yBAAyB,EAAE;MAC/B;IACJ;IACA,IAAI7I,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACiE,KAAK;IACrB;IACA,IAAInB,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC0F,MAAM;IACtB;IACAO,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC3F,aAAa,CAACL,YAAY,EAAEY,UAAU,CAAC;IAChD;IACA;IACAP,aAAaA,CAAC4F,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC3C,IAAI,IAAI,CAACV,MAAM,KAAKS,UAAU,IAAI,IAAI,CAACT,MAAM,KAAKU,UAAU,EAAE;QAC1D,IAAI,CAACV,MAAM,GAAGQ,OAAO;QACrB,IAAIA,OAAO,IAAIjG,YAAY,EAAE;UACzB,IAAI,CAACc,cAAc,GAAG,IAAI;QAC9B;MACJ,CAAC,MACI;QACD,MAAM,IAAI/D,KAAK,CAAE,GAAE,IAAI,CAAC4C,IAAK,KAAI,IAAI,CAACf,MAAO,6BAA4BqH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,OAAO,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAG,UAAS,IAAI,CAACV,MAAO,IAAG,CAAC;MAC/L;IACJ;IACAW,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAACxG,IAAI,IAAI,OAAO,IAAI,CAACA,IAAI,CAACyG,QAAQ,KAAK,WAAW,EAAE;QACxD,OAAO,IAAI,CAACzG,IAAI,CAACyG,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxC,CAAC,MACI;QACD,OAAOE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;MAC/C;IACJ;IACA;IACA;IACAY,MAAMA,CAAA,EAAG;MACL,OAAO;QACH7G,IAAI,EAAE,IAAI,CAACA,IAAI;QACfI,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBnB,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB3B,IAAI,EAAE,IAAI,CAACA,IAAI,CAACZ,IAAI;QACpBwF,QAAQ,EAAE,IAAI,CAACA;MACnB,CAAC;IACL;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAM4E,gBAAgB,GAAGrK,UAAU,CAAC,YAAY,CAAC;EACjD,MAAMsK,aAAa,GAAGtK,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAMuK,UAAU,GAAGvK,UAAU,CAAC,MAAM,CAAC;EACrC,IAAIwK,eAAe,GAAG,EAAE;EACxB,IAAIC,yBAAyB,GAAG,KAAK;EACrC,IAAIC,2BAA2B;EAC/B,SAASC,uBAAuBA,CAACC,IAAI,EAAE;IACnC,IAAI,CAACF,2BAA2B,EAAE;MAC9B,IAAI5K,MAAM,CAACwK,aAAa,CAAC,EAAE;QACvBI,2BAA2B,GAAG5K,MAAM,CAACwK,aAAa,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;MAClE;IACJ;IACA,IAAIH,2BAA2B,EAAE;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAU,CAAC;MACxD,IAAI,CAACO,UAAU,EAAE;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAM,CAAC;MACpD;MACAI,UAAU,CAACtB,IAAI,CAACkB,2BAA2B,EAAEE,IAAI,CAAC;IACtD,CAAC,MACI;MACD9K,MAAM,CAACuK,gBAAgB,CAAC,CAACO,IAAI,EAAE,CAAC,CAAC;IACrC;EACJ;EACA,SAAS5F,iBAAiBA,CAAC5B,IAAI,EAAE;IAC7B;IACA;IACA,IAAIsG,yBAAyB,KAAK,CAAC,IAAIc,eAAe,CAAC5E,MAAM,KAAK,CAAC,EAAE;MACjE;MACA+E,uBAAuB,CAAChB,mBAAmB,CAAC;IAChD;IACAvG,IAAI,IAAIoH,eAAe,CAAC5B,IAAI,CAACxF,IAAI,CAAC;EACtC;EACA,SAASuG,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACc,yBAAyB,EAAE;MAC5BA,yBAAyB,GAAG,IAAI;MAChC,OAAOD,eAAe,CAAC5E,MAAM,EAAE;QAC3B,MAAMmF,KAAK,GAAGP,eAAe;QAC7BA,eAAe,GAAG,EAAE;QACpB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,KAAK,CAACnF,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAMvC,IAAI,GAAG2H,KAAK,CAACpF,CAAC,CAAC;UACrB,IAAI;YACAvC,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACvC,CAAC,CACD,OAAOH,KAAK,EAAE;YACVxB,IAAI,CAACuJ,gBAAgB,CAAC/H,KAAK,CAAC;UAChC;QACJ;MACJ;MACAxB,IAAI,CAACwJ,kBAAkB,CAAC,CAAC;MACzBR,yBAAyB,GAAG,KAAK;IACrC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMpH,OAAO,GAAG;IAAEpD,IAAI,EAAE;EAAU,CAAC;EACnC,MAAM2D,YAAY,GAAG,cAAc;IAAEY,UAAU,GAAG,YAAY;IAAEN,SAAS,GAAG,WAAW;IAAEF,OAAO,GAAG,SAAS;IAAEwB,SAAS,GAAG,WAAW;IAAEjB,OAAO,GAAG,SAAS;EAC1J,MAAMY,SAAS,GAAG,WAAW;IAAErB,SAAS,GAAG,WAAW;IAAED,SAAS,GAAG,WAAW;EAC/E,MAAMnD,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMe,IAAI,GAAG;IACTyJ,MAAM,EAAElL,UAAU;IAClBmL,gBAAgB,EAAEA,CAAA,KAAMnK,iBAAiB;IACzCgK,gBAAgB,EAAEI,IAAI;IACtBH,kBAAkB,EAAEG,IAAI;IACxBpG,iBAAiB,EAAEA,iBAAiB;IACpCqG,iBAAiB,EAAEA,CAAA,KAAM,CAAC7K,QAAQ,CAACR,UAAU,CAAC,iCAAiC,CAAC,CAAC;IACjFsL,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,iBAAiB,EAAEH,IAAI;IACvBI,WAAW,EAAEA,CAAA,KAAMJ,IAAI;IACvBK,aAAa,EAAEA,CAAA,KAAM,EAAE;IACvBC,SAAS,EAAEA,CAAA,KAAMN,IAAI;IACrBO,cAAc,EAAEA,CAAA,KAAMP,IAAI;IAC1BQ,mBAAmB,EAAEA,CAAA,KAAMR,IAAI;IAC/BS,UAAU,EAAEA,CAAA,KAAM,KAAK;IACvBC,gBAAgB,EAAEA,CAAA,KAAMzH,SAAS;IACjC0H,oBAAoB,EAAEA,CAAA,KAAMX,IAAI;IAChCY,8BAA8B,EAAEA,CAAA,KAAM3H,SAAS;IAC/C4H,YAAY,EAAEA,CAAA,KAAM5H,SAAS;IAC7B6H,UAAU,EAAEA,CAAA,KAAM,EAAE;IACpBC,UAAU,EAAEA,CAAA,KAAMf,IAAI;IACtBgB,mBAAmB,EAAEA,CAAA,KAAMhB,IAAI;IAC/BiB,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,qBAAqB,EAAEA,CAAA,KAAMlB,IAAI;IACjCmB,iBAAiB,EAAEA,CAAA,KAAMnB,IAAI;IAC7BoB,cAAc,EAAEA,CAAA,KAAMpB,IAAI;IAC1BT,uBAAuB,EAAEA;EAC7B,CAAC;EACD,IAAI3J,iBAAiB,GAAG;IAAED,MAAM,EAAE,IAAI;IAAEF,IAAI,EAAE,IAAIL,QAAQ,CAAC,IAAI,EAAE,IAAI;EAAE,CAAC;EACxE,IAAIU,YAAY,GAAG,IAAI;EACvB,IAAIwI,yBAAyB,GAAG,CAAC;EACjC,SAAS0B,IAAIA,CAAA,EAAG,CAAE;EAClB9K,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC,OAAOE,QAAQ;AACnB;AAEA,SAASiM,QAAQA,CAAA,EAAG;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM3M,MAAM,GAAGC,UAAU;EACzB,MAAMwB,cAAc,GAAGzB,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;EAC7E,IAAIF,MAAM,CAAC,MAAM,CAAC,KAAKyB,cAAc,IAAI,OAAOzB,MAAM,CAAC,MAAM,CAAC,CAACE,UAAU,KAAK,UAAU,CAAC,EAAE;IACvF,MAAM,IAAIW,KAAK,CAAC,sBAAsB,CAAC;EAC3C;EACA;EACAb,MAAM,CAAC,MAAM,CAAC,KAAKK,QAAQ,CAAC,CAAC;EAC7B,OAAOL,MAAM,CAAC,MAAM,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkM,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;AACtE;AACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;AAClD;AACA,MAAMC,oBAAoB,GAAG1C,MAAM,CAAC2C,cAAc;AAClD;AACA,MAAMZ,YAAY,GAAG/B,MAAM,CAAC4C,MAAM;AAClC;AACA,MAAMZ,UAAU,GAAGa,KAAK,CAAC5C,SAAS,CAAC6C,KAAK;AACxC;AACA,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,yBAAyB,GAAG,qBAAqB;AACvD;AACA,MAAMC,8BAA8B,GAAGnN,UAAU,CAACiN,sBAAsB,CAAC;AACzE;AACA,MAAMG,iCAAiC,GAAGpN,UAAU,CAACkN,yBAAyB,CAAC;AAC/E;AACA,MAAMG,QAAQ,GAAG,MAAM;AACvB;AACA,MAAMC,SAAS,GAAG,OAAO;AACzB;AACA,MAAMC,kBAAkB,GAAGvN,UAAU,CAAC,EAAE,CAAC;AACzC,SAASoM,mBAAmBA,CAAC7J,QAAQ,EAAEC,MAAM,EAAE;EAC3C,OAAOgL,IAAI,CAAC1M,OAAO,CAACwB,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;AAC9C;AACA,SAASiL,gCAAgCA,CAACjL,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,EAAE;EAC5F,OAAOmI,IAAI,CAAC1M,OAAO,CAACsE,iBAAiB,CAAC5C,MAAM,EAAED,QAAQ,EAAEiB,IAAI,EAAEyB,cAAc,EAAEI,YAAY,CAAC;AAC/F;AACA,MAAMqI,UAAU,GAAG1N,UAAU;AAC7B,MAAM2N,cAAc,GAAG,OAAOC,MAAM,KAAK,WAAW;AACpD,MAAMC,cAAc,GAAGF,cAAc,GAAGC,MAAM,GAAGvJ,SAAS;AAC1D,MAAMyJ,OAAO,GAAIH,cAAc,IAAIE,cAAc,IAAK9N,UAAU;AAChE,MAAMgO,gBAAgB,GAAG,iBAAiB;AAC1C,SAAStC,aAAaA,CAAChC,IAAI,EAAEjH,MAAM,EAAE;EACjC,KAAK,IAAImD,CAAC,GAAG8D,IAAI,CAAC7D,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAI,OAAO8D,IAAI,CAAC9D,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B8D,IAAI,CAAC9D,CAAC,CAAC,GAAGyG,mBAAmB,CAAC3C,IAAI,CAAC9D,CAAC,CAAC,EAAEnD,MAAM,GAAG,GAAG,GAAGmD,CAAC,CAAC;IAC5D;EACJ;EACA,OAAO8D,IAAI;AACf;AACA,SAASuE,cAAcA,CAAC7D,SAAS,EAAE8D,OAAO,EAAE;EACxC,MAAMzL,MAAM,GAAG2H,SAAS,CAACvI,WAAW,CAAC,MAAM,CAAC;EAC5C,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,OAAO,CAACrI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAM1F,IAAI,GAAGgO,OAAO,CAACtI,CAAC,CAAC;IACvB,MAAMI,QAAQ,GAAGoE,SAAS,CAAClK,IAAI,CAAC;IAChC,IAAI8F,QAAQ,EAAE;MACV,MAAMmI,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAS,EAAElK,IAAI,CAAC;MACrE,IAAI,CAACkO,kBAAkB,CAACD,aAAa,CAAC,EAAE;QACpC;MACJ;MACA/D,SAAS,CAAClK,IAAI,CAAC,GAAG,CAAE8F,QAAQ,IAAK;QAC7B,MAAMqI,OAAO,GAAG,SAAAA,CAAA,EAAY;UACxB,OAAOrI,QAAQ,CAAC2C,KAAK,CAAC,IAAI,EAAE+C,aAAa,CAAC7I,SAAS,EAAEJ,MAAM,GAAG,GAAG,GAAGvC,IAAI,CAAC,CAAC;QAC9E,CAAC;QACDqM,qBAAqB,CAAC8B,OAAO,EAAErI,QAAQ,CAAC;QACxC,OAAOqI,OAAO;MAClB,CAAC,EAAErI,QAAQ,CAAC;IAChB;EACJ;AACJ;AACA,SAASoI,kBAAkBA,CAACE,YAAY,EAAE;EACtC,IAAI,CAACA,YAAY,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAIA,YAAY,CAACC,QAAQ,KAAK,KAAK,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,OAAO,EAAE,OAAOD,YAAY,CAACnM,GAAG,KAAK,UAAU,IAAI,OAAOmM,YAAY,CAACE,GAAG,KAAK,WAAW,CAAC;AAC/F;AACA,MAAMC,WAAW,GAAG,OAAOC,iBAAiB,KAAK,WAAW,IAAInF,IAAI,YAAYmF,iBAAiB;AACjG;AACA;AACA,MAAMC,MAAM,GAAG,EAAE,IAAI,IAAIZ,OAAO,CAAC,IAC7B,OAAOA,OAAO,CAACa,OAAO,KAAK,WAAW,IACtCb,OAAO,CAACa,OAAO,CAAC3E,QAAQ,CAAC,CAAC,KAAK,kBAAkB;AACrD,MAAM4E,SAAS,GAAG,CAACF,MAAM,IAAI,CAACF,WAAW,IAAI,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAa,CAAC,CAAC;AAChG;AACA;AACA;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAO,KAAK,WAAW,IAChDb,OAAO,CAACa,OAAO,CAAC3E,QAAQ,CAAC,CAAC,KAAK,kBAAkB,IACjD,CAACwE,WAAW,IACZ,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAa,CAAC,CAAC;AACvD,MAAMiB,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAMC,wBAAwB,GAAGrB,UAAU,CAAC,qBAAqB,CAAC;AAClE,MAAMsB,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAInB,OAAO,CAACmB,KAAK;EAC9B,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACA,IAAIC,eAAe,GAAGJ,sBAAsB,CAACG,KAAK,CAAC1L,IAAI,CAAC;EACxD,IAAI,CAAC2L,eAAe,EAAE;IAClBA,eAAe,GAAGJ,sBAAsB,CAACG,KAAK,CAAC1L,IAAI,CAAC,GAAGmK,UAAU,CAAC,aAAa,GAAGuB,KAAK,CAAC1L,IAAI,CAAC;EACjG;EACA,MAAM0C,MAAM,GAAG,IAAI,IAAIgJ,KAAK,CAAChJ,MAAM,IAAI6H,OAAO;EAC9C,MAAMqB,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAe,CAAC;EACxC,IAAIE,MAAM;EACV,IAAIR,SAAS,IAAI3I,MAAM,KAAK4H,cAAc,IAAIoB,KAAK,CAAC1L,IAAI,KAAK,OAAO,EAAE;IAClE;IACA;IACA;IACA,MAAM8L,UAAU,GAAGJ,KAAK;IACxBG,MAAM,GACFD,QAAQ,IACJA,QAAQ,CAAC3F,IAAI,CAAC,IAAI,EAAE6F,UAAU,CAACC,OAAO,EAAED,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,EAAEH,UAAU,CAACI,KAAK,EAAEJ,UAAU,CAACpM,KAAK,CAAC;IAC3H,IAAImM,MAAM,KAAK,IAAI,EAAE;MACjBH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ,CAAC,MACI;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACzG,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;IACpD;IACA;IACA;IACA;IACA;IACA;IACAqM,KAAK,CAAC1L,IAAI,KAAK,cAAc;IACzB;IACA;IACA;IACA;IACA;IACAuK,OAAO,CAACiB,wBAAwB,CAAC;IACjC;IACA;IACA,OAAOK,MAAM,KAAK,QAAQ,EAAE;MAC5BH,KAAK,CAACU,WAAW,GAAGP,MAAM;IAC9B,CAAC,MACI,IAAIA,MAAM,IAAI/K,SAAS,IAAI,CAAC+K,MAAM,EAAE;MACrCH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA,OAAON,MAAM;AACjB,CAAC;AACD,SAASQ,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE3F,SAAS,EAAE;EACzC,IAAI4F,IAAI,GAAG/D,8BAA8B,CAAC6D,GAAG,EAAEC,IAAI,CAAC;EACpD,IAAI,CAACC,IAAI,IAAI5F,SAAS,EAAE;IACpB;IACA,MAAM+D,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAS,EAAE2F,IAAI,CAAC;IACrE,IAAI5B,aAAa,EAAE;MACf6B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAK,CAAC;IACnD;EACJ;EACA;EACA;EACA,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACE,YAAY,EAAE;IAC7B;EACJ;EACA,MAAMC,mBAAmB,GAAGxC,UAAU,CAAC,IAAI,GAAGoC,IAAI,GAAG,SAAS,CAAC;EAC/D,IAAID,GAAG,CAACvO,cAAc,CAAC4O,mBAAmB,CAAC,IAAIL,GAAG,CAACK,mBAAmB,CAAC,EAAE;IACrE;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,OAAOH,IAAI,CAACzB,QAAQ;EACpB,OAAOyB,IAAI,CAACjH,KAAK;EACjB,MAAMqH,eAAe,GAAGJ,IAAI,CAAC7N,GAAG;EAChC,MAAMkO,eAAe,GAAGL,IAAI,CAACxB,GAAG;EAChC;EACA,MAAM8B,SAAS,GAAGP,IAAI,CAAC9C,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAIkC,eAAe,GAAGJ,sBAAsB,CAACuB,SAAS,CAAC;EACvD,IAAI,CAACnB,eAAe,EAAE;IAClBA,eAAe,GAAGJ,sBAAsB,CAACuB,SAAS,CAAC,GAAG3C,UAAU,CAAC,aAAa,GAAG2C,SAAS,CAAC;EAC/F;EACAN,IAAI,CAACxB,GAAG,GAAG,UAAU+B,QAAQ,EAAE;IAC3B;IACA;IACA,IAAIrK,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI4J,GAAG,KAAK/B,OAAO,EAAE;MAC5B7H,MAAM,GAAG6H,OAAO;IACpB;IACA,IAAI,CAAC7H,MAAM,EAAE;MACT;IACJ;IACA,MAAMsK,aAAa,GAAGtK,MAAM,CAACiJ,eAAe,CAAC;IAC7C,IAAI,OAAOqB,aAAa,KAAK,UAAU,EAAE;MACrCtK,MAAM,CAACuK,mBAAmB,CAACH,SAAS,EAAErB,MAAM,CAAC;IACjD;IACA;IACA;IACAoB,eAAe,IAAIA,eAAe,CAAC5G,IAAI,CAACvD,MAAM,EAAE,IAAI,CAAC;IACrDA,MAAM,CAACiJ,eAAe,CAAC,GAAGoB,QAAQ;IAClC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAChCrK,MAAM,CAACwK,gBAAgB,CAACJ,SAAS,EAAErB,MAAM,EAAE,KAAK,CAAC;IACrD;EACJ,CAAC;EACD;EACA;EACAe,IAAI,CAAC7N,GAAG,GAAG,YAAY;IACnB;IACA;IACA,IAAI+D,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI4J,GAAG,KAAK/B,OAAO,EAAE;MAC5B7H,MAAM,GAAG6H,OAAO;IACpB;IACA,IAAI,CAAC7H,MAAM,EAAE;MACT,OAAO,IAAI;IACf;IACA,MAAMkJ,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAe,CAAC;IACxC,IAAIC,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB,CAAC,MACI,IAAIgB,eAAe,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAIrH,KAAK,GAAGqH,eAAe,CAAC3G,IAAI,CAAC,IAAI,CAAC;MACtC,IAAIV,KAAK,EAAE;QACPiH,IAAI,CAACxB,GAAG,CAAC/E,IAAI,CAAC,IAAI,EAAEV,KAAK,CAAC;QAC1B,IAAI,OAAO7C,MAAM,CAAC8H,gBAAgB,CAAC,KAAK,UAAU,EAAE;UAChD9H,MAAM,CAACyK,eAAe,CAACZ,IAAI,CAAC;QAChC;QACA,OAAOhH,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDiD,oBAAoB,CAAC8D,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACrCF,GAAG,CAACK,mBAAmB,CAAC,GAAG,IAAI;AACnC;AACA,SAAS3E,iBAAiBA,CAACsE,GAAG,EAAE9N,UAAU,EAAEoI,SAAS,EAAE;EACnD,IAAIpI,UAAU,EAAE;IACZ,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,UAAU,CAAC6D,MAAM,EAAED,CAAC,EAAE,EAAE;MACxCiK,aAAa,CAACC,GAAG,EAAE,IAAI,GAAG9N,UAAU,CAAC4D,CAAC,CAAC,EAAEwE,SAAS,CAAC;IACvD;EACJ,CAAC,MACI;IACD,MAAMwG,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMb,IAAI,IAAID,GAAG,EAAE;MACpB,IAAIC,IAAI,CAAC9C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;QAC1B2D,YAAY,CAAC/H,IAAI,CAACkH,IAAI,CAAC;MAC3B;IACJ;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAAC/K,MAAM,EAAEgL,CAAC,EAAE,EAAE;MAC1ChB,aAAa,CAACC,GAAG,EAAEc,YAAY,CAACC,CAAC,CAAC,EAAEzG,SAAS,CAAC;IAClD;EACJ;AACJ;AACA,MAAM0G,mBAAmB,GAAGnD,UAAU,CAAC,kBAAkB,CAAC;AAC1D;AACA,SAASvB,UAAUA,CAAC2E,SAAS,EAAE;EAC3B,MAAMC,aAAa,GAAGjD,OAAO,CAACgD,SAAS,CAAC;EACxC,IAAI,CAACC,aAAa,EACd;EACJ;EACAjD,OAAO,CAACJ,UAAU,CAACoD,SAAS,CAAC,CAAC,GAAGC,aAAa;EAC9CjD,OAAO,CAACgD,SAAS,CAAC,GAAG,YAAY;IAC7B,MAAME,CAAC,GAAGvF,aAAa,CAAC7I,SAAS,EAAEkO,SAAS,CAAC;IAC7C,QAAQE,CAAC,CAACpL,MAAM;MACZ,KAAK,CAAC;QACF,IAAI,CAACiL,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAAC,CAAC;QAC/C;MACJ,KAAK,CAAC;QACF,IAAI,CAACF,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;QACI,MAAM,IAAIrQ,KAAK,CAAC,oBAAoB,CAAC;IAC7C;EACJ,CAAC;EACD;EACA2L,qBAAqB,CAACwB,OAAO,CAACgD,SAAS,CAAC,EAAEC,aAAa,CAAC;EACxD,MAAME,QAAQ,GAAG,IAAIF,aAAa,CAAC,YAAY,CAAE,CAAC,CAAC;EACnD,IAAIjB,IAAI;EACR,KAAKA,IAAI,IAAImB,QAAQ,EAAE;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAgB,IAAIhB,IAAI,KAAK,cAAc,EACzD;IACJ,CAAC,UAAUA,IAAI,EAAE;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAI,CAAC,KAAK,UAAU,EAAE;QACtChC,OAAO,CAACgD,SAAS,CAAC,CAAC3G,SAAS,CAAC2F,IAAI,CAAC,GAAG,YAAY;UAC7C,OAAO,IAAI,CAACe,mBAAmB,CAAC,CAACf,IAAI,CAAC,CAACpH,KAAK,CAAC,IAAI,CAACmI,mBAAmB,CAAC,EAAEjO,SAAS,CAAC;QACtF,CAAC;MACL,CAAC,MACI;QACDmJ,oBAAoB,CAAC+B,OAAO,CAACgD,SAAS,CAAC,CAAC3G,SAAS,EAAE2F,IAAI,EAAE;UACrDvB,GAAG,EAAE,SAAAA,CAAUnN,EAAE,EAAE;YACf,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;cAC1B,IAAI,CAACyP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAG1D,mBAAmB,CAAChL,EAAE,EAAE0P,SAAS,GAAG,GAAG,GAAGhB,IAAI,CAAC;cACjF;cACA;cACA;cACAxD,qBAAqB,CAAC,IAAI,CAACuE,mBAAmB,CAAC,CAACf,IAAI,CAAC,EAAE1O,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,IAAI,CAACyP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAG1O,EAAE;YACxC;UACJ,CAAC;UACDc,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAO,IAAI,CAAC2O,mBAAmB,CAAC,CAACf,IAAI,CAAC;UAC1C;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAEA,IAAI,CAAC;EACZ;EACA,KAAKA,IAAI,IAAIiB,aAAa,EAAE;IACxB,IAAIjB,IAAI,KAAK,WAAW,IAAIiB,aAAa,CAACzP,cAAc,CAACwO,IAAI,CAAC,EAAE;MAC5DhC,OAAO,CAACgD,SAAS,CAAC,CAAChB,IAAI,CAAC,GAAGiB,aAAa,CAACjB,IAAI,CAAC;IAClD;EACJ;AACJ;AACA,SAAStE,WAAWA,CAACvF,MAAM,EAAEhG,IAAI,EAAEiR,OAAO,EAAE;EACxC,IAAIC,KAAK,GAAGlL,MAAM;EAClB,OAAOkL,KAAK,IAAI,CAACA,KAAK,CAAC7P,cAAc,CAACrB,IAAI,CAAC,EAAE;IACzCkR,KAAK,GAAGvE,oBAAoB,CAACuE,KAAK,CAAC;EACvC;EACA,IAAI,CAACA,KAAK,IAAIlL,MAAM,CAAChG,IAAI,CAAC,EAAE;IACxB;IACAkR,KAAK,GAAGlL,MAAM;EAClB;EACA,MAAMmL,YAAY,GAAG1D,UAAU,CAACzN,IAAI,CAAC;EACrC,IAAI8F,QAAQ,GAAG,IAAI;EACnB,IAAIoL,KAAK,KAAK,EAAEpL,QAAQ,GAAGoL,KAAK,CAACC,YAAY,CAAC,CAAC,IAAI,CAACD,KAAK,CAAC7P,cAAc,CAAC8P,YAAY,CAAC,CAAC,EAAE;IACrFrL,QAAQ,GAAGoL,KAAK,CAACC,YAAY,CAAC,GAAGD,KAAK,CAAClR,IAAI,CAAC;IAC5C;IACA;IACA,MAAM8P,IAAI,GAAGoB,KAAK,IAAInF,8BAA8B,CAACmF,KAAK,EAAElR,IAAI,CAAC;IACjE,IAAIkO,kBAAkB,CAAC4B,IAAI,CAAC,EAAE;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAACnL,QAAQ,EAAEqL,YAAY,EAAEnR,IAAI,CAAC;MAC3DkR,KAAK,CAAClR,IAAI,CAAC,GAAG,YAAY;QACtB,OAAOoR,aAAa,CAAC,IAAI,EAAEzO,SAAS,CAAC;MACzC,CAAC;MACD0J,qBAAqB,CAAC6E,KAAK,CAAClR,IAAI,CAAC,EAAE8F,QAAQ,CAAC;IAChD;EACJ;EACA,OAAOA,QAAQ;AACnB;AACA;AACA,SAAS4F,cAAcA,CAACkE,GAAG,EAAEyB,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAIC,SAAS,GAAG,IAAI;EACpB,SAAS5M,YAAYA,CAACxB,IAAI,EAAE;IACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAI;IACtBA,IAAI,CAACiG,IAAI,CAACjG,IAAI,CAACiO,KAAK,CAAC,GAAG,YAAY;MAChCrO,IAAI,CAACJ,MAAM,CAAC0F,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;IACtC,CAAC;IACD4O,SAAS,CAAC9I,KAAK,CAAClF,IAAI,CAACyC,MAAM,EAAEzC,IAAI,CAACiG,IAAI,CAAC;IACvC,OAAOrG,IAAI;EACf;EACAoO,SAAS,GAAGhG,WAAW,CAACqE,GAAG,EAAEyB,QAAQ,EAAGvL,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IACvE,MAAMiI,IAAI,GAAGH,WAAW,CAACjI,IAAI,EAAEG,IAAI,CAAC;IACpC,IAAIiI,IAAI,CAACD,KAAK,IAAI,CAAC,IAAI,OAAOhI,IAAI,CAACiI,IAAI,CAACD,KAAK,CAAC,KAAK,UAAU,EAAE;MAC3D,OAAOhE,gCAAgC,CAACiE,IAAI,CAACzR,IAAI,EAAEwJ,IAAI,CAACiI,IAAI,CAACD,KAAK,CAAC,EAAEC,IAAI,EAAE9M,YAAY,CAAC;IAC5F,CAAC,MACI;MACD;MACA,OAAOmB,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IACrC;EACJ,CAAC,CAAC;AACN;AACA,SAAS6C,qBAAqBA,CAAC8B,OAAO,EAAEuD,QAAQ,EAAE;EAC9CvD,OAAO,CAACV,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAGiE,QAAQ;AACtD;AACA,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,QAAQ,GAAG,KAAK;AACpB,SAASC,IAAIA,CAAA,EAAG;EACZ,IAAI;IACA,MAAMC,EAAE,GAAGlE,cAAc,CAACmE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7D,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAOjP,KAAK,EAAE,CAAE;EAChB,OAAO,KAAK;AAChB;AACA,SAAS4I,UAAUA,CAAA,EAAG;EAClB,IAAI+F,kBAAkB,EAAE;IACpB,OAAOC,QAAQ;EACnB;EACAD,kBAAkB,GAAG,IAAI;EACzB,IAAI;IACA,MAAMG,EAAE,GAAGlE,cAAc,CAACmE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3FL,QAAQ,GAAG,IAAI;IACnB;EACJ,CAAC,CACD,OAAO5O,KAAK,EAAE,CAAE;EAChB,OAAO4O,QAAQ;AACnB;AACA,SAASM,UAAUA,CAACrJ,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA,SAASsJ,QAAQA,CAACtJ,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuJ,gBAAgB,GAAG,KAAK;AAC5B,IAAI,OAAOzE,MAAM,KAAK,WAAW,EAAE;EAC/B,IAAI;IACA,MAAMxE,OAAO,GAAGc,MAAM,CAACyC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACjDzK,GAAG,EAAE,SAAAA,CAAA,EAAY;QACbmQ,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAzE,MAAM,CAAC6C,gBAAgB,CAAC,MAAM,EAAErH,OAAO,EAAEA,OAAO,CAAC;IACjDwE,MAAM,CAAC4C,mBAAmB,CAAC,MAAM,EAAEpH,OAAO,EAAEA,OAAO,CAAC;EACxD,CAAC,CACD,OAAOrE,GAAG,EAAE;IACRsN,gBAAgB,GAAG,KAAK;EAC5B;AACJ;AACA;AACA,MAAMC,8BAA8B,GAAG;EACnC/I,IAAI,EAAE;AACV,CAAC;AACD,MAAMgJ,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGnF,kBAAkB,GAAG,qBAAqB,CAAC;AAC3F,MAAMoF,4BAA4B,GAAGjF,UAAU,CAAC,oBAAoB,CAAC;AACrE,SAASkF,iBAAiBA,CAACvC,SAAS,EAAEwC,iBAAiB,EAAE;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACxC,SAAS,CAAC,GAAGA,SAAS,IAAI/C,SAAS;EACjG,MAAMyF,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACxC,SAAS,CAAC,GAAGA,SAAS,IAAIhD,QAAQ;EAC/F,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGuF,cAAc;EAClD,MAAME,aAAa,GAAGzF,kBAAkB,GAAGwF,aAAa;EACxDR,oBAAoB,CAAClC,SAAS,CAAC,GAAG,CAAC,CAAC;EACpCkC,oBAAoB,CAAClC,SAAS,CAAC,CAAC/C,SAAS,CAAC,GAAGpC,MAAM;EACnDqH,oBAAoB,CAAClC,SAAS,CAAC,CAAChD,QAAQ,CAAC,GAAG2F,aAAa;AAC7D;AACA,SAAS1H,gBAAgBA,CAACwC,OAAO,EAAEmF,GAAG,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAAG,IAAKpG,sBAAsB;EACvF,MAAMqG,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAAE,IAAKrG,yBAAyB;EAC5F,MAAMsG,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAAS,IAAK,gBAAgB;EAC7F,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAAK,IAAK,oBAAoB;EACxG,MAAMC,0BAA0B,GAAGlG,UAAU,CAAC0F,kBAAkB,CAAC;EACjE,MAAMS,yBAAyB,GAAG,GAAG,GAAGT,kBAAkB,GAAG,GAAG;EAChE,MAAMU,sBAAsB,GAAG,iBAAiB;EAChD,MAAMC,6BAA6B,GAAG,GAAG,GAAGD,sBAAsB,GAAG,GAAG;EACxE,MAAMxP,UAAU,GAAG,SAAAA,CAAUlB,IAAI,EAAE6C,MAAM,EAAEgJ,KAAK,EAAE;IAC9C;IACA;IACA,IAAI7L,IAAI,CAAC4Q,SAAS,EAAE;MAChB;IACJ;IACA,MAAMjO,QAAQ,GAAG3C,IAAI,CAACb,QAAQ;IAC9B,IAAI,OAAOwD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACkO,WAAW,EAAE;MACtD;MACA7Q,IAAI,CAACb,QAAQ,GAAI0M,KAAK,IAAKlJ,QAAQ,CAACkO,WAAW,CAAChF,KAAK,CAAC;MACtD7L,IAAI,CAAC8Q,gBAAgB,GAAGnO,QAAQ;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI9C,KAAK;IACT,IAAI;MACAG,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE6C,MAAM,EAAE,CAACgJ,KAAK,CAAC,CAAC;IACtC,CAAC,CACD,OAAOlK,GAAG,EAAE;MACR9B,KAAK,GAAG8B,GAAG;IACf;IACA,MAAMqE,OAAO,GAAGhG,IAAI,CAACgG,OAAO;IAC5B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC+K,IAAI,EAAE;MACxD;MACA;MACA;MACA,MAAMpO,QAAQ,GAAG3C,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAACb,QAAQ;MAC9E0D,MAAM,CAACqN,qBAAqB,CAAC,CAAC9J,IAAI,CAACvD,MAAM,EAAEgJ,KAAK,CAAC1L,IAAI,EAAEwC,QAAQ,EAAEqD,OAAO,CAAC;IAC7E;IACA,OAAOnG,KAAK;EAChB,CAAC;EACD,SAASmR,cAAcA,CAACC,OAAO,EAAEpF,KAAK,EAAEqF,SAAS,EAAE;IAC/C;IACA;IACArF,KAAK,GAAGA,KAAK,IAAInB,OAAO,CAACmB,KAAK;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA;IACA;IACA,MAAMhJ,MAAM,GAAGoO,OAAO,IAAIpF,KAAK,CAAChJ,MAAM,IAAI6H,OAAO;IACjD,MAAMyG,KAAK,GAAGtO,MAAM,CAACsM,oBAAoB,CAACtD,KAAK,CAAC1L,IAAI,CAAC,CAAC+Q,SAAS,GAAGjH,QAAQ,GAAGC,SAAS,CAAC,CAAC;IACxF,IAAIiH,KAAK,EAAE;MACP,MAAMC,MAAM,GAAG,EAAE;MACjB;MACA;MACA,IAAID,KAAK,CAAC3O,MAAM,KAAK,CAAC,EAAE;QACpB,MAAMb,GAAG,GAAGT,UAAU,CAACiQ,KAAK,CAAC,CAAC,CAAC,EAAEtO,MAAM,EAAEgJ,KAAK,CAAC;QAC/ClK,GAAG,IAAIyP,MAAM,CAAC5L,IAAI,CAAC7D,GAAG,CAAC;MAC3B,CAAC,MACI;QACD;QACA;QACA;QACA,MAAM0P,SAAS,GAAGF,KAAK,CAACvH,KAAK,CAAC,CAAC;QAC/B,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8O,SAAS,CAAC7O,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,IAAIsJ,KAAK,IAAIA,KAAK,CAAC0D,4BAA4B,CAAC,KAAK,IAAI,EAAE;YACvD;UACJ;UACA,MAAM5N,GAAG,GAAGT,UAAU,CAACmQ,SAAS,CAAC9O,CAAC,CAAC,EAAEM,MAAM,EAAEgJ,KAAK,CAAC;UACnDlK,GAAG,IAAIyP,MAAM,CAAC5L,IAAI,CAAC7D,GAAG,CAAC;QAC3B;MACJ;MACA;MACA;MACA,IAAIyP,MAAM,CAAC5O,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM4O,MAAM,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,KAAK,IAAI7O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6O,MAAM,CAAC5O,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC,MAAMZ,GAAG,GAAGyP,MAAM,CAAC7O,CAAC,CAAC;UACrBsN,GAAG,CAACtI,uBAAuB,CAAC,MAAM;YAC9B,MAAM5F,GAAG;UACb,CAAC,CAAC;QACN;MACJ;IACJ;EACJ;EACA;EACA,MAAM2P,uBAAuB,GAAG,SAAAA,CAAUzF,KAAK,EAAE;IAC7C,OAAOmF,cAAc,CAAC,IAAI,EAAEnF,KAAK,EAAE,KAAK,CAAC;EAC7C,CAAC;EACD;EACA,MAAM0F,8BAA8B,GAAG,SAAAA,CAAU1F,KAAK,EAAE;IACpD,OAAOmF,cAAc,CAAC,IAAI,EAAEnF,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC;EACD,SAAS2F,uBAAuBA,CAAC/E,GAAG,EAAEsD,YAAY,EAAE;IAChD,IAAI,CAACtD,GAAG,EAAE;MACN,OAAO,KAAK;IAChB;IACA,IAAIgF,iBAAiB,GAAG,IAAI;IAC5B,IAAI1B,YAAY,IAAIA,YAAY,CAAC5J,IAAI,KAAKlF,SAAS,EAAE;MACjDwQ,iBAAiB,GAAG1B,YAAY,CAAC5J,IAAI;IACzC;IACA,MAAMuL,eAAe,GAAG3B,YAAY,IAAIA,YAAY,CAAC4B,EAAE;IACvD,IAAIxT,cAAc,GAAG,IAAI;IACzB,IAAI4R,YAAY,IAAIA,YAAY,CAAC6B,MAAM,KAAK3Q,SAAS,EAAE;MACnD9C,cAAc,GAAG4R,YAAY,CAAC6B,MAAM;IACxC;IACA,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAI9B,YAAY,IAAIA,YAAY,CAAC+B,EAAE,KAAK7Q,SAAS,EAAE;MAC/C4Q,YAAY,GAAG9B,YAAY,CAAC+B,EAAE;IAClC;IACA,IAAI/D,KAAK,GAAGtB,GAAG;IACf,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAAC7P,cAAc,CAAC8R,kBAAkB,CAAC,EAAE;MACvDjC,KAAK,GAAGvE,oBAAoB,CAACuE,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,IAAItB,GAAG,CAACuD,kBAAkB,CAAC,EAAE;MACnC;MACAjC,KAAK,GAAGtB,GAAG;IACf;IACA,IAAI,CAACsB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIA,KAAK,CAACyC,0BAA0B,CAAC,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,MAAMf,iBAAiB,GAAGM,YAAY,IAAIA,YAAY,CAACN,iBAAiB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMsC,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,sBAAsB,GAAIjE,KAAK,CAACyC,0BAA0B,CAAC,GAAGzC,KAAK,CAACiC,kBAAkB,CAAE;IAC9F,MAAMiC,yBAAyB,GAAIlE,KAAK,CAACzD,UAAU,CAAC4F,qBAAqB,CAAC,CAAC,GACvEnC,KAAK,CAACmC,qBAAqB,CAAE;IACjC,MAAMgC,eAAe,GAAInE,KAAK,CAACzD,UAAU,CAAC8F,wBAAwB,CAAC,CAAC,GAChErC,KAAK,CAACqC,wBAAwB,CAAE;IACpC,MAAM+B,wBAAwB,GAAIpE,KAAK,CAACzD,UAAU,CAACgG,mCAAmC,CAAC,CAAC,GACpFvC,KAAK,CAACuC,mCAAmC,CAAE;IAC/C,IAAI8B,0BAA0B;IAC9B,IAAIrC,YAAY,IAAIA,YAAY,CAACsC,OAAO,EAAE;MACtCD,0BAA0B,GAAGrE,KAAK,CAACzD,UAAU,CAACyF,YAAY,CAACsC,OAAO,CAAC,CAAC,GAChEtE,KAAK,CAACgC,YAAY,CAACsC,OAAO,CAAC;IACnC;IACA;AACR;AACA;AACA;IACQ,SAASC,yBAAyBA,CAACtM,OAAO,EAAEuM,OAAO,EAAE;MACjD,IAAI,CAACtD,gBAAgB,IAAI,OAAOjJ,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;QAC7D;QACA;QACA;QACA,OAAO,CAAC,CAACA,OAAO,CAACwM,OAAO;MAC5B;MACA,IAAI,CAACvD,gBAAgB,IAAI,CAACsD,OAAO,EAAE;QAC/B,OAAOvM,OAAO;MAClB;MACA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9B,OAAO;UAAEwM,OAAO,EAAExM,OAAO;UAAEuM,OAAO,EAAE;QAAK,CAAC;MAC9C;MACA,IAAI,CAACvM,OAAO,EAAE;QACV,OAAO;UAAEuM,OAAO,EAAE;QAAK,CAAC;MAC5B;MACA,IAAI,OAAOvM,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACuM,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;UAAE,GAAGvM,OAAO;UAAEuM,OAAO,EAAE;QAAK,CAAC;MACxC;MACA,OAAOvM,OAAO;IAClB;IACA,MAAMyM,oBAAoB,GAAG,SAAAA,CAAUzS,IAAI,EAAE;MACzC;MACA;MACA,IAAI+R,QAAQ,CAACW,UAAU,EAAE;QACrB;MACJ;MACA,OAAOV,sBAAsB,CAAC5L,IAAI,CAAC2L,QAAQ,CAAClP,MAAM,EAAEkP,QAAQ,CAAC9E,SAAS,EAAE8E,QAAQ,CAACS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAES,QAAQ,CAAC/L,OAAO,CAAC;IAC1K,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,MAAM2M,kBAAkB,GAAG,SAAAA,CAAU3S,IAAI,EAAE;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAAC4Q,SAAS,EAAE;QACjB,MAAMgC,gBAAgB,GAAGzD,oBAAoB,CAACnP,IAAI,CAACiN,SAAS,CAAC;QAC7D,IAAI4F,eAAe;QACnB,IAAID,gBAAgB,EAAE;UAClBC,eAAe,GAAGD,gBAAgB,CAAC5S,IAAI,CAACwS,OAAO,GAAGvI,QAAQ,GAAGC,SAAS,CAAC;QAC3E;QACA,MAAM4I,aAAa,GAAGD,eAAe,IAAI7S,IAAI,CAAC6C,MAAM,CAACgQ,eAAe,CAAC;QACrE,IAAIC,aAAa,EAAE;UACf,KAAK,IAAIvQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,aAAa,CAACtQ,MAAM,EAAED,CAAC,EAAE,EAAE;YAC3C,MAAMwQ,YAAY,GAAGD,aAAa,CAACvQ,CAAC,CAAC;YACrC,IAAIwQ,YAAY,KAAK/S,IAAI,EAAE;cACvB8S,aAAa,CAACE,MAAM,CAACzQ,CAAC,EAAE,CAAC,CAAC;cAC1B;cACAvC,IAAI,CAAC4Q,SAAS,GAAG,IAAI;cACrB,IAAI5Q,IAAI,CAACiT,mBAAmB,EAAE;gBAC1BjT,IAAI,CAACiT,mBAAmB,CAAC,CAAC;gBAC1BjT,IAAI,CAACiT,mBAAmB,GAAG,IAAI;cACnC;cACA,IAAIH,aAAa,CAACtQ,MAAM,KAAK,CAAC,EAAE;gBAC5B;gBACA;gBACAxC,IAAI,CAACkT,UAAU,GAAG,IAAI;gBACtBlT,IAAI,CAAC6C,MAAM,CAACgQ,eAAe,CAAC,GAAG,IAAI;cACvC;cACA;YACJ;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA,IAAI,CAAC7S,IAAI,CAACkT,UAAU,EAAE;QAClB;MACJ;MACA,OAAOjB,yBAAyB,CAAC7L,IAAI,CAACpG,IAAI,CAAC6C,MAAM,EAAE7C,IAAI,CAACiN,SAAS,EAAEjN,IAAI,CAACwS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAEtR,IAAI,CAACgG,OAAO,CAAC;IAC7J,CAAC;IACD,MAAMmN,uBAAuB,GAAG,SAAAA,CAAUnT,IAAI,EAAE;MAC5C,OAAOgS,sBAAsB,CAAC5L,IAAI,CAAC2L,QAAQ,CAAClP,MAAM,EAAEkP,QAAQ,CAAC9E,SAAS,EAAEjN,IAAI,CAACJ,MAAM,EAAEmS,QAAQ,CAAC/L,OAAO,CAAC;IAC1G,CAAC;IACD,MAAMoN,qBAAqB,GAAG,SAAAA,CAAUpT,IAAI,EAAE;MAC1C,OAAOoS,0BAA0B,CAAChM,IAAI,CAAC2L,QAAQ,CAAClP,MAAM,EAAEkP,QAAQ,CAAC9E,SAAS,EAAEjN,IAAI,CAACJ,MAAM,EAAEmS,QAAQ,CAAC/L,OAAO,CAAC;IAC9G,CAAC;IACD,MAAMqN,qBAAqB,GAAG,SAAAA,CAAUrT,IAAI,EAAE;MAC1C,OAAOiS,yBAAyB,CAAC7L,IAAI,CAACpG,IAAI,CAAC6C,MAAM,EAAE7C,IAAI,CAACiN,SAAS,EAAEjN,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAACgG,OAAO,CAAC;IACjG,CAAC;IACD,MAAMnE,cAAc,GAAG4P,iBAAiB,GAAGgB,oBAAoB,GAAGU,uBAAuB;IACzF,MAAMlR,YAAY,GAAGwP,iBAAiB,GAAGkB,kBAAkB,GAAGU,qBAAqB;IACnF,MAAMC,6BAA6B,GAAG,SAAAA,CAAUtT,IAAI,EAAE2C,QAAQ,EAAE;MAC5D,MAAM4Q,cAAc,GAAG,OAAO5Q,QAAQ;MACtC,OAAS4Q,cAAc,KAAK,UAAU,IAAIvT,IAAI,CAACb,QAAQ,KAAKwD,QAAQ,IAC/D4Q,cAAc,KAAK,QAAQ,IAAIvT,IAAI,CAAC8Q,gBAAgB,KAAKnO,QAAS;IAC3E,CAAC;IACD,MAAM6Q,OAAO,GAAGzD,YAAY,IAAIA,YAAY,CAAC0D,IAAI,GAAG1D,YAAY,CAAC0D,IAAI,GAAGH,6BAA6B;IACrG,MAAMI,eAAe,GAAGtJ,IAAI,CAACE,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC5D,MAAMqJ,aAAa,GAAGjJ,OAAO,CAACJ,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC3D,SAASsJ,wBAAwBA,CAAC5N,OAAO,EAAE;MACvC,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;QACjD;QACA;QACA;QACA,MAAM6N,UAAU,GAAG;UAAE,GAAG7N;QAAQ,CAAC;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,OAAO,CAAC8N,MAAM,EAAE;UAChBD,UAAU,CAACC,MAAM,GAAG9N,OAAO,CAAC8N,MAAM;QACtC;QACA,OAAOD,UAAU;MACrB;MACA,OAAO7N,OAAO;IAClB;IACA,MAAM+N,eAAe,GAAG,SAAAA,CAAUC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEtC,YAAY,GAAG,KAAK,EAAEQ,OAAO,GAAG,KAAK,EAAE;MAClI,OAAO,YAAY;QACf,MAAMxP,MAAM,GAAG,IAAI,IAAI6H,OAAO;QAC9B,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAIuQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDnH,SAAS,GAAG8C,YAAY,CAACqE,iBAAiB,CAACnH,SAAS,CAAC;QACzD;QACA,IAAItK,QAAQ,GAAGnD,SAAS,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACmD,QAAQ,EAAE;UACX,OAAOqR,cAAc,CAAC1O,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;QAChD;QACA,IAAI8L,MAAM,IAAI2B,SAAS,KAAK,mBAAmB,EAAE;UAC7C;UACA,OAAO+G,cAAc,CAAC1O,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;QAChD;QACA;QACA;QACA;QACA,IAAI6U,aAAa,GAAG,KAAK;QACzB,IAAI,OAAO1R,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAACA,QAAQ,CAACkO,WAAW,EAAE;YACvB,OAAOmD,cAAc,CAAC1O,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;UAChD;UACA6U,aAAa,GAAG,IAAI;QACxB;QACA,IAAI3C,eAAe,IAAI,CAACA,eAAe,CAACsC,cAAc,EAAErR,QAAQ,EAAEE,MAAM,EAAErD,SAAS,CAAC,EAAE;UAClF;QACJ;QACA,MAAM+S,OAAO,GAAGtD,gBAAgB,IAAI,CAAC,CAAC0E,aAAa,IAAIA,aAAa,CAAC7E,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;QAC9F,MAAMjH,OAAO,GAAG4N,wBAAwB,CAACtB,yBAAyB,CAAC9S,SAAS,CAAC,CAAC,CAAC,EAAE+S,OAAO,CAAC,CAAC;QAC1F,MAAMuB,MAAM,GAAG9N,OAAO,EAAE8N,MAAM;QAC9B,IAAIA,MAAM,EAAEQ,OAAO,EAAE;UACjB;UACA;QACJ;QACA,IAAIZ,eAAe,EAAE;UACjB;UACA,KAAK,IAAInR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmR,eAAe,CAAClR,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,IAAI0K,SAAS,KAAKyG,eAAe,CAACnR,CAAC,CAAC,EAAE;cAClC,IAAIgQ,OAAO,EAAE;gBACT,OAAOyB,cAAc,CAAC5N,IAAI,CAACvD,MAAM,EAAEoK,SAAS,EAAEtK,QAAQ,EAAEqD,OAAO,CAAC;cACpE,CAAC,MACI;gBACD,OAAOgO,cAAc,CAAC1O,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;cAChD;YACJ;UACJ;QACJ;QACA,MAAMgT,OAAO,GAAG,CAACxM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACwM,OAAO;QACxF,MAAMzB,IAAI,GAAG/K,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC+K,IAAI,GAAG,KAAK;QAC1E,MAAMtT,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAIkV,gBAAgB,GAAGzD,oBAAoB,CAAClC,SAAS,CAAC;QACtD,IAAI,CAAC2F,gBAAgB,EAAE;UACnBpD,iBAAiB,CAACvC,SAAS,EAAEwC,iBAAiB,CAAC;UAC/CmD,gBAAgB,GAAGzD,oBAAoB,CAAClC,SAAS,CAAC;QACtD;QACA,MAAM4F,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGvI,QAAQ,GAAGC,SAAS,CAAC;QACxE,IAAI4I,aAAa,GAAGjQ,MAAM,CAACgQ,eAAe,CAAC;QAC3C,IAAIH,UAAU,GAAG,KAAK;QACtB,IAAII,aAAa,EAAE;UACf;UACAJ,UAAU,GAAG,IAAI;UACjB,IAAIvU,cAAc,EAAE;YAChB,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,aAAa,CAACtQ,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,IAAIiR,OAAO,CAACV,aAAa,CAACvQ,CAAC,CAAC,EAAEI,QAAQ,CAAC,EAAE;gBACrC;gBACA;cACJ;YACJ;UACJ;QACJ,CAAC,MACI;UACDmQ,aAAa,GAAGjQ,MAAM,CAACgQ,eAAe,CAAC,GAAG,EAAE;QAChD;QACA,IAAIzT,MAAM;QACV,MAAMmV,eAAe,GAAG1R,MAAM,CAACrE,WAAW,CAAC,MAAM,CAAC;QAClD,MAAMgW,YAAY,GAAGpF,aAAa,CAACmF,eAAe,CAAC;QACnD,IAAIC,YAAY,EAAE;UACdpV,MAAM,GAAGoV,YAAY,CAACvH,SAAS,CAAC;QACpC;QACA,IAAI,CAAC7N,MAAM,EAAE;UACTA,MAAM,GACFmV,eAAe,GACXN,SAAS,IACRxE,iBAAiB,GAAGA,iBAAiB,CAACxC,SAAS,CAAC,GAAGA,SAAS,CAAC;QAC1E;QACA;QACA;QACA;QACA;QACA;QACA8E,QAAQ,CAAC/L,OAAO,GAAGA,OAAO;QAC1B,IAAI+K,IAAI,EAAE;UACN;UACA;UACA;UACAgB,QAAQ,CAAC/L,OAAO,CAAC+K,IAAI,GAAG,KAAK;QACjC;QACAgB,QAAQ,CAAClP,MAAM,GAAGA,MAAM;QACxBkP,QAAQ,CAACS,OAAO,GAAGA,OAAO;QAC1BT,QAAQ,CAAC9E,SAAS,GAAGA,SAAS;QAC9B8E,QAAQ,CAACW,UAAU,GAAGA,UAAU;QAChC,MAAMtS,IAAI,GAAGqR,iBAAiB,GAAGvC,8BAA8B,GAAGjO,SAAS;QAC3E;QACA,IAAIb,IAAI,EAAE;UACNA,IAAI,CAAC2R,QAAQ,GAAGA,QAAQ;QAC5B;QACA,IAAI+B,MAAM,EAAE;UACR;UACA;UACA;UACA/B,QAAQ,CAAC/L,OAAO,CAAC8N,MAAM,GAAG7S,SAAS;QACvC;QACA;QACA;QACA;QACA;QACA,MAAMjB,IAAI,GAAGvC,IAAI,CAACyE,iBAAiB,CAAC9C,MAAM,EAAEuD,QAAQ,EAAEvC,IAAI,EAAE8T,gBAAgB,EAAEC,cAAc,CAAC;QAC7F,IAAIL,MAAM,EAAE;UACR;UACA/B,QAAQ,CAAC/L,OAAO,CAAC8N,MAAM,GAAGA,MAAM;UAChC;UACA;UACA;UACA,MAAMW,OAAO,GAAGA,CAAA,KAAMzU,IAAI,CAACvC,IAAI,CAAC0E,UAAU,CAACnC,IAAI,CAAC;UAChDgU,cAAc,CAAC5N,IAAI,CAAC0N,MAAM,EAAE,OAAO,EAAEW,OAAO,EAAE;YAAE1D,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7D;UACA;UACA;UACA;UACA/Q,IAAI,CAACiT,mBAAmB,GAAG,MAAMa,MAAM,CAAC1G,mBAAmB,CAAC,OAAO,EAAEqH,OAAO,CAAC;QACjF;QACA;QACA;QACA1C,QAAQ,CAAClP,MAAM,GAAG,IAAI;QACtB;QACA,IAAIzC,IAAI,EAAE;UACNA,IAAI,CAAC2R,QAAQ,GAAG,IAAI;QACxB;QACA;QACA;QACA,IAAIhB,IAAI,EAAE;UACNgB,QAAQ,CAAC/L,OAAO,CAAC+K,IAAI,GAAG,IAAI;QAChC;QACA,IAAI,EAAE,CAAC9B,gBAAgB,IAAI,OAAOjP,IAAI,CAACgG,OAAO,KAAK,SAAS,CAAC,EAAE;UAC3D;UACA;UACAhG,IAAI,CAACgG,OAAO,GAAGA,OAAO;QAC1B;QACAhG,IAAI,CAAC6C,MAAM,GAAGA,MAAM;QACpB7C,IAAI,CAACwS,OAAO,GAAGA,OAAO;QACtBxS,IAAI,CAACiN,SAAS,GAAGA,SAAS;QAC1B,IAAIoH,aAAa,EAAE;UACf;UACArU,IAAI,CAAC8Q,gBAAgB,GAAGnO,QAAQ;QACpC;QACA,IAAI,CAAC0P,OAAO,EAAE;UACVS,aAAa,CAACtN,IAAI,CAACxF,IAAI,CAAC;QAC5B,CAAC,MACI;UACD8S,aAAa,CAAC4B,OAAO,CAAC1U,IAAI,CAAC;QAC/B;QACA,IAAI6R,YAAY,EAAE;UACd,OAAOhP,MAAM;QACjB;MACJ,CAAC;IACL,CAAC;IACDkL,KAAK,CAACiC,kBAAkB,CAAC,GAAG+D,eAAe,CAAC/B,sBAAsB,EAAEvB,yBAAyB,EAAE5O,cAAc,EAAEI,YAAY,EAAE4P,YAAY,CAAC;IAC1I,IAAIO,0BAA0B,EAAE;MAC5BrE,KAAK,CAAC2C,sBAAsB,CAAC,GAAGqD,eAAe,CAAC3B,0BAA0B,EAAEzB,6BAA6B,EAAEyC,qBAAqB,EAAEnR,YAAY,EAAE4P,YAAY,EAAE,IAAI,CAAC;IACvK;IACA9D,KAAK,CAACmC,qBAAqB,CAAC,GAAG,YAAY;MACvC,MAAMrN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIuQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDnH,SAAS,GAAG8C,YAAY,CAACqE,iBAAiB,CAACnH,SAAS,CAAC;MACzD;MACA,MAAMjH,OAAO,GAAGxG,SAAS,CAAC,CAAC,CAAC;MAC5B,MAAMgT,OAAO,GAAG,CAACxM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACwM,OAAO;MACxF,MAAM7P,QAAQ,GAAGnD,SAAS,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACmD,QAAQ,EAAE;QACX,OAAOsP,yBAAyB,CAAC3M,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;MAC3D;MACA,IAAIkS,eAAe,IACf,CAACA,eAAe,CAACO,yBAAyB,EAAEtP,QAAQ,EAAEE,MAAM,EAAErD,SAAS,CAAC,EAAE;QAC1E;MACJ;MACA,MAAMoT,gBAAgB,GAAGzD,oBAAoB,CAAClC,SAAS,CAAC;MACxD,IAAI4F,eAAe;MACnB,IAAID,gBAAgB,EAAE;QAClBC,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGvI,QAAQ,GAAGC,SAAS,CAAC;MACtE;MACA,MAAM4I,aAAa,GAAGD,eAAe,IAAIhQ,MAAM,CAACgQ,eAAe,CAAC;MAChE;MACA;MACA;MACA;MACA,IAAIC,aAAa,EAAE;QACf,KAAK,IAAIvQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,aAAa,CAACtQ,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3C,MAAMwQ,YAAY,GAAGD,aAAa,CAACvQ,CAAC,CAAC;UACrC,IAAIiR,OAAO,CAACT,YAAY,EAAEpQ,QAAQ,CAAC,EAAE;YACjCmQ,aAAa,CAACE,MAAM,CAACzQ,CAAC,EAAE,CAAC,CAAC;YAC1B;YACAwQ,YAAY,CAACnC,SAAS,GAAG,IAAI;YAC7B,IAAIkC,aAAa,CAACtQ,MAAM,KAAK,CAAC,EAAE;cAC5B;cACA;cACAuQ,YAAY,CAACG,UAAU,GAAG,IAAI;cAC9BrQ,MAAM,CAACgQ,eAAe,CAAC,GAAG,IAAI;cAC9B;cACA;cACA;cACA;cACA;cACA,IAAI,CAACL,OAAO,IAAI,OAAOvF,SAAS,KAAK,QAAQ,EAAE;gBAC3C,MAAM0H,gBAAgB,GAAGxK,kBAAkB,GAAG,aAAa,GAAG8C,SAAS;gBACvEpK,MAAM,CAAC8R,gBAAgB,CAAC,GAAG,IAAI;cACnC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA5B,YAAY,CAACtV,IAAI,CAAC0E,UAAU,CAAC4Q,YAAY,CAAC;YAC1C,IAAIlB,YAAY,EAAE;cACd,OAAOhP,MAAM;YACjB;YACA;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOoP,yBAAyB,CAAC3M,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;IAC3D,CAAC;IACDuO,KAAK,CAACqC,wBAAwB,CAAC,GAAG,YAAY;MAC1C,MAAMvN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIuQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDnH,SAAS,GAAG8C,YAAY,CAACqE,iBAAiB,CAACnH,SAAS,CAAC;MACzD;MACA,MAAMoD,SAAS,GAAG,EAAE;MACpB,MAAMc,KAAK,GAAGyD,cAAc,CAAC/R,MAAM,EAAE4M,iBAAiB,GAAGA,iBAAiB,CAACxC,SAAS,CAAC,GAAGA,SAAS,CAAC;MAClG,KAAK,IAAI1K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4O,KAAK,CAAC3O,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAMvC,IAAI,GAAGmR,KAAK,CAAC5O,CAAC,CAAC;QACrB,IAAII,QAAQ,GAAG3C,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAACb,QAAQ;QAC5EkR,SAAS,CAAC7K,IAAI,CAAC7C,QAAQ,CAAC;MAC5B;MACA,OAAO0N,SAAS;IACpB,CAAC;IACDtC,KAAK,CAACuC,mCAAmC,CAAC,GAAG,YAAY;MACrD,MAAMzN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI,CAACyN,SAAS,EAAE;QACZ,MAAM4H,IAAI,GAAG/N,MAAM,CAAC+N,IAAI,CAAChS,MAAM,CAAC;QAChC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsS,IAAI,CAACrS,MAAM,EAAED,CAAC,EAAE,EAAE;UAClC,MAAMmK,IAAI,GAAGmI,IAAI,CAACtS,CAAC,CAAC;UACpB,MAAMuS,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACrI,IAAI,CAAC;UAC/C,IAAIsI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAAgB,EAAE;YACzC,IAAI,CAAC1E,mCAAmC,CAAC,CAAClK,IAAI,CAAC,IAAI,EAAE4O,OAAO,CAAC;UACjE;QACJ;QACA;QACA,IAAI,CAAC1E,mCAAmC,CAAC,CAAClK,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;MAC1E,CAAC,MACI;QACD,IAAI2J,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDnH,SAAS,GAAG8C,YAAY,CAACqE,iBAAiB,CAACnH,SAAS,CAAC;QACzD;QACA,MAAM2F,gBAAgB,GAAGzD,oBAAoB,CAAClC,SAAS,CAAC;QACxD,IAAI2F,gBAAgB,EAAE;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAAC1I,SAAS,CAAC;UACnD,MAAM+K,sBAAsB,GAAGrC,gBAAgB,CAAC3I,QAAQ,CAAC;UACzD,MAAMkH,KAAK,GAAGtO,MAAM,CAACgQ,eAAe,CAAC;UACrC,MAAMqC,YAAY,GAAGrS,MAAM,CAACoS,sBAAsB,CAAC;UACnD,IAAI9D,KAAK,EAAE;YACP,MAAMgE,WAAW,GAAGhE,KAAK,CAACvH,KAAK,CAAC,CAAC;YACjC,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4S,WAAW,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMvC,IAAI,GAAGmV,WAAW,CAAC5S,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAG3C,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAAC+Q,qBAAqB,CAAC,CAAC9J,IAAI,CAAC,IAAI,EAAE6G,SAAS,EAAEtK,QAAQ,EAAE3C,IAAI,CAACgG,OAAO,CAAC;YAC7E;UACJ;UACA,IAAIkP,YAAY,EAAE;YACd,MAAMC,WAAW,GAAGD,YAAY,CAACtL,KAAK,CAAC,CAAC;YACxC,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4S,WAAW,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMvC,IAAI,GAAGmV,WAAW,CAAC5S,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAG3C,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAAC8Q,gBAAgB,GAAG9Q,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAAC+Q,qBAAqB,CAAC,CAAC9J,IAAI,CAAC,IAAI,EAAE6G,SAAS,EAAEtK,QAAQ,EAAE3C,IAAI,CAACgG,OAAO,CAAC;YAC7E;UACJ;QACJ;MACJ;MACA,IAAI6L,YAAY,EAAE;QACd,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IACA3I,qBAAqB,CAAC6E,KAAK,CAACiC,kBAAkB,CAAC,EAAEgC,sBAAsB,CAAC;IACxE9I,qBAAqB,CAAC6E,KAAK,CAACmC,qBAAqB,CAAC,EAAE+B,yBAAyB,CAAC;IAC9E,IAAIE,wBAAwB,EAAE;MAC1BjJ,qBAAqB,CAAC6E,KAAK,CAACuC,mCAAmC,CAAC,EAAE6B,wBAAwB,CAAC;IAC/F;IACA,IAAID,eAAe,EAAE;MACjBhJ,qBAAqB,CAAC6E,KAAK,CAACqC,wBAAwB,CAAC,EAAE8B,eAAe,CAAC;IAC3E;IACA,OAAO,IAAI;EACf;EACA,IAAIkD,OAAO,GAAG,EAAE;EAChB,KAAK,IAAI7S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuN,IAAI,CAACtN,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC6S,OAAO,CAAC7S,CAAC,CAAC,GAAGiP,uBAAuB,CAAC1B,IAAI,CAACvN,CAAC,CAAC,EAAEwN,YAAY,CAAC;EAC/D;EACA,OAAOqF,OAAO;AAClB;AACA,SAASR,cAAcA,CAAC/R,MAAM,EAAEoK,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,EAAE;IACZ,MAAMoI,UAAU,GAAG,EAAE;IACrB,KAAK,IAAI3I,IAAI,IAAI7J,MAAM,EAAE;MACrB,MAAMiS,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACrI,IAAI,CAAC;MAC/C,IAAIsI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIE,OAAO,KAAK,CAAC/H,SAAS,IAAI+H,OAAO,KAAK/H,SAAS,CAAC,EAAE;QAClD,MAAMkE,KAAK,GAAGtO,MAAM,CAAC6J,IAAI,CAAC;QAC1B,IAAIyE,KAAK,EAAE;UACP,KAAK,IAAI5O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4O,KAAK,CAAC3O,MAAM,EAAED,CAAC,EAAE,EAAE;YACnC8S,UAAU,CAAC7P,IAAI,CAAC2L,KAAK,CAAC5O,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;IACJ;IACA,OAAO8S,UAAU;EACrB;EACA,IAAIxC,eAAe,GAAG1D,oBAAoB,CAAClC,SAAS,CAAC;EACrD,IAAI,CAAC4F,eAAe,EAAE;IAClBrD,iBAAiB,CAACvC,SAAS,CAAC;IAC5B4F,eAAe,GAAG1D,oBAAoB,CAAClC,SAAS,CAAC;EACrD;EACA,MAAMqI,iBAAiB,GAAGzS,MAAM,CAACgQ,eAAe,CAAC3I,SAAS,CAAC,CAAC;EAC5D,MAAMqL,gBAAgB,GAAG1S,MAAM,CAACgQ,eAAe,CAAC5I,QAAQ,CAAC,CAAC;EAC1D,IAAI,CAACqL,iBAAiB,EAAE;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAAC3L,KAAK,CAAC,CAAC,GAAG,EAAE;EAC3D,CAAC,MACI;IACD,OAAO2L,gBAAgB,GACjBD,iBAAiB,CAACE,MAAM,CAACD,gBAAgB,CAAC,GAC1CD,iBAAiB,CAAC1L,KAAK,CAAC,CAAC;EACnC;AACJ;AACA,SAASpB,mBAAmBA,CAAC9L,MAAM,EAAEmT,GAAG,EAAE;EACtC,MAAM4F,KAAK,GAAG/Y,MAAM,CAAC,OAAO,CAAC;EAC7B,IAAI+Y,KAAK,IAAIA,KAAK,CAAC1O,SAAS,EAAE;IAC1B8I,GAAG,CAACzH,WAAW,CAACqN,KAAK,CAAC1O,SAAS,EAAE,0BAA0B,EAAGpE,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;MAC7FH,IAAI,CAACqJ,4BAA4B,CAAC,GAAG,IAAI;MACzC;MACA;MACA;MACA5M,QAAQ,IAAIA,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IAC1C,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASqP,mBAAmBA,CAAChZ,MAAM,EAAEmT,GAAG,EAAE;EACtCA,GAAG,CAACzH,WAAW,CAAC1L,MAAM,EAAE,gBAAgB,EAAGiG,QAAQ,IAAK;IACpD,OAAO,UAAUuD,IAAI,EAAEG,IAAI,EAAE;MACzB+D,IAAI,CAAC1M,OAAO,CAACkE,iBAAiB,CAAC,gBAAgB,EAAEyE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;EACL,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,MAAMsP,UAAU,GAAGrL,UAAU,CAAC,UAAU,CAAC;AACzC,SAASsL,UAAUA,CAACpL,MAAM,EAAEqL,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACzD,IAAI3H,SAAS,GAAG,IAAI;EACpB,IAAI4H,WAAW,GAAG,IAAI;EACtBH,OAAO,IAAIE,UAAU;EACrBD,UAAU,IAAIC,UAAU;EACxB,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,SAASzU,YAAYA,CAACxB,IAAI,EAAE;IACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAI;IACtBA,IAAI,CAACiG,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;MACvB,OAAOrG,IAAI,CAACJ,MAAM,CAAC0F,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;IAC7C,CAAC;IACD,MAAM0W,UAAU,GAAG9H,SAAS,CAAC9I,KAAK,CAACkF,MAAM,EAAEpK,IAAI,CAACiG,IAAI,CAAC;IACrD;IACA;IACA;IACA,IAAI2I,QAAQ,CAACkH,UAAU,CAAC,EAAE;MACtB9V,IAAI,CAACyG,QAAQ,GAAGqP,UAAU;IAC9B,CAAC,MACI;MACD9V,IAAI,CAAC+V,MAAM,GAAGD,UAAU;MACxB;MACA9V,IAAI,CAACE,aAAa,GAAGyO,UAAU,CAACmH,UAAU,CAACE,OAAO,CAAC;IACvD;IACA,OAAOpW,IAAI;EACf;EACA,SAASqW,SAASA,CAACrW,IAAI,EAAE;IACrB,MAAM;MAAEmW,MAAM;MAAEtP;IAAS,CAAC,GAAG7G,IAAI,CAACI,IAAI;IACtC,OAAO4V,WAAW,CAAC5P,IAAI,CAACoE,MAAM,EAAE2L,MAAM,IAAItP,QAAQ,CAAC;EACvD;EACAuH,SAAS,GAAGhG,WAAW,CAACoC,MAAM,EAAEqL,OAAO,EAAGlT,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IACzE,IAAI0I,UAAU,CAAC1I,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,MAAML,OAAO,GAAG;QACZ1F,aAAa,EAAE,KAAK;QACpBD,UAAU,EAAE0V,UAAU,KAAK,UAAU;QACrCO,KAAK,EAAEP,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,UAAU,GAAG1P,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGpF,SAAS;QACvFoF,IAAI,EAAEA;MACV,CAAC;MACD,MAAMlH,QAAQ,GAAGkH,IAAI,CAAC,CAAC,CAAC;MACxBA,IAAI,CAAC,CAAC,CAAC,GAAG,SAASkQ,KAAKA,CAAA,EAAG;QACvB,IAAI;UACA,OAAOpX,QAAQ,CAACmG,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;QAC1C,CAAC,SACO;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM;YAAE2W,MAAM;YAAEtP,QAAQ;YAAExG,UAAU;YAAEC;UAAc,CAAC,GAAG0F,OAAO;UAC/D,IAAI,CAAC3F,UAAU,IAAI,CAACC,aAAa,EAAE;YAC/B,IAAIuG,QAAQ,EAAE;cACV;cACA;cACA,OAAOoP,eAAe,CAACpP,QAAQ,CAAC;YACpC,CAAC,MACI,IAAIsP,MAAM,EAAE;cACb;cACA;cACAA,MAAM,CAACR,UAAU,CAAC,GAAG,IAAI;YAC7B;UACJ;QACJ;MACJ,CAAC;MACD,MAAM3V,IAAI,GAAGqK,gCAAgC,CAACwL,OAAO,EAAExP,IAAI,CAAC,CAAC,CAAC,EAAEL,OAAO,EAAExE,YAAY,EAAE6U,SAAS,CAAC;MACjG,IAAI,CAACrW,IAAI,EAAE;QACP,OAAOA,IAAI;MACf;MACA;MACA,MAAM;QAAE6G,QAAQ;QAAEsP,MAAM;QAAE7V,aAAa;QAAED;MAAW,CAAC,GAAGL,IAAI,CAACI,IAAI;MACjE,IAAIyG,QAAQ,EAAE;QACV;QACA;QACAoP,eAAe,CAACpP,QAAQ,CAAC,GAAG7G,IAAI;MACpC,CAAC,MACI,IAAImW,MAAM,EAAE;QACb;QACA;QACAA,MAAM,CAACR,UAAU,CAAC,GAAG3V,IAAI;QACzB,IAAIM,aAAa,IAAI,CAACD,UAAU,EAAE;UAC9B,MAAMmW,eAAe,GAAGL,MAAM,CAACC,OAAO;UACtCD,MAAM,CAACC,OAAO,GAAG,YAAY;YACzB,MAAM;cAAE3Y,IAAI;cAAE8C;YAAM,CAAC,GAAGP,IAAI;YAC5B,IAAIO,KAAK,KAAK,cAAc,EAAE;cAC1BP,IAAI,CAACiG,MAAM,GAAG,WAAW;cACzBxI,IAAI,CAAC8D,gBAAgB,CAACvB,IAAI,EAAE,CAAC,CAAC;YAClC,CAAC,MACI,IAAIO,KAAK,KAAK,SAAS,EAAE;cAC1BP,IAAI,CAACiG,MAAM,GAAG,YAAY;YAC9B;YACA,OAAOuQ,eAAe,CAACpQ,IAAI,CAAC,IAAI,CAAC;UACrC,CAAC;QACL;MACJ;MACA,OAAO+P,MAAM,IAAItP,QAAQ,IAAI7G,IAAI;IACrC,CAAC,MACI;MACD;MACA,OAAO2C,QAAQ,CAAC2C,KAAK,CAACkF,MAAM,EAAEnE,IAAI,CAAC;IACvC;EACJ,CAAC,CAAC;EACF2P,WAAW,GAAG5N,WAAW,CAACoC,MAAM,EAAEsL,UAAU,EAAGnT,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IAC9E,MAAMoQ,EAAE,GAAGpQ,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIrG,IAAI;IACR,IAAIgP,QAAQ,CAACyH,EAAE,CAAC,EAAE;MACd;MACAzW,IAAI,GAAGiW,eAAe,CAACQ,EAAE,CAAC;MAC1B,OAAOR,eAAe,CAACQ,EAAE,CAAC;IAC9B,CAAC,MACI;MACD;MACAzW,IAAI,GAAGyW,EAAE,GAAGd,UAAU,CAAC;MACvB,IAAI3V,IAAI,EAAE;QACNyW,EAAE,CAACd,UAAU,CAAC,GAAG,IAAI;MACzB,CAAC,MACI;QACD3V,IAAI,GAAGyW,EAAE;MACb;IACJ;IACA,IAAIzW,IAAI,EAAEG,IAAI,EAAE;MACZ,IAAIH,IAAI,CAACgB,QAAQ,EAAE;QACf;QACAhB,IAAI,CAACvC,IAAI,CAAC0E,UAAU,CAACnC,IAAI,CAAC;MAC9B;IACJ,CAAC,MACI;MACD;MACA2C,QAAQ,CAAC2C,KAAK,CAACkF,MAAM,EAAEnE,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;AACN;AAEA,SAASqQ,mBAAmBA,CAAChM,OAAO,EAAEmF,GAAG,EAAE;EACvC,MAAM;IAAErE,SAAS;IAAEC;EAAM,CAAC,GAAGoE,GAAG,CAACnH,gBAAgB,CAAC,CAAC;EACnD,IAAK,CAAC8C,SAAS,IAAI,CAACC,KAAK,IAAK,CAACf,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,IAAIA,OAAO,CAAC,EAAE;IACxF;EACJ;EACA;EACA,MAAMiM,SAAS,GAAG,CACd,mBAAmB,EACnB,sBAAsB,EACtB,iBAAiB,EACjB,0BAA0B,EAC1B,wBAAwB,EACxB,sBAAsB,EACtB,mBAAmB,EACnB,0BAA0B,CAC7B;EACD9G,GAAG,CAACzG,cAAc,CAACyG,GAAG,EAAEnF,OAAO,CAACkM,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAED,SAAS,CAAC;AAC1F;AAEA,SAASE,gBAAgBA,CAACnM,OAAO,EAAEmF,GAAG,EAAE;EACpC,IAAIzF,IAAI,CAACyF,GAAG,CAAC/H,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE;IACtC;IACA;EACJ;EACA,MAAM;IAAEgP,UAAU;IAAE3H,oBAAoB;IAAElF,QAAQ;IAAEC,SAAS;IAAEC;EAAmB,CAAC,GAAG0F,GAAG,CAACnH,gBAAgB,CAAC,CAAC;EAC5G;EACA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuU,UAAU,CAACtU,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAM0K,SAAS,GAAG6J,UAAU,CAACvU,CAAC,CAAC;IAC/B,MAAMmN,cAAc,GAAGzC,SAAS,GAAG/C,SAAS;IAC5C,MAAMyF,aAAa,GAAG1C,SAAS,GAAGhD,QAAQ;IAC1C,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGuF,cAAc;IAClD,MAAME,aAAa,GAAGzF,kBAAkB,GAAGwF,aAAa;IACxDR,oBAAoB,CAAClC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpCkC,oBAAoB,CAAClC,SAAS,CAAC,CAAC/C,SAAS,CAAC,GAAGpC,MAAM;IACnDqH,oBAAoB,CAAClC,SAAS,CAAC,CAAChD,QAAQ,CAAC,GAAG2F,aAAa;EAC7D;EACA,MAAMmH,YAAY,GAAGrM,OAAO,CAAC,aAAa,CAAC;EAC3C,IAAI,CAACqM,YAAY,IAAI,CAACA,YAAY,CAAChQ,SAAS,EAAE;IAC1C;EACJ;EACA8I,GAAG,CAAC3H,gBAAgB,CAACwC,OAAO,EAAEmF,GAAG,EAAE,CAACkH,YAAY,IAAIA,YAAY,CAAChQ,SAAS,CAAC,CAAC;EAC5E,OAAO,IAAI;AACf;AACA,SAASiQ,UAAUA,CAACta,MAAM,EAAEmT,GAAG,EAAE;EAC7BA,GAAG,CAACrH,mBAAmB,CAAC9L,MAAM,EAAEmT,GAAG,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAAS5G,gBAAgBA,CAACpG,MAAM,EAAE0K,YAAY,EAAE0J,gBAAgB,EAAE;EAC9D,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACzU,MAAM,KAAK,CAAC,EAAE;IACpD,OAAO+K,YAAY;EACvB;EACA,MAAM2J,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAEC,EAAE,IAAKA,EAAE,CAACvU,MAAM,KAAKA,MAAM,CAAC;EACjE,IAAI,CAACqU,GAAG,IAAIA,GAAG,CAAC1U,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO+K,YAAY;EACvB;EACA,MAAM8J,sBAAsB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACD,gBAAgB;EACtD,OAAO1J,YAAY,CAAC4J,MAAM,CAAEG,EAAE,IAAKD,sBAAsB,CAACvI,OAAO,CAACwI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF;AACA,SAASC,uBAAuBA,CAAC1U,MAAM,EAAE0K,YAAY,EAAE0J,gBAAgB,EAAElQ,SAAS,EAAE;EAChF;EACA;EACA,IAAI,CAAClE,MAAM,EAAE;IACT;EACJ;EACA,MAAM2U,kBAAkB,GAAGvO,gBAAgB,CAACpG,MAAM,EAAE0K,YAAY,EAAE0J,gBAAgB,CAAC;EACnF9O,iBAAiB,CAACtF,MAAM,EAAE2U,kBAAkB,EAAEzQ,SAAS,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA,SAAS0Q,eAAeA,CAAC5U,MAAM,EAAE;EAC7B,OAAOiE,MAAM,CAAC4Q,mBAAmB,CAAC7U,MAAM,CAAC,CACpCsU,MAAM,CAAEta,IAAI,IAAKA,IAAI,CAAC8a,UAAU,CAAC,IAAI,CAAC,IAAI9a,IAAI,CAAC2F,MAAM,GAAG,CAAC,CAAC,CAC1DoV,GAAG,CAAE/a,IAAI,IAAKA,IAAI,CAACgb,SAAS,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,SAASC,uBAAuBA,CAACjI,GAAG,EAAEnF,OAAO,EAAE;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAK,EAAE;IAClB;EACJ;EACA,IAAIrB,IAAI,CAACyF,GAAG,CAAC/H,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;IACjC;IACA;EACJ;EACA,MAAMmP,gBAAgB,GAAGvM,OAAO,CAAC,6BAA6B,CAAC;EAC/D;EACA,IAAIqN,YAAY,GAAG,EAAE;EACrB,IAAIvM,SAAS,EAAE;IACX,MAAMf,cAAc,GAAGD,MAAM;IAC7BuN,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,UAAU,EACV,YAAY,EACZ,SAAS,EACT,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,qBAAqB,EACrB,kBAAkB,EAClB,mBAAmB,EACnB,oBAAoB,EACpB,QAAQ,CACX,CAAC;IACF,MAAMwC,qBAAqB,GAAGtJ,IAAI,CAAC,CAAC,GAC9B,CAAC;MAAE7L,MAAM,EAAE4H,cAAc;MAAEwM,gBAAgB,EAAE,CAAC,OAAO;IAAE,CAAC,CAAC,GACzD,EAAE;IACR;IACA;IACAM,uBAAuB,CAAC9M,cAAc,EAAEgN,eAAe,CAAChN,cAAc,CAAC,EAAEwM,gBAAgB,GAAGA,gBAAgB,CAACzB,MAAM,CAACwC,qBAAqB,CAAC,GAAGf,gBAAgB,EAAEzN,oBAAoB,CAACiB,cAAc,CAAC,CAAC;EACxM;EACAsN,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,gBAAgB,EAChB,2BAA2B,EAC3B,UAAU,EACV,YAAY,EACZ,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,WAAW,CACd,CAAC;EACF,KAAK,IAAIjT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwV,YAAY,CAACvV,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,MAAMM,MAAM,GAAG6H,OAAO,CAACqN,YAAY,CAACxV,CAAC,CAAC,CAAC;IACvCM,MAAM,IACFA,MAAM,CAACkE,SAAS,IAChBwQ,uBAAuB,CAAC1U,MAAM,CAACkE,SAAS,EAAE0Q,eAAe,CAAC5U,MAAM,CAACkE,SAAS,CAAC,EAAEkQ,gBAAgB,CAAC;EACtG;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASgB,YAAYA,CAAC7N,IAAI,EAAE;EACxBA,IAAI,CAACrM,YAAY,CAAC,QAAQ,EAAGrB,MAAM,IAAK;IACpC,MAAMwb,WAAW,GAAGxb,MAAM,CAAC0N,IAAI,CAACxN,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1D,IAAIsb,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,CAAC;EACF9N,IAAI,CAACrM,YAAY,CAAC,QAAQ,EAAGrB,MAAM,IAAK;IACpC,MAAMyO,GAAG,GAAG,KAAK;IACjB,MAAMgN,KAAK,GAAG,OAAO;IACrBvC,UAAU,CAAClZ,MAAM,EAAEyO,GAAG,EAAEgN,KAAK,EAAE,SAAS,CAAC;IACzCvC,UAAU,CAAClZ,MAAM,EAAEyO,GAAG,EAAEgN,KAAK,EAAE,UAAU,CAAC;IAC1CvC,UAAU,CAAClZ,MAAM,EAAEyO,GAAG,EAAEgN,KAAK,EAAE,WAAW,CAAC;EAC/C,CAAC,CAAC;EACF/N,IAAI,CAACrM,YAAY,CAAC,uBAAuB,EAAGrB,MAAM,IAAK;IACnDkZ,UAAU,CAAClZ,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IACzDkZ,UAAU,CAAClZ,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC;IAC/DkZ,UAAU,CAAClZ,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACzE,CAAC,CAAC;EACF0N,IAAI,CAACrM,YAAY,CAAC,UAAU,EAAE,CAACrB,MAAM,EAAE0N,IAAI,KAAK;IAC5C,MAAMgO,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IACtD,KAAK,IAAI7V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6V,eAAe,CAAC5V,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAM1F,IAAI,GAAGub,eAAe,CAAC7V,CAAC,CAAC;MAC/B6F,WAAW,CAAC1L,MAAM,EAAEG,IAAI,EAAE,CAAC8F,QAAQ,EAAEmF,MAAM,EAAEjL,IAAI,KAAK;QAClD,OAAO,UAAUwb,CAAC,EAAEhS,IAAI,EAAE;UACtB,OAAO+D,IAAI,CAAC1M,OAAO,CAAC+B,GAAG,CAACkD,QAAQ,EAAEjG,MAAM,EAAE2J,IAAI,EAAExJ,IAAI,CAAC;QACzD,CAAC;MACL,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFuN,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACpDmH,UAAU,CAACta,MAAM,EAAEmT,GAAG,CAAC;IACvBgH,gBAAgB,CAACna,MAAM,EAAEmT,GAAG,CAAC;IAC7B;IACA,MAAMyI,yBAAyB,GAAG5b,MAAM,CAAC,2BAA2B,CAAC;IACrE,IAAI4b,yBAAyB,IAAIA,yBAAyB,CAACvR,SAAS,EAAE;MAClE8I,GAAG,CAAC3H,gBAAgB,CAACxL,MAAM,EAAEmT,GAAG,EAAE,CAACyI,yBAAyB,CAACvR,SAAS,CAAC,CAAC;IAC5E;EACJ,CAAC,CAAC;EACFqD,IAAI,CAACrM,YAAY,CAAC,kBAAkB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACzD9G,UAAU,CAAC,kBAAkB,CAAC;IAC9BA,UAAU,CAAC,wBAAwB,CAAC;EACxC,CAAC,CAAC;EACFqB,IAAI,CAACrM,YAAY,CAAC,sBAAsB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IAC7D9G,UAAU,CAAC,sBAAsB,CAAC;EACtC,CAAC,CAAC;EACFqB,IAAI,CAACrM,YAAY,CAAC,YAAY,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACnD9G,UAAU,CAAC,YAAY,CAAC;EAC5B,CAAC,CAAC;EACFqB,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACpDiI,uBAAuB,CAACjI,GAAG,EAAEnT,MAAM,CAAC;EACxC,CAAC,CAAC;EACF0N,IAAI,CAACrM,YAAY,CAAC,gBAAgB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACvD6G,mBAAmB,CAACha,MAAM,EAAEmT,GAAG,CAAC;EACpC,CAAC,CAAC;EACFzF,IAAI,CAACrM,YAAY,CAAC,KAAK,EAAE,CAACrB,MAAM,EAAE0N,IAAI,KAAK;IACvC;IACAmO,QAAQ,CAAC7b,MAAM,CAAC;IAChB,MAAM8b,QAAQ,GAAGlO,UAAU,CAAC,SAAS,CAAC;IACtC,MAAMmO,QAAQ,GAAGnO,UAAU,CAAC,SAAS,CAAC;IACtC,MAAMoO,YAAY,GAAGpO,UAAU,CAAC,aAAa,CAAC;IAC9C,MAAMqO,aAAa,GAAGrO,UAAU,CAAC,cAAc,CAAC;IAChD,MAAMsO,OAAO,GAAGtO,UAAU,CAAC,QAAQ,CAAC;IACpC,MAAMuO,0BAA0B,GAAGvO,UAAU,CAAC,yBAAyB,CAAC;IACxE,SAASiO,QAAQA,CAAC/N,MAAM,EAAE;MACtB,MAAMsO,cAAc,GAAGtO,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAI,CAACsO,cAAc,EAAE;QACjB;QACA;MACJ;MACA,MAAMC,uBAAuB,GAAGD,cAAc,CAAC/R,SAAS;MACxD,SAASiS,eAAeA,CAACnW,MAAM,EAAE;QAC7B,OAAOA,MAAM,CAAC2V,QAAQ,CAAC;MAC3B;MACA,IAAIS,cAAc,GAAGF,uBAAuB,CAAChP,8BAA8B,CAAC;MAC5E,IAAImP,iBAAiB,GAAGH,uBAAuB,CAAC/O,iCAAiC,CAAC;MAClF,IAAI,CAACiP,cAAc,EAAE;QACjB,MAAMX,yBAAyB,GAAG9N,MAAM,CAAC,2BAA2B,CAAC;QACrE,IAAI8N,yBAAyB,EAAE;UAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAACvR,SAAS;UAC9EkS,cAAc,GAAGE,kCAAkC,CAACpP,8BAA8B,CAAC;UACnFmP,iBAAiB,GAAGC,kCAAkC,CAACnP,iCAAiC,CAAC;QAC7F;MACJ;MACA,MAAMoP,kBAAkB,GAAG,kBAAkB;MAC7C,MAAMC,SAAS,GAAG,WAAW;MAC7B,SAAS7X,YAAYA,CAACxB,IAAI,EAAE;QACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAI;QACtB,MAAMyC,MAAM,GAAGzC,IAAI,CAACyC,MAAM;QAC1BA,MAAM,CAAC8V,aAAa,CAAC,GAAG,KAAK;QAC7B9V,MAAM,CAACgW,0BAA0B,CAAC,GAAG,KAAK;QAC1C;QACA,MAAM9M,QAAQ,GAAGlJ,MAAM,CAAC6V,YAAY,CAAC;QACrC,IAAI,CAACO,cAAc,EAAE;UACjBA,cAAc,GAAGpW,MAAM,CAACkH,8BAA8B,CAAC;UACvDmP,iBAAiB,GAAGrW,MAAM,CAACmH,iCAAiC,CAAC;QACjE;QACA,IAAI+B,QAAQ,EAAE;UACVmN,iBAAiB,CAAC9S,IAAI,CAACvD,MAAM,EAAEuW,kBAAkB,EAAErN,QAAQ,CAAC;QAChE;QACA,MAAMuN,WAAW,GAAIzW,MAAM,CAAC6V,YAAY,CAAC,GAAG,MAAM;UAC9C,IAAI7V,MAAM,CAAC0W,UAAU,KAAK1W,MAAM,CAAC2W,IAAI,EAAE;YACnC;YACA;YACA,IAAI,CAACpZ,IAAI,CAACkU,OAAO,IAAIzR,MAAM,CAAC8V,aAAa,CAAC,IAAI3Y,IAAI,CAACO,KAAK,KAAK8Y,SAAS,EAAE;cACpE;cACA;cACA;cACA;cACA;cACA;cACA;cACA,MAAMI,SAAS,GAAG5W,MAAM,CAACuH,IAAI,CAACxN,UAAU,CAAC,WAAW,CAAC,CAAC;cACtD,IAAIiG,MAAM,CAAC6W,MAAM,KAAK,CAAC,IAAID,SAAS,IAAIA,SAAS,CAACjX,MAAM,GAAG,CAAC,EAAE;gBAC1D,MAAMmX,SAAS,GAAG3Z,IAAI,CAACJ,MAAM;gBAC7BI,IAAI,CAACJ,MAAM,GAAG,YAAY;kBACtB;kBACA;kBACA,MAAM6Z,SAAS,GAAG5W,MAAM,CAACuH,IAAI,CAACxN,UAAU,CAAC,WAAW,CAAC,CAAC;kBACtD,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkX,SAAS,CAACjX,MAAM,EAAED,CAAC,EAAE,EAAE;oBACvC,IAAIkX,SAAS,CAAClX,CAAC,CAAC,KAAKvC,IAAI,EAAE;sBACvByZ,SAAS,CAACzG,MAAM,CAACzQ,CAAC,EAAE,CAAC,CAAC;oBAC1B;kBACJ;kBACA,IAAI,CAACnC,IAAI,CAACkU,OAAO,IAAItU,IAAI,CAACO,KAAK,KAAK8Y,SAAS,EAAE;oBAC3CM,SAAS,CAACvT,IAAI,CAACpG,IAAI,CAAC;kBACxB;gBACJ,CAAC;gBACDyZ,SAAS,CAACjU,IAAI,CAACxF,IAAI,CAAC;cACxB,CAAC,MACI;gBACDA,IAAI,CAACJ,MAAM,CAAC,CAAC;cACjB;YACJ,CAAC,MACI,IAAI,CAACQ,IAAI,CAACkU,OAAO,IAAIzR,MAAM,CAAC8V,aAAa,CAAC,KAAK,KAAK,EAAE;cACvD;cACA9V,MAAM,CAACgW,0BAA0B,CAAC,GAAG,IAAI;YAC7C;UACJ;QACJ,CAAE;QACFI,cAAc,CAAC7S,IAAI,CAACvD,MAAM,EAAEuW,kBAAkB,EAAEE,WAAW,CAAC;QAC5D,MAAMM,UAAU,GAAG/W,MAAM,CAAC2V,QAAQ,CAAC;QACnC,IAAI,CAACoB,UAAU,EAAE;UACb/W,MAAM,CAAC2V,QAAQ,CAAC,GAAGxY,IAAI;QAC3B;QACA6Z,UAAU,CAACvU,KAAK,CAACzC,MAAM,EAAEzC,IAAI,CAACiG,IAAI,CAAC;QACnCxD,MAAM,CAAC8V,aAAa,CAAC,GAAG,IAAI;QAC5B,OAAO3Y,IAAI;MACf;MACA,SAAS8Z,mBAAmBA,CAAA,EAAG,CAAE;MACjC,SAASzD,SAASA,CAACrW,IAAI,EAAE;QACrB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAI;QACtB;QACA;QACAA,IAAI,CAACkU,OAAO,GAAG,IAAI;QACnB,OAAOyF,WAAW,CAACzU,KAAK,CAAClF,IAAI,CAACyC,MAAM,EAAEzC,IAAI,CAACiG,IAAI,CAAC;MACpD;MACA,MAAM2T,UAAU,GAAG5R,WAAW,CAAC2Q,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAU7S,IAAI,EAAEG,IAAI,EAAE;QACxFH,IAAI,CAACuS,QAAQ,CAAC,GAAGpS,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;QACjCH,IAAI,CAAC0S,OAAO,CAAC,GAAGvS,IAAI,CAAC,CAAC,CAAC;QACvB,OAAO2T,UAAU,CAAC1U,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACvC,CAAC,CAAC;MACF,MAAM4T,qBAAqB,GAAG,qBAAqB;MACnD,MAAMC,iBAAiB,GAAG5P,UAAU,CAAC,mBAAmB,CAAC;MACzD,MAAM6P,mBAAmB,GAAG7P,UAAU,CAAC,qBAAqB,CAAC;MAC7D,MAAMuP,UAAU,GAAGzR,WAAW,CAAC2Q,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAU7S,IAAI,EAAEG,IAAI,EAAE;QACxF,IAAI+D,IAAI,CAAC1M,OAAO,CAACyc,mBAAmB,CAAC,KAAK,IAAI,EAAE;UAC5C;UACA;UACA;UACA,OAAON,UAAU,CAACvU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC;QACA,IAAIH,IAAI,CAACuS,QAAQ,CAAC,EAAE;UAChB;UACA,OAAOoB,UAAU,CAACvU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC,CAAC,MACI;UACD,MAAML,OAAO,GAAG;YACZnD,MAAM,EAAEqD,IAAI;YACZkU,GAAG,EAAElU,IAAI,CAAC0S,OAAO,CAAC;YAClBvY,UAAU,EAAE,KAAK;YACjBgG,IAAI,EAAEA,IAAI;YACViO,OAAO,EAAE;UACb,CAAC;UACD,MAAMtU,IAAI,GAAGqK,gCAAgC,CAAC4P,qBAAqB,EAAEH,mBAAmB,EAAE9T,OAAO,EAAExE,YAAY,EAAE6U,SAAS,CAAC;UAC3H,IAAInQ,IAAI,IACJA,IAAI,CAAC2S,0BAA0B,CAAC,KAAK,IAAI,IACzC,CAAC7S,OAAO,CAACsO,OAAO,IAChBtU,IAAI,CAACO,KAAK,KAAK8Y,SAAS,EAAE;YAC1B;YACA;YACA;YACArZ,IAAI,CAACJ,MAAM,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;MACF,MAAMma,WAAW,GAAG3R,WAAW,CAAC2Q,uBAAuB,EAAE,OAAO,EAAE,MAAM,UAAU7S,IAAI,EAAEG,IAAI,EAAE;QAC1F,MAAMrG,IAAI,GAAGgZ,eAAe,CAAC9S,IAAI,CAAC;QAClC,IAAIlG,IAAI,IAAI,OAAOA,IAAI,CAACG,IAAI,IAAI,QAAQ,EAAE;UACtC;UACA;UACA;UACA;UACA,IAAIH,IAAI,CAACgB,QAAQ,IAAI,IAAI,IAAKhB,IAAI,CAACI,IAAI,IAAIJ,IAAI,CAACI,IAAI,CAACkU,OAAQ,EAAE;YAC3D;UACJ;UACAtU,IAAI,CAACvC,IAAI,CAAC0E,UAAU,CAACnC,IAAI,CAAC;QAC9B,CAAC,MACI,IAAIoK,IAAI,CAAC1M,OAAO,CAACwc,iBAAiB,CAAC,KAAK,IAAI,EAAE;UAC/C;UACA,OAAOH,WAAW,CAACzU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC;QACA;QACA;QACA;MACJ,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF+D,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAGrB,MAAM,IAAK;IACzC;IACA,IAAIA,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,WAAW,CAAC,CAAC2d,WAAW,EAAE;MACxDzP,cAAc,CAAClO,MAAM,CAAC,WAAW,CAAC,CAAC2d,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;IAC5F;EACJ,CAAC,CAAC;EACFjQ,IAAI,CAACrM,YAAY,CAAC,uBAAuB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,KAAK;IACzD;IACA,SAASkQ,2BAA2BA,CAACtF,OAAO,EAAE;MAC1C,OAAO,UAAUuF,CAAC,EAAE;QAChB,MAAMC,UAAU,GAAG5F,cAAc,CAAClY,MAAM,EAAEsY,OAAO,CAAC;QAClDwF,UAAU,CAACC,OAAO,CAAEha,SAAS,IAAK;UAC9B;UACA;UACA,MAAMia,qBAAqB,GAAGhe,MAAM,CAAC,uBAAuB,CAAC;UAC7D,IAAIge,qBAAqB,EAAE;YACvB,MAAMC,GAAG,GAAG,IAAID,qBAAqB,CAAC1F,OAAO,EAAE;cAC3C4F,OAAO,EAAEL,CAAC,CAACK,OAAO;cAClBC,MAAM,EAAEN,CAAC,CAACO;YACd,CAAC,CAAC;YACFra,SAAS,CAACb,MAAM,CAAC+a,GAAG,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,IAAIje,MAAM,CAAC,uBAAuB,CAAC,EAAE;MACjC0N,IAAI,CAACE,UAAU,CAAC,kCAAkC,CAAC,CAAC,GAChDgQ,2BAA2B,CAAC,oBAAoB,CAAC;MACrDlQ,IAAI,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,GACvCgQ,2BAA2B,CAAC,kBAAkB,CAAC;IACvD;EACJ,CAAC,CAAC;EACFlQ,IAAI,CAACrM,YAAY,CAAC,gBAAgB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACvD6F,mBAAmB,CAAChZ,MAAM,EAAEmT,GAAG,CAAC;EACpC,CAAC,CAAC;AACN;AAEA,SAASkL,YAAYA,CAAC3Q,IAAI,EAAE;EACxBA,IAAI,CAACrM,YAAY,CAAC,kBAAkB,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IACzD,MAAMjH,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;IACtE,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;IAClD,SAASyR,sBAAsBA,CAACvO,GAAG,EAAE;MACjC,IAAIA,GAAG,IAAIA,GAAG,CAAC7F,QAAQ,KAAKE,MAAM,CAACC,SAAS,CAACH,QAAQ,EAAE;QACnD,MAAM8G,SAAS,GAAGjB,GAAG,CAACjO,WAAW,IAAIiO,GAAG,CAACjO,WAAW,CAAC3B,IAAI;QACzD,OAAO,CAAC6Q,SAAS,GAAGA,SAAS,GAAG,EAAE,IAAI,IAAI,GAAGuN,IAAI,CAACC,SAAS,CAACzO,GAAG,CAAC;MACpE;MACA,OAAOA,GAAG,GAAGA,GAAG,CAAC7F,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACqG,GAAG,CAAC;IACrE;IACA,MAAM7P,UAAU,GAAGiT,GAAG,CAAC/H,MAAM;IAC7B,MAAMqT,sBAAsB,GAAG,EAAE;IACjC,MAAMC,yCAAyC,GAAG1e,MAAM,CAACE,UAAU,CAAC,6CAA6C,CAAC,CAAC,KAAK,KAAK;IAC7H,MAAMsK,aAAa,GAAGtK,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAMuK,UAAU,GAAGvK,UAAU,CAAC,MAAM,CAAC;IACrC,MAAMye,aAAa,GAAG,mBAAmB;IACzCxL,GAAG,CAACjI,gBAAgB,GAAI2S,CAAC,IAAK;MAC1B,IAAI1K,GAAG,CAAC5H,iBAAiB,CAAC,CAAC,EAAE;QACzB,MAAM6S,SAAS,GAAGP,CAAC,IAAIA,CAAC,CAACO,SAAS;QAClC,IAAIA,SAAS,EAAE;UACXQ,OAAO,CAACzb,KAAK,CAAC,8BAA8B,EAAEib,SAAS,YAAYvd,KAAK,GAAGud,SAAS,CAAC5O,OAAO,GAAG4O,SAAS,EAAE,SAAS,EAAEP,CAAC,CAAC9c,IAAI,CAACZ,IAAI,EAAE,SAAS,EAAE0d,CAAC,CAACva,IAAI,IAAIua,CAAC,CAACva,IAAI,CAACZ,MAAM,EAAE,UAAU,EAAE0b,SAAS,EAAEA,SAAS,YAAYvd,KAAK,GAAGud,SAAS,CAACS,KAAK,GAAGta,SAAS,CAAC;QAC1P,CAAC,MACI;UACDqa,OAAO,CAACzb,KAAK,CAAC0a,CAAC,CAAC;QACpB;MACJ;IACJ,CAAC;IACD1K,GAAG,CAAChI,kBAAkB,GAAG,MAAM;MAC3B,OAAOsT,sBAAsB,CAAC3Y,MAAM,EAAE;QAClC,MAAMgZ,oBAAoB,GAAGL,sBAAsB,CAACM,KAAK,CAAC,CAAC;QAC3D,IAAI;UACAD,oBAAoB,CAAC/d,IAAI,CAAC8B,UAAU,CAAC,MAAM;YACvC,IAAIic,oBAAoB,CAACE,aAAa,EAAE;cACpC,MAAMF,oBAAoB,CAACV,SAAS;YACxC;YACA,MAAMU,oBAAoB;UAC9B,CAAC,CAAC;QACN,CAAC,CACD,OAAO3b,KAAK,EAAE;UACV8b,wBAAwB,CAAC9b,KAAK,CAAC;QACnC;MACJ;IACJ,CAAC;IACD,MAAM+b,0CAA0C,GAAGhf,UAAU,CAAC,kCAAkC,CAAC;IACjG,SAAS+e,wBAAwBA,CAACpB,CAAC,EAAE;MACjC1K,GAAG,CAACjI,gBAAgB,CAAC2S,CAAC,CAAC;MACvB,IAAI;QACA,MAAMsB,OAAO,GAAGzR,IAAI,CAACwR,0CAA0C,CAAC;QAChE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;UAC/BA,OAAO,CAACzV,IAAI,CAAC,IAAI,EAAEmU,CAAC,CAAC;QACzB;MACJ,CAAC,CACD,OAAO5Y,GAAG,EAAE,CAAE;IAClB;IACA,SAASma,UAAUA,CAACpW,KAAK,EAAE;MACvB,OAAOA,KAAK,IAAIA,KAAK,CAACqW,IAAI;IAC9B;IACA,SAASC,iBAAiBA,CAACtW,KAAK,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,SAASuW,gBAAgBA,CAACnB,SAAS,EAAE;MACjC,OAAOoB,gBAAgB,CAACC,MAAM,CAACrB,SAAS,CAAC;IAC7C;IACA,MAAMsB,WAAW,GAAGxf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAMyf,WAAW,GAAGzf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAM0f,aAAa,GAAG1f,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAM2f,wBAAwB,GAAG3f,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAM4f,wBAAwB,GAAG5f,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAMwC,MAAM,GAAG,cAAc;IAC7B,MAAMqd,UAAU,GAAG,IAAI;IACvB,MAAMC,QAAQ,GAAG,IAAI;IACrB,MAAMC,QAAQ,GAAG,KAAK;IACtB,MAAMC,iBAAiB,GAAG,CAAC;IAC3B,SAASC,YAAYA,CAACjC,OAAO,EAAEra,KAAK,EAAE;MAClC,OAAQuc,CAAC,IAAK;QACV,IAAI;UACAC,cAAc,CAACnC,OAAO,EAAEra,KAAK,EAAEuc,CAAC,CAAC;QACrC,CAAC,CACD,OAAOnb,GAAG,EAAE;UACRob,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAEjZ,GAAG,CAAC;QACvC;QACA;MACJ,CAAC;IACL;IACA,MAAMoP,IAAI,GAAG,SAAAA,CAAA,EAAY;MACrB,IAAIiM,SAAS,GAAG,KAAK;MACrB,OAAO,SAASC,OAAOA,CAACC,eAAe,EAAE;QACrC,OAAO,YAAY;UACf,IAAIF,SAAS,EAAE;YACX;UACJ;UACAA,SAAS,GAAG,IAAI;UAChBE,eAAe,CAAC5X,KAAK,CAAC,IAAI,EAAE9F,SAAS,CAAC;QAC1C,CAAC;MACL,CAAC;IACL,CAAC;IACD,MAAM2d,UAAU,GAAG,8BAA8B;IACjD,MAAMC,yBAAyB,GAAGxgB,UAAU,CAAC,kBAAkB,CAAC;IAChE;IACA,SAASmgB,cAAcA,CAACnC,OAAO,EAAEra,KAAK,EAAEmF,KAAK,EAAE;MAC3C,MAAM2X,WAAW,GAAGtM,IAAI,CAAC,CAAC;MAC1B,IAAI6J,OAAO,KAAKlV,KAAK,EAAE;QACnB,MAAM,IAAI4X,SAAS,CAACH,UAAU,CAAC;MACnC;MACA,IAAIvC,OAAO,CAACwB,WAAW,CAAC,KAAKK,UAAU,EAAE;QACrC;QACA,IAAIV,IAAI,GAAG,IAAI;QACf,IAAI;UACA,IAAI,OAAOrW,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;YAC1DqW,IAAI,GAAGrW,KAAK,IAAIA,KAAK,CAACqW,IAAI;UAC9B;QACJ,CAAC,CACD,OAAOpa,GAAG,EAAE;UACR0b,WAAW,CAAC,MAAM;YACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAEjZ,GAAG,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC;UACJ,OAAOiZ,OAAO;QAClB;QACA;QACA,IAAIra,KAAK,KAAKoc,QAAQ,IAClBjX,KAAK,YAAYwW,gBAAgB,IACjCxW,KAAK,CAACxH,cAAc,CAACke,WAAW,CAAC,IACjC1W,KAAK,CAACxH,cAAc,CAACme,WAAW,CAAC,IACjC3W,KAAK,CAAC0W,WAAW,CAAC,KAAKK,UAAU,EAAE;UACnCc,oBAAoB,CAAC7X,KAAK,CAAC;UAC3BqX,cAAc,CAACnC,OAAO,EAAElV,KAAK,CAAC0W,WAAW,CAAC,EAAE1W,KAAK,CAAC2W,WAAW,CAAC,CAAC;QACnE,CAAC,MACI,IAAI9b,KAAK,KAAKoc,QAAQ,IAAI,OAAOZ,IAAI,KAAK,UAAU,EAAE;UACvD,IAAI;YACAA,IAAI,CAAC3V,IAAI,CAACV,KAAK,EAAE2X,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAEra,KAAK,CAAC,CAAC,EAAE8c,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;UAC1G,CAAC,CACD,OAAOjZ,GAAG,EAAE;YACR0b,WAAW,CAAC,MAAM;cACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAEjZ,GAAG,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC;UACR;QACJ,CAAC,MACI;UACDiZ,OAAO,CAACwB,WAAW,CAAC,GAAG7b,KAAK;UAC5B,MAAMoH,KAAK,GAAGiT,OAAO,CAACyB,WAAW,CAAC;UAClCzB,OAAO,CAACyB,WAAW,CAAC,GAAG3W,KAAK;UAC5B,IAAIkV,OAAO,CAAC0B,aAAa,CAAC,KAAKA,aAAa,EAAE;YAC1C;YACA,IAAI/b,KAAK,KAAKmc,QAAQ,EAAE;cACpB;cACA;cACA9B,OAAO,CAACwB,WAAW,CAAC,GAAGxB,OAAO,CAAC4B,wBAAwB,CAAC;cACxD5B,OAAO,CAACyB,WAAW,CAAC,GAAGzB,OAAO,CAAC2B,wBAAwB,CAAC;YAC5D;UACJ;UACA;UACA;UACA,IAAIhc,KAAK,KAAKoc,QAAQ,IAAIjX,KAAK,YAAYnI,KAAK,EAAE;YAC9C;YACA,MAAMigB,KAAK,GAAGpT,IAAI,CAACvM,WAAW,IAC1BuM,IAAI,CAACvM,WAAW,CAACuC,IAAI,IACrBgK,IAAI,CAACvM,WAAW,CAACuC,IAAI,CAACib,aAAa,CAAC;YACxC,IAAImC,KAAK,EAAE;cACP;cACA7U,oBAAoB,CAACjD,KAAK,EAAE0X,yBAAyB,EAAE;gBACnDvQ,YAAY,EAAE,IAAI;gBAClBD,UAAU,EAAE,KAAK;gBACjB1B,QAAQ,EAAE,IAAI;gBACdxF,KAAK,EAAE8X;cACX,CAAC,CAAC;YACN;UACJ;UACA,KAAK,IAAIjb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,KAAK,CAACnF,MAAM,GAAG;YAC/Bib,uBAAuB,CAAC7C,OAAO,EAAEjT,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,CAAC;UACpF;UACA,IAAIoF,KAAK,CAACnF,MAAM,IAAI,CAAC,IAAIjC,KAAK,IAAIoc,QAAQ,EAAE;YACxC/B,OAAO,CAACwB,WAAW,CAAC,GAAGQ,iBAAiB;YACxC,IAAIpB,oBAAoB,GAAG9V,KAAK;YAChC,IAAI;cACA;cACA;cACA;cACA,MAAM,IAAInI,KAAK,CAAC,yBAAyB,GACrCyd,sBAAsB,CAACtV,KAAK,CAAC,IAC5BA,KAAK,IAAIA,KAAK,CAAC6V,KAAK,GAAG,IAAI,GAAG7V,KAAK,CAAC6V,KAAK,GAAG,EAAE,CAAC,CAAC;YACzD,CAAC,CACD,OAAO5Z,GAAG,EAAE;cACR6Z,oBAAoB,GAAG7Z,GAAG;YAC9B;YACA,IAAIyZ,yCAAyC,EAAE;cAC3C;cACA;cACAI,oBAAoB,CAACE,aAAa,GAAG,IAAI;YAC7C;YACAF,oBAAoB,CAACV,SAAS,GAAGpV,KAAK;YACtC8V,oBAAoB,CAACZ,OAAO,GAAGA,OAAO;YACtCY,oBAAoB,CAAC/d,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;YACxC8d,oBAAoB,CAACxb,IAAI,GAAGoK,IAAI,CAACvM,WAAW;YAC5Csd,sBAAsB,CAAC3V,IAAI,CAACgW,oBAAoB,CAAC;YACjD3L,GAAG,CAACjO,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;MACA;MACA,OAAOgZ,OAAO;IAClB;IACA,MAAM8C,yBAAyB,GAAG9gB,UAAU,CAAC,yBAAyB,CAAC;IACvE,SAAS2gB,oBAAoBA,CAAC3C,OAAO,EAAE;MACnC,IAAIA,OAAO,CAACwB,WAAW,CAAC,KAAKQ,iBAAiB,EAAE;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,MAAMf,OAAO,GAAGzR,IAAI,CAACsT,yBAAyB,CAAC;UAC/C,IAAI7B,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;YAC1CA,OAAO,CAACzV,IAAI,CAAC,IAAI,EAAE;cAAE0U,SAAS,EAAEF,OAAO,CAACyB,WAAW,CAAC;cAAEzB,OAAO,EAAEA;YAAQ,CAAC,CAAC;UAC7E;QACJ,CAAC,CACD,OAAOjZ,GAAG,EAAE,CAAE;QACdiZ,OAAO,CAACwB,WAAW,CAAC,GAAGO,QAAQ;QAC/B,KAAK,IAAIpa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4Y,sBAAsB,CAAC3Y,MAAM,EAAED,CAAC,EAAE,EAAE;UACpD,IAAIqY,OAAO,KAAKO,sBAAsB,CAAC5Y,CAAC,CAAC,CAACqY,OAAO,EAAE;YAC/CO,sBAAsB,CAACnI,MAAM,CAACzQ,CAAC,EAAE,CAAC,CAAC;UACvC;QACJ;MACJ;IACJ;IACA,SAASkb,uBAAuBA,CAAC7C,OAAO,EAAEnd,IAAI,EAAEkgB,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACnFN,oBAAoB,CAAC3C,OAAO,CAAC;MAC7B,MAAMkD,YAAY,GAAGlD,OAAO,CAACwB,WAAW,CAAC;MACzC,MAAMzZ,QAAQ,GAAGmb,YAAY,GACvB,OAAOF,WAAW,KAAK,UAAU,GAC7BA,WAAW,GACX5B,iBAAiB,GACrB,OAAO6B,UAAU,KAAK,UAAU,GAC5BA,UAAU,GACV5B,gBAAgB;MAC1Bxe,IAAI,CAACmE,iBAAiB,CAACxC,MAAM,EAAE,MAAM;QACjC,IAAI;UACA,MAAM2e,kBAAkB,GAAGnD,OAAO,CAACyB,WAAW,CAAC;UAC/C,MAAM2B,gBAAgB,GAAG,CAAC,CAACL,YAAY,IAAIrB,aAAa,KAAKqB,YAAY,CAACrB,aAAa,CAAC;UACxF,IAAI0B,gBAAgB,EAAE;YAClB;YACAL,YAAY,CAACpB,wBAAwB,CAAC,GAAGwB,kBAAkB;YAC3DJ,YAAY,CAACnB,wBAAwB,CAAC,GAAGsB,YAAY;UACzD;UACA;UACA,MAAMpY,KAAK,GAAGjI,IAAI,CAACgC,GAAG,CAACkD,QAAQ,EAAE1B,SAAS,EAAE+c,gBAAgB,IAAIrb,QAAQ,KAAKsZ,gBAAgB,IAAItZ,QAAQ,KAAKqZ,iBAAiB,GACzH,EAAE,GACF,CAAC+B,kBAAkB,CAAC,CAAC;UAC3BhB,cAAc,CAACY,YAAY,EAAE,IAAI,EAAEjY,KAAK,CAAC;QAC7C,CAAC,CACD,OAAO7F,KAAK,EAAE;UACV;UACAkd,cAAc,CAACY,YAAY,EAAE,KAAK,EAAE9d,KAAK,CAAC;QAC9C;MACJ,CAAC,EAAE8d,YAAY,CAAC;IACpB;IACA,MAAMM,4BAA4B,GAAG,+CAA+C;IACpF,MAAMjW,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;IAC5B,MAAMkW,cAAc,GAAGxhB,MAAM,CAACwhB,cAAc;IAC5C,MAAMhC,gBAAgB,CAAC;MACnB,OAAOtV,QAAQA,CAAA,EAAG;QACd,OAAOqX,4BAA4B;MACvC;MACA,OAAOxW,OAAOA,CAAC/B,KAAK,EAAE;QAClB,IAAIA,KAAK,YAAYwW,gBAAgB,EAAE;UACnC,OAAOxW,KAAK;QAChB;QACA,OAAOqX,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEL,QAAQ,EAAEhX,KAAK,CAAC;MAC1D;MACA,OAAOyW,MAAMA,CAACtc,KAAK,EAAE;QACjB,OAAOkd,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEJ,QAAQ,EAAE9c,KAAK,CAAC;MAC1D;MACA,OAAOse,aAAaA,CAAA,EAAG;QACnB,MAAMnS,MAAM,GAAG,CAAC,CAAC;QACjBA,MAAM,CAAC4O,OAAO,GAAG,IAAIsB,gBAAgB,CAAC,CAACkC,GAAG,EAAEC,GAAG,KAAK;UAChDrS,MAAM,CAACvE,OAAO,GAAG2W,GAAG;UACpBpS,MAAM,CAACmQ,MAAM,GAAGkC,GAAG;QACvB,CAAC,CAAC;QACF,OAAOrS,MAAM;MACjB;MACA,OAAOsS,GAAGA,CAACC,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;UAC1D,OAAOC,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,MAAMS,QAAQ,GAAG,EAAE;QACnB,IAAIrc,KAAK,GAAG,CAAC;QACb,IAAI;UACA,KAAK,IAAIwa,CAAC,IAAIyB,MAAM,EAAE;YAClBjc,KAAK,EAAE;YACPqc,QAAQ,CAACnZ,IAAI,CAAC0W,gBAAgB,CAACzU,OAAO,CAACqV,CAAC,CAAC,CAAC;UAC9C;QACJ,CAAC,CACD,OAAOnb,GAAG,EAAE;UACR,OAAO+c,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAI5b,KAAK,KAAK,CAAC,EAAE;UACb,OAAOoc,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIU,QAAQ,GAAG,KAAK;QACpB,MAAMxN,MAAM,GAAG,EAAE;QACjB,OAAO,IAAI8K,gBAAgB,CAAC,CAACzU,OAAO,EAAE0U,MAAM,KAAK;UAC7C,KAAK,IAAI5Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoc,QAAQ,CAACnc,MAAM,EAAED,CAAC,EAAE,EAAE;YACtCoc,QAAQ,CAACpc,CAAC,CAAC,CAACwZ,IAAI,CAAEe,CAAC,IAAK;cACpB,IAAI8B,QAAQ,EAAE;gBACV;cACJ;cACAA,QAAQ,GAAG,IAAI;cACfnX,OAAO,CAACqV,CAAC,CAAC;YACd,CAAC,EAAGnb,GAAG,IAAK;cACRyP,MAAM,CAAC5L,IAAI,CAAC7D,GAAG,CAAC;cAChBW,KAAK,EAAE;cACP,IAAIA,KAAK,KAAK,CAAC,EAAE;gBACbsc,QAAQ,GAAG,IAAI;gBACfzC,MAAM,CAAC,IAAI+B,cAAc,CAAC9M,MAAM,EAAE,4BAA4B,CAAC,CAAC;cACpE;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,OAAOyN,IAAIA,CAACN,MAAM,EAAE;QAChB,IAAI9W,OAAO;QACX,IAAI0U,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjC5W,OAAO,GAAG2W,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF,SAASS,SAASA,CAACpZ,KAAK,EAAE;UACtB+B,OAAO,CAAC/B,KAAK,CAAC;QAClB;QACA,SAASqZ,QAAQA,CAAClf,KAAK,EAAE;UACrBsc,MAAM,CAACtc,KAAK,CAAC;QACjB;QACA,KAAK,IAAI6F,KAAK,IAAI6Y,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAACpW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACAA,KAAK,CAACqW,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;QACnC;QACA,OAAOnE,OAAO;MAClB;MACA,OAAOoE,GAAGA,CAACT,MAAM,EAAE;QACf,OAAOrC,gBAAgB,CAAC+C,eAAe,CAACV,MAAM,CAAC;MACnD;MACA,OAAOW,UAAUA,CAACX,MAAM,EAAE;QACtB,MAAMY,CAAC,GAAG,IAAI,IAAI,IAAI,CAACpY,SAAS,YAAYmV,gBAAgB,GAAG,IAAI,GAAGA,gBAAgB;QACtF,OAAOiD,CAAC,CAACF,eAAe,CAACV,MAAM,EAAE;UAC7Ba,YAAY,EAAG1Z,KAAK,KAAM;YAAEgU,MAAM,EAAE,WAAW;YAAEhU;UAAM,CAAC,CAAC;UACzD2Z,aAAa,EAAG1d,GAAG,KAAM;YAAE+X,MAAM,EAAE,UAAU;YAAEmB,MAAM,EAAElZ;UAAI,CAAC;QAChE,CAAC,CAAC;MACN;MACA,OAAOsd,eAAeA,CAACV,MAAM,EAAEpf,QAAQ,EAAE;QACrC,IAAIsI,OAAO;QACX,IAAI0U,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjC5W,OAAO,GAAG2W,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF;QACA,IAAIiB,eAAe,GAAG,CAAC;QACvB,IAAIC,UAAU,GAAG,CAAC;QAClB,MAAMC,cAAc,GAAG,EAAE;QACzB,KAAK,IAAI9Z,KAAK,IAAI6Y,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAACpW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACA,MAAM+Z,aAAa,GAAGF,UAAU;UAChC,IAAI;YACA7Z,KAAK,CAACqW,IAAI,CAAErW,KAAK,IAAK;cAClB8Z,cAAc,CAACC,aAAa,CAAC,GAAGtgB,QAAQ,GAAGA,QAAQ,CAACigB,YAAY,CAAC1Z,KAAK,CAAC,GAAGA,KAAK;cAC/E4Z,eAAe,EAAE;cACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;gBACvB7X,OAAO,CAAC+X,cAAc,CAAC;cAC3B;YACJ,CAAC,EAAG7d,GAAG,IAAK;cACR,IAAI,CAACxC,QAAQ,EAAE;gBACXgd,MAAM,CAACxa,GAAG,CAAC;cACf,CAAC,MACI;gBACD6d,cAAc,CAACC,aAAa,CAAC,GAAGtgB,QAAQ,CAACkgB,aAAa,CAAC1d,GAAG,CAAC;gBAC3D2d,eAAe,EAAE;gBACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;kBACvB7X,OAAO,CAAC+X,cAAc,CAAC;gBAC3B;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CACD,OAAOE,OAAO,EAAE;YACZvD,MAAM,CAACuD,OAAO,CAAC;UACnB;UACAJ,eAAe,EAAE;UACjBC,UAAU,EAAE;QAChB;QACA;QACAD,eAAe,IAAI,CAAC;QACpB,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvB7X,OAAO,CAAC+X,cAAc,CAAC;QAC3B;QACA,OAAO5E,OAAO;MAClB;MACApc,WAAWA,CAACmhB,QAAQ,EAAE;QAClB,MAAM/E,OAAO,GAAG,IAAI;QACpB,IAAI,EAAEA,OAAO,YAAYsB,gBAAgB,CAAC,EAAE;UACxC,MAAM,IAAI3e,KAAK,CAAC,gCAAgC,CAAC;QACrD;QACAqd,OAAO,CAACwB,WAAW,CAAC,GAAGK,UAAU;QACjC7B,OAAO,CAACyB,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAI;UACA,MAAMgB,WAAW,GAAGtM,IAAI,CAAC,CAAC;UAC1B4O,QAAQ,IACJA,QAAQ,CAACtC,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE8B,QAAQ,CAAC,CAAC,EAAEW,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE+B,QAAQ,CAAC,CAAC,CAAC;QAC5G,CAAC,CACD,OAAO9c,KAAK,EAAE;UACVkd,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE/a,KAAK,CAAC;QACzC;MACJ;MACA,KAAK2e,MAAM,CAACoB,WAAW,IAAI;QACvB,OAAO,SAAS;MACpB;MACA,KAAKpB,MAAM,CAACqB,OAAO,IAAI;QACnB,OAAO3D,gBAAgB;MAC3B;MACAH,IAAIA,CAAC6B,WAAW,EAAEC,UAAU,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIiC,CAAC,GAAG,IAAI,CAACthB,WAAW,GAAGggB,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG,IAAI,CAACthB,WAAW,IAAI0d,gBAAgB;QAC5C;QACA,MAAMyB,YAAY,GAAG,IAAImC,CAAC,CAAC9X,IAAI,CAAC;QAChC,MAAMvK,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAI,IAAI,CAAC0e,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAAC7W,IAAI,CAAC/H,IAAI,EAAEkgB,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QACvE,CAAC,MACI;UACDJ,uBAAuB,CAAC,IAAI,EAAEhgB,IAAI,EAAEkgB,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QAC9E;QACA,OAAOF,YAAY;MACvB;MACAoC,KAAKA,CAAClC,UAAU,EAAE;QACd,OAAO,IAAI,CAAC9B,IAAI,CAAC,IAAI,EAAE8B,UAAU,CAAC;MACtC;MACAmC,OAAOA,CAACC,SAAS,EAAE;QACf;QACA,IAAIH,CAAC,GAAG,IAAI,CAACthB,WAAW,GAAGggB,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG5D,gBAAgB;QACxB;QACA,MAAMyB,YAAY,GAAG,IAAImC,CAAC,CAAC9X,IAAI,CAAC;QAChC2V,YAAY,CAACrB,aAAa,CAAC,GAAGA,aAAa;QAC3C,MAAM7e,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAI,IAAI,CAAC0e,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAAC7W,IAAI,CAAC/H,IAAI,EAAEkgB,YAAY,EAAEsC,SAAS,EAAEA,SAAS,CAAC;QACpE,CAAC,MACI;UACDxC,uBAAuB,CAAC,IAAI,EAAEhgB,IAAI,EAAEkgB,YAAY,EAAEsC,SAAS,EAAEA,SAAS,CAAC;QAC3E;QACA,OAAOtC,YAAY;MACvB;IACJ;IACA;IACA;IACAzB,gBAAgB,CAAC,SAAS,CAAC,GAAGA,gBAAgB,CAACzU,OAAO;IACtDyU,gBAAgB,CAAC,QAAQ,CAAC,GAAGA,gBAAgB,CAACC,MAAM;IACpDD,gBAAgB,CAAC,MAAM,CAAC,GAAGA,gBAAgB,CAAC2C,IAAI;IAChD3C,gBAAgB,CAAC,KAAK,CAAC,GAAGA,gBAAgB,CAAC8C,GAAG;IAC9C,MAAMkB,aAAa,GAAIxjB,MAAM,CAACwK,aAAa,CAAC,GAAGxK,MAAM,CAAC,SAAS,CAAE;IACjEA,MAAM,CAAC,SAAS,CAAC,GAAGwf,gBAAgB;IACpC,MAAMiE,iBAAiB,GAAGvjB,UAAU,CAAC,aAAa,CAAC;IACnD,SAAS0L,SAASA,CAAC8X,IAAI,EAAE;MACrB,MAAMrS,KAAK,GAAGqS,IAAI,CAACrZ,SAAS;MAC5B,MAAM2F,IAAI,GAAG9D,8BAA8B,CAACmF,KAAK,EAAE,MAAM,CAAC;MAC1D,IAAIrB,IAAI,KAAKA,IAAI,CAACxB,QAAQ,KAAK,KAAK,IAAI,CAACwB,IAAI,CAACG,YAAY,CAAC,EAAE;QACzD;QACA;QACA;MACJ;MACA,MAAMwT,YAAY,GAAGtS,KAAK,CAACgO,IAAI;MAC/B;MACAhO,KAAK,CAAC5G,UAAU,CAAC,GAAGkZ,YAAY;MAChCD,IAAI,CAACrZ,SAAS,CAACgV,IAAI,GAAG,UAAU+C,SAAS,EAAEC,QAAQ,EAAE;QACjD,MAAMuB,OAAO,GAAG,IAAIpE,gBAAgB,CAAC,CAACzU,OAAO,EAAE0U,MAAM,KAAK;UACtDkE,YAAY,CAACja,IAAI,CAAC,IAAI,EAAEqB,OAAO,EAAE0U,MAAM,CAAC;QAC5C,CAAC,CAAC;QACF,OAAOmE,OAAO,CAACvE,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MACDqB,IAAI,CAACD,iBAAiB,CAAC,GAAG,IAAI;IAClC;IACAtQ,GAAG,CAACvH,SAAS,GAAGA,SAAS;IACzB,SAASiY,OAAOA,CAACviB,EAAE,EAAE;MACjB,OAAO,UAAUkI,IAAI,EAAEG,IAAI,EAAE;QACzB,IAAIma,aAAa,GAAGxiB,EAAE,CAACsH,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC,IAAIma,aAAa,YAAYtE,gBAAgB,EAAE;UAC3C,OAAOsE,aAAa;QACxB;QACA,IAAIC,IAAI,GAAGD,aAAa,CAAChiB,WAAW;QACpC,IAAI,CAACiiB,IAAI,CAACN,iBAAiB,CAAC,EAAE;UAC1B7X,SAAS,CAACmY,IAAI,CAAC;QACnB;QACA,OAAOD,aAAa;MACxB,CAAC;IACL;IACA,IAAIN,aAAa,EAAE;MACf5X,SAAS,CAAC4X,aAAa,CAAC;MACxB9X,WAAW,CAAC1L,MAAM,EAAE,OAAO,EAAGiG,QAAQ,IAAK4d,OAAO,CAAC5d,QAAQ,CAAC,CAAC;IACjE;IACA;IACA+b,OAAO,CAACtU,IAAI,CAACxN,UAAU,CAAC,uBAAuB,CAAC,CAAC,GAAGue,sBAAsB;IAC1E,OAAOe,gBAAgB;EAC3B,CAAC,CAAC;AACN;AAEA,SAASwE,aAAaA,CAACtW,IAAI,EAAE;EACzB;EACA;EACAA,IAAI,CAACrM,YAAY,CAAC,UAAU,EAAGrB,MAAM,IAAK;IACtC;IACA,MAAMikB,wBAAwB,GAAGC,QAAQ,CAAC7Z,SAAS,CAACH,QAAQ;IAC5D,MAAMia,wBAAwB,GAAGvW,UAAU,CAAC,kBAAkB,CAAC;IAC/D,MAAMwW,cAAc,GAAGxW,UAAU,CAAC,SAAS,CAAC;IAC5C,MAAMyW,YAAY,GAAGzW,UAAU,CAAC,OAAO,CAAC;IACxC,MAAM0W,mBAAmB,GAAG,SAASpa,QAAQA,CAAA,EAAG;MAC5C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC5B,MAAMkK,gBAAgB,GAAG,IAAI,CAAC+P,wBAAwB,CAAC;QACvD,IAAI/P,gBAAgB,EAAE;UAClB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;YACxC,OAAO6P,wBAAwB,CAACva,IAAI,CAAC0K,gBAAgB,CAAC;UAC1D,CAAC,MACI;YACD,OAAOhK,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC0K,gBAAgB,CAAC;UAC3D;QACJ;QACA,IAAI,IAAI,KAAK4N,OAAO,EAAE;UAClB,MAAMuC,aAAa,GAAGvkB,MAAM,CAACokB,cAAc,CAAC;UAC5C,IAAIG,aAAa,EAAE;YACf,OAAON,wBAAwB,CAACva,IAAI,CAAC6a,aAAa,CAAC;UACvD;QACJ;QACA,IAAI,IAAI,KAAK1jB,KAAK,EAAE;UAChB,MAAM2jB,WAAW,GAAGxkB,MAAM,CAACqkB,YAAY,CAAC;UACxC,IAAIG,WAAW,EAAE;YACb,OAAOP,wBAAwB,CAACva,IAAI,CAAC8a,WAAW,CAAC;UACrD;QACJ;MACJ;MACA,OAAOP,wBAAwB,CAACva,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC;IACD4a,mBAAmB,CAACH,wBAAwB,CAAC,GAAGF,wBAAwB;IACxEC,QAAQ,CAAC7Z,SAAS,CAACH,QAAQ,GAAGoa,mBAAmB;IACjD;IACA,MAAMG,sBAAsB,GAAGra,MAAM,CAACC,SAAS,CAACH,QAAQ;IACxD,MAAMwa,wBAAwB,GAAG,kBAAkB;IACnDta,MAAM,CAACC,SAAS,CAACH,QAAQ,GAAG,YAAY;MACpC,IAAI,OAAO8X,OAAO,KAAK,UAAU,IAAI,IAAI,YAAYA,OAAO,EAAE;QAC1D,OAAO0C,wBAAwB;MACnC;MACA,OAAOD,sBAAsB,CAAC/a,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASgD,cAAcA,CAACyG,GAAG,EAAEhN,MAAM,EAAEwe,UAAU,EAAEC,MAAM,EAAE3K,SAAS,EAAE;EAChE,MAAM7O,MAAM,GAAGsC,IAAI,CAACxN,UAAU,CAAC0kB,MAAM,CAAC;EACtC,IAAIze,MAAM,CAACiF,MAAM,CAAC,EAAE;IAChB;EACJ;EACA,MAAMyZ,cAAc,GAAI1e,MAAM,CAACiF,MAAM,CAAC,GAAGjF,MAAM,CAACye,MAAM,CAAE;EACxDze,MAAM,CAACye,MAAM,CAAC,GAAG,UAAUzkB,IAAI,EAAE2kB,IAAI,EAAExb,OAAO,EAAE;IAC5C,IAAIwb,IAAI,IAAIA,IAAI,CAACza,SAAS,EAAE;MACxB4P,SAAS,CAAC8D,OAAO,CAAC,UAAUtb,QAAQ,EAAE;QAClC,MAAMC,MAAM,GAAI,GAAEiiB,UAAW,IAAGC,MAAO,IAAG,GAAGniB,QAAQ;QACrD,MAAM4H,SAAS,GAAGya,IAAI,CAACza,SAAS;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIA,SAAS,CAAC7I,cAAc,CAACiB,QAAQ,CAAC,EAAE;YACpC,MAAMsiB,UAAU,GAAG5R,GAAG,CAACjH,8BAA8B,CAAC7B,SAAS,EAAE5H,QAAQ,CAAC;YAC1E,IAAIsiB,UAAU,IAAIA,UAAU,CAAC/b,KAAK,EAAE;cAChC+b,UAAU,CAAC/b,KAAK,GAAGmK,GAAG,CAAC7G,mBAAmB,CAACyY,UAAU,CAAC/b,KAAK,EAAEtG,MAAM,CAAC;cACpEyQ,GAAG,CAAC1G,iBAAiB,CAACqY,IAAI,CAACza,SAAS,EAAE5H,QAAQ,EAAEsiB,UAAU,CAAC;YAC/D,CAAC,MACI,IAAI1a,SAAS,CAAC5H,QAAQ,CAAC,EAAE;cAC1B4H,SAAS,CAAC5H,QAAQ,CAAC,GAAG0Q,GAAG,CAAC7G,mBAAmB,CAACjC,SAAS,CAAC5H,QAAQ,CAAC,EAAEC,MAAM,CAAC;YAC9E;UACJ,CAAC,MACI,IAAI2H,SAAS,CAAC5H,QAAQ,CAAC,EAAE;YAC1B4H,SAAS,CAAC5H,QAAQ,CAAC,GAAG0Q,GAAG,CAAC7G,mBAAmB,CAACjC,SAAS,CAAC5H,QAAQ,CAAC,EAAEC,MAAM,CAAC;UAC9E;QACJ,CAAC,CACD,MAAM;UACF;UACA;QAAA;MAER,CAAC,CAAC;IACN;IACA,OAAOmiB,cAAc,CAACnb,IAAI,CAACvD,MAAM,EAAEhG,IAAI,EAAE2kB,IAAI,EAAExb,OAAO,CAAC;EAC3D,CAAC;EACD6J,GAAG,CAAC3G,qBAAqB,CAACrG,MAAM,CAACye,MAAM,CAAC,EAAEC,cAAc,CAAC;AAC7D;AAEA,SAASG,SAASA,CAACtX,IAAI,EAAE;EACrBA,IAAI,CAACrM,YAAY,CAAC,MAAM,EAAE,CAACrB,MAAM,EAAE0N,IAAI,EAAEyF,GAAG,KAAK;IAC7C;IACA;IACA,MAAMiH,UAAU,GAAGW,eAAe,CAAC/a,MAAM,CAAC;IAC1CmT,GAAG,CAAC1H,iBAAiB,GAAGA,iBAAiB;IACzC0H,GAAG,CAACzH,WAAW,GAAGA,WAAW;IAC7ByH,GAAG,CAACxH,aAAa,GAAGA,aAAa;IACjCwH,GAAG,CAACtH,cAAc,GAAGA,cAAc;IACnC;IACA;IACA;IACA;IACA;IACA,MAAMoZ,0BAA0B,GAAGvX,IAAI,CAACxN,UAAU,CAAC,qBAAqB,CAAC;IACzE,MAAMglB,uBAAuB,GAAGxX,IAAI,CAACxN,UAAU,CAAC,kBAAkB,CAAC;IACnE,IAAIF,MAAM,CAACklB,uBAAuB,CAAC,EAAE;MACjCllB,MAAM,CAACilB,0BAA0B,CAAC,GAAGjlB,MAAM,CAACklB,uBAAuB,CAAC;IACxE;IACA,IAAIllB,MAAM,CAACilB,0BAA0B,CAAC,EAAE;MACpCvX,IAAI,CAACuX,0BAA0B,CAAC,GAAGvX,IAAI,CAACwX,uBAAuB,CAAC,GAC5DllB,MAAM,CAACilB,0BAA0B,CAAC;IAC1C;IACA9R,GAAG,CAACrH,mBAAmB,GAAGA,mBAAmB;IAC7CqH,GAAG,CAAC3H,gBAAgB,GAAGA,gBAAgB;IACvC2H,GAAG,CAACpH,UAAU,GAAGA,UAAU;IAC3BoH,GAAG,CAAClH,oBAAoB,GAAGA,oBAAoB;IAC/CkH,GAAG,CAACjH,8BAA8B,GAAGA,8BAA8B;IACnEiH,GAAG,CAAChH,YAAY,GAAGA,YAAY;IAC/BgH,GAAG,CAAC/G,UAAU,GAAGA,UAAU;IAC3B+G,GAAG,CAAC9G,UAAU,GAAGA,UAAU;IAC3B8G,GAAG,CAAC7G,mBAAmB,GAAGA,mBAAmB;IAC7C6G,GAAG,CAAC5G,gBAAgB,GAAGA,gBAAgB;IACvC4G,GAAG,CAAC3G,qBAAqB,GAAGA,qBAAqB;IACjD2G,GAAG,CAAC1G,iBAAiB,GAAGrC,MAAM,CAACyC,cAAc;IAC7CsG,GAAG,CAACzG,cAAc,GAAGA,cAAc;IACnCyG,GAAG,CAACnH,gBAAgB,GAAG,OAAO;MAC1B0G,aAAa;MACbD,oBAAoB;MACpB2H,UAAU;MACVtL,SAAS;MACTC,KAAK;MACLH,MAAM;MACNrB,QAAQ;MACRC,SAAS;MACTC,kBAAkB;MAClBN,sBAAsB;MACtBC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAAS+X,WAAWA,CAACzX,IAAI,EAAE;EACvB2Q,YAAY,CAAC3Q,IAAI,CAAC;EAClBsW,aAAa,CAACtW,IAAI,CAAC;EACnBsX,SAAS,CAACtX,IAAI,CAAC;AACnB;AAEA,MAAM0X,MAAM,GAAGzY,QAAQ,CAAC,CAAC;AACzBwY,WAAW,CAACC,MAAM,CAAC;AACnB7J,YAAY,CAAC6J,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}