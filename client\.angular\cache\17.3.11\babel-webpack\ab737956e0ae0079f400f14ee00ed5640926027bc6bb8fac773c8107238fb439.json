{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBind = require('call-bind');\n\n// eslint-disable-next-line no-extra-parens\nvar $indexOf = callBind( /** @type {typeof String.prototype.indexOf} */GetIntrinsic('String.prototype.indexOf'));\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n  // eslint-disable-next-line no-extra-parens\n  var intrinsic = /** @type {Parameters<typeof callBind>[0]} */GetIntrinsic(name, !!allowMissing);\n  if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n    return callBind(intrinsic);\n  }\n  return intrinsic;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}