{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/multiselect\";\nfunction AccountRelationshipsComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵlistener(\"click\", function AccountRelationshipsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountRelationshipsComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 14)(5, AccountRelationshipsComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 12);\n    i0.ɵɵlistener(\"click\", function AccountRelationshipsComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"RelationshipType\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 13);\n    i0.ɵɵtext(3, \" Relationship Type \");\n    i0.ɵɵtemplate(4, AccountRelationshipsComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 14)(5, AccountRelationshipsComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountRelationshipsComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 16);\n    i0.ɵɵelementStart(7, \"th\", 17)(8, \"div\", 18);\n    i0.ɵɵtext(9, \" Actions \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"RelationshipType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"RelationshipType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const relation_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (relation_r5 == null ? null : relation_r5.BusinessPartner) || \"-\", \" \");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const relation_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (relation_r5 == null ? null : relation_r5.Shareholder) || \"-\", \" \");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const relation_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (relation_r5 == null ? null : relation_r5.Address) || \"-\", \" \");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_10_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 26);\n    i0.ɵɵtemplate(3, AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 27)(4, AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 27)(5, AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_5_Template, 2, 1, \"ng-container\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"BusinessPartner\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Shareholder\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Address\");\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountRelationshipsComponent_ng_template_10_ng_container_3_Template, 6, 4, \"ng-container\", 16);\n    i0.ɵɵelementStart(4, \"td\", 17)(5, \"button\", 24)(6, \"i\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const relation_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (relation_r5 == null ? null : relation_r5.RelationshipType) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(relation_r5.Action);\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"No relationships found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountRelationshipsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"Loading relationships data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AccountRelationshipsComponent {\n  constructor(accountservice) {\n    this.accountservice = accountservice;\n    this.unsubscribe$ = new Subject();\n    this.relationshipdetails = [];\n    this.bp_id = '';\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'BusinessPartner',\n      header: 'Business Partner'\n    }, {\n      field: 'Shareholder',\n      header: 'Sales Organization'\n    }, {\n      field: 'Address',\n      header: 'Created On'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.relationshipdetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.relationshipdetails = response?.contact_activity;\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountRelationshipsComponent_Factory(t) {\n      return new (t || AccountRelationshipsComponent)(i0.ɵɵdirectiveInject(i1.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountRelationshipsComponent,\n      selectors: [[\"app-account-relationships\"]],\n      decls: 13,\n      vars: 11,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\", \"align-items-center\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"flex\", \"align-items-center\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"font-medium\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-red-500\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\"]],\n      template: function AccountRelationshipsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Relationships\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"p-button\", 4);\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountRelationshipsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function AccountRelationshipsComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, AccountRelationshipsComponent_ng_template_9_Template, 10, 3, \"ng-template\", 8)(10, AccountRelationshipsComponent_ng_template_10_Template, 8, 3, \"ng-template\", 9)(11, AccountRelationshipsComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, AccountRelationshipsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.relationshipdetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.PrimeTemplate, i4.Table, i4.SortableColumn, i4.FrozenColumn, i4.ReorderableColumn, i5.NgControlStatus, i5.NgModel, i6.Button, i7.MultiSelect],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "AccountRelationshipsComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "AccountRelationshipsComponent_ng_template_9_ng_container_6_i_4_Template", "AccountRelationshipsComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "AccountRelationshipsComponent_ng_template_9_Template_th_click_1_listener", "_r1", "AccountRelationshipsComponent_ng_template_9_i_4_Template", "AccountRelationshipsComponent_ng_template_9_i_5_Template", "AccountRelationshipsComponent_ng_template_9_ng_container_6_Template", "selectedColumns", "relation_r5", "BusinessPartner", "Shareholder", "Address", "AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_3_Template", "AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_4_Template", "AccountRelationshipsComponent_ng_template_10_ng_container_3_ng_container_5_Template", "col_r6", "AccountRelationshipsComponent_ng_template_10_ng_container_3_Template", "RelationshipType", "ɵɵtextInterpolate", "Action", "AccountRelationshipsComponent", "constructor", "accountservice", "unsubscribe$", "relationshipdetails", "bp_id", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "account", "pipe", "subscribe", "response", "contact_activity", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "AccountService", "selectors", "decls", "vars", "consts", "template", "AccountRelationshipsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "AccountRelationshipsComponent_Template_p_multiSelect_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "AccountRelationshipsComponent_Template_p_table_onColReorder_8_listener", "AccountRelationshipsComponent_ng_template_9_Template", "AccountRelationshipsComponent_ng_template_10_Template", "AccountRelationshipsComponent_ng_template_11_Template", "AccountRelationshipsComponent_ng_template_12_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-relationships\\account-relationships.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-relationships\\account-relationships.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n@Component({\r\n  selector: 'app-account-relationships',\r\n  templateUrl: './account-relationships.component.html',\r\n  styleUrl: './account-relationships.component.scss',\r\n})\r\nexport class AccountRelationshipsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public relationshipdetails: any[] = [];\r\n  public bp_id: string = '';\r\n\r\n  constructor(private accountservice: AccountService) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'BusinessPartner', header: 'Business Partner' },\r\n    { field: 'Shareholder', header: 'Sales Organization' },\r\n    { field: 'Address', header: 'Created On' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.relationshipdetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.relationshipdetails = response?.contact_activity;\r\n        }\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Relationships</h4>\r\n\r\n        <div class=\"flex gap-3 ml-auto align-items-center\">\r\n            <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"relationshipdetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('RelationshipType')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Relationship Type\r\n                            <i *ngIf=\"sortField === 'RelationshipType'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'RelationshipType'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">\r\n                        <div class=\"flex align-items-center \">\r\n                            Actions\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-relation let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg font-medium\">\r\n                        {{ relation?.RelationshipType || '-' }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'BusinessPartner'\">\r\n                                    {{ relation?.BusinessPartner || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'Shareholder'\">\r\n                                    {{ relation?.Shareholder || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'Address'\">\r\n                                    {{ relation?.Address || '-' }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\">\r\n                            <i class=\"material-symbols-rounded text-red-500\">{{\r\n                                relation.Action\r\n                                }}</i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg\">No relationships found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg\">Loading relationships data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICyBbC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAwE;;;;;IAOpED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,wFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,uEAAA,gBACkF,IAAAC,uEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAhB7ChB,EADJ,CAAAM,cAAA,SAAI,aACwF;IAAtEN,EAAA,CAAAO,UAAA,mBAAAmB,yEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,kBAAkB,CAAC;IAAA,EAAC;IACtDf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,0BACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,wDAAA,gBACkF,IAAAC,wDAAA,gBAEd;IAE5E7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,mEAAA,2BAAkD;IAY9C9B,EADJ,CAAAM,cAAA,aAAkC,cACQ;IAClCN,EAAA,CAAAiB,MAAA,gBACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAM,EACL,EACJ;;;;IAtBWrB,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,wBAAsC;IAGtCzB,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,wBAAsC;IAGpBzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAC,eAAA,cACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAA4C;IACxCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAE,WAAA,cACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAAwC;IACpCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAG,OAAA,cACJ;;;;;IAbZnC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAkB,UAAA,IAAAkB,mFAAA,2BAAgD,IAAAC,mFAAA,2BAIJ,IAAAC,mFAAA,2BAIJ;;IAKhDtC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAdarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAqC,MAAA,CAAAvB,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAI/BF,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAE,UAAA,+BAA2B;IAI3BF,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,2BAAuB;;;;;IAflDF,EADJ,CAAAM,cAAA,aAA2B,aACoC;IACvDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAsB,oEAAA,2BAAkD;IAsB1CxC,EAHR,CAAAM,cAAA,aAAkC,iBAEoE,YAC7C;IAAAN,EAAA,CAAAiB,MAAA,GAE3C;IAGlBjB,EAHkB,CAAAqB,YAAA,EAAI,EACL,EACR,EACJ;;;;;IA9BGrB,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAS,gBAAA,cACJ;IAE8BzC,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;IAsBS/B,EAAA,CAAAsB,SAAA,GAE3C;IAF2CtB,EAAA,CAAA0C,iBAAA,CAAAV,WAAA,CAAAW,MAAA,CAE3C;;;;;IASd3C,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,8BAAuB;IACzEjB,EADyE,CAAAqB,YAAA,EAAK,EACzE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,+CAAwC;IAC1FjB,EAD0F,CAAAqB,YAAA,EAAK,EAC1F;;;ADnFrB,OAAM,MAAOuB,6BAA6B;EAKxCC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAJ1B,KAAAC,YAAY,GAAG,IAAIjD,OAAO,EAAQ;IACnC,KAAAkD,mBAAmB,GAAU,EAAE;IAC/B,KAAAC,KAAK,GAAW,EAAE;IAIjB,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEnC,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAkB,CAAE,EACxD;MAAER,KAAK,EAAE,aAAa;MAAEQ,MAAM,EAAE;IAAoB,CAAE,EACtD;MAAER,KAAK,EAAE,SAAS;MAAEQ,MAAM,EAAE;IAAY,CAAE,CAC3C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAXiC;EAatDW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC4C,mBAAmB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAErC,KAAK,CAAC;MAC9C,MAAMyC,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEtC,KAAK,CAAC;MAE9C,IAAI0C,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACrD,SAAS,GAAGsD,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE5C,KAAa;IACvC,IAAI,CAAC4C,IAAI,IAAI,CAAC5C,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC6C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC5C,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC8C,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACpB,cAAc,CAACqB,OAAO,CACxBC,IAAI,CAACrE,SAAS,CAAC,IAAI,CAACgD,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACrB,KAAK,GAAGqB,QAAQ,EAAErB,KAAK;QAC5B,IAAI,CAACD,mBAAmB,GAAGsB,QAAQ,EAAEC,gBAAgB;MACvD;IACF,CAAC,CAAC;IAEJ,IAAI,CAACrB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAIpB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACmB,gBAAgB;EAC9B;EAEA,IAAInB,eAAeA,CAACyC,GAAU;IAC5B,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACsB,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAC5B,gBAAgB,CAAC2B,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAAC7B,gBAAgB,CAAC8B,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC7B,gBAAgB,CAAC8B,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACnC,YAAY,CAACoC,IAAI,EAAE;IACxB,IAAI,CAACpC,YAAY,CAACqC,QAAQ,EAAE;EAC9B;;;uBAnFWxC,6BAA6B,EAAA5C,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA7B3C,6BAA6B;MAAA4C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlC9F,EAFR,CAAAM,cAAA,aAA2D,aACuC,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,oBAAa;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAEjErB,EAAA,CAAAM,cAAA,aAAmD;UAC/CN,EAAA,CAAAC,SAAA,kBACuD;UAEvDD,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAAgG,gBAAA,2BAAAC,8EAAAC,MAAA;YAAAlG,EAAA,CAAAmG,kBAAA,CAAAJ,GAAA,CAAAhE,eAAA,EAAAmE,MAAA,MAAAH,GAAA,CAAAhE,eAAA,GAAAmE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrElG,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAIFrB,EAFJ,CAAAM,cAAA,aAAuB,iBAI0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAA6F,uEAAAF,MAAA;YAAA,OAAgBH,GAAA,CAAAnB,eAAA,CAAAsB,MAAA,CAAuB;UAAA,EAAC;UA0ExClG,EAxEA,CAAAkB,UAAA,IAAAmF,oDAAA,0BAAgC,KAAAC,qDAAA,yBA8BiC,KAAAC,qDAAA,0BAqC3B,KAAAC,qDAAA,0BAKD;UAOjDxG,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UA9FMrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAErCF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAA6F,GAAA,CAAA5C,IAAA,CAAgB;UAACnD,EAAA,CAAAyG,gBAAA,YAAAV,GAAA,CAAAhE,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAOzIF,EAAA,CAAAsB,SAAA,GAA6B;UACqCtB,EADlE,CAAAE,UAAA,UAAA6F,GAAA,CAAA/C,mBAAA,CAA6B,YAAyB,mBAAmB,cAAc,oBAC/C,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}