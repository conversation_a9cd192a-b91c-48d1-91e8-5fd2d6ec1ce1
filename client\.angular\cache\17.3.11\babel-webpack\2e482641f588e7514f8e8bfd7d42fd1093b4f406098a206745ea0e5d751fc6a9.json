{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./users.service\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProfileComponent_div_12_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid email address.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_10_div_1_Template, 2, 0, \"div\", 23)(2, ProfileComponent_div_12_div_10_div_2_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_19_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"firstName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_28_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"lastName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_37_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"phone\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 11);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_12_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveChanges());\n    });\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" User Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"div\", 14);\n    i0.ɵɵelement(9, \"input\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ProfileComponent_div_12_div_10_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"label\", 13)(13, \"span\", 8);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"div\", 14);\n    i0.ɵɵelement(18, \"input\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileComponent_div_12_div_19_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\", 13)(22, \"span\", 8);\n    i0.ɵɵtext(23, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\")(26, \"div\", 14);\n    i0.ɵɵelement(27, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, ProfileComponent_div_12_div_28_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 13)(31, \"span\", 8);\n    i0.ɵɵtext(32, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"div\", 14);\n    i0.ɵɵelement(36, \"input\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, ProfileComponent_div_12_div_37_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 20)(39, \"button\", 21);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"email\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"firstName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"lastName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"phone\"].errors);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.saving ? \"Saving\" : \"Save Changes\");\n  }\n}\nexport class ProfileComponent {\n  constructor(fb, manageUserService, authService) {\n    this.fb = fb;\n    this.manageUserService = manageUserService;\n    this.authService = authService;\n    this.toggle = {\n      profile: true,\n      password: false\n    };\n    this.isFormSubmitted = false;\n    this.isPasswordFormSubmitted = false;\n    this.loadingUserDetails = false;\n    this.userDetails = {};\n    this.saving = false;\n    this.changingPassword = false;\n  }\n  ngOnInit() {\n    this.profileForm = this.fb.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n    this.fetchUserDetails();\n  }\n  get f() {\n    return this.profileForm.controls;\n  }\n  saveChanges() {\n    this.isFormSubmitted = true;\n    if (!this.profileForm.valid) {\n      return;\n    }\n    const value = this.profileForm.value;\n    this.saving = true;\n    const isAdmin = this.authService.userDetail.isAdmin || false;\n    if (isAdmin) {\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\n        firstname: value.firstName,\n        lastname: value.lastName,\n        address: value.address,\n        email: value.email,\n        phone: value.phone\n      }).subscribe(user => {\n        this.saving = false;\n        this.userDetails = user;\n        this.authService.checkAdminUser().subscribe();\n      }, err => {\n        this.saving = false;\n      });\n    } else {\n      this.manageUserService.updateUser(this.userDetails.id, {\n        firstname: value.firstName,\n        lastname: value.lastName,\n        address: value.address,\n        email: value.email\n      }).subscribe(user => {\n        this.saving = false;\n        this.userDetails = user;\n        this.authService.checkAdminUser().subscribe();\n      }, err => {\n        this.saving = false;\n      });\n    }\n  }\n  fetchUserDetails() {\n    this.loadingUserDetails = true;\n    const user = this.authService.userDetail;\n    this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\n      if (user) {\n        this.userDetails = user;\n        this.profileForm.patchValue({\n          firstName: user.firstname,\n          lastName: user.lastname,\n          address: user.address,\n          email: user.email,\n          phone: user.phone\n        });\n      }\n      this.loadingUserDetails = false;\n    });\n  }\n  toggleState(key) {\n    this.toggle[key] = !this.toggle[key];\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UsersService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 13,\n      vars: 3,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"card-list\", \"flex\", \"flex-column\", \"gap-2\", \"mb-3\"], [1, \"card\", \"shadow-2\", \"p-4\", \"h-full\", \"flex\", \"flex-column\", \"border-left-5\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"block\", \"font-bold\", \"text-xl\", \"m-0\", \"text-primary\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\", 4, \"ngIf\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [\"autocomplete\", \"off\", 1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"ngSubmit\", \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-15rem\", \"gap-1\"], [1, \"form-input\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"class\", \"text-red-600 mt-2\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"firstName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"lastName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"phone\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [1, \"form-submit-sec\", \"mt-5\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"], [1, \"text-red-600\", \"mt-2\"], [4, \"ngIf\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"My Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_6_listener() {\n            return ctx.toggleState(\"profile\");\n          });\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"User Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, ProfileComponent_div_12_Template, 41, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"active\", ctx.toggle[\"profile\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"profile\"]);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ProfileComponent_div_12_div_10_div_1_Template", "ProfileComponent_div_12_div_10_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "f", "errors", "ProfileComponent_div_12_div_19_div_1_Template", "ProfileComponent_div_12_div_28_div_1_Template", "ProfileComponent_div_12_div_37_div_1_Template", "ɵɵlistener", "ProfileComponent_div_12_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "saveChanges", "ɵɵelement", "ProfileComponent_div_12_div_10_Template", "ProfileComponent_div_12_div_19_Template", "ProfileComponent_div_12_div_28_Template", "ProfileComponent_div_12_div_37_Template", "profileForm", "isFormSubmitted", "ɵɵtextInterpolate", "saving", "ProfileComponent", "constructor", "fb", "manageUserService", "authService", "toggle", "profile", "password", "isPasswordFormSubmitted", "loadingUserDetails", "userDetails", "changingPassword", "ngOnInit", "group", "firstName", "required", "lastName", "email", "phone", "fetchUserDetails", "controls", "valid", "value", "isAdmin", "userDetail", "updateAdminUser", "id", "firstname", "lastname", "address", "subscribe", "user", "checkAdminUser", "err", "updateUser", "getUserForallRoles", "documentId", "patchValue", "toggleState", "key", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UsersService", "i3", "AuthService", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_Template_div_click_6_listener", "ProfileComponent_div_12_Template", "ɵɵclassProp"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\profile\\profile.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, ValidationErrors, ValidatorFn, AbstractControl, FormBuilder, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { UsersService } from './users.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrl: './profile.component.scss'\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  toggle: { [key: string]: boolean } = {\r\n    profile: true,\r\n    password: false\r\n  };\r\n  profileForm!: FormGroup;\r\n  isFormSubmitted = false;\r\n  isPasswordFormSubmitted = false;\r\n  loadingUserDetails = false;\r\n  userDetails: any = {};\r\n  saving = false;\r\n  changingPassword = false;\r\n\r\n  constructor(private fb: FormBuilder, private manageUserService: UsersService, private authService: AuthService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.profileForm = this.fb.group({\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phone: [''],\r\n    });\r\n    this.fetchUserDetails();\r\n  }\r\n\r\n  get f(): { [key: string]: AbstractControl } {\r\n    return this.profileForm.controls;\r\n  }\r\n\r\n  saveChanges(): void {\r\n    this.isFormSubmitted = true;\r\n    if (!this.profileForm.valid) {\r\n      return;\r\n    }\r\n    const value = this.profileForm.value;\r\n    this.saving = true;\r\n    const isAdmin = this.authService.userDetail.isAdmin || false;\r\n    if (isAdmin) {\r\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\r\n        firstname: value.firstName,\r\n        lastname: value.lastName,\r\n        address: value.address,\r\n        email: value.email,\r\n        phone: value.phone,\r\n      }).subscribe((user) => {\r\n        this.saving = false;\r\n        this.userDetails = user;\r\n        this.authService.checkAdminUser().subscribe();\r\n      }, (err) => {\r\n        this.saving = false;\r\n      });\r\n    } else {\r\n      this.manageUserService.updateUser(this.userDetails.id, {\r\n        firstname: value.firstName,\r\n        lastname: value.lastName,\r\n        address: value.address,\r\n        email: value.email,\r\n      }).subscribe((user) => {\r\n        this.saving = false;\r\n        this.userDetails = user;\r\n        this.authService.checkAdminUser().subscribe();\r\n      }, (err) => {\r\n        this.saving = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  fetchUserDetails() {\r\n    this.loadingUserDetails = true;\r\n    const user = this.authService.userDetail;\r\n    this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\r\n      if (user) {\r\n        this.userDetails = user;\r\n        this.profileForm.patchValue({\r\n          firstName: user.firstname,\r\n          lastName: user.lastname,\r\n          address: user.address,\r\n          email: user.email,\r\n          phone: user.phone,\r\n        });\r\n      }\r\n      this.loadingUserDetails = false;\r\n    });\r\n  }\r\n\r\n  toggleState(key: string) {\r\n    this.toggle[key] = !this.toggle[key];\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <h3 class=\"m-0\">My Account</h3>\r\n    </div>\r\n\r\n    <div class=\"card-list flex flex-column gap-2 mb-3\">\r\n        <div class=\"card shadow-2 p-4 h-full flex flex-column border-left-5 gap-2\">\r\n            <div class=\"flex align-items-center justify-content-between cursor-pointer\"\r\n                (click)=\"toggleState('profile')\">\r\n                <h3 class=\"block font-bold text-xl m-0 text-primary\">User Details</h3>\r\n                <button\r\n                    class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                    [class.active]=\"toggle['profile']\">\r\n                    <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n                </button>\r\n            </div>\r\n            <div *ngIf=\"toggle['profile']\"\r\n                class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <form class=\"relative flex flex-column gap-1\" [formGroup]=\"profileForm\" (ngSubmit)=\"saveChanges()\"\r\n                    autocomplete=\"off\">\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> User Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"email\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"email\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['email'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['email'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"f['email'].errors['email']\">\r\n                                    Please enter a valid email address.</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> First Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"firstName\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['firstName'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['firstName'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> Last Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"lastName\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['lastName'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['lastName'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">phone_in_talk</span> Phone Number</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"phone\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['phone'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['phone'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"form-submit-sec mt-5\">\r\n                        <button type=\"submit\"\r\n                            class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">{{saving\r\n                            ? 'Saving' : 'Save Changes'}}</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;;;;;;;;IC4BnFC,EAAA,CAAAC,cAAA,UAA2C;IACvCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAwC;IACpCD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJjDH,EAAA,CAAAC,cAAA,cAA4E;IAGxED,EAFA,CAAAI,UAAA,IAAAC,6CAAA,kBAA2C,IAAAC,6CAAA,kBAEH;IAE5CN,EAAA,CAAAG,YAAA,EAAM;;;;IAJIH,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,aAAmC;IAEnCX,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,UAAgC;;;;;IActCX,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAAgF;IAC5ED,EAAA,CAAAI,UAAA,IAAAQ,6CAAA,kBAA+C;IAEnDZ,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,cAAAC,MAAA,aAAuC;;;;;IAc7CX,EAAA,CAAAC,cAAA,UAA8C;IAC1CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAI,UAAA,IAAAS,6CAAA,kBAA8C;IAElDb,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;;;;;IAc5CX,EAAA,CAAAC,cAAA,UAA2C;IACvCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAU,6CAAA,kBAA2C;IAE/Cd,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,aAAmC;;;;;;IAvDzDX,EAFJ,CAAAC,cAAA,cACgG,eAErE;IADiDD,EAAA,CAAAe,UAAA,sBAAAC,0DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAT,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYX,MAAA,CAAAY,WAAA,EAAa;IAAA,EAAC;IAGTrB,EADrF,CAAAC,cAAA,cAAqD,gBACgC,cACxC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpEH,EADJ,CAAAC,cAAA,UAAK,cACuB;IACpBD,EAAA,CAAAsB,SAAA,gBAC4B;IAChCtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAmB,uCAAA,kBAA4E;IAOpFvB,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAErEH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAsB,SAAA,iBACgC;IACpCtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAoB,uCAAA,kBAAgF;IAKxFxB,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpEH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAsB,SAAA,iBAC+B;IACnCtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAqB,uCAAA,kBAA+E;IAKvFzB,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE9EH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAsB,SAAA,iBAC4B;IAChCtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAsB,uCAAA,kBAA4E;IAKpF1B,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAE6E;IAAAD,EAAA,CAAAE,MAAA,IAC1E;IAG7CF,EAH6C,CAAAG,YAAA,EAAS,EACxC,EACH,EACL;;;;IAlE4CH,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAQ,UAAA,cAAAC,MAAA,CAAAkB,WAAA,CAAyB;IAUrD3B,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAmB,eAAA,IAAAnB,MAAA,CAAAC,CAAA,UAAAC,MAAA,CAA0C;IAgB1CX,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAmB,eAAA,IAAAnB,MAAA,CAAAC,CAAA,cAAAC,MAAA,CAA8C;IAc9CX,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAmB,eAAA,IAAAnB,MAAA,CAAAC,CAAA,aAAAC,MAAA,CAA6C;IAc7CX,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAmB,eAAA,IAAAnB,MAAA,CAAAC,CAAA,UAAAC,MAAA,CAA0C;IAQuDX,EAAA,CAAAO,SAAA,GAC1E;IAD0EP,EAAA,CAAA6B,iBAAA,CAAApB,MAAA,CAAAqB,MAAA,6BAC1E;;;ADtEzD,OAAM,MAAOC,gBAAgB;EAa3BC,YAAoBC,EAAe,EAAUC,iBAA+B,EAAUC,WAAwB;IAA1F,KAAAF,EAAE,GAAFA,EAAE;IAAuB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAAwB,KAAAC,WAAW,GAAXA,WAAW;IAZjG,KAAAC,MAAM,GAA+B;MACnCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX;IAED,KAAAV,eAAe,GAAG,KAAK;IACvB,KAAAW,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAX,MAAM,GAAG,KAAK;IACd,KAAAY,gBAAgB,GAAG,KAAK;EAE0F;EAElHC,QAAQA,CAAA;IACN,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACM,EAAE,CAACW,KAAK,CAAC;MAC/BC,SAAS,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAAC+C,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC+C,QAAQ,CAAC;MACnCE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAAC+C,QAAQ,EAAE/C,UAAU,CAACiD,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;IACF,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA,IAAIxC,CAACA,CAAA;IACH,OAAO,IAAI,CAACiB,WAAW,CAACwB,QAAQ;EAClC;EAEA9B,WAAWA,CAAA;IACT,IAAI,CAACO,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC,IAAI,CAACD,WAAW,CAACyB,KAAK,EAAE;MAC3B;IACF;IACA,MAAMC,KAAK,GAAG,IAAI,CAAC1B,WAAW,CAAC0B,KAAK;IACpC,IAAI,CAACvB,MAAM,GAAG,IAAI;IAClB,MAAMwB,OAAO,GAAG,IAAI,CAACnB,WAAW,CAACoB,UAAU,CAACD,OAAO,IAAI,KAAK;IAC5D,IAAIA,OAAO,EAAE;MACX,IAAI,CAACpB,iBAAiB,CAACsB,eAAe,CAAC,IAAI,CAACf,WAAW,CAACgB,EAAE,EAAE;QAC1DC,SAAS,EAAEL,KAAK,CAACR,SAAS;QAC1Bc,QAAQ,EAAEN,KAAK,CAACN,QAAQ;QACxBa,OAAO,EAAEP,KAAK,CAACO,OAAO;QACtBZ,KAAK,EAAEK,KAAK,CAACL,KAAK;QAClBC,KAAK,EAAEI,KAAK,CAACJ;OACd,CAAC,CAACY,SAAS,CAAEC,IAAI,IAAI;QACpB,IAAI,CAAChC,MAAM,GAAG,KAAK;QACnB,IAAI,CAACW,WAAW,GAAGqB,IAAI;QACvB,IAAI,CAAC3B,WAAW,CAAC4B,cAAc,EAAE,CAACF,SAAS,EAAE;MAC/C,CAAC,EAAGG,GAAG,IAAI;QACT,IAAI,CAAClC,MAAM,GAAG,KAAK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACI,iBAAiB,CAAC+B,UAAU,CAAC,IAAI,CAACxB,WAAW,CAACgB,EAAE,EAAE;QACrDC,SAAS,EAAEL,KAAK,CAACR,SAAS;QAC1Bc,QAAQ,EAAEN,KAAK,CAACN,QAAQ;QACxBa,OAAO,EAAEP,KAAK,CAACO,OAAO;QACtBZ,KAAK,EAAEK,KAAK,CAACL;OACd,CAAC,CAACa,SAAS,CAAEC,IAAI,IAAI;QACpB,IAAI,CAAChC,MAAM,GAAG,KAAK;QACnB,IAAI,CAACW,WAAW,GAAGqB,IAAI;QACvB,IAAI,CAAC3B,WAAW,CAAC4B,cAAc,EAAE,CAACF,SAAS,EAAE;MAC/C,CAAC,EAAGG,GAAG,IAAI;QACT,IAAI,CAAClC,MAAM,GAAG,KAAK;MACrB,CAAC,CAAC;IACJ;EACF;EAEAoB,gBAAgBA,CAAA;IACd,IAAI,CAACV,kBAAkB,GAAG,IAAI;IAC9B,MAAMsB,IAAI,GAAG,IAAI,CAAC3B,WAAW,CAACoB,UAAU;IACxC,IAAI,CAACrB,iBAAiB,CAACgC,kBAAkB,CAACJ,IAAI,CAACK,UAAU,CAAC,CAACN,SAAS,CAACC,IAAI,IAAG;MAC1E,IAAIA,IAAI,EAAE;QACR,IAAI,CAACrB,WAAW,GAAGqB,IAAI;QACvB,IAAI,CAACnC,WAAW,CAACyC,UAAU,CAAC;UAC1BvB,SAAS,EAAEiB,IAAI,CAACJ,SAAS;UACzBX,QAAQ,EAAEe,IAAI,CAACH,QAAQ;UACvBC,OAAO,EAAEE,IAAI,CAACF,OAAO;UACrBZ,KAAK,EAAEc,IAAI,CAACd,KAAK;UACjBC,KAAK,EAAEa,IAAI,CAACb;SACb,CAAC;MACJ;MACA,IAAI,CAACT,kBAAkB,GAAG,KAAK;IACjC,CAAC,CAAC;EACJ;EAEA6B,WAAWA,CAACC,GAAW;IACrB,IAAI,CAAClC,MAAM,CAACkC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAClC,MAAM,CAACkC,GAAG,CAAC;EACtC;;;uBAvFWvC,gBAAgB,EAAA/B,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzE,EAAA,CAAAuE,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA3E,EAAA,CAAAuE,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB9C,gBAAgB;MAAA+C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTrBpF,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;UAIEH,EAFR,CAAAC,cAAA,aAAmD,aAC4B,aAElC;UAAjCD,EAAA,CAAAe,UAAA,mBAAAuE,+CAAA;YAAA,OAASD,GAAA,CAAAhB,WAAA,CAAY,SAAS,CAAC;UAAA,EAAC;UAChCrE,EAAA,CAAAC,cAAA,YAAqD;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIlEH,EAHJ,CAAAC,cAAA,gBAEuC,eACI;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAElEF,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;UACNH,EAAA,CAAAI,UAAA,KAAAmF,gCAAA,kBACgG;UAsE5GvF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;UA3EcH,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAwF,WAAA,WAAAH,GAAA,CAAAjD,MAAA,YAAkC;UAIpCpC,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAQ,UAAA,SAAA6E,GAAA,CAAAjD,MAAA,YAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}