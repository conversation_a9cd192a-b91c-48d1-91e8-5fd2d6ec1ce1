{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { ServiceTicketsOverviewComponent } from './service-tickets-overview/service-tickets-overview.component';\nimport { ServiceTicketsContactsComponent } from './service-tickets-contacts/service-tickets-contacts.component';\nimport { ServiceTicketsAiInsightsComponent } from './service-tickets-ai-insights/service-tickets-ai-insights.component';\nimport { ServiceTicketsAttachmentsComponent } from './service-tickets-attachments/service-tickets-attachments.component';\nimport { ServiceTicketsNotesComponent } from './service-tickets-notes/service-tickets-notes.component';\nimport { ServiceTicketsActivitiesComponent } from './service-tickets-activities/service-tickets-activities.component';\nimport { ServiceTicketsRelationshipsComponent } from './service-tickets-relationships/service-tickets-relationships.component';\nimport { ServiceTicketsTicketsComponent } from './service-tickets-tickets/service-tickets-tickets.component';\nimport { ServiceTicketsSalesQuotesComponent } from './service-tickets-sales-quotes/service-tickets-sales-quotes.component';\nimport { ServiceTicketsSalesOrdersComponent } from './service-tickets-sales-orders/service-tickets-sales-orders.component';\nimport { ServiceTicketsReturnsComponent } from './service-tickets-returns/service-tickets-returns.component';\nimport { ServiceTicketsInvoicesComponent } from './service-tickets-invoices/service-tickets-invoices.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ServiceTicketsComponent,\n  children: [{\n    path: 'overview',\n    component: ServiceTicketsOverviewComponent\n  }, {\n    path: 'contacts',\n    component: ServiceTicketsContactsComponent\n  }, {\n    path: 'ai-insights',\n    component: ServiceTicketsAiInsightsComponent\n  }, {\n    path: 'attachments',\n    component: ServiceTicketsAttachmentsComponent\n  }, {\n    path: 'notes',\n    component: ServiceTicketsNotesComponent\n  }, {\n    path: 'activities',\n    component: ServiceTicketsActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: ServiceTicketsRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: ServiceTicketsTicketsComponent\n  }, {\n    path: 'sales-quotes',\n    component: ServiceTicketsSalesQuotesComponent\n  }, {\n    path: 'sales-orders',\n    component: ServiceTicketsSalesOrdersComponent\n  }, {\n    path: 'returns',\n    component: ServiceTicketsReturnsComponent\n  }, {\n    path: 'invoices',\n    component: ServiceTicketsInvoicesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class ServiceTicketsRoutingModule {\n  static {\n    this.ɵfac = function ServiceTicketsRoutingModule_Factory(t) {\n      return new (t || ServiceTicketsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ServiceTicketsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ServiceTicketsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ServiceTicketsComponent", "ServiceTicketsOverviewComponent", "ServiceTicketsContactsComponent", "ServiceTicketsAiInsightsComponent", "ServiceTicketsAttachmentsComponent", "ServiceTicketsNotesComponent", "ServiceTicketsActivitiesComponent", "ServiceTicketsRelationshipsComponent", "ServiceTicketsTicketsComponent", "ServiceTicketsSalesQuotesComponent", "ServiceTicketsSalesOrdersComponent", "ServiceTicketsReturnsComponent", "ServiceTicketsInvoicesComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ServiceTicketsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\service-tickets\\service-tickets-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ServiceTicketsComponent } from './service-tickets.component';\r\nimport { ServiceTicketsOverviewComponent } from './service-tickets-overview/service-tickets-overview.component';\r\nimport { ServiceTicketsContactsComponent } from './service-tickets-contacts/service-tickets-contacts.component';\r\nimport { ServiceTicketsAiInsightsComponent } from './service-tickets-ai-insights/service-tickets-ai-insights.component';\r\nimport { ServiceTicketsAttachmentsComponent } from './service-tickets-attachments/service-tickets-attachments.component';\r\nimport { ServiceTicketsNotesComponent } from './service-tickets-notes/service-tickets-notes.component';\r\nimport { ServiceTicketsActivitiesComponent } from './service-tickets-activities/service-tickets-activities.component';\r\nimport { ServiceTicketsRelationshipsComponent } from './service-tickets-relationships/service-tickets-relationships.component';\r\nimport { ServiceTicketsTicketsComponent } from './service-tickets-tickets/service-tickets-tickets.component';\r\nimport { ServiceTicketsSalesQuotesComponent } from './service-tickets-sales-quotes/service-tickets-sales-quotes.component';\r\nimport { ServiceTicketsSalesOrdersComponent } from './service-tickets-sales-orders/service-tickets-sales-orders.component';\r\nimport { ServiceTicketsReturnsComponent } from './service-tickets-returns/service-tickets-returns.component';\r\nimport { ServiceTicketsInvoicesComponent } from './service-tickets-invoices/service-tickets-invoices.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ServiceTicketsComponent,\r\n    children: [\r\n      { path: 'overview', component: ServiceTicketsOverviewComponent },\r\n      { path: 'contacts', component: ServiceTicketsContactsComponent },\r\n      { path: 'ai-insights', component: ServiceTicketsAiInsightsComponent },\r\n      { path: 'attachments', component: ServiceTicketsAttachmentsComponent },\r\n      { path: 'notes', component: ServiceTicketsNotesComponent },\r\n      { path: 'activities', component: ServiceTicketsActivitiesComponent },\r\n      { path: 'relationships', component: ServiceTicketsRelationshipsComponent },\r\n      { path: 'tickets', component: ServiceTicketsTicketsComponent },\r\n      { path: 'sales-quotes', component: ServiceTicketsSalesQuotesComponent },\r\n      { path: 'sales-orders', component: ServiceTicketsSalesOrdersComponent },\r\n      { path: 'returns', component: ServiceTicketsReturnsComponent },\r\n      { path: 'invoices', component: ServiceTicketsInvoicesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ServiceTicketsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,kCAAkC,QAAQ,qEAAqE;AACxH,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,iCAAiC,QAAQ,mEAAmE;AACrH,SAASC,oCAAoC,QAAQ,yEAAyE;AAC9H,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,+BAA+B,QAAQ,+DAA+D;;;AAE/G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEf,uBAAuB;EAClCgB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEd;EAA+B,CAAE,EAChE;IAAEa,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEb;EAA+B,CAAE,EAChE;IAAEY,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEZ;EAAiC,CAAE,EACrE;IAAEW,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEX;EAAkC,CAAE,EACtE;IAAEU,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEV;EAA4B,CAAE,EAC1D;IAAES,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAET;EAAiC,CAAE,EACpE;IAAEQ,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAER;EAAoC,CAAE,EAC1E;IAAEO,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEP;EAA8B,CAAE,EAC9D;IAAEM,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEN;EAAkC,CAAE,EACvE;IAAEK,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEL;EAAkC,CAAE,EACvE;IAAEI,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEJ;EAA8B,CAAE,EAC9D;IAAEG,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEH;EAA+B,CAAE,EAChE;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BpB,YAAY,CAACqB,QAAQ,CAACP,MAAM,CAAC,EAC7Bd,YAAY;IAAA;EAAA;;;2EAEXoB,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAF5BxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}