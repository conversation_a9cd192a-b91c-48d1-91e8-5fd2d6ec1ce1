{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"../../opportunities.service\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/tooltip\";\nimport * as i7 from \"./activities-sales-call-form/activities-sales-call-form.component\";\nimport * as i8 from \"@angular/common\";\nfunction OpportunitiesFollowUpComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 11)(2, \"div\", 12);\n    i0.ɵɵtext(3, \"Name\");\n    i0.ɵɵelement(4, \"p-sortIcon\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 14)(6, \"div\", 12);\n    i0.ɵɵtext(7, \"Type\");\n    i0.ɵɵelement(8, \"p-sortIcon\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 16)(10, \"div\", 12);\n    i0.ɵɵtext(11, \"Responsible\");\n    i0.ɵɵelement(12, \"p-sortIcon\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 18)(14, \"div\", 12);\n    i0.ɵɵtext(15, \"Created On\");\n    i0.ɵɵelement(16, \"p-sortIcon\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 20);\n    i0.ɵɵtext(18, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesFollowUpComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 21);\n    i0.ɵɵlistener(\"click\", function OpportunitiesFollowUpComponent_ng_template_8_Template_tr_click_0_listener() {\n      const followup_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToFollowupDetail(followup_r2));\n    });\n    i0.ɵɵelementStart(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 23)(11, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function OpportunitiesFollowUpComponent_ng_template_8_Template_button_click_11_listener($event) {\n      const followup_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(followup_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followup_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (followup_r2 == null ? null : followup_r2.activity == null ? null : followup_r2.activity.subject) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getLabelFromDropdown(\"activityDocumentType\", followup_r2 == null ? null : followup_r2.activity == null ? null : followup_r2.activity.document_type) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (followup_r2 == null ? null : followup_r2.partner_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 4, followup_r2 == null ? null : followup_r2.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction OpportunitiesFollowUpComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"No follow up found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesFollowUpComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 25);\n    i0.ɵɵtext(2, \"Loading follow up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OpportunitiesFollowUpComponent {\n  constructor(router, route, messageservice, confirmationservice, opportunitiesservice) {\n    this.router = router;\n    this.route = route;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.opportunity_id = '';\n    this.followupdetails = null;\n    this.submitted = false;\n    this.showActivitiesDialog = false;\n    this.dropdowns = {\n      activityDocumentType: []\n    };\n  }\n  ngOnInit() {\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.opportunity_id = response?.opportunity_id;\n        const allItems = response?.opportunity_followups || [];\n        // Filter by type_code === '0002' and then map\n        this.followupdetails = allItems.filter(item => item?.type_code === '0002').map(item => {\n          const partnerFn = item?.activity?.business_partner?.customer?.partner_functions?.find(p => p?.partner_function === 'YI');\n          const partnerName = partnerFn?.bp_full_name || null;\n          return {\n            ...item,\n            partner_name: partnerName\n          };\n        });\n      }\n    });\n  }\n  loadActivityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteFollowupItem(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showActivityDialog(position) {\n    this.showActivitiesDialog = true;\n    this.submitted = false;\n  }\n  navigateToFollowupDetail(item) {\n    this.router.navigate([item?.activity?.activity_id], {\n      relativeTo: this.route,\n      state: {\n        followupdata: item\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesFollowUpComponent_Factory(t) {\n      return new (t || OpportunitiesFollowUpComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i3.OpportunitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesFollowUpComponent,\n      selectors: [[\"app-opportunities-follow-up\"]],\n      decls: 12,\n      vars: 6,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"filter-sec\", \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [3, \"onClose\", \"visible\"], [\"pSortableColumn\", \"activity.subject\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"activity.subject\"], [\"pSortableColumn\", \"activity.document_type\"], [\"field\", \"activity.document_type\"], [\"pSortableColumn\", \"partner_name\"], [\"field\", \"partner_name\"], [\"pSortableColumn\", \"createdAt\"], [\"field\", \"createdAt\"], [1, \"border-round-right-lg\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"border-round-right-lg\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"5\", 1, \"border-round-left-lg\", \"border-round-right-lg\"]],\n      template: function OpportunitiesFollowUpComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Follow Up Activities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function OpportunitiesFollowUpComponent_Template_p_button_click_4_listener() {\n            return ctx.showActivityDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, OpportunitiesFollowUpComponent_ng_template_7_Template, 19, 0, \"ng-template\", 6)(8, OpportunitiesFollowUpComponent_ng_template_8_Template, 12, 7, \"ng-template\", 7)(9, OpportunitiesFollowUpComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, OpportunitiesFollowUpComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"app-opportunities-sales-call-form\", 10);\n          i0.ɵɵlistener(\"onClose\", function OpportunitiesFollowUpComponent_Template_app_opportunities_sales_call_form_onClose_11_listener() {\n            return ctx.showActivitiesDialog = false;\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.followupdetails)(\"rows\", 10)(\"paginator\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"visible\", ctx.showActivitiesDialog);\n        }\n      },\n      dependencies: [i4.Table, i2.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i5.ButtonDirective, i5.Button, i6.Tooltip, i7.ActivitiesSalesCallFormComponent, i8.DatePipe],\n      styles: [\".followup-popup .p-dialog.p-component.p-dialog-resizable {\\n  width: calc(100vw - 490px) !important;\\n}\\n  .followup-popup .field {\\n  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1mb2xsb3ctdXAvb3Bwb3J0dW5pdGllcy1mb2xsb3ctdXAuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRVE7RUFDSSxxQ0FBQTtBQURaO0FBSVE7RUFDSSw0REFBQTtBQUZaIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIHtcclxuICAgIC5mb2xsb3d1cC1wb3B1cCB7XHJcbiAgICAgICAgLnAtZGlhbG9nLnAtY29tcG9uZW50LnAtZGlhbG9nLXJlc2l6YWJsZSB7XHJcbiAgICAgICAgICAgIHdpZHRoOiBjYWxjKDEwMHZ3IC0gNDkwcHgpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZmllbGQge1xyXG4gICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzNjBweCwgMWZyKSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "OpportunitiesFollowUpComponent_ng_template_8_Template_tr_click_0_listener", "followup_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToFollowupDetail", "OpportunitiesFollowUpComponent_ng_template_8_Template_button_click_11_listener", "$event", "stopPropagation", "confirmRemove", "ɵɵadvance", "ɵɵtextInterpolate1", "activity", "subject", "getLabelFromDropdown", "document_type", "partner_name", "ɵɵpipeBind2", "createdAt", "OpportunitiesFollowUpComponent", "constructor", "router", "route", "messageservice", "confirmationservice", "opportunitiesservice", "unsubscribe$", "opportunity_id", "followupdetails", "submitted", "showActivitiesDialog", "dropdowns", "activityDocumentType", "ngOnInit", "loadActivityDropDown", "opportunity", "pipe", "subscribe", "response", "allItems", "opportunity_followups", "filter", "item", "type_code", "map", "partnerFn", "business_partner", "customer", "partner_functions", "find", "p", "partner_function", "partner<PERSON>ame", "bp_full_name", "target", "type", "getOpportunityDropdownOptions", "res", "data", "attr", "label", "description", "value", "code", "dropdownKey", "opt", "confirm", "message", "header", "icon", "accept", "remove", "deleteFollowupItem", "documentId", "next", "add", "severity", "detail", "getOpportunityByID", "error", "showActivityDialog", "position", "navigate", "activity_id", "relativeTo", "state", "followupdata", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "MessageService", "ConfirmationService", "i3", "OpportunitiesService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesFollowUpComponent_Template", "rf", "ctx", "OpportunitiesFollowUpComponent_Template_p_button_click_4_listener", "ɵɵtemplate", "OpportunitiesFollowUpComponent_ng_template_7_Template", "OpportunitiesFollowUpComponent_ng_template_8_Template", "OpportunitiesFollowUpComponent_ng_template_9_Template", "OpportunitiesFollowUpComponent_ng_template_10_Template", "OpportunitiesFollowUpComponent_Template_app_opportunities_sales_call_form_onClose_11_listener", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\opportunities-follow-up.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-follow-up\\opportunities-follow-up.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-follow-up',\r\n  templateUrl: './opportunities-follow-up.component.html',\r\n  styleUrl: './opportunities-follow-up.component.scss',\r\n})\r\nexport class OpportunitiesFollowUpComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public opportunity_id: string = '';\r\n  public followupdetails: any = null;\r\n  public submitted = false;\r\n  public showActivitiesDialog = false;\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService,\r\n    private opportunitiesservice: OpportunitiesService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.opportunitiesservice.opportunity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.opportunity_id = response?.opportunity_id;\r\n\r\n          const allItems = response?.opportunity_followups || [];\r\n\r\n          // Filter by type_code === '0002' and then map\r\n          this.followupdetails = allItems\r\n            .filter((item: any) => item?.type_code === '0002')\r\n            .map((item: any) => {\r\n              const partnerFn =\r\n                item?.activity?.business_partner?.customer?.partner_functions?.find(\r\n                  (p: any) => p?.partner_function === 'YI'\r\n                );\r\n              const partnerName = partnerFn?.bp_full_name || null;\r\n\r\n              return {\r\n                ...item,\r\n                partner_name: partnerName,\r\n              };\r\n            });\r\n        }\r\n      });\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteFollowupItem(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  showActivityDialog(position: string) {\r\n    this.showActivitiesDialog = true;\r\n    this.submitted = false;\r\n  }\r\n\r\n  navigateToFollowupDetail(item: any) {\r\n    this.router.navigate([item?.activity?.activity_id], {\r\n      relativeTo: this.route,\r\n      state: { followupdata: item },\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"filter-sec card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Follow Up Activities</h4>\r\n        <p-button label=\"Add\" (click)=\"showActivityDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n            class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"followupdetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"activity.subject\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">Name<p-sortIcon\r\n                                field=\"activity.subject\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"activity.document_type\">\r\n                        <div class=\"flex align-items-center gap-2\">Type<p-sortIcon\r\n                                field=\"activity.document_type\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"partner_name\">\r\n                        <div class=\"flex align-items-center gap-2\">Responsible<p-sortIcon\r\n                                field=\"partner_name\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\">\r\n                        <div class=\"flex align-items-center gap-2\">Created On<p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-followup>\r\n                <tr (click)=\"navigateToFollowupDetail(followup)\" class=\"cursor-pointer\">\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ followup?.activity?.subject || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{\r\n                        getLabelFromDropdown('activityDocumentType',followup?.activity?.document_type)\r\n                        || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ followup?.partner_name || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ followup?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();confirmRemove(followup);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">No follow up found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"5\" class=\"border-round-left-lg border-round-right-lg\">Loading follow up data. Please\r\n                        wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n<app-opportunities-sales-call-form [visible]=\"showActivitiesDialog\" (onClose)=\"showActivitiesDialog = false\">\r\n</app-opportunities-sales-call-form>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;ICajBC,EAFR,CAAAC,cAAA,SAAI,aACoE,cACrB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,SAAA,qBACD;IAClDH,EADkD,CAAAI,YAAA,EAAM,EACnD;IAEDJ,EADJ,CAAAC,cAAA,aAA6C,cACE;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,SAAA,qBACK;IACxDH,EADwD,CAAAI,YAAA,EAAM,EACzD;IAEDJ,EADJ,CAAAC,cAAA,aAAmC,eACY;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,SAAA,sBACZ;IAC9CH,EAD8C,CAAAI,YAAA,EAAM,EAC/C;IAEDJ,EADJ,CAAAC,cAAA,cAAgC,eACe;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAEpGH,EADI,CAAAI,YAAA,EAAM,EACL;IACLJ,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAC7CF,EAD6C,CAAAI,YAAA,EAAK,EAC7C;;;;;;IAILJ,EAAA,CAAAC,cAAA,aAAwE;IAApED,EAAA,CAAAK,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAP,WAAA,CAAkC;IAAA,EAAC;IAC5CP,EAAA,CAAAC,cAAA,aAAsF;IAClFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GAGJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAEDJ,EADJ,CAAAC,cAAA,cAA8C,kBAEsB;IAA5DD,EAAA,CAAAK,UAAA,mBAAAU,+EAAAC,MAAA;MAAA,MAAAT,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAASI,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAACF,MAAA,CAAAO,aAAA,CAAAX,WAAA,CAAuB;IAAA,EAAE;IAEvEP,EAFwE,CAAAI,YAAA,EAAS,EACxE,EACJ;;;;;IAjBGJ,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,OAAAb,WAAA,kBAAAA,WAAA,CAAAc,QAAA,kBAAAd,WAAA,CAAAc,QAAA,CAAAC,OAAA,cACJ;IAEItB,EAAA,CAAAmB,SAAA,GAGJ;IAHInB,EAAA,CAAAoB,kBAAA,MAAAT,MAAA,CAAAY,oBAAA,yBAAAhB,WAAA,kBAAAA,WAAA,CAAAc,QAAA,kBAAAd,WAAA,CAAAc,QAAA,CAAAG,aAAA,cAGJ;IAEIxB,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,OAAAb,WAAA,kBAAAA,WAAA,CAAAkB,YAAA,cACJ;IAEIzB,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAA0B,WAAA,OAAAnB,WAAA,kBAAAA,WAAA,CAAAoB,SAAA,8BACJ;;;;;IASA3B,EADJ,CAAAC,cAAA,SAAI,aACmE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAC1FF,EAD0F,CAAAI,YAAA,EAAK,EAC1F;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACmE;IAAAD,EAAA,CAAAE,MAAA,2CAC1D;IACbF,EADa,CAAAI,YAAA,EAAK,EACb;;;ADrDrB,OAAM,MAAOwB,8BAA8B;EAUzCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,cAA8B,EAC9BC,mBAAwC,EACxCC,oBAA0C;IAJ1C,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,oBAAoB,GAApBA,oBAAoB;IAdtB,KAAAC,YAAY,GAAG,IAAIrC,OAAO,EAAQ;IACnC,KAAAsC,cAAc,GAAW,EAAE;IAC3B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,SAAS,GAA0B;MACxCC,oBAAoB,EAAE;KACvB;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACT,oBAAoB,CAACU,WAAW,CAClCC,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACX,cAAc,GAAGW,QAAQ,EAAEX,cAAc;QAE9C,MAAMY,QAAQ,GAAGD,QAAQ,EAAEE,qBAAqB,IAAI,EAAE;QAEtD;QACA,IAAI,CAACZ,eAAe,GAAGW,QAAQ,CAC5BE,MAAM,CAAEC,IAAS,IAAKA,IAAI,EAAEC,SAAS,KAAK,MAAM,CAAC,CACjDC,GAAG,CAAEF,IAAS,IAAI;UACjB,MAAMG,SAAS,GACbH,IAAI,EAAE9B,QAAQ,EAAEkC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAChEC,CAAM,IAAKA,CAAC,EAAEC,gBAAgB,KAAK,IAAI,CACzC;UACH,MAAMC,WAAW,GAAGP,SAAS,EAAEQ,YAAY,IAAI,IAAI;UAEnD,OAAO;YACL,GAAGX,IAAI;YACP1B,YAAY,EAAEoC;WACf;QACH,CAAC,CAAC;MACN;IACF,CAAC,CAAC;EACN;EAEAlB,oBAAoBA,CAACoB,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAAC9B,oBAAoB,CACtB+B,6BAA6B,CAACD,IAAI,CAAC,CACnClB,SAAS,CAAEoB,GAAQ,IAAI;MACtB,IAAI,CAAC1B,SAAS,CAACuB,MAAM,CAAC,GACpBG,GAAG,EAAEC,IAAI,EAAEd,GAAG,CAAEe,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAjD,oBAAoBA,CAACkD,WAAmB,EAAEF,KAAa;IACrD,MAAMpB,IAAI,GAAG,IAAI,CAACX,SAAS,CAACiC,WAAW,CAAC,EAAEf,IAAI,CAC3CgB,GAAG,IAAKA,GAAG,CAACH,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOpB,IAAI,EAAEkB,KAAK,IAAIE,KAAK;EAC7B;EAEArD,aAAaA,CAACiC,IAAS;IACrB,IAAI,CAAClB,mBAAmB,CAAC0C,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC7B,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA6B,MAAMA,CAAC7B,IAAS;IACd,IAAI,CAACjB,oBAAoB,CACtB+C,kBAAkB,CAAC9B,IAAI,CAAC+B,UAAU,CAAC,CACnCrC,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTqC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnD,cAAc,CAACoD,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACpD,oBAAoB,CACtBqD,kBAAkB,CAAC,IAAI,CAACnD,cAAc,CAAC,CACvCS,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAClCW,SAAS,EAAE;MAChB,CAAC;MACD0C,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxD,cAAc,CAACoD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAG,kBAAkBA,CAACC,QAAgB;IACjC,IAAI,CAACnD,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACD,SAAS,GAAG,KAAK;EACxB;EAEAxB,wBAAwBA,CAACqC,IAAS;IAChC,IAAI,CAACrB,MAAM,CAAC6D,QAAQ,CAAC,CAACxC,IAAI,EAAE9B,QAAQ,EAAEuE,WAAW,CAAC,EAAE;MAClDC,UAAU,EAAE,IAAI,CAAC9D,KAAK;MACtB+D,KAAK,EAAE;QAAEC,YAAY,EAAE5C;MAAI;KAC5B,CAAC;EACJ;EAEA6C,WAAWA,CAAA;IACT,IAAI,CAAC7D,YAAY,CAACgD,IAAI,EAAE;IACxB,IAAI,CAAChD,YAAY,CAAC8D,QAAQ,EAAE;EAC9B;;;uBAvHWrE,8BAA8B,EAAA5B,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAArG,EAAA,CAAAkG,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAvG,EAAA,CAAAkG,iBAAA,CAAAI,EAAA,CAAAE,mBAAA,GAAAxG,EAAA,CAAAkG,iBAAA,CAAAO,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA9B9E,8BAA8B;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnCjH,EAFR,CAAAC,cAAA,aAA2D,aACuC,YAC3C;UAAAD,EAAA,CAAAE,MAAA,2BAAoB;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACxEJ,EAAA,CAAAC,cAAA,kBAC2E;UADrDD,EAAA,CAAAK,UAAA,mBAAA8G,kEAAA;YAAA,OAASD,GAAA,CAAAzB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAE/DzF,EAFI,CAAAI,YAAA,EAC2E,EACzE;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAmD1BD,EAjDA,CAAAoH,UAAA,IAAAC,qDAAA,0BAAgC,IAAAC,qDAAA,0BAsBW,IAAAC,qDAAA,yBAsBL,KAAAC,sDAAA,yBAKD;UAQjDxH,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UAENJ,EAAA,CAAAC,cAAA,6CAA6G;UAAzCD,EAAA,CAAAK,UAAA,qBAAAoH,8FAAA;YAAA,OAAAP,GAAA,CAAA3E,oBAAA,GAAkC,KAAK;UAAA,EAAC;UAC5GvC,EAAA,CAAAI,YAAA,EAAoC;;;UAnERJ,EAAA,CAAAmB,SAAA,GAAmC;UAACnB,EAApC,CAAA0H,UAAA,oCAAmC,iBAAiB;UAI/D1H,EAAA,CAAAmB,SAAA,GAAyB;UAAwCnB,EAAjE,CAAA0H,UAAA,UAAAR,GAAA,CAAA7E,eAAA,CAAyB,YAAyB,mBAAiC;UA8DjErC,EAAA,CAAAmB,SAAA,GAAgC;UAAhCnB,EAAA,CAAA0H,UAAA,YAAAR,GAAA,CAAA3E,oBAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}