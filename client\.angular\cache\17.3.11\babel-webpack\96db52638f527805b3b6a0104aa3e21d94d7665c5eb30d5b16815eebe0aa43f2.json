{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ContactsService {\n  constructor(http) {\n    this.http = http;\n    this.contactSubject = new BehaviorSubject(null);\n    this.contact = this.contactSubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createContact(data) {\n    return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\n  }\n  updateContact(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n  }\n  updateBpStatus(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  getContacts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'bp_company_id,bp_person_id').set('filters[business_partner_person][roles][bp_role][$in][0]', 'BUP001').set('populate[business_partner_company][fields][0]', 'bp_full_name').set('populate[business_partner_person][fields][0]', 'bp_full_name').set('populate[business_partner_person][fields][1]', 'is_marked_for_archiving').set('populate[business_partner_person][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner_person][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number_type').set('populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number').set('populate[business_partner_person][populate][bp_extension][fields][0]', 'web_registered').set('populate[business_partner_person][populate][bp_extension][fields][1]', 'business_department');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_person_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][bp_company_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][business_partner_person][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][3][business_partner_company][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][4][business_partner_company][addresses][phone_numbers][phone_number][$containsi]', searchTerm);\n      params = params.set('filters[$or][5][business_partner_company][addresses][emails][email_address][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n      params\n    });\n  }\n  getContactByID(contactId) {\n    const params = new HttpParams().set('filters[documentId][$eq]', contactId).set('populate[business_partner_company][fields][0]', 'bp_full_name').set('populate[business_partner_person][populate][bp_extension][populate]', '*').set('populate[business_partner_person][populate][addresses][populate]', '*').set('populate[business_partner_person][populate][contact_person_addresses][populate]', '*').set('populate[business_partner_person][populate][notes][populate]', '*');\n    // .set(\n    //   'populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]',\n    //   '*'\n    // )\n    // .set(\n    //   'populate[contact_companies][populate][person_func_and_dept][populate]',\n    //   '*'\n    // )\n    // .set(\n    //   'populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate]',\n    //   '*'\n    // );\n    return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n      params\n    }).pipe(map(response => {\n      const contactDetails = response?.data[0] || null;\n      this.contactSubject.next(contactDetails);\n      return response;\n    }));\n  }\n  getCPDepartment() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getCPFunction() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getAccounts(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  static {\n    this.ɵfac = function ContactsService_Factory(t) {\n      return new (t || ContactsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContactsService,\n      factory: ContactsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ContactsService", "constructor", "http", "contactSubject", "contact", "asObservable", "createNote", "data", "post", "CRM_NOTE", "createContact", "CREATE_CONTACT", "updateContact", "Id", "put", "PROSPECT_CONTACT", "updateBpStatus", "PARTNERS", "updateNote", "getContacts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS_CONTACTS", "getContactByID", "contactId", "pipe", "response", "contactDetails", "next", "getCPDepartment", "CONFIG_DATA", "getCPFunction", "getAccounts", "item", "bp_id", "bp_full_name", "deleteNote", "id", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ContactsService {\r\n  public contactSubject = new BehaviorSubject<any>(null);\r\n  public contact = this.contactSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createContact(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\r\n  }\r\n\r\n  updateContact(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`,\r\n      data\r\n    );\r\n  }\r\n\r\n  updateBpStatus(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, { data });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  getContacts(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set('fields', 'bp_company_id,bp_person_id')\r\n      .set('filters[business_partner_person][roles][bp_role][$in][0]', 'BUP001')\r\n      .set('populate[business_partner_company][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_person][fields][0]', 'bp_full_name')\r\n      .set(\r\n        'populate[business_partner_person][fields][1]',\r\n        'is_marked_for_archiving'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number_type'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][bp_extension][fields][0]',\r\n        'web_registered'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][bp_extension][fields][1]',\r\n        'business_department'\r\n      );\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][bp_person_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][1][bp_company_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][2][business_partner_person][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][3][business_partner_company][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][4][business_partner_company][addresses][phone_numbers][phone_number][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][5][business_partner_company][addresses][emails][email_address][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getContactByID(contactId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[documentId][$eq]', contactId)\r\n      .set('populate[business_partner_company][fields][0]', 'bp_full_name')\r\n      .set(\r\n        'populate[business_partner_person][populate][bp_extension][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][contact_person_addresses][populate]',\r\n        '*'\r\n      )\r\n      .set('populate[business_partner_person][populate][notes][populate]', '*');\r\n    // .set(\r\n    //   'populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]',\r\n    //   '*'\r\n    // )\r\n    // .set(\r\n    //   'populate[contact_companies][populate][person_func_and_dept][populate]',\r\n    //   '*'\r\n    // )\r\n    // .set(\r\n    //   'populate[contact_companies][populate][business_partner_person][populate][contact_person_addresses][populate]',\r\n    //   '*'\r\n    // );\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const contactDetails = response?.data[0] || null;\r\n          this.contactSubject.next(contactDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getCPDepartment() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'CP_DEPARTMENTS');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getCPFunction() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'FUNCTION_CP');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getAccounts(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAO,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,aAAaA,CAACH,IAAS;IACrB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACY,cAAc,EAAE,EAAEJ,IAAI,CAAC;EACnE;EAEAK,aAAaA,CAACC,EAAU,EAAEN,IAAS;IACjC,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAClB,GAAGf,gBAAgB,CAACgB,gBAAgB,IAAIF,EAAE,OAAO,EACjDN,IAAI,CACL;EACH;EAEAS,cAAcA,CAACH,EAAU,EAAEN,IAAS;IAClC,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACkB,QAAQ,IAAIJ,EAAE,EAAE,EAAE;MAAEN;IAAI,CAAE,CAAC;EACtE;EAEAW,UAAUA,CAACL,EAAU,EAAEN,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACU,QAAQ,IAAII,EAAE,EAAE,EAAE;MACzDN;KACD,CAAC;EACJ;EAEAY,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAI7B,UAAU,EAAE,CAC1B8B,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC,CAChDD,GAAG,CAAC,QAAQ,EAAE,4BAA4B,CAAC,CAC3CA,GAAG,CAAC,0DAA0D,EAAE,QAAQ,CAAC,CACzEA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,8CAA8C,EAAE,cAAc,CAAC,CACnEA,GAAG,CACF,8CAA8C,EAC9C,yBAAyB,CAC1B,CACAA,GAAG,CACF,6FAA6F,EAC7F,eAAe,CAChB,CACAA,GAAG,CACF,qFAAqF,EACrF,eAAe,CAChB,CACAA,GAAG,CACF,4FAA4F,EAC5F,mBAAmB,CACpB,CACAA,GAAG,CACF,4FAA4F,EAC5F,cAAc,CACf,CACAA,GAAG,CACF,sEAAsE,EACtE,gBAAgB,CACjB,CACAA,GAAG,CACF,sEAAsE,EACtE,qBAAqB,CACtB;IACH,IAAIJ,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,2CAA2C,EAC3CF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,4CAA4C,EAC5CF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oEAAoE,EACpEF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,qEAAqE,EACrEF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,+FAA+F,EAC/FF,UAAU,CACX;MACDC,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,yFAAyF,EACzFF,UAAU,CACX;IACH;IAEA,OAAO,IAAI,CAACtB,IAAI,CAAC4B,GAAG,CAAQ,GAAG/B,gBAAgB,CAACgC,iBAAiB,EAAE,EAAE;MACnEN;KACD,CAAC;EACJ;EAEAO,cAAcA,CAACC,SAAiB;IAC9B,MAAMR,MAAM,GAAG,IAAI7B,UAAU,EAAE,CAC5B8B,GAAG,CAAC,0BAA0B,EAAEO,SAAS,CAAC,CAC1CP,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CACF,qEAAqE,EACrE,GAAG,CACJ,CACAA,GAAG,CACF,kEAAkE,EAClE,GAAG,CACJ,CACAA,GAAG,CACF,iFAAiF,EACjF,GAAG,CACJ,CACAA,GAAG,CAAC,8DAA8D,EAAE,GAAG,CAAC;IAC3E;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,OAAO,IAAI,CAACxB,IAAI,CACb4B,GAAG,CAAQ,GAAG/B,gBAAgB,CAACgC,iBAAiB,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC,CAC/DS,IAAI,CACHpC,GAAG,CAAEqC,QAAa,IAAI;MACpB,MAAMC,cAAc,GAAGD,QAAQ,EAAE5B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MAChD,IAAI,CAACJ,cAAc,CAACkC,IAAI,CAACD,cAAc,CAAC;MACxC,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,eAAeA,CAAA;IACb,IAAIb,MAAM,GAAG,IAAI7B,UAAU,EAAE,CAC1B8B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9C,OAAO,IAAI,CAACxB,IAAI,CAAC4B,GAAG,CAAM,GAAG/B,gBAAgB,CAACwC,WAAW,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC1E;EAEAe,aAAaA,CAAA;IACX,IAAIf,MAAM,GAAG,IAAI7B,UAAU,EAAE,CAC1B8B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,aAAa,CAAC;IAE3C,OAAO,IAAI,CAACxB,IAAI,CAAC4B,GAAG,CAAM,GAAG/B,gBAAgB,CAACwC,WAAW,EAAE,EAAE;MAAEd;IAAM,CAAE,CAAC;EAC1E;EAEAgB,WAAWA,CAAChB,MAAW;IACrB,OAAO,IAAI,CAACvB,IAAI,CAAC4B,GAAG,CAAM,GAAG/B,gBAAgB,CAACkB,QAAQ,EAAE,EAAE;MAAEQ;IAAM,CAAE,CAAC,CAACS,IAAI,CACxEpC,GAAG,CAAEqC,QAAQ,IACX,CAACA,QAAQ,EAAE5B,IAAI,IAAI,EAAE,EAAET,GAAG,CAAE4C,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAC,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAAC5C,IAAI,CAAC6C,MAAM,CAAM,GAAGhD,gBAAgB,CAACU,QAAQ,IAAIqC,EAAE,EAAE,CAAC;EACpE;;;uBAxLW9C,eAAe,EAAAgD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfnD,eAAe;MAAAoD,OAAA,EAAfpD,eAAe,CAAAqD,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}