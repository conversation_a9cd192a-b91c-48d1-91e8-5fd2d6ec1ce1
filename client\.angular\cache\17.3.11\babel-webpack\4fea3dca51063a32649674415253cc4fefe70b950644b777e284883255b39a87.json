{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i4 from \"src/app/store/activities/activities.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => ({\n  height: \"125px\"\n});\nfunction OpportunitiesFormComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Opportunities\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_12_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesFormComponent_ng_template_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r2.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesFormComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesFormComponent_ng_template_20_span_2_Template, 2, 1, \"span\", 37);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.bp_full_name);\n  }\n}\nfunction OpportunitiesFormComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_21_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"prospect_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesFormComponent_ng_template_31_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.email, \"\");\n  }\n}\nfunction OpportunitiesFormComponent_ng_template_31_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.mobile, \"\");\n  }\n}\nfunction OpportunitiesFormComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, OpportunitiesFormComponent_ng_template_31_span_3_Template, 2, 1, \"span\", 37)(4, OpportunitiesFormComponent_ng_template_31_span_4_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r3.bp_id, \": \", item_r3.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.mobile);\n  }\n}\nfunction OpportunitiesFormComponent_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_32_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesFormComponent_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_47_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"expected_revenue_amount\"].errors && ctx_r0.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesFormComponent_div_68_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_68_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesFormComponent_div_93_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesFormComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, OpportunitiesFormComponent_div_93_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"note\"].errors && ctx_r0.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport let OpportunitiesFormComponent = /*#__PURE__*/(() => {\n  class OpportunitiesFormComponent {\n    constructor(formBuilder, route, opportunitiesservice, activitiesservice, messageservice) {\n      this.formBuilder = formBuilder;\n      this.route = route;\n      this.opportunitiesservice = opportunitiesservice;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.unsubscribe$ = new Subject();\n      this.visible = false;\n      this.onClose = new EventEmitter();\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.activity_id = '';\n      this.owner_id = null;\n      this.dropdowns = {\n        opportunityCategory: [],\n        opportunityStatus: [],\n        opportunitySource: []\n      };\n      this.OpportunityForm = this.formBuilder.group({\n        name: ['', [Validators.required]],\n        prospect_party_id: ['', [Validators.required]],\n        primary_contact_party_id: ['', [Validators.required]],\n        origin_type_code: [''],\n        expected_revenue_amount: ['', [Validators.required]],\n        expected_revenue_start_date: [''],\n        expected_revenue_end_date: [''],\n        life_cycle_status_code: ['', [Validators.required]],\n        probability_percent: [''],\n        group_code: [''],\n        note: ['', [Validators.required]]\n      });\n    }\n    ngOnInit() {\n      this.activity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n      this.OpportunityForm.get('prospect_party_id')?.valueChanges.pipe(takeUntil(this.unsubscribe$), tap(selectedBpId => {\n        if (selectedBpId) {\n          this.loadAccountByContacts(selectedBpId);\n        } else {\n          this.contacts$ = of(this.defaultOptions);\n        }\n      }), catchError(err => {\n        console.error('Account selection error:', err);\n        this.contacts$ = of(this.defaultOptions);\n        return of();\n      })).subscribe();\n      this.getOwner().subscribe({\n        next: response => {\n          this.owner_id = response;\n        },\n        error: err => {\n          console.error('Error fetching bp_id:', err);\n        }\n      });\n      this.loadAccounts();\n      this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    }\n    getOwner() {\n      return this.activitiesservice.getEmailwisePartner();\n    }\n    loadOpportunityDropDown(target, type) {\n      this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n        const options = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n        // Assign options to dropdown object\n        this.dropdowns[target] = options;\n        // Set 'Open' as default selected for activityStatus only\n        if (target === 'opportunityStatus') {\n          const openOption = options.find(opt => opt.label.toLowerCase() === 'discover');\n          if (openOption) {\n            this.OpportunityForm.get('life_cycle_status_code')?.setValue(openOption.value);\n          }\n        }\n      });\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Emit default empty options first\n      this.accountInput$.pipe(debounceTime(300),\n      // Add debounce to reduce API calls\n      distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          'filters[roles][bp_role][$in][0]': 'FLCU01',\n          'filters[roles][bp_role][$in][1]': 'FLCU00',\n          'fields[0]': 'bp_id',\n          'fields[1]': 'first_name',\n          'fields[2]': 'last_name',\n          'fields[3]': 'bp_full_name'\n        };\n        if (term) {\n          params['filters[$or][0][bp_id][$containsi]'] = term;\n          params['filters[$or][1][bp_full_name][$containsi]'] = term;\n        }\n        return this.opportunitiesservice.getPartners(params).pipe(map(response => response ?? []),\n        // Ensure non-null\n        catchError(error => {\n          console.error('Account fetch error:', error);\n          return of([]); // Return empty list on error\n        }), finalize(() => this.accountLoading = false) // Always turn off loading\n        );\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.OpportunityForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.OpportunityForm.value\n        };\n        const data = {\n          name: value?.name,\n          prospect_party_id: value?.prospect_party_id,\n          primary_contact_party_id: value?.primary_contact_party_id,\n          origin_type_code: value?.origin_type_code,\n          expected_revenue_amount: value?.expected_revenue_amount,\n          expected_revenue_start_date: value?.expected_revenue_start_date ? _this.formatDate(value.expected_revenue_start_date) : null,\n          expected_revenue_end_date: value?.expected_revenue_end_date ? _this.formatDate(value.expected_revenue_end_date) : null,\n          life_cycle_status_code: value?.life_cycle_status_code,\n          probability_percent: value?.probability_percent,\n          group_code: value?.group_code,\n          main_employee_responsible_party_id: _this.owner_id,\n          note: value?.note,\n          type_code: '0005',\n          activity_id: _this.activity_id\n        };\n        _this.opportunitiesservice.createFollowup(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.saving = false;\n            _this.visible = false;\n            _this.OpportunityForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Follow Up Added Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.activity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.OpportunityForm.controls;\n    }\n    hideDialog() {\n      this.onClose.emit();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function OpportunitiesFormComponent_Factory(t) {\n        return new (t || OpportunitiesFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.OpportunitiesService), i0.ɵɵdirectiveInject(i4.ActivitiesService), i0.ɵɵdirectiveInject(i5.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OpportunitiesFormComponent,\n        selectors: [[\"app-opportunities-form\"]],\n        inputs: {\n          visible: \"visible\"\n        },\n        outputs: {\n          onClose: \"onClose\"\n        },\n        decls: 97,\n        vars: 60,\n        consts: [[1, \"opportunity-popup\", 3, \"visibleChange\", \"onHide\", \"visible\", \"modal\", \"position\", \"draggable\"], [\"pTemplate\", \"header\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"grid\", \"mt-0\", \"text-base\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\"], [\"for\", \"Name\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"prospect_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"for\", \"Primary Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\", \"sm:col-12\", \"mt-3\"], [\"for\", \"Source\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select a Source\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Expected Value\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"expected_revenue_amount\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Create Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Expected Decision Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Probability\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [1, \"col-12\", \"mt-3\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter your note here...\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"]],\n        template: function OpportunitiesFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dialog\", 0);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesFormComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onHide\", function OpportunitiesFormComponent_Template_p_dialog_onHide_0_listener() {\n              return ctx.hideDialog();\n            });\n            i0.ɵɵtemplate(1, OpportunitiesFormComponent_ng_template_1_Template, 2, 0, \"ng-template\", 1);\n            i0.ɵɵelementStart(2, \"form\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5)(6, \"span\", 6);\n            i0.ɵɵtext(7, \"badge\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(8, \"Name \");\n            i0.ɵɵelementStart(9, \"span\", 7);\n            i0.ɵɵtext(10, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(11, \"input\", 8);\n            i0.ɵɵtemplate(12, OpportunitiesFormComponent_div_12_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 4)(14, \"label\", 10)(15, \"span\", 6);\n            i0.ɵɵtext(16, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(17, \"Account \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"ng-select\", 11);\n            i0.ɵɵpipe(19, \"async\");\n            i0.ɵɵtemplate(20, OpportunitiesFormComponent_ng_template_20_Template, 3, 2, \"ng-template\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(21, OpportunitiesFormComponent_div_21_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 4)(23, \"label\", 13)(24, \"span\", 6);\n            i0.ɵɵtext(25, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(26, \"Primary Contact \");\n            i0.ɵɵelementStart(27, \"span\", 7);\n            i0.ɵɵtext(28, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"ng-select\", 14);\n            i0.ɵɵpipe(30, \"async\");\n            i0.ɵɵtemplate(31, OpportunitiesFormComponent_ng_template_31_Template, 5, 4, \"ng-template\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, OpportunitiesFormComponent_div_32_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 15)(34, \"label\", 16)(35, \"span\", 6);\n            i0.ɵɵtext(36, \"source\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(37, \"Source \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"p-dropdown\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 15)(40, \"label\", 18)(41, \"span\", 6);\n            i0.ɵɵtext(42, \"show_chart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(43, \"Expected Value \");\n            i0.ɵɵelementStart(44, \"span\", 7);\n            i0.ɵɵtext(45, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(46, \"input\", 19);\n            i0.ɵɵtemplate(47, OpportunitiesFormComponent_div_47_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 15)(49, \"label\", 20)(50, \"span\", 6);\n            i0.ɵɵtext(51, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(52, \"Create Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(53, \"p-calendar\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"div\", 15)(55, \"label\", 22)(56, \"span\", 6);\n            i0.ɵɵtext(57, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(58, \"Expected Decision Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(59, \"p-calendar\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"div\", 15)(61, \"label\", 24)(62, \"span\", 6);\n            i0.ɵɵtext(63, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(64, \"Status \");\n            i0.ɵɵelementStart(65, \"span\", 7);\n            i0.ɵɵtext(66, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(67, \"p-dropdown\", 25);\n            i0.ɵɵtemplate(68, OpportunitiesFormComponent_div_68_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 15)(70, \"label\", 26)(71, \"span\", 6);\n            i0.ɵɵtext(72, \"percent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(73, \"Probability \");\n            i0.ɵɵelementStart(74, \"span\", 7);\n            i0.ɵɵtext(75, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(76, \"input\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"div\", 15)(78, \"label\", 28)(79, \"span\", 6);\n            i0.ɵɵtext(80, \"category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(81, \"Category \");\n            i0.ɵɵelementStart(82, \"span\", 7);\n            i0.ɵɵtext(83, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(84, \"p-dropdown\", 29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"div\", 30)(86, \"label\", 31)(87, \"span\", 6);\n            i0.ɵɵtext(88, \"notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(89, \"Notes \");\n            i0.ɵɵelementStart(90, \"span\", 7);\n            i0.ɵɵtext(91, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(92, \"p-editor\", 32);\n            i0.ɵɵtemplate(93, OpportunitiesFormComponent_div_93_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(94, \"div\", 33)(95, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function OpportunitiesFormComponent_Template_button_click_95_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function OpportunitiesFormComponent_Template_button_click_96_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"modal\", true)(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.OpportunityForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(47, _c0, ctx.submitted && ctx.f[\"name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"name\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(19, 43, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(49, _c0, ctx.submitted && ctx.f[\"prospect_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"prospect_party_id\"].errors);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(30, 45, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(51, _c0, ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunitySource\"]);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c0, ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(55, _c0, ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityCategory\"]);\n            i0.ɵɵadvance(8);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(57, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(58, _c0, ctx.submitted && ctx.f[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgIf, i7.NgSelectComponent, i7.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.PrimeTemplate, i8.ButtonDirective, i9.Dropdown, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i6.AsyncPipe],\n        styles: [\".opportunity-popup .p-dialog{margin-right:50px}  .opportunity-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .opportunity-popup .p-dialog .p-dialog-header h4{margin:0}  .opportunity-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}  .opportunity-popup .p-dialog.p-component.p-dialog-resizable{width:calc(100vw - 510px)!important}  .opportunity-popup .field{grid-template-columns:repeat(auto-fill,minmax(360px,1fr))}\"]\n      });\n    }\n  }\n  return OpportunitiesFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}