{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ViewChildren, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils } from 'primeng/utils';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nconst _c0 = [\"content\"];\nconst _c1 = [\"navbar\"];\nconst _c2 = [\"inkbar\"];\nconst _c3 = [\"prevBtn\"];\nconst _c4 = [\"nextBtn\"];\nconst _c5 = [\"tabLink\"];\nconst _c6 = [\"tab\"];\nconst _c7 = a0 => ({\n  \"p-tabmenu p-component\": true,\n  \"p-tabmenu-scrollable\": a0\n});\nconst _c8 = (a0, a1, a2) => ({\n  \"p-tabmenuitem\": true,\n  \"p-disabled\": a0,\n  \"p-highlight\": a1,\n  \"p-hidden\": a2\n});\nconst _c9 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nconst _c10 = () => ({\n  exact: false\n});\nfunction TabMenu_button_2_ChevronLeftIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_button_2_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17, 3);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navBackward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_2_ChevronLeftIcon_2_Template, 1, 1, \"ChevronLeftIcon\", 18)(3, TabMenu_button_2_3_Template, 1, 0, null, 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.previousIconTemplate);\n  }\n}\nfunction TabMenu_li_7_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 28);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r5.icon)(\"ngStyle\", item_r5.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"label\"));\n  }\n}\nfunction TabMenu_li_7_a_2_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemProp(item_r5, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r5.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"badge\"));\n  }\n}\nfunction TabMenu_li_7_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 24, 5);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_2_span_3_Template, 1, 2, \"span\", 25)(4, TabMenu_li_7_a_2_span_4_Template, 2, 1, \"span\", 26)(5, TabMenu_li_7_a_2_ng_template_5_Template, 1, 1, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor)(7, TabMenu_li_7_a_2_span_7_Template, 2, 2, \"span\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r8 = i0.ɵɵreference(6);\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(item_r5, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(item_r5, \"url\"), i0.ɵɵsanitizeUrl)(\"id\", ctx_r2.getItemProp(item_r5, \"id\"))(\"aria-disabled\", ctx_r2.disabled(item_r5))(\"aria-label\", ctx_r2.getItemProp(item_r5, \"label\"))(\"tabindex\", ctx_r2.disabled(item_r5) ? -1 : 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.escape !== false)(\"ngIfElse\", htmlLabel_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.badge);\n  }\n}\nfunction TabMenu_li_7_a_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 28);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r5.icon)(\"ngStyle\", item_r5.iconStyle);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_li_7_a_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"label\"));\n  }\n}\nfunction TabMenu_li_7_a_3_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemProp(item_r5, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_3_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r5.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"badge\"));\n  }\n}\nfunction TabMenu_li_7_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32, 5);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_3_span_3_Template, 1, 3, \"span\", 25)(4, TabMenu_li_7_a_3_span_4_Template, 2, 1, \"span\", 26)(5, TabMenu_li_7_a_3_ng_template_5_Template, 1, 1, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor)(7, TabMenu_li_7_a_3_span_7_Template, 2, 2, \"span\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r9 = i0.ɵɵreference(6);\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r5.routerLink)(\"queryParams\", item_r5.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r5.routerLinkActiveOptions || i0.ɵɵpureFunction0(19, _c10))(\"target\", item_r5.target)(\"fragment\", item_r5.fragment)(\"queryParamsHandling\", item_r5.queryParamsHandling)(\"preserveFragment\", item_r5.preserveFragment)(\"skipLocationChange\", item_r5.skipLocationChange)(\"replaceUrl\", item_r5.replaceUrl)(\"state\", item_r5.state);\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemProp(item_r5, \"id\"))(\"aria-disabled\", ctx_r2.disabled(item_r5))(\"aria-label\", ctx_r2.getItemProp(item_r5, \"label\"))(\"tabindex\", ctx_r2.disabled(item_r5) ? -1 : 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.escape !== false)(\"ngIfElse\", htmlRouteLabel_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.badge);\n  }\n}\nfunction TabMenu_li_7_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TabMenu_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 20, 4);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_Template_li_click_0_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.itemClick($event, item_r5));\n    })(\"keydown\", function TabMenu_li_7_Template_li_keydown_0_listener($event) {\n      const ctx_r5 = i0.ɵɵrestoreView(_r4);\n      const item_r5 = ctx_r5.$implicit;\n      const i_r7 = ctx_r5.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeydownItem($event, i_r7, item_r5));\n    })(\"focus\", function TabMenu_li_7_Template_li_focus_0_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMenuItemFocus(item_r5));\n    });\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_Template, 8, 10, \"a\", 21)(3, TabMenu_li_7_a_3_Template, 8, 20, \"a\", 22)(4, TabMenu_li_7_ng_container_4_Template, 1, 0, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(item_r5.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r5.style)(\"ngClass\", i0.ɵɵpureFunction3(11, _c8, ctx_r2.getItemProp(item_r5, \"disabled\"), ctx_r2.isActive(item_r5), item_r5.visible === false))(\"tooltipOptions\", item_r5.tooltipOptions);\n    i0.ɵɵattribute(\"data-p-disabled\", ctx_r2.disabled(item_r5))(\"data-p-highlight\", ctx_r2.focusedItemInfo() === item_r5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r5.routerLink && !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.routerLink && !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(15, _c9, item_r5, i_r7));\n  }\n}\nfunction TabMenu_button_10_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_button_10_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_10_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_10_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33, 8);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navForward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_10_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 18)(3, TabMenu_button_10_3_Template, 1, 0, null, 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.nextIconTemplate);\n  }\n}\nclass TabMenu {\n  platformId;\n  router;\n  route;\n  cd;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._focusableItems = (this._model || []).reduce((result, item) => {\n      result.push(item);\n      return result;\n    }, []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Defines the default active menuitem\n   * @group Props\n   */\n  activeItem;\n  /**\n   * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n   * @group Props\n   */\n  scrollable;\n  /**\n   * Defines if popup mode enabled.\n   */\n  popup;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Event fired when a tab is selected.\n   * @param {MenuItem} item - Menu item.\n   * @group Emits\n   */\n  activeItemChange = new EventEmitter();\n  content;\n  navbar;\n  inkbar;\n  prevBtn;\n  nextBtn;\n  tabLink;\n  tab;\n  templates;\n  itemTemplate;\n  previousIconTemplate;\n  nextIconTemplate;\n  tabChanged;\n  backwardIsDisabled = true;\n  forwardIsDisabled = false;\n  timerIdForInitialAutoScroll = null;\n  _focusableItems;\n  _model;\n  focusedItemInfo = signal(null);\n  get focusableItems() {\n    if (!this._focusableItems || !this._focusableItems.length) {\n      this._focusableItems = (this.model || []).reduce((result, item) => {\n        result.push(item);\n        return result;\n      }, []);\n    }\n    return this._focusableItems;\n  }\n  constructor(platformId, router, route, cd) {\n    this.platformId = platformId;\n    this.router = router;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'nexticon':\n          this.nextIconTemplate = item.template;\n          break;\n        case 'previousicon':\n          this.previousIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.updateInkBar();\n      this.initAutoScrollForActiveItem();\n      this.initButtonState();\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.tabChanged) {\n      this.updateInkBar();\n      this.tabChanged = false;\n    }\n  }\n  ngOnDestroy() {\n    this.clearAutoScrollHandler();\n  }\n  isActive(item) {\n    if (item.routerLink) {\n      const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n      return this.router.isActive(this.router.createUrlTree(routerLink, {\n        relativeTo: this.route\n      }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n    }\n    return item === this.activeItem;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  visible(item) {\n    return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n  }\n  disabled(item) {\n    return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n  }\n  onMenuItemFocus(item) {\n    this.focusedItemInfo.set(item);\n  }\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    this.activeItem = item;\n    this.activeItemChange.emit(item);\n    this.tabChanged = true;\n    this.cd.markForCheck();\n  }\n  onKeydownItem(event, index, item) {\n    let i = index;\n    let foundElement = {};\n    const tabLinks = this.tabLink.toArray();\n    const tabs = this.tab.toArray();\n    switch (event.code) {\n      case 'ArrowRight':\n        foundElement = this.findNextItem(tabs, i);\n        i = foundElement['i'];\n        break;\n      case 'ArrowLeft':\n        foundElement = this.findPrevItem(tabs, i);\n        i = foundElement['i'];\n        break;\n      case 'End':\n        foundElement = this.findPrevItem(tabs, this.model.length);\n        i = foundElement['i'];\n        event.preventDefault();\n        break;\n      case 'Home':\n        foundElement = this.findNextItem(tabs, -1);\n        i = foundElement['i'];\n        event.preventDefault();\n        break;\n      case 'Space':\n      case 'Enter':\n        this.itemClick(event, item);\n        break;\n      case 'Tab':\n        this.onTabKeyDown(tabLinks);\n        break;\n      default:\n        break;\n    }\n    if (tabLinks[i] && tabLinks[index]) {\n      tabLinks[index].nativeElement.tabIndex = '-1';\n      tabLinks[i].nativeElement.tabIndex = '0';\n      tabLinks[i].nativeElement.focus();\n    }\n    this.cd.markForCheck();\n  }\n  onTabKeyDown(tabLinks) {\n    tabLinks.forEach(item => {\n      item.nativeElement.tabIndex = DomHandler.getAttribute(item.nativeElement.parentElement, 'data-p-highlight') ? '0' : '-1';\n    });\n  }\n  findNextItem(items, index) {\n    let i = index + 1;\n    if (i >= items.length) {\n      return {\n        nextItem: items[items.length],\n        i: items.length\n      };\n    }\n    let nextItem = items[i];\n    if (nextItem) return DomHandler.getAttribute(nextItem.nativeElement, 'data-p-disabled') ? this.findNextItem(items, i) : {\n      nextItem: nextItem.nativeElement,\n      i\n    };else return null;\n  }\n  findPrevItem(items, index) {\n    let i = index - 1;\n    if (i < 0) {\n      return {\n        prevItem: items[0],\n        i: 0\n      };\n    }\n    let prevItem = items[i];\n    if (prevItem) return DomHandler.getAttribute(prevItem.nativeElement, 'data-p-disabled') ? this.findPrevItem(items, i) : {\n      prevItem: prevItem.nativeElement,\n      i\n    };else return null;\n  }\n  updateInkBar() {\n    const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n    if (tabHeader) {\n      this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n      this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n    }\n  }\n  getVisibleButtonWidths() {\n    return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => el ? acc + DomHandler.getWidth(el) : acc, 0);\n  }\n  updateButtonState() {\n    const content = this.content?.nativeElement;\n    const {\n      scrollLeft,\n      scrollWidth\n    } = content;\n    const width = DomHandler.getWidth(content);\n    this.backwardIsDisabled = scrollLeft === 0;\n    this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n  }\n  updateScrollBar(index) {\n    const tabHeader = this.navbar?.nativeElement.children[index];\n    if (!tabHeader) {\n      return;\n    }\n    tabHeader.scrollIntoView({\n      block: 'nearest',\n      inline: 'center'\n    });\n  }\n  onScroll(event) {\n    this.scrollable && this.updateButtonState();\n    event.preventDefault();\n  }\n  navBackward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft - width;\n    content.scrollLeft = pos <= 0 ? 0 : pos;\n  }\n  navForward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft + width;\n    const lastPos = content.scrollWidth - width;\n    content.scrollLeft = pos >= lastPos ? lastPos : pos;\n  }\n  initAutoScrollForActiveItem() {\n    if (!this.scrollable) {\n      return;\n    }\n    this.clearAutoScrollHandler();\n    // We have to wait for the rendering and then can scroll to element.\n    this.timerIdForInitialAutoScroll = setTimeout(() => {\n      const activeItem = this.model.findIndex(menuItem => this.isActive(menuItem));\n      if (activeItem !== -1) {\n        this.updateScrollBar(activeItem);\n      }\n    });\n  }\n  clearAutoScrollHandler() {\n    if (this.timerIdForInitialAutoScroll) {\n      clearTimeout(this.timerIdForInitialAutoScroll);\n      this.timerIdForInitialAutoScroll = null;\n    }\n  }\n  initButtonState() {\n    if (this.scrollable) {\n      // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n      // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n      Promise.resolve().then(() => {\n        this.updateButtonState();\n        this.cd.markForCheck();\n      });\n    }\n  }\n  static ɵfac = function TabMenu_Factory(t) {\n    return new (t || TabMenu)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TabMenu,\n    selectors: [[\"p-tabMenu\"]],\n    contentQueries: function TabMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function TabMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.prevBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabLink = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tab = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      activeItem: \"activeItem\",\n      scrollable: \"scrollable\",\n      popup: \"popup\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      activeItemChange: \"activeItemChange\"\n    },\n    decls: 11,\n    vars: 11,\n    consts: [[\"content\", \"\"], [\"navbar\", \"\"], [\"inkbar\", \"\"], [\"prevBtn\", \"\"], [\"tab\", \"\"], [\"tabLink\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"nextBtn\", \"\"], [3, \"ngClass\", \"ngStyle\"], [1, \"p-tabmenu-nav-container\"], [\"class\", \"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabmenu-nav-content\", 3, \"scroll\"], [\"role\", \"menubar\", 1, \"p-tabmenu-nav\", \"p-reset\"], [\"role\", \"presentation\", \"pTooltip\", \"\", 3, \"ngStyle\", \"class\", \"ngClass\", \"tooltipOptions\", \"click\", \"keydown\", \"focus\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"none\", 1, \"p-tabmenu-ink-bar\"], [\"class\", \"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-prev\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"presentation\", \"pTooltip\", \"\", 3, \"click\", \"keydown\", \"focus\", \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", \"role\", \"menuitem\", \"pRipple\", \"\", 3, \"target\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-next\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"]],\n    template: function TabMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n        i0.ɵɵtemplate(2, TabMenu_button_2_Template, 4, 2, \"button\", 11);\n        i0.ɵɵelementStart(3, \"div\", 12, 0);\n        i0.ɵɵlistener(\"scroll\", function TabMenu_Template_div_scroll_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onScroll($event));\n        });\n        i0.ɵɵelementStart(5, \"ul\", 13, 1);\n        i0.ɵɵtemplate(7, TabMenu_li_7_Template, 5, 18, \"li\", 14);\n        i0.ɵɵelement(8, \"li\", 15, 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(10, TabMenu_button_10_Template, 4, 2, \"button\", 16);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c7, ctx.scrollable))(\"ngStyle\", ctx.style);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.backwardIsDisabled);\n        i0.ɵɵadvance(3);\n        i0.ɵɵattribute(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.focusableItems);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.forwardIsDisabled);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.RouterLink, i1.RouterLinkActive, i3.Ripple, i4.Tooltip, ChevronLeftIcon, ChevronRightIcon],\n    styles: [\"@layer primeng{.p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tabMenu',\n      template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"menubar\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\">\n                        <li\n                            #tab\n                            *ngFor=\"let item of focusableItems; let i = index\"\n                            role=\"presentation\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.data-p-disabled]=\"disabled(item)\"\n                            [attr.data-p-highlight]=\"focusedItemInfo() === item\"\n                            (click)=\"itemClick($event, item)\"\n                            (keydown)=\"onKeydownItem($event, i, item)\"\n                            (focus)=\"onMenuItemFocus(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': getItemProp(item, 'disabled'), 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                #tabLink\n                                *ngIf=\"!item.routerLink && !itemTemplate\"\n                                class=\"p-menuitem-link\"\n                                role=\"menuitem\"\n                                [attr.href]=\"getItemProp(item, 'url')\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <a\n                                #tabLink\n                                *ngIf=\"item.routerLink && !itemTemplate\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"menuitem\"\n                                class=\"p-menuitem-link\"\n                                [target]=\"item.target\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [attr.aria-hidden]=\"true\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\" role=\"none\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}}\\n\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i1.Router\n  }, {\n    type: i1.ActivatedRoute\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    model: [{\n      type: Input\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    scrollable: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    activeItemChange: [{\n      type: Output\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    navbar: [{\n      type: ViewChild,\n      args: ['navbar']\n    }],\n    inkbar: [{\n      type: ViewChild,\n      args: ['inkbar']\n    }],\n    prevBtn: [{\n      type: ViewChild,\n      args: ['prevBtn']\n    }],\n    nextBtn: [{\n      type: ViewChild,\n      args: ['nextBtn']\n    }],\n    tabLink: [{\n      type: ViewChildren,\n      args: ['tabLink']\n    }],\n    tab: [{\n      type: ViewChildren,\n      args: ['tab']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass TabMenuModule {\n  static ɵfac = function TabMenuModule_Factory(t) {\n    return new (t || TabMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TabMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n      exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n      declarations: [TabMenu]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };", "map": {"version": 3, "names": ["i2", "isPlatformBrowser", "CommonModule", "i0", "EventEmitter", "signal", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ViewChildren", "ContentChildren", "NgModule", "i1", "RouterModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ChevronLeftIcon", "ChevronRightIcon", "i3", "RippleModule", "i4", "TooltipModule", "ObjectUtils", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "a0", "_c8", "a1", "a2", "_c9", "$implicit", "index", "_c10", "exact", "TabMenu_button_2_ChevronLeftIcon_2_Template", "rf", "ctx", "ɵɵelement", "ɵɵattribute", "TabMenu_button_2_3_ng_template_0_Template", "TabMenu_button_2_3_Template", "ɵɵtemplate", "TabMenu_button_2_Template", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TabMenu_button_2_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navBackward", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "previousIconTemplate", "TabMenu_li_7_a_2_span_3_Template", "item_r5", "icon", "iconStyle", "TabMenu_li_7_a_2_span_4_Template", "ɵɵtext", "ɵɵtextInterpolate", "getItemProp", "TabMenu_li_7_a_2_ng_template_5_Template", "ɵɵsanitizeHtml", "TabMenu_li_7_a_2_span_7_Template", "badgeStyleClass", "TabMenu_li_7_a_2_Template", "ɵɵelementContainerStart", "ɵɵtemplateRefExtractor", "ɵɵelementContainerEnd", "htmlLabel_r8", "ɵɵreference", "ɵɵsanitizeUrl", "disabled", "escape", "badge", "TabMenu_li_7_a_3_span_3_Template", "TabMenu_li_7_a_3_span_4_Template", "TabMenu_li_7_a_3_ng_template_5_Template", "TabMenu_li_7_a_3_span_7_Template", "TabMenu_li_7_a_3_Template", "htmlRouteLabel_r9", "routerLink", "queryParams", "routerLinkActiveOptions", "ɵɵpureFunction0", "target", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "TabMenu_li_7_ng_container_4_Template", "ɵɵelementContainer", "TabMenu_li_7_Template", "_r4", "TabMenu_li_7_Template_li_click_0_listener", "$event", "itemClick", "TabMenu_li_7_Template_li_keydown_0_listener", "ctx_r5", "i_r7", "onKeydownItem", "TabMenu_li_7_Template_li_focus_0_listener", "onMenuItemFocus", "ɵɵclassMap", "styleClass", "style", "ɵɵpureFunction3", "isActive", "visible", "tooltipOptions", "focusedItemInfo", "itemTemplate", "ɵɵpureFunction2", "TabMenu_button_10_ChevronRightIcon_2_Template", "TabMenu_button_10_3_ng_template_0_Template", "TabMenu_button_10_3_Template", "TabMenu_button_10_Template", "_r10", "TabMenu_button_10_Template_button_click_0_listener", "navForward", "nextIconTemplate", "TabMenu", "platformId", "router", "route", "cd", "model", "value", "_model", "_focusableItems", "reduce", "result", "item", "push", "activeItem", "scrollable", "popup", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "activeItemChange", "content", "navbar", "inkbar", "prevBtn", "nextBtn", "tabLink", "tab", "templates", "tabChanged", "backwardIsDisabled", "forwardIsDisabled", "timerIdForInitialAutoScroll", "focusableItems", "length", "constructor", "ngAfterContentInit", "for<PERSON>ach", "getType", "template", "ngAfterViewInit", "updateInkBar", "initAutoScrollForActiveItem", "initButtonState", "ngAfterViewChecked", "ngOnDestroy", "clearAutoScrollHandler", "Array", "isArray", "createUrlTree", "relativeTo", "toString", "name", "getItemValue", "undefined", "set", "event", "preventDefault", "url", "command", "originalEvent", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "foundElement", "tabLinks", "toArray", "tabs", "code", "findNextItem", "findPrevItem", "onTabKeyDown", "nativeElement", "tabIndex", "focus", "getAttribute", "parentElement", "items", "nextItem", "prevItem", "tabHeader", "findSingle", "width", "getWidth", "left", "getOffset", "getVisibleButtonWidths", "acc", "el", "updateButtonState", "scrollLeft", "scrollWidth", "parseInt", "updateScrollBar", "children", "scrollIntoView", "block", "inline", "onScroll", "pos", "lastPos", "setTimeout", "findIndex", "menuItem", "clearTimeout", "Promise", "resolve", "then", "ɵfac", "TabMenu_Factory", "t", "ɵɵdirectiveInject", "Router", "ActivatedRoute", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TabMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "TabMenu_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "decls", "vars", "consts", "TabMenu_Template", "_r1", "TabMenu_Template_div_scroll_3_listener", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "decorators", "TabMenuModule", "TabMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-tabmenu.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { isPlatform<PERSON>rowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ViewChildren, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils } from 'primeng/utils';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nclass TabMenu {\n    platformId;\n    router;\n    route;\n    cd;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n        this._model = value;\n        this._focusableItems = (this._model || []).reduce((result, item) => {\n            result.push(item);\n            return result;\n        }, []);\n    }\n    get model() {\n        return this._model;\n    }\n    /**\n     * Defines the default active menuitem\n     * @group Props\n     */\n    activeItem;\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Defines if popup mode enabled.\n     */\n    popup;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Event fired when a tab is selected.\n     * @param {MenuItem} item - Menu item.\n     * @group Emits\n     */\n    activeItemChange = new EventEmitter();\n    content;\n    navbar;\n    inkbar;\n    prevBtn;\n    nextBtn;\n    tabLink;\n    tab;\n    templates;\n    itemTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    tabChanged;\n    backwardIsDisabled = true;\n    forwardIsDisabled = false;\n    timerIdForInitialAutoScroll = null;\n    _focusableItems;\n    _model;\n    focusedItemInfo = signal(null);\n    get focusableItems() {\n        if (!this._focusableItems || !this._focusableItems.length) {\n            this._focusableItems = (this.model || []).reduce((result, item) => {\n                result.push(item);\n                return result;\n            }, []);\n        }\n        return this._focusableItems;\n    }\n    constructor(platformId, router, route, cd) {\n        this.platformId = platformId;\n        this.router = router;\n        this.route = route;\n        this.cd = cd;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.updateInkBar();\n            this.initAutoScrollForActiveItem();\n            this.initButtonState();\n        }\n    }\n    ngAfterViewChecked() {\n        if (this.tabChanged) {\n            this.updateInkBar();\n            this.tabChanged = false;\n        }\n    }\n    ngOnDestroy() {\n        this.clearAutoScrollHandler();\n    }\n    isActive(item) {\n        if (item.routerLink) {\n            const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n            return this.router.isActive(this.router.createUrlTree(routerLink, { relativeTo: this.route }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n        }\n        return item === this.activeItem;\n    }\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    visible(item) {\n        return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n    }\n    disabled(item) {\n        return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n    }\n    onMenuItemFocus(item) {\n        this.focusedItemInfo.set(item);\n    }\n    itemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        this.activeItem = item;\n        this.activeItemChange.emit(item);\n        this.tabChanged = true;\n        this.cd.markForCheck();\n    }\n    onKeydownItem(event, index, item) {\n        let i = index;\n        let foundElement = {};\n        const tabLinks = this.tabLink.toArray();\n        const tabs = this.tab.toArray();\n        switch (event.code) {\n            case 'ArrowRight':\n                foundElement = this.findNextItem(tabs, i);\n                i = foundElement['i'];\n                break;\n            case 'ArrowLeft':\n                foundElement = this.findPrevItem(tabs, i);\n                i = foundElement['i'];\n                break;\n            case 'End':\n                foundElement = this.findPrevItem(tabs, this.model.length);\n                i = foundElement['i'];\n                event.preventDefault();\n                break;\n            case 'Home':\n                foundElement = this.findNextItem(tabs, -1);\n                i = foundElement['i'];\n                event.preventDefault();\n                break;\n            case 'Space':\n            case 'Enter':\n                this.itemClick(event, item);\n                break;\n            case 'Tab':\n                this.onTabKeyDown(tabLinks);\n                break;\n            default:\n                break;\n        }\n        if (tabLinks[i] && tabLinks[index]) {\n            tabLinks[index].nativeElement.tabIndex = '-1';\n            tabLinks[i].nativeElement.tabIndex = '0';\n            tabLinks[i].nativeElement.focus();\n        }\n        this.cd.markForCheck();\n    }\n    onTabKeyDown(tabLinks) {\n        tabLinks.forEach((item) => {\n            item.nativeElement.tabIndex = DomHandler.getAttribute(item.nativeElement.parentElement, 'data-p-highlight') ? '0' : '-1';\n        });\n    }\n    findNextItem(items, index) {\n        let i = index + 1;\n        if (i >= items.length) {\n            return { nextItem: items[items.length], i: items.length };\n        }\n        let nextItem = items[i];\n        if (nextItem)\n            return DomHandler.getAttribute(nextItem.nativeElement, 'data-p-disabled') ? this.findNextItem(items, i) : { nextItem: nextItem.nativeElement, i };\n        else\n            return null;\n    }\n    findPrevItem(items, index) {\n        let i = index - 1;\n        if (i < 0) {\n            return { prevItem: items[0], i: 0 };\n        }\n        let prevItem = items[i];\n        if (prevItem)\n            return DomHandler.getAttribute(prevItem.nativeElement, 'data-p-disabled') ? this.findPrevItem(items, i) : { prevItem: prevItem.nativeElement, i };\n        else\n            return null;\n    }\n    updateInkBar() {\n        const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n        if (tabHeader) {\n            this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n            this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n        }\n    }\n    getVisibleButtonWidths() {\n        return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => (el ? acc + DomHandler.getWidth(el) : acc), 0);\n    }\n    updateButtonState() {\n        const content = this.content?.nativeElement;\n        const { scrollLeft, scrollWidth } = content;\n        const width = DomHandler.getWidth(content);\n        this.backwardIsDisabled = scrollLeft === 0;\n        this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n    }\n    updateScrollBar(index) {\n        const tabHeader = this.navbar?.nativeElement.children[index];\n        if (!tabHeader) {\n            return;\n        }\n        tabHeader.scrollIntoView({ block: 'nearest', inline: 'center' });\n    }\n    onScroll(event) {\n        this.scrollable && this.updateButtonState();\n        event.preventDefault();\n    }\n    navBackward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft - width;\n        content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n    navForward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft + width;\n        const lastPos = content.scrollWidth - width;\n        content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n    initAutoScrollForActiveItem() {\n        if (!this.scrollable) {\n            return;\n        }\n        this.clearAutoScrollHandler();\n        // We have to wait for the rendering and then can scroll to element.\n        this.timerIdForInitialAutoScroll = setTimeout(() => {\n            const activeItem = this.model.findIndex((menuItem) => this.isActive(menuItem));\n            if (activeItem !== -1) {\n                this.updateScrollBar(activeItem);\n            }\n        });\n    }\n    clearAutoScrollHandler() {\n        if (this.timerIdForInitialAutoScroll) {\n            clearTimeout(this.timerIdForInitialAutoScroll);\n            this.timerIdForInitialAutoScroll = null;\n        }\n    }\n    initButtonState() {\n        if (this.scrollable) {\n            // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n            // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n            Promise.resolve().then(() => {\n                this.updateButtonState();\n                this.cd.markForCheck();\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenu, deps: [{ token: PLATFORM_ID }, { token: i1.Router }, { token: i1.ActivatedRoute }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: TabMenu, selector: \"p-tabMenu\", inputs: { model: \"model\", activeItem: \"activeItem\", scrollable: \"scrollable\", popup: \"popup\", style: \"style\", styleClass: \"styleClass\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\" }, outputs: { activeItemChange: \"activeItemChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"content\", first: true, predicate: [\"content\"], descendants: true }, { propertyName: \"navbar\", first: true, predicate: [\"navbar\"], descendants: true }, { propertyName: \"inkbar\", first: true, predicate: [\"inkbar\"], descendants: true }, { propertyName: \"prevBtn\", first: true, predicate: [\"prevBtn\"], descendants: true }, { propertyName: \"nextBtn\", first: true, predicate: [\"nextBtn\"], descendants: true }, { propertyName: \"tabLink\", predicate: [\"tabLink\"], descendants: true }, { propertyName: \"tab\", predicate: [\"tab\"], descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"menubar\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\">\n                        <li\n                            #tab\n                            *ngFor=\"let item of focusableItems; let i = index\"\n                            role=\"presentation\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.data-p-disabled]=\"disabled(item)\"\n                            [attr.data-p-highlight]=\"focusedItemInfo() === item\"\n                            (click)=\"itemClick($event, item)\"\n                            (keydown)=\"onKeydownItem($event, i, item)\"\n                            (focus)=\"onMenuItemFocus(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': getItemProp(item, 'disabled'), 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                #tabLink\n                                *ngIf=\"!item.routerLink && !itemTemplate\"\n                                class=\"p-menuitem-link\"\n                                role=\"menuitem\"\n                                [attr.href]=\"getItemProp(item, 'url')\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <a\n                                #tabLink\n                                *ngIf=\"item.routerLink && !itemTemplate\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"menuitem\"\n                                class=\"p-menuitem-link\"\n                                [target]=\"item.target\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [attr.aria-hidden]=\"true\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\" role=\"none\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.RouterLink), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.RouterLinkActive), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.Tooltip), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(() => ChevronLeftIcon), selector: \"ChevronLeftIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tabMenu', template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"menubar\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\">\n                        <li\n                            #tab\n                            *ngFor=\"let item of focusableItems; let i = index\"\n                            role=\"presentation\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.data-p-disabled]=\"disabled(item)\"\n                            [attr.data-p-highlight]=\"focusedItemInfo() === item\"\n                            (click)=\"itemClick($event, item)\"\n                            (keydown)=\"onKeydownItem($event, i, item)\"\n                            (focus)=\"onMenuItemFocus(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': getItemProp(item, 'disabled'), 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                #tabLink\n                                *ngIf=\"!item.routerLink && !itemTemplate\"\n                                class=\"p-menuitem-link\"\n                                role=\"menuitem\"\n                                [attr.href]=\"getItemProp(item, 'url')\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <a\n                                #tabLink\n                                *ngIf=\"item.routerLink && !itemTemplate\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"menuitem\"\n                                class=\"p-menuitem-link\"\n                                [target]=\"item.target\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [attr.aria-hidden]=\"true\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\" role=\"none\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}}\\n\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i1.Router }, { type: i1.ActivatedRoute }, { type: i0.ChangeDetectorRef }], propDecorators: { model: [{\n                type: Input\n            }], activeItem: [{\n                type: Input\n            }], scrollable: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], activeItemChange: [{\n                type: Output\n            }], content: [{\n                type: ViewChild,\n                args: ['content']\n            }], navbar: [{\n                type: ViewChild,\n                args: ['navbar']\n            }], inkbar: [{\n                type: ViewChild,\n                args: ['inkbar']\n            }], prevBtn: [{\n                type: ViewChild,\n                args: ['prevBtn']\n            }], nextBtn: [{\n                type: ViewChild,\n                args: ['nextBtn']\n            }], tabLink: [{\n                type: ViewChildren,\n                args: ['tabLink']\n            }], tab: [{\n                type: ViewChildren,\n                args: ['tab']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass TabMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenuModule, declarations: [TabMenu], imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon], exports: [TabMenu, RouterModule, SharedModule, TooltipModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenuModule, imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TabMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n                    exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n                    declarations: [TabMenu]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACnM,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;;AAE3C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAA;EAAA,wBAAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAD,EAAA,EAAAE,EAAA,EAAAC,EAAA;EAAA;EAAA,cAAAH,EAAA;EAAA,eAAAE,EAAA;EAAA,YAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAJ,EAAA,EAAAE,EAAA;EAAAG,SAAA,EAAAL,EAAA;EAAAM,KAAA,EAAAJ;AAAA;AAAA,MAAAK,IAAA,GAAAA,CAAA;EAAAC,KAAA;AAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAkT6F5C,EAAE,CAAA8C,SAAA,qBAKD,CAAC;EAAA;EAAA,IAAAF,EAAA;IALF5C,EAAE,CAAA+C,WAAA;EAAA;AAAA;AAAA,SAAAC,0CAAAJ,EAAA,EAAAC,GAAA;AAAA,SAAAI,4BAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAkD,UAAA,IAAAF,yCAAA,qBAMtB,CAAC;EAAA;AAAA;AAAA,SAAAG,0BAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAQ,GAAA,GANmBpD,EAAE,CAAAqD,gBAAA;IAAFrD,EAAE,CAAAsD,cAAA,mBAI+F,CAAC;IAJlGtD,EAAE,CAAAuD,UAAA,mBAAAC,kDAAA;MAAFxD,EAAE,CAAAyD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CAIyCF,MAAA,CAAAG,WAAA,CAAY,CAAC;IAAA,EAAC;IAJzD7D,EAAE,CAAAkD,UAAA,IAAAP,2CAAA,6BAKD,CAAC,IAAAM,2BAAA,gBACtB,CAAC;IANmBjD,EAAE,CAAA8D,YAAA,CAOvE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAc,MAAA,GAPoE1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAA+D,SAAA,EAK/B,CAAC;IAL4B/D,EAAE,CAAAgE,UAAA,UAAAN,MAAA,CAAAO,oBAK/B,CAAC;IAL4BjE,EAAE,CAAA+D,SAAA,CAMxB,CAAC;IANqB/D,EAAE,CAAAgE,UAAA,qBAAAN,MAAA,CAAAO,oBAMxB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IANqB5C,EAAE,CAAA8C,SAAA,cAuC4C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,OAAA,GAvC/CnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAFvC,EAAE,CAAAgE,UAAA,YAAAG,OAAA,CAAAC,IAuCT,CAAC,YAAAD,OAAA,CAAAE,SAA4C,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvCvC5C,EAAE,CAAAsD,cAAA,cAwCgB,CAAC;IAxCnBtD,EAAE,CAAAuE,MAAA,EAwCgD,CAAC;IAxCnDvE,EAAE,CAAA8D,YAAA,CAwCuD,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAuB,OAAA,GAxC1DnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAA+D,SAAA,CAwCgD,CAAC;IAxCnD/D,EAAE,CAAAwE,iBAAA,CAAAd,MAAA,CAAAe,WAAA,CAAAN,OAAA,UAwCgD,CAAC;EAAA;AAAA;AAAA,SAAAO,wCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxCnD5C,EAAE,CAAA8C,SAAA,cAyC0C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,OAAA,GAzC7CnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,cAAAN,MAAA,CAAAe,WAAA,CAAAN,OAAA,YAAFnE,EAAE,CAAA2E,cAyCkC,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzCrC5C,EAAE,CAAAsD,cAAA,cA0CuB,CAAC;IA1C1BtD,EAAE,CAAAuE,MAAA,EA0CuD,CAAC;IA1C1DvE,EAAE,CAAA8D,YAAA,CA0C8D,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAuB,OAAA,GA1CjEnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,YAAAG,OAAA,CAAAU,eA0CsB,CAAC;IA1CzB7E,EAAE,CAAA+D,SAAA,CA0CuD,CAAC;IA1C1D/D,EAAE,CAAAwE,iBAAA,CAAAd,MAAA,CAAAe,WAAA,CAAAN,OAAA,UA0CuD,CAAC;EAAA;AAAA;AAAA,SAAAW,0BAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1C1D5C,EAAE,CAAAsD,cAAA,cAqCnE,CAAC;IArCgEtD,EAAE,CAAA+E,uBAAA,EAsClD,CAAC;IAtC+C/E,EAAE,CAAAkD,UAAA,IAAAgB,gCAAA,kBAuCqC,CAAC,IAAAI,gCAAA,kBACtB,CAAC,IAAAI,uCAAA,gCAxCnB1E,EAAE,CAAAgF,sBAyCpC,CAAC,IAAAJ,gCAAA,kBAC0D,CAAC;IA1C1B5E,EAAE,CAAAiF,qBAAA;IAAFjF,EAAE,CAAA8D,YAAA,CA4ChE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAsC,YAAA,GA5C6DlF,EAAE,CAAAmF,WAAA;IAAA,MAAAhB,OAAA,GAAFnE,EAAE,CAAA2D,aAAA,GAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,WAAAN,MAAA,CAAAe,WAAA,CAAAN,OAAA,WAmC1B,CAAC;IAnCuBnE,EAAE,CAAA+C,WAAA,SAAAW,MAAA,CAAAe,WAAA,CAAAN,OAAA,UAAFnE,EAAE,CAAAoF,aAAA,QAAA1B,MAAA,CAAAe,WAAA,CAAAN,OAAA,0BAAAT,MAAA,CAAA2B,QAAA,CAAAlB,OAAA,iBAAAT,MAAA,CAAAe,WAAA,CAAAN,OAAA,wBAAAT,MAAA,CAAA2B,QAAA,CAAAlB,OAAA;IAAFnE,EAAE,CAAA+D,SAAA,EAuCQ,CAAC;IAvCX/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAC,IAuCQ,CAAC;IAvCXpE,EAAE,CAAA+D,SAAA,CAwCA,CAAC;IAxCH/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAmB,MAAA,UAwCA,CAAC,aAAAJ,YAAa,CAAC;IAxCjBlF,EAAE,CAAA+D,SAAA,EA0CZ,CAAC;IA1CS/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAoB,KA0CZ,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1CS5C,EAAE,CAAA8C,SAAA,cAoEsE,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,OAAA,GApEzEnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAFvC,EAAE,CAAAgE,UAAA,YAAAG,OAAA,CAAAC,IAoEiB,CAAC,YAAAD,OAAA,CAAAE,SAA4C,CAAC;IApEjErE,EAAE,CAAA+C,WAAA;EAAA;AAAA;AAAA,SAAA0C,iCAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAsD,cAAA,cAqEqB,CAAC;IArExBtD,EAAE,CAAAuE,MAAA,EAqEqD,CAAC;IArExDvE,EAAE,CAAA8D,YAAA,CAqE4D,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAuB,OAAA,GArE/DnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAA+D,SAAA,CAqEqD,CAAC;IArExD/D,EAAE,CAAAwE,iBAAA,CAAAd,MAAA,CAAAe,WAAA,CAAAN,OAAA,UAqEqD,CAAC;EAAA;AAAA;AAAA,SAAAuB,wCAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArExD5C,EAAE,CAAA8C,SAAA,cAsE+C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,OAAA,GAtElDnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,cAAAN,MAAA,CAAAe,WAAA,CAAAN,OAAA,YAAFnE,EAAE,CAAA2E,cAsEuC,CAAC;EAAA;AAAA;AAAA,SAAAgB,iCAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtE1C5C,EAAE,CAAAsD,cAAA,cAuEuB,CAAC;IAvE1BtD,EAAE,CAAAuE,MAAA,EAuEuD,CAAC;IAvE1DvE,EAAE,CAAA8D,YAAA,CAuE8D,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAuB,OAAA,GAvEjEnE,EAAE,CAAA2D,aAAA,IAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,YAAAG,OAAA,CAAAU,eAuEsB,CAAC;IAvEzB7E,EAAE,CAAA+D,SAAA,CAuEuD,CAAC;IAvE1D/D,EAAE,CAAAwE,iBAAA,CAAAd,MAAA,CAAAe,WAAA,CAAAN,OAAA,UAuEuD,CAAC;EAAA;AAAA;AAAA,SAAAyB,0BAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvE1D5C,EAAE,CAAAsD,cAAA,cAkEnE,CAAC;IAlEgEtD,EAAE,CAAA+E,uBAAA,EAmElD,CAAC;IAnE+C/E,EAAE,CAAAkD,UAAA,IAAAsC,gCAAA,kBAoE+D,CAAC,IAAAC,gCAAA,kBAC3C,CAAC,IAAAC,uCAAA,gCArExB1F,EAAE,CAAAgF,sBAsE/B,CAAC,IAAAW,gCAAA,kBACqD,CAAC;IAvE1B3F,EAAE,CAAAiF,qBAAA;IAAFjF,EAAE,CAAA8D,YAAA,CAyEhE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAiD,iBAAA,GAzE6D7F,EAAE,CAAAmF,WAAA;IAAA,MAAAhB,OAAA,GAAFnE,EAAE,CAAA2D,aAAA,GAAApB,SAAA;IAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAgE,UAAA,eAAAG,OAAA,CAAA2B,UAgDlC,CAAC,gBAAA3B,OAAA,CAAA4B,WACC,CAAC,6CACY,CAAC,4BAAA5B,OAAA,CAAA6B,uBAAA,IAlDgBhG,EAAE,CAAAiG,eAAA,KAAAxD,IAAA,CAmDY,CAAC,WAAA0B,OAAA,CAAA+B,MAGvD,CAAC,aAAA/B,OAAA,CAAAgC,QAKG,CAAC,wBAAAhC,OAAA,CAAAiC,mBACqB,CAAC,qBAAAjC,OAAA,CAAAkC,gBACP,CAAC,uBAAAlC,OAAA,CAAAmC,kBACG,CAAC,eAAAnC,OAAA,CAAAoC,UACjB,CAAC,UAAApC,OAAA,CAAAqC,KACX,CAAC;IAhEyCxG,EAAE,CAAA+C,WAAA,OAAAW,MAAA,CAAAe,WAAA,CAAAN,OAAA,0BAAAT,MAAA,CAAA2B,QAAA,CAAAlB,OAAA,iBAAAT,MAAA,CAAAe,WAAA,CAAAN,OAAA,wBAAAT,MAAA,CAAA2B,QAAA,CAAAlB,OAAA;IAAFnE,EAAE,CAAA+D,SAAA,EAoEkC,CAAC;IApErC/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAC,IAoEkC,CAAC;IApErCpE,EAAE,CAAA+D,SAAA,CAqEA,CAAC;IArEH/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAmB,MAAA,UAqEA,CAAC,aAAAO,iBAAkB,CAAC;IArEtB7F,EAAE,CAAA+D,SAAA,EAuEZ,CAAC;IAvES/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAAoB,KAuEZ,CAAC;EAAA;AAAA;AAAA,SAAAkB,qCAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvES5C,EAAE,CAAA0G,kBAAA,EA0EkC,CAAC;EAAA;AAAA;AAAA,SAAAC,sBAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgE,GAAA,GA1ErC5G,EAAE,CAAAqD,gBAAA;IAAFrD,EAAE,CAAAsD,cAAA,eAwBvE,CAAC;IAxBoEtD,EAAE,CAAAuD,UAAA,mBAAAsD,0CAAAC,MAAA;MAAA,MAAA3C,OAAA,GAAFnE,EAAE,CAAAyD,aAAA,CAAAmD,GAAA,EAAArE,SAAA;MAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CAkB1DF,MAAA,CAAAqD,SAAA,CAAAD,MAAA,EAAA3C,OAAsB,CAAC;IAAA,EAAC,qBAAA6C,4CAAAF,MAAA;MAAA,MAAAG,MAAA,GAlBgCjH,EAAE,CAAAyD,aAAA,CAAAmD,GAAA;MAAA,MAAAzC,OAAA,GAAA8C,MAAA,CAAA1E,SAAA;MAAA,MAAA2E,IAAA,GAAAD,MAAA,CAAAzE,KAAA;MAAA,MAAAkB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CAmBxDF,MAAA,CAAAyD,aAAA,CAAAL,MAAA,EAAAI,IAAA,EAAA/C,OAA6B,CAAC;IAAA,EAAC,mBAAAiD,0CAAA;MAAA,MAAAjD,OAAA,GAnBuBnE,EAAE,CAAAyD,aAAA,CAAAmD,GAAA,EAAArE,SAAA;MAAA,MAAAmB,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CAoB1DF,MAAA,CAAA2D,eAAA,CAAAlD,OAAoB,CAAC;IAAA,EAAC;IApBkCnE,EAAE,CAAAkD,UAAA,IAAA4B,yBAAA,gBAqCnE,CAAC,IAAAc,yBAAA,gBA6BD,CAAC,IAAAa,oCAAA,0BAQqF,CAAC;IA1EtBzG,EAAE,CAAA8D,YAAA,CA2EnE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAuB,OAAA,GAAAtB,GAAA,CAAAN,SAAA;IAAA,MAAA2E,IAAA,GAAArE,GAAA,CAAAL,KAAA;IAAA,MAAAkB,MAAA,GA3EgE1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAsH,UAAA,CAAAnD,OAAA,CAAAoD,UAe3C,CAAC;IAfwCvH,EAAE,CAAAgE,UAAA,YAAAG,OAAA,CAAAqD,KAc9C,CAAC,YAd2CxH,EAAE,CAAAyH,eAAA,KAAAtF,GAAA,EAAAuB,MAAA,CAAAe,WAAA,CAAAN,OAAA,eAAAT,MAAA,CAAAgE,QAAA,CAAAvD,OAAA,GAAAA,OAAA,CAAAwD,OAAA,WAqBiF,CAAC,mBAAAxD,OAAA,CAAAyD,cAEhH,CAAC;IAvB2B5H,EAAE,CAAA+C,WAAA,oBAAAW,MAAA,CAAA2B,QAAA,CAAAlB,OAAA,uBAAAT,MAAA,CAAAmE,eAAA,OAAA1D,OAAA;IAAFnE,EAAE,CAAA+D,SAAA,EA2BxB,CAAC;IA3BqB/D,EAAE,CAAAgE,UAAA,UAAAG,OAAA,CAAA2B,UAAA,KAAApC,MAAA,CAAAoE,YA2BxB,CAAC;IA3BqB9H,EAAE,CAAA+D,SAAA,CA+CzB,CAAC;IA/CsB/D,EAAE,CAAAgE,UAAA,SAAAG,OAAA,CAAA2B,UAAA,KAAApC,MAAA,CAAAoE,YA+CzB,CAAC;IA/CsB9H,EAAE,CAAA+D,SAAA,CA0ErB,CAAC;IA1EkB/D,EAAE,CAAAgE,UAAA,qBAAAN,MAAA,CAAAoE,YA0ErB,CAAC,4BA1EkB9H,EAAE,CAAA+H,eAAA,KAAAzF,GAAA,EAAA6B,OAAA,EAAA+C,IAAA,CA0EiB,CAAC;EAAA;AAAA;AAAA,SAAAc,8CAAApF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1EpB5C,EAAE,CAAA8C,SAAA,sBAgFA,CAAC;EAAA;EAAA,IAAAF,EAAA;IAhFH5C,EAAE,CAAA+C,WAAA;EAAA;AAAA;AAAA,SAAAkF,2CAAArF,EAAA,EAAAC,GAAA;AAAA,SAAAqF,6BAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5C,EAAE,CAAAkD,UAAA,IAAA+E,0CAAA,qBAiF1B,CAAC;EAAA;AAAA;AAAA,SAAAE,2BAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwF,IAAA,GAjFuBpI,EAAE,CAAAqD,gBAAA;IAAFrD,EAAE,CAAAsD,cAAA,mBA+E6F,CAAC;IA/EhGtD,EAAE,CAAAuD,UAAA,mBAAA8E,mDAAA;MAAFrI,EAAE,CAAAyD,aAAA,CAAA2E,IAAA;MAAA,MAAA1E,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CA+EwCF,MAAA,CAAA4E,UAAA,CAAW,CAAC;IAAA,EAAC;IA/EvDtI,EAAE,CAAAkD,UAAA,IAAA8E,6CAAA,8BAgFA,CAAC,IAAAE,4BAAA,gBAC3B,CAAC;IAjFuBlI,EAAE,CAAA8D,YAAA,CAkFvE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAc,MAAA,GAlFoE1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAA+D,SAAA,EAgF9B,CAAC;IAhF2B/D,EAAE,CAAAgE,UAAA,UAAAN,MAAA,CAAAO,oBAgF9B,CAAC;IAhF2BjE,EAAE,CAAA+D,SAAA,CAiF5B,CAAC;IAjFyB/D,EAAE,CAAAgE,UAAA,qBAAAN,MAAA,CAAA6E,gBAiF5B,CAAC;EAAA;AAAA;AA/XpE,MAAMC,OAAO,CAAC;EACVC,UAAU;EACVC,MAAM;EACNC,KAAK;EACLC,EAAE;EACF;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAACC,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,eAAe,GAAG,CAAC,IAAI,CAACD,MAAM,IAAI,EAAE,EAAEE,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAK;MAChED,MAAM,CAACE,IAAI,CAACD,IAAI,CAAC;MACjB,OAAOD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA,IAAIL,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACE,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIM,UAAU;EACV;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACI/B,KAAK;EACL;AACJ;AACA;AACA;EACID,UAAU;EACV;AACJ;AACA;AACA;EACIiC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,gBAAgB,GAAG,IAAIzJ,YAAY,CAAC,CAAC;EACrC0J,OAAO;EACPC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,OAAO;EACPC,GAAG;EACHC,SAAS;EACTpC,YAAY;EACZ7D,oBAAoB;EACpBsE,gBAAgB;EAChB4B,UAAU;EACVC,kBAAkB,GAAG,IAAI;EACzBC,iBAAiB,GAAG,KAAK;EACzBC,2BAA2B,GAAG,IAAI;EAClCtB,eAAe;EACfD,MAAM;EACNlB,eAAe,GAAG3H,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAIqK,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACvB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACwB,MAAM,EAAE;MACvD,IAAI,CAACxB,eAAe,GAAG,CAAC,IAAI,CAACH,KAAK,IAAI,EAAE,EAAEI,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAK;QAC/DD,MAAM,CAACE,IAAI,CAACD,IAAI,CAAC;QACjB,OAAOD,MAAM;MACjB,CAAC,EAAE,EAAE,CAAC;IACV;IACA,OAAO,IAAI,CAACF,eAAe;EAC/B;EACAyB,WAAWA,CAAChC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,EAAE,EAAE;IACvC,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,EAAE,GAAGA,EAAE;EAChB;EACA8B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACR,SAAS,EAAES,OAAO,CAAExB,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACyB,OAAO,CAAC,CAAC;QAClB,KAAK,MAAM;UACP,IAAI,CAAC9C,YAAY,GAAGqB,IAAI,CAAC0B,QAAQ;UACjC;QACJ,KAAK,UAAU;UACX,IAAI,CAACtC,gBAAgB,GAAGY,IAAI,CAAC0B,QAAQ;UACrC;QACJ,KAAK,cAAc;UACf,IAAI,CAAC5G,oBAAoB,GAAGkF,IAAI,CAAC0B,QAAQ;UACzC;QACJ;UACI,IAAI,CAAC/C,YAAY,GAAGqB,IAAI,CAAC0B,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd,IAAIhL,iBAAiB,CAAC,IAAI,CAAC2I,UAAU,CAAC,EAAE;MACpC,IAAI,CAACsC,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACf,UAAU,EAAE;MACjB,IAAI,CAACY,YAAY,CAAC,CAAC;MACnB,IAAI,CAACZ,UAAU,GAAG,KAAK;IAC3B;EACJ;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA1D,QAAQA,CAACyB,IAAI,EAAE;IACX,IAAIA,IAAI,CAACrD,UAAU,EAAE;MACjB,MAAMA,UAAU,GAAGuF,KAAK,CAACC,OAAO,CAACnC,IAAI,CAACrD,UAAU,CAAC,GAAGqD,IAAI,CAACrD,UAAU,GAAG,CAACqD,IAAI,CAACrD,UAAU,CAAC;MACvF,OAAO,IAAI,CAAC4C,MAAM,CAAChB,QAAQ,CAAC,IAAI,CAACgB,MAAM,CAAC6C,aAAa,CAACzF,UAAU,EAAE;QAAE0F,UAAU,EAAE,IAAI,CAAC7C;MAAM,CAAC,CAAC,CAAC8C,QAAQ,CAAC,CAAC,EAAEtC,IAAI,CAACnD,uBAAuB,EAAEtD,KAAK,IAAIyG,IAAI,CAACnD,uBAAuB,IAAI,KAAK,CAAC;IAC3L;IACA,OAAOmD,IAAI,KAAK,IAAI,CAACE,UAAU;EACnC;EACA5E,WAAWA,CAAC0E,IAAI,EAAEuC,IAAI,EAAE;IACpB,OAAOvC,IAAI,GAAG1H,WAAW,CAACkK,YAAY,CAACxC,IAAI,CAACuC,IAAI,CAAC,CAAC,GAAGE,SAAS;EAClE;EACAjE,OAAOA,CAACwB,IAAI,EAAE;IACV,OAAO,OAAOA,IAAI,CAACxB,OAAO,KAAK,UAAU,GAAGwB,IAAI,CAACxB,OAAO,CAAC,CAAC,GAAGwB,IAAI,CAACxB,OAAO,KAAK,KAAK;EACvF;EACAtC,QAAQA,CAAC8D,IAAI,EAAE;IACX,OAAO,OAAOA,IAAI,CAAC9D,QAAQ,KAAK,UAAU,GAAG8D,IAAI,CAAC9D,QAAQ,CAAC,CAAC,GAAG8D,IAAI,CAAC9D,QAAQ;EAChF;EACAgC,eAAeA,CAAC8B,IAAI,EAAE;IAClB,IAAI,CAACtB,eAAe,CAACgE,GAAG,CAAC1C,IAAI,CAAC;EAClC;EACApC,SAASA,CAAC+E,KAAK,EAAE3C,IAAI,EAAE;IACnB,IAAIA,IAAI,CAAC9D,QAAQ,EAAE;MACfyG,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB;IACJ;IACA,IAAI,CAAC5C,IAAI,CAAC6C,GAAG,IAAI,CAAC7C,IAAI,CAACrD,UAAU,EAAE;MAC/BgG,KAAK,CAACC,cAAc,CAAC,CAAC;IAC1B;IACA,IAAI5C,IAAI,CAAC8C,OAAO,EAAE;MACd9C,IAAI,CAAC8C,OAAO,CAAC;QACTC,aAAa,EAAEJ,KAAK;QACpB3C,IAAI,EAAEA;MACV,CAAC,CAAC;IACN;IACA,IAAI,CAACE,UAAU,GAAGF,IAAI;IACtB,IAAI,CAACO,gBAAgB,CAACyC,IAAI,CAAChD,IAAI,CAAC;IAChC,IAAI,CAACgB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACvB,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACAjF,aAAaA,CAAC2E,KAAK,EAAEtJ,KAAK,EAAE2G,IAAI,EAAE;IAC9B,IAAIkD,CAAC,GAAG7J,KAAK;IACb,IAAI8J,YAAY,GAAG,CAAC,CAAC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACvC,OAAO,CAACwC,OAAO,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAG,IAAI,CAACxC,GAAG,CAACuC,OAAO,CAAC,CAAC;IAC/B,QAAQV,KAAK,CAACY,IAAI;MACd,KAAK,YAAY;QACbJ,YAAY,GAAG,IAAI,CAACK,YAAY,CAACF,IAAI,EAAEJ,CAAC,CAAC;QACzCA,CAAC,GAAGC,YAAY,CAAC,GAAG,CAAC;QACrB;MACJ,KAAK,WAAW;QACZA,YAAY,GAAG,IAAI,CAACM,YAAY,CAACH,IAAI,EAAEJ,CAAC,CAAC;QACzCA,CAAC,GAAGC,YAAY,CAAC,GAAG,CAAC;QACrB;MACJ,KAAK,KAAK;QACNA,YAAY,GAAG,IAAI,CAACM,YAAY,CAACH,IAAI,EAAE,IAAI,CAAC5D,KAAK,CAAC2B,MAAM,CAAC;QACzD6B,CAAC,GAAGC,YAAY,CAAC,GAAG,CAAC;QACrBR,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,MAAM;QACPO,YAAY,GAAG,IAAI,CAACK,YAAY,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC;QAC1CJ,CAAC,GAAGC,YAAY,CAAC,GAAG,CAAC;QACrBR,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,OAAO;MACZ,KAAK,OAAO;QACR,IAAI,CAAChF,SAAS,CAAC+E,KAAK,EAAE3C,IAAI,CAAC;QAC3B;MACJ,KAAK,KAAK;QACN,IAAI,CAAC0D,YAAY,CAACN,QAAQ,CAAC;QAC3B;MACJ;QACI;IACR;IACA,IAAIA,QAAQ,CAACF,CAAC,CAAC,IAAIE,QAAQ,CAAC/J,KAAK,CAAC,EAAE;MAChC+J,QAAQ,CAAC/J,KAAK,CAAC,CAACsK,aAAa,CAACC,QAAQ,GAAG,IAAI;MAC7CR,QAAQ,CAACF,CAAC,CAAC,CAACS,aAAa,CAACC,QAAQ,GAAG,GAAG;MACxCR,QAAQ,CAACF,CAAC,CAAC,CAACS,aAAa,CAACE,KAAK,CAAC,CAAC;IACrC;IACA,IAAI,CAACpE,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACAS,YAAYA,CAACN,QAAQ,EAAE;IACnBA,QAAQ,CAAC5B,OAAO,CAAExB,IAAI,IAAK;MACvBA,IAAI,CAAC2D,aAAa,CAACC,QAAQ,GAAG7L,UAAU,CAAC+L,YAAY,CAAC9D,IAAI,CAAC2D,aAAa,CAACI,aAAa,EAAE,kBAAkB,CAAC,GAAG,GAAG,GAAG,IAAI;IAC5H,CAAC,CAAC;EACN;EACAP,YAAYA,CAACQ,KAAK,EAAE3K,KAAK,EAAE;IACvB,IAAI6J,CAAC,GAAG7J,KAAK,GAAG,CAAC;IACjB,IAAI6J,CAAC,IAAIc,KAAK,CAAC3C,MAAM,EAAE;MACnB,OAAO;QAAE4C,QAAQ,EAAED,KAAK,CAACA,KAAK,CAAC3C,MAAM,CAAC;QAAE6B,CAAC,EAAEc,KAAK,CAAC3C;MAAO,CAAC;IAC7D;IACA,IAAI4C,QAAQ,GAAGD,KAAK,CAACd,CAAC,CAAC;IACvB,IAAIe,QAAQ,EACR,OAAOlM,UAAU,CAAC+L,YAAY,CAACG,QAAQ,CAACN,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACH,YAAY,CAACQ,KAAK,EAAEd,CAAC,CAAC,GAAG;MAAEe,QAAQ,EAAEA,QAAQ,CAACN,aAAa;MAAET;IAAE,CAAC,CAAC,KAElJ,OAAO,IAAI;EACnB;EACAO,YAAYA,CAACO,KAAK,EAAE3K,KAAK,EAAE;IACvB,IAAI6J,CAAC,GAAG7J,KAAK,GAAG,CAAC;IACjB,IAAI6J,CAAC,GAAG,CAAC,EAAE;MACP,OAAO;QAAEgB,QAAQ,EAAEF,KAAK,CAAC,CAAC,CAAC;QAAEd,CAAC,EAAE;MAAE,CAAC;IACvC;IACA,IAAIgB,QAAQ,GAAGF,KAAK,CAACd,CAAC,CAAC;IACvB,IAAIgB,QAAQ,EACR,OAAOnM,UAAU,CAAC+L,YAAY,CAACI,QAAQ,CAACP,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACF,YAAY,CAACO,KAAK,EAAEd,CAAC,CAAC,GAAG;MAAEgB,QAAQ,EAAEA,QAAQ,CAACP,aAAa;MAAET;IAAE,CAAC,CAAC,KAElJ,OAAO,IAAI;EACnB;EACAtB,YAAYA,CAAA,EAAG;IACX,MAAMuC,SAAS,GAAGpM,UAAU,CAACqM,UAAU,CAAC,IAAI,CAAC3D,MAAM,EAAEkD,aAAa,EAAE,gBAAgB,CAAC;IACrF,IAAIQ,SAAS,EAAE;MACX,IAAI,CAACzD,MAAM,CAACiD,aAAa,CAACtF,KAAK,CAACgG,KAAK,GAAGtM,UAAU,CAACuM,QAAQ,CAACH,SAAS,CAAC,GAAG,IAAI;MAC7E,IAAI,CAACzD,MAAM,CAACiD,aAAa,CAACtF,KAAK,CAACkG,IAAI,GAAGxM,UAAU,CAACyM,SAAS,CAACL,SAAS,CAAC,CAACI,IAAI,GAAGxM,UAAU,CAACyM,SAAS,CAAC,IAAI,CAAC/D,MAAM,EAAEkD,aAAa,CAAC,CAACY,IAAI,GAAG,IAAI;IAC9I;EACJ;EACAE,sBAAsBA,CAAA,EAAG;IACrB,OAAO,CAAC,IAAI,CAAC9D,OAAO,EAAEgD,aAAa,EAAE,IAAI,CAAC/C,OAAO,EAAE+C,aAAa,CAAC,CAAC7D,MAAM,CAAC,CAAC4E,GAAG,EAAEC,EAAE,KAAMA,EAAE,GAAGD,GAAG,GAAG3M,UAAU,CAACuM,QAAQ,CAACK,EAAE,CAAC,GAAGD,GAAI,EAAE,CAAC,CAAC;EACxI;EACAE,iBAAiBA,CAAA,EAAG;IAChB,MAAMpE,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEmD,aAAa;IAC3C,MAAM;MAAEkB,UAAU;MAAEC;IAAY,CAAC,GAAGtE,OAAO;IAC3C,MAAM6D,KAAK,GAAGtM,UAAU,CAACuM,QAAQ,CAAC9D,OAAO,CAAC;IAC1C,IAAI,CAACS,kBAAkB,GAAG4D,UAAU,KAAK,CAAC;IAC1C,IAAI,CAAC3D,iBAAiB,GAAG6D,QAAQ,CAACF,UAAU,CAAC,KAAKC,WAAW,GAAGT,KAAK;EACzE;EACAW,eAAeA,CAAC3L,KAAK,EAAE;IACnB,MAAM8K,SAAS,GAAG,IAAI,CAAC1D,MAAM,EAAEkD,aAAa,CAACsB,QAAQ,CAAC5L,KAAK,CAAC;IAC5D,IAAI,CAAC8K,SAAS,EAAE;MACZ;IACJ;IACAA,SAAS,CAACe,cAAc,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAC,CAAC;EACpE;EACAC,QAAQA,CAAC1C,KAAK,EAAE;IACZ,IAAI,CAACxC,UAAU,IAAI,IAAI,CAACyE,iBAAiB,CAAC,CAAC;IAC3CjC,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B;EACAlI,WAAWA,CAAA,EAAG;IACV,MAAM8F,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEmD,aAAa;IAC3C,MAAMU,KAAK,GAAGtM,UAAU,CAACuM,QAAQ,CAAC9D,OAAO,CAAC,GAAG,IAAI,CAACiE,sBAAsB,CAAC,CAAC;IAC1E,MAAMa,GAAG,GAAG9E,OAAO,CAACqE,UAAU,GAAGR,KAAK;IACtC7D,OAAO,CAACqE,UAAU,GAAGS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC3C;EACAnG,UAAUA,CAAA,EAAG;IACT,MAAMqB,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEmD,aAAa;IAC3C,MAAMU,KAAK,GAAGtM,UAAU,CAACuM,QAAQ,CAAC9D,OAAO,CAAC,GAAG,IAAI,CAACiE,sBAAsB,CAAC,CAAC;IAC1E,MAAMa,GAAG,GAAG9E,OAAO,CAACqE,UAAU,GAAGR,KAAK;IACtC,MAAMkB,OAAO,GAAG/E,OAAO,CAACsE,WAAW,GAAGT,KAAK;IAC3C7D,OAAO,CAACqE,UAAU,GAAGS,GAAG,IAAIC,OAAO,GAAGA,OAAO,GAAGD,GAAG;EACvD;EACAzD,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC,IAAI,CAAC1B,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAAC8B,sBAAsB,CAAC,CAAC;IAC7B;IACA,IAAI,CAACd,2BAA2B,GAAGqE,UAAU,CAAC,MAAM;MAChD,MAAMtF,UAAU,GAAG,IAAI,CAACR,KAAK,CAAC+F,SAAS,CAAEC,QAAQ,IAAK,IAAI,CAACnH,QAAQ,CAACmH,QAAQ,CAAC,CAAC;MAC9E,IAAIxF,UAAU,KAAK,CAAC,CAAC,EAAE;QACnB,IAAI,CAAC8E,eAAe,CAAC9E,UAAU,CAAC;MACpC;IACJ,CAAC,CAAC;EACN;EACA+B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACd,2BAA2B,EAAE;MAClCwE,YAAY,CAAC,IAAI,CAACxE,2BAA2B,CAAC;MAC9C,IAAI,CAACA,2BAA2B,GAAG,IAAI;IAC3C;EACJ;EACAW,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC3B,UAAU,EAAE;MACjB;MACA;MACAyF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAClB,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACnF,EAAE,CAACwD,YAAY,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;EACJ;EACA,OAAO8C,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5G,OAAO,EAAjBxI,EAAE,CAAAqP,iBAAA,CAAiClP,WAAW,GAA9CH,EAAE,CAAAqP,iBAAA,CAAyDvO,EAAE,CAACwO,MAAM,GAApEtP,EAAE,CAAAqP,iBAAA,CAA+EvO,EAAE,CAACyO,cAAc,GAAlGvP,EAAE,CAAAqP,iBAAA,CAA6GrP,EAAE,CAACwP,iBAAiB;EAAA;EAC5N,OAAOC,IAAI,kBAD8EzP,EAAE,CAAA0P,iBAAA;IAAAC,IAAA,EACJnH,OAAO;IAAAoH,SAAA;IAAAC,cAAA,WAAAC,uBAAAlN,EAAA,EAAAC,GAAA,EAAAkN,QAAA;MAAA,IAAAnN,EAAA;QADL5C,EAAE,CAAAgQ,cAAA,CAAAD,QAAA,EAC4W/O,aAAa;MAAA;MAAA,IAAA4B,EAAA;QAAA,IAAAqN,EAAA;QAD3XjQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAqH,SAAA,GAAA+F,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,cAAAzN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5C,EAAE,CAAAsQ,WAAA,CAAA5O,GAAA;QAAF1B,EAAE,CAAAsQ,WAAA,CAAA3O,GAAA;QAAF3B,EAAE,CAAAsQ,WAAA,CAAA1O,GAAA;QAAF5B,EAAE,CAAAsQ,WAAA,CAAAzO,GAAA;QAAF7B,EAAE,CAAAsQ,WAAA,CAAAxO,GAAA;QAAF9B,EAAE,CAAAsQ,WAAA,CAAAvO,GAAA;QAAF/B,EAAE,CAAAsQ,WAAA,CAAAtO,GAAA;MAAA;MAAA,IAAAY,EAAA;QAAA,IAAAqN,EAAA;QAAFjQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAA8G,OAAA,GAAAsG,EAAA,CAAAM,KAAA;QAAFvQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAA+G,MAAA,GAAAqG,EAAA,CAAAM,KAAA;QAAFvQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAgH,MAAA,GAAAoG,EAAA,CAAAM,KAAA;QAAFvQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAiH,OAAA,GAAAmG,EAAA,CAAAM,KAAA;QAAFvQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAkH,OAAA,GAAAkG,EAAA,CAAAM,KAAA;QAAFvQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAmH,OAAA,GAAAiG,EAAA;QAAFjQ,EAAE,CAAAkQ,cAAA,CAAAD,EAAA,GAAFjQ,EAAE,CAAAmQ,WAAA,QAAAtN,GAAA,CAAAoH,GAAA,GAAAgG,EAAA;MAAA;IAAA;IAAAO,SAAA;IAAAC,MAAA;MAAA5H,KAAA;MAAAQ,UAAA;MAAAC,UAAA;MAAAC,KAAA;MAAA/B,KAAA;MAAAD,UAAA;MAAAiC,SAAA;MAAAC,cAAA;IAAA;IAAAiH,OAAA;MAAAhH,gBAAA;IAAA;IAAAiH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAhG,QAAA,WAAAiG,iBAAAlO,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAmO,GAAA,GAAF/Q,EAAE,CAAAqD,gBAAA;QAAFrD,EAAE,CAAAsD,cAAA,YAEsC,CAAC,aACtF,CAAC;QAH4CtD,EAAE,CAAAkD,UAAA,IAAAC,yBAAA,oBAI+F,CAAC;QAJlGnD,EAAE,CAAAsD,cAAA,gBAQR,CAAC;QARKtD,EAAE,CAAAuD,UAAA,oBAAAyN,uCAAAlK,MAAA;UAAF9G,EAAE,CAAAyD,aAAA,CAAAsN,GAAA;UAAA,OAAF/Q,EAAE,CAAA4D,WAAA,CAQzBf,GAAA,CAAA2L,QAAA,CAAA1H,MAAe,CAAC;QAAA,EAAC;QARM9G,EAAE,CAAAsD,cAAA,eASmD,CAAC;QATtDtD,EAAE,CAAAkD,UAAA,IAAAyD,qBAAA,iBAwBvE,CAAC;QAxBoE3G,EAAE,CAAA8C,SAAA,eA4EjB,CAAC;QA5Ec9C,EAAE,CAAA8D,YAAA,CA6EvE,CAAC,CACJ,CAAC;QA9EuE9D,EAAE,CAAAkD,UAAA,KAAAiF,0BAAA,oBA+E6F,CAAC;QA/EhGnI,EAAE,CAAA8D,YAAA,CAmF9E,CAAC,CACL,CAAC;MAAA;MAAA,IAAAlB,EAAA;QApF+E5C,EAAE,CAAAsH,UAAA,CAAAzE,GAAA,CAAA0E,UAEqC,CAAC;QAFxCvH,EAAE,CAAAgE,UAAA,YAAFhE,EAAE,CAAAiR,eAAA,IAAAhP,GAAA,EAAAY,GAAA,CAAAyG,UAAA,CAEF,CAAC,YAAAzG,GAAA,CAAA2E,KAAiB,CAAC;QAFnBxH,EAAE,CAAA+D,SAAA,EAIhC,CAAC;QAJ6B/D,EAAE,CAAAgE,UAAA,SAAAnB,GAAA,CAAAyG,UAAA,KAAAzG,GAAA,CAAAuH,kBAIhC,CAAC;QAJ6BpK,EAAE,CAAA+D,SAAA,EASoB,CAAC;QATvB/D,EAAE,CAAA+C,WAAA,oBAAAF,GAAA,CAAA4G,cAAA,gBAAA5G,GAAA,CAAA2G,SAAA;QAAFxJ,EAAE,CAAA+D,SAAA,EAYhC,CAAC;QAZ6B/D,EAAE,CAAAgE,UAAA,YAAAnB,GAAA,CAAA0H,cAYhC,CAAC;QAZ6BvK,EAAE,CAAA+D,SAAA,EA+EjC,CAAC;QA/E8B/D,EAAE,CAAAgE,UAAA,SAAAnB,GAAA,CAAAyG,UAAA,KAAAzG,GAAA,CAAAwH,iBA+EjC,CAAC;MAAA;IAAA;IAAA6G,YAAA,EAAAA,CAAA,MAMg/BrR,EAAE,CAACsR,OAAO,EAAyGtR,EAAE,CAACuR,OAAO,EAAwIvR,EAAE,CAACwR,IAAI,EAAkHxR,EAAE,CAACyR,gBAAgB,EAAyKzR,EAAE,CAAC0R,OAAO,EAAgGzQ,EAAE,CAAC0Q,UAAU,EAAiP1Q,EAAE,CAAC2Q,gBAAgB,EAAmOpQ,EAAE,CAACqQ,MAAM,EAA2EnQ,EAAE,CAACoQ,OAAO,EAAkWxQ,eAAe,EAAiFC,gBAAgB;IAAAwQ,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACvvF;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvF6F/R,EAAE,CAAAgS,iBAAA,CAuFJxJ,OAAO,EAAc,CAAC;IACrGmH,IAAI,EAAEvP,SAAS;IACf6R,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAErH,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEiH,eAAe,EAAEzR,uBAAuB,CAAC8R,MAAM;MAAEN,aAAa,EAAEvR,iBAAiB,CAAC8R,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,88BAA88B;IAAE,CAAC;EACz+B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjC,IAAI,EAAE/D,SAAS;IAAE2G,UAAU,EAAE,CAAC;MAC/C5C,IAAI,EAAEpP,MAAM;MACZ0R,IAAI,EAAE,CAAC9R,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEwP,IAAI,EAAE7O,EAAE,CAACwO;EAAO,CAAC,EAAE;IAAEK,IAAI,EAAE7O,EAAE,CAACyO;EAAe,CAAC,EAAE;IAAEI,IAAI,EAAE3P,EAAE,CAACwP;EAAkB,CAAC,CAAC,EAAkB;IAAE3G,KAAK,EAAE,CAAC;MACnH8G,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAE6I,UAAU,EAAE,CAAC;MACbsG,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAE8I,UAAU,EAAE,CAAC;MACbqG,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAE+I,KAAK,EAAE,CAAC;MACRoG,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAEgH,KAAK,EAAE,CAAC;MACRmI,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAE+G,UAAU,EAAE,CAAC;MACboI,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAEgJ,SAAS,EAAE,CAAC;MACZmG,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAEiJ,cAAc,EAAE,CAAC;MACjBkG,IAAI,EAAEnP;IACV,CAAC,CAAC;IAAEkJ,gBAAgB,EAAE,CAAC;MACnBiG,IAAI,EAAElP;IACV,CAAC,CAAC;IAAEkJ,OAAO,EAAE,CAAC;MACVgG,IAAI,EAAEjP,SAAS;MACfuR,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAErI,MAAM,EAAE,CAAC;MACT+F,IAAI,EAAEjP,SAAS;MACfuR,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEpI,MAAM,EAAE,CAAC;MACT8F,IAAI,EAAEjP,SAAS;MACfuR,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEnI,OAAO,EAAE,CAAC;MACV6F,IAAI,EAAEjP,SAAS;MACfuR,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAElI,OAAO,EAAE,CAAC;MACV4F,IAAI,EAAEjP,SAAS;MACfuR,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEjI,OAAO,EAAE,CAAC;MACV2F,IAAI,EAAEhP,YAAY;MAClBsR,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEhI,GAAG,EAAE,CAAC;MACN0F,IAAI,EAAEhP,YAAY;MAClBsR,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAE/H,SAAS,EAAE,CAAC;MACZyF,IAAI,EAAE/O,eAAe;MACrBqR,IAAI,EAAE,CAACjR,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwR,aAAa,CAAC;EAChB,OAAOtD,IAAI,YAAAuD,sBAAArD,CAAA;IAAA,YAAAA,CAAA,IAAwFoD,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAhO8E1S,EAAE,CAAA2S,gBAAA;IAAAhD,IAAA,EAgOS6C;EAAa;EACjH,OAAOI,IAAI,kBAjO8E5S,EAAE,CAAA6S,gBAAA;IAAAC,OAAA,GAiOkC/S,YAAY,EAAEgB,YAAY,EAAEE,YAAY,EAAEK,YAAY,EAAEE,aAAa,EAAEL,eAAe,EAAEC,gBAAgB,EAAEL,YAAY,EAAEE,YAAY,EAAEO,aAAa;EAAA;AACpR;AACA;EAAA,QAAAuQ,SAAA,oBAAAA,SAAA,KAnO6F/R,EAAE,CAAAgS,iBAAA,CAmOJQ,aAAa,EAAc,CAAC;IAC3G7C,IAAI,EAAE9O,QAAQ;IACdoR,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAAC/S,YAAY,EAAEgB,YAAY,EAAEE,YAAY,EAAEK,YAAY,EAAEE,aAAa,EAAEL,eAAe,EAAEC,gBAAgB,CAAC;MACnH2R,OAAO,EAAE,CAACvK,OAAO,EAAEzH,YAAY,EAAEE,YAAY,EAAEO,aAAa,CAAC;MAC7DwR,YAAY,EAAE,CAACxK,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,OAAO,EAAEgK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}