{"ast": null, "code": "import { environment } from '../../environments/environment';\nexport const ENDPOINT = {\n  NODE: environment.apiEndpoint,\n  CMS: environment.cmsApiEndpoint\n};\nexport const ApiConstant = {\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\n  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\n  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\n  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\n  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\n  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\n  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\n  USERS: `${environment.apiEndpoint}/users`,\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\n  PARTNERS: `${environment.apiEndpoint}/api/business-partners`,\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\n  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\n  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\n  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\n  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\n  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\n  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\n  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\n  CREDIT_MEMO: `${environment.apiEndpoint}/api/credit-memos/vendor`,\n  IMAGES: `${environment.apiEndpoint}/media`,\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\n  SALES_ORDER: `${environment.apiEndpoint}/api/sales-orders`,\n  SALES_ORDER_GENERIC: `${environment.apiEndpoint}/api/sales-orders/generic`,\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\n  CARTS: `${environment.apiEndpoint}/carts`,\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\n  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\n  SETTINGS: `${environment.apiEndpoint}/settings`,\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\n  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\n  TICKETS: `${environment.apiEndpoint}/tickets`,\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\n  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\n  SALES_QUOTE_GENERIC: `${environment.apiEndpoint}/api/sales-quotes/generic`,\n  QUOTE: `${environment.apiEndpoint}/quote-statuses`,\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\n  RETURN_ORDER: `${environment.apiEndpoint}/api/return-orders`,\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\n  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  BANNER: `${environment.apiEndpoint}/banner`,\n  BUSINESS_PARTNER: `${environment.apiEndpoint}/api/business-partners`\n};\nexport const CMS_APIContstant = {\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\n  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\n  TICKET: `${environment.cmsApiEndpoint}/api/tickets`,\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\n  FILE_UPLOAD: `${environment.cmsApiEndpoint}/api/import-file-states`,\n  FILE_EXPORT: `${environment.cmsApiEndpoint}/api/export/import-file-logs`,\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\n  PARTNERS_CONTACTS: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\n  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\n  CONFIG_DATA: `${environment.cmsApiEndpoint}/api/configurations`,\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\n  ACCOUNT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\n  PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner`,\n  REGISTER_PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner/registration`,\n  PROSPECT_ADDRESS_REGISTER: `${environment.cmsApiEndpoint}/api/business-partner-address/registration`,\n  PROSPECT_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-address`,\n  CREATE_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/registration`,\n  EXISTING_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/mapping`,\n  PROSPECT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact`,\n  CRM_NOTE: `${environment.cmsApiEndpoint}/api/crm-notes`,\n  CRM_ACTIVITY: `${environment.cmsApiEndpoint}/api/crm-activities`,\n  CRM_ACTIVITY_PHONE_CALL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-activity/registration/phone-call`,\n  CRM_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-item/registration`,\n  CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/activity-registration`,\n  CRM_INVOLVED_PARTIES: `${environment.cmsApiEndpoint}/api/crm-involved-parties`,\n  CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-items`,\n  CRM_OPPORTUNITY_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity/registration`,\n  CRM_OPPORTUNITY_FOLLOWUP: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-documents`,\n  CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/opportunity-registration`,\n  CRM_OPPORTUNITY_CONTACT: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-parties`,\n  CRM_OPPORTUNITY_CONTACT_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-party/registration`,\n  CRM_OPPORTUNITY: `${environment.cmsApiEndpoint}/api/crm-opportunities`,\n  CRM_OPPORTUNITY_SALES_TEAM: `${environment.cmsApiEndpoint}/api/crm-opportunity-sales-team-parties`,\n  CRM_ORGANIZATIONAL: `${environment.cmsApiEndpoint}/api/crm-organisational-units`,\n  CRM_ORGANIZATIONAL_FUNCTIONS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-functions`,\n  CRM_ORGANIZATIONAL_EMPLOYEES: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-employees`,\n  CRM_ORGANIZATIONAL_MANAGERS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-managers`,\n  CRM_COMPETITORS: `${environment.cmsApiEndpoint}/api/crm-competitors`,\n  REGISTER_PROSPECT_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function/registration`,\n  SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function`,\n  BP_EXTENSIONS: `${environment.cmsApiEndpoint}/api/business-partner-extensions`,\n  DELETE_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\n  MARKETING_ATTRIBUTES: `${environment.cmsApiEndpoint}/api/bp-marketing-attributes`,\n  CONTENT_CRM: `${environment.cmsApiEndpoint}/api/content-crms`\n};\nexport const AppConstant = {\n  SESSION_TIMEOUT: 3600 * 1000,\n  // in MS\n  PRODUCT_IMAGE_FALLBACK: 'assets/layout/images/demo-product.jpeg'\n};\nexport const Permission = {\n  VIEW_INVOICE: 'P0003'\n};", "map": {"version": 3, "names": ["environment", "ENDPOINT", "NODE", "apiEndpoint", "CMS", "cmsApiEndpoint", "ApiConstant", "FETCH_TOKEN", "ORDER_HISTORY", "GET_ALL_PRODUCTS", "GET_PRODUCT_IMAGES", "PRODUCT_IMAGE", "PRODUCT_PLANT", "PRODUCT_DESCRIPTION", "USERS", "ADMIN_USERS", "SINGOUT", "PARTNERS", "CUSTOMER_COMPANIES", "CUSTOMER_TEXT", "CUSTOMER_PARTNER_FUNCTION", "CUSTOMER_SALES_AREA", "GET_ORDER_STATUSES", "GET_INVOICE_FORM_TYPES", "RELATIONSHIP_TYPES", "CONDITIONS", "GET_CATALOGS", "PRODUCT_CATEGORIES", "PRODUCT_CATALOGS", "GET_INVOICE_STATUSES", "GET_SALE_ORDER_STATUSES", "GET_INVOICE_TYPES", "GET_ORDER_TYPES", "ORDER_DETAILS", "SCHEDULED_ORDER_DETAILS", "INVOICE", "CREDIT_MEMO", "IMAGES", "SALES_ORDER_SIMULATION", "SALES_ORDER_CREATION", "SALES_ORDER", "SALES_ORDER_GENERIC", "ADD_SHIPPING_ADDRESS", "CARTS", "GET_MATERIAL_STOCK", "GET_SALES_PRICE", "CUSTOMERS", "SETTINGS", "COMPANY_LOGO", "SIMILAR_PRODUCTS", "TICKET_LIST", "TICKETS", "TICKET_STATUSES", "SALES_QUOTE_CREATION", "SALES_QUOTE", "SALES_QUOTE_GENERIC", "QUOTE", "RETURN_STATUS", "RETURN_REASON", "REFUND_PROGRESS", "RETURN_ORDER", "NOTIFICATION", "PRODUCT_REGISTER", "BULK_UPLOAD_STATUS", "CUSTOMER_TEXT_DESCR", "SALES_ORDER_SCHEDULER_CREATION", "SALES_ORDER_SCHEDULER", "BANNER", "BUSINESS_PARTNER", "CMS_APIContstant", "USER_ROLES", "STORE_DESIGN", "TICKET", "FILE_UPLOAD", "FILE_EXPORT", "MAIN_MENU_API_DETAILS", "PARTNERS_ADDRESS", "PARTNERS_CONTACTS", "SINGIN", "USER_DETAILS", "USER_CART", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "USER_PERMISSIONS", "GET_CATEGORIES", "CONFIG_DATA", "PRODUCT_MDEIA", "ACCOUNT_CONTACT", "PROSPECTS", "REGISTER_PROSPECTS", "PROSPECT_ADDRESS_REGISTER", "PROSPECT_ADDRESS", "CREATE_CONTACT", "EXISTING_CONTACT", "PROSPECT_CONTACT", "CRM_NOTE", "CRM_ACTIVITY", "CRM_ACTIVITY_PHONE_CALL_REGISTRATION", "CRM_ACTIVITY_FOLLOW_UP_REGISTRATION", "CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION", "CRM_INVOLVED_PARTIES", "CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS", "CRM_OPPORTUNITY_REGISTRATION", "CRM_OPPORTUNITY_FOLLOWUP", "CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION", "CRM_OPPORTUNITY_CONTACT", "CRM_OPPORTUNITY_CONTACT_REGISTRATION", "CRM_OPPORTUNITY", "CRM_OPPORTUNITY_SALES_TEAM", "CRM_ORGANIZATIONAL", "CRM_ORGANIZATIONAL_FUNCTIONS", "CRM_ORGANIZATIONAL_EMPLOYEES", "CRM_ORGANIZATIONAL_MANAGERS", "CRM_COMPETITORS", "REGISTER_PROSPECT_SALES_TEAM", "SALES_TEAM", "BP_EXTENSIONS", "DELETE_SALES_TEAM", "MARKETING_ATTRIBUTES", "CONTENT_CRM", "AppConstant", "SESSION_TIMEOUT", "PRODUCT_IMAGE_FALLBACK", "Permission", "VIEW_INVOICE"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\constants\\api.constants.ts"], "sourcesContent": ["import { environment } from '../../environments/environment';\r\n\r\nexport const ENDPOINT = {\r\n  NODE: environment.apiEndpoint,\r\n  CMS: environment.cmsApiEndpoint,\r\n};\r\n\r\nexport const ApiConstant = {\r\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\r\n  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\r\n  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\r\n  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\r\n  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\r\n  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\r\n  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\r\n  USERS: `${environment.apiEndpoint}/users`,\r\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\r\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\r\n  PARTNERS: `${environment.apiEndpoint}/api/business-partners`,\r\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\r\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\r\n  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\r\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\r\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\r\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\r\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\r\n  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\r\n  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\r\n  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\r\n  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\r\n  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\r\n  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\r\n  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\r\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\r\n  CREDIT_MEMO: `${environment.apiEndpoint}/api/credit-memos/vendor`,\r\n  IMAGES: `${environment.apiEndpoint}/media`,\r\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\r\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\r\n  SALES_ORDER: `${environment.apiEndpoint}/api/sales-orders`,\r\n  SALES_ORDER_GENERIC: `${environment.apiEndpoint}/api/sales-orders/generic`,\r\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\r\n  CARTS: `${environment.apiEndpoint}/carts`,\r\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\r\n  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\r\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\r\n  SETTINGS: `${environment.apiEndpoint}/settings`,\r\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\r\n  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\r\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\r\n  TICKETS: `${environment.apiEndpoint}/tickets`,\r\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\r\n  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\r\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\r\n  SALES_QUOTE_GENERIC: `${environment.apiEndpoint}/api/sales-quotes/generic`,\r\n  QUOTE: `${environment.apiEndpoint}/quote-statuses`,\r\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\r\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\r\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\r\n  RETURN_ORDER: `${environment.apiEndpoint}/api/return-orders`,\r\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\r\n  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\r\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\r\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\r\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  BANNER: `${environment.apiEndpoint}/banner`,\r\n  BUSINESS_PARTNER: `${environment.apiEndpoint}/api/business-partners`,\r\n};\r\n\r\nexport const CMS_APIContstant = {\r\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\r\n  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\r\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\r\n  TICKET: `${environment.cmsApiEndpoint}/api/tickets`,\r\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\r\n  FILE_UPLOAD: `${environment.cmsApiEndpoint}/api/import-file-states`,\r\n  FILE_EXPORT: `${environment.cmsApiEndpoint}/api/export/import-file-logs`,\r\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\r\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\r\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\r\n  PARTNERS_CONTACTS: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\r\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\r\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\r\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\r\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\r\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\r\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\r\n  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\r\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\r\n  CONFIG_DATA: `${environment.cmsApiEndpoint}/api/configurations`,\r\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\r\n  ACCOUNT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\r\n  PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner`,\r\n  REGISTER_PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner/registration`,\r\n  PROSPECT_ADDRESS_REGISTER: `${environment.cmsApiEndpoint}/api/business-partner-address/registration`,\r\n  PROSPECT_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-address`,\r\n  CREATE_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/registration`,\r\n  EXISTING_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/mapping`,\r\n  PROSPECT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact`,\r\n  CRM_NOTE: `${environment.cmsApiEndpoint}/api/crm-notes`,\r\n  CRM_ACTIVITY: `${environment.cmsApiEndpoint}/api/crm-activities`,\r\n  CRM_ACTIVITY_PHONE_CALL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-activity/registration/phone-call`,\r\n  CRM_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-item/registration`,\r\n  CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/activity-registration`,\r\n  CRM_INVOLVED_PARTIES: `${environment.cmsApiEndpoint}/api/crm-involved-parties`,\r\n  CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-items`,\r\n  CRM_OPPORTUNITY_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity/registration`,\r\n  CRM_OPPORTUNITY_FOLLOWUP: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-documents`,\r\n  CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/opportunity-registration`,\r\n  CRM_OPPORTUNITY_CONTACT: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-parties`,\r\n  CRM_OPPORTUNITY_CONTACT_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-party/registration`,\r\n  CRM_OPPORTUNITY: `${environment.cmsApiEndpoint}/api/crm-opportunities`,\r\n  CRM_OPPORTUNITY_SALES_TEAM: `${environment.cmsApiEndpoint}/api/crm-opportunity-sales-team-parties`,\r\n  CRM_ORGANIZATIONAL: `${environment.cmsApiEndpoint}/api/crm-organisational-units`,\r\n  CRM_ORGANIZATIONAL_FUNCTIONS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-functions`,\r\n  CRM_ORGANIZATIONAL_EMPLOYEES: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-employees`,\r\n  CRM_ORGANIZATIONAL_MANAGERS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-managers`,\r\n  CRM_COMPETITORS: `${environment.cmsApiEndpoint}/api/crm-competitors`,\r\n  REGISTER_PROSPECT_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function/registration`,\r\n  SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function`,\r\n  BP_EXTENSIONS: `${environment.cmsApiEndpoint}/api/business-partner-extensions`,\r\n  DELETE_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\r\n  MARKETING_ATTRIBUTES: `${environment.cmsApiEndpoint}/api/bp-marketing-attributes`,\r\n  CONTENT_CRM: `${environment.cmsApiEndpoint}/api/content-crms`,\r\n};\r\n\r\nexport const AppConstant = {\r\n  SESSION_TIMEOUT: 3600 * 1000, // in MS\r\n  PRODUCT_IMAGE_FALLBACK: 'assets/layout/images/demo-product.jpeg',\r\n};\r\n\r\nexport const Permission = {\r\n  VIEW_INVOICE: 'P0003',\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gCAAgC;AAE5D,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAEF,WAAW,CAACG,WAAW;EAC7BC,GAAG,EAAEJ,WAAW,CAACK;CAClB;AAED,OAAO,MAAMC,WAAW,GAAG;EACzBC,WAAW,EAAE,GAAGP,WAAW,CAACG,WAAW,iBAAiB;EACxDK,aAAa,EAAE,GAAGR,WAAW,CAACG,WAAW,SAAS;EAClDM,gBAAgB,EAAE,GAAGT,WAAW,CAACG,WAAW,WAAW;EACvDO,kBAAkB,EAAE,GAAGV,WAAW,CAACG,WAAW,sBAAsB;EACpEQ,aAAa,EAAE,GAAGX,WAAW,CAACG,WAAW,QAAQ;EACjDS,aAAa,EAAE,GAAGZ,WAAW,CAACG,WAAW,iBAAiB;EAC1DU,mBAAmB,EAAE,GAAGb,WAAW,CAACG,WAAW,uBAAuB;EACtEW,KAAK,EAAE,GAAGd,WAAW,CAACG,WAAW,QAAQ;EACzCY,WAAW,EAAE,GAAGf,WAAW,CAACG,WAAW,kBAAkB;EACzDa,OAAO,EAAE,GAAGhB,WAAW,CAACG,WAAW,cAAc;EACjDc,QAAQ,EAAE,GAAGjB,WAAW,CAACG,WAAW,wBAAwB;EAC5De,kBAAkB,EAAE,GAAGlB,WAAW,CAACG,WAAW,qBAAqB;EACnEgB,aAAa,EAAE,GAAGnB,WAAW,CAACG,WAAW,gBAAgB;EACzDiB,yBAAyB,EAAE,GAAGpB,WAAW,CAACG,WAAW,6BAA6B;EAClFkB,mBAAmB,EAAE,GAAGrB,WAAW,CAACG,WAAW,uBAAuB;EACtEmB,kBAAkB,EAAE,GAAGtB,WAAW,CAACG,WAAW,sBAAsB;EACpEoB,sBAAsB,EAAE,GAAGvB,WAAW,CAACG,WAAW,qBAAqB;EACvEqB,kBAAkB,EAAE,GAAGxB,WAAW,CAACG,WAAW,qBAAqB;EACnEsB,UAAU,EAAE,GAAGzB,WAAW,CAACG,WAAW,aAAa;EACnDuB,YAAY,EAAE,GAAG1B,WAAW,CAACG,WAAW,mBAAmB;EAC3DwB,kBAAkB,EAAE,GAAG3B,WAAW,CAACG,WAAW,mCAAmC;EACjFyB,gBAAgB,EAAE,GAAG5B,WAAW,CAACG,WAAW,iCAAiC;EAC7E0B,oBAAoB,EAAE,GAAG7B,WAAW,CAACG,WAAW,mBAAmB;EACnE2B,uBAAuB,EAAE,GAAG9B,WAAW,CAACG,WAAW,sBAAsB;EACzE4B,iBAAiB,EAAE,GAAG/B,WAAW,CAACG,WAAW,gBAAgB;EAC7D6B,eAAe,EAAE,GAAGhC,WAAW,CAACG,WAAW,cAAc;EACzD8B,aAAa,EAAE,GAAGjC,WAAW,CAACG,WAAW,SAAS;EAClD+B,uBAAuB,EAAE,GAAGlC,WAAW,CAACG,WAAW,yBAAyB;EAC5EgC,OAAO,EAAE,GAAGnC,WAAW,CAACG,WAAW,eAAe;EAClDiC,WAAW,EAAE,GAAGpC,WAAW,CAACG,WAAW,0BAA0B;EACjEkC,MAAM,EAAE,GAAGrC,WAAW,CAACG,WAAW,QAAQ;EAC1CmC,sBAAsB,EAAE,GAAGtC,WAAW,CAACG,WAAW,yBAAyB;EAC3EoC,oBAAoB,EAAE,GAAGvC,WAAW,CAACG,WAAW,uBAAuB;EACvEqC,WAAW,EAAE,GAAGxC,WAAW,CAACG,WAAW,mBAAmB;EAC1DsC,mBAAmB,EAAE,GAAGzC,WAAW,CAACG,WAAW,2BAA2B;EAC1EuC,oBAAoB,EAAE,GAAG1C,WAAW,CAACG,WAAW,mCAAmC;EACnFwC,KAAK,EAAE,GAAG3C,WAAW,CAACG,WAAW,QAAQ;EACzCyC,kBAAkB,EAAE,GAAG5C,WAAW,CAACG,WAAW,+BAA+B;EAC7E0C,eAAe,EAAE,GAAG7C,WAAW,CAACG,WAAW,cAAc;EACzD2C,SAAS,EAAE,GAAG9C,WAAW,CAACG,WAAW,YAAY;EACjD4C,QAAQ,EAAE,GAAG/C,WAAW,CAACG,WAAW,WAAW;EAC/C6C,YAAY,EAAE,GAAGhD,WAAW,CAACG,WAAW,eAAe;EACvD8C,gBAAgB,EAAE,GAAGjD,WAAW,CAACG,WAAW,mBAAmB;EAC/D+C,WAAW,EAAE,GAAGlD,WAAW,CAACG,WAAW,eAAe;EACtDgD,OAAO,EAAE,GAAGnD,WAAW,CAACG,WAAW,UAAU;EAC7CiD,eAAe,EAAE,GAAGpD,WAAW,CAACG,WAAW,kBAAkB;EAC7DkD,oBAAoB,EAAE,GAAGrD,WAAW,CAACG,WAAW,qBAAqB;EACrEmD,WAAW,EAAE,GAAGtD,WAAW,CAACG,WAAW,mBAAmB;EAC1DoD,mBAAmB,EAAE,GAAGvD,WAAW,CAACG,WAAW,2BAA2B;EAC1EqD,KAAK,EAAE,GAAGxD,WAAW,CAACG,WAAW,iBAAiB;EAClDsD,aAAa,EAAE,GAAGzD,WAAW,CAACG,WAAW,kBAAkB;EAC3DuD,aAAa,EAAE,GAAG1D,WAAW,CAACG,WAAW,gBAAgB;EACzDwD,eAAe,EAAE,GAAG3D,WAAW,CAACG,WAAW,kBAAkB;EAC7DyD,YAAY,EAAE,GAAG5D,WAAW,CAACG,WAAW,oBAAoB;EAC5D0D,YAAY,EAAE,GAAG7D,WAAW,CAACG,WAAW,eAAe;EACvD2D,gBAAgB,EAAE,GAAG9D,WAAW,CAACG,WAAW,mBAAmB;EAC/D4D,kBAAkB,EAAE,GAAG/D,WAAW,CAACG,WAAW,sBAAsB;EACpE6D,mBAAmB,EAAE,GAAGhE,WAAW,CAACG,WAAW,4BAA4B;EAC3E8D,8BAA8B,EAAE,GAAGjE,WAAW,CAACG,WAAW,yBAAyB;EACnF+D,qBAAqB,EAAE,GAAGlE,WAAW,CAACG,WAAW,yBAAyB;EAC1EgE,MAAM,EAAE,GAAGnE,WAAW,CAACG,WAAW,SAAS;EAC3CiE,gBAAgB,EAAE,GAAGpE,WAAW,CAACG,WAAW;CAC7C;AAED,OAAO,MAAMkE,gBAAgB,GAAG;EAC9BC,UAAU,EAAE,GAAGtE,WAAW,CAACK,cAAc,8BAA8B;EACvEkE,YAAY,EAAE,GAAGvE,WAAW,CAACK,cAAc,4BAA4B;EACvEe,yBAAyB,EAAE,GAAGpB,WAAW,CAACK,cAAc,iCAAiC;EACzFmE,MAAM,EAAE,GAAGxE,WAAW,CAACK,cAAc,cAAc;EACnD0C,QAAQ,EAAE,GAAG/C,WAAW,CAACK,cAAc,eAAe;EACtDoE,WAAW,EAAE,GAAGzE,WAAW,CAACK,cAAc,yBAAyB;EACnEqE,WAAW,EAAE,GAAG1E,WAAW,CAACK,cAAc,8BAA8B;EACxEsE,qBAAqB,EAAE,GAAG3E,WAAW,CAACK,cAAc,oBAAoB;EACxEY,QAAQ,EAAE,GAAGjB,WAAW,CAACK,cAAc,wBAAwB;EAC/DuE,gBAAgB,EAAE,GAAG5E,WAAW,CAACK,cAAc,iCAAiC;EAChFwE,iBAAiB,EAAE,GAAG7E,WAAW,CAACK,cAAc,gCAAgC;EAChFyE,MAAM,EAAE,GAAG9E,WAAW,CAACK,cAAc,iBAAiB;EACtD0E,YAAY,EAAE,GAAG/E,WAAW,CAACK,cAAc,YAAY;EACvD2E,SAAS,EAAE,GAAGhF,WAAW,CAACK,cAAc,YAAY;EACpD4E,sBAAsB,EAAE,GAAGjF,WAAW,CAACK,cAAc,2BAA2B;EAChF6E,cAAc,EAAE,GAAGlF,WAAW,CAACK,cAAc,0BAA0B;EACvEyC,SAAS,EAAE,GAAG9C,WAAW,CAACK,cAAc,gBAAgB;EACxD8E,gBAAgB,EAAE,GAAGnF,WAAW,CAACK,cAAc,6BAA6B;EAC5E+E,cAAc,EAAE,GAAGpF,WAAW,CAACK,cAAc,yBAAyB;EACtEgF,WAAW,EAAE,GAAGrF,WAAW,CAACK,cAAc,qBAAqB;EAC/DiF,aAAa,EAAE,GAAGtF,WAAW,CAACK,cAAc,qBAAqB;EACjEkF,eAAe,EAAE,GAAGvF,WAAW,CAACK,cAAc,gCAAgC;EAC9EmF,SAAS,EAAE,GAAGxF,WAAW,CAACK,cAAc,uBAAuB;EAC/DoF,kBAAkB,EAAE,GAAGzF,WAAW,CAACK,cAAc,oCAAoC;EACrFqF,yBAAyB,EAAE,GAAG1F,WAAW,CAACK,cAAc,4CAA4C;EACpGsF,gBAAgB,EAAE,GAAG3F,WAAW,CAACK,cAAc,+BAA+B;EAC9EuF,cAAc,EAAE,GAAG5F,WAAW,CAACK,cAAc,4CAA4C;EACzFwF,gBAAgB,EAAE,GAAG7F,WAAW,CAACK,cAAc,uCAAuC;EACtFyF,gBAAgB,EAAE,GAAG9F,WAAW,CAACK,cAAc,+BAA+B;EAC9E0F,QAAQ,EAAE,GAAG/F,WAAW,CAACK,cAAc,gBAAgB;EACvD2F,YAAY,EAAE,GAAGhG,WAAW,CAACK,cAAc,qBAAqB;EAChE4F,oCAAoC,EAAE,GAAGjG,WAAW,CAACK,cAAc,2CAA2C;EAC9G6F,mCAAmC,EAAE,GAAGlG,WAAW,CAACK,cAAc,kDAAkD;EACpH8F,+CAA+C,EAAE,GAAGnG,WAAW,CAACK,cAAc,6EAA6E;EAC3J+F,oBAAoB,EAAE,GAAGpG,WAAW,CAACK,cAAc,2BAA2B;EAC9EgG,mCAAmC,EAAE,GAAGrG,WAAW,CAACK,cAAc,sCAAsC;EACxGiG,4BAA4B,EAAE,GAAGtG,WAAW,CAACK,cAAc,mCAAmC;EAC9FkG,wBAAwB,EAAE,GAAGvG,WAAW,CAACK,cAAc,wDAAwD;EAC/GmG,sCAAsC,EAAE,GAAGxG,WAAW,CAACK,cAAc,gFAAgF;EACrJoG,uBAAuB,EAAE,GAAGzG,WAAW,CAACK,cAAc,4CAA4C;EAClGqG,oCAAoC,EAAE,GAAG1G,WAAW,CAACK,cAAc,uDAAuD;EAC1HsG,eAAe,EAAE,GAAG3G,WAAW,CAACK,cAAc,wBAAwB;EACtEuG,0BAA0B,EAAE,GAAG5G,WAAW,CAACK,cAAc,yCAAyC;EAClGwG,kBAAkB,EAAE,GAAG7G,WAAW,CAACK,cAAc,+BAA+B;EAChFyG,4BAA4B,EAAE,GAAG9G,WAAW,CAACK,cAAc,wCAAwC;EACnG0G,4BAA4B,EAAE,GAAG/G,WAAW,CAACK,cAAc,wCAAwC;EACnG2G,2BAA2B,EAAE,GAAGhH,WAAW,CAACK,cAAc,uCAAuC;EACjG4G,eAAe,EAAE,GAAGjH,WAAW,CAACK,cAAc,sBAAsB;EACpE6G,4BAA4B,EAAE,GAAGlH,WAAW,CAACK,cAAc,6CAA6C;EACxG8G,UAAU,EAAE,GAAGnH,WAAW,CAACK,cAAc,gCAAgC;EACzE+G,aAAa,EAAE,GAAGpH,WAAW,CAACK,cAAc,kCAAkC;EAC9EgH,iBAAiB,EAAE,GAAGrH,WAAW,CAACK,cAAc,iCAAiC;EACjFiH,oBAAoB,EAAE,GAAGtH,WAAW,CAACK,cAAc,8BAA8B;EACjFkH,WAAW,EAAE,GAAGvH,WAAW,CAACK,cAAc;CAC3C;AAED,OAAO,MAAMmH,WAAW,GAAG;EACzBC,eAAe,EAAE,IAAI,GAAG,IAAI;EAAE;EAC9BC,sBAAsB,EAAE;CACzB;AAED,OAAO,MAAMC,UAAU,GAAG;EACxBC,YAAY,EAAE;CACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}