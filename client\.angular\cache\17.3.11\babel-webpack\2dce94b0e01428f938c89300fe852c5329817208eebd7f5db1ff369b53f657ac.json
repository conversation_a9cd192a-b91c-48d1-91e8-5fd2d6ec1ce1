{"ast": null, "code": "import { Subject, fork<PERSON>oin, takeUntil } from 'rxjs';\nimport { AppConstant } from 'src/app/constants/api.constants';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../sales-quotes.service\";\nimport * as i3 from \"src/app/store/services/setting.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/progressspinner\";\nfunction SalesQuotesOverviewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesQuotesOverviewComponent_div_1_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 33);\n    i0.ɵɵtext(2, \"Item Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 34);\n    i0.ɵɵtext(4, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 35);\n    i0.ɵɵtext(6, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesQuotesOverviewComponent_div_1_ng_template_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 36)(2, \"div\", 37)(3, \"div\", 38)(4, \"h5\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 41)(9, \"p\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 43)(13, \"p\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 45);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const items_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(items_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1.meterial, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 6, items_r1.quantity), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.price, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.eachPrice, \" each \");\n  }\n}\nfunction SalesQuotesOverviewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"h4\", 7);\n    i0.ɵɵtext(5, \"Quote Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"ul\", 9)(8, \"li\", 10)(9, \"div\", 11)(10, \"i\", 12);\n    i0.ɵɵtext(11, \"tag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"h6\", 14);\n    i0.ɵɵtext(14, \"Quote #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"li\", 10)(18, \"div\", 11)(19, \"i\", 12);\n    i0.ɵɵtext(20, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h6\", 14);\n    i0.ɵɵtext(23, \"Customer #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 15);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"li\", 10)(27, \"div\", 11)(28, \"i\", 12);\n    i0.ɵɵtext(29, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 13)(31, \"h6\", 14);\n    i0.ɵɵtext(32, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 15);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"li\", 10)(36, \"div\", 11)(37, \"i\", 12);\n    i0.ɵɵtext(38, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 13)(40, \"h6\", 14);\n    i0.ɵɵtext(41, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\", 15);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"li\", 10)(45, \"div\", 11)(46, \"i\", 12);\n    i0.ɵɵtext(47, \"event\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 13)(49, \"h6\", 14);\n    i0.ɵɵtext(50, \"Date Placed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 15);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(53, \"div\", 5)(54, \"div\", 6)(55, \"h4\", 7);\n    i0.ɵɵtext(56, \"Quote Description\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 8)(58, \"ul\", 16)(59, \"li\", 10)(60, \"div\", 17)(61, \"i\", 12);\n    i0.ɵɵtext(62, \"tag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 13)(64, \"h6\", 14);\n    i0.ɵɵtext(65, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"p\", 15);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(68, \"div\", 18)(69, \"div\", 19)(70, \"h4\", 7);\n    i0.ɵɵtext(71, \"Items\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 20)(73, \"p-table\", 21);\n    i0.ɵɵtemplate(74, SalesQuotesOverviewComponent_div_1_ng_template_74_Template, 7, 0, \"ng-template\", 22)(75, SalesQuotesOverviewComponent_div_1_ng_template_75_Template, 17, 8, \"ng-template\", 23);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(76, \"div\", 24)(77, \"div\", 25)(78, \"h5\", 26);\n    i0.ɵɵtext(79, \"Quote Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 27)(81, \"ul\", 28)(82, \"li\", 29)(83, \"span\", 30);\n    i0.ɵɵtext(84, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"li\", 29)(87, \"span\", 30);\n    i0.ɵɵtext(88, \"Subtotal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 31)(91, \"h5\", 32);\n    i0.ɵɵtext(92, \"Total \");\n    i0.ɵɵelementStart(93, \"span\");\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.quote == null ? null : ctx_r1.quote.QUOTE_HDR == null ? null : ctx_r1.quote.QUOTE_HDR.DOC_NUMBER);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r1.customer == null ? null : ctx_r1.customer.customer == null ? null : ctx_r1.customer.customer.customer_id) || \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((ctx_r1.customer == null ? null : ctx_r1.customer.customer == null ? null : ctx_r1.customer.customer.customer_name) || \"-\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.quote == null ? null : ctx_r1.quote.QUOTE_HDR == null ? null : ctx_r1.quote.QUOTE_HDR.DOC_NAME);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.moment(ctx_r1.quote == null ? null : ctx_r1.quote.QUOTE_HDR == null ? null : ctx_r1.quote.QUOTE_HDR.DOC_DATE, \"YYYYMMDD\").format(\"MM/DD/YYYY\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.quote == null ? null : ctx_r1.quote.QUOTE_HDR_TEXT[0] == null ? null : ctx_r1.quote.QUOTE_HDR_TEXT[0].TEXT);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.quoteItems);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusName(ctx_r1.quote == null ? null : ctx_r1.quote.QUOTE_HDR == null ? null : ctx_r1.quote.QUOTE_HDR.DOC_STAT), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.quote == null ? null : ctx_r1.quote.formatted_sub_total, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.quote == null ? null : ctx_r1.quote.formatted_sub_total);\n  }\n}\nexport let SalesQuotesOverviewComponent = /*#__PURE__*/(() => {\n  class SalesQuotesOverviewComponent {\n    constructor(route, salesquotesservice, settingsservice, messageservice) {\n      this.route = route;\n      this.salesquotesservice = salesquotesservice;\n      this.settingsservice = settingsservice;\n      this.messageservice = messageservice;\n      this.ngUnsubscribe = new Subject();\n      this.moment = moment;\n      this.loading = false;\n      this.quoteID = null;\n      this.quote = null;\n      this.statuses = [];\n      this.quoteItems = [];\n      this.customer = {};\n    }\n    ngOnInit() {\n      this.route.parent?.paramMap.pipe(takeUntil(this.ngUnsubscribe)).subscribe(params => {\n        const id = params.get('id');\n        if (id) {\n          this.quoteID = id;\n          this.loading = true;\n          this.getSettings();\n        }\n      });\n    }\n    getSettings() {\n      this.settingsservice.getSettings().pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: data => {\n          this.getQuoteDetails(data?.sales_quote_type_code);\n        },\n        error: e => {\n          console.error('Error while processing settings request.', e);\n        }\n      });\n    }\n    getQuoteDetails(docType) {\n      // Get all quote status\n      const status$ = this.salesquotesservice.getAllStatuses();\n      // Get quote detail\n      const payload = {};\n      payload.SD_DOC = this.quoteID;\n      payload.DOC_TYPE = docType || 'QT';\n      const quote$ = this.salesquotesservice.getQuoteDetails(payload);\n      forkJoin([status$, quote$]).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: result => {\n          this.statuses = result[0]?.data || [];\n          this.quote = result[1]?.SALESQUOTE || null;\n          result[1]?.SALESQUOTE?.QUOTE_LINE_DETAIL.map(item => {\n            this.setImage(item);\n            this.quoteItems.push({\n              description: item.SHORT_TEXT,\n              meterial: item.MATERIAL,\n              quantity: item.REQ_QTY,\n              price: item.formatted_base_price,\n              eachPrice: item.formatted_base_price_each,\n              imageUrl: item.imageUrl\n            });\n          });\n          this.getPartnerFunction(result[1]?.SALESQUOTE?.QUOTE_HDR?.SOLDTO);\n          this.loading = false;\n        },\n        error: () => {\n          this.loading = false;\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    getStatusName(code) {\n      const status = this.statuses.find(o => o.code === code);\n      if (status) {\n        return status.description;\n      }\n      return '';\n    }\n    setImage(item) {\n      item.imageUrl = AppConstant.PRODUCT_IMAGE_FALLBACK;\n      this.salesquotesservice.getImages(item.MATERIAL).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: value => {\n          if (value?.data?.length) {\n            const images = value.data.filter(item => item.dimension == '1200X1200');\n            if (images.length) {\n              item.imageUrl = images[0].url;\n            }\n          }\n        }\n      });\n    }\n    getPartnerFunction(customerid) {\n      this.salesquotesservice.getPartnerFunction(customerid).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: value => {\n          this.customer = value.find(o => o.customer_id === customerid && o.partner_function === 'SP');\n          this.customer = value.find(o => o.bp_customer_number === customerid && o.partner_function === 'SH');\n          return {\n            shipToParty: this.customer\n          };\n        },\n        error: () => {\n          console.log('Error while processing get ship to request.', {\n            type: 'Error'\n          });\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function SalesQuotesOverviewComponent_Factory(t) {\n        return new (t || SalesQuotesOverviewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.SalesQuotesService), i0.ɵɵdirectiveInject(i3.SettingsService), i0.ɵɵdirectiveInject(i4.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesQuotesOverviewComponent,\n        selectors: [[\"app-sales-quotes-overview\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"class\", \"grid mt-0 relative\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"gap-5\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"min-w-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"table-data\", \"border-round\", \"overflow-hidden\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"lg:w-30rem\", \"md:w-30rem\", \"sm:w-full\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"mt-2\", \"mb-4\", \"uppercase\", \"text-center\", \"text-primary\"], [1, \"cart-sidebar-price\", \"py-4\", \"border-none\", \"border-y-1\", \"border-solid\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"font-semibold\"], [1, \"text-color-secondary\"], [1, \"cart-sidebar-t-price\", \"py-4\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"text-primary\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"]],\n        template: function SalesQuotesOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SalesQuotesOverviewComponent_div_0_Template, 2, 0, \"div\", 0)(1, SalesQuotesOverviewComponent_div_1_Template, 95, 10, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i5.NgIf, i6.Table, i4.PrimeTemplate, i7.ProgressSpinner, i5.DecimalPipe],\n        styles: [\".card-heading h4.ml-0{margin-left:0!important}\"]\n      });\n    }\n  }\n  return SalesQuotesOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}