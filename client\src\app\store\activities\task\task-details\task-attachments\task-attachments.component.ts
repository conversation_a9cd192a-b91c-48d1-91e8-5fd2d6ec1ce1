import { Component, OnInit } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { ActivitiesService } from '../../../activities.service';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-task-attachments',
  templateUrl: './task-attachments.component.html',
  styleUrl: './task-attachments.component.scss',
})
export class TaskAttachmentsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  public attachmentdetails: any[] = [];
  public bp_id: string = '';

  constructor(private activitiesservice: ActivitiesService) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'Type', header: 'Type' },
    { field: 'ChangedOn', header: 'Changed On' },
    { field: 'ChangedBy', header: 'Changed By' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.attachmentdetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit() {
    this.activitiesservice.activity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.bp_id = response?.bp_id;
          this.attachmentdetails = response?.contact_activity;
        }
      });

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
