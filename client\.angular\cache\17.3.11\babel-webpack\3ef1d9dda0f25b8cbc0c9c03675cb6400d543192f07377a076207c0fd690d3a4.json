{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar gOPD = require('gopd');\nvar $TypeError = require('es-errors/type');\nvar $floor = GetIntrinsic('%Math.floor%');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionLength(fn, length) {\n  if (typeof fn !== 'function') {\n    throw new $TypeError('`fn` is not a function');\n  }\n  if (typeof length !== 'number' || length < 0 || length > 0xFFFFFFFF || $floor(length) !== length) {\n    throw new $TypeError('`length` must be a positive 32-bit integer');\n  }\n  var loose = arguments.length > 2 && !!arguments[2];\n  var functionLengthIsConfigurable = true;\n  var functionLengthIsWritable = true;\n  if ('length' in fn && gOPD) {\n    var desc = gOPD(fn, 'length');\n    if (desc && !desc.configurable) {\n      functionLengthIsConfigurable = false;\n    }\n    if (desc && !desc.writable) {\n      functionLengthIsWritable = false;\n    }\n  }\n  if (functionLengthIsConfigurable || functionLengthIsWritable || !loose) {\n    if (hasDescriptors) {\n      define( /** @type {Parameters<define>[0]} */fn, 'length', length, true, true);\n    } else {\n      define( /** @type {Parameters<define>[0]} */fn, 'length', length);\n    }\n  }\n  return fn;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}