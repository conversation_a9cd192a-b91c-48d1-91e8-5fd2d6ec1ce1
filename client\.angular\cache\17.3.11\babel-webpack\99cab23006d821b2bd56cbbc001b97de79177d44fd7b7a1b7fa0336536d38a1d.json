{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TicketStorageService = /*#__PURE__*/(() => {\n  class TicketStorageService {\n    constructor() {\n      this.STORAGE_PREFIX = 'ticket_form_data_';\n    }\n    /**\n     * Save ticket form data to local storage using ticket ID as key\n     * @param ticketId - The unique ticket ID to use as storage key\n     * @param formData - The form data containing invoice_no, order_no, and credit_memo_no\n     */\n    saveTicketFormData(ticketId, formData) {\n      if (!ticketId) {\n        console.warn('Ticket ID is required to save form data');\n        return;\n      }\n      const ticketFormData = {\n        invoice_no: formData.invoice_no || '',\n        order_no: formData.order_no || '',\n        credit_memo_no: formData.credit_memo_no || '',\n        timestamp: new Date().toISOString()\n      };\n      try {\n        const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n        localStorage.setItem(storageKey, JSON.stringify(ticketFormData));\n      } catch (error) {\n        console.error('Error saving ticket form data to local storage:', error);\n      }\n    }\n    /**\n     * Retrieve ticket form data from local storage by ticket ID\n     * @param ticketId - The unique ticket ID used as storage key\n     * @returns The stored ticket form data or null if not found\n     */\n    getTicketFormData(ticketId) {\n      if (!ticketId) {\n        console.warn('Ticket ID is required to retrieve form data');\n        return null;\n      }\n      try {\n        const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n        const storedData = localStorage.getItem(storageKey);\n        return storedData ? JSON.parse(storedData) : null;\n      } catch (error) {\n        console.error('Error retrieving ticket form data from local storage:', error);\n        return null;\n      }\n    }\n    /**\n     * Clear ticket form data from local storage by ticket ID\n     * @param ticketId - The unique ticket ID used as storage key\n     */\n    clearTicketFormData(ticketId) {\n      if (!ticketId) {\n        console.warn('Ticket ID is required to clear form data');\n        return;\n      }\n      try {\n        const storageKey = `${this.STORAGE_PREFIX}${ticketId}`;\n        localStorage.removeItem(storageKey);\n      } catch (error) {\n        console.error('Error clearing ticket form data from local storage:', error);\n      }\n    }\n    /**\n     * Get all ticket form data keys from local storage\n     * @returns Array of ticket IDs that have stored form data\n     */\n    getAllTicketFormDataKeys() {\n      try {\n        const keys = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key && key.startsWith(this.STORAGE_PREFIX)) {\n            keys.push(key.replace(this.STORAGE_PREFIX, ''));\n          }\n        }\n        return keys;\n      } catch (error) {\n        console.error('Error getting ticket form data keys from local storage:', error);\n        return [];\n      }\n    }\n    /**\n     * Clear all ticket form data from local storage\n     * Useful for cleanup operations\n     */\n    clearAllTicketFormData() {\n      try {\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key && key.startsWith(this.STORAGE_PREFIX)) {\n            keysToRemove.push(key);\n          }\n        }\n        keysToRemove.forEach(key => {\n          localStorage.removeItem(key);\n        });\n      } catch (error) {\n        console.error('Error clearing all ticket form data from local storage:', error);\n      }\n    }\n    /**\n     * Clear old ticket form data (older than specified days)\n     * @param daysOld - Number of days after which data should be considered old\n     */\n    clearOldTicketFormData(daysOld = 30) {\n      try {\n        const cutoffDate = new Date();\n        cutoffDate.setDate(cutoffDate.getDate() - daysOld);\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key && key.startsWith(this.STORAGE_PREFIX)) {\n            const storedData = localStorage.getItem(key);\n            if (storedData) {\n              try {\n                const parsedData = JSON.parse(storedData);\n                const dataDate = new Date(parsedData.timestamp);\n                if (dataDate < cutoffDate) {\n                  keysToRemove.push(key);\n                }\n              } catch (parseError) {\n                // If we can't parse the data, consider it old and remove it\n                keysToRemove.push(key);\n              }\n            }\n          }\n        }\n        keysToRemove.forEach(key => {\n          localStorage.removeItem(key);\n        });\n        if (keysToRemove.length > 0) {\n          console.log(`Cleared ${keysToRemove.length} old ticket form data entries`);\n        }\n      } catch (error) {\n        console.error('Error clearing old ticket form data from local storage:', error);\n      }\n    }\n    static {\n      this.ɵfac = function TicketStorageService_Factory(t) {\n        return new (t || TicketStorageService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TicketStorageService,\n        factory: TicketStorageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TicketStorageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}