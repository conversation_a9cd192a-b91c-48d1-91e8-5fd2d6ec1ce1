{"ast": null, "code": "import * as i0 from \"@angular/core\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵproperty(\"item\", item_r2)(\"index\", i_r3)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2)(2, AppMenuComponent_ng_container_1_li_2_Template, 1, 0, \"li\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.separator);\n  }\n}\nexport let AppMenuComponent = /*#__PURE__*/(() => {\n  class AppMenuComponent {\n    constructor() {\n      this.model = [];\n    }\n    ngOnInit() {\n      this.model = [{\n        label: 'Apps',\n        icon: 'pi pi-th-large',\n        items: [{\n          label: 'Dashboard',\n          icon: 'pi pi-fw pi-home',\n          routerLink: ['/backoffice']\n        }, {\n          label: 'Customer',\n          icon: 'pi pi-fw pi-users',\n          routerLink: ['/backoffice/customer']\n        }]\n      }];\n    }\n    static {\n      this.ɵfac = function AppMenuComponent_Factory(t) {\n        return new (t || AppMenuComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppMenuComponent,\n        selectors: [[\"app-menu\"]],\n        decls: 2,\n        vars: 1,\n        consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"]],\n        template: function AppMenuComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"ul\", 0);\n            i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.model);\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return AppMenuComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}