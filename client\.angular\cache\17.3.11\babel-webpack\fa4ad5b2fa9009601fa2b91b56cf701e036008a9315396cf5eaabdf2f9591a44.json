{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatform<PERSON>rowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nconst _c0 = [\"sliderHandle\"];\nconst _c1 = [\"sliderHandleStart\"];\nconst _c2 = [\"sliderHandleEnd\"];\nconst _c3 = (a0, a1, a2, a3) => ({\n  \"p-slider p-component\": true,\n  \"p-disabled\": a0,\n  \"p-slider-horizontal\": a1,\n  \"p-slider-vertical\": a2,\n  \"p-slider-animate\": a3\n});\nconst _c4 = (a0, a1) => ({\n  left: a0,\n  width: a1\n});\nconst _c5 = (a0, a1) => ({\n  bottom: a0,\n  height: a1\n});\nconst _c6 = a0 => ({\n  height: a0\n});\nconst _c7 = a0 => ({\n  width: a0\n});\nconst _c8 = (a0, a1) => ({\n  left: a0,\n  bottom: a1\n});\nconst _c9 = a0 => ({\n  \"p-slider-handle-active\": a0\n});\nfunction Slider_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(2, _c4, ctx_r0.offset !== null && ctx_r0.offset !== undefined ? ctx_r0.offset + \"%\" : ctx_r0.handleValues[0] + \"%\", ctx_r0.diff ? ctx_r0.diff + \"%\" : ctx_r0.handleValues[1] - ctx_r0.handleValues[0] + \"%\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"range\");\n  }\n}\nfunction Slider_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(2, _c5, ctx_r0.offset !== null && ctx_r0.offset !== undefined ? ctx_r0.offset + \"%\" : ctx_r0.handleValues[0] + \"%\", ctx_r0.diff ? ctx_r0.diff + \"%\" : ctx_r0.handleValues[1] - ctx_r0.handleValues[0] + \"%\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"range\");\n  }\n}\nfunction Slider_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c6, ctx_r0.handleValue + \"%\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"range\");\n  }\n}\nfunction Slider_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c7, ctx_r0.handleValue + \"%\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"range\");\n  }\n}\nfunction Slider_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 9, 0);\n    i0.ɵɵlistener(\"touchstart\", function Slider_span_5_Template_span_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragStart($event));\n    })(\"touchmove\", function Slider_span_5_Template_span_touchmove_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDrag($event));\n    })(\"touchend\", function Slider_span_5_Template_span_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragEnd($event));\n    })(\"mousedown\", function Slider_span_5_Template_span_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseDown($event));\n    })(\"keydown\", function Slider_span_5_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onKeyDown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transition\", ctx_r0.dragging ? \"none\" : null);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(11, _c8, ctx_r0.orientation == \"horizontal\" ? ctx_r0.handleValue + \"%\" : null, ctx_r0.orientation == \"vertical\" ? ctx_r0.handleValue + \"%\" : null));\n    i0.ɵɵattribute(\"tabindex\", ctx_r0.disabled ? null : ctx_r0.tabindex)(\"aria-valuemin\", ctx_r0.min)(\"aria-valuenow\", ctx_r0.value)(\"aria-valuemax\", ctx_r0.max)(\"aria-labelledby\", ctx_r0.ariaLabelledBy)(\"aria-label\", ctx_r0.ariaLabel)(\"aria-orientation\", ctx_r0.orientation)(\"data-pc-section\", \"handle\");\n  }\n}\nfunction Slider_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 10, 1);\n    i0.ɵɵlistener(\"keydown\", function Slider_span_6_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onKeyDown($event, 0));\n    })(\"mousedown\", function Slider_span_6_Template_span_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseDown($event, 0));\n    })(\"touchstart\", function Slider_span_6_Template_span_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragStart($event, 0));\n    })(\"touchmove\", function Slider_span_6_Template_span_touchmove_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDrag($event, 0));\n    })(\"touchend\", function Slider_span_6_Template_span_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transition\", ctx_r0.dragging ? \"none\" : null);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(12, _c8, ctx_r0.rangeStartLeft, ctx_r0.rangeStartBottom))(\"ngClass\", i0.ɵɵpureFunction1(15, _c9, ctx_r0.handleIndex == 0));\n    i0.ɵɵattribute(\"tabindex\", ctx_r0.disabled ? null : ctx_r0.tabindex)(\"aria-valuemin\", ctx_r0.min)(\"aria-valuenow\", ctx_r0.value ? ctx_r0.value[0] : null)(\"aria-valuemax\", ctx_r0.max)(\"aria-labelledby\", ctx_r0.ariaLabelledBy)(\"aria-label\", ctx_r0.ariaLabel)(\"aria-orientation\", ctx_r0.orientation)(\"data-pc-section\", \"startHandler\");\n  }\n}\nfunction Slider_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11, 2);\n    i0.ɵɵlistener(\"keydown\", function Slider_span_7_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onKeyDown($event, 1));\n    })(\"mousedown\", function Slider_span_7_Template_span_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseDown($event, 1));\n    })(\"touchstart\", function Slider_span_7_Template_span_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragStart($event, 1));\n    })(\"touchmove\", function Slider_span_7_Template_span_touchmove_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDrag($event, 1));\n    })(\"touchend\", function Slider_span_7_Template_span_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transition\", ctx_r0.dragging ? \"none\" : null);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(12, _c8, ctx_r0.rangeEndLeft, ctx_r0.rangeEndBottom))(\"ngClass\", i0.ɵɵpureFunction1(15, _c9, ctx_r0.handleIndex == 1));\n    i0.ɵɵattribute(\"tabindex\", ctx_r0.disabled ? null : ctx_r0.tabindex)(\"aria-valuemin\", ctx_r0.min)(\"aria-valuenow\", ctx_r0.value ? ctx_r0.value[1] : null)(\"aria-valuemax\", ctx_r0.max)(\"aria-labelledby\", ctx_r0.ariaLabelledBy)(\"aria-label\", ctx_r0.ariaLabel)(\"aria-orientation\", ctx_r0.orientation)(\"data-pc-section\", \"endHandler\");\n  }\n}\nconst SLIDER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Slider),\n  multi: true\n};\n/**\n * Slider is a component to provide input with a drag handle.\n * @group Components\n */\nlet Slider = /*#__PURE__*/(() => {\n  class Slider {\n    document;\n    platformId;\n    el;\n    renderer;\n    ngZone;\n    cd;\n    /**\n     * When enabled, displays an animation on click of the slider bar.\n     * @group Props\n     */\n    animate;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    min = 0;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    max = 100;\n    /**\n     * Orientation of the slider.\n     * @group Props\n     */\n    orientation = 'horizontal';\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    step;\n    /**\n     * When specified, allows two boundary values to be picked.\n     * @group Props\n     */\n    range;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Callback to invoke on value change.\n     * @param {SliderChangeEvent} event - Custom value change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke when slide ended.\n     * @param {SliderSlideEndEvent} event - Custom slide end event.\n     * @group Emits\n     */\n    onSlideEnd = new EventEmitter();\n    sliderHandle;\n    sliderHandleStart;\n    sliderHandleEnd;\n    value;\n    values;\n    handleValue;\n    handleValues = [];\n    diff;\n    offset;\n    bottom;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    dragging;\n    dragListener;\n    mouseupListener;\n    initX;\n    initY;\n    barWidth;\n    barHeight;\n    sliderHandleClick;\n    handleIndex = 0;\n    startHandleValue;\n    startx;\n    starty;\n    constructor(document, platformId, el, renderer, ngZone, cd) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.ngZone = ngZone;\n      this.cd = cd;\n    }\n    onMouseDown(event, index) {\n      if (this.disabled) {\n        return;\n      }\n      this.dragging = true;\n      this.updateDomData();\n      this.sliderHandleClick = true;\n      if (this.range && this.handleValues && this.handleValues[0] === this.max) {\n        this.handleIndex = 0;\n      } else {\n        this.handleIndex = index;\n      }\n      this.bindDragListeners();\n      event.target.focus();\n      event.preventDefault();\n      if (this.animate) {\n        DomHandler.removeClass(this.el.nativeElement.children[0], 'p-slider-animate');\n      }\n    }\n    onDragStart(event, index) {\n      if (this.disabled) {\n        return;\n      }\n      var touchobj = event.changedTouches[0];\n      this.startHandleValue = this.range ? this.handleValues[index] : this.handleValue;\n      this.dragging = true;\n      if (this.range && this.handleValues && this.handleValues[0] === this.max) {\n        this.handleIndex = 0;\n      } else {\n        this.handleIndex = index;\n      }\n      if (this.orientation === 'horizontal') {\n        this.startx = parseInt(touchobj.clientX, 10);\n        this.barWidth = this.el.nativeElement.children[0].offsetWidth;\n      } else {\n        this.starty = parseInt(touchobj.clientY, 10);\n        this.barHeight = this.el.nativeElement.children[0].offsetHeight;\n      }\n      if (this.animate) {\n        DomHandler.removeClass(this.el.nativeElement.children[0], 'p-slider-animate');\n      }\n      event.preventDefault();\n    }\n    onDrag(event) {\n      if (this.disabled) {\n        return;\n      }\n      var touchobj = event.changedTouches[0],\n        handleValue = 0;\n      if (this.orientation === 'horizontal') {\n        handleValue = Math.floor((parseInt(touchobj.clientX, 10) - this.startx) * 100 / this.barWidth) + this.startHandleValue;\n      } else {\n        handleValue = Math.floor((this.starty - parseInt(touchobj.clientY, 10)) * 100 / this.barHeight) + this.startHandleValue;\n      }\n      this.setValueFromHandle(event, handleValue);\n      event.preventDefault();\n    }\n    onDragEnd(event) {\n      if (this.disabled) {\n        return;\n      }\n      this.dragging = false;\n      if (this.range) this.onSlideEnd.emit({\n        originalEvent: event,\n        values: this.values\n      });else this.onSlideEnd.emit({\n        originalEvent: event,\n        value: this.value\n      });\n      if (this.animate) {\n        DomHandler.addClass(this.el.nativeElement.children[0], 'p-slider-animate');\n      }\n      event.preventDefault();\n    }\n    onBarClick(event) {\n      if (this.disabled) {\n        return;\n      }\n      if (!this.sliderHandleClick) {\n        this.updateDomData();\n        this.handleChange(event);\n        if (this.range) this.onSlideEnd.emit({\n          originalEvent: event,\n          values: this.values\n        });else this.onSlideEnd.emit({\n          originalEvent: event,\n          value: this.value\n        });\n      }\n      this.sliderHandleClick = false;\n    }\n    onKeyDown(event, index) {\n      this.handleIndex = index;\n      switch (event.code) {\n        case 'ArrowDown':\n        case 'ArrowLeft':\n          this.decrementValue(event, index);\n          event.preventDefault();\n          break;\n        case 'ArrowUp':\n        case 'ArrowRight':\n          this.incrementValue(event, index);\n          event.preventDefault();\n          break;\n        case 'PageDown':\n          this.decrementValue(event, index, true);\n          event.preventDefault();\n          break;\n        case 'PageUp':\n          this.incrementValue(event, index, true);\n          event.preventDefault();\n          break;\n        case 'Home':\n          this.updateValue(this.min, event);\n          event.preventDefault();\n          break;\n        case 'End':\n          this.updateValue(this.max, event);\n          event.preventDefault();\n          break;\n        default:\n          break;\n      }\n    }\n    decrementValue(event, index, pageKey = false) {\n      let newValue;\n      if (this.range) {\n        if (this.step) newValue = this.values[index] - this.step;else newValue = this.values[index] - 1;\n      } else {\n        if (this.step) newValue = this.value - this.step;else if (!this.step && pageKey) newValue = this.value - 10;else newValue = this.value - 1;\n      }\n      this.updateValue(newValue, event);\n      event.preventDefault();\n    }\n    incrementValue(event, index, pageKey = false) {\n      let newValue;\n      if (this.range) {\n        if (this.step) newValue = this.values[index] + this.step;else newValue = this.values[index] + 1;\n      } else {\n        if (this.step) newValue = this.value + this.step;else if (!this.step && pageKey) newValue = this.value + 10;else newValue = this.value + 1;\n      }\n      this.updateValue(newValue, event);\n      event.preventDefault();\n    }\n    handleChange(event) {\n      let handleValue = this.calculateHandleValue(event);\n      this.setValueFromHandle(event, handleValue);\n    }\n    bindDragListeners() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.ngZone.runOutsideAngular(() => {\n          const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n          if (!this.dragListener) {\n            this.dragListener = this.renderer.listen(documentTarget, 'mousemove', event => {\n              if (this.dragging) {\n                this.ngZone.run(() => {\n                  this.handleChange(event);\n                });\n              }\n            });\n          }\n          if (!this.mouseupListener) {\n            this.mouseupListener = this.renderer.listen(documentTarget, 'mouseup', event => {\n              if (this.dragging) {\n                this.dragging = false;\n                this.ngZone.run(() => {\n                  if (this.range) this.onSlideEnd.emit({\n                    originalEvent: event,\n                    values: this.values\n                  });else this.onSlideEnd.emit({\n                    originalEvent: event,\n                    value: this.value\n                  });\n                  if (this.animate) {\n                    DomHandler.addClass(this.el.nativeElement.children[0], 'p-slider-animate');\n                  }\n                });\n              }\n            });\n          }\n        });\n      }\n    }\n    unbindDragListeners() {\n      if (this.dragListener) {\n        this.dragListener();\n        this.dragListener = null;\n      }\n      if (this.mouseupListener) {\n        this.mouseupListener();\n        this.mouseupListener = null;\n      }\n    }\n    setValueFromHandle(event, handleValue) {\n      let newValue = this.getValueFromHandle(handleValue);\n      if (this.range) {\n        if (this.step) {\n          this.handleStepChange(newValue, this.values[this.handleIndex]);\n        } else {\n          this.handleValues[this.handleIndex] = handleValue;\n          this.updateValue(newValue, event);\n        }\n      } else {\n        if (this.step) {\n          this.handleStepChange(newValue, this.value);\n        } else {\n          this.handleValue = handleValue;\n          this.updateValue(newValue, event);\n        }\n      }\n      this.cd.markForCheck();\n    }\n    handleStepChange(newValue, oldValue) {\n      let diff = newValue - oldValue;\n      let val = oldValue;\n      let _step = this.step;\n      if (diff < 0) {\n        val = oldValue + Math.ceil(newValue / _step - oldValue / _step) * _step;\n      } else if (diff > 0) {\n        val = oldValue + Math.floor(newValue / _step - oldValue / _step) * _step;\n      }\n      this.updateValue(val);\n      this.updateHandleValue();\n    }\n    writeValue(value) {\n      if (this.range) this.values = value || [0, 0];else this.value = value || 0;\n      this.updateHandleValue();\n      this.updateDiffAndOffset();\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    get rangeStartLeft() {\n      if (!this.isVertical()) return this.handleValues[0] > 100 ? 100 + '%' : this.handleValues[0] + '%';\n      return null;\n    }\n    get rangeStartBottom() {\n      return this.isVertical() ? this.handleValues[0] + '%' : 'auto';\n    }\n    get rangeEndLeft() {\n      return this.isVertical() ? null : this.handleValues[1] + '%';\n    }\n    get rangeEndBottom() {\n      return this.isVertical() ? this.handleValues[1] + '%' : 'auto';\n    }\n    isVertical() {\n      return this.orientation === 'vertical';\n    }\n    updateDomData() {\n      let rect = this.el.nativeElement.children[0].getBoundingClientRect();\n      this.initX = rect.left + DomHandler.getWindowScrollLeft();\n      this.initY = rect.top + DomHandler.getWindowScrollTop();\n      this.barWidth = this.el.nativeElement.children[0].offsetWidth;\n      this.barHeight = this.el.nativeElement.children[0].offsetHeight;\n    }\n    calculateHandleValue(event) {\n      if (this.orientation === 'horizontal') return (event.pageX - this.initX) * 100 / this.barWidth;else return (this.initY + this.barHeight - event.pageY) * 100 / this.barHeight;\n    }\n    updateHandleValue() {\n      if (this.range) {\n        this.handleValues[0] = (this.values[0] < this.min ? 0 : this.values[0] - this.min) * 100 / (this.max - this.min);\n        this.handleValues[1] = (this.values[1] > this.max ? 100 : this.values[1] - this.min) * 100 / (this.max - this.min);\n      } else {\n        if (this.value < this.min) this.handleValue = 0;else if (this.value > this.max) this.handleValue = 100;else this.handleValue = (this.value - this.min) * 100 / (this.max - this.min);\n      }\n      if (this.step) {\n        this.updateDiffAndOffset();\n      }\n    }\n    updateDiffAndOffset() {\n      this.diff = this.getDiff();\n      this.offset = this.getOffset();\n    }\n    getDiff() {\n      return Math.abs(this.handleValues[0] - this.handleValues[1]);\n    }\n    getOffset() {\n      return Math.min(this.handleValues[0], this.handleValues[1]);\n    }\n    updateValue(val, event) {\n      if (this.range) {\n        let value = val;\n        if (this.handleIndex == 0) {\n          if (value < this.min) {\n            value = this.min;\n            this.handleValues[0] = 0;\n          } else if (value > this.values[1]) {\n            if (value > this.max) {\n              value = this.max;\n              this.handleValues[0] = 100;\n            }\n          }\n          this.sliderHandleStart?.nativeElement.focus();\n        } else {\n          if (value > this.max) {\n            value = this.max;\n            this.handleValues[1] = 100;\n            this.offset = this.handleValues[1];\n          } else if (value < this.min) {\n            value = this.min;\n            this.handleValues[1] = 0;\n          } else if (value < this.values[0]) {\n            this.offset = this.handleValues[1];\n          }\n          this.sliderHandleEnd?.nativeElement.focus();\n        }\n        if (this.step) {\n          this.updateHandleValue();\n        } else {\n          this.updateDiffAndOffset();\n        }\n        this.values[this.handleIndex] = this.getNormalizedValue(value);\n        let newValues = [this.minVal, this.maxVal];\n        this.onModelChange(newValues);\n        this.onChange.emit({\n          event: event,\n          values: this.values\n        });\n      } else {\n        if (val < this.min) {\n          val = this.min;\n          this.handleValue = 0;\n        } else if (val > this.max) {\n          val = this.max;\n          this.handleValue = 100;\n        }\n        this.value = this.getNormalizedValue(val);\n        this.onModelChange(this.value);\n        this.onChange.emit({\n          event: event,\n          value: this.value\n        });\n        this.sliderHandle?.nativeElement.focus();\n      }\n      this.updateHandleValue();\n    }\n    getValueFromHandle(handleValue) {\n      return (this.max - this.min) * (handleValue / 100) + this.min;\n    }\n    getDecimalsCount(value) {\n      if (value && Math.floor(value) !== value) return value.toString().split('.')[1].length || 0;\n      return 0;\n    }\n    getNormalizedValue(val) {\n      let decimalsCount = this.getDecimalsCount(this.step);\n      if (decimalsCount > 0) {\n        return +parseFloat(val.toString()).toFixed(decimalsCount);\n      } else {\n        return Math.floor(val);\n      }\n    }\n    ngOnDestroy() {\n      this.unbindDragListeners();\n    }\n    get minVal() {\n      return Math.min(this.values[1], this.values[0]);\n    }\n    get maxVal() {\n      return Math.max(this.values[1], this.values[0]);\n    }\n    static ɵfac = function Slider_Factory(t) {\n      return new (t || Slider)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Slider,\n      selectors: [[\"p-slider\"]],\n      viewQuery: function Slider_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sliderHandle = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sliderHandleStart = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sliderHandleEnd = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        animate: \"animate\",\n        disabled: \"disabled\",\n        min: \"min\",\n        max: \"max\",\n        orientation: \"orientation\",\n        step: \"step\",\n        range: \"range\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        tabindex: \"tabindex\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onSlideEnd: \"onSlideEnd\"\n      },\n      features: [i0.ɵɵProvidersFeature([SLIDER_VALUE_ACCESSOR])],\n      decls: 8,\n      vars: 18,\n      consts: [[\"sliderHandle\", \"\"], [\"sliderHandleStart\", \"\"], [\"sliderHandleEnd\", \"\"], [3, \"click\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-slider-range\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-slider-handle\", \"role\", \"slider\", 3, \"transition\", \"ngStyle\", \"touchstart\", \"touchmove\", \"touchend\", \"mousedown\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-slider-handle\", \"role\", \"slider\", 3, \"transition\", \"ngStyle\", \"ngClass\", \"keydown\", \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", 4, \"ngIf\"], [\"class\", \"p-slider-handle\", 3, \"transition\", \"ngStyle\", \"ngClass\", \"keydown\", \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", 4, \"ngIf\"], [1, \"p-slider-range\", 3, \"ngStyle\"], [\"role\", \"slider\", 1, \"p-slider-handle\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"mousedown\", \"keydown\", \"ngStyle\"], [\"role\", \"slider\", 1, \"p-slider-handle\", 3, \"keydown\", \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", \"ngStyle\", \"ngClass\"], [1, \"p-slider-handle\", 3, \"keydown\", \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", \"ngStyle\", \"ngClass\"]],\n      template: function Slider_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function Slider_Template_div_click_0_listener($event) {\n            return ctx.onBarClick($event);\n          });\n          i0.ɵɵtemplate(1, Slider_span_1_Template, 1, 5, \"span\", 4)(2, Slider_span_2_Template, 1, 5, \"span\", 4)(3, Slider_span_3_Template, 1, 4, \"span\", 4)(4, Slider_span_4_Template, 1, 4, \"span\", 4)(5, Slider_span_5_Template, 2, 14, \"span\", 5)(6, Slider_span_6_Template, 2, 17, \"span\", 6)(7, Slider_span_7_Template, 2, 17, \"span\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction4(13, _c3, ctx.disabled, ctx.orientation == \"horizontal\", ctx.orientation == \"vertical\", ctx.animate));\n          i0.ɵɵattribute(\"data-pc-name\", \"slider\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.range && ctx.orientation == \"horizontal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.range && ctx.orientation == \"vertical\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.range && ctx.orientation == \"vertical\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.range && ctx.orientation == \"horizontal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.range);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.range);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.range);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n      styles: [\"@layer primeng{.p-slider{position:relative}.p-slider .p-slider-handle{position:absolute;cursor:grab;touch-action:none;display:block}.p-slider-range{position:absolute;display:block}.p-slider-horizontal .p-slider-range{top:0;left:0;height:100%}.p-slider-horizontal .p-slider-handle{top:50%}.p-slider-vertical{height:100px}.p-slider-vertical .p-slider-handle{left:50%}.p-slider-vertical .p-slider-range{bottom:0;left:0;width:100%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Slider;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SliderModule = /*#__PURE__*/(() => {\n  class SliderModule {\n    static ɵfac = function SliderModule_Factory(t) {\n      return new (t || SliderModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SliderModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return SliderModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SLIDER_VALUE_ACCESSOR, Slider, SliderModule };\n//# sourceMappingURL=primeng-slider.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}