{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./competitors.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/multiselect\";\nfunction CompetitorsComponent_ng_template_14_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction CompetitorsComponent_ng_template_14_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 26);\n    i0.ɵɵlistener(\"click\", function CompetitorsComponent_ng_template_14_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, CompetitorsComponent_ng_template_14_ng_container_8_i_4_Template, 1, 1, \"i\", 21)(5, CompetitorsComponent_ng_template_14_ng_container_8_i_5_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 18);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 19);\n    i0.ɵɵlistener(\"click\", function CompetitorsComponent_ng_template_14_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"competitor_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5, \" ID \");\n    i0.ɵɵtemplate(6, CompetitorsComponent_ng_template_14_i_6_Template, 1, 1, \"i\", 21)(7, CompetitorsComponent_ng_template_14_i_7_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CompetitorsComponent_ng_template_14_ng_container_8_Template, 6, 4, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"competitor_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"competitor_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.name) || \"-\", \" \");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.status_code) || \"-\", \" \");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 31);\n    i0.ɵɵtemplate(3, CompetitorsComponent_ng_template_15_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 32)(4, CompetitorsComponent_ng_template_15_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 32);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"status_code\");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"td\", 28);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CompetitorsComponent_ng_template_15_ng_container_5_Template, 5, 3, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", competitor_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.competitor_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction CompetitorsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \" No competitor found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CompetitorsComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \" Loading competitor data. Please wait. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CompetitorsComponent = /*#__PURE__*/(() => {\n  class CompetitorsComponent {\n    constructor(competitorsservice) {\n      this.competitorsservice = competitorsservice;\n      this.unsubscribe$ = new Subject();\n      this.competitors = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n      this.searchInputChanged = new Subject();\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'name',\n        header: 'Name'\n      }, {\n        field: 'status_code',\n        header: 'Status'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.competitors.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.searchInputChanged.pipe(debounceTime(500),\n      // Adjust delay here (ms)\n      distinctUntilChanged()).subscribe(term => {\n        this.globalSearchTerm = term;\n        this.loadCompetitors({\n          first: 0,\n          rows: 15\n        });\n      });\n      this.breadcrumbitems = [{\n        label: 'Competitors',\n        routerLink: ['/store/competitors']\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: ['/']\n      };\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadCompetitors(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      this.competitorsservice.getCompetitors(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n        next: response => {\n          this.competitors = response?.data ?? [];\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching competitors', error);\n          this.loading = false;\n        }\n      });\n    }\n    onSearchInputChange(event) {\n      const input = event.target.value;\n      this.searchInputChanged.next(input);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function CompetitorsComponent_Factory(t) {\n        return new (t || CompetitorsComponent)(i0.ɵɵdirectiveInject(i1.CompetitorsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CompetitorsComponent,\n        selectors: [[\"app-competitors\"]],\n        decls: 18,\n        vars: 14,\n        consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Competitor\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"totalRecords\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"6\", 1, \"border-round-left-lg\", \"pl-3\"]],\n        template: function CompetitorsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompetitorsComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function CompetitorsComponent_Template_input_input_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"i\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"p-multiSelect\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompetitorsComponent_Template_p_multiSelect_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 12)(12, \"p-table\", 13, 1);\n            i0.ɵɵlistener(\"onLazyLoad\", function CompetitorsComponent_Template_p_table_onLazyLoad_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadCompetitors($event));\n            })(\"onColReorder\", function CompetitorsComponent_Template_p_table_onColReorder_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(14, CompetitorsComponent_ng_template_14_Template, 9, 3, \"ng-template\", 14)(15, CompetitorsComponent_ng_template_15_Template, 6, 3, \"ng-template\", 15)(16, CompetitorsComponent_ng_template_16_Template, 3, 0, \"ng-template\", 16)(17, CompetitorsComponent_ng_template_17_Template, 3, 0, \"ng-template\", 17);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.competitors)(\"rows\", 15)(\"totalRecords\", ctx.totalRecords)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.Breadcrumb, i4.PrimeTemplate, i5.Table, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.MultiSelect]\n      });\n    }\n  }\n  return CompetitorsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}