{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nimport * as i14 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction TaskOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29)(4, \"span\", 30);\n    i0.ɵɵtext(5, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"label\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Subject \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \"Transaction Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 32)(32, \"a\", 33);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 28)(36, \"label\", 29)(37, \"span\", 30);\n    i0.ɵɵtext(38, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 32)(41, \"a\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 29)(46, \"span\", 30);\n    i0.ɵɵtext(47, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 31);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 27)(52, \"div\", 28)(53, \"label\", 29)(54, \"span\", 30);\n    i0.ɵɵtext(55, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Processor \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 31);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(59, \"div\", 27)(60, \"div\", 28)(61, \"label\", 29)(62, \"span\", 30);\n    i0.ɵɵtext(63, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(64, \" Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 31);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 27)(69, \"div\", 28)(70, \"label\", 29)(71, \"span\", 30);\n    i0.ɵɵtext(72, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(73, \" Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 31);\n    i0.ɵɵtext(75);\n    i0.ɵɵpipe(76, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(77, \"div\", 27)(78, \"div\", 28)(79, \"label\", 29)(80, \"span\", 30);\n    i0.ɵɵtext(81, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(82, \" Priority \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 31);\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(85, \"div\", 27)(86, \"div\", 28)(87, \"label\", 29)(88, \"span\", 30);\n    i0.ɵɵtext(89, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(90, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 31);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.subject) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityDocumentType\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.document_type) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0] == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.task_category) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_processor == null ? null : ctx_r0.overviewDetails.business_partner_processor.bp_full_name) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date) ? i0.ɵɵpipeBind2(67, 13, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date) ? i0.ɵɵpipeBind2(76, 16, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityPriority\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.priority) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_status) || \"-\", \" \");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, TaskOverviewComponent_form_6_ng_template_12_span_2_Template, 2, 1, \"span\", 50);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_13_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors && ctx_r0.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.mobile, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, TaskOverviewComponent_form_6_ng_template_24_span_2_Template, 2, 1, \"span\", 50)(3, TaskOverviewComponent_form_6_ng_template_24_span_3_Template, 2, 1, \"span\", 50);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bp_id, \": \", item_r4.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.mobile);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_25_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors && ctx_r0.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_35_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors && ctx_r0.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_45_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"task_category\"].errors && ctx_r0.f[\"task_category\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_54_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_54_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TaskOverviewComponent_form_6_ng_template_54_span_3_Template, 2, 1, \"span\", 50)(4, TaskOverviewComponent_form_6_ng_template_54_span_4_Template, 2, 1, \"span\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_85_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_85_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors && ctx_r0.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\", 35)(5, \"span\", 36);\n    i0.ɵɵtext(6, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \"Account \");\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ng-select\", 38);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, TaskOverviewComponent_form_6_ng_template_12_Template, 3, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, TaskOverviewComponent_form_6_div_13_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 27)(15, \"div\", 28)(16, \"label\", 35)(17, \"span\", 36);\n    i0.ɵɵtext(18, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \"Contact \");\n    i0.ɵɵelementStart(20, \"span\", 37);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"ng-select\", 41);\n    i0.ɵɵpipe(23, \"async\");\n    i0.ɵɵtemplate(24, TaskOverviewComponent_form_6_ng_template_24_Template, 4, 4, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TaskOverviewComponent_form_6_div_25_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"div\", 28)(28, \"label\", 35)(29, \"span\", 36);\n    i0.ɵɵtext(30, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Subject \");\n    i0.ɵɵelementStart(32, \"span\", 37);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"input\", 42);\n    i0.ɵɵtemplate(35, TaskOverviewComponent_form_6_div_35_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 27)(37, \"div\", 28)(38, \"label\", 35)(39, \"span\", 36);\n    i0.ɵɵtext(40, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \"Category \");\n    i0.ɵɵelementStart(42, \"span\", 37);\n    i0.ɵɵtext(43, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(44, \"p-dropdown\", 43);\n    i0.ɵɵtemplate(45, TaskOverviewComponent_form_6_div_45_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 27)(47, \"div\", 28)(48, \"label\", 35)(49, \"span\", 36);\n    i0.ɵɵtext(50, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \"Processor \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"ng-select\", 44);\n    i0.ɵɵpipe(53, \"async\");\n    i0.ɵɵtemplate(54, TaskOverviewComponent_form_6_ng_template_54_Template, 5, 4, \"ng-template\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 27)(56, \"div\", 28)(57, \"label\", 35)(58, \"span\", 36);\n    i0.ɵɵtext(59, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \"Create Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"p-calendar\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 27)(63, \"div\", 28)(64, \"label\", 35)(65, \"span\", 36);\n    i0.ɵɵtext(66, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \"Expected Decision Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"p-calendar\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 27)(70, \"div\", 28)(71, \"label\", 35)(72, \"span\", 36);\n    i0.ɵɵtext(73, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(74, \"Priority \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(75, \"p-dropdown\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"div\", 27)(77, \"div\", 28)(78, \"label\", 35)(79, \"span\", 36);\n    i0.ɵɵtext(80, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \"Status \");\n    i0.ɵɵelementStart(82, \"span\", 37);\n    i0.ɵɵtext(83, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(84, \"p-dropdown\", 48);\n    i0.ɵɵtemplate(85, TaskOverviewComponent_form_6_div_85_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(86, \"div\", 49)(87, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_form_6_Template_button_click_87_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onUpdate());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.TaskOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(11, 42, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(48, _c2, ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(23, 44, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(50, _c2, ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c2, ctx_r0.submitted && ctx_r0.f[\"subject\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityCategory\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(54, _c2, ctx_r0.submitted && ctx_r0.f[\"task_category\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"task_category\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(53, 46, ctx_r0.employees$))(\"hideSelected\", true)(\"loading\", ctx_r0.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.employeeInput$)(\"maxSelectedItems\", 10)(\"closeOnSelect\", false);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityPriority\"]);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(56, _c2, ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 61);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener() {\n      const col_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r8.field, ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template, 1, 1, \"i\", 55)(5, TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template, 1, 0, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r8.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r8.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== col_r8.field);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 53);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_16_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.customSort(\"note\", ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 54);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, TaskOverviewComponent_ng_template_16_i_4_Template, 1, 1, \"i\", 55)(5, TaskOverviewComponent_ng_template_16_i_5_Template, 1, 0, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TaskOverviewComponent_ng_template_16_ng_container_6_Template, 6, 4, \"ng-container\", 57);\n    i0.ɵɵelementStart(7, \"th\", 58);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r10 == null ? null : notes_r10.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (notes_r10 == null ? null : notes_r10.updatedBy) || \"-\", \" \");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 66);\n    i0.ɵɵtemplate(3, TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 67)(4, TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template, 2, 1, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r11.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedBy\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 62);\n    i0.ɵɵelement(1, \"td\", 63);\n    i0.ɵɵtemplate(2, TaskOverviewComponent_ng_template_17_ng_container_2_Template, 5, 3, \"ng-container\", 57);\n    i0.ɵɵelementStart(3, \"td\", 58)(4, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_17_Template_button_click_4_listener() {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_17_Template_button_click_5_listener($event) {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r10));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r10 == null ? null : notes_r10.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction TaskOverviewComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 68);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 68);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskOverviewComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_div_26_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class TaskOverviewComponent {\n  constructor(formBuilder, activitiesservice, messageservice, confirmationservice) {\n    this.formBuilder = formBuilder;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.notedetails = null;\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityStatus: [],\n      activityCategory: [],\n      activityPriority: []\n    };\n    this.TaskOverviewForm = this.formBuilder.group({\n      subject: ['', [Validators.required]],\n      main_account_party_id: ['', [Validators.required]],\n      main_contact_party_id: ['', [Validators.required]],\n      task_category: ['', [Validators.required]],\n      processor_party_id: [''],\n      start_date: [''],\n      end_date: [''],\n      priority: [''],\n      activity_status: ['', [Validators.required]]\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this._selectedNotesColumns = [];\n    this.NotesCols = [{\n      field: 'updatedAt',\n      header: 'Last Updated On'\n    }, {\n      field: 'updatedBy',\n      header: 'Updated By'\n    }];\n    this.sortFieldNotes = '';\n    this.sortOrderNotes = 1;\n    this.compareById = (a, b) => a === b;\n  }\n  ngOnInit() {\n    // task successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('taskMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('taskMessage');\n      }\n    }, 100);\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');\n    this.TaskOverviewForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.loadAccounts();\n    this.loadEmployees();\n    this.activitiesservice.activity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.id = response?.activity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n    this._selectedNotesColumns = this.NotesCols;\n  }\n  get selectedNotesColumns() {\n    return this._selectedNotesColumns;\n  }\n  set selectedNotesColumns(val) {\n    this._selectedNotesColumns = this.NotesCols.filter(col => val.includes(col));\n  }\n  onNotesColumnReorder(event) {\n    const draggedCol = this.NotesCols[event.dragIndex];\n    this.NotesCols.splice(event.dragIndex, 1);\n    this.NotesCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'Notes') {\n      this.sortFieldNotes = field;\n      this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrderNotes * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(activity) {\n    this.existingActivity = {\n      main_account_party_id: activity?.main_account_party_id,\n      main_contact_party_id: activity?.main_contact_party_id,\n      subject: activity?.subject,\n      task_category: activity?.task_category,\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\n      activity_status: activity?.activity_status\n    };\n    this.editid = activity.documentId;\n    this.TaskOverviewForm.patchValue(this.existingActivity);\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.accountInput$.pipe(distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\n        [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.accountLoading = false), catchError(error => {\n        this.accountLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  loadEmployees() {\n    this.employees$ = concat(of(this.defaultOptions),\n    // Emit default empty options first\n    this.employeeInput$.pipe(debounceTime(300),\n    // Add debounce to reduce API calls\n    distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        'filters[roles][bp_role][$in][0]': 'BUP003',\n        'fields[0]': 'bp_id',\n        'fields[1]': 'first_name',\n        'fields[2]': 'last_name',\n        'fields[3]': 'bp_full_name'\n      };\n      if (term) {\n        params['filters[$or][0][bp_id][$containsi]'] = term;\n        params['filters[$or][1][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(response => response ?? []),\n      // Ensure non-null\n      catchError(error => {\n        console.error('Employee fetch error:', error);\n        return of([]); // Return empty list on error\n      }), finalize(() => this.employeeLoading = false) // Always turn off loading\n      );\n    })));\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        activity_id: _this.id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.activitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.activitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onUpdate() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.TaskOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.TaskOverviewForm.value\n      };\n      const data = {\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        task_category: value?.task_category,\n        start_date: value?.start_date ? _this2.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this2.formatDate(value.end_date) : null,\n        processor_party_id: value?.processor_party_id,\n        priority: value?.priority,\n        activity_status: value?.activity_status\n      };\n      _this2.activitiesservice.updateActivity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Task Updated successFully!'\n          });\n          _this2.activitiesservice.getActivityByID(_this2.id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.isEditMode = true;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.TaskOverviewForm.controls;\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function TaskOverviewComponent_Factory(t) {\n      return new (t || TaskOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskOverviewComponent,\n      selectors: [[\"app-task-overview\"]],\n      decls: 30,\n      vars: 31,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"task_category\", \"placeholder\", \"Select Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"processor_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"closeOnSelect\"], [\"formControlName\", \"start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Create Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Expected Decision Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"priority\", \"placeholder\", \"Select a Prioriry\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [4, \"ngIf\"], [1, \"p-error\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"note-text\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function TaskOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, TaskOverviewComponent_div_5_Template, 93, 19, \"div\", 4)(6, TaskOverviewComponent_form_6_Template, 88, 58, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"p-button\", 9);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_p_button_click_12_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-multiSelect\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedNotesColumns, $event) || (ctx.selectedNotesColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-table\", 12);\n          i0.ɵɵlistener(\"onColReorder\", function TaskOverviewComponent_Template_p_table_onColReorder_15_listener($event) {\n            return ctx.onNotesColumnReorder($event);\n          });\n          i0.ɵɵtemplate(16, TaskOverviewComponent_ng_template_16_Template, 9, 3, \"ng-template\", 13)(17, TaskOverviewComponent_ng_template_17_Template, 6, 2, \"ng-template\", 14)(18, TaskOverviewComponent_ng_template_18_Template, 3, 0, \"ng-template\", 15)(19, TaskOverviewComponent_ng_template_19_Template, 3, 0, \"ng-template\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"p-dialog\", 17);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function TaskOverviewComponent_Template_p_dialog_visibleChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(21, TaskOverviewComponent_ng_template_21_Template, 2, 0, \"ng-template\", 13);\n          i0.ɵɵelementStart(22, \"form\", 18)(23, \"div\", 19)(24, \"div\", 20);\n          i0.ɵɵelement(25, \"p-editor\", 21);\n          i0.ɵɵtemplate(26, TaskOverviewComponent_div_26_Template, 2, 1, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 23)(28, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_button_click_28_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_button_click_29_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.NotesCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedNotesColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(27, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(28, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i14.MultiSelect, i4.AsyncPipe, i4.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy90YXNrL3Rhc2stZGV0YWlscy90YXNrLW92ZXJ2aWV3L3Rhc2stb3ZlcnZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7RUFJSSxxQkFBQTtFQUNBLFdBQUE7QUFDSjs7QUFJUTtFQUNJLGtCQUFBO0FBRFo7QUFHWTtFQUNJLDRCQUFBO0VBQ0EsMkNBQUE7QUFEaEI7QUFHZ0I7RUFDSSxTQUFBO0FBRHBCO0FBS1k7RUFDSSw0QkFBQTtFQUNBLGlCQUFBO0FBSGhCIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gICAgY29sb3I6IHZhcigtLXJlZC01MDApO1xyXG4gICAgcmlnaHQ6IDEwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCB7XHJcbiAgICAubm90ZS1wb3B1cCB7XHJcbiAgICAgICAgLnAtZGlhbG9nIHtcclxuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1MHB4O1xyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXN1cmZhY2UtMTAwKTtcclxuXHJcbiAgICAgICAgICAgICAgICBoNCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS43MTRyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "activity_id", "subject", "getLabelFromDropdown", "document_type", "ɵɵproperty", "business_partner", "documentId", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "bp_full_name", "business_partner_contact", "contact_persons", "task_category", "business_partner_processor", "start_date", "ɵɵpipeBind2", "end_date", "priority", "activity_status", "item_r3", "ɵɵtemplate", "TaskOverviewComponent_form_6_ng_template_12_span_2_Template", "bp_id", "TaskOverviewComponent_form_6_div_13_div_1_Template", "submitted", "f", "errors", "item_r4", "email", "mobile", "TaskOverviewComponent_form_6_ng_template_24_span_2_Template", "TaskOverviewComponent_form_6_ng_template_24_span_3_Template", "ɵɵtextInterpolate2", "TaskOverviewComponent_form_6_div_25_div_1_Template", "TaskOverviewComponent_form_6_div_35_div_1_Template", "TaskOverviewComponent_form_6_div_45_div_1_Template", "item_r5", "TaskOverviewComponent_form_6_ng_template_54_span_3_Template", "TaskOverviewComponent_form_6_ng_template_54_span_4_Template", "TaskOverviewComponent_form_6_div_85_div_1_Template", "TaskOverviewComponent_form_6_ng_template_12_Template", "TaskOverviewComponent_form_6_div_13_Template", "TaskOverviewComponent_form_6_ng_template_24_Template", "TaskOverviewComponent_form_6_div_25_Template", "ɵɵelement", "TaskOverviewComponent_form_6_div_35_Template", "TaskOverviewComponent_form_6_div_45_Template", "TaskOverviewComponent_form_6_ng_template_54_Template", "TaskOverviewComponent_form_6_div_85_Template", "ɵɵlistener", "TaskOverviewComponent_form_6_Template_button_click_87_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onUpdate", "TaskOverviewForm", "ɵɵpipeBind1", "accounts$", "accountLoading", "compareById", "accountInput$", "ɵɵpureFunction1", "_c2", "contacts$", "contactLoading", "contactInput$", "dropdowns", "ɵɵclassMap", "employees$", "employeeLoading", "employeeInput$", "sortOrderNotes", "ɵɵelementContainerStart", "TaskOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener", "col_r8", "_r7", "$implicit", "customSort", "field", "notedetails", "TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template", "TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template", "header", "sortFieldNotes", "TaskOverviewComponent_ng_template_16_Template_th_click_1_listener", "_r6", "TaskOverviewComponent_ng_template_16_i_4_Template", "TaskOverviewComponent_ng_template_16_i_5_Template", "TaskOverviewComponent_ng_template_16_ng_container_6_Template", "selectedNotesColumns", "notes_r10", "updatedAt", "updatedBy", "TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template", "TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template", "col_r11", "TaskOverviewComponent_ng_template_17_ng_container_2_Template", "TaskOverviewComponent_ng_template_17_Template_button_click_4_listener", "_r9", "editNote", "TaskOverviewComponent_ng_template_17_Template_button_click_5_listener", "$event", "stopPropagation", "confirmRemove", "note", "ɵɵsanitizeHtml", "TaskOverviewComponent_div_26_div_1_Template", "fNote", "TaskOverviewComponent", "constructor", "formBuilder", "activitiesservice", "messageservice", "confirmationservice", "ngUnsubscribe", "defaultOptions", "saving", "id", "editid", "isEditMode", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "activityDocumentType", "activityStatus", "activityCategory", "activityPriority", "group", "required", "main_account_party_id", "main_contact_party_id", "processor_party_id", "NoteForm", "_selectedNotesColumns", "NotesCols", "a", "b", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadActivityDropDown", "get", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "err", "console", "error", "subscribe", "loadAccounts", "loadEmployees", "activity", "response", "notes", "fetchOverviewData", "val", "filter", "col", "includes", "onNotesColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "data", "type", "sort", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "value", "i", "length", "target", "getActivityDropdownOptions", "res", "attr", "label", "description", "code", "dropdownKey", "item", "find", "opt", "patchValue", "existingActivity", "Date", "term", "params", "getPartners", "bpId", "getPartnersContact", "contacts", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getActivityByID", "createNote", "_this2", "formatDate", "updateActivity", "next", "confirm", "message", "icon", "accept", "remove", "deleteNote", "stripHtml", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showDialog", "position", "toggleEdit", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivitiesService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "TaskOverviewComponent_Template", "rf", "ctx", "TaskOverviewComponent_Template_p_button_click_4_listener", "TaskOverviewComponent_div_5_Template", "TaskOverviewComponent_form_6_Template", "TaskOverviewComponent_Template_p_button_click_12_listener", "ɵɵtwoWayListener", "TaskOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener", "ɵɵtwoWayBindingSet", "TaskOverviewComponent_Template_p_table_onColReorder_15_listener", "TaskOverviewComponent_ng_template_16_Template", "TaskOverviewComponent_ng_template_17_Template", "TaskOverviewComponent_ng_template_18_Template", "TaskOverviewComponent_ng_template_19_Template", "TaskOverviewComponent_Template_p_dialog_visibleChange_20_listener", "TaskOverviewComponent_ng_template_21_Template", "TaskOverviewComponent_div_26_Template", "TaskOverviewComponent_Template_button_click_28_listener", "TaskOverviewComponent_Template_button_click_29_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-overview\\task-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-overview\\task-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n  finalize,\r\n} from 'rxjs/operators';\r\n\r\ninterface NotesColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-task-overview',\r\n  templateUrl: './task-overview.component.html',\r\n  styleUrl: './task-overview.component.scss',\r\n})\r\nexport class TaskOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingActivity: any;\r\n  public id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public notedetails: any = null;\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityStatus: [],\r\n    activityCategory: [],\r\n    activityPriority: [],\r\n  };\r\n\r\n  public TaskOverviewForm: FormGroup = this.formBuilder.group({\r\n    subject: ['', [Validators.required]],\r\n    main_account_party_id: ['', [Validators.required]],\r\n    main_contact_party_id: ['', [Validators.required]],\r\n    task_category: ['', [Validators.required]],\r\n    processor_party_id: [''],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    priority: [''],\r\n    activity_status: ['', [Validators.required]],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  private _selectedNotesColumns: NotesColumn[] = [];\r\n\r\n  public NotesCols: NotesColumn[] = [\r\n    { field: 'updatedAt', header: 'Last Updated On' },\r\n    { field: 'updatedBy', header: 'Updated By' },\r\n  ];\r\n\r\n  sortFieldNotes: string = '';\r\n  sortOrderNotes: number = 1;\r\n\r\n  ngOnInit(): void {\r\n    // task successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('taskMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('taskMessage');\r\n      }\r\n    }, 100);\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');\r\n    this.TaskOverviewForm.get('main_account_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.ngUnsubscribe),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.loadAccounts();\r\n    this.loadEmployees();\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.id = response?.activity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n\r\n    this._selectedNotesColumns = this.NotesCols;\r\n  }\r\n\r\n  get selectedNotesColumns(): any[] {\r\n    return this._selectedNotesColumns;\r\n  }\r\n\r\n  set selectedNotesColumns(val: any[]) {\r\n    this._selectedNotesColumns = this.NotesCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  onNotesColumnReorder(event: any) {\r\n    const draggedCol = this.NotesCols[event.dragIndex];\r\n    this.NotesCols.splice(event.dragIndex, 1);\r\n    this.NotesCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(field: string, data: any[], type: 'Notes') {\r\n    if (type === 'Notes') {\r\n      this.sortFieldNotes = field;\r\n      this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrderNotes * result;\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(activity: any) {\r\n    this.existingActivity = {\r\n      main_account_party_id: activity?.main_account_party_id,\r\n      main_contact_party_id: activity?.main_contact_party_id,\r\n      subject: activity?.subject,\r\n      task_category: activity?.task_category,\r\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\r\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\r\n      activity_status: activity?.activity_status,\r\n    };\r\n\r\n    this.editid = activity.documentId;\r\n    this.TaskOverviewForm.patchValue(this.existingActivity);\r\n  }\r\n\r\n  private loadAccounts() {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.accountInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\r\n            [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.accountLoading = false)),\r\n            catchError((error) => {\r\n              this.accountLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  private loadEmployees(): void {\r\n    this.employees$ = concat(\r\n      of(this.defaultOptions), // Emit default empty options first\r\n      this.employeeInput$.pipe(\r\n        debounceTime(300), // Add debounce to reduce API calls\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            'filters[roles][bp_role][$in][0]': 'BUP003',\r\n            'fields[0]': 'bp_id',\r\n            'fields[1]': 'first_name',\r\n            'fields[2]': 'last_name',\r\n            'fields[3]': 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params['filters[$or][0][bp_id][$containsi]'] = term;\r\n            params['filters[$or][1][bp_full_name][$containsi]'] = term;\r\n          }\r\n\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((response: any) => response ?? []), // Ensure non-null\r\n            catchError((error) => {\r\n              console.error('Employee fetch error:', error);\r\n              return of([]); // Return empty list on error\r\n            }),\r\n            finalize(() => (this.employeeLoading = false)) // Always turn off loading\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      activity_id: this.id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.activitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.activitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onUpdate() {\r\n    this.submitted = true;\r\n\r\n    if (this.TaskOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.TaskOverviewForm.value };\r\n\r\n    const data = {\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      task_category: value?.task_category,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      processor_party_id: value?.processor_party_id,\r\n      priority: value?.priority,\r\n      activity_status: value?.activity_status,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .updateActivity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Task Updated successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.isEditMode = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.TaskOverviewForm.controls;\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  compareById = (a: any, b: any) => a === b;\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil':''\" iconPos=\"right\"\r\n            class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold px-3'\" (click)=\"toggleEdit()\" [rounded]=\"true\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.activity_id || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">subject</span> Subject\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.subject || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">description</span>Transaction Type\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityDocumentType',\r\n                    overviewDetails?.document_type) || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/account/' + overviewDetails?.business_partner?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/contacts/' + overviewDetails?.business_partner_contact?.contact_persons?.[0]?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner_contact?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    getLabelFromDropdown('activityCategory',overviewDetails?.task_category)\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Processor\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_processor?.bp_full_name\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> Create Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.start_date ?\r\n                    (overviewDetails?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> Expected Decision Date\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.end_date ?\r\n                    (overviewDetails?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">flag</span> Priority\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityPriority',\r\n                    overviewDetails?.priority) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">check_circle</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityStatus',\r\n                    overviewDetails?.activity_status) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"TaskOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_account_party_id\" [compareWith]=\"compareById\" [typeahead]=\"accountInput$\"\r\n                        [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_account_party_id'].errors &&\r\n                                f['main_account_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">person</span>Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_contact_party_id'].errors &&\r\n                                f['main_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">subject</span> Subject\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['subject'].errors &&\r\n                                    f['subject'].errors['required']\r\n                                  \">\r\n                            Subject is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"task_category\"\r\n                        placeholder=\"Select Category\" optionLabel=\"label\" optionValue=\"value\"\r\n                        [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['task_category'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['task_category'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['task_category'].errors &&\r\n                                f['task_category'].errors['required']\r\n                              \">\r\n                            Category is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">person</span>Processor\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"employees$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"employeeLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"processor_party_id\" [typeahead]=\"employeeInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [closeOnSelect]=\"false\"\r\n                        [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </div>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>Create Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"start_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Create Date\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>Expected Decision Date\r\n                    </label>\r\n                    <p-calendar formControlName=\"end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Expected Decision Date\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">flag</span>Priority\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityPriority']\" formControlName=\"priority\"\r\n                        placeholder=\"Select a Prioriry\" optionLabel=\"label\" optionValue=\"value\"\r\n                        styleClass=\"h-3rem w-full\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">check_circle</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                        placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['activity_status'].errors &&\r\n                                f['activity_status'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onUpdate()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"NotesCols\" [(ngModel)]=\"selectedNotesColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"notedetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onNotesColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg\" (click)=\"customSort('note', notedetails, 'Notes')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Note\r\n                            <i *ngIf=\"sortFieldNotes === 'note'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldNotes !== 'note'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedNotesColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, notedetails, 'Notes')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldNotes === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldNotes !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg note-text\" [innerHTML]=\"notes?.note || '-'\">\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedNotesColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedBy'\">\r\n                                    {{ notes?.updatedBy || '-'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAGtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,QACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICHHC,EAJhB,CAAAC,cAAA,cAA6D,cACV,cACnB,gBACqD,eACR;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,WAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAEhGF,EAFgG,CAAAG,YAAA,EAAM,EAC5F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,yBACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAErD;IAGZF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IA/G2DH,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,WAAA,SAC/C;IAQ+CR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,OAAA,SAAmC;IAQnCT,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,yBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,aAAA,SAE/C;IASCX,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAY,UAAA,gCAAAN,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAC,UAAA,iBAAAd,EAAA,CAAAe,aAAA,CAA0F;IAEzFf,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAI,YAAA,cACJ;IAWGjB,EAAA,CAAAI,SAAA,GAAyH;IAAzHJ,EAAA,CAAAY,UAAA,iCAAAN,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,wBAAA,kBAAAZ,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,kBAAAb,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,qBAAAb,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,IAAAL,UAAA,iBAAAd,EAAA,CAAAe,aAAA,CAAyH;IAExHf,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,wBAAA,kBAAAZ,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAD,YAAA,cACJ;IASiDjB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,qBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAa,aAAA,SAEzC;IAQyCpB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAc,0BAAA,kBAAAf,MAAA,CAAAC,eAAA,CAAAc,0BAAA,CAAAJ,YAAA,SAEzC;IAQyCjB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,UAAA,IAAAtB,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,UAAA,mCAGrD;IAQqDtB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,QAAA,IAAAxB,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAiB,QAAA,mCAGrD;IAQqDxB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,qBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,QAAA,cAErD;IAQqDzB,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,mBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,eAAA,cAErD;;;;;IAmBY1B,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAgB,kBAAA,QAAAW,OAAA,CAAAV,YAAA,KAAyB;;;;;IAD1DjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAA4B,UAAA,IAAAC,2DAAA,mBAAgC;;;;IAD1B7B,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAsB,OAAA,CAAAG,KAAA,CAAgB;IACf9B,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAAe,OAAA,CAAAV,YAAA,CAAuB;;;;;IAIlCjB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAA4B,UAAA,IAAAG,kDAAA,kBAIQ;IAGZ/B,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,IAAA5B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,aAID;;;;;IAmBDlC,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAgB,kBAAA,QAAAmB,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5CpC,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAgB,kBAAA,QAAAmB,OAAA,CAAAE,MAAA,KAAmB;;;;;IAF9CrC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAA4B,UAAA,IAAAU,2DAAA,mBAAyB,IAAAC,2DAAA,mBACC;;;;IAFpBvC,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAwC,kBAAA,KAAAL,OAAA,CAAAL,KAAA,QAAAK,OAAA,CAAAlB,YAAA,KAAyC;IACxCjB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAY,UAAA,SAAAuB,OAAA,CAAAC,KAAA,CAAgB;IAChBpC,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAY,UAAA,SAAAuB,OAAA,CAAAE,MAAA,CAAiB;;;;;IAI5BrC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAA4B,UAAA,IAAAa,kDAAA,kBAIQ;IAGZzC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,IAAA5B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,aAID;;;;;IAeLlC,EAAA,CAAAC,cAAA,UAIY;IACRD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAA4B,UAAA,IAAAc,kDAAA,kBAIY;IAGhB1C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIG;IAJHJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,YAAAC,MAAA,IAAA5B,MAAA,CAAA2B,CAAA,YAAAC,MAAA,aAIG;;;;;IAkBTlC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAA4B,UAAA,IAAAe,kDAAA,kBAIQ;IAGZ3C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,kBAAAC,MAAA,IAAA5B,MAAA,CAAA2B,CAAA,kBAAAC,MAAA,aAID;;;;;IAmBGlC,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAgB,kBAAA,QAAA4B,OAAA,CAAAR,KAAA,KAAkB;;;;;IAC5CpC,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAgB,kBAAA,QAAA4B,OAAA,CAAAP,MAAA,KAAmB;;;;;IAF9CrC,EADJ,CAAAC,cAAA,cAA2C,WACjC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAA4B,UAAA,IAAAiB,2DAAA,mBAAyB,IAAAC,2DAAA,mBACC;IAC9B9C,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwC,kBAAA,KAAAI,OAAA,CAAAd,KAAA,QAAAc,OAAA,CAAA3B,YAAA,KAAyC;IACxCjB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAY,UAAA,SAAAgC,OAAA,CAAAR,KAAA,CAAgB;IAChBpC,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAY,UAAA,SAAAgC,OAAA,CAAAP,MAAA,CAAiB;;;;;IA8ChCrC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAA4B,UAAA,IAAAmB,kDAAA,kBAIQ;IAGZ/C,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,oBAAAC,MAAA,IAAA5B,MAAA,CAAA2B,CAAA,oBAAAC,MAAA,aAID;;;;;;IA7JLlC,EALpB,CAAAC,cAAA,eAAwD,cACX,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC9E;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIiF;;IAC7ED,EAAA,CAAA4B,UAAA,KAAAoB,oDAAA,0BAA2C;IAI/ChD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAA4B,UAAA,KAAAqB,4CAAA,kBAA4E;IAUpFjD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBACtE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIiF;;IAC7ED,EAAA,CAAA4B,UAAA,KAAAsB,oDAAA,0BAA2C;IAK/ClD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAA4B,UAAA,KAAAuB,4CAAA,kBAA4E;IAUpFnD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAoD,SAAA,iBAC2F;IAC3FpD,EAAA,CAAA4B,UAAA,KAAAyB,4CAAA,kBAA8D;IAUtErD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAoD,SAAA,sBAIa;IACbpD,EAAA,CAAA4B,UAAA,KAAA0B,4CAAA,kBAAoE;IAU5EtD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,kBAC1E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAIuE;;IACnED,EAAA,CAAA4B,UAAA,KAAA2B,oDAAA,0BAA2C;IASvDvD,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,oBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAoD,SAAA,sBACwF;IAEhGpD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,+BAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAoD,SAAA,sBACmG;IAE3GpD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAoD,SAAA,sBAGa;IAErBpD,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC5E;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAoD,SAAA,sBAGa;IACbpD,EAAA,CAAA4B,UAAA,KAAA4B,4CAAA,kBAAsE;IAWlFxD,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAoD,kBAEvB;IAArBD,EAAA,CAAAyD,UAAA,mBAAAC,+DAAA;MAAA1D,EAAA,CAAA2D,aAAA,CAAAC,GAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAA6D,aAAA;MAAA,OAAA7D,EAAA,CAAA8D,WAAA,CAASxD,MAAA,CAAAyD,QAAA,EAAU;IAAA,EAAC;IAEhC/D,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IA7KkBH,EAAA,CAAAY,UAAA,cAAAN,MAAA,CAAA0D,gBAAA,CAA8B;IAQjBhE,EAAA,CAAAI,SAAA,IAA2B;IAI7CJ,EAJkB,CAAAY,UAAA,UAAAZ,EAAA,CAAAiE,WAAA,SAAA3D,MAAA,CAAA4D,SAAA,EAA2B,sBACxB,YAAA5D,MAAA,CAAA6D,cAAA,CAA2B,oBAAoB,gBAAA7D,MAAA,CAAA8D,WAAA,CACD,cAAA9D,MAAA,CAAA+D,aAAA,CAA4B,wBACxE,YAAArE,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAAjE,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,EACqD;IAM1ElC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,CAAoD;IAiBpClC,EAAA,CAAAI,SAAA,GAA2B;IAI7CJ,EAJkB,CAAAY,UAAA,UAAAZ,EAAA,CAAAiE,WAAA,SAAA3D,MAAA,CAAAkE,SAAA,EAA2B,sBACxB,YAAAlE,MAAA,CAAAmE,cAAA,CAA2B,oBAAoB,cAAAnE,MAAA,CAAAoE,aAAA,CACD,wBAAwB,wBACpD,YAAA1E,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAAjE,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,EACqC;IAO1ElC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,0BAAAC,MAAA,CAAoD;IAkBhClC,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAAjE,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,YAAAC,MAAA,EAA8D;IAClFlC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,YAAAC,MAAA,CAAsC;IAiBhClC,EAAA,CAAAI,SAAA,GAAyC;IAGjDJ,EAHQ,CAAAY,UAAA,YAAAN,MAAA,CAAAqE,SAAA,qBAAyC,+BAEnB,YAAA3E,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAAjE,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,kBAAAC,MAAA,EACsC;IAElElC,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,kBAAAC,MAAA,CAA4C;IAoB9ClC,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAA4E,UAAA,0DAAkE;IADlD5E,EAHE,CAAAY,UAAA,UAAAZ,EAAA,CAAAiE,WAAA,SAAA3D,MAAA,CAAAuE,UAAA,EAA4B,sBACzB,YAAAvE,MAAA,CAAAwE,eAAA,CAA4B,oBAAoB,cAAAxE,MAAA,CAAAyE,cAAA,CACJ,wBAAwB,wBAClD;IAiBF/E,EAAA,CAAAI,SAAA,GAAsB;IACjCJ,EADW,CAAAY,UAAA,uBAAsB,kBAChB;IAQRZ,EAAA,CAAAI,SAAA,GAAsB;IACpBJ,EADF,CAAAY,UAAA,uBAAsB,kBACH;IAQ9CZ,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAqE,SAAA,qBAAyC;IAYzC3E,EAAA,CAAAI,SAAA,GAAuC;IAE/CJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAAqE,SAAA,mBAAuC,YAAA3E,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAAjE,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,oBAAAC,MAAA,EAEuB;IAEpElC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA0B,SAAA,IAAA1B,MAAA,CAAA2B,CAAA,oBAAAC,MAAA,CAA8C;;;;;IA6C5ClC,EAAA,CAAAoD,SAAA,YAC2F;;;;IAAvFpD,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA0E,cAAA,yDAAkF;;;;;IACtFhF,EAAA,CAAAoD,SAAA,YAAiE;;;;;IAS7DpD,EAAA,CAAAoD,SAAA,YAC2F;;;;IAAvFpD,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA0E,cAAA,yDAAkF;;;;;IACtFhF,EAAA,CAAAoD,SAAA,YAAoE;;;;;;IAPhFpD,EAAA,CAAAiF,uBAAA,GAAuD;IACnDjF,EAAA,CAAAC,cAAA,aAC0D;IAAtDD,EAAA,CAAAyD,UAAA,mBAAAyB,iFAAA;MAAA,MAAAC,MAAA,GAAAnF,EAAA,CAAA2D,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAA6D,aAAA;MAAA,OAAA7D,EAAA,CAAA8D,WAAA,CAASxD,MAAA,CAAAgF,UAAA,CAAAH,MAAA,CAAAI,KAAA,EAAAjF,MAAA,CAAAkF,WAAA,EAAmC,OAAO,CAAC;IAAA,EAAC;IACrDxF,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,GACA;IAEAF,EAFA,CAAA4B,UAAA,IAAA6D,gEAAA,gBACuF,IAAAC,gEAAA,gBACvB;IAExE1F,EADI,CAAAG,YAAA,EAAM,EACL;;;;;;IARDH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAY,UAAA,oBAAAuE,MAAA,CAAAI,KAAA,CAA6B;IAGzBvF,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAgB,kBAAA,MAAAmE,MAAA,CAAAQ,MAAA,MACA;IAAI3F,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAsF,cAAA,KAAAT,MAAA,CAAAI,KAAA,CAAkC;IAElCvF,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAsF,cAAA,KAAAT,MAAA,CAAAI,KAAA,CAAkC;;;;;;IAhBlDvF,EADJ,CAAAC,cAAA,SAAI,aACkG;IAAnDD,EAAA,CAAAyD,UAAA,mBAAAoC,kEAAA;MAAA7F,EAAA,CAAA2D,aAAA,CAAAmC,GAAA;MAAA,MAAAxF,MAAA,GAAAN,EAAA,CAAA6D,aAAA;MAAA,OAAA7D,EAAA,CAAA8D,WAAA,CAASxD,MAAA,CAAAgF,UAAA,CAAW,MAAM,EAAAhF,MAAA,CAAAkF,WAAA,EAAe,OAAO,CAAC;IAAA,EAAC;IAC7FxF,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,aACA;IAEAF,EAFA,CAAA4B,UAAA,IAAAmE,iDAAA,gBACuF,IAAAC,iDAAA,gBAC1B;IAErEhG,EADI,CAAAG,YAAA,EAAM,EACL;IAELH,EAAA,CAAA4B,UAAA,IAAAqE,4DAAA,2BAAuD;IAWvDjG,EAAA,CAAAC,cAAA,aAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAC7CF,EAD6C,CAAAG,YAAA,EAAK,EAC7C;;;;IAlBWH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAsF,cAAA,YAA+B;IAE/B5F,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAsF,cAAA,YAA+B;IAIb5F,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA4F,oBAAA,CAAuB;;;;;IAsBzClG,EAAA,CAAAiF,uBAAA,GAA0C;IACtCjF,EAAA,CAAAE,MAAA,GACJ;;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAuB,WAAA,OAAA4E,SAAA,kBAAAA,SAAA,CAAAC,SAAA,8BACJ;;;;;IAEApG,EAAA,CAAAiF,uBAAA,GAA0C;IACtCjF,EAAA,CAAAE,MAAA,GACJ;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAmF,SAAA,kBAAAA,SAAA,CAAAE,SAAA,cACJ;;;;;IATZrG,EAAA,CAAAiF,uBAAA,GAAuD;IACnDjF,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAiF,uBAAA,OAAqC;IAKjCjF,EAJA,CAAA4B,UAAA,IAAA0E,2EAAA,2BAA0C,IAAAC,2EAAA,2BAIA;;IAKlDvG,EAAA,CAAAG,YAAA,EAAK;;;;;IAVaH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,UAAA,aAAA4F,OAAA,CAAAjB,KAAA,CAAsB;IACjBvF,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAY,UAAA,6BAAyB;IAIzBZ,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAY,UAAA,6BAAyB;;;;;;IAVxDZ,EAAA,CAAAC,cAAA,aAA2B;IACvBD,EAAA,CAAAoD,SAAA,aACK;IACLpD,EAAA,CAAA4B,UAAA,IAAA6E,4DAAA,2BAAuD;IAenDzG,EADJ,CAAAC,cAAA,aAAkC,iBAEA;IAA1BD,EAAA,CAAAyD,UAAA,mBAAAiD,sEAAA;MAAA,MAAAP,SAAA,GAAAnG,EAAA,CAAA2D,aAAA,CAAAgD,GAAA,EAAAtB,SAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAA6D,aAAA;MAAA,OAAA7D,EAAA,CAAA8D,WAAA,CAASxD,MAAA,CAAAsG,QAAA,CAAAT,SAAA,CAAe;IAAA,EAAC;IAACnG,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,iBAC8D;IAA1DD,EAAA,CAAAyD,UAAA,mBAAAoD,sEAAAC,MAAA;MAAA,MAAAX,SAAA,GAAAnG,EAAA,CAAA2D,aAAA,CAAAgD,GAAA,EAAAtB,SAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAA6D,aAAA;MAASiD,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAA/G,EAAA,CAAA8D,WAAA,CAAExD,MAAA,CAAA0G,aAAA,CAAAb,SAAA,CAAoB;IAAA,EAAE;IAErEnG,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAtBwDH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAY,UAAA,eAAAuF,SAAA,kBAAAA,SAAA,CAAAc,IAAA,UAAAjH,EAAA,CAAAkH,cAAA,CAAgC;IAE3DlH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA4F,oBAAA,CAAuB;;;;;IAyBrDlG,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAChEF,EADgE,CAAAG,YAAA,EAAK,EAChE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACnFF,EADmF,CAAAG,YAAA,EAAK,EACnF;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA4B,UAAA,IAAAuF,2CAAA,kBAA8C;IAClDnH,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8G,KAAA,SAAAlF,MAAA,aAAsC;;;ADvXhE,OAAM,MAAOmF,qBAAqB;EAiDhCC,YACUC,WAAwB,EACxBC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IApDrB,KAAAC,aAAa,GAAG,IAAIvI,OAAO,EAAQ;IACpC,KAAAmB,eAAe,GAAQ,IAAI;IAE3B,KAAA4D,cAAc,GAAG,KAAK;IACtB,KAAAE,aAAa,GAAG,IAAIjF,OAAO,EAAU;IAErC,KAAAqF,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAItF,OAAO,EAAU;IAErC,KAAA0F,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAI3F,OAAO,EAAU;IACrC,KAAAwI,cAAc,GAAQ,EAAE;IACzB,KAAA5F,SAAS,GAAG,KAAK;IACjB,KAAA6F,MAAM,GAAG,KAAK;IAEd,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAxC,WAAW,GAAQ,IAAI;IACvB,KAAAyC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAA1D,SAAS,GAA0B;MACxC2D,oBAAoB,EAAE,EAAE;MACxBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE;KACnB;IAEM,KAAAzE,gBAAgB,GAAc,IAAI,CAACuD,WAAW,CAACmB,KAAK,CAAC;MAC1DjI,OAAO,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACwJ,QAAQ,CAAC,CAAC;MACpCC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACzJ,UAAU,CAACwJ,QAAQ,CAAC,CAAC;MAClDE,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAC1J,UAAU,CAACwJ,QAAQ,CAAC,CAAC;MAClDvH,aAAa,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAACwJ,QAAQ,CAAC,CAAC;MAC1CG,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBxH,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACwJ,QAAQ,CAAC;KAC5C,CAAC;IAEK,KAAAI,QAAQ,GAAc,IAAI,CAACxB,WAAW,CAACmB,KAAK,CAAC;MAClDzB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC9H,UAAU,CAACwJ,QAAQ,CAAC;KACjC,CAAC;IASM,KAAAK,qBAAqB,GAAkB,EAAE;IAE1C,KAAAC,SAAS,GAAkB,CAChC;MAAE1D,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAiB,CAAE,EACjD;MAAEJ,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAZ,cAAc,GAAW,CAAC;IA8b1B,KAAAZ,WAAW,GAAG,CAAC8E,CAAM,EAAEC,CAAM,KAAKD,CAAC,KAAKC,CAAC;EAxctC;EAYHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;MAC5D,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC7B,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,aAAa,CAAC;MAC1C;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CAAC,kBAAkB,EAAE,uBAAuB,CAAC;IACtE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CAAC,kBAAkB,EAAE,4BAA4B,CAAC;IAC3E,IAAI,CAAC7F,gBAAgB,CAAC8F,GAAG,CAAC,uBAAuB,CAAC,EAC9CC,YAAY,CAACC,IAAI,CACjB3K,SAAS,CAAC,IAAI,CAACsI,aAAa,CAAC,EAC7BhI,GAAG,CAAEsK,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACzF,SAAS,GAAGhF,EAAE,CAAC,IAAI,CAACoI,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACF/H,UAAU,CAAEsK,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAAC3F,SAAS,GAAGhF,EAAE,CAAC,IAAI,CAACoI,cAAc,CAAC;MACxC,OAAOpI,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACA8K,SAAS,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAAChD,iBAAiB,CAACiD,QAAQ,CAC5BT,IAAI,CAAC3K,SAAS,CAAC,IAAI,CAACsI,aAAa,CAAC,CAAC,CACnC2C,SAAS,CAAEI,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAAC5C,EAAE,GAAG4C,QAAQ,EAAElK,WAAW;MAC/B,IAAI,CAACD,eAAe,GAAGmK,QAAQ;MAC/B,IAAI,CAAClF,WAAW,GAAGkF,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAACpK,eAAe,EAAE;QACxB,IAAI,CAACqK,iBAAiB,CAAC,IAAI,CAACrK,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;IAEJ,IAAI,CAACyI,qBAAqB,GAAG,IAAI,CAACC,SAAS;EAC7C;EAEA,IAAI/C,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAAC8C,qBAAqB;EACnC;EAEA,IAAI9C,oBAAoBA,CAAC2E,GAAU;IACjC,IAAI,CAAC7B,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAC6B,MAAM,CAAEC,GAAG,IACrDF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEAE,oBAAoBA,CAACC,KAAU;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAAClC,SAAS,CAACiC,KAAK,CAACE,SAAS,CAAC;IAClD,IAAI,CAACnC,SAAS,CAACoC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACzC,IAAI,CAACnC,SAAS,CAACoC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACvD;EAEA7F,UAAUA,CAACC,KAAa,EAAEgG,IAAW,EAAEC,IAAa;IAClD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAAC5F,cAAc,GAAGL,KAAK;MAC3B,IAAI,CAACP,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D;IAEAuG,IAAI,CAACE,IAAI,CAAC,CAACvC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMuC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACzC,CAAC,EAAE3D,KAAK,CAAC;MAC9C,MAAMqG,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACxC,CAAC,EAAE5D,KAAK,CAAC;MAE9C,IAAIsG,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAAC5G,cAAc,GAAG6G,MAAM;IACrC,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAACJ,IAAS,EAAEhG,KAAa;IACvC,IAAI,CAACgG,IAAI,IAAI,CAAChG,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAACwG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOR,IAAI,CAAChG,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAIyG,MAAM,GAAGzG,KAAK,CAAC0G,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIC,KAAK,GAAGX,IAAI;MAChB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAID,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOD,KAAK;IACd;EACF;EAEArC,oBAAoBA,CAACwC,MAAc,EAAEb,IAAY;IAC/C,IAAI,CAAChE,iBAAiB,CACnB8E,0BAA0B,CAACd,IAAI,CAAC,CAChClB,SAAS,CAAEiC,GAAQ,IAAI;MACtB,IAAI,CAAC5H,SAAS,CAAC0H,MAAM,CAAC,GACpBE,GAAG,EAAEhB,IAAI,EAAEhM,GAAG,CAAEiN,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBR,KAAK,EAAEM,IAAI,CAACG;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAjM,oBAAoBA,CAACkM,WAAmB,EAAEV,KAAa;IACrD,MAAMW,IAAI,GAAG,IAAI,CAAClI,SAAS,CAACiI,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACb,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOW,IAAI,EAAEJ,KAAK,IAAIP,KAAK;EAC7B;EAEAtF,QAAQA,CAACK,IAAS;IAChB,IAAI,CAACgB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGpB,IAAI,EAAEnG,UAAU;IAClC,IAAI,CAACiI,QAAQ,CAACiE,UAAU,CAAC/F,IAAI,CAAC;EAChC;EAEA2D,iBAAiBA,CAACH,QAAa;IAC7B,IAAI,CAACwC,gBAAgB,GAAG;MACtBrE,qBAAqB,EAAE6B,QAAQ,EAAE7B,qBAAqB;MACtDC,qBAAqB,EAAE4B,QAAQ,EAAE5B,qBAAqB;MACtDpI,OAAO,EAAEgK,QAAQ,EAAEhK,OAAO;MAC1BW,aAAa,EAAEqJ,QAAQ,EAAErJ,aAAa;MACtCE,UAAU,EAAEmJ,QAAQ,EAAEnJ,UAAU,GAAG,IAAI4L,IAAI,CAACzC,QAAQ,EAAEnJ,UAAU,CAAC,GAAG,IAAI;MACxEE,QAAQ,EAAEiJ,QAAQ,EAAEjJ,QAAQ,GAAG,IAAI0L,IAAI,CAACzC,QAAQ,EAAEjJ,QAAQ,CAAC,GAAG,IAAI;MAClEE,eAAe,EAAE+I,QAAQ,EAAE/I;KAC5B;IAED,IAAI,CAACqG,MAAM,GAAG0C,QAAQ,CAAC3J,UAAU;IACjC,IAAI,CAACkD,gBAAgB,CAACgJ,UAAU,CAAC,IAAI,CAACC,gBAAgB,CAAC;EACzD;EAEQ1C,YAAYA,CAAA;IAClB,IAAI,CAACrG,SAAS,GAAG5E,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACoI,cAAc,CAAC;IAAE;IACzB,IAAI,CAACvD,aAAa,CAAC2F,IAAI,CACrBvK,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACwE,cAAc,GAAG,IAAK,CAAC,EACvCzE,SAAS,CAAEyN,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MACA,OAAO,IAAI,CAAC3F,iBAAiB,CAAC6F,WAAW,CAACD,MAAM,CAAC,CAACpD,IAAI,CACpDzK,GAAG,CAAEgM,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACF5L,GAAG,CAAC,MAAO,IAAI,CAACwE,cAAc,GAAG,KAAM,CAAC,EACxCtE,UAAU,CAAEwK,KAAK,IAAI;QACnB,IAAI,CAAClG,cAAc,GAAG,KAAK;QAC3B,OAAO3E,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQ0K,qBAAqBA,CAACoD,IAAY;IACxC,IAAI,CAAC9I,SAAS,GAAG,IAAI,CAACE,aAAa,CAACsF,IAAI,CACtCpK,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC8E,cAAc,GAAG,IAAK,CAAC,EACvC/E,SAAS,CAAEyN,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEE,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIH,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAC3F,iBAAiB,CAAC+F,kBAAkB,CAACH,MAAM,CAAC,CAACpD,IAAI,CAC3DzK,GAAG,CAAEmL,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxC/K,GAAG,CAAE6N,QAAe,IAAI;QACtB,IAAI,CAAC/I,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACF5E,UAAU,CAAEwK,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC5F,cAAc,GAAG,KAAK;QAC3B,OAAOjF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEQgL,aAAaA,CAAA;IACnB,IAAI,CAAC3F,UAAU,GAAGvF,MAAM,CACtBE,EAAE,CAAC,IAAI,CAACoI,cAAc,CAAC;IAAE;IACzB,IAAI,CAAC7C,cAAc,CAACiF,IAAI,CACtBlK,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACmF,eAAe,GAAG,IAAK,CAAC,EACxCpF,SAAS,CAAEyN,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,iCAAiC,EAAE,QAAQ;QAC3C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE;OACd;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC3F,iBAAiB,CAAC6F,WAAW,CAACD,MAAM,CAAC,CAACpD,IAAI,CACpDzK,GAAG,CAAEmL,QAAa,IAAKA,QAAQ,IAAI,EAAE,CAAC;MAAE;MACxC7K,UAAU,CAAEwK,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO7K,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,EACFO,QAAQ,CAAC,MAAO,IAAI,CAAC+E,eAAe,GAAG,KAAM,CAAC,CAAC;OAChD;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEM2I,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACvF,aAAa,GAAG,IAAI;MACzBuF,KAAI,CAACzF,WAAW,GAAG,IAAI;MAEvB,IAAIyF,KAAI,CAAC3E,QAAQ,CAAC6E,OAAO,EAAE;QACzBF,KAAI,CAACzF,WAAW,GAAG,IAAI;QACvB;MACF;MAEAyF,KAAI,CAACtF,UAAU,GAAG,IAAI;MACtB,MAAM8D,KAAK,GAAG;QAAE,GAAGwB,KAAI,CAAC3E,QAAQ,CAACmD;MAAK,CAAE;MAExC,MAAMX,IAAI,GAAG;QACX/K,WAAW,EAAEkN,KAAI,CAAC5F,EAAE;QACpBb,IAAI,EAAEiF,KAAK,EAAEjF;OACd;MAED,IAAIyG,KAAI,CAACrF,UAAU,EAAE;QACnBqF,KAAI,CAAClG,iBAAiB,CACnBqG,UAAU,CAACH,KAAI,CAACrF,UAAU,EAAEkD,IAAI,CAAC,CACjCvB,IAAI,CAAC3K,SAAS,CAACqO,KAAI,CAAC/F,aAAa,CAAC,CAAC,CACnC2C,SAAS,CAAC;UACTwD,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACtF,UAAU,GAAG,KAAK;YACvBsF,KAAI,CAACzF,WAAW,GAAG,KAAK;YACxByF,KAAI,CAAC3E,QAAQ,CAACgF,KAAK,EAAE;YACrBL,KAAI,CAACjG,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF+D,KAAI,CAAClG,iBAAiB,CACnBwG,eAAe,CAACN,KAAI,CAAC5F,EAAE,CAAC,CACxBkC,IAAI,CAAC3K,SAAS,CAACqO,KAAI,CAAC/F,aAAa,CAAC,CAAC,CACnC2C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGkC,GAAQ,IAAI;YAClBmB,KAAI,CAACtF,UAAU,GAAG,KAAK;YACvBsF,KAAI,CAACzF,WAAW,GAAG,IAAI;YACvByF,KAAI,CAACjG,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACL+D,KAAI,CAAClG,iBAAiB,CACnByG,UAAU,CAAC1C,IAAI,CAAC,CAChBvB,IAAI,CAAC3K,SAAS,CAACqO,KAAI,CAAC/F,aAAa,CAAC,CAAC,CACnC2C,SAAS,CAAC;UACTwD,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACtF,UAAU,GAAG,KAAK;YACvBsF,KAAI,CAACzF,WAAW,GAAG,KAAK;YACxByF,KAAI,CAAC3E,QAAQ,CAACgF,KAAK,EAAE;YACrBL,KAAI,CAACjG,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACF+D,KAAI,CAAClG,iBAAiB,CACnBwG,eAAe,CAACN,KAAI,CAAC5F,EAAE,CAAC,CACxBkC,IAAI,CAAC3K,SAAS,CAACqO,KAAI,CAAC/F,aAAa,CAAC,CAAC,CACnC2C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGkC,GAAQ,IAAI;YAClBmB,KAAI,CAACtF,UAAU,GAAG,KAAK;YACvBsF,KAAI,CAACzF,WAAW,GAAG,IAAI;YACvByF,KAAI,CAACjG,cAAc,CAACgC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEM5F,QAAQA,CAAA;IAAA,IAAAmK,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAAClM,SAAS,GAAG,IAAI;MAErB,IAAIkM,MAAI,CAAClK,gBAAgB,CAAC4J,OAAO,EAAE;QACjC;MACF;MAEAM,MAAI,CAACrG,MAAM,GAAG,IAAI;MAClB,MAAMqE,KAAK,GAAG;QAAE,GAAGgC,MAAI,CAAClK,gBAAgB,CAACkI;MAAK,CAAE;MAEhD,MAAMX,IAAI,GAAG;QACX9K,OAAO,EAAEyL,KAAK,EAAEzL,OAAO;QACvBmI,qBAAqB,EAAEsD,KAAK,EAAEtD,qBAAqB;QACnDC,qBAAqB,EAAEqD,KAAK,EAAErD,qBAAqB;QACnDzH,aAAa,EAAE8K,KAAK,EAAE9K,aAAa;QACnCE,UAAU,EAAE4K,KAAK,EAAE5K,UAAU,GAAG4M,MAAI,CAACC,UAAU,CAACjC,KAAK,CAAC5K,UAAU,CAAC,GAAG,IAAI;QACxEE,QAAQ,EAAE0K,KAAK,EAAE1K,QAAQ,GAAG0M,MAAI,CAACC,UAAU,CAACjC,KAAK,CAAC1K,QAAQ,CAAC,GAAG,IAAI;QAClEsH,kBAAkB,EAAEoD,KAAK,EAAEpD,kBAAkB;QAC7CrH,QAAQ,EAAEyK,KAAK,EAAEzK,QAAQ;QACzBC,eAAe,EAAEwK,KAAK,EAAExK;OACzB;MAEDwM,MAAI,CAAC1G,iBAAiB,CACnB4G,cAAc,CAACF,MAAI,CAACnG,MAAM,EAAEwD,IAAI,CAAC,CACjCvB,IAAI,CAAC3K,SAAS,CAAC6O,MAAI,CAACvG,aAAa,CAAC,CAAC,CACnC2C,SAAS,CAAC;QACT+D,IAAI,EAAG3D,QAAa,IAAI;UACtBwD,MAAI,CAACzG,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFuE,MAAI,CAAC1G,iBAAiB,CACnBwG,eAAe,CAACE,MAAI,CAACpG,EAAE,CAAC,CACxBkC,IAAI,CAAC3K,SAAS,CAAC6O,MAAI,CAACvG,aAAa,CAAC,CAAC,CACnC2C,SAAS,EAAE;UACd4D,MAAI,CAAClG,UAAU,GAAG,KAAK;QACzB,CAAC;QACDqC,KAAK,EAAGkC,GAAQ,IAAI;UAClB2B,MAAI,CAACrG,MAAM,GAAG,KAAK;UACnBqG,MAAI,CAAClG,UAAU,GAAG,IAAI;UACtBkG,MAAI,CAACzG,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEA3C,aAAaA,CAAC6F,IAAS;IACrB,IAAI,CAACnF,mBAAmB,CAAC4G,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChE5I,MAAM,EAAE,SAAS;MACjB6I,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC7B,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA6B,MAAMA,CAAC7B,IAAS;IACd,IAAI,CAACrF,iBAAiB,CACnBmH,UAAU,CAAC9B,IAAI,CAAC/L,UAAU,CAAC,CAC3BkJ,IAAI,CAAC3K,SAAS,CAAC,IAAI,CAACsI,aAAa,CAAC,CAAC,CACnC2C,SAAS,CAAC;MACT+D,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC5G,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACnC,iBAAiB,CACnBwG,eAAe,CAAC,IAAI,CAAClG,EAAE,CAAC,CACxBkC,IAAI,CAAC3K,SAAS,CAAC,IAAI,CAACsI,aAAa,CAAC,CAAC,CACnC2C,SAAS,EAAE;MAChB,CAAC;MACDD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC5C,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAiF,SAASA,CAACC,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAhB,UAAUA,CAACiB,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAI1N,CAACA,CAAA;IACH,OAAO,IAAI,CAAC+B,gBAAgB,CAAC6L,QAAQ;EACvC;EAEA,IAAIzI,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC2B,QAAQ,CAAC8G,QAAQ;EAC/B;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAC7H,YAAY,GAAG6H,QAAQ;IAC5B,IAAI,CAAC9H,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACY,QAAQ,CAACgF,KAAK,EAAE;EACvB;EAEAiC,UAAUA,CAAA;IACR,IAAI,CAAChI,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAIAiI,WAAWA,CAAA;IACT,IAAI,CAACtI,aAAa,CAAC0G,IAAI,EAAE;IACzB,IAAI,CAAC1G,aAAa,CAACmG,QAAQ,EAAE;EAC/B;;;uBAngBWzG,qBAAqB,EAAArH,EAAA,CAAAkQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApQ,EAAA,CAAAkQ,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAtQ,EAAA,CAAAkQ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxQ,EAAA,CAAAkQ,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAArBpJ,qBAAqB;MAAAqJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB1BhR,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,kBACyG;UAA1CD,EAAA,CAAAyD,UAAA,mBAAAyN,yDAAA;YAAA,OAASD,GAAA,CAAAjB,UAAA,EAAY;UAAA,EAAC;UACzFhQ,EAFI,CAAAG,YAAA,EACyG,EACvG;UAuHNH,EAtHA,CAAA4B,UAAA,IAAAuP,oCAAA,mBAA6D,IAAAC,qCAAA,oBAsHL;UA8K5DpR,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,aAAgE,aACoC,YAC7C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrDH,EADJ,CAAAC,cAAA,cAA2C,mBAEoB;UADrCD,EAAA,CAAAyD,UAAA,mBAAA4N,0DAAA;YAAA,OAASJ,GAAA,CAAAnB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAAnD9P,EAAA,CAAAG,YAAA,EAC2D;UAE3DH,EAAA,CAAAC,cAAA,yBAE+I;UAF1GD,EAAA,CAAAsR,gBAAA,2BAAAC,uEAAAzK,MAAA;YAAA9G,EAAA,CAAAwR,kBAAA,CAAAP,GAAA,CAAA/K,oBAAA,EAAAY,MAAA,MAAAmK,GAAA,CAAA/K,oBAAA,GAAAY,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAK/E9G,EAFQ,CAAAG,YAAA,EAAgB,EACd,EACJ;UAIFH,EAFJ,CAAAC,cAAA,eAAuB,mBAIoC;UADHD,EAAA,CAAAyD,UAAA,0BAAAgO,gEAAA3K,MAAA;YAAA,OAAgBmK,GAAA,CAAAhG,oBAAA,CAAAnE,MAAA,CAA4B;UAAA,EAAC;UA6D7F9G,EA1DA,CAAA4B,UAAA,KAAA8P,6CAAA,0BAAgC,KAAAC,6CAAA,0BA0BQ,KAAAC,6CAAA,0BA2BF,KAAAC,6CAAA,0BAKD;UAOjD7R,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAAsR,gBAAA,2BAAAQ,kEAAAhL,MAAA;YAAA9G,EAAA,CAAAwR,kBAAA,CAAAP,GAAA,CAAAhJ,WAAA,EAAAnB,MAAA,MAAAmK,GAAA,CAAAhJ,WAAA,GAAAnB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9C9G,EAAA,CAAA4B,UAAA,KAAAmQ,6CAAA,0BAAgC;UAMxB/R,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAAoD,SAAA,oBAC0E;UAC1EpD,EAAA,CAAA4B,UAAA,KAAAoQ,qCAAA,kBACmE;UAI3EhS,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAAyD,UAAA,mBAAAwO,wDAAA;YAAA,OAAAhB,GAAA,CAAAhJ,WAAA,GAAuB,KAAK;UAAA,EAAC;UAACjI,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAAyD,UAAA,mBAAAyO,wDAAA;YAAA,OAASjB,GAAA,CAAAxD,YAAA,EAAc;UAAA,EAAC;UAIxCzN,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UA1ZOH,EAAA,CAAAI,SAAA,GAAuC;UACqCJ,EAD5E,CAAAY,UAAA,UAAAqQ,GAAA,CAAAjJ,UAAA,oBAAuC,UAAAiJ,GAAA,CAAAjJ,UAAA,uBAAyC,2CAC5B,iBAAwC;UAEpGhI,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAY,UAAA,UAAAqQ,GAAA,CAAAjJ,UAAA,CAAiB;UAsHhBhI,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAY,UAAA,SAAAqQ,GAAA,CAAAjJ,UAAA,CAAgB;UAsLXhI,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAY,UAAA,oCAAmC,iBAAiB;UAEzCZ,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAY,UAAA,YAAAqQ,GAAA,CAAAhI,SAAA,CAAqB;UAACjJ,EAAA,CAAAmS,gBAAA,YAAAlB,GAAA,CAAA/K,oBAAA,CAAkC;UAEnElG,EAAA,CAAAY,UAAA,2IAA0I;UAOzIZ,EAAA,CAAAI,SAAA,GAAqB;UACNJ,EADf,CAAAY,UAAA,UAAAqQ,GAAA,CAAAzL,WAAA,CAAqB,YAA4B,mBAAuC,oBAC1E,4BAA4B;UAqERxF,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAoS,UAAA,CAAApS,EAAA,CAAAqS,eAAA,KAAAC,GAAA,EAA4B;UAArEtS,EAAA,CAAAY,UAAA,eAAc;UAACZ,EAAA,CAAAmS,gBAAA,YAAAlB,GAAA,CAAAhJ,WAAA,CAAyB;UAAmDjI,EAArB,CAAAY,UAAA,qBAAoB,oBAAoB;UAM9GZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAY,UAAA,cAAAqQ,GAAA,CAAAlI,QAAA,CAAsB;UAGkD/I,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAoS,UAAA,CAAApS,EAAA,CAAAqS,eAAA,KAAAE,GAAA,EAA6B;UAC3FvS,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAA0M,GAAA,CAAA9I,aAAA,IAAA8I,GAAA,CAAA7J,KAAA,SAAAlF,MAAA,EAAmE;UACjElC,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAY,UAAA,SAAAqQ,GAAA,CAAA9I,aAAA,IAAA8I,GAAA,CAAA7J,KAAA,SAAAlF,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}