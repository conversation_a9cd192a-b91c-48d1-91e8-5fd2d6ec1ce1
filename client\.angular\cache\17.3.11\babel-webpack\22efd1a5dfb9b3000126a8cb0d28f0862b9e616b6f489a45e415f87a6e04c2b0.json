{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ApiConstant, AppConstant, CMS_APIContstant, Permission } from \"src/app/constants/api.constants\";\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"primeng/api\";\nexport class AuthService {\n  constructor(http, messageService, ngZone) {\n    this.http = http;\n    this.messageService = messageService;\n    this.ngZone = ngZone;\n    this.permissions = new BehaviorSubject([]);\n    this.cmsTokenVal = new BehaviorSubject(\"\");\n    this.sessionChannel = new BroadcastChannel(\"session\");\n    this.logoutTriggered = false;\n    this.TokenKey = 'jwtToken';\n    this.UserDetailsKey = 'userInfo';\n    const user = this.getAuth();\n    this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : \"\");\n    this.bindUserActivityEvents();\n  }\n  checkAdminUser() {\n    const user = this.getAuth();\n    if (user && user[this.UserDetailsKey]?.documentId) {\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(tap(cartres => {\n        cartres && (cartres.cart = cartres?.vendor);\n        if (cartres?.cart) {\n          const cart = cartres.cart || null;\n          this.updateAuth({\n            cart,\n            customer: cart.customer,\n            userDetails: {\n              address: cartres.address,\n              email: cartres.email,\n              firstname: cartres.firstname,\n              lastname: cartres.lastname,\n              username: cartres.username\n            }\n          });\n        }\n      }));\n    } else {\n      return of(null);\n    }\n  }\n  bindUserActivityEvents() {\n    const events = [\"click\", \"keydown\"];\n    for (let i = 0; i < events.length; i++) {\n      const element = events[i];\n      fromEvent(document, element).subscribe(data => {\n        this.setInavtivityTimer();\n        this.sessionChannel.postMessage({\n          type: \"activityFound\"\n        });\n      });\n    }\n    this.sessionChannel.onmessage = event => {\n      if (event?.data?.type == \"activityFound\") {\n        this.ngZone.run(() => {\n          this.setInavtivityTimer();\n        });\n      }\n      if (event?.data?.type == \"logout\") {\n        this.logoutTriggered = true;\n        this.doLogout();\n      }\n    };\n    this.setInavtivityTimer();\n    this.sessionChannel.postMessage({\n      type: \"activityFound\"\n    });\n  }\n  setInavtivityTimer() {\n    clearTimeout(this.timer);\n    if (!this.isLoggedIn) {\n      return;\n    }\n    this.timer = setTimeout(() => {\n      this.doLogout();\n    }, AppConstant.SESSION_TIMEOUT);\n  }\n  login(username, password, rememberMe) {\n    return this.http.post(CMS_APIContstant.SINGIN, {\n      identifier: (username || \"\").toLowerCase(),\n      password\n    }).pipe(tap(res => {\n      if (res) {\n        this.setAuth(res.jwt, res.user, rememberMe);\n      }\n      return res;\n    }), switchMap(res => {\n      return this.getUserPermissionsDB(res.user).pipe(map(permissions => {\n        const hasPermission = permissions?.includes(Permission.Vendor_Portal);\n        if (!hasPermission) {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'You do not have permission to access Vendor Portal.'\n          });\n          setTimeout(() => {\n            this.doLogout();\n          }, 2000);\n        }\n        return {\n          data: res,\n          hasPermission\n        };\n      }));\n    }), switchMap(res => {\n      if (!res?.hasPermission) {\n        return of(null);\n      }\n      if (res?.data?.user) {\n        return this.getCartDetails(res.data?.user.documentId).pipe(map(data => {\n          if (data?.cart) {\n            res.data.user.cart = data.cart;\n            res.data.user.customer = data.cart.customer;\n          }\n          this.updateAuth(res.data?.user);\n          return res.data;\n        }));\n      }\n      return of(null);\n    }));\n  }\n  getCartDetails(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`).pipe(map(res => {\n      res.cart = res.vendor;\n      if (res?.cart?.cart_items?.length) {\n        res.cart.cart_items = res.cart.cart_items.map(item => ({\n          ...item,\n          requested_quantity: item.requested_quantity.toString()\n        }));\n      }\n      return res;\n    }));\n  }\n  getToken() {\n    const val = this.userSubject.value;\n    return val ? val[this.TokenKey] : null;\n  }\n  get partnerFunction() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n      return user[this.UserDetailsKey].customer.partner_functions[0];\n    }\n    return {};\n  }\n  get cart() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.cart) {\n      return user[this.UserDetailsKey].cart;\n    }\n    return {};\n  }\n  get userDetail() {\n    const user = this.userSubject.value;\n    return user ? user[this.UserDetailsKey] : null;\n  }\n  get isLoggedIn() {\n    return !!this.userSubject.value;\n  }\n  get isSupplierSelected() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.supplier) {\n      return true;\n    }\n    return false;\n  }\n  updateAuth(user) {\n    const auth = this.getAuth();\n    if (user?.userDetails) {\n      auth[this.UserDetailsKey] = {\n        ...auth[this.UserDetailsKey],\n        ...user?.userDetails\n      };\n    }\n    if (user?.cart) {\n      auth[this.UserDetailsKey].cart = user?.cart;\n    }\n    if (user?.customer) {\n      auth[this.UserDetailsKey].customer = user?.customer;\n    }\n    if (user?.supplier) {\n      auth[this.UserDetailsKey].supplier = user?.supplier;\n    }\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n  }\n  isRememberMeSelected() {\n    return !!localStorage.getItem(this.TokenKey);\n  }\n  doLogout() {\n    this.resetAuth();\n  }\n  resetAuth() {\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\n      this.removeAuthToken();\n      !this.logoutTriggered && this.sessionChannel.postMessage({\n        type: \"logout\"\n      });\n      this.userSubject.next(null);\n      window.location.href = \"#/auth/login\";\n      window.location.reload();\n    });\n  }\n  getAuth() {\n    const authtoken = this.getAuthToken();\n    const userDetails = this.getUserDetails();\n    if (authtoken && this.isJsonString(userDetails)) {\n      return {\n        [this.UserDetailsKey]: JSON.parse(userDetails),\n        [this.TokenKey]: JSON.parse(authtoken)\n      };\n    }\n    return {};\n  }\n  setAuth(token, user, rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    } else {\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    }\n    this.userSubject.next({\n      [this.UserDetailsKey]: user,\n      [this.TokenKey]: token\n    });\n  }\n  getAuthToken() {\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n  }\n  getUserDetails() {\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n  }\n  removeAuthToken() {\n    localStorage.removeItem(this.TokenKey);\n    sessionStorage.removeItem(this.TokenKey);\n    localStorage.removeItem(this.UserDetailsKey);\n    sessionStorage.removeItem(this.UserDetailsKey);\n  }\n  isJsonString(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  getUserPermissions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const userDetails = _this.userDetail;\n      return yield lastValueFrom(_this.getUserPermissionsDB(userDetails));\n    })();\n  }\n  getUserPermissionsDB(userDetails) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`).pipe(map(res => {\n      if (res?.data?.length) {\n        const data = res?.data || [];\n        this.permissions.next(data);\n        return data;\n      }\n      return [];\n    })).pipe(catchError(error => {\n      this.permissions.next([]);\n      return error;\n    }));\n  }\n  get getPermissions() {\n    return this.permissions?.value || [];\n  }\n  getCMSToken() {\n    return this.http.get(ApiConstant.FETCH_TOKEN).pipe(map(response => {\n      this.cmsTokenVal.next(response.token);\n      return response.token;\n    }));\n  }\n  get cmsToken() {\n    return of(this.cmsTokenVal?.value || \"\");\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConstant", "AppConstant", "CMS_APIContstant", "Permission", "BehaviorSubject", "catchError", "fromEvent", "lastValueFrom", "map", "of", "switchMap", "tap", "AuthService", "constructor", "http", "messageService", "ngZone", "permissions", "cmsTokenVal", "sessionChannel", "BroadcastChannel", "logoutTriggered", "TokenKey", "UserDetailsKey", "user", "getAuth", "userSubject", "Object", "keys", "length", "bindUserActivityEvents", "checkAdminUser", "documentId", "getCartDetails", "pipe", "cartres", "cart", "vendor", "updateAuth", "customer", "userDetails", "address", "email", "firstname", "lastname", "username", "events", "i", "element", "document", "subscribe", "data", "setInavtivityTimer", "postMessage", "type", "onmessage", "event", "run", "doLogout", "clearTimeout", "timer", "isLoggedIn", "setTimeout", "SESSION_TIMEOUT", "login", "password", "rememberMe", "post", "SINGIN", "identifier", "toLowerCase", "res", "setAuth", "jwt", "getUserPermissionsDB", "hasPermission", "includes", "Vendor_Portal", "add", "severity", "summary", "detail", "userId", "get", "USER_DETAILS", "cart_items", "item", "requested_quantity", "toString", "getToken", "val", "value", "partnerFunction", "partner_functions", "userDetail", "isSupplierSelected", "supplier", "auth", "isRememberMeSelected", "localStorage", "getItem", "resetAuth", "removeAuthToken", "next", "window", "location", "href", "reload", "authtoken", "getAuthToken", "getUserDetails", "isJsonString", "JSON", "parse", "token", "setItem", "stringify", "sessionStorage", "removeItem", "str", "e", "getUserPermissions", "_this", "_asyncToGenerator", "error", "getPermissions", "getCMSToken", "FETCH_TOKEN", "response", "cmsToken", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "MessageService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\authentication\\auth.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from \"@angular/core\";\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { ApiConstant, AppConstant, CMS_APIContstant, Permission } from \"src/app/constants/api.constants\";\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  fromEvent,\r\n  lastValueFrom,\r\n  map,\r\n  Observable,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from \"rxjs\";\r\nimport { MessageService } from \"primeng/api\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class AuthService {\r\n  public userSubject: BehaviorSubject<any>;\r\n  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);\r\n  private cmsTokenVal: BehaviorSubject<any> = new BehaviorSubject<any>(\"\");\r\n  private sessionChannel = new BroadcastChannel(\"session\");\r\n  private timer: any;\r\n  private logoutTriggered = false;\r\n  public TokenKey = 'jwtToken';\r\n  public UserDetailsKey = 'userInfo';\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private messageService: MessageService,\r\n    private ngZone: NgZone\r\n  ) {\r\n    const user: any = this.getAuth();\r\n    this.userSubject = new BehaviorSubject<any>(Object.keys(user).length ? user : \"\");\r\n    this.bindUserActivityEvents();\r\n  }\r\n\r\n  checkAdminUser() {\r\n    const user: any = this.getAuth();\r\n    if (user && user[this.UserDetailsKey]?.documentId) {\r\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(\r\n        tap((cartres: any) => {\r\n          cartres && (cartres.cart = cartres?.vendor);\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.updateAuth({\r\n              cart,\r\n              customer: cart.customer,\r\n              userDetails: {\r\n                address: cartres.address,\r\n                email: cartres.email,\r\n                firstname: cartres.firstname,\r\n                lastname: cartres.lastname,\r\n                username: cartres.username,\r\n              }\r\n            });\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n  bindUserActivityEvents() {\r\n    const events = [\"click\", \"keydown\"];\r\n    for (let i = 0; i < events.length; i++) {\r\n      const element = events[i];\r\n      fromEvent(document, element).subscribe((data) => {\r\n        this.setInavtivityTimer();\r\n        this.sessionChannel.postMessage({\r\n          type: \"activityFound\",\r\n        });\r\n      });\r\n    }\r\n    this.sessionChannel.onmessage = (event) => {\r\n      if (event?.data?.type == \"activityFound\") {\r\n        this.ngZone.run(() => {\r\n          this.setInavtivityTimer();\r\n        });\r\n      }\r\n      if (event?.data?.type == \"logout\") {\r\n        this.logoutTriggered = true;\r\n        this.doLogout();\r\n      }\r\n    };\r\n    this.setInavtivityTimer();\r\n    this.sessionChannel.postMessage({\r\n      type: \"activityFound\",\r\n    });\r\n  }\r\n\r\n  setInavtivityTimer() {\r\n    clearTimeout(this.timer);\r\n    if (!this.isLoggedIn) {\r\n      return;\r\n    }\r\n    this.timer = setTimeout(() => {\r\n      this.doLogout();\r\n    }, AppConstant.SESSION_TIMEOUT);\r\n  }\r\n\r\n  login(username: string, password: string, rememberMe: boolean) {\r\n    return this.http\r\n      .post<any>(CMS_APIContstant.SINGIN, {\r\n        identifier: (username || \"\").toLowerCase(),\r\n        password,\r\n      })\r\n      .pipe(\r\n        tap((res) => {\r\n          if (res) {\r\n            this.setAuth(res.jwt, res.user, rememberMe);\r\n          }\r\n          return res;\r\n        }),\r\n        switchMap((res) => {\r\n          return this.getUserPermissionsDB(res.user).pipe(\r\n            map((permissions) => {\r\n              const hasPermission = permissions?.includes(Permission.Vendor_Portal);\r\n              if (!hasPermission) {\r\n                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You do not have permission to access Vendor Portal.' });\r\n                setTimeout(() => {\r\n                  this.doLogout();\r\n                }, 2000);\r\n              }\r\n              return { data: res, hasPermission };\r\n            })\r\n          )\r\n        }),\r\n        switchMap((res) => {\r\n          if (!res?.hasPermission) {\r\n            return of(null);\r\n          }\r\n          if (res?.data?.user) {\r\n            return this.getCartDetails(res.data?.user.documentId).pipe(\r\n              map((data: any) => {\r\n                if (data?.cart) {\r\n                  res.data.user.cart = data.cart;\r\n                  res.data.user.customer = data.cart.customer;\r\n                }\r\n                this.updateAuth(res.data?.user);\r\n                return res.data;\r\n              })\r\n            )\r\n          }\r\n          return of(null);\r\n        }),\r\n      );\r\n  }\r\n\r\n  getCartDetails(userId: string): any {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`).pipe(\r\n      map((res: any) => {\r\n        res.cart = res.vendor;\r\n        if (res?.cart?.cart_items?.length) {\r\n          res.cart.cart_items = res.cart.cart_items.map((item: any) => ({\r\n            ...item,\r\n            requested_quantity: item.requested_quantity.toString()\r\n          }))\r\n        }\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  getToken() {\r\n    const val = this.userSubject.value;\r\n    return val ? val[this.TokenKey] : null;\r\n  }\r\n\r\n  get partnerFunction() {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\r\n      return user[this.UserDetailsKey].customer.partner_functions[0];\r\n    }\r\n    return {};\r\n  }\r\n\r\n  get cart() {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.cart) {\r\n      return user[this.UserDetailsKey].cart;\r\n    }\r\n    return {};\r\n  }\r\n\r\n  get userDetail() {\r\n    const user = this.userSubject.value;\r\n    return user ? user[this.UserDetailsKey] : null;\r\n  }\r\n\r\n  get isLoggedIn(): boolean {\r\n    return !!this.userSubject.value;\r\n  }\r\n\r\n  get isSupplierSelected(): boolean {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.supplier) {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  updateAuth(user: any) {\r\n    const auth: any = this.getAuth();\r\n    if (user?.userDetails) {\r\n      auth[this.UserDetailsKey] = {\r\n        ...auth[this.UserDetailsKey],\r\n        ...user?.userDetails\r\n      };\r\n    }\r\n    if (user?.cart) {\r\n      auth[this.UserDetailsKey].cart = user?.cart;\r\n    }\r\n    if (user?.customer) {\r\n      auth[this.UserDetailsKey].customer = user?.customer;\r\n    }\r\n    if (user?.supplier) {\r\n      auth[this.UserDetailsKey].supplier = user?.supplier;\r\n    }\r\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\r\n  }\r\n\r\n  isRememberMeSelected(): boolean {\r\n    return !!localStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  doLogout() {\r\n    this.resetAuth();\r\n  }\r\n\r\n  resetAuth() {\r\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\r\n      this.removeAuthToken();\r\n      !this.logoutTriggered &&\r\n        this.sessionChannel.postMessage({\r\n          type: \"logout\",\r\n        });\r\n      this.userSubject.next(null);\r\n      window.location.href = \"#/auth/login\";\r\n      window.location.reload();\r\n    });\r\n  }\r\n\r\n  getAuth(): any {\r\n    const authtoken: any = this.getAuthToken();\r\n    const userDetails: any = this.getUserDetails();\r\n    if (authtoken && this.isJsonString(userDetails)) {\r\n      return {\r\n        [this.UserDetailsKey]: JSON.parse(userDetails),\r\n        [this.TokenKey]: JSON.parse(authtoken)\r\n      }\r\n    }\r\n    return {};\r\n  }\r\n\r\n  setAuth(token: string, user: any, rememberMe: boolean) {\r\n    if (rememberMe) {\r\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    } else {\r\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    }\r\n    this.userSubject.next({\r\n      [this.UserDetailsKey]: user,\r\n      [this.TokenKey]: token\r\n    });\r\n  }\r\n\r\n  getAuthToken() {\r\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  getUserDetails() {\r\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\r\n  }\r\n\r\n  removeAuthToken() {\r\n    localStorage.removeItem(this.TokenKey);\r\n    sessionStorage.removeItem(this.TokenKey);\r\n    localStorage.removeItem(this.UserDetailsKey);\r\n    sessionStorage.removeItem(this.UserDetailsKey);\r\n  }\r\n\r\n  isJsonString(str: any) {\r\n    try {\r\n      JSON.parse(str);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async getUserPermissions() {\r\n    const userDetails = this.userDetail;\r\n    return await lastValueFrom(this.getUserPermissionsDB(userDetails));\r\n  }\r\n\r\n  getUserPermissionsDB(userDetails: any) {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`)\r\n      .pipe(\r\n        map((res) => {\r\n          if (res?.data?.length) {\r\n            const data = (res?.data || []);\r\n            this.permissions.next(data);\r\n            return data;\r\n          }\r\n          return [];\r\n        })\r\n      )\r\n      .pipe(\r\n        catchError((error) => {\r\n          this.permissions.next([]);\r\n          return error;\r\n        })\r\n      )\r\n  }\r\n\r\n  get getPermissions(): any[] {\r\n    return this.permissions?.value || [];\r\n  }\r\n\r\n  getCMSToken(): Observable<string> {\r\n    return this.http.get<{ token: string }>(ApiConstant.FETCH_TOKEN).pipe(\r\n      map((response) => {\r\n        this.cmsTokenVal.next(response.token);\r\n        return response.token as string;\r\n      }),\r\n    )\r\n  }\r\n\r\n  get cmsToken(): Observable<string> {\r\n    return of(this.cmsTokenVal?.value || \"\");\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAASA,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,iCAAiC;AACxG,SACEC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,GAAG,EAEHC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;;;;AAMb,OAAM,MAAOC,WAAW;EAUtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAXT,KAAAC,WAAW,GAAyB,IAAIb,eAAe,CAAM,EAAE,CAAC;IAC/D,KAAAc,WAAW,GAAyB,IAAId,eAAe,CAAM,EAAE,CAAC;IAChE,KAAAe,cAAc,GAAG,IAAIC,gBAAgB,CAAC,SAAS,CAAC;IAEhD,KAAAC,eAAe,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,UAAU;IACrB,KAAAC,cAAc,GAAG,UAAU;IAOhC,MAAMC,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,IAAItB,eAAe,CAAMuB,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAGL,IAAI,GAAG,EAAE,CAAC;IACjF,IAAI,CAACM,sBAAsB,EAAE;EAC/B;EAEAC,cAAcA,CAAA;IACZ,MAAMP,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAID,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,UAAU,EAAE;MACjD,OAAO,IAAI,CAACC,cAAc,CAACT,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACS,UAAU,CAAC,CAACE,IAAI,CACnEvB,GAAG,CAAEwB,OAAY,IAAI;QACnBA,OAAO,KAAKA,OAAO,CAACC,IAAI,GAAGD,OAAO,EAAEE,MAAM,CAAC;QAC3C,IAAIF,OAAO,EAAEC,IAAI,EAAE;UACjB,MAAMA,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAI,IAAI;UACjC,IAAI,CAACE,UAAU,CAAC;YACdF,IAAI;YACJG,QAAQ,EAAEH,IAAI,CAACG,QAAQ;YACvBC,WAAW,EAAE;cACXC,OAAO,EAAEN,OAAO,CAACM,OAAO;cACxBC,KAAK,EAAEP,OAAO,CAACO,KAAK;cACpBC,SAAS,EAAER,OAAO,CAACQ,SAAS;cAC5BC,QAAQ,EAAET,OAAO,CAACS,QAAQ;cAC1BC,QAAQ,EAAEV,OAAO,CAACU;;WAErB,CAAC;QACJ;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL,OAAOpC,EAAE,CAAC,IAAI,CAAC;IACjB;EACF;EAEAqB,sBAAsBA,CAAA;IACpB,MAAMgB,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACjB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MACtC,MAAMC,OAAO,GAAGF,MAAM,CAACC,CAAC,CAAC;MACzBzC,SAAS,CAAC2C,QAAQ,EAAED,OAAO,CAAC,CAACE,SAAS,CAAEC,IAAI,IAAI;QAC9C,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAACjC,cAAc,CAACkC,WAAW,CAAC;UAC9BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACnC,cAAc,CAACoC,SAAS,GAAIC,KAAK,IAAI;MACxC,IAAIA,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,eAAe,EAAE;QACxC,IAAI,CAACtC,MAAM,CAACyC,GAAG,CAAC,MAAK;UACnB,IAAI,CAACL,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MACA,IAAII,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,QAAQ,EAAE;QACjC,IAAI,CAACjC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACqC,QAAQ,EAAE;MACjB;IACF,CAAC;IACD,IAAI,CAACN,kBAAkB,EAAE;IACzB,IAAI,CAACjC,cAAc,CAACkC,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAF,kBAAkBA,CAAA;IAChBO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAACD,KAAK,GAAGE,UAAU,CAAC,MAAK;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAEzD,WAAW,CAAC8D,eAAe,CAAC;EACjC;EAEAC,KAAKA,CAACnB,QAAgB,EAAEoB,QAAgB,EAAEC,UAAmB;IAC3D,OAAO,IAAI,CAACpD,IAAI,CACbqD,IAAI,CAAMjE,gBAAgB,CAACkE,MAAM,EAAE;MAClCC,UAAU,EAAE,CAACxB,QAAQ,IAAI,EAAE,EAAEyB,WAAW,EAAE;MAC1CL;KACD,CAAC,CACD/B,IAAI,CACHvB,GAAG,CAAE4D,GAAG,IAAI;MACV,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,OAAO,CAACD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC/C,IAAI,EAAE0C,UAAU,CAAC;MAC7C;MACA,OAAOK,GAAG;IACZ,CAAC,CAAC,EACF7D,SAAS,CAAE6D,GAAG,IAAI;MAChB,OAAO,IAAI,CAACG,oBAAoB,CAACH,GAAG,CAAC/C,IAAI,CAAC,CAACU,IAAI,CAC7C1B,GAAG,CAAES,WAAW,IAAI;QAClB,MAAM0D,aAAa,GAAG1D,WAAW,EAAE2D,QAAQ,CAACzE,UAAU,CAAC0E,aAAa,CAAC;QACrE,IAAI,CAACF,aAAa,EAAE;UAClB,IAAI,CAAC5D,cAAc,CAAC+D,GAAG,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAqD,CAAE,CAAC;UAC/HnB,UAAU,CAAC,MAAK;YACd,IAAI,CAACJ,QAAQ,EAAE;UACjB,CAAC,EAAE,IAAI,CAAC;QACV;QACA,OAAO;UAAEP,IAAI,EAAEoB,GAAG;UAAEI;QAAa,CAAE;MACrC,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFjE,SAAS,CAAE6D,GAAG,IAAI;MAChB,IAAI,CAACA,GAAG,EAAEI,aAAa,EAAE;QACvB,OAAOlE,EAAE,CAAC,IAAI,CAAC;MACjB;MACA,IAAI8D,GAAG,EAAEpB,IAAI,EAAE3B,IAAI,EAAE;QACnB,OAAO,IAAI,CAACS,cAAc,CAACsC,GAAG,CAACpB,IAAI,EAAE3B,IAAI,CAACQ,UAAU,CAAC,CAACE,IAAI,CACxD1B,GAAG,CAAE2C,IAAS,IAAI;UAChB,IAAIA,IAAI,EAAEf,IAAI,EAAE;YACdmC,GAAG,CAACpB,IAAI,CAAC3B,IAAI,CAACY,IAAI,GAAGe,IAAI,CAACf,IAAI;YAC9BmC,GAAG,CAACpB,IAAI,CAAC3B,IAAI,CAACe,QAAQ,GAAGY,IAAI,CAACf,IAAI,CAACG,QAAQ;UAC7C;UACA,IAAI,CAACD,UAAU,CAACiC,GAAG,CAACpB,IAAI,EAAE3B,IAAI,CAAC;UAC/B,OAAO+C,GAAG,CAACpB,IAAI;QACjB,CAAC,CAAC,CACH;MACH;MACA,OAAO1C,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEAwB,cAAcA,CAACiD,MAAc;IAC3B,OAAO,IAAI,CAACpE,IAAI,CAACqE,GAAG,CAAC,GAAGjF,gBAAgB,CAACkF,YAAY,IAAIF,MAAM,KAAK,CAAC,CAAChD,IAAI,CACxE1B,GAAG,CAAE+D,GAAQ,IAAI;MACfA,GAAG,CAACnC,IAAI,GAAGmC,GAAG,CAAClC,MAAM;MACrB,IAAIkC,GAAG,EAAEnC,IAAI,EAAEiD,UAAU,EAAExD,MAAM,EAAE;QACjC0C,GAAG,CAACnC,IAAI,CAACiD,UAAU,GAAGd,GAAG,CAACnC,IAAI,CAACiD,UAAU,CAAC7E,GAAG,CAAE8E,IAAS,KAAM;UAC5D,GAAGA,IAAI;UACPC,kBAAkB,EAAED,IAAI,CAACC,kBAAkB,CAACC,QAAQ;SACrD,CAAC,CAAC;MACL;MACA,OAAOjB,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEAkB,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,IAAI,CAAChE,WAAW,CAACiE,KAAK;IAClC,OAAOD,GAAG,GAAGA,GAAG,CAAC,IAAI,CAACpE,QAAQ,CAAC,GAAG,IAAI;EACxC;EAEA,IAAIsE,eAAeA,CAAA;IACjB,MAAMpE,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiE,KAAK;IACnC,IAAInE,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEgB,QAAQ,EAAEsD,iBAAiB,EAAEhE,MAAM,EAAE;MAC1E,OAAOL,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACgB,QAAQ,CAACsD,iBAAiB,CAAC,CAAC,CAAC;IAChE;IACA,OAAO,EAAE;EACX;EAEA,IAAIzD,IAAIA,CAAA;IACN,MAAMZ,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiE,KAAK;IACnC,IAAInE,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEa,IAAI,EAAE;MAC3C,OAAOZ,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACa,IAAI;IACvC;IACA,OAAO,EAAE;EACX;EAEA,IAAI0D,UAAUA,CAAA;IACZ,MAAMtE,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiE,KAAK;IACnC,OAAOnE,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG,IAAI;EAChD;EAEA,IAAIsC,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAACnC,WAAW,CAACiE,KAAK;EACjC;EAEA,IAAII,kBAAkBA,CAAA;IACpB,MAAMvE,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiE,KAAK;IACnC,IAAInE,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEyE,QAAQ,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA1D,UAAUA,CAACd,IAAS;IAClB,MAAMyE,IAAI,GAAQ,IAAI,CAACxE,OAAO,EAAE;IAChC,IAAID,IAAI,EAAEgB,WAAW,EAAE;MACrByD,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC,GAAG;QAC1B,GAAG0E,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC;QAC5B,GAAGC,IAAI,EAAEgB;OACV;IACH;IACA,IAAIhB,IAAI,EAAEY,IAAI,EAAE;MACd6D,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC,CAACa,IAAI,GAAGZ,IAAI,EAAEY,IAAI;IAC7C;IACA,IAAIZ,IAAI,EAAEe,QAAQ,EAAE;MAClB0D,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC,CAACgB,QAAQ,GAAGf,IAAI,EAAEe,QAAQ;IACrD;IACA,IAAIf,IAAI,EAAEwE,QAAQ,EAAE;MAClBC,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC,CAACyE,QAAQ,GAAGxE,IAAI,EAAEwE,QAAQ;IACrD;IACA,IAAI,CAACxB,OAAO,CAACyB,IAAI,CAAC,IAAI,CAAC3E,QAAQ,CAAC,EAAE2E,IAAI,CAAC,IAAI,CAAC1E,cAAc,CAAC,EAAE,IAAI,CAAC2E,oBAAoB,EAAE,CAAC;EAC3F;EAEAA,oBAAoBA,CAAA;IAClB,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9E,QAAQ,CAAC;EAC9C;EAEAoC,QAAQA,CAAA;IACN,IAAI,CAAC2C,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACvF,IAAI,CAACqE,GAAG,CAAC,GAAGjF,gBAAgB,CAACkF,YAAY,IAAI,IAAI,CAACU,UAAU,CAAC9D,UAAU,SAAS,CAAC,CAACkB,SAAS,CAAC,MAAK;MACpG,IAAI,CAACoD,eAAe,EAAE;MACtB,CAAC,IAAI,CAACjF,eAAe,IACnB,IAAI,CAACF,cAAc,CAACkC,WAAW,CAAC;QAC9BC,IAAI,EAAE;OACP,CAAC;MACJ,IAAI,CAAC5B,WAAW,CAAC6E,IAAI,CAAC,IAAI,CAAC;MAC3BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAlF,OAAOA,CAAA;IACL,MAAMmF,SAAS,GAAQ,IAAI,CAACC,YAAY,EAAE;IAC1C,MAAMrE,WAAW,GAAQ,IAAI,CAACsE,cAAc,EAAE;IAC9C,IAAIF,SAAS,IAAI,IAAI,CAACG,YAAY,CAACvE,WAAW,CAAC,EAAE;MAC/C,OAAO;QACL,CAAC,IAAI,CAACjB,cAAc,GAAGyF,IAAI,CAACC,KAAK,CAACzE,WAAW,CAAC;QAC9C,CAAC,IAAI,CAAClB,QAAQ,GAAG0F,IAAI,CAACC,KAAK,CAACL,SAAS;OACtC;IACH;IACA,OAAO,EAAE;EACX;EAEApC,OAAOA,CAAC0C,KAAa,EAAE1F,IAAS,EAAE0C,UAAmB;IACnD,IAAIA,UAAU,EAAE;MACdiC,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC7F,QAAQ,EAAE0F,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC1Df,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC5F,cAAc,EAAEyF,IAAI,CAACI,SAAS,CAAC5F,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL6F,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC7F,QAAQ,EAAE0F,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC5DG,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC5F,cAAc,EAAEyF,IAAI,CAACI,SAAS,CAAC5F,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,CAACE,WAAW,CAAC6E,IAAI,CAAC;MACpB,CAAC,IAAI,CAAChF,cAAc,GAAGC,IAAI;MAC3B,CAAC,IAAI,CAACF,QAAQ,GAAG4F;KAClB,CAAC;EACJ;EAEAL,YAAYA,CAAA;IACV,OAAOV,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9E,QAAQ,CAAC,IAAI+F,cAAc,CAACjB,OAAO,CAAC,IAAI,CAAC9E,QAAQ,CAAC;EACrF;EAEAwF,cAAcA,CAAA;IACZ,OAAOX,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC7E,cAAc,CAAC,IAAI8F,cAAc,CAACjB,OAAO,CAAC,IAAI,CAAC7E,cAAc,CAAC;EACjG;EAEA+E,eAAeA,CAAA;IACbH,YAAY,CAACmB,UAAU,CAAC,IAAI,CAAChG,QAAQ,CAAC;IACtC+F,cAAc,CAACC,UAAU,CAAC,IAAI,CAAChG,QAAQ,CAAC;IACxC6E,YAAY,CAACmB,UAAU,CAAC,IAAI,CAAC/F,cAAc,CAAC;IAC5C8F,cAAc,CAACC,UAAU,CAAC,IAAI,CAAC/F,cAAc,CAAC;EAChD;EAEAwF,YAAYA,CAACQ,GAAQ;IACnB,IAAI;MACFP,IAAI,CAACC,KAAK,CAACM,GAAG,CAAC;IACjB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEMC,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMnF,WAAW,GAAGkF,KAAI,CAAC5B,UAAU;MACnC,aAAavF,aAAa,CAACmH,KAAI,CAAChD,oBAAoB,CAAClC,WAAW,CAAC,CAAC;IAAC;EACrE;EAEAkC,oBAAoBA,CAAClC,WAAgB;IACnC,OAAO,IAAI,CAAC1B,IAAI,CACbqE,GAAG,CAAM,GAAGjF,gBAAgB,CAACkF,YAAY,IAAI5C,WAAW,CAACR,UAAU,qBAAqB,CAAC,CACzFE,IAAI,CACH1B,GAAG,CAAE+D,GAAG,IAAI;MACV,IAAIA,GAAG,EAAEpB,IAAI,EAAEtB,MAAM,EAAE;QACrB,MAAMsB,IAAI,GAAIoB,GAAG,EAAEpB,IAAI,IAAI,EAAG;QAC9B,IAAI,CAAClC,WAAW,CAACsF,IAAI,CAACpD,IAAI,CAAC;QAC3B,OAAOA,IAAI;MACb;MACA,OAAO,EAAE;IACX,CAAC,CAAC,CACH,CACAjB,IAAI,CACH7B,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAAC3G,WAAW,CAACsF,IAAI,CAAC,EAAE,CAAC;MACzB,OAAOqB,KAAK;IACd,CAAC,CAAC,CACH;EACL;EAEA,IAAIC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC5G,WAAW,EAAE0E,KAAK,IAAI,EAAE;EACtC;EAEAmC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAChH,IAAI,CAACqE,GAAG,CAAoBnF,WAAW,CAAC+H,WAAW,CAAC,CAAC7F,IAAI,CACnE1B,GAAG,CAAEwH,QAAQ,IAAI;MACf,IAAI,CAAC9G,WAAW,CAACqF,IAAI,CAACyB,QAAQ,CAACd,KAAK,CAAC;MACrC,OAAOc,QAAQ,CAACd,KAAe;IACjC,CAAC,CAAC,CACH;EACH;EAEA,IAAIe,QAAQA,CAAA;IACV,OAAOxH,EAAE,CAAC,IAAI,CAACS,WAAW,EAAEyE,KAAK,IAAI,EAAE,CAAC;EAC1C;;;uBA9TW/E,WAAW,EAAAsH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAX5H,WAAW;MAAA6H,OAAA,EAAX7H,WAAW,CAAA8H,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}