{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let BackofficeComponent = /*#__PURE__*/(() => {\n  class BackofficeComponent {\n    constructor(renderer) {\n      this.renderer = renderer;\n    }\n    ngOnInit() {\n      // Inject theme \n      const href = 'assets/layout/styles/theme/theme-dark/magenta/theme.css';\n      const link = this.renderer.createElement('link');\n      this.renderer.setAttribute(link, 'id', 'theme-link');\n      this.renderer.setAttribute(link, 'rel', 'stylesheet');\n      this.renderer.setAttribute(link, 'type', 'text/css');\n      this.renderer.setAttribute(link, 'href', href);\n      // Append the link tag to the head of the document\n      this.renderer.appendChild(document.head, link);\n    }\n    ngOnDestroy() {\n      // Find and remove the link tag when the component is destroyed\n      const link = document.getElementById('theme-link');\n      if (link) {\n        link.remove();\n      }\n    }\n    static {\n      this.ɵfac = function BackofficeComponent_Factory(t) {\n        return new (t || BackofficeComponent)(i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BackofficeComponent,\n        selectors: [[\"app-backoffice\"]],\n        decls: 1,\n        vars: 0,\n        template: function BackofficeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [i1.RouterOutlet]\n      });\n    }\n  }\n  return BackofficeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}