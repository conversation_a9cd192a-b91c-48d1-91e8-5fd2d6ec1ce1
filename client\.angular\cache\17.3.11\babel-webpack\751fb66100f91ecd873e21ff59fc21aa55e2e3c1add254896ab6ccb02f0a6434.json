{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/dropdown\";\nimport * as i6 from \"primeng/tabview\";\nimport * as i7 from \"primeng/breadcrumb\";\nfunction SalesQuotesDetailsComponent_p_tabPanel_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction SalesQuotesDetailsComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 10);\n    i0.ɵɵtemplate(1, SalesQuotesDetailsComponent_p_tabPanel_8_ng_template_1_Template, 2, 2, \"ng-template\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let SalesQuotesDetailsComponent = /*#__PURE__*/(() => {\n  class SalesQuotesDetailsComponent {\n    constructor(router, route) {\n      this.router = router;\n      this.route = route;\n      this.unsubscribe$ = new Subject();\n      this.id = '';\n      this.activeItem = {};\n      this.activeIndex = 0;\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'Change Quote',\n        code: 'CQ'\n      }];\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `/store/sales-quotes/${id}/overview`\n      }\n      // {\n      //   label: 'Contacts',\n      //   routerLink: `/store/sales-quotes/${id}/contacts`\n      // },\n      // {\n      //   label: 'Sales Team',\n      //   routerLink: `/store/sales-quotes/${id}/sales-team`\n      // },\n      // {\n      //   label: 'AI Insights',\n      //   routerLink: `/store/sales-quotes/${id}/ai-insights`\n      // },\n      // {\n      //   label: 'Organization Data',\n      //   routerLink:  `/store/sales-quotes/${id}/organization-data`\n      // },\n      // {\n      //   label: 'Attachments',\n      //   routerLink: `/store/sales-quotes/${id}/attachments`,\n      // },\n      // {\n      //   label: 'Notes',\n      //   routerLink: `/store/sales-quotes/${id}/notes`,\n      // },\n      ];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Sales Quotes',\n        routerLink: ['/store/sales-quotes']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    NavigatetoChangeQuote(event) {\n      if (!event) return;\n      if (event.code == 'CQ') {\n        const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2&/SalesQuotationManage('${this.id}')`;\n        window.open(url, '_blank');\n      }\n    }\n    goToBack() {\n      this.router.navigate(['/store/sales-quotes']);\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesQuotesDetailsComponent_Factory(t) {\n        return new (t || SalesQuotesDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesQuotesDetailsComponent,\n        selectors: [[\"app-sales-quotes-details\"]],\n        decls: 11,\n        vars: 9,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function SalesQuotesDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-dropdown\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesQuotesDetailsComponent_Template_p_dropdown_ngModelChange_4_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function SalesQuotesDetailsComponent_Template_p_dropdown_onChange_4_listener($event) {\n              return ctx.NavigatetoChangeQuote($event.value);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"p-tabView\", 7);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function SalesQuotesDetailsComponent_Template_p_tabView_activeIndexChange_7_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function SalesQuotesDetailsComponent_Template_p_tabView_onChange_7_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(8, SalesQuotesDetailsComponent_p_tabPanel_8_Template, 2, 1, \"p-tabPanel\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 9);\n            i0.ɵɵelement(10, \"router-outlet\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          }\n        },\n        dependencies: [i2.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.NgControlStatus, i3.NgModel, i4.PrimeTemplate, i5.Dropdown, i6.TabView, i6.TabPanel, i7.Breadcrumb]\n      });\n    }\n  }\n  return SalesQuotesDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}