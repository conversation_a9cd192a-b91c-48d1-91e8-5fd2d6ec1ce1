{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, map, of } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../account.service\";\nimport * as i4 from \"src/app/store/opportunities/opportunities.service\";\nimport * as i5 from \"src/app/store/activities/activities.service\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/calendar\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"@ng-select/ng-select\";\nimport * as i14 from \"primeng/inputtext\";\nimport * as i15 from \"primeng/dialog\";\nimport * as i16 from \"primeng/editor\";\nimport * as i17 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  height: \"125px\"\n});\nfunction AccountOpportunitiesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 52);\n    i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 47)(5, AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 45);\n    i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 46);\n    i0.ɵɵtext(3, \" Name \");\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 47)(5, AccountOpportunitiesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r6 == null ? null : opportunity_r6.business_partner_owner == null ? null : opportunity_r6.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"opportunityStatus\", opportunity_r6 == null ? null : opportunity_r6.life_cycle_status_code) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r6 == null ? null : opportunity_r6.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r6 == null ? null : opportunity_r6.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r6 == null ? null : opportunity_r6.last_changed_by) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r6 == null ? null : opportunity_r6.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r6 == null ? null : opportunity_r6.expected_revenue_end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r6 == null ? null : opportunity_r6.opportunity_id) || \"-\", \" \");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 56);\n    i0.ɵɵtemplate(3, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_3_Template, 2, 1, \"ng-container\", 57)(4, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_4_Template, 2, 1, \"ng-container\", 57)(5, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_5_Template, 3, 4, \"ng-container\", 57)(6, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_6_Template, 2, 1, \"ng-container\", 57)(7, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_7_Template, 3, 4, \"ng-container\", 57)(8, AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_8_Template, 2, 1, \"ng-container\", 57);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"life_cycle_status_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"last_changed_by\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"opportunity_id\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 53);\n    i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_ng_template_10_Template_tr_click_0_listener() {\n      const opportunity_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToOpportunityDetail(opportunity_r6));\n    });\n    i0.ɵɵelementStart(1, \"td\", 54)(2, \"div\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_10_ng_container_4_Template, 9, 7, \"ng-container\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", opportunity_r6.name || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 58);\n    i0.ɵɵtext(2, \" No opportunities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 58);\n    i0.ɵɵtext(2, \" Loading opportunities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Opportunities\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_25_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"name\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.email, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r9.mobile, \"\");\n  }\n}\nfunction AccountOpportunitiesComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"input\", 62);\n    i0.ɵɵlistener(\"change\", function AccountOpportunitiesComponent_ng_template_41_Template_input_change_1_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).item;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSelection(item_r9.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AccountOpportunitiesComponent_ng_template_41_span_4_Template, 2, 1, \"span\", 60)(5, AccountOpportunitiesComponent_ng_template_41_span_5_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.item;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.isSelected(item_r9.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.bp_id, \": \", item_r9.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.mobile);\n  }\n}\nfunction AccountOpportunitiesComponent_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_42_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"primary_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expected Value is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_57_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"expected_revenue_amount\"].errors && ctx_r1.f[\"expected_revenue_amount\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_78_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_78_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"life_cycle_status_code\"].errors[\"required\"]);\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Notes is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountOpportunitiesComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AccountOpportunitiesComponent_div_103_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submitted && ctx_r1.f[\"note\"].errors && ctx_r1.f[\"note\"].errors[\"required\"]);\n  }\n}\nexport class AccountOpportunitiesComponent {\n  constructor(router, route, formBuilder, accountservice, opportunitiesservice, activitiesservice, messageservice) {\n    this.router = router;\n    this.route = route;\n    this.formBuilder = formBuilder;\n    this.accountservice = accountservice;\n    this.opportunitiesservice = opportunitiesservice;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.opportunitiesdetails = [];\n    this.bp_id = '';\n    this.account_id = '';\n    this.documentId = '';\n    this.position = 'right';\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.visible = false;\n    this.defaultSelected = false;\n    this.owner_id = null;\n    this.dropdowns = {\n      opportunityCategory: [],\n      opportunityStatus: [],\n      opportunitySource: []\n    };\n    this.OpportunityForm = this.formBuilder.group({\n      name: ['', [Validators.required]],\n      primary_contact_party_id: ['', [Validators.required]],\n      origin_type_code: [''],\n      expected_revenue_amount: ['', [Validators.required]],\n      expected_revenue_start_date: [''],\n      expected_revenue_end_date: [''],\n      life_cycle_status_code: ['', [Validators.required]],\n      probability_percent: [''],\n      group_code: [''],\n      note: ['', [Validators.required]]\n    });\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'business_partner_owner.bp_full_name',\n      header: 'Owner'\n    }, {\n      field: 'life_cycle_status_code',\n      header: 'Status'\n    }, {\n      field: 'expected_revenue_end_date',\n      header: 'Last Updated Date'\n    }, {\n      field: 'last_changed_by',\n      header: 'Last Updated By'\n    }, {\n      field: 'expected_revenue_end_date',\n      header: 'Close Date'\n    }, {\n      field: 'opportunity_id',\n      header: 'Opportunity ID'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.opportunitiesdetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response.bp_id;\n        this.documentId = response?.documentId;\n        this.account_id = response.bp_id;\n        this.loadAccountByContacts(this.account_id);\n        if (this.bp_id) {\n          this.opportunitiesservice.getOpportunity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n            next: opportunityResponse => {\n              if (Array.isArray(opportunityResponse.data)) {\n                this.opportunitiesdetails = opportunityResponse.data.slice(0, 10);\n              } else {\n                this.opportunitiesdetails = [];\n              }\n            },\n            error: error => {\n              console.error('Error fetching Opportunities:', error);\n            }\n          });\n        }\n      }\n    });\n    this.getOwner().subscribe({\n      next: response => {\n        this.owner_id = response;\n      },\n      error: err => {\n        console.error('Error fetching bp_id:', err);\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  getOwner() {\n    return this.activitiesservice.getEmailwisePartner();\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      const options = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n      // Assign options to dropdown object\n      this.dropdowns[target] = options;\n      // Set 'Open' as default selected for activityStatus only\n      if (target === 'opportunityStatus') {\n        const openOption = options.find(opt => opt.label.toLowerCase() === 'discover');\n        if (openOption) {\n          this.OpportunityForm.get('life_cycle_status_code')?.setValue(openOption.value);\n        }\n      }\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n        const currentValue = this.OpportunityForm.get('primary_contact_party_id')?.value;\n        // Only set default once and only if no manual selection\n        if (contacts.length > 0 && !currentValue?.length && !this.defaultSelected) {\n          this.OpportunityForm.get('primary_contact_party_id')?.setValue([contacts[0].bp_id]);\n          this.defaultSelected = true; // Mark as default set\n        }\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  toggleSelection(id) {\n    const control = this.OpportunityForm.get('primary_contact_party_id');\n    let currentValue = control?.value || [];\n    if (currentValue.includes(id)) {\n      currentValue = currentValue.filter(v => v !== id);\n    } else {\n      currentValue = [...currentValue, id];\n    }\n    control?.setValue(currentValue);\n  }\n  isSelected(id) {\n    return this.OpportunityForm.get('primary_contact_party_id')?.value?.includes(id);\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.OpportunityForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.OpportunityForm.value\n      };\n      const data = {\n        name: value?.name,\n        prospect_party_id: _this.account_id,\n        primary_contact_party_id: value?.primary_contact_party_id,\n        origin_type_code: value?.origin_type_code,\n        expected_revenue_amount: value?.expected_revenue_amount,\n        expected_revenue_start_date: value?.expected_revenue_start_date ? _this.formatDate(value.expected_revenue_start_date) : null,\n        expected_revenue_end_date: value?.expected_revenue_end_date ? _this.formatDate(value.expected_revenue_end_date) : null,\n        life_cycle_status_code: value?.life_cycle_status_code,\n        probability_percent: value?.probability_percent,\n        group_code: value?.group_code,\n        main_employee_responsible_party_id: _this.owner_id,\n        note: value?.note\n      };\n      _this.opportunitiesservice.createOpportunity(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.saving = false;\n          _this.visible = false;\n          _this.OpportunityForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Opportunities Added successfully!.'\n          });\n          _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: res => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.OpportunityForm.controls;\n  }\n  showDialog(position) {\n    this.position = position;\n    this.visible = true;\n    this.submitted = false;\n    this.OpportunityForm.reset();\n    setTimeout(() => {\n      this.loadOpportunityDropDown('opportunityCategory', 'CRM_OPPORTUNITY_GROUP');\n      this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n      this.loadOpportunityDropDown('opportunitySource', 'CRM_OPPORTUNITY_ORIGIN_TYPE');\n    }, 0);\n  }\n  navigateToOpportunityDetail(item) {\n    this.router.navigate(['detail', item?.opportunity_id], {\n      relativeTo: this.route,\n      state: {\n        opportunitydata: item,\n        moduleId: this.documentId\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function AccountOpportunitiesComponent_Factory(t) {\n      return new (t || AccountOpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.OpportunitiesService), i0.ɵɵdirectiveInject(i5.ActivitiesService), i0.ɵɵdirectiveInject(i6.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountOpportunitiesComponent,\n      selectors: [[\"app-account-opportunities\"]],\n      decls: 107,\n      vars: 60,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"d-grid\", \"gap-3\", \"text-base\"], [1, \"flex-1\"], [\"for\", \"Name\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"Account\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"readonly\", \"\", \"pTooltip\", \"Account ID\", 1, \"h-3rem\", \"w-full\", 3, \"value\"], [\"for\", \"Primary Contact\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"primary_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"for\", \"Source\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"origin_type_code\", \"placeholder\", \"Select a Source\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Expected Value\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"expected_revenue_amount\", \"type\", \"text\", \"formControlName\", \"expected_revenue_amount\", \"placeholder\", \"Expected Value\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Create Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_start_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Create Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Expected Decision Date\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"expected_revenue_end_date\", \"inputId\", \"calendar-12h\", \"hourFormat\", \"12\", \"styleClass\", \"h-3rem w-full\", \"appendTo\", \"body\", \"placeholder\", \"Expected Decision Date\", 3, \"showTime\", \"showIcon\"], [\"for\", \"Status\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"life_cycle_status_code\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"for\", \"Probability\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"pInputText\", \"\", \"id\", \"probability_percent\", \"type\", \"text\", \"formControlName\", \"probability_percent\", \"placeholder\", \"Probability\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Category\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"group_code\", \"placeholder\", \"Select a Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"for\", \"Notes\", 1, \"flex\", \"align-items-center\", \"font-semibold\", \"gap-1\", \"mb-1\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter your note here...\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"note-text\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\", \"pl-3\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"]],\n      template: function AccountOpportunitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Opportunities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_p_button_click_5_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountOpportunitiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function AccountOpportunitiesComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, AccountOpportunitiesComponent_ng_template_9_Template, 7, 3, \"ng-template\", 8)(10, AccountOpportunitiesComponent_ng_template_10_Template, 5, 2, \"ng-template\", 9)(11, AccountOpportunitiesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, AccountOpportunitiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AccountOpportunitiesComponent_Template_p_dialog_visibleChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(14, AccountOpportunitiesComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n          i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"div\", 15)(18, \"label\", 16)(19, \"span\", 17);\n          i0.ɵɵtext(20, \"badge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \"Name \");\n          i0.ɵɵelementStart(22, \"span\", 18);\n          i0.ɵɵtext(23, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(24, \"input\", 19);\n          i0.ɵɵtemplate(25, AccountOpportunitiesComponent_div_25_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 15)(27, \"label\", 21)(28, \"span\", 17);\n          i0.ɵɵtext(29, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \"Account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"input\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 15)(33, \"label\", 23)(34, \"span\", 17);\n          i0.ɵɵtext(35, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \"Primary Contact \");\n          i0.ɵɵelementStart(37, \"span\", 18);\n          i0.ɵɵtext(38, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"ng-select\", 24);\n          i0.ɵɵpipe(40, \"async\");\n          i0.ɵɵtemplate(41, AccountOpportunitiesComponent_ng_template_41_Template, 6, 5, \"ng-template\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, AccountOpportunitiesComponent_div_42_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 15)(44, \"label\", 26)(45, \"span\", 17);\n          i0.ɵɵtext(46, \"source\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \"Source \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"p-dropdown\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"label\", 28)(51, \"span\", 17);\n          i0.ɵɵtext(52, \"show_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \"Expected Value \");\n          i0.ɵɵelementStart(54, \"span\", 18);\n          i0.ɵɵtext(55, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(56, \"input\", 29);\n          i0.ɵɵtemplate(57, AccountOpportunitiesComponent_div_57_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 15)(59, \"label\", 30)(60, \"span\", 17);\n          i0.ɵɵtext(61, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \"Create Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"p-calendar\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 15)(65, \"label\", 32)(66, \"span\", 17);\n          i0.ɵɵtext(67, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(68, \"Expected Decision Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"p-calendar\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 15)(71, \"label\", 34)(72, \"span\", 17);\n          i0.ɵɵtext(73, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(74, \"Status \");\n          i0.ɵɵelementStart(75, \"span\", 18);\n          i0.ɵɵtext(76, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(77, \"p-dropdown\", 35);\n          i0.ɵɵtemplate(78, AccountOpportunitiesComponent_div_78_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 15)(80, \"label\", 36)(81, \"span\", 17);\n          i0.ɵɵtext(82, \"percent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(83, \"Probability \");\n          i0.ɵɵelementStart(84, \"span\", 18);\n          i0.ɵɵtext(85, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(86, \"input\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 15)(88, \"label\", 38)(89, \"span\", 17);\n          i0.ɵɵtext(90, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(91, \"Category \");\n          i0.ɵɵelementStart(92, \"span\", 18);\n          i0.ɵɵtext(93, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(94, \"p-dropdown\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 15)(96, \"label\", 40)(97, \"span\", 17);\n          i0.ɵɵtext(98, \"notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(99, \"Notes \");\n          i0.ɵɵelementStart(100, \"span\", 18);\n          i0.ɵɵtext(101, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(102, \"p-editor\", 41);\n          i0.ɵɵtemplate(103, AccountOpportunitiesComponent_div_103_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"div\", 42)(105, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_105_listener() {\n            return ctx.visible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function AccountOpportunitiesComponent_Template_button_click_106_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.opportunitiesdetails)(\"rows\", 10)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(48, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.OpportunityForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c1, ctx.submitted && ctx.f[\"name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"name\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.account_id);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(40, 46, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(51, _c1, ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"primary_contact_party_id\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunitySource\"]);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c1, ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"expected_revenue_amount\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"showTime\", true)(\"showIcon\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(55, _c1, ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"life_cycle_status_code\"].errors);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"options\", ctx.dropdowns[\"opportunityCategory\"]);\n          i0.ɵɵadvance(8);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(57, _c2));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(58, _c1, ctx.submitted && ctx.f[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"note\"].errors);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i7.NgSwitch, i7.NgSwitchCase, i8.Tooltip, i6.PrimeTemplate, i9.Dropdown, i10.Table, i10.SortableColumn, i10.FrozenColumn, i10.ReorderableColumn, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i11.Calendar, i12.ButtonDirective, i12.Button, i13.NgSelectComponent, i13.NgOptionTemplateDirective, i14.InputText, i15.Dialog, i16.Editor, i17.MultiSelect, i7.AsyncPipe, i7.DatePipe],\n      styles: [\".opportunity-popup .p-dialog.p-component.p-dialog-resizable {\\n  width: calc(100vw - 490px) !important;\\n}\\n  .opportunity-popup .field {\\n  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWNjb3VudC9hY2NvdW50LWRldGFpbHMvYWNjb3VudC1vcHBvcnR1bml0aWVzL2FjY291bnQtb3Bwb3J0dW5pdGllcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFUTtFQUNJLHFDQUFBO0FBRFo7QUFJUTtFQUNJLDREQUFBO0FBRloiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLm9wcG9ydHVuaXR5LXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cucC1jb21wb25lbnQucC1kaWFsb2ctcmVzaXphYmxlIHtcclxuICAgICAgICAgICAgd2lkdGg6IGNhbGMoMTAwdncgLSA0OTBweCkgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5maWVsZCB7XHJcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDM2MHB4LCAxZnIpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "map", "of", "Validators", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_4_Template", "AccountOpportunitiesComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "AccountOpportunitiesComponent_ng_template_9_Template_th_click_1_listener", "_r1", "AccountOpportunitiesComponent_ng_template_9_i_4_Template", "AccountOpportunitiesComponent_ng_template_9_i_5_Template", "AccountOpportunitiesComponent_ng_template_9_ng_container_6_Template", "selectedColumns", "opportunity_r6", "business_partner_owner", "bp_full_name", "getLabelFromDropdown", "life_cycle_status_code", "expected_revenue_end_date", "ɵɵpipeBind2", "last_changed_by", "opportunity_id", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_3_Template", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_4_Template", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_5_Template", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_6_Template", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_7_Template", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_ng_container_8_Template", "col_r7", "AccountOpportunitiesComponent_ng_template_10_Template_tr_click_0_listener", "_r5", "navigateToOpportunityDetail", "AccountOpportunitiesComponent_ng_template_10_ng_container_4_Template", "name", "AccountOpportunitiesComponent_div_25_div_1_Template", "submitted", "f", "errors", "item_r9", "email", "mobile", "AccountOpportunitiesComponent_ng_template_41_Template_input_change_1_listener", "_r8", "item", "toggleSelection", "bp_id", "AccountOpportunitiesComponent_ng_template_41_span_4_Template", "AccountOpportunitiesComponent_ng_template_41_span_5_Template", "isSelected", "ɵɵtextInterpolate2", "AccountOpportunitiesComponent_div_42_div_1_Template", "AccountOpportunitiesComponent_div_57_div_1_Template", "AccountOpportunitiesComponent_div_78_div_1_Template", "AccountOpportunitiesComponent_div_103_div_1_Template", "AccountOpportunitiesComponent", "constructor", "router", "route", "formBuilder", "accountservice", "opportunitiesservice", "activitiesservice", "messageservice", "unsubscribe$", "opportunitiesdetails", "account_id", "documentId", "position", "contactLoading", "contactInput$", "defaultOptions", "saving", "visible", "defaultSelected", "owner_id", "dropdowns", "opportunityCategory", "opportunityStatus", "opportunitySource", "OpportunityForm", "group", "required", "primary_contact_party_id", "origin_type_code", "expected_revenue_amount", "expected_revenue_start_date", "probability_percent", "group_code", "note", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "account", "pipe", "subscribe", "response", "loadAccountByContacts", "getOpportunity", "next", "opportunityResponse", "Array", "isArray", "slice", "error", "console", "get<PERSON>wner", "err", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "getEmail<PERSON><PERSON><PERSON><PERSON>", "loadOpportunityDropDown", "target", "type", "getOpportunityDropdownOptions", "res", "options", "attr", "label", "description", "value", "code", "openOption", "find", "opt", "toLowerCase", "get", "setValue", "dropdownKey", "bpId", "contacts$", "term", "params", "getPartnersContact", "contacts", "currentValue", "length", "id", "control", "v", "onSubmit", "_this", "_asyncToGenerator", "invalid", "prospect_party_id", "formatDate", "main_employee_responsible_party_id", "createOpportunity", "reset", "add", "severity", "detail", "getAccountByID", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showDialog", "setTimeout", "navigate", "relativeTo", "state", "opportunitydata", "moduleId", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "FormBuilder", "i3", "AccountService", "i4", "OpportunitiesService", "i5", "ActivitiesService", "i6", "MessageService", "selectors", "decls", "vars", "consts", "template", "AccountOpportunitiesComponent_Template", "rf", "ctx", "AccountOpportunitiesComponent_Template_p_button_click_5_listener", "ɵɵtwoWayListener", "AccountOpportunitiesComponent_Template_p_multiSelect_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "AccountOpportunitiesComponent_Template_p_table_onColReorder_8_listener", "AccountOpportunitiesComponent_ng_template_9_Template", "AccountOpportunitiesComponent_ng_template_10_Template", "AccountOpportunitiesComponent_ng_template_11_Template", "AccountOpportunitiesComponent_ng_template_12_Template", "AccountOpportunitiesComponent_Template_p_dialog_visibleChange_13_listener", "AccountOpportunitiesComponent_ng_template_14_Template", "AccountOpportunitiesComponent_div_25_Template", "AccountOpportunitiesComponent_ng_template_41_Template", "AccountOpportunitiesComponent_div_42_Template", "AccountOpportunitiesComponent_div_57_Template", "AccountOpportunitiesComponent_div_78_Template", "AccountOpportunitiesComponent_div_103_Template", "AccountOpportunitiesComponent_Template_button_click_105_listener", "AccountOpportunitiesComponent_Template_button_click_106_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "ɵɵpipeBind1", "_c2"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-opportunities\\account-opportunities.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { AccountService } from '../../account.service';\r\nimport { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n} from 'rxjs/operators';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\n\r\ninterface DropdownOption {\r\n  label: string;\r\n  value: string;\r\n}\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-account-opportunities',\r\n  templateUrl: './account-opportunities.component.html',\r\n  styleUrl: './account-opportunities.component.scss',\r\n})\r\nexport class AccountOpportunitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public opportunitiesdetails: any[] = [];\r\n  public bp_id: string = '';\r\n  public account_id: string = '';\r\n  public documentId: string = '';\r\n  public position: string = 'right';\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public visible: boolean = false;\r\n  private defaultSelected = false;\r\n  private owner_id: string | null = null;\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityCategory: [],\r\n    opportunityStatus: [],\r\n    opportunitySource: [],\r\n  };\r\n\r\n  public OpportunityForm: FormGroup = this.formBuilder.group({\r\n    name: ['', [Validators.required]],\r\n    primary_contact_party_id: ['', [Validators.required]],\r\n    origin_type_code: [''],\r\n    expected_revenue_amount: ['', [Validators.required]],\r\n    expected_revenue_start_date: [''],\r\n    expected_revenue_end_date: [''],\r\n    life_cycle_status_code: ['', [Validators.required]],\r\n    probability_percent: [''],\r\n    group_code: [''],\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private formBuilder: FormBuilder,\r\n    private accountservice: AccountService,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'business_partner_owner.bp_full_name', header: 'Owner' },\r\n    { field: 'life_cycle_status_code', header: 'Status' },\r\n    { field: 'expected_revenue_end_date', header: 'Last Updated Date' },\r\n    { field: 'last_changed_by', header: 'Last Updated By' },\r\n    { field: 'expected_revenue_end_date', header: 'Close Date' },\r\n    { field: 'opportunity_id', header: 'Opportunity ID' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.opportunitiesdetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response.bp_id;\r\n          this.documentId = response?.documentId;\r\n          this.account_id = response.bp_id;\r\n          this.loadAccountByContacts(this.account_id);\r\n          if (this.bp_id) {\r\n            this.opportunitiesservice\r\n              .getOpportunity(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe({\r\n                next: (opportunityResponse: any) => {\r\n                  if (Array.isArray(opportunityResponse.data)) {\r\n                    this.opportunitiesdetails = opportunityResponse.data.slice(\r\n                      0,\r\n                      10\r\n                    );\r\n                  } else {\r\n                    this.opportunitiesdetails = [];\r\n                  }\r\n                },\r\n                error: (error: any) => {\r\n                  console.error('Error fetching Opportunities:', error);\r\n                },\r\n              });\r\n          }\r\n        }\r\n      });\r\n\r\n    this.getOwner().subscribe({\r\n      next: (response: string | null) => {\r\n        this.owner_id = response;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching bp_id:', err);\r\n      },\r\n    });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  private getOwner(): Observable<string | null> {\r\n    return this.activitiesservice.getEmailwisePartner();\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        const options: DropdownOption[] =\r\n          res?.data?.map(\r\n            (attr: { description: string; code: string }): DropdownOption => ({\r\n              label: attr.description,\r\n              value: attr.code,\r\n            })\r\n          ) ?? [];\r\n\r\n        // Assign options to dropdown object\r\n        this.dropdowns[target] = options;\r\n\r\n        // Set 'Open' as default selected for activityStatus only\r\n        if (target === 'opportunityStatus') {\r\n          const openOption = options.find(\r\n            (opt) => opt.label.toLowerCase() === 'discover'\r\n          );\r\n          if (openOption) {\r\n            this.OpportunityForm.get('life_cycle_status_code')?.setValue(\r\n              openOption.value\r\n            );\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n\r\n            const currentValue = this.OpportunityForm.get(\r\n              'primary_contact_party_id'\r\n            )?.value;\r\n\r\n            // Only set default once and only if no manual selection\r\n            if (\r\n              contacts.length > 0 &&\r\n              !currentValue?.length &&\r\n              !this.defaultSelected\r\n            ) {\r\n              this.OpportunityForm.get('primary_contact_party_id')?.setValue([\r\n                contacts[0].bp_id,\r\n              ]);\r\n              this.defaultSelected = true; // Mark as default set\r\n            }\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  toggleSelection(id: string): void {\r\n    const control = this.OpportunityForm.get('primary_contact_party_id');\r\n    let currentValue = control?.value || [];\r\n\r\n    if (currentValue.includes(id)) {\r\n      currentValue = currentValue.filter((v: any) => v !== id);\r\n    } else {\r\n      currentValue = [...currentValue, id];\r\n    }\r\n\r\n    control?.setValue(currentValue);\r\n  }\r\n\r\n  isSelected(id: string): boolean {\r\n    return this.OpportunityForm.get(\r\n      'primary_contact_party_id'\r\n    )?.value?.includes(id);\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.OpportunityForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.OpportunityForm.value };\r\n\r\n    const data = {\r\n      name: value?.name,\r\n      prospect_party_id: this.account_id,\r\n      primary_contact_party_id: value?.primary_contact_party_id,\r\n      origin_type_code: value?.origin_type_code,\r\n      expected_revenue_amount: value?.expected_revenue_amount,\r\n      expected_revenue_start_date: value?.expected_revenue_start_date\r\n        ? this.formatDate(value.expected_revenue_start_date)\r\n        : null,\r\n      expected_revenue_end_date: value?.expected_revenue_end_date\r\n        ? this.formatDate(value.expected_revenue_end_date)\r\n        : null,\r\n      life_cycle_status_code: value?.life_cycle_status_code,\r\n      probability_percent: value?.probability_percent,\r\n      group_code: value?.group_code,\r\n      main_employee_responsible_party_id: this.owner_id,\r\n      note: value?.note,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .createOpportunity(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.saving = false;\r\n          this.visible = false;\r\n          this.OpportunityForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Opportunities Added successfully!.',\r\n          });\r\n          this.accountservice\r\n            .getAccountByID(this.documentId)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.OpportunityForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.position = position;\r\n    this.visible = true;\r\n    this.submitted = false;\r\n    this.OpportunityForm.reset();\r\n    setTimeout(() => {\r\n      this.loadOpportunityDropDown(\r\n        'opportunityCategory',\r\n        'CRM_OPPORTUNITY_GROUP'\r\n      );\r\n      this.loadOpportunityDropDown(\r\n        'opportunityStatus',\r\n        'CRM_OPPORTUNITY_STATUS'\r\n      );\r\n      this.loadOpportunityDropDown(\r\n        'opportunitySource',\r\n        'CRM_OPPORTUNITY_ORIGIN_TYPE'\r\n      );\r\n    }, 0);\r\n  }\r\n\r\n  navigateToOpportunityDetail(item: any) {\r\n    this.router.navigate(['detail', item?.opportunity_id], {\r\n      relativeTo: this.route,\r\n      state: { opportunitydata: item, moduleId: this.documentId },\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Opportunities</h4>\r\n        <div class=\"flex align-items-center gap-3 ml-auto\">\r\n            <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" (click)=\"showDialog('right')\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"opportunitiesdetails\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('name')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortField === 'name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-opportunity let-columns=\"columns\">\r\n                <tr (click)=\"navigateToOpportunityDetail(opportunity)\" class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        <div class=\"note-text\">\r\n                            {{ opportunity.name || '-' }}\r\n                        </div>\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'business_partner_owner.bp_full_name'\">\r\n                                    {{ opportunity?.business_partner_owner?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'life_cycle_status_code'\">\r\n                                    {{ getLabelFromDropdown('opportunityStatus',\r\n                                    opportunity?.life_cycle_status_code) || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'expected_revenue_end_date'\">\r\n                                    {{ opportunity?.expected_revenue_end_date ? (opportunity?.expected_revenue_end_date\r\n                                    | date:\r\n                                    'MM-dd-yyyy hh:mm a')\r\n                                    : '-'\r\n                                    }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'last_changed_by'\">\r\n                                    {{ opportunity?.last_changed_by || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'expected_revenue_end_date'\">\r\n                                    {{ opportunity?.expected_revenue_end_date ? (opportunity?.expected_revenue_end_date\r\n                                    | date:\r\n                                    'MM-dd-yyyy hh:mm a')\r\n                                    : '-'\r\n                                    }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'opportunity_id'\">\r\n                                    {{ opportunity?.opportunity_id || '-' }}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg pl-3\">\r\n                        No opportunities found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg pl-3\">\r\n                        Loading opportunities data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"visible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"opportunity-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Opportunities</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"OpportunityForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field d-grid gap-3 text-base\">\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Name\">\r\n                    <span class=\"material-symbols-rounded\">badge</span>Name\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"name\" type=\"text\" formControlName=\"name\" placeholder=\"Name\" class=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['name'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['name'].errors['required']\">\r\n                        Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Account\">\r\n                    <span class=\"material-symbols-rounded\">account_circle</span>Account\r\n                </label>\r\n                <input pInputText type=\"text\" [value]=\"account_id\" class=\"h-3rem w-full\" readonly\r\n                    pTooltip=\"Account ID\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Primary Contact\">\r\n                    <span class=\"material-symbols-rounded\">person</span>Primary Contact\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                    [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                    formControlName=\"primary_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                    appendTo=\"body\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <input type=\"checkbox\" [checked]=\"isSelected(item.bp_id)\"\r\n                                (change)=\"toggleSelection(item.bp_id)\"\r\n                                style=\"width: 20px; height: 20px; margin-right: 6px;\" />\r\n                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                            <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                            <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                        </div>\r\n                    </ng-template>\r\n                </ng-select>\r\n                <div *ngIf=\"submitted && f['primary_contact_party_id'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['primary_contact_party_id'].errors['required']\">\r\n                        Contact is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Source\">\r\n                    <span class=\"material-symbols-rounded\">source</span>Source\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunitySource']\" formControlName=\"origin_type_code\"\r\n                    placeholder=\"Select a Source\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Expected Value\">\r\n                    <span class=\"material-symbols-rounded\">show_chart</span>Expected Value\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"expected_revenue_amount\" type=\"text\" formControlName=\"expected_revenue_amount\"\r\n                    placeholder=\"Expected Value\" class=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }\" />\r\n                <div *ngIf=\"submitted && f['expected_revenue_amount'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                submitted &&\r\n                                f['expected_revenue_amount'].errors &&\r\n                                f['expected_revenue_amount'].errors['required']\r\n                              \">\r\n                        Expected Value is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Create Date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>Create Date\r\n                </label>\r\n                <p-calendar formControlName=\"expected_revenue_start_date\" inputId=\"calendar-12h\" [showTime]=\"true\"\r\n                    hourFormat=\"12\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\" appendTo=\"body\"\r\n                    placeholder=\"Create Date\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Expected Decision Date\">\r\n                    <span class=\"material-symbols-rounded\">schedule</span>Expected Decision Date\r\n                </label>\r\n                <p-calendar formControlName=\"expected_revenue_end_date\" inputId=\"calendar-12h\" [showTime]=\"true\"\r\n                    hourFormat=\"12\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\" appendTo=\"body\"\r\n                    placeholder=\"Expected Decision Date\" />\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Status\">\r\n                    <span class=\"material-symbols-rounded\">check_circle</span>Status\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunityStatus']\" formControlName=\"life_cycle_status_code\"\r\n                    placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }\">\r\n                </p-dropdown>\r\n                <div *ngIf=\"submitted && f['life_cycle_status_code'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"submitted && f['life_cycle_status_code'].errors['required']\">\r\n                        Status is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Probability\">\r\n                    <span class=\"material-symbols-rounded\">percent</span>Probability\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <input pInputText id=\"probability_percent\" type=\"text\" formControlName=\"probability_percent\"\r\n                    placeholder=\"Probability\" class=\"h-3rem w-full\" />\r\n            </div>\r\n\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Category\">\r\n                    <span class=\"material-symbols-rounded\">category</span>Category\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-dropdown [options]=\"dropdowns['opportunityCategory']\" formControlName=\"group_code\"\r\n                    placeholder=\"Select a Category\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\">\r\n                </p-dropdown>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <label class=\"flex align-items-center font-semibold gap-1 mb-1\" for=\"Notes\">\r\n                    <span class=\"material-symbols-rounded\">notes</span>Notes\r\n                    <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter your note here...\" [style]=\"{ height: '125px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': submitted && f['note'].errors }\" />\r\n                <div *ngIf=\"submitted && f['note'].errors\" class=\"p-error\">\r\n                    <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['note'].errors &&\r\n                                    f['note'].errors['required']\r\n                                  \">\r\n                        Notes is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"visible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,EAAsBC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAItE,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,QACP,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICWKC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA4D;;;;;IAQxDD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,wFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,uEAAA,gBACkF,IAAAC,uEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAjB7ChB,EADJ,CAAAM,cAAA,SAAI,aAC4E;IAA1DN,EAAA,CAAAO,UAAA,mBAAAmB,yEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAC1Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,aACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,wDAAA,gBACkF,IAAAC,wDAAA,gBAE1B;IAEhE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAY,mEAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAG1BzB,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAIRzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAyBpC/B,EAAA,CAAAK,uBAAA,GAAoE;IAChEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAC,sBAAA,kBAAAD,cAAA,CAAAC,sBAAA,CAAAC,YAAA,cACJ;;;;;IAEAlC,EAAA,CAAAK,uBAAA,GAAuD;IACnDL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAgC,oBAAA,sBAAAH,cAAA,kBAAAA,cAAA,CAAAI,sBAAA,cAEJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA0D;IACtDL,EAAA,CAAAiB,MAAA,GAKJ;;;;;;IALIjB,EAAA,CAAAsB,SAAA,EAKJ;IALItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAK,yBAAA,IAAArC,EAAA,CAAAsC,WAAA,OAAAN,cAAA,kBAAAA,cAAA,CAAAK,yBAAA,mCAKJ;;;;;IAEArC,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAO,eAAA,cACJ;;;;;IAEAvC,EAAA,CAAAK,uBAAA,GAA0D;IACtDL,EAAA,CAAAiB,MAAA,GAKJ;;;;;;IALIjB,EAAA,CAAAsB,SAAA,EAKJ;IALItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAK,yBAAA,IAAArC,EAAA,CAAAsC,WAAA,OAAAN,cAAA,kBAAAA,cAAA,CAAAK,yBAAA,mCAKJ;;;;;IAEArC,EAAA,CAAAK,uBAAA,GAA+C;IAC3CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAQ,cAAA,cACJ;;;;;IAlCZxC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IA8BjCL,EA7BA,CAAAkB,UAAA,IAAAuB,mFAAA,2BAAoE,IAAAC,mFAAA,2BAIb,IAAAC,mFAAA,2BAKG,IAAAC,mFAAA,2BAQV,IAAAC,mFAAA,2BAIU,IAAAC,mFAAA,2BAQX;;IAIvD9C,EAAA,CAAAqB,YAAA,EAAK;;;;;IAlCarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAA6C,MAAA,CAAA/B,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,uDAAmD;IAInDF,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,0CAAsC;IAKtCF,EAAA,CAAAsB,SAAA,EAAyC;IAAzCtB,EAAA,CAAAE,UAAA,6CAAyC;IAQzCF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAI/BF,EAAA,CAAAsB,SAAA,EAAyC;IAAzCtB,EAAA,CAAAE,UAAA,6CAAyC;IAQzCF,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAAE,UAAA,kCAA8B;;;;;;IAvC7DF,EAAA,CAAAM,cAAA,aAA8E;IAA1EN,EAAA,CAAAO,UAAA,mBAAAyC,0EAAA;MAAA,MAAAhB,cAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAAuC,GAAA,EAAArC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAA+C,2BAAA,CAAAlB,cAAA,CAAwC;IAAA,EAAC;IAE9ChC,EADJ,CAAAM,cAAA,aAAoG,cACzE;IACnBN,EAAA,CAAAiB,MAAA,GACJ;IACJjB,EADI,CAAAqB,YAAA,EAAM,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAiC,oEAAA,2BAAkD;IAsCtDnD,EAAA,CAAAqB,YAAA,EAAK;;;;;IA1COrB,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,cAAA,CAAAoB,IAAA,aACJ;IAG0BpD,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA2ChD/B,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,gCACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,mDACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAQbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,oBAAa;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAaVrB,EAAA,CAAAM,cAAA,UAAuD;IACnDN,EAAA,CAAAiB,MAAA,0BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHVrB,EAAA,CAAAM,cAAA,cAA2D;IACvDN,EAAA,CAAAkB,UAAA,IAAAmC,mDAAA,kBAAuD;IAG3DrD,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAA+C;IAA/CtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmD,SAAA,IAAAnD,MAAA,CAAAoD,CAAA,SAAAC,MAAA,aAA+C;;;;;IA6B7CxD,EAAA,CAAAM,cAAA,WAAyB;IAACN,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAAzBrB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAuB,kBAAA,QAAAkC,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5C1D,EAAA,CAAAM,cAAA,WAA0B;IAACN,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IAA1BrB,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAuB,kBAAA,QAAAkC,OAAA,CAAAE,MAAA,KAAmB;;;;;;IAL9C3D,EADJ,CAAAM,cAAA,cAA2C,gBAGqB;IADxDN,EAAA,CAAAO,UAAA,oBAAAqD,8EAAA;MAAA,MAAAH,OAAA,GAAAzD,EAAA,CAAAU,aAAA,CAAAmD,GAAA,EAAAC,IAAA;MAAA,MAAA3D,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUX,MAAA,CAAA4D,eAAA,CAAAN,OAAA,CAAAO,KAAA,CAA2B;IAAA,EAAC;IAD1ChE,EAAA,CAAAqB,YAAA,EAE4D;IAC5DrB,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAiB,MAAA,GAAyC;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;IAEtDrB,EADA,CAAAkB,UAAA,IAAA+C,4DAAA,mBAAyB,IAAAC,4DAAA,mBACC;IAC9BlE,EAAA,CAAAqB,YAAA,EAAM;;;;;IANqBrB,EAAA,CAAAsB,SAAA,EAAkC;IAAlCtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAgE,UAAA,CAAAV,OAAA,CAAAO,KAAA,EAAkC;IAGnDhE,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAAoE,kBAAA,KAAAX,OAAA,CAAAO,KAAA,QAAAP,OAAA,CAAAvB,YAAA,KAAyC;IACxClC,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAE,UAAA,SAAAuD,OAAA,CAAAC,KAAA,CAAgB;IAChB1D,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAAE,UAAA,SAAAuD,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhC3D,EAAA,CAAAM,cAAA,UAA2E;IACvEN,EAAA,CAAAiB,MAAA,6BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHVrB,EAAA,CAAAM,cAAA,cAA+E;IAC3EN,EAAA,CAAAkB,UAAA,IAAAmD,mDAAA,kBAA2E;IAG/ErE,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmD,SAAA,IAAAnD,MAAA,CAAAoD,CAAA,6BAAAC,MAAA,aAAmE;;;;;IAsBzExD,EAAA,CAAAM,cAAA,UAIY;IACRN,EAAA,CAAAiB,MAAA,oCACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAPVrB,EAAA,CAAAM,cAAA,cAA8E;IAC1EN,EAAA,CAAAkB,UAAA,IAAAoD,mDAAA,kBAIY;IAGhBtE,EAAA,CAAAqB,YAAA,EAAM;;;;IAPIrB,EAAA,CAAAsB,SAAA,EAIG;IAJHtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmD,SAAA,IAAAnD,MAAA,CAAAoD,CAAA,4BAAAC,MAAA,IAAArD,MAAA,CAAAoD,CAAA,4BAAAC,MAAA,aAIG;;;;;IA+BTxD,EAAA,CAAAM,cAAA,UAAyE;IACrEN,EAAA,CAAAiB,MAAA,4BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAHVrB,EAAA,CAAAM,cAAA,cAA6E;IACzEN,EAAA,CAAAkB,UAAA,IAAAqD,mDAAA,kBAAyE;IAG7EvE,EAAA,CAAAqB,YAAA,EAAM;;;;IAHIrB,EAAA,CAAAsB,SAAA,EAAiE;IAAjEtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmD,SAAA,IAAAnD,MAAA,CAAAoD,CAAA,2BAAAC,MAAA,aAAiE;;;;;IA+BvExD,EAAA,CAAAM,cAAA,UAIgB;IACZN,EAAA,CAAAiB,MAAA,2BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAM;;;;;IAPVrB,EAAA,CAAAM,cAAA,cAA2D;IACvDN,EAAA,CAAAkB,UAAA,IAAAsD,oDAAA,kBAIgB;IAGpBxE,EAAA,CAAAqB,YAAA,EAAM;;;;IAPIrB,EAAA,CAAAsB,SAAA,EAIO;IAJPtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmD,SAAA,IAAAnD,MAAA,CAAAoD,CAAA,SAAAC,MAAA,IAAArD,MAAA,CAAAoD,CAAA,SAAAC,MAAA,aAIO;;;AD9NjC,OAAM,MAAOiB,6BAA6B;EAoCxCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,WAAwB,EACxBC,cAA8B,EAC9BC,oBAA0C,EAC1CC,iBAAoC,EACpCC,cAA8B;IAN9B,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IA1ChB,KAAAC,YAAY,GAAG,IAAI7F,OAAO,EAAQ;IACnC,KAAA8F,oBAAoB,GAAU,EAAE;IAChC,KAAAnB,KAAK,GAAW,EAAE;IAClB,KAAAoB,UAAU,GAAW,EAAE;IACvB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,QAAQ,GAAW,OAAO;IAE1B,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAInG,OAAO,EAAU;IACpC,KAAAoG,cAAc,GAAQ,EAAE;IACzB,KAAAnC,SAAS,GAAG,KAAK;IACjB,KAAAoC,MAAM,GAAG,KAAK;IACd,KAAAC,OAAO,GAAY,KAAK;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,QAAQ,GAAkB,IAAI;IAE/B,KAAAC,SAAS,GAA0B;MACxCC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;KACpB;IAEM,KAAAC,eAAe,GAAc,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAAC;MACzD/C,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC3D,UAAU,CAAC2G,QAAQ,CAAC,CAAC;MACjCC,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAAC5G,UAAU,CAAC2G,QAAQ,CAAC,CAAC;MACrDE,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAAC9G,UAAU,CAAC2G,QAAQ,CAAC,CAAC;MACpDI,2BAA2B,EAAE,CAAC,EAAE,CAAC;MACjCnE,yBAAyB,EAAE,CAAC,EAAE,CAAC;MAC/BD,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAAC3C,UAAU,CAAC2G,QAAQ,CAAC,CAAC;MACnDK,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClH,UAAU,CAAC2G,QAAQ,CAAC;KACjC,CAAC;IAYM,KAAAQ,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAE7F,KAAK,EAAE,qCAAqC;MAAEQ,MAAM,EAAE;IAAO,CAAE,EACjE;MAAER,KAAK,EAAE,wBAAwB;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrD;MAAER,KAAK,EAAE,2BAA2B;MAAEQ,MAAM,EAAE;IAAmB,CAAE,EACnE;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EACvD;MAAER,KAAK,EAAE,2BAA2B;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC5D;MAAER,KAAK,EAAE,gBAAgB;MAAEQ,MAAM,EAAE;IAAgB,CAAE,CACtD;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAdlB;EAgBHW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC+E,oBAAoB,CAAC2B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACtC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE/F,KAAK,CAAC;MAC9C,MAAMmG,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAEhG,KAAK,CAAC;MAE9C,IAAIoG,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAAC/G,SAAS,GAAGgH,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAEtG,KAAa;IACvC,IAAI,CAACsG,IAAI,IAAI,CAACtG,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACuG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAACtG,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACwG,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC9C,cAAc,CAAC+C,OAAO,CACxBC,IAAI,CAACxI,SAAS,CAAC,IAAI,CAAC4F,YAAY,CAAC,CAAC,CAClC6C,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAChE,KAAK,GAAGgE,QAAQ,CAAChE,KAAK;QAC3B,IAAI,CAACqB,UAAU,GAAG2C,QAAQ,EAAE3C,UAAU;QACtC,IAAI,CAACD,UAAU,GAAG4C,QAAQ,CAAChE,KAAK;QAChC,IAAI,CAACiE,qBAAqB,CAAC,IAAI,CAAC7C,UAAU,CAAC;QAC3C,IAAI,IAAI,CAACpB,KAAK,EAAE;UACd,IAAI,CAACe,oBAAoB,CACtBmD,cAAc,CAAC,IAAI,CAAClE,KAAK,CAAC,CAC1B8D,IAAI,CAACxI,SAAS,CAAC,IAAI,CAAC4F,YAAY,CAAC,CAAC,CAClC6C,SAAS,CAAC;YACTI,IAAI,EAAGC,mBAAwB,IAAI;cACjC,IAAIC,KAAK,CAACC,OAAO,CAACF,mBAAmB,CAACd,IAAI,CAAC,EAAE;gBAC3C,IAAI,CAACnC,oBAAoB,GAAGiD,mBAAmB,CAACd,IAAI,CAACiB,KAAK,CACxD,CAAC,EACD,EAAE,CACH;cACH,CAAC,MAAM;gBACL,IAAI,CAACpD,oBAAoB,GAAG,EAAE;cAChC;YACF,CAAC;YACDqD,KAAK,EAAGA,KAAU,IAAI;cACpBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACvD;WACD,CAAC;QACN;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAACE,QAAQ,EAAE,CAACX,SAAS,CAAC;MACxBI,IAAI,EAAGH,QAAuB,IAAI;QAChC,IAAI,CAACnC,QAAQ,GAAGmC,QAAQ;MAC1B,CAAC;MACDQ,KAAK,EAAGG,GAAG,IAAI;QACbF,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEG,GAAG,CAAC;MAC7C;KACD,CAAC;IAEF,IAAI,CAAC/B,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAI9E,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC6E,gBAAgB;EAC9B;EAEA,IAAI7E,eAAeA,CAAC6G,GAAU;IAC5B,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACgC,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACtC,gBAAgB,CAACqC,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEQR,QAAQA,CAAA;IACd,OAAO,IAAI,CAAC1D,iBAAiB,CAACsE,mBAAmB,EAAE;EACrD;EAEAC,uBAAuBA,CAACC,MAAc,EAAEC,IAAY;IAClD,IAAI,CAAC1E,oBAAoB,CACtB2E,6BAA6B,CAACD,IAAI,CAAC,CACnC1B,SAAS,CAAE4B,GAAQ,IAAI;MACtB,MAAMC,OAAO,GACXD,GAAG,EAAErC,IAAI,EAAE/H,GAAG,CACXsK,IAA2C,KAAsB;QAChEC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CACH,IAAI,EAAE;MAET;MACA,IAAI,CAACnE,SAAS,CAAC0D,MAAM,CAAC,GAAGI,OAAO;MAEhC;MACA,IAAIJ,MAAM,KAAK,mBAAmB,EAAE;QAClC,MAAMU,UAAU,GAAGN,OAAO,CAACO,IAAI,CAC5BC,GAAG,IAAKA,GAAG,CAACN,KAAK,CAACO,WAAW,EAAE,KAAK,UAAU,CAChD;QACD,IAAIH,UAAU,EAAE;UACd,IAAI,CAAChE,eAAe,CAACoE,GAAG,CAAC,wBAAwB,CAAC,EAAEC,QAAQ,CAC1DL,UAAU,CAACF,KAAK,CACjB;QACH;MACF;IACF,CAAC,CAAC;EACN;EAEA7H,oBAAoBA,CAACqI,WAAmB,EAAER,KAAa;IACrD,MAAMlG,IAAI,GAAG,IAAI,CAACgC,SAAS,CAAC0E,WAAW,CAAC,EAAEL,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACJ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOlG,IAAI,EAAEgG,KAAK,IAAIE,KAAK;EAC7B;EAEQ/B,qBAAqBA,CAACwC,IAAY;IACxC,IAAI,CAACC,SAAS,GAAG,IAAI,CAAClF,aAAa,CAACsC,IAAI,CACtCjI,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC2F,cAAc,GAAG,IAAK,CAAC,EACvC5F,SAAS,CAAEgL,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEH,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIE,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAC3F,iBAAiB,CAAC6F,kBAAkB,CAACD,MAAM,CAAC,CAAC9C,IAAI,CAC3DvI,GAAG,CAAEyI,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxCpI,GAAG,CAAEkL,QAAe,IAAI;QACtB,IAAI,CAACvF,cAAc,GAAG,KAAK;QAE3B,MAAMwF,YAAY,GAAG,IAAI,CAAC7E,eAAe,CAACoE,GAAG,CAC3C,0BAA0B,CAC3B,EAAEN,KAAK;QAER;QACA,IACEc,QAAQ,CAACE,MAAM,GAAG,CAAC,IACnB,CAACD,YAAY,EAAEC,MAAM,IACrB,CAAC,IAAI,CAACpF,eAAe,EACrB;UACA,IAAI,CAACM,eAAe,CAACoE,GAAG,CAAC,0BAA0B,CAAC,EAAEC,QAAQ,CAAC,CAC7DO,QAAQ,CAAC,CAAC,CAAC,CAAC9G,KAAK,CAClB,CAAC;UACF,IAAI,CAAC4B,eAAe,GAAG,IAAI,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC,EACF9F,UAAU,CAAE0I,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjD,cAAc,GAAG,KAAK;QAC3B,OAAO/F,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEAuE,eAAeA,CAACkH,EAAU;IACxB,MAAMC,OAAO,GAAG,IAAI,CAAChF,eAAe,CAACoE,GAAG,CAAC,0BAA0B,CAAC;IACpE,IAAIS,YAAY,GAAGG,OAAO,EAAElB,KAAK,IAAI,EAAE;IAEvC,IAAIe,YAAY,CAAChC,QAAQ,CAACkC,EAAE,CAAC,EAAE;MAC7BF,YAAY,GAAGA,YAAY,CAAClC,MAAM,CAAEsC,CAAM,IAAKA,CAAC,KAAKF,EAAE,CAAC;IAC1D,CAAC,MAAM;MACLF,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAEE,EAAE,CAAC;IACtC;IAEAC,OAAO,EAAEX,QAAQ,CAACQ,YAAY,CAAC;EACjC;EAEA5G,UAAUA,CAAC8G,EAAU;IACnB,OAAO,IAAI,CAAC/E,eAAe,CAACoE,GAAG,CAC7B,0BAA0B,CAC3B,EAAEN,KAAK,EAAEjB,QAAQ,CAACkC,EAAE,CAAC;EACxB;EAEMG,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAAC/H,SAAS,GAAG,IAAI;MAErB,IAAI+H,KAAI,CAACnF,eAAe,CAACqF,OAAO,EAAE;QAChC;MACF;MAEAF,KAAI,CAAC3F,MAAM,GAAG,IAAI;MAClB,MAAMsE,KAAK,GAAG;QAAE,GAAGqB,KAAI,CAACnF,eAAe,CAAC8D;MAAK,CAAE;MAE/C,MAAM1C,IAAI,GAAG;QACXlE,IAAI,EAAE4G,KAAK,EAAE5G,IAAI;QACjBoI,iBAAiB,EAAEH,KAAI,CAACjG,UAAU;QAClCiB,wBAAwB,EAAE2D,KAAK,EAAE3D,wBAAwB;QACzDC,gBAAgB,EAAE0D,KAAK,EAAE1D,gBAAgB;QACzCC,uBAAuB,EAAEyD,KAAK,EAAEzD,uBAAuB;QACvDC,2BAA2B,EAAEwD,KAAK,EAAExD,2BAA2B,GAC3D6E,KAAI,CAACI,UAAU,CAACzB,KAAK,CAACxD,2BAA2B,CAAC,GAClD,IAAI;QACRnE,yBAAyB,EAAE2H,KAAK,EAAE3H,yBAAyB,GACvDgJ,KAAI,CAACI,UAAU,CAACzB,KAAK,CAAC3H,yBAAyB,CAAC,GAChD,IAAI;QACRD,sBAAsB,EAAE4H,KAAK,EAAE5H,sBAAsB;QACrDqE,mBAAmB,EAAEuD,KAAK,EAAEvD,mBAAmB;QAC/CC,UAAU,EAAEsD,KAAK,EAAEtD,UAAU;QAC7BgF,kCAAkC,EAAEL,KAAI,CAACxF,QAAQ;QACjDc,IAAI,EAAEqD,KAAK,EAAErD;OACd;MAED0E,KAAI,CAACtG,oBAAoB,CACtB4G,iBAAiB,CAACrE,IAAI,CAAC,CACvBQ,IAAI,CAACxI,SAAS,CAAC+L,KAAI,CAACnG,YAAY,CAAC,CAAC,CAClC6C,SAAS,CAAC;QACTI,IAAI,EAAEA,CAAA,KAAK;UACTkD,KAAI,CAAC3F,MAAM,GAAG,KAAK;UACnB2F,KAAI,CAAC1F,OAAO,GAAG,KAAK;UACpB0F,KAAI,CAACnF,eAAe,CAAC0F,KAAK,EAAE;UAC5BP,KAAI,CAACpG,cAAc,CAAC4G,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFV,KAAI,CAACvG,cAAc,CAChBkH,cAAc,CAACX,KAAI,CAAChG,UAAU,CAAC,CAC/ByC,IAAI,CAACxI,SAAS,CAAC+L,KAAI,CAACnG,YAAY,CAAC,CAAC,CAClC6C,SAAS,EAAE;QAChB,CAAC;QACDS,KAAK,EAAGmB,GAAQ,IAAI;UAClB0B,KAAI,CAAC3F,MAAM,GAAG,KAAK;UACnB2F,KAAI,CAACpG,cAAc,CAAC4G,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAN,UAAUA,CAACQ,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAIjJ,CAACA,CAAA;IACH,OAAO,IAAI,CAAC2C,eAAe,CAACwG,QAAQ;EACtC;EAEAC,UAAUA,CAACrH,QAAgB;IACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACK,OAAO,GAAG,IAAI;IACnB,IAAI,CAACrC,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC4C,eAAe,CAAC0F,KAAK,EAAE;IAC5BgB,UAAU,CAAC,MAAK;MACd,IAAI,CAACrD,uBAAuB,CAC1B,qBAAqB,EACrB,uBAAuB,CACxB;MACD,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,wBAAwB,CACzB;MACD,IAAI,CAACA,uBAAuB,CAC1B,mBAAmB,EACnB,6BAA6B,CAC9B;IACH,CAAC,EAAE,CAAC,CAAC;EACP;EAEArG,2BAA2BA,CAACY,IAAS;IACnC,IAAI,CAACa,MAAM,CAACkI,QAAQ,CAAC,CAAC,QAAQ,EAAE/I,IAAI,EAAEtB,cAAc,CAAC,EAAE;MACrDsK,UAAU,EAAE,IAAI,CAAClI,KAAK;MACtBmI,KAAK,EAAE;QAAEC,eAAe,EAAElJ,IAAI;QAAEmJ,QAAQ,EAAE,IAAI,CAAC5H;MAAU;KAC1D,CAAC;EACJ;EAEA6H,WAAWA,CAAA;IACT,IAAI,CAAChI,YAAY,CAACiD,IAAI,EAAE;IACxB,IAAI,CAACjD,YAAY,CAACiI,QAAQ,EAAE;EAC9B;;;uBA5WW1I,6BAA6B,EAAAzE,EAAA,CAAAoN,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtN,EAAA,CAAAoN,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAvN,EAAA,CAAAoN,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAzN,EAAA,CAAAoN,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA3N,EAAA,CAAAoN,iBAAA,CAAAQ,EAAA,CAAAC,oBAAA,GAAA7N,EAAA,CAAAoN,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAA/N,EAAA,CAAAoN,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA7BxJ,6BAA6B;MAAAyJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BlCxO,EAFR,CAAAM,cAAA,aAA2D,aACuC,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,oBAAa;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAE7DrB,EADJ,CAAAM,cAAA,aAAmD,kBAE0C;UAAhCN,EAAA,CAAAO,UAAA,mBAAAmO,iEAAA;YAAA,OAASD,GAAA,CAAA9B,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UADtF3M,EAAA,CAAAqB,YAAA,EACyF;UAEzFrB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAA2O,gBAAA,2BAAAC,8EAAAC,MAAA;YAAA7O,EAAA,CAAA8O,kBAAA,CAAAL,GAAA,CAAA1M,eAAA,EAAA8M,MAAA,MAAAJ,GAAA,CAAA1M,eAAA,GAAA8M,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE7O,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAAwO,uEAAAF,MAAA;YAAA,OAAgBJ,GAAA,CAAAzF,eAAA,CAAA6F,MAAA,CAAuB;UAAA,EAAC;UAoFxC7O,EAlFA,CAAAkB,UAAA,IAAA8N,oDAAA,yBAAgC,KAAAC,qDAAA,yBA0BoC,KAAAC,qDAAA,0BAiD9B,KAAAC,qDAAA,0BAOD;UASjDnP,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAM,cAAA,oBAC8B;UADLN,EAAA,CAAA2O,gBAAA,2BAAAS,0EAAAP,MAAA;YAAA7O,EAAA,CAAA8O,kBAAA,CAAAL,GAAA,CAAA9I,OAAA,EAAAkJ,MAAA,MAAAJ,GAAA,CAAA9I,OAAA,GAAAkJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAE1C7O,EAAA,CAAAkB,UAAA,KAAAmO,qDAAA,yBAAgC;UAQhBrP,EAJhB,CAAAM,cAAA,gBAA4E,eAC9B,eAClB,iBAC2D,gBAChC;UAAAN,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,aACnD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,iBACkE;UAClED,EAAA,CAAAkB,UAAA,KAAAoO,6CAAA,kBAA2D;UAK/DtP,EAAA,CAAAqB,YAAA,EAAM;UAIErB,EAFR,CAAAM,cAAA,eAAoB,iBAC8D,gBACnC;UAAAN,EAAA,CAAAiB,MAAA,sBAAc;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,gBAChE;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAC,SAAA,iBAC4B;UAChCD,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBACsE,gBAC3C;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,wBACpD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAM,cAAA,qBAIoF;;UAChFN,EAAA,CAAAkB,UAAA,KAAAqO,qDAAA,0BAA2C;UAU/CvP,EAAA,CAAAqB,YAAA,EAAY;UACZrB,EAAA,CAAAkB,UAAA,KAAAsO,6CAAA,kBAA+E;UAKnFxP,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBAC6D,gBAClC;UAAAN,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,eACxD;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAC,SAAA,sBAEa;UACjBD,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBACqE,gBAC1C;UAAAN,EAAA,CAAAiB,MAAA,kBAAU;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,uBACxD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,iBAEqF;UACrFD,EAAA,CAAAkB,UAAA,KAAAuO,6CAAA,kBAA8E;UASlFzP,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBACkE,gBACvC;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,oBAC1D;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAC,SAAA,sBAEgC;UACpCD,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBAC6E,gBAClD;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,+BAC1D;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACRrB,EAAA,CAAAC,SAAA,sBAE2C;UAC/CD,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBAC6D,gBAClC;UAAAN,EAAA,CAAAiB,MAAA,oBAAY;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,eAC1D;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,sBAGa;UACbD,EAAA,CAAAkB,UAAA,KAAAwO,6CAAA,kBAA6E;UAKjF1P,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBACkE,gBACvC;UAAAN,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,oBACrD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,iBACsD;UAC1DD,EAAA,CAAAqB,YAAA,EAAM;UAIErB,EAFR,CAAAM,cAAA,eAAoB,iBAC+D,gBACpC;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,iBACtD;UAAAjB,EAAA,CAAAM,cAAA,gBAA2B;UAAAN,EAAA,CAAAiB,MAAA,SAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,sBAEa;UACjBD,EAAA,CAAAqB,YAAA,EAAM;UAGErB,EAFR,CAAAM,cAAA,eAAoB,iBAC4D,gBACjC;UAAAN,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAAArB,EAAA,CAAAiB,MAAA,cACnD;UAAAjB,EAAA,CAAAM,cAAA,iBAA2B;UAAAN,EAAA,CAAAiB,MAAA,UAAC;UAChCjB,EADgC,CAAAqB,YAAA,EAAO,EAC/B;UACRrB,EAAA,CAAAC,SAAA,qBACkE;UAClED,EAAA,CAAAkB,UAAA,MAAAyO,8CAAA,kBAA2D;UAUnE3P,EADI,CAAAqB,YAAA,EAAM,EACJ;UAEFrB,EADJ,CAAAM,cAAA,gBAAgD,mBAGd;UAA1BN,EAAA,CAAAO,UAAA,mBAAAqP,iEAAA;YAAA,OAAAnB,GAAA,CAAA9I,OAAA,GAAmB,KAAK;UAAA,EAAC;UAAC3F,EAAA,CAAAqB,YAAA,EAAS;UACvCrB,EAAA,CAAAM,cAAA,mBACyB;UAArBN,EAAA,CAAAO,UAAA,mBAAAsP,iEAAA;YAAA,OAASpB,GAAA,CAAArD,QAAA,EAAU;UAAA,EAAC;UAIpCpL,EAJqC,CAAAqB,YAAA,EAAS,EAChC,EACH,EAEA;;;UAxQKrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzCF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAuO,GAAA,CAAA5H,IAAA,CAAgB;UAAC7G,EAAA,CAAA8P,gBAAA,YAAArB,GAAA,CAAA1M,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAA8B;UACoCtB,EADlE,CAAAE,UAAA,UAAAuO,GAAA,CAAAtJ,oBAAA,CAA8B,YAAyB,mBAAmB,cAAc,oBAChD,4BAAqD;UA+F/DnF,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAA+P,UAAA,CAAA/P,EAAA,CAAAgQ,eAAA,KAAAC,GAAA,EAA4B;UAAjEjQ,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAA8P,gBAAA,YAAArB,GAAA,CAAA9I,OAAA,CAAqB;UAAmD3F,EAArB,CAAAE,UAAA,qBAAoB,oBAAoB;UAM1GF,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAE,UAAA,cAAAuO,GAAA,CAAAvI,eAAA,CAA6B;UAQnBlG,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAkQ,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,SAAAC,MAAA,EAA2D;UACzDxD,EAAA,CAAAsB,SAAA,EAAmC;UAAnCtB,EAAA,CAAAE,UAAA,SAAAuO,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,SAAAC,MAAA,CAAmC;UAWXxD,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAE,UAAA,UAAAuO,GAAA,CAAArJ,UAAA,CAAoB;UAQ5BpF,EAAA,CAAAsB,SAAA,GAA2B;UAI7CtB,EAJkB,CAAAE,UAAA,UAAAF,EAAA,CAAAoQ,WAAA,SAAA3B,GAAA,CAAA/D,SAAA,EAA2B,sBACxB,YAAA+D,GAAA,CAAAlJ,cAAA,CAA2B,oBAAoB,cAAAkJ,GAAA,CAAAjJ,aAAA,CACE,wBAAwB,kBAC7D,wBAAwB,YAAAxF,EAAA,CAAAkQ,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,6BAAAC,MAAA,EACsB;UAY7ExD,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAE,UAAA,SAAAuO,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,6BAAAC,MAAA,CAAuD;UAUjDxD,EAAA,CAAAsB,SAAA,GAA0C;UAA1CtB,EAAA,CAAAE,UAAA,YAAAuO,GAAA,CAAA3I,SAAA,sBAA0C;UAWlD9F,EAAA,CAAAsB,SAAA,GAA8E;UAA9EtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAkQ,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,4BAAAC,MAAA,EAA8E;UAC5ExD,EAAA,CAAAsB,SAAA,EAAsD;UAAtDtB,EAAA,CAAAE,UAAA,SAAAuO,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,4BAAAC,MAAA,CAAsD;UAcqBxD,EAAA,CAAAsB,SAAA,GAAiB;UAC9EtB,EAD6D,CAAAE,UAAA,kBAAiB,kBAC7D;UAO0CF,EAAA,CAAAsB,SAAA,GAAiB;UAC5EtB,EAD2D,CAAAE,UAAA,kBAAiB,kBAC3D;UAQzBF,EAAA,CAAAsB,SAAA,GAA0C;UAElDtB,EAFQ,CAAAE,UAAA,YAAAuO,GAAA,CAAA3I,SAAA,sBAA0C,YAAA9F,EAAA,CAAAkQ,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,2BAAAC,MAAA,EAE2B;UAE3ExD,EAAA,CAAAsB,SAAA,EAAqD;UAArDtB,EAAA,CAAAE,UAAA,SAAAuO,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,2BAAAC,MAAA,CAAqD;UAoB/CxD,EAAA,CAAAsB,SAAA,IAA4C;UAA5CtB,EAAA,CAAAE,UAAA,YAAAuO,GAAA,CAAA3I,SAAA,wBAA4C;UASe9F,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAA+P,UAAA,CAAA/P,EAAA,CAAAgQ,eAAA,KAAAK,GAAA,EAA6B;UAChGrQ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAkQ,eAAA,KAAAC,GAAA,EAAA1B,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,SAAAC,MAAA,EAA2D;UACzDxD,EAAA,CAAAsB,SAAA,EAAmC;UAAnCtB,EAAA,CAAAE,UAAA,SAAAuO,GAAA,CAAAnL,SAAA,IAAAmL,GAAA,CAAAlL,CAAA,SAAAC,MAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}