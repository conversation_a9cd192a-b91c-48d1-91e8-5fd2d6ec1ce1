{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { ApiConstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class ProspectsService {\n  constructor(http, authservice) {\n    this.http = http;\n    this.authservice = authservice;\n    this.prospectSubject = new BehaviorSubject(null);\n    this.prospect = this.prospectSubject.asObservable();\n  }\n  createProspect(data) {\n    return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECTS}`, data);\n  }\n  createMarketing(data) {\n    return this.http.post(`${CMS_APIContstant.MARKETING_ATTRIBUTES}`, {\n      data\n    });\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createAddress(data) {\n    return this.http.post(`${CMS_APIContstant.PROSPECT_ADDRESS_REGISTER}`, data);\n  }\n  createContact(data) {\n    return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\n  }\n  createEmployee(data) {\n    return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECT_SALES_TEAM}`, data);\n  }\n  updateProspect(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PROSPECTS}/${Id}/save`, data);\n  }\n  updateMarketing(Id, data) {\n    return this.http.put(`${CMS_APIContstant.MARKETING_ATTRIBUTES}/${Id}`, {\n      data\n    });\n  }\n  updateAddress(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PROSPECT_ADDRESS}/${Id}/save`, data);\n  }\n  updateContact(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`, data);\n  }\n  updateReactivate(contactdata) {\n    const data = {\n      validity_end_date: '9999-12-29'\n    };\n    return this.http.put(`${CMS_APIContstant.PARTNERS_CONTACTS}/${contactdata.documentId}`, {\n      data\n    });\n  }\n  updateEmployee(Id, data) {\n    return this.http.put(`${CMS_APIContstant.SALES_TEAM}/${Id}/save`, data);\n  }\n  updateBpStatus(Id, data) {\n    return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  getProspects(page, pageSize, sortField, sortOrder, searchTerm, obsolete, myprospect) {\n    let params = new HttpParams().set('pagination[page]', Math.max(page, 1).toString()).set('pagination[pageSize]', Math.max(pageSize, 1).toString()).set('fields', 'bp_id,bp_full_name,is_marked_for_archiving').set('filters[roles][bp_role][$in][0]', 'PRO001').set('populate[addresses][fields][0]', 'house_number').set('populate[addresses][fields][1]', 'street_name').set('populate[addresses][fields][2]', 'city_name').set('populate[addresses][fields][3]', 'region').set('populate[addresses][fields][4]', 'country').set('populate[addresses][fields][5]', 'postal_code').set('populate[addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[addresses][populate][emails][fields][0]', 'email_address').set('populate[addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[customer][fields][0]', 'id').set('populate[customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][fields][0]', 'bp_full_name');\n    if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm && searchTerm.trim() !== '') {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][addresses][house_number][$containsi]', searchTerm).set('filters[$or][3][addresses][city_name][$containsi]', searchTerm).set('filters[$or][4][addresses][country][$containsi]', searchTerm).set('filters[$or][5][addresses][emails][email_address][$containsi]', searchTerm).set('filters[$or][6][addresses][phone_numbers][phone_number][$containsi]', searchTerm).set('filters[$or][7][contact_companies][business_partner_person][first_name][$containsi]', searchTerm).set('filters[$or][8][contact_companies][business_partner_person][last_name][$containsi]', searchTerm).set('filters[$or][9][customer][partner_functions][bp_identification][business_partner][bp_full_name][$containsi]', searchTerm);\n    }\n    // Combine obsolete and myprospect filters carefully to avoid index collision\n    if (obsolete || myprospect) {\n      let andIndex = 0;\n      if (obsolete) {\n        params = params.set(`filters[$and][${andIndex}][is_marked_for_archiving][$eq]`, 'true');\n        andIndex++;\n      }\n      if (myprospect) {\n        const email = this.authservice.getUserEmail();\n        if (email) {\n          params = params.set(`filters[$and][${andIndex}][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][${andIndex + 1}][customer][partner_functions][bp_identification][business_partner][addresses][emails][email_address][$containsi]`, email);\n        } else {\n          console.warn('No email found for the logged-in user');\n        }\n      }\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getEmployee(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate=identifications`, {\n      params\n    }).pipe(map(response => {\n      let data = response.data || [];\n      return data.map(item => {\n        const identification = item?.identifications?.find((i, index) => index === 0);\n        if (identification) {\n          item.bp_customer_number = identification?.bp_identification_number || null;\n        }\n        return item;\n      });\n    }));\n  }\n  getContacts(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      const contact = item?.addresses?.[0];\n      const email = contact?.emails?.[0]?.email_address || '';\n      const mobile = (contact?.phone_numbers || []).filter(item => item.phone_number_type === '3').map(item => item.phone_number);\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: (item?.first_name ? item.first_name : '') + (item?.last_name ? ' ' + item.last_name : ''),\n        email: email,\n        mobile: mobile\n      };\n    })));\n  }\n  getPartnerfunction() {\n    let params = new HttpParams().set('filters[usage][$eq]', 'CRM').set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    }).pipe(map(response => {\n      let data = response.data || [];\n      return data.map(item => ({\n        label: item.description,\n        // Display text\n        value: item.code // Stored value\n      }));\n    }));\n  }\n  getCPFunction() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'FUNCTION_CP');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getSizeUnit() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'BPMA_SIZE_UNIT');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getChainScale() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'BPMA_STR_CHAIN_SCALE');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getCPDepartment() {\n    let params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'CP_DEPARTMENTS');\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getGlobalNote(id) {\n    let params = new HttpParams().set('filters[is_global_note][$eq]', 'true').set('filters[bp_id][$eq]', id);\n    return this.http.get(`${CMS_APIContstant.CRM_NOTE}`, {\n      params\n    });\n  }\n  delete(id) {\n    return this.http.delete(`${CMS_APIContstant.PROSPECTS}/${id}`);\n  }\n  deleteContact(id) {\n    return this.http.delete(`${CMS_APIContstant.PARTNERS_CONTACTS}/${id}`);\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  deleteAddress(id) {\n    return this.http.delete(`${CMS_APIContstant.PARTNERS_ADDRESS}/${id}`);\n  }\n  deleteEmployee(id) {\n    return this.http.delete(`${CMS_APIContstant.DELETE_SALES_TEAM}/${id}`);\n  }\n  getProspectByID(prospectId) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', prospectId).set('populate[addresses][populate]', '*').set('populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]', '*').set('populate[notes][populate]', '*').set('populate[bp_extension][populate]', '*').set('populate[marketing_attributes][populate]', '*').set('populate[contact_companies][populate][person_func_and_dept][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][addresses][populate]', '*').set('populate[contact_companies][populate][business_partner_person][populate][bp_extension][populate]', '*').set('populate[address_usages][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => {\n      const prospectDetails = response?.data[0] || null;\n      this.prospectSubject.next(prospectDetails);\n      return response;\n    }));\n  }\n  bpCreation(body) {\n    return this.http.post(`${ApiConstant.BUSINESS_PARTNER}`, body);\n  }\n  static {\n    this.ɵfac = function ProspectsService_Factory(t) {\n      return new (t || ProspectsService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProspectsService,\n      factory: ProspectsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ApiConstant", "ProspectsService", "constructor", "http", "authservice", "prospectSubject", "prospect", "asObservable", "createProspect", "data", "post", "REGISTER_PROSPECTS", "createMarketing", "MARKETING_ATTRIBUTES", "createNote", "CRM_NOTE", "createAddress", "PROSPECT_ADDRESS_REGISTER", "createContact", "CREATE_CONTACT", "createEmployee", "REGISTER_PROSPECT_SALES_TEAM", "updateProspect", "Id", "put", "PROSPECTS", "updateMarketing", "updateAddress", "PROSPECT_ADDRESS", "updateContact", "PROSPECT_CONTACT", "updateReactivate", "contactdata", "validity_end_date", "PARTNERS_CONTACTS", "documentId", "updateEmployee", "SALES_TEAM", "updateBpStatus", "PARTNERS", "updateNote", "getProspects", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "obsolete", "myprospect", "params", "set", "Math", "max", "toString", "trim", "undefined", "order", "andIndex", "email", "getUserEmail", "console", "warn", "get", "getEmployee", "pipe", "response", "item", "identification", "identifications", "find", "i", "index", "bp_customer_number", "bp_identification_number", "getContacts", "contact", "addresses", "emails", "email_address", "mobile", "phone_numbers", "filter", "phone_number_type", "phone_number", "bp_id", "bp_full_name", "first_name", "last_name", "getPartnerfunction", "CONFIG_DATA", "label", "description", "value", "code", "getCPFunction", "getSizeUnit", "getChainScale", "getCPDepartment", "getGlobalNote", "id", "delete", "deleteContact", "deleteNote", "deleteAddress", "PARTNERS_ADDRESS", "deleteEmployee", "DELETE_SALES_TEAM", "getProspectByID", "prospectId", "prospectDetails", "next", "bpCreation", "body", "BUSINESS_PARTNER", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { ApiConstant } from 'src/app/constants/api.constants';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ProspectsService {\r\n  public prospectSubject = new BehaviorSubject<any>(null);\r\n  public prospect = this.prospectSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient, private authservice: AuthService) {}\r\n\r\n  createProspect(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECTS}`, data);\r\n  }\r\n\r\n  createMarketing(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.MARKETING_ATTRIBUTES}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createAddress(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.PROSPECT_ADDRESS_REGISTER}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createContact(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);\r\n  }\r\n\r\n  createEmployee(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.REGISTER_PROSPECT_SALES_TEAM}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  updateProspect(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.PROSPECTS}/${Id}/save`, data);\r\n  }\r\n\r\n  updateMarketing(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.MARKETING_ATTRIBUTES}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateAddress(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.PROSPECT_ADDRESS}/${Id}/save`,\r\n      data\r\n    );\r\n  }\r\n\r\n  updateContact(Id: string, data: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`,\r\n      data\r\n    );\r\n  }\r\n\r\n  updateReactivate(contactdata: any): Observable<any> {\r\n    const data = {\r\n      validity_end_date: '9999-12-29',\r\n    };\r\n    return this.http.put(\r\n      `${CMS_APIContstant.PARTNERS_CONTACTS}/${contactdata.documentId}`,\r\n      { data }\r\n    );\r\n  }\r\n\r\n  updateEmployee(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.SALES_TEAM}/${Id}/save`, data);\r\n  }\r\n\r\n  updateBpStatus(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, { data });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  getProspects(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    obsolete?: boolean,\r\n    myprospect?: boolean\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', Math.max(page, 1).toString())\r\n      .set('pagination[pageSize]', Math.max(pageSize, 1).toString())\r\n      .set('fields', 'bp_id,bp_full_name,is_marked_for_archiving')\r\n      .set('filters[roles][bp_role][$in][0]', 'PRO001')\r\n      .set('populate[addresses][fields][0]', 'house_number')\r\n      .set('populate[addresses][fields][1]', 'street_name')\r\n      .set('populate[addresses][fields][2]', 'city_name')\r\n      .set('populate[addresses][fields][3]', 'region')\r\n      .set('populate[addresses][fields][4]', 'country')\r\n      .set('populate[addresses][fields][5]', 'postal_code')\r\n      .set(\r\n        'populate[addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set('populate[addresses][populate][emails][fields][0]', 'email_address')\r\n      .set(\r\n        'populate[addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number_type'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][fields][0]',\r\n        'first_name'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][fields][1]',\r\n        'last_name'\r\n      )\r\n      .set('populate[customer][fields][0]', 'id')\r\n      .set(\r\n        'populate[customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      );\r\n\r\n    if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm && searchTerm.trim() !== '') {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)\r\n        .set('filters[$or][2][addresses][house_number][$containsi]', searchTerm)\r\n        .set('filters[$or][3][addresses][city_name][$containsi]', searchTerm)\r\n        .set('filters[$or][4][addresses][country][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][5][addresses][emails][email_address][$containsi]',\r\n          searchTerm\r\n        )\r\n        .set(\r\n          'filters[$or][6][addresses][phone_numbers][phone_number][$containsi]',\r\n          searchTerm\r\n        )\r\n        .set(\r\n          'filters[$or][7][contact_companies][business_partner_person][first_name][$containsi]',\r\n          searchTerm\r\n        )\r\n        .set(\r\n          'filters[$or][8][contact_companies][business_partner_person][last_name][$containsi]',\r\n          searchTerm\r\n        )\r\n        .set(\r\n          'filters[$or][9][customer][partner_functions][bp_identification][business_partner][bp_full_name][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n\r\n    // Combine obsolete and myprospect filters carefully to avoid index collision\r\n    if (obsolete || myprospect) {\r\n      let andIndex = 0;\r\n      if (obsolete) {\r\n        params = params.set(\r\n          `filters[$and][${andIndex}][is_marked_for_archiving][$eq]`,\r\n          'true'\r\n        );\r\n        andIndex++;\r\n      }\r\n      if (myprospect) {\r\n        const email = this.authservice.getUserEmail();\r\n        if (email) {\r\n          params = params\r\n            .set(\r\n              `filters[$and][${andIndex}][customer][partner_functions][partner_function][$eq]`,\r\n              'YI'\r\n            )\r\n            .set(\r\n              `filters[$and][${\r\n                andIndex + 1\r\n              }][customer][partner_functions][bp_identification][business_partner][addresses][emails][email_address][$containsi]`,\r\n              email\r\n            );\r\n        } else {\r\n          console.warn('No email found for the logged-in user');\r\n        }\r\n      }\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params });\r\n  }\r\n\r\n  getEmployee(params: any) {\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.PARTNERS}?populate=identifications`, {\r\n        params,\r\n      })\r\n      .pipe(\r\n        map((response: any) => {\r\n          let data = response.data || [];\r\n          return data.map((item: any) => {\r\n            const identification = item?.identifications?.find(\r\n              (i: any, index: number) => index === 0\r\n            );\r\n            if (identification) {\r\n              item.bp_customer_number =\r\n                identification?.bp_identification_number || null;\r\n            }\r\n            return item;\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  getContacts(params: any) {\r\n    return this.http\r\n      .get<any>(\r\n        `${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`,\r\n        { params }\r\n      )\r\n      .pipe(\r\n        map((response) =>\r\n          (response?.data || []).map((item: any) => {\r\n            const contact = item?.addresses?.[0];\r\n\r\n            const email = contact?.emails?.[0]?.email_address || '';\r\n            const mobile = (contact?.phone_numbers || [])\r\n              .filter((item: any) => item.phone_number_type === '3')\r\n              .map((item: any) => item.phone_number);\r\n\r\n            return {\r\n              bp_id: item?.bp_id || '',\r\n              bp_full_name:\r\n                (item?.first_name ? item.first_name : '') +\r\n                (item?.last_name ? ' ' + item.last_name : ''),\r\n              email: email,\r\n              mobile: mobile,\r\n            };\r\n          })\r\n        )\r\n      );\r\n  }\r\n\r\n  getPartnerfunction(): Observable<{ label: string; value: string }[]> {\r\n    let params = new HttpParams()\r\n      .set('filters[usage][$eq]', 'CRM')\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\r\n\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          let data = response.data || [];\r\n          return data.map((item: any) => ({\r\n            label: item.description, // Display text\r\n            value: item.code, // Stored value\r\n          }));\r\n        })\r\n      );\r\n  }\r\n\r\n  getCPFunction() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'FUNCTION_CP');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getSizeUnit() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'BPMA_SIZE_UNIT');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getChainScale() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'BPMA_STR_CHAIN_SCALE');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getCPDepartment() {\r\n    let params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'CP_DEPARTMENTS');\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getGlobalNote(id: string) {\r\n    let params = new HttpParams()\r\n      .set('filters[is_global_note][$eq]', 'true')\r\n      .set('filters[bp_id][$eq]', id);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  delete(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.PROSPECTS}/${id}`);\r\n  }\r\n\r\n  deleteContact(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}/${id}`);\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  deleteAddress(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.PARTNERS_ADDRESS}/${id}`);\r\n  }\r\n\r\n  deleteEmployee(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.DELETE_SALES_TEAM}/${id}`);\r\n  }\r\n\r\n  getProspectByID(prospectId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', prospectId)\r\n      .set('populate[addresses][populate]', '*')\r\n      .set(\r\n        'populate[customer][populate][partner_functions][populate][bp_identification][populate][business_partner][populate][addresses][populate]',\r\n        '*'\r\n      )\r\n      .set('populate[notes][populate]', '*')\r\n      .set('populate[bp_extension][populate]', '*')\r\n      .set('populate[marketing_attributes][populate]', '*')\r\n      .set(\r\n        'populate[contact_companies][populate][person_func_and_dept][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][populate][addresses][populate]',\r\n        '*'\r\n      )\r\n      .set(\r\n        'populate[contact_companies][populate][business_partner_person][populate][bp_extension][populate]',\r\n        '*'\r\n      )\r\n      .set('populate[address_usages][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const prospectDetails = response?.data[0] || null;\r\n          this.prospectSubject.next(prospectDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  bpCreation(body: any): any {\r\n    return this.http.post<any>(`${ApiConstant.BUSINESS_PARTNER}`, body);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,QAAQ,iCAAiC;;;;AAM7D,OAAM,MAAOC,gBAAgB;EAI3BC,YAAoBC,IAAgB,EAAUC,WAAwB;IAAlD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,WAAW,GAAXA,WAAW;IAHlD,KAAAC,eAAe,GAAG,IAAIR,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAS,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEoB;EAEzEC,cAAcA,CAACC,IAAS;IACtB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGX,gBAAgB,CAACY,kBAAkB,EAAE,EAAEF,IAAI,CAAC;EACvE;EAEAG,eAAeA,CAACH,IAAS;IACvB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGX,gBAAgB,CAACc,oBAAoB,EAAE,EAAE;MAChEJ;KACD,CAAC;EACJ;EAEAK,UAAUA,CAACL,IAAS;IAClB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGX,gBAAgB,CAACgB,QAAQ,EAAE,EAAE;MACpDN;KACD,CAAC;EACJ;EAEAO,aAAaA,CAACP,IAAS;IACrB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGX,gBAAgB,CAACkB,yBAAyB,EAAE,EAC/CR,IAAI,CACL;EACH;EAEAS,aAAaA,CAACT,IAAS;IACrB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGX,gBAAgB,CAACoB,cAAc,EAAE,EAAEV,IAAI,CAAC;EACnE;EAEAW,cAAcA,CAACX,IAAS;IACtB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGX,gBAAgB,CAACsB,4BAA4B,EAAE,EAClDZ,IAAI,CACL;EACH;EAEAa,cAAcA,CAACC,EAAU,EAAEd,IAAS;IAClC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAAC,GAAGzB,gBAAgB,CAAC0B,SAAS,IAAIF,EAAE,OAAO,EAAEd,IAAI,CAAC;EACxE;EAEAiB,eAAeA,CAACH,EAAU,EAAEd,IAAS;IACnC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAAC,GAAGzB,gBAAgB,CAACc,oBAAoB,IAAIU,EAAE,EAAE,EAAE;MACrEd;KACD,CAAC;EACJ;EAEAkB,aAAaA,CAACJ,EAAU,EAAEd,IAAS;IACjC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAClB,GAAGzB,gBAAgB,CAAC6B,gBAAgB,IAAIL,EAAE,OAAO,EACjDd,IAAI,CACL;EACH;EAEAoB,aAAaA,CAACN,EAAU,EAAEd,IAAS;IACjC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAClB,GAAGzB,gBAAgB,CAAC+B,gBAAgB,IAAIP,EAAE,OAAO,EACjDd,IAAI,CACL;EACH;EAEAsB,gBAAgBA,CAACC,WAAgB;IAC/B,MAAMvB,IAAI,GAAG;MACXwB,iBAAiB,EAAE;KACpB;IACD,OAAO,IAAI,CAAC9B,IAAI,CAACqB,GAAG,CAClB,GAAGzB,gBAAgB,CAACmC,iBAAiB,IAAIF,WAAW,CAACG,UAAU,EAAE,EACjE;MAAE1B;IAAI,CAAE,CACT;EACH;EAEA2B,cAAcA,CAACb,EAAU,EAAEd,IAAS;IAClC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAAC,GAAGzB,gBAAgB,CAACsC,UAAU,IAAId,EAAE,OAAO,EAAEd,IAAI,CAAC;EACzE;EAEA6B,cAAcA,CAACf,EAAU,EAAEd,IAAS;IAClC,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAAC,GAAGzB,gBAAgB,CAACwC,QAAQ,IAAIhB,EAAE,EAAE,EAAE;MAAEd;IAAI,CAAE,CAAC;EACtE;EAEA+B,UAAUA,CAACjB,EAAU,EAAEd,IAAS;IAC9B,OAAO,IAAI,CAACN,IAAI,CAACqB,GAAG,CAAC,GAAGzB,gBAAgB,CAACgB,QAAQ,IAAIQ,EAAE,EAAE,EAAE;MACzDd;KACD,CAAC;EACJ;EAEAgC,YAAYA,CACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBC,QAAkB,EAClBC,UAAoB;IAEpB,IAAIC,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,kBAAkB,EAAEC,IAAI,CAACC,GAAG,CAACV,IAAI,EAAE,CAAC,CAAC,CAACW,QAAQ,EAAE,CAAC,CACrDH,GAAG,CAAC,sBAAsB,EAAEC,IAAI,CAACC,GAAG,CAACT,QAAQ,EAAE,CAAC,CAAC,CAACU,QAAQ,EAAE,CAAC,CAC7DH,GAAG,CAAC,QAAQ,EAAE,4CAA4C,CAAC,CAC3DA,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAChDA,GAAG,CAAC,gCAAgC,EAAE,cAAc,CAAC,CACrDA,GAAG,CAAC,gCAAgC,EAAE,aAAa,CAAC,CACpDA,GAAG,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAClDA,GAAG,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAC/CA,GAAG,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAChDA,GAAG,CAAC,gCAAgC,EAAE,aAAa,CAAC,CACpDA,GAAG,CACF,0DAA0D,EAC1D,eAAe,CAChB,CACAA,GAAG,CAAC,kDAAkD,EAAE,eAAe,CAAC,CACxEA,GAAG,CACF,yDAAyD,EACzD,cAAc,CACf,CACAA,GAAG,CACF,yDAAyD,EACzD,mBAAmB,CACpB,CACAA,GAAG,CACF,2EAA2E,EAC3E,YAAY,CACb,CACAA,GAAG,CACF,2EAA2E,EAC3E,WAAW,CACZ,CACAA,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAC1CA,GAAG,CACF,4DAA4D,EAC5D,kBAAkB,CACnB,CACAA,GAAG,CACF,qHAAqH,EACrH,cAAc,CACf;IAEH,IAAIN,SAAS,IAAIA,SAAS,CAACU,IAAI,EAAE,KAAK,EAAE,IAAIT,SAAS,KAAKU,SAAS,EAAE;MACnE,MAAMC,KAAK,GAAGX,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CI,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGN,SAAS,IAAIY,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIV,UAAU,IAAIA,UAAU,CAACQ,IAAI,EAAE,KAAK,EAAE,EAAE;MAC1CL,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEJ,UAAU,CAAC,CACrDI,GAAG,CAAC,2CAA2C,EAAEJ,UAAU,CAAC,CAC5DI,GAAG,CAAC,sDAAsD,EAAEJ,UAAU,CAAC,CACvEI,GAAG,CAAC,mDAAmD,EAAEJ,UAAU,CAAC,CACpEI,GAAG,CAAC,iDAAiD,EAAEJ,UAAU,CAAC,CAClEI,GAAG,CACF,+DAA+D,EAC/DJ,UAAU,CACX,CACAI,GAAG,CACF,qEAAqE,EACrEJ,UAAU,CACX,CACAI,GAAG,CACF,qFAAqF,EACrFJ,UAAU,CACX,CACAI,GAAG,CACF,oFAAoF,EACpFJ,UAAU,CACX,CACAI,GAAG,CACF,6GAA6G,EAC7GJ,UAAU,CACX;IACL;IAEA;IACA,IAAIC,QAAQ,IAAIC,UAAU,EAAE;MAC1B,IAAIS,QAAQ,GAAG,CAAC;MAChB,IAAIV,QAAQ,EAAE;QACZE,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,iBAAiBO,QAAQ,iCAAiC,EAC1D,MAAM,CACP;QACDA,QAAQ,EAAE;MACZ;MACA,IAAIT,UAAU,EAAE;QACd,MAAMU,KAAK,GAAG,IAAI,CAACtD,WAAW,CAACuD,YAAY,EAAE;QAC7C,IAAID,KAAK,EAAE;UACTT,MAAM,GAAGA,MAAM,CACZC,GAAG,CACF,iBAAiBO,QAAQ,uDAAuD,EAChF,IAAI,CACL,CACAP,GAAG,CACF,iBACEO,QAAQ,GAAG,CACb,mHAAmH,EACnHC,KAAK,CACN;QACL,CAAC,MAAM;UACLE,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;QACvD;MACF;IACF;IAEA,OAAO,IAAI,CAAC1D,IAAI,CAAC2D,GAAG,CAAQ,GAAG/D,gBAAgB,CAACwC,QAAQ,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EACzE;EAEAc,WAAWA,CAACd,MAAW;IACrB,OAAO,IAAI,CAAC9C,IAAI,CACb2D,GAAG,CAAQ,GAAG/D,gBAAgB,CAACwC,QAAQ,2BAA2B,EAAE;MACnEU;KACD,CAAC,CACDe,IAAI,CACHlE,GAAG,CAAEmE,QAAa,IAAI;MACpB,IAAIxD,IAAI,GAAGwD,QAAQ,CAACxD,IAAI,IAAI,EAAE;MAC9B,OAAOA,IAAI,CAACX,GAAG,CAAEoE,IAAS,IAAI;QAC5B,MAAMC,cAAc,GAAGD,IAAI,EAAEE,eAAe,EAAEC,IAAI,CAChD,CAACC,CAAM,EAAEC,KAAa,KAAKA,KAAK,KAAK,CAAC,CACvC;QACD,IAAIJ,cAAc,EAAE;UAClBD,IAAI,CAACM,kBAAkB,GACrBL,cAAc,EAAEM,wBAAwB,IAAI,IAAI;QACpD;QACA,OAAOP,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEAQ,WAAWA,CAACzB,MAAW;IACrB,OAAO,IAAI,CAAC9C,IAAI,CACb2D,GAAG,CACF,GAAG/D,gBAAgB,CAACwC,QAAQ,qEAAqE,EACjG;MAAEU;IAAM,CAAE,CACX,CACAe,IAAI,CACHlE,GAAG,CAAEmE,QAAQ,IACX,CAACA,QAAQ,EAAExD,IAAI,IAAI,EAAE,EAAEX,GAAG,CAAEoE,IAAS,IAAI;MACvC,MAAMS,OAAO,GAAGT,IAAI,EAAEU,SAAS,GAAG,CAAC,CAAC;MAEpC,MAAMlB,KAAK,GAAGiB,OAAO,EAAEE,MAAM,GAAG,CAAC,CAAC,EAAEC,aAAa,IAAI,EAAE;MACvD,MAAMC,MAAM,GAAG,CAACJ,OAAO,EAAEK,aAAa,IAAI,EAAE,EACzCC,MAAM,CAAEf,IAAS,IAAKA,IAAI,CAACgB,iBAAiB,KAAK,GAAG,CAAC,CACrDpF,GAAG,CAAEoE,IAAS,IAAKA,IAAI,CAACiB,YAAY,CAAC;MAExC,OAAO;QACLC,KAAK,EAAElB,IAAI,EAAEkB,KAAK,IAAI,EAAE;QACxBC,YAAY,EACV,CAACnB,IAAI,EAAEoB,UAAU,GAAGpB,IAAI,CAACoB,UAAU,GAAG,EAAE,KACvCpB,IAAI,EAAEqB,SAAS,GAAG,GAAG,GAAGrB,IAAI,CAACqB,SAAS,GAAG,EAAE,CAAC;QAC/C7B,KAAK,EAAEA,KAAK;QACZqB,MAAM,EAAEA;OACT;IACH,CAAC,CAAC,CACH,CACF;EACL;EAEAS,kBAAkBA,CAAA;IAChB,IAAIvC,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,CACjCA,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;IAEjD,OAAO,IAAI,CAAC/C,IAAI,CACb2D,GAAG,CAAM,GAAG/D,gBAAgB,CAAC0F,WAAW,EAAE,EAAE;MAAExC;IAAM,CAAE,CAAC,CACvDe,IAAI,CACHlE,GAAG,CAAEmE,QAAa,IAAI;MACpB,IAAIxD,IAAI,GAAGwD,QAAQ,CAACxD,IAAI,IAAI,EAAE;MAC9B,OAAOA,IAAI,CAACX,GAAG,CAAEoE,IAAS,KAAM;QAC9BwB,KAAK,EAAExB,IAAI,CAACyB,WAAW;QAAE;QACzBC,KAAK,EAAE1B,IAAI,CAAC2B,IAAI,CAAE;OACnB,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACL;EAEAC,aAAaA,CAAA;IACX,IAAI7C,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,aAAa,CAAC;IAE3C,OAAO,IAAI,CAAC/C,IAAI,CAAC2D,GAAG,CAAM,GAAG/D,gBAAgB,CAAC0F,WAAW,EAAE,EAAE;MAAExC;IAAM,CAAE,CAAC;EAC1E;EAEA8C,WAAWA,CAAA;IACT,IAAI9C,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9C,OAAO,IAAI,CAAC/C,IAAI,CAAC2D,GAAG,CAAM,GAAG/D,gBAAgB,CAAC0F,WAAW,EAAE,EAAE;MAAExC;IAAM,CAAE,CAAC;EAC1E;EAEA+C,aAAaA,CAAA;IACX,IAAI/C,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;IAEpD,OAAO,IAAI,CAAC/C,IAAI,CAAC2D,GAAG,CAAM,GAAG/D,gBAAgB,CAAC0F,WAAW,EAAE,EAAE;MAAExC;IAAM,CAAE,CAAC;EAC1E;EAEAgD,eAAeA,CAAA;IACb,IAAIhD,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9C,OAAO,IAAI,CAAC/C,IAAI,CAAC2D,GAAG,CAAM,GAAG/D,gBAAgB,CAAC0F,WAAW,EAAE,EAAE;MAAExC;IAAM,CAAE,CAAC;EAC1E;EAEAiD,aAAaA,CAACC,EAAU;IACtB,IAAIlD,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC1BsD,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAC3CA,GAAG,CAAC,qBAAqB,EAAEiD,EAAE,CAAC;IAEjC,OAAO,IAAI,CAAChG,IAAI,CAAC2D,GAAG,CAAM,GAAG/D,gBAAgB,CAACgB,QAAQ,EAAE,EAAE;MACxDkC;KACD,CAAC;EACJ;EAEAmD,MAAMA,CAACD,EAAU;IACf,OAAO,IAAI,CAAChG,IAAI,CAACiG,MAAM,CAAM,GAAGrG,gBAAgB,CAAC0B,SAAS,IAAI0E,EAAE,EAAE,CAAC;EACrE;EAEAE,aAAaA,CAACF,EAAU;IACtB,OAAO,IAAI,CAAChG,IAAI,CAACiG,MAAM,CAAM,GAAGrG,gBAAgB,CAACmC,iBAAiB,IAAIiE,EAAE,EAAE,CAAC;EAC7E;EAEAG,UAAUA,CAACH,EAAU;IACnB,OAAO,IAAI,CAAChG,IAAI,CAACiG,MAAM,CAAM,GAAGrG,gBAAgB,CAACgB,QAAQ,IAAIoF,EAAE,EAAE,CAAC;EACpE;EAEAI,aAAaA,CAACJ,EAAU;IACtB,OAAO,IAAI,CAAChG,IAAI,CAACiG,MAAM,CAAM,GAAGrG,gBAAgB,CAACyG,gBAAgB,IAAIL,EAAE,EAAE,CAAC;EAC5E;EAEAM,cAAcA,CAACN,EAAU;IACvB,OAAO,IAAI,CAAChG,IAAI,CAACiG,MAAM,CAAM,GAAGrG,gBAAgB,CAAC2G,iBAAiB,IAAIP,EAAE,EAAE,CAAC;EAC7E;EAEAQ,eAAeA,CAACC,UAAkB;IAChC,MAAM3D,MAAM,GAAG,IAAIrD,UAAU,EAAE,CAC5BsD,GAAG,CAAC,qBAAqB,EAAE0D,UAAU,CAAC,CACtC1D,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,CACzCA,GAAG,CACF,yIAAyI,EACzI,GAAG,CACJ,CACAA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAC5CA,GAAG,CAAC,0CAA0C,EAAE,GAAG,CAAC,CACpDA,GAAG,CACF,uEAAuE,EACvE,GAAG,CACJ,CACAA,GAAG,CACF,+FAA+F,EAC/F,GAAG,CACJ,CACAA,GAAG,CACF,kGAAkG,EAClG,GAAG,CACJ,CACAA,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC;IAEjD,OAAO,IAAI,CAAC/C,IAAI,CACb2D,GAAG,CAAQ,GAAG/D,gBAAgB,CAACwC,QAAQ,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC,CACtDe,IAAI,CACHlE,GAAG,CAAEmE,QAAa,IAAI;MACpB,MAAM4C,eAAe,GAAG5C,QAAQ,EAAExD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACjD,IAAI,CAACJ,eAAe,CAACyG,IAAI,CAACD,eAAe,CAAC;MAC1C,OAAO5C,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEA8C,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAAC7G,IAAI,CAACO,IAAI,CAAM,GAAGV,WAAW,CAACiH,gBAAgB,EAAE,EAAED,IAAI,CAAC;EACrE;;;uBAxXW/G,gBAAgB,EAAAiH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAhBtH,gBAAgB;MAAAuH,OAAA,EAAhBvH,gBAAgB,CAAAwH,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}