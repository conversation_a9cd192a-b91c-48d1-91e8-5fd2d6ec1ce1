{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./forgot-password.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/dialog\";\nimport * as i5 from \"primeng/dropdown\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ForgotPasswordComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email is invalid\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, ForgotPasswordComponent_div_7_div_1_Template, 2, 0, \"div\", 15)(2, ForgotPasswordComponent_div_7_div_2_Template, 2, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email\"].errors[\"email\"]);\n  }\n}\nexport let ForgotPasswordComponent = /*#__PURE__*/(() => {\n  class ForgotPasswordComponent {\n    onDialogHide() {\n      this.visible = false;\n      this.visibleChange.emit(this.visible);\n    }\n    constructor(formBuilder, service) {\n      this.formBuilder = formBuilder;\n      this.service = service;\n      this.form = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]]\n      });\n      this.submitted = false;\n      this.saving = false;\n      this.visible = false;\n      this.visibleChange = new EventEmitter();\n      this.cities = [{\n        name: \"What's was your first car?\",\n        code: \"NY\"\n      }, {\n        name: \"What was your favorite school teacher's name?\",\n        code: \"RM\"\n      }, {\n        name: 'What is your date of birth?',\n        code: 'LDN'\n      }, {\n        name: 'What’s your favorite movie?',\n        code: 'IST'\n      }, {\n        name: 'What is your astrological sign?',\n        code: 'PRS'\n      }];\n    }\n    ngOnInit() {}\n    get f() {\n      return this.form.controls;\n    }\n    onSubmit() {\n      this.submitted = true;\n      if (this.form.invalid) {\n        return;\n      }\n      this.saving = true;\n      this.service.forgotPassword(this.form.value).subscribe({\n        complete: () => {\n          this.onReset();\n          // this.activeModal.close();\n          this.saving = false;\n          // this._snackBar.open('Reset password link sent successfully!');\n        },\n        error: err => {\n          this.saving = false;\n          // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\n        }\n      });\n    }\n    onReset() {\n      this.submitted = false;\n      this.form.reset();\n    }\n    static {\n      this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n        return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ForgotPasswordService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ForgotPasswordComponent,\n        selectors: [[\"app-forgot-password\"]],\n        inputs: {\n          visible: \"visible\"\n        },\n        outputs: {\n          visibleChange: \"visibleChange\"\n        },\n        decls: 31,\n        vars: 18,\n        consts: [[\"header\", \"Forgot Password\", \"styleClass\", \"bg-white w-30rem\", 3, \"visibleChange\", \"onHide\", \"visible\", \"modal\", \"closable\", \"resizable\"], [3, \"formGroup\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"required\", \"mb-6\"], [1, \"field\", \"col-12\", \"md:col-12\", \"mb-0\", \"pb-0\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"email\", \"placeholder\", \"Enter your registerd email\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", \"mb-4\", \"h-3rem\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-text\", \"hint\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\"], [\"optionLabel\", \"name\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"showClear\", \"styleClass\"], [\"type\", \"text\", \"id\", \"username\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", \"h-3rem\"], [1, \"flex\", \"justify-content-between\", \"gap-3\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3rem\", \"justify-content-center\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3rem\", \"justify-content-center\", 3, \"click\"], [1, \"invalid-feedback\"], [4, \"ngIf\"]],\n        template: function ForgotPasswordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dialog\", 0);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function ForgotPasswordComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onHide\", function ForgotPasswordComponent_Template_p_dialog_onHide_0_listener() {\n              return ctx.onDialogHide();\n            });\n            i0.ɵɵelementStart(1, \"form\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n            i0.ɵɵtext(5, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"input\", 5);\n            i0.ɵɵtemplate(7, ForgotPasswordComponent_div_7_Template, 3, 2, \"div\", 6);\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9, \"We will send reset instructions on your registered email\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 8);\n            i0.ɵɵtext(12, \"Security Question 1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p-dropdown\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ForgotPasswordComponent_Template_p_dropdown_ngModelChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 8);\n            i0.ɵɵtext(16, \"Answer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"input\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 3)(19, \"label\", 8);\n            i0.ɵɵtext(20, \"Security Question 2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p-dropdown\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ForgotPasswordComponent_Template_p_dropdown_ngModelChange_21_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 3)(23, \"label\", 8);\n            i0.ɵɵtext(24, \"Answer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 11)(27, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function ForgotPasswordComponent_Template_button_click_27_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(28, \"Reset Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function ForgotPasswordComponent_Template_button_click_29_listener() {\n              return ctx.visible = false;\n            });\n            i0.ɵɵtext(30, \" Cancel \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"modal\", true)(\"closable\", true)(\"resizable\", false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.form);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ctx.submitted && ctx.f[\"email\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.cities);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n            i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"options\", ctx.cities);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n            i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", !!ctx.form.invalid || ctx.saving);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i4.Dialog, i5.Dropdown]\n      });\n    }\n  }\n  return ForgotPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}