{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Sindhi [sd]\n//! author : <PERSON><PERSON> : https://github.com/narainsagar\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['جنوري', 'فيبروري', 'مارچ', 'اپريل', 'مئي', 'جون', 'جولاءِ', 'آگسٽ', 'سيپٽمبر', 'آڪٽوبر', 'نومبر', 'ڊسمبر'],\n    days = ['آچر', 'سومر', 'اڱارو', 'اربع', 'خميس', 'جمع', 'ڇنڇر'];\n  var sd = moment.defineLocale('sd', {\n    months: months,\n    monthsShort: months,\n    weekdays: days,\n    weekdaysShort: days,\n    weekdaysMin: days,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd، D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /صبح|شام/,\n    isPM: function (input) {\n      return 'شام' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'صبح';\n      }\n      return 'شام';\n    },\n    calendar: {\n      sameDay: '[اڄ] LT',\n      nextDay: '[سڀاڻي] LT',\n      nextWeek: 'dddd [اڳين هفتي تي] LT',\n      lastDay: '[ڪالهه] LT',\n      lastWeek: '[گزريل هفتي] dddd [تي] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s پوء',\n      past: '%s اڳ',\n      s: 'چند سيڪنڊ',\n      ss: '%d سيڪنڊ',\n      m: 'هڪ منٽ',\n      mm: '%d منٽ',\n      h: 'هڪ ڪلاڪ',\n      hh: '%d ڪلاڪ',\n      d: 'هڪ ڏينهن',\n      dd: '%d ڏينهن',\n      M: 'هڪ مهينو',\n      MM: '%d مهينا',\n      y: 'هڪ سال',\n      yy: '%d سال'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sd;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "days", "sd", "defineLocale", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "postformat", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/sd.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Sindhi [sd]\n//! author : <PERSON><PERSON> : https://github.com/narainsagar\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            'جنوري',\n            'فيبروري',\n            'مارچ',\n            'اپريل',\n            'مئي',\n            'جون',\n            'جولاءِ',\n            'آگسٽ',\n            'سيپٽمبر',\n            'آڪٽوبر',\n            'نومبر',\n            'ڊسمبر',\n        ],\n        days = ['آچر', 'سومر', 'اڱارو', 'اربع', 'خميس', 'جمع', 'ڇنڇر'];\n\n    var sd = moment.defineLocale('sd', {\n        months: months,\n        monthsShort: months,\n        weekdays: days,\n        weekdaysShort: days,\n        weekdaysMin: days,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd، D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /صبح|شام/,\n        isPM: function (input) {\n            return 'شام' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'صبح';\n            }\n            return 'شام';\n        },\n        calendar: {\n            sameDay: '[اڄ] LT',\n            nextDay: '[سڀاڻي] LT',\n            nextWeek: 'dddd [اڳين هفتي تي] LT',\n            lastDay: '[ڪالهه] LT',\n            lastWeek: '[گزريل هفتي] dddd [تي] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s پوء',\n            past: '%s اڳ',\n            s: 'چند سيڪنڊ',\n            ss: '%d سيڪنڊ',\n            m: 'هڪ منٽ',\n            mm: '%d منٽ',\n            h: 'هڪ ڪلاڪ',\n            hh: '%d ڪلاڪ',\n            d: 'هڪ ڏينهن',\n            dd: '%d ڏينهن',\n            M: 'هڪ مهينو',\n            MM: '%d مهينا',\n            y: 'هڪ سال',\n            yy: '%d سال',\n        },\n        preparse: function (string) {\n            return string.replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string.replace(/,/g, '،');\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return sd;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAG,CACL,OAAO,EACP,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,CACV;IACDC,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EAElE,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BH,MAAM,EAAEA,MAAM;IACdI,WAAW,EAAEJ,MAAM;IACnBK,QAAQ,EAAEJ,IAAI;IACdK,aAAa,EAAEL,IAAI;IACnBM,WAAW,EAAEN,IAAI;IACjBO,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,SAAS;IACxBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,KAAK,KAAKA,KAAK;IAC1B,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,KAAK;MAChB;MACA,OAAO,KAAK;IAChB,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,wBAAwB;MAClCC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUF,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOhD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}