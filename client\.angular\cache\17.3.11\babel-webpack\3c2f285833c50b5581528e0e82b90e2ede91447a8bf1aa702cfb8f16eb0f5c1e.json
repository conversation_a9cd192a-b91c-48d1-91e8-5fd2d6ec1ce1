{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./sales-orders.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/paginator\";\nimport * as i13 from \"primeng/progressspinner\";\nimport * as i14 from \"primeng/multiselect\";\nconst _c0 = a0 => [a0];\nfunction SalesOrdersComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 39);\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 39);\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 40);\n    i0.ɵɵlistener(\"click\", function SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_i_4_Template, 1, 1, \"i\", 35)(5, SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_i_5_Template, 1, 0, \"i\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 32);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 33);\n    i0.ɵɵlistener(\"click\", function SalesOrdersComponent_p_table_59_ng_template_1_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"SD_DOC\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵtext(5, \" Order # \");\n    i0.ɵɵtemplate(6, SalesOrdersComponent_p_table_59_ng_template_1_i_6_Template, 1, 1, \"i\", 35)(7, SalesOrdersComponent_p_table_59_ng_template_1_i_7_Template, 1, 0, \"i\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SalesOrdersComponent_p_table_59_ng_template_1_ng_container_8_Template, 6, 4, \"ng-container\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.PURCH_NO, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_DATE, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.DOC_STATUS, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.TOTAL_NET_AMOUNT, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.TXN_CURRENCY, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.CHANNEL, \" \");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 45);\n    i0.ɵɵtemplate(3, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 46)(4, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 46)(5, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 46)(6, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 46)(7, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_7_Template, 2, 1, \"ng-container\", 46)(8, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 46);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"PURCH_NO\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_DATE\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_STATUS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"TOTAL_NET_AMOUNT\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"TXN_CURRENCY\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"CHANNEL\");\n  }\n}\nfunction SalesOrdersComponent_p_table_59_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"td\", 42);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SalesOrdersComponent_p_table_59_ng_template_2_ng_container_5_Template, 9, 7, \"ng-container\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", tableinfo_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, \"/store/sales-orders/\" + tableinfo_r6.SD_DOC));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.SD_DOC, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction SalesOrdersComponent_p_table_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 29);\n    i0.ɵɵlistener(\"onColReorder\", function SalesOrdersComponent_p_table_59_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(1, SalesOrdersComponent_p_table_59_ng_template_1_Template, 9, 3, \"ng-template\", 30)(2, SalesOrdersComponent_p_table_59_ng_template_2_Template, 6, 6, \"ng-template\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.tableData)(\"rows\", 14)(\"totalRecords\", ctx_r1.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n  }\n}\nfunction SalesOrdersComponent_p_paginator_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 47);\n    i0.ɵɵlistener(\"onPageChange\", function SalesOrdersComponent_p_paginator_60_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"first\", ctx_r1.first)(\"rows\", ctx_r1.rows)(\"totalRecords\", ctx_r1.totalRecords);\n  }\n}\nexport let SalesOrdersComponent = /*#__PURE__*/(() => {\n  class SalesOrdersComponent {\n    constructor(fb, salesOrdersService) {\n      this.fb = fb;\n      this.salesOrdersService = salesOrdersService;\n      this.unsubscribe$ = new Subject();\n      this.items = [];\n      this.home = {};\n      this.allData = [];\n      this.tableData = [];\n      this.totalRecords = 1000;\n      this.loading = false;\n      this.first = 0;\n      this.rows = 10;\n      this.channels = ['Web Order', 'S4 Order'];\n      this.orderStatuses = [];\n      this.orderStatusesValue = {};\n      this.orderValue = {};\n      this.orderType = '';\n      this.currentPage = 1;\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'PURCH_NO',\n        header: 'P.O. #'\n      }, {\n        field: 'DOC_DATE',\n        header: 'Date Placed'\n      }, {\n        field: 'DOC_STATUS',\n        header: 'Order Status'\n      }, {\n        field: 'TOTAL_NET_AMOUNT',\n        header: 'Net Amount'\n      }, {\n        field: 'TXN_CURRENCY',\n        header: 'Currency'\n      }, {\n        field: 'CHANNEL',\n        header: 'Channel'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n      this.filterForm = this.fb.group({\n        dateFrom: [''],\n        dateTo: [''],\n        purchaseOrder: [''],\n        order: [''],\n        orderStatuses: ['All'],\n        channel: ['']\n      });\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.tableData.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.items = [{\n        label: 'Sales Orders',\n        routerLink: ['/store/sales-orders']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.loading = true;\n      this.salesOrdersService.fetchOrderStatuses({\n        'filters[type][$eq]': 'ORDER_STATUS'\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.data.length) {\n            this.orderStatuses = response?.data.map(val => {\n              this.orderStatusesValue[val.description] = val.code;\n              this.orderValue[val.code] = val.description;\n              return val.description;\n            });\n            this.orderStatuses = ['All', ...this.orderStatuses];\n          }\n        },\n        error: error => {\n          this.loading = false;\n          console.error('Error fetching avatars:', error);\n        }\n      });\n      this.salesOrdersService.fetchOrderStatuses({\n        'filters[type][$eq]': 'ORDER_TYPE'\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.data.length) {\n            this.orderType = response?.data.map(val => {\n              return val.code;\n            }).join(';');\n          }\n          this.onPageChange({\n            first: this.first,\n            rows: this.rows\n          });\n        },\n        error: error => {\n          this.loading = false;\n          this.onPageChange({\n            first: this.first,\n            rows: this.rows\n          });\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    fetchOrders(count) {\n      this.loading = true;\n      const filterValues = this.filterForm.value;\n      const rawParams = {\n        // SOLDTO: this.sellerDetails.customer_id,\n        //SOLDTO: '00830VGB',\n        //VKORG: 1000,\n        COUNT: count,\n        SD_DOC: filterValues.order,\n        DOCUMENT_DATE: filterValues.dateFrom ? new Date(filterValues.dateFrom).toISOString().slice(0, 10) : '',\n        DOCUMENT_DATE_TO: filterValues.dateTo ? new Date(filterValues.dateTo).toISOString().slice(0, 10) : '',\n        DOC_STATUS: this.orderStatusesValue[filterValues.orderStatuses] ? this.orderStatusesValue[filterValues.orderStatuses] : 'A;C;B',\n        PURCHASE_ORDER: filterValues.purchaseOrder,\n        CHANNEL: filterValues.channel,\n        //DOC_TYPE: this.orderType,\n        DOC_TYPE: 'OR'\n      };\n      // Remove empty or undefined values from params\n      const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n      this.salesOrdersService.fetchOrders(params).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.resultData && response.resultData.length > 0) {\n            this.tableData = response.resultData.map(record => ({\n              PURCH_NO: record?.PURCH_NO || '-',\n              SD_DOC: record?.SD_DOC || '-',\n              CHANNEL: record?.CHANNEL || '-',\n              DOC_TYPE: record?.DOC_TYPE || '-',\n              DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n              TXN_CURRENCY: record?.TXN_CURRENCY || '-',\n              DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-',\n              TOTAL_NET_AMOUNT: record?.TOTAL_NET_AMOUNT || '-'\n            }));\n            console.log('this.tableData ', this.tableData);\n            const newRecords = response.resultData.length;\n            const totalFetched = this.allData.length + newRecords;\n            const skipCount = totalFetched - newRecords;\n            this.allData.push(...this.tableData.slice(skipCount));\n            this.totalRecords = this.allData.length;\n            this.paginateData();\n          } else {\n            this.allData = [];\n            this.totalRecords = 0;\n            this.paginateData();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching avatars:', error);\n          this.loading = false;\n        }\n      });\n    }\n    onPageChange(event) {\n      this.first = event.first;\n      this.rows = event.rows;\n      this.currentPage = this.first / this.rows + 1;\n      if (this.first + this.rows >= this.allData.length) {\n        this.fetchOrders(this.allData.length + 1000);\n      }\n      this.paginateData();\n    }\n    paginateData() {\n      this.tableData = this.allData.slice(this.first, this.first + this.rows);\n    }\n    onSearch() {\n      this.allData = [];\n      this.totalRecords = 1000;\n      this.fetchOrders(this.totalRecords);\n    }\n    onClear() {\n      this.allData = [];\n      this.totalRecords = 1000;\n      this.filterForm.reset({\n        dateFrom: '',\n        dateTo: '',\n        purchaseOrder: '',\n        order: '',\n        orderStatuses: '',\n        channel: ''\n      });\n      this.fetchOrders(this.totalRecords);\n    }\n    createOrder() {\n      const url = `${environment.crms4Endpoint}/ui#SalesOrder-manageV2`;\n      window.open(url, '_blank');\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesOrdersComponent_Factory(t) {\n        return new (t || SalesOrdersComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SalesOrdersService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesOrdersComponent,\n        selectors: [[\"app-sales-orders\"]],\n        decls: 61,\n        vars: 16,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\", 3, \"ngSubmit\", \"formGroup\"], [1, \"filter-sec\", \"grid\", \"mt-0\", \"mb-5\"], [1, \"col-12\", \"lg:col-2\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"formControlName\", \"dateFrom\", \"placeholder\", \"Date From\", \"styleClass\", \"h-3rem w-full\", 1, \"w-full\", 3, \"showIcon\"], [\"formControlName\", \"dateTo\", \"placeholder\", \"Date To\", \"styleClass\", \"h-3rem w-full\", 3, \"showIcon\"], [1, \"input-main\", \"w-100\"], [\"pInputText\", \"\", \"formControlName\", \"purchaseOrder\", \"placeholder\", \"Purchase Order #\", 1, \"p-inputtext\", \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"formControlName\", \"order\", \"placeholder\", \"Order #\", 1, \"p-inputtext\", \"h-3rem\", \"w-full\"], [\"formControlName\", \"orderStatuses\", \"placeholder\", \"Order Status\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"channel\", \"placeholder\", \"Channel\", 3, \"options\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Clear\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Search\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\", \"onColReorder\", 4, \"ngIf\"], [3, \"first\", \"rows\", \"totalRecords\", \"onPageChange\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"onPageChange\", \"first\", \"rows\", \"totalRecords\"]],\n        template: function SalesOrdersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function SalesOrdersComponent_Template_button_click_5_listener() {\n              return ctx.createOrder();\n            });\n            i0.ɵɵelementStart(6, \"span\", 6);\n            i0.ɵɵtext(7, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(8, \" Create \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p-multiSelect\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesOrdersComponent_Template_p_multiSelect_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function SalesOrdersComponent_Template_form_ngSubmit_10_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"label\", 12)(15, \"span\", 13);\n            i0.ɵɵtext(16, \"calendar_month\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(17, \" Date From \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(18, \"p-calendar\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11)(21, \"label\", 12)(22, \"span\", 13);\n            i0.ɵɵtext(23, \"calendar_month\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(24, \" Date To \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"p-calendar\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 10)(27, \"div\", 16)(28, \"label\", 12)(29, \"span\", 13);\n            i0.ɵɵtext(30, \"list_alt\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31, \" Purchase Order # \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(32, \"input\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 10)(34, \"div\", 11)(35, \"label\", 12)(36, \"span\", 13);\n            i0.ɵɵtext(37, \"orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(38, \"Order # \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(39, \"input\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 10)(41, \"div\", 11)(42, \"label\", 12)(43, \"span\", 13);\n            i0.ɵɵtext(44, \"order_approve\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(45, \"Order Status \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"p-dropdown\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 10)(48, \"div\", 11)(49, \"label\", 12)(50, \"span\", 13);\n            i0.ɵɵtext(51, \"featured_play_list\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(52, \" Channel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(53, \"p-dropdown\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 21)(55, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SalesOrdersComponent_Template_button_click_55_listener() {\n              return ctx.onClear();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(56, \"button\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"div\", 24);\n            i0.ɵɵtemplate(58, SalesOrdersComponent_div_58_Template, 2, 0, \"div\", 25)(59, SalesOrdersComponent_p_table_59_Template, 3, 6, \"p-table\", 26)(60, SalesOrdersComponent_p_paginator_60_Template, 1, 3, \"p-paginator\", 27);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"showIcon\", true);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"showIcon\", true);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"options\", ctx.orderStatuses)(\"styleClass\", \"h-3rem w-full flex align-items-center\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.channels)(\"styleClass\", \"h-3rem w-full flex align-items-center\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i4.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i7.ButtonDirective, i8.Dropdown, i9.Breadcrumb, i10.Calendar, i11.InputText, i12.Paginator, i1.FormGroupDirective, i1.FormControlName, i13.ProgressSpinner, i14.MultiSelect],\n        styles: [\".filter-sec[_ngcontent-%COMP%]{width:100%!important}.filter-sec[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%]{width:100%}.filter-sec[_ngcontent-%COMP%]   .input-main[_ngcontent-%COMP%]   p-dropdown[_ngcontent-%COMP%]{width:100%}.customer-info[_ngcontent-%COMP%]{border:1px solid #ccc;background-color:#e7ecf2;padding:15px;display:flex;margin-bottom:30px;justify-content:space-between;gap:18px!important;border-radius:15px;box-shadow:5px 5px 10px #0003}.form-info[_ngcontent-%COMP%]{border:1px solid #ccc;margin-bottom:50px;padding:20px 10px 10px;border-radius:15px;box-shadow:5px 5px 10px #0003}\"]\n      });\n    }\n  }\n  return SalesOrdersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}