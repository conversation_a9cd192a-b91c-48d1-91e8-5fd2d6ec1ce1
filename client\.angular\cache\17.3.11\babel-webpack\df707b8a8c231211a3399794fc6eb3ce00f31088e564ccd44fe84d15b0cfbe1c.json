{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { AppLayoutModule as StoreAppLayoutModule } from './store/layout/app.layout.module';\nimport { AppLayoutModule as BackofficeAppLayoutModule } from './backoffice/layout/app.layout.module';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AuthInterceptor } from './core/authentication/auth.intreceptor';\nimport { appInitializerProviders } from './core/bootstrap/initializers';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(t) {\n        return new (t || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [{\n          provide: HTTP_INTERCEPTORS,\n          useClass: AuthInterceptor,\n          multi: true\n        }, appInitializerProviders, MessageService],\n        imports: [BrowserModule, AppRoutingModule, StoreAppLayoutModule, BackofficeAppLayoutModule]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}