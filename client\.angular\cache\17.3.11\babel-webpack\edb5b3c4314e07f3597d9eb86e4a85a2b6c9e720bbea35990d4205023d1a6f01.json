{"ast": null, "code": "import { Subject, takeUntil, fork<PERSON>oin } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"src/app/store/services/setting.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/progressspinner\";\nimport * as i10 from \"primeng/multiselect\";\nfunction AccountSalesQuotesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 22);\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 22);\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 23);\n    i0.ɵɵlistener(\"click\", function AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_i_4_Template, 1, 1, \"i\", 18)(5, AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_i_5_Template, 1, 0, \"i\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r5.field);\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 16);\n    i0.ɵɵlistener(\"click\", function AccountSalesQuotesComponent_p_table_9_ng_template_2_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(\"SD_DOC\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵtext(3, \" Quote # \");\n    i0.ɵɵtemplate(4, AccountSalesQuotesComponent_p_table_9_ng_template_2_i_4_Template, 1, 1, \"i\", 18)(5, AccountSalesQuotesComponent_p_table_9_ng_template_2_i_5_Template, 1, 0, \"i\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountSalesQuotesComponent_p_table_9_ng_template_2_ng_container_6_Template, 6, 4, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"SD_DOC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quote_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", quote_r7 == null ? null : quote_r7.DOC_NAME, \" \");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quote_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", quote_r7 == null ? null : quote_r7.DOC_STATUS, \" \");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quote_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, quote_r7 == null ? null : quote_r7.DOC_DATE, \"MM/dd/yyyy\"), \" \");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 26);\n    i0.ɵɵtemplate(3, AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_3_Template, 2, 1, \"ng-container\", 27)(4, AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_4_Template, 2, 1, \"ng-container\", 27)(5, AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_ng_container_5_Template, 3, 4, \"ng-container\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_NAME\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_STATUS\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DOC_DATE\");\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24);\n    i0.ɵɵlistener(\"click\", function AccountSalesQuotesComponent_p_table_9_ng_template_3_Template_tr_click_0_listener() {\n      const quote_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToQuoteDetail(quote_r7));\n    });\n    i0.ɵɵelementStart(1, \"td\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSalesQuotesComponent_p_table_9_ng_template_3_ng_container_3_Template, 6, 4, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quote_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", quote_r7 == null ? null : quote_r7.SD_DOC, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"No quotes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"Loading quotes data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuotesComponent_p_table_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 11, 0);\n    i0.ɵɵlistener(\"onColReorder\", function AccountSalesQuotesComponent_p_table_9_Template_p_table_onColReorder_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColumnReorder($event));\n    });\n    i0.ɵɵtemplate(2, AccountSalesQuotesComponent_p_table_9_ng_template_2_Template, 7, 3, \"ng-template\", 12)(3, AccountSalesQuotesComponent_p_table_9_ng_template_3_Template, 4, 2, \"ng-template\", 13)(4, AccountSalesQuotesComponent_p_table_9_ng_template_4_Template, 3, 0, \"ng-template\", 14)(5, AccountSalesQuotesComponent_p_table_9_ng_template_5_Template, 3, 0, \"ng-template\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.QuoteData)(\"rows\", 8)(\"rowHover\", true)(\"paginator\", true)(\"totalRecords\", ctx_r1.totalRecords)(\"reorderableColumns\", true);\n  }\n}\nexport let AccountSalesQuotesComponent = /*#__PURE__*/(() => {\n  class AccountSalesQuotesComponent {\n    constructor(accountservice, settingsservice, router, route) {\n      this.accountservice = accountservice;\n      this.settingsservice = settingsservice;\n      this.router = router;\n      this.route = route;\n      this.unsubscribe$ = new Subject();\n      this.totalRecords = 0;\n      this.loading = true;\n      this.allData = [];\n      this.QuoteData = [];\n      this.first = 0;\n      this.rows = 10;\n      this.currentPage = 1;\n      this.orderStatusesValue = {};\n      this.statuses = [];\n      this.orderValue = {};\n      this.orderType = '';\n      this.QuoteStatus = ['All'];\n      this.customer = {};\n      this.quoteDetail = null;\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'DOC_NAME',\n        header: 'Quote Name'\n      }, {\n        field: 'DOC_STATUS',\n        header: 'Quote Status'\n      }, {\n        field: 'DOC_DATE',\n        header: 'Date Placed'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.QuoteData.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.loadInitialData(response.customer.customer_id);\n        }\n      });\n      this.accountservice.fetchOrderStatuses({\n        'filters[type][$eq]': 'QUOTE_STATUS'\n      }).subscribe({\n        next: response => {\n          if (response?.data.length) {\n            this.QuoteStatus = response?.data.map(val => {\n              this.orderStatusesValue[val.description] = val.code;\n              this.orderValue[val.code] = val.description;\n              this.orderStatusesValue['All'] = this.orderStatusesValue['All'] ? `${this.orderStatusesValue['All']};${val.code}` : val.code;\n              return val.description;\n            });\n            this.QuoteStatus = ['All', ...this.QuoteStatus];\n          }\n        },\n        error: error => {\n          console.error('Error fetching order statuses:', error);\n        }\n      });\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadInitialData(soldToParty) {\n      forkJoin({\n        partnerFunction: this.accountservice.getPartnerFunction(soldToParty),\n        settings: this.settingsservice.getSettings()\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: ({\n          partnerFunction,\n          settings\n        }) => {\n          this.customer = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n          this.orderType = settings?.[0]?.sales_quote_type_code || '';\n          if (this.customer) {\n            this.fetchQuotes(1000);\n          }\n        },\n        error: error => {\n          console.error('Error fetching initial data:', error);\n        }\n      });\n    }\n    fetchQuotes(count) {\n      this.loading = true;\n      const rawParams = {\n        SOLDTO: this.customer?.customer_id,\n        VKORG: this.customer?.sales_organization,\n        COUNT: count,\n        DOC_STATUS: Object.keys(this.orderValue).join(';'),\n        DOC_TYPE: this.orderType\n      };\n      const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n      this.accountservice.fetchSalesquoteOrders(params).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response?.SALESQUOTES) {\n            this.QuoteData = response.SALESQUOTES.map(record => ({\n              SD_DOC: record?.SD_DOC || '-',\n              DOC_NAME: record?.DOC_NAME || '-',\n              DOC_TYPE: record?.DOC_TYPE || '-',\n              DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n              DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-'\n            }));\n            this.allData = [...this.QuoteData];\n            this.totalRecords = this.allData.length;\n            this.paginateData();\n          } else {\n            this.allData = [];\n            this.totalRecords = 0;\n            this.paginateData();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching sales quotes:', error);\n          this.loading = false;\n        }\n      });\n    }\n    onPageChange(event) {\n      this.first = event.first;\n      this.rows = event.rows;\n      this.currentPage = this.first / this.rows + 1;\n      if (this.first + this.rows >= this.allData.length && this.allData.length % 100 == 0) {\n        this.fetchQuotes(this.allData.length + 1000);\n      }\n      this.paginateData();\n    }\n    paginateData() {\n      this.QuoteData = this.allData.slice(this.first, this.first + this.rows);\n    }\n    navigateToQuoteDetail(quote) {\n      this.router.navigate([quote.SD_DOC], {\n        relativeTo: this.route,\n        state: {\n          quoteData: quote,\n          customerData: this.customer?.customer\n        }\n      });\n    }\n    createQuote() {\n      const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2`;\n      window.open(url, '_blank');\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountSalesQuotesComponent_Factory(t) {\n        return new (t || AccountSalesQuotesComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.SettingsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountSalesQuotesComponent,\n        selectors: [[\"app-account-sales-quotes\"]],\n        decls: 10,\n        vars: 7,\n        consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Create\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", \"class\", \"scrollable-table\", 3, \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\", \"reorderableColumns\", \"onColReorder\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\", 3, \"click\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"5\", 1, \"border-round-left-lg\"]],\n        template: function AccountSalesQuotesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n            i0.ɵɵtext(3, \"Sales Quotes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"p-button\", 5);\n            i0.ɵɵlistener(\"click\", function AccountSalesQuotesComponent_Template_p_button_click_5_listener() {\n              return ctx.createQuote();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p-multiSelect\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSalesQuotesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 7);\n            i0.ɵɵtemplate(8, AccountSalesQuotesComponent_div_8_Template, 2, 0, \"div\", 8)(9, AccountSalesQuotesComponent_p_table_9_Template, 6, 6, \"p-table\", 9);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.PrimeTemplate, i6.Table, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.NgControlStatus, i7.NgModel, i8.Button, i9.ProgressSpinner, i10.MultiSelect, i4.DatePipe],\n        styles: [\".p-sidebar-header{display:none!important}\"]\n      });\n    }\n  }\n  return AccountSalesQuotesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}