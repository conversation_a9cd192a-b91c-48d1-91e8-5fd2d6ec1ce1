{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/breadcrumb\";\nconst _c0 = () => [\"/auth/signup\"];\nfunction AiInsightsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 15);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"External ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Delivery\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Requested Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 16);\n    i0.ɵɵtext(18, \"Expected Value\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AiInsightsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", tableinfo_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/ai-insights/overview\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ExternalID, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Account, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Description, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.Delivery, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.RequestedDate, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r1.ExpectedValue, \" \");\n  }\n}\nexport let AiInsightsComponent = /*#__PURE__*/(() => {\n  class AiInsightsComponent {\n    constructor() {\n      this.tableData = [];\n    }\n    ngOnInit() {\n      this.items = [{\n        label: 'AI Insights',\n        routerLink: ['/store/ai-insights']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.AdminM = [{\n        name: 'ALL(0)',\n        code: 'all'\n      }, {\n        name: 'My Orders (20)',\n        code: 'my-orders'\n      }, {\n        name: 'My Teams Orders (20)',\n        code: 'team-o'\n      }, {\n        name: 'Orders My Territories (20)',\n        code: 'territories-o'\n      }];\n      this.tableData = [{\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }, {\n        Id: '052',\n        ExternalID: 'BG1001',\n        Account: 'Hilton',\n        Description: 'Identity opp...',\n        Status: 'OPEN',\n        Delivery: 'Adam Smith',\n        RequestedDate: '04/11/2024',\n        ExpectedValue: '55000.00 USD'\n      }];\n    }\n    static {\n      this.ɵfac = function AiInsightsComponent_Factory(t) {\n        return new (t || AiInsightsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AiInsightsComponent,\n        selectors: [[\"app-ai-insights\"]],\n        decls: 17,\n        vars: 8,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [1, \"border-round-right-lg\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"]],\n        template: function AiInsightsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"span\", 6);\n            i0.ɵɵelement(7, \"input\", 7)(8, \"i\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"button\", 9)(10, \"span\", 10);\n            i0.ɵɵtext(11, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(12, \" Create \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"p-table\", 12);\n            i0.ɵɵtemplate(15, AiInsightsComponent_ng_template_15_Template, 19, 0, \"ng-template\", 13)(16, AiInsightsComponent_ng_template_16_Template, 19, 10, \"ng-template\", 14);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c0));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"value\", ctx.tableData)(\"rows\", 14)(\"paginator\", true);\n          }\n        },\n        dependencies: [i1.RouterLink, i2.Table, i3.PrimeTemplate, i2.TableCheckbox, i2.TableHeaderCheckbox, i4.Breadcrumb]\n      });\n    }\n  }\n  return AiInsightsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}