{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { animate, state, style, transition, trigger } from '@angular/animations';\nimport { filter } from 'rxjs/operators';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./app.sidebar.component\";\nimport * as i4 from \"./app.menu.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/tooltip\";\nimport * as i7 from \"primeng/ripple\";\nconst _c0 = [\"submenu\"];\nconst _c1 = [\"app-menuitem\", \"\"];\nconst _c2 = () => ({\n  paths: \"exact\",\n  queryParams: \"ignored\",\n  matrixParams: \"ignored\",\n  fragment: \"ignored\"\n});\nfunction AppMenuitemComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n  }\n}\nfunction AppMenuitemComponent_a_2_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n}\nfunction AppMenuitemComponent_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.itemClick($event));\n    })(\"mouseenter\", function AppMenuitemComponent_a_2_Template_a_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseEnter());\n    });\n    i0.ɵɵelementStart(1, \"i\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AppMenuitemComponent_a_2_i_5_Template, 1, 0, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.item.class)(\"pTooltip\", ctx_r0.item.label)(\"tooltipDisabled\", !(ctx_r0.isSlim && ctx_r0.root && !ctx_r0.active));\n    i0.ɵɵattribute(\"href\", ctx_r0.item.url, i0.ɵɵsanitizeUrl)(\"target\", ctx_r0.item.target);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.items);\n  }\n}\nfunction AppMenuitemComponent_a_3_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n}\nfunction AppMenuitemComponent_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.itemClick($event));\n    })(\"mouseenter\", function AppMenuitemComponent_a_3_Template_a_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onMouseEnter());\n    });\n    i0.ɵɵelementStart(1, \"i\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AppMenuitemComponent_a_3_i_5_Template, 1, 0, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.item.class)(\"routerLink\", ctx_r0.item.routerLink)(\"routerLinkActiveOptions\", ctx_r0.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c2))(\"fragment\", ctx_r0.item.fragment)(\"queryParamsHandling\", ctx_r0.item.queryParamsHandling)(\"preserveFragment\", ctx_r0.item.preserveFragment)(\"skipLocationChange\", ctx_r0.item.skipLocationChange)(\"replaceUrl\", ctx_r0.item.replaceUrl)(\"state\", ctx_r0.item.state)(\"queryParams\", ctx_r0.item.queryParams)(\"pTooltip\", ctx_r0.item.label)(\"tooltipDisabled\", !(ctx_r0.isSlim && ctx_r0.root));\n    i0.ɵɵattribute(\"target\", ctx_r0.item.target);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.items);\n  }\n}\nfunction AppMenuitemComponent_ul_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const child_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(child_r5.badgeClass);\n    i0.ɵɵproperty(\"item\", child_r5)(\"index\", i_r6)(\"parentKey\", ctx_r0.key);\n  }\n}\nfunction AppMenuitemComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", null, 0);\n    i0.ɵɵlistener(\"@children.done\", function AppMenuitemComponent_ul_4_Template_ul_animation_children_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmenuAnimated($event));\n    });\n    i0.ɵɵtemplate(2, AppMenuitemComponent_ul_4_ng_template_2_Template, 1, 5, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@children\", ctx_r0.submenuAnimation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.item.items);\n  }\n}\nexport class AppMenuitemComponent {\n  constructor(layoutService, cd, router, appSidebar, menuService) {\n    this.layoutService = layoutService;\n    this.cd = cd;\n    this.router = router;\n    this.appSidebar = appSidebar;\n    this.menuService = menuService;\n    this.active = false;\n    this.key = \"\";\n    this.menuSourceSubscription = this.menuService.menuSource$.subscribe(value => {\n      Promise.resolve(null).then(() => {\n        if (value.routeEvent) {\n          this.active = value.key === this.key || value.key.startsWith(this.key + '-') ? true : false;\n        } else {\n          if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {\n            this.active = false;\n          }\n        }\n      });\n    });\n    this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {\n      this.active = false;\n    });\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(params => {\n      if (this.isSlimPlus || this.isSlim || this.isHorizontal) {\n        this.active = false;\n      } else {\n        if (this.item.routerLink) {\n          this.updateActiveStateFromRoute();\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);\n    if (!(this.isSlimPlus || this.isSlim || this.isHorizontal) && this.item.routerLink) {\n      this.updateActiveStateFromRoute();\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.root && this.active && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\n      this.calculatePosition(this.submenu?.nativeElement, this.submenu?.nativeElement.parentElement);\n    }\n  }\n  updateActiveStateFromRoute() {\n    let activeRoute = this.router.isActive(this.item.routerLink[0], {\n      paths: 'exact',\n      queryParams: 'ignored',\n      matrixParams: 'ignored',\n      fragment: 'ignored'\n    });\n    if (activeRoute) {\n      this.menuService.onMenuStateChange({\n        key: this.key,\n        routeEvent: true\n      });\n    }\n  }\n  onSubmenuAnimated(event) {\n    if (event.toState === 'visible' && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\n      const el = event.element;\n      const elParent = el.parentElement;\n      this.calculatePosition(el, elParent);\n    }\n  }\n  calculatePosition(overlay, target) {\n    if (overlay) {\n      const {\n        left,\n        top\n      } = target.getBoundingClientRect();\n      const [vWidth, vHeight] = [window.innerWidth, window.innerHeight];\n      const [oWidth, oHeight] = [overlay.offsetWidth, overlay.offsetHeight];\n      const scrollbarWidth = DomHandler.calculateScrollbarWidth();\n      // reset\n      overlay.style.top = '';\n      overlay.style.left = '';\n      if (this.layoutService.isHorizontal()) {\n        const width = left + oWidth + scrollbarWidth;\n        overlay.style.left = vWidth < width ? `${left - (width - vWidth)}px` : `${left}px`;\n      } else if (this.layoutService.isSlim() || this.layoutService.isSlimPlus()) {\n        const height = top + oHeight;\n        overlay.style.top = vHeight < height ? `${top - (height - vHeight)}px` : `${top}px`;\n        console.log('top', top, 'vHeight', vHeight, 'oHeight', oHeight, 'height', height);\n      }\n    }\n  }\n  itemClick(event) {\n    // avoid processing disabled items\n    if (this.item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    // navigate with hover\n    if (this.root && this.isSlim || this.isHorizontal || this.isSlimPlus) {\n      this.layoutService.state.menuHoverActive = !this.layoutService.state.menuHoverActive;\n    }\n    // execute command\n    if (this.item.command) {\n      this.item.command({\n        originalEvent: event,\n        item: this.item\n      });\n    }\n    // toggle active state\n    if (this.item.items) {\n      this.active = !this.active;\n      if (this.root && this.active && (this.isSlim || this.isHorizontal || this.isSlimPlus)) {\n        this.layoutService.onOverlaySubmenuOpen();\n      }\n    } else {\n      if (this.layoutService.isMobile()) {\n        this.layoutService.state.staticMenuMobileActive = false;\n      }\n      if (this.isSlim || this.isHorizontal || this.isSlimPlus) {\n        this.menuService.reset();\n        this.layoutService.state.menuHoverActive = false;\n      }\n    }\n    this.menuService.onMenuStateChange({\n      key: this.key\n    });\n  }\n  onMouseEnter() {\n    // activate item on hover\n    if (this.root && (this.isSlim || this.isHorizontal || this.isSlimPlus) && this.layoutService.isDesktop()) {\n      if (this.layoutService.state.menuHoverActive) {\n        this.active = true;\n        this.menuService.onMenuStateChange({\n          key: this.key\n        });\n      }\n    }\n  }\n  get submenuAnimation() {\n    if (this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) return this.active ? 'visible' : 'hidden';else return this.root ? 'expanded' : this.active ? 'expanded' : 'collapsed';\n  }\n  get isHorizontal() {\n    return this.layoutService.isHorizontal();\n  }\n  get isSlim() {\n    return this.layoutService.isSlim();\n  }\n  get isSlimPlus() {\n    return this.layoutService.isSlimPlus();\n  }\n  get activeClass() {\n    return this.active && !this.root;\n  }\n  ngOnDestroy() {\n    if (this.menuSourceSubscription) {\n      this.menuSourceSubscription.unsubscribe();\n    }\n    if (this.menuResetSubscription) {\n      this.menuResetSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AppMenuitemComponent_Factory(t) {\n      return new (t || AppMenuitemComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AppSidebarComponent), i0.ɵɵdirectiveInject(i4.MenuService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppMenuitemComponent,\n      selectors: [[\"\", \"app-menuitem\", \"\"]],\n      viewQuery: function AppMenuitemComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenu = _t.first);\n        }\n      },\n      hostVars: 4,\n      hostBindings: function AppMenuitemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"layout-root-menuitem\", ctx.root)(\"active-menuitem\", ctx.activeClass);\n        }\n      },\n      inputs: {\n        item: \"item\",\n        index: \"index\",\n        root: \"root\",\n        parentKey: \"parentKey\"\n      },\n      attrs: _c1,\n      decls: 5,\n      vars: 4,\n      consts: [[\"submenu\", \"\"], [\"class\", \"layout-menuitem-root-text\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"pTooltip\", \"tooltipDisabled\", \"click\", \"mouseenter\", 4, \"ngIf\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"pTooltip\", \"tooltipDisabled\", \"click\", \"mouseenter\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"layout-menuitem-root-text\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"click\", \"mouseenter\", \"ngClass\", \"pTooltip\", \"tooltipDisabled\"], [1, \"material-symbols-rounded\", \"layout-menuitem-icon\", \"text-2xl\", \"text-orange-700\"], [1, \"layout-menuitem-text\"], [\"class\", \"pi pi-fw pi-angle-down layout-submenu-toggler\", 4, \"ngIf\"], [1, \"pi\", \"pi-fw\", \"pi-angle-down\", \"layout-submenu-toggler\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"click\", \"mouseenter\", \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"pTooltip\", \"tooltipDisabled\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"parentKey\"]],\n      template: function AppMenuitemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0);\n          i0.ɵɵtemplate(1, AppMenuitemComponent_div_1_Template, 2, 1, \"div\", 1)(2, AppMenuitemComponent_a_2_Template, 6, 8, \"a\", 2)(3, AppMenuitemComponent_a_3_Template, 6, 17, \"a\", 3)(4, AppMenuitemComponent_ul_4_Template, 3, 2, \"ul\", 4);\n          i0.ɵɵelementContainerEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.root && ctx.item.visible !== false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (!ctx.item.routerLink || ctx.item.items) && ctx.item.visible !== false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.item.routerLink && !ctx.item.items && ctx.item.visible !== false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.item.items && ctx.item.visible !== false);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.Tooltip, i7.Ripple, i2.RouterLink, i2.RouterLinkActive, AppMenuitemComponent],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('children', [state('collapsed', style({\n          height: '0'\n        })), state('expanded', style({\n          height: '*'\n        })), state('hidden', style({\n          display: 'none'\n        })), state('visible', style({\n          display: 'block'\n        })), transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)'))])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "animate", "state", "style", "transition", "trigger", "filter", "<PERSON><PERSON><PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "item", "label", "ɵɵelement", "ɵɵlistener", "AppMenuitemComponent_a_2_Template_a_click_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "itemClick", "AppMenuitemComponent_a_2_Template_a_mouseenter_0_listener", "onMouseEnter", "ɵɵtemplate", "AppMenuitemComponent_a_2_i_5_Template", "ɵɵproperty", "class", "isSlim", "root", "active", "icon", "items", "AppMenuitemComponent_a_3_Template_a_click_0_listener", "_r3", "AppMenuitemComponent_a_3_Template_a_mouseenter_0_listener", "AppMenuitemComponent_a_3_i_5_Template", "routerLink", "routerLinkActiveOptions", "ɵɵpureFunction0", "_c2", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "queryParams", "ɵɵclassMap", "child_r5", "badgeClass", "i_r6", "key", "AppMenuitemComponent_ul_4_Template_ul_animation_children_done_0_listener", "_r4", "onSubmenuAnimated", "AppMenuitemComponent_ul_4_ng_template_2_Template", "submenuAnimation", "AppMenuitemComponent", "constructor", "layoutService", "cd", "router", "appSidebar", "menuService", "menuSourceSubscription", "menuSource$", "subscribe", "value", "Promise", "resolve", "then", "routeEvent", "startsWith", "menuResetSubscription", "resetSource$", "events", "pipe", "event", "params", "isSlimPlus", "isHorizontal", "updateActiveStateFromRoute", "ngOnInit", "parent<PERSON><PERSON>", "index", "String", "ngAfterViewChecked", "isDesktop", "calculatePosition", "submenu", "nativeElement", "parentElement", "activeRoute", "isActive", "paths", "matrixParams", "onMenuStateChange", "toState", "el", "element", "<PERSON><PERSON><PERSON><PERSON>", "overlay", "target", "left", "top", "getBoundingClientRect", "vWidth", "vHeight", "window", "innerWidth", "innerHeight", "oWidth", "oHeight", "offsetWidth", "offsetHeight", "scrollbarWidth", "calculateScrollbarWidth", "width", "height", "console", "log", "disabled", "preventDefault", "menuHoverActive", "command", "originalEvent", "onOverlaySubmenuOpen", "isMobile", "staticMenuMobileActive", "reset", "activeClass", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "LayoutService", "ChangeDetectorRef", "i2", "Router", "i3", "AppSidebarComponent", "i4", "MenuService", "selectors", "viewQuery", "AppMenuitemComponent_Query", "rf", "ctx", "ɵɵclassProp", "ɵɵelementContainerStart", "AppMenuitemComponent_div_1_Template", "AppMenuitemComponent_a_2_Template", "AppMenuitemComponent_a_3_Template", "AppMenuitemComponent_ul_4_Template", "visible", "encapsulation", "data", "animation", "display"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\layout\\app.menuitem.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, ElementRef, HostBinding, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { animate, state, style, transition, trigger, AnimationEvent } from '@angular/animations';\r\nimport { Subscription } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\nimport { MenuService } from './app.menu.service';\r\nimport { LayoutService } from './service/app.layout.service';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { DomHandler } from 'primeng/dom';\r\n\r\n\r\n@Component({\r\n    // eslint-disable-next-line @angular-eslint/component-selector\r\n    selector: '[app-menuitem]',\r\n    template: `\r\n    <ng-container>\r\n    <div *ngIf=\"root && item.visible !== false\" class=\"layout-menuitem-root-text\">{{ item.label }}</div>\r\n    <a\r\n        *ngIf=\"(!item.routerLink || item.items) && item.visible !== false\"\r\n        [attr.href]=\"item.url\"\r\n        (click)=\"itemClick($event)\"\r\n        (mouseenter)=\"onMouseEnter()\"\r\n        [ngClass]=\"item.class\"\r\n        [attr.target]=\"item.target\"\r\n        tabindex=\"0\"\r\n        pRipple\r\n        [pTooltip]=\"item.label\"\r\n        [tooltipDisabled]=\"!(isSlim && root && !active)\"\r\n    >\r\n        <i class=\"material-symbols-rounded layout-menuitem-icon text-2xl text-orange-700\">{{ item.icon }}</i>\r\n        <span class=\"layout-menuitem-text\">{{ item.label }}</span>\r\n        <i class=\"pi pi-fw pi-angle-down layout-submenu-toggler\" *ngIf=\"item.items\"></i>\r\n    </a>\r\n    <a\r\n        *ngIf=\"item.routerLink && !item.items && item.visible !== false\"\r\n        (click)=\"itemClick($event)\"\r\n        (mouseenter)=\"onMouseEnter()\"\r\n        [ngClass]=\"item.class\"\r\n        [routerLink]=\"item.routerLink\"\r\n        routerLinkActive=\"active-route\"\r\n        [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { paths: 'exact', queryParams: 'ignored', matrixParams: 'ignored', fragment: 'ignored' }\"\r\n        [fragment]=\"item.fragment\"\r\n        [queryParamsHandling]=\"item.queryParamsHandling\"\r\n        [preserveFragment]=\"item.preserveFragment\"\r\n        [skipLocationChange]=\"item.skipLocationChange\"\r\n        [replaceUrl]=\"item.replaceUrl\"\r\n        [state]=\"item.state\"\r\n        [queryParams]=\"item.queryParams\"\r\n        [attr.target]=\"item.target\"\r\n        tabindex=\"0\"\r\n        pRipple\r\n        [pTooltip]=\"item.label\"\r\n        [tooltipDisabled]=\"!(isSlim && root)\"\r\n    >\r\n        <i class=\"material-symbols-rounded layout-menuitem-icon text-2xl text-orange-700\">{{ item.icon }}</i>\r\n        <span class=\"layout-menuitem-text\">{{ item.label }}</span>\r\n        <i class=\"pi pi-fw pi-angle-down layout-submenu-toggler\" *ngIf=\"item.items\"></i>\r\n    </a>\r\n\r\n    <ul #submenu *ngIf=\"item.items && item.visible !== false\" [@children]=\"submenuAnimation\" (@children.done)=\"onSubmenuAnimated($event)\">\r\n    <ng-template ngFor let-child let-i=\"index\" [ngForOf]=\"item.items\">\r\n        <li app-menuitem [item]=\"child\" [index]=\"i\" [parentKey]=\"key\" [class]=\"child.badgeClass\"></li>\r\n    </ng-template>\r\n</ul>\r\n</ng-container>\r\n    `,\r\n    animations: [\r\n        trigger('children', [\r\n            state('collapsed', style({\r\n                height: '0'\r\n            })),\r\n            state('expanded', style({\r\n                height: '*'\r\n            })),\r\n            state('hidden', style({\r\n                display: 'none'\r\n            })),\r\n            state('visible', style({\r\n                display: 'block'\r\n            })),\r\n            transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)'))\r\n        ])\r\n    ]\r\n})\r\nexport class AppMenuitemComponent implements OnInit, OnDestroy {\r\n\r\n    @Input() item: any;\r\n\r\n    @Input() index!: number;\r\n\r\n    @Input() @HostBinding('class.layout-root-menuitem') root!: boolean;\r\n\r\n    @Input() parentKey!: string;\r\n\r\n    @ViewChild('submenu') submenu!: ElementRef;\r\n\r\n    active = false;\r\n\r\n    menuSourceSubscription: Subscription;\r\n\r\n    menuResetSubscription: Subscription;\r\n\r\n    key: string = \"\";\r\n\r\n    constructor(public layoutService: LayoutService, private cd: ChangeDetectorRef, public router: Router, private appSidebar: AppSidebarComponent, private menuService: MenuService) {\r\n        this.menuSourceSubscription = this.menuService.menuSource$.subscribe(value => {\r\n            Promise.resolve(null).then(() => {\r\n                if (value.routeEvent) {\r\n                    this.active = (value.key === this.key || value.key.startsWith(this.key + '-')) ? true : false;\r\n                }\r\n                else {\r\n                    if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {\r\n                        this.active = false;\r\n                    }\r\n                }\r\n            });\r\n        });\r\n\r\n        this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {\r\n            this.active = false;\r\n        });\r\n\r\n        this.router.events.pipe(filter(event => event instanceof NavigationEnd))\r\n            .subscribe(params => {\r\n                if (this.isSlimPlus || this.isSlim || this.isHorizontal) {\r\n                    this.active = false;\r\n                }\r\n                else {\r\n                    if (this.item.routerLink) {\r\n                        this.updateActiveStateFromRoute();\r\n                    }\r\n                }\r\n            });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);\r\n\r\n        if (!(this.isSlimPlus || this.isSlim || this.isHorizontal) && this.item.routerLink) {\r\n            this.updateActiveStateFromRoute();\r\n        }\r\n    }\r\n\r\n    ngAfterViewChecked() {\r\n        if (this.root && this.active && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\r\n            this.calculatePosition(this.submenu?.nativeElement, this.submenu?.nativeElement.parentElement);\r\n        }\r\n    }\r\n\r\n    updateActiveStateFromRoute() {\r\n        let activeRoute = this.router.isActive(this.item.routerLink[0], { paths: 'exact', queryParams: 'ignored', matrixParams: 'ignored', fragment: 'ignored' });\r\n\r\n        if (activeRoute) {\r\n            this.menuService.onMenuStateChange({ key: this.key, routeEvent: true });\r\n        }\r\n    }\r\n    onSubmenuAnimated(event: AnimationEvent) {\r\n        if (event.toState === 'visible' && this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus())) {\r\n            const el = <HTMLUListElement>event.element;\r\n            const elParent = <HTMLUListElement>el.parentElement;\r\n            this.calculatePosition(el, elParent);\r\n        }\r\n    }\r\n\r\n    calculatePosition(overlay: HTMLElement, target: HTMLElement) {\r\n        if (overlay) {\r\n            const { left, top } = target.getBoundingClientRect();\r\n            const [vWidth, vHeight] = [window.innerWidth, window.innerHeight];\r\n            const [oWidth, oHeight] = [overlay.offsetWidth, overlay.offsetHeight];\r\n            const scrollbarWidth = DomHandler.calculateScrollbarWidth();\r\n            // reset\r\n            overlay.style.top = '';\r\n            overlay.style.left = '';\r\n\r\n            if (this.layoutService.isHorizontal()) {\r\n                const width = left + oWidth + scrollbarWidth;\r\n                overlay.style.left = vWidth < width ? `${left - (width - vWidth)}px` : `${left}px`;\r\n            } else if (this.layoutService.isSlim() || this.layoutService.isSlimPlus()) {\r\n                const height = top + oHeight;\r\n                overlay.style.top = vHeight < height ? `${top - (height - vHeight)}px` : `${top}px`;\r\n                console.log('top', top, 'vHeight', vHeight, 'oHeight', oHeight, 'height', height);\r\n            }\r\n        }\r\n    }\r\n\r\n    itemClick(event: Event) {\r\n        // avoid processing disabled items\r\n        if (this.item.disabled) {\r\n            event.preventDefault();\r\n            return;\r\n        }\r\n\r\n        // navigate with hover\r\n        if (this.root && this.isSlim || this.isHorizontal || this.isSlimPlus) {\r\n            this.layoutService.state.menuHoverActive = !this.layoutService.state.menuHoverActive;\r\n        }\r\n\r\n        // execute command\r\n        if (this.item.command) {\r\n            this.item.command({ originalEvent: event, item: this.item });\r\n        }\r\n\r\n        // toggle active state\r\n        if (this.item.items) {\r\n            this.active = !this.active;\r\n\r\n            if (this.root && this.active && (this.isSlim || this.isHorizontal || this.isSlimPlus)) {\r\n                this.layoutService.onOverlaySubmenuOpen();\r\n            }\r\n        }\r\n        else {\r\n            if (this.layoutService.isMobile()) {\r\n                this.layoutService.state.staticMenuMobileActive = false;\r\n            }\r\n\r\n            if (this.isSlim || this.isHorizontal || this.isSlimPlus) {\r\n                this.menuService.reset();\r\n                this.layoutService.state.menuHoverActive = false;\r\n            }\r\n        }\r\n\r\n        this.menuService.onMenuStateChange({ key: this.key });\r\n    }\r\n\r\n    onMouseEnter() {\r\n        // activate item on hover\r\n        if (this.root && (this.isSlim || this.isHorizontal || this.isSlimPlus) && this.layoutService.isDesktop()) {\r\n            if (this.layoutService.state.menuHoverActive) {\r\n                this.active = true;\r\n                this.menuService.onMenuStateChange({ key: this.key });\r\n            }\r\n        }\r\n    }\r\n\r\n    get submenuAnimation() {\r\n        if (this.layoutService.isDesktop() && (this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus()))\r\n            return this.active ? 'visible' : 'hidden';\r\n        else\r\n            return this.root ? 'expanded' : (this.active ? 'expanded' : 'collapsed');\r\n    }\r\n\r\n    get isHorizontal() {\r\n        return this.layoutService.isHorizontal();\r\n    }\r\n\r\n    get isSlim() {\r\n        return this.layoutService.isSlim();\r\n    }\r\n\r\n    get isSlimPlus() {\r\n        return this.layoutService.isSlimPlus();\r\n    }\r\n\r\n    @HostBinding('class.active-menuitem')\r\n    get activeClass() {\r\n        return this.active && !this.root;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        if (this.menuSourceSubscription) {\r\n            this.menuSourceSubscription.unsubscribe();\r\n        }\r\n\r\n        if (this.menuResetSubscription) {\r\n            this.menuResetSubscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAgB,iBAAiB;AACvD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAwB,qBAAqB;AAEhG,SAASC,MAAM,QAAQ,gBAAgB;AAIvC,SAASC,UAAU,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;IAQpCC,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;;;;;IAe1FR,EAAA,CAAAS,SAAA,YAAgF;;;;;;IAdpFT,EAAA,CAAAC,cAAA,WAWC;IAPGD,EADA,CAAAU,UAAA,mBAAAC,qDAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,SAAA,CAAAL,MAAA,CAAiB;IAAA,EAAC,wBAAAM,0DAAA;MAAAlB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACbV,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAQ7BnB,EAAA,CAAAC,cAAA,WAAkF;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrGH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAoB,UAAA,IAAAC,qCAAA,eAA4E;IAChFrB,EAAA,CAAAG,YAAA,EAAI;;;;IALAH,EALA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAC,IAAA,CAAAgB,KAAA,CAAsB,aAAAjB,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAIC,sBAAAF,MAAA,CAAAkB,MAAA,IAAAlB,MAAA,CAAAmB,IAAA,KAAAnB,MAAA,CAAAoB,MAAA,EACyB;;IAEkC1B,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAoB,IAAA,CAAe;IAC9D3B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;IACOR,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAC,IAAA,CAAAqB,KAAA,CAAgB;;;;;IAyB1E5B,EAAA,CAAAS,SAAA,YAAgF;;;;;;IAvBpFT,EAAA,CAAAC,cAAA,YAoBC;IAjBGD,EADA,CAAAU,UAAA,mBAAAmB,qDAAAjB,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,SAAA,CAAAL,MAAA,CAAiB;IAAA,EAAC,wBAAAmB,0DAAA;MAAA/B,EAAA,CAAAa,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACbV,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IAkB7BnB,EAAA,CAAAC,cAAA,WAAkF;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrGH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAoB,UAAA,IAAAY,qCAAA,eAA4E;IAChFhC,EAAA,CAAAG,YAAA,EAAI;;;;IALAH,EAfA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAC,IAAA,CAAAgB,KAAA,CAAsB,eAAAjB,MAAA,CAAAC,IAAA,CAAA0B,UAAA,CACQ,4BAAA3B,MAAA,CAAAC,IAAA,CAAA2B,uBAAA,IAAAlC,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAEsH,aAAA9B,MAAA,CAAAC,IAAA,CAAA8B,QAAA,CAC1H,wBAAA/B,MAAA,CAAAC,IAAA,CAAA+B,mBAAA,CACsB,qBAAAhC,MAAA,CAAAC,IAAA,CAAAgC,gBAAA,CACN,uBAAAjC,MAAA,CAAAC,IAAA,CAAAiC,kBAAA,CACI,eAAAlC,MAAA,CAAAC,IAAA,CAAAkC,UAAA,CAChB,UAAAnC,MAAA,CAAAC,IAAA,CAAAb,KAAA,CACV,gBAAAY,MAAA,CAAAC,IAAA,CAAAmC,WAAA,CACY,aAAApC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAIT,sBAAAF,MAAA,CAAAkB,MAAA,IAAAlB,MAAA,CAAAmB,IAAA,EACc;;IAE6CzB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAoB,IAAA,CAAe;IAC9D3B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;IACOR,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAC,IAAA,CAAAqB,KAAA,CAAgB;;;;;IAK1E5B,EAAA,CAAAS,SAAA,aAA8F;;;;;;IAAhCT,EAAA,CAAA2C,UAAA,CAAAC,QAAA,CAAAC,UAAA,CAA0B;IAA5C7C,EAA3B,CAAAsB,UAAA,SAAAsB,QAAA,CAAc,UAAAE,IAAA,CAAY,cAAAxC,MAAA,CAAAyC,GAAA,CAAkB;;;;;;IAFjE/C,EAAA,CAAAC,cAAA,kBAAsI;IAA7CD,EAAA,CAAAU,UAAA,4BAAAsC,yEAAApC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAkBV,MAAA,CAAA4C,iBAAA,CAAAtC,MAAA,CAAyB;IAAA,EAAC;IACrIZ,EAAA,CAAAoB,UAAA,IAAA+B,gDAAA,0BAAkE;IAGtEnD,EAAA,CAAAG,YAAA,EAAK;;;;IAJyDH,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAA8C,gBAAA,CAA8B;IAC7CpD,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAC,IAAA,CAAAqB,KAAA,CAAsB;;;AAwBrE,OAAM,MAAOyB,oBAAoB;EAoB7BC,YAAmBC,aAA4B,EAAUC,EAAqB,EAASC,MAAc,EAAUC,UAA+B,EAAUC,WAAwB;IAA7J,KAAAJ,aAAa,GAAbA,aAAa;IAAyB,KAAAC,EAAE,GAAFA,EAAE;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,UAAU,GAAVA,UAAU;IAA+B,KAAAC,WAAW,GAAXA,WAAW;IARnK,KAAAjC,MAAM,GAAG,KAAK;IAMd,KAAAqB,GAAG,GAAW,EAAE;IAGZ,IAAI,CAACa,sBAAsB,GAAG,IAAI,CAACD,WAAW,CAACE,WAAW,CAACC,SAAS,CAACC,KAAK,IAAG;MACzEC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAK;QAC5B,IAAIH,KAAK,CAACI,UAAU,EAAE;UAClB,IAAI,CAACzC,MAAM,GAAIqC,KAAK,CAAChB,GAAG,KAAK,IAAI,CAACA,GAAG,IAAIgB,KAAK,CAAChB,GAAG,CAACqB,UAAU,CAAC,IAAI,CAACrB,GAAG,GAAG,GAAG,CAAC,GAAI,IAAI,GAAG,KAAK;QACjG,CAAC,MACI;UACD,IAAIgB,KAAK,CAAChB,GAAG,KAAK,IAAI,CAACA,GAAG,IAAI,CAACgB,KAAK,CAAChB,GAAG,CAACqB,UAAU,CAAC,IAAI,CAACrB,GAAG,GAAG,GAAG,CAAC,EAAE;YACjE,IAAI,CAACrB,MAAM,GAAG,KAAK;UACvB;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,IAAI,CAAC2C,qBAAqB,GAAG,IAAI,CAACV,WAAW,CAACW,YAAY,CAACR,SAAS,CAAC,MAAK;MACtE,IAAI,CAACpC,MAAM,GAAG,KAAK;IACvB,CAAC,CAAC;IAEF,IAAI,CAAC+B,MAAM,CAACc,MAAM,CAACC,IAAI,CAAC1E,MAAM,CAAC2E,KAAK,IAAIA,KAAK,YAAYjF,aAAa,CAAC,CAAC,CACnEsE,SAAS,CAACY,MAAM,IAAG;MAChB,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACnD,MAAM,IAAI,IAAI,CAACoD,YAAY,EAAE;QACrD,IAAI,CAAClD,MAAM,GAAG,KAAK;MACvB,CAAC,MACI;QACD,IAAI,IAAI,CAACnB,IAAI,CAAC0B,UAAU,EAAE;UACtB,IAAI,CAAC4C,0BAA0B,EAAE;QACrC;MACJ;IACJ,CAAC,CAAC;EACV;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAAC/B,GAAG,GAAG,IAAI,CAACgC,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG,GAAG,IAAI,CAACC,KAAK,GAAGC,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC;IAElF,IAAI,EAAE,IAAI,CAACL,UAAU,IAAI,IAAI,CAACnD,MAAM,IAAI,IAAI,CAACoD,YAAY,CAAC,IAAI,IAAI,CAACrE,IAAI,CAAC0B,UAAU,EAAE;MAChF,IAAI,CAAC4C,0BAA0B,EAAE;IACrC;EACJ;EAEAK,kBAAkBA,CAAA;IACd,IAAI,IAAI,CAACzD,IAAI,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAAC6B,aAAa,CAAC4B,SAAS,EAAE,KAAK,IAAI,CAAC5B,aAAa,CAACqB,YAAY,EAAE,IAAI,IAAI,CAACrB,aAAa,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAAC+B,aAAa,CAACoB,UAAU,EAAE,CAAC,EAAE;MACrK,IAAI,CAACS,iBAAiB,CAAC,IAAI,CAACC,OAAO,EAAEC,aAAa,EAAE,IAAI,CAACD,OAAO,EAAEC,aAAa,CAACC,aAAa,CAAC;IAClG;EACJ;EAEAV,0BAA0BA,CAAA;IACtB,IAAIW,WAAW,GAAG,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,IAAI,CAAClF,IAAI,CAAC0B,UAAU,CAAC,CAAC,CAAC,EAAE;MAAEyD,KAAK,EAAE,OAAO;MAAEhD,WAAW,EAAE,SAAS;MAAEiD,YAAY,EAAE,SAAS;MAAEtD,QAAQ,EAAE;IAAS,CAAE,CAAC;IAEzJ,IAAImD,WAAW,EAAE;MACb,IAAI,CAAC7B,WAAW,CAACiC,iBAAiB,CAAC;QAAE7C,GAAG,EAAE,IAAI,CAACA,GAAG;QAAEoB,UAAU,EAAE;MAAI,CAAE,CAAC;IAC3E;EACJ;EACAjB,iBAAiBA,CAACuB,KAAqB;IACnC,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAI,IAAI,CAACtC,aAAa,CAAC4B,SAAS,EAAE,KAAK,IAAI,CAAC5B,aAAa,CAACqB,YAAY,EAAE,IAAI,IAAI,CAACrB,aAAa,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAAC+B,aAAa,CAACoB,UAAU,EAAE,CAAC,EAAE;MACxK,MAAMmB,EAAE,GAAqBrB,KAAK,CAACsB,OAAO;MAC1C,MAAMC,QAAQ,GAAqBF,EAAE,CAACP,aAAa;MACnD,IAAI,CAACH,iBAAiB,CAACU,EAAE,EAAEE,QAAQ,CAAC;IACxC;EACJ;EAEAZ,iBAAiBA,CAACa,OAAoB,EAAEC,MAAmB;IACvD,IAAID,OAAO,EAAE;MACT,MAAM;QAAEE,IAAI;QAAEC;MAAG,CAAE,GAAGF,MAAM,CAACG,qBAAqB,EAAE;MACpD,MAAM,CAACC,MAAM,EAAEC,OAAO,CAAC,GAAG,CAACC,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;MACjE,MAAM,CAACC,MAAM,EAAEC,OAAO,CAAC,GAAG,CAACX,OAAO,CAACY,WAAW,EAAEZ,OAAO,CAACa,YAAY,CAAC;MACrE,MAAMC,cAAc,GAAGhH,UAAU,CAACiH,uBAAuB,EAAE;MAC3D;MACAf,OAAO,CAACtG,KAAK,CAACyG,GAAG,GAAG,EAAE;MACtBH,OAAO,CAACtG,KAAK,CAACwG,IAAI,GAAG,EAAE;MAEvB,IAAI,IAAI,CAAC5C,aAAa,CAACqB,YAAY,EAAE,EAAE;QACnC,MAAMqC,KAAK,GAAGd,IAAI,GAAGQ,MAAM,GAAGI,cAAc;QAC5Cd,OAAO,CAACtG,KAAK,CAACwG,IAAI,GAAGG,MAAM,GAAGW,KAAK,GAAG,GAAGd,IAAI,IAAIc,KAAK,GAAGX,MAAM,CAAC,IAAI,GAAG,GAAGH,IAAI,IAAI;MACtF,CAAC,MAAM,IAAI,IAAI,CAAC5C,aAAa,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAAC+B,aAAa,CAACoB,UAAU,EAAE,EAAE;QACvE,MAAMuC,MAAM,GAAGd,GAAG,GAAGQ,OAAO;QAC5BX,OAAO,CAACtG,KAAK,CAACyG,GAAG,GAAGG,OAAO,GAAGW,MAAM,GAAG,GAAGd,GAAG,IAAIc,MAAM,GAAGX,OAAO,CAAC,IAAI,GAAG,GAAGH,GAAG,IAAI;QACnFe,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEhB,GAAG,EAAE,SAAS,EAAEG,OAAO,EAAE,SAAS,EAAEK,OAAO,EAAE,QAAQ,EAAEM,MAAM,CAAC;MACrF;IACJ;EACJ;EAEAjG,SAASA,CAACwD,KAAY;IAClB;IACA,IAAI,IAAI,CAAClE,IAAI,CAAC8G,QAAQ,EAAE;MACpB5C,KAAK,CAAC6C,cAAc,EAAE;MACtB;IACJ;IAEA;IACA,IAAI,IAAI,CAAC7F,IAAI,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,CAACoD,YAAY,IAAI,IAAI,CAACD,UAAU,EAAE;MAClE,IAAI,CAACpB,aAAa,CAAC7D,KAAK,CAAC6H,eAAe,GAAG,CAAC,IAAI,CAAChE,aAAa,CAAC7D,KAAK,CAAC6H,eAAe;IACxF;IAEA;IACA,IAAI,IAAI,CAAChH,IAAI,CAACiH,OAAO,EAAE;MACnB,IAAI,CAACjH,IAAI,CAACiH,OAAO,CAAC;QAAEC,aAAa,EAAEhD,KAAK;QAAElE,IAAI,EAAE,IAAI,CAACA;MAAI,CAAE,CAAC;IAChE;IAEA;IACA,IAAI,IAAI,CAACA,IAAI,CAACqB,KAAK,EAAE;MACjB,IAAI,CAACF,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAE1B,IAAI,IAAI,CAACD,IAAI,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,CAACF,MAAM,IAAI,IAAI,CAACoD,YAAY,IAAI,IAAI,CAACD,UAAU,CAAC,EAAE;QACnF,IAAI,CAACpB,aAAa,CAACmE,oBAAoB,EAAE;MAC7C;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACnE,aAAa,CAACoE,QAAQ,EAAE,EAAE;QAC/B,IAAI,CAACpE,aAAa,CAAC7D,KAAK,CAACkI,sBAAsB,GAAG,KAAK;MAC3D;MAEA,IAAI,IAAI,CAACpG,MAAM,IAAI,IAAI,CAACoD,YAAY,IAAI,IAAI,CAACD,UAAU,EAAE;QACrD,IAAI,CAAChB,WAAW,CAACkE,KAAK,EAAE;QACxB,IAAI,CAACtE,aAAa,CAAC7D,KAAK,CAAC6H,eAAe,GAAG,KAAK;MACpD;IACJ;IAEA,IAAI,CAAC5D,WAAW,CAACiC,iBAAiB,CAAC;MAAE7C,GAAG,EAAE,IAAI,CAACA;IAAG,CAAE,CAAC;EACzD;EAEA5B,YAAYA,CAAA;IACR;IACA,IAAI,IAAI,CAACM,IAAI,KAAK,IAAI,CAACD,MAAM,IAAI,IAAI,CAACoD,YAAY,IAAI,IAAI,CAACD,UAAU,CAAC,IAAI,IAAI,CAACpB,aAAa,CAAC4B,SAAS,EAAE,EAAE;MACtG,IAAI,IAAI,CAAC5B,aAAa,CAAC7D,KAAK,CAAC6H,eAAe,EAAE;QAC1C,IAAI,CAAC7F,MAAM,GAAG,IAAI;QAClB,IAAI,CAACiC,WAAW,CAACiC,iBAAiB,CAAC;UAAE7C,GAAG,EAAE,IAAI,CAACA;QAAG,CAAE,CAAC;MACzD;IACJ;EACJ;EAEA,IAAIK,gBAAgBA,CAAA;IAChB,IAAI,IAAI,CAACG,aAAa,CAAC4B,SAAS,EAAE,KAAK,IAAI,CAAC5B,aAAa,CAACqB,YAAY,EAAE,IAAI,IAAI,CAACrB,aAAa,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAAC+B,aAAa,CAACoB,UAAU,EAAE,CAAC,EACvI,OAAO,IAAI,CAACjD,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,KAE1C,OAAO,IAAI,CAACD,IAAI,GAAG,UAAU,GAAI,IAAI,CAACC,MAAM,GAAG,UAAU,GAAG,WAAY;EAChF;EAEA,IAAIkD,YAAYA,CAAA;IACZ,OAAO,IAAI,CAACrB,aAAa,CAACqB,YAAY,EAAE;EAC5C;EAEA,IAAIpD,MAAMA,CAAA;IACN,OAAO,IAAI,CAAC+B,aAAa,CAAC/B,MAAM,EAAE;EACtC;EAEA,IAAImD,UAAUA,CAAA;IACV,OAAO,IAAI,CAACpB,aAAa,CAACoB,UAAU,EAAE;EAC1C;EAEA,IACImD,WAAWA,CAAA;IACX,OAAO,IAAI,CAACpG,MAAM,IAAI,CAAC,IAAI,CAACD,IAAI;EACpC;EAEAsG,WAAWA,CAAA;IACP,IAAI,IAAI,CAACnE,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACoE,WAAW,EAAE;IAC7C;IAEA,IAAI,IAAI,CAAC3D,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC2D,WAAW,EAAE;IAC5C;EACJ;;;uBAtLS3E,oBAAoB,EAAArD,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAjI,EAAA,CAAAoI,iBAAA,GAAApI,EAAA,CAAAiI,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAtI,EAAA,CAAAiI,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAxI,EAAA,CAAAiI,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBrF,oBAAoB;MAAAsF,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;UAApB9I,EAAA,CAAAgJ,WAAA,yBAAAD,GAAA,CAAAtH,IAAA,CAAoB,oBAAAsH,GAAA,CAAAjB,WAAA;;;;;;;;;;;;;;;UArE7B9H,EAAA,CAAAiJ,uBAAA,GAAc;UA4CdjJ,EA3CA,CAAAoB,UAAA,IAAA8H,mCAAA,iBAA8E,IAAAC,iCAAA,eAY7E,IAAAC,iCAAA,gBAyBA,IAAAC,kCAAA,gBAMqI;;;;UA3ChIrJ,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAAsB,UAAA,SAAAyH,GAAA,CAAAtH,IAAA,IAAAsH,GAAA,CAAAxI,IAAA,CAAA+I,OAAA,WAAoC;UAErCtJ,EAAA,CAAAI,SAAA,EAAgE;UAAhEJ,EAAA,CAAAsB,UAAA,WAAAyH,GAAA,CAAAxI,IAAA,CAAA0B,UAAA,IAAA8G,GAAA,CAAAxI,IAAA,CAAAqB,KAAA,KAAAmH,GAAA,CAAAxI,IAAA,CAAA+I,OAAA,WAAgE;UAgBhEtJ,EAAA,CAAAI,SAAA,EAA8D;UAA9DJ,EAAA,CAAAsB,UAAA,SAAAyH,GAAA,CAAAxI,IAAA,CAAA0B,UAAA,KAAA8G,GAAA,CAAAxI,IAAA,CAAAqB,KAAA,IAAAmH,GAAA,CAAAxI,IAAA,CAAA+I,OAAA,WAA8D;UAyBrDtJ,EAAA,CAAAI,SAAA,EAA0C;UAA1CJ,EAAA,CAAAsB,UAAA,SAAAyH,GAAA,CAAAxI,IAAA,CAAAqB,KAAA,IAAAmH,GAAA,CAAAxI,IAAA,CAAA+I,OAAA,WAA0C;;;iHAyB/CjG,oBAAoB;MAAAkG,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAlBjB,CACR5J,OAAO,CAAC,UAAU,EAAE,CAChBH,KAAK,CAAC,WAAW,EAAEC,KAAK,CAAC;UACrBuH,MAAM,EAAE;SACX,CAAC,CAAC,EACHxH,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;UACpBuH,MAAM,EAAE;SACX,CAAC,CAAC,EACHxH,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;UAClB+J,OAAO,EAAE;SACZ,CAAC,CAAC,EACHhK,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;UACnB+J,OAAO,EAAE;SACZ,CAAC,CAAC,EACH9J,UAAU,CAAC,wBAAwB,EAAEH,OAAO,CAAC,sCAAsC,CAAC,CAAC,CACxF,CAAC;MACL;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}