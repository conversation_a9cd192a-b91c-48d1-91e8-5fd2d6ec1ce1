{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomerComponent } from './customer.component';\nimport { CustomerDetailsComponent } from './customer-details/customer-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomerComponent\n}, {\n  path: ':id',\n  component: CustomerDetailsComponent\n}];\nexport let CustomerRoutingModule = /*#__PURE__*/(() => {\n  class CustomerRoutingModule {\n    static {\n      this.ɵfac = function CustomerRoutingModule_Factory(t) {\n        return new (t || CustomerRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CustomerRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return CustomerRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}