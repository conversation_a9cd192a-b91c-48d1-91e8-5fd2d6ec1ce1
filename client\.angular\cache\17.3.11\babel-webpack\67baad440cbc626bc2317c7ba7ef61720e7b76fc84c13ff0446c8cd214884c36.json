{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from 'rxjs';\nimport { stringify } from 'qs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http, ngZone) {\n    this.http = http;\n    this.ngZone = ngZone;\n    this.permissions = new BehaviorSubject([]);\n    this.sessionChannel = new BroadcastChannel('session');\n    this.logoutTriggered = false;\n    this.TokenKey = 'jwtToken';\n    this.UserDetailsKey = 'userInfo';\n    const user = this.getAuth();\n    this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : '');\n    if (user[this.UserDetailsKey]?.isAdmin && (!user[this.UserDetailsKey]?.cart || !user[this.UserDetailsKey]?.customer)) {\n      const user = this.userDetail;\n      this.getCartDetails(user[this.UserDetailsKey].documentId).subscribe({\n        next: cartres => {\n          if (cartres?.cart) {\n            const cart = cartres.cart || null;\n            this.updateAuth({\n              cart,\n              customer: cart.customer\n            });\n          }\n        }\n      });\n    }\n    this.bindUserActivityEvents();\n  }\n  bindUserActivityEvents() {\n    const events = ['click', 'keydown'];\n    for (let i = 0; i < events.length; i++) {\n      const element = events[i];\n      fromEvent(document, element).subscribe(data => {\n        this.setInavtivityTimer();\n        this.sessionChannel.postMessage({\n          type: 'activityFound'\n        });\n      });\n    }\n    this.sessionChannel.onmessage = event => {\n      if (event?.data?.type == 'activityFound') {\n        this.ngZone.run(() => {\n          this.setInavtivityTimer();\n        });\n      }\n      if (event?.data?.type == 'logout') {\n        this.logoutTriggered = true;\n        this.doLogout();\n      }\n    };\n    this.setInavtivityTimer();\n    this.sessionChannel.postMessage({\n      type: 'activityFound'\n    });\n  }\n  setInavtivityTimer() {\n    clearTimeout(this.timer);\n    if (!this.isLoggedIn) {\n      return;\n    }\n    this.timer = setTimeout(() => {\n      this.doLogout();\n    }, AppConstant.SESSION_TIMEOUT);\n  }\n  login(username, password, rememberMe) {\n    return this.http.post(CMS_APIContstant.SINGIN, {\n      identifier: (username || '').toLowerCase(),\n      password\n    }).pipe(tap(res => {\n      if (res) {\n        this.setAuth(res.jwt, res.user, rememberMe);\n      }\n      return res;\n    }), switchMap(res => {\n      if (res?.user) {\n        return this.getCartDetails(res.user.documentId).pipe(map(data => {\n          if (data?.cart) {\n            res.user.cart = data.cart;\n            res.user.customer = data.cart.customer;\n          }\n          this.updateAuth(res.user);\n          return res;\n        }));\n      }\n      return of(null);\n    }));\n  }\n  getCartDetails(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\n  }\n  getToken() {\n    const val = this.userSubject.value;\n    return val ? val[this.TokenKey] : null;\n  }\n  get userDetail() {\n    const user = this.userSubject.value;\n    return user ? user[this.UserDetailsKey] : null;\n  }\n  get partnerFunction() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n      return user[this.UserDetailsKey].customer.partner_functions[0];\n    }\n    return {};\n  }\n  get isLoggedIn() {\n    return !!this.userSubject.value;\n  }\n  get isCustomerSelected() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer) {\n      return true;\n    }\n    return false;\n  }\n  updateAuth(user) {\n    const auth = this.getAuth();\n    if (user?.cart) {\n      auth[this.UserDetailsKey].cart = user?.cart;\n    }\n    if (user?.customer) {\n      auth[this.UserDetailsKey].customer = user?.customer;\n    }\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n  }\n  isRememberMeSelected() {\n    return !!localStorage.getItem(this.TokenKey);\n  }\n  doLogout() {\n    this.resetAuth();\n  }\n  resetAuth() {\n    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {\n      this.removeAuthToken();\n      !this.logoutTriggered && this.sessionChannel.postMessage({\n        type: 'logout'\n      });\n      this.userSubject.next(null);\n      window.location.href = '#/auth/login';\n      window.location.reload();\n    });\n  }\n  getAuth() {\n    const authtoken = this.getAuthToken();\n    const userDetails = this.getUserDetails();\n    if (authtoken && this.isJsonString(userDetails)) {\n      return {\n        [this.UserDetailsKey]: JSON.parse(userDetails),\n        [this.TokenKey]: authtoken\n      };\n    }\n    return {};\n  }\n  setAuth(token, user, rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.TokenKey, token);\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    } else {\n      sessionStorage.setItem(this.TokenKey, token);\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    }\n    this.userSubject.next({\n      [this.UserDetailsKey]: user,\n      [this.TokenKey]: token\n    });\n  }\n  getAuthToken() {\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n  }\n  getUserDetails() {\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n  }\n  getUserEmail() {\n    const userData = sessionStorage.getItem('userInfo');\n    if (userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        return parsedUser.email || null;\n      } catch (error) {\n        return null;\n      }\n    }\n    return null;\n  }\n  removeAuthToken() {\n    localStorage.removeItem(this.TokenKey);\n    sessionStorage.removeItem(this.TokenKey);\n    localStorage.removeItem(this.UserDetailsKey);\n    sessionStorage.removeItem(this.UserDetailsKey);\n  }\n  isJsonString(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  getUserPermissions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const userDetails = _this.userDetail;\n      const obj = {};\n      const query = stringify(obj);\n      //TODO: Change api when available and add permissions\n      return yield lastValueFrom(_this.http.get(`${CMS_APIContstant.USER_PERMISSIONS}?${query}`).pipe(map(res => {\n        if (res?.data?.length) {\n          const data = (res?.data || []).map(permission => permission.code);\n          _this.permissions.next(data);\n          return data;\n        }\n        return [];\n      })).pipe(catchError(error => {\n        _this.permissions.next([]);\n        return error;\n      })));\n    })();\n  }\n  get getPermissions() {\n    return this.permissions?.value || [];\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AppConstant", "CMS_APIContstant", "BehaviorSubject", "catchError", "fromEvent", "lastValueFrom", "map", "of", "switchMap", "tap", "stringify", "AuthService", "constructor", "http", "ngZone", "permissions", "sessionChannel", "BroadcastChannel", "logoutTriggered", "TokenKey", "UserDetailsKey", "user", "getAuth", "userSubject", "Object", "keys", "length", "isAdmin", "cart", "customer", "userDetail", "getCartDetails", "documentId", "subscribe", "next", "cartres", "updateAuth", "bindUserActivityEvents", "events", "i", "element", "document", "data", "setInavtivityTimer", "postMessage", "type", "onmessage", "event", "run", "doLogout", "clearTimeout", "timer", "isLoggedIn", "setTimeout", "SESSION_TIMEOUT", "login", "username", "password", "rememberMe", "post", "SINGIN", "identifier", "toLowerCase", "pipe", "res", "setAuth", "jwt", "userId", "get", "USER_DETAILS", "getToken", "val", "value", "partnerFunction", "partner_functions", "isCustomerSelected", "auth", "isRememberMeSelected", "localStorage", "getItem", "resetAuth", "removeAuthToken", "window", "location", "href", "reload", "authtoken", "getAuthToken", "userDetails", "getUserDetails", "isJsonString", "JSON", "parse", "token", "setItem", "sessionStorage", "getUserEmail", "userData", "parsedUser", "email", "error", "removeItem", "str", "e", "getUserPermissions", "_this", "_asyncToGenerator", "obj", "query", "USER_PERMISSIONS", "permission", "code", "getPermissions", "i0", "ɵɵinject", "i1", "HttpClient", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\core\\authentication\\auth.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AppConstant, CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  fromEvent,\r\n  lastValueFrom,\r\n  map,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from 'rxjs';\r\nimport { stringify } from 'qs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  public userSubject: BehaviorSubject<any>;\r\n  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);\r\n  private sessionChannel = new BroadcastChannel('session');\r\n  private timer: any;\r\n  private logoutTriggered = false;\r\n  public TokenKey = 'jwtToken';\r\n  public UserDetailsKey = 'userInfo';\r\n\r\n  constructor(private http: HttpClient, private ngZone: NgZone) {\r\n    const user: any = this.getAuth();\r\n    this.userSubject = new BehaviorSubject<any>(\r\n      Object.keys(user).length ? user : ''\r\n    );\r\n    if (\r\n      user[this.UserDetailsKey]?.isAdmin &&\r\n      (!user[this.UserDetailsKey]?.cart || !user[this.UserDetailsKey]?.customer)\r\n    ) {\r\n      const user = this.userDetail;\r\n      this.getCartDetails(user[this.UserDetailsKey].documentId).subscribe({\r\n        next: (cartres: any) => {\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.updateAuth({\r\n              cart,\r\n              customer: cart.customer,\r\n            });\r\n          }\r\n        },\r\n      });\r\n    }\r\n    this.bindUserActivityEvents();\r\n  }\r\n\r\n  bindUserActivityEvents() {\r\n    const events = ['click', 'keydown'];\r\n    for (let i = 0; i < events.length; i++) {\r\n      const element = events[i];\r\n      fromEvent(document, element).subscribe((data) => {\r\n        this.setInavtivityTimer();\r\n        this.sessionChannel.postMessage({\r\n          type: 'activityFound',\r\n        });\r\n      });\r\n    }\r\n    this.sessionChannel.onmessage = (event) => {\r\n      if (event?.data?.type == 'activityFound') {\r\n        this.ngZone.run(() => {\r\n          this.setInavtivityTimer();\r\n        });\r\n      }\r\n      if (event?.data?.type == 'logout') {\r\n        this.logoutTriggered = true;\r\n        this.doLogout();\r\n      }\r\n    };\r\n    this.setInavtivityTimer();\r\n    this.sessionChannel.postMessage({\r\n      type: 'activityFound',\r\n    });\r\n  }\r\n\r\n  setInavtivityTimer() {\r\n    clearTimeout(this.timer);\r\n    if (!this.isLoggedIn) {\r\n      return;\r\n    }\r\n    this.timer = setTimeout(() => {\r\n      this.doLogout();\r\n    }, AppConstant.SESSION_TIMEOUT);\r\n  }\r\n\r\n  login(username: string, password: string, rememberMe: boolean) {\r\n    return this.http\r\n      .post<any>(CMS_APIContstant.SINGIN, {\r\n        identifier: (username || '').toLowerCase(),\r\n        password,\r\n      })\r\n      .pipe(\r\n        tap((res) => {\r\n          if (res) {\r\n            this.setAuth(res.jwt, res.user, rememberMe);\r\n          }\r\n          return res;\r\n        }),\r\n        switchMap((res) => {\r\n          if (res?.user) {\r\n            return this.getCartDetails(res.user.documentId).pipe(\r\n              map((data: any) => {\r\n                if (data?.cart) {\r\n                  res.user.cart = data.cart;\r\n                  res.user.customer = data.cart.customer;\r\n                }\r\n                this.updateAuth(res.user);\r\n                return res;\r\n              })\r\n            );\r\n          }\r\n          return of(null);\r\n        })\r\n      );\r\n  }\r\n  getCartDetails(userId: string): any {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);\r\n  }\r\n\r\n  getToken() {\r\n    const val = this.userSubject.value;\r\n    return val ? val[this.TokenKey] : null;\r\n  }\r\n\r\n  get userDetail() {\r\n    const user = this.userSubject.value;\r\n    return user ? user[this.UserDetailsKey] : null;\r\n  }\r\n  get partnerFunction() {\r\n    const user = this.userSubject.value;\r\n    if (\r\n      user &&\r\n      user[this.UserDetailsKey]?.customer?.partner_functions?.length\r\n    ) {\r\n      return user[this.UserDetailsKey].customer.partner_functions[0];\r\n    }\r\n    return {};\r\n  }\r\n  get isLoggedIn(): boolean {\r\n    return !!this.userSubject.value;\r\n  }\r\n\r\n  get isCustomerSelected(): boolean {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer) {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  updateAuth(user: any) {\r\n    const auth: any = this.getAuth();\r\n    if (user?.cart) {\r\n      auth[this.UserDetailsKey].cart = user?.cart;\r\n    }\r\n    if (user?.customer) {\r\n      auth[this.UserDetailsKey].customer = user?.customer;\r\n    }\r\n    this.setAuth(\r\n      auth[this.TokenKey],\r\n      auth[this.UserDetailsKey],\r\n      this.isRememberMeSelected()\r\n    );\r\n  }\r\n\r\n  isRememberMeSelected(): boolean {\r\n    return !!localStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  doLogout() {\r\n    this.resetAuth();\r\n  }\r\n\r\n  resetAuth() {\r\n    this.http\r\n      .get(\r\n        `${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`\r\n      )\r\n      .subscribe(() => {\r\n        this.removeAuthToken();\r\n        !this.logoutTriggered &&\r\n          this.sessionChannel.postMessage({\r\n            type: 'logout',\r\n          });\r\n        this.userSubject.next(null);\r\n        window.location.href = '#/auth/login';\r\n        window.location.reload();\r\n      });\r\n  }\r\n\r\n  getAuth(): any {\r\n    const authtoken: any = this.getAuthToken();\r\n    const userDetails: any = this.getUserDetails();\r\n    if (authtoken && this.isJsonString(userDetails)) {\r\n      return {\r\n        [this.UserDetailsKey]: JSON.parse(userDetails),\r\n        [this.TokenKey]: authtoken,\r\n      };\r\n    }\r\n    return {};\r\n  }\r\n\r\n  setAuth(token: string, user: any, rememberMe: boolean) {\r\n    if (rememberMe) {\r\n      localStorage.setItem(this.TokenKey, token);\r\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    } else {\r\n      sessionStorage.setItem(this.TokenKey, token);\r\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    }\r\n    this.userSubject.next({\r\n      [this.UserDetailsKey]: user,\r\n      [this.TokenKey]: token,\r\n    });\r\n  }\r\n\r\n  getAuthToken() {\r\n    return (\r\n      localStorage.getItem(this.TokenKey) ||\r\n      sessionStorage.getItem(this.TokenKey)\r\n    );\r\n  }\r\n\r\n  getUserDetails() {\r\n    return (\r\n      localStorage.getItem(this.UserDetailsKey) ||\r\n      sessionStorage.getItem(this.UserDetailsKey)\r\n    );\r\n  }\r\n\r\n  getUserEmail(): string | null {\r\n    const userData = sessionStorage.getItem('userInfo');\r\n\r\n    if (userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        return parsedUser.email || null;\r\n      } catch (error) {\r\n        return null;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  removeAuthToken() {\r\n    localStorage.removeItem(this.TokenKey);\r\n    sessionStorage.removeItem(this.TokenKey);\r\n    localStorage.removeItem(this.UserDetailsKey);\r\n    sessionStorage.removeItem(this.UserDetailsKey);\r\n  }\r\n\r\n  isJsonString(str: any) {\r\n    try {\r\n      JSON.parse(str);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async getUserPermissions() {\r\n    const userDetails = this.userDetail;\r\n    const obj: any = {};\r\n    const query = stringify(obj);\r\n    //TODO: Change api when available and add permissions\r\n    return await lastValueFrom(\r\n      this.http\r\n        .get<any>(`${CMS_APIContstant.USER_PERMISSIONS}?${query}`)\r\n        .pipe(\r\n          map((res) => {\r\n            if (res?.data?.length) {\r\n              const data = (res?.data || []).map(\r\n                (permission: any) => permission.code\r\n              );\r\n              this.permissions.next(data);\r\n              return data;\r\n            }\r\n            return [];\r\n          })\r\n        )\r\n        .pipe(\r\n          catchError((error) => {\r\n            this.permissions.next([]);\r\n            return error;\r\n          })\r\n        )\r\n    );\r\n  }\r\n\r\n  get getPermissions(): any[] {\r\n    return this.permissions?.value || [];\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/E,SACEC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,GAAG,EACHC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;AACb,SAASC,SAAS,QAAQ,IAAI;;;AAK9B,OAAM,MAAOC,WAAW;EAStBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP7C,KAAAC,WAAW,GAAyB,IAAIb,eAAe,CAAM,EAAE,CAAC;IAC/D,KAAAc,cAAc,GAAG,IAAIC,gBAAgB,CAAC,SAAS,CAAC;IAEhD,KAAAC,eAAe,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,UAAU;IACrB,KAAAC,cAAc,GAAG,UAAU;IAGhC,MAAMC,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,IAAIrB,eAAe,CACpCsB,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAGL,IAAI,GAAG,EAAE,CACrC;IACD,IACEA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEO,OAAO,KACjC,CAACN,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEQ,IAAI,IAAI,CAACP,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,CAAC,EAC1E;MACA,MAAMR,IAAI,GAAG,IAAI,CAACS,UAAU;MAC5B,IAAI,CAACC,cAAc,CAACV,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACY,UAAU,CAAC,CAACC,SAAS,CAAC;QAClEC,IAAI,EAAGC,OAAY,IAAI;UACrB,IAAIA,OAAO,EAAEP,IAAI,EAAE;YACjB,MAAMA,IAAI,GAAGO,OAAO,CAACP,IAAI,IAAI,IAAI;YACjC,IAAI,CAACQ,UAAU,CAAC;cACdR,IAAI;cACJC,QAAQ,EAAED,IAAI,CAACC;aAChB,CAAC;UACJ;QACF;OACD,CAAC;IACJ;IACA,IAAI,CAACQ,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,MAAMC,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACZ,MAAM,EAAEa,CAAC,EAAE,EAAE;MACtC,MAAMC,OAAO,GAAGF,MAAM,CAACC,CAAC,CAAC;MACzBnC,SAAS,CAACqC,QAAQ,EAAED,OAAO,CAAC,CAACP,SAAS,CAAES,IAAI,IAAI;QAC9C,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAAC3B,cAAc,CAAC4B,WAAW,CAAC;UAC9BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAAC7B,cAAc,CAAC8B,SAAS,GAAIC,KAAK,IAAI;MACxC,IAAIA,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,eAAe,EAAE;QACxC,IAAI,CAAC/B,MAAM,CAACkC,GAAG,CAAC,MAAK;UACnB,IAAI,CAACL,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MACA,IAAII,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,QAAQ,EAAE;QACjC,IAAI,CAAC3B,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC+B,QAAQ,EAAE;MACjB;IACF,CAAC;IACD,IAAI,CAACN,kBAAkB,EAAE;IACzB,IAAI,CAAC3B,cAAc,CAAC4B,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAF,kBAAkBA,CAAA;IAChBO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAACD,KAAK,GAAGE,UAAU,CAAC,MAAK;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAEjD,WAAW,CAACsD,eAAe,CAAC;EACjC;EAEAC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,UAAmB;IAC3D,OAAO,IAAI,CAAC7C,IAAI,CACb8C,IAAI,CAAM1D,gBAAgB,CAAC2D,MAAM,EAAE;MAClCC,UAAU,EAAE,CAACL,QAAQ,IAAI,EAAE,EAAEM,WAAW,EAAE;MAC1CL;KACD,CAAC,CACDM,IAAI,CACHtD,GAAG,CAAEuD,GAAG,IAAI;MACV,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,OAAO,CAACD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC3C,IAAI,EAAEqC,UAAU,CAAC;MAC7C;MACA,OAAOM,GAAG;IACZ,CAAC,CAAC,EACFxD,SAAS,CAAEwD,GAAG,IAAI;MAChB,IAAIA,GAAG,EAAE3C,IAAI,EAAE;QACb,OAAO,IAAI,CAACU,cAAc,CAACiC,GAAG,CAAC3C,IAAI,CAACW,UAAU,CAAC,CAAC+B,IAAI,CAClDzD,GAAG,CAAEoC,IAAS,IAAI;UAChB,IAAIA,IAAI,EAAEd,IAAI,EAAE;YACdoC,GAAG,CAAC3C,IAAI,CAACO,IAAI,GAAGc,IAAI,CAACd,IAAI;YACzBoC,GAAG,CAAC3C,IAAI,CAACQ,QAAQ,GAAGa,IAAI,CAACd,IAAI,CAACC,QAAQ;UACxC;UACA,IAAI,CAACO,UAAU,CAAC4B,GAAG,CAAC3C,IAAI,CAAC;UACzB,OAAO2C,GAAG;QACZ,CAAC,CAAC,CACH;MACH;MACA,OAAOzD,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EACAwB,cAAcA,CAACoC,MAAc;IAC3B,OAAO,IAAI,CAACtD,IAAI,CAACuD,GAAG,CAAC,GAAGnE,gBAAgB,CAACoE,YAAY,IAAIF,MAAM,KAAK,CAAC;EACvE;EAEAG,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,IAAI,CAAChD,WAAW,CAACiD,KAAK;IAClC,OAAOD,GAAG,GAAGA,GAAG,CAAC,IAAI,CAACpD,QAAQ,CAAC,GAAG,IAAI;EACxC;EAEA,IAAIW,UAAUA,CAAA;IACZ,MAAMT,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiD,KAAK;IACnC,OAAOnD,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG,IAAI;EAChD;EACA,IAAIqD,eAAeA,CAAA;IACjB,MAAMpD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiD,KAAK;IACnC,IACEnD,IAAI,IACJA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,EAAE6C,iBAAiB,EAAEhD,MAAM,EAC9D;MACA,OAAOL,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACS,QAAQ,CAAC6C,iBAAiB,CAAC,CAAC,CAAC;IAChE;IACA,OAAO,EAAE;EACX;EACA,IAAItB,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAAC7B,WAAW,CAACiD,KAAK;EACjC;EAEA,IAAIG,kBAAkBA,CAAA;IACpB,MAAMtD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACiD,KAAK;IACnC,IAAInD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,QAAQ,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAO,UAAUA,CAACf,IAAS;IAClB,MAAMuD,IAAI,GAAQ,IAAI,CAACtD,OAAO,EAAE;IAChC,IAAID,IAAI,EAAEO,IAAI,EAAE;MACdgD,IAAI,CAAC,IAAI,CAACxD,cAAc,CAAC,CAACQ,IAAI,GAAGP,IAAI,EAAEO,IAAI;IAC7C;IACA,IAAIP,IAAI,EAAEQ,QAAQ,EAAE;MAClB+C,IAAI,CAAC,IAAI,CAACxD,cAAc,CAAC,CAACS,QAAQ,GAAGR,IAAI,EAAEQ,QAAQ;IACrD;IACA,IAAI,CAACoC,OAAO,CACVW,IAAI,CAAC,IAAI,CAACzD,QAAQ,CAAC,EACnByD,IAAI,CAAC,IAAI,CAACxD,cAAc,CAAC,EACzB,IAAI,CAACyD,oBAAoB,EAAE,CAC5B;EACH;EAEAA,oBAAoBA,CAAA;IAClB,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC5D,QAAQ,CAAC;EAC9C;EAEA8B,QAAQA,CAAA;IACN,IAAI,CAAC+B,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACnE,IAAI,CACNuD,GAAG,CACF,GAAGnE,gBAAgB,CAACoE,YAAY,IAAI,IAAI,CAACvC,UAAU,CAACE,UAAU,SAAS,CACxE,CACAC,SAAS,CAAC,MAAK;MACd,IAAI,CAACgD,eAAe,EAAE;MACtB,CAAC,IAAI,CAAC/D,eAAe,IACnB,IAAI,CAACF,cAAc,CAAC4B,WAAW,CAAC;QAC9BC,IAAI,EAAE;OACP,CAAC;MACJ,IAAI,CAACtB,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC;MAC3BgD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;IAC1B,CAAC,CAAC;EACN;EAEA/D,OAAOA,CAAA;IACL,MAAMgE,SAAS,GAAQ,IAAI,CAACC,YAAY,EAAE;IAC1C,MAAMC,WAAW,GAAQ,IAAI,CAACC,cAAc,EAAE;IAC9C,IAAIH,SAAS,IAAI,IAAI,CAACI,YAAY,CAACF,WAAW,CAAC,EAAE;MAC/C,OAAO;QACL,CAAC,IAAI,CAACpE,cAAc,GAAGuE,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;QAC9C,CAAC,IAAI,CAACrE,QAAQ,GAAGmE;OAClB;IACH;IACA,OAAO,EAAE;EACX;EAEArB,OAAOA,CAAC4B,KAAa,EAAExE,IAAS,EAAEqC,UAAmB;IACnD,IAAIA,UAAU,EAAE;MACdoB,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC3E,QAAQ,EAAE0E,KAAK,CAAC;MAC1Cf,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC1E,cAAc,EAAEuE,IAAI,CAACjF,SAAS,CAACW,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL0E,cAAc,CAACD,OAAO,CAAC,IAAI,CAAC3E,QAAQ,EAAE0E,KAAK,CAAC;MAC5CE,cAAc,CAACD,OAAO,CAAC,IAAI,CAAC1E,cAAc,EAAEuE,IAAI,CAACjF,SAAS,CAACW,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,CAACE,WAAW,CAACW,IAAI,CAAC;MACpB,CAAC,IAAI,CAACd,cAAc,GAAGC,IAAI;MAC3B,CAAC,IAAI,CAACF,QAAQ,GAAG0E;KAClB,CAAC;EACJ;EAEAN,YAAYA,CAAA;IACV,OACET,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC5D,QAAQ,CAAC,IACnC4E,cAAc,CAAChB,OAAO,CAAC,IAAI,CAAC5D,QAAQ,CAAC;EAEzC;EAEAsE,cAAcA,CAAA;IACZ,OACEX,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC3D,cAAc,CAAC,IACzC2E,cAAc,CAAChB,OAAO,CAAC,IAAI,CAAC3D,cAAc,CAAC;EAE/C;EAEA4E,YAAYA,CAAA;IACV,MAAMC,QAAQ,GAAGF,cAAc,CAAChB,OAAO,CAAC,UAAU,CAAC;IAEnD,IAAIkB,QAAQ,EAAE;MACZ,IAAI;QACF,MAAMC,UAAU,GAAGP,IAAI,CAACC,KAAK,CAACK,QAAQ,CAAC;QACvC,OAAOC,UAAU,CAACC,KAAK,IAAI,IAAI;MACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAEAnB,eAAeA,CAAA;IACbH,YAAY,CAACuB,UAAU,CAAC,IAAI,CAAClF,QAAQ,CAAC;IACtC4E,cAAc,CAACM,UAAU,CAAC,IAAI,CAAClF,QAAQ,CAAC;IACxC2D,YAAY,CAACuB,UAAU,CAAC,IAAI,CAACjF,cAAc,CAAC;IAC5C2E,cAAc,CAACM,UAAU,CAAC,IAAI,CAACjF,cAAc,CAAC;EAChD;EAEAsE,YAAYA,CAACY,GAAQ;IACnB,IAAI;MACFX,IAAI,CAACC,KAAK,CAACU,GAAG,CAAC;IACjB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEMC,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMlB,WAAW,GAAGiB,KAAI,CAAC3E,UAAU;MACnC,MAAM6E,GAAG,GAAQ,EAAE;MACnB,MAAMC,KAAK,GAAGlG,SAAS,CAACiG,GAAG,CAAC;MAC5B;MACA,aAAatG,aAAa,CACxBoG,KAAI,CAAC5F,IAAI,CACNuD,GAAG,CAAM,GAAGnE,gBAAgB,CAAC4G,gBAAgB,IAAID,KAAK,EAAE,CAAC,CACzD7C,IAAI,CACHzD,GAAG,CAAE0D,GAAG,IAAI;QACV,IAAIA,GAAG,EAAEtB,IAAI,EAAEhB,MAAM,EAAE;UACrB,MAAMgB,IAAI,GAAG,CAACsB,GAAG,EAAEtB,IAAI,IAAI,EAAE,EAAEpC,GAAG,CAC/BwG,UAAe,IAAKA,UAAU,CAACC,IAAI,CACrC;UACDN,KAAI,CAAC1F,WAAW,CAACmB,IAAI,CAACQ,IAAI,CAAC;UAC3B,OAAOA,IAAI;QACb;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH,CACAqB,IAAI,CACH5D,UAAU,CAAEiG,KAAK,IAAI;QACnBK,KAAI,CAAC1F,WAAW,CAACmB,IAAI,CAAC,EAAE,CAAC;QACzB,OAAOkE,KAAK;MACd,CAAC,CAAC,CACH,CACJ;IAAC;EACJ;EAEA,IAAIY,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACjG,WAAW,EAAEyD,KAAK,IAAI,EAAE;EACtC;;;uBAtRW7D,WAAW,EAAAsG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAI,MAAA;IAAA;EAAA;;;aAAX1G,WAAW;MAAA2G,OAAA,EAAX3G,WAAW,CAAA4G,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}