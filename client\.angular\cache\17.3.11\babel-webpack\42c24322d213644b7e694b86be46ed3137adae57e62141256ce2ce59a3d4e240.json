{"ast": null, "code": "import { MessageService } from 'primeng/api';\nimport { stringify } from \"qs\";\nimport { map, of, switchMap } from 'rxjs';\nimport { Country, State, City } from 'country-state-city';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../account/account.service\";\nimport * as i5 from \"../services/service-ticket.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/table\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/inputtext\";\nfunction IdentifyAccountComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"h5\", 21)(4, \"i\", 9);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.first_name, \" \", ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.last_name, \" \");\n  }\n}\nfunction IdentifyAccountComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"form\", 23)(2, \"div\", 24)(3, \"div\", 18)(4, \"div\", 25)(5, \"div\", 26)(6, \"label\", 27)(7, \"span\", 28);\n    i0.ɵɵtext(8, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Account Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 25)(12, \"div\", 26)(13, \"label\", 27)(14, \"span\", 28);\n    i0.ɵɵtext(15, \"tag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" CRM ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 25)(19, \"div\", 26)(20, \"label\", 27)(21, \"span\", 28);\n    i0.ɵɵtext(22, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Street \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"label\", 27)(28, \"span\", 28);\n    i0.ɵɵtext(29, \"public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Country \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"p-dropdown\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 33)(33, \"div\", 26)(34, \"label\", 27)(35, \"span\", 28);\n    i0.ɵɵtext(36, \"my_location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" State \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"p-dropdown\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33)(40, \"div\", 26)(41, \"label\", 27)(42, \"span\", 28);\n    i0.ɵɵtext(43, \"map\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44, \" City \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"p-dropdown\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 33)(47, \"div\", 26)(48, \"label\", 27)(49, \"span\", 28);\n    i0.ɵɵtext(50, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \" Zip Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 33)(54, \"div\", 26)(55, \"label\", 27)(56, \"span\", 28);\n    i0.ɵɵtext(57, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 33)(61, \"div\", 26)(62, \"label\", 27)(63, \"span\", 28);\n    i0.ɵɵtext(64, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \" Telephone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"input\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 33)(68, \"div\", 26)(69, \"label\", 27)(70, \"span\", 28);\n    i0.ɵɵtext(71, \"pin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \" Invoice # \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"input\", 39);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(74, \"div\", 40)(75, \"div\", 13)(76, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.search());\n    });\n    i0.ɵɵelementStart(77, \"i\", 15);\n    i0.ɵɵtext(78, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_80_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.clear());\n    });\n    i0.ɵɵelementStart(81, \"i\", 15);\n    i0.ɵɵtext(82, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(83, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_div_12_Template_button_click_84_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.reset());\n    });\n    i0.ɵɵelementStart(85, \"i\", 15);\n    i0.ɵɵtext(86, \"rule_settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" Reset \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.filterForm);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"options\", ctx_r0.countries);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.states);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.cities);\n    i0.ɵɵadvance(31);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loading ? \"Searching...\" : \"Search\", \" \");\n  }\n}\nfunction IdentifyAccountComponent_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No records found.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 48);\n    i0.ɵɵelementStart(2, \"th\");\n    i0.ɵɵtext(3, \"Account Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\");\n    i0.ɵɵtext(5, \"Account Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Address\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 49);\n    i0.ɵɵelement(2, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 51)(5, \"span\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const expanded_r4 = ctx.expanded;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", item_r3)(\"icon\", expanded_r4 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\")(\"disabled\", !item_r3.contacts || !item_r3.contacts.length);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getInitials(item_r3.bp_full_name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.bp_full_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.phoneNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.address);\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 49);\n    i0.ɵɵelementStart(2, \"th\");\n    i0.ɵɵtext(3, \"ID #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\");\n    i0.ɵɵtext(5, \"First name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Last name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Phone no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 58);\n    i0.ɵɵtext(13, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 49)(2, \"input\", 59);\n    i0.ɵɵlistener(\"change\", function IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_3_Template_input_change_2_listener() {\n      const tableinfo_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.selectedContact = tableinfo_r6);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 58);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tableinfo_r6 = ctx.$implicit;\n    const item_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"contactSelection\", item_r7.bp_id, \"\");\n    i0.ɵɵproperty(\"value\", tableinfo_r6)(\"checked\", (ctx_r0.selectedContact == null ? null : ctx_r0.selectedContact.bp_id) === tableinfo_r6.bp_id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tableinfo_r6.bp_id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r6.first_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r6.last_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r6.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r6.phoneNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tableinfo_r6.status);\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"p-table\", 57);\n    i0.ɵɵtemplate(2, IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_2_Template, 14, 0, \"ng-template\", 45)(3, IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_3_Template, 15, 10, \"ng-template\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", item_r7.contacts)(\"rows\", 8)(\"paginator\", true);\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 54);\n    i0.ɵɵtemplate(2, IdentifyAccountComponent_p_table_23_ng_template_3_div_2_Template, 4, 3, \"div\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r7.contacts && item_r7.contacts.length);\n  }\n}\nfunction IdentifyAccountComponent_p_table_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 44);\n    i0.ɵɵtemplate(1, IdentifyAccountComponent_p_table_23_ng_template_1_Template, 12, 0, \"ng-template\", 45)(2, IdentifyAccountComponent_p_table_23_ng_template_2_Template, 17, 9, \"ng-template\", 46)(3, IdentifyAccountComponent_p_table_23_ng_template_3_Template, 3, 1, \"ng-template\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r0.data)(\"rows\", 8)(\"paginator\", true);\n  }\n}\nexport class IdentifyAccountComponent {\n  constructor(renderer, messageservice, router, fb, service, ticketService) {\n    this.renderer = renderer;\n    this.messageservice = messageservice;\n    this.router = router;\n    this.fb = fb;\n    this.service = service;\n    this.ticketService = ticketService;\n    this.bodyClass = 'identify-account-body';\n    this.countries = (() => {\n      const allCountries = Country.getAllCountries();\n      const usaIndex = allCountries.findIndex(c => c.isoCode === 'US');\n      const canadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\n      const usa = usaIndex !== -1 ? allCountries.splice(usaIndex, 1)[0] : null;\n      // After removing USA, Canada index may shift if it was after USA\n      const newCanadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\n      const canada = newCanadaIndex !== -1 ? allCountries.splice(newCanadaIndex, 1)[0] : null;\n      const result = [];\n      if (usa) result.push(usa);\n      if (canada) result.push(canada);\n      return result.concat(allCountries);\n    })();\n    this.states = [];\n    this.cities = [];\n    this.items = [{\n      label: 'Identify Account',\n      routerLink: ['/store/identify-account']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.filterForm = this.fb.group({\n      bp_id: [''],\n      bp_name: [''],\n      // s4_hana_id: [''],\n      street: [''],\n      city: [''],\n      state: [''],\n      zip_code: [''],\n      country: [''],\n      email: [''],\n      phone: [''],\n      invoice_no: ['']\n    });\n    this.data = [];\n    this.loading = false;\n    this.selectedContact = null;\n    this.checked = false;\n    this.showDiv = false;\n    this.creatingTicket = false;\n    this.filterForm.get('country')?.valueChanges.subscribe(countryCode => {\n      if (countryCode) {\n        this.states = State.getStatesOfCountry(countryCode);\n      } else {\n        this.states = [];\n      }\n      this.filterForm.get('state')?.setValue('');\n      this.cities = [];\n      this.filterForm.get('city')?.setValue('');\n    });\n    this.filterForm.get('state')?.valueChanges.subscribe(stateCode => {\n      const countryCode = this.filterForm.get('country')?.value;\n      if (countryCode && stateCode) {\n        this.cities = City.getCitiesOfState(countryCode, stateCode);\n      } else {\n        this.cities = [];\n      }\n      this.filterForm.get('city')?.setValue('');\n    });\n  }\n  ngOnInit() {\n    this.renderer.addClass(document.body, this.bodyClass);\n    this.filterForm.get('country')?.valueChanges.subscribe(countryCode => {\n      if (countryCode) {\n        this.states = State.getStatesOfCountry(countryCode);\n      } else {\n        this.states = [];\n      }\n      this.filterForm.get('state')?.setValue('');\n    });\n  }\n  search() {\n    const obj = {\n      populate: ['roles'],\n      filters: {\n        $and: [{\n          roles: {\n            bp_role: {\n              $in: ['FLCU00', 'FLCU01', 'BUP001']\n            }\n          }\n        }]\n      }\n    };\n    if (this.filterForm.value.bp_id) {\n      obj.filters.$and.push({\n        'bp_id': {\n          $eqi: this.filterForm.value.bp_id || ''\n        }\n      });\n    }\n    if (this.filterForm.value.bp_name) {\n      obj.filters.$and.push({\n        'bp_full_name': {\n          $containsi: this.filterForm.value.bp_name || ''\n        }\n      });\n    }\n    if (this.filterForm.value.street) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            street_name: {\n              $containsi: this.filterForm.value.street || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.city) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            city_name: {\n              $containsi: this.filterForm.value.city || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.state) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            region: {\n              $containsi: this.filterForm.value.state || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.zip_code) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            postal_code: {\n              $containsi: this.filterForm.value.zip_code || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.country) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            country: {\n              $containsi: this.filterForm.value.country || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.email) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            emails: {\n              email_address: {\n                $containsi: this.filterForm.value.email || ''\n              }\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.phone) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            phone_numbers: {\n              phone_number: {\n                $containsi: this.filterForm.value.phone || ''\n              }\n            }\n          }\n        }\n      });\n    }\n    const params = stringify(obj);\n    this.loading = true;\n    this.data = [];\n    this.selectedContact = null;\n    this.service.search(params).pipe(switchMap(res => {\n      if (res?.length) {\n        const bpIds = [];\n        const contactBPIs = [];\n        for (let i = 0; i < res.length; i++) {\n          const bp = res[i];\n          const contactRole = bp.roles.find(role => role.bp_role == 'BUP001');\n          if (contactRole) {\n            contactBPIs.push(bp.bp_id);\n          } else {\n            bpIds.push(bp.bp_id);\n          }\n        }\n        if (!contactBPIs.length) {\n          return of(bpIds);\n        }\n        const params = stringify({\n          filters: {\n            $and: [{\n              bp_person_id: {\n                $in: contactBPIs\n              }\n            }]\n          }\n        });\n        return this.service.getAccountDetailsByContact(params).pipe(map(contactDetails => {\n          if (!contactDetails?.length) {\n            return bpIds;\n          }\n          for (let index = 0; index < contactDetails.length; index++) {\n            const element = contactDetails[index];\n            if (!bpIds.includes(element.bp_company_id)) {\n              bpIds.push(element.bp_company_id);\n            }\n          }\n          return bpIds;\n        }));\n      } else {\n        return of([]);\n      }\n    }), switchMap(bpIds => {\n      if (!bpIds.length) {\n        return of([]);\n      }\n      const params = stringify({\n        filters: {\n          $and: [{\n            bp_id: {\n              $in: bpIds\n            }\n          }]\n        },\n        populate: {\n          address_usages: {\n            fields: ['address_usage'],\n            populate: {\n              business_partner_address: {\n                fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\n                populate: {\n                  emails: {\n                    fields: ['email_address']\n                  },\n                  phone_numbers: {\n                    fields: ['phone_number']\n                  }\n                }\n              }\n            }\n          },\n          contact_companies: {\n            populate: {\n              business_partner_person: {\n                fields: ['bp_id', 'first_name', 'last_name', 'is_marked_for_archiving'],\n                populate: {\n                  addresses: {\n                    populate: '*'\n                  }\n                }\n              }\n            }\n          }\n        }\n      });\n      return this.service.search(params);\n    })).subscribe(res => {\n      this.data = this.formatData(res);\n      this.loading = false;\n    }, () => {\n      this.loading = false;\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Error while processing your request.'\n      });\n    });\n  }\n  getContactDetails(addresses) {\n    if (!addresses?.length || !addresses[0].business_partner_address) {\n      return {\n        address: '',\n        phoneNo: '',\n        email: ''\n      };\n    }\n    const address = addresses[0].business_partner_address;\n    if (!address) {\n      return {\n        address: '',\n        phoneNo: '',\n        email: ''\n      };\n    }\n    return this.getAddress(address);\n  }\n  getAddress(address) {\n    if (!address) {\n      return {\n        address: '',\n        phoneNo: '',\n        email: ''\n      };\n    }\n    return {\n      address: `${address.street_name || ''}, ${address.postal_code || ''}, ${address.city_name || ''}, ${address.region || ''}, ${address.country || ''}`,\n      phoneNo: address?.phone_numbers?.length ? address.phone_numbers[0].phone_number : '',\n      email: address?.emails?.length ? address.emails[0].email_address : ''\n    };\n  }\n  getContact(contacts) {\n    const data = [];\n    for (let i = 0; i < contacts.length; i++) {\n      const contact = contacts[i];\n      if (contact.business_partner_person) {\n        const person = contact.business_partner_person;\n        if (person.is_marked_for_archiving === false) {\n          data.push({\n            bp_id: person.bp_id,\n            bp_company_id: contact.bp_company_id,\n            first_name: person.first_name || '',\n            last_name: person.last_name || '',\n            status: 'ACTIVE',\n            ...this.getAddress(person.addresses[0])\n          });\n        }\n      }\n    }\n    return data;\n  }\n  formatData(data) {\n    return data.map(item => {\n      return {\n        bp_id: item.bp_id,\n        bp_full_name: item.bp_full_name,\n        ...this.getContactDetails(item.address_usages),\n        contacts: this.getContact(item.contact_companies || [])\n      };\n    });\n  }\n  clear() {\n    this.filterForm.reset();\n  }\n  reset() {\n    this.data = [];\n    this.selectedContact = null;\n  }\n  getInitials(name) {\n    return name.trim().split(/\\s+/) // split by spaces\n    .slice(0, 2) // only take first two words\n    .map(word => word[0].toUpperCase()).join('');\n  }\n  toggleDiv() {\n    this.showDiv = !this.showDiv;\n  }\n  createTicket() {\n    if (!this.selectedContact) {\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Please select a contact.'\n      });\n      return;\n    }\n    const data = {\n      account_id: this.selectedContact.bp_company_id,\n      contact_id: this.selectedContact.bp_id,\n      status_id: 'NEW'\n    };\n    this.creatingTicket = true;\n    this.ticketService.createTicket({\n      data\n    }).subscribe(response => {\n      this.creatingTicket = false;\n      if (response?.data?.documentId) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Ticket created successfully.'\n        });\n        const params = stringify({\n          filters: {\n            $and: [{\n              bp_id: {\n                $in: [this.selectedContact.bp_company_id]\n              }\n            }]\n          }\n        });\n        this.service.search(params).subscribe(res => {\n          if (res?.length) {\n            this.router.navigate(['/store/service-ticket-details', response?.data?.id, res[0].documentId]);\n          }\n        });\n      }\n    }, () => {\n      this.creatingTicket = false;\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Error while creating the ticket.'\n      });\n    });\n  }\n  static {\n    this.ɵfac = function IdentifyAccountComponent_Factory(t) {\n      return new (t || IdentifyAccountComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AccountService), i0.ɵɵdirectiveInject(i5.ServiceTicketService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IdentifyAccountComponent,\n      selectors: [[\"app-identify-account\"]],\n      features: [i0.ɵɵProvidersFeature([MessageService])],\n      decls: 24,\n      vars: 12,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"mt-3\", \"flex\", \"flex-column\", \"gap-3\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [\"class\", \"grid mt-0\", 4, \"ngIf\"], [1, \"account-sec\", \"w-full\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-4\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\", \"mt-3\"], [1, \"acc-title\", \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"header-title\", \"m-0\", \"pl-3\", \"relative\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"mt-3 border-none border-top-1 border-solid border-gray-50\", 4, \"ngIf\"], [1, \"search-result\", \"mt-3\", \"w-full\"], [1, \"acc-title\", \"mb-3\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-gray-50\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [3, \"click\", \"outlined\", \"disabled\", \"styleClass\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [4, \"ngIf\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\", 4, \"ngIf\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\"], [1, \"identify-name-box\", \"px-3\", \"flex\", \"align-items-center\", \"w-full\", \"h-4rem\", \"surface-b\", \"border-round\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"gap-2\", \"text-lg\", \"font-semibold\", \"text-primary\"], [1, \"mt-3\", \"border-none\", \"border-top-1\", \"border-solid\", \"border-gray-50\"], [1, \"account-p-tabs\", \"relative\", 3, \"formGroup\"], [1, \"acc-tab-info\", \"pb-2\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-1\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_name\", \"placeholder\", \"Account Name\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_id\", \"placeholder\", \"CRM ID\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"street\", \"placeholder\", \"Street\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"formControlName\", \"country\", \"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"dataKey\", \"isoCode\", \"placeholder\", \"Select Country\", \"styleClass\", \"h-2-8rem w-full\", 3, \"options\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\", \"pt-0\"], [\"formControlName\", \"state\", \"optionLabel\", \"name\", \"optionValue\", \"isoCode\", \"dataKey\", \"isoCode\", \"styleClass\", \"h-2-8rem w-full\", \"placeholder\", \"Select State\", 3, \"options\"], [\"formControlName\", \"city\", \"optionLabel\", \"name\", \"optionValue\", \"name\", \"dataKey\", \"name\", \"styleClass\", \"h-2-8rem w-full\", \"placeholder\", \"Select City\", 3, \"options\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"zip_code\", \"placeholder\", \"Zip Code\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"phone\", \"placeholder\", \"Telephone\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"invoice_no\", \"placeholder\", \"Invoice #\", 1, \"p-inputtext\", \"h-2-8rem\", \"w-full\"], [1, \"acc-title\", \"flex\", \"align-items-center\", \"justify-content-between\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-red-100\", \"border-none\", \"text-red-500\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"font-medium\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", \"gap-1\", 3, \"click\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [1, \"border-round-left-lg\", 2, \"width\", \"3rem\"], [1, \"border-round-left-lg\"], [\"type\", \"button\", \"pButton\", \"\", \"pRowToggler\", \"\", 1, \"p-button-text\", \"p-button-plain\", \"p-button-sm\", 3, \"pRowToggler\", \"icon\", \"disabled\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-2rem\", \"h-2rem\", \"bg-blue-500\", \"border-circle\", \"font-semibold\", \"text-white\"], [1, \"m-0\", \"text-base\", \"text-900\", \"w-20rem\", \"text-overflow-ellipsis\"], [\"colspan\", \"6\", 1, \"border-round-right-lg\"], [\"class\", \"p-3\", 4, \"ngIf\"], [1, \"p-3\"], [\"dataKey\", \"id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [1, \"border-round-right-lg\"], [\"type\", \"radio\", 1, \"custom-ratio-btn\", 3, \"change\", \"name\", \"value\", \"checked\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\"]],\n      template: function IdentifyAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, IdentifyAccountComponent_div_4_Template, 7, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_div_click_6_listener() {\n            return ctx.toggleDiv();\n          });\n          i0.ɵɵelementStart(7, \"h5\", 7);\n          i0.ɵɵtext(8, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 8)(10, \"span\", 9);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, IdentifyAccountComponent_div_12_Template, 88, 6, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"h5\", 7);\n          i0.ɵɵtext(16, \"Result List\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"p-button\", 14);\n          i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_p_button_click_18_listener() {\n            return ctx.createTicket();\n          });\n          i0.ɵɵelementStart(19, \"i\", 15);\n          i0.ɵɵtext(20, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(22, IdentifyAccountComponent_p_22_Template, 2, 0, \"p\", 16)(23, IdentifyAccountComponent_p_table_23_Template, 4, 3, \"p-table\", 17);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedContact);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(!ctx.showDiv ? \"keyboard_arrow_down\" : \"keyboard_arrow_up\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showDiv);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"outlined\", true)(\"disabled\", !ctx.selectedContact || ctx.creatingTicket)(\"styleClass\", \"flex align-items-center justify-content-center gap-2 text-color-secondary\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.creatingTicket ? \"Confirming...\" : \"Confirm\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.data.length && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.data.length);\n        }\n      },\n      dependencies: [i6.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i7.Breadcrumb, i1.PrimeTemplate, i8.Dropdown, i9.Table, i9.RowToggler, i10.ButtonDirective, i10.Button, i11.InputText],\n      styles: [\".identify-account-body .topbar-start h1 {\\n  display: none;\\n}\\n  .min-width {\\n  min-width: 18rem;\\n}\\n  .custom-ratio-btn {\\n  width: 16px;\\n  height: 16px;\\n  accent-color: var(--primary-500);\\n}\\n  .search-result p-accordion p-accordiontab {\\n  margin: 0 0 4px 0 !important;\\n  display: flex;\\n}\\n  .search-result p-accordion p-accordiontab:last-child {\\n  margin: 0 0 0px 0 !important;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-tab {\\n  width: 100%;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link {\\n  border: none;\\n  flex-direction: row-reverse;\\n  width: 100%;\\n  justify-content: space-between;\\n  border-radius: 8px;\\n  min-height: 48px;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link span.p-accordion-toggle-icon {\\n  margin: 0;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link:hover {\\n  box-shadow: 0 1px 3px var(--surface-100);\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-content {\\n  border-radius: 8px;\\n  border: 1px solid var(--surface-b);\\n}\\n  .search-result p-accordion p-accordiontab:nth-child(odd) .p-accordion-header .p-accordion-header-link {\\n  background: var(--surface-b);\\n}\\n  .search-result p-accordion p-accordiontab:nth-child(even) .p-accordion-header .p-accordion-header-link {\\n  background: var(--surface-0);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MessageService", "stringify", "map", "of", "switchMap", "Country", "State", "City", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "selectedContact", "first_name", "last_name", "ɵɵelement", "ɵɵlistener", "IdentifyAccountComponent_div_12_Template_button_click_76_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "search", "IdentifyAccountComponent_div_12_Template_button_click_80_listener", "clear", "IdentifyAccountComponent_div_12_Template_button_click_84_listener", "reset", "ɵɵproperty", "filterForm", "countries", "states", "cities", "loading", "ɵɵtextInterpolate1", "item_r3", "expanded_r4", "contacts", "length", "ɵɵtextInterpolate", "getInitials", "bp_full_name", "bp_id", "email", "phoneNo", "address", "IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_3_Template_input_change_2_listener", "tableinfo_r6", "_r5", "$implicit", "ɵɵpropertyInterpolate1", "item_r7", "status", "ɵɵtemplate", "IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_2_Template", "IdentifyAccountComponent_p_table_23_ng_template_3_div_2_ng_template_3_Template", "IdentifyAccountComponent_p_table_23_ng_template_3_div_2_Template", "IdentifyAccountComponent_p_table_23_ng_template_1_Template", "IdentifyAccountComponent_p_table_23_ng_template_2_Template", "IdentifyAccountComponent_p_table_23_ng_template_3_Template", "data", "IdentifyAccountComponent", "constructor", "renderer", "messageservice", "router", "fb", "service", "ticketService", "bodyClass", "allCountries", "getAllCountries", "usaIndex", "findIndex", "c", "isoCode", "canadaIndex", "usa", "splice", "newCanadaIndex", "canada", "result", "push", "concat", "items", "label", "routerLink", "home", "icon", "group", "bp_name", "street", "city", "state", "zip_code", "country", "phone", "invoice_no", "checked", "showDiv", "creatingTicket", "get", "valueChanges", "subscribe", "countryCode", "getStatesOfCountry", "setValue", "stateCode", "value", "getCitiesOfState", "ngOnInit", "addClass", "document", "body", "obj", "populate", "filters", "$and", "roles", "bp_role", "$in", "$eqi", "$containsi", "business_partner_address", "street_name", "city_name", "region", "postal_code", "emails", "email_address", "phone_numbers", "phone_number", "params", "pipe", "res", "bpIds", "contactBPIs", "i", "bp", "contactRole", "find", "role", "bp_person_id", "getAccountDetailsByContact", "contactDetails", "index", "element", "includes", "bp_company_id", "address_usages", "fields", "contact_companies", "business_partner_person", "addresses", "formatData", "add", "severity", "detail", "getContactDetails", "get<PERSON><PERSON><PERSON>", "getContact", "contact", "person", "is_marked_for_archiving", "item", "name", "trim", "split", "slice", "word", "toUpperCase", "join", "toggleDiv", "createTicket", "account_id", "contact_id", "status_id", "response", "documentId", "navigate", "id", "ɵɵdirectiveInject", "Renderer2", "i1", "i2", "Router", "i3", "FormBuilder", "i4", "AccountService", "i5", "ServiceTicketService", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "IdentifyAccountComponent_Template", "rf", "ctx", "IdentifyAccountComponent_div_4_Template", "IdentifyAccountComponent_Template_div_click_6_listener", "IdentifyAccountComponent_div_12_Template", "IdentifyAccountComponent_Template_p_button_click_18_listener", "IdentifyAccountComponent_p_22_Template", "IdentifyAccountComponent_p_table_23_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { AccountService } from '../account/account.service';\r\nimport { stringify } from \"qs\";\r\nimport { FormBuilder } from '@angular/forms';\r\nimport { map, of, switchMap } from 'rxjs';\r\nimport { ServiceTicketService } from '../services/service-ticket.service';\r\nimport { Country, State, City } from 'country-state-city';\r\nimport { Router } from '@angular/router';\r\n@Component({\r\n  selector: 'app-identify-account',\r\n  templateUrl: './identify-account.component.html',\r\n  styleUrl: './identify-account.component.scss',\r\n  providers: [MessageService]\r\n})\r\nexport class IdentifyAccountComponent {\r\n\r\n  private bodyClass = 'identify-account-body';\r\n\r\n  countries = (() => {\r\n    const allCountries = Country.getAllCountries();\r\n    const usaIndex = allCountries.findIndex(c => c.isoCode === 'US');\r\n    const canadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\r\n    const usa = usaIndex !== -1 ? allCountries.splice(usaIndex, 1)[0] : null;\r\n    // After removing USA, Canada index may shift if it was after USA\r\n    const newCanadaIndex = allCountries.findIndex(c => c.isoCode === 'CA');\r\n    const canada = newCanadaIndex !== -1 ? allCountries.splice(newCanadaIndex, 1)[0] : null;\r\n    const result = [];\r\n    if (usa) result.push(usa);\r\n    if (canada) result.push(canada);\r\n    return result.concat(allCountries);\r\n  })();\r\n  states: any[] = [];\r\n  cities: any[] = [];\r\n  items: MenuItem[] | any = [\r\n    { label: 'Identify Account', routerLink: ['/store/identify-account'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n  filterForm = this.fb.group({\r\n    bp_id: [''],\r\n    bp_name: [''],\r\n    // s4_hana_id: [''],\r\n    street: [''],\r\n    city: [''],\r\n    state: [''],\r\n    zip_code: [''],\r\n    country: [''],\r\n    email: [''],\r\n    phone: [''],\r\n    invoice_no: [''],\r\n  });\r\n  data: any[] = [];\r\n  loading: boolean = false;\r\n\r\n  selectedContact: any = null;\r\n\r\n  constructor(\r\n    private renderer: Renderer2,\r\n    private messageservice: MessageService,\r\n    private router: Router,\r\n    private fb: FormBuilder,\r\n    private service: AccountService,\r\n    private ticketService: ServiceTicketService,\r\n  ) {\r\n    this.filterForm.get('country')?.valueChanges.subscribe((countryCode) => {\r\n      if (countryCode) {\r\n        this.states = State.getStatesOfCountry(countryCode);\r\n      } else {\r\n        this.states = [];\r\n      }\r\n      this.filterForm.get('state')?.setValue('');\r\n      this.cities = [];\r\n      this.filterForm.get('city')?.setValue('');\r\n    });\r\n    this.filterForm.get('state')?.valueChanges.subscribe((stateCode) => {\r\n      const countryCode = this.filterForm.get('country')?.value;\r\n      if (countryCode && stateCode) {\r\n        this.cities = City.getCitiesOfState(countryCode, stateCode);\r\n      } else {\r\n        this.cities = [];\r\n      }\r\n      this.filterForm.get('city')?.setValue('');\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.renderer.addClass(document.body, this.bodyClass);\r\n    this.filterForm.get('country')?.valueChanges.subscribe((countryCode) => {\r\n      if (countryCode) {\r\n        this.states = State.getStatesOfCountry(countryCode);\r\n      } else {\r\n        this.states = [];\r\n      }\r\n      this.filterForm.get('state')?.setValue('');\r\n    });\r\n  }\r\n\r\n  checked: boolean = false;\r\n\r\n  search() {\r\n    const obj: any = {\r\n      populate: ['roles'],\r\n      filters: {\r\n        $and: [\r\n          {\r\n            roles: {\r\n              bp_role: {\r\n                $in: ['FLCU00', 'FLCU01', 'BUP001']\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n    if (this.filterForm.value.bp_id) {\r\n      obj.filters.$and.push({\r\n        'bp_id': {\r\n          $eqi: this.filterForm.value.bp_id || ''\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.bp_name) {\r\n      obj.filters.$and.push({\r\n        'bp_full_name': {\r\n          $containsi: this.filterForm.value.bp_name || ''\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.street) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            street_name: {\r\n              $containsi: this.filterForm.value.street || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.city) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            city_name: {\r\n              $containsi: this.filterForm.value.city || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.state) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            region: {\r\n              $containsi: this.filterForm.value.state || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.zip_code) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            postal_code: {\r\n              $containsi: this.filterForm.value.zip_code || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.country) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            country: {\r\n              $containsi: this.filterForm.value.country || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.email) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            emails: {\r\n              email_address: {\r\n                $containsi: this.filterForm.value.email || ''\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.phone) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            phone_numbers: {\r\n              phone_number: {\r\n                $containsi: this.filterForm.value.phone || ''\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    const params = stringify(obj);\r\n    this.loading = true;\r\n    this.data = [];\r\n    this.selectedContact = null;\r\n    this.service.search(params).pipe(\r\n      switchMap((res: any) => {\r\n        if (res?.length) {\r\n          const bpIds: string[] = [];\r\n          const contactBPIs = [];\r\n          for (let i = 0; i < res.length; i++) {\r\n            const bp = res[i];\r\n            const contactRole = bp.roles.find((role: any) => role.bp_role == 'BUP001');\r\n            if (contactRole) {\r\n              contactBPIs.push(bp.bp_id);\r\n            } else {\r\n              bpIds.push(bp.bp_id);\r\n            }\r\n          }\r\n          if (!contactBPIs.length) {\r\n            return of(bpIds);\r\n          }\r\n          const params = stringify({\r\n            filters: {\r\n              $and: [\r\n                {\r\n                  bp_person_id: {\r\n                    $in: contactBPIs\r\n                  }\r\n                }\r\n              ]\r\n            }\r\n          });\r\n          return this.service.getAccountDetailsByContact(params).pipe(\r\n            map((contactDetails: any) => {\r\n              if (!contactDetails?.length) {\r\n                return bpIds;\r\n              }\r\n              for (let index = 0; index < contactDetails.length; index++) {\r\n                const element = contactDetails[index];\r\n                if (!bpIds.includes(element.bp_company_id)) {\r\n                  bpIds.push(element.bp_company_id);\r\n                }\r\n              }\r\n              return bpIds;\r\n            })\r\n          );\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }),\r\n      switchMap((bpIds: string[]) => {\r\n        if (!bpIds.length) {\r\n          return of([]);\r\n        }\r\n        const params = stringify({\r\n          filters: {\r\n            $and: [\r\n              {\r\n                bp_id: {\r\n                  $in: bpIds\r\n                }\r\n              }\r\n            ]\r\n          },\r\n          populate: {\r\n            address_usages: {\r\n              fields: ['address_usage'],\r\n              populate: {\r\n                business_partner_address: {\r\n                  fields: ['street_name', 'postal_code', 'city_name', 'region', 'country'],\r\n                  populate: {\r\n                    emails: {\r\n                      fields: ['email_address']\r\n                    },\r\n                    phone_numbers: {\r\n                      fields: ['phone_number']\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            contact_companies: {\r\n              populate: {\r\n                business_partner_person: {\r\n                  fields: ['bp_id', 'first_name', 'last_name', 'is_marked_for_archiving'],\r\n                  populate: {\r\n                    addresses: {\r\n                      populate: '*'\r\n                    },\r\n                  }\r\n                }\r\n              }\r\n            },\r\n\r\n          }\r\n        });\r\n        return this.service.search(params);\r\n      })\r\n    ).subscribe((res: any) => {\r\n      this.data = this.formatData(res);\r\n      this.loading = false;\r\n    }, () => {\r\n      this.loading = false;\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail: 'Error while processing your request.',\r\n      });\r\n    });\r\n  }\r\n\r\n  getContactDetails(addresses: any[]) {\r\n    if (!addresses?.length || !addresses[0].business_partner_address) {\r\n      return {\r\n        address: '',\r\n        phoneNo: '',\r\n        email: ''\r\n      }\r\n    }\r\n    const address = addresses[0].business_partner_address;\r\n    if (!address) {\r\n      return {\r\n        address: '',\r\n        phoneNo: '',\r\n        email: ''\r\n      }\r\n    }\r\n    return this.getAddress(address);\r\n  }\r\n\r\n  getAddress(address: any) {\r\n    if (!address) {\r\n      return {\r\n        address: '',\r\n        phoneNo: '',\r\n        email: ''\r\n      };\r\n    }\r\n    return {\r\n      address: `${address.street_name || ''}, ${address.postal_code || ''}, ${address.city_name || ''}, ${address.region || ''}, ${address.country || ''}`,\r\n      phoneNo: address?.phone_numbers?.length ? address.phone_numbers[0].phone_number : '',\r\n      email: address?.emails?.length ? address.emails[0].email_address : ''\r\n    }\r\n  }\r\n\r\n  getContact(contacts: any[]) {\r\n    const data: any[] = [];\r\n    for (let i = 0; i < contacts.length; i++) {\r\n      const contact = contacts[i];\r\n      if (contact.business_partner_person) {\r\n        const person = contact.business_partner_person;\r\n        if (person.is_marked_for_archiving === false) {\r\n          data.push({\r\n            bp_id: person.bp_id,\r\n            bp_company_id: contact.bp_company_id,\r\n            first_name: person.first_name || '',\r\n            last_name: person.last_name || '',\r\n            status: 'ACTIVE',\r\n            ...this.getAddress(person.addresses[0])\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return data;\r\n  }\r\n\r\n  formatData(data: any[]) {\r\n    return data.map((item: any) => {\r\n      return {\r\n        bp_id: item.bp_id,\r\n        bp_full_name: item.bp_full_name,\r\n        ...this.getContactDetails(item.address_usages),\r\n        contacts: this.getContact(item.contact_companies || [])\r\n      }\r\n    });\r\n  }\r\n\r\n  clear() {\r\n    this.filterForm.reset();\r\n  }\r\n\r\n  reset() {\r\n    this.data = [];\r\n    this.selectedContact = null;\r\n  }\r\n\r\n  getInitials(name: string) {\r\n    return name\r\n      .trim()\r\n      .split(/\\s+/) // split by spaces\r\n      .slice(0, 2) // only take first two words\r\n      .map(word => word[0].toUpperCase())\r\n      .join('');\r\n  }\r\n\r\n  showDiv = false;\r\n\r\n  toggleDiv() {\r\n    this.showDiv = !this.showDiv;\r\n  }\r\n\r\n  creatingTicket = false;\r\n  createTicket() {\r\n    if (!this.selectedContact) {\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail: 'Please select a contact.',\r\n      });\r\n      return;\r\n    }\r\n    const data = {\r\n      account_id: this.selectedContact.bp_company_id,\r\n      contact_id: this.selectedContact.bp_id,\r\n      status_id: 'NEW',\r\n    };\r\n    this.creatingTicket = true;\r\n    this.ticketService.createTicket({ data }).subscribe((response) => {\r\n      this.creatingTicket = false;\r\n      if (response?.data?.documentId) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Ticket created successfully.',\r\n        });\r\n        const params = stringify({\r\n          filters: {\r\n            $and: [\r\n              {\r\n                bp_id: {\r\n                  $in: [this.selectedContact.bp_company_id]\r\n                }\r\n              }\r\n            ]\r\n          },\r\n        });\r\n        this.service.search(params).subscribe((res: any) => {\r\n          if (res?.length) {\r\n            this.router.navigate(['/store/service-ticket-details', response?.data?.id, res[0].documentId]);\r\n          }\r\n        });\r\n      }\r\n    }, () => {\r\n      this.creatingTicket = false;\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail: 'Error while creating the ticket.',\r\n      });\r\n    });\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec mt-3 flex  flex-column gap-3\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"grid mt-0\" *ngIf=\"selectedContact\">\r\n            <div class=\"col-12\">\r\n                <div\r\n                    class=\"identify-name-box px-3 flex align-items-center w-full h-4rem surface-b border-round border-1 border-solid border-50\">\r\n                    <h5 class=\"m-0 flex align-items-center gap-2 text-lg font-semibold text-primary\">\r\n                        <i class=\"material-symbols-rounded\">person</i> {{ selectedContact?.first_name }} {{\r\n                        selectedContact?.last_name }}\r\n                    </h5>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"account-sec w-full shadow-1 border-round-xl surface-0 p-4 mb-4 border-1 border-solid border-50 mt-3\">\r\n        <div class=\"acc-title flex align-items-center justify-content-between cursor-pointer\" (click)=\"toggleDiv()\">\r\n            <h5 class=\"header-title m-0 pl-3 relative\">Account</h5>\r\n            <!-- <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-9rem flex align-items-center justify-content-center gap-2 text-primary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">variable_add</i> More Fields\r\n                </p-button>\r\n            </div> -->\r\n            <button\r\n                class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\">\r\n                <span class='material-symbols-rounded'>{{ !showDiv ? 'keyboard_arrow_down' : 'keyboard_arrow_up'\r\n                    }}</span>\r\n            </button>\r\n        </div>\r\n        <div *ngIf=\"!showDiv\" class=\"mt-3 border-none border-top-1 border-solid border-gray-50\">\r\n            <form class=\"account-p-tabs relative\" [formGroup]=\"filterForm\">\r\n                <div class=\"acc-tab-info pb-2\">\r\n                    <div class=\"grid mt-0\">\r\n                        <div class=\"col-12 lg:col-3 md:col-3\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">person</span> Account\r\n                                    Name\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"bp_name\" placeholder=\"Account Name\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">tag</span> CRM ID\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"bp_id\" placeholder=\"CRM ID\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <!-- <div class=\"col-12 lg:col-3 md:col-3\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"text-500 font-medium\">S4/HANA ID</label>\r\n                                <input pInputText id=\"username\" formControlName=\"s4_hana_id\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div> -->\r\n                        <div class=\"col-12 lg:col-3 md:col-3\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">location_on</span> Street\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"street\" placeholder=\"Street\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">public</span> Country\r\n                                </label>\r\n                                <p-dropdown [options]=\"countries\" formControlName=\"country\" optionLabel=\"name\"\r\n                                    optionValue=\"isoCode\" dataKey=\"isoCode\" placeholder=\"Select Country\"\r\n                                    styleClass=\"h-2-8rem w-full\"></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">my_location</span> State\r\n                                </label>\r\n                                <p-dropdown [options]=\"states\" formControlName=\"state\" optionLabel=\"name\"\r\n                                    optionValue=\"isoCode\" dataKey=\"isoCode\" styleClass=\"h-2-8rem w-full\"\r\n                                    placeholder=\"Select State\"></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">map</span> City\r\n                                </label>\r\n                                <p-dropdown [options]=\"cities\" formControlName=\"city\" optionLabel=\"name\"\r\n                                    optionValue=\"name\" dataKey=\"name\" styleClass=\"h-2-8rem w-full\"\r\n                                    placeholder=\"Select City\"></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">code</span> Zip Code\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"zip_code\" placeholder=\"Zip Code\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">mail</span> Email\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"email\" placeholder=\"Email\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">phone_in_talk</span>\r\n                                    Telephone\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"phone\" placeholder=\"Telephone\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                            <div class=\"flex flex-column gap-2\">\r\n                                <label for=\"username\" class=\"flex align-items-center gap-1 mb-1 font-medium\">\r\n                                    <span class=\"material-symbols-rounded text-2xl text-600\">pin</span> Invoice #\r\n                                </label>\r\n                                <input pInputText id=\"username\" formControlName=\"invoice_no\" placeholder=\"Invoice #\"\r\n                                    class=\"p-inputtext h-2-8rem w-full\" />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </form>\r\n            <div class=\"acc-title flex align-items-center justify-content-between\">\r\n                <div class=\"flex align-items-center gap-3\">\r\n                    <button pButton type=\"submit\" (click)=\"search()\" [disabled]=\"loading\"\r\n                        class=\"p-button-rounded justify-content-center w-9rem h-3rem gap-1\">\r\n                        <i class=\"material-symbols-rounded text-2xl\">search</i> {{ loading ? 'Searching...': 'Search'}}\r\n                    </button>\r\n                    <button pButton type=\"button\" (click)=\"clear()\"\r\n                        class=\"p-button-rounded bg-red-100 border-none text-red-500 font-medium justify-content-center w-9rem h-3rem gap-1\">\r\n                        <i class=\"material-symbols-rounded text-2xl\">cancel</i> Clear\r\n                    </button>\r\n                    <button pButton type=\"button\" (click)=\"reset()\"\r\n                        class=\"p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem gap-1\">\r\n                        <i class=\"material-symbols-rounded text-2xl\">rule_settings</i> Reset\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"search-result mt-3 w-full\">\r\n        <div\r\n            class=\"acc-title mb-3 pb-3 flex align-items-center justify-content-between border-none border-bottom-1 border-solid border-gray-50\">\r\n            <h5 class=\"header-title m-0 pl-3 relative\">Result List</h5>\r\n            <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\" [disabled]=\"!selectedContact || creatingTicket\" (click)=\"createTicket()\"\r\n                    [styleClass]=\"'flex align-items-center justify-content-center gap-2 text-color-secondary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">check_circle</i> {{ creatingTicket ? 'Confirming...':\r\n                    'Confirm' }}\r\n                </p-button>\r\n            </div>\r\n        </div>\r\n\r\n        <p *ngIf=\"!data.length && !loading\">No records found.</p>\r\n\r\n        <p-table *ngIf=\"data.length\" [value]=\"data\" dataKey=\"bp_id\" [rows]=\"8\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\" style=\"width: 3rem\"></th>\r\n                    <th>Account Name</th>\r\n                    <th>Account Id</th>\r\n                    <th>Email</th>\r\n                    <th>Phone</th>\r\n                    <th>Address</th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-item let-expanded=\"expanded\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        <button type=\"button\" pButton pRowToggler [pRowToggler]=\"item\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"\r\n                            [disabled]=\"!item.contacts || !item.contacts.length\"\r\n                            class=\"p-button-text p-button-plain p-button-sm\"></button>\r\n                    </td>\r\n                    <td>\r\n                        <span class=\"flex align-items-center gap-2\">\r\n                            <span\r\n                                class=\"flex align-items-center justify-content-center w-2rem h-2rem bg-blue-500 border-circle font-semibold text-white\">{{\r\n                                getInitials(item.bp_full_name) }}</span>\r\n                            <span class=\"m-0 text-base text-900 w-20rem text-overflow-ellipsis\">{{ item.bp_full_name\r\n                                }}</span>\r\n                        </span>\r\n                    </td>\r\n                    <td>{{ item.bp_id }}</td>\r\n                    <td>{{ item.email }}</td>\r\n                    <td>{{ item.phoneNo }}</td>\r\n                    <td>{{ item.address }}</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-item>\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-right-lg\">\r\n                        <div *ngIf=\"item.contacts && item.contacts.length\" class=\"p-3\">\r\n                            <p-table [value]=\"item.contacts\" dataKey=\"id\" [rows]=\"8\" styleClass=\"w-full\"\r\n                                [paginator]=\"true\" responsiveLayout=\"scroll\">\r\n                                <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\"></th>\r\n                    <th>ID #</th>\r\n                    <th>First name</th>\r\n                    <th>Last name</th>\r\n                    <th>Email Id</th>\r\n                    <th>Phone no</th>\r\n                    <th class=\"border-round-right-lg\">Status</th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-tableinfo>\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\">\r\n                        <input type=\"radio\" class=\"custom-ratio-btn\" name=\"contactSelection{{item.bp_id}}\"\r\n                            [value]=\"tableinfo\" [checked]=\"selectedContact?.bp_id === tableinfo.bp_id\"\r\n                            (change)=\"selectedContact = tableinfo\" />\r\n                    </td>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline\">\r\n                        {{ tableinfo.bp_id }}\r\n                    </td>\r\n                    <td>{{ tableinfo.first_name }}</td>\r\n                    <td>{{ tableinfo.last_name }}</td>\r\n                    <td>{{ tableinfo.email }}</td>\r\n                    <td>{{ tableinfo.phoneNo }}</td>\r\n                    <td class=\"border-round-right-lg\">{{ tableinfo.status }}</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n    </td>\r\n    </tr>\r\n    </ng-template>\r\n    </p-table>\r\n</div>\r\n</div>"], "mappings": "AACA,SAAmBA,cAAc,QAAQ,aAAa;AAEtD,SAASC,SAAS,QAAQ,IAAI;AAE9B,SAASC,GAAG,EAAEC,EAAE,EAAEC,SAAS,QAAQ,MAAM;AAEzC,SAASC,OAAO,EAAEC,KAAK,EAAEC,IAAI,QAAQ,oBAAoB;;;;;;;;;;;;;;;ICGjCC,EALhB,CAAAC,cAAA,cAA+C,cACvB,cAEgH,aAC3C,WACzC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,GAEnD;IAGZF,EAHY,CAAAG,YAAA,EAAK,EACH,EACJ,EACJ;;;;IALyDH,EAAA,CAAAI,SAAA,GAEnD;IAFmDJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,UAAA,OAAAF,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,SAAA,MAEnD;;;;;;IA2BgBT,EAP5B,CAAAC,cAAA,cAAwF,eACrB,cAC5B,cACJ,cACmB,cACE,gBAC6C,eAChB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAE3E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAsC,eACE,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAWMH,EAHZ,CAAAC,cAAA,eAAsC,eACE,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAAsC,eACE,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAC3E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,sBAE8C;IAEtDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,sBAE4C;IAEpDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,cACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,sBAE2C;IAEnDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAE,MAAA,mBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAElDV,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA2C,eACH,iBAC6C,gBAChB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAU,SAAA,iBAC0C;IAK9DV,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACH;IAGCH,EAFR,CAAAC,cAAA,eAAuE,eACxB,kBAEiC;IAD1CD,EAAA,CAAAW,UAAA,mBAAAC,kEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,MAAA,EAAQ;IAAA,EAAC;IAE5CjB,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,IAC5D;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwH;IAD1FD,EAAA,CAAAW,UAAA,mBAAAO,kEAAA;MAAAlB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAa,KAAA,EAAO;IAAA,EAAC;IAE3CnB,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,eAC5D;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAC+H;IADjGD,EAAA,CAAAW,UAAA,mBAAAS,kEAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAe,KAAA,EAAO;IAAA,EAAC;IAE3CrB,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAACH,EAAA,CAAAE,MAAA,eACnE;IAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ;;;;IA5HoCH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAAiB,UAAA,CAAwB;IA2C9BvB,EAAA,CAAAI,SAAA,IAAqB;IAArBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAkB,SAAA,CAAqB;IAUrBxB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAmB,MAAA,CAAkB;IAUlBzB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAoB,MAAA,CAAkB;IA+CO1B,EAAA,CAAAI,SAAA,IAAoB;IAApBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAqB,OAAA,CAAoB;IAET3B,EAAA,CAAAI,SAAA,GAC5D;IAD4DJ,EAAA,CAAA4B,kBAAA,MAAAtB,MAAA,CAAAqB,OAAA,kCAC5D;;;;;IA0BZ3B,EAAA,CAAAC,cAAA,QAAoC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjDH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAU,SAAA,aAA0D;IAC1DV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IACfF,EADe,CAAAG,YAAA,EAAK,EACf;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACiC;IAC7BD,EAAA,CAAAU,SAAA,iBAG8D;IAClEV,EAAA,CAAAG,YAAA,EAAK;IAGGH,EAFR,CAAAC,cAAA,SAAI,eAC4C,eAEoF;IAAAD,EAAA,CAAAE,MAAA,GACvF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,GAC9D;IAEdF,EAFc,CAAAG,YAAA,EAAO,EACV,EACN;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;;;IAlB6CH,EAAA,CAAAI,SAAA,GAAoB;IAE1DJ,EAFsC,CAAAsB,UAAA,gBAAAO,OAAA,CAAoB,SAAAC,WAAA,gDACM,cAAAD,OAAA,CAAAE,QAAA,KAAAF,OAAA,CAAAE,QAAA,CAAAC,MAAA,CACZ;IAMwEhC,EAAA,CAAAI,SAAA,GACvF;IADuFJ,EAAA,CAAAiC,iBAAA,CAAA3B,MAAA,CAAA4B,WAAA,CAAAL,OAAA,CAAAM,YAAA,EACvF;IAC+BnC,EAAA,CAAAI,SAAA,GAC9D;IAD8DJ,EAAA,CAAAiC,iBAAA,CAAAJ,OAAA,CAAAM,YAAA,CAC9D;IAGVnC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiC,iBAAA,CAAAJ,OAAA,CAAAO,KAAA,CAAgB;IAChBpC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiC,iBAAA,CAAAJ,OAAA,CAAAQ,KAAA,CAAgB;IAChBrC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAiC,iBAAA,CAAAJ,OAAA,CAAAS,OAAA,CAAkB;IAClBtC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAiC,iBAAA,CAAAJ,OAAA,CAAAU,OAAA,CAAkB;;;;;IAU1BvC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAU,SAAA,aAAsC;IACtCV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC5CF,EAD4C,CAAAG,YAAA,EAAK,EAC5C;;;;;;IAKGH,EAFR,CAAAC,cAAA,SAAI,aACiC,gBAGgB;IAAzCD,EAAA,CAAAW,UAAA,oBAAA6B,uGAAA;MAAA,MAAAC,YAAA,GAAAzC,EAAA,CAAAa,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAV,MAAA,CAAAC,eAAA,GAAAkC,YAAA;IAAA,EAAsC;IAC9CzC,EAHI,CAAAG,YAAA,EAE6C,EAC5C;IACLH,EAAA,CAAAC,cAAA,aAAiE;IAC7DD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC5DF,EAD4D,CAAAG,YAAA,EAAK,EAC5D;;;;;;IAZgDH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA4C,sBAAA,6BAAAC,OAAA,CAAAT,KAAA,KAAqC;IAC1DpC,EAApB,CAAAsB,UAAA,UAAAmB,YAAA,CAAmB,aAAAnC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAA6B,KAAA,MAAAK,YAAA,CAAAL,KAAA,CAAuD;IAI9EpC,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAA4B,kBAAA,MAAAa,YAAA,CAAAL,KAAA,MACJ;IACIpC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAiC,iBAAA,CAAAQ,YAAA,CAAAjC,UAAA,CAA0B;IAC1BR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAiC,iBAAA,CAAAQ,YAAA,CAAAhC,SAAA,CAAyB;IACzBT,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiC,iBAAA,CAAAQ,YAAA,CAAAJ,KAAA,CAAqB;IACrBrC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAiC,iBAAA,CAAAQ,YAAA,CAAAH,OAAA,CAAuB;IACOtC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiC,iBAAA,CAAAQ,YAAA,CAAAK,MAAA,CAAsB;;;;;IA3BhD9C,EADJ,CAAAC,cAAA,cAA+D,kBAEV;IAYjED,EAXoB,CAAA+C,UAAA,IAAAC,8EAAA,2BAAgC,IAAAC,8EAAA,4BAWR;IAkBpDjD,EADI,CAAAG,YAAA,EAAU,EACR;;;;IA/B2BH,EAAA,CAAAI,SAAA,EAAuB;IAC5BJ,EADK,CAAAsB,UAAA,UAAAuB,OAAA,CAAAd,QAAA,CAAuB,WAAwB,mBAClC;;;;;IAH9B/B,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAC1CD,EAAA,CAAA+C,UAAA,IAAAG,gEAAA,kBAA+D;IAkCnFlD,EADA,CAAAG,YAAA,EAAK,EACA;;;;IAlCqBH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,SAAAuB,OAAA,CAAAd,QAAA,IAAAc,OAAA,CAAAd,QAAA,CAAAC,MAAA,CAA2C;;;;;IAtCjEhC,EAAA,CAAAC,cAAA,kBAC8B;IAkC1BD,EAjCA,CAAA+C,UAAA,IAAAI,0DAAA,2BAAgC,IAAAC,0DAAA,2BAU+B,IAAAC,0DAAA,0BAuBhB;IAuCvDrD,EAAA,CAAAG,YAAA,EAAU;;;;IA1EqFH,EAA9D,CAAAsB,UAAA,UAAAhB,MAAA,CAAAgD,IAAA,CAAc,WAA2B,mBAAuC;;;AD/JrH,OAAM,MAAOC,wBAAwB;EAyCnCC,YACUC,QAAmB,EACnBC,cAA8B,EAC9BC,MAAc,EACdC,EAAe,EACfC,OAAuB,EACvBC,aAAmC;IALnC,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IA7Cf,KAAAC,SAAS,GAAG,uBAAuB;IAE3C,KAAAvC,SAAS,GAAG,CAAC,MAAK;MAChB,MAAMwC,YAAY,GAAGnE,OAAO,CAACoE,eAAe,EAAE;MAC9C,MAAMC,QAAQ,GAAGF,YAAY,CAACG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAAC;MAChE,MAAMC,WAAW,GAAGN,YAAY,CAACG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAAC;MACnE,MAAME,GAAG,GAAGL,QAAQ,KAAK,CAAC,CAAC,GAAGF,YAAY,CAACQ,MAAM,CAACN,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACxE;MACA,MAAMO,cAAc,GAAGT,YAAY,CAACG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAAC;MACtE,MAAMK,MAAM,GAAGD,cAAc,KAAK,CAAC,CAAC,GAAGT,YAAY,CAACQ,MAAM,CAACC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACvF,MAAME,MAAM,GAAG,EAAE;MACjB,IAAIJ,GAAG,EAAEI,MAAM,CAACC,IAAI,CAACL,GAAG,CAAC;MACzB,IAAIG,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC;MAC/B,OAAOC,MAAM,CAACE,MAAM,CAACb,YAAY,CAAC;IACpC,CAAC,EAAC,CAAE;IACJ,KAAAvC,MAAM,GAAU,EAAE;IAClB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAoD,KAAK,GAAqB,CACxB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,UAAU,EAAE,CAAC,yBAAyB;IAAC,CAAE,CACvE;IACD,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAChE,KAAAzD,UAAU,GAAG,IAAI,CAACqC,EAAE,CAACuB,KAAK,CAAC;MACzB/C,KAAK,EAAE,CAAC,EAAE,CAAC;MACXgD,OAAO,EAAE,CAAC,EAAE,CAAC;MACb;MACAC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbpD,KAAK,EAAE,CAAC,EAAE,CAAC;MACXqD,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;IACF,KAAArC,IAAI,GAAU,EAAE;IAChB,KAAA3B,OAAO,GAAY,KAAK;IAExB,KAAApB,eAAe,GAAQ,IAAI;IA2C3B,KAAAqF,OAAO,GAAY,KAAK;IAkTxB,KAAAC,OAAO,GAAG,KAAK;IAMf,KAAAC,cAAc,GAAG,KAAK;IAzVpB,IAAI,CAACvE,UAAU,CAACwE,GAAG,CAAC,SAAS,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAEC,WAAW,IAAI;MACrE,IAAIA,WAAW,EAAE;QACf,IAAI,CAACzE,MAAM,GAAG3B,KAAK,CAACqG,kBAAkB,CAACD,WAAW,CAAC;MACrD,CAAC,MAAM;QACL,IAAI,CAACzE,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACF,UAAU,CAACwE,GAAG,CAAC,OAAO,CAAC,EAAEK,QAAQ,CAAC,EAAE,CAAC;MAC1C,IAAI,CAAC1E,MAAM,GAAG,EAAE;MAChB,IAAI,CAACH,UAAU,CAACwE,GAAG,CAAC,MAAM,CAAC,EAAEK,QAAQ,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC;IACF,IAAI,CAAC7E,UAAU,CAACwE,GAAG,CAAC,OAAO,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAEI,SAAS,IAAI;MACjE,MAAMH,WAAW,GAAG,IAAI,CAAC3E,UAAU,CAACwE,GAAG,CAAC,SAAS,CAAC,EAAEO,KAAK;MACzD,IAAIJ,WAAW,IAAIG,SAAS,EAAE;QAC5B,IAAI,CAAC3E,MAAM,GAAG3B,IAAI,CAACwG,gBAAgB,CAACL,WAAW,EAAEG,SAAS,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI,CAAC3E,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACH,UAAU,CAACwE,GAAG,CAAC,MAAM,CAAC,EAAEK,QAAQ,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEAI,QAAQA,CAAA;IACN,IAAI,CAAC/C,QAAQ,CAACgD,QAAQ,CAACC,QAAQ,CAACC,IAAI,EAAE,IAAI,CAAC5C,SAAS,CAAC;IACrD,IAAI,CAACxC,UAAU,CAACwE,GAAG,CAAC,SAAS,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAEC,WAAW,IAAI;MACrE,IAAIA,WAAW,EAAE;QACf,IAAI,CAACzE,MAAM,GAAG3B,KAAK,CAACqG,kBAAkB,CAACD,WAAW,CAAC;MACrD,CAAC,MAAM;QACL,IAAI,CAACzE,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACF,UAAU,CAACwE,GAAG,CAAC,OAAO,CAAC,EAAEK,QAAQ,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC;EACJ;EAIAnF,MAAMA,CAAA;IACJ,MAAM2F,GAAG,GAAQ;MACfC,QAAQ,EAAE,CAAC,OAAO,CAAC;MACnBC,OAAO,EAAE;QACPC,IAAI,EAAE,CACJ;UACEC,KAAK,EAAE;YACLC,OAAO,EAAE;cACPC,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ;;;SAGvC;;KAGN;IACD,IAAI,IAAI,CAAC3F,UAAU,CAAC+E,KAAK,CAAClE,KAAK,EAAE;MAC/BwE,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,OAAO,EAAE;UACPuC,IAAI,EAAE,IAAI,CAAC5F,UAAU,CAAC+E,KAAK,CAAClE,KAAK,IAAI;;OAExC,CAAC;IACJ;IACA,IAAI,IAAI,CAACb,UAAU,CAAC+E,KAAK,CAAClB,OAAO,EAAE;MACjCwB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,cAAc,EAAE;UACdwC,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAAClB,OAAO,IAAI;;OAEhD,CAAC;IACJ;IACA,IAAI,IAAI,CAAC7D,UAAU,CAAC+E,KAAK,CAACjB,MAAM,EAAE;MAChCuB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBC,WAAW,EAAE;cACXF,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACjB,MAAM,IAAI;;;;OAInD,CAAC;IACJ;IACA,IAAI,IAAI,CAAC9D,UAAU,CAAC+E,KAAK,CAAChB,IAAI,EAAE;MAC9BsB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBE,SAAS,EAAE;cACTH,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAAChB,IAAI,IAAI;;;;OAIjD,CAAC;IACJ;IACA,IAAI,IAAI,CAAC/D,UAAU,CAAC+E,KAAK,CAACf,KAAK,EAAE;MAC/BqB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBG,MAAM,EAAE;cACNJ,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACf,KAAK,IAAI;;;;OAIlD,CAAC;IACJ;IACA,IAAI,IAAI,CAAChE,UAAU,CAAC+E,KAAK,CAACd,QAAQ,EAAE;MAClCoB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBI,WAAW,EAAE;cACXL,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACd,QAAQ,IAAI;;;;OAIrD,CAAC;IACJ;IACA,IAAI,IAAI,CAACjE,UAAU,CAAC+E,KAAK,CAACb,OAAO,EAAE;MACjCmB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxB5B,OAAO,EAAE;cACP2B,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACb,OAAO,IAAI;;;;OAIpD,CAAC;IACJ;IACA,IAAI,IAAI,CAAClE,UAAU,CAAC+E,KAAK,CAACjE,KAAK,EAAE;MAC/BuE,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBK,MAAM,EAAE;cACNC,aAAa,EAAE;gBACbP,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACjE,KAAK,IAAI;;;;;OAKpD,CAAC;IACJ;IACA,IAAI,IAAI,CAACd,UAAU,CAAC+E,KAAK,CAACZ,KAAK,EAAE;MAC/BkB,GAAG,CAACE,OAAO,CAACC,IAAI,CAACnC,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChByC,wBAAwB,EAAE;YACxBO,aAAa,EAAE;cACbC,YAAY,EAAE;gBACZT,UAAU,EAAE,IAAI,CAAC7F,UAAU,CAAC+E,KAAK,CAACZ,KAAK,IAAI;;;;;OAKpD,CAAC;IACJ;IACA,MAAMoC,MAAM,GAAGrI,SAAS,CAACmH,GAAG,CAAC;IAC7B,IAAI,CAACjF,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC2B,IAAI,GAAG,EAAE;IACd,IAAI,CAAC/C,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACsD,OAAO,CAAC5C,MAAM,CAAC6G,MAAM,CAAC,CAACC,IAAI,CAC9BnI,SAAS,CAAEoI,GAAQ,IAAI;MACrB,IAAIA,GAAG,EAAEhG,MAAM,EAAE;QACf,MAAMiG,KAAK,GAAa,EAAE;QAC1B,MAAMC,WAAW,GAAG,EAAE;QACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAAChG,MAAM,EAAEmG,CAAC,EAAE,EAAE;UACnC,MAAMC,EAAE,GAAGJ,GAAG,CAACG,CAAC,CAAC;UACjB,MAAME,WAAW,GAAGD,EAAE,CAACpB,KAAK,CAACsB,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACtB,OAAO,IAAI,QAAQ,CAAC;UAC1E,IAAIoB,WAAW,EAAE;YACfH,WAAW,CAACtD,IAAI,CAACwD,EAAE,CAAChG,KAAK,CAAC;UAC5B,CAAC,MAAM;YACL6F,KAAK,CAACrD,IAAI,CAACwD,EAAE,CAAChG,KAAK,CAAC;UACtB;QACF;QACA,IAAI,CAAC8F,WAAW,CAAClG,MAAM,EAAE;UACvB,OAAOrC,EAAE,CAACsI,KAAK,CAAC;QAClB;QACA,MAAMH,MAAM,GAAGrI,SAAS,CAAC;UACvBqH,OAAO,EAAE;YACPC,IAAI,EAAE,CACJ;cACEyB,YAAY,EAAE;gBACZtB,GAAG,EAAEgB;;aAER;;SAGN,CAAC;QACF,OAAO,IAAI,CAACrE,OAAO,CAAC4E,0BAA0B,CAACX,MAAM,CAAC,CAACC,IAAI,CACzDrI,GAAG,CAAEgJ,cAAmB,IAAI;UAC1B,IAAI,CAACA,cAAc,EAAE1G,MAAM,EAAE;YAC3B,OAAOiG,KAAK;UACd;UACA,KAAK,IAAIU,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,cAAc,CAAC1G,MAAM,EAAE2G,KAAK,EAAE,EAAE;YAC1D,MAAMC,OAAO,GAAGF,cAAc,CAACC,KAAK,CAAC;YACrC,IAAI,CAACV,KAAK,CAACY,QAAQ,CAACD,OAAO,CAACE,aAAa,CAAC,EAAE;cAC1Cb,KAAK,CAACrD,IAAI,CAACgE,OAAO,CAACE,aAAa,CAAC;YACnC;UACF;UACA,OAAOb,KAAK;QACd,CAAC,CAAC,CACH;MACH,CAAC,MAAM;QACL,OAAOtI,EAAE,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,EACFC,SAAS,CAAEqI,KAAe,IAAI;MAC5B,IAAI,CAACA,KAAK,CAACjG,MAAM,EAAE;QACjB,OAAOrC,EAAE,CAAC,EAAE,CAAC;MACf;MACA,MAAMmI,MAAM,GAAGrI,SAAS,CAAC;QACvBqH,OAAO,EAAE;UACPC,IAAI,EAAE,CACJ;YACE3E,KAAK,EAAE;cACL8E,GAAG,EAAEe;;WAER;SAEJ;QACDpB,QAAQ,EAAE;UACRkC,cAAc,EAAE;YACdC,MAAM,EAAE,CAAC,eAAe,CAAC;YACzBnC,QAAQ,EAAE;cACRQ,wBAAwB,EAAE;gBACxB2B,MAAM,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;gBACxEnC,QAAQ,EAAE;kBACRa,MAAM,EAAE;oBACNsB,MAAM,EAAE,CAAC,eAAe;mBACzB;kBACDpB,aAAa,EAAE;oBACboB,MAAM,EAAE,CAAC,cAAc;;;;;WAKhC;UACDC,iBAAiB,EAAE;YACjBpC,QAAQ,EAAE;cACRqC,uBAAuB,EAAE;gBACvBF,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,yBAAyB,CAAC;gBACvEnC,QAAQ,EAAE;kBACRsC,SAAS,EAAE;oBACTtC,QAAQ,EAAE;;;;;;;OAQvB,CAAC;MACF,OAAO,IAAI,CAAChD,OAAO,CAAC5C,MAAM,CAAC6G,MAAM,CAAC;IACpC,CAAC,CAAC,CACH,CAAC7B,SAAS,CAAE+B,GAAQ,IAAI;MACvB,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAAC8F,UAAU,CAACpB,GAAG,CAAC;MAChC,IAAI,CAACrG,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,MAAK;MACN,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAAC+B,cAAc,CAAC2F,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAACL,SAAgB;IAChC,IAAI,CAACA,SAAS,EAAEnH,MAAM,IAAI,CAACmH,SAAS,CAAC,CAAC,CAAC,CAAC9B,wBAAwB,EAAE;MAChE,OAAO;QACL9E,OAAO,EAAE,EAAE;QACXD,OAAO,EAAE,EAAE;QACXD,KAAK,EAAE;OACR;IACH;IACA,MAAME,OAAO,GAAG4G,SAAS,CAAC,CAAC,CAAC,CAAC9B,wBAAwB;IACrD,IAAI,CAAC9E,OAAO,EAAE;MACZ,OAAO;QACLA,OAAO,EAAE,EAAE;QACXD,OAAO,EAAE,EAAE;QACXD,KAAK,EAAE;OACR;IACH;IACA,OAAO,IAAI,CAACoH,UAAU,CAAClH,OAAO,CAAC;EACjC;EAEAkH,UAAUA,CAAClH,OAAY;IACrB,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO;QACLA,OAAO,EAAE,EAAE;QACXD,OAAO,EAAE,EAAE;QACXD,KAAK,EAAE;OACR;IACH;IACA,OAAO;MACLE,OAAO,EAAE,GAAGA,OAAO,CAAC+E,WAAW,IAAI,EAAE,KAAK/E,OAAO,CAACkF,WAAW,IAAI,EAAE,KAAKlF,OAAO,CAACgF,SAAS,IAAI,EAAE,KAAKhF,OAAO,CAACiF,MAAM,IAAI,EAAE,KAAKjF,OAAO,CAACkD,OAAO,IAAI,EAAE,EAAE;MACpJnD,OAAO,EAAEC,OAAO,EAAEqF,aAAa,EAAE5F,MAAM,GAAGO,OAAO,CAACqF,aAAa,CAAC,CAAC,CAAC,CAACC,YAAY,GAAG,EAAE;MACpFxF,KAAK,EAAEE,OAAO,EAAEmF,MAAM,EAAE1F,MAAM,GAAGO,OAAO,CAACmF,MAAM,CAAC,CAAC,CAAC,CAACC,aAAa,GAAG;KACpE;EACH;EAEA+B,UAAUA,CAAC3H,QAAe;IACxB,MAAMuB,IAAI,GAAU,EAAE;IACtB,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpG,QAAQ,CAACC,MAAM,EAAEmG,CAAC,EAAE,EAAE;MACxC,MAAMwB,OAAO,GAAG5H,QAAQ,CAACoG,CAAC,CAAC;MAC3B,IAAIwB,OAAO,CAACT,uBAAuB,EAAE;QACnC,MAAMU,MAAM,GAAGD,OAAO,CAACT,uBAAuB;QAC9C,IAAIU,MAAM,CAACC,uBAAuB,KAAK,KAAK,EAAE;UAC5CvG,IAAI,CAACsB,IAAI,CAAC;YACRxC,KAAK,EAAEwH,MAAM,CAACxH,KAAK;YACnB0G,aAAa,EAAEa,OAAO,CAACb,aAAa;YACpCtI,UAAU,EAAEoJ,MAAM,CAACpJ,UAAU,IAAI,EAAE;YACnCC,SAAS,EAAEmJ,MAAM,CAACnJ,SAAS,IAAI,EAAE;YACjCqC,MAAM,EAAE,QAAQ;YAChB,GAAG,IAAI,CAAC2G,UAAU,CAACG,MAAM,CAACT,SAAS,CAAC,CAAC,CAAC;WACvC,CAAC;QACJ;MACF;IACF;IACA,OAAO7F,IAAI;EACb;EAEA8F,UAAUA,CAAC9F,IAAW;IACpB,OAAOA,IAAI,CAAC5D,GAAG,CAAEoK,IAAS,IAAI;MAC5B,OAAO;QACL1H,KAAK,EAAE0H,IAAI,CAAC1H,KAAK;QACjBD,YAAY,EAAE2H,IAAI,CAAC3H,YAAY;QAC/B,GAAG,IAAI,CAACqH,iBAAiB,CAACM,IAAI,CAACf,cAAc,CAAC;QAC9ChH,QAAQ,EAAE,IAAI,CAAC2H,UAAU,CAACI,IAAI,CAACb,iBAAiB,IAAI,EAAE;OACvD;IACH,CAAC,CAAC;EACJ;EAEA9H,KAAKA,CAAA;IACH,IAAI,CAACI,UAAU,CAACF,KAAK,EAAE;EACzB;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACiC,IAAI,GAAG,EAAE;IACd,IAAI,CAAC/C,eAAe,GAAG,IAAI;EAC7B;EAEA2B,WAAWA,CAAC6H,IAAY;IACtB,OAAOA,IAAI,CACRC,IAAI,EAAE,CACNC,KAAK,CAAC,KAAK,CAAC,CAAC;IAAA,CACbC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAA,CACZxK,GAAG,CAACyK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,CAAC,CAClCC,IAAI,CAAC,EAAE,CAAC;EACb;EAIAC,SAASA,CAAA;IACP,IAAI,CAACzE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAC9B;EAGA0E,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAChK,eAAe,EAAE;MACzB,IAAI,CAACmD,cAAc,CAAC2F,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IACA,MAAMjG,IAAI,GAAG;MACXkH,UAAU,EAAE,IAAI,CAACjK,eAAe,CAACuI,aAAa;MAC9C2B,UAAU,EAAE,IAAI,CAAClK,eAAe,CAAC6B,KAAK;MACtCsI,SAAS,EAAE;KACZ;IACD,IAAI,CAAC5E,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAChC,aAAa,CAACyG,YAAY,CAAC;MAAEjH;IAAI,CAAE,CAAC,CAAC2C,SAAS,CAAE0E,QAAQ,IAAI;MAC/D,IAAI,CAAC7E,cAAc,GAAG,KAAK;MAC3B,IAAI6E,QAAQ,EAAErH,IAAI,EAAEsH,UAAU,EAAE;QAC9B,IAAI,CAAClH,cAAc,CAAC2F,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,MAAMzB,MAAM,GAAGrI,SAAS,CAAC;UACvBqH,OAAO,EAAE;YACPC,IAAI,EAAE,CACJ;cACE3E,KAAK,EAAE;gBACL8E,GAAG,EAAE,CAAC,IAAI,CAAC3G,eAAe,CAACuI,aAAa;;aAE3C;;SAGN,CAAC;QACF,IAAI,CAACjF,OAAO,CAAC5C,MAAM,CAAC6G,MAAM,CAAC,CAAC7B,SAAS,CAAE+B,GAAQ,IAAI;UACjD,IAAIA,GAAG,EAAEhG,MAAM,EAAE;YACf,IAAI,CAAC2B,MAAM,CAACkH,QAAQ,CAAC,CAAC,+BAA+B,EAAEF,QAAQ,EAAErH,IAAI,EAAEwH,EAAE,EAAE9C,GAAG,CAAC,CAAC,CAAC,CAAC4C,UAAU,CAAC,CAAC;UAChG;QACF,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,MAAK;MACN,IAAI,CAAC9E,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACpC,cAAc,CAAC2F,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACJ;;;uBAxbWhG,wBAAwB,EAAAvD,EAAA,CAAA+K,iBAAA,CAAA/K,EAAA,CAAAgL,SAAA,GAAAhL,EAAA,CAAA+K,iBAAA,CAAAE,EAAA,CAAAzL,cAAA,GAAAQ,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnL,EAAA,CAAA+K,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArL,EAAA,CAAA+K,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAvL,EAAA,CAAA+K,iBAAA,CAAAS,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAxBlI,wBAAwB;MAAAmI,SAAA;MAAAC,QAAA,GAAA3L,EAAA,CAAA4L,kBAAA,CAFxB,CAACpM,cAAc,CAAC;MAAAqM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXrBlM,EAFR,CAAAC,cAAA,aAA8D,aACL,aACrB;UACxBD,EAAA,CAAAU,SAAA,sBAAqF;UACzFV,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA+C,UAAA,IAAAqJ,uCAAA,iBAA+C;UAWnDpM,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,aAAiH,aACD;UAAtBD,EAAA,CAAAW,UAAA,mBAAA0L,uDAAA;YAAA,OAASF,GAAA,CAAA7B,SAAA,EAAW;UAAA,EAAC;UACvGtK,EAAA,CAAAC,cAAA,YAA2C;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UASnDH,EAFJ,CAAAC,cAAA,gBAC0I,eAC/F;UAAAD,EAAA,CAAAE,MAAA,IACjC;UAEdF,EAFc,CAAAG,YAAA,EAAO,EACR,EACP;UACNH,EAAA,CAAA+C,UAAA,KAAAuJ,wCAAA,mBAAwF;UA8H5FtM,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAHR,CAAAC,cAAA,eAAuC,eAEqG,aACzF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEvDH,EADJ,CAAAC,cAAA,eAA2C,oBAEwD;UADnBD,EAAA,CAAAW,UAAA,mBAAA4L,6DAAA;YAAA,OAASJ,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UAEhGvK,EAAA,CAAAC,cAAA,aAA6C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,IAElE;UAERF,EAFQ,CAAAG,YAAA,EAAW,EACT,EACJ;UAINH,EAFA,CAAA+C,UAAA,KAAAyJ,sCAAA,gBAAoC,KAAAC,4CAAA,sBAGN;UA2EtCzM,EADA,CAAAG,YAAA,EAAM,EACA;;;UAvPoBH,EAAA,CAAAI,SAAA,GAAe;UAAeJ,EAA9B,CAAAsB,UAAA,UAAA6K,GAAA,CAAArH,KAAA,CAAe,SAAAqH,GAAA,CAAAlH,IAAA,CAAc,uCAAuC;UAE9DjF,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAsB,UAAA,SAAA6K,GAAA,CAAA5L,eAAA,CAAqB;UAuBEP,EAAA,CAAAI,SAAA,GACjC;UADiCJ,EAAA,CAAAiC,iBAAA,EAAAkK,GAAA,CAAAtG,OAAA,+CACjC;UAGR7F,EAAA,CAAAI,SAAA,EAAc;UAAdJ,EAAA,CAAAsB,UAAA,UAAA6K,GAAA,CAAAtG,OAAA,CAAc;UAoIF7F,EAAA,CAAAI,SAAA,GAAiB;UACvBJ,EADM,CAAAsB,UAAA,kBAAiB,cAAA6K,GAAA,CAAA5L,eAAA,IAAA4L,GAAA,CAAArG,cAAA,CAAgD,2FACmB;UAC5B9F,EAAA,CAAAI,SAAA,GAElE;UAFkEJ,EAAA,CAAA4B,kBAAA,MAAAuK,GAAA,CAAArG,cAAA,oCAElE;UAIJ9F,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAAsB,UAAA,UAAA6K,GAAA,CAAA7I,IAAA,CAAAtB,MAAA,KAAAmK,GAAA,CAAAxK,OAAA,CAA8B;UAExB3B,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAsB,UAAA,SAAA6K,GAAA,CAAA7I,IAAA,CAAAtB,MAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}