{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../activities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = () => [\"/auth/signup\"];\nfunction SalesCallComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 20);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\")(4, \"div\", 21);\n    i0.ɵɵtext(5, \" Account ID \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"th\", 22)(7, \"div\", 21);\n    i0.ɵɵtext(8, \" Subject \");\n    i0.ɵɵelement(9, \"p-sortIcon\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Ranking\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Customer Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\");\n    i0.ɵɵtext(21, \"Brand\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\");\n    i0.ɵɵtext(23, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 24)(25, \"div\", 21);\n    i0.ɵɵtext(26, \" Status \");\n    i0.ɵɵelement(27, \"p-sortIcon\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"th\", 26)(29, \"div\", 21);\n    i0.ɵɵtext(30, \" Created On \");\n    i0.ɵɵelement(31, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"Owner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\");\n    i0.ɵɵtext(35, \"Customer Timezone\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 28)(1, \"td\", 20);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\");\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"td\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const call_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/calls/\" + call_r3.bp_id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", call_r3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.account_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.description) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.account) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.ranking) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.state) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.note) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.customer_group) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.brand) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.category) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.createdAt) ? i0.ɵɵpipeBind2(25, 15, call_r3 == null ? null : call_r3.createdAt, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.owner) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (call_r3 == null ? null : call_r3.customer_timezone) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No calls found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"Loading calls data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SalesCallComponent {\n  constructor(activitiesservice) {\n    this.activitiesservice = activitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.calls = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Sales Call',\n      routerLink: ['/store/activities']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Appointments',\n      code: 'MA'\n    }, {\n      name: 'My Appointments This Month',\n      code: 'MAM'\n    }, {\n      name: 'My Appointments This Week',\n      code: 'MAW'\n    }, {\n      name: 'My Appointments Today',\n      code: 'MAT'\n    }, {\n      name: 'My Completed Appointments',\n      code: 'MCA'\n    }];\n  }\n  loadSalesCall(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.activitiesservice.getSalesCall(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.calls = response?.data || [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching calls', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadSalesCall({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallComponent_Factory(t) {\n      return new (t || SalesCallComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallComponent,\n      selectors: [[\"app-sales-call\"]],\n      decls: 22,\n      vars: 15,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Sales Call\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"bg-orange-700\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [1, \"flex\", \"align-items-center\"], [\"pSortableColumn\", \"description\"], [\"field\", \"description\"], [\"pSortableColumn\", \"activity_status\"], [\"field\", \"activity_status\"], [\"pSortableColumn\", \"start_date\"], [\"field\", \"start_date\"], [1, \"cursor-pointer\", 3, \"routerLink\"], [3, \"value\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\"], [\"colspan\", \"14\"]],\n      template: function SalesCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function SalesCallComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(17);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12)(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"p-table\", 15, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function SalesCallComponent_Template_p_table_onLazyLoad_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadSalesCall($event));\n          });\n          i0.ɵɵtemplate(18, SalesCallComponent_ng_template_18_Template, 36, 0, \"ng-template\", 16)(19, SalesCallComponent_ng_template_19_Template, 30, 18, \"ng-template\", 17)(20, SalesCallComponent_ng_template_20_Template, 3, 0, \"ng-template\", 18)(21, SalesCallComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.calls)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true);\n        }\n      },\n      dependencies: [i2.RouterLink, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.Table, i5.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i4.TableCheckbox, i4.TableHeaderCheckbox, i6.Dropdown, i7.Breadcrumb, i8.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "call_r3", "bp_id", "ɵɵadvance", "ɵɵtextInterpolate1", "account_id", "description", "account", "ranking", "state", "note", "customer_group", "brand", "category", "activity_status", "createdAt", "ɵɵpipeBind2", "owner", "customer_timezone", "SalesCallComponent", "constructor", "activitiesservice", "unsubscribe$", "calls", "totalRecords", "loading", "globalSearchTerm", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "loadSalesCall", "event", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getSalesCall", "subscribe", "next", "response", "data", "meta", "pagination", "total", "error", "console", "onGlobalFilter", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "selectors", "decls", "vars", "consts", "template", "SalesCallComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "SalesCallComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "SalesCallComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "SalesCallComponent_Template_p_dropdown_ngModelChange_10_listener", "selectedActions", "SalesCallComponent_Template_p_table_onLazyLoad_16_listener", "ɵɵtemplate", "SalesCallComponent_ng_template_18_Template", "SalesCallComponent_ng_template_19_Template", "SalesCallComponent_ng_template_20_Template", "SalesCallComponent_ng_template_21_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject } from 'rxjs';\r\nimport { ActivitiesService } from '../activities.service';\r\nimport { Table } from 'primeng/table';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-call',\r\n  templateUrl: './sales-call.component.html',\r\n  styleUrl: './sales-call.component.scss',\r\n})\r\nexport class SalesCallComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public calls: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n\r\n  constructor(private activitiesservice: ActivitiesService) {}\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Sales Call', routerLink: ['/store/activities'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Appointments', code: 'MA' },\r\n      { name: 'My Appointments This Month', code: 'MAM' },\r\n      { name: 'My Appointments This Week', code: 'MAW' },\r\n      { name: 'My Appointments Today', code: 'MAT' },\r\n      { name: 'My Completed Appointments', code: 'MCA' },\r\n    ];\r\n  }\r\n\r\n  loadSalesCall(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.activitiesservice\r\n      .getSalesCall(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.calls = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching calls', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadSalesCall({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" placeholder=\"Search Sales Call\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                        (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round border-1 surface-border\">\r\n                    <i class=\"pi pi-search\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Filter\"\r\n                [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold'\" />\r\n            <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component bg-orange-700 w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"calls\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadSalesCall($event)\" [loading]=\"loading\"\r\n            [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th>\r\n                        <div class=\"flex align-items-center\">\r\n                            Account ID\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"description\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Subject\r\n                            <p-sortIcon field=\"description\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Account</th>\r\n                    <th>Ranking</th>\r\n                    <th>State</th>\r\n                    <th>Notes</th>\r\n                    <th>Customer Group</th>\r\n                    <th>Brand</th>\r\n                    <th>Category</th>\r\n                    <th pSortableColumn=\"activity_status\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Status\r\n                            <p-sortIcon field=\"activity_status\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"start_date\">\r\n                        <div class=\"flex align-items-center\">\r\n                            Created On\r\n                            <p-sortIcon field=\"start_date\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Owner</th>\r\n                    <th>Customer Timezone</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-call>\r\n                <tr class=\"cursor-pointer\" [routerLink]=\"'/store/activities/calls/' + call.bp_id\">\r\n                    <td class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"call\" />\r\n                    </td>\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline\">\r\n                        {{ call?.account_id || '-'}}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.description || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.account || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.ranking || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.state || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.note || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.customer_group || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.brand || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.category || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.activity_status || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.createdAt ? (call?.createdAt | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                        }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.owner || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ call?.customer_timezone || '-' }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"14\">No calls found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"14\">Loading calls data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;;;;;;;;;;;;;IC4BVC,EADJ,CAAAC,cAAA,SAAI,aACyD;IACrDD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,SAAI,cACqC;IACjCD,EAAA,CAAAI,MAAA,mBACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAkC,cACO;IACjCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA6C;IAErDF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEbH,EADJ,CAAAC,cAAA,cAAsC,eACG;IACjCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAiD;IAEzDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAiC,eACQ;IACjCD,EAAA,CAAAI,MAAA,oBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA4C;IAEpDF,EADI,CAAAG,YAAA,EAAM,EACL;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,yBAAiB;IACzBJ,EADyB,CAAAG,YAAA,EAAK,EACzB;;;;;IAKDH,EADJ,CAAAC,cAAA,aAAkF,aACrB;IACrDD,EAAA,CAAAE,SAAA,0BAAkC;IACtCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAiE;IAC7DD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IAEJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IA5CsBH,EAAA,CAAAK,UAAA,4CAAAC,OAAA,CAAAC,KAAA,CAAsD;IAExDP,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAK,UAAA,UAAAC,OAAA,CAAc;IAG/BN,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAI,UAAA,cACJ;IAEIV,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAK,WAAA,cACJ;IAEIX,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAM,OAAA,cACJ;IAEIZ,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAO,OAAA,cACJ;IAEIb,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAQ,KAAA,cACJ;IAEId,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAS,IAAA,cACJ;IAEIf,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAU,cAAA,cACJ;IAEIhB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAW,KAAA,cACJ;IAEIjB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAY,QAAA,cACJ;IAEIlB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAa,eAAA,cACJ;IAEInB,EAAA,CAAAQ,SAAA,GAEJ;IAFIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAc,SAAA,IAAApB,EAAA,CAAAqB,WAAA,SAAAf,OAAA,kBAAAA,OAAA,CAAAc,SAAA,mCAEJ;IAEIpB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAgB,KAAA,cACJ;IAEItB,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,OAAAH,OAAA,kBAAAA,OAAA,CAAAiB,iBAAA,cACJ;;;;;IAKAvB,EADJ,CAAAC,cAAA,SAAI,aACiB;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IACpCJ,EADoC,CAAAG,YAAA,EAAK,EACpC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACiB;IAAAD,EAAA,CAAAI,MAAA,uCAAgC;IACrDJ,EADqD,CAAAG,YAAA,EAAK,EACrD;;;AD3GrB,OAAM,MAAOqB,kBAAkB;EAW7BC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAV7B,KAAAC,YAAY,GAAG,IAAI5B,OAAO,EAAQ;IAGnC,KAAA6B,KAAK,GAAU,EAAE;IACjB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;EAIuB;EAE3DC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,YAAY;MAAEC,UAAU,EAAE,CAAC,mBAAmB;IAAC,CAAE,CAC3D;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAI,CAAE,EACvC;MAAED,IAAI,EAAE,4BAA4B;MAAEC,IAAI,EAAE;IAAK,CAAE,EACnD;MAAED,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAAK,CAAE,EAClD;MAAED,IAAI,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC9C;MAAED,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAAK,CAAE,CACnD;EACH;EAEAC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAMa,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,MAAME,SAAS,GAAGL,KAAK,CAACK,SAAS;IACjC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAEjC,IAAI,CAACtB,iBAAiB,CACnBuB,YAAY,CACXN,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAACjB,gBAAgB,CACtB,CACAmB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACxB,KAAK,GAAGwB,QAAQ,EAAEC,IAAI,IAAI,EAAE;QACjC,IAAI,CAACxB,YAAY,GAAGuB,QAAQ,EAAEE,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA6B,cAAcA,CAACC,KAAY,EAAElB,KAAY;IACvC,IAAI,CAACD,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAAClC,YAAY,CAACwB,IAAI,EAAE;IACxB,IAAI,CAACxB,YAAY,CAACmC,QAAQ,EAAE;EAC9B;;;uBA/DWtC,kBAAkB,EAAAxB,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAlBzC,kBAAkB;MAAA0C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCdvBxE,EAFR,CAAAC,cAAA,aAA8D,aACmB,aAC7C;UACxBD,EAAA,CAAAE,SAAA,sBAA+F;UACnGF,EAAA,CAAAG,YAAA,EAAM;UAKMH,EAJZ,CAAAC,cAAA,aAA2C,aAEb,cACW,kBAG0E;UAF5CD,EAAA,CAAA0E,gBAAA,2BAAAC,2DAAAC,MAAA;YAAA5E,EAAA,CAAA6E,aAAA,CAAAC,GAAA;YAAA9E,EAAA,CAAA+E,kBAAA,CAAAN,GAAA,CAAA1C,gBAAA,EAAA6C,MAAA,MAAAH,GAAA,CAAA1C,gBAAA,GAAA6C,MAAA;YAAA,OAAA5E,EAAA,CAAAgF,WAAA,CAAAJ,MAAA;UAAA,EAA8B;UACrF5E,EAAA,CAAAiF,UAAA,mBAAAC,mDAAAN,MAAA;YAAA5E,EAAA,CAAA6E,aAAA,CAAAC,GAAA;YAAA,MAAAK,MAAA,GAAAnF,EAAA,CAAAoF,WAAA;YAAA,OAAApF,EAAA,CAAAgF,WAAA,CAASP,GAAA,CAAAd,cAAA,CAAAwB,MAAA,EAAAP,MAAA,CAA2B;UAAA,EAAC;UADzC5E,EAAA,CAAAG,YAAA,EAEuG;UACvGH,EAAA,CAAAE,SAAA,YAA4B;UAEpCF,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAC,cAAA,sBACwF;UADxDD,EAAA,CAAA0E,gBAAA,2BAAAW,iEAAAT,MAAA;YAAA5E,EAAA,CAAA6E,aAAA,CAAAC,GAAA;YAAA9E,EAAA,CAAA+E,kBAAA,CAAAN,GAAA,CAAAa,eAAA,EAAAV,MAAA,MAAAH,GAAA,CAAAa,eAAA,GAAAV,MAAA;YAAA,OAAA5E,EAAA,CAAAgF,WAAA,CAAAJ,MAAA;UAAA,EAA6B;UAA7D5E,EAAA,CAAAG,YAAA,EACwF;UAGpFH,EAFJ,CAAAC,cAAA,kBACuI,gBACnF;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAI,MAAA,gBACpE;UAERJ,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EADJ,CAAAC,cAAA,eAAuB,sBAE0E;UADtCD,EAAA,CAAAiF,UAAA,wBAAAM,2DAAAX,MAAA;YAAA5E,EAAA,CAAA6E,aAAA,CAAAC,GAAA;YAAA,OAAA9E,EAAA,CAAAgF,WAAA,CAAcP,GAAA,CAAAhC,aAAA,CAAAmC,MAAA,CAAqB;UAAA,EAAC;UA+FvF5E,EA5FA,CAAAwF,UAAA,KAAAC,0CAAA,2BAAgC,KAAAC,0CAAA,4BAwCO,KAAAC,0CAAA,0BA+CD,KAAAC,0CAAA,0BAKD;UAOjD5F,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;;;UA5HoBH,EAAA,CAAAQ,SAAA,GAAyB;UAAeR,EAAxC,CAAAK,UAAA,UAAAoE,GAAA,CAAAxC,eAAA,CAAyB,SAAAwC,GAAA,CAAArC,IAAA,CAAc,uCAAuC;UAMzBpC,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAA6F,gBAAA,YAAApB,GAAA,CAAA1C,gBAAA,CAA8B;UAMrF/B,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAK,UAAA,YAAAoE,GAAA,CAAAnC,OAAA,CAAmB;UAACtC,EAAA,CAAA6F,gBAAA,YAAApB,GAAA,CAAAa,eAAA,CAA6B;UACzDtF,EAAA,CAAAK,UAAA,kFAAiF;UAC/DL,EAAA,CAAAQ,SAAA,EAA+B;UAA/BR,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAA8F,eAAA,KAAAC,GAAA,EAA+B;UAQ3C/F,EAAA,CAAAQ,SAAA,GAAe;UACwBR,EADvC,CAAAK,UAAA,UAAAoE,GAAA,CAAA7C,KAAA,CAAe,YAAyB,YAAA6C,GAAA,CAAA3C,OAAA,CAAyD,mBACzF,iBAAA2C,GAAA,CAAA5C,YAAA,CAA8B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}