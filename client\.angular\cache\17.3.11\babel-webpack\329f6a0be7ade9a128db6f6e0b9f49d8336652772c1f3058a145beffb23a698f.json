{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of, forkJoin } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../opportunities.service\";\nimport * as i3 from \"src/app/store/account/account.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"primeng/tooltip\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/checkbox\";\nconst _c0 = () => ({\n  width: \"38rem\"\n});\nconst _c1 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c2 = () => ({\n  width: \"45rem\"\n});\nfunction OpportunitiesContactsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 44);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 45)(4, \"div\", 46);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵelement(6, \"p-sortIcon\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 48)(8, \"div\", 46);\n    i0.ɵɵtext(9, \"Function\");\n    i0.ɵɵelement(10, \"p-sortIcon\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"th\", 50)(12, \"div\", 46);\n    i0.ɵɵtext(13, \" Department \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 51)(16, \"div\", 46);\n    i0.ɵɵtext(17, \" Phone \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\", 53)(20, \"div\", 46);\n    i0.ɵɵtext(21, \" Mobile \");\n    i0.ɵɵelement(22, \"p-sortIcon\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"th\", 55)(24, \"div\", 46);\n    i0.ɵɵtext(25, \" E-Mail \");\n    i0.ɵɵelement(26, \"p-sortIcon\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\", 57);\n    i0.ɵɵtext(28, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 58);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_ng_template_10_Template_button_click_16_listener($event) {\n      const contact_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r2.confirmRemove(contact_r2));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", contact_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.business_partner == null ? null : contact_r2.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.contact_person_function_name == null ? null : contact_r2.contact_person_function_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.contact_person_department_name == null ? null : contact_r2.contact_person_department_name.name) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.phone_number) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.mobile) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (contact_r2 == null ? null : contact_r2.email_address) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 62);\n    i0.ɵɵtext(2, \"No contacts found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 62);\n    i0.ɵɵtext(2, \" Loading contacts data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_25_div_1_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"first_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_42_div_1_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"last_name\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_73_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_73_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_73_div_1_Template, 2, 0, \"div\", 35)(2, OpportunitiesContactsComponent_div_73_div_2_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitted && ctx_r2.f[\"email_address\"].errors && ctx_r2.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_81_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_81_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.ContactForm.get(\"phone_number\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_91_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mobile is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_91_div_1_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"mobile\"].errors[\"required\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_92_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1, \" Please enter a valid Mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, OpportunitiesContactsComponent_div_92_div_1_Template, 2, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.ContactForm.get(\"mobile\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction OpportunitiesContactsComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 14)(2, \"label\", 66)(3, \"span\", 16);\n    i0.ɵɵtext(4, \"remove_circle_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \"DeActivate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 18);\n    i0.ɵɵelement(7, \"p-checkbox\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"label\", 68)(10, \"span\", 16);\n    i0.ɵɵtext(11, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \"VIP Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 18);\n    i0.ɵɵelement(14, \"p-checkbox\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Contact Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_108_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.bp_full_name, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_108_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.email, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_108_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r4.phone, \"\");\n  }\n}\nfunction OpportunitiesContactsComponent_ng_template_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, OpportunitiesContactsComponent_ng_template_108_span_2_Template, 2, 1, \"span\", 35)(3, OpportunitiesContactsComponent_ng_template_108_span_3_Template, 2, 1, \"span\", 35)(4, OpportunitiesContactsComponent_ng_template_108_span_4_Template, 2, 1, \"span\", 35);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.bp_full_name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.phone);\n  }\n}\nexport class OpportunitiesContactsComponent {\n  constructor(route, opportunitiesservice, accountservice, formBuilder, messageservice, confirmationservice) {\n    this.route = route;\n    this.opportunitiesservice = opportunitiesservice;\n    this.accountservice = accountservice;\n    this.formBuilder = formBuilder;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.contactDetails = null;\n    this.id = '';\n    this.opportunity_id = '';\n    this.bp_id = '';\n    this.departments = null;\n    this.functions = null;\n    this.addDialogVisible = false;\n    this.existingDialogVisible = false;\n    this.visible = false;\n    this.position = 'right';\n    this.submitted = false;\n    this.editid = '';\n    this.documentId = '';\n    this.saving = false;\n    this.cpDepartments = [];\n    this.cpFunctions = [];\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.defaultOptions = [];\n    this.selectedContacts = [];\n    this.ContactForm = this.formBuilder.group({\n      first_name: ['', [Validators.required]],\n      middle_name: [''],\n      last_name: ['', [Validators.required]],\n      job_title: [''],\n      contact_person_function_name: [''],\n      contact_person_department_name: [''],\n      email_address: ['', [Validators.required, Validators.email]],\n      phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n      contact_person_vip_type: [''],\n      validity_end_date: [''],\n      contactexisting: ['']\n    });\n  }\n  ngOnInit() {\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.loadContacts();\n    forkJoin({\n      departments: this.accountservice.getCPDepartment(),\n      functions: this.accountservice.getCPFunction()\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n      departments,\n      functions\n    }) => {\n      // Load departments\n      this.cpDepartments = (departments?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      // Load functions\n      this.cpFunctions = (functions?.data || []).map(item => ({\n        name: item.description,\n        value: item.code\n      }));\n      this.opportunitiesservice.opportunity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.id = response?.opportunity_id;\n          this.bp_id = response?.business_partner?.bp_id;\n          this.documentId = response?.documentId;\n          this.contactDetails = response?.opportunity_contact_parties || [];\n          this.contactDetails = this.contactDetails.map(contact => {\n            return {\n              ...contact,\n              email_address: contact?.business_partner?.addresses?.[0]?.emails?.[0]?.email_address || '',\n              phone_number: (contact?.business_partner?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number,\n              mobile: (contact?.business_partner?.addresses?.[0]?.phone_numbers || []).find(item => item.phone_number_type === '3')?.phone_number,\n              // Ensure department & function values are set correctly\n              contact_person_department_name: this.cpDepartments?.find(d => d.value === contact?.business_partner?.contact_person_func_and_depts?.[0]?.contact_person_department) || null,\n              contact_person_function_name: this.cpFunctions?.find(f => f.value === contact?.business_partner?.contact_person_func_and_depts?.[0]?.contact_person_function) || null\n            };\n          });\n        }\n      });\n    });\n  }\n  loadContacts() {\n    this.contacts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.contactInput$.pipe(distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP001',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.accountservice.getContacts(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.contactLoading = false), catchError(error => {\n        this.contactLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.visible = true;\n      if (_this.ContactForm.value?.contactexisting) {\n        const existing = _this.ContactForm.value.contactexisting;\n        const data = {\n          opportunity_party_contact_party_id: existing?.bp_id,\n          opportunity_id: _this.opportunity_id,\n          role_code: ''\n        };\n        _this.saving = true;\n        _this.opportunitiesservice.createExistingContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          complete: () => {\n            _this.saving = false;\n            _this.existingDialogVisible = false;\n            _this.ContactForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Contact Added successfully!.'\n            });\n            _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.addDialogVisible = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n        // Skip rest of logic for new contact\n        return;\n      }\n      if (_this.ContactForm.invalid) {\n        console.log('Form is invalid:', _this.ContactForm.errors);\n        _this.visible = true;\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.ContactForm.value\n      };\n      const data = {\n        bp_id: _this.bp_id,\n        first_name: value?.first_name || '',\n        middle_name: value?.middle_name,\n        last_name: value?.last_name || '',\n        job_title: value?.job_title || '',\n        contact_person_function_name: value?.contact_person_function_name?.name || '',\n        contact_person_function: value?.contact_person_function_name?.value || '',\n        contact_person_department_name: value?.contact_person_department_name?.name || '',\n        contact_person_department: value?.contact_person_department_name?.value || '',\n        email_address: value?.email_address,\n        phone_number: value?.phone_number,\n        mobile: value?.mobile,\n        contact_person_vip_type: value?.contact_person_vip_type,\n        opportunity_party_contact_main_indicator: '',\n        role_code: '',\n        opportunity_id: _this.opportunity_id\n      };\n      _this.opportunitiesservice.createOpportunityContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        complete: () => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.existingDialogVisible = false;\n          _this.ContactForm.reset();\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Contact created successfully!.'\n          });\n          _this.opportunitiesservice.getOpportunityByID(_this.opportunity_id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.addDialogVisible = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.opportunitiesservice.deleteContact(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.opportunitiesservice.getOpportunityByID(this.opportunity_id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  showNewDialog(position) {\n    this.position = position;\n    this.addDialogVisible = true;\n    this.submitted = false;\n    this.ContactForm.reset();\n  }\n  showExistingDialog(position) {\n    this.position = position;\n    this.existingDialogVisible = true;\n  }\n  get f() {\n    return this.ContactForm.controls;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesContactsComponent_Factory(t) {\n      return new (t || OpportunitiesContactsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.OpportunitiesService), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i5.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesContactsComponent,\n      selectors: [[\"app-opportunities-contacts\"]],\n      decls: 112,\n      vars: 56,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add New\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"label\", \"Existing Contact\", \"icon\", \"pi pi-users\", \"iconPos\", \"right\", 3, \"click\", \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"selectionChange\", \"value\", \"selection\", \"rows\", \"paginator\", \"scrollable\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"opportunity-contact-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [\"for\", \"First Name \", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"text-red-500\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"first_name\", \"formControlName\", \"first_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [\"for\", \"Middle Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"middle_name\", \"formControlName\", \"middle_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Last Name\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"last_name\", \"formControlName\", \"last_name\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Job Title\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"job_title\", \"formControlName\", \"job_title\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [\"for\", \"Function\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_function_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Function\", 3, \"options\", \"styleClass\"], [\"for\", \"Department\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"formControlName\", \"contact_person_department_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Department\", 3, \"options\", \"styleClass\"], [\"for\", \"Email\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"email\", \"id\", \"email_address\", \"formControlName\", \"email_address\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"for\", \"Phone\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"phone_number\", \"formControlName\", \"phone_number\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"for\", \"Mobile\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"mobile\", \"formControlName\", \"mobile\", \"autocomplete\", \"off\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"for\", \"Contacts\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"formControlName\", \"contactexisting\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"table-checkbox\", \"text-center\"], [\"pFrozenColumn\", \"\", \"pSortableColumn\", \"business_partner.bp_full_name\", 2, \"width\", \"12%\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"business_partner.bp_full_name\"], [\"pSortableColumn\", \"contact_person_function_name.name\", 2, \"width\", \"10%\"], [\"field\", \"contact_person_department_name.name\"], [\"pSortableColumn\", \"contact_person_department_name.name\", 2, \"width\", \"10%\"], [\"pSortableColumn\", \"phone_number\", 2, \"width\", \"10%\"], [\"field\", \"phone_number\"], [\"pSortableColumn\", \"mobile\", 2, \"width\", \"10%\"], [\"field\", \"mobile\"], [\"pSortableColumn\", \"email_address\", 2, \"width\", \"20%\"], [\"field\", \"email_address\"], [2, \"width\", \"5%\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"p-error\"], [\"for\", \"DeActivate\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"validity_end_date\", \"formControlName\", \"validity_end_date\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"], [\"for\", \"VIP Contact\", 1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-12rem\", \"gap-1\"], [\"id\", \"contact_person_vip_type\", \"formControlName\", \"contact_person_vip_type\", 1, \"h-3rem\", \"w-full\", 3, \"binary\"]],\n      template: function OpportunitiesContactsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Contacts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_p_button_click_5_listener() {\n            return ctx.showNewDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_p_button_click_6_listener() {\n            return ctx.showExistingDialog(\"right\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵtwoWayListener(\"selectionChange\", function OpportunitiesContactsComponent_Template_p_table_selectionChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedContacts, $event) || (ctx.selectedContacts = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(9, OpportunitiesContactsComponent_ng_template_9_Template, 29, 0, \"ng-template\", 8)(10, OpportunitiesContactsComponent_ng_template_10_Template, 17, 7, \"ng-template\", 9)(11, OpportunitiesContactsComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, OpportunitiesContactsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.addDialogVisible, $event) || (ctx.addDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(14, OpportunitiesContactsComponent_ng_template_14_Template, 2, 0, \"ng-template\", 8);\n          i0.ɵɵelementStart(15, \"form\", 13)(16, \"div\", 14)(17, \"label\", 15)(18, \"span\", 16);\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20, \"First Name \");\n          i0.ɵɵelementStart(21, \"span\", 17);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 18);\n          i0.ɵɵelement(24, \"input\", 19);\n          i0.ɵɵtemplate(25, OpportunitiesContactsComponent_div_25_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"label\", 21)(28, \"span\", 16);\n          i0.ɵɵtext(29, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \"Middle Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 18);\n          i0.ɵɵelement(32, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 14)(34, \"label\", 23)(35, \"span\", 16);\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \"Last Name \");\n          i0.ɵɵelementStart(38, \"span\", 17);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 18);\n          i0.ɵɵelement(41, \"input\", 24);\n          i0.ɵɵtemplate(42, OpportunitiesContactsComponent_div_42_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 14)(44, \"label\", 25)(45, \"span\", 16);\n          i0.ɵɵtext(46, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \"Job Title \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 18);\n          i0.ɵɵelement(49, \"input\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 14)(51, \"label\", 27)(52, \"span\", 16);\n          i0.ɵɵtext(53, \"functions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54, \"Function \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 18);\n          i0.ɵɵelement(56, \"p-dropdown\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 14)(58, \"label\", 29)(59, \"span\", 16);\n          i0.ɵɵtext(60, \"inbox_text_person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(61, \"Department \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 18);\n          i0.ɵɵelement(63, \"p-dropdown\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 14)(65, \"label\", 31)(66, \"span\", 16);\n          i0.ɵɵtext(67, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(68, \"Email\");\n          i0.ɵɵelementStart(69, \"span\", 17);\n          i0.ɵɵtext(70, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 18);\n          i0.ɵɵelement(72, \"input\", 32);\n          i0.ɵɵtemplate(73, OpportunitiesContactsComponent_div_73_Template, 3, 2, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 14)(75, \"label\", 33)(76, \"span\", 16);\n          i0.ɵɵtext(77, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(78, \"Phone \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 18);\n          i0.ɵɵelement(80, \"input\", 34);\n          i0.ɵɵtemplate(81, OpportunitiesContactsComponent_div_81_Template, 2, 1, \"div\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 14)(83, \"label\", 36)(84, \"span\", 16);\n          i0.ɵɵtext(85, \"smartphone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(86, \"Mobile # \");\n          i0.ɵɵelementStart(87, \"span\", 17);\n          i0.ɵɵtext(88, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 18);\n          i0.ɵɵelement(90, \"input\", 37);\n          i0.ɵɵtemplate(91, OpportunitiesContactsComponent_div_91_Template, 2, 1, \"div\", 20)(92, OpportunitiesContactsComponent_div_92_Template, 2, 1, \"div\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(93, OpportunitiesContactsComponent_div_93_Template, 15, 2, \"div\", 35);\n          i0.ɵɵelementStart(94, \"div\", 38)(95, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_95_listener() {\n            return ctx.addDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_96_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"p-dialog\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function OpportunitiesContactsComponent_Template_p_dialog_visibleChange_97_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.existingDialogVisible, $event) || (ctx.existingDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(98, OpportunitiesContactsComponent_ng_template_98_Template, 2, 0, \"ng-template\", 8);\n          i0.ɵɵelementStart(99, \"form\", 13)(100, \"div\", 14)(101, \"label\", 41)(102, \"span\", 16);\n          i0.ɵɵtext(103, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(104, \"Contacts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 18)(106, \"ng-select\", 42);\n          i0.ɵɵpipe(107, \"async\");\n          i0.ɵɵtemplate(108, OpportunitiesContactsComponent_ng_template_108_Template, 5, 4, \"ng-template\", 43);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(109, \"div\", 38)(110, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_110_listener() {\n            return ctx.existingDialogVisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function OpportunitiesContactsComponent_Template_button_click_111_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_25_0;\n          let tmp_28_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.contactDetails);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedContacts);\n          i0.ɵɵproperty(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(46, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.addDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(47, _c1, ctx.submitted && ctx.f[\"first_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"first_name\"].errors);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c1, ctx.submitted && ctx.f[\"last_name\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"last_name\"].errors);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"options\", ctx.cpFunctions)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"options\", ctx.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c1, ctx.submitted && ctx.f[\"email_address\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_25_0.touched) && ((tmp_25_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_25_0.invalid));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c1, ctx.submitted && ctx.f[\"mobile\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"mobile\"].errors);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_28_0.touched) && ((tmp_28_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_28_0.invalid));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editid && ctx.ContactForm.value.first_name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(55, _c2));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.existingDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(107, 44, ctx.contacts$))(\"hideSelected\", true)(\"loading\", ctx.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.contactInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i7.Table, i5.PrimeTemplate, i7.SortableColumn, i7.FrozenColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.ButtonDirective, i8.Button, i9.Dropdown, i10.Dialog, i11.NgSelectComponent, i11.NgOptionTemplateDirective, i12.Tooltip, i13.InputText, i14.Checkbox, i6.AsyncPipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .opportunity-contact-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .opportunity-contact-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3Bwb3J0dW5pdGllcy9vcHBvcnR1bml0aWVzLWRldGFpbHMvb3Bwb3J0dW5pdGllcy1jb250YWN0cy9vcHBvcnR1bml0aWVzLWNvbnRhY3RzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0VBSUUscUJBQUE7RUFDQSxXQUFBO0FBQ0Y7O0FBSUk7RUFDRSxrQkFBQTtBQUROO0FBR007RUFDRSw0QkFBQTtFQUNBLDJDQUFBO0FBRFI7QUFHUTtFQUNFLFNBQUE7QUFEVjtBQUtNO0VBQ0UsNEJBQUE7RUFDQSxpQkFBQTtBQUhSIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gIGNvbG9yOiB2YXIoLS1yZWQtNTAwKTtcclxuICByaWdodDogMTBweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIHtcclxuICAub3Bwb3J0dW5pdHktY29udGFjdC1wb3B1cCB7XHJcbiAgICAucC1kaWFsb2cge1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zdXJmYWNlLTEwMCk7XHJcblxyXG4gICAgICAgIGg0IHtcclxuICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgIHBhZGRpbmc6IDEuNzE0cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "fork<PERSON><PERSON>n", "distinctUntilChanged", "switchMap", "tap", "catchError", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "OpportunitiesContactsComponent_ng_template_10_Template_button_click_16_listener", "$event", "contact_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "stopPropagation", "ɵɵresetView", "confirmRemove", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "business_partner", "bp_full_name", "contact_person_function_name", "name", "contact_person_department_name", "phone_number", "mobile", "email_address", "ɵɵtemplate", "OpportunitiesContactsComponent_div_25_div_1_Template", "f", "errors", "OpportunitiesContactsComponent_div_42_div_1_Template", "OpportunitiesContactsComponent_div_73_div_1_Template", "OpportunitiesContactsComponent_div_73_div_2_Template", "submitted", "OpportunitiesContactsComponent_div_81_div_1_Template", "tmp_1_0", "ContactForm", "get", "OpportunitiesContactsComponent_div_91_div_1_Template", "OpportunitiesContactsComponent_div_92_div_1_Template", "item_r4", "email", "phone", "OpportunitiesContactsComponent_ng_template_108_span_2_Template", "OpportunitiesContactsComponent_ng_template_108_span_3_Template", "OpportunitiesContactsComponent_ng_template_108_span_4_Template", "ɵɵtextInterpolate", "bp_id", "OpportunitiesContactsComponent", "constructor", "route", "opportunitiesservice", "accountservice", "formBuilder", "messageservice", "confirmationservice", "unsubscribe$", "contactDetails", "id", "opportunity_id", "departments", "functions", "addDialogVisible", "existingDialogVisible", "visible", "position", "editid", "documentId", "saving", "cpDepartments", "cpFunctions", "contactLoading", "contactInput$", "defaultOptions", "selectedContacts", "group", "first_name", "required", "middle_name", "last_name", "job_title", "pattern", "contact_person_vip_type", "validity_end_date", "contactexisting", "ngOnInit", "parent", "snapshot", "paramMap", "loadContacts", "getCPDepartment", "getCPFunction", "pipe", "subscribe", "data", "item", "description", "value", "code", "opportunity", "response", "opportunity_contact_parties", "contact", "addresses", "emails", "phone_numbers", "find", "phone_number_type", "d", "contact_person_func_and_depts", "contact_person_department", "contact_person_function", "contacts$", "term", "params", "getContacts", "error", "onSubmit", "_this", "_asyncToGenerator", "existing", "opportunity_party_contact_party_id", "role_code", "createExistingContact", "complete", "reset", "add", "severity", "detail", "getOpportunityByID", "invalid", "console", "log", "opportunity_party_contact_main_indicator", "createOpportunityContact", "confirm", "message", "header", "icon", "accept", "remove", "deleteContact", "next", "showNewDialog", "showExistingDialog", "controls", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "OpportunitiesService", "i3", "AccountService", "i4", "FormBuilder", "i5", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "OpportunitiesContactsComponent_Template", "rf", "ctx", "OpportunitiesContactsComponent_Template_p_button_click_5_listener", "OpportunitiesContactsComponent_Template_p_button_click_6_listener", "ɵɵtwoWayListener", "OpportunitiesContactsComponent_Template_p_table_selectionChange_8_listener", "ɵɵtwoWayBindingSet", "OpportunitiesContactsComponent_ng_template_9_Template", "OpportunitiesContactsComponent_ng_template_10_Template", "OpportunitiesContactsComponent_ng_template_11_Template", "OpportunitiesContactsComponent_ng_template_12_Template", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_13_listener", "OpportunitiesContactsComponent_ng_template_14_Template", "OpportunitiesContactsComponent_div_25_Template", "OpportunitiesContactsComponent_div_42_Template", "OpportunitiesContactsComponent_div_73_Template", "OpportunitiesContactsComponent_div_81_Template", "OpportunitiesContactsComponent_div_91_Template", "OpportunitiesContactsComponent_div_92_Template", "OpportunitiesContactsComponent_div_93_Template", "OpportunitiesContactsComponent_Template_button_click_95_listener", "OpportunitiesContactsComponent_Template_button_click_96_listener", "OpportunitiesContactsComponent_Template_p_dialog_visibleChange_97_listener", "OpportunitiesContactsComponent_ng_template_98_Template", "OpportunitiesContactsComponent_ng_template_108_Template", "OpportunitiesContactsComponent_Template_button_click_110_listener", "OpportunitiesContactsComponent_Template_button_click_111_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "tmp_25_0", "touched", "tmp_28_0", "_c2", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-contacts\\opportunities-contacts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { OpportunitiesService } from '../../opportunities.service';\r\nimport { AccountService } from 'src/app/store/account/account.service';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport {\r\n  Subject,\r\n  takeUntil,\r\n  Observable,\r\n  concat,\r\n  map,\r\n  of,\r\n  forkJoin,\r\n} from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  catchError,\r\n} from 'rxjs/operators';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-contacts',\r\n  templateUrl: './opportunities-contacts.component.html',\r\n  styleUrl: './opportunities-contacts.component.scss',\r\n})\r\nexport class OpportunitiesContactsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contactDetails: any = null;\r\n  public id: string = '';\r\n  public opportunity_id: string = '';\r\n  public bp_id: string = '';\r\n  public departments: any = null;\r\n  public functions: any = null;\r\n  public addDialogVisible: boolean = false;\r\n  public existingDialogVisible: boolean = false;\r\n  public visible: boolean = false;\r\n  public position: string = 'right';\r\n  public submitted = false;\r\n  public editid: string = '';\r\n  public documentId: string = '';\r\n  public saving = false;\r\n  public cpDepartments: { name: string; value: string }[] = [];\r\n  public cpFunctions: { name: string; value: string }[] = [];\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public selectedContacts = [];\r\n\r\n  public ContactForm: FormGroup = this.formBuilder.group({\r\n    first_name: ['', [Validators.required]],\r\n    middle_name: [''],\r\n    last_name: ['', [Validators.required]],\r\n    job_title: [''],\r\n    contact_person_function_name: [''],\r\n    contact_person_department_name: [''],\r\n    email_address: ['', [Validators.required, Validators.email]],\r\n    phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\r\n    contact_person_vip_type: [''],\r\n    validity_end_date: [''],\r\n    contactexisting: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private accountservice: AccountService,\r\n    private formBuilder: FormBuilder,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.opportunity_id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.loadContacts();\r\n    forkJoin({\r\n      departments: this.accountservice.getCPDepartment(),\r\n      functions: this.accountservice.getCPFunction(),\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(({ departments, functions }) => {\r\n        // Load departments\r\n        this.cpDepartments = (departments?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n\r\n        // Load functions\r\n        this.cpFunctions = (functions?.data || []).map((item: any) => ({\r\n          name: item.description,\r\n          value: item.code,\r\n        }));\r\n        this.opportunitiesservice.opportunity\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe((response: any) => {\r\n            if (response) {\r\n              this.id = response?.opportunity_id;\r\n              this.bp_id = response?.business_partner?.bp_id;\r\n              this.documentId = response?.documentId;\r\n              this.contactDetails = response?.opportunity_contact_parties || [];\r\n\r\n              this.contactDetails = this.contactDetails.map((contact: any) => {\r\n                return {\r\n                  ...contact,\r\n                  email_address:\r\n                    contact?.business_partner?.addresses?.[0]?.emails?.[0]\r\n                      ?.email_address || '',\r\n                  phone_number: (\r\n                    contact?.business_partner?.addresses?.[0]?.phone_numbers ||\r\n                    []\r\n                  ).find((item: any) => item.phone_number_type === '1')\r\n                    ?.phone_number,\r\n                  mobile: (\r\n                    contact?.business_partner?.addresses?.[0]?.phone_numbers ||\r\n                    []\r\n                  ).find((item: any) => item.phone_number_type === '3')\r\n                    ?.phone_number,\r\n\r\n                  // Ensure department & function values are set correctly\r\n                  contact_person_department_name:\r\n                    this.cpDepartments?.find(\r\n                      (d: any) =>\r\n                        d.value ===\r\n                        contact?.business_partner\r\n                          ?.contact_person_func_and_depts?.[0]\r\n                          ?.contact_person_department\r\n                    ) || null,\r\n\r\n                  contact_person_function_name:\r\n                    this.cpFunctions?.find(\r\n                      (f: any) =>\r\n                        f.value ===\r\n                        contact?.business_partner\r\n                          ?.contact_person_func_and_depts?.[0]\r\n                          ?.contact_person_function\r\n                    ) || null,\r\n                };\r\n              });\r\n            }\r\n          });\r\n      });\r\n  }\r\n\r\n  private loadContacts() {\r\n    this.contacts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.contactInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.contactLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP001',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n\r\n          return this.accountservice.getContacts(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.contactLoading = false)),\r\n            catchError((error) => {\r\n              this.contactLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n    this.visible = true;\r\n    if (this.ContactForm.value?.contactexisting) {\r\n      const existing = this.ContactForm.value.contactexisting;\r\n\r\n      const data = {\r\n        opportunity_party_contact_party_id: existing?.bp_id,\r\n        opportunity_id: this.opportunity_id,\r\n        role_code: '',\r\n      };\r\n\r\n      this.saving = true;\r\n\r\n      this.opportunitiesservice\r\n        .createExistingContact(data)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.saving = false;\r\n            this.existingDialogVisible = false;\r\n            this.ContactForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Contact Added successfully!.',\r\n            });\r\n            this.opportunitiesservice\r\n              .getOpportunityByID(this.opportunity_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe();\r\n          },\r\n          error: () => {\r\n            this.saving = false;\r\n            this.addDialogVisible = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n\r\n      // Skip rest of logic for new contact\r\n      return;\r\n    }\r\n    if (this.ContactForm.invalid) {\r\n      console.log('Form is invalid:', this.ContactForm.errors);\r\n      this.visible = true;\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.ContactForm.value };\r\n\r\n    const data = {\r\n      bp_id: this.bp_id,\r\n      first_name: value?.first_name || '',\r\n      middle_name: value?.middle_name,\r\n      last_name: value?.last_name || '',\r\n      job_title: value?.job_title || '',\r\n      contact_person_function_name:\r\n        value?.contact_person_function_name?.name || '',\r\n      contact_person_function: value?.contact_person_function_name?.value || '',\r\n      contact_person_department_name:\r\n        value?.contact_person_department_name?.name || '',\r\n      contact_person_department:\r\n        value?.contact_person_department_name?.value || '',\r\n      email_address: value?.email_address,\r\n      phone_number: value?.phone_number,\r\n      mobile: value?.mobile,\r\n      contact_person_vip_type: value?.contact_person_vip_type,\r\n      opportunity_party_contact_main_indicator: '',\r\n      role_code: '',\r\n      opportunity_id: this.opportunity_id,\r\n    };\r\n\r\n    this.opportunitiesservice\r\n      .createOpportunityContact(data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        complete: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.existingDialogVisible = false;\r\n          this.ContactForm.reset();\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Contact created successfully!.',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this.addDialogVisible = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.opportunitiesservice\r\n      .deleteContact(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.opportunitiesservice\r\n            .getOpportunityByID(this.opportunity_id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  showNewDialog(position: string) {\r\n    this.position = position;\r\n    this.addDialogVisible = true;\r\n    this.submitted = false;\r\n    this.ContactForm.reset();\r\n  }\r\n\r\n  showExistingDialog(position: string) {\r\n    this.position = position;\r\n    this.existingDialogVisible = true;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.ContactForm.controls;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Contacts</h4>\r\n        <div class=\"flex gap-3 ml-auto\">\r\n            <!-- <p-button label=\"Reactivate\" icon=\"pi pi-check\" iconPos=\"right\" class=\"font-semibold\" [rounded]=\"true\"\r\n                [styleClass]=\"'px-3'\" (click)=\"reactivateSelectedContacts()\"\r\n                [disabled]=\"!selectedContacts || selectedContacts.length === 0\" /> -->\r\n            <p-button label=\"Add New\" (click)=\"showNewDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\"\r\n                class=\"ml-auto\" [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n            <p-button label=\"Existing Contact\" (click)=\"showExistingDialog('right')\" icon=\"pi pi-users\" iconPos=\"right\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"contactDetails\" [(selection)]=\"selectedContacts\" dataKey=\"id\" [rows]=\"10\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem table-checkbox text-center\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn pSortableColumn=\"business_partner.bp_full_name\" style=\"width: 12%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Name\r\n                            <p-sortIcon field=\"business_partner.bp_full_name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <th pSortableColumn=\"contact_person_function_name.name\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">Function<p-sortIcon\r\n                                field=\"contact_person_department_name.name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <th pSortableColumn=\"contact_person_department_name.name\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Department\r\n                            <p-sortIcon field=\"contact_person_department_name.name\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"phone_number\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Phone\r\n                            <p-sortIcon field=\"phone_number\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"mobile\" style=\"width: 10%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Mobile\r\n                            <p-sortIcon field=\"mobile\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"email_address\" style=\"width: 20%\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            E-Mail\r\n                            <p-sortIcon field=\"email_address\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <!-- <th style=\"width: 7%\">Primary Contact</th> -->\r\n                    <th style=\"width: 5%\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-contact>\r\n                <tr>\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"contact\" />\r\n                    </td>\r\n                    <td pFrozenColumn>\r\n                        {{ contact?.business_partner?.bp_full_name || \"-\" }}\r\n                    </td>\r\n\r\n                    <td>\r\n                        {{ contact?.contact_person_function_name?.name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.contact_person_department_name?.name || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.phone_number || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.mobile || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ contact?.email_address || \"-\" }}\r\n                    </td>\r\n                    <!-- <td>\r\n                        <p-checkbox [binary]=\"true\" [ngModel]=\"contact.contact_person_vip_type\"\r\n                            [disabled]=\"true\"></p-checkbox>\r\n                    </td> -->\r\n                    <td>\r\n\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(contact);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td class=\"border-round-left-lg\" colspan=\"13\">No contacts found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"13\" class=\"border-round-left-lg\">\r\n                        Loading contacts data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"addDialogVisible\" [style]=\"{ width: '38rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"First Name \">\r\n                <span class=\"material-symbols-rounded\">person</span>First Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"first_name\" formControlName=\"first_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['first_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['first_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['first_name'].errors['required']\">\r\n                        First Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Middle Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Middle Name\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"middle_name\" formControlName=\"middle_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Last Name\">\r\n                <span class=\"material-symbols-rounded\">person</span>Last Name\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"last_name\" formControlName=\"last_name\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['last_name'].errors }\" />\r\n                <div *ngIf=\"submitted && f['last_name'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['last_name'].errors['required']\">\r\n                        Last Name is required.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Job Title\">\r\n                <span class=\"material-symbols-rounded\">work</span>Job Title\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"job_title\" formControlName=\"job_title\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Function\">\r\n                <span class=\"material-symbols-rounded\">functions</span>Function\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpFunctions\" formControlName=\"contact_person_function_name\" optionLabel=\"name\"\r\n                    dataKey=\"value\" placeholder=\"Select Function\" [styleClass]=\"'h-3rem w-full'\"></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Department\">\r\n                <span class=\"material-symbols-rounded\">inbox_text_person</span>Department\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-dropdown [options]=\"cpDepartments\" formControlName=\"contact_person_department_name\"\r\n                    optionLabel=\"name\" dataKey=\"value\" placeholder=\"Select Department\" [styleClass]=\"'h-3rem w-full'\">\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Email\">\r\n                <span class=\"material-symbols-rounded\">mail</span>Email<span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"email\" id=\"email_address\" formControlName=\"email_address\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['email_address'].errors }\" />\r\n                <div *ngIf=\"submitted && f['email_address'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"\r\n              submitted &&\r\n              f['email_address'].errors &&\r\n              f['email_address'].errors['required']\r\n            \">\r\n                        Email is required.\r\n                    </div>\r\n                    <div *ngIf=\"f['email_address'].errors['email']\">\r\n                        Email is invalid.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Phone\">\r\n                <span class=\"material-symbols-rounded\">phone_in_talk</span>Phone\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"phone_number\" formControlName=\"phone_number\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" />\r\n                <div *ngIf=\"ContactForm.get('phone_number')?.touched && ContactForm.get('phone_number')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('phone_number')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Phone number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Mobile\">\r\n                <span class=\"material-symbols-rounded\">smartphone</span>Mobile #\r\n                <span class=\"text-red-500\">*</span>\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <input pInputText type=\"text\" id=\"mobile\" formControlName=\"mobile\" class=\"h-3rem w-full\"\r\n                    autocomplete=\"off\" [ngClass]=\"{ 'is-invalid': submitted && f['mobile'].errors }\" />\r\n                <div *ngIf=\"submitted && f['mobile'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"f['mobile'].errors['required']\">\r\n                        Mobile is required.\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"ContactForm.get('mobile')?.touched && ContactForm.get('mobile')?.invalid\">\r\n                    <div *ngIf=\"ContactForm.get('mobile')?.errors?.['pattern']\" class=\"p-error\">\r\n                        Please enter a valid Mobile number.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"editid && ContactForm.value.first_name\">\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"DeActivate\">\r\n                    <span class=\"material-symbols-rounded\">remove_circle_outline</span>DeActivate\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"validity_end_date\" formControlName=\"validity_end_date\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"VIP Contact\">\r\n                    <span class=\"material-symbols-rounded\">star</span>VIP Contact\r\n                </label>\r\n                <div class=\"form-input flex-1 relative\">\r\n                    <p-checkbox id=\"contact_person_vip_type\" formControlName=\"contact_person_vip_type\" [binary]=\"true\"\r\n                        class=\"h-3rem w-full\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"addDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"existingDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"opportunity-contact-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Contact Information</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"ContactForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <label class=\"relative flex align-items-center text font-semibold w-12rem gap-1\" for=\"Contacts\">\r\n                <span class=\"material-symbols-rounded\">person</span>Contacts\r\n            </label>\r\n            <div class=\"form-input flex-1 relative\">\r\n                <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" [hideSelected]=\"true\"\r\n                    [loading]=\"contactLoading\" [minTermLength]=\"0\" formControlName=\"contactexisting\"\r\n                    [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\" appendTo=\"body\">\r\n                    <ng-template ng-option-tmp let-item=\"item\">\r\n                        <span>{{ item.bp_id }}</span>\r\n                        <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                        <span *ngIf=\"item.phone\"> : {{ item.phone }}</span>\r\n                    </ng-template>\r\n                </ng-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"existingDialogVisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": ";AAGA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SACEC,OAAO,EACPC,SAAS,EAETC,MAAM,EACNC,GAAG,EACHC,EAAE,EACFC,QAAQ,QACH,MAAM;AAEb,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,UAAU,QACL,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAHC,EADJ,CAAAC,cAAA,SAAI,aACsF;IAClFD,EAAA,CAAAE,SAAA,4BAAyB;IAC7BF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAAqF,cACtC;IACvCD,EAAA,CAAAI,MAAA,aACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA+D;IAEvEF,EADI,CAAAG,YAAA,EAAM,EACL;IAGDH,EADJ,CAAAC,cAAA,aAA2E,cAC5B;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAE,SAAA,sBACc;IAErEF,EADI,CAAAG,YAAA,EAAM,EACL;IAGDH,EADJ,CAAAC,cAAA,cAA6E,eAC9B;IACvCD,EAAA,CAAAI,MAAA,oBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAqE;IAE7EF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAsD,eACP;IACvCD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAgD,eACD;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAwC;IAEhDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAuD,eACR;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA+C;IAEvDF,EADI,CAAAG,YAAA,EAAM,EACL;IAELH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAChCJ,EADgC,CAAAG,YAAA,EAAK,EAChC;;;;;;IAKDH,EADJ,CAAAC,cAAA,SAAI,aACuE;IACnED,EAAA,CAAAE,SAAA,0BAAqC;IACzCF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAkB;IACdD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAODH,EAFJ,CAAAC,cAAA,UAAI,kBAGgE;IAA5DD,EAAA,CAAAK,UAAA,mBAAAC,gFAAAC,MAAA;MAAA,MAAAC,UAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAASN,MAAA,CAAAO,eAAA,EAAwB;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAEH,MAAA,CAAAI,aAAA,CAAAR,UAAA,CAAsB;IAAA,EAAE;IAEvER,EAFwE,CAAAG,YAAA,EAAS,EACxE,EACJ;;;;IA9BoBH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,UAAAV,UAAA,CAAiB;IAGlCR,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAY,gBAAA,kBAAAZ,UAAA,CAAAY,gBAAA,CAAAC,YAAA,cACJ;IAGIrB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAc,4BAAA,kBAAAd,UAAA,CAAAc,4BAAA,CAAAC,IAAA,cACJ;IAEIvB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAgB,8BAAA,kBAAAhB,UAAA,CAAAgB,8BAAA,CAAAD,IAAA,cACJ;IAEIvB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAiB,YAAA,cACJ;IAEIzB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAkB,MAAA,cACJ;IAEI1B,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAmB,kBAAA,OAAAX,UAAA,kBAAAA,UAAA,CAAAmB,aAAA,cACJ;;;;;IAcA3B,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IACpEJ,EADoE,CAAAG,YAAA,EAAK,EACpE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC8C;IAC1CD,EAAA,CAAAI,MAAA,8CACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IAchBH,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAI,MAAA,gCACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA4B,UAAA,IAAAC,oDAAA,kBAAgD;IAGpD7B,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAN,MAAA,CAAAkB,CAAA,eAAAC,MAAA,aAAwC;;;;;IAyB9C/B,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAI,MAAA,+BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA4B,UAAA,IAAAI,oDAAA,kBAA+C;IAGnDhC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAN,MAAA,CAAAkB,CAAA,cAAAC,MAAA,aAAuC;;;;;IA2C7C/B,EAAA,CAAAC,cAAA,UAIN;IACUD,EAAA,CAAAI,MAAA,2BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAI,MAAA,0BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAXVH,EAAA,CAAAC,cAAA,cACmE;IAQ/DD,EAPA,CAAA4B,UAAA,IAAAK,oDAAA,kBAIN,IAAAC,oDAAA,kBAGsD;IAGpDlC,EAAA,CAAAG,YAAA,EAAM;;;;IAVIH,EAAA,CAAAiB,SAAA,EAIf;IAJejB,EAAA,CAAAkB,UAAA,SAAAN,MAAA,CAAAuB,SAAA,IAAAvB,MAAA,CAAAkB,CAAA,kBAAAC,MAAA,IAAAnB,MAAA,CAAAkB,CAAA,kBAAAC,MAAA,aAIf;IAGe/B,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,SAAAN,MAAA,CAAAkB,CAAA,kBAAAC,MAAA,UAAwC;;;;;IAc9C/B,EAAA,CAAAC,cAAA,cAAkF;IAC9ED,EAAA,CAAAI,MAAA,2CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAkG;IAC9FD,EAAA,CAAA4B,UAAA,IAAAQ,oDAAA,kBAAkF;IAGtFpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAHIH,EAAA,CAAAiB,SAAA,EAA0D;IAA1DjB,EAAA,CAAAkB,UAAA,UAAAmB,OAAA,GAAAzB,MAAA,CAAA0B,WAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAA0D;;;;;IAgBhE/B,EAAA,CAAAC,cAAA,UAA4C;IACxCD,EAAA,CAAAI,MAAA,4BACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJVH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAA4B,UAAA,IAAAY,oDAAA,kBAA4C;IAGhDxC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAiB,SAAA,EAAoC;IAApCjB,EAAA,CAAAkB,UAAA,SAAAN,MAAA,CAAAkB,CAAA,WAAAC,MAAA,aAAoC;;;;;IAK1C/B,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,MAAA,4CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAsF;IAClFD,EAAA,CAAA4B,UAAA,IAAAa,oDAAA,kBAA4E;IAGhFzC,EAAA,CAAAG,YAAA,EAAM;;;;;IAHIH,EAAA,CAAAiB,SAAA,EAAoD;IAApDjB,EAAA,CAAAkB,UAAA,UAAAmB,OAAA,GAAAzB,MAAA,CAAA0B,WAAA,CAAAC,GAAA,6BAAAF,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAAoD;;;;;IAS1D/B,EAHZ,CAAAC,cAAA,UAAoD,cACK,gBACiD,eACvD;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAI,MAAA,kBACvE;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAwC;IACpCD,EAAA,CAAAE,SAAA,qBACuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,cAAqD,gBACkD,gBACxD;IAAAD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAI,MAAA,oBACtD;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACpCD,EAAA,CAAAE,SAAA,sBACuC;IAGnDF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;IAb6EH,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,UAAA,gBAAe;IASHlB,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,UAAA,gBAAe;;;;;IAiB9GlB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IAcZH,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAiB,SAAA,EAAyB;IAAzBjB,EAAA,CAAAmB,kBAAA,QAAAuB,OAAA,CAAArB,YAAA,KAAyB;;;;;IAC1DrB,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAiB,SAAA,EAAkB;IAAlBjB,EAAA,CAAAmB,kBAAA,QAAAuB,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5C3C,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAiB,SAAA,EAAkB;IAAlBjB,EAAA,CAAAmB,kBAAA,QAAAuB,OAAA,CAAAE,KAAA,KAAkB;;;;;IAH5C5C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAFA,CAAA4B,UAAA,IAAAiB,8DAAA,mBAAgC,IAAAC,8DAAA,mBACP,IAAAC,8DAAA,mBACA;;;;IAHnB/C,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAgD,iBAAA,CAAAN,OAAA,CAAAO,KAAA,CAAgB;IACfjD,EAAA,CAAAiB,SAAA,EAAuB;IAAvBjB,EAAA,CAAAkB,UAAA,SAAAwB,OAAA,CAAArB,YAAA,CAAuB;IACvBrB,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,SAAAwB,OAAA,CAAAC,KAAA,CAAgB;IAChB3C,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,SAAAwB,OAAA,CAAAE,KAAA,CAAgB;;;AD5Q/C,OAAM,MAAOM,8BAA8B;EAuCzCC,YACUC,KAAqB,EACrBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,WAAwB,EACxBC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA5CrB,KAAAC,YAAY,GAAG,IAAIpE,OAAO,EAAQ;IACnC,KAAAqE,cAAc,GAAQ,IAAI;IAC1B,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAZ,KAAK,GAAW,EAAE;IAClB,KAAAa,WAAW,GAAQ,IAAI;IACvB,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAhC,SAAS,GAAG,KAAK;IACjB,KAAAiC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,aAAa,GAAsC,EAAE;IACrD,KAAAC,WAAW,GAAsC,EAAE;IAEnD,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAIpF,OAAO,EAAU;IACpC,KAAAqF,cAAc,GAAQ,EAAE;IACzB,KAAAC,gBAAgB,GAAG,EAAE;IAErB,KAAAtC,WAAW,GAAc,IAAI,CAACiB,WAAW,CAACsB,KAAK,CAAC;MACrDC,UAAU,EAAE,CAAC,EAAE,EAAE,CAACzF,UAAU,CAAC0F,QAAQ,CAAC,CAAC;MACvCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC5F,UAAU,CAAC0F,QAAQ,CAAC,CAAC;MACtCG,SAAS,EAAE,CAAC,EAAE,CAAC;MACf5D,4BAA4B,EAAE,CAAC,EAAE,CAAC;MAClCE,8BAA8B,EAAE,CAAC,EAAE,CAAC;MACpCG,aAAa,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAAC0F,QAAQ,EAAE1F,UAAU,CAACsD,KAAK,CAAC,CAAC;MAC5DlB,YAAY,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC8F,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACzDzD,MAAM,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAAC0F,QAAQ,EAAE1F,UAAU,CAAC8F,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MACxEC,uBAAuB,EAAE,CAAC,EAAE,CAAC;MAC7BC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,eAAe,EAAE,CAAC,EAAE;KACrB,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACT,KAAK,CAACoC,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACnD,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1E,IAAI,CAACoD,YAAY,EAAE;IACnBhG,QAAQ,CAAC;MACPmE,WAAW,EAAE,IAAI,CAACR,cAAc,CAACsC,eAAe,EAAE;MAClD7B,SAAS,EAAE,IAAI,CAACT,cAAc,CAACuC,aAAa;KAC7C,CAAC,CACCC,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACmE,YAAY,CAAC,CAAC,CAClCqC,SAAS,CAAC,CAAC;MAAEjC,WAAW;MAAEC;IAAS,CAAE,KAAI;MACxC;MACA,IAAI,CAACQ,aAAa,GAAG,CAACT,WAAW,EAAEkC,IAAI,IAAI,EAAE,EAAEvG,GAAG,CAAEwG,IAAS,KAAM;QACjE1E,IAAI,EAAE0E,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MAEH;MACA,IAAI,CAAC5B,WAAW,GAAG,CAACT,SAAS,EAAEiC,IAAI,IAAI,EAAE,EAAEvG,GAAG,CAAEwG,IAAS,KAAM;QAC7D1E,IAAI,EAAE0E,IAAI,CAACC,WAAW;QACtBC,KAAK,EAAEF,IAAI,CAACG;OACb,CAAC,CAAC;MACH,IAAI,CAAC/C,oBAAoB,CAACgD,WAAW,CAClCP,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACmE,YAAY,CAAC,CAAC,CAClCqC,SAAS,CAAEO,QAAa,IAAI;QAC3B,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAC1C,EAAE,GAAG0C,QAAQ,EAAEzC,cAAc;UAClC,IAAI,CAACZ,KAAK,GAAGqD,QAAQ,EAAElF,gBAAgB,EAAE6B,KAAK;UAC9C,IAAI,CAACoB,UAAU,GAAGiC,QAAQ,EAAEjC,UAAU;UACtC,IAAI,CAACV,cAAc,GAAG2C,QAAQ,EAAEC,2BAA2B,IAAI,EAAE;UAEjE,IAAI,CAAC5C,cAAc,GAAG,IAAI,CAACA,cAAc,CAAClE,GAAG,CAAE+G,OAAY,IAAI;YAC7D,OAAO;cACL,GAAGA,OAAO;cACV7E,aAAa,EACX6E,OAAO,EAAEpF,gBAAgB,EAAEqF,SAAS,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,EAClD/E,aAAa,IAAI,EAAE;cACzBF,YAAY,EAAE,CACZ+E,OAAO,EAAEpF,gBAAgB,EAAEqF,SAAS,GAAG,CAAC,CAAC,EAAEE,aAAa,IACxD,EAAE,EACFC,IAAI,CAAEX,IAAS,IAAKA,IAAI,CAACY,iBAAiB,KAAK,GAAG,CAAC,EACjDpF,YAAY;cAChBC,MAAM,EAAE,CACN8E,OAAO,EAAEpF,gBAAgB,EAAEqF,SAAS,GAAG,CAAC,CAAC,EAAEE,aAAa,IACxD,EAAE,EACFC,IAAI,CAAEX,IAAS,IAAKA,IAAI,CAACY,iBAAiB,KAAK,GAAG,CAAC,EACjDpF,YAAY;cAEhB;cACAD,8BAA8B,EAC5B,IAAI,CAAC+C,aAAa,EAAEqC,IAAI,CACrBE,CAAM,IACLA,CAAC,CAACX,KAAK,KACPK,OAAO,EAAEpF,gBAAgB,EACrB2F,6BAA6B,GAAG,CAAC,CAAC,EAClCC,yBAAyB,CAChC,IAAI,IAAI;cAEX1F,4BAA4B,EAC1B,IAAI,CAACkD,WAAW,EAAEoC,IAAI,CACnB9E,CAAM,IACLA,CAAC,CAACqE,KAAK,KACPK,OAAO,EAAEpF,gBAAgB,EACrB2F,6BAA6B,GAAG,CAAC,CAAC,EAClCE,uBAAuB,CAC9B,IAAI;aACR;UACH,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEQtB,YAAYA,CAAA;IAClB,IAAI,CAACuB,SAAS,GAAG1H,MAAM,CACrBE,EAAE,CAAC,IAAI,CAACiF,cAAc,CAAC;IAAE;IACzB,IAAI,CAACD,aAAa,CAACoB,IAAI,CACrBlG,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAAC2E,cAAc,GAAG,IAAK,CAAC,EACvC5E,SAAS,CAAEsH,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MAEA,OAAO,IAAI,CAAC7D,cAAc,CAAC+D,WAAW,CAACD,MAAM,CAAC,CAACtB,IAAI,CACjDrG,GAAG,CAAEuG,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFlG,GAAG,CAAC,MAAO,IAAI,CAAC2E,cAAc,GAAG,KAAM,CAAC,EACxC1E,UAAU,CAAEuH,KAAK,IAAI;QACnB,IAAI,CAAC7C,cAAc,GAAG,KAAK;QAC3B,OAAO/E,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEM6H,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACrF,SAAS,GAAG,IAAI;MACrBqF,KAAI,CAACtD,OAAO,GAAG,IAAI;MACnB,IAAIsD,KAAI,CAAClF,WAAW,CAAC6D,KAAK,EAAEb,eAAe,EAAE;QAC3C,MAAMoC,QAAQ,GAAGF,KAAI,CAAClF,WAAW,CAAC6D,KAAK,CAACb,eAAe;QAEvD,MAAMU,IAAI,GAAG;UACX2B,kCAAkC,EAAED,QAAQ,EAAEzE,KAAK;UACnDY,cAAc,EAAE2D,KAAI,CAAC3D,cAAc;UACnC+D,SAAS,EAAE;SACZ;QAEDJ,KAAI,CAAClD,MAAM,GAAG,IAAI;QAElBkD,KAAI,CAACnE,oBAAoB,CACtBwE,qBAAqB,CAAC7B,IAAI,CAAC,CAC3BF,IAAI,CAACvG,SAAS,CAACiI,KAAI,CAAC9D,YAAY,CAAC,CAAC,CAClCqC,SAAS,CAAC;UACT+B,QAAQ,EAAEA,CAAA,KAAK;YACbN,KAAI,CAAClD,MAAM,GAAG,KAAK;YACnBkD,KAAI,CAACvD,qBAAqB,GAAG,KAAK;YAClCuD,KAAI,CAAClF,WAAW,CAACyF,KAAK,EAAE;YACxBP,KAAI,CAAChE,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFV,KAAI,CAACnE,oBAAoB,CACtB8E,kBAAkB,CAACX,KAAI,CAAC3D,cAAc,CAAC,CACvCiC,IAAI,CAACvG,SAAS,CAACiI,KAAI,CAAC9D,YAAY,CAAC,CAAC,CAClCqC,SAAS,EAAE;UAChB,CAAC;UACDuB,KAAK,EAAEA,CAAA,KAAK;YACVE,KAAI,CAAClD,MAAM,GAAG,KAAK;YACnBkD,KAAI,CAACxD,gBAAgB,GAAG,KAAK;YAC7BwD,KAAI,CAAChE,cAAc,CAACwE,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;QAEJ;QACA;MACF;MACA,IAAIV,KAAI,CAAClF,WAAW,CAAC8F,OAAO,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEd,KAAI,CAAClF,WAAW,CAACP,MAAM,CAAC;QACxDyF,KAAI,CAACtD,OAAO,GAAG,IAAI;QACnB;MACF;MAEAsD,KAAI,CAAClD,MAAM,GAAG,IAAI;MAClB,MAAM6B,KAAK,GAAG;QAAE,GAAGqB,KAAI,CAAClF,WAAW,CAAC6D;MAAK,CAAE;MAE3C,MAAMH,IAAI,GAAG;QACX/C,KAAK,EAAEuE,KAAI,CAACvE,KAAK;QACjB6B,UAAU,EAAEqB,KAAK,EAAErB,UAAU,IAAI,EAAE;QACnCE,WAAW,EAAEmB,KAAK,EAAEnB,WAAW;QAC/BC,SAAS,EAAEkB,KAAK,EAAElB,SAAS,IAAI,EAAE;QACjCC,SAAS,EAAEiB,KAAK,EAAEjB,SAAS,IAAI,EAAE;QACjC5D,4BAA4B,EAC1B6E,KAAK,EAAE7E,4BAA4B,EAAEC,IAAI,IAAI,EAAE;QACjD0F,uBAAuB,EAAEd,KAAK,EAAE7E,4BAA4B,EAAE6E,KAAK,IAAI,EAAE;QACzE3E,8BAA8B,EAC5B2E,KAAK,EAAE3E,8BAA8B,EAAED,IAAI,IAAI,EAAE;QACnDyF,yBAAyB,EACvBb,KAAK,EAAE3E,8BAA8B,EAAE2E,KAAK,IAAI,EAAE;QACpDxE,aAAa,EAAEwE,KAAK,EAAExE,aAAa;QACnCF,YAAY,EAAE0E,KAAK,EAAE1E,YAAY;QACjCC,MAAM,EAAEyE,KAAK,EAAEzE,MAAM;QACrB0D,uBAAuB,EAAEe,KAAK,EAAEf,uBAAuB;QACvDmD,wCAAwC,EAAE,EAAE;QAC5CX,SAAS,EAAE,EAAE;QACb/D,cAAc,EAAE2D,KAAI,CAAC3D;OACtB;MAED2D,KAAI,CAACnE,oBAAoB,CACtBmF,wBAAwB,CAACxC,IAAI,CAAC,CAC9BF,IAAI,CAACvG,SAAS,CAACiI,KAAI,CAAC9D,YAAY,CAAC,CAAC,CAClCqC,SAAS,CAAC;QACT+B,QAAQ,EAAEA,CAAA,KAAK;UACbN,KAAI,CAAClD,MAAM,GAAG,KAAK;UACnBkD,KAAI,CAACxD,gBAAgB,GAAG,KAAK;UAC7BwD,KAAI,CAACvD,qBAAqB,GAAG,KAAK;UAClCuD,KAAI,CAAClF,WAAW,CAACyF,KAAK,EAAE;UACxBP,KAAI,CAAChE,cAAc,CAACwE,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFV,KAAI,CAACnE,oBAAoB,CACtB8E,kBAAkB,CAACX,KAAI,CAAC3D,cAAc,CAAC,CACvCiC,IAAI,CAACvG,SAAS,CAACiI,KAAI,CAAC9D,YAAY,CAAC,CAAC,CAClCqC,SAAS,EAAE;QAChB,CAAC;QACDuB,KAAK,EAAEA,CAAA,KAAK;UACVE,KAAI,CAAClD,MAAM,GAAG,KAAK;UACnBkD,KAAI,CAACxD,gBAAgB,GAAG,KAAK;UAC7BwD,KAAI,CAAChE,cAAc,CAACwE,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEAlH,aAAaA,CAACiF,IAAS;IACrB,IAAI,CAACxC,mBAAmB,CAACgF,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC7C,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA6C,MAAMA,CAAC7C,IAAS;IACd,IAAI,CAAC5C,oBAAoB,CACtB0F,aAAa,CAAC9C,IAAI,CAAC5B,UAAU,CAAC,CAC9ByB,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACmE,YAAY,CAAC,CAAC,CAClCqC,SAAS,CAAC;MACTiD,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxF,cAAc,CAACwE,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC7E,oBAAoB,CACtB8E,kBAAkB,CAAC,IAAI,CAACtE,cAAc,CAAC,CACvCiC,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACmE,YAAY,CAAC,CAAC,CAClCqC,SAAS,EAAE;MAChB,CAAC;MACDuB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9D,cAAc,CAACwE,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAe,aAAaA,CAAC9E,QAAgB;IAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC7B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,WAAW,CAACyF,KAAK,EAAE;EAC1B;EAEAmB,kBAAkBA,CAAC/E,QAAgB;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACF,qBAAqB,GAAG,IAAI;EACnC;EAEA,IAAInC,CAACA,CAAA;IACH,OAAO,IAAI,CAACQ,WAAW,CAAC6G,QAAQ;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC1F,YAAY,CAACsF,IAAI,EAAE;IACxB,IAAI,CAACtF,YAAY,CAACoE,QAAQ,EAAE;EAC9B;;;uBAxTW5E,8BAA8B,EAAAlD,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA/J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAA9B9G,8BAA8B;MAAA+G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBnCvK,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAI,MAAA,eAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAKxDH,EAJJ,CAAAC,cAAA,aAAgC,kBAK+C;UADjDD,EAAA,CAAAK,UAAA,mBAAAoK,kEAAA;YAAA,OAASD,GAAA,CAAAvB,aAAA,CAAc,OAAO,CAAC;UAAA,EAAC;UAA1DjJ,EAAA,CAAAG,YAAA,EAC2E;UAC3EH,EAAA,CAAAC,cAAA,kBAC2D;UADxBD,EAAA,CAAAK,UAAA,mBAAAqK,kEAAA;YAAA,OAASF,GAAA,CAAAtB,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAGhFlJ,EAHQ,CAAAG,YAAA,EAC2D,EACzD,EACJ;UAGFH,EADJ,CAAAC,cAAA,aAAuB,iBAEwD;UADzCD,EAAA,CAAA2K,gBAAA,6BAAAC,2EAAArK,MAAA;YAAAP,EAAA,CAAA6K,kBAAA,CAAAL,GAAA,CAAA5F,gBAAA,EAAArE,MAAA,MAAAiK,GAAA,CAAA5F,gBAAA,GAAArE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAyF9DP,EAvFA,CAAA4B,UAAA,IAAAkJ,qDAAA,0BAAgC,KAAAC,sDAAA,0BA+CU,KAAAC,sDAAA,0BAmCJ,KAAAC,sDAAA,0BAKD;UASjDjL,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC0D;UADjCD,EAAA,CAAA2K,gBAAA,2BAAAO,2EAAA3K,MAAA;YAAAP,EAAA,CAAA6K,kBAAA,CAAAL,GAAA,CAAAxG,gBAAA,EAAAzD,MAAA,MAAAiK,GAAA,CAAAxG,gBAAA,GAAAzD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAEnDP,EAAA,CAAA4B,UAAA,KAAAuJ,sDAAA,yBAAgC;UAOpBnL,EAHZ,CAAAC,cAAA,gBAAwE,eACf,iBACkD,gBACxD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,mBACpD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC2F;UAC3FF,EAAA,CAAA4B,UAAA,KAAAwJ,8CAAA,kBACmE;UAM3EpL,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACkD,gBACxD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,oBACxD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UAEjCF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgD,gBACtD;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACpD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC0F;UAC1FF,EAAA,CAAA4B,UAAA,KAAAyJ,8CAAA,kBACmE;UAM3ErL,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACgD,gBACtD;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACtD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UAEjCF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC+C,gBACrD;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,iBAC3D;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,sBAC8F;UAEtGF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBACiD,gBACvD;UAAAD,EAAA,CAAAI,MAAA,yBAAiB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,mBACnE;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,sBAEa;UAErBF,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4C,gBAClD;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UACvFJ,EADuF,CAAAG,YAAA,EAAO,EACtF;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBAC8F;UAC9FF,EAAA,CAAA4B,UAAA,KAAA0J,8CAAA,kBACmE;UAa3EtL,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC4C,gBAClD;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,cAC/D;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACyB;UACzBF,EAAA,CAAA4B,UAAA,KAAA2J,8CAAA,kBAAkG;UAM1GvL,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAqD,iBAC6C,gBACnD;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,iBACxD;UAAAJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAChCJ,EADgC,CAAAG,YAAA,EAAO,EAC/B;UACRH,EAAA,CAAAC,cAAA,eAAwC;UACpCD,EAAA,CAAAE,SAAA,iBACuF;UAOvFF,EANA,CAAA4B,UAAA,KAAA4J,8CAAA,kBACmE,KAAAC,8CAAA,kBAKmB;UAM9FzL,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAA4B,UAAA,KAAA8J,8CAAA,mBAAoD;UAqBhD1L,EADJ,CAAAC,cAAA,eAAoD,kBAGT;UAAnCD,EAAA,CAAAK,UAAA,mBAAAsL,iEAAA;YAAA,OAAAnB,GAAA,CAAAxG,gBAAA,GAA4B,KAAK;UAAA,EAAC;UAAChE,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAAuL,iEAAA;YAAA,OAASpB,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAGpCvH,EAHqC,CAAAG,YAAA,EAAS,EAChC,EACH,EACA;UACXH,EAAA,CAAAC,cAAA,oBAC0D;UADjCD,EAAA,CAAA2K,gBAAA,2BAAAkB,2EAAAtL,MAAA;YAAAP,EAAA,CAAA6K,kBAAA,CAAAL,GAAA,CAAAvG,qBAAA,EAAA1D,MAAA,MAAAiK,GAAA,CAAAvG,qBAAA,GAAA1D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAExDP,EAAA,CAAA4B,UAAA,KAAAkK,sDAAA,yBAAgC;UAOpB9L,EAHZ,CAAAC,cAAA,gBAAwE,gBACf,kBAC+C,iBACrD;UAAAD,EAAA,CAAAI,MAAA,eAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAI,MAAA,kBACxD;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAEJH,EADJ,CAAAC,cAAA,gBAAwC,sBAGoC;;UACpED,EAAA,CAAA4B,UAAA,MAAAmK,uDAAA,0BAA2C;UAQvD/L,EAFQ,CAAAG,YAAA,EAAY,EACV,EACJ;UAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAGJ;UAAxCD,EAAA,CAAAK,UAAA,mBAAA2L,kEAAA;YAAA,OAAAxB,GAAA,CAAAvG,qBAAA,GAAiC,KAAK;UAAA,EAAC;UAACjE,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAC,cAAA,mBACyB;UAArBD,EAAA,CAAAK,UAAA,mBAAA4L,kEAAA;YAAA,OAASzB,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAGpCvH,EAHqC,CAAAG,YAAA,EAAS,EAChC,EACH,EACA;;;;;UA5SqBH,EAAA,CAAAiB,SAAA,GAAmC;UAACjB,EAApC,CAAAkB,UAAA,oCAAmC,iBAAiB;UAEpElB,EAAA,CAAAiB,SAAA,EAAmC;UAACjB,EAApC,CAAAkB,UAAA,oCAAmC,iBAAiB;UAKnDlB,EAAA,CAAAiB,SAAA,GAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,UAAAsJ,GAAA,CAAA7G,cAAA,CAAwB;UAAC3D,EAAA,CAAAkM,gBAAA,cAAA1B,GAAA,CAAA5F,gBAAA,CAAgC;UACpC5E,EADkD,CAAAkB,UAAA,YAAW,mBAAmB,oBAC7D;UAkGDlB,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAmM,UAAA,CAAAnM,EAAA,CAAAoM,eAAA,KAAAC,GAAA,EAA4B;UAA1ErM,EAAA,CAAAkB,UAAA,eAAc;UAAClB,EAAA,CAAAkM,gBAAA,YAAA1B,GAAA,CAAAxG,gBAAA,CAA8B;UACnDhE,EADiF,CAAAkB,UAAA,qBAAoB,oBAClF;UAKblB,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAkB,UAAA,cAAAsJ,GAAA,CAAAlI,WAAA,CAAyB;UAQItC,EAAA,CAAAiB,SAAA,GAAiE;UAAjEjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAsM,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,eAAAC,MAAA,EAAiE;UAClF/B,EAAA,CAAAiB,SAAA,EAAyC;UAAzCjB,EAAA,CAAAkB,UAAA,SAAAsJ,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,eAAAC,MAAA,CAAyC;UAwBxB/B,EAAA,CAAAiB,SAAA,IAAgE;UAAhEjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAsM,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,cAAAC,MAAA,EAAgE;UACjF/B,EAAA,CAAAiB,SAAA,EAAwC;UAAxCjB,EAAA,CAAAkB,UAAA,SAAAsJ,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,cAAAC,MAAA,CAAwC;UAsBlC/B,EAAA,CAAAiB,SAAA,IAAuB;UACejB,EADtC,CAAAkB,UAAA,YAAAsJ,GAAA,CAAAhG,WAAA,CAAuB,+BAC6C;UAQpExE,EAAA,CAAAiB,SAAA,GAAyB;UACkCjB,EAD3D,CAAAkB,UAAA,YAAAsJ,GAAA,CAAAjG,aAAA,CAAyB,+BACgE;UAU9EvE,EAAA,CAAAiB,SAAA,GAAoE;UAApEjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAsM,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,kBAAAC,MAAA,EAAoE;UACrF/B,EAAA,CAAAiB,SAAA,EAA4C;UAA5CjB,EAAA,CAAAkB,UAAA,SAAAsJ,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,kBAAAC,MAAA,CAA4C;UAsB5C/B,EAAA,CAAAiB,SAAA,GAA0F;UAA1FjB,EAAA,CAAAkB,UAAA,WAAAsL,QAAA,GAAAhC,GAAA,CAAAlI,WAAA,CAAAC,GAAA,mCAAAiK,QAAA,CAAAC,OAAA,OAAAD,QAAA,GAAAhC,GAAA,CAAAlI,WAAA,CAAAC,GAAA,mCAAAiK,QAAA,CAAApE,OAAA,EAA0F;UAczEpI,EAAA,CAAAiB,SAAA,GAA6D;UAA7DjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAsM,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,WAAAC,MAAA,EAA6D;UAC9E/B,EAAA,CAAAiB,SAAA,EAAqC;UAArCjB,EAAA,CAAAkB,UAAA,SAAAsJ,GAAA,CAAArI,SAAA,IAAAqI,GAAA,CAAA1I,CAAA,WAAAC,MAAA,CAAqC;UAMrC/B,EAAA,CAAAiB,SAAA,EAA8E;UAA9EjB,EAAA,CAAAkB,UAAA,WAAAwL,QAAA,GAAAlC,GAAA,CAAAlI,WAAA,CAAAC,GAAA,6BAAAmK,QAAA,CAAAD,OAAA,OAAAC,QAAA,GAAAlC,GAAA,CAAAlI,WAAA,CAAAC,GAAA,6BAAAmK,QAAA,CAAAtE,OAAA,EAA8E;UAOtFpI,EAAA,CAAAiB,SAAA,EAA4C;UAA5CjB,EAAA,CAAAkB,UAAA,SAAAsJ,GAAA,CAAApG,MAAA,IAAAoG,GAAA,CAAAlI,WAAA,CAAA6D,KAAA,CAAArB,UAAA,CAA4C;UA6BG9E,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAmM,UAAA,CAAAnM,EAAA,CAAAoM,eAAA,KAAAO,GAAA,EAA4B;UAA/E3M,EAAA,CAAAkB,UAAA,eAAc;UAAClB,EAAA,CAAAkM,gBAAA,YAAA1B,GAAA,CAAAvG,qBAAA,CAAmC;UACxDjE,EADsF,CAAAkB,UAAA,qBAAoB,oBACvF;UAKblB,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAkB,UAAA,cAAAsJ,GAAA,CAAAlI,WAAA,CAAyB;UAMGtC,EAAA,CAAAiB,SAAA,GAA2B;UAEjBjB,EAFV,CAAAkB,UAAA,UAAAlB,EAAA,CAAA4M,WAAA,UAAApC,GAAA,CAAAtD,SAAA,EAA2B,sBAA+C,YAAAsD,GAAA,CAAA/F,cAAA,CAClE,oBAAoB,cAAA+F,GAAA,CAAA9F,aAAA,CACnB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}