import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { ActivitiesService } from '../../../activities.service';
import { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';
import { MessageService, ConfirmationService } from 'primeng/api';
import { Router, ActivatedRoute } from '@angular/router';
import { ActivitiesCallFollowupFormComponent } from 'src/app/store/common-form/activities-call-followup-form/activities-call-followup-form.component';
import { OpportunitiesFollowupFormComponent } from 'src/app/store/common-form/opportunities-followup-form/opportunities-followup-form.component';
import { ActivitiesTaskFollowupFormComponent } from 'src/app/store/common-form/activities-task-followup-form/activities-task-followup-form.component';

interface ActivitiesColumn {
  field: string;
  header: string;
}

interface ActivitiesColumnTask {
  field: string;
  header: string;
}
interface OpportunitiesColumn {
  field: string;
  header: string;
}

@Component({
  selector: 'app-task-follow-items',
  templateUrl: './task-follow-items.component.html',
  styleUrl: './task-follow-items.component.scss',
})
export class TaskFollowItemsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @ViewChild(ActivitiesCallFollowupFormComponent)
  activityFollowupDialog!: ActivitiesCallFollowupFormComponent;
  @ViewChild(OpportunitiesFollowupFormComponent)
  oppotunityFollowupDialog!: OpportunitiesFollowupFormComponent;
  @ViewChild(ActivitiesTaskFollowupFormComponent)
  activityTaskFollowupDialog!: ActivitiesTaskFollowupFormComponent;
  public followupdetails: any[] = [];
  public followupopportunitydetails: any[] = [];
  public followuptaskdetails: any[] = [];
  public activity_id: string = '';
  public submitted = false;
  public saving = false;
  public visible: boolean = false;
  public dropdowns: Record<string, any[]> = {
    activityDocumentType: [],
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private activitiesservice: ActivitiesService,
    private opportunitiesservice: OpportunitiesService,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedActivitiesColumns: ActivitiesColumn[] = [];

  private _selectedActivitiesTaskColumns: ActivitiesColumnTask[] = [];

  private _selectedOpportunitiesColumn: OpportunitiesColumn[] = [];

  public ActivitiesCols: ActivitiesColumn[] = [
    { field: 'type_code', header: 'Type' },
    { field: 'partner_name', header: 'Responsible' },
    { field: 'createdAt', header: 'Created On' },
  ];

  public ActivitiesTaskCols: ActivitiesColumnTask[] = [
    { field: 'type_code', header: 'Type' },
    { field: 'partner_name', header: 'Responsible' },
    { field: 'createdAt', header: 'Created On' },
  ];

  public OpportunitiesCols: OpportunitiesColumn[] = [
    { field: 'type_code', header: 'Type' },
    { field: 'partner_name', header: 'Responsible' },
    { field: 'createdAt', header: 'Created On' },
  ];

  sortFieldActivities: string = '';
  sortOrderActivities: number = 1;

  sortFieldActivitiesTask: string = '';
  sortOrderActivitiesTask: number = 1;

  sortFieldOpportunities: string = '';
  sortOrderOpportunities: number = 1;

  ngOnInit() {
    this.loadActivityDropDown(
      'activityDocumentType',
      'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM'
    );
    this.activitiesservice.activity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.activity_id = response?.activity_id;

          const allActivityItems = response?.follow_up_and_related_items || [];
          const allOpportunityItems = response?.opportunity_followups || [];

          // Filter only FOLLOW_UP items and inject individual partner_name from each item
          this.followupdetails = allActivityItems
            .filter((item: any) => item?.type_code === '0002')
            .map((item: any) => {
              const partnerFn =
                item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(
                  (p: any) => p?.partner_function === 'YI'
                );
              const partnerName = partnerFn?.bp_full_name || null;
              return {
                ...item,
                partner_name: partnerName,
              };
            });

          this.followuptaskdetails = allActivityItems
            .filter((item: any) => item?.type_code === '0006')
            .map((item: any) => {
              const partnerFn =
                item?.activity_transaction?.business_partner?.customer?.partner_functions?.find(
                  (p: any) => p?.partner_function === 'YI'
                );
              const partnerName = partnerFn?.bp_full_name || null;
              return {
                ...item,
                partner_name: partnerName,
              };
            });

          this.followupopportunitydetails = allOpportunityItems
            .filter((item: any) => item?.type_code === '0005')
            .map((item: any) => {
              const partnerFn =
                item?.opportunity?.business_partner?.customer?.partner_functions?.find(
                  (p: any) => p?.partner_function === 'YI'
                );
              const partnerName = partnerFn?.bp_full_name || null;

              return {
                ...item,
                partner_name: partnerName,
              };
            });
        }
      });

    this._selectedActivitiesColumns = this.ActivitiesCols;

    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols;

    this._selectedOpportunitiesColumn = this.OpportunitiesCols;
  }

  get selectedActivitiesColumns(): any[] {
    return this._selectedActivitiesColumns;
  }

  get selectedActivitiesTaskColumns(): any[] {
    return this._selectedActivitiesTaskColumns;
  }

  get selectedOpportunitiesColumns(): any[] {
    return this._selectedOpportunitiesColumn;
  }

  set selectedActivitiesColumns(val: any[]) {
    this._selectedActivitiesColumns = this.ActivitiesCols.filter((col) =>
      val.includes(col)
    );
  }

  set selectedActivitiesTaskColumns(val: any[]) {
    this._selectedActivitiesTaskColumns = this.ActivitiesTaskCols.filter(
      (col) => val.includes(col)
    );
  }

  set selectedOpportunitiesColumns(val: any[]) {
    this._selectedOpportunitiesColumn = this.OpportunitiesCols.filter((col) =>
      val.includes(col)
    );
  }

  onActivitiesColumnReorder(event: any) {
    const draggedCol = this.ActivitiesCols[event.dragIndex];
    this.ActivitiesCols.splice(event.dragIndex, 1);
    this.ActivitiesCols.splice(event.dropIndex, 0, draggedCol);
  }

  onActivitiesTaskColumnReorder(event: any) {
    const draggedCol = this.ActivitiesTaskCols[event.dragIndex];
    this.ActivitiesTaskCols.splice(event.dragIndex, 1);
    this.ActivitiesTaskCols.splice(event.dropIndex, 0, draggedCol);
  }

  onOpportunitiesColumnReorder(event: any) {
    const draggedCol = this.OpportunitiesCols[event.dragIndex];
    this.OpportunitiesCols.splice(event.dragIndex, 1);
    this.OpportunitiesCols.splice(event.dropIndex, 0, draggedCol);
  }

  customSort(
    field: string,
    data: any[],
    type: 'activities' | 'task' | 'opportunities'
  ) {
    if (type === 'activities') {
      this.sortFieldActivities = field;
      this.sortOrderActivities = this.sortOrderActivities === 1 ? -1 : 1;
    } else if (type === 'task') {
      this.sortFieldActivitiesTask = field;
      this.sortOrderActivitiesTask =
        this.sortOrderActivitiesTask === 1 ? -1 : 1;
    } else {
      this.sortFieldOpportunities = field;
      this.sortOrderOpportunities = this.sortOrderOpportunities === 1 ? -1 : 1;
    }

    data.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = null;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return (
        (type === 'activities'
          ? this.sortOrderActivities
          : type === 'task'
          ? this.sortOrderActivitiesTask
          : this.sortOrderOpportunities) * result
      );
    });
  }

  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;

    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      let fields = field.split('.');
      let value = data;
      for (let i = 0; i < fields.length; i++) {
        if (value == null) return null;
        value = value[fields[i]];
      }
      return value;
    }
  }

  loadActivityDropDown(target: string, type: string): void {
    this.activitiesservice
      .getActivityDropdownOptions(type)
      .subscribe((res: any) => {
        this.dropdowns[target] =
          res?.data?.map((attr: any) => ({
            label: attr.description,
            value: attr.code,
          })) ?? [];
      });
  }

  getLabelFromDropdown(dropdownKey: string, value: string): string {
    const item = this.dropdowns[dropdownKey]?.find(
      (opt) => opt.value === value
    );
    return item?.label || value;
  }

  confirmRemove(item: any) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item);
      },
    });
  }

  remove(item: any) {
    this.activitiesservice
      .deleteFollowupItem(item.documentId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Record Deleted Successfully!',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  confirmOpportunityRemove(item: any) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item);
      },
    });
  }

  removeOpportunity(item: any) {
    this.opportunitiesservice
      .deleteFollowupItem(item.documentId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Record Deleted Successfully!',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  openActivityFollowUpDialog() {
    this.activityFollowupDialog.showDialog('right');
  }

  openOpportunityFollowUpDialog() {
    this.oppotunityFollowupDialog.showDialog('right');
  }

  openActivityTaskFollowUpDialog() {
    this.activityTaskFollowupDialog.showDialog('right');
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
