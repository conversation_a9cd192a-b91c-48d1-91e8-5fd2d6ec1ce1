{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBind = require('call-bind');\n\n// eslint-disable-next-line no-extra-parens\nvar $indexOf = callBind( /** @type {typeof String.prototype.indexOf} */GetIntrinsic('String.prototype.indexOf'));\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n  // eslint-disable-next-line no-extra-parens\n  var intrinsic = /** @type {Parameters<typeof callBind>[0]} */GetIntrinsic(name, !!allowMissing);\n  if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n    return callBind(intrinsic);\n  }\n  return intrinsic;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBind", "$indexOf", "module", "exports", "callBoundIntrinsic", "name", "allowMissing", "intrinsic"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/call-bound/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBind = require('call-bind');\n\n// eslint-disable-next-line no-extra-parens\nvar $indexOf = callBind(/** @type {typeof String.prototype.indexOf} */ (GetIntrinsic('String.prototype.indexOf')));\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\t// eslint-disable-next-line no-extra-parens\n\tvar intrinsic = /** @type {Parameters<typeof callBind>[0]} */ (GetIntrinsic(name, !!allowMissing));\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBind(intrinsic);\n\t}\n\treturn intrinsic;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,WAAW,CAAC;;AAEnC;AACA,IAAIE,QAAQ,GAAGD,QAAQ,EAAC,8CAAgDF,YAAY,CAAC,0BAA0B,CAAE,CAAC;;AAElH;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAChE;EACA,IAAIC,SAAS,GAAG,6CAA+CT,YAAY,CAACO,IAAI,EAAE,CAAC,CAACC,YAAY,CAAE;EAClG,IAAI,OAAOC,SAAS,KAAK,UAAU,IAAIN,QAAQ,CAACI,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1E,OAAOL,QAAQ,CAACO,SAAS,CAAC;EAC3B;EACA,OAAOA,SAAS;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}