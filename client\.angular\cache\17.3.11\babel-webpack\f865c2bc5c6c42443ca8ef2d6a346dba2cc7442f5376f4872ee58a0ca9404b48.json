{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../service/app.layout.service\";\nimport * as i2 from \"../app.menu.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/sidebar\";\nimport * as i6 from \"primeng/radiobutton\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputswitch\";\nconst _c0 = a0 => ({\n  \"background-color\": a0\n});\nconst _c1 = a0 => ({\n  \"text-primary-500\": a0\n});\nfunction AppConfigComponent_div_6_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction AppConfigComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AppConfigComponent_div_6_Template_button_click_1_listener() {\n      const theme_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeTheme(theme_r2.name));\n    });\n    i0.ɵɵtemplate(2, AppConfigComponent_div_6_i_2_Template, 1, 0, \"i\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const theme_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c0, theme_r2.color));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", theme_r2.name == ctx_r2.layoutService.config().theme);\n  }\n}\nfunction AppConfigComponent_i_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 20);\n  }\n  if (rf & 2) {\n    const s_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, s_r4 === ctx_r2.scale));\n  }\n}\nfunction AppConfigComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h5\");\n    i0.ɵɵtext(2, \"Menu Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 21)(5, \"p-radioButton\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_ng_container_14_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.menuMode, $event) || (ctx_r2.menuMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 23);\n    i0.ɵɵtext(7, \"Reveal\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.menuMode);\n  }\n}\nfunction AppConfigComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h5\");\n    i0.ɵɵtext(2, \"Ripple Effect\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputSwitch\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_ng_container_25_Template_p_inputSwitch_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.ripple, $event) || (ctx_r2.ripple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.ripple);\n  }\n}\nexport let AppConfigComponent = /*#__PURE__*/(() => {\n  class AppConfigComponent {\n    constructor(layoutService, menuService) {\n      this.layoutService = layoutService;\n      this.menuService = menuService;\n      this.minimal = false;\n      this.componentThemes = [];\n      this.layoutThemes = [];\n      this.scales = [12, 13, 14, 15, 16];\n    }\n    get visible() {\n      return this.layoutService.state.configSidebarVisible;\n    }\n    set visible(_val) {\n      this.layoutService.state.configSidebarVisible = _val;\n    }\n    get scale() {\n      return this.layoutService.config().scale;\n    }\n    set scale(_val) {\n      this.layoutService.config.update(config => ({\n        ...config,\n        scale: _val\n      }));\n    }\n    get menuMode() {\n      return this.layoutService.config().menuMode;\n    }\n    set menuMode(_val) {\n      this.layoutService.config.update(config => ({\n        ...config,\n        menuMode: _val\n      }));\n      if (this.layoutService.isSlimPlus() || this.layoutService.isSlim() || this.layoutService.isHorizontal()) {\n        this.menuService.reset();\n      }\n    }\n    get colorScheme() {\n      return this.layoutService.config().colorScheme;\n    }\n    set colorScheme(_val) {\n      console.log(_val);\n      this.layoutService.config.update(config => ({\n        ...config,\n        colorScheme: _val\n      }));\n    }\n    get ripple() {\n      return this.layoutService.config().ripple;\n    }\n    set ripple(_val) {\n      this.layoutService.config.update(config => ({\n        ...config,\n        ripple: _val\n      }));\n    }\n    get theme() {\n      return this.layoutService.config().theme;\n    }\n    set theme(_val) {\n      this.layoutService.config.update(config => ({\n        ...config,\n        theme: _val\n      }));\n    }\n    ngOnInit() {\n      this.componentThemes = [{\n        name: 'blue',\n        color: '#0F8BFD'\n      }, {\n        name: 'green',\n        color: '#0BD18A'\n      }, {\n        name: 'magenta',\n        color: '#EC4DBC'\n      }, {\n        name: 'orange',\n        color: '#FD9214'\n      }, {\n        name: 'purple',\n        color: '#873EFE'\n      }, {\n        name: 'red',\n        color: '#FC6161'\n      }, {\n        name: 'teal',\n        color: '#00D0DE'\n      }, {\n        name: 'yellow',\n        color: '#EEE500'\n      }, {\n        name: 'snjya',\n        color: '#184997'\n      }];\n    }\n    onConfigButtonClick() {\n      this.layoutService.showConfigSidebar();\n    }\n    changeColorScheme(colorScheme) {\n      this.colorScheme = colorScheme;\n    }\n    changeTheme(theme) {\n      this.theme = theme;\n    }\n    decrementScale() {\n      this.scale--;\n    }\n    incrementScale() {\n      this.scale++;\n    }\n    static {\n      this.ɵfac = function AppConfigComponent_Factory(t) {\n        return new (t || AppConfigComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.MenuService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppConfigComponent,\n        selectors: [[\"app-config\"]],\n        inputs: {\n          minimal: \"minimal\"\n        },\n        decls: 26,\n        vars: 10,\n        consts: [[\"type\", \"button\", 1, \"layout-config-button\", \"p-link\", 3, \"click\"], [1, \"pi\", \"pi-cog\"], [\"position\", \"right\", \"styleClass\", \"layout-config-sidebar w-18rem\", 3, \"visibleChange\", \"visible\", \"transitionOptions\"], [1, \"flex\", \"flex-wrap\", \"row-gap-3\"], [\"class\", \"w-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\"], [\"icon\", \"pi pi-minus\", \"type\", \"button\", \"pButton\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"w-2rem\", \"h-2rem\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"flex\", \"gap-2\", \"align-items-center\"], [\"class\", \"pi pi-circle-fill text-300\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"icon\", \"pi pi-plus\", \"type\", \"button\", \"pButton\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"w-2rem\", \"h-2rem\", \"ml-2\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"field-radiobutton\"], [\"name\", \"colorScheme\", \"value\", \"light\", \"inputId\", \"mode-light\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode-light\"], [\"name\", \"colorScheme\", \"value\", \"dark\", \"inputId\", \"mode-dark\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode-dark\"], [1, \"w-3\"], [\"type\", \"button\", 1, \"cursor-pointer\", \"p-link\", \"w-2rem\", \"h-2rem\", \"border-circle\", \"flex-shrink-0\", \"flex\", \"align-items-center\", \"justify-content-center\", 3, \"click\", \"ngStyle\"], [\"class\", \"pi pi-check text-white\", 4, \"ngIf\"], [1, \"pi\", \"pi-check\", \"text-white\"], [1, \"pi\", \"pi-circle-fill\", \"text-300\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"w-6\"], [\"name\", \"menuMode\", \"value\", \"reveal\", \"inputId\", \"mode6\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"mode5\"], [3, \"ngModelChange\", \"ngModel\"]],\n        template: function AppConfigComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_0_listener() {\n              return ctx.onConfigButtonClick();\n            });\n            i0.ɵɵelement(1, \"i\", 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"p-sidebar\", 2);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function AppConfigComponent_Template_p_sidebar_visibleChange_2_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(3, \"h5\");\n            i0.ɵɵtext(4, \"Themes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 3);\n            i0.ɵɵtemplate(6, AppConfigComponent_div_6_Template, 3, 4, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"h5\");\n            i0.ɵɵtext(8, \"Scale\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 5)(10, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_10_listener() {\n              return ctx.decrementScale();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 7);\n            i0.ɵɵtemplate(12, AppConfigComponent_i_12_Template, 1, 3, \"i\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function AppConfigComponent_Template_button_click_13_listener() {\n              return ctx.incrementScale();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(14, AppConfigComponent_ng_container_14_Template, 8, 1, \"ng-container\", 10);\n            i0.ɵɵelementStart(15, \"h5\");\n            i0.ɵɵtext(16, \"Color Scheme\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 11)(18, \"p-radioButton\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_Template_p_radioButton_ngModelChange_18_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.colorScheme, $event) || (ctx.colorScheme = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"label\", 13);\n            i0.ɵɵtext(20, \"Light\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"p-radioButton\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AppConfigComponent_Template_p_radioButton_ngModelChange_22_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.colorScheme, $event) || (ctx.colorScheme = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"label\", 15);\n            i0.ɵɵtext(24, \"Dark\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, AppConfigComponent_ng_container_25_Template, 4, 1, \"ng-container\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n            i0.ɵɵproperty(\"transitionOptions\", \".3s cubic-bezier(0, 0, 0.2, 1)\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.componentThemes);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.scale === ctx.scales[0]);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.scales);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.scale === ctx.scales[ctx.scales.length - 1]);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.minimal);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.colorScheme);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.colorScheme);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", !ctx.minimal);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgStyle, i4.NgControlStatus, i4.NgModel, i5.Sidebar, i6.RadioButton, i7.ButtonDirective, i8.InputSwitch],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppConfigComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}