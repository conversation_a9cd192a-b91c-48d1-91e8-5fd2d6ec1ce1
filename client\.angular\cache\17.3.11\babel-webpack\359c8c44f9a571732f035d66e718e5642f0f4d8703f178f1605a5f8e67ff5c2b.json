{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let InitialsPipe = /*#__PURE__*/(() => {\n  class InitialsPipe {\n    transform(value) {\n      const words = value?.trim().split(' ');\n      const firstInitial = words[0]?.charAt(0).toUpperCase() || '';\n      const secondInitial = words[1]?.charAt(0).toUpperCase() || '';\n      return firstInitial + secondInitial;\n    }\n    static {\n      this.ɵfac = function InitialsPipe_Factory(t) {\n        return new (t || InitialsPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"initials\",\n        type: InitialsPipe,\n        pure: true\n      });\n    }\n  }\n  return InitialsPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}