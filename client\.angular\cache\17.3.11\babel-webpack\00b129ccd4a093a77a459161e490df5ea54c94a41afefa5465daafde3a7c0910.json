{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ResetPasswordComponent } from './reset-password.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ResetPasswordModule = /*#__PURE__*/(() => {\n  class ResetPasswordModule {\n    static {\n      this.ɵfac = function ResetPasswordModule_Factory(t) {\n        return new (t || ResetPasswordModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ResetPasswordModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule.forChild([{\n          path: '',\n          component: ResetPasswordComponent\n        }])]\n      });\n    }\n  }\n  return ResetPasswordModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}