{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ImportRoutingModule } from './import-routing.module';\nimport { ImportComponent } from './import.component';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { TableModule } from 'primeng/table';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nexport class ImportModule {\n  static {\n    this.ɵfac = function ImportModule_Factory(t) {\n      return new (t || ImportModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ImportModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService],\n      imports: [CommonModule, ConfirmDialogModule, TabMenuModule, TableModule, ProgressBarModule, ToastModule, ImportRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ImportModule, {\n    declarations: [ImportComponent],\n    imports: [CommonModule, ConfirmDialogModule, TabMenuModule, TableModule, ProgressBarModule, ToastModule, ImportRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ImportRoutingModule", "ImportComponent", "ConfirmDialogModule", "TabMenuModule", "TableModule", "ProgressBarModule", "ToastModule", "MessageService", "ImportModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\import\\import.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImportRoutingModule } from './import-routing.module';\r\nimport { ImportComponent } from './import.component';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ProgressBarModule } from 'primeng/progressbar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ImportComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ConfirmDialogModule,\r\n    TabMenuModule,\r\n    TableModule,\r\n    ProgressBarModule,\r\n    ToastModule,\r\n    ImportRoutingModule,\r\n  ],\r\n  providers: [\r\n    MessageService\r\n  ],\r\n})\r\nexport class ImportModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,aAAa;;AAoB5C,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;iBAJZ,CACTD,cAAc,CACf;MAAAE,OAAA,GAVCV,YAAY,EACZG,mBAAmB,EACnBC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,WAAW,EACXN,mBAAmB;IAAA;EAAA;;;2EAMVQ,YAAY;IAAAE,YAAA,GAfrBT,eAAe;IAAAQ,OAAA,GAGfV,YAAY,EACZG,mBAAmB,EACnBC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,WAAW,EACXN,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}