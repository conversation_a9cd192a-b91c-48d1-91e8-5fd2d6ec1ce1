{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./auth.service\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(router, auth) {\n      this.router = router;\n      this.auth = auth;\n    }\n    canActivate(route, state) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        return yield _this.authenticate(route, state.url);\n      })();\n    }\n    canActivateChild(childRoute, state) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        return yield _this2.authenticate(childRoute, state.url);\n      })();\n    }\n    authenticate(route, url) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (_this3.auth.isLoggedIn) {\n          // If User already login and try to access auth pages.\n          if (url.startsWith(\"/auth\")) {\n            return _this3.router.parseUrl(\"/store\");\n          }\n          // const routeData: any = route?.data || null;\n          // const permission: any = routeData?.permission || null;\n          // let permissions = this.auth.getPermissions;\n          // if (!permissions.length) {\n          //   permissions = await this.auth.getUserPermissions();\n          //   if (!permissions.length) {\n          //     return this.router.parseUrl(\"/store\");\n          //   }\n          // }\n          // if (permission !== null && !permissions.includes(permission)) {\n          //   return this.router.parseUrl(\"/store\");\n          // }\n          return true;\n        } else {\n          if (!url.startsWith(\"/auth\")) {\n            return _this3.router.parseUrl(\"/auth/login\");\n          }\n        }\n        return true;\n      })();\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(t) {\n        return new (t || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}