{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../account.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/dropdown\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/common\";\nfunction AccountSalesQuoteDetailsComponent_ng_template_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 36);\n    i0.ɵɵtext(2, \"Item Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 37);\n    i0.ɵɵtext(4, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 38);\n    i0.ɵɵtext(6, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesQuoteDetailsComponent_ng_template_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39)(2, \"div\", 40)(3, \"div\", 41)(4, \"h5\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 44)(9, \"p\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 46)(13, \"p\", 47);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 48);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const items_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(items_r1.SHORT_TEXT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1.MATERIAL, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 6, items_r1.REQ_QTY), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.formatted_base_price, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", items_r1 == null ? null : items_r1.formatted_base_price_each, \" each \");\n  }\n}\nexport let AccountSalesQuoteDetailsComponent = /*#__PURE__*/(() => {\n  class AccountSalesQuoteDetailsComponent {\n    constructor(route, router, accountservice) {\n      this.route = route;\n      this.router = router;\n      this.accountservice = accountservice;\n      this.quoteDetail = null;\n      this.unsubscribe$ = new Subject();\n      this.statuses = [];\n    }\n    ngOnInit() {\n      this.Actions = [{\n        name: 'Change Quote',\n        code: 'CQ'\n      }];\n      this.quoteData = history.state.quoteData;\n      this.customerData = history.state.customerData;\n      this.accountservice.getQuoteDetails(this.quoteData).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        this.quoteDetail = response?.SALESQUOTE;\n        if (this.quoteDetail?.QUOTE_HDR?.DOC_DATE) {\n          const rawDate = this.quoteDetail.QUOTE_HDR.DOC_DATE.toString(); // Ensure it's a string\n          if (rawDate.length === 8) {\n            this.quoteDetail.QUOTE_HDR.DOC_DATE = `${rawDate.substring(4, 6)}/${rawDate.substring(6, 8)}/${rawDate.substring(0, 4)}`;\n          } else {\n            this.quoteDetail.QUOTE_HDR.DOC_DATE = '-';\n          }\n        }\n      }, error => {\n        console.error('Error fetching quote details:', error);\n      });\n    }\n    getStatusName(code) {\n      const status = this.statuses.find(o => o.code === code);\n      if (status) {\n        return status.description;\n      }\n      return '';\n    }\n    NavigatetoChangeQuote(event) {\n      if (!event) return;\n      if (event.code == 'CQ') {\n        const url = `${environment.crms4Endpoint}/ui#SalesQuotation-manageV2&/SalesQuotationManage('${this.quoteData.SD_DOC}')`;\n        window.open(url, '_blank');\n      }\n    }\n    goToBack() {\n      window.history.back();\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountSalesQuoteDetailsComponent_Factory(t) {\n        return new (t || AccountSalesQuoteDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AccountService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountSalesQuoteDetailsComponent,\n        selectors: [[\"app-account-sales-quote-details\"]],\n        decls: 106,\n        vars: 13,\n        consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"pt-0\"], [1, \"p-3\", \"mb-4\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"ml-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"filter-sec\", \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"pt-0\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"], [1, \"order-details-list\", \"m-0\", \"p-0\", \"gap-5\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"min-w-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-3\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"table-data\", \"border-round\", \"overflow-hidden\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"lg:w-30rem\", \"md:w-30rem\", \"sm:w-full\", \"pt-0\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"mt-2\", \"mb-4\", \"uppercase\", \"text-center\", \"text-primary\"], [1, \"cart-sidebar-price\", \"py-4\", \"border-none\", \"border-y-1\", \"border-solid\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"font-semibold\"], [1, \"text-color-secondary\"], [1, \"cart-sidebar-t-price\", \"py-4\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"text-primary\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"]],\n        template: function AccountSalesQuoteDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n            i0.ɵɵtext(5, \"Quote Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function AccountSalesQuoteDetailsComponent_Template_button_click_7_listener() {\n              return ctx.goToBack();\n            });\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9, \"arrow_back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Back \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"p-dropdown\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AccountSalesQuoteDetailsComponent_Template_p_dropdown_ngModelChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function AccountSalesQuoteDetailsComponent_Template_p_dropdown_onChange_11_listener($event) {\n              return ctx.NavigatetoChangeQuote($event.value);\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 2)(14, \"div\", 10)(15, \"h4\", 4);\n            i0.ɵɵtext(16, \"Quote Information\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 11)(18, \"ul\", 12)(19, \"li\", 13)(20, \"div\", 14)(21, \"i\", 15);\n            i0.ɵɵtext(22, \"tag\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 16)(24, \"h6\", 17);\n            i0.ɵɵtext(25, \"Quote #\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 18);\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"li\", 13)(29, \"div\", 14)(30, \"i\", 15);\n            i0.ɵɵtext(31, \"person\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 16)(33, \"h6\", 17);\n            i0.ɵɵtext(34, \"Customer #\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"p\", 18);\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"li\", 13)(38, \"div\", 14)(39, \"i\", 15);\n            i0.ɵɵtext(40, \"person\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 16)(42, \"h6\", 17);\n            i0.ɵɵtext(43, \"Customer Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"p\", 18);\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"li\", 13)(47, \"div\", 14)(48, \"i\", 15);\n            i0.ɵɵtext(49, \"person\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 16)(51, \"h6\", 17);\n            i0.ɵɵtext(52, \"Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"p\", 18);\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"li\", 13)(56, \"div\", 14)(57, \"i\", 15);\n            i0.ɵɵtext(58, \"event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\", 16)(60, \"h6\", 17);\n            i0.ɵɵtext(61, \"Date Placed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\", 18);\n            i0.ɵɵtext(63);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(64, \"div\", 2)(65, \"div\", 10)(66, \"h4\", 4);\n            i0.ɵɵtext(67, \"Quote Description\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 11)(69, \"ul\", 19)(70, \"li\", 13)(71, \"div\", 20)(72, \"i\", 15);\n            i0.ɵɵtext(73, \"tag\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 16)(75, \"h6\", 17);\n            i0.ɵɵtext(76, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"p\", 18);\n            i0.ɵɵtext(78);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(79, \"div\", 21)(80, \"div\", 22)(81, \"h4\", 4);\n            i0.ɵɵtext(82, \"Items\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(83, \"div\", 23)(84, \"p-table\", 24);\n            i0.ɵɵtemplate(85, AccountSalesQuoteDetailsComponent_ng_template_85_Template, 7, 0, \"ng-template\", 25)(86, AccountSalesQuoteDetailsComponent_ng_template_86_Template, 17, 8, \"ng-template\", 26);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(87, \"div\", 27)(88, \"div\", 28)(89, \"h5\", 29);\n            i0.ɵɵtext(90, \"Quote Summary\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"div\", 30)(92, \"ul\", 31)(93, \"li\", 32)(94, \"span\", 33);\n            i0.ɵɵtext(95, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(96);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"li\", 32)(98, \"span\", 33);\n            i0.ɵɵtext(99, \"Subtotal\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(100);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(101, \"div\", 34)(102, \"h5\", 35);\n            i0.ɵɵtext(103, \"Total \");\n            i0.ɵɵelementStart(104, \"span\");\n            i0.ɵɵtext(105);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_NUMBER) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.customerData == null ? null : ctx.customerData.customer_id) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.customerData == null ? null : ctx.customerData.customer_name) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_NAME) || \"-\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_DATE);\n            i0.ɵɵadvance(15);\n            i0.ɵɵtextInterpolate(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR_TEXT[0] == null ? null : ctx.quoteDetail.QUOTE_HDR_TEXT[0].TEXT);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"value\", ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_LINE_DETAIL);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusName(ctx.quoteDetail == null ? null : ctx.quoteDetail.QUOTE_HDR == null ? null : ctx.quoteDetail.QUOTE_HDR.DOC_STAT) || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.quoteDetail == null ? null : ctx.quoteDetail.formatted_sub_total) || \"$0.00\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.quoteDetail == null ? null : ctx.quoteDetail.formatted_total) || \"$0.00\");\n          }\n        },\n        dependencies: [i3.PrimeTemplate, i4.Dropdown, i5.Table, i6.NgControlStatus, i6.NgModel, i7.DecimalPipe]\n      });\n    }\n  }\n  return AccountSalesQuoteDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}