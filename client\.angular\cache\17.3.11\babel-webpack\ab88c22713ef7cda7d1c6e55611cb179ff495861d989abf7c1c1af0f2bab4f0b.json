{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { OpportunitiesRoutingModule } from './opportunities-routing.module';\nimport { OpportunitiesComponent } from './opportunities.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { DialogModule } from 'primeng/dialog';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { ToastModule } from 'primeng/toast';\nimport { TabViewModule } from 'primeng/tabview';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\nimport { EditorModule } from 'primeng/editor';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { OpportunitiesDocumentFlowComponent } from './opportunities-details/opportunities-document-flow/opportunities-document-flow.component';\nimport { OpportunitiesHierarchyComponent } from './opportunities-details/opportunities-hierarchy/opportunities-hierarchy.component';\nimport { OpportunitiesFollowUpComponent } from './opportunities-details/opportunities-follow-up/opportunities-follow-up.component';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ActivitiesSalesCallFormComponent } from './opportunities-details/opportunities-follow-up/activities-sales-call-form/activities-sales-call-form.component';\nimport { SalesCallFollowItemDetailComponent } from './opportunities-details/opportunities-follow-up/sales-call-follow-item-detail/sales-call-follow-item-detail.component';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport class OpportunitiesModule {\n  static {\n    this.ɵfac = function OpportunitiesModule_Factory(t) {\n      return new (t || OpportunitiesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OpportunitiesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, OpportunitiesRoutingModule, FormsModule, ReactiveFormsModule, TableModule, ToastModule, ButtonModule, DropdownModule, DialogModule, EditorModule, NgSelectModule, InputSwitchModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, ConfirmDialogModule, CheckboxModule, MultiSelectModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OpportunitiesModule, {\n    declarations: [OpportunitiesComponent, OpportunitiesDetailsComponent, OpportunitiesOverviewComponent, OpportunitiesContactsComponent, OpportunitiesSalesTeamComponent, OpportunitiesAiInsightsComponent, OpportunitiesOrganizationDataComponent, OpportunitiesAttachmentsComponent, OpportunitiesNotesComponent, AddOpportunitieComponent, OpportunitiesDocumentFlowComponent, OpportunitiesHierarchyComponent, OpportunitiesFollowUpComponent, ActivitiesSalesCallFormComponent, SalesCallFollowItemDetailComponent],\n    imports: [CommonModule, OpportunitiesRoutingModule, FormsModule, ReactiveFormsModule, TableModule, ToastModule, ButtonModule, DropdownModule, DialogModule, EditorModule, NgSelectModule, InputSwitchModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, ConfirmDialogModule, CheckboxModule, MultiSelectModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "OpportunitiesRoutingModule", "OpportunitiesComponent", "FormsModule", "ReactiveFormsModule", "AutoCompleteModule", "BreadcrumbModule", "ButtonModule", "NgSelectModule", "CalendarModule", "DropdownModule", "DialogModule", "InputTextModule", "TableModule", "ToastModule", "TabViewModule", "InputSwitchModule", "MessageService", "ConfirmationService", "OpportunitiesDetailsComponent", "OpportunitiesOverviewComponent", "OpportunitiesContactsComponent", "OpportunitiesSalesTeamComponent", "OpportunitiesAiInsightsComponent", "OpportunitiesOrganizationDataComponent", "OpportunitiesAttachmentsComponent", "OpportunitiesNotesComponent", "AddOpportunitieComponent", "EditorModule", "ConfirmDialogModule", "OpportunitiesDocumentFlowComponent", "OpportunitiesHierarchyComponent", "OpportunitiesFollowUpComponent", "CheckboxModule", "SharedModule", "ActivitiesSalesCallFormComponent", "SalesCallFollowItemDetailComponent", "MultiSelectModule", "OpportunitiesModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { OpportunitiesRoutingModule } from './opportunities-routing.module';\r\nimport { OpportunitiesComponent } from './opportunities.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { OpportunitiesDetailsComponent } from './opportunities-details/opportunities-details.component';\r\nimport { OpportunitiesOverviewComponent } from './opportunities-details/opportunities-overview/opportunities-overview.component';\r\nimport { OpportunitiesContactsComponent } from './opportunities-details/opportunities-contacts/opportunities-contacts.component';\r\nimport { OpportunitiesSalesTeamComponent } from './opportunities-details/opportunities-sales-team/opportunities-sales-team.component';\r\nimport { OpportunitiesAiInsightsComponent } from './opportunities-details/opportunities-ai-insights/opportunities-ai-insights.component';\r\nimport { OpportunitiesOrganizationDataComponent } from './opportunities-details/opportunities-organization-data/opportunities-organization-data.component';\r\nimport { OpportunitiesAttachmentsComponent } from './opportunities-details/opportunities-attachments/opportunities-attachments.component';\r\nimport { OpportunitiesNotesComponent } from './opportunities-details/opportunities-notes/opportunities-notes.component';\r\nimport { AddOpportunitieComponent } from './add-opportunitie/add-opportunitie.component';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { OpportunitiesDocumentFlowComponent } from './opportunities-details/opportunities-document-flow/opportunities-document-flow.component';\r\nimport { OpportunitiesHierarchyComponent } from './opportunities-details/opportunities-hierarchy/opportunities-hierarchy.component';\r\nimport { OpportunitiesFollowUpComponent } from './opportunities-details/opportunities-follow-up/opportunities-follow-up.component';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { ActivitiesSalesCallFormComponent } from './opportunities-details/opportunities-follow-up/activities-sales-call-form/activities-sales-call-form.component';\r\nimport { SalesCallFollowItemDetailComponent } from './opportunities-details/opportunities-follow-up/sales-call-follow-item-detail/sales-call-follow-item-detail.component';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    OpportunitiesComponent,\r\n    OpportunitiesDetailsComponent,\r\n    OpportunitiesOverviewComponent,\r\n    OpportunitiesContactsComponent,\r\n    OpportunitiesSalesTeamComponent,\r\n    OpportunitiesAiInsightsComponent,\r\n    OpportunitiesOrganizationDataComponent,\r\n    OpportunitiesAttachmentsComponent,\r\n    OpportunitiesNotesComponent,\r\n    AddOpportunitieComponent,\r\n    OpportunitiesDocumentFlowComponent,\r\n    OpportunitiesHierarchyComponent,\r\n    OpportunitiesFollowUpComponent,\r\n    ActivitiesSalesCallFormComponent,\r\n    SalesCallFollowItemDetailComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    OpportunitiesRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    TableModule,\r\n    ToastModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    DialogModule,\r\n    EditorModule,\r\n    NgSelectModule,\r\n    InputSwitchModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n    BreadcrumbModule,\r\n    CalendarModule,\r\n    InputTextModule,\r\n    ConfirmDialogModule,\r\n    CheckboxModule,\r\n    MultiSelectModule,\r\n    SharedModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class OpportunitiesModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,6BAA6B,QAAQ,yDAAyD;AACvG,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,8BAA8B,QAAQ,iFAAiF;AAChI,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,gCAAgC,QAAQ,uFAAuF;AACxI,SAASC,sCAAsC,QAAQ,mGAAmG;AAC1J,SAASC,iCAAiC,QAAQ,uFAAuF;AACzI,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,kCAAkC,QAAQ,2FAA2F;AAC9I,SAASC,+BAA+B,QAAQ,mFAAmF;AACnI,SAASC,8BAA8B,QAAQ,mFAAmF;AAClI,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gCAAgC,QAAQ,iHAAiH;AAClK,SAASC,kCAAkC,QAAQ,uHAAuH;AAC1K,SAASC,iBAAiB,QAAQ,qBAAqB;;AA6CvD,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAFnB,CAACrB,cAAc,EAAEC,mBAAmB,CAAC;MAAAqB,OAAA,GAtB9CvC,YAAY,EACZC,0BAA0B,EAC1BE,WAAW,EACXC,mBAAmB,EACnBS,WAAW,EACXC,WAAW,EACXP,YAAY,EACZG,cAAc,EACdC,YAAY,EACZiB,YAAY,EACZpB,cAAc,EACdQ,iBAAiB,EACjBD,aAAa,EACbV,kBAAkB,EAClBC,gBAAgB,EAChBG,cAAc,EACdG,eAAe,EACfiB,mBAAmB,EACnBI,cAAc,EACdI,iBAAiB,EACjBH,YAAY;IAAA;EAAA;;;2EAIHI,mBAAmB;IAAAE,YAAA,GAzC5BtC,sBAAsB,EACtBiB,6BAA6B,EAC7BC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,gCAAgC,EAChCC,sCAAsC,EACtCC,iCAAiC,EACjCC,2BAA2B,EAC3BC,wBAAwB,EACxBG,kCAAkC,EAClCC,+BAA+B,EAC/BC,8BAA8B,EAC9BG,gCAAgC,EAChCC,kCAAkC;IAAAG,OAAA,GAGlCvC,YAAY,EACZC,0BAA0B,EAC1BE,WAAW,EACXC,mBAAmB,EACnBS,WAAW,EACXC,WAAW,EACXP,YAAY,EACZG,cAAc,EACdC,YAAY,EACZiB,YAAY,EACZpB,cAAc,EACdQ,iBAAiB,EACjBD,aAAa,EACbV,kBAAkB,EAClBC,gBAAgB,EAChBG,cAAc,EACdG,eAAe,EACfiB,mBAAmB,EACnBI,cAAc,EACdI,iBAAiB,EACjBH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}