{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * Carousel is a content slider featuring various customization options.\n * @group Components\n */\nconst _c0 = [\"itemsContainer\"];\nconst _c1 = [\"indicatorContent\"];\nconst _c2 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c3 = [\"p-header\", \"p-footer\"];\nconst _c4 = (a0, a1) => ({\n  \"p-carousel p-component\": true,\n  \"p-carousel-vertical\": a0,\n  \"p-carousel-horizontal\": a1\n});\nconst _c5 = a0 => ({\n  height: a0\n});\nconst _c6 = a0 => ({\n  \"p-carousel-prev p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c7 = (a0, a1, a2) => ({\n  \"p-carousel-item p-carousel-item-cloned\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c8 = a0 => ({\n  $implicit: a0\n});\nconst _c9 = (a0, a1, a2) => ({\n  \"p-carousel-item\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c10 = a0 => ({\n  \"p-carousel-next p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c11 = a0 => ({\n  \"p-carousel-indicator\": true,\n  \"p-highlight\": a0\n});\nfunction Carousel_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Carousel_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n  }\n}\nfunction Carousel_button_4_ng_container_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_4_ng_container_1_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_button_4_ng_container_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 17)(2, Carousel_button_4_ng_container_1_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_button_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_button_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_button_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_button_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtemplate(1, Carousel_button_4_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Carousel_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function Carousel_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_button_4_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Carousel_button_4_span_2_Template, 2, 1, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c6, ctx_r1.isBackwardNavDisabled()))(\"disabled\", ctx_r1.isBackwardNavDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPrevButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Carousel_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c7, ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length, 0 === index_r5, ctx_r1.clonedItemsForStarting.length - 1 === index_r5));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r5))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c8, item_r4));\n  }\n}\nfunction Carousel_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_9_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c9, ctx_r1.firstIndex() <= index_r7 && ctx_r1.lastIndex() >= index_r7, ctx_r1.firstIndex() === index_r7, ctx_r1.lastIndex() === index_r7));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r7))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c8, item_r6));\n  }\n}\nfunction Carousel_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, Carousel_div_10_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const index_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c7, ctx_r1.totalShiftedItems * -1 === ctx_r1.numVisible, 0 === index_r9, ctx_r1.clonedItemsForFinishing.length - 1 === index_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c8, item_r8));\n  }\n}\nfunction Carousel_button_11_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_11_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_button_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_button_11_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 17)(2, Carousel_button_11_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_button_11_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_button_11_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_button_11_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_button_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtemplate(1, Carousel_button_11_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nextIconTemplate);\n  }\n}\nfunction Carousel_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function Carousel_button_11_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_button_11_ng_container_1_Template, 3, 2, \"ng-container\", 15)(2, Carousel_button_11_span_2_Template, 2, 1, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c10, ctx_r1.isForwardNavDisabled()))(\"disabled\", ctx_r1.isForwardNavDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaNextButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.nextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nextIconTemplate);\n  }\n}\nfunction Carousel_ul_12_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function Carousel_ul_12_li_2_Template_button_click_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDotClick($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c11, ctx_r1._page === i_r13));\n    i0.ɵɵattribute(\"data-pc-section\", \"indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.indicatorStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-link\")(\"ngStyle\", ctx_r1.indicatorStyle)(\"tabindex\", ctx_r1._page === i_r13 ? 0 : -1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(i_r13 + 1))(\"aria-current\", ctx_r1._page === i_r13 ? \"page\" : undefined);\n  }\n}\nfunction Carousel_ul_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 21, 1);\n    i0.ɵɵlistener(\"keydown\", function Carousel_ul_12_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Carousel_ul_12_li_2_Template, 2, 11, \"li\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.indicatorsContentClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-carousel-indicators p-reset\")(\"ngStyle\", ctx_r1.indicatorsContentStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.totalDotsArray());\n  }\n}\nfunction Carousel_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Carousel_div_13_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nclass Carousel {\n  el;\n  zone;\n  cd;\n  renderer;\n  document;\n  platformId;\n  config;\n  /**\n   * Index of the first item.\n   * @defaultValue 0\n   * @group Props\n   */\n  get page() {\n    return this._page;\n  }\n  set page(val) {\n    if (this.isCreated && val !== this._page) {\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (val > this._page && val <= this.totalDots() - 1) {\n        this.step(-1, val);\n      } else if (val < this._page) {\n        this.step(1, val);\n      }\n    }\n    this._page = val;\n  }\n  /**\n   * Number of items per page.\n   * @defaultValue 1\n   * @group Props\n   */\n  get numVisible() {\n    return this._numVisible;\n  }\n  set numVisible(val) {\n    this._numVisible = val;\n  }\n  /**\n   * Number of items to scroll.\n   * @defaultValue 1\n   * @group Props\n   */\n  get numScroll() {\n    return this._numVisible;\n  }\n  set numScroll(val) {\n    this._numScroll = val;\n  }\n  /**\n   * An array of options for responsive design.\n   * @see {CarouselResponsiveOptions}\n   * @group Props\n   */\n  responsiveOptions;\n  /**\n   * Specifies the layout of the component.\n   * @group Props\n   */\n  orientation = 'horizontal';\n  /**\n   * Height of the viewport in vertical layout.\n   * @group Props\n   */\n  verticalViewPortHeight = '300px';\n  /**\n   * Style class of main content.\n   * @group Props\n   */\n  contentClass = '';\n  /**\n   * Style class of the indicator items.\n   * @group Props\n   */\n  indicatorsContentClass = '';\n  /**\n   * Inline style of the indicator items.\n   * @group Props\n   */\n  indicatorsContentStyle;\n  /**\n   * Style class of the indicators.\n   * @group Props\n   */\n  indicatorStyleClass = '';\n  /**\n   * Style of the indicators.\n   * @group Props\n   */\n  indicatorStyle;\n  /**\n   * An array of objects to display.\n   * @defaultValue null\n   * @group Props\n   */\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = val;\n  }\n  /**\n   * Defines if scrolling would be infinite.\n   * @group Props\n   */\n  circular = false;\n  /**\n   * Whether to display indicator container.\n   * @group Props\n   */\n  showIndicators = true;\n  /**\n   * Whether to display navigation buttons in container.\n   * @group Props\n   */\n  showNavigators = true;\n  /**\n   * Time in milliseconds to scroll items automatically.\n   * @group Props\n   */\n  autoplayInterval = 0;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the viewport container.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Callback to invoke after scroll.\n   * @param {CarouselPageEvent} event - Custom page event.\n   * @group Emits\n   */\n  onPage = new EventEmitter();\n  itemsContainer;\n  indicatorContent;\n  headerFacet;\n  footerFacet;\n  templates;\n  _numVisible = 1;\n  _numScroll = 1;\n  _oldNumScroll = 0;\n  prevState = {\n    numScroll: 0,\n    numVisible: 0,\n    value: []\n  };\n  defaultNumScroll = 1;\n  defaultNumVisible = 1;\n  _page = 0;\n  _value;\n  carouselStyle;\n  id;\n  totalShiftedItems;\n  isRemainingItemsAdded = false;\n  animationTimeout;\n  translateTimeout;\n  remainingItems = 0;\n  _items;\n  startPos;\n  documentResizeListener;\n  clonedItemsForStarting;\n  clonedItemsForFinishing;\n  allowAutoplay;\n  interval;\n  isCreated;\n  swipeThreshold = 20;\n  itemTemplate;\n  headerTemplate;\n  footerTemplate;\n  previousIconTemplate;\n  nextIconTemplate;\n  window;\n  constructor(el, zone, cd, renderer, document, platformId, config) {\n    this.el = el;\n    this.zone = zone;\n    this.cd = cd;\n    this.renderer = renderer;\n    this.document = document;\n    this.platformId = platformId;\n    this.config = config;\n    this.totalShiftedItems = this.page * this.numScroll * -1;\n    this.window = this.document.defaultView;\n  }\n  ngOnChanges(simpleChange) {\n    if (isPlatformBrowser(this.platformId)) {\n      if (simpleChange.value) {\n        if (this.circular && this._value) {\n          this.setCloneItems();\n        }\n      }\n      if (this.isCreated) {\n        if (simpleChange.numVisible) {\n          if (this.responsiveOptions) {\n            this.defaultNumVisible = this.numVisible;\n          }\n          if (this.isCircular()) {\n            this.setCloneItems();\n          }\n          this.createStyle();\n          this.calculatePosition();\n        }\n        if (simpleChange.numScroll) {\n          if (this.responsiveOptions) {\n            this.defaultNumScroll = this.numScroll;\n          }\n        }\n      }\n    }\n    this.cd.markForCheck();\n  }\n  ngAfterContentInit() {\n    this.id = UniqueComponentId();\n    if (isPlatformBrowser(this.platformId)) {\n      this.allowAutoplay = !!this.autoplayInterval;\n      if (this.circular) {\n        this.setCloneItems();\n      }\n      if (this.responsiveOptions) {\n        this.defaultNumScroll = this._numScroll;\n        this.defaultNumVisible = this._numVisible;\n      }\n      this.createStyle();\n      this.calculatePosition();\n      if (this.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n        case 'previousicon':\n          this.previousIconTemplate = item.template;\n          break;\n        case 'nexticon':\n          this.nextIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n    this.cd.detectChanges();\n  }\n  ngAfterContentChecked() {\n    if (isPlatformBrowser(this.platformId)) {\n      const isCircular = this.isCircular();\n      let totalShiftedItems = this.totalShiftedItems;\n      if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n        if (this.autoplayInterval) {\n          this.stopAutoplay(false);\n        }\n        this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n        let page = this._page;\n        if (this.totalDots() !== 0 && page >= this.totalDots()) {\n          page = this.totalDots() - 1;\n          this._page = page;\n          this.onPage.emit({\n            page: this.page\n          });\n        }\n        totalShiftedItems = page * this._numScroll * -1;\n        if (isCircular) {\n          totalShiftedItems -= this._numVisible;\n        }\n        if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n          totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n          this.isRemainingItemsAdded = true;\n        } else {\n          this.isRemainingItemsAdded = false;\n        }\n        if (totalShiftedItems !== this.totalShiftedItems) {\n          this.totalShiftedItems = totalShiftedItems;\n        }\n        this._oldNumScroll = this._numScroll;\n        this.prevState.numScroll = this._numScroll;\n        this.prevState.numVisible = this._numVisible;\n        this.prevState.value = [...this._value];\n        if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n          this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n        }\n        this.isCreated = true;\n        if (this.autoplayInterval && this.isAutoplay()) {\n          this.startAutoplay();\n        }\n      }\n      if (isCircular) {\n        if (this.page === 0) {\n          totalShiftedItems = -1 * this._numVisible;\n        } else if (totalShiftedItems === 0) {\n          totalShiftedItems = -1 * this.value.length;\n          if (this.remainingItems > 0) {\n            this.isRemainingItemsAdded = true;\n          }\n        }\n        if (totalShiftedItems !== this.totalShiftedItems) {\n          this.totalShiftedItems = totalShiftedItems;\n        }\n      }\n    }\n  }\n  createStyle() {\n    if (!this.carouselStyle) {\n      this.carouselStyle = this.renderer.createElement('style');\n      this.carouselStyle.type = 'text/css';\n      this.renderer.appendChild(this.document.head, this.carouselStyle);\n    }\n    let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n    if (this.responsiveOptions) {\n      this.responsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n      for (let i = 0; i < this.responsiveOptions.length; i++) {\n        let res = this.responsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n    this.carouselStyle.innerHTML = innerHTML;\n  }\n  calculatePosition() {\n    if (this.responsiveOptions) {\n      let matchedResponsiveData = {\n        numVisible: this.defaultNumVisible,\n        numScroll: this.defaultNumScroll\n      };\n      if (typeof window !== 'undefined') {\n        let windowWidth = window.innerWidth;\n        for (let i = 0; i < this.responsiveOptions.length; i++) {\n          let res = this.responsiveOptions[i];\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n      }\n      if (this._numScroll !== matchedResponsiveData.numScroll) {\n        let page = this._page;\n        page = Math.floor(page * this._numScroll / matchedResponsiveData.numScroll);\n        let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n        if (this.isCircular()) {\n          totalShiftedItems -= matchedResponsiveData.numVisible;\n        }\n        this.totalShiftedItems = totalShiftedItems;\n        this._numScroll = matchedResponsiveData.numScroll;\n        this._page = page;\n        this.onPage.emit({\n          page: this.page\n        });\n      }\n      if (this._numVisible !== matchedResponsiveData.numVisible) {\n        this._numVisible = matchedResponsiveData.numVisible;\n        this.setCloneItems();\n      }\n      this.cd.markForCheck();\n    }\n  }\n  setCloneItems() {\n    this.clonedItemsForStarting = [];\n    this.clonedItemsForFinishing = [];\n    if (this.isCircular()) {\n      this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n      this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n    }\n  }\n  firstIndex() {\n    return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n  }\n  lastIndex() {\n    return this.firstIndex() + this.numVisible - 1;\n  }\n  totalDots() {\n    return this.value?.length ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n  }\n  totalDotsArray() {\n    const totalDots = this.totalDots();\n    return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n  }\n  isVertical() {\n    return this.orientation === 'vertical';\n  }\n  isCircular() {\n    return this.circular && this.value && this.value.length >= this.numVisible;\n  }\n  isAutoplay() {\n    return this.autoplayInterval && this.allowAutoplay;\n  }\n  isForwardNavDisabled() {\n    return this.isEmpty() || this._page >= this.totalDots() - 1 && !this.isCircular();\n  }\n  isBackwardNavDisabled() {\n    return this.isEmpty() || this._page <= 0 && !this.isCircular();\n  }\n  isEmpty() {\n    return !this.value || this.value.length === 0;\n  }\n  navForward(e, index) {\n    if (this.isCircular() || this._page < this.totalDots() - 1) {\n      this.step(-1, index);\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e, index) {\n    if (this.isCircular() || this._page !== 0) {\n      this.step(1, index);\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onDotClick(e, index) {\n    let page = this._page;\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (index > page) {\n      this.navForward(e, index);\n    } else if (index < page) {\n      this.navBackward(e, index);\n    }\n  }\n  onIndicatorKeydown(event) {\n    switch (event.code) {\n      case 'ArrowRight':\n        this.onRightKey();\n        break;\n      case 'ArrowLeft':\n        this.onLeftKey();\n        break;\n    }\n  }\n  onRightKey() {\n    const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n  }\n  onLeftKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n  }\n  onHomeKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, 0);\n  }\n  onEndKey() {\n    const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]r')];\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n  }\n  onTabKey() {\n    const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const highlightedIndex = indicators.findIndex(ind => DomHandler.getAttribute(ind, 'data-p-highlight') === true);\n    const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n    const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    indicators[activeIndex].children[0].tabIndex = '-1';\n    indicators[highlightedIndex].children[0].tabIndex = '0';\n  }\n  findFocusedIndicatorIndex() {\n    const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n    return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n  }\n  changedFocusedIndicator(prevInd, nextInd) {\n    const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    indicators[prevInd].children[0].tabIndex = '-1';\n    indicators[nextInd].children[0].tabIndex = '0';\n    indicators[nextInd].children[0].focus();\n  }\n  step(dir, page) {\n    let totalShiftedItems = this.totalShiftedItems;\n    const isCircular = this.isCircular();\n    if (page != null) {\n      totalShiftedItems = this._numScroll * page * -1;\n      if (isCircular) {\n        totalShiftedItems -= this._numVisible;\n      }\n      this.isRemainingItemsAdded = false;\n    } else {\n      totalShiftedItems += this._numScroll * dir;\n      if (this.isRemainingItemsAdded) {\n        totalShiftedItems += this.remainingItems - this._numScroll * dir;\n        this.isRemainingItemsAdded = false;\n      }\n      let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n      page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n    }\n    if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n      totalShiftedItems = -1 * (this.value.length + this._numVisible);\n      page = 0;\n    } else if (isCircular && this.page === 0 && dir === 1) {\n      totalShiftedItems = 0;\n      page = this.totalDots() - 1;\n    } else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n      totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n      this.isRemainingItemsAdded = true;\n    }\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n    this.totalShiftedItems = totalShiftedItems;\n    this._page = page;\n    this.onPage.emit({\n      page: this.page\n    });\n    this.cd.markForCheck();\n  }\n  startAutoplay() {\n    this.interval = setInterval(() => {\n      if (this.totalDots() > 0) {\n        if (this.page === this.totalDots() - 1) {\n          this.step(-1, 0);\n        } else {\n          this.step(-1, this.page + 1);\n        }\n      }\n    }, this.autoplayInterval);\n    this.allowAutoplay = true;\n    this.cd.markForCheck();\n  }\n  stopAutoplay(changeAllow = true) {\n    if (this.interval) {\n      clearInterval(this.interval);\n      this.interval = undefined;\n      if (changeAllow) {\n        this.allowAutoplay = false;\n      }\n    }\n    this.cd.markForCheck();\n  }\n  isPlaying() {\n    return !!this.interval;\n  }\n  onTransitionEnd() {\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transition = '';\n      if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      }\n    }\n  }\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n    if (this.isVertical()) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n  changePageOnTouch(e, diff) {\n    if (Math.abs(diff) > this.swipeThreshold) {\n      if (diff < 0) {\n        this.navForward(e);\n      } else {\n        this.navBackward(e);\n      }\n    }\n  }\n  ariaPrevButtonLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.prevPageLabel : undefined;\n  }\n  ariaSlideLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.slide : undefined;\n  }\n  ariaNextButtonLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.nextPageLabel : undefined;\n  }\n  ariaSlideNumber(value) {\n    return this.config.translation.aria ? this.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  bindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n          this.calculatePosition();\n        });\n      }\n    }\n  }\n  unbindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n  }\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n  }\n  static ɵfac = function Carousel_Factory(t) {\n    return new (t || Carousel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Carousel,\n    selectors: [[\"p-carousel\"]],\n    contentQueries: function Carousel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 5);\n        i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Carousel_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.indicatorContent = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      page: \"page\",\n      numVisible: \"numVisible\",\n      numScroll: \"numScroll\",\n      responsiveOptions: \"responsiveOptions\",\n      orientation: \"orientation\",\n      verticalViewPortHeight: \"verticalViewPortHeight\",\n      contentClass: \"contentClass\",\n      indicatorsContentClass: \"indicatorsContentClass\",\n      indicatorsContentStyle: \"indicatorsContentStyle\",\n      indicatorStyleClass: \"indicatorStyleClass\",\n      indicatorStyle: \"indicatorStyle\",\n      value: \"value\",\n      circular: \"circular\",\n      showIndicators: \"showIndicators\",\n      showNavigators: \"showNavigators\",\n      autoplayInterval: \"autoplayInterval\",\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    outputs: {\n      onPage: \"onPage\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 14,\n    vars: 23,\n    consts: [[\"itemsContainer\", \"\"], [\"indicatorContent\", \"\"], [\"role\", \"region\", 3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-carousel-header\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-carousel-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-carousel-items-content\", 3, \"ngStyle\"], [1, \"p-carousel-items-container\", 3, \"transitionend\", \"touchend\", \"touchstart\", \"touchmove\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\", \"class\", \"ngStyle\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-carousel-footer\", 4, \"ngIf\"], [1, \"p-carousel-header\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [4, \"ngIf\"], [\"class\", \"p-carousel-prev-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-carousel-prev-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"keydown\", \"ngClass\", \"ngStyle\"], [\"type\", \"button\", 3, \"click\", \"ngClass\", \"ngStyle\", \"tabindex\"], [1, \"p-carousel-footer\"]],\n    template: function Carousel_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelementStart(0, \"div\", 2);\n        i0.ɵɵtemplate(1, Carousel_div_1_Template, 3, 1, \"div\", 3);\n        i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5);\n        i0.ɵɵtemplate(4, Carousel_button_4_Template, 3, 7, \"button\", 6);\n        i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8, 0);\n        i0.ɵɵlistener(\"transitionend\", function Carousel_Template_div_transitionend_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTransitionEnd());\n        })(\"touchend\", function Carousel_Template_div_touchend_6_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchEnd($event));\n        })(\"touchstart\", function Carousel_Template_div_touchstart_6_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchStart($event));\n        })(\"touchmove\", function Carousel_Template_div_touchmove_6_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchMove($event));\n        });\n        i0.ɵɵtemplate(8, Carousel_div_8_Template, 2, 12, \"div\", 9)(9, Carousel_div_9_Template, 2, 12, \"div\", 9)(10, Carousel_div_10_Template, 2, 9, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(11, Carousel_button_11_Template, 3, 7, \"button\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, Carousel_ul_12_Template, 3, 5, \"ul\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, Carousel_div_13_Template, 3, 1, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx.isVertical(), !ctx.isVertical()))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"id\", ctx.id);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.contentClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-carousel-content\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"aria-live\", ctx.allowAutoplay ? \"polite\" : \"off\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c5, ctx.isVertical() ? ctx.verticalViewPortHeight : \"auto\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForStarting);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForFinishing);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon],\n    styles: [\"@layer primeng{.p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Carousel, [{\n    type: Component,\n    args: [{\n      selector: 'p-carousel',\n      template: `\n        <div [attr.id]=\"id\" [ngClass]=\"{ 'p-carousel p-component': true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical() }\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"region\">\n            <div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n                <div class=\"p-carousel-container\" [attr.aria-live]=\"allowAutoplay ? 'polite' : 'off'\">\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-prev p-link': true, 'p-disabled': isBackwardNavDisabled() }\"\n                        [disabled]=\"isBackwardNavDisabled()\"\n                        [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                        (click)=\"navBackward($event)\"\n                        pRipple\n                    >\n                        <ng-container *ngIf=\"!previousIconTemplate\">\n                            <ChevronLeftIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronUpIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"previousIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                    <div class=\"p-carousel-items-content\" [ngStyle]=\"{ height: isVertical() ? verticalViewPortHeight : 'auto' }\">\n                        <div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div\n                                *ngFor=\"let item of clonedItemsForStarting; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === value.length,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForStarting.length - 1 === index\n                                }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of value; let index = index\"\n                                [ngClass]=\"{ 'p-carousel-item': true, 'p-carousel-item-active': firstIndex() <= index && lastIndex() >= index, 'p-carousel-item-start': firstIndex() === index, 'p-carousel-item-end': lastIndex() === index }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of clonedItemsForFinishing; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === numVisible,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForFinishing.length - 1 === index\n                                }\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                        </div>\n                    </div>\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled() }\"\n                        [disabled]=\"isForwardNavDisabled()\"\n                        (click)=\"navForward($event)\"\n                        pRipple\n                        [attr.aria-label]=\"ariaNextButtonLabel()\"\n                    >\n                        <ng-container *ngIf=\"!nextIconTemplate\">\n                            <ChevronRightIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronDownIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"nextIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <ul #indicatorContent [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\" (keydown)=\"onIndicatorKeydown($event)\">\n                    <li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{ 'p-carousel-indicator': true, 'p-highlight': _page === i }\" [attr.data-pc-section]=\"'indicator'\">\n                        <button\n                            type=\"button\"\n                            [ngClass]=\"'p-link'\"\n                            (click)=\"onDotClick($event, i)\"\n                            [class]=\"indicatorStyleClass\"\n                            [ngStyle]=\"indicatorStyle\"\n                            [attr.aria-label]=\"ariaPageLabel(i + 1)\"\n                            [attr.aria-current]=\"_page === i ? 'page' : undefined\"\n                            [tabindex]=\"_page === i ? 0 : -1\"\n                        ></button>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i1.PrimeNGConfig\n  }], {\n    page: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    numScroll: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    verticalViewPortHeight: [{\n      type: Input\n    }],\n    contentClass: [{\n      type: Input\n    }],\n    indicatorsContentClass: [{\n      type: Input\n    }],\n    indicatorsContentStyle: [{\n      type: Input\n    }],\n    indicatorStyleClass: [{\n      type: Input\n    }],\n    indicatorStyle: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    showNavigators: [{\n      type: Input\n    }],\n    autoplayInterval: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    onPage: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    indicatorContent: [{\n      type: ViewChild,\n      args: ['indicatorContent']\n    }],\n    headerFacet: [{\n      type: ContentChild,\n      args: [Header]\n    }],\n    footerFacet: [{\n      type: ContentChild,\n      args: [Footer]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass CarouselModule {\n  static ɵfac = function CarouselModule_Factory(t) {\n    return new (t || CarouselModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CarouselModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon, CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon],\n      exports: [CommonModule, Carousel, SharedModule],\n      declarations: [Carousel]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Carousel, CarouselModule };", "map": {"version": 3, "names": ["i2", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChild", "ContentChildren", "NgModule", "i1", "Header", "Footer", "PrimeTemplate", "SharedModule", "ChevronDownIcon", "ChevronLeftIcon", "ChevronRightIcon", "ChevronUpIcon", "i3", "RippleModule", "UniqueComponentId", "<PERSON><PERSON><PERSON><PERSON>", "_c0", "_c1", "_c2", "_c3", "_c4", "a0", "a1", "_c5", "height", "_c6", "_c7", "a2", "_c8", "$implicit", "_c9", "_c10", "_c11", "Carousel_div_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainer", "Carousel_div_1_Template", "ɵɵelementStart", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "headerTemplate", "Carousel_button_4_ng_container_1_ChevronLeftIcon_1_Template", "ɵɵelement", "Carousel_button_4_ng_container_1_ChevronUpIcon_2_Template", "Carousel_button_4_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "isVertical", "Carousel_button_4_span_2_1_ng_template_0_Template", "Carousel_button_4_span_2_1_Template", "Carousel_button_4_span_2_Template", "previousIconTemplate", "Carousel_button_4_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "Carousel_button_4_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "navBackward", "ɵɵpureFunction1", "isBackwardNavDisabled", "ɵɵattribute", "ariaPrevButtonLabel", "Carousel_div_8_ng_container_1_Template", "Carousel_div_8_Template", "item_r4", "index_r5", "index", "ɵɵpureFunction3", "totalShiftedItems", "value", "length", "clonedItemsForStarting", "ariaSlideNumber", "ariaSlideLabel", "itemTemplate", "Carousel_div_9_ng_container_1_Template", "Carousel_div_9_Template", "item_r6", "index_r7", "firstIndex", "lastIndex", "Carousel_div_10_ng_container_1_Template", "Carousel_div_10_Template", "item_r8", "index_r9", "numVisible", "clonedItemsForFinishing", "Carousel_button_11_ng_container_1_ChevronRightIcon_1_Template", "Carousel_button_11_ng_container_1_ChevronDownIcon_2_Template", "Carousel_button_11_ng_container_1_Template", "Carousel_button_11_span_2_1_ng_template_0_Template", "Carousel_button_11_span_2_1_Template", "Carousel_button_11_span_2_Template", "nextIconTemplate", "Carousel_button_11_Template", "_r10", "Carousel_button_11_Template_button_click_0_listener", "navForward", "isForwardNavDisabled", "ariaNextButtonLabel", "Carousel_ul_12_li_2_Template", "_r12", "Carousel_ul_12_li_2_Template_button_click_1_listener", "i_r13", "onDotClick", "_page", "ɵɵclassMap", "indicatorStyleClass", "indicatorStyle", "ariaPageLabel", "undefined", "Carousel_ul_12_Template", "_r11", "Carousel_ul_12_Template_ul_keydown_0_listener", "onIndicatorKeydown", "indicatorsContentClass", "indicatorsContentStyle", "totalDotsArray", "Carousel_div_13_ng_container_2_Template", "Carousel_div_13_Template", "footerTemplate", "Carousel", "el", "zone", "cd", "renderer", "document", "platformId", "config", "page", "val", "isCreated", "autoplayInterval", "stopAutoplay", "totalDots", "step", "_numVisible", "numScroll", "_numScroll", "responsiveOptions", "orientation", "verticalViewPortHeight", "contentClass", "_value", "circular", "showIndicators", "showNavigators", "style", "styleClass", "onPage", "itemsContainer", "indicatorContent", "headerFacet", "footer<PERSON><PERSON><PERSON>", "templates", "_oldNumScroll", "prevState", "defaultNumScroll", "defaultNumVisible", "carouselStyle", "id", "isRemainingItemsAdded", "animationTimeout", "translateTimeout", "remainingItems", "_items", "startPos", "documentResizeListener", "allowAutoplay", "interval", "swipe<PERSON><PERSON><PERSON><PERSON>", "window", "constructor", "defaultView", "ngOnChanges", "simpleChange", "setCloneItems", "isCircular", "createStyle", "calculatePosition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngAfterContentInit", "bindDocumentListeners", "for<PERSON>ach", "item", "getType", "template", "detectChanges", "ngAfterContentChecked", "emit", "nativeElement", "transform", "isAutoplay", "startAutoplay", "createElement", "type", "append<PERSON><PERSON><PERSON>", "head", "innerHTML", "sort", "data1", "data2", "value1", "breakpoint", "value2", "result", "localeCompare", "numeric", "i", "res", "matchedResponsiveData", "windowWidth", "innerWidth", "parseInt", "Math", "floor", "push", "slice", "ceil", "Array", "fill", "isEmpty", "e", "cancelable", "preventDefault", "event", "code", "onRightKey", "onLeftKey", "indicators", "find", "activeIndex", "findFocusedIndicatorIndex", "changedFocusedIndicator", "onHomeKey", "onEndKey", "onTabKey", "highlightedIndex", "findIndex", "ind", "getAttribute", "activeIndicator", "findSingle", "parentElement", "children", "tabIndex", "prevInd", "nextInd", "focus", "dir", "originalShiftedItems", "abs", "transition", "setInterval", "changeAllow", "clearInterval", "isPlaying", "onTransitionEnd", "onTouchStart", "<PERSON><PERSON><PERSON>", "changedTouches", "x", "pageX", "y", "pageY", "onTouchMove", "onTouchEnd", "changePageOnTouch", "diff", "translation", "aria", "prevPageLabel", "slide", "nextPageLabel", "slideNumber", "replace", "pageLabel", "listen", "unbindDocumentListeners", "ngOnDestroy", "ɵfac", "Carousel_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ChangeDetectorRef", "Renderer2", "PrimeNGConfig", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "Carousel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "Carousel_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "Carousel_Template", "_r1", "ɵɵprojectionDef", "Carousel_Template_div_transitionend_6_listener", "Carousel_Template_div_touchend_6_listener", "Carousel_Template_div_touchstart_6_listener", "Carousel_Template_div_touchmove_6_listener", "ɵɵpureFunction2", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "Document", "decorators", "CarouselModule", "CarouselModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-carousel.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\n/**\n * Carousel is a content slider featuring various customization options.\n * @group Components\n */\nclass Carousel {\n    el;\n    zone;\n    cd;\n    renderer;\n    document;\n    platformId;\n    config;\n    /**\n     * Index of the first item.\n     * @defaultValue 0\n     * @group Props\n     */\n    get page() {\n        return this._page;\n    }\n    set page(val) {\n        if (this.isCreated && val !== this._page) {\n            if (this.autoplayInterval) {\n                this.stopAutoplay();\n            }\n            if (val > this._page && val <= this.totalDots() - 1) {\n                this.step(-1, val);\n            }\n            else if (val < this._page) {\n                this.step(1, val);\n            }\n        }\n        this._page = val;\n    }\n    /**\n     * Number of items per page.\n     * @defaultValue 1\n     * @group Props\n     */\n    get numVisible() {\n        return this._numVisible;\n    }\n    set numVisible(val) {\n        this._numVisible = val;\n    }\n    /**\n     * Number of items to scroll.\n     * @defaultValue 1\n     * @group Props\n     */\n    get numScroll() {\n        return this._numVisible;\n    }\n    set numScroll(val) {\n        this._numScroll = val;\n    }\n    /**\n     * An array of options for responsive design.\n     * @see {CarouselResponsiveOptions}\n     * @group Props\n     */\n    responsiveOptions;\n    /**\n     * Specifies the layout of the component.\n     * @group Props\n     */\n    orientation = 'horizontal';\n    /**\n     * Height of the viewport in vertical layout.\n     * @group Props\n     */\n    verticalViewPortHeight = '300px';\n    /**\n     * Style class of main content.\n     * @group Props\n     */\n    contentClass = '';\n    /**\n     * Style class of the indicator items.\n     * @group Props\n     */\n    indicatorsContentClass = '';\n    /**\n     * Inline style of the indicator items.\n     * @group Props\n     */\n    indicatorsContentStyle;\n    /**\n     * Style class of the indicators.\n     * @group Props\n     */\n    indicatorStyleClass = '';\n    /**\n     * Style of the indicators.\n     * @group Props\n     */\n    indicatorStyle;\n    /**\n     * An array of objects to display.\n     * @defaultValue null\n     * @group Props\n     */\n    get value() {\n        return this._value;\n    }\n    set value(val) {\n        this._value = val;\n    }\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    circular = false;\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    showIndicators = true;\n    /**\n     * Whether to display navigation buttons in container.\n     * @group Props\n     */\n    showNavigators = true;\n    /**\n     * Time in milliseconds to scroll items automatically.\n     * @group Props\n     */\n    autoplayInterval = 0;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the viewport container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Callback to invoke after scroll.\n     * @param {CarouselPageEvent} event - Custom page event.\n     * @group Emits\n     */\n    onPage = new EventEmitter();\n    itemsContainer;\n    indicatorContent;\n    headerFacet;\n    footerFacet;\n    templates;\n    _numVisible = 1;\n    _numScroll = 1;\n    _oldNumScroll = 0;\n    prevState = {\n        numScroll: 0,\n        numVisible: 0,\n        value: []\n    };\n    defaultNumScroll = 1;\n    defaultNumVisible = 1;\n    _page = 0;\n    _value;\n    carouselStyle;\n    id;\n    totalShiftedItems;\n    isRemainingItemsAdded = false;\n    animationTimeout;\n    translateTimeout;\n    remainingItems = 0;\n    _items;\n    startPos;\n    documentResizeListener;\n    clonedItemsForStarting;\n    clonedItemsForFinishing;\n    allowAutoplay;\n    interval;\n    isCreated;\n    swipeThreshold = 20;\n    itemTemplate;\n    headerTemplate;\n    footerTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    window;\n    constructor(el, zone, cd, renderer, document, platformId, config) {\n        this.el = el;\n        this.zone = zone;\n        this.cd = cd;\n        this.renderer = renderer;\n        this.document = document;\n        this.platformId = platformId;\n        this.config = config;\n        this.totalShiftedItems = this.page * this.numScroll * -1;\n        this.window = this.document.defaultView;\n    }\n    ngOnChanges(simpleChange) {\n        if (isPlatformBrowser(this.platformId)) {\n            if (simpleChange.value) {\n                if (this.circular && this._value) {\n                    this.setCloneItems();\n                }\n            }\n            if (this.isCreated) {\n                if (simpleChange.numVisible) {\n                    if (this.responsiveOptions) {\n                        this.defaultNumVisible = this.numVisible;\n                    }\n                    if (this.isCircular()) {\n                        this.setCloneItems();\n                    }\n                    this.createStyle();\n                    this.calculatePosition();\n                }\n                if (simpleChange.numScroll) {\n                    if (this.responsiveOptions) {\n                        this.defaultNumScroll = this.numScroll;\n                    }\n                }\n            }\n        }\n        this.cd.markForCheck();\n    }\n    ngAfterContentInit() {\n        this.id = UniqueComponentId();\n        if (isPlatformBrowser(this.platformId)) {\n            this.allowAutoplay = !!this.autoplayInterval;\n            if (this.circular) {\n                this.setCloneItems();\n            }\n            if (this.responsiveOptions) {\n                this.defaultNumScroll = this._numScroll;\n                this.defaultNumVisible = this._numVisible;\n            }\n            this.createStyle();\n            this.calculatePosition();\n            if (this.responsiveOptions) {\n                this.bindDocumentListeners();\n            }\n        }\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n        this.cd.detectChanges();\n    }\n    ngAfterContentChecked() {\n        if (isPlatformBrowser(this.platformId)) {\n            const isCircular = this.isCircular();\n            let totalShiftedItems = this.totalShiftedItems;\n            if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n                if (this.autoplayInterval) {\n                    this.stopAutoplay(false);\n                }\n                this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n                let page = this._page;\n                if (this.totalDots() !== 0 && page >= this.totalDots()) {\n                    page = this.totalDots() - 1;\n                    this._page = page;\n                    this.onPage.emit({\n                        page: this.page\n                    });\n                }\n                totalShiftedItems = page * this._numScroll * -1;\n                if (isCircular) {\n                    totalShiftedItems -= this._numVisible;\n                }\n                if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n                    totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n                    this.isRemainingItemsAdded = true;\n                }\n                else {\n                    this.isRemainingItemsAdded = false;\n                }\n                if (totalShiftedItems !== this.totalShiftedItems) {\n                    this.totalShiftedItems = totalShiftedItems;\n                }\n                this._oldNumScroll = this._numScroll;\n                this.prevState.numScroll = this._numScroll;\n                this.prevState.numVisible = this._numVisible;\n                this.prevState.value = [...this._value];\n                if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n                    this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n                }\n                this.isCreated = true;\n                if (this.autoplayInterval && this.isAutoplay()) {\n                    this.startAutoplay();\n                }\n            }\n            if (isCircular) {\n                if (this.page === 0) {\n                    totalShiftedItems = -1 * this._numVisible;\n                }\n                else if (totalShiftedItems === 0) {\n                    totalShiftedItems = -1 * this.value.length;\n                    if (this.remainingItems > 0) {\n                        this.isRemainingItemsAdded = true;\n                    }\n                }\n                if (totalShiftedItems !== this.totalShiftedItems) {\n                    this.totalShiftedItems = totalShiftedItems;\n                }\n            }\n        }\n    }\n    createStyle() {\n        if (!this.carouselStyle) {\n            this.carouselStyle = this.renderer.createElement('style');\n            this.carouselStyle.type = 'text/css';\n            this.renderer.appendChild(this.document.head, this.carouselStyle);\n        }\n        let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n        if (this.responsiveOptions) {\n            this.responsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n                if (value1 == null && value2 != null)\n                    result = -1;\n                else if (value1 != null && value2 == null)\n                    result = 1;\n                else if (value1 == null && value2 == null)\n                    result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string')\n                    result = value1.localeCompare(value2, undefined, { numeric: true });\n                else\n                    result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n                return -1 * result;\n            });\n            for (let i = 0; i < this.responsiveOptions.length; i++) {\n                let res = this.responsiveOptions[i];\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n            }\n        }\n        this.carouselStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n        if (this.responsiveOptions) {\n            let matchedResponsiveData = {\n                numVisible: this.defaultNumVisible,\n                numScroll: this.defaultNumScroll\n            };\n            if (typeof window !== 'undefined') {\n                let windowWidth = window.innerWidth;\n                for (let i = 0; i < this.responsiveOptions.length; i++) {\n                    let res = this.responsiveOptions[i];\n                    if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                        matchedResponsiveData = res;\n                    }\n                }\n            }\n            if (this._numScroll !== matchedResponsiveData.numScroll) {\n                let page = this._page;\n                page = Math.floor((page * this._numScroll) / matchedResponsiveData.numScroll);\n                let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n                if (this.isCircular()) {\n                    totalShiftedItems -= matchedResponsiveData.numVisible;\n                }\n                this.totalShiftedItems = totalShiftedItems;\n                this._numScroll = matchedResponsiveData.numScroll;\n                this._page = page;\n                this.onPage.emit({\n                    page: this.page\n                });\n            }\n            if (this._numVisible !== matchedResponsiveData.numVisible) {\n                this._numVisible = matchedResponsiveData.numVisible;\n                this.setCloneItems();\n            }\n            this.cd.markForCheck();\n        }\n    }\n    setCloneItems() {\n        this.clonedItemsForStarting = [];\n        this.clonedItemsForFinishing = [];\n        if (this.isCircular()) {\n            this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n            this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n        }\n    }\n    firstIndex() {\n        return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n    }\n    lastIndex() {\n        return this.firstIndex() + this.numVisible - 1;\n    }\n    totalDots() {\n        return this.value?.length ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n    }\n    totalDotsArray() {\n        const totalDots = this.totalDots();\n        return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n    }\n    isVertical() {\n        return this.orientation === 'vertical';\n    }\n    isCircular() {\n        return this.circular && this.value && this.value.length >= this.numVisible;\n    }\n    isAutoplay() {\n        return this.autoplayInterval && this.allowAutoplay;\n    }\n    isForwardNavDisabled() {\n        return this.isEmpty() || (this._page >= this.totalDots() - 1 && !this.isCircular());\n    }\n    isBackwardNavDisabled() {\n        return this.isEmpty() || (this._page <= 0 && !this.isCircular());\n    }\n    isEmpty() {\n        return !this.value || this.value.length === 0;\n    }\n    navForward(e, index) {\n        if (this.isCircular() || this._page < this.totalDots() - 1) {\n            this.step(-1, index);\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e, index) {\n        if (this.isCircular() || this._page !== 0) {\n            this.step(1, index);\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onDotClick(e, index) {\n        let page = this._page;\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n        if (index > page) {\n            this.navForward(e, index);\n        }\n        else if (index < page) {\n            this.navBackward(e, index);\n        }\n    }\n    onIndicatorKeydown(event) {\n        switch (event.code) {\n            case 'ArrowRight':\n                this.onRightKey();\n                break;\n            case 'ArrowLeft':\n                this.onLeftKey();\n                break;\n        }\n    }\n    onRightKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n    onLeftKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n    onHomeKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, 0);\n    }\n    onEndKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]r')];\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n    onTabKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const highlightedIndex = indicators.findIndex((ind) => DomHandler.getAttribute(ind, 'data-p-highlight') === true);\n        const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n        const activeIndex = indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n        indicators[activeIndex].children[0].tabIndex = '-1';\n        indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n    findFocusedIndicatorIndex() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n        return indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n    }\n    changedFocusedIndicator(prevInd, nextInd) {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        indicators[prevInd].children[0].tabIndex = '-1';\n        indicators[nextInd].children[0].tabIndex = '0';\n        indicators[nextInd].children[0].focus();\n    }\n    step(dir, page) {\n        let totalShiftedItems = this.totalShiftedItems;\n        const isCircular = this.isCircular();\n        if (page != null) {\n            totalShiftedItems = this._numScroll * page * -1;\n            if (isCircular) {\n                totalShiftedItems -= this._numVisible;\n            }\n            this.isRemainingItemsAdded = false;\n        }\n        else {\n            totalShiftedItems += this._numScroll * dir;\n            if (this.isRemainingItemsAdded) {\n                totalShiftedItems += this.remainingItems - this._numScroll * dir;\n                this.isRemainingItemsAdded = false;\n            }\n            let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n            page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n        }\n        if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n            totalShiftedItems = -1 * (this.value.length + this._numVisible);\n            page = 0;\n        }\n        else if (isCircular && this.page === 0 && dir === 1) {\n            totalShiftedItems = 0;\n            page = this.totalDots() - 1;\n        }\n        else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n            totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n            this.isRemainingItemsAdded = true;\n        }\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n        this.totalShiftedItems = totalShiftedItems;\n        this._page = page;\n        this.onPage.emit({\n            page: this.page\n        });\n        this.cd.markForCheck();\n    }\n    startAutoplay() {\n        this.interval = setInterval(() => {\n            if (this.totalDots() > 0) {\n                if (this.page === this.totalDots() - 1) {\n                    this.step(-1, 0);\n                }\n                else {\n                    this.step(-1, this.page + 1);\n                }\n            }\n        }, this.autoplayInterval);\n        this.allowAutoplay = true;\n        this.cd.markForCheck();\n    }\n    stopAutoplay(changeAllow = true) {\n        if (this.interval) {\n            clearInterval(this.interval);\n            this.interval = undefined;\n            if (changeAllow) {\n                this.allowAutoplay = false;\n            }\n        }\n        this.cd.markForCheck();\n    }\n    isPlaying() {\n        return !!this.interval;\n    }\n    onTransitionEnd() {\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transition = '';\n            if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            }\n        }\n    }\n    onTouchStart(e) {\n        let touchobj = e.changedTouches[0];\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n    onTouchMove(e) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onTouchEnd(e) {\n        let touchobj = e.changedTouches[0];\n        if (this.isVertical()) {\n            this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n        }\n        else {\n            this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n        }\n    }\n    changePageOnTouch(e, diff) {\n        if (Math.abs(diff) > this.swipeThreshold) {\n            if (diff < 0) {\n                this.navForward(e);\n            }\n            else {\n                this.navBackward(e);\n            }\n        }\n    }\n    ariaPrevButtonLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.prevPageLabel : undefined;\n    }\n    ariaSlideLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.slide : undefined;\n    }\n    ariaNextButtonLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.nextPageLabel : undefined;\n    }\n    ariaSlideNumber(value) {\n        return this.config.translation.aria ? this.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n    ariaPageLabel(value) {\n        return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    bindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentResizeListener) {\n                this.documentResizeListener = this.renderer.listen(this.window, 'resize', (event) => {\n                    this.calculatePosition();\n                });\n            }\n        }\n    }\n    unbindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.documentResizeListener) {\n                this.documentResizeListener();\n                this.documentResizeListener = null;\n            }\n        }\n    }\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Carousel, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Carousel, selector: \"p-carousel\", inputs: { page: \"page\", numVisible: \"numVisible\", numScroll: \"numScroll\", responsiveOptions: \"responsiveOptions\", orientation: \"orientation\", verticalViewPortHeight: \"verticalViewPortHeight\", contentClass: \"contentClass\", indicatorsContentClass: \"indicatorsContentClass\", indicatorsContentStyle: \"indicatorsContentStyle\", indicatorStyleClass: \"indicatorStyleClass\", indicatorStyle: \"indicatorStyle\", value: \"value\", circular: \"circular\", showIndicators: \"showIndicators\", showNavigators: \"showNavigators\", autoplayInterval: \"autoplayInterval\", style: \"style\", styleClass: \"styleClass\" }, outputs: { onPage: \"onPage\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", first: true, predicate: Header, descendants: true }, { propertyName: \"footerFacet\", first: true, predicate: Footer, descendants: true }, { propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"itemsContainer\", first: true, predicate: [\"itemsContainer\"], descendants: true }, { propertyName: \"indicatorContent\", first: true, predicate: [\"indicatorContent\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div [attr.id]=\"id\" [ngClass]=\"{ 'p-carousel p-component': true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical() }\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"region\">\n            <div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n                <div class=\"p-carousel-container\" [attr.aria-live]=\"allowAutoplay ? 'polite' : 'off'\">\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-prev p-link': true, 'p-disabled': isBackwardNavDisabled() }\"\n                        [disabled]=\"isBackwardNavDisabled()\"\n                        [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                        (click)=\"navBackward($event)\"\n                        pRipple\n                    >\n                        <ng-container *ngIf=\"!previousIconTemplate\">\n                            <ChevronLeftIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronUpIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"previousIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                    <div class=\"p-carousel-items-content\" [ngStyle]=\"{ height: isVertical() ? verticalViewPortHeight : 'auto' }\">\n                        <div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div\n                                *ngFor=\"let item of clonedItemsForStarting; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === value.length,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForStarting.length - 1 === index\n                                }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of value; let index = index\"\n                                [ngClass]=\"{ 'p-carousel-item': true, 'p-carousel-item-active': firstIndex() <= index && lastIndex() >= index, 'p-carousel-item-start': firstIndex() === index, 'p-carousel-item-end': lastIndex() === index }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of clonedItemsForFinishing; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === numVisible,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForFinishing.length - 1 === index\n                                }\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                        </div>\n                    </div>\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled() }\"\n                        [disabled]=\"isForwardNavDisabled()\"\n                        (click)=\"navForward($event)\"\n                        pRipple\n                        [attr.aria-label]=\"ariaNextButtonLabel()\"\n                    >\n                        <ng-container *ngIf=\"!nextIconTemplate\">\n                            <ChevronRightIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronDownIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"nextIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <ul #indicatorContent [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\" (keydown)=\"onIndicatorKeydown($event)\">\n                    <li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{ 'p-carousel-indicator': true, 'p-highlight': _page === i }\" [attr.data-pc-section]=\"'indicator'\">\n                        <button\n                            type=\"button\"\n                            [ngClass]=\"'p-link'\"\n                            (click)=\"onDotClick($event, i)\"\n                            [class]=\"indicatorStyleClass\"\n                            [ngStyle]=\"indicatorStyle\"\n                            [attr.aria-label]=\"ariaPageLabel(i + 1)\"\n                            [attr.aria-current]=\"_page === i ? 'page' : undefined\"\n                            [tabindex]=\"_page === i ? 0 : -1\"\n                        ></button>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronLeftIcon), selector: \"ChevronLeftIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronDownIcon), selector: \"ChevronDownIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronUpIcon), selector: \"ChevronUpIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Carousel, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-carousel', template: `\n        <div [attr.id]=\"id\" [ngClass]=\"{ 'p-carousel p-component': true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical() }\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"region\">\n            <div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n                <div class=\"p-carousel-container\" [attr.aria-live]=\"allowAutoplay ? 'polite' : 'off'\">\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-prev p-link': true, 'p-disabled': isBackwardNavDisabled() }\"\n                        [disabled]=\"isBackwardNavDisabled()\"\n                        [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                        (click)=\"navBackward($event)\"\n                        pRipple\n                    >\n                        <ng-container *ngIf=\"!previousIconTemplate\">\n                            <ChevronLeftIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronUpIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"previousIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                    <div class=\"p-carousel-items-content\" [ngStyle]=\"{ height: isVertical() ? verticalViewPortHeight : 'auto' }\">\n                        <div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div\n                                *ngFor=\"let item of clonedItemsForStarting; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === value.length,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForStarting.length - 1 === index\n                                }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of value; let index = index\"\n                                [ngClass]=\"{ 'p-carousel-item': true, 'p-carousel-item-active': firstIndex() <= index && lastIndex() >= index, 'p-carousel-item-start': firstIndex() === index, 'p-carousel-item-end': lastIndex() === index }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of clonedItemsForFinishing; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === numVisible,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForFinishing.length - 1 === index\n                                }\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                        </div>\n                    </div>\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled() }\"\n                        [disabled]=\"isForwardNavDisabled()\"\n                        (click)=\"navForward($event)\"\n                        pRipple\n                        [attr.aria-label]=\"ariaNextButtonLabel()\"\n                    >\n                        <ng-container *ngIf=\"!nextIconTemplate\">\n                            <ChevronRightIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronDownIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"nextIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <ul #indicatorContent [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\" (keydown)=\"onIndicatorKeydown($event)\">\n                    <li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{ 'p-carousel-indicator': true, 'p-highlight': _page === i }\" [attr.data-pc-section]=\"'indicator'\">\n                        <button\n                            type=\"button\"\n                            [ngClass]=\"'p-link'\"\n                            (click)=\"onDotClick($event, i)\"\n                            [class]=\"indicatorStyleClass\"\n                            [ngStyle]=\"indicatorStyle\"\n                            [attr.aria-label]=\"ariaPageLabel(i + 1)\"\n                            [attr.aria-current]=\"_page === i ? 'page' : undefined\"\n                            [tabindex]=\"_page === i ? 0 : -1\"\n                        ></button>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i1.PrimeNGConfig }], propDecorators: { page: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], numScroll: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], verticalViewPortHeight: [{\n                type: Input\n            }], contentClass: [{\n                type: Input\n            }], indicatorsContentClass: [{\n                type: Input\n            }], indicatorsContentStyle: [{\n                type: Input\n            }], indicatorStyleClass: [{\n                type: Input\n            }], indicatorStyle: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], showNavigators: [{\n                type: Input\n            }], autoplayInterval: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], onPage: [{\n                type: Output\n            }], itemsContainer: [{\n                type: ViewChild,\n                args: ['itemsContainer']\n            }], indicatorContent: [{\n                type: ViewChild,\n                args: ['indicatorContent']\n            }], headerFacet: [{\n                type: ContentChild,\n                args: [Header]\n            }], footerFacet: [{\n                type: ContentChild,\n                args: [Footer]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass CarouselModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: CarouselModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: CarouselModule, declarations: [Carousel], imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon], exports: [CommonModule, Carousel, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: CarouselModule, imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon, CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: CarouselModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon],\n                    exports: [CommonModule, Carousel, SharedModule],\n                    declarations: [Carousel]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Carousel, CarouselModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC3L,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,MAAM,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,uBAAAD,EAAA;EAAA,yBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAF,EAAA;EAAAG,MAAA,EAAAH;AAAA;AAAA,MAAAI,GAAA,GAAAJ,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAK,GAAA,GAAAA,CAAAL,EAAA,EAAAC,EAAA,EAAAK,EAAA;EAAA;EAAA,0BAAAN,EAAA;EAAA,yBAAAC,EAAA;EAAA,uBAAAK;AAAA;AAAA,MAAAC,GAAA,GAAAP,EAAA;EAAAQ,SAAA,EAAAR;AAAA;AAAA,MAAAS,GAAA,GAAAA,CAAAT,EAAA,EAAAC,EAAA,EAAAK,EAAA;EAAA;EAAA,0BAAAN,EAAA;EAAA,yBAAAC,EAAA;EAAA,uBAAAK;AAAA;AAAA,MAAAI,IAAA,GAAAV,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAW,IAAA,GAAAX,EAAA;EAAA;EAAA,eAAAA;AAAA;AAAA,SAAAY,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0pB6F5C,EAAE,CAAA8C,kBAAA,EAKhB,CAAC;EAAA;AAAA;AAAA,SAAAC,wBAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALa5C,EAAE,CAAAgD,cAAA,aAGf,CAAC;IAHYhD,EAAE,CAAAiD,YAAA,EAIrC,CAAC;IAJkCjD,EAAE,CAAAkD,UAAA,IAAAP,sCAAA,0BAK/B,CAAC;IAL4B3C,EAAE,CAAAmD,YAAA,CAM9E,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAN2EpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,EAKjC,CAAC;IAL8BtD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAI,cAKjC,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAL8B5C,EAAE,CAAA0D,SAAA,yBAmBS,CAAC;EAAA;EAAA,IAAAd,EAAA;IAnBZ5C,EAAE,CAAAuD,UAAA,mCAmBM,CAAC;EAAA;AAAA;AAAA,SAAAI,0DAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBT5C,EAAE,CAAA0D,SAAA,uBAoBM,CAAC;EAAA;EAAA,IAAAd,EAAA;IApBT5C,EAAE,CAAAuD,UAAA,mCAoBG,CAAC;EAAA;AAAA;AAAA,SAAAK,0CAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBN5C,EAAE,CAAA6D,uBAAA,EAkB5B,CAAC;IAlByB7D,EAAE,CAAAkD,UAAA,IAAAO,2DAAA,6BAmBS,CAAC,IAAAE,yDAAA,2BACJ,CAAC;IApBT3D,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAQ,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,CAmB/B,CAAC;IAnB4BtD,EAAE,CAAAuD,UAAA,UAAAH,MAAA,CAAAW,UAAA,EAmB/B,CAAC;IAnB4B/D,EAAE,CAAAsD,SAAA,CAoBlC,CAAC;IApB+BtD,EAAE,CAAAuD,UAAA,SAAAH,MAAA,CAAAW,UAAA,EAoBlC,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAApB,EAAA,EAAAC,GAAA;AAAA,SAAAoB,oCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApB+B5C,EAAE,CAAAkD,UAAA,IAAAc,iDAAA,qBAuBd,CAAC;EAAA;AAAA;AAAA,SAAAE,kCAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBW5C,EAAE,CAAAgD,cAAA,cAsBR,CAAC;IAtBKhD,EAAE,CAAAkD,UAAA,IAAAe,mCAAA,gBAuBd,CAAC;IAvBWjE,EAAE,CAAAmD,YAAA,CAwBjE,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAxB8DpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,CAuBhB,CAAC;IAvBatD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAe,oBAuBhB,CAAC;EAAA;AAAA;AAAA,SAAAC,2BAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyB,GAAA,GAvBarE,EAAE,CAAAsE,gBAAA;IAAFtE,EAAE,CAAAgD,cAAA,gBAiB3E,CAAC;IAjBwEhD,EAAE,CAAAuE,UAAA,mBAAAC,mDAAAC,MAAA;MAAFzE,EAAE,CAAA0E,aAAA,CAAAL,GAAA;MAAA,MAAAjB,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAA2E,WAAA,CAe9DvB,MAAA,CAAAwB,WAAA,CAAAH,MAAkB,CAAC;IAAA,EAAC;IAfwCzE,EAAE,CAAAkD,UAAA,IAAAU,yCAAA,0BAkB5B,CAAC,IAAAM,iCAAA,kBAImB,CAAC;IAtBKlE,EAAE,CAAAmD,YAAA,CAyBnE,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAzBgEpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAA6E,eAAA,IAAA1C,GAAA,EAAAiB,MAAA,CAAA0B,qBAAA,GAYa,CAAC,aAAA1B,MAAA,CAAA0B,qBAAA,EAClD,CAAC;IAbiC9E,EAAE,CAAA+E,WAAA,eAAA3B,MAAA,CAAA4B,mBAAA;IAAFhF,EAAE,CAAAsD,SAAA,CAkB9B,CAAC;IAlB2BtD,EAAE,CAAAuD,UAAA,UAAAH,MAAA,CAAAe,oBAkB9B,CAAC;IAlB2BnE,EAAE,CAAAsD,SAAA,CAsBvC,CAAC;IAtBoCtD,EAAE,CAAAuD,UAAA,SAAAH,MAAA,CAAAe,oBAsBvC,CAAC;EAAA;AAAA;AAAA,SAAAc,uCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtBoC5C,EAAE,CAAA8C,kBAAA,EAwC4B,CAAC;EAAA;AAAA;AAAA,SAAAoC,wBAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxC/B5C,EAAE,CAAAgD,cAAA,YAuCnE,CAAC;IAvCgEhD,EAAE,CAAAkD,UAAA,IAAA+B,sCAAA,0BAwCa,CAAC;IAxChBjF,EAAE,CAAAmD,YAAA,CAyC9D,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAuC,OAAA,GAAAtC,GAAA,CAAAN,SAAA;IAAA,MAAA6C,QAAA,GAAAvC,GAAA,CAAAwC,KAAA;IAAA,MAAAjC,MAAA,GAzC2DpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAAsF,eAAA,IAAAlD,GAAA,EAAAgB,MAAA,CAAAmC,iBAAA,UAAAnC,MAAA,CAAAoC,KAAA,CAAAC,MAAA,QAAAL,QAAA,EAAAhC,MAAA,CAAAsC,sBAAA,CAAAD,MAAA,SAAAL,QAAA,CAmC9D,CAAC;IAnC2DpF,EAAE,CAAA+E,WAAA,kBAAA3B,MAAA,CAAAmC,iBAAA,UAAAnC,MAAA,CAAAoC,KAAA,CAAAC,MAAA,iBAAArC,MAAA,CAAAuC,eAAA,CAAAP,QAAA,2BAAAhC,MAAA,CAAAwC,cAAA;IAAF5F,EAAE,CAAAsD,SAAA,CAwCjB,CAAC;IAxCctD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAyC,YAwCjB,CAAC,4BAxCc7F,EAAE,CAAA6E,eAAA,KAAAvC,GAAA,EAAA6C,OAAA,CAwCW,CAAC;EAAA;AAAA;AAAA,SAAAW,uCAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxCd5C,EAAE,CAAA8C,kBAAA,EAiD4B,CAAC;EAAA;AAAA;AAAA,SAAAiD,wBAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjD/B5C,EAAE,CAAAgD,cAAA,YAgDnE,CAAC;IAhDgEhD,EAAE,CAAAkD,UAAA,IAAA4C,sCAAA,0BAiDa,CAAC;IAjDhB9F,EAAE,CAAAmD,YAAA,CAkD9D,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAoD,OAAA,GAAAnD,GAAA,CAAAN,SAAA;IAAA,MAAA0D,QAAA,GAAApD,GAAA,CAAAwC,KAAA;IAAA,MAAAjC,MAAA,GAlD2DpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAAsF,eAAA,IAAA9C,GAAA,EAAAY,MAAA,CAAA8C,UAAA,MAAAD,QAAA,IAAA7C,MAAA,CAAA+C,SAAA,MAAAF,QAAA,EAAA7C,MAAA,CAAA8C,UAAA,OAAAD,QAAA,EAAA7C,MAAA,CAAA+C,SAAA,OAAAF,QAAA,CA4C+I,CAAC;IA5ClJjG,EAAE,CAAA+E,WAAA,kBAAA3B,MAAA,CAAAmC,iBAAA,UAAAnC,MAAA,CAAAoC,KAAA,CAAAC,MAAA,iBAAArC,MAAA,CAAAuC,eAAA,CAAAM,QAAA,2BAAA7C,MAAA,CAAAwC,cAAA;IAAF5F,EAAE,CAAAsD,SAAA,CAiDjB,CAAC;IAjDctD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAyC,YAiDjB,CAAC,4BAjDc7F,EAAE,CAAA6E,eAAA,KAAAvC,GAAA,EAAA0D,OAAA,CAiDW,CAAC;EAAA;AAAA;AAAA,SAAAI,wCAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjDd5C,EAAE,CAAA8C,kBAAA,EA4D4B,CAAC;EAAA;AAAA;AAAA,SAAAuD,yBAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5D/B5C,EAAE,CAAAgD,cAAA,YA2DnE,CAAC;IA3DgEhD,EAAE,CAAAkD,UAAA,IAAAkD,uCAAA,0BA4Da,CAAC;IA5DhBpG,EAAE,CAAAmD,YAAA,CA6D9D,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAA0D,OAAA,GAAAzD,GAAA,CAAAN,SAAA;IAAA,MAAAgE,QAAA,GAAA1D,GAAA,CAAAwC,KAAA;IAAA,MAAAjC,MAAA,GA7D2DpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAAsF,eAAA,IAAAlD,GAAA,EAAAgB,MAAA,CAAAmC,iBAAA,UAAAnC,MAAA,CAAAoD,UAAA,QAAAD,QAAA,EAAAnD,MAAA,CAAAqD,uBAAA,CAAAhB,MAAA,SAAAc,QAAA,CA0D9D,CAAC;IA1D2DvG,EAAE,CAAAsD,SAAA,CA4DjB,CAAC;IA5DctD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAyC,YA4DjB,CAAC,4BA5Dc7F,EAAE,CAAA6E,eAAA,IAAAvC,GAAA,EAAAgE,OAAA,CA4DW,CAAC;EAAA;AAAA;AAAA,SAAAI,8DAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Dd5C,EAAE,CAAA0D,SAAA,0BA0EU,CAAC;EAAA;EAAA,IAAAd,EAAA;IA1Eb5C,EAAE,CAAAuD,UAAA,mCA0EO,CAAC;EAAA;AAAA;AAAA,SAAAoD,6DAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1EV5C,EAAE,CAAA0D,SAAA,yBA2EQ,CAAC;EAAA;EAAA,IAAAd,EAAA;IA3EX5C,EAAE,CAAAuD,UAAA,mCA2EK,CAAC;EAAA;AAAA;AAAA,SAAAqD,2CAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3ER5C,EAAE,CAAA6D,uBAAA,EAyEhC,CAAC;IAzE6B7D,EAAE,CAAAkD,UAAA,IAAAwD,6DAAA,8BA0EU,CAAC,IAAAC,4DAAA,6BACH,CAAC;IA3EX3G,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAQ,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,CA0E9B,CAAC;IA1E2BtD,EAAE,CAAAuD,UAAA,UAAAH,MAAA,CAAAW,UAAA,EA0E9B,CAAC;IA1E2B/D,EAAE,CAAAsD,SAAA,CA2EhC,CAAC;IA3E6BtD,EAAE,CAAAuD,UAAA,SAAAH,MAAA,CAAAW,UAAA,EA2EhC,CAAC;EAAA;AAAA;AAAA,SAAA8C,mDAAAjE,EAAA,EAAAC,GAAA;AAAA,SAAAiE,qCAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3E6B5C,EAAE,CAAAkD,UAAA,IAAA2D,kDAAA,qBA8ElB,CAAC;EAAA;AAAA;AAAA,SAAAE,mCAAAnE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Ee5C,EAAE,CAAAgD,cAAA,cA6EZ,CAAC;IA7EShD,EAAE,CAAAkD,UAAA,IAAA4D,oCAAA,gBA8ElB,CAAC;IA9Ee9G,EAAE,CAAAmD,YAAA,CA+EjE,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GA/E8DpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,CA8EpB,CAAC;IA9EiBtD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAA4D,gBA8EpB,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsE,IAAA,GA9EiBlH,EAAE,CAAAsE,gBAAA;IAAFtE,EAAE,CAAAgD,cAAA,gBAwE3E,CAAC;IAxEwEhD,EAAE,CAAAuE,UAAA,mBAAA4C,oDAAA1C,MAAA;MAAFzE,EAAE,CAAA0E,aAAA,CAAAwC,IAAA;MAAA,MAAA9D,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAA2E,WAAA,CAqE9DvB,MAAA,CAAAgE,UAAA,CAAA3C,MAAiB,CAAC;IAAA,EAAC;IArEyCzE,EAAE,CAAAkD,UAAA,IAAA0D,0CAAA,0BAyEhC,CAAC,IAAAG,kCAAA,kBAImB,CAAC;IA7ES/G,EAAE,CAAAmD,YAAA,CAgFnE,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAhFgEpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAA6E,eAAA,IAAApC,IAAA,EAAAW,MAAA,CAAAiE,oBAAA,GAmEY,CAAC,aAAAjE,MAAA,CAAAiE,oBAAA,EAClD,CAAC;IApEkCrH,EAAE,CAAA+E,WAAA,eAAA3B,MAAA,CAAAkE,mBAAA;IAAFtH,EAAE,CAAAsD,SAAA,CAyElC,CAAC;IAzE+BtD,EAAE,CAAAuD,UAAA,UAAAH,MAAA,CAAA4D,gBAyElC,CAAC;IAzE+BhH,EAAE,CAAAsD,SAAA,CA6E3C,CAAC;IA7EwCtD,EAAE,CAAAuD,UAAA,SAAAH,MAAA,CAAA4D,gBA6E3C,CAAC;EAAA;AAAA;AAAA,SAAAO,6BAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4E,IAAA,GA7EwCxH,EAAE,CAAAsE,gBAAA;IAAFtE,EAAE,CAAAgD,cAAA,WAmF+F,CAAC,gBAUvK,CAAC;IA7FoEhD,EAAE,CAAAuE,UAAA,mBAAAkD,qDAAAhD,MAAA;MAAA,MAAAiD,KAAA,GAAF1H,EAAE,CAAA0E,aAAA,CAAA8C,IAAA,EAAAnC,KAAA;MAAA,MAAAjC,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAA2E,WAAA,CAuF1DvB,MAAA,CAAAuE,UAAA,CAAAlD,MAAA,EAAAiD,KAAoB,CAAC;IAAA,EAAC;IAvFkC1H,EAAE,CAAAmD,YAAA,CA6F9D,CAAC,CACV,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAA8E,KAAA,GAAA7E,GAAA,CAAAwC,KAAA;IAAA,MAAAjC,MAAA,GA9FoEpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAA6E,eAAA,IAAAnC,IAAA,EAAAU,MAAA,CAAAwE,KAAA,KAAAF,KAAA,CAmFyD,CAAC;IAnF5D1H,EAAE,CAAA+E,WAAA;IAAF/E,EAAE,CAAAsD,SAAA,CAwFvC,CAAC;IAxFoCtD,EAAE,CAAA6H,UAAA,CAAAzE,MAAA,CAAA0E,mBAwFvC,CAAC;IAxFoC9H,EAAE,CAAAuD,UAAA,oBAsFhD,CAAC,YAAAH,MAAA,CAAA2E,cAGK,CAAC,aAAA3E,MAAA,CAAAwE,KAAA,KAAAF,KAAA,SAGM,CAAC;IA5FgC1H,EAAE,CAAA+E,WAAA,eAAA3B,MAAA,CAAA4E,aAAA,CAAAN,KAAA,uBAAAtE,MAAA,CAAAwE,KAAA,KAAAF,KAAA,YAAAO,SAAA;EAAA;AAAA;AAAA,SAAAC,wBAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuF,IAAA,GAAFnI,EAAE,CAAAsE,gBAAA;IAAFtE,EAAE,CAAAgD,cAAA,eAkFoH,CAAC;IAlFvHhD,EAAE,CAAAuE,UAAA,qBAAA6D,8CAAA3D,MAAA;MAAFzE,EAAE,CAAA0E,aAAA,CAAAyD,IAAA;MAAA,MAAA/E,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAA2E,WAAA,CAkFyFvB,MAAA,CAAAiF,kBAAA,CAAA5D,MAAyB,CAAC;IAAA,EAAC;IAlFtHzE,EAAE,CAAAkD,UAAA,IAAAqE,4BAAA,gBAmF+F,CAAC;IAnFlGvH,EAAE,CAAAmD,YAAA,CA+F3E,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GA/FwEpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAA6H,UAAA,CAAAzE,MAAA,CAAAkF,sBAkFkB,CAAC;IAlFrBtI,EAAE,CAAAuD,UAAA,2CAkFf,CAAC,YAAAH,MAAA,CAAAmF,sBAAmE,CAAC;IAlFxDvI,EAAE,CAAAsD,SAAA,EAmF9B,CAAC;IAnF2BtD,EAAE,CAAAuD,UAAA,YAAAH,MAAA,CAAAoF,cAAA,EAmF9B,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAA7F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnF2B5C,EAAE,CAAA8C,kBAAA,EAmGhB,CAAC;EAAA;AAAA;AAAA,SAAA4F,yBAAA9F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnGa5C,EAAE,CAAAgD,cAAA,aAiGf,CAAC;IAjGYhD,EAAE,CAAAiD,YAAA,KAkGrC,CAAC;IAlGkCjD,EAAE,CAAAkD,UAAA,IAAAuF,uCAAA,0BAmG/B,CAAC;IAnG4BzI,EAAE,CAAAmD,YAAA,CAoG9E,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GApG2EpD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAAsD,SAAA,EAmGjC,CAAC;IAnG8BtD,EAAE,CAAAuD,UAAA,qBAAAH,MAAA,CAAAuF,cAmGjC,CAAC;EAAA;AAAA;AAzvB/D,MAAMC,QAAQ,CAAC;EACXC,EAAE;EACFC,IAAI;EACJC,EAAE;EACFC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC,MAAM;EACN;AACJ;AACA;AACA;AACA;EACI,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,KAAK;EACrB;EACA,IAAIwB,IAAIA,CAACC,GAAG,EAAE;IACV,IAAI,IAAI,CAACC,SAAS,IAAID,GAAG,KAAK,IAAI,CAACzB,KAAK,EAAE;MACtC,IAAI,IAAI,CAAC2B,gBAAgB,EAAE;QACvB,IAAI,CAACC,YAAY,CAAC,CAAC;MACvB;MACA,IAAIH,GAAG,GAAG,IAAI,CAACzB,KAAK,IAAIyB,GAAG,IAAI,IAAI,CAACI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACjD,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEL,GAAG,CAAC;MACtB,CAAC,MACI,IAAIA,GAAG,GAAG,IAAI,CAACzB,KAAK,EAAE;QACvB,IAAI,CAAC8B,IAAI,CAAC,CAAC,EAAEL,GAAG,CAAC;MACrB;IACJ;IACA,IAAI,CAACzB,KAAK,GAAGyB,GAAG;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI7C,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACmD,WAAW;EAC3B;EACA,IAAInD,UAAUA,CAAC6C,GAAG,EAAE;IAChB,IAAI,CAACM,WAAW,GAAGN,GAAG;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIO,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,WAAW;EAC3B;EACA,IAAIC,SAASA,CAACP,GAAG,EAAE;IACf,IAAI,CAACQ,UAAU,GAAGR,GAAG;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIS,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,WAAW,GAAG,YAAY;EAC1B;AACJ;AACA;AACA;EACIC,sBAAsB,GAAG,OAAO;EAChC;AACJ;AACA;AACA;EACIC,YAAY,GAAG,EAAE;EACjB;AACJ;AACA;AACA;EACI3B,sBAAsB,GAAG,EAAE;EAC3B;AACJ;AACA;AACA;EACIC,sBAAsB;EACtB;AACJ;AACA;AACA;EACIT,mBAAmB,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACI,IAAIvC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0E,MAAM;EACtB;EACA,IAAI1E,KAAKA,CAAC6D,GAAG,EAAE;IACX,IAAI,CAACa,MAAM,GAAGb,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACIc,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;EACId,gBAAgB,GAAG,CAAC;EACpB;AACJ;AACA;AACA;EACIe,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;AACA;EACIC,MAAM,GAAG,IAAIvK,YAAY,CAAC,CAAC;EAC3BwK,cAAc;EACdC,gBAAgB;EAChBC,WAAW;EACXC,WAAW;EACXC,SAAS;EACTlB,WAAW,GAAG,CAAC;EACfE,UAAU,GAAG,CAAC;EACdiB,aAAa,GAAG,CAAC;EACjBC,SAAS,GAAG;IACRnB,SAAS,EAAE,CAAC;IACZpD,UAAU,EAAE,CAAC;IACbhB,KAAK,EAAE;EACX,CAAC;EACDwF,gBAAgB,GAAG,CAAC;EACpBC,iBAAiB,GAAG,CAAC;EACrBrD,KAAK,GAAG,CAAC;EACTsC,MAAM;EACNgB,aAAa;EACbC,EAAE;EACF5F,iBAAiB;EACjB6F,qBAAqB,GAAG,KAAK;EAC7BC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc,GAAG,CAAC;EAClBC,MAAM;EACNC,QAAQ;EACRC,sBAAsB;EACtBhG,sBAAsB;EACtBe,uBAAuB;EACvBkF,aAAa;EACbC,QAAQ;EACRtC,SAAS;EACTuC,cAAc,GAAG,EAAE;EACnBhG,YAAY;EACZrC,cAAc;EACdmF,cAAc;EACdxE,oBAAoB;EACpB6C,gBAAgB;EAChB8E,MAAM;EACNC,WAAWA,CAAClD,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAE;IAC9D,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC5D,iBAAiB,GAAG,IAAI,CAAC6D,IAAI,GAAG,IAAI,CAACQ,SAAS,GAAG,CAAC,CAAC;IACxD,IAAI,CAACkC,MAAM,GAAG,IAAI,CAAC7C,QAAQ,CAAC+C,WAAW;EAC3C;EACAC,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAIrM,iBAAiB,CAAC,IAAI,CAACqJ,UAAU,CAAC,EAAE;MACpC,IAAIgD,YAAY,CAAC1G,KAAK,EAAE;QACpB,IAAI,IAAI,CAAC2E,QAAQ,IAAI,IAAI,CAACD,MAAM,EAAE;UAC9B,IAAI,CAACiC,aAAa,CAAC,CAAC;QACxB;MACJ;MACA,IAAI,IAAI,CAAC7C,SAAS,EAAE;QAChB,IAAI4C,YAAY,CAAC1F,UAAU,EAAE;UACzB,IAAI,IAAI,CAACsD,iBAAiB,EAAE;YACxB,IAAI,CAACmB,iBAAiB,GAAG,IAAI,CAACzE,UAAU;UAC5C;UACA,IAAI,IAAI,CAAC4F,UAAU,CAAC,CAAC,EAAE;YACnB,IAAI,CAACD,aAAa,CAAC,CAAC;UACxB;UACA,IAAI,CAACE,WAAW,CAAC,CAAC;UAClB,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5B;QACA,IAAIJ,YAAY,CAACtC,SAAS,EAAE;UACxB,IAAI,IAAI,CAACE,iBAAiB,EAAE;YACxB,IAAI,CAACkB,gBAAgB,GAAG,IAAI,CAACpB,SAAS;UAC1C;QACJ;MACJ;IACJ;IACA,IAAI,CAACb,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrB,EAAE,GAAG3J,iBAAiB,CAAC,CAAC;IAC7B,IAAI3B,iBAAiB,CAAC,IAAI,CAACqJ,UAAU,CAAC,EAAE;MACpC,IAAI,CAACyC,aAAa,GAAG,CAAC,CAAC,IAAI,CAACpC,gBAAgB;MAC5C,IAAI,IAAI,CAACY,QAAQ,EAAE;QACf,IAAI,CAACgC,aAAa,CAAC,CAAC;MACxB;MACA,IAAI,IAAI,CAACrC,iBAAiB,EAAE;QACxB,IAAI,CAACkB,gBAAgB,GAAG,IAAI,CAACnB,UAAU;QACvC,IAAI,CAACoB,iBAAiB,GAAG,IAAI,CAACtB,WAAW;MAC7C;MACA,IAAI,CAAC0C,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACxB,IAAI,IAAI,CAACxC,iBAAiB,EAAE;QACxB,IAAI,CAAC2C,qBAAqB,CAAC,CAAC;MAChC;IACJ;IACA,IAAI,CAAC5B,SAAS,EAAE6B,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,MAAM;UACP,IAAI,CAAC/G,YAAY,GAAG8G,IAAI,CAACE,QAAQ;UACjC;QACJ,KAAK,QAAQ;UACT,IAAI,CAACrJ,cAAc,GAAGmJ,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,QAAQ;UACT,IAAI,CAAClE,cAAc,GAAGgE,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,cAAc;UACf,IAAI,CAAC1I,oBAAoB,GAAGwI,IAAI,CAACE,QAAQ;UACzC;QACJ,KAAK,UAAU;UACX,IAAI,CAAC7F,gBAAgB,GAAG2F,IAAI,CAACE,QAAQ;UACrC;QACJ;UACI,IAAI,CAAChH,YAAY,GAAG8G,IAAI,CAACE,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;IACF,IAAI,CAAC9D,EAAE,CAAC+D,aAAa,CAAC,CAAC;EAC3B;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAIlN,iBAAiB,CAAC,IAAI,CAACqJ,UAAU,CAAC,EAAE;MACpC,MAAMkD,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;MACpC,IAAI7G,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC9C,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACiF,cAAc,KAAK,IAAI,CAACM,SAAS,CAACnB,SAAS,KAAK,IAAI,CAACC,UAAU,IAAI,IAAI,CAACkB,SAAS,CAACvE,UAAU,KAAK,IAAI,CAACmD,WAAW,IAAI,IAAI,CAACoB,SAAS,CAACvF,KAAK,CAACC,MAAM,KAAK,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC,EAAE;QAC5L,IAAI,IAAI,CAAC8D,gBAAgB,EAAE;UACvB,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC;QAC5B;QACA,IAAI,CAAC+B,cAAc,GAAG,CAAC,IAAI,CAAC/F,KAAK,CAACC,MAAM,GAAG,IAAI,CAACkE,WAAW,IAAI,IAAI,CAACE,UAAU;QAC9E,IAAIT,IAAI,GAAG,IAAI,CAACxB,KAAK;QACrB,IAAI,IAAI,CAAC6B,SAAS,CAAC,CAAC,KAAK,CAAC,IAAIL,IAAI,IAAI,IAAI,CAACK,SAAS,CAAC,CAAC,EAAE;UACpDL,IAAI,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC;UAC3B,IAAI,CAAC7B,KAAK,GAAGwB,IAAI;UACjB,IAAI,CAACoB,MAAM,CAACwC,IAAI,CAAC;YACb5D,IAAI,EAAE,IAAI,CAACA;UACf,CAAC,CAAC;QACN;QACA7D,iBAAiB,GAAG6D,IAAI,GAAG,IAAI,CAACS,UAAU,GAAG,CAAC,CAAC;QAC/C,IAAIuC,UAAU,EAAE;UACZ7G,iBAAiB,IAAI,IAAI,CAACoE,WAAW;QACzC;QACA,IAAIP,IAAI,KAAK,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC8B,cAAc,GAAG,CAAC,EAAE;UAC1DhG,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAI,CAACgG,cAAc,GAAG,IAAI,CAAC1B,UAAU;UAC/D,IAAI,CAACuB,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACD,IAAI,CAACA,qBAAqB,GAAG,KAAK;QACtC;QACA,IAAI7F,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,EAAE;UAC9C,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;QAC9C;QACA,IAAI,CAACuF,aAAa,GAAG,IAAI,CAACjB,UAAU;QACpC,IAAI,CAACkB,SAAS,CAACnB,SAAS,GAAG,IAAI,CAACC,UAAU;QAC1C,IAAI,CAACkB,SAAS,CAACvE,UAAU,GAAG,IAAI,CAACmD,WAAW;QAC5C,IAAI,CAACoB,SAAS,CAACvF,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC0E,MAAM,CAAC;QACvC,IAAI,IAAI,CAACT,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,cAAc,CAACwC,aAAa,EAAE;UAC3D,IAAI,CAACxC,cAAc,CAACwC,aAAa,CAAC3C,KAAK,CAAC4C,SAAS,GAAG,IAAI,CAACnJ,UAAU,CAAC,CAAC,GAAI,kBAAiBwB,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,OAAM,GAAI,eAAcpE,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,UAAS;QACzN;QACA,IAAI,CAACL,SAAS,GAAG,IAAI;QACrB,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAAC4D,UAAU,CAAC,CAAC,EAAE;UAC5C,IAAI,CAACC,aAAa,CAAC,CAAC;QACxB;MACJ;MACA,IAAIhB,UAAU,EAAE;QACZ,IAAI,IAAI,CAAChD,IAAI,KAAK,CAAC,EAAE;UACjB7D,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACoE,WAAW;QAC7C,CAAC,MACI,IAAIpE,iBAAiB,KAAK,CAAC,EAAE;UAC9BA,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;UAC1C,IAAI,IAAI,CAAC8F,cAAc,GAAG,CAAC,EAAE;YACzB,IAAI,CAACH,qBAAqB,GAAG,IAAI;UACrC;QACJ;QACA,IAAI7F,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,EAAE;UAC9C,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;QAC9C;MACJ;IACJ;EACJ;EACA8G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACnB,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAI,CAAClC,QAAQ,CAACqE,aAAa,CAAC,OAAO,CAAC;MACzD,IAAI,CAACnC,aAAa,CAACoC,IAAI,GAAG,UAAU;MACpC,IAAI,CAACtE,QAAQ,CAACuE,WAAW,CAAC,IAAI,CAACtE,QAAQ,CAACuE,IAAI,EAAE,IAAI,CAACtC,aAAa,CAAC;IACrE;IACA,IAAIuC,SAAS,GAAI;AACzB,eAAe,IAAI,CAACtC,EAAG;AACvB,gBAAgB,GAAG,GAAG,IAAI,CAAC3E,UAAW;AACtC;AACA,SAAS;IACD,IAAI,IAAI,CAACsD,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC4D,IAAI,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC1C,MAAMC,MAAM,GAAGF,KAAK,CAACG,UAAU;QAC/B,MAAMC,MAAM,GAAGH,KAAK,CAACE,UAAU;QAC/B,IAAIE,MAAM,GAAG,IAAI;QACjB,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAChCC,MAAM,GAAG,CAAC,CAAC,CAAC,KACX,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EACrCC,MAAM,GAAG,CAAC,CAAC,KACV,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EACrCC,MAAM,GAAG,CAAC,CAAC,KACV,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC7DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,EAAE9F,SAAS,EAAE;UAAEiG,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC,KAEpEF,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;QAC3D,OAAO,CAAC,CAAC,GAAGC,MAAM;MACtB,CAAC,CAAC;MACF,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrE,iBAAiB,CAACrE,MAAM,EAAE0I,CAAC,EAAE,EAAE;QACpD,IAAIC,GAAG,GAAG,IAAI,CAACtE,iBAAiB,CAACqE,CAAC,CAAC;QACnCV,SAAS,IAAK;AAC9B,oDAAoDW,GAAG,CAACN,UAAW;AACnE,2BAA2B,IAAI,CAAC3C,EAAG;AACnC,wCAAwC,GAAG,GAAGiD,GAAG,CAAC5H,UAAW;AAC7D;AACA;AACA,iBAAiB;MACL;IACJ;IACA,IAAI,CAAC0E,aAAa,CAACuC,SAAS,GAAGA,SAAS;EAC5C;EACAnB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACxC,iBAAiB,EAAE;MACxB,IAAIuE,qBAAqB,GAAG;QACxB7H,UAAU,EAAE,IAAI,CAACyE,iBAAiB;QAClCrB,SAAS,EAAE,IAAI,CAACoB;MACpB,CAAC;MACD,IAAI,OAAOc,MAAM,KAAK,WAAW,EAAE;QAC/B,IAAIwC,WAAW,GAAGxC,MAAM,CAACyC,UAAU;QACnC,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrE,iBAAiB,CAACrE,MAAM,EAAE0I,CAAC,EAAE,EAAE;UACpD,IAAIC,GAAG,GAAG,IAAI,CAACtE,iBAAiB,CAACqE,CAAC,CAAC;UACnC,IAAIK,QAAQ,CAACJ,GAAG,CAACN,UAAU,EAAE,EAAE,CAAC,IAAIQ,WAAW,EAAE;YAC7CD,qBAAqB,GAAGD,GAAG;UAC/B;QACJ;MACJ;MACA,IAAI,IAAI,CAACvE,UAAU,KAAKwE,qBAAqB,CAACzE,SAAS,EAAE;QACrD,IAAIR,IAAI,GAAG,IAAI,CAACxB,KAAK;QACrBwB,IAAI,GAAGqF,IAAI,CAACC,KAAK,CAAEtF,IAAI,GAAG,IAAI,CAACS,UAAU,GAAIwE,qBAAqB,CAACzE,SAAS,CAAC;QAC7E,IAAIrE,iBAAiB,GAAG8I,qBAAqB,CAACzE,SAAS,GAAG,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;QACxE,IAAI,IAAI,CAACgD,UAAU,CAAC,CAAC,EAAE;UACnB7G,iBAAiB,IAAI8I,qBAAqB,CAAC7H,UAAU;QACzD;QACA,IAAI,CAACjB,iBAAiB,GAAGA,iBAAiB;QAC1C,IAAI,CAACsE,UAAU,GAAGwE,qBAAqB,CAACzE,SAAS;QACjD,IAAI,CAAChC,KAAK,GAAGwB,IAAI;QACjB,IAAI,CAACoB,MAAM,CAACwC,IAAI,CAAC;UACb5D,IAAI,EAAE,IAAI,CAACA;QACf,CAAC,CAAC;MACN;MACA,IAAI,IAAI,CAACO,WAAW,KAAK0E,qBAAqB,CAAC7H,UAAU,EAAE;QACvD,IAAI,CAACmD,WAAW,GAAG0E,qBAAqB,CAAC7H,UAAU;QACnD,IAAI,CAAC2F,aAAa,CAAC,CAAC;MACxB;MACA,IAAI,CAACpD,EAAE,CAACwD,YAAY,CAAC,CAAC;IAC1B;EACJ;EACAJ,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACzG,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACe,uBAAuB,GAAG,EAAE;IACjC,IAAI,IAAI,CAAC2F,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAAC1G,sBAAsB,CAACiJ,IAAI,CAAC,GAAG,IAAI,CAACnJ,KAAK,CAACoJ,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACjF,WAAW,CAAC,CAAC;MAC5E,IAAI,CAAClD,uBAAuB,CAACkI,IAAI,CAAC,GAAG,IAAI,CAACnJ,KAAK,CAACoJ,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjF,WAAW,CAAC,CAAC;IAC/E;EACJ;EACAzD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACkG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC7G,iBAAiB,GAAG,IAAI,CAACiB,UAAU,CAAC,GAAG,IAAI,CAACjB,iBAAiB,GAAG,CAAC,CAAC;EAC5G;EACAY,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,UAAU,CAAC,CAAC,GAAG,IAAI,CAACM,UAAU,GAAG,CAAC;EAClD;EACAiD,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjE,KAAK,EAAEC,MAAM,GAAGgJ,IAAI,CAACI,IAAI,CAAC,CAAC,IAAI,CAACrJ,KAAK,CAACC,MAAM,GAAG,IAAI,CAACkE,WAAW,IAAI,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;EAC3G;EACArB,cAAcA,CAAA,EAAG;IACb,MAAMiB,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;IAClC,OAAOA,SAAS,IAAI,CAAC,GAAG,EAAE,GAAGqF,KAAK,CAACrF,SAAS,CAAC,CAACsF,IAAI,CAAC,CAAC,CAAC;EACzD;EACAhL,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACgG,WAAW,KAAK,UAAU;EAC1C;EACAqC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjC,QAAQ,IAAI,IAAI,CAAC3E,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,IAAI,IAAI,CAACe,UAAU;EAC9E;EACA2G,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC5D,gBAAgB,IAAI,IAAI,CAACoC,aAAa;EACtD;EACAtE,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC2H,OAAO,CAAC,CAAC,IAAK,IAAI,CAACpH,KAAK,IAAI,IAAI,CAAC6B,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC2C,UAAU,CAAC,CAAE;EACvF;EACAtH,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACkK,OAAO,CAAC,CAAC,IAAK,IAAI,CAACpH,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAACwE,UAAU,CAAC,CAAE;EACpE;EACA4C,OAAOA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAACxJ,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,KAAK,CAAC;EACjD;EACA2B,UAAUA,CAAC6H,CAAC,EAAE5J,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC+G,UAAU,CAAC,CAAC,IAAI,IAAI,CAACxE,KAAK,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;MACxD,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAErE,KAAK,CAAC;IACxB;IACA,IAAI,IAAI,CAACkE,gBAAgB,EAAE;MACvB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;IACA,IAAIyF,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAvK,WAAWA,CAACqK,CAAC,EAAE5J,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC+G,UAAU,CAAC,CAAC,IAAI,IAAI,CAACxE,KAAK,KAAK,CAAC,EAAE;MACvC,IAAI,CAAC8B,IAAI,CAAC,CAAC,EAAErE,KAAK,CAAC;IACvB;IACA,IAAI,IAAI,CAACkE,gBAAgB,EAAE;MACvB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;IACA,IAAIyF,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAxH,UAAUA,CAACsH,CAAC,EAAE5J,KAAK,EAAE;IACjB,IAAI+D,IAAI,GAAG,IAAI,CAACxB,KAAK;IACrB,IAAI,IAAI,CAAC2B,gBAAgB,EAAE;MACvB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;IACA,IAAInE,KAAK,GAAG+D,IAAI,EAAE;MACd,IAAI,CAAChC,UAAU,CAAC6H,CAAC,EAAE5J,KAAK,CAAC;IAC7B,CAAC,MACI,IAAIA,KAAK,GAAG+D,IAAI,EAAE;MACnB,IAAI,CAACxE,WAAW,CAACqK,CAAC,EAAE5J,KAAK,CAAC;IAC9B;EACJ;EACAgD,kBAAkBA,CAAC+G,KAAK,EAAE;IACtB,QAAQA,KAAK,CAACC,IAAI;MACd,KAAK,YAAY;QACb,IAAI,CAACC,UAAU,CAAC,CAAC;QACjB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,SAAS,CAAC,CAAC;QAChB;IACR;EACJ;EACAD,UAAUA,CAAA,EAAG;IACT,MAAME,UAAU,GAAG,CAAC,GAAG/N,UAAU,CAACgO,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAACuC,aAAa,EAAE,+BAA+B,CAAC,CAAC;IAC7G,MAAMyC,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAACF,WAAW,EAAEA,WAAW,GAAG,CAAC,KAAKF,UAAU,CAAC/J,MAAM,GAAG+J,UAAU,CAAC/J,MAAM,GAAG,CAAC,GAAGiK,WAAW,GAAG,CAAC,CAAC;EAC9H;EACAH,SAASA,CAAA,EAAG;IACR,MAAMG,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAACF,WAAW,EAAEA,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;EACzF;EACAG,SAASA,CAAA,EAAG;IACR,MAAMH,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAACF,WAAW,EAAE,CAAC,CAAC;EAChD;EACAI,QAAQA,CAAA,EAAG;IACP,MAAMN,UAAU,GAAG,CAAC,GAAG/N,UAAU,CAACgO,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAACuC,aAAa,EAAE,gCAAgC,CAAC,CAAC;IAC9G,MAAMyC,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAACF,WAAW,EAAEF,UAAU,CAAC/J,MAAM,GAAG,CAAC,CAAC;EACpE;EACAsK,QAAQA,CAAA,EAAG;IACP,MAAMP,UAAU,GAAG,CAAC,GAAG/N,UAAU,CAACgO,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAACuC,aAAa,EAAE,+BAA+B,CAAC,CAAC;IAC7G,MAAM+C,gBAAgB,GAAGR,UAAU,CAACS,SAAS,CAAEC,GAAG,IAAKzO,UAAU,CAAC0O,YAAY,CAACD,GAAG,EAAE,kBAAkB,CAAC,KAAK,IAAI,CAAC;IACjH,MAAME,eAAe,GAAG3O,UAAU,CAAC4O,UAAU,CAAC,IAAI,CAAC3F,gBAAgB,CAACuC,aAAa,EAAE,sDAAsD,CAAC;IAC1I,MAAMyC,WAAW,GAAGF,UAAU,CAACS,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKE,eAAe,CAACE,aAAa,CAAC;IACxFd,UAAU,CAACE,WAAW,CAAC,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,IAAI;IACnDhB,UAAU,CAACQ,gBAAgB,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,GAAG;EAC3D;EACAb,yBAAyBA,CAAA,EAAG;IACxB,MAAMH,UAAU,GAAG,CAAC,GAAG/N,UAAU,CAACgO,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAACuC,aAAa,EAAE,+BAA+B,CAAC,CAAC;IAC7G,MAAMmD,eAAe,GAAG3O,UAAU,CAAC4O,UAAU,CAAC,IAAI,CAAC3F,gBAAgB,CAACuC,aAAa,EAAE,sDAAsD,CAAC;IAC1I,OAAOuC,UAAU,CAACS,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKE,eAAe,CAACE,aAAa,CAAC;EAC/E;EACAV,uBAAuBA,CAACa,OAAO,EAAEC,OAAO,EAAE;IACtC,MAAMlB,UAAU,GAAG,CAAC,GAAG/N,UAAU,CAACgO,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,CAACuC,aAAa,EAAE,+BAA+B,CAAC,CAAC;IAC7GuC,UAAU,CAACiB,OAAO,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,IAAI;IAC/ChB,UAAU,CAACkB,OAAO,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,GAAG;IAC9ChB,UAAU,CAACkB,OAAO,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC;EAC3C;EACAjH,IAAIA,CAACkH,GAAG,EAAExH,IAAI,EAAE;IACZ,IAAI7D,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAC9C,MAAM6G,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IACpC,IAAIhD,IAAI,IAAI,IAAI,EAAE;MACd7D,iBAAiB,GAAG,IAAI,CAACsE,UAAU,GAAGT,IAAI,GAAG,CAAC,CAAC;MAC/C,IAAIgD,UAAU,EAAE;QACZ7G,iBAAiB,IAAI,IAAI,CAACoE,WAAW;MACzC;MACA,IAAI,CAACyB,qBAAqB,GAAG,KAAK;IACtC,CAAC,MACI;MACD7F,iBAAiB,IAAI,IAAI,CAACsE,UAAU,GAAG+G,GAAG;MAC1C,IAAI,IAAI,CAACxF,qBAAqB,EAAE;QAC5B7F,iBAAiB,IAAI,IAAI,CAACgG,cAAc,GAAG,IAAI,CAAC1B,UAAU,GAAG+G,GAAG;QAChE,IAAI,CAACxF,qBAAqB,GAAG,KAAK;MACtC;MACA,IAAIyF,oBAAoB,GAAGzE,UAAU,GAAG7G,iBAAiB,GAAG,IAAI,CAACoE,WAAW,GAAGpE,iBAAiB;MAChG6D,IAAI,GAAGqF,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACC,KAAK,CAACmC,oBAAoB,GAAG,IAAI,CAAChH,UAAU,CAAC,CAAC;IACvE;IACA,IAAIuC,UAAU,IAAI,IAAI,CAAChD,IAAI,KAAK,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,IAAImH,GAAG,KAAK,CAAC,CAAC,EAAE;MAChErL,iBAAiB,GAAG,CAAC,CAAC,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,IAAI,CAACkE,WAAW,CAAC;MAC/DP,IAAI,GAAG,CAAC;IACZ,CAAC,MACI,IAAIgD,UAAU,IAAI,IAAI,CAAChD,IAAI,KAAK,CAAC,IAAIwH,GAAG,KAAK,CAAC,EAAE;MACjDrL,iBAAiB,GAAG,CAAC;MACrB6D,IAAI,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI,IAAIL,IAAI,KAAK,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC8B,cAAc,GAAG,CAAC,EAAE;MAC/DhG,iBAAiB,IAAI,IAAI,CAACgG,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC1B,UAAU,GAAG+G,GAAG;MACrE,IAAI,CAACxF,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAI,IAAI,CAACX,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACwC,aAAa,CAAC3C,KAAK,CAAC4C,SAAS,GAAG,IAAI,CAACnJ,UAAU,CAAC,CAAC,GAAI,kBAAiBwB,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,OAAM,GAAI,eAAcpE,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,UAAS;MACrN,IAAI,CAACc,cAAc,CAACwC,aAAa,CAAC3C,KAAK,CAACyG,UAAU,GAAG,yBAAyB;IAClF;IACA,IAAI,CAACxL,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACqC,KAAK,GAAGwB,IAAI;IACjB,IAAI,CAACoB,MAAM,CAACwC,IAAI,CAAC;MACb5D,IAAI,EAAE,IAAI,CAACA;IACf,CAAC,CAAC;IACF,IAAI,CAACL,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACAa,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACxB,QAAQ,GAAGoF,WAAW,CAAC,MAAM;MAC9B,IAAI,IAAI,CAACvH,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB,IAAI,IAAI,CAACL,IAAI,KAAK,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;UACpC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,MACI;UACD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC;QAChC;MACJ;IACJ,CAAC,EAAE,IAAI,CAACG,gBAAgB,CAAC;IACzB,IAAI,CAACoC,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC5C,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACA/C,YAAYA,CAACyH,WAAW,GAAG,IAAI,EAAE;IAC7B,IAAI,IAAI,CAACrF,QAAQ,EAAE;MACfsF,aAAa,CAAC,IAAI,CAACtF,QAAQ,CAAC;MAC5B,IAAI,CAACA,QAAQ,GAAG3D,SAAS;MACzB,IAAIgJ,WAAW,EAAE;QACb,IAAI,CAACtF,aAAa,GAAG,KAAK;MAC9B;IACJ;IACA,IAAI,CAAC5C,EAAE,CAACwD,YAAY,CAAC,CAAC;EAC1B;EACA4E,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,CAAC,IAAI,CAACvF,QAAQ;EAC1B;EACAwF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC3G,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACwC,aAAa,CAAC3C,KAAK,CAACyG,UAAU,GAAG,EAAE;MACvD,IAAI,CAAC,IAAI,CAAC3H,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC2C,UAAU,CAAC,CAAC,EAAE;QAC9E,IAAI,CAAC3B,cAAc,CAACwC,aAAa,CAAC3C,KAAK,CAAC4C,SAAS,GAAG,IAAI,CAACnJ,UAAU,CAAC,CAAC,GAAI,kBAAiB,IAAI,CAACwB,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,OAAM,GAAI,eAAc,IAAI,CAACpE,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACoE,WAAW,CAAE,UAAS;MACnO;IACJ;EACJ;EACA0H,YAAYA,CAACpC,CAAC,EAAE;IACZ,IAAIqC,QAAQ,GAAGrC,CAAC,CAACsC,cAAc,CAAC,CAAC,CAAC;IAClC,IAAI,CAAC9F,QAAQ,GAAG;MACZ+F,CAAC,EAAEF,QAAQ,CAACG,KAAK;MACjBC,CAAC,EAAEJ,QAAQ,CAACK;IAChB,CAAC;EACL;EACAC,WAAWA,CAAC3C,CAAC,EAAE;IACX,IAAIA,CAAC,CAACC,UAAU,EAAE;MACdD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACA0C,UAAUA,CAAC5C,CAAC,EAAE;IACV,IAAIqC,QAAQ,GAAGrC,CAAC,CAACsC,cAAc,CAAC,CAAC,CAAC;IAClC,IAAI,IAAI,CAACxN,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAAC+N,iBAAiB,CAAC7C,CAAC,EAAEqC,QAAQ,CAACK,KAAK,GAAG,IAAI,CAAClG,QAAQ,CAACiG,CAAC,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACI,iBAAiB,CAAC7C,CAAC,EAAEqC,QAAQ,CAACG,KAAK,GAAG,IAAI,CAAChG,QAAQ,CAAC+F,CAAC,CAAC;IAC/D;EACJ;EACAM,iBAAiBA,CAAC7C,CAAC,EAAE8C,IAAI,EAAE;IACvB,IAAItD,IAAI,CAACqC,GAAG,CAACiB,IAAI,CAAC,GAAG,IAAI,CAAClG,cAAc,EAAE;MACtC,IAAIkG,IAAI,GAAG,CAAC,EAAE;QACV,IAAI,CAAC3K,UAAU,CAAC6H,CAAC,CAAC;MACtB,CAAC,MACI;QACD,IAAI,CAACrK,WAAW,CAACqK,CAAC,CAAC;MACvB;IACJ;EACJ;EACAjK,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACmE,MAAM,CAAC6I,WAAW,CAACC,IAAI,GAAG,IAAI,CAAC9I,MAAM,CAAC6I,WAAW,CAACC,IAAI,CAACC,aAAa,GAAGjK,SAAS;EAChG;EACArC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACuD,MAAM,CAAC6I,WAAW,CAACC,IAAI,GAAG,IAAI,CAAC9I,MAAM,CAAC6I,WAAW,CAACC,IAAI,CAACE,KAAK,GAAGlK,SAAS;EACxF;EACAX,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC6B,MAAM,CAAC6I,WAAW,CAACC,IAAI,GAAG,IAAI,CAAC9I,MAAM,CAAC6I,WAAW,CAACC,IAAI,CAACG,aAAa,GAAGnK,SAAS;EAChG;EACAtC,eAAeA,CAACH,KAAK,EAAE;IACnB,OAAO,IAAI,CAAC2D,MAAM,CAAC6I,WAAW,CAACC,IAAI,GAAG,IAAI,CAAC9I,MAAM,CAAC6I,WAAW,CAACC,IAAI,CAACI,WAAW,CAACC,OAAO,CAAC,gBAAgB,EAAE9M,KAAK,CAAC,GAAGyC,SAAS;EAC/H;EACAD,aAAaA,CAACxC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC2D,MAAM,CAAC6I,WAAW,CAACC,IAAI,GAAG,IAAI,CAAC9I,MAAM,CAAC6I,WAAW,CAACC,IAAI,CAACM,SAAS,CAACD,OAAO,CAAC,SAAS,EAAE9M,KAAK,CAAC,GAAGyC,SAAS;EACtH;EACAwE,qBAAqBA,CAAA,EAAG;IACpB,IAAI5M,iBAAiB,CAAC,IAAI,CAACqJ,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACwC,sBAAsB,EAAE;QAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC1C,QAAQ,CAACwJ,MAAM,CAAC,IAAI,CAAC1G,MAAM,EAAE,QAAQ,EAAGsD,KAAK,IAAK;UACjF,IAAI,CAAC9C,iBAAiB,CAAC,CAAC;QAC5B,CAAC,CAAC;MACN;IACJ;EACJ;EACAmG,uBAAuBA,CAAA,EAAG;IACtB,IAAI5S,iBAAiB,CAAC,IAAI,CAACqJ,UAAU,CAAC,EAAE;MACpC,IAAI,IAAI,CAACwC,sBAAsB,EAAE;QAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;QAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACtC;IACJ;EACJ;EACAgH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5I,iBAAiB,EAAE;MACxB,IAAI,CAAC2I,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAAClJ,gBAAgB,EAAE;MACvB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;EACJ;EACA,OAAOmJ,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjK,QAAQ,EAAlB5I,EAAE,CAAA8S,iBAAA,CAAkC9S,EAAE,CAAC+S,UAAU,GAAjD/S,EAAE,CAAA8S,iBAAA,CAA4D9S,EAAE,CAACgT,MAAM,GAAvEhT,EAAE,CAAA8S,iBAAA,CAAkF9S,EAAE,CAACiT,iBAAiB,GAAxGjT,EAAE,CAAA8S,iBAAA,CAAmH9S,EAAE,CAACkT,SAAS,GAAjIlT,EAAE,CAAA8S,iBAAA,CAA4IhT,QAAQ,GAAtJE,EAAE,CAAA8S,iBAAA,CAAiK5S,WAAW,GAA9KF,EAAE,CAAA8S,iBAAA,CAAyLjS,EAAE,CAACsS,aAAa;EAAA;EACpS,OAAOC,IAAI,kBAD8EpT,EAAE,CAAAqT,iBAAA;IAAA/F,IAAA,EACJ1E,QAAQ;IAAA0K,SAAA;IAAAC,cAAA,WAAAC,wBAAA5Q,EAAA,EAAAC,GAAA,EAAA4Q,QAAA;MAAA,IAAA7Q,EAAA;QADN5C,EAAE,CAAA0T,cAAA,CAAAD,QAAA,EACivB3S,MAAM;QADzvBd,EAAE,CAAA0T,cAAA,CAAAD,QAAA,EACq0B1S,MAAM;QAD70Bf,EAAE,CAAA0T,cAAA,CAAAD,QAAA,EAC04BzS,aAAa;MAAA;MAAA,IAAA4B,EAAA;QAAA,IAAA+Q,EAAA;QADz5B3T,EAAE,CAAA4T,cAAA,CAAAD,EAAA,GAAF3T,EAAE,CAAA6T,WAAA,QAAAhR,GAAA,CAAA8H,WAAA,GAAAgJ,EAAA,CAAAG,KAAA;QAAF9T,EAAE,CAAA4T,cAAA,CAAAD,EAAA,GAAF3T,EAAE,CAAA6T,WAAA,QAAAhR,GAAA,CAAA+H,WAAA,GAAA+I,EAAA,CAAAG,KAAA;QAAF9T,EAAE,CAAA4T,cAAA,CAAAD,EAAA,GAAF3T,EAAE,CAAA6T,WAAA,QAAAhR,GAAA,CAAAgI,SAAA,GAAA8I,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAC,eAAApR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5C,EAAE,CAAAiU,WAAA,CAAAvS,GAAA;QAAF1B,EAAE,CAAAiU,WAAA,CAAAtS,GAAA;MAAA;MAAA,IAAAiB,EAAA;QAAA,IAAA+Q,EAAA;QAAF3T,EAAE,CAAA4T,cAAA,CAAAD,EAAA,GAAF3T,EAAE,CAAA6T,WAAA,QAAAhR,GAAA,CAAA4H,cAAA,GAAAkJ,EAAA,CAAAG,KAAA;QAAF9T,EAAE,CAAA4T,cAAA,CAAAD,EAAA,GAAF3T,EAAE,CAAA6T,WAAA,QAAAhR,GAAA,CAAA6H,gBAAA,GAAAiJ,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA;IAAAC,MAAA;MAAA/K,IAAA;MAAA5C,UAAA;MAAAoD,SAAA;MAAAE,iBAAA;MAAAC,WAAA;MAAAC,sBAAA;MAAAC,YAAA;MAAA3B,sBAAA;MAAAC,sBAAA;MAAAT,mBAAA;MAAAC,cAAA;MAAAvC,KAAA;MAAA2E,QAAA;MAAAC,cAAA;MAAAC,cAAA;MAAAd,gBAAA;MAAAe,KAAA;MAAAC,UAAA;IAAA;IAAA6J,OAAA;MAAA5J,MAAA;IAAA;IAAA6J,QAAA,GAAFrU,EAAE,CAAAsU,oBAAA;IAAAC,kBAAA,EAAA1S,GAAA;IAAA2S,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA7H,QAAA,WAAA8H,kBAAA/R,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAgS,GAAA,GAAF5U,EAAE,CAAAsE,gBAAA;QAAFtE,EAAE,CAAA6U,eAAA,CAAAjT,GAAA;QAAF5B,EAAE,CAAAgD,cAAA,YAE6G,CAAC;QAFhHhD,EAAE,CAAAkD,UAAA,IAAAH,uBAAA,gBAGf,CAAC;QAHY/C,EAAE,CAAAgD,cAAA,YAOvB,CAAC,YAC4B,CAAC;QARThD,EAAE,CAAAkD,UAAA,IAAAkB,0BAAA,mBAiB3E,CAAC;QAjBwEpE,EAAE,CAAAgD,cAAA,YA0BiC,CAAC,eACwF,CAAC;QA3B7HhD,EAAE,CAAAuE,UAAA,2BAAAuQ,+CAAA;UAAF9U,EAAE,CAAA0E,aAAA,CAAAkQ,GAAA;UAAA,OAAF5U,EAAE,CAAA2E,WAAA,CA2BE9B,GAAA,CAAAuO,eAAA,CAAgB,CAAC;QAAA,EAAC,sBAAA2D,0CAAAtQ,MAAA;UA3BtBzE,EAAE,CAAA0E,aAAA,CAAAkQ,GAAA;UAAA,OAAF5U,EAAE,CAAA2E,WAAA,CA2BiC9B,GAAA,CAAAgP,UAAA,CAAApN,MAAiB,CAAC;QAAA,EAAC,wBAAAuQ,4CAAAvQ,MAAA;UA3BtDzE,EAAE,CAAA0E,aAAA,CAAAkQ,GAAA;UAAA,OAAF5U,EAAE,CAAA2E,WAAA,CA2BmE9B,GAAA,CAAAwO,YAAA,CAAA5M,MAAmB,CAAC;QAAA,EAAC,uBAAAwQ,2CAAAxQ,MAAA;UA3B1FzE,EAAE,CAAA0E,aAAA,CAAAkQ,GAAA;UAAA,OAAF5U,EAAE,CAAA2E,WAAA,CA2BsG9B,GAAA,CAAA+O,WAAA,CAAAnN,MAAkB,CAAC;QAAA,EAAC;QA3B5HzE,EAAE,CAAAkD,UAAA,IAAAgC,uBAAA,iBAuCnE,CAAC,IAAAa,uBAAA,iBASD,CAAC,KAAAM,wBAAA,gBAWD,CAAC;QA3DgErG,EAAE,CAAAmD,YAAA,CA8DlE,CAAC,CACL,CAAC;QA/DmEnD,EAAE,CAAAkD,UAAA,KAAA+D,2BAAA,mBAwE3E,CAAC;QAxEwEjH,EAAE,CAAAmD,YAAA,CAiF1E,CAAC;QAjFuEnD,EAAE,CAAAkD,UAAA,KAAAgF,uBAAA,gBAkFoH,CAAC;QAlFvHlI,EAAE,CAAAmD,YAAA,CAgG9E,CAAC;QAhG2EnD,EAAE,CAAAkD,UAAA,KAAAwF,wBAAA,iBAiGf,CAAC;QAjGY1I,EAAE,CAAAmD,YAAA,CAqGlF,CAAC;MAAA;MAAA,IAAAP,EAAA;QArG+E5C,EAAE,CAAA6H,UAAA,CAAAhF,GAAA,CAAA0H,UAE8F,CAAC;QAFjGvK,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAAkV,eAAA,KAAApT,GAAA,EAAAe,GAAA,CAAAkB,UAAA,KAAAlB,GAAA,CAAAkB,UAAA,GAEuD,CAAC,YAAAlB,GAAA,CAAAyH,KAAiB,CAAC;QAF5EtK,EAAE,CAAA+E,WAAA,OAAAlC,GAAA,CAAAsI,EAAA;QAAFnL,EAAE,CAAAsD,SAAA,CAGjB,CAAC;QAHctD,EAAE,CAAAuD,UAAA,SAAAV,GAAA,CAAA8H,WAAA,IAAA9H,GAAA,CAAAW,cAGjB,CAAC;QAHcxD,EAAE,CAAAsD,SAAA,CAOzD,CAAC;QAPsDtD,EAAE,CAAA6H,UAAA,CAAAhF,GAAA,CAAAoH,YAOzD,CAAC;QAPsDjK,EAAE,CAAAuD,UAAA,gCAOxB,CAAC;QAPqBvD,EAAE,CAAAsD,SAAA,CAQK,CAAC;QARRtD,EAAE,CAAA+E,WAAA,cAAAlC,GAAA,CAAA8I,aAAA;QAAF3L,EAAE,CAAAsD,SAAA,CAWnD,CAAC;QAXgDtD,EAAE,CAAAuD,UAAA,SAAAV,GAAA,CAAAwH,cAWnD,CAAC;QAXgDrK,EAAE,CAAAsD,SAAA,CA0BgC,CAAC;QA1BnCtD,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAA6E,eAAA,KAAA5C,GAAA,EAAAY,GAAA,CAAAkB,UAAA,KAAAlB,GAAA,CAAAmH,sBAAA,UA0BgC,CAAC;QA1BnChK,EAAE,CAAAsD,SAAA,EA6BpB,CAAC;QA7BiBtD,EAAE,CAAAuD,UAAA,YAAAV,GAAA,CAAA6C,sBA6BpB,CAAC;QA7BiB1F,EAAE,CAAAsD,SAAA,CA2CrC,CAAC;QA3CkCtD,EAAE,CAAAuD,UAAA,YAAAV,GAAA,CAAA2C,KA2CrC,CAAC;QA3CkCxF,EAAE,CAAAsD,SAAA,CAoDnB,CAAC;QApDgBtD,EAAE,CAAAuD,UAAA,YAAAV,GAAA,CAAA4D,uBAoDnB,CAAC;QApDgBzG,EAAE,CAAAsD,SAAA,CAkEnD,CAAC;QAlEgDtD,EAAE,CAAAuD,UAAA,SAAAV,GAAA,CAAAwH,cAkEnD,CAAC;QAlEgDrK,EAAE,CAAAsD,SAAA,CAkF2E,CAAC;QAlF9EtD,EAAE,CAAAuD,UAAA,SAAAV,GAAA,CAAAuH,cAkF2E,CAAC;QAlF9EpK,EAAE,CAAAsD,SAAA,CAiGjB,CAAC;QAjGctD,EAAE,CAAAuD,UAAA,SAAAV,GAAA,CAAA+H,WAAA,IAAA/H,GAAA,CAAA8F,cAiGjB,CAAC;MAAA;IAAA;IAAAwM,YAAA,EAAAA,CAAA,MAK26BvV,EAAE,CAACwV,OAAO,EAAyGxV,EAAE,CAACyV,OAAO,EAAwIzV,EAAE,CAAC0V,IAAI,EAAkH1V,EAAE,CAAC2V,gBAAgB,EAAyK3V,EAAE,CAAC4V,OAAO,EAAgGlU,EAAE,CAACmU,MAAM,EAA2ErU,gBAAgB,EAAkFD,eAAe,EAAiFD,eAAe,EAAiFG,aAAa;IAAAqU,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACjiE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxG6F7V,EAAE,CAAA8V,iBAAA,CAwGJlN,QAAQ,EAAc,CAAC;IACtG0E,IAAI,EAAEnN,SAAS;IACf4V,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEnJ,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE+I,eAAe,EAAExV,uBAAuB,CAAC6V,MAAM;MAAEN,aAAa,EAAEtV,iBAAiB,CAAC6V,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,y5BAAy5B;IAAE,CAAC;EACp7B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpI,IAAI,EAAEtN,EAAE,CAAC+S;EAAW,CAAC,EAAE;IAAEzF,IAAI,EAAEtN,EAAE,CAACgT;EAAO,CAAC,EAAE;IAAE1F,IAAI,EAAEtN,EAAE,CAACiT;EAAkB,CAAC,EAAE;IAAE3F,IAAI,EAAEtN,EAAE,CAACkT;EAAU,CAAC,EAAE;IAAE5F,IAAI,EAAE+I,QAAQ;IAAEC,UAAU,EAAE,CAAC;MACpJhJ,IAAI,EAAEhN,MAAM;MACZyV,IAAI,EAAE,CAACjW,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEwN,IAAI,EAAErF,SAAS;IAAEqO,UAAU,EAAE,CAAC;MAClChJ,IAAI,EAAEhN,MAAM;MACZyV,IAAI,EAAE,CAAC7V,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEoN,IAAI,EAAEzM,EAAE,CAACsS;EAAc,CAAC,CAAC,EAAkB;IAAE/J,IAAI,EAAE,CAAC;MAC5DkE,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEiG,UAAU,EAAE,CAAC;MACb8G,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEqJ,SAAS,EAAE,CAAC;MACZ0D,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEuJ,iBAAiB,EAAE,CAAC;MACpBwD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEwJ,WAAW,EAAE,CAAC;MACduD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEyJ,sBAAsB,EAAE,CAAC;MACzBsD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE0J,YAAY,EAAE,CAAC;MACfqD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE+H,sBAAsB,EAAE,CAAC;MACzBgF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEgI,sBAAsB,EAAE,CAAC;MACzB+E,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEuH,mBAAmB,EAAE,CAAC;MACtBwF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEwH,cAAc,EAAE,CAAC;MACjBuF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEiF,KAAK,EAAE,CAAC;MACR8H,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE4J,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE6J,cAAc,EAAE,CAAC;MACjBkD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE8J,cAAc,EAAE,CAAC;MACjBiD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEgJ,gBAAgB,EAAE,CAAC;MACnB+D,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE+J,KAAK,EAAE,CAAC;MACRgD,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEgK,UAAU,EAAE,CAAC;MACb+C,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEiK,MAAM,EAAE,CAAC;MACT8C,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEiK,cAAc,EAAE,CAAC;MACjB6C,IAAI,EAAE7M,SAAS;MACfsV,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAErL,gBAAgB,EAAE,CAAC;MACnB4C,IAAI,EAAE7M,SAAS;MACfsV,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEpL,WAAW,EAAE,CAAC;MACd2C,IAAI,EAAE5M,YAAY;MAClBqV,IAAI,EAAE,CAACjV,MAAM;IACjB,CAAC,CAAC;IAAE8J,WAAW,EAAE,CAAC;MACd0C,IAAI,EAAE5M,YAAY;MAClBqV,IAAI,EAAE,CAAChV,MAAM;IACjB,CAAC,CAAC;IAAE8J,SAAS,EAAE,CAAC;MACZyC,IAAI,EAAE3M,eAAe;MACrBoV,IAAI,EAAE,CAAC/U,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMuV,cAAc,CAAC;EACjB,OAAO5D,IAAI,YAAA6D,uBAAA3D,CAAA;IAAA,YAAAA,CAAA,IAAwF0D,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAhR8EzW,EAAE,CAAA0W,gBAAA;IAAApJ,IAAA,EAgRSiJ;EAAc;EAClH,OAAOI,IAAI,kBAjR8E3W,EAAE,CAAA4W,gBAAA;IAAAC,OAAA,GAiRmC9W,YAAY,EAAEkB,YAAY,EAAEM,YAAY,EAAEH,gBAAgB,EAAED,eAAe,EAAED,eAAe,EAAEG,aAAa,EAAEtB,YAAY,EAAEkB,YAAY;EAAA;AACzQ;AACA;EAAA,QAAA4U,SAAA,oBAAAA,SAAA,KAnR6F7V,EAAE,CAAA8V,iBAAA,CAmRJS,cAAc,EAAc,CAAC;IAC5GjJ,IAAI,EAAE1M,QAAQ;IACdmV,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAAC9W,YAAY,EAAEkB,YAAY,EAAEM,YAAY,EAAEH,gBAAgB,EAAED,eAAe,EAAED,eAAe,EAAEG,aAAa,CAAC;MACtHyV,OAAO,EAAE,CAAC/W,YAAY,EAAE6I,QAAQ,EAAE3H,YAAY,CAAC;MAC/C8V,YAAY,EAAE,CAACnO,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAE2N,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}