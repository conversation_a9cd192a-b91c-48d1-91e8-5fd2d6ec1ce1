import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { OpportunitiesService } from 'src/app/store/opportunities/opportunities.service';
import { ActivitiesService } from 'src/app/store/activities/activities.service';
import { MessageService } from 'primeng/api';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  startWith,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';

interface DropdownOption {
  label: string;
  value: string;
}

@Component({
  selector: 'app-opportunities-followup-form',
  templateUrl: './opportunities-followup-form.component.html',
  styleUrl: './opportunities-followup-form.component.scss',
})
export class OpportunitiesFollowupFormComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @Input() visible: boolean = false;
  @Output() onClose = new EventEmitter<void>();
  public accounts$?: Observable<any[]>;
  public accountLoading = false;
  public accountInput$ = new Subject<string>();
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public submitted = false;
  public saving = false;
  public activity_id: string = '';
  private owner_id: string | null = null;
  public position: string = 'right';

  public dropdowns: Record<string, any[]> = {
    opportunityCategory: [],
    opportunityStatus: [],
    opportunitySource: [],
  };

  public FollowUpForm: FormGroup = this.formBuilder.group({
    name: ['', [Validators.required]],
    prospect_party_id: ['', [Validators.required]],
    primary_contact_party_id: ['', [Validators.required]],
    origin_type_code: [''],
    expected_revenue_amount: ['', [Validators.required]],
    expected_revenue_start_date: [''],
    expected_revenue_end_date: [''],
    life_cycle_status_code: ['', [Validators.required]],
    probability_percent: [''],
    group_code: [''],
    note: ['', [Validators.required]],
  });

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private opportunitiesservice: OpportunitiesService,
    private activitiesservice: ActivitiesService,
    private messageservice: MessageService
  ) {}

  ngOnInit() {
    this.activity_id = this.route.parent?.snapshot.paramMap.get('id') || '';
    this.FollowUpForm.get('prospect_party_id')
      ?.valueChanges.pipe(
        takeUntil(this.unsubscribe$),
        tap((selectedBpId) => {
          if (selectedBpId) {
            this.loadAccountByContacts(selectedBpId);
          } else {
            this.contacts$ = of(this.defaultOptions);
          }
        }),
        catchError((err) => {
          console.error('Account selection error:', err);
          this.contacts$ = of(this.defaultOptions);
          return of();
        })
      )
      .subscribe();
    this.getOwner().subscribe({
      next: (response: string | null) => {
        this.owner_id = response;
      },
      error: (err) => {
        console.error('Error fetching bp_id:', err);
      },
    });
    this.loadAccounts();
    this.loadOpportunityDropDown(
      'opportunityCategory',
      'CRM_OPPORTUNITY_GROUP'
    );
    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');
    this.loadOpportunityDropDown(
      'opportunitySource',
      'CRM_OPPORTUNITY_ORIGIN_TYPE'
    );
  }

  private getOwner(): Observable<string | null> {
    return this.activitiesservice.getEmailwisePartner();
  }

  loadOpportunityDropDown(target: string, type: string): void {
    this.opportunitiesservice
      .getOpportunityDropdownOptions(type)
      .subscribe((res: any) => {
        const options: DropdownOption[] =
          res?.data?.map(
            (attr: { description: string; code: string }): DropdownOption => ({
              label: attr.description,
              value: attr.code,
            })
          ) ?? [];

        // Assign options to dropdown object
        this.dropdowns[target] = options;

        // Set 'Open' as default selected for activityStatus only
        if (target === 'opportunityStatus') {
          const openOption = options.find(
            (opt) => opt.label.toLowerCase() === 'discover'
          );
          if (openOption) {
            this.FollowUpForm.get('life_cycle_status_code')?.setValue(
              openOption.value
            );
          }
        }
      });
  }

  private loadAccounts(): void {
    this.accounts$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.accountInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.accountLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'filters[roles][bp_role][$in][0]': 'FLCU01',
            'filters[roles][bp_role][$in][1]': 'FLCU00',
            'fields[0]': 'bp_id',
            'fields[1]': 'first_name',
            'fields[2]': 'last_name',
            'fields[3]': 'bp_full_name',
          };

          if (term) {
            params['filters[$or][0][bp_id][$containsi]'] = term;
            params['filters[$or][1][bp_full_name][$containsi]'] = term;
          }

          return this.opportunitiesservice.getPartners(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Account fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.accountLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  private loadAccountByContacts(bpId: string): void {
    this.contacts$ = this.contactInput$.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.contactLoading = true)),
      switchMap((term: string) => {
        const params: any = {
          'filters[bp_company_id][$eq]': bpId,
          'populate[business_partner_person][populate][addresses][populate]':
            '*',
        };

        if (term) {
          params[
            'filters[$or][0][business_partner_person][bp_id][$containsi]'
          ] = term;
          params[
            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'
          ] = term;
        }

        return this.activitiesservice.getPartnersContact(params).pipe(
          map((response: any[]) => response || []),
          tap((contacts: any[]) => {
            this.contactLoading = false;
          }),
          catchError((error) => {
            console.error('Contact loading failed:', error);
            this.contactLoading = false;
            return of([]);
          })
        );
      })
    );
  }

  async onSubmit() {
    this.submitted = true;

    if (this.FollowUpForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.FollowUpForm.value };

    const data = {
      name: value?.name,
      prospect_party_id: value?.prospect_party_id,
      primary_contact_party_id: value?.primary_contact_party_id,
      origin_type_code: value?.origin_type_code,
      expected_revenue_amount: value?.expected_revenue_amount,
      expected_revenue_start_date: value?.expected_revenue_start_date
        ? this.formatDate(value.expected_revenue_start_date)
        : null,
      expected_revenue_end_date: value?.expected_revenue_end_date
        ? this.formatDate(value.expected_revenue_end_date)
        : null,
      life_cycle_status_code: value?.life_cycle_status_code,
      probability_percent: value?.probability_percent,
      group_code: value?.group_code,
      main_employee_responsible_party_id: this.owner_id,
      note: value?.note,
      type_code: '0005',
      activity_id: this.activity_id,
    };

    this.opportunitiesservice
      .createFollowup(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.saving = false;
          this.visible = false;
          this.FollowUpForm.reset();
          this.messageservice.add({
            severity: 'success',
            detail: 'Follow Up Added Successfully!.',
          });
          this.activitiesservice
            .getActivityByID(this.activity_id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe();
        },
        error: () => {
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  get f(): any {
    return this.FollowUpForm.controls;
  }

  showDialog(position: string) {
    this.position = position;
    this.visible = true;
    this.submitted = false;
    this.FollowUpForm.reset();
  }

  hideDialog(): void {
    this.onClose.emit();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
