{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ImportComponent } from './import.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ImportComponent\n}, {\n  path: ':id',\n  component: ImportComponent\n}, {\n  path: ':id/:sub-id',\n  component: ImportComponent\n}, {\n  path: '**',\n  redirectTo: '',\n  pathMatch: 'full'\n}];\nexport let ImportRoutingModule = /*#__PURE__*/(() => {\n  class ImportRoutingModule {\n    static {\n      this.ɵfac = function ImportRoutingModule_Factory(t) {\n        return new (t || ImportRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ImportRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ImportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}