{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { TableModule } from 'primeng/table';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EditorModule } from 'primeng/editor';\nimport * as i0 from \"@angular/core\";\nexport let CommonFormModule = /*#__PURE__*/(() => {\n  class CommonFormModule {\n    static {\n      this.ɵfac = function CommonFormModule_Factory(t) {\n        return new (t || CommonFormModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CommonFormModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, EditorModule, DropdownModule, CalendarModule]\n      });\n    }\n  }\n  return CommonFormModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}